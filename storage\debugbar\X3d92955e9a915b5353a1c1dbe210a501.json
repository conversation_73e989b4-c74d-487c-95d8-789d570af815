{"__meta": {"id": "X3d92955e9a915b5353a1c1dbe210a501", "datetime": "2025-06-28 16:01:25", "utime": **********.551918, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.017524, "end": **********.551938, "duration": 0.5344140529632568, "duration_str": "534ms", "measures": [{"label": "Booting", "start": **********.017524, "relative_start": 0, "end": **********.477151, "relative_end": **********.477151, "duration": 0.4596269130706787, "duration_str": "460ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.477164, "relative_start": 0.4596400260925293, "end": **********.551941, "relative_end": 2.86102294921875e-06, "duration": 0.07477688789367676, "duration_str": "74.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831920, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00496, "accumulated_duration_str": "4.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.528081, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 36.694}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5393121, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 36.694, "width_percent": 12.903}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"produ%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"produ%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;produ%", "%&lt;div class=&quot;product-stock&quot; id=&quot;produ%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5424058, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 49.597, "width_percent": 50.403}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-2105494207 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2105494207\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1346264262 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1346264262\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1459029446 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"36 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;produ</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459029446\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">69</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNPQzQxNjJXWERyRFAzV0graDYxb1E9PSIsInZhbHVlIjoicDVuQmFCMTlEOUNNMnVTZ1c1Q3pIVVJReTVRNjBBMDhJZVZuWEROY0tsbUVpUUlXMVo1aXFWa2kvYWZFak5BM2dnMXNlWXZxS1dCekwvZElva3d6RjFMZVVZeTBadkpNK0R2MS9Vd0VQVHN0TzZPZHhJREtYRDZQTFFDcGRPdDdTckV1Yjg2VDJ5azk5UVFLNWxTTzZ5UCtDTVJGOWd3cDBFZUprdEhQbTBNSUlaL0VEcnJtKzRmamFPSytTdDlSY2IxNkMrWTliZExPaVM5QjJjeVg4WVpockVQY2x1QXNMSGRScWFNSjVueU9Wc1ZtZVdjWTJJSkFVYnpTcmdoNUZKUkkwUmpxSGxvQmc5UTVtMGhXYUFmTUhtaGRGaURMN3ViU2dyNlo3SnFMZDJxRE5jaE1kd1ZjY1pwWjRNSzIyQks3YWR6UW5LdkRhcU81QzBWOHBEUEN2dHBURHJybERjU0hITE51eTNMZ1gzZTVQQ3AySlVpU2xuZnB3QlBFVlQ1V29GaVI2anRPNXVEek5vdDhFNVFoTHRCN1VaU284czZHU1BOVDdJaFpyMFd5Mllla2pKdnc2MmVHeHJ6cTlBang0V2VhdlhHQmdvVVNIc0dSaXE4M2VVNFphVHVDLzBKZHNCNm9RR0JOWk0xN0Y4NTJiMnNjVEh3by9kTDYiLCJtYWMiOiJjNTMwMDYyMjFhNGE5NzhjMGQ5NjljYzkxN2M0N2U3MmM2MGIwMmY1MWYyOTVmMGI0ODQxOTZjMWRmMzc1NzVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9WcGszWFhpWlplbG0ycmUrZkF3bUE9PSIsInZhbHVlIjoiU2ZEMlg2MG5EbkdGWkJsWGV0SU5mc0luSy9GeklsaDVZL1JTUC9CQVVtNUliT1FDUE1WdzZ2aFdFenFWUm9HNHA4SFpDVm5TeUZvbmFBbW5Ddi9lTDlqMnFJd3huTzl2b0ZVUVBmYkVvRFdZaWVQVndxeVVIL2FybERvTTY2LzhkK2o4UlpFdDVnTGVZaHdLVUZZbjNPZzBNN1NGa1Q5T1lWR1dXdnpIZytGUGV0L0dNUzJFK29xVFJwUWR2QUIzY0RUMG9qY0Uyc05tS1o3T3g1ZWcyekJMOG02eUgzYmJJcmcycXhBb0xvVkNTTytZc3dGYzNRemNMd0hnc2gyMElUSk5RQ2RaVmpuVzZpd1YyY1FTTTRpV2R2aGEwaWhFdjdEWWxMMWZRazdLOXhtVnNyK0lhamQ5emZMZUwyM1FkQlF5cWV1bTdDbVg1UXh5NVlhOXVQUWk4c21tOUVpOE5ENVpJZEllRFZRbDUwZk9EZHZKaUhUcHVBZFUvK1F1Um5mRThGRWRkNmY0dGMvbmZZSmp2c2h5OFNiZDV5Y2MzSzBjVHo1d2g3eWVJWk9pL1Z3SGxzd1M1TzlkNXNiMXFtRE41SmJWNHNBekd1bnNjT3BjRTFTQW5xczdOR3h3TTlvWUgvOTZYTXE1OHFUOWVPclc2aVBjTjVuZm03TC8iLCJtYWMiOiJmMjYyYzc1NzhhZDFkYTc2ZTg5MjNhM2FiMTdmNGRmNDAzZmY0MzZmYTE0NzJkYWNiMmM3ZjM0YmQ3MDExM2M1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-530780557 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-530780557\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-613312804 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhmckdOczhxbDdxc1RQSGJJakRSVXc9PSIsInZhbHVlIjoibnRGWG8yTEhOVlAwNDFzdlI1aWJSV0F1OW9WVEdRTTc5UEF3L0hnaGdtYmk1Mm0rM3pmc2lRSEFTSUlUNEprdmU2bno5bWw3dmk4b3FWSXJnQVZTVis1R0c3dFVaMUg1MmVCSXRkcm5wOXk2WkdUQmxVZ1pjakZrSFdOM2wwTDEzSlgxL0FrL3gyczAwZmpBRzNJcERjeStZaDFTQnVNM24rdDFYVERIRmxaUWZsRFh6K0w5Y1JqZFNUa3Nsc2RJZGtHbi9PVDF4dURFdS9ZSzNQUWVoUld4ZlNmM0s3aXRRNkIrcUZZWGVPR3FOWVlRSHRRYWs5Szg5ZjFSM0p3WFhMeWw3QlVjRlVIMGpGM05lMmRMc2llblR4NGxwQWN6d0NkWFBpbEg4MWlJNkVvT05rL1ZFQnJkKzU5SFFtTzZiaWQ2OERCM1hFMHhkcTBETXlYSERpTlMyWTFEa0MzU3RmMysydTRaVm1pckdlWEUxZHFQOU9VYjlMdExYdW4ycEFPL05nbjNxTU5ERDZRcEJON0RJNW1WZEl6Uk5TSGNka3pETTJId3I2NWxsTm5xNTVmK3NEamJqNDgzV0N5U3luN0JBNUZSSWFqNDVVdm1oU3hQSFR4OUYyVXMzWVNTR0xIaWVobkdIeVdSLzhPdlhBTlozRlVNWFVUcWd4VFMiLCJtYWMiOiI3ZDAxNTYyODI4MzUxYzkzM2VlOWMwZTRiNmM3M2NkMmU0YmYxMWE0ODU1ODBlOGE3ZDBjNzEzZDUxMTczMTRmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkVIY0twVmZwUkJkbjk0eGVMUU1oakE9PSIsInZhbHVlIjoidGRjdTZFZFNwcjlTNkFVMmFrYWZEaEh5NEZTOXNTVmZQL3BnQ1ZIcWpISExWSTkwNDNJcmN1WVVoTzJJeEVwcDFNejBLWGtTVzllcTlyeDZKQTN6aWFLZHBFcEVIMTdMaTZSWkFVNnFkUCt2UVNLZ1FHMnhLdUtkYmtTRjFiUlliRUdiUlo4ZFRaaHpwN1cyTHZCU2lqeTdHTTh5SDNlZjRLVUJRMzFUdjArMmFYY2JBU2Z2QmwxbXAxeE82d3pQNkNrZkRkdmRUZk9QdlEvUzd1bHVTdEZiMDB0L1dQWXZ3cmhQUWdpWGxocjJMcDNiSkMrT2IvV1Jwc2tNUDlWZHBKUHJSMittb2UzaGdsVUJucldwSFhxbk83YzBuM0tBRVVJeVYvZkZ4aldiYUE2OUxqUzdlUDhHRFltamxaeDhoSk05aTRxNmdpZHc2endGeXdpWkFhYlpCcndGRGl1ZEc2ZkloTXFJUUxJMDllUEhHMHBadTIwOC9WV0E0d2kvNDBROHIrWHNLU0JodDd5d3VWbnI2WU9VL0hzRjNENDhOWkpRZkxMOHhESERpanpjMmNHSi9XZWg5R0RINDg5a05BY0VBNlErQW82QXVyUTFZSHRVVWZUQW1qczV4b2NWbFdFc0MzMVFtZ2F5YW1wTWN5Qis5c0gyVlR6KzQ1eTAiLCJtYWMiOiI2NDhiMTQzZjVhMjk0NGVkY2I5MWQwMGU2NWE3YTQ1Nzc5Zjk4MmIyZjNiOWRjNWU3MzQwNzE3YjZmOGRkNDQ1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhmckdOczhxbDdxc1RQSGJJakRSVXc9PSIsInZhbHVlIjoibnRGWG8yTEhOVlAwNDFzdlI1aWJSV0F1OW9WVEdRTTc5UEF3L0hnaGdtYmk1Mm0rM3pmc2lRSEFTSUlUNEprdmU2bno5bWw3dmk4b3FWSXJnQVZTVis1R0c3dFVaMUg1MmVCSXRkcm5wOXk2WkdUQmxVZ1pjakZrSFdOM2wwTDEzSlgxL0FrL3gyczAwZmpBRzNJcERjeStZaDFTQnVNM24rdDFYVERIRmxaUWZsRFh6K0w5Y1JqZFNUa3Nsc2RJZGtHbi9PVDF4dURFdS9ZSzNQUWVoUld4ZlNmM0s3aXRRNkIrcUZZWGVPR3FOWVlRSHRRYWs5Szg5ZjFSM0p3WFhMeWw3QlVjRlVIMGpGM05lMmRMc2llblR4NGxwQWN6d0NkWFBpbEg4MWlJNkVvT05rL1ZFQnJkKzU5SFFtTzZiaWQ2OERCM1hFMHhkcTBETXlYSERpTlMyWTFEa0MzU3RmMysydTRaVm1pckdlWEUxZHFQOU9VYjlMdExYdW4ycEFPL05nbjNxTU5ERDZRcEJON0RJNW1WZEl6Uk5TSGNka3pETTJId3I2NWxsTm5xNTVmK3NEamJqNDgzV0N5U3luN0JBNUZSSWFqNDVVdm1oU3hQSFR4OUYyVXMzWVNTR0xIaWVobkdIeVdSLzhPdlhBTlozRlVNWFVUcWd4VFMiLCJtYWMiOiI3ZDAxNTYyODI4MzUxYzkzM2VlOWMwZTRiNmM3M2NkMmU0YmYxMWE0ODU1ODBlOGE3ZDBjNzEzZDUxMTczMTRmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkVIY0twVmZwUkJkbjk0eGVMUU1oakE9PSIsInZhbHVlIjoidGRjdTZFZFNwcjlTNkFVMmFrYWZEaEh5NEZTOXNTVmZQL3BnQ1ZIcWpISExWSTkwNDNJcmN1WVVoTzJJeEVwcDFNejBLWGtTVzllcTlyeDZKQTN6aWFLZHBFcEVIMTdMaTZSWkFVNnFkUCt2UVNLZ1FHMnhLdUtkYmtTRjFiUlliRUdiUlo4ZFRaaHpwN1cyTHZCU2lqeTdHTTh5SDNlZjRLVUJRMzFUdjArMmFYY2JBU2Z2QmwxbXAxeE82d3pQNkNrZkRkdmRUZk9QdlEvUzd1bHVTdEZiMDB0L1dQWXZ3cmhQUWdpWGxocjJMcDNiSkMrT2IvV1Jwc2tNUDlWZHBKUHJSMittb2UzaGdsVUJucldwSFhxbk83YzBuM0tBRVVJeVYvZkZ4aldiYUE2OUxqUzdlUDhHRFltamxaeDhoSk05aTRxNmdpZHc2endGeXdpWkFhYlpCcndGRGl1ZEc2ZkloTXFJUUxJMDllUEhHMHBadTIwOC9WV0E0d2kvNDBROHIrWHNLU0JodDd5d3VWbnI2WU9VL0hzRjNENDhOWkpRZkxMOHhESERpanpjMmNHSi9XZWg5R0RINDg5a05BY0VBNlErQW82QXVyUTFZSHRVVWZUQW1qczV4b2NWbFdFc0MzMVFtZ2F5YW1wTWN5Qis5c0gyVlR6KzQ1eTAiLCJtYWMiOiI2NDhiMTQzZjVhMjk0NGVkY2I5MWQwMGU2NWE3YTQ1Nzc5Zjk4MmIyZjNiOWRjNWU3MzQwNzE3YjZmOGRkNDQ1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613312804\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-390402297 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-390402297\", {\"maxDepth\":0})</script>\n"}}