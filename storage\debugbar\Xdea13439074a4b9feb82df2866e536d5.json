{"__meta": {"id": "Xdea13439074a4b9feb82df2866e536d5", "datetime": "2025-06-28 15:50:07", "utime": **********.854798, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.483405, "end": **********.854811, "duration": 0.37140583992004395, "duration_str": "371ms", "measures": [{"label": "Booting", "start": **********.483405, "relative_start": 0, "end": **********.797079, "relative_end": **********.797079, "duration": 0.3136739730834961, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.797087, "relative_start": 0.31368184089660645, "end": **********.854812, "relative_end": 9.5367431640625e-07, "duration": 0.057724952697753906, "duration_str": "57.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45819512, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00686, "accumulated_duration_str": "6.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.827395, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 24.781}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.837137, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 24.781, "width_percent": 5.977}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*************%' or `sku` LIKE '%*************%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*************%", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8395462, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 30.758, "width_percent": 32.216}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.844435, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 62.974, "width_percent": 28.28}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1995}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.848486, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1995", "source": "app/Http/Controllers/PosController.php:1995", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1995", "ajax": false, "filename": "PosController.php", "line": "1995"}, "connection": "kdmkjkqknb", "start_percent": 91.254, "width_percent": 8.746}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1975267821 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1975267821\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1734249311 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1734249311\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-285238248 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285238248\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-329132755 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125486476%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFOV0pKZFQwTjBlR3RSNHIvb2ppdnc9PSIsInZhbHVlIjoiZGo3cmRHVUNQY0s0N204anZhNmV2UElxa2V0cnlRU2JaVno5T0pnRGRJcFpGd0JaMklHV3VrREVERlJzQXg2YjY2alBnTDNrR0F1MVRwekorREd2MmVsd2lSY3dJRm1GeEl4aEh6VTBpSFl2VEJsTllFVHRvQkJDYzdyamJNb3VEcmpTdzE1ZC9xUk5Ed1dkMW9GbG56bFFQRWExYUtmT1NMUzk5N2x5dkNxdUxpR29QY3gwQ3hpRERVMXViUWpsL3BBd3FGVmcxMnUvVmFTSDJGWTdUbXNQUEJkd053OGFSUVNibXZNZERkWXZ1aHFxVU9hcFVaL1JLUFRwd1NENk5GcE5LNVY1a2Q0czNSZkRYdCsxUlhGYjRxdnRlZVFsbjBOVFZEdmNraTAzcHNkV2gzV0U2VWgvclh2RUxUTVY5VWNXTXl5UXdlOFM3a1JlbXg5SGM3SzIxczU0QUhLWlB0R3BCYnVIV2VpdUZIUHlHTGdEbDUwSCt2STloWUhORXVRcXFDekppamw0UFZQKzZmUTZuSjJRZ1d2WERzYm94Uit5SXhKMXUybUs2bXByV3ZybzJ2Z3V2ZXFuUE1MaS9UcERyeVFBaUhyVDRxTWJmbk5wbWl5RFhVZmViSTBGMk5yZ0pmSklFZkhHVTBUTG9rR0pCSTNoL2NWSGtDS3MiLCJtYWMiOiIzMTM0ZmI2OGE4OGY2M2I1ZDFmZTMxMGJmMGZkOGEwNzkwZjYxMDcwMGY2N2JhOTQwYTQ3ZTZlOTE1YmU0OTg5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRoTFJ2aDBRM3YzTk5BY29PSzU0Ymc9PSIsInZhbHVlIjoiOWNsUHJyeTBrUC93Z1llaG1kdlJIQldCcmtLMlZRRlE2cmFONjUrT00ydHJrLzdyOGVhVnRDcXBtQkJRWnhxSUtHYmwyeFRlalFpa2lTdTlremdIai9PY3F0THI5ekZMcCsvNUtZN29wdW9wak1KbTR4MWdaeHM2N3NBWmRzTDY3Uk9XNVRmdHpSWEk3eVdBTEZTN3pGTVkxWXhRenJZSVdTR21qMkhiTnQ1eWk5MmJzNDRxZVJSYTNka1FmOWZjWmg2cUZBZEZQd3NoaTYrR1ZSM3N5cXUxTFpNcHZFMmhvRzJNYXFHcVZzTngwaUdrTGhXWHhscEtoOGhkK3dTMDNtM0JYKzlaWTZlOGxBV1VLZlgxTmEreDA5dURxZ2JNK3ZMVU5HSENRNG8wdTdXaDZ3Z3VmaHJ5MmtqVGFnMFdaeXN4blNVVzdYVTgzLzF5UzRlTjB1ZHFBcUVRTG1FZ3d1ODlieVFBUyt6bFhXQ0xBR3dVcU00TXIwaUlqbHJxVzB0ZklQOUY4M3NWd1cvLzI5ODlHRm5ET0tScHNmRHIyVTV6ektVdktoVFpqVEhJVmlDcGZaOUJHSVBPQWVKZmRaQ3Zrb2RnTktYNnJaQUdJNW8zRC95d0NhYzFJT1p2Q1c1R09XdUJ4TnltY2lDRnRoempqam1zZEFTeXFWdnYiLCJtYWMiOiIxODUxNzllZmNmNjExN2IxMjM0OTZlZWVkODc0ZDA3Zjk0ZjA1ZDUxNjNlOWJjOGQ5NjRiNjU0MGYxNzgyNDUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-329132755\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-207082904 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-207082904\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-177243622 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:50:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhQVVV5OVdsd0JvKzI0RzhuV3VtRUE9PSIsInZhbHVlIjoiQjhlaGQ3TlAyTHRkQWlLQ2pBVEl2R1FsODhhR0JWZFJXUEVxZE0rSmtCdFF2eU1kRmVVUjl3QWtla2trN0UxejM1aFFuZm1TU3Y4OStjeTJPU3VTQjlmNHNENW03NWtiMnJsa25JcHltV2I0SHEvc3BweER3VmhmWlJCUzAwRWJmS0g1cXM2cnpKOUN2ZFR3QTMwcGRYT20xOTBWMUhVN05JTzgrM1pJakhycmpCdEFDQkZGS05iR25QNGRiQzNjbC9lOVlxdnJkenY4amFMc1c0K1g5cTVYMG1PdUdTaEtPWFRpYnozdDNpaFplQ05pSXlhOVRNOVVWKzYrMkNlbEozd09BWUJUcTVlRW9Fb3FkdlFZekE4Mkt4eDFRaTFaeG93YlplRVZMWXgzejNWaVE1T3BUdW1pdytmQm4rbCtrWS9TQ0dYV2ZPVnhYUmZaMDRyVXBULzhrb0R0Z3BsNzJSTWJDcURUM056WkhUWjM3R1FnYzd5S2REVThTRDdZM1pac25iSjF3WTVWeGNyMUlrdEEvSVRvQUgyM2hHcTBEanV3QXVpYWRyZ20yZWJxREsxMy9LR2pxQ0NRWllFVXpBZ1k4WDhvTmNybHdEdndVdmJscFA2RUh2b3pIRWJ1enlCdUw2S0hrWlF2ZEtDbzF3V1dacUc3NmJiajgzVWoiLCJtYWMiOiI4MGJiMTk4NWViNmU0ZGVjYTFmZjc2N2RjNjQ5YWU2MTgxYjAwNGFjNGEyYWJjZjA0YTcwM2VlNDllMDM4MzBmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:50:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZlOUwvK08zcVVkaEdlUHYvTzY3Qnc9PSIsInZhbHVlIjoiWHcrc21OQXhTOXJmbkNFQWFBYUZXalNGUjErZU9KQmk3bEJtVWpiRFVpVnFzZzh1RXh2WHkzK3pKeXlrdlZaY1c2Um8wcnUvL3hYMHJzQk9Wb0phUzJnUDhaS1l3ZlRIc2NGZCtZV1RranA5Z1BhazM0YlVuSjA3NyttVnVjbm1DVkJ0SVZuYWJsVzVua3lrZE5ZMFR6cEtoa01XMFF0ZWQ5V2pOYWlCbVhGSzBWNFh4Znk5WmxSV0lFc2J5cnRHdjBxTVNTbTRFdHp6WHZQM3AvcTR4Y21SNlNFQWZzdWV0QklGWXY3RG5YUTNDYUJCemhERDRzbmpOdXAvUWlXam9uWjlFblJNcFdGYjk1TTl3cXArQWtuOW5RbWFyMGMxdHBkam9FV2FSVlRlRmRpTmlXRVA5WHMvK3lZSnZXYXo1d3k1QzdnZDc3VmxMVVhqL2s3cW03RGhxdXBlcFNTUXpyajRsYlBTOFJKRUk5S013bzJKUGUyRWtJc1prM1JqL1MyUkVqMmZkczhCdDZZVEtMSE1CeGIydjFMaUVyR1JndFhEeWxuU3BsRmZ4R09vdVNNdmp4bm9NVjZtZmEzZUJMeXM3bjJTZ05SMFNFVGhZa2YrcTgzTytZVitsdlIxS2VwRFBLYkxwQUVrZC96akNCUzVpdkwyOU1pK2pMQVciLCJtYWMiOiJiNThiYTQ3OGNjZjI0ZWY5YjdhYWY0NzMxNjc2ZDY3MDc3ZTA0MTQyMzE4ODZkYjg5YmJlNTZlM2E4YjJlMDk4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:50:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhQVVV5OVdsd0JvKzI0RzhuV3VtRUE9PSIsInZhbHVlIjoiQjhlaGQ3TlAyTHRkQWlLQ2pBVEl2R1FsODhhR0JWZFJXUEVxZE0rSmtCdFF2eU1kRmVVUjl3QWtla2trN0UxejM1aFFuZm1TU3Y4OStjeTJPU3VTQjlmNHNENW03NWtiMnJsa25JcHltV2I0SHEvc3BweER3VmhmWlJCUzAwRWJmS0g1cXM2cnpKOUN2ZFR3QTMwcGRYT20xOTBWMUhVN05JTzgrM1pJakhycmpCdEFDQkZGS05iR25QNGRiQzNjbC9lOVlxdnJkenY4amFMc1c0K1g5cTVYMG1PdUdTaEtPWFRpYnozdDNpaFplQ05pSXlhOVRNOVVWKzYrMkNlbEozd09BWUJUcTVlRW9Fb3FkdlFZekE4Mkt4eDFRaTFaeG93YlplRVZMWXgzejNWaVE1T3BUdW1pdytmQm4rbCtrWS9TQ0dYV2ZPVnhYUmZaMDRyVXBULzhrb0R0Z3BsNzJSTWJDcURUM056WkhUWjM3R1FnYzd5S2REVThTRDdZM1pac25iSjF3WTVWeGNyMUlrdEEvSVRvQUgyM2hHcTBEanV3QXVpYWRyZ20yZWJxREsxMy9LR2pxQ0NRWllFVXpBZ1k4WDhvTmNybHdEdndVdmJscFA2RUh2b3pIRWJ1enlCdUw2S0hrWlF2ZEtDbzF3V1dacUc3NmJiajgzVWoiLCJtYWMiOiI4MGJiMTk4NWViNmU0ZGVjYTFmZjc2N2RjNjQ5YWU2MTgxYjAwNGFjNGEyYWJjZjA0YTcwM2VlNDllMDM4MzBmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:50:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZlOUwvK08zcVVkaEdlUHYvTzY3Qnc9PSIsInZhbHVlIjoiWHcrc21OQXhTOXJmbkNFQWFBYUZXalNGUjErZU9KQmk3bEJtVWpiRFVpVnFzZzh1RXh2WHkzK3pKeXlrdlZaY1c2Um8wcnUvL3hYMHJzQk9Wb0phUzJnUDhaS1l3ZlRIc2NGZCtZV1RranA5Z1BhazM0YlVuSjA3NyttVnVjbm1DVkJ0SVZuYWJsVzVua3lrZE5ZMFR6cEtoa01XMFF0ZWQ5V2pOYWlCbVhGSzBWNFh4Znk5WmxSV0lFc2J5cnRHdjBxTVNTbTRFdHp6WHZQM3AvcTR4Y21SNlNFQWZzdWV0QklGWXY3RG5YUTNDYUJCemhERDRzbmpOdXAvUWlXam9uWjlFblJNcFdGYjk1TTl3cXArQWtuOW5RbWFyMGMxdHBkam9FV2FSVlRlRmRpTmlXRVA5WHMvK3lZSnZXYXo1d3k1QzdnZDc3VmxMVVhqL2s3cW03RGhxdXBlcFNTUXpyajRsYlBTOFJKRUk5S013bzJKUGUyRWtJc1prM1JqL1MyUkVqMmZkczhCdDZZVEtMSE1CeGIydjFMaUVyR1JndFhEeWxuU3BsRmZ4R09vdVNNdmp4bm9NVjZtZmEzZUJMeXM3bjJTZ05SMFNFVGhZa2YrcTgzTytZVitsdlIxS2VwRFBLYkxwQUVrZC96akNCUzVpdkwyOU1pK2pMQVciLCJtYWMiOiJiNThiYTQ3OGNjZjI0ZWY5YjdhYWY0NzMxNjc2ZDY3MDc3ZTA0MTQyMzE4ODZkYjg5YmJlNTZlM2E4YjJlMDk4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:50:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177243622\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-265791145 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265791145\", {\"maxDepth\":0})</script>\n"}}