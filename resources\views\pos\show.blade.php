@php
    $logo=\App\Models\Utility::get_file('uploads/logo');
    $company_logo=Utility::getValByName('company_logo');
@endphp

@if (isset($pos) && $pos)
    <div class="card">
        <div class="card-body">
            <div class="row mt-2">
                <div class="col-6">
                    <img src="{{$logo.'/'.(isset($company_logo) && !empty($company_logo)?$company_logo:'logo-dark.png')}}" width="120px;">
                </div>
{{--                <div class="col-6 text-end">--}}
{{--                    <a href="#" class="btn btn-sm btn-primary" onclick="saveAsPDF()"><span class="ti ti-download"></span></a>--}}
{{--                </div>--}}
            </div>
            <div id="printableArea" style="overflow: auto;">
                <div class="row mt-3">
                    <div class="col-6">
                        <h1 class="invoice-id h6">{{ Auth::user()->posNumberFormat($pos->pos_id) }}</h1>
                        <div class="date"><b>{{ __('Date') }}: </b>{{ Auth::user()->dateFormat($pos->pos_date) }}</div>
                    </div>
                    <div class="col-6 text-end">
                        <div class="text-dark "><b>{{ __('Warehouse Name') }}: </b>
                            {{ $pos->warehouse ? $pos->warehouse->name : __('Unknown Warehouse') }}
                        </div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col contacts d-flex justify-content-between pb-4">
                        <div class="invoice-to">
                            <div class="text-dark h6"><b>{{ __('Billed To :') }}</b></div>
                            <div>
                                <strong>{{ $pos->customer ? $pos->customer->name : __('Walk-in Customer') }}</strong><br>
                                @if($pos->customer && $pos->customer->email)
                                    {{ $pos->customer->email }}<br>
                                @endif
                                @if($pos->customer && $pos->customer->phone)
                                    {{ $pos->customer->phone }}<br>
                                @endif
                            </div>
                        </div>
                        <div class="company-details">
                            <div class="text-dark h6"><b>{{ __('From:') }}</b></div>
                            <div>
                                <strong>{{ Auth::user()->name }}</strong><br>
                                {{ Auth::user()->email }}<br>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <table class="table">
                        <thead>
                        <tr>
                            <th class="text-left">{{ __('Items') }}</th>
                            <th>{{ __('Quantity') }}</th>
                            <th class="text-right">{{ __('Price') }}</th>
                            <th class="text-right">{{ __('Tax') }}</th>
                            <th class="text-right">{{ __('Tax Amount') }}</th>
                            <th class="text-right">{{ __('Total') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($pos->items as $key => $item)
                            @php
                                $subtotal = $item->getTotal();
                                $taxAmount = 0;
                                if (!empty($item->tax)) {
                                    $taxAmount = ($subtotal * $item->tax) / 100;
                                }
                                $total = $subtotal + $taxAmount - $item->discount;
                            @endphp
                            <tr>
                                <td class="cart-summary-table text-left">
                                    @if($item->product_id > 0 && $item->product)
                                        {{ $item->product->name }}
                                    @elseif($item->description)
                                        {{ $item->description }}
                                    @else
                                        {{ __('Unknown Product') }}
                                    @endif
                                </td>
                                <td class="cart-summary-table">
                                    {{ $item->quantity }}
                                </td>
                                <td class="text-right cart-summary-table">
                                    {{ Auth::user()->priceFormat($item->price) }}
                                </td>
                                <td class="text-right cart-summary-table">
                                    {{ $item->tax }}%
                                </td>
                                <td class="text-right cart-summary-table">
                                    {{ Auth::user()->priceFormat($taxAmount) }}
                                </td>
                                <td class="text-right cart-summary-table">
                                    {{ Auth::user()->priceFormat($total) }}
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                        <tfoot>
                        @php
                            $subTotal = $pos->getSubTotal();
                            $totalDiscount = $pos->getTotalDiscount();
                            $totalTax = $pos->getTotalTax();
                            $grandTotal = $pos->getTotal();
                        @endphp
                        <tr>
                            <td class="">{{ __('Sub Total') }}</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-right">{{ Auth::user()->priceFormat($subTotal) }}</td>
                        </tr>
                        @if($totalTax > 0)
                        <tr>
                            <td class="">{{ __('VAT') }}</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-right">{{ Auth::user()->priceFormat($totalTax) }}</td>
                        </tr>
                        @endif
                        @if($totalDiscount > 0)
                        <tr>
                            <td class="">{{ __('Discount') }}</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-right">{{ Auth::user()->priceFormat($totalDiscount) }}</td>
                        </tr>
                        @endif
                        <tr class="pos-header">
                            <td class="">{{ __('Total') }}</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-right">{{ Auth::user()->priceFormat($grandTotal) }}</td>
                        </tr>
                        </tfoot>
                    </table>
                </div>
            </div>

            <!-- Print and Action Buttons -->
            <div class="mt-3 text-end">
                <a href="{{ route('pos.thermal.print', $pos->id) }}" target="_blank" class="btn btn-primary">
                    <i class="ti ti-printer"></i> {{ __('Print Invoice') }}
                </a>
                @if($pos->posPayment)
                    <span class="badge bg-success ms-2">
                        <i class="ti ti-check"></i> {{ __('Paid') }} - {{ ucfirst($pos->posPayment->payment_type) }}
                    </span>
                @endif
            </div>
        </div>
    </div>

@endif


