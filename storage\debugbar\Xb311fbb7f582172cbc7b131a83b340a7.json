{"__meta": {"id": "Xb311fbb7f582172cbc7b131a83b340a7", "datetime": "2025-06-28 11:24:59", "utime": **********.172004, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109898.753951, "end": **********.17202, "duration": 0.41806888580322266, "duration_str": "418ms", "measures": [{"label": "Booting", "start": 1751109898.753951, "relative_start": 0, "end": **********.101661, "relative_end": **********.101661, "duration": 0.34770989418029785, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.101669, "relative_start": 0.3477180004119873, "end": **********.172022, "relative_end": 2.1457672119140625e-06, "duration": 0.07035303115844727, "duration_str": "70.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45738744, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1910\" onclick=\"\">app/Http/Controllers/PosController.php:1910-1968</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.009130000000000001, "accumulated_duration_str": "9.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.130874, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 17.306}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1402988, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 17.306, "width_percent": 4.71}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%232%' or `sku` LIKE '%232%') limit 10", "type": "query", "params": [], "bindings": ["15", "%232%", "%232%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.142865, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 22.015, "width_percent": 19.277}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (285, 545, 802, 964, 1117, 1346, 1441, 1541, 1542, 1544) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.147544, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 41.292, "width_percent": 25.63}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.152235, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 66.922, "width_percent": 3.505}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.153661, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 70.427, "width_percent": 2.3}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1549299, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 72.727, "width_percent": 1.972}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1561942, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 74.699, "width_percent": 2.957}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.157871, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 77.656, "width_percent": 6.243}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.159618, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 83.899, "width_percent": 3.834}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1610901, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 87.733, "width_percent": 3.614}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1627629, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 91.347, "width_percent": 2.738}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1641781, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 94.085, "width_percent": 3.505}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1659858, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 97.59, "width_percent": 2.41}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 21, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-276901869 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-276901869\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1428691976 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1428691976\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-150367131 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"3 characters\">232</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150367131\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1272615205 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">24</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IkJveEtKVTBCQXluV0wwMkoyeHVNSmc9PSIsInZhbHVlIjoiVkhycUFVTWtlU2VOeE9OUUxzVzBtZHZKbTZhWWFHWVlWYUV3ZmhQby9oRGwzTTh0OWhCZHRxcjJ1c0htN1BtZlVEa2Y1YXlSenFpS3lva0I4UHNjTHQvSm1BcjNSYkx0TnBVbHlkZlRxVHZONVdlK0I1U0tYaEZqVlVJd2JFN1ozZW5hdzVJQkxEK0FnSU5zRy83QmZRSC96SFdOMTN2Rm1MVllpSS9kcVFpclMyY0txTVo0UWxWUkJIUDJJNkkzdy9taGVqYWY4c1o4a0FGSDh4cWtNZmtDSjZIUWIvU3RyQmhLYW9qYWFiNS9iREVhang2ZE90T1B4d0VmZnBhMU1aeEs3ZEFCelIvNlVnZ1ptVzBORXhFN3paa2hpRHdCS0drdjh3QWhBTDlyWW5mUEJNU3hWZkR0QlJBQ2N5MXZoVEJWWWR6OHJNNHNPOEhuTW5JNkphcjVNUVBNSkFNNE1ETG5Zb0ZLWTBHVkIwOEptOG1HV3N2dDdXM2xQQW4zUDNQOWdoNC9sRHFLOTBpNTBwdTF5YzVvM2pTRjFxYWZHU3MrTWF3dnlvRTQ0alJvQXh2bC9TdWdJSVpObTZOYm1IZ2UvOStzZTYvZ0pTNnJjL05BbWRPR1lQNHlqU1M5ckJZU0g4VHZyY1VVb3Rwbys0dWF3TUV5SHBQWGwvcGQiLCJtYWMiOiI3MDU1MjhhYjA4NTU3NmY2NjAyYmFhMjMzMTY4NmRlYTZiYzkyZjEyYzAwYjU4NzIwYWJjNWZmNmZhYTE4Yzc2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IngxUVdpcnBnVTV6NXYzb2dEb0dGUUE9PSIsInZhbHVlIjoiSGF5SFI1c09zWGRqRWdRdnFGZS9GbUE5Q1NkdVhOTTIwLzZOOUpCajZEc3pkSnNJZ0ZZckc5ais5NVRJb3RXcEVpYmNiWUlvZC9VNGhrWHladzhHbVZ0eWxmeXlVOWhadU5GT1dWR0FMTEh1NVpPNWg1MHh4R2o3azExdTNycnozWC9vdW0rbGhmOU10L3FVZ2swZmV4RXpDaEQ5cnUwbFlzVXJ3ZkVTRGtCZGVIbjJGWmFONWxTdU5rZVZScmF5YVdsdzhsTURyL0Z2SStDV0RwZkp3NGUyUG9CbW1TVkQ5bUg5NEREWVl1RWZ3RS9XUTdUZk9ZNTNtNXhKdklNUE16QUU2b2JTcnI5ajU5MG45dUlsaWo3MWo5dDVJVTdCdlRUVFk3cUJwbG54QUpEWHZ3aDNQUVJxK3hiRUsxdnlvYW9sNHBRNGREVFN4N08zZlFLL0w2Y0grbE9nelVCZ21Gb3VpWmErdHVwTmFEa09sZzc5N2NWZTRwMFBPNWM1NFBiTVYzVTB4WE1wTXJ5S2wxRE10YXN4b21mU05pZ3lxY0J5UzZOK1VzSjR1YUxuN1QvRlByZDVLK3ZmcVdxeHEyZmdETE5CaDhUN3pkMW56SUpuM1R0WkpNckhNZzdxZTcvSWl0NDZMdUsvSVFQWjhBblRzYzNnaDFrVkl4cDIiLCJtYWMiOiJjNzU3MjUwM2FlNGU1Yzk5MmM3MTIxYzZlZmRlZjM5Yzk2YzFmYmM5NGY0NzQwOWFjNzI1ZTZmNGI3NDdiY2FhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272615205\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-58624795 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58624795\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-737788323 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:24:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBBc2l4d1BjNFp4MVFldEVjL1JScVE9PSIsInZhbHVlIjoidWRCMHBuVjFLYzdWU2NGWUhYUGdZREZLcll0QlJNNE1qSmVJTHR6R085bW1DVXl0MWsyRmFHSEtEOHBXdU1TZzZLYkpUb1d1SFNpeTNFR3lMNmRrcTEwNU5IQlVTNG5HVVNDM2xjMS95cUQ1bStNRmxBSGl5aVVLdE50WDc3VW9CTjhuRHErYzVTckZnVTEwNDJHald4SEJNaUR1aWIzOGZtSnNVS3k2NnhoRldYVmVOWUxNQ1c3dGhqT3M5dFZjN0MzSGF5WFdYUkJ0WmkveGxzYlE5N1hEMGxwcXRqVE1OOG1uZEtUc3RpUWtxdXdPalIvaTZUTVU2SFp3MkFpVTk4d0VlTlpSaHd4ZjczM2I4NmV0eFpnbWowcGJpdjRvejVVcnU5MzlueHdhL3VmdnRCK1BzNU0reUN0MUlHUEU3aDFVTlljU2VOSHp2elowS2tsbmFxZGQzbFU1ZXl1c1dReVhhWCt4d0NSMWRaUXV4Q0x6R0lWY0VPUmo5bWhnOXFEOElqQ1BPeVNKR3BUZDF3VlIxTE5iK2RoSmd3REhlcjdOaTI1SUJNMlRMdmtpeE1oUlg0ZG1OUVVlQTNkNjNCSDU1Y3AvRVAyV1BJWE0xY3BOeHkvRHVTKzR4d3ZOSmdOMVU3czRuQ1M4d09xZ2pNZXliOFk3MzZ2SHpzYUsiLCJtYWMiOiI0YzIxMGIxNzY4N2JkMmJlMGY2MTJlMzg3YzgzZDc2MzJjYmZjYTg1OGFjOThmNWIxYmRiZjQyZTQ3ZTUzNmMwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFscW9pZ2ZmNk8vSU11UUFTbVlJdXc9PSIsInZhbHVlIjoiSU5ETzJTNUtkRXlIMjlEVWJycEE0UjEvN2IxeFp4bncrMElZSVZjNEdNUGp0ZDBmSHp3bXVEdXpBVmVsK3pobysyU3cxbFptS1VtY09mMlZldlNLTGE4RE5XTFBwWGljOCs3N2VtRS9BWVVEaGNaYVRIaWNOVWloSGFpRmlkUGRXVVp5d3dwbjNYazdBMXhOU0VzREpQRzlweHIxRHN3WE04UlRnUDJrcExES25IV1ZIbzZBZSt1S2lZbWxndnV0dUdHRXhwT3kvVFZ6QUNlS3pTcFZEZE0ra1JGajRTTmpJalFyUjgzTEQ5N2hJUlJYMGVNQUhZMDBNT2hSeVZ3NEFrTjVBWmJlQUw2a21TZU05czhHL3Y3bVVLRkgvMXdpL1czR2hobUovelFMTVllODhteDg1bkVDdTB6WFNpSEhRekhCL0ZLQmdZWjROZTVkbmduYkovTlhqRCttNEVmeUFCaks4Z2VraEl4TEZ2N3BWTGI4eStiTUg2M1JvRjF1ZnNCczk2OURCWmV2Z1NxVWc0cC94UVBwdzI0blZqcllyWVBSaStMakRVUzk4MGw3a1VtZU1yQW5GVUNRcWxYamZoWjJ3Nyt3cVZUUis4TVpVVmtDZ3h1TkFhWjFMMTkxRmpLZ2lDRzZaU3g0Wm5JL2lQZlRuRHBYU2RRdGpxakIiLCJtYWMiOiI1MTM0ZDk4OWJjODJiZmMzNWVkYjlmMTA0MzRkYzdjNTU4MjM5ZGU3Y2U0ZDJjZDNlYzNjZmUyODc5MTNhNTQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBBc2l4d1BjNFp4MVFldEVjL1JScVE9PSIsInZhbHVlIjoidWRCMHBuVjFLYzdWU2NGWUhYUGdZREZLcll0QlJNNE1qSmVJTHR6R085bW1DVXl0MWsyRmFHSEtEOHBXdU1TZzZLYkpUb1d1SFNpeTNFR3lMNmRrcTEwNU5IQlVTNG5HVVNDM2xjMS95cUQ1bStNRmxBSGl5aVVLdE50WDc3VW9CTjhuRHErYzVTckZnVTEwNDJHald4SEJNaUR1aWIzOGZtSnNVS3k2NnhoRldYVmVOWUxNQ1c3dGhqT3M5dFZjN0MzSGF5WFdYUkJ0WmkveGxzYlE5N1hEMGxwcXRqVE1OOG1uZEtUc3RpUWtxdXdPalIvaTZUTVU2SFp3MkFpVTk4d0VlTlpSaHd4ZjczM2I4NmV0eFpnbWowcGJpdjRvejVVcnU5MzlueHdhL3VmdnRCK1BzNU0reUN0MUlHUEU3aDFVTlljU2VOSHp2elowS2tsbmFxZGQzbFU1ZXl1c1dReVhhWCt4d0NSMWRaUXV4Q0x6R0lWY0VPUmo5bWhnOXFEOElqQ1BPeVNKR3BUZDF3VlIxTE5iK2RoSmd3REhlcjdOaTI1SUJNMlRMdmtpeE1oUlg0ZG1OUVVlQTNkNjNCSDU1Y3AvRVAyV1BJWE0xY3BOeHkvRHVTKzR4d3ZOSmdOMVU3czRuQ1M4d09xZ2pNZXliOFk3MzZ2SHpzYUsiLCJtYWMiOiI0YzIxMGIxNzY4N2JkMmJlMGY2MTJlMzg3YzgzZDc2MzJjYmZjYTg1OGFjOThmNWIxYmRiZjQyZTQ3ZTUzNmMwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFscW9pZ2ZmNk8vSU11UUFTbVlJdXc9PSIsInZhbHVlIjoiSU5ETzJTNUtkRXlIMjlEVWJycEE0UjEvN2IxeFp4bncrMElZSVZjNEdNUGp0ZDBmSHp3bXVEdXpBVmVsK3pobysyU3cxbFptS1VtY09mMlZldlNLTGE4RE5XTFBwWGljOCs3N2VtRS9BWVVEaGNaYVRIaWNOVWloSGFpRmlkUGRXVVp5d3dwbjNYazdBMXhOU0VzREpQRzlweHIxRHN3WE04UlRnUDJrcExES25IV1ZIbzZBZSt1S2lZbWxndnV0dUdHRXhwT3kvVFZ6QUNlS3pTcFZEZE0ra1JGajRTTmpJalFyUjgzTEQ5N2hJUlJYMGVNQUhZMDBNT2hSeVZ3NEFrTjVBWmJlQUw2a21TZU05czhHL3Y3bVVLRkgvMXdpL1czR2hobUovelFMTVllODhteDg1bkVDdTB6WFNpSEhRekhCL0ZLQmdZWjROZTVkbmduYkovTlhqRCttNEVmeUFCaks4Z2VraEl4TEZ2N3BWTGI4eStiTUg2M1JvRjF1ZnNCczk2OURCWmV2Z1NxVWc0cC94UVBwdzI0blZqcllyWVBSaStMakRVUzk4MGw3a1VtZU1yQW5GVUNRcWxYamZoWjJ3Nyt3cVZUUis4TVpVVmtDZ3h1TkFhWjFMMTkxRmpLZ2lDRzZaU3g0Wm5JL2lQZlRuRHBYU2RRdGpxakIiLCJtYWMiOiI1MTM0ZDk4OWJjODJiZmMzNWVkYjlmMTA0MzRkYzdjNTU4MjM5ZGU3Y2U0ZDJjZDNlYzNjZmUyODc5MTNhNTQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-737788323\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2005916699 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005916699\", {\"maxDepth\":0})</script>\n"}}