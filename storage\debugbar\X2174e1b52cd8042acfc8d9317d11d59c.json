{"__meta": {"id": "X2174e1b52cd8042acfc8d9317d11d59c", "datetime": "2025-06-28 16:19:27", "utime": **********.906408, "method": "GET", "uri": "/enhanced-pos/get-next-invoice-number", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.393487, "end": **********.906424, "duration": 0.512937068939209, "duration_str": "513ms", "measures": [{"label": "Booting", "start": **********.393487, "relative_start": 0, "end": **********.848663, "relative_end": **********.848663, "duration": 0.45517611503601074, "duration_str": "455ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.848674, "relative_start": 0.4551870822906494, "end": **********.906426, "relative_end": 1.9073486328125e-06, "duration": 0.05775189399719238, "duration_str": "57.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46412024, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-next-invoice-number", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getNextInvoiceNumber", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_next_invoice_number", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2421\" onclick=\"\">app/Http/Controllers/PosController.php:2421-2436</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00286, "accumulated_duration_str": "2.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.886194, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.091}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8969822, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.091, "width_percent": 22.028}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2411}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2424}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.899789, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2411", "source": "app/Http/Controllers/PosController.php:2411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2411", "ajax": false, "filename": "PosController.php", "line": "2411"}, "connection": "kdmkjkqknb", "start_percent": 81.119, "width_percent": 18.881}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1464/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-next-invoice-number", "status_code": "<pre class=sf-dump id=sf-dump-1741597289 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1741597289\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-657956747 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-657956747\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-963533153 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-963533153\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-943305848 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127556646%7C37%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlIvZkVqMTBacHlOaFJ5TVlHYy9FS3c9PSIsInZhbHVlIjoiemdtbkVuNEM2dzE1ZWRPU3hDUmU5UzNKbzVEZEhvNXBpT2xtMUV6YWVGL1ZqNUZORmlwWDM1TERieThDT2tDSXlWUkxxZ3RxenFOUFE3cG1Pdm5xNmpGajBYL3NyVUNqbWtiZytnKzFGUUd0ZFRNa2sycVJKQ1FpYUVIMVZ3alRkWWptbngwU09vUmJwOFNyMkJ2dzgwUUFVTnU3cGpST21oMmh3RGZxb0ZaaEEreStHRmhtTkZGU2NycEg5dnZEak91ZkRWTDFXdDNIVmszR1JlZFhMb0dTTEUrOHpJdGR5elZFVUsvSEFuWHNUVUtydGFMMDNUUDMrdjRMOEhXV3laY2hqVFY3TlVKcGJiSjc4K1U0c3JPTlB1bEg5Rk5pQXhtREpQUDRCTlBaWDlVRFV3SUIzZXpmS1ljd2dNWDJPT2crRFhla3pDV3Z2ZEJaQWtTTWZtV2F0eElsVGo5R2NOREZlaFc2QnFhbTVLaGw5aW5WTGR5TlNleHgwa29SM2lvekJVelhNMEl4Zks4ZncvU2lpUWJBVVlmN2RTN2FJNWcxWllNeDZWblFqWGUvZkJaQk1IeFFxRHltSzVPcTZoekVJT3ZObVhlMWt0c29YbkdWMFQ3bGFVaHltZW5OcFFSMkVWVUs3akoraXRaMzhCU3NmckEvWnc3QmpVU2UiLCJtYWMiOiI0ZmQ4YWI2NjNiOTZhZDc4NTc5MWFmNDk0ZGQ1NGY3YTE3NGI5YmQ2OTU5YjNhYmU1N2Y0YmZmMWJlNDdmY2M1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlB1REgvVklHTXo1cHNOMVByQUpMbEE9PSIsInZhbHVlIjoiSFBSa0I2citnVy9HbFpYc3lZbDBBblJIcjAyMGdtN0hMcjU3Q0o1R2piVnpPM0FzNkRvYjBuaTQ4ZWV0MnBWZzdaMUQyQlRwVm83cFpMZWlvT2hGZ04xRldGbnByNlNnVGJxUXdlK0p5aFIvWWdMMnZoK2dTaHRWZUFWalFUd2UrdGtxL1FQUklydUNxWDJOU1FRazByM2R6OGpRQkkrWU9iK2wxNExBYUxRNDBDc1M4TWtPQ0NOK1NZZWtvS2RqUDc3ZG9nT2NpZ3pSZ3o1MUlDUGlZRkF6TXc3MWcwS3dBemF6MlNCT3RlSGxFL3VJUndXQ2poeXV2UklkUjliOC83Y0NMOGs4dFVwOGEyUktuNFE3amRZdjYwUVQrZ0wyanJRWVlEUERjekpqNGpsRnZkWnlSNU9PZE1DTDA3RVpDZ2hoVzlteGRPOTNXTzdYVW1lWTMxUnZYL0pTRnBZTXQveGZVSCtYMS82YzhJQVRQVmtxZ2pJeWQvK2dFQzh0ZnM5THhLcmpFM1Jna2RtdTM2RFViaE5vNk0veUwwOHBCVndhQ0NBYnh1LzJBaVZOMlRSeG9idVRQV3RVcCtJT2hUVWQ4aXhGUDRoY1VGSWxaSWoxK2c4dEhrNXJJTlhHK21lNUY0NXhpczNZNFlBSjBmdC9oUXIzTmVXTS8raEgiLCJtYWMiOiI5MjNkYWFhNDNkOTlkNDY3NGQ4NDQ1YjIwYTk0ZjE4NjQyYzM2MWFmNDdmODk1YjIxYTk5YTlhOGI1YzJhMTk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-943305848\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-562106588 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562106588\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1113772583 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjB6LzhQcVhjTE9rTE4vZWtvMEhhV1E9PSIsInZhbHVlIjoiL1FhSlBFemNUbmNwU2R1QkxIclRRU3ZXTlN4ZFdGZlR5SGMxRmRzT3JhOThEbXBXODJhU004UHkrNitVZEZzNlpGRXNxWDRYS1VRSGJCOXNUYWw3ZFZJYkdBWUdZK3YxYlMwSm0wczRJQnZRK1MwUjdvTHhWTnE0MGJrS1o3VWtsMjJYVVNTQmpVL3REUUxpNytiWnJzSGN1Y1ZEYSs0OW84dHR1RFgxVjBUZ0FRNk5TdE0xUkpoMksyWlJMUjFEUmYxTE1oMDhaUm1UT0tVR2JJTnJRUnBZSVVta3dRTDBoYm1KYmxGY1RHcFYyL25UVkJrQkhWVTJBNTZ2Qm1GR1laSSswUk9MTzFaTWQrLzMyNW5PL0VUdWZvNVBZZjNNTU1FKzVzU21RcDVKZUoyZWpIc2VlbUoyQjUvYnZpeDhTUVdybjRMUnRLODhocWNWSWJTVUNndjFBUVlQK1gzUjBFMUV1L01aWnBldFdtNTI4bkpMSzQ3QVJNdjhkZkdrS3dINnJCYjhZSDB6TXhjRk1SRTBqUWJvVmRmSDJmMGxTbDEvcXdKTndGYjg3a0s3TW9xbGVzbWhCK0VQNmgwd0x3UjREKzlQRmR0cG11TEl3MC9DS2tReC94bWFXNUpINFg1RlI2YkhpMDkrSktrcnh4NTRyQlZVZ0JNRVpMb2EiLCJtYWMiOiI3MmQyMTI0Y2ZlYTgzM2E1ZjAwMDI4NzI5YmY5NTBhN2Q1NDYzYmRjZjU4YTI0YzY1MDhhMDYwMWIzY2FkNDZjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlE2RzdUbTJ6aVMyekNTWjJnMEJER3c9PSIsInZhbHVlIjoiTSs2cFVXLzhScXZ3Uk0zaFpKRzJsMUdFaDQ1bU0vSDdKcFZEc0NXZm1WbmtTdkVCYy81bklZYVpadDdFUlZ1aDhTNnU5TVlFdVhSOGRJSk5KR0wwV1c1bFg0cUpvRVpUTzdzS1dscS9VT3NMU2lNK2Y2T0Jod1BDa01udm1VREROMUdTb1lmaEdrcnVrNzZsbHBGS0ZYdjVZS2FjU1N1c1lmYWJZbHk0YytiOFdxL3NWM2JZNmdrbk1aYXpZOGsyamV3ZmE5ZUtFbXY3OUIyQlhjQ2NrQks2TjcyQ1FUWWR0MHJPcFN2MnRXOFg4UWdGaDM1RloxYVhzd01jY29YWGYxWnJYUFdEdE5TelRpWVFacW9ORllvTU13cmdUSUFuK0svTkxZMVhhNXpwdjNnelVuYjFHNlhreTlEUlpvZk12VkV6Sm9SMUpEM3NIYTZicy9sVlpGTTF3U2N0em4xS0xabzhTOVBJMFdDaVBGR01XbGxZbWJnZnEyUjN6UmY3ckNFNzVLNW5HeUsrKzY3dWFzdTRWK1lHV1Vib3U1VmowSENRb3F0czRUbkNxU0V3TmJocS83WUgyTFJZcy9maUtnZDRyWms0TnVoSkFZYlBqSDdwenAxc1RuVzMvTElRSy9jbi9wK1VyV1pWMEE4M1RkUDJmaFhWUUY1ZFVabGoiLCJtYWMiOiI3Y2JlZGMyYmY0Y2EyYTEzN2I3ODRhMzNmZWY2ODI1YWExZjQyMmE2ZjY4N2FiNGFkYjM4OTE0ZmVmOGQ3M2I4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjB6LzhQcVhjTE9rTE4vZWtvMEhhV1E9PSIsInZhbHVlIjoiL1FhSlBFemNUbmNwU2R1QkxIclRRU3ZXTlN4ZFdGZlR5SGMxRmRzT3JhOThEbXBXODJhU004UHkrNitVZEZzNlpGRXNxWDRYS1VRSGJCOXNUYWw3ZFZJYkdBWUdZK3YxYlMwSm0wczRJQnZRK1MwUjdvTHhWTnE0MGJrS1o3VWtsMjJYVVNTQmpVL3REUUxpNytiWnJzSGN1Y1ZEYSs0OW84dHR1RFgxVjBUZ0FRNk5TdE0xUkpoMksyWlJMUjFEUmYxTE1oMDhaUm1UT0tVR2JJTnJRUnBZSVVta3dRTDBoYm1KYmxGY1RHcFYyL25UVkJrQkhWVTJBNTZ2Qm1GR1laSSswUk9MTzFaTWQrLzMyNW5PL0VUdWZvNVBZZjNNTU1FKzVzU21RcDVKZUoyZWpIc2VlbUoyQjUvYnZpeDhTUVdybjRMUnRLODhocWNWSWJTVUNndjFBUVlQK1gzUjBFMUV1L01aWnBldFdtNTI4bkpMSzQ3QVJNdjhkZkdrS3dINnJCYjhZSDB6TXhjRk1SRTBqUWJvVmRmSDJmMGxTbDEvcXdKTndGYjg3a0s3TW9xbGVzbWhCK0VQNmgwd0x3UjREKzlQRmR0cG11TEl3MC9DS2tReC94bWFXNUpINFg1RlI2YkhpMDkrSktrcnh4NTRyQlZVZ0JNRVpMb2EiLCJtYWMiOiI3MmQyMTI0Y2ZlYTgzM2E1ZjAwMDI4NzI5YmY5NTBhN2Q1NDYzYmRjZjU4YTI0YzY1MDhhMDYwMWIzY2FkNDZjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlE2RzdUbTJ6aVMyekNTWjJnMEJER3c9PSIsInZhbHVlIjoiTSs2cFVXLzhScXZ3Uk0zaFpKRzJsMUdFaDQ1bU0vSDdKcFZEc0NXZm1WbmtTdkVCYy81bklZYVpadDdFUlZ1aDhTNnU5TVlFdVhSOGRJSk5KR0wwV1c1bFg0cUpvRVpUTzdzS1dscS9VT3NMU2lNK2Y2T0Jod1BDa01udm1VREROMUdTb1lmaEdrcnVrNzZsbHBGS0ZYdjVZS2FjU1N1c1lmYWJZbHk0YytiOFdxL3NWM2JZNmdrbk1aYXpZOGsyamV3ZmE5ZUtFbXY3OUIyQlhjQ2NrQks2TjcyQ1FUWWR0MHJPcFN2MnRXOFg4UWdGaDM1RloxYVhzd01jY29YWGYxWnJYUFdEdE5TelRpWVFacW9ORllvTU13cmdUSUFuK0svTkxZMVhhNXpwdjNnelVuYjFHNlhreTlEUlpvZk12VkV6Sm9SMUpEM3NIYTZicy9sVlpGTTF3U2N0em4xS0xabzhTOVBJMFdDaVBGR01XbGxZbWJnZnEyUjN6UmY3ckNFNzVLNW5HeUsrKzY3dWFzdTRWK1lHV1Vib3U1VmowSENRb3F0czRUbkNxU0V3TmJocS83WUgyTFJZcy9maUtnZDRyWms0TnVoSkFZYlBqSDdwenAxc1RuVzMvTElRSy9jbi9wK1VyV1pWMEE4M1RkUDJmaFhWUUY1ZFVabGoiLCJtYWMiOiI3Y2JlZGMyYmY0Y2EyYTEzN2I3ODRhMzNmZWY2ODI1YWExZjQyMmE2ZjY4N2FiNGFkYjM4OTE0ZmVmOGQ3M2I4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113772583\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1636694321 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1464/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636694321\", {\"maxDepth\":0})</script>\n"}}