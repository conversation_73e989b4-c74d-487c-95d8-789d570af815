{"__meta": {"id": "Xb70bdd28592a196c0a96518ce3cb572b", "datetime": "2025-06-28 15:04:40", "utime": **********.09945, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123079.683738, "end": **********.099464, "duration": 0.4157259464263916, "duration_str": "416ms", "measures": [{"label": "Booting", "start": 1751123079.683738, "relative_start": 0, "end": **********.02023, "relative_end": **********.02023, "duration": 0.33649206161499023, "duration_str": "336ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.020239, "relative_start": 0.3365011215209961, "end": **********.099465, "relative_end": 9.5367431640625e-07, "duration": 0.07922577857971191, "duration_str": "79.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49496056, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028539999999999996, "accumulated_duration_str": "28.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.05192, "duration": 0.011810000000000001, "duration_str": "11.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 41.381}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.071713, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 41.381, "width_percent": 1.402}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nAND p.warehouse_id = 8\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`warehouse_id` = '8' order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.07428, "duration": 0.016329999999999997, "duration_str": "16.33ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 42.782, "width_percent": 57.218}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-183013890 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-183013890\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1049025290 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049025290\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1012788587 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1012788587\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123070655%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndmUXEwcEZ3bzRWeGQxdnVSSEJxd1E9PSIsInZhbHVlIjoiazh6enhiQkprQjRBYXV5VG4waW9CTml2QlE0MUtvNEFXNVVvREVYbHhhdjl2azJHOW9rL3MyaTRubHUxNnF1eWhleC9rek1BWFdVWkNmcTh6OTVJL0R2U1djOStYN29XMTF5V016eVlGVWZCajNFakUvVmFTbU82aTJKMmNRZTZSUzBQRXdIWk9SaTJYRUFqekJvWENDYUFyQXJ6V1Z6TlErajRXclIxL3lxYnhGaVJBUE1paXBuOHBFNkNlSWFSVnlzQlc5QTNodDA0bUx1V2k0eFR5TUw4UnBsT2VWcU1hdEZEemtISjB3OVFyUUV1R0xFbWxnQURjM0kzQVdEaENFUnA0TEFsazVMclNNNVppVDRLTjVvNFhlUnBhUTZ0OWFiZXcrWEt0dTNWWjUzeWY1SkdTZ1U2MVp4M3VJL3BFc0RQckRxWFVPaGx5N3p2MGNjTTdJaTFnNFh6cVhnYlpiR1FtUnE3Wmpmc0tXbm5uSkpOb0p5V0tXWFFmTFNpVE9UblVlNmFHRG5yVnRCNzlTSkQ2WjRuZ1l0YndzOGE5Qk1LazBna0xscVlKQUt6a0g3N3d5MUVocUtzcGxMb3NaSVE5VktPdGV6N0JyS29ORUtzcjRGVE1XbUtZbVRNM0luVmpEd1pyWmFKcnhxcU1La0hJTHVrUXhYdEM1emIiLCJtYWMiOiJmYTgwMzM1NDQ0MzY1M2UyMzVlMzAxZjlmYmJhZTVjNGZhNTc3MzEyZjE0OTcxMDIwYzNmZDdkYTE0NzYzN2Q1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJ4eVZQUnNORXFLYTBFWjVOSVhxaVE9PSIsInZhbHVlIjoid29rNHZiN3BCR0hpVXMxOGNyeGFpNEdqb0Z3N3UxM0d5aHNuVTROdkptanZ3WHo4Ry9sckZMTmJkeDFmajNkMVpMMy94dlNCeFZ4TlBpZ0tGd1IzeUZtc3drSE53Y2xTcTVxUm85SDZFTUo5dkpMTTNEV3JFa3lYUmlwUzZLTk8zSDRSbGhqWG4yU0pJcnk1L2R3ak5LRTMwOWlqU3lmSVpJV2VueEwya091TDY0cTZTcnpTaVo5bVBnMWtCR2J4cXBiUmQybGxLNkRVVytnM1lzOG5IUmRFL21LL3AxaHIzaXVTYkpFbTZiUlN3Sng2VWhzSUl3T29oNjViLzVjQjFFRW5kZHVKaFYxWExqTStocFV3MmZER1lFZ1k0dTk0bGpiUzR2ZU1qUGt3UnVDQ1pwdkFXRExLU2k1NHAxbGNWMURWYU4zeTJuQmtXTnBVY2tJT25RKytvQ2VjR2FidE82TWoySUpxTFg3eXlTcjJrbjI1Y2FZbTdrRkNxSDJWc1NpYzVhVG1iTVBObmV5aVhxMU5oK1ZHbnRIQ0tuZE5PeGw1aTA2U1pxR2x4UkJNZGpKcjBUM1N2SDBFUWU1b1VGTGxnMFJyTG00V3EvL2UrMUlpaS80bG5MY3Z3MnZCcTN2TzZlK3UwYzJZWmQ4aDl6RG5aMHBNUEh2d1BaaWgiLCJtYWMiOiJjMThhNDFmOTdkMDBkNjVjNmMxOGQzMzJlNTQ3ODA2NTFkZWFlZGY2ZmY3MWJjNDMyNDNkZjYzYzJjODQ3YTJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-695695380 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-695695380\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-261610116 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:04:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik4wS0NJQlUzT2h6MHpJWjRnaGtKcHc9PSIsInZhbHVlIjoiN1A0eDZRVEh0SmdlckRiRkQ5alJUKzJESkNFNnpiMmF6clNFamtlQStKa2d0ZkJyaTRveFhOY2xBYi9EU2JnZjBveUQydVI4Z3JxaXVvc2VYdVl0NkUzYXg3MjR0SzJrT1B5bzJoUTVUbUxpYTdqZnB6UjFEUDVUamE3cVJMdmdkWDRYenU4QlVnU2d6R203aU5xZVdkVElWdTU0RkxRRlRjckFXaFVqRnBTK0xkRmxZeFp3TXIrSFBqVEhzYVR4R2xPYjNYSUhaYTZqZWdzOEl2bjkzZ05VNngwZVpEd0R4VmNGa0JqcEZTQ2JjUEZBN3lNeWJyTkVXQStuaDhXVm1nYTc4cmpmK1hoVml5b09MWldZdjJPakFMaGZlVUdRckVhaXNYRDFwR2NtWm1jY3NNTVpEYVZCT045ajB1c2pqdHc4L25KYUZ3OVh6T3VKREthUjZyN0pJSUtiVytaMm96Q21weEhWamNtY1d1WXA5ZEpuSTU2Z0RDN1M2S2R1c1dhejlIdVRJUUdjb2RQRmxUTTFXSUhFZTV5d0xPb1I2a3k0Mlg2Yk9zcktSdy90K2xydmUzcm5TckUwWWJyMDV4djJQNmd3LzlnKythaUNUVFczN2pDRk9mS3F1cmIzYUdDalNXSDBNYlZac0RlUlNJTjZQeS8zend6OUhCT0IiLCJtYWMiOiJiNTE1OTViYjkyZDMzMGE3ZGQxNzZjMWIzMTY3ZWEzZmRlNzczMmRiOTQyMzRlNzY1MDY0NjIwYWE5ZTRkYTg5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpCSXgva3gwSTFLRUY3bmVQUXFpaFE9PSIsInZhbHVlIjoiZFlkZzlVU2lEaElXSWlBakRnQTlXOGZOai80VFQyVkRMeHdoZmhpQ1d3WnVFUFhUQUxCTkRpL00ya0ZrRWNwMWJXdFhSekNYY2NaYkdZbnJsSG05RUswYWlkZGNLZllQZml6Um9CL01aMmZoWllwN3R1RWN5N0k4eS83VVM4VVR5UFJnNWJmT2FLRi9sSStkYjhWMXVILzlzeWFIeTBTQkh6dFhSSVZmdXRrM3h3M290QllkVFB2TGRZZEw2UnRsVGlYbHd2WXQ4c2xERXVZOUYzZWhkN3pQS2h1TUFSSVVsaFNTSG1wNjJxTmZjUUtyMUpRQmxwYnkzMWhNYzlGWnZEa2xZVzh1TGE1bHpKRlNGTGVXV051b0tHZnJmN2ZkZkpPQ0o2Z3pJOHdDZktiT3lIdEV0VmhkclI5ZXBCeDFCL2tLZ0N6ZkFxcXpjYmg2VEg4Nm9QYm1OUHNucnVHbDR4MVRYbnBOUlY1WW9YTGxhNXI2WUMzU2dqWWZwVFM1VWczSzBVb0FCZXNhdDd5WXZkbkpBVjl5Q0NhUVREeUxRcGYrVmRmN3pDREQvalFiQmswdWR3NzliZy82S00zbVVncXJiRmpWaTdYSjNJNXpaeXNGMlQxWklEYmpqZG1Bd1JSNGJ2UlBPUlRxRDVHeW9FOUJGT1prc29MN3lxNXgiLCJtYWMiOiJmODM0OTEzYWZjMjE4YWQzMzdiMDNiOTM3MDQ2MmFjNjUyNjUxODgzN2ZjZmIyZTQ1Nzc0NWQ1NjgwZTZkZjM1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik4wS0NJQlUzT2h6MHpJWjRnaGtKcHc9PSIsInZhbHVlIjoiN1A0eDZRVEh0SmdlckRiRkQ5alJUKzJESkNFNnpiMmF6clNFamtlQStKa2d0ZkJyaTRveFhOY2xBYi9EU2JnZjBveUQydVI4Z3JxaXVvc2VYdVl0NkUzYXg3MjR0SzJrT1B5bzJoUTVUbUxpYTdqZnB6UjFEUDVUamE3cVJMdmdkWDRYenU4QlVnU2d6R203aU5xZVdkVElWdTU0RkxRRlRjckFXaFVqRnBTK0xkRmxZeFp3TXIrSFBqVEhzYVR4R2xPYjNYSUhaYTZqZWdzOEl2bjkzZ05VNngwZVpEd0R4VmNGa0JqcEZTQ2JjUEZBN3lNeWJyTkVXQStuaDhXVm1nYTc4cmpmK1hoVml5b09MWldZdjJPakFMaGZlVUdRckVhaXNYRDFwR2NtWm1jY3NNTVpEYVZCT045ajB1c2pqdHc4L25KYUZ3OVh6T3VKREthUjZyN0pJSUtiVytaMm96Q21weEhWamNtY1d1WXA5ZEpuSTU2Z0RDN1M2S2R1c1dhejlIdVRJUUdjb2RQRmxUTTFXSUhFZTV5d0xPb1I2a3k0Mlg2Yk9zcktSdy90K2xydmUzcm5TckUwWWJyMDV4djJQNmd3LzlnKythaUNUVFczN2pDRk9mS3F1cmIzYUdDalNXSDBNYlZac0RlUlNJTjZQeS8zend6OUhCT0IiLCJtYWMiOiJiNTE1OTViYjkyZDMzMGE3ZGQxNzZjMWIzMTY3ZWEzZmRlNzczMmRiOTQyMzRlNzY1MDY0NjIwYWE5ZTRkYTg5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpCSXgva3gwSTFLRUY3bmVQUXFpaFE9PSIsInZhbHVlIjoiZFlkZzlVU2lEaElXSWlBakRnQTlXOGZOai80VFQyVkRMeHdoZmhpQ1d3WnVFUFhUQUxCTkRpL00ya0ZrRWNwMWJXdFhSekNYY2NaYkdZbnJsSG05RUswYWlkZGNLZllQZml6Um9CL01aMmZoWllwN3R1RWN5N0k4eS83VVM4VVR5UFJnNWJmT2FLRi9sSStkYjhWMXVILzlzeWFIeTBTQkh6dFhSSVZmdXRrM3h3M290QllkVFB2TGRZZEw2UnRsVGlYbHd2WXQ4c2xERXVZOUYzZWhkN3pQS2h1TUFSSVVsaFNTSG1wNjJxTmZjUUtyMUpRQmxwYnkzMWhNYzlGWnZEa2xZVzh1TGE1bHpKRlNGTGVXV051b0tHZnJmN2ZkZkpPQ0o2Z3pJOHdDZktiT3lIdEV0VmhkclI5ZXBCeDFCL2tLZ0N6ZkFxcXpjYmg2VEg4Nm9QYm1OUHNucnVHbDR4MVRYbnBOUlY1WW9YTGxhNXI2WUMzU2dqWWZwVFM1VWczSzBVb0FCZXNhdDd5WXZkbkpBVjl5Q0NhUVREeUxRcGYrVmRmN3pDREQvalFiQmswdWR3NzliZy82S00zbVVncXJiRmpWaTdYSjNJNXpaeXNGMlQxWklEYmpqZG1Bd1JSNGJ2UlBPUlRxRDVHeW9FOUJGT1prc29MN3lxNXgiLCJtYWMiOiJmODM0OTEzYWZjMjE4YWQzMzdiMDNiOTM3MDQ2MmFjNjUyNjUxODgzN2ZjZmIyZTQ1Nzc0NWQ1NjgwZTZkZjM1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261610116\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2133631299 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133631299\", {\"maxDepth\":0})</script>\n"}}