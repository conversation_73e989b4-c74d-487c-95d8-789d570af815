{"__meta": {"id": "X854764e2cd46bb7b6bbed138d4c074d3", "datetime": "2025-06-28 15:35:05", "utime": **********.267232, "method": "GET", "uri": "/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.458199, "end": **********.26725, "duration": 0.8090510368347168, "duration_str": "809ms", "measures": [{"label": "Booting", "start": **********.458199, "relative_start": 0, "end": **********.904417, "relative_end": **********.904417, "duration": 0.44621801376342773, "duration_str": "446ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.904431, "relative_start": 0.4462320804595947, "end": **********.267251, "relative_end": 9.5367431640625e-07, "duration": 0.3628199100494385, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53507968, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.index", "param_count": null, "params": [], "start": **********.023473, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/index.blade.phppos.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.index"}]}, "route": {"uri": "GET pos", "middleware": "web, verified, auth, XSS, revalidate", "as": "pos.index", "controller": "App\\Http\\Controllers\\PosController@index", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=37\" onclick=\"\">app/Http/Controllers/PosController.php:37-109</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.009569999999999999, "accumulated_duration_str": "9.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.951991, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.287}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.964822, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.287, "width_percent": 6.897}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.981637, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 32.184, "width_percent": 9.404}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.98417, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 41.588, "width_percent": 2.717}, {"sql": "select *, CONCAT(name) AS name from `warehouses` where `created_by` = 15 and `id` = 8", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9907541, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "PosController.php:50", "source": "app/Http/Controllers/PosController.php:50", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=50", "ajax": false, "filename": "PosController.php", "line": "50"}, "connection": "kdmkjkqknb", "start_percent": 44.305, "width_percent": 7.21}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'kdmkjkqknb' and table_name = 'customers' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.993676, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "PosController.php:57", "source": "app/Http/Controllers/PosController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=57", "ajax": false, "filename": "PosController.php", "line": "57"}, "connection": "kdmkjkqknb", "start_percent": 51.515, "width_percent": 23.929}, {"sql": "select * from `customers` where `created_by` = 15 and (`warehouse_id` = 8 or `warehouse_id` is null or `warehouse_id` = '')", "type": "query", "params": [], "bindings": ["15", "8", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.997458, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PosController.php:69", "source": "app/Http/Controllers/PosController.php:69", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=69", "ajax": false, "filename": "PosController.php", "line": "69"}, "connection": "kdmkjkqknb", "start_percent": 75.444, "width_percent": 6.165}, {"sql": "select * from `users` where `warehouse_id` = 8 and `type` = 'delivery'", "type": "query", "params": [], "bindings": ["8", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 72}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.999408, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "PosController.php:72", "source": "app/Http/Controllers/PosController.php:72", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=72", "ajax": false, "filename": "PosController.php", "line": "72"}, "connection": "kdmkjkqknb", "start_percent": 81.609, "width_percent": 3.239}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 559}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.00231, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PosController.php:559", "source": "app/Http/Controllers/PosController.php:559", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=559", "ajax": false, "filename": "PosController.php", "line": "559"}, "connection": "kdmkjkqknb", "start_percent": 84.848, "width_percent": 3.657}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "pos.index", "file": "C:\\laragon\\www\\erpq24\\resources\\views/pos/index.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.24791, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 88.506, "width_percent": 7.21}, {"sql": "select `value`, `name` from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4097}, {"index": 14, "namespace": "view", "name": "pos.index", "file": "C:\\laragon\\www\\erpq24\\resources\\views/pos/index.blade.php", "line": 6}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.250357, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4097", "source": "app/Models/Utility.php:4097", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4097", "ajax": false, "filename": "Utility.php", "line": "4097"}, "connection": "kdmkjkqknb", "start_percent": 95.716, "width_percent": 4.284}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 4, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-248590621 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-248590621\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.989129, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-93691957 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-93691957\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.001636, "xdebug_link": null}, {"message": "[ability => manage delevery, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1889348819 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889348819\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.254685, "xdebug_link": null}, {"message": "[ability => manage discount, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1097793555 data-indent-pad=\"  \"><span class=sf-dump-note>manage discount</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage discount</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097793555\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.257582, "xdebug_link": null}]}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos", "status_code": "<pre class=sf-dump id=sf-dump-1941143997 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1941143997\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-643829478 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-643829478\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1828747266 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1828747266\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1066634003 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751124481231%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZzNmlWbTRCT2pNYVgvUVVyMWYrVGc9PSIsInZhbHVlIjoiR21WU0VMUXhoaDAzUjhCeXhiWE5QRU5pY0k0ZVBiWnFkYzBMUFB4ZjREa3poQWhjczI5bDZ1K3ZheE83MlVERStWL0dNb3kyRmY1YkRwZWlaUi8vZk1FTjdmWVEreng4N2l4QzJvdkI1STFmSHA2bTBvd1VpQmFRSnlVN0t5R29DVFlWcTNkeTFmcjFhOXhjUDBWU1RGdThoMmh3eFY2ZTVON1RSZUFPTTFwbDlNUkl4SHM0NTgrbk1TNWlCMEp5OGlyZTVNSHpTN3dUaGpyK3hJQjFMMzJwLzNQWi9FTEJmbmMyQjg3TmNUTmZ3YjlUL2YvQnUyWGtQVGIyOExCN05mSlNDVHlFaHB1TE5kQ3pjMjZHVXE3b1o0SHN1WjBMNGJyd1d5SElhMCsvcU16aWVhY251ekV2RHk5V0hWWS9GbXJzUUZ1ZUE1WnNXRitLaEhJSDRRVTV5MHp1cUxRQ1ZBVVhhWnRhL2lQcDVhVHVTZmVtaFkvU2hXZzFSWHZ0SGpsUlMwUm4vOGRlVC91eWNIK1o2bldEUmpuOS9BS1BqaHEvY2QzbWhpU29LRmVpeGNYOGhqejR4eEp2QkQ0WFoxVC9hcFlUKzNNc0NFQzZCM3hTeU5VWFlYWm1QOWdLdnJDWjdSTWNoY0QxNTRDOEtYRXMvTXVOdXQwTTRrMHkiLCJtYWMiOiJkZWQ1M2NkYzY5ZmM0ZmYzZjIwZWFkYjU3MDIxN2U4ZDM2OGEwNzdmNzgxYTY3ZDMwMzZmODg2ZmMyOTQzZTg0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InEzZXZEeGwyZXk5YTFPR1JqY3JRcVE9PSIsInZhbHVlIjoiU3U3RWw3amI1amhkZWxBdnFJV2pIS2tjcGcrR1A5WktXOW12QXNxWW1BK0VUbVhDQkhTVlBtcFVQK1FLZ0FzVGZaUkZuSDN0NmpzaEpQcU5OMXFYWWNWL2cvdG5XMjVxeDdXSXlyWmdrZHgxT3ZIVmRPM1NLQTFBeVhlSDlKNzBXZ0t1YlZYYit4VHU1ODhHNmw0WERhQkszSExJUUUxR2hlV3VFTnpoeFE2QmJKdFUybHZCeHRXUHFGRit6ZW5DUHNjUHpOSkE0Rjh5NDV3b0V0TzR5RWZyc0lQTGcvRkJVZmE3Mk9WaGR2ZnRCTU5XTHdRek95aVFpT1B3QXg0WlcrMU93WHhsWTh5L3h2ZmhnVzl1MGZ6TVhVMWI1VlVvUVRKMkp2RDhoSVQ1aVB3SFROVzZWRU11ZVRjOEM4WUtCY3dxa1lta2d3empDQ3pKK0gzY24yTXlhQk1wMk1QVzI2dmk1OXlnRTZPYkJqTmRGZUZOdkk0WnNRYnVPZmJMNkM5b3NjMWZoYW1rT0hJNDFVenNjckhuN2F1NkZPL3BSbFY0NDBvaVNGa1JwUVZkd2FwZ0hJYUNZTFVxbzVubC81MUhKTmcxN2VWRDZIR0RGZFVjcEd5L21LRmhjWkgzeFJsM1ZwbDRUNm5WK0h1bnZlY05MbTJuYkp3OEQ3bnIiLCJtYWMiOiI1MzNhODVkYTk2NDA3MmMwY2EwMTU4NDZhMDQzZjQ1OGFiMTc4NTZmOGE2YjZlM2Q5M2Y3OGRmYzExOTA4YjBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066634003\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-864987700 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-864987700\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:35:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRyOURTd1dYZlZjcTBUOFVOL2xTQlE9PSIsInZhbHVlIjoiVHhlTXpYd2FDbE8yYktZSzJUM3A0U3Jwc1lNd1dwdWMzWTl2WlRxbktFZkVlN2VtWEFLeDhUaEdtMm9CTlJSZXQzWE0zcHh1YmxTTlp1M2tVdVBXVks4bUJmRlJ2K0lYSUNVQVJMbjkxeFFmUmJpS2JJeDhDZkMrcnpQZEtTY3NNZ0lXRmN6MlN5bG43WFVBeE1KYWk3S1J1WTduVDE3c3JLSGZ5c0VNVWtlbklxRGFlY1Q3eFU5Z0FEVzZsUkVuL1g3SmVHSzhqNTcrN2kwT2VyUmFENE81eVRabGZuN3lVQ2lVTS91dDE4NjF5UXhEQTJobkpOQTNkZ0FyYXR3andZUzZQMzV0UVI2UHg2M0NtT2FkRUorTDV6T3JVRlNKaWhuZ1RoR1NHVGs0YW1EN0Q3eWxkcnhjU3RqN3RyZDhoTEZUNkpYekdQckh4Z2VleS9KNEJxTlZENWl0M21NTDQ0d05HdTdhVHNFQUhJSXd3cU5RbXptemxvTnAvNVpwZ3hLT3grN3FsNWxkaVRmVjNIQUZSQlZWUEhRck9pbC8vVHVLN2dEOEI0UVcyaVNIOFZCNEU2WlFTTmZJV01uT0gvaldvM3daZ2hhMWdrS0VONlArc3crVGZ2TUhDQlpZVyswSmtpOTBxRzhaZk0vWnB0VXA2aXd6c3picWdGSy8iLCJtYWMiOiI4MDNkZDVlODZlNmUwMjFhYWZhOGIyODg0MzJjM2Y0NWNiOTk1MDA2NWEzM2NiYWJkYmVmNjQ5ZTQ4OGRkYWMxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:35:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkhvN1B5cHN4Q2Vza3BCVHRFZjd3SEE9PSIsInZhbHVlIjoibVBHS2pEbEIxSWE3eG81R1E3QlZSazJkSkpoYWFxOXp2d0VjN2F4TmdyQjFablVqRDB1UzVGZGNVYzlvZjNnTUl4TzVNV2s4bEhkS1JkWjlkSVUwMFBGakZDYkk2K3M3aTdFQnZDbGJQRXRza2E1dklCUlhHQVRVWCtnSFBDbmg0QWt6VFVvY0FhSmhFUzZJUzFNcWhQamxGQnFLNkxtckxUZnptS0RXMVJaemoyUk1PVUZoM1ZwR2NXR0xJV0hodzVLeEJYNDdYNHc4eUNDT0dET2pGQVY5Y1QyeTNvQ3BxYmtJQlBKeC9jNDlsVVZtcHlHSGttdUF4c1k3ZWhybklhZ0gwaTAvQTlSUXRBR3NoM2pid0luZTN6UWppdnNFc01vMTJyajErKzRSOXZqUFNOemRtVGZDTFpVSzJkMDdSZEcyL0MybFg0cGhYQkp3SENmMzV1MWxmWitCcTZiYlBURjBOSVFPaFg4THBodUlSVE8zdEF4Y3NOdlZGYnpYQitHczZteXc0U3p4NXpxY2FqYnE4bmE1dVBaUHRMdTRBN3lTb1BmOERyd0R1OHhXYkVzTXUwUHh1NnFYUDRWQWM0UzRER0VRcTczTG5OWXBMMVhBN2t4U0Z5MDcxL3hQUUxsRElpdk5wVkIvZDMxdXRIdlJRVU5GSnY5WEtoamIiLCJtYWMiOiI2ZjY1NTE0OGZmNWU2MTczNDEyYzkwNTc1MTk1NDEzYWQ5ZWYxYWFlNTYxNGMzZGNiZTE4ZTgxMTA2ZGRhNWU1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:35:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRyOURTd1dYZlZjcTBUOFVOL2xTQlE9PSIsInZhbHVlIjoiVHhlTXpYd2FDbE8yYktZSzJUM3A0U3Jwc1lNd1dwdWMzWTl2WlRxbktFZkVlN2VtWEFLeDhUaEdtMm9CTlJSZXQzWE0zcHh1YmxTTlp1M2tVdVBXVks4bUJmRlJ2K0lYSUNVQVJMbjkxeFFmUmJpS2JJeDhDZkMrcnpQZEtTY3NNZ0lXRmN6MlN5bG43WFVBeE1KYWk3S1J1WTduVDE3c3JLSGZ5c0VNVWtlbklxRGFlY1Q3eFU5Z0FEVzZsUkVuL1g3SmVHSzhqNTcrN2kwT2VyUmFENE81eVRabGZuN3lVQ2lVTS91dDE4NjF5UXhEQTJobkpOQTNkZ0FyYXR3andZUzZQMzV0UVI2UHg2M0NtT2FkRUorTDV6T3JVRlNKaWhuZ1RoR1NHVGs0YW1EN0Q3eWxkcnhjU3RqN3RyZDhoTEZUNkpYekdQckh4Z2VleS9KNEJxTlZENWl0M21NTDQ0d05HdTdhVHNFQUhJSXd3cU5RbXptemxvTnAvNVpwZ3hLT3grN3FsNWxkaVRmVjNIQUZSQlZWUEhRck9pbC8vVHVLN2dEOEI0UVcyaVNIOFZCNEU2WlFTTmZJV01uT0gvaldvM3daZ2hhMWdrS0VONlArc3crVGZ2TUhDQlpZVyswSmtpOTBxRzhaZk0vWnB0VXA2aXd6c3picWdGSy8iLCJtYWMiOiI4MDNkZDVlODZlNmUwMjFhYWZhOGIyODg0MzJjM2Y0NWNiOTk1MDA2NWEzM2NiYWJkYmVmNjQ5ZTQ4OGRkYWMxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:35:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkhvN1B5cHN4Q2Vza3BCVHRFZjd3SEE9PSIsInZhbHVlIjoibVBHS2pEbEIxSWE3eG81R1E3QlZSazJkSkpoYWFxOXp2d0VjN2F4TmdyQjFablVqRDB1UzVGZGNVYzlvZjNnTUl4TzVNV2s4bEhkS1JkWjlkSVUwMFBGakZDYkk2K3M3aTdFQnZDbGJQRXRza2E1dklCUlhHQVRVWCtnSFBDbmg0QWt6VFVvY0FhSmhFUzZJUzFNcWhQamxGQnFLNkxtckxUZnptS0RXMVJaemoyUk1PVUZoM1ZwR2NXR0xJV0hodzVLeEJYNDdYNHc4eUNDT0dET2pGQVY5Y1QyeTNvQ3BxYmtJQlBKeC9jNDlsVVZtcHlHSGttdUF4c1k3ZWhybklhZ0gwaTAvQTlSUXRBR3NoM2pid0luZTN6UWppdnNFc01vMTJyajErKzRSOXZqUFNOemRtVGZDTFpVSzJkMDdSZEcyL0MybFg0cGhYQkp3SENmMzV1MWxmWitCcTZiYlBURjBOSVFPaFg4THBodUlSVE8zdEF4Y3NOdlZGYnpYQitHczZteXc0U3p4NXpxY2FqYnE4bmE1dVBaUHRMdTRBN3lTb1BmOERyd0R1OHhXYkVzTXUwUHh1NnFYUDRWQWM0UzRER0VRcTczTG5OWXBMMVhBN2t4U0Z5MDcxL3hQUUxsRElpdk5wVkIvZDMxdXRIdlJRVU5GSnY5WEtoamIiLCJtYWMiOiI2ZjY1NTE0OGZmNWU2MTczNDEyYzkwNTc1MTk1NDEzYWQ5ZWYxYWFlNTYxNGMzZGNiZTE4ZTgxMTA2ZGRhNWU1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:35:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}