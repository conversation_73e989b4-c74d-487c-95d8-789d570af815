{"__meta": {"id": "Xfa4338a099a50f0464cbc0c00bc230e9", "datetime": "2025-06-28 16:03:11", "utime": **********.705593, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.217154, "end": **********.705612, "duration": 0.48845791816711426, "duration_str": "488ms", "measures": [{"label": "Booting", "start": **********.217154, "relative_start": 0, "end": **********.642668, "relative_end": **********.642668, "duration": 0.42551398277282715, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.642677, "relative_start": 0.425523042678833, "end": **********.705613, "relative_end": 9.5367431640625e-07, "duration": 0.06293582916259766, "duration_str": "62.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45698056, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00336, "accumulated_duration_str": "3.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6747441, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.774}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.68747, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.774, "width_percent": 16.369}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.693687, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.143, "width_percent": 17.857}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-695943987 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-695943987\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1077539162 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1077539162\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-779922180 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-779922180\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126585944%7C15%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhzNzRNV0ExRTZGV3ZsdzN0bmNyUVE9PSIsInZhbHVlIjoiYk9NNVBuYndzcG1lTWVvSXlqVXBGM0ZmSHdPUGxyRUVVK3lRYktZRXB0NjZkU0xTbEJieTc5NUgwc3pTMkFjWWprWEdlOEpKdlk1dkVOR21FbmQ4L3l1K1djRGRLZE5vL2JHaXBzbHVhWjJRc05zaExUaVIrb0hnS3JMREVEb3dGcDNKWHd2M2g2ZWYyRFZLZXY3aStZRkVTTUVUWXV5UHNBbHBSMCtsQ0wxOGNxVnFuTDk3K203NmpxWGVRZjlLR3pnNUpUUnpUNlV4SFhUU0l5d1p5YldNeVUrWXNreGNReHZWRkhmeE5FdGlrZTFIWE0zK0s1eTlIaTZIYjN6VzNFUWM2Vlhzb0IvQVgxOUpYdTVmYXN0SWxaZHN4bVlNajFIRHZNL29aRWZqeUwwRklROFVUL1dQTWZKZE5XSGJMNGRMZTZMcFBmejE5TUk4QlJsS0hSUzhzWUFRbC9iWmw5WE1CQ3JLSmJidkpSRnRtSkhzMmxiYXpEWngvZzJLK3l2ZXkvYnFpSlYrem1ZMmdjYldsUjMrc29CN3gxVFJvbXZ5NTNHL1FjMU9ROTh3dFJkWlBMR00yTktLWS9LWWVWVVI1R3J0amtpeVBxNGwrNmcwMmY2VUU1WUYwUWRBN21KbHd5MlhkREkrY2czMGh2Uzg0QkNaQmdFU2RmSmMiLCJtYWMiOiJmZjQ0MDZiNGI0OWU1NmEwOTQ0YWE3ZGQ4MGE3YjljYjY0MDFjYTZjMWFkMjM5NzQ5YjJlN2M3NDAxOWM4M2IyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZjZkhzVUVMTVludFN2aWFNQWJzb3c9PSIsInZhbHVlIjoiWUxRNzNHRVV0bExIcFh5RE15Sy9EaXhRUHM1aTkwMTVzQVpwMVhmdkRFSWc2YzBNQXNLaXBYeVVVQWI0d3Q5V3hydGtpVDVKRndVOXcwOGZHK3NodTgvMUFqQ1NQdmEvRnF4UFRkRkhPdTg2aGl3dVh3MjEvb2ZlU1dKWFlkTEVGS0lpUG1MbnFzTWFlOW9xNi9iWnRuNHZlSWF2anFtZ2I2bWxFYi9FcmkwTnhWZW9pNUZuT3J1SGdDRjVVeHpTMEM3NjZNT1ZRWlhJTEZUMGN3Ui9PVU5mekI3VmFBc2piVHFuTFg0bGp5ZkEwYjlUbngwT3NEeEtadHJ2cUtUbzNZY3h3SDh3K3FMYzdGTHRuQnNhSnBJeTJtd2dLRWhQeUNpN2svWXUxSTJWNXVCbTVnTXBNRVFQRHlkdVVaUTErSjAvelBSQ2dKWjlUMzRRdFFLbHhpK1FwRjI2cHBYZFZ1WUhrRitIMzdQTHptbEFnS3AxU0JwNmt0RFBlUmhUTGFpcDQ4emxCcFVqTjB1YU9WcDdlaWtpSnBqN0tYWjhlTEFqRjV0eUlFVjdNT0M0OGgxMGU5Z204YjhQaDNpUXpRSUpXYmY2Q0hvMjZtZiszRHVwNE52dnZUQVdoeXJ0c0svT2lYOGZKQllJRGhTNDg5dFQ1STZ2RzhjU3RzZFkiLCJtYWMiOiI3Yzg4NTdjMGQyNmMxMzAzY2NiZWYwZTM4Y2YzNzRiOTg0YWFjZWQ2MmJlMGFmZDdmY2U4MmMzNDExZGM0OTI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-459639365 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459639365\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2066807866 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldDV1lNNkpheUxaR2Y5aVlxSjhKNnc9PSIsInZhbHVlIjoiOUJrNy9LenFXSkpVTnI4cUVyL1JuWkZIK2NSeGp3TWdqNjd2UVBXWFBUenhScG9mTHUwcE94RVhpS1VZa2tOemRaRVdHWVB6aytzanZiK0VGUXV0TFJRVCtmakN5MUx1QVF1aUwyY1BQcHFSV2dzcmx4clNoR2NqS0JyYnhPTTl6bHRvanh4OGF6WDA5VE5pbWhCNjFXZVZrYjg4WWRuKzNQVXZaOVkrM3FFTFNFVUlSb2I5ZjJxd0JWem9ENFY1bjRCQytvVGJONW5iUlVXRUJ2QzN4NmFObHBMbDNzRkJHaHBVbG5YU0ZsSkFWclpQRllZU1M3dWJaTncxVm8zTnBFbFJiSVNmb21wWWJCbTJ4RUl3Q0xpRFlzR3NGMjk0RWJlTFpndWJBaTQwVXE5TmMyeU9pTlZKWjRmQ3BTZTNJMklVS0ptZU9iZFdja3RmR2I1NVpJUjJvaGlVYWJjMytBVndTMkdzTVdqWlU4eXIzY2M1anRKOWhqRFo4am1jOUFMYTcrdzc4SHJwVEoxNkZBTkxKSjhBSm94bFZIdStaSkVhcTBUUHpSMlJIM0pvcXBvV0JHRGo3QUNPelhCNXovaEJNTmUyUUpLTkRyZXpPY21Hekx0QUsvWXpsM3VuUHNOZGxETjl3M1RLMmFqYWtycFEyK2NkaE9yS1lLTC8iLCJtYWMiOiJjNzZjNDdhNWNmMzg3M2RjZDI0ZTNhNDU4YTBjZGM1ZmU1ODViMGRhZDllZGM4YzgwMjFiMThlYmUzOWRhZjUwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlGTzJKQk1zN2xvQzI4cm5CZVlxYUE9PSIsInZhbHVlIjoiQWp3cXczajdLcGJzOG9uWHZrb29DcVJ2ZWJjQU1mUVV1ZWl3TkVBL0VrQndOQXdUN0t1NXA4TXZaUnVRZzBCSkNnamJZbHUwODlaa2QzWTQyZlN6VWVtSE5MOUFVVklXQVBwbjYyVHVBUGt6azk1c0l4RGk1RXVVb21pMDloWXEvdzF4SVRWdk42UHhTUWdFN0Q3MXdaNkZZZTRRS3M0Q3VmTmZ3dTVRWC9XejRuSm5kblhjZHUvVGlJVnRsYlh6NVdpWmN2WisvUkk2cHVTbi9lZ3lzV3o5M1J6YTE2aGlZSGtlSEZ2cG8xZit1UzZTa01qN0dIbXg2eHpxcys0WWw5VGlEd1VTd0YwZkVNZjBKaDZtL2NlVVR3clluVW45OS93bFk5QnBibGpYN21WRXJDNFVLQkxpWTh3RWNaN1o2UkI2a3VlUkRNZm5jY2tuWHFreWhBcEV5bjJQUjdVVnMvOHdsZDBkdGtta3NYemtQY0xMZ05MZ2dwREJXMFN0SWZsM3ZjZkZlRTk4bU9nTjlVdndDVUhXWDZwQTh6NGhCL2ZTSE16djUwSjZ2Q1FYdkV5QzdqOVNZK1lEcURCL3FVRnVuRWRmSW5ON3lPU1lvTGwzblN5SFJLdjljTWUxcjVDWlA4RGFxR1REUEp1eGV1R0NsYzZIekZ2QlVkSXEiLCJtYWMiOiIxN2MzNjA0M2E1NWQ3OTQ2M2RjMDk1MTRlNTQ1N2Q0NDljY2Y5MGUzOWMxMGQzYjUyODM4MDczMDMyYmM0NjYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldDV1lNNkpheUxaR2Y5aVlxSjhKNnc9PSIsInZhbHVlIjoiOUJrNy9LenFXSkpVTnI4cUVyL1JuWkZIK2NSeGp3TWdqNjd2UVBXWFBUenhScG9mTHUwcE94RVhpS1VZa2tOemRaRVdHWVB6aytzanZiK0VGUXV0TFJRVCtmakN5MUx1QVF1aUwyY1BQcHFSV2dzcmx4clNoR2NqS0JyYnhPTTl6bHRvanh4OGF6WDA5VE5pbWhCNjFXZVZrYjg4WWRuKzNQVXZaOVkrM3FFTFNFVUlSb2I5ZjJxd0JWem9ENFY1bjRCQytvVGJONW5iUlVXRUJ2QzN4NmFObHBMbDNzRkJHaHBVbG5YU0ZsSkFWclpQRllZU1M3dWJaTncxVm8zTnBFbFJiSVNmb21wWWJCbTJ4RUl3Q0xpRFlzR3NGMjk0RWJlTFpndWJBaTQwVXE5TmMyeU9pTlZKWjRmQ3BTZTNJMklVS0ptZU9iZFdja3RmR2I1NVpJUjJvaGlVYWJjMytBVndTMkdzTVdqWlU4eXIzY2M1anRKOWhqRFo4am1jOUFMYTcrdzc4SHJwVEoxNkZBTkxKSjhBSm94bFZIdStaSkVhcTBUUHpSMlJIM0pvcXBvV0JHRGo3QUNPelhCNXovaEJNTmUyUUpLTkRyZXpPY21Hekx0QUsvWXpsM3VuUHNOZGxETjl3M1RLMmFqYWtycFEyK2NkaE9yS1lLTC8iLCJtYWMiOiJjNzZjNDdhNWNmMzg3M2RjZDI0ZTNhNDU4YTBjZGM1ZmU1ODViMGRhZDllZGM4YzgwMjFiMThlYmUzOWRhZjUwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlGTzJKQk1zN2xvQzI4cm5CZVlxYUE9PSIsInZhbHVlIjoiQWp3cXczajdLcGJzOG9uWHZrb29DcVJ2ZWJjQU1mUVV1ZWl3TkVBL0VrQndOQXdUN0t1NXA4TXZaUnVRZzBCSkNnamJZbHUwODlaa2QzWTQyZlN6VWVtSE5MOUFVVklXQVBwbjYyVHVBUGt6azk1c0l4RGk1RXVVb21pMDloWXEvdzF4SVRWdk42UHhTUWdFN0Q3MXdaNkZZZTRRS3M0Q3VmTmZ3dTVRWC9XejRuSm5kblhjZHUvVGlJVnRsYlh6NVdpWmN2WisvUkk2cHVTbi9lZ3lzV3o5M1J6YTE2aGlZSGtlSEZ2cG8xZit1UzZTa01qN0dIbXg2eHpxcys0WWw5VGlEd1VTd0YwZkVNZjBKaDZtL2NlVVR3clluVW45OS93bFk5QnBibGpYN21WRXJDNFVLQkxpWTh3RWNaN1o2UkI2a3VlUkRNZm5jY2tuWHFreWhBcEV5bjJQUjdVVnMvOHdsZDBkdGtta3NYemtQY0xMZ05MZ2dwREJXMFN0SWZsM3ZjZkZlRTk4bU9nTjlVdndDVUhXWDZwQTh6NGhCL2ZTSE16djUwSjZ2Q1FYdkV5QzdqOVNZK1lEcURCL3FVRnVuRWRmSW5ON3lPU1lvTGwzblN5SFJLdjljTWUxcjVDWlA4RGFxR1REUEp1eGV1R0NsYzZIekZ2QlVkSXEiLCJtYWMiOiIxN2MzNjA0M2E1NWQ3OTQ2M2RjMDk1MTRlNTQ1N2Q0NDljY2Y5MGUzOWMxMGQzYjUyODM4MDczMDMyYmM0NjYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066807866\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-812802323 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-812802323\", {\"maxDepth\":0})</script>\n"}}