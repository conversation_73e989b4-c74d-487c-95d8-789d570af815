{"__meta": {"id": "Xcd21b874c8cfa9b4f5399923b0180c2b", "datetime": "2025-06-28 11:22:04", "utime": **********.999433, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.609873, "end": **********.999447, "duration": 0.3895740509033203, "duration_str": "390ms", "measures": [{"label": "Booting", "start": **********.609873, "relative_start": 0, "end": **********.95014, "relative_end": **********.95014, "duration": 0.3402669429779053, "duration_str": "340ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.950148, "relative_start": 0.3402750492095947, "end": **********.999449, "relative_end": 1.9073486328125e-06, "duration": 0.0493009090423584, "duration_str": "49.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43406296, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01257, "accumulated_duration_str": "12.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.975706, "duration": 0.01257, "duration_str": "12.57ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "oOcJ3vwijTOlUa5TvSag9RRj6ZaDejIzujCCr2fL", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-652355819 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-652355819\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1624973329 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1624973329\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1980612619 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980612619\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-986653970 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-986653970\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1956059531 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:22:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxqbEdBejNPSDhLSkprZUh4S3FqRmc9PSIsInZhbHVlIjoiVE1wSDJ6ODRpRzA0ZHA4MndDZHJtai9CQ0dhRXduZ0FiU2lleXZxaHVWMldZVTk1emhVNTdDWHpublZnOGhjeUErdHNGbHMvdHhnRm52ZXdyN2xDK3Q0UkFCdFVJK3dSdnlOVkhHNGRiaEppamI4N002dFhpc2VZakhCTmRDMVA1RTI1OWh0Wk9pMVBBNHo3SlZ2MHExbHZ4TEZiSWdVSXQ3TXo3S1RKWFRMN3J0Ujc0UEk0SGpZZmVxc2FER3k1NGtvcWpJNkNHK3A2S3UvaGRnWEpZT1M2VVJFcnRKb25EOGJubWYzdC9lbit3WEhEZm4rTC82R2VpQnFIQ0xhNDd0OWNSQW5TWnVGYmF1ak5jTndwZFlxZlZHUWludzBITjBJdS8zMDZodHBWeWkzempkc1Zrd2EvZ20rZDBGRGJGZjd6N0NXb1JKMTNTSFJxOUVHSUQraVZBMEFpcmZuT1lUSHNKU1VZenpreFNMQ0x6dzh3dnRkaTYvN2dFU1JmL2dKdFVZaFdTZC9NQjZNamtYODZpbnAvZExTWjdvanRpU1lLZytGandoVlhRRHhoOHIra3R3S0tHb2RtbEd5dXEvbmRaM1lhWHQvTFlEWDgxMGhEK21qeTlTNFgwOW1vQ1QvNm9DR1dSR3BURDc0RkJmRzJMOWc0WDFadURBZ24iLCJtYWMiOiJmNWNlOTg2Mjk4OTAyZWRlYTg0MjgxOGM2ZDg3NDdjNjlkYWZlNzYxYTY0ODEwY2IxYzcxYTc5MDBiYmUxMTBkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:04 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImxqV2pNWFdPSFB0dUNSZ0N2QkFaMHc9PSIsInZhbHVlIjoibGhXN0pBa3VhRHIyc09sdHE3SVc2eHI0eHdmSFlTa1d4YUdxNjhmVWRBZHlRRU5YajB4TVVnQjlaZ0NvWktwTFVQSHpMSUx4NU54K0lSOGU3dDJzQWt6Z25QbmdQT1YzVVFLZHg0Z2JpcmxNd05CQkFLZ2ZjQ1FmbkVYejlMT05kWVZDVVZpWWpBTDFZRWtmaUwwclliZEdzOW10WlVSTnpVY1k1aGdEeUVRY3Z6M2l3c2FMN2I0Sk1mS3N3SUI1ejVqamVPdzhMM2kyV1d5SXptUTdGb25xb1RLYzc3K2JhSzNYSHlPb0J3ZDBWTkFGQVhPaTB2TmxNRkM2YjMzT3pnS2ZyNzVUaWxtdDhoTW1ENXR5MncxaGxUTkROais3ejBrSVZRT2t2VXpIWjdwY3hOcE9JRlhOS1JUSDkyTDdKRXd1V3BJa1FLRUlaYXFjcnlnR2hRNEJTUmJ5VEUxT1BrZC9hZ3ZxQ0IrUnp4MXViMlFRNXhkdUhlMXZyVmRZaFR5RXE5NEozZWZFM1l3a2pzOGdWZXFGRktLdEt2K240WGUwczdkQm51VFFMc0VFTlh5OVNuQVVFdzFXaTlIVU5nZEFVZ0tJNjZpZCtEcFU2RlNYRkttYU01UFV6Q1N0aUVJUVN6NEl0UFkveTM4emhSbkFwcUFpQzdCNWZsaXMiLCJtYWMiOiIzOTYwMWJkMmNjMThiZGQ3MjE0ZjUzOGZiNDlmMjA3ZTY2Y2YzOGQ1ODUzMWVjNTFlODI3M2U3NDFjNTExZDIyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:04 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxqbEdBejNPSDhLSkprZUh4S3FqRmc9PSIsInZhbHVlIjoiVE1wSDJ6ODRpRzA0ZHA4MndDZHJtai9CQ0dhRXduZ0FiU2lleXZxaHVWMldZVTk1emhVNTdDWHpublZnOGhjeUErdHNGbHMvdHhnRm52ZXdyN2xDK3Q0UkFCdFVJK3dSdnlOVkhHNGRiaEppamI4N002dFhpc2VZakhCTmRDMVA1RTI1OWh0Wk9pMVBBNHo3SlZ2MHExbHZ4TEZiSWdVSXQ3TXo3S1RKWFRMN3J0Ujc0UEk0SGpZZmVxc2FER3k1NGtvcWpJNkNHK3A2S3UvaGRnWEpZT1M2VVJFcnRKb25EOGJubWYzdC9lbit3WEhEZm4rTC82R2VpQnFIQ0xhNDd0OWNSQW5TWnVGYmF1ak5jTndwZFlxZlZHUWludzBITjBJdS8zMDZodHBWeWkzempkc1Zrd2EvZ20rZDBGRGJGZjd6N0NXb1JKMTNTSFJxOUVHSUQraVZBMEFpcmZuT1lUSHNKU1VZenpreFNMQ0x6dzh3dnRkaTYvN2dFU1JmL2dKdFVZaFdTZC9NQjZNamtYODZpbnAvZExTWjdvanRpU1lLZytGandoVlhRRHhoOHIra3R3S0tHb2RtbEd5dXEvbmRaM1lhWHQvTFlEWDgxMGhEK21qeTlTNFgwOW1vQ1QvNm9DR1dSR3BURDc0RkJmRzJMOWc0WDFadURBZ24iLCJtYWMiOiJmNWNlOTg2Mjk4OTAyZWRlYTg0MjgxOGM2ZDg3NDdjNjlkYWZlNzYxYTY0ODEwY2IxYzcxYTc5MDBiYmUxMTBkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImxqV2pNWFdPSFB0dUNSZ0N2QkFaMHc9PSIsInZhbHVlIjoibGhXN0pBa3VhRHIyc09sdHE3SVc2eHI0eHdmSFlTa1d4YUdxNjhmVWRBZHlRRU5YajB4TVVnQjlaZ0NvWktwTFVQSHpMSUx4NU54K0lSOGU3dDJzQWt6Z25QbmdQT1YzVVFLZHg0Z2JpcmxNd05CQkFLZ2ZjQ1FmbkVYejlMT05kWVZDVVZpWWpBTDFZRWtmaUwwclliZEdzOW10WlVSTnpVY1k1aGdEeUVRY3Z6M2l3c2FMN2I0Sk1mS3N3SUI1ejVqamVPdzhMM2kyV1d5SXptUTdGb25xb1RLYzc3K2JhSzNYSHlPb0J3ZDBWTkFGQVhPaTB2TmxNRkM2YjMzT3pnS2ZyNzVUaWxtdDhoTW1ENXR5MncxaGxUTkROais3ejBrSVZRT2t2VXpIWjdwY3hOcE9JRlhOS1JUSDkyTDdKRXd1V3BJa1FLRUlaYXFjcnlnR2hRNEJTUmJ5VEUxT1BrZC9hZ3ZxQ0IrUnp4MXViMlFRNXhkdUhlMXZyVmRZaFR5RXE5NEozZWZFM1l3a2pzOGdWZXFGRktLdEt2K240WGUwczdkQm51VFFMc0VFTlh5OVNuQVVFdzFXaTlIVU5nZEFVZ0tJNjZpZCtEcFU2RlNYRkttYU01UFV6Q1N0aUVJUVN6NEl0UFkveTM4emhSbkFwcUFpQzdCNWZsaXMiLCJtYWMiOiIzOTYwMWJkMmNjMThiZGQ3MjE0ZjUzOGZiNDlmMjA3ZTY2Y2YzOGQ1ODUzMWVjNTFlODI3M2U3NDFjNTExZDIyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1956059531\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2040914664 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oOcJ3vwijTOlUa5TvSag9RRj6ZaDejIzujCCr2fL</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040914664\", {\"maxDepth\":0})</script>\n"}}