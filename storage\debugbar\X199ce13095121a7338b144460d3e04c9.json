{"__meta": {"id": "X199ce13095121a7338b144460d3e04c9", "datetime": "2025-06-28 16:34:38", "utime": **********.939812, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.526854, "end": **********.939825, "duration": 0.41297101974487305, "duration_str": "413ms", "measures": [{"label": "Booting", "start": **********.526854, "relative_start": 0, "end": **********.864082, "relative_end": **********.864082, "duration": 0.33722805976867676, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.864092, "relative_start": 0.337238073348999, "end": **********.939826, "relative_end": 9.5367431640625e-07, "duration": 0.07573390007019043, "duration_str": "75.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45893072, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026289999999999997, "accumulated_duration_str": "26.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.894974, "duration": 0.023, "duration_str": "23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.486}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.928067, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.486, "width_percent": 2.016}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%enhanced-po%' or `sku` LIKE '%enhanced-po%') limit 10", "type": "query", "params": [], "bindings": ["15", "%enhanced-po%", "%enhanced-po%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.930722, "duration": 0.00276, "duration_str": "2.76ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 89.502, "width_percent": 10.498}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1208777154 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1208777154\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1446018510 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1446018510\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-3617922 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"11 characters\">enhanced-po</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-3617922\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1403401786 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">32</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128472755%7C51%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikp4MWwxRnVQZHIwSUdrZXozYmRrTmc9PSIsInZhbHVlIjoiOTlRdFhRSER0ZzJTd2wvZ1FhSktRU1FZT3g3dnBUSExzdzhkbzQ1VTRSN1ZuemlkRWYxRjhVVVR5eTlWWEkxL3M3SGF2MkhtUEpSTlQvQ1FoamNWS2FYNjRIVVpIS0VrZSs5QzlJZ2NjSkl4aktSOGtGMW1QNEpoVjJOQzBqbG5pckFyOHBuZmlQVklGdjhRRlpJejRWTmx3TTlnaWY2WE9IT2xsT3Rzd2VJTE1BcmdVTW00UXdpQjhleXRVOGlFVUdaQksrQ29mS0ZhQVNjeGd1TE1FclVSRTAyQzEzaENDWTRiN0V2c0krbDF4ZU01TFJXWG8veWh0QVZzQzQvNENkYU0yUXVBNUxEZSt6cjdmVUtjcUlCTVBQUXlQRHdVWTlJNVJMQ1pRMjZzSEZSQWZtQnVuU0IvZWpkdklDa2dVSHBaaXRWQnFONlFFSHcwanlocDA0ckZNd0VCdW8walN4TVlyMTVUR1ZNSWJFWUk3eUY0ZVF2NS80MjlsRlhsR1ZuQkppV05RNE1Od3hiNWRKWGtPWnc4c0lDWGV1Mm5hUHMvVnpRVkhxQi80dEZtSDhnYWpGWmdKUGJiM3pFcjdza3lOdnY5M05HT0RhWURZTmlFcVJOaDJXa1Z6b1dPcWQ1dms3V21YamdpeUpseUFwNFNqN200YzhxWWxTUGoiLCJtYWMiOiJmZDk3MDMyOTBjMjI5NzAwNWIxYTU5NWQzN2U5M2QxMDQyYTIwOGYzMDJiNzgyOTVlZWMxZGYwNDMwZjYxZWQ0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Inl4WWllQStWWGdEUWk2WEtKYTVlWGc9PSIsInZhbHVlIjoiME1QSXZJQVVSNDNIUWJhYUJpMTI0RHNnVldGOHNtYndyUHNvWjVhbDRMeXZBRXEvejJzRFZMWkpUMk0wbHF2YmNUWTRUS2N0OXRLYVNsNVNRcmZUejlXU2NtcEEvT2E0Q1J0ZUdLUFNYNXVUYy9oVVhTZnVxZ1BCb1RXTHZ5N0c0Znl0VFJDNmhzSVY4OGE2NnpJVHB5OU5tU0VIemdEWkoydXhlRnVCbm1hZ0N1eGNvVU9hMnBmdmdweHZqSnBlS3k5Uk52dVVkUy9tTzAvd1dJTE9lTFl5Nm1pMEVkdUpCbFJqOFpta0hZaWhkMGRJeWlreVdNL0QzTnh3RmJjblFEclNqcFVMOHdua1I0UWozVjJTcVRKZUptNkoxbHIyZ0cwSzNId0cwSzBxYnd5a2NVZm5YbnhBMzhCVXRyY1Mwdi96dUh4SXExQ3VHbnVObkxta21DSzlKc0hROEI4ZE5rYzVTbHBqb3pjTGtsYk1qdzI0K0d6SHF1Z1kyckJ0MVJzVnRrNHhOWVRBNC9IWVJFejdhL2kzbzk4RkdrNjdHWVhjbnBxeURiWDJ4UTMrRUNLTWYwRmdHOXY3QkJpVlhpb3JPdktZU1l1RlB2RG91VUl1SHZQeXpkWUNHcG1Ja1RwcEE2RUhEVVYwaGNFdDltTUpYelJRWmhEOEk4dWQiLCJtYWMiOiI4YjRkNzUwNWU2NTMxNDkwN2RhNmJiMDU5NmU0MjdhNzY5M2I1OWY5YmUyMTBkMzg2MzM3ODk0NGQyMDYxMDIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1403401786\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2116918115 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116918115\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1246186063 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpLQWYzclltcm1ZZFZpVnJ0WENxK2c9PSIsInZhbHVlIjoiRFVjRy9sYTgyNjhITERQeEdSTzFNNE1sYitnTG9jVEpTSDJhTXc0amVVOStQVVhaRUkyZks5RjZGcnRCMWgxVTNCbVJMa2czcFNSS1JueGtpaFBvY0lYVVR5MlVLcDRBN0pmQmpLa2tKL1Q2Z0tiOGlxZTFEbGFublRrbHhmbXhLcHYvWjBlTzJmU0tMY2VnVGhmWHcwVkdTMmFMQlB2QWZ5d0dpVlY3TG9IU1FWZkNwYWtIaHdzOS9CclA1Tzl3YlU1ejdjNE9tYjI2bVJadmU2ZGVKNmx6dFVsRDUvUWIzTkF5OUtoT0RMZHk2WEhuNU1kWXZuMEhKRXdJRjhCc3YzZGNYTmNHQ0c3R0NqWm9NYzROOWgvL2Q2ZER5RDJmcnZNQmxKS1o0SWpHekNYenNWTHR6aDVydlp0dVVUbitQaUN6Vm1mclFGZ1o3YlpJYnpqTHR0cEhYVVpGVzB1NVZyYjRySUNPMk9UV1dwdG43ekd0VVZBNDE0NFdlOE9lYTBrWUNiK3lqOGlWekE2dTNmR1J4K1FQa1Jhb2RIOGpoVGFuZlRvOTc1dEdoWTkyYTBXditSclVVeGprdG00RlFKNzI3WndVU0RoeW9EMkY1d2t1YXRuSHB4bzlQOHNZQmVGVTVSNGVGMExXWkFMRWN6dFJwcCttREFudGlVcWciLCJtYWMiOiI3NGUxMWU3YzZmNzk3NzFlNDA4ODlkNDQ4ODE5Nzg1MDk2MjliMjZjMzQ5N2NhMjkxYzM2ODYzMmQxZjQxY2ViIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlI4Q0VJLzhpaFRWZXcvK2gxcVMyTFE9PSIsInZhbHVlIjoiVXIzSDI4VU9xbGtsVnByb0tBM2U4aGFMcmpueFpqYWJ0MktSWHdaSjFuVkNhcW1EMHVpRTUxUEpEdGhVdWFiVllORUM5OG8wQkgvN3ZaYVNlMTNnbElwbDVrYlZyWUlGc2VzNzV3WllhMW83bGdtUzdsQ3JZSjBnYmhzOGROQnRDM1hIWHpIajF1NEFSeHljZG92T2E4QTdRMFR0ZkxhMHlRVHZzMTZXUVVJN3pkNkFQc29MaTZGYWkzZTc2RDZTU2dya2UyVUNXN0F6djVnUDl4aVJSVGJvb1pvWFlFR2VTaHVNR0FIdCtrQllkc2JTTVVVaDlwNi9laFZtbWxVM1hqbDVBdVJDU2wrRUxYdmtER3JNZjdEZ29rUUgxUEVGbjB2a2dWWDhtZWhlT1E3NEpaSXdYN0pOKy9od3ZFU3orOUU0V1ZYVms2eW80N282aVZVc0MrMnlldWVFRExUbE5RbElvQ0dKK25yTGJZZlZIMGtFYUdiSmE1blNGL3lhTXJ3cGMydE82eE1aTVc5dWRGc1pLZDV4TzdrZnI3VXFGbGxhOGtLcU9EVzhuZmRBMnBTY3ZocUk2WkZib2ExZXdNOTNxWk5ZUTVLUy9WMGlnRk4zbWtiRTM1c1hya2tCMDBucFZkdFVnd3pVRkRwZHR6UlJTYzRBTEdpd2hjYXciLCJtYWMiOiI5ZWM1MzU5YmFkY2U4YjJhNTQyZmUzY2FjYzk0ZTA3ZWNlYjY1MzlmNjJjYzI2YzI1ZDU4ZmMyN2UwMzBkZjQyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpLQWYzclltcm1ZZFZpVnJ0WENxK2c9PSIsInZhbHVlIjoiRFVjRy9sYTgyNjhITERQeEdSTzFNNE1sYitnTG9jVEpTSDJhTXc0amVVOStQVVhaRUkyZks5RjZGcnRCMWgxVTNCbVJMa2czcFNSS1JueGtpaFBvY0lYVVR5MlVLcDRBN0pmQmpLa2tKL1Q2Z0tiOGlxZTFEbGFublRrbHhmbXhLcHYvWjBlTzJmU0tMY2VnVGhmWHcwVkdTMmFMQlB2QWZ5d0dpVlY3TG9IU1FWZkNwYWtIaHdzOS9CclA1Tzl3YlU1ejdjNE9tYjI2bVJadmU2ZGVKNmx6dFVsRDUvUWIzTkF5OUtoT0RMZHk2WEhuNU1kWXZuMEhKRXdJRjhCc3YzZGNYTmNHQ0c3R0NqWm9NYzROOWgvL2Q2ZER5RDJmcnZNQmxKS1o0SWpHekNYenNWTHR6aDVydlp0dVVUbitQaUN6Vm1mclFGZ1o3YlpJYnpqTHR0cEhYVVpGVzB1NVZyYjRySUNPMk9UV1dwdG43ekd0VVZBNDE0NFdlOE9lYTBrWUNiK3lqOGlWekE2dTNmR1J4K1FQa1Jhb2RIOGpoVGFuZlRvOTc1dEdoWTkyYTBXditSclVVeGprdG00RlFKNzI3WndVU0RoeW9EMkY1d2t1YXRuSHB4bzlQOHNZQmVGVTVSNGVGMExXWkFMRWN6dFJwcCttREFudGlVcWciLCJtYWMiOiI3NGUxMWU3YzZmNzk3NzFlNDA4ODlkNDQ4ODE5Nzg1MDk2MjliMjZjMzQ5N2NhMjkxYzM2ODYzMmQxZjQxY2ViIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlI4Q0VJLzhpaFRWZXcvK2gxcVMyTFE9PSIsInZhbHVlIjoiVXIzSDI4VU9xbGtsVnByb0tBM2U4aGFMcmpueFpqYWJ0MktSWHdaSjFuVkNhcW1EMHVpRTUxUEpEdGhVdWFiVllORUM5OG8wQkgvN3ZaYVNlMTNnbElwbDVrYlZyWUlGc2VzNzV3WllhMW83bGdtUzdsQ3JZSjBnYmhzOGROQnRDM1hIWHpIajF1NEFSeHljZG92T2E4QTdRMFR0ZkxhMHlRVHZzMTZXUVVJN3pkNkFQc29MaTZGYWkzZTc2RDZTU2dya2UyVUNXN0F6djVnUDl4aVJSVGJvb1pvWFlFR2VTaHVNR0FIdCtrQllkc2JTTVVVaDlwNi9laFZtbWxVM1hqbDVBdVJDU2wrRUxYdmtER3JNZjdEZ29rUUgxUEVGbjB2a2dWWDhtZWhlT1E3NEpaSXdYN0pOKy9od3ZFU3orOUU0V1ZYVms2eW80N282aVZVc0MrMnlldWVFRExUbE5RbElvQ0dKK25yTGJZZlZIMGtFYUdiSmE1blNGL3lhTXJ3cGMydE82eE1aTVc5dWRGc1pLZDV4TzdrZnI3VXFGbGxhOGtLcU9EVzhuZmRBMnBTY3ZocUk2WkZib2ExZXdNOTNxWk5ZUTVLUy9WMGlnRk4zbWtiRTM1c1hya2tCMDBucFZkdFVnd3pVRkRwZHR6UlJTYzRBTEdpd2hjYXciLCJtYWMiOiI5ZWM1MzU5YmFkY2U4YjJhNTQyZmUzY2FjYzk0ZTA3ZWNlYjY1MzlmNjJjYzI2YzI1ZDU4ZmMyN2UwMzBkZjQyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246186063\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1099349283 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099349283\", {\"maxDepth\":0})</script>\n"}}