{"__meta": {"id": "Xad7888425b3927d7a13a808247922de0", "datetime": "2025-06-28 15:46:38", "utime": **********.111992, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125597.61065, "end": **********.11201, "duration": 0.5013599395751953, "duration_str": "501ms", "measures": [{"label": "Booting", "start": 1751125597.61065, "relative_start": 0, "end": **********.036595, "relative_end": **********.036595, "duration": 0.4259450435638428, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.036603, "relative_start": 0.4259529113769531, "end": **********.112012, "relative_end": 1.9073486328125e-06, "duration": 0.075408935546875, "duration_str": "75.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45163840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02137, "accumulated_duration_str": "21.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.066153, "duration": 0.02047, "duration_str": "20.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.788}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0964048, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.788, "width_percent": 1.965}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.103369, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.754, "width_percent": 2.246}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1461360079 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1461360079\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1397863789 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1397863789\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1260910495 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1260910495\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-741548132 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125596119%7C20%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkUzYnhjOElieXBhUDVIM0JOVXpzREE9PSIsInZhbHVlIjoiUUFhQmFuMGRBdktZTUVjOTdkSCtrMzJNWjZyK0dxNy94OWo3c3ZiNXZqeFRzVVg0WUNoUG5TcnZZc2hhem1nZ2g1bGpIQ3ZSejU4azJkTHovRHFpMm82M0l3SWFhemxFL0c5bVdVVlAxemlzcGdaM25RQTdodlN5RjZ1R1VGZlBlaHR1VVRzcWIxM2t5NXVIU2VDaVUrdmVwVnVibkFZc28wVXI4QnNSU3NyT29WTCtLNDBaOHJVdWVvVDFSOEJTUDFXbE1jTXpjNkJOZjZvZHdRUyt0SlY3R092VkVJOWE1Y1VQTElOQzhlSkFzS3NhbmJVS2VETXI5NEhNbEJ1REpzenNhSjkxa1U2TmxQbE5XZXNqekpKVG1TdXB1S3Y5NkdKSlZBSjduYk5tdmIwZUlkR3o2U0RpSDN1dEQ4M2d0ZHhMQ2s4Wmg0bVNNS1pROHBoZFNxWHRJUVEvNit6U0dnNUFrRmNZeDl4eFp5dFQrWUlYd2ovVElGQXMzb3dKSTQ3cWJtcENOUmlZVFdhUWJPZGlLZmY5OTlZU09Ybk9UZ3FMVU1KQUNTcjFGUWYvL2VRRUZmL1ZZL08vSzJGdWpiOVdUNlhRazAwbW1EMmNqUUVxWVJvZHBHQUNrQmdGOW92Zk1HVWJoSTRaZTNZWWJyQWFjTGNSVExhZUEvV3MiLCJtYWMiOiJjOWFlYmUxMjk2YWQ4NGM3YThjY2IwM2E2NDBhNTQxZTU4NGRhOTkxN2VjMTM4YmYwZDdhMWUxNjlmZGNlZmZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVUMHJ4RG5OZlBGNWNMRTN0THAzdUE9PSIsInZhbHVlIjoiS2t4a2s1OEZmakR6NkhSRXBSMFVuaHgxQmVVYmNTeW85ZzFac0t2eXI2Y0syQXI5UUJIYUJoSmJLUFYrMXRiMWExUFJPc2ZTMWdTdlQyVFdodEFOdUJEdjdtVFBHekNBODFPbk94NGgrNGRoVDduSTFKa2xtWkIrbEZ6enBldkllNktydmowd3J3UDhPSWxvUSt0SFVsMU1vdzJMNHdNcmRNMDBtZ1A2UHFhdFpsSlFTNnl0ZkVBVDA5MlZHWGZBbktZSmg0cVE5NXJ0SDUweUl0NE12NnRMd1VMc1V3OWw1UlRzek85eGlWd2NFTjZtSkh3YVo1QlRIeXNseGhqUzF6SVVjTUE0NEpjNDVNUUhDNE5tYkp3Zmp0UjdDUjJQRmJmRFB4Z0taQlA1dWI4Wkw0S1NjaFJnalJ3U1VWaGpDd3VNOWFqemVaYlFwcGxzMEh0WXVQaGpHb2YvUTZtMW9wQ1lRbGZUVEFKWjZPaG9ab3pWdUVvYkFnZmUrYk12aVpzZ0dYQkh2UmNZRXFRMlBSWkZHeTJ5TEdkc2NCelgzMzZ5YmVkTnZYUkwvaEp6REpVTGxPWXVrMnlCTncwR1hwZzYyZ3BEekxOOFpPVUMwb29KNGgwUVFkTzV5ZTFHTDBlYll6OEs3dUJNTnFybTVxd25tTTgzT2J0OXJIR1giLCJtYWMiOiI3ZGRhNmI0YmEyMzJmMWM2YjNmNWEzMzJjYjA5NzA1YWM4YjUxY2Y0NDY5ZTI2ODY1OTY4ZGJlMGI5NzYwNTgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-741548132\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1677491323 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677491323\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1415756016 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:46:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVsSUhuY05DK0JxRkNCZkJBZkJCTWc9PSIsInZhbHVlIjoiQ2ZDTlR2NzlRaWlWb2M0bHR6dVpWNTkzNlU0Y2cyWHlYZnVZNnFkV2IzSTdTeFloWWxYYmI3VDdJakozekgyTzhiTjQ2Q1Jlb2RUMW94eUVtaWs4QUF3aW5DdnlRNGdFM3pLV1A0R3NFaWp6SEtOK0E2S2hBV2ZRUmhsU1BkRlZ2U0RMNllHeTdUbWtCZ0hDN3ZNWElUeFo1ais3MzhNT3NSZHlnMXFzb3lRbkJVUFUzd3dNOUVQNXVCWTNmMzNOZWxkMVpzUyt5bmlLQ1JCYXU3L2NsUm9zRjZLTHVCWjd0VVlNVnNRbDg1cEZwT2VmOGRxVjBHa2UxSHo0elZwTVc3THZqa0RtTTBKZCtKa1U4M0ovQ3lMS2YybndTaHN2MzZTTndnOHgydUxVQllwdWFISFRhcVBKZlN4SGhxK2FtTUtnclgrYVJCbk9EeVZZUnFrYmxJYWozZ25qOG4yMEk5emNGVnZYOTBINmNrMmNOUEJVd3ZKNTExazFOalpIeFNqeGJLV1llVW8zMFhiZDh2cW5vVlZNWEs0YUVsR0NrMm1jdlVkaC8rUjhuWllZR1pwSWt1RHpCU24xbDhBOTdxaGZGa2g0NnBZTTBVcVpCZ1kza2NiNWt4Y045Q0x1Y01NaTNiYmJZZk9nOXpldjZxbGZ0UFVicFlsUjN6MkUiLCJtYWMiOiJhYTE3ZGU1MjQwYzNiYzdkNDAzYjlhM2FkNmVhYTI2N2IyNDA4NjJkM2UxOGMxMTM1YzVlMDE3M2M3ZmQ0ZWRiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IldzRjJmNUUzbzVjOGdmMm94OFdYcmc9PSIsInZhbHVlIjoiZGI2MXkwOVFpKzcxOXFVTldZckRkbWNQenAwREdmTUlZWmlrbDB6ZTVHbkRVcWx1Zy9GTW9jTHBDUHpxeVF0TGdoN1hpTVNRR29vME5wOHpNU09pTU1YNVIwbUJIVmdHaGV6QWdpRGUzM0ZlVXY1VktIelo5emtmMDFEclJJa0VGcnVmMUhBMFhvL0lGNkxjSm4vU1h0aHRIMDN0SkxuTUJNM3lMZ0VzYklQb080R0JBUERDdEhNa0lsanFBbnZ2RC9uZTFKRWJEUVJ5bDZET2Z5cWpZRXVsTnpUUjN0ZzRPeXNvMitPV2k1SHEyTDdQV3hvWmhVRytOZjdSekJIajNKNld5aDhLZHRPWllMWHBhSXlueEJPNlpyWUtxbnBUcjRNeExBZnFlU1d5NFJaRVNza25xQVA2Qk9wOWh6dU8zWExiMGJiTnA4MDVTdHQ0VjA3QVBvVm9KWjRyNXl0MGpTWldMWHl6QmZtYnIydGQveXF1ZzBmWVMyWUZIZmZXSVp1U3FxTjc2UXFrQkFzeXZzNU02Z2QzOVEyeW9DM05tR0hkcWh2SnJOejFvcjdpQUpybFBaRjZKSG1BV3Zab2RxTG84YWNPMkFUZWVtNittYjFnSWtqclFnYmY4RzMyamhxcmhUQmFIWGorQUYvYnJ1QmZXVFVURDJhRldTWEIiLCJtYWMiOiJiNTgyMTZlZDkzNTAyY2Y5ZjBiZjcyMjE1ZDFiN2M3NWUyOGFiNDY1OGQwMTNhODFkN2U1Yjg4ODFlZDBmYTJjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVsSUhuY05DK0JxRkNCZkJBZkJCTWc9PSIsInZhbHVlIjoiQ2ZDTlR2NzlRaWlWb2M0bHR6dVpWNTkzNlU0Y2cyWHlYZnVZNnFkV2IzSTdTeFloWWxYYmI3VDdJakozekgyTzhiTjQ2Q1Jlb2RUMW94eUVtaWs4QUF3aW5DdnlRNGdFM3pLV1A0R3NFaWp6SEtOK0E2S2hBV2ZRUmhsU1BkRlZ2U0RMNllHeTdUbWtCZ0hDN3ZNWElUeFo1ais3MzhNT3NSZHlnMXFzb3lRbkJVUFUzd3dNOUVQNXVCWTNmMzNOZWxkMVpzUyt5bmlLQ1JCYXU3L2NsUm9zRjZLTHVCWjd0VVlNVnNRbDg1cEZwT2VmOGRxVjBHa2UxSHo0elZwTVc3THZqa0RtTTBKZCtKa1U4M0ovQ3lMS2YybndTaHN2MzZTTndnOHgydUxVQllwdWFISFRhcVBKZlN4SGhxK2FtTUtnclgrYVJCbk9EeVZZUnFrYmxJYWozZ25qOG4yMEk5emNGVnZYOTBINmNrMmNOUEJVd3ZKNTExazFOalpIeFNqeGJLV1llVW8zMFhiZDh2cW5vVlZNWEs0YUVsR0NrMm1jdlVkaC8rUjhuWllZR1pwSWt1RHpCU24xbDhBOTdxaGZGa2g0NnBZTTBVcVpCZ1kza2NiNWt4Y045Q0x1Y01NaTNiYmJZZk9nOXpldjZxbGZ0UFVicFlsUjN6MkUiLCJtYWMiOiJhYTE3ZGU1MjQwYzNiYzdkNDAzYjlhM2FkNmVhYTI2N2IyNDA4NjJkM2UxOGMxMTM1YzVlMDE3M2M3ZmQ0ZWRiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IldzRjJmNUUzbzVjOGdmMm94OFdYcmc9PSIsInZhbHVlIjoiZGI2MXkwOVFpKzcxOXFVTldZckRkbWNQenAwREdmTUlZWmlrbDB6ZTVHbkRVcWx1Zy9GTW9jTHBDUHpxeVF0TGdoN1hpTVNRR29vME5wOHpNU09pTU1YNVIwbUJIVmdHaGV6QWdpRGUzM0ZlVXY1VktIelo5emtmMDFEclJJa0VGcnVmMUhBMFhvL0lGNkxjSm4vU1h0aHRIMDN0SkxuTUJNM3lMZ0VzYklQb080R0JBUERDdEhNa0lsanFBbnZ2RC9uZTFKRWJEUVJ5bDZET2Z5cWpZRXVsTnpUUjN0ZzRPeXNvMitPV2k1SHEyTDdQV3hvWmhVRytOZjdSekJIajNKNld5aDhLZHRPWllMWHBhSXlueEJPNlpyWUtxbnBUcjRNeExBZnFlU1d5NFJaRVNza25xQVA2Qk9wOWh6dU8zWExiMGJiTnA4MDVTdHQ0VjA3QVBvVm9KWjRyNXl0MGpTWldMWHl6QmZtYnIydGQveXF1ZzBmWVMyWUZIZmZXSVp1U3FxTjc2UXFrQkFzeXZzNU02Z2QzOVEyeW9DM05tR0hkcWh2SnJOejFvcjdpQUpybFBaRjZKSG1BV3Zab2RxTG84YWNPMkFUZWVtNittYjFnSWtqclFnYmY4RzMyamhxcmhUQmFIWGorQUYvYnJ1QmZXVFVURDJhRldTWEIiLCJtYWMiOiJiNTgyMTZlZDkzNTAyY2Y5ZjBiZjcyMjE1ZDFiN2M3NWUyOGFiNDY1OGQwMTNhODFkN2U1Yjg4ODFlZDBmYTJjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415756016\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1537126527 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537126527\", {\"maxDepth\":0})</script>\n"}}