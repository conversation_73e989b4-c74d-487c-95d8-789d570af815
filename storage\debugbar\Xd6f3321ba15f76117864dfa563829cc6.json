{"__meta": {"id": "Xd6f3321ba15f76117864dfa563829cc6", "datetime": "2025-06-28 15:43:17", "utime": 1751125397.00645, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.536662, "end": 1751125397.006467, "duration": 0.4698050022125244, "duration_str": "470ms", "measures": [{"label": "Booting", "start": **********.536662, "relative_start": 0, "end": **********.937055, "relative_end": **********.937055, "duration": 0.400393009185791, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.937066, "relative_start": 0.4004039764404297, "end": 1751125397.00647, "relative_end": 2.86102294921875e-06, "duration": 0.06940388679504395, "duration_str": "69.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46278328, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00667, "accumulated_duration_str": "6.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.974754, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.387}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.986298, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.387, "width_percent": 10.045}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9897408, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 36.432, "width_percent": 8.846}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.991607, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 45.277, "width_percent": 40.03}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9960878, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 85.307, "width_percent": 6.447}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.997854, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 91.754, "width_percent": 8.246}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-599278110 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-599278110\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1120373622 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1120373622\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1979544712 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1979544712\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1664183154 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125383583%7C16%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtwWm1oeG9pbjFFZnJ3ZDgwYm9VMnc9PSIsInZhbHVlIjoibVBBQ2tFcmNwU0VsSG0vVE5tSWpxOWIweUpVRnUrNWZLb1QyR2x5bTRHdUZUSGZBUmRtdjI3K3lhQ3E2clRRSEFPVmsxTStSRDJibjBiWExrdHU1dGlUcThnSi9LNktrR2xqUDVvM2tEK0VRdGhZbm1KeU5pSGN5T2VTMldhdm9YOXdjZjQ0VUN5WEFITEhoaDRCMnJtSmFaR2l2Q0VlbzNuU1NQN0hpYk1KQ1F0SHk3TnU5Y1lnVG41NFhVWUNONUFzNlF2V3ovdzQwbzBWTmJqbllaVUdET1YzbURpVzRsdHlyY2V0ekNSbjhVZ2tGTUhiZmRmNUJwMFRPUEhEYjA1TkczUUZpc1czZEtQWllkZGlabU0xRW9Id252L2dDNjQ2MDNzWnJDQk94Q3g3TFVIT0xEYkNiRTZiTTU1L3R3aWpHZlE4dnppNUNWTkdFaDNDZkplRzBqVTFCU29xdHRwNGFMOXJEM29ZSWxjZlVnZ3U4OW1wN0FIZ3EvbWpEUy9Xb3pleU5tVTkwbm9MRkp3amVvK054eWdqNTluUzNEWUhtcjAvcmtQdC9oMENjTVhSaHVXd3lQOU9BYWNJQ21PRmxrU3dKUjRqcSs1OTBrb0VGK2gwRUlzMFQzbDdyRFZ4Wk9SY3lzblMvQUZaYXBoMDA5WXhsb01IQjNLTDIiLCJtYWMiOiIwOGM0YTI1ZjNlMDg3MGYxYjMyNjM2YmU0YTM2NjM5MDc1YzBiYWRiOWE1YmJlM2JlYTZhNWRjMjM4NzI0MjE4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im81Rk9Td0VrK2NETjNqNEM2RTZZWnc9PSIsInZhbHVlIjoiTTJQRmNXV2xCNG9mc01zZUJoM1NSakxNaE83T0xqQm9tVVhyZUhOcWlBOUFqMmZaVCtsbkpZMXVTNjU1TmFmTDk5UlBuRzY1MnZmUVJObU5DWGY4UUNLczJtZjB2SHVkOVdGaFdJeDQwaEpNaGxCQWxMMjFERVM2VEdZM2lGSVMvVk8xVlJmSHI1OUd1alJpNDdaME91ODFkMGdhQ3hrWVpMeTY4eDVWalYvdDl4RXF4S2xJYWlOVEdQODVXdkFHUkdUVkt1UDg0bWtKc1Uxd1FoajEyVzBCR3hGS3VEOVBKZ3VhUHZQb2xhMEppV01BRUV4RGwzR3JYRURwVE1DTkxLRzFvNG92TVFnejJRSFR3akRPanZFQW1QbERWNFlFOGE1OGdOZWlzaWx6R1g2eUdZcUhMQ0VtQURSazhGbzVFdkRxV3lQUSt5bVJDN0Q4Mk5KZ3daRStTUmVtZ2hUUU9wSWttVGNrQTVYUmN6MVdlSThjV2xHY1hTRytGckdPZVlCV3hscWJnRnhUdmJVNERybkNSM1Fqd0xlZHk3RFhVbllOMlVmekFMckJWVHlDUFFHWFhlbk5RVEp0MWhFSndkYUM0S3p4K2xLSnF2QlhUQjlPQXBrYmdrVVBTTm5MWGdQcTlQMzRHUmFlTkVpN3h0b2wyNy90S0ZCVlhaTS8iLCJtYWMiOiIzZGJjZDgyYTQxYzFjY2Q3ZDBjOGVlN2RkNWY4NGNmZmIzMzRkZTNjODY5Y2RiMzJiMDQ4NWZmODZkYTZlODA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1664183154\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-423999612 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-423999612\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1616700757 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:43:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlsOUtmQVYxVXJPSXBNa0dsbENZOHc9PSIsInZhbHVlIjoiakFWSytJYWh5UFEzRWpManFwMjRrbGNjQUNTZjJsUUtCWjF3SU40OWhCVHdhWFhnUjBvUWF3K0JBd2FlNGExaFcwNWM1WHFGOFVGK0lWM2s4MERuWWpiVkg0L3NENmdyMHRFdGRldEFpaUFEamYvYndvc0hWTzBZSURmcnFQNnVQOGxySGRDUkhoRzVraFBlWEJHM2t6NU1SSzhBMThwZUhlUnExV0Y4aEtRWFV2cFV0V29XVnF5dzNrS1FKa20yd3MyUjJpcG8xSVorNncyNERKWkF6Wm8yUTc4aTRZWE5GNldLVVFTTmJCTCs2dFNxK2J3LzVWaEhyb0hkOXIzbldScjlhOHAwWUQ1angrOE0vc1M1TlNhdGdWQUVqZktZUGlxZGkyV1I3clZicmhQMm0yc1FEY09LZXRBS1d4MnRmU0JlVmlJOS9GWC9ob1pmQ0JPb3prdDJJUHA5djViV1gwTWpZWXZTbVk3WHBMS0lNMDZLUG1WYmNIWTFSMENXWStTRUExV3krTkNDTjJ4ODM4eHV3VjBUaXBpK0FEYnNZM0FYNVpkVjMrMzRUZWdTOXdQbWxQNkFIUUFlcUI4SEhBbVdjSHFqZ1FZaHRzV2QyZHBaMG02OUkvWGtmN0FkMkZPcWYxNGd5ZTJIekZhOFVqRjFhcHlJVUVDRk1XY0oiLCJtYWMiOiI3YjhmMTYyZDk1NGUxNGNmM2U4NTRiMjUxMzg5ZTVmZWYzMWNkMzI2OGVkZDI2ODIxMmRlMTkyOGY1NGRmMmUxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlhITVlWY1lBdktyQWVTZkNlR3Y0UFE9PSIsInZhbHVlIjoiYlJ2Qit2UmlXa0NxRnluRG5ZOWtKb1k5aVFIazh6MFlnTkFLRkFvcEM2aU1FWVFKTW50bklXd2pLQ1ZZUWZsaFFDdXd3b2Q1aWZYNzNSOHc5RUdRd2pGTTBuV3hxM0pNRUF2VWJhdjFRYmE0TFBOVVVBOWxzdTZlcU1NdXZmUTRuVk9WM0JRUHRvV3dHR3ArNEd5VjlWdTVsUDk1TFZrNDl0LzRGZjJKQUJLS0xKK243MjRhWmdLV081Zk43WWt3NFdidEpTMm4vQ0cvZ1ZzWElUdVJDU093U01xU0pZUjdDYVhlelpINHhQNGJhUkZmVG80cjBERmpUQ3c5SGFoblJNYmJLSExGYTl3dkdSUTVpOTlYOFFtbHJmaWN3NjUwWktNdElCbUlqV1NLWnZnMU5Md2VjT3RBNzVBS2JTczlDakZ0S2JkZlJUZzZzSWpsQkR3UnVSWkVNMUszM2gxNkVVQ3M1UXNkaUVoZUtqTldScGZFdHI1QWF1NTNIT04zSnZxcjlzL0pMTEJaRDFCUlpwOHhtVHYxRmRvQm00S0ppeEZOYmpPakZidGQ3SFlJMzBtUXhtMElGOGVvMEE1QTFISXQ5Ry9xRENnemFGU29IbjdrSXQvYWFmcTBGcXNZQkJ6M09kSTVCYnlyTXloZ1RWS3p3U3E1bkNRSmViUDciLCJtYWMiOiJkYzA3MmExNDRkY2E0NmI3NzliNTQ5YmQ0OTcxZWQ5ZTVlOTI2ZjM1NmU5ZDcxMzFmNmM1NTVlZjU2M2UyNDIzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlsOUtmQVYxVXJPSXBNa0dsbENZOHc9PSIsInZhbHVlIjoiakFWSytJYWh5UFEzRWpManFwMjRrbGNjQUNTZjJsUUtCWjF3SU40OWhCVHdhWFhnUjBvUWF3K0JBd2FlNGExaFcwNWM1WHFGOFVGK0lWM2s4MERuWWpiVkg0L3NENmdyMHRFdGRldEFpaUFEamYvYndvc0hWTzBZSURmcnFQNnVQOGxySGRDUkhoRzVraFBlWEJHM2t6NU1SSzhBMThwZUhlUnExV0Y4aEtRWFV2cFV0V29XVnF5dzNrS1FKa20yd3MyUjJpcG8xSVorNncyNERKWkF6Wm8yUTc4aTRZWE5GNldLVVFTTmJCTCs2dFNxK2J3LzVWaEhyb0hkOXIzbldScjlhOHAwWUQ1angrOE0vc1M1TlNhdGdWQUVqZktZUGlxZGkyV1I3clZicmhQMm0yc1FEY09LZXRBS1d4MnRmU0JlVmlJOS9GWC9ob1pmQ0JPb3prdDJJUHA5djViV1gwTWpZWXZTbVk3WHBMS0lNMDZLUG1WYmNIWTFSMENXWStTRUExV3krTkNDTjJ4ODM4eHV3VjBUaXBpK0FEYnNZM0FYNVpkVjMrMzRUZWdTOXdQbWxQNkFIUUFlcUI4SEhBbVdjSHFqZ1FZaHRzV2QyZHBaMG02OUkvWGtmN0FkMkZPcWYxNGd5ZTJIekZhOFVqRjFhcHlJVUVDRk1XY0oiLCJtYWMiOiI3YjhmMTYyZDk1NGUxNGNmM2U4NTRiMjUxMzg5ZTVmZWYzMWNkMzI2OGVkZDI2ODIxMmRlMTkyOGY1NGRmMmUxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlhITVlWY1lBdktyQWVTZkNlR3Y0UFE9PSIsInZhbHVlIjoiYlJ2Qit2UmlXa0NxRnluRG5ZOWtKb1k5aVFIazh6MFlnTkFLRkFvcEM2aU1FWVFKTW50bklXd2pLQ1ZZUWZsaFFDdXd3b2Q1aWZYNzNSOHc5RUdRd2pGTTBuV3hxM0pNRUF2VWJhdjFRYmE0TFBOVVVBOWxzdTZlcU1NdXZmUTRuVk9WM0JRUHRvV3dHR3ArNEd5VjlWdTVsUDk1TFZrNDl0LzRGZjJKQUJLS0xKK243MjRhWmdLV081Zk43WWt3NFdidEpTMm4vQ0cvZ1ZzWElUdVJDU093U01xU0pZUjdDYVhlelpINHhQNGJhUkZmVG80cjBERmpUQ3c5SGFoblJNYmJLSExGYTl3dkdSUTVpOTlYOFFtbHJmaWN3NjUwWktNdElCbUlqV1NLWnZnMU5Md2VjT3RBNzVBS2JTczlDakZ0S2JkZlJUZzZzSWpsQkR3UnVSWkVNMUszM2gxNkVVQ3M1UXNkaUVoZUtqTldScGZFdHI1QWF1NTNIT04zSnZxcjlzL0pMTEJaRDFCUlpwOHhtVHYxRmRvQm00S0ppeEZOYmpPakZidGQ3SFlJMzBtUXhtMElGOGVvMEE1QTFISXQ5Ry9xRENnemFGU29IbjdrSXQvYWFmcTBGcXNZQkJ6M09kSTVCYnlyTXloZ1RWS3p3U3E1bkNRSmViUDciLCJtYWMiOiJkYzA3MmExNDRkY2E0NmI3NzliNTQ5YmQ0OTcxZWQ5ZTVlOTI2ZjM1NmU5ZDcxMzFmNmM1NTVlZjU2M2UyNDIzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1616700757\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1764188395 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1764188395\", {\"maxDepth\":0})</script>\n"}}