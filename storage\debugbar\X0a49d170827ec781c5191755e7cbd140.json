{"__meta": {"id": "X0a49d170827ec781c5191755e7cbd140", "datetime": "2025-06-28 16:34:54", "utime": **********.178531, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128493.732165, "end": **********.178549, "duration": 0.4463839530944824, "duration_str": "446ms", "measures": [{"label": "Booting", "start": 1751128493.732165, "relative_start": 0, "end": **********.127847, "relative_end": **********.127847, "duration": 0.39568185806274414, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.127856, "relative_start": 0.39569091796875, "end": **********.178551, "relative_end": 1.9073486328125e-06, "duration": 0.050694942474365234, "duration_str": "50.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45714832, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00269, "accumulated_duration_str": "2.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1533802, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.632}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.163559, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.632, "width_percent": 15.985}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.168711, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.617, "width_percent": 13.383}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IlBJL2kvS2dOcDBUZVVyMTQ3bHlCQXc9PSIsInZhbHVlIjoiT0VoYVhCQjdIdUpOYWpGbStxL2NyQT09IiwibWFjIjoiMWEwZGEzOTBiZDgwNGRjYmNmNzg5MGNjNDRkNjI1MDQyZTA5YTlhMDhkNDMzNjQwMTQ3YTEzNmMyYjBlZTQzNyIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1603850213 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1603850213\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-425752494 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlBJL2kvS2dOcDBUZVVyMTQ3bHlCQXc9PSIsInZhbHVlIjoiT0VoYVhCQjdIdUpOYWpGbStxL2NyQT09IiwibWFjIjoiMWEwZGEzOTBiZDgwNGRjYmNmNzg5MGNjNDRkNjI1MDQyZTA5YTlhMDhkNDMzNjQwMTQ3YTEzNmMyYjBlZTQzNyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128490396%7C52%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdXSFZ1Nnl0NG9obzNPQVNoKzJEZHc9PSIsInZhbHVlIjoieUx1YkhTT0h3a0hrS2dZOHJJTy9oMWNuNVBESE9sdTBBKzVhb0xzc1Q1cGZDdHBxTVhPeis5TDlzdnJjbTQ5dG0rd3R4Rm4vVHprTVNLajJaQTZmaXZjZW02OFdINjJNdXhvbTRZYlRxZzlBN3NxVld0MU1LSVNwR1V6OCt2RU50QUc5MkcxSlZFMWVUYWFGcW5tMkdlQjl2R2JaTjhXK0RBdEU3MTdPTDZCd2RCdm1VYTFiaXZ3Zk9YcERNbGdjYUxVaFlBM3JiWnVBK0o3eWV6Y2o5V0RmcGppUkJnOGM5QXE4TkVoaU9zYXE0NVhaYXhXRTFTZndaNkRVbDJZekpjWmdCaWZLb1RNc012N3lqcXhzM2ljSlo5YVJGejg0L0JkbGdsVWM0NkxOV08rNHJ5Sm13bis4NjlGRHlOUTFqYk1qcy9DakpFbHk1OWR2UExTZDF0T0YzS1M0bkpxWmpLVzAzOVFuUVFRRTZqTUhDYXQ4TTN2NUNvYmJKSDFCMDFGUnd5MUphdjFGMXhhOVQ4OC80cTN3VjNkT0FPL2RrYnptdG9oclFIQlZiWFFSV1g4VDlhQXVqZ3NJNlBjYmxUeFN2SDNrdTJ6aEFtWU1aZ1duSE8zNDVmbkh1cmJHRmlwSEdJVkluTEF2V0N2OWxBRXJUZUpTMHA1TG52OVYiLCJtYWMiOiJmYWM0ZWM2ODBhODQ1ZGQ1ZjMyZWVhNDA1MTUxNWQ5MjQyOGJiMjBlMjEyZTg5YmE5YTIxNTdiYmY0ZWM5MmNiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhXSDdwSmhiNzlvZ1phc3REWllvdmc9PSIsInZhbHVlIjoiU3RHREdFejRXcGxuQ3lXMEloK0RNZEdtcVBqMnEzTWhYdjhvTlhkaEl0SkFFTGMxaWlYS29aM0Z4VlUvNzJUZkhTZ3RIdUVlTktRT2dnTlhWV0FDRjk1V0hoUDhacTh2TUdKNXhUdlZmbER2N3RBWmtneGgzSXhmS2R4RXRLTTgvaE5wUE5zSGxQY1RVNXUrbTliYmM4ajF2dUN6dnhIajZDWWlQTHpuMldKUWRuT2Ewb2JuTXhrWWlTdHlOVkdhRkJ1UE54d2RHQ2JKN1FMTHN0RlV6MCt1b0xNVnlNY3k3Z3N1ZEpqajM4ZmVJNW1xeXY2YTlPVHZGVlRjL1RkLy9pQng2U2VUNjNsclFQWXc3NDkycGYxLytMNktrVFZiemtwSXZMWEl0RjZJUEluOW5Za0tkV2FFNXpBNVR4R282NENoYnpPYm5JTUdvQWtuOFQrb296TXZ6SFQ3SE4vNTBpRnpsNHpIMkJ6S3YzR09ZMmtIcDZVL3Q5OWc0eDBBY1pNeWwzbzFJWUI0anBGOGlIM05YUmFMSUZGZW1EOUFMcTVGck5vUnFqYi9RbHF4cWhobmtWVXBDcnhjRFdLdXdaalhqdzRqNVdaanIzTzkrZldPaXV6bmcraUZIN044ekZMNWN6cEtrNHFReWZFY0ZRd3RWM2pxUGp0dDlJWUgiLCJtYWMiOiI2NzY5YWM4ZjlmNmM4ZWUxYTZiM2MwOTRkNTkyMjVkMDkzMjY3N2M3MGY5YWJlMDhhNmJjNWRmNWZjMmM1YTUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-425752494\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-657901774 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-657901774\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1983652961 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJnejg4eEtZY20xc1VhUXhBQWF1OFE9PSIsInZhbHVlIjoiYzZhYkFvUThlRXMwQlZzY21GSG1laFBjbEI0VWVxeUxPZTJYd0QzUHViZ0lLcUZaNUFUNmkvZmN3YTdCT0tab3hPeGNva2w4RTc4bnpGRVpMTTRNcStrUmpSUFRSVmloMkVqd29jS09LVEcrSjhGQVVEcXF6RndJS1ZmS3J6QlU4RlVOd2QveisrY045TjkrRDgyRkRkNFlBa2tLSm1NelFMY1c4aVRDNmVvSkNOQTMvWWJCQjAySHNGZG1TY2I5cjhEb0M0ZHJYM3VpUG9mMlNPdEpLbXZXREo2UTBFK0dPR1ZOMmJMVDM2aXhrZnRZYWxzeWVkUWlFeVN3L2kxZm5DZGpOM0pVSEhMNTRPbFkzVzY2VXA5K0l6QUpQRkFRQldEYWdtU3dLRUo4cHRmVTBiaVBxS2pZTFQ0eENkamVRcEcvNC9LOG5KdnBFYUgwclF5ZmJERXFRZndRdEM1K01TS1F4d3VZcFdLV1ZBZ0lGTDM5OVdXYXFmaE16YkcramRjVXRPV0c1QWRSc20xU0RFRUVPSnNzVGd0YkJzVHlvTjB6VVRlck5qZzRsY25rRU5WeWE1RDZIV0ljWGVpTTdqbGdsOVVLYnVzRFhDTCtjU3N0ZnovaFNFYnI2bjBERjJNeFlnVjM5ZGlKc3RyS1hPZFVtWmZhVFBjRndpcXciLCJtYWMiOiIwZDBmZTk4MTJkYTU4MjllZWIzYzIyODZlZDEwZWJmNThiNjRmNDc4OTY1YzNkNzU4NTM2N2IxZjVhNTY1Yzk1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik4xZXhhaFZnYkJUSEVBL052bDd0YlE9PSIsInZhbHVlIjoiRWh1Z0t6TVducVozR0ducEhlMnJwNERQb1BPOTJjdGhFSC95SU1MVXBQdEF5QnpqUnFCNnF5K1BDdytQZ3hPcTdBVERsVlhFejRmU2NHYjhEbm9laUJuN2hXblpnU1g0T2xWRUt5RUpnWjR5L3cvOVZzS3pZUzdaNG1qZzlRNUxUUXFtR2szVkJIcjJ6SG5YVzBZTUVUVURuZUpzdFdDU0dYTU14cHFSaVh6Q1F6VFNzQnRvQnE3bjV1dkpqbWp2YmgwWm5XVTRpQ05OSjNpOXU1S095cnp5ZTc3S012MTU3N0ZBL05TVmxXVWg0THZ1MHNpRlpidVpYNkJ0T0tHZkxYc2ZaY3VSNzZ4TlZCazZMS2FOanVkbk5KOXk0QW43T1lTSzIzcTM5M2h0cTFPMG5Dc2k0eDlyR3lYbWhPajgwUENWelp5UTdnTUhYVTh5a3pyT0xPS0l4WW9xRDZHNkNNWWdFL2FmenZpOXBCMjhrT25Tc0FLZEgwb2RiQk56OFloVDVXSU1mck01aWFmOTFjU2cvUm9uN25odTF5ck1OWWMxR1lqMk9JdXdqRFcrQ1BKeUNKeElCYXcxaVdZOG95Z2pwUHBwRHhUYWxWM0d4RmZqbCtEMU0vV0lIeDk4bnhWYitDSjJNTEE5djBzbnRuZ2lHOStNcTdUeXEvV2giLCJtYWMiOiI2ODQ3ZjhjMTk0YjRhN2M5MzZmMzU1N2EwOGU4MGYxY2IwODNhMDE5ZDQzNmZhZWQ5Nzk3YzU5MWYxODQxOWNlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJnejg4eEtZY20xc1VhUXhBQWF1OFE9PSIsInZhbHVlIjoiYzZhYkFvUThlRXMwQlZzY21GSG1laFBjbEI0VWVxeUxPZTJYd0QzUHViZ0lLcUZaNUFUNmkvZmN3YTdCT0tab3hPeGNva2w4RTc4bnpGRVpMTTRNcStrUmpSUFRSVmloMkVqd29jS09LVEcrSjhGQVVEcXF6RndJS1ZmS3J6QlU4RlVOd2QveisrY045TjkrRDgyRkRkNFlBa2tLSm1NelFMY1c4aVRDNmVvSkNOQTMvWWJCQjAySHNGZG1TY2I5cjhEb0M0ZHJYM3VpUG9mMlNPdEpLbXZXREo2UTBFK0dPR1ZOMmJMVDM2aXhrZnRZYWxzeWVkUWlFeVN3L2kxZm5DZGpOM0pVSEhMNTRPbFkzVzY2VXA5K0l6QUpQRkFRQldEYWdtU3dLRUo4cHRmVTBiaVBxS2pZTFQ0eENkamVRcEcvNC9LOG5KdnBFYUgwclF5ZmJERXFRZndRdEM1K01TS1F4d3VZcFdLV1ZBZ0lGTDM5OVdXYXFmaE16YkcramRjVXRPV0c1QWRSc20xU0RFRUVPSnNzVGd0YkJzVHlvTjB6VVRlck5qZzRsY25rRU5WeWE1RDZIV0ljWGVpTTdqbGdsOVVLYnVzRFhDTCtjU3N0ZnovaFNFYnI2bjBERjJNeFlnVjM5ZGlKc3RyS1hPZFVtWmZhVFBjRndpcXciLCJtYWMiOiIwZDBmZTk4MTJkYTU4MjllZWIzYzIyODZlZDEwZWJmNThiNjRmNDc4OTY1YzNkNzU4NTM2N2IxZjVhNTY1Yzk1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik4xZXhhaFZnYkJUSEVBL052bDd0YlE9PSIsInZhbHVlIjoiRWh1Z0t6TVducVozR0ducEhlMnJwNERQb1BPOTJjdGhFSC95SU1MVXBQdEF5QnpqUnFCNnF5K1BDdytQZ3hPcTdBVERsVlhFejRmU2NHYjhEbm9laUJuN2hXblpnU1g0T2xWRUt5RUpnWjR5L3cvOVZzS3pZUzdaNG1qZzlRNUxUUXFtR2szVkJIcjJ6SG5YVzBZTUVUVURuZUpzdFdDU0dYTU14cHFSaVh6Q1F6VFNzQnRvQnE3bjV1dkpqbWp2YmgwWm5XVTRpQ05OSjNpOXU1S095cnp5ZTc3S012MTU3N0ZBL05TVmxXVWg0THZ1MHNpRlpidVpYNkJ0T0tHZkxYc2ZaY3VSNzZ4TlZCazZMS2FOanVkbk5KOXk0QW43T1lTSzIzcTM5M2h0cTFPMG5Dc2k0eDlyR3lYbWhPajgwUENWelp5UTdnTUhYVTh5a3pyT0xPS0l4WW9xRDZHNkNNWWdFL2FmenZpOXBCMjhrT25Tc0FLZEgwb2RiQk56OFloVDVXSU1mck01aWFmOTFjU2cvUm9uN25odTF5ck1OWWMxR1lqMk9JdXdqRFcrQ1BKeUNKeElCYXcxaVdZOG95Z2pwUHBwRHhUYWxWM0d4RmZqbCtEMU0vV0lIeDk4bnhWYitDSjJNTEE5djBzbnRuZ2lHOStNcTdUeXEvV2giLCJtYWMiOiI2ODQ3ZjhjMTk0YjRhN2M5MzZmMzU1N2EwOGU4MGYxY2IwODNhMDE5ZDQzNmZhZWQ5Nzk3YzU5MWYxODQxOWNlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983652961\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlBJL2kvS2dOcDBUZVVyMTQ3bHlCQXc9PSIsInZhbHVlIjoiT0VoYVhCQjdIdUpOYWpGbStxL2NyQT09IiwibWFjIjoiMWEwZGEzOTBiZDgwNGRjYmNmNzg5MGNjNDRkNjI1MDQyZTA5YTlhMDhkNDMzNjQwMTQ3YTEzNmMyYjBlZTQzNyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}