{"__meta": {"id": "X73b7d6b1ac86d17bcf933292f4701f53", "datetime": "2025-06-28 16:30:55", "utime": **********.95204, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.584791, "end": **********.952053, "duration": 0.3672621250152588, "duration_str": "367ms", "measures": [{"label": "Booting", "start": **********.584791, "relative_start": 0, "end": **********.894275, "relative_end": **********.894275, "duration": 0.30948400497436523, "duration_str": "309ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.894284, "relative_start": 0.3094930648803711, "end": **********.952054, "relative_end": 9.5367431640625e-07, "duration": 0.0577700138092041, "duration_str": "57.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45874616, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00698, "accumulated_duration_str": "6.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.924801, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.063}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.934155, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.063, "width_percent": 7.307}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%جالكسي كيك الكراميل 30جم%' or `sku` LIKE '%جالكسي كيك الكراميل 30جم%') limit 10", "type": "query", "params": [], "bindings": ["15", "%جالكسي كيك الكراميل 30جم%", "%جالكسي كيك الكراميل 30جم%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.936676, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 29.37, "width_percent": 34.814}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9417899, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 64.183, "width_percent": 30.372}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1995}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9461288, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1995", "source": "app/Http/Controllers/PosController.php:1995", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1995", "ajax": false, "filename": "PosController.php", "line": "1995"}, "connection": "kdmkjkqknb", "start_percent": 94.556, "width_percent": 5.444}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1465/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-2048617707 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2048617707\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-533243938 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">140</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128238007%7C50%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkI2Y1dkMzdQdzd1U1gxVVBOTHIyR2c9PSIsInZhbHVlIjoieHcvQ2daQlJsd3FldVZTZmlER0NwZitSVjJnT3FZTWpmaVorb3B5UG9Oekt6OFA1Y2pITisvMkhNU01aaUFMWjZocWtzTWdyUmhDSDYyUFl0aGZBVFRURmJiQVRndWVsVVB5THdGQno4dUQ0QnMyNk9Ta0JyZHlNakU0NitUREhGaVNadi9zNnNaTkI2dTJiYllSNXdDZHpCODhKMUJLaTRXbFhNemt3M0JjN1FaRXowLzZPdlYzR012RUZKVCs4QTdqMWZ2MlNEVnYyeGlYUXdWbUJjLzZ4UXZweklVZFdET3Z2Q1hERWNQWVVYa2pIWjRkSnJ2YnF5MCtOeXZlSzhsYWVhNmJGTFVBcXFoM1JReGVrOVh5eFM2OEs1YXRYcmZrUFoyaHlsSlBRcmwwbm5ZdlQzcU81WnV5aS83K2R3VXQ5elBuZ2laZ2RsSEFYVnBPSmZ6QkdEQnl5M0l6anByM2tjNVZhbkhHUjN4RjEyN1VDSEhBM0wvQ29SKzBhZWNUc1g2YmJtdHRIZ1FCY0c3ZmNqMWdESkllcGxEUjY1cksxMnh0eEsvMEZCUlJwbXUrM3FHMDFSbWJYUjc4Y25CT0Rva0c4K21xc2JYakxvUUtmc3pSREVLYVVzUEpHRFVxbFNPQUlKZ08xOTBDS0pIa2ozdWZydURkMGg1amkiLCJtYWMiOiJlOGE3MTk0NTczNDc5MzRlY2U1YmFkNDY2MjU4ZTFkYzc5NTkwYTMyYjE4NzA2ZDFlMWU1NDZlMWNhNzgzYzhiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNSWWtlVWtjaFhTS2ZIM3ZkVEdmOUE9PSIsInZhbHVlIjoiYW9rb1hsL1hIbDVFQVpkQ2VwQ25CU0hVZFZBbTdWcTVVSldoWFdlQitqWXlhdzFTTzRFckJBZ3RrNUJ0QWtBQ1VDS1RrcjlkbzdtNHp6bFFiS0JPOTd6R3VtSWxneVlCRXVrMWdZMFN5YWMrZERWNUpkVStveHpOcW44ODkyMENWSDZ1c1NYa2k4dmRWZTY1UGE2eFlmNWpYMmFibkZHTStHR1Myd3JPQzU0U3NVcmVsSnBvMVRSbkZqQXZhVjJ3L0RIYjAwTk5mTmRZVTFyLys5ejdKOXlQWHludjNwL3hVR0VLQjcrZ1dQZXBqcVdMek94QXpXWmhrb3hBZmZXTHlPQm52UE1LcWRsallVcmtzZWh5bE9jbGR3SG9XK1NMbHZ3OElQbDErWXBEejhuVTQ0a2g0UXlyR21HTjJKQTZHZlBNMVN4dm5jaGtlcldhOXM5aHNlS2sxbUJqMlhKYzZqNG9paW15eVZHUE5iSDZsMnBZdEVGQzJwT1lQOVVUWXB2RFRXY3FhUFUvRGJSTnR6VmZob2VRRXFEZ1gyYzRRZnlMT1hpcjIzRWZMSDlHeWxIbFVCV3BjUjl3cnpKUEFwVE9rM21nMUhhUk03Y1ZnbjN0VDlaYTYvNnFEOW9aWnVkWTV3NStqd1JhU2xQVW9pcHZUSVhkcWJvZU1YMk0iLCJtYWMiOiIzYTE3YTgzMzQ1ZmJhYmY5YTRjYWQ1NDIwYmNhNTk0NmNlMWQ4YjZmZTUyZDEyYTI0NGZmNTIwYjc2MGE0NDVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-533243938\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-710993459 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-710993459\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-802103827 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImIwK2x1QU0vbmFKY1k4SlZxb2orZEE9PSIsInZhbHVlIjoiWHBsUHB6SkFUSldTUm12VUhXdUl1YzVWRkRKcDlCVmNrdWdUTjdTYXRHcUdaTTU5U0VqT0thVlBYcDNHYUloRHUwRG5qcS92dDRNYzVqVUU5QTVQRU1DdUhNcXEreERPK0lXbDcrMjdiV0dzeG9ZTFBtVVFrQXBPMUdCNzZDREtYNEZPcW95clhsSWc3MWsvRWJnVlAzNlduSnNVcTBiWlhORVlyRyt0ajJYb0FVTzFLeW9ZdjRjQTlNc3pxTTNjYWpKbjJOcFdlWnhGQTJlL0dpMU5kZXZCelBUWjZuQ0k0TDdZTHpXVlhpUzBuQ29wdGl2TnlmRnNESE1NUVE0bm56MEthb2ZueE9UTHlZMWpoYXlVZmZoZ21rUjJuM0VMd3VJZ2lRY1J1R3pwb1J3djNPQ1NtZUVjaTFtY09TclgvM0hDSkM3eHNXbE1TK3BZZUZGRVd4SWhqNHJVMlJiZGdYdmhmYlNzbUJEWGpCUFRzamMzQ2hkSDVGaHdzaDFHTENLQWMrekFTSnFKNlZ4dm9pY1BCSmY3N1lJUkpCdExUbE02Ym1lNDdjWjR5dHNQVHJBUVpFSzU4ZmdUbzdNdk5Xa0ROOXljMmsvd20ySC90c2ZVK3lhZmJubHByVXBhUlBRTjViSGxEbjM2bkFhRGE1Z01BN0xRVTZ5VHZVT3ciLCJtYWMiOiI4OTFlZjgxNzAxZjc0MzAzNDdkMjk3OTNiN2RmOWY1ODc3ZGMzOTk5NDE0MjRkODUxMTlmYTUyNjEzMmQ0MzA0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijlva3RJZ0FtalFXd3J6Ty9vaW4xWFE9PSIsInZhbHVlIjoiMWcvRWFhbEUxT0NUZnlGZWxiOFhteVNtR1daNFEwdURCeWRnNi9jdW1CQW5KVjA1ZWtTVWFHbFRpbXg3cGVwRTdhQURQUHYvZjFYMllEdjBLdmJPbEt2UjV1OE9QekJWb3M2VGErRmhYWjdadThFOG1GRE5oZGN4UlY3dXVlYkdtdzhpUTZqVFFqYlNadGJiMkF4Smo0Y3BTZkoycG5PWDU0eWVRdW1oRW1nTTVmem91N3ZYcFNZeVk0ZzN5Y2grN2hEcVJURFVoZUdNcGRuMHhOcXVveUxMOGNaMStpRkNWU3NBalI0VG0yYm5iTnlPVEwzWEpCcHdhZXcxbzYveXZxYkp2UFpLenZKanlJM0hTcjhJdTNlVWJXWUFEZGZsRFd5enhmNllnMllkYnpjbTEyd01vTHcvYWlnU0RJekhxUmtRUkg1NmxQSmEwaFpENlFndVQ2Z01VTHJJeHdMSlFsOW15a09FRnl0L3hMcjdlR0hkQmdiV25rSXlvaGJQUHMxZU0razZDNTJuVzNMM25OUDBweWQrK0Y1SThUS1Ftalcwa3JqRW9ENWs5ME42OTVwT1lpWmpBa1RNc0hBU2hLQ0g1WEhOQitXZFozWUdCcThIL2t2SjF4eHZ1RDVQVG5raGZ0dTNlaENpbk1zYWdyK2owVXBucVV5QmZ4cDMiLCJtYWMiOiIzZTA0MDk4N2NiZWJmYTY4MTM1MGNkNmE1MzdkYzliM2IzNDhiMzExMThhNDU0NTM3ZWFlOWQwNTg1NDE4M2VkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImIwK2x1QU0vbmFKY1k4SlZxb2orZEE9PSIsInZhbHVlIjoiWHBsUHB6SkFUSldTUm12VUhXdUl1YzVWRkRKcDlCVmNrdWdUTjdTYXRHcUdaTTU5U0VqT0thVlBYcDNHYUloRHUwRG5qcS92dDRNYzVqVUU5QTVQRU1DdUhNcXEreERPK0lXbDcrMjdiV0dzeG9ZTFBtVVFrQXBPMUdCNzZDREtYNEZPcW95clhsSWc3MWsvRWJnVlAzNlduSnNVcTBiWlhORVlyRyt0ajJYb0FVTzFLeW9ZdjRjQTlNc3pxTTNjYWpKbjJOcFdlWnhGQTJlL0dpMU5kZXZCelBUWjZuQ0k0TDdZTHpXVlhpUzBuQ29wdGl2TnlmRnNESE1NUVE0bm56MEthb2ZueE9UTHlZMWpoYXlVZmZoZ21rUjJuM0VMd3VJZ2lRY1J1R3pwb1J3djNPQ1NtZUVjaTFtY09TclgvM0hDSkM3eHNXbE1TK3BZZUZGRVd4SWhqNHJVMlJiZGdYdmhmYlNzbUJEWGpCUFRzamMzQ2hkSDVGaHdzaDFHTENLQWMrekFTSnFKNlZ4dm9pY1BCSmY3N1lJUkpCdExUbE02Ym1lNDdjWjR5dHNQVHJBUVpFSzU4ZmdUbzdNdk5Xa0ROOXljMmsvd20ySC90c2ZVK3lhZmJubHByVXBhUlBRTjViSGxEbjM2bkFhRGE1Z01BN0xRVTZ5VHZVT3ciLCJtYWMiOiI4OTFlZjgxNzAxZjc0MzAzNDdkMjk3OTNiN2RmOWY1ODc3ZGMzOTk5NDE0MjRkODUxMTlmYTUyNjEzMmQ0MzA0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijlva3RJZ0FtalFXd3J6Ty9vaW4xWFE9PSIsInZhbHVlIjoiMWcvRWFhbEUxT0NUZnlGZWxiOFhteVNtR1daNFEwdURCeWRnNi9jdW1CQW5KVjA1ZWtTVWFHbFRpbXg3cGVwRTdhQURQUHYvZjFYMllEdjBLdmJPbEt2UjV1OE9QekJWb3M2VGErRmhYWjdadThFOG1GRE5oZGN4UlY3dXVlYkdtdzhpUTZqVFFqYlNadGJiMkF4Smo0Y3BTZkoycG5PWDU0eWVRdW1oRW1nTTVmem91N3ZYcFNZeVk0ZzN5Y2grN2hEcVJURFVoZUdNcGRuMHhOcXVveUxMOGNaMStpRkNWU3NBalI0VG0yYm5iTnlPVEwzWEpCcHdhZXcxbzYveXZxYkp2UFpLenZKanlJM0hTcjhJdTNlVWJXWUFEZGZsRFd5enhmNllnMllkYnpjbTEyd01vTHcvYWlnU0RJekhxUmtRUkg1NmxQSmEwaFpENlFndVQ2Z01VTHJJeHdMSlFsOW15a09FRnl0L3hMcjdlR0hkQmdiV25rSXlvaGJQUHMxZU0razZDNTJuVzNMM25OUDBweWQrK0Y1SThUS1Ftalcwa3JqRW9ENWs5ME42OTVwT1lpWmpBa1RNc0hBU2hLQ0g1WEhOQitXZFozWUdCcThIL2t2SjF4eHZ1RDVQVG5raGZ0dTNlaENpbk1zYWdyK2owVXBucVV5QmZ4cDMiLCJtYWMiOiIzZTA0MDk4N2NiZWJmYTY4MTM1MGNkNmE1MzdkYzliM2IzNDhiMzExMThhNDU0NTM3ZWFlOWQwNTg1NDE4M2VkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802103827\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1465/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}