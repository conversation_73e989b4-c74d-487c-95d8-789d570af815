{"__meta": {"id": "Xb3cf6a9086e43ce91c30d5787e9b9348", "datetime": "2025-06-28 15:08:53", "utime": **********.7875, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.338191, "end": **********.787514, "duration": 0.4493229389190674, "duration_str": "449ms", "measures": [{"label": "Booting", "start": **********.338191, "relative_start": 0, "end": **********.721005, "relative_end": **********.721005, "duration": 0.3828139305114746, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.721014, "relative_start": 0.38282299041748047, "end": **********.787515, "relative_end": 9.5367431640625e-07, "duration": 0.06650090217590332, "duration_str": "66.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45692632, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1910\" onclick=\"\">app/Http/Controllers/PosController.php:1910-1968</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00729, "accumulated_duration_str": "7.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.758514, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.222}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7681298, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.222, "width_percent": 8.368}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*************%' or `sku` LIKE '%*************%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*************%", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7710679, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 30.59, "width_percent": 34.431}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.776488, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 65.021, "width_percent": 30.864}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.780996, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 95.885, "width_percent": 4.115}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1864458265 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1864458265\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-197149673 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-197149673\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1434729260 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434729260\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-580500528 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123332501%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdKNUdwMEgyNTQ5ZFNPS2NzRlhsRlE9PSIsInZhbHVlIjoieUNHZXVva2h4U2FEZlJQYWlmK3VwZm5IUjQrdmV3K2JNVFc2U2lqTE5rUkNwdGhJQU1YNld3M3o3TVBCVE1BSEQvYnVPZ3RUUEdjbU9CVEg2aFQ5VFZ3STZKaktCdzFWaTJDTjNmcWlXek1Kejdjb3BWRU1USE02cHNmMExjcDB3dTQ0RTFUZmhIR3FJclBjT3NRU29NUHZQM3o3M1dWNnlKamFKK0o2Zkc2ZHBxKzhNeWhRMC9tYUgwZWRTUGFGQStZUE5BV1hLbnlaSVJ2RUlERmNtbFZSbmpWS1dCMTBwNkJzM2FjUFVBYmRNa3ZZNDNVcnRJWmlYL2VLN3U4c2hEYW1kbVN2VkFOaHJlUE9Jbko3dVhORURqQ0pTQWtpTjhaTGFIbzdSNmFZT3ZHemFDUlU4ZElROFZtclc0VFB0QmJPY2ZVbGtQUTRTMWRJOWp0NWVSNDJzNXlBZjViLzllSTdWaDR1aXNIMzlLamxKeis3WURLRmFTTGdJRktNRjBBU1JUTE4zSi9ZNXY2RXg5eVp1dHcxTmZGZXQ1REVVNWZaa3RnQnVuY3ZtRExCQjZQSjVDSjl2MExEUVdxU09pWUJ0S0FXM3d3UUg5MmpMbzN3RzFZVHBrbDd5ZytSc0JiMkNQdUFYNWNRUGlWMWN2RkxZeC9YWXZlNTNlRXIiLCJtYWMiOiJjNDkwODg0ZjgyMzk3NjYwYTYzYjk3MTRiMWQwMWY2MDQ0YmVlM2UzM2UwZGI0ZmE1ZDY5MDFlZTA5ZTQzMDdmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitWNjlTemtYMlA1YUprclcxNlJya3c9PSIsInZhbHVlIjoiZ0Z4cjQrb1FadlJYSGVMWlYrVjY5aG5xMXVXRHFwZzlsc1pReDRqTDY2dzJBMHhML2V5bldZU2duTHJEM3dVbG9QSmZONHBuSS9QbXRVMnlUeDJjalo3ZnYzSmtzeFY0TGNkZEZ1bUN0TStvQ0pnSmpkakhGQ0lEWWFuZi94dkp1aXB3TS9CbHlUd04vaGdoUGtkSUZiWURVcXIydGRTckp3b1V2TGpsZk9hbGdnTUdOR1c1Q2NmOTZlN1Uvemo3TE5TTEhseXlhVUNhSFlJN1ptZTE0eG02MWZBdjV4RWtVTEtrTEtyRW1mVG1oYkpDQVh2ZkNCYzViRkkyS01LQXhkcW95VUNXazdYVyt6RFdpcGtQdmRyamlUV3d1bGJwNHlKZWNHVTN1WWhjRDR4YisrdkhJK1gvcm1RMjExU0pWdEpzRW5LRFNUZm8wK0JmUkdobGhGQWluUmpHWWRTeDFWN1crOWZzSlk5eU8yNGc2L1RjVUFoMUs2WUtmTzNGeHdPcCtMTDl0L3hxZXdmMGZscUo4UjNMNkhaUk5QMkpBUFZJWUNtMWs3ZFVtTzQ4SklXZGZkK1pOVG14WDRtMG8xOFZINkhnTlVRRmkvdGtBMkwxMWQrdU9LZDQvbkNnZWgzbGZ4enVGMjZNMG9kUFgyT3VaN2FvQ2Q0V2FMRWciLCJtYWMiOiI5MDQyZjVlZDI4ZWYwZGFmMDFhYWEyY2Y5ODM5MWY5YzZmOWFkYzI2OTM2NTUxMjM4NzlkYTUzMzk3ZmIzOGIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580500528\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-309575151 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-309575151\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjA2bTY0MzY5NlBvRmJlMmZMUjg3U1E9PSIsInZhbHVlIjoiTmxiRUZQS1lEREZKQzQwZzlzem1EYk93V1p0RzRrN0RWZkx3OTFaUWt3LzA4dTdEUmE2dWk5OGwrd2ZNYjBmdjdnRkJDbHNjb3Y2RzlMbStuRDFudUUxNEpORnVGOUMrSVVpV0p6Y2xtMWhWenF2dUdnanZOSDF1TFdKS3JnTS9YSUlERnhZUzdXVGg4RWpUdTU5VWNCZ2xKaTlrQTY1a21KUGluQUVQUVVpNGxMWWx2UTZrT09pMXVmVkNUMm52Z2tUQm1RUThuNnFkS0RhekVYSmh1TzcrWThOWTBRK05xY1EyMUFMUmVNbW1lRkw5eUI0OWtFdjV4SldBRVdGa2hXRjUxVTN1SXZRMlVmdXNnaWZ0dXhqOU9Lekd1cXk3RzI0aWxNY3l6Q0dra0tDQVpkV2FoT2psVVAzSTQ0NjQ4NGVCSHJIVUQxMElCbkNkRmVCU1JuamNFd0thZlNKeHZzSmQ0UFBMTHJXZFhic0cyUjhhakttQ0xQbEkyVjlpZEJ0UXhhRE0rVU5wdThaMlIxZENIa2VwVitzK0VTY3A2M2ZKZkZ0S0plVEx2VFpqWkFTNVArTFdacnUvSEc4WWlUeHhvMmcwNWFMblk4NHlVeUVkWmtRVUNQeUFveUlYS21IZXI3RU5TVXZhYmluZjBKb2haN2hZY2lRZkxWTkoiLCJtYWMiOiI4ZTk4YTkwNjcyOWIzMDBlZGMzZWI5YWEwMzk4NjIwY2ZmYjQyZjJmZGVkNWQ3NWY4Y2EwNjUyNTg4MzM1NDM0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRoam5TQ0NVRjE2bk52RFBwRkNkOWc9PSIsInZhbHVlIjoiUVhRU2JwckRMQnN4SWVkZmJFUVJiQ0JyQlhURjVxcUt0U2FkTmJjbGpnTXJEbUZGRm1kSGNnNGl1UkM2cFlPbFZMMzI4M0pMTlgxM2xGOHlSNnVsbDcvd3lXcnBaWDhYTEJ0REs5cVpDRFRWY2VRUk5CS3N5M1FQdmtQeHN4WDk1amc0cDdEc0pXZVdvZ1hxT1JDWUVrRmRNanpGRXJibWthdy9BR3dlOFE2ZnhsKzI1ZjVsc211SzlxbEQxZ3AvWWU2NkR2UTEzNUpabE56RnhNc2RtaXlOQzRVaVlmMXJyMWgxSGtKckRBWE1NVDdIbXlFSnEwQUZJdDhQWkpsaVArMWdmaVZxbGo5clV5aWU0bXIxQVFLOHh5bDIzWVR2ZlFMdUF1eXVnbWVwd252WjVYODJnTVN5NW5STER2aTZtQW12QnFYSjhPQkRhcGVsR05MU0FSYWtpcEJPeWcyRnlHejhSc0RvQ1pyV0RyVGd3aDUyRXErZko4cFZuQXRSd0hyRDZ6a2pxYXBxYUdVczZEaFdSL2FuU09zaXMxaUFLOW9lY3VWbWQ1RnZ0U3NaMDAxaTJVQThUKzlYaEZickg4Z3VJNjZuZUhSUjFyWGJ6cW9WWTlHZnZkNGg3RHdIU3pnblBXWTB2OHQycm9pcTZ2a1g4VVVpMDVZWXVNVmIiLCJtYWMiOiI0YmNiNGQ3OThkYzMyOWEwY2ZjOGY5OTQ4NWIzMmRkMTQ5YzdjZGNhZTZiM2NjZjYzMTgxZWFlMGVmNTliNDlkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjA2bTY0MzY5NlBvRmJlMmZMUjg3U1E9PSIsInZhbHVlIjoiTmxiRUZQS1lEREZKQzQwZzlzem1EYk93V1p0RzRrN0RWZkx3OTFaUWt3LzA4dTdEUmE2dWk5OGwrd2ZNYjBmdjdnRkJDbHNjb3Y2RzlMbStuRDFudUUxNEpORnVGOUMrSVVpV0p6Y2xtMWhWenF2dUdnanZOSDF1TFdKS3JnTS9YSUlERnhZUzdXVGg4RWpUdTU5VWNCZ2xKaTlrQTY1a21KUGluQUVQUVVpNGxMWWx2UTZrT09pMXVmVkNUMm52Z2tUQm1RUThuNnFkS0RhekVYSmh1TzcrWThOWTBRK05xY1EyMUFMUmVNbW1lRkw5eUI0OWtFdjV4SldBRVdGa2hXRjUxVTN1SXZRMlVmdXNnaWZ0dXhqOU9Lekd1cXk3RzI0aWxNY3l6Q0dra0tDQVpkV2FoT2psVVAzSTQ0NjQ4NGVCSHJIVUQxMElCbkNkRmVCU1JuamNFd0thZlNKeHZzSmQ0UFBMTHJXZFhic0cyUjhhakttQ0xQbEkyVjlpZEJ0UXhhRE0rVU5wdThaMlIxZENIa2VwVitzK0VTY3A2M2ZKZkZ0S0plVEx2VFpqWkFTNVArTFdacnUvSEc4WWlUeHhvMmcwNWFMblk4NHlVeUVkWmtRVUNQeUFveUlYS21IZXI3RU5TVXZhYmluZjBKb2haN2hZY2lRZkxWTkoiLCJtYWMiOiI4ZTk4YTkwNjcyOWIzMDBlZGMzZWI5YWEwMzk4NjIwY2ZmYjQyZjJmZGVkNWQ3NWY4Y2EwNjUyNTg4MzM1NDM0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRoam5TQ0NVRjE2bk52RFBwRkNkOWc9PSIsInZhbHVlIjoiUVhRU2JwckRMQnN4SWVkZmJFUVJiQ0JyQlhURjVxcUt0U2FkTmJjbGpnTXJEbUZGRm1kSGNnNGl1UkM2cFlPbFZMMzI4M0pMTlgxM2xGOHlSNnVsbDcvd3lXcnBaWDhYTEJ0REs5cVpDRFRWY2VRUk5CS3N5M1FQdmtQeHN4WDk1amc0cDdEc0pXZVdvZ1hxT1JDWUVrRmRNanpGRXJibWthdy9BR3dlOFE2ZnhsKzI1ZjVsc211SzlxbEQxZ3AvWWU2NkR2UTEzNUpabE56RnhNc2RtaXlOQzRVaVlmMXJyMWgxSGtKckRBWE1NVDdIbXlFSnEwQUZJdDhQWkpsaVArMWdmaVZxbGo5clV5aWU0bXIxQVFLOHh5bDIzWVR2ZlFMdUF1eXVnbWVwd252WjVYODJnTVN5NW5STER2aTZtQW12QnFYSjhPQkRhcGVsR05MU0FSYWtpcEJPeWcyRnlHejhSc0RvQ1pyV0RyVGd3aDUyRXErZko4cFZuQXRSd0hyRDZ6a2pxYXBxYUdVczZEaFdSL2FuU09zaXMxaUFLOW9lY3VWbWQ1RnZ0U3NaMDAxaTJVQThUKzlYaEZickg4Z3VJNjZuZUhSUjFyWGJ6cW9WWTlHZnZkNGg3RHdIU3pnblBXWTB2OHQycm9pcTZ2a1g4VVVpMDVZWXVNVmIiLCJtYWMiOiI0YmNiNGQ3OThkYzMyOWEwY2ZjOGY5OTQ4NWIzMmRkMTQ5YzdjZGNhZTZiM2NjZjYzMTgxZWFlMGVmNTliNDlkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}