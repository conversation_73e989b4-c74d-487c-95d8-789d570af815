{"__meta": {"id": "X0e6a796b244f01b8d82483e35fd503e0", "datetime": "2025-06-28 15:04:30", "utime": **********.829056, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.343234, "end": **********.829074, "duration": 0.48583984375, "duration_str": "486ms", "measures": [{"label": "Booting", "start": **********.343234, "relative_start": 0, "end": **********.772506, "relative_end": **********.772506, "duration": 0.429271936416626, "duration_str": "429ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.772514, "relative_start": 0.42928004264831543, "end": **********.829076, "relative_end": 2.1457672119140625e-06, "duration": 0.056561946868896484, "duration_str": "56.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45068624, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00286, "accumulated_duration_str": "2.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.802458, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.077}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.814475, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.077, "width_percent": 13.287}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.820976, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.364, "width_percent": 13.636}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-155426528 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-155426528\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-942835331 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-942835331\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-934504432 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-934504432\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123069134%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkI5LzBGR1JrSE5tM3plSHFUZVB5elE9PSIsInZhbHVlIjoiMWJnWVROdlg5RHJYTmhDbFFPV0JhaC9VSGJvQVdDUFE5VkxLUjlNU1dRbHg2dDUrZzJUZzVqeVdQSWJQRWtjMjRiOG5IT3VZTGRlUFd0bS82OFRDdmp3NVlWQWlyeEc5WWNTbHJVVnlsNWJqcUppdk0zcUxaOVd4djhQaE0vSG1JT0ZzRitrNFhHbSttLzZVc3FpYy8wRFdNWkRBK2pML1NTSW0wRTZxT0pkY3NZdENiZ0xHSnRldXM5Y0dPYXdXeWhwQWNCeFZCWHpDQmJraklsNitRYTRQWFVkNVlGK1o0T1dMcDRoWW9UL3NnS2xtQUtITlltN2cyaDFGKzB6N1g3ZGVUendKT2dyamFQN25XaTY4YWN6K1Nxc1NUa2VYR0dJT0pPd2pVVXNsMHQ5Wjd0QktkamFTRmRlOWtpVEpHNG5YYW5VQ2xrTXdpK0xlMlh3MlFFN0c5TjZOcFlsMzEwYVVLRWFzbWl3UExwcWRJd2ZzUVNuREdCYzR3eGlObHRmRDNKdXhYdzZYaTBGWnFlckhuVGZ3YmlKaGlxUFp6bjF5N0Y5V1VETkZkQUhiWVlYZ3JKb3Boc0docVBjMDk0SjBSTjVOYlRrNDgrdGRTQ1RtVHR6Q3R3QWF2VGdiMnVET21Va3pFR0I3dE1wWUhobEZVdGtpMFY2N0dsSU8iLCJtYWMiOiIzYWUyMTAyNDM3ZWExZGQzY2RlMDYwYTIxYzJkMDE5OTY2MGM1YzkyOTY5N2E3ZDg1YWFlODEzZmZhMDkyZGZjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik9aQW1DaFJHSjBVTEZya2lQMFdpenc9PSIsInZhbHVlIjoiQ3FNQWJ4NUhMWFBjc25lbTBkVUFuNVZsK1BqM2s0cEVDUGxvS1p5T1ViM2p2Wnd6ZVBaNjRKT2pJUG83cHZXTHN4aVplaEFCRlBaNkFndXhTYjlrQ2J6NW5OWlB3WVAwK2dTTXpYT0hlbHVlQ2R5M2xVejVjZ3hkOWdJMGNNTnlUK0F3MG44djR6Sll3dWlWRjV3NTNKc2Z0VmFMTWxZb2dTN2daLzNSL2VGUmNSbUJya1Z1b0xmTzJlSWlMVWNWSGM2cjR5ZUpHZFRhcTZSVU9Wdm9TMWY5ellMMlNCZldldm9FY0xDek1aU1YwSmIyTkR1WEJmVUhXNEt2NHh6bVE3RWo1RERyTUNxN1pqVXA1MGdKZFJrMi94RUtXNUJkS0hFSUtGbVJXbmVRenRCVGZoZzVFL05nNTNlNDY5WklVckkzNFVwek9oekJmdmJLUnl5WnZHc0RmaitLNXBXVWZXbmNrc2I5U0ZpRDdMamc1bTlWUlJ4RE9tWEtVR3NxNW1yWFFTb2M0bmt5M2RidmtFOFRnWHZxTHUySGFKbXIrTW5xalhtaW54WXlmZzhnNWo2djBDNzRGMnp4ZnlzODVSTjJSS3F6cU9UdW1tdWk1SDlmOExRVGc5NzQ0SFhKUlE5bC9FSExIM1Z6MzA1VW04YUovRXZGdXBwL0NnNlgiLCJtYWMiOiIzZmJmM2MyMmJiODliNzQ2M2U3MDYxMDU0YjcwN2M1OThkNjIyZTUxYjM3ZDdiM2NiZmZjNGU4MmQ2MWEwOTYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-434646397 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:04:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVBamhmMGZNNHBWUjFTWlhBZ3E5U1E9PSIsInZhbHVlIjoiZ25pUmlrT2Y2SWovNW5oLzVpMXRLUFEvTDBLaG0zYkZaOFh1bEZGTzdPcmxieDV1RWg5UkVpRFVPMGZtZTZySS9UTitZVnY0NXZmc05rZzNGdzJnSWQrTTh0a0w0VGxGUjBleVZ6bEVjS0t5ZzB1aVlEaHREcCt6WXhWQklKeTZzOTFIVTlMMCt3d1l0eGRpcEFtUE00UExBZVExaU5jR0Y0TmNIQjBDVTVCbTRTQ1Y4QWNnMVNxc252SzFCOTVFWUw1UUxjZktKakMrWGtFQkxyQ3Z0d3k4V04xRDdLV3Mvb2duTnBLeW91Sm5LQ1Jya2t6ajdRK1ppUVEvMCtnc1RLbnJMVnNQcVhjWEEwL2ttZnpBbnJ3dUlXcmpHVFZTREZjU3Voc3VXRE5PNWZHa2FaUklBN2drZHFpNXVnUnJ0K1c4OGZPNXRZWTM0YVRBZ0Z0RmlSelQzNnFkL1ZLSTE3c1VtSERSZ0RUSzF5aktBTnBLTERselpNb3A1b3FEVFZLN0tnT2FKTGZxbTVzUkJnT3UxN0xZUjhBQVAxQ3NDdVAxaHo1aDIwSTVmQ3h5aExCaDRqZlFrRGpJTGRNOGVNZTVwM2QrWDNPbTd5VDF4SlFzNFdDQzBoRE96a0ZpZ3Z3aTFzb0hHRE14VWYvV3JMTjVZN3E3MHBFZzZMVmwiLCJtYWMiOiJhZmYxZjE4MzYyM2YwYTU5ZmJhYjVkMzQ3ZTdiZmEyNjNmNjk1ODA3MjQ5Y2NmZDRkZDMxMjRiY2Q0YWEzMDMyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik9LeVVNYmk5c2ZrYmtxbGFjL0dBSUE9PSIsInZhbHVlIjoiWUhzN3RNeXJVRzdqYVpqQ3VucnJXZFVGWFBrbWhhV0dNcjFEbDlad1hyaWdGTmxuTmhWd003K3N1Q1dnR1FNWU9HTGRjVjBEY2RVaC9oSTN5NTBXMDNOS3h5M0wxeSs1R3Baa0FZbWdRaVlWZTlST3plU2VKbWttcFgrZlkzNTdiYkh3VFVDLzVNQkFuTGVkMGVBc2EvQURqdklzRmZVbW43eWpValpvSVQxOE9pckJhNHc2bU14RUFpL2hxMGgwaXNMTGllMGVxZEFFdEI2dHZ0aUVLNGNROWRRSE5VNUQ3a1BpOVhDY0NnMVIzamRJWEdZaHhEWHQvZnFzRHZTRnJ6dldSQkRvZkdpTnRPT3BicmhoU0JVNzNRUHhFUEpWdXBBeDg4WDFnODhQWUFzcjVFR1dteUhyZnVsV0JBMmxiMFcyL244NHFLSWpNS2FhcG83dlBheVBUVzdyci9HbWxYYk5mOXdtdTEzbWVLVURKakJmdjFEdThTNzg0ZkxDaTVSRHl1bnJwbkRTREkxUmlib3ZhYlZvRVlJK3VjTmhSZGdRamgyOWVUR1VIOEFPeGwvWmgrMDBtV1BNMFN3MnBiNHF0K0VVSFg3WEtqc2huSHMzcFVIVUZPa2g4cittZno3TjRDSi84aVFZNzZINTVrdWVjbGJYbmk3ZGlqSXUiLCJtYWMiOiI2MDY4NDNlNTEzOWFmYWI4MzBlOTdlMmRiYzI1OWFiMDljMWY2ZWQxMWFiNjNmYzFhZjE5YTgyYTk5MWI5Y2FlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVBamhmMGZNNHBWUjFTWlhBZ3E5U1E9PSIsInZhbHVlIjoiZ25pUmlrT2Y2SWovNW5oLzVpMXRLUFEvTDBLaG0zYkZaOFh1bEZGTzdPcmxieDV1RWg5UkVpRFVPMGZtZTZySS9UTitZVnY0NXZmc05rZzNGdzJnSWQrTTh0a0w0VGxGUjBleVZ6bEVjS0t5ZzB1aVlEaHREcCt6WXhWQklKeTZzOTFIVTlMMCt3d1l0eGRpcEFtUE00UExBZVExaU5jR0Y0TmNIQjBDVTVCbTRTQ1Y4QWNnMVNxc252SzFCOTVFWUw1UUxjZktKakMrWGtFQkxyQ3Z0d3k4V04xRDdLV3Mvb2duTnBLeW91Sm5LQ1Jya2t6ajdRK1ppUVEvMCtnc1RLbnJMVnNQcVhjWEEwL2ttZnpBbnJ3dUlXcmpHVFZTREZjU3Voc3VXRE5PNWZHa2FaUklBN2drZHFpNXVnUnJ0K1c4OGZPNXRZWTM0YVRBZ0Z0RmlSelQzNnFkL1ZLSTE3c1VtSERSZ0RUSzF5aktBTnBLTERselpNb3A1b3FEVFZLN0tnT2FKTGZxbTVzUkJnT3UxN0xZUjhBQVAxQ3NDdVAxaHo1aDIwSTVmQ3h5aExCaDRqZlFrRGpJTGRNOGVNZTVwM2QrWDNPbTd5VDF4SlFzNFdDQzBoRE96a0ZpZ3Z3aTFzb0hHRE14VWYvV3JMTjVZN3E3MHBFZzZMVmwiLCJtYWMiOiJhZmYxZjE4MzYyM2YwYTU5ZmJhYjVkMzQ3ZTdiZmEyNjNmNjk1ODA3MjQ5Y2NmZDRkZDMxMjRiY2Q0YWEzMDMyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik9LeVVNYmk5c2ZrYmtxbGFjL0dBSUE9PSIsInZhbHVlIjoiWUhzN3RNeXJVRzdqYVpqQ3VucnJXZFVGWFBrbWhhV0dNcjFEbDlad1hyaWdGTmxuTmhWd003K3N1Q1dnR1FNWU9HTGRjVjBEY2RVaC9oSTN5NTBXMDNOS3h5M0wxeSs1R3Baa0FZbWdRaVlWZTlST3plU2VKbWttcFgrZlkzNTdiYkh3VFVDLzVNQkFuTGVkMGVBc2EvQURqdklzRmZVbW43eWpValpvSVQxOE9pckJhNHc2bU14RUFpL2hxMGgwaXNMTGllMGVxZEFFdEI2dHZ0aUVLNGNROWRRSE5VNUQ3a1BpOVhDY0NnMVIzamRJWEdZaHhEWHQvZnFzRHZTRnJ6dldSQkRvZkdpTnRPT3BicmhoU0JVNzNRUHhFUEpWdXBBeDg4WDFnODhQWUFzcjVFR1dteUhyZnVsV0JBMmxiMFcyL244NHFLSWpNS2FhcG83dlBheVBUVzdyci9HbWxYYk5mOXdtdTEzbWVLVURKakJmdjFEdThTNzg0ZkxDaTVSRHl1bnJwbkRTREkxUmlib3ZhYlZvRVlJK3VjTmhSZGdRamgyOWVUR1VIOEFPeGwvWmgrMDBtV1BNMFN3MnBiNHF0K0VVSFg3WEtqc2huSHMzcFVIVUZPa2g4cittZno3TjRDSi84aVFZNzZINTVrdWVjbGJYbmk3ZGlqSXUiLCJtYWMiOiI2MDY4NDNlNTEzOWFmYWI4MzBlOTdlMmRiYzI1OWFiMDljMWY2ZWQxMWFiNjNmYzFhZjE5YTgyYTk5MWI5Y2FlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434646397\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-65642418 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65642418\", {\"maxDepth\":0})</script>\n"}}