{"__meta": {"id": "X9ba4e032f393ee9a58c0f1188e369c79", "datetime": "2025-06-28 16:01:25", "utime": **********.031082, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.520726, "end": **********.031098, "duration": 0.5103719234466553, "duration_str": "510ms", "measures": [{"label": "Booting", "start": **********.520726, "relative_start": 0, "end": **********.954426, "relative_end": **********.954426, "duration": 0.4337000846862793, "duration_str": "434ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.954437, "relative_start": 0.43371105194091797, "end": **********.031099, "relative_end": 1.1920928955078125e-06, "duration": 0.07666206359863281, "duration_str": "76.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831936, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024220000000000002, "accumulated_duration_str": "24.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.987684, "duration": 0.02034, "duration_str": "20.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.98}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.016649, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.98, "width_percent": 3.097}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productS%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productS%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;productS%", "%&lt;div class=&quot;product-stock&quot; id=&quot;productS%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.020634, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 87.077, "width_percent": 12.923}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-849625433 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-849625433\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-51428904 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-51428904\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1263890569 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"39 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;productS</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263890569\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-925228574 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">72</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitLODB2RWE1SnpWdjZWMHdwTE1PVGc9PSIsInZhbHVlIjoiQVVqQmpkdnM4WUNOTGFvbXd1R1U1SWNlaVVCVWJLNHdrcmU3bzJXRGFTMGZxSW1RZkhRUkNQRE9wSGNqcDJIQmtVUmd4Y29xNG5HdGpMakVpbXppR0tZN3hKYWRWUmE1RGovbEdKTWFlTkd6cUd4RVVzckREaU9rdTZRRXB6Z2JLc0FzZlRsT0txMFhIT2NXUWl2b0ttT0N4dFFnazJndjZ1NExJTW1ROUFMRUZHQzVzK0h0Zng4ZndjbWZrS1lQYkJodnBpTmNWaVNDeTA1VElJdW1vR2NpMmZNVzFtSmZzVnhQR0RSdnRYb2FtalUxYTUzQXRXTnpOVjNKYmVrSm1KV3ljU25hckxLVEx5UE9YcUlqaEMrWGRabXFpVGhZM1pZU2lVblhBbDhxWTlPNERueDU5b0UxTkFjeHFONkJ3ajFJVkJ6TkJ0NTdsVnV0RmhwUHRaL1JWbElQQVV2bkUzNXhpUFpkN3dTVW0vNzVCT1gyT1RqQWR5ZVA1dkhvYnZCejBka2xMTlFXdk5wc05VUFhvajNYeXdCdUVKMEpNSWg4S0FvYXR5eGRjSHlLWVNFaWpPekxBcmg0Vm5sRW53ZTZPNXNTcmh3ekdyRVlnMWhQWmx0MzNTdDRKcHZ2V1l4UjU1TUV6YnhBNGhucFd1NmVuRUhWMEx2M0tqQnIiLCJtYWMiOiI0YTIwZGU2ZDlhMzI3NmVlMzE1ODFiMzM5ZTA3ZDA0ZmYzOWJmOTZiMWQ0MjE5NzllYmEwZmZlZDBkYjI5NjYwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkllbGNxL1h2NXFSZnpKT3BHQXJmeEE9PSIsInZhbHVlIjoiZXdmVEpaODUycHdZbDNRdk1XOTJLTFQxZlp4QXVCbGJlQmVpVE92aXV4RWVRQ3ZJTlhRN0JEcTUrUk8xK2hoV1NzOHR1d1dHdVNZNGxNVnhFY1dXdFlhOHlTdzA3NUpHUmpjU1hVVjhUcHpGM1FibDByUktvdWFqYVM4SkhVaUtpSXpaYnJPc3V1am8wMW9XOE1zKzRmbEdGWXhJYkxlOVAvSzU4Z3FxZDB6VEl3YzZwM0lVUlo1bWpBV0tYc3M1STJ6QjZTajdEMjFHdUVPMnVpUzRtUllETHRJNzJ1RUVxK2NEMktkcVpOOEtWcnFSTHdtWVQzL2p4REJjR3ZDZXZNMDFBZG00a0VhejYzMGd1ZDNHdDBuQVZPcWkvSkJRdVFDeTlRQnNsZTd4clh0Z0FlSmtQOVFiTTN3eHJQVXpCVy9WMXUrdVRXS2RHdjc2c1M4S0pGbGhCQ2x6QlFZZW1zVzRNdkMxdXk3NUQxUkRpNklVTkYzdllFQ0x6cUt6VG92Mm5UNmVCaHMxMzBVb2VZV2FiZ0ZTUUlHVzZCT0ZvMU4wTHcvM0lkWTNUSkhRZGE1RkxOZk1YNUExeTdyR2ZuWmo0ejJPenNJM0wvblNKRnpOT0hKODRPbXV3Mm8zLzc2Ky9ESGN4TXFvVFZ0NHJKM2ZSNjlBcmxtdENpQkwiLCJtYWMiOiJmNjViOWJiNWY1ZDQzM2ZkOGZiZTNmNmE2ZDZmNDhhODdmOTdmNGE0MjM0Nzg1MTA0M2M0Y2RjNWRkY2NjOTJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925228574\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1551489787 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551489787\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1864729051 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjMyU1dtMXhhcGpQQXBVODBXMlc4V2c9PSIsInZhbHVlIjoibjlvKzF2Q3NIOUxkTkpCajRMYjQ5R0RoRVFYY3doUVQzNWRLTUFweHVxcCsxNUxxVUU5d2hBUDJDOVl2UVE4amxsZGJ1dXVTbTRpbEhYZ0UzaytUem8rUUtsODI0MVVibTE2YzRYZXp3RGE2QndRQm81VERBSmRKYmhTdGtITi9OMXFVMnFKR0RlSjdsZDVMalhFaDhVZlFTTTFtTmRmRTB1ZWM0bndEMmswT3VpMmx4UFBRMnNYOFlWZFM3aHROTVZhZzIrZXFLVjROeW9DdGcveC9LYndNMUdJbWtBMHRLUWpIejhLNjkvWDQ2c2VvUG1ERGNFR25yWUZRNlgrZFZhd01nbWMvRE9UK1hCVDk5UDJHaTN1cFZCQnV1Z28rWjlBOEJVaEpQdHdJYzEzbXFabStxTVRHVGZsTWVNTnUzZEpQK2lNeitkOTIzcUZ5ZkVnejRvbjdHMjErSHhrOFo3eFphVS9JK05obllET3RXdnVFVjNHM0Rablo2Mjd5azc3RFhTM2MyV3AvejJQeHB3a3JxYjBDU2ZxNmU2NjhmdEQvTGRybjJVZUlpMCtnWS85ODAzMFJoanY5OWVkVUovaFgyc0dSSGl6VGZCc09TeEJmZjFaRW5uVGR1THpYNDJTQ3h3dHMzTVREeHN3UHFCaTV5Q1EvRmVFd2dKVWgiLCJtYWMiOiJhMWQ2OTUzOTRmOTVhOTc0MGNiN2UzMWE0ODA1MTYxMzdkMjI2MjU1NzViZWUwYWY0ODhjYzYwZGQxN2QwYmQ1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlU5TllkbnNOWjlQMC9OTk1Qa015R1E9PSIsInZhbHVlIjoicHErVlpmWDg3cHBqNTBRNXlmUTNPRjFlL2tLMEIvSmhTU2x1UUdBY2RvbSt4Mm92Y0ZObHMzUFBJd0pkVkJnUzNOWnc5VWJxQVlFUEpIcE1QblRtakt1QUlsSDk2NWJ5M0s3Qmg4c2pCUlFBUXNjckNJcVBMc0wwZlJWeHcyWHhrcWpUejB0M01CYWh6YVZnVjdNN0tzT0NSS0dGVUZxc0xTVFJoR3o1UlRBWjRSVEl5eUd6OGovMHFYRlU0SWxjSFJVNmc4R0xQTCtyN2JZNTdDbUlIUVVqUlE5dXd0eWFCTUt3UWgzQ3Uxc3MyQkZZS3pFUHo1R3IvYURmSFBaZ2tReUlmSjZOald2QnR1UWhZMFd1UGU0WXBlS0U3VDFNajhMenNXT2twbGlOTjEwdFl3VEF1M3hoM1htZFFTTVRQb2JNTmRPcXh0Z1VkYW1jKy9FalVwZnNvMmRQTUN6WThJNXRZK3l2bVdoUnFKYXpmS1QycHVlOHp6a09UcEdWL2NJME16bHJQRHRuc28zOXFhM1Q0ZWxjODYzQzFscXJVV0pLTzYrVEdMbWJnS0lINHRHQkc2NTdrZHNiTjQwOGd1eGF0SG9yS0VIREQvVW5ScFdGTDRoOEMvV3BkRHQzSlNzd28rTFA4Zk5HQUVVVkN6OWtHNkNYSFFGdkNTOWciLCJtYWMiOiIzMzZhOGIwMmJkYmE0YjFkZTM0YjZhNzc4ZTEwOGZmZTk3MjgxMGExMzI4MmIzOWMyMzg2ODU3ZjQ1MzU0NGY4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjMyU1dtMXhhcGpQQXBVODBXMlc4V2c9PSIsInZhbHVlIjoibjlvKzF2Q3NIOUxkTkpCajRMYjQ5R0RoRVFYY3doUVQzNWRLTUFweHVxcCsxNUxxVUU5d2hBUDJDOVl2UVE4amxsZGJ1dXVTbTRpbEhYZ0UzaytUem8rUUtsODI0MVVibTE2YzRYZXp3RGE2QndRQm81VERBSmRKYmhTdGtITi9OMXFVMnFKR0RlSjdsZDVMalhFaDhVZlFTTTFtTmRmRTB1ZWM0bndEMmswT3VpMmx4UFBRMnNYOFlWZFM3aHROTVZhZzIrZXFLVjROeW9DdGcveC9LYndNMUdJbWtBMHRLUWpIejhLNjkvWDQ2c2VvUG1ERGNFR25yWUZRNlgrZFZhd01nbWMvRE9UK1hCVDk5UDJHaTN1cFZCQnV1Z28rWjlBOEJVaEpQdHdJYzEzbXFabStxTVRHVGZsTWVNTnUzZEpQK2lNeitkOTIzcUZ5ZkVnejRvbjdHMjErSHhrOFo3eFphVS9JK05obllET3RXdnVFVjNHM0Rablo2Mjd5azc3RFhTM2MyV3AvejJQeHB3a3JxYjBDU2ZxNmU2NjhmdEQvTGRybjJVZUlpMCtnWS85ODAzMFJoanY5OWVkVUovaFgyc0dSSGl6VGZCc09TeEJmZjFaRW5uVGR1THpYNDJTQ3h3dHMzTVREeHN3UHFCaTV5Q1EvRmVFd2dKVWgiLCJtYWMiOiJhMWQ2OTUzOTRmOTVhOTc0MGNiN2UzMWE0ODA1MTYxMzdkMjI2MjU1NzViZWUwYWY0ODhjYzYwZGQxN2QwYmQ1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlU5TllkbnNOWjlQMC9OTk1Qa015R1E9PSIsInZhbHVlIjoicHErVlpmWDg3cHBqNTBRNXlmUTNPRjFlL2tLMEIvSmhTU2x1UUdBY2RvbSt4Mm92Y0ZObHMzUFBJd0pkVkJnUzNOWnc5VWJxQVlFUEpIcE1QblRtakt1QUlsSDk2NWJ5M0s3Qmg4c2pCUlFBUXNjckNJcVBMc0wwZlJWeHcyWHhrcWpUejB0M01CYWh6YVZnVjdNN0tzT0NSS0dGVUZxc0xTVFJoR3o1UlRBWjRSVEl5eUd6OGovMHFYRlU0SWxjSFJVNmc4R0xQTCtyN2JZNTdDbUlIUVVqUlE5dXd0eWFCTUt3UWgzQ3Uxc3MyQkZZS3pFUHo1R3IvYURmSFBaZ2tReUlmSjZOald2QnR1UWhZMFd1UGU0WXBlS0U3VDFNajhMenNXT2twbGlOTjEwdFl3VEF1M3hoM1htZFFTTVRQb2JNTmRPcXh0Z1VkYW1jKy9FalVwZnNvMmRQTUN6WThJNXRZK3l2bVdoUnFKYXpmS1QycHVlOHp6a09UcEdWL2NJME16bHJQRHRuc28zOXFhM1Q0ZWxjODYzQzFscXJVV0pLTzYrVEdMbWJnS0lINHRHQkc2NTdrZHNiTjQwOGd1eGF0SG9yS0VIREQvVW5ScFdGTDRoOEMvV3BkRHQzSlNzd28rTFA4Zk5HQUVVVkN6OWtHNkNYSFFGdkNTOWciLCJtYWMiOiIzMzZhOGIwMmJkYmE0YjFkZTM0YjZhNzc4ZTEwOGZmZTk3MjgxMGExMzI4MmIzOWMyMzg2ODU3ZjQ1MzU0NGY4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864729051\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-940305465 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940305465\", {\"maxDepth\":0})</script>\n"}}