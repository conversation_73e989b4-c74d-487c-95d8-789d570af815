{"__meta": {"id": "X315dc6332916af423469c6fda728de3c", "datetime": "2025-06-28 16:10:20", "utime": **********.841448, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.398288, "end": **********.841466, "duration": 0.4431779384613037, "duration_str": "443ms", "measures": [{"label": "Booting", "start": **********.398288, "relative_start": 0, "end": **********.781406, "relative_end": **********.781406, "duration": 0.3831179141998291, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.781415, "relative_start": 0.38312697410583496, "end": **********.841468, "relative_end": 2.1457672119140625e-06, "duration": 0.060053110122680664, "duration_str": "60.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45697920, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00239, "accumulated_duration_str": "2.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.812162, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.69}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8223088, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.69, "width_percent": 17.992}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8279328, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.682, "width_percent": 16.318}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1604044504 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126882941%7C30%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFmN2VXUm5jZUVIRGd3cUhmM0JpVGc9PSIsInZhbHVlIjoiQUxaSWR6cGQrenB5ZEhkc2ZWU2JROEVXbVdTZmJHakErNUJJSUgydmMrYUNrRko4VVVwQVpkQ0s2bXpvTkk1NmFQV3U1aFFiclI3QkluQjZ0VnJHWkJaaHZGeG5GdTRvcktVeDdwM3dHZ3JsbEUyM1pOVG5SWWlwS1RzQ1Z6eTVla3pCd1FrRXpQUVJiU0NQVnk1YVVNS0daRXY5M0p0RW5zRHM1UnluWjgwNzVvenc0U1dqZk5CRkZ1NnpBcWw3SXd4M0lhM0NrNTNGbWw0TjM2SHI1bjU2WEYzdXhMWGlXTTB5K3BuWXdMVWpYQTJ4Umc5N2ttcDFRN3VSdVJUYmlaNmN3RTZ5a1F6RkVqMjY5RHJ2TkF2OFNHMkdjSUt3T25vVTd4RzF5cXIzSlpVUHVTQUlERDlWbW5xb1Q3Ky9QZUxra0hzR3hzSXhSZVQyeGJGYVM4VHd5dTdoWDVyR09IVnpXWkdRdEQ3QW1TV2p6ZTdMZ2ZiMmZFdFVjSTBxd0FESGdKc3NvaWZZdnBnQktnUXkwelM2ZFBWY1d3OVNTVDVuZDdKbFJSUGpNQjdDaERZL2ExSU5iaFBXd2pXeFM2Tko5MUhQM3FKNHZVWnNveisrVllzME0yc29iNHY5cGRNMlBOcVcxdVk4NGtiaE9MS0pxQlJiL0JzUHlqSU8iLCJtYWMiOiI2NTgwNmE2ZDg3MjI5MTRjNzY1OWY2NWFiNGY5Zjg4ZDUzNTMwZDhiYWU3ZDVmYjJiZWU5MWMyNGM2NmY0NjhmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJlNEl6ZEUvaDcwVThnUkJqQUY2UHc9PSIsInZhbHVlIjoiNmxXT1cxWXlmN1ZkQ0V6QTdpSWppb0g1OVkyQUtuQ0NFZ0ZRa1pxSVh6VU1WNDVNcGZHRE0wWnRPcTIwTDFUUHhwN0xHMjdCSnFYNHhCMko3Y3ZzMjdYNVZFdGJDRk4vMERYdDRvVFkrV2dzMThzTGI1MnYvT0VTRjRkaTFCOThjQ1hlUGRIcWxaZGZ0VVhscDVSNjNjV1NjdG1FMnk5SzlDbEFKS3RWbDVzOFM5MitMMWhnNFBmMzQxdHQ3MjBYVEtEZ1l3cC9ObHpqS211UmZrc3E5TFlURzhWRnd5djdLYkdPTVhiWE02aGRBQ0ZKdXUxVkUzMHJjeXlSdnVGSVU0QjNlSGg0Nlo0QkV0WnN6ZWZmaHhwVlZWc1cwVWVLNldoMVFQVnkxN1gzRjREQTYwZjBMNjVvT0Mya3B4VHZqdkJxQnJQeXdiL3B4RFBIMFlwYkdKaTNGWWI5YWZKWUgyMlc5QU1NaHBubVA4OW11cE0vWDBValoxZmtuS2FPQ3Q5QjBiTjVYRit2eTNxM2dzY2xQT2d5cDZMamtweDc5bXpNNVBzbVlGblBncVlxTHRJZkxtdjUzNHFBMXJ6VVpTZWUrYmVyUGl3em1TNjZtY1dyWW5GekNwZjdTSkhGa01KM2JCNVFmcFVMdXdPOWJ6eGhVSjI0Y0NBNEpjSm4iLCJtYWMiOiI4ZjcwZDAyZTc5YzgyY2RjYTk2ZGJlZGE4NzIxMjMwYTc2MTdhMzAyN2YxNmNhNjc3OGVjZWM1M2UyZGMwNzQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1604044504\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1589578047 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1589578047\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-744762542 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:10:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdubDFPaG5zRGNXeGR6VjEzTVZYNEE9PSIsInZhbHVlIjoiQjh6cHRVZllScW03NjB0TmQraDYvV3QvWm10WXRiemZQUTBiL1BDMGc1ZUY5N2dva0ZsV25PNVhTWW80bkhLOUl0RkZsTzJFUzN1MXFkL3N5amJkK2F2VnRrWkZsaXp2WHNRZ3J1cGIrZzJWS24xWEt5Qm9zNU9CZTVuVGY4dHFjTlVodFRrbG5QaVBIZXJOUjNJRE8vb2VKWDJDVHNkZnZuQWEzTDZyWFlUOUxIQkRMWVRSOE1yY2xsb2k1QmhpWGlyZDFqY0NQK2ZocXpLSGNDNVZ2R1lSK2FSSzdVbmNkNEpMb2tUU2svUHpUNmg2b2RDZ0M4TXhBaXJzcUwzejgzczc4UUdYT08vcVpBV21JbjI0SjhiZGpCbnBnYS9BYVpORGwyT2FPaXFBQnNBbGJIQllYQnZTbUVnUytGYlh6NmZweEFXUkc0cStKVENmRlJ4N0IwTGl6QnVya0xFeU8yY1QwQm9HSE9ZV0UxMzNWenBUcHJ3akYrZ1NVcXhwR0E4bDNUV2dsOFRzTm9hSUxTTlZTcm1IcVVCNGpHWUxhZkdDV2RIMlRQb24xNXhNZk1OR25lcGJNM1NPN1h5akV1bjNZU0JoU2lRL1NkVXVyYnNVZkU0NFhSUkhGZDNTMlRwa2piTVF5aHhSVnhzMVhzU2dxL0twVjZzYk8xUUUiLCJtYWMiOiJjOWFhZmMzNzRhN2I4ZjY1NjEyNDEyNDNmNTljOTBmMTAzMDE3NjYxYWI0MDUzOTM0ZmY4ZmEwMTVlMWU4MWQyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpNWmtkV2ZrRUtmL0dXZjBVYjdwanc9PSIsInZhbHVlIjoiWkY3ZFYwNEt0bnc0ZHlKL2crTWI3Y1c1RTR0WXo5VmNxckpqMCtzT29Fbm1leHdLT3FtL0lnaW5BMzBEbkNFZGpCcVgyK3A5U3pkOFl6NzNLU0JIbTZDTDNobEFDYSsyd291S2xhQWF4TWlEOFJKU0xkbmduRXQrdWNPME55UklxUTFYaUY4eWI5NWxuTU02MGQzeWkveW5JRUJQN1pQS2k4d3VkTlZNYWp2TnJ4cGFLa294bVJ1NVpDTEtJcUhPcG9IbVpLb3kzM05hWis5bnRlY1M1NlhjR0ZFSklWU1hCbzNaVUtJSW1NVm5KL2hQdllKRTFTWjdJOTBCL0JIbnA4bjVMWDhwa3FzSHJhd09NemNTc2JuTHZpUTNESTlwR2dheWh0RDNkVXhqTUJwUU04Sjd6Y2lzbnREcmZGdFpsbElwa0diRGxDRHM1ejJaRXFhZjJMcnorRmRtRlNmOG1zRm1wZnFrYnpUVmo5cDByUXNmakVZSzVQSzV6c0hTZzNpNEFhdWxKK1NjSklJdkdhbVJ6VmJiVEw1U0gvQWR0OWlRUHU5TTVQS0pQdDZaaDJBUUhJS2Jpb0lLL3RCUVVKUXF2WG1rMHhKZ0s5eENIMnBjTGZxZS9wWVdrdVg4U0NkYnY4b0sxRVFxRmFpSTgxcm5rSnpoWjZNbTJLRTUiLCJtYWMiOiI2ZThlOTNkYjgzYWRmY2I4MDA5NDQ0ZGQ0MjEyYmJiMjE3Y2YwZWM2MTNkYjRmOTc4OTJlMWViZjIwYmU4NTcyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdubDFPaG5zRGNXeGR6VjEzTVZYNEE9PSIsInZhbHVlIjoiQjh6cHRVZllScW03NjB0TmQraDYvV3QvWm10WXRiemZQUTBiL1BDMGc1ZUY5N2dva0ZsV25PNVhTWW80bkhLOUl0RkZsTzJFUzN1MXFkL3N5amJkK2F2VnRrWkZsaXp2WHNRZ3J1cGIrZzJWS24xWEt5Qm9zNU9CZTVuVGY4dHFjTlVodFRrbG5QaVBIZXJOUjNJRE8vb2VKWDJDVHNkZnZuQWEzTDZyWFlUOUxIQkRMWVRSOE1yY2xsb2k1QmhpWGlyZDFqY0NQK2ZocXpLSGNDNVZ2R1lSK2FSSzdVbmNkNEpMb2tUU2svUHpUNmg2b2RDZ0M4TXhBaXJzcUwzejgzczc4UUdYT08vcVpBV21JbjI0SjhiZGpCbnBnYS9BYVpORGwyT2FPaXFBQnNBbGJIQllYQnZTbUVnUytGYlh6NmZweEFXUkc0cStKVENmRlJ4N0IwTGl6QnVya0xFeU8yY1QwQm9HSE9ZV0UxMzNWenBUcHJ3akYrZ1NVcXhwR0E4bDNUV2dsOFRzTm9hSUxTTlZTcm1IcVVCNGpHWUxhZkdDV2RIMlRQb24xNXhNZk1OR25lcGJNM1NPN1h5akV1bjNZU0JoU2lRL1NkVXVyYnNVZkU0NFhSUkhGZDNTMlRwa2piTVF5aHhSVnhzMVhzU2dxL0twVjZzYk8xUUUiLCJtYWMiOiJjOWFhZmMzNzRhN2I4ZjY1NjEyNDEyNDNmNTljOTBmMTAzMDE3NjYxYWI0MDUzOTM0ZmY4ZmEwMTVlMWU4MWQyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpNWmtkV2ZrRUtmL0dXZjBVYjdwanc9PSIsInZhbHVlIjoiWkY3ZFYwNEt0bnc0ZHlKL2crTWI3Y1c1RTR0WXo5VmNxckpqMCtzT29Fbm1leHdLT3FtL0lnaW5BMzBEbkNFZGpCcVgyK3A5U3pkOFl6NzNLU0JIbTZDTDNobEFDYSsyd291S2xhQWF4TWlEOFJKU0xkbmduRXQrdWNPME55UklxUTFYaUY4eWI5NWxuTU02MGQzeWkveW5JRUJQN1pQS2k4d3VkTlZNYWp2TnJ4cGFLa294bVJ1NVpDTEtJcUhPcG9IbVpLb3kzM05hWis5bnRlY1M1NlhjR0ZFSklWU1hCbzNaVUtJSW1NVm5KL2hQdllKRTFTWjdJOTBCL0JIbnA4bjVMWDhwa3FzSHJhd09NemNTc2JuTHZpUTNESTlwR2dheWh0RDNkVXhqTUJwUU04Sjd6Y2lzbnREcmZGdFpsbElwa0diRGxDRHM1ejJaRXFhZjJMcnorRmRtRlNmOG1zRm1wZnFrYnpUVmo5cDByUXNmakVZSzVQSzV6c0hTZzNpNEFhdWxKK1NjSklJdkdhbVJ6VmJiVEw1U0gvQWR0OWlRUHU5TTVQS0pQdDZaaDJBUUhJS2Jpb0lLL3RCUVVKUXF2WG1rMHhKZ0s5eENIMnBjTGZxZS9wWVdrdVg4U0NkYnY4b0sxRVFxRmFpSTgxcm5rSnpoWjZNbTJLRTUiLCJtYWMiOiI2ZThlOTNkYjgzYWRmY2I4MDA5NDQ0ZGQ0MjEyYmJiMjE3Y2YwZWM2MTNkYjRmOTc4OTJlMWViZjIwYmU4NTcyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-744762542\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}