{"__meta": {"id": "X08a39048cc926ba9b29c740aadf300e8", "datetime": "2025-06-28 15:35:07", "utime": **********.650348, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.098821, "end": **********.650361, "duration": 0.5515401363372803, "duration_str": "552ms", "measures": [{"label": "Booting", "start": **********.098821, "relative_start": 0, "end": **********.574509, "relative_end": **********.574509, "duration": 0.47568798065185547, "duration_str": "476ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.574518, "relative_start": 0.47569704055786133, "end": **********.650362, "relative_end": 9.5367431640625e-07, "duration": 0.07584404945373535, "duration_str": "75.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45390624, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0156, "accumulated_duration_str": "15.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6165, "duration": 0.015179999999999999, "duration_str": "15.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.308}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.642906, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.308, "width_percent": 2.692}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1175760721 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1175760721\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-71045266 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-71045266\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1996817960 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996817960\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1164514726 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751124481231%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imw0c3V3WXp0NWJvTlpkbFRDVE9mYlE9PSIsInZhbHVlIjoiemRwSTRFR1BodkdQM2h2ZTlKM3lJQUhkVERKd1ZhSENmanJJamRUVXBEdlVkS2dtZW5VNlo3RlcrdGFnYnYyME16MjFtQnBEaVZyRm03dVNxdlZBbkpjUGdrUmROajlkN1o5ZXpIWmN5c0VqK0pwVDE2STJ3M1JFcmNpOHNuOU1Ea1BpUW50V1pUN3QydXM3bjA3TkxXMUJ4bGN1RFN2Qm41c21ES2FBM0FqRzVsamcvZU5heG45TWZ2QkdhM01tRWNuOE1jMHFEZGNsV3Fqd0Rab252aDFLTWNUcHdNdFkwb0ZjWDViaC9LdklxaXRSazhQOEZVR2xsMkZpMjM2SXhXQ0NoOUQ2SjE2VTd6L1RqaXNCc29La0kyK0pVZjRKWEdCcDIxWEpqcnE1VmtETFdMVmxJVjBDRlRtTjAzdGtjUkZQWkQySnBoUHJvQ1dCNERsdDE4RlptT2JxZTJjSER0SVBwMjlZR3Nna2VMTVhNeXc3ZWRBUVp3UnNXUG03d0hjd0tCaUh1Q1lXUHZvVmIxbEhwemdCZ3FVeFNIbkNtNlBnaTRlOVQ1SUhaNkxSNGUwa25IZ0RoWTRCVFUzcU53RTZPNmhnenVpNVppYS9oNTNkUTlFaE12b3c2Mlc3VnJvOXlLUjNTRHlLOGhMRndRMVlqTVBzdUIrSHBCS2MiLCJtYWMiOiI2YmQ3ODdhMzk1Mzg1YjRjOWNhZmQyMmM0M2Q4MTQzMGNlNmMyNTRhYTIzZTgzNDUyYjIzMjcxMDgzYTRhMGQzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1EeWQza2dBUG1pU0FSZ2dwS0RKM0E9PSIsInZhbHVlIjoibEIwSXBHRkM5VjFKOGhwMlZ0VnhCZ25NejFPcmhOT1M2NDFEVTN4eDQyR29LT25wTW45SlhPeGhZYnhFVktaM0V1TGdhaE5IUGRvUWdYRnhONU0wd3dCTkJDQ0tvMGVlV2ZrQXY4R2hPNXN3azdvM1dsQWRLandEbzIzdHJrRzNuMjN3T0JURzhTT3VHdnNDcFVkR1R5ZHVXTUl2YWMwWGdnT1hHdHZVQW9DMk9xOUZsdzVYNjYyU1FIdmRpemZpTU8vUlNFWTBXQXZGa1BxL01wQXVkTitLQ1k0eEovL05Xc1pVa3U4YUwwUTREeTdDbkQvSTF4Q1pJUC9tamJzSnZ5MVFhcWVuUTJnZVhtWHgxNURjbGJnc1V1d0FJcSs1dHc0aFhHWGIyc0psVkUxbnNhZzcxcTZ4NE05dFVGK2RKcnk4M2tqdFdjcDRLMzUzaGZlQVdtOTN5VFpnVHN6Z0hlczdRNDJhRGsyb2g3SUQzU0xwQi9VRnRFTWVGTkY4M09HNUFjdTgvSzVLMnJiR2w0QllybGdNOEl5NHBLa3Z0NzRPcFp4ZkxVN095dllZdlVSY3JhZm03Uk94RDNlbk5JWFZGSjFCemFkVjZiNWZPcDQwamRrYVNILzRDK2E0MEdQMEVBN2o0SU1FekFCK1RzVDV3djVHdUFqWWxzMm8iLCJtYWMiOiI0NmJhYTkwODRlNTBmZWM2MjFiYzc0ZmE4NzM0ZjhmZWE0NDMxMjc4YjkzN2IyNTI1MTJlZDU0Nzc1Njg4YmJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164514726\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-188128552 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188128552\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-771917259 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:35:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVDYmsrNlg5SzY5Q0g5QzJrcHlCR2c9PSIsInZhbHVlIjoibUo5SS93MHh3UjdtZkNVM1VFL1lnZWRuVW5EZnNjVEtXQi83UE1MTXNJL0NyRTl0a2FhR2EwZHF5alVIRkxBQ2FweW03dDNFb0orQlNRMVpqSTZLcDQzVFdqQWJjRzhRaXNDQzBxNjQ1ZUVqR1BIdTVVYWkwYXF0aXNwcHpZblEyek5KWkJxQjEvL1VKcGNrQTBxRDhsNnJvUGpJdzhldm94K2o0N3RwazE5U04yYVBWTk10bGJjOUdTemZ0dkdycmQ4UE5iSklkWFNWSEx4ZlVIV2pybitmMFhHRG5kVGcrMmtmNWpzaFdkZ2pFdE1NV2R5dytaTUdrZldzNkVBY3N4dk95c3MvMmlQRmxGNkNaTFhoSGhyUDdjZ21TQndrY0tOQk1ETjlNYndvZVlQdSsraUVjU2h1Y0ZoczhCK0M0cmsxUm5SVjRReGN1UkJDUlFZTHpnYUFuaWFUT1J4WjZ0UzdMWFlSRWdkTkEwaklsbmZrdHZrRjlDaThaMTNtTzRZLzVJQU1XbWdYYTZGY3AzYkhGaUtpODQ0SldoZ1ZmcVNmK2JPdzZEQS94UE44SW1td0RqaGZmLzg5d2RHOEdJMXFpdmZkMWwrdnNBODhyeTRYL1o4Q1ZXUjJwSUpxaEdFeG1FelA5aG9RcTdKMm90NUVJSWpBdVd0bTRMQkIiLCJtYWMiOiI2ODk0M2M2OGMzY2EzNmUzM2JlZmZhMDM5YjNmNGM3M2Q3ODUwNWY2YTg0MTJmMzNhY2U1M2IzNzM1OGE1YTlkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:35:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InllWk05eXJJYWFiM1F4Qldpb1VoUVE9PSIsInZhbHVlIjoiLzBuTSsyRU5oVEJHcndFSitXNEN6YmprM3JTRzhxUm5kSUpZdTQzL3c3bWdtZE5RQnM5dElKZVBTKzluMTlRdWU5VVRTWlE3NHhXV2Q2TWlMVXNEdzRET0NXbmdvMitIUExlb2JIWFdoTWUyTGl6eWpNQ29PanZGM0lQNG95ZWZEZFN3WlEvbGlFM1g0Q21zSHpIWEhnQWhyaldPODU0cnRlSkNSU3ZtRmswTTZDT3RiQmVQWU9JTjRRaEtFOVFBZ25mRXc2d1duOWxpMzBDcFMrbDlPb1d3ZUphMFA2M1pJNFpZcWlVbVNIU1pzV2QxUWExZmpEdVM2U0tyRjh1bkR1Z1hpMWViQURLcC9VVFFHQThzTVF6S2NKUHZ3TnpQTWxqeUp2dGFUQklQRDExUVNoK3VDa3c2WG1DMjVOR3MyVWxOUTgyVDVyVDFGdkQrU1JPQnlTTkRsZjJIUTYvZkd5c0lWT3I2MFc5N1VQdXRpS2taamxWazFIYnk0U0lqWnZZQlpwdHFLUEJLZVFUN1JXM1FWbUg1QWRUWXNVajR6aWNTcmdjc3Q2aGNZbDNPZk51dFFlWis3S0d3ZmxZUmUrWEplUHh1Z0pzQlBhQjd6Z2NTS0VtcVpNM1BzYndrUklMU3ZVd3kvdUtNSzNYVWp2bXh1OWxOaUZNam1aNkIiLCJtYWMiOiJmOWFiMjllMjBmMmQ4OGNjYjRmYjA2NmNlNDJkNmQ4OTQ2YWY4ZGQwMWRkODcwMmE0NGVkMmVmYjQ4ZDlmMGZjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:35:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVDYmsrNlg5SzY5Q0g5QzJrcHlCR2c9PSIsInZhbHVlIjoibUo5SS93MHh3UjdtZkNVM1VFL1lnZWRuVW5EZnNjVEtXQi83UE1MTXNJL0NyRTl0a2FhR2EwZHF5alVIRkxBQ2FweW03dDNFb0orQlNRMVpqSTZLcDQzVFdqQWJjRzhRaXNDQzBxNjQ1ZUVqR1BIdTVVYWkwYXF0aXNwcHpZblEyek5KWkJxQjEvL1VKcGNrQTBxRDhsNnJvUGpJdzhldm94K2o0N3RwazE5U04yYVBWTk10bGJjOUdTemZ0dkdycmQ4UE5iSklkWFNWSEx4ZlVIV2pybitmMFhHRG5kVGcrMmtmNWpzaFdkZ2pFdE1NV2R5dytaTUdrZldzNkVBY3N4dk95c3MvMmlQRmxGNkNaTFhoSGhyUDdjZ21TQndrY0tOQk1ETjlNYndvZVlQdSsraUVjU2h1Y0ZoczhCK0M0cmsxUm5SVjRReGN1UkJDUlFZTHpnYUFuaWFUT1J4WjZ0UzdMWFlSRWdkTkEwaklsbmZrdHZrRjlDaThaMTNtTzRZLzVJQU1XbWdYYTZGY3AzYkhGaUtpODQ0SldoZ1ZmcVNmK2JPdzZEQS94UE44SW1td0RqaGZmLzg5d2RHOEdJMXFpdmZkMWwrdnNBODhyeTRYL1o4Q1ZXUjJwSUpxaEdFeG1FelA5aG9RcTdKMm90NUVJSWpBdVd0bTRMQkIiLCJtYWMiOiI2ODk0M2M2OGMzY2EzNmUzM2JlZmZhMDM5YjNmNGM3M2Q3ODUwNWY2YTg0MTJmMzNhY2U1M2IzNzM1OGE1YTlkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:35:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InllWk05eXJJYWFiM1F4Qldpb1VoUVE9PSIsInZhbHVlIjoiLzBuTSsyRU5oVEJHcndFSitXNEN6YmprM3JTRzhxUm5kSUpZdTQzL3c3bWdtZE5RQnM5dElKZVBTKzluMTlRdWU5VVRTWlE3NHhXV2Q2TWlMVXNEdzRET0NXbmdvMitIUExlb2JIWFdoTWUyTGl6eWpNQ29PanZGM0lQNG95ZWZEZFN3WlEvbGlFM1g0Q21zSHpIWEhnQWhyaldPODU0cnRlSkNSU3ZtRmswTTZDT3RiQmVQWU9JTjRRaEtFOVFBZ25mRXc2d1duOWxpMzBDcFMrbDlPb1d3ZUphMFA2M1pJNFpZcWlVbVNIU1pzV2QxUWExZmpEdVM2U0tyRjh1bkR1Z1hpMWViQURLcC9VVFFHQThzTVF6S2NKUHZ3TnpQTWxqeUp2dGFUQklQRDExUVNoK3VDa3c2WG1DMjVOR3MyVWxOUTgyVDVyVDFGdkQrU1JPQnlTTkRsZjJIUTYvZkd5c0lWT3I2MFc5N1VQdXRpS2taamxWazFIYnk0U0lqWnZZQlpwdHFLUEJLZVFUN1JXM1FWbUg1QWRUWXNVajR6aWNTcmdjc3Q2aGNZbDNPZk51dFFlWis3S0d3ZmxZUmUrWEplUHh1Z0pzQlBhQjd6Z2NTS0VtcVpNM1BzYndrUklMU3ZVd3kvdUtNSzNYVWp2bXh1OWxOaUZNam1aNkIiLCJtYWMiOiJmOWFiMjllMjBmMmQ4OGNjYjRmYjA2NmNlNDJkNmQ4OTQ2YWY4ZGQwMWRkODcwMmE0NGVkMmVmYjQ4ZDlmMGZjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:35:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-771917259\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1099182976 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099182976\", {\"maxDepth\":0})</script>\n"}}