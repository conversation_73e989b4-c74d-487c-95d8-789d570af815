{"__meta": {"id": "Xb2eb20e37e5d3b977a1aba04ec9185e7", "datetime": "2025-06-28 15:00:34", "utime": **********.270653, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122833.793102, "end": **********.270669, "duration": 0.4775669574737549, "duration_str": "478ms", "measures": [{"label": "Booting", "start": 1751122833.793102, "relative_start": 0, "end": **********.209173, "relative_end": **********.209173, "duration": 0.41607093811035156, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.209182, "relative_start": 0.4160799980163574, "end": **********.27067, "relative_end": 9.5367431640625e-07, "duration": 0.06148791313171387, "duration_str": "61.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45594464, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00354, "accumulated_duration_str": "3.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2418458, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.582}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2530751, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.582, "width_percent": 19.492}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.261429, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.073, "width_percent": 18.927}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-587619501 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-587619501\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1292209762 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1292209762\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1868539306 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1868539306\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1412431734 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751122826313%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlnODRaQm0vL1BtRVlHTWxYcWovcVE9PSIsInZhbHVlIjoiVUEwTUhmWjJxYmJqeTdZTGdYUWJPeWdydjIvWVpqMXlHU1dxUHZjSWt5ellVWHcvUis4cVdFOEEyRXdPS2pSendBYitXTnV1Tm9NbnY0SFp6eVU1cThLWFZIVnB0Vmxha0tBbEMvVThKeXpZMU9TWUhrYzdPbiswNm85bGJaa3lDdHlUMjVNekVXV1huWGQwNWZxZlNTbGZLY0Myc3VXeEZENE5iTnRyazduTWJNbHNTUjMyZmE2b3V2L3pyc2Z0Q0JoSi9BSml2VWsxM2dkMGw5cmZhWUdxRmhkV0tOSTIzOHdiZ2lmUGNSdmp2SDYvWkMwMmd5V1pBVTAzcHpaejNvQ0lCSmhwSGNUQ0Q4QzdyUUNqSlFUUFdnVEx0YU5jUXppVmpDbUpOVmpNdzJTL2Q2UzFXdS90Z1NZazZzZm1LUkJ4S2xPUVd6SlhHUVVGYXNyTzc3NndQWW5vYmcrR2hrRUVZU2ZmdTdlRm84bm4rTkNYRFZpN21sR1BmN25FSHNLT1FLbGtXVGxPeXA3R0pKOEZlNGc3U2NCamxQNm9KNUxsL1FoMjVIa2pHcXUzZTZTMkNrWHY2Ym43R1hlbFVPWDdjNWZrNHpSMHNtRkpDV3pEcEV3OXBrbXIxbzFmdTZrMzloc2Y2QzJwTzVaNUhRdDVQcjV6bXlJeStoN3MiLCJtYWMiOiI1MDQwNDc3NTcxM2UyYjQxNGI3NzVhMzExYjk3YzczNzVkMGUzY2VmNzhiM2Q0YzMyMmU1N2E3ODRkOTZkYzA2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZaampmS0EzejJCTEs1TVNZMHNEVFE9PSIsInZhbHVlIjoiNHV3K1JGZEs1UDFlMHlFUVNqa3NLWk92eldxTWdjZGQ4MXFpdXBvY1h4NTg5SlNoeGx6NlFLOElqcGU0WHBxMHlKT0IwRlptQmI0VVlGZkpkQVMyRnlMZXBlWDhJSGVScDZHVmpGQktPVTA1YWFyL1RFcUxyekpOMEhvMjJpYUtvOVorM1NCaExqcEM3N3FVb1VTZlRkYjVZMEZGenhGRGVuelo4MWMvcE5LazRWYnJBSWI1dUJQR1lyMmsvYkYwYVR5SGlYN29RNE5Qa0xyWUdSbFlzUVhwZmlMbk10aVZMRHk4WVdKMGRZODhQb0tiWjhxemlqMUZsUFdPeFdkWDRiZjRyQTlVSEpVN1pCc0J6OGVaSWk2RithNlpnbkFvc1NiYjdzUlA2QnZmMmdlaTVTSnRJeFBWOHc1TThoZCtLZEZnMkxGOERmcmsyd2lWSnBNRFdnYXJ1b1o3bVRiRlZOZldMM1Vyd3A4eU5xWU5NdFJUTDhwMXd2ZGpHZHFiYllmSkNRSXR0NXllWStuRWUvRzhIdE04cUpEYlQrTHRLckVYMGROaGJDcWFWQlhmTWt3UnBKKy9SdzFIcVhQWXkzTWE2cFRPS3dtVVFUb2o2aWZTc1BwSUR6bDdYeUduVFFnM0JDSnBkRm1SV0JSSjFxOEtIQm5Eb2gwTW16aXAiLCJtYWMiOiJhNjQzN2U5M2FmYWFmNGI2MjFiNTdjZWYzYzRjNTRlOWU4YzA4NzE0NjZkNzBmZDBkNDY4MzM1ZGFhYjM1ODQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1412431734\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1110860724 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1110860724\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-120675824 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:00:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imw5V3AyM2krT21oWGp3QVVMNFJKQ3c9PSIsInZhbHVlIjoiMTF5Z1lNM3NVejdieGxEQ010azRnL01wakFyU3NtL0Y3b0t5S0VrZHhhUU10QXhPdzVjQ2lORXB2aDZJaWtTR1lGZ2RGY0xFK2tVRDYrdlcvYStBVm9saEpWQ0F5WHdCeC81MnQzcHNKMUJEenZ6QTlBVTdTUlFJNitJVTlXSnZvNFhCbExUV2ZpYzNjUDFHRnJTVmljWnNjT3dkbUpVaHAzazRBMGxVVWJsR3ZxSUF4TmIvbUdHZENNSDJGc3pDbERtMVBkUnY0bXYvaVpRM3JOcE5lT0thWkt0NElnRUUyUkU4OHBhckpocEJEMXNNUmw1aURwdlo2YjhGVG9YTnRweS9SdGIvSk5TS1M4UGFsVDJzRk14emxjdE8rcElNYmVacWc2cFJ1Z2JqbmxKRUduSHpnckplc3UwSUZQc3lqZXpNNXYrMVhCU01HNmJPcXJIVmd3L2Q5Y2VJQXlXTDArbklNZ3FSOTF5aG1sSlNLR0ZnVm9OK3lZYVdYV2RJNjNyOVNuVUdmQ3JIbDk2aEgxMmtBSUxiNTRNODRSOTNnYWRXSE00dEJ2QkFUZDJQL1k4bWF5NFVqTkFOanl2UmJGYzJFR01ITU0zWDVvTTlhK0I3aDFzdS9qSmFTdCtjTElhOEd3Q0tUK2pDRml2WXN4OE45eWxyci94RjkyUFEiLCJtYWMiOiJkOGYwODBiNjQ2NzY0ZWIxZmJkMGE3M2MxMTZhNjIxN2IzMjg4ODU2MmRhMDIyYTU0YmU2MWMxNjhjY2RhOGE3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9FcFpIczhhb0FQdjUyRCtEa2xhQ1E9PSIsInZhbHVlIjoiWXFPcVdkaTNFN20yTFp6Qnc0SGlqL3Bmb3Q5dGgxVXgyU2dDc201UEppdTJCZTRuOURGbXBWUXYydlFvZ0Z6ZFFPeVRLR2RmWW5tc2VkZkp3TEpsMi9iTUR3QXF4WFB0VjN3OTNnQnZMMFh4N3FNRFUycHNQeWc0aXNwNTVBZXdLUEJzRlFBbzJGYVJDTWI4YWVuRERxY21WUi9mZUdGMmIrU1N6cnhFTWw3R1kxYW93U0NYQVFieTFHc0xJU0Uya0Q2OTUwb3BUNzNRRU4wOEl0cFN2UUJCdlBrVmMyd2plNGxWQmxWR21TU0c1bnNFY3NYQWYvRmpWaHMzQTRFb2Y1N1pRR2FXWnF3RjNaMERRUVVQTGtZYjdsb1I5SnhGUmk1aTFmWXJCRlpxMjRvQVEvVmZVSWM1UmNhUkZtV0MvSkdRdzhyUjhsZEZZa3Z2MFhTYjlIK0cvNXJuS1FUN1dhc3lsbStTaVQvL3lSNDlqTm82Qkl5aERlZldpSmxycEpvbVRZRFdNL051T0h6NmNXbXRKWGIwOTlKY2ZvOER3SEFCWUNVNTBNek5WZGY5ZjVPaHpic0UyYmpocC9TbnF5SWR4RHloVUg0YURHVjRSczhHbVJmTnRuYXp6bnY0enZLbm5meEh1bzV6VHROeWVwWlByWStUN3lIUTZMNDAiLCJtYWMiOiJkM2IyOGU1MDY0ZTkyNWFmMjNhN2ZmNDRkM2RmZmFmNGE3NTkzYWVjYmM1NzMxZDRjZDY0YmQ1MTU2NjdiNTM2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imw5V3AyM2krT21oWGp3QVVMNFJKQ3c9PSIsInZhbHVlIjoiMTF5Z1lNM3NVejdieGxEQ010azRnL01wakFyU3NtL0Y3b0t5S0VrZHhhUU10QXhPdzVjQ2lORXB2aDZJaWtTR1lGZ2RGY0xFK2tVRDYrdlcvYStBVm9saEpWQ0F5WHdCeC81MnQzcHNKMUJEenZ6QTlBVTdTUlFJNitJVTlXSnZvNFhCbExUV2ZpYzNjUDFHRnJTVmljWnNjT3dkbUpVaHAzazRBMGxVVWJsR3ZxSUF4TmIvbUdHZENNSDJGc3pDbERtMVBkUnY0bXYvaVpRM3JOcE5lT0thWkt0NElnRUUyUkU4OHBhckpocEJEMXNNUmw1aURwdlo2YjhGVG9YTnRweS9SdGIvSk5TS1M4UGFsVDJzRk14emxjdE8rcElNYmVacWc2cFJ1Z2JqbmxKRUduSHpnckplc3UwSUZQc3lqZXpNNXYrMVhCU01HNmJPcXJIVmd3L2Q5Y2VJQXlXTDArbklNZ3FSOTF5aG1sSlNLR0ZnVm9OK3lZYVdYV2RJNjNyOVNuVUdmQ3JIbDk2aEgxMmtBSUxiNTRNODRSOTNnYWRXSE00dEJ2QkFUZDJQL1k4bWF5NFVqTkFOanl2UmJGYzJFR01ITU0zWDVvTTlhK0I3aDFzdS9qSmFTdCtjTElhOEd3Q0tUK2pDRml2WXN4OE45eWxyci94RjkyUFEiLCJtYWMiOiJkOGYwODBiNjQ2NzY0ZWIxZmJkMGE3M2MxMTZhNjIxN2IzMjg4ODU2MmRhMDIyYTU0YmU2MWMxNjhjY2RhOGE3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9FcFpIczhhb0FQdjUyRCtEa2xhQ1E9PSIsInZhbHVlIjoiWXFPcVdkaTNFN20yTFp6Qnc0SGlqL3Bmb3Q5dGgxVXgyU2dDc201UEppdTJCZTRuOURGbXBWUXYydlFvZ0Z6ZFFPeVRLR2RmWW5tc2VkZkp3TEpsMi9iTUR3QXF4WFB0VjN3OTNnQnZMMFh4N3FNRFUycHNQeWc0aXNwNTVBZXdLUEJzRlFBbzJGYVJDTWI4YWVuRERxY21WUi9mZUdGMmIrU1N6cnhFTWw3R1kxYW93U0NYQVFieTFHc0xJU0Uya0Q2OTUwb3BUNzNRRU4wOEl0cFN2UUJCdlBrVmMyd2plNGxWQmxWR21TU0c1bnNFY3NYQWYvRmpWaHMzQTRFb2Y1N1pRR2FXWnF3RjNaMERRUVVQTGtZYjdsb1I5SnhGUmk1aTFmWXJCRlpxMjRvQVEvVmZVSWM1UmNhUkZtV0MvSkdRdzhyUjhsZEZZa3Z2MFhTYjlIK0cvNXJuS1FUN1dhc3lsbStTaVQvL3lSNDlqTm82Qkl5aERlZldpSmxycEpvbVRZRFdNL051T0h6NmNXbXRKWGIwOTlKY2ZvOER3SEFCWUNVNTBNek5WZGY5ZjVPaHpic0UyYmpocC9TbnF5SWR4RHloVUg0YURHVjRSczhHbVJmTnRuYXp6bnY0enZLbm5meEh1bzV6VHROeWVwWlByWStUN3lIUTZMNDAiLCJtYWMiOiJkM2IyOGU1MDY0ZTkyNWFmMjNhN2ZmNDRkM2RmZmFmNGE3NTkzYWVjYmM1NzMxZDRjZDY0YmQ1MTU2NjdiNTM2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120675824\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-963426587 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-963426587\", {\"maxDepth\":0})</script>\n"}}