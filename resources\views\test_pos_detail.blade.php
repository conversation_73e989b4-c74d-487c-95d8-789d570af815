<!DOCTYPE html>
<html>
<head>
    <title>Test POS Detail</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>اختبار تفاصيل فاتورة POS</h1>
    
    @if(isset($pos) && $pos)
        <div class="success">
            <h2>✓ تم العثور على الفاتورة</h2>
            <p><strong>رقم الفاتورة:</strong> {{ $pos->pos_id }}</p>
            <p><strong>التاريخ:</strong> {{ $pos->pos_date }}</p>
            <p><strong>العميل:</strong> {{ $pos->customer ? $pos->customer->name : '<PERSON><PERSON><PERSON><PERSON> عادي' }}</p>
            <p><strong>المستودع:</strong> {{ $pos->warehouse ? $pos->warehouse->name : 'غير محدد' }}</p>
        </div>
        
        <h3>المنتجات:</h3>
        @if($pos->items && count($pos->items) > 0)
            <table>
                <thead>
                    <tr>
                        <th>اسم المنتج</th>
                        <th>معرف المنتج</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                        <th>الوصف</th>
                        <th>نوع المنتج</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($pos->items as $item)
                        <tr>
                            <td>
                                @if($item->product_id > 0 && $item->product)
                                    <span class="success">{{ $item->product->name }}</span>
                                @elseif($item->description)
                                    <span class="info">{{ $item->description }}</span>
                                @else
                                    <span class="error">منتج غير معروف</span>
                                @endif
                            </td>
                            <td>{{ $item->product_id }}</td>
                            <td>{{ $item->quantity }}</td>
                            <td>{{ number_format($item->price, 2) }}</td>
                            <td>{{ number_format($item->getTotal(), 2) }}</td>
                            <td>{{ $item->description ?? '-' }}</td>
                            <td>
                                @if($item->product_id > 0)
                                    <span class="success">منتج عادي</span>
                                @else
                                    <span class="info">منتج يدوي</span>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            
            <h3>المجاميع:</h3>
            <table style="width: 50%;">
                <tr>
                    <td><strong>المجموع الفرعي:</strong></td>
                    <td>{{ number_format($pos->getSubTotal(), 2) }} ريال</td>
                </tr>
                <tr>
                    <td><strong>الخصم:</strong></td>
                    <td>{{ number_format($pos->getTotalDiscount(), 2) }} ريال</td>
                </tr>
                <tr>
                    <td><strong>الضريبة:</strong></td>
                    <td>{{ number_format($pos->getTotalTax(), 2) }} ريال</td>
                </tr>
                <tr style="background-color: #f2f2f2;">
                    <td><strong>الإجمالي النهائي:</strong></td>
                    <td><strong>{{ number_format($pos->getTotal(), 2) }} ريال</strong></td>
                </tr>
            </table>
        @else
            <div class="error">
                <p>❌ لا توجد منتجات في هذه الفاتورة</p>
            </div>
        @endif
        
        <h3>معلومات الدفع:</h3>
        @if($pos->posPayment)
            <p><strong>نوع الدفع:</strong> {{ $pos->posPayment->payment_type }}</p>
            <p><strong>المبلغ النقدي:</strong> {{ number_format($pos->posPayment->cash_amount ?? 0, 2) }} ريال</p>
            <p><strong>المبلغ الشبكي:</strong> {{ number_format($pos->posPayment->network_amount ?? 0, 2) }} ريال</p>
            @if($pos->posPayment->transaction_number)
                <p><strong>رقم المعاملة:</strong> {{ $pos->posPayment->transaction_number }}</p>
            @endif
        @else
            <div class="error">
                <p>❌ لا توجد معلومات دفع</p>
            </div>
        @endif
        
    @else
        <div class="error">
            <h2>❌ لم يتم العثور على الفاتورة</h2>
        </div>
    @endif
    
    <hr>
    <h3>روابط مفيدة:</h3>
    <p><a href="{{ route('pos.index') }}">العودة لنظام POS</a></p>
    <p><a href="{{ route('dashboard') }}">العودة للوحة التحكم</a></p>
</body>
</html>
