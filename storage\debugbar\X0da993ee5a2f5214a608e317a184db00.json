{"__meta": {"id": "X0da993ee5a2f5214a608e317a184db00", "datetime": "2025-06-28 15:08:39", "utime": **********.37966, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123318.943037, "end": **********.379673, "duration": 0.43663597106933594, "duration_str": "437ms", "measures": [{"label": "Booting", "start": 1751123318.943037, "relative_start": 0, "end": **********.299914, "relative_end": **********.299914, "duration": 0.35687685012817383, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.299925, "relative_start": 0.3568880558013916, "end": **********.379675, "relative_end": 1.9073486328125e-06, "duration": 0.07974982261657715, "duration_str": "79.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49496056, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019039999999999998, "accumulated_duration_str": "19.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.338438, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 10.032}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3497288, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 10.032, "width_percent": 3.939}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nAND p.warehouse_id = 8\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`warehouse_id` = '8' order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3541448, "duration": 0.01638, "duration_str": "16.38ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 13.971, "width_percent": 86.029}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-1205865704 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1205865704\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-402623520 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402623520\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-71055388 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-71055388\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123070655%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhZZ3RUNmRZSi90elRCeC94ZkI0Unc9PSIsInZhbHVlIjoiYzNwdHgwVTBHbFowOEkwMStTY2NzOU85amdKYWNGUFp5ZTVOSUxtSXNiUFV1QVJPSk1yVHRqODlEUENXTm9iZ1NwMWs0WjV6OEN0a21LQXpSdlJCbzJCaGc2TkhLVEdxOEJhNHdPK2ptN0ROYnVieTN5ZjZIeW5OQ2c4K3NxZktSK2NSQUVETkJhcmFSY3Zza2VUYlF6OW9pSnFDbkdTTHRSSnlXNXBOVTZYM3BvOWNQV2NnOHIzdkxyRGdkTHR0bFJoMXowcVlrdGVTUldjejNhUk9MOEE2Y3pJZXY1cGF6ZXZzeTcxdTRaUmVEWEh6VWs3ZXMrVjFja1loMktvQ3BCdWRCRVFhY2ZDSGZ6STVGYy9RR3d1SWVCc3BOSC9kVEpYQlIrcDlnTnJ4OUhuN3VHRG9iWFBDTG9iQVRFNUpnaWQzamZ1NERRV0pMc25HUjQ0UWhmMGE5cGxDbFRjMDFkNXZ5WE5ianJWQkJPQ0hTaGhhR2Z4M1dBQ3pOTWg5anhQV09IOGQvWEV6OGZWNUJZa0ZOVXFVQmVRNXVPUUFPTGVPR0VHYUthT0FKM045em1Ka1RyTVRWNDFKZ1RhRERxdWMrZnZqL3JkZFFTSnZjQ3lCZFRpbnVaSmNuTFlRZXB1YWhJMjNWVjltU3lRaUliNXRTNHJEU2lqMUVtYXQiLCJtYWMiOiI5YmZiZmIzODE5ZTA5ODY2NDVmMmU1MTMwYzI4NmRkNTcwMGVhZjRmYzZlYTdiODk1ZTgzZTU2ODkxYjA4OTkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZJV1c4VG1wckF5SkxUcnVLdVdVL1E9PSIsInZhbHVlIjoiQVk4VGxJeG13bm9rTlhQeTdKTXg2N0V5SHZVMnZkNjdkWms2TG9kMFVWQmQ4ZmU3S3c4eUt6ZlpWRHVKRHBLQ2NoMmNGN0laRDhEK2Jvci9jTDY0aGdSeUtJREZpZUxKYk1LeUNZN3NvZVF3RjVNRE1TTXkrU012dDJab052SkEzdXp4MTc2TGg0d2xvaFo5N2NvYkEwUmhoNjVIWndkUmRZVktnUm5YNkZlbVdnVURXQktDeTRXNkp4TXpoSkVFcFhFUVc5bWd6TGsxZW5TbjRtMTZyWVl5RkN6U2QyTHZESGd5V29TRCtJbXhJd3NzNG5KaWtpenpIMjF5ZUt4bzRYcVlLWm9BVGk0aGUvdXF4VFBGRGt3ckFuQmh5eUpXNzRXOWRqa0xTLzljS2dOY3BhMVIva3lta1RRMDZzL3g3S3RWM3g2V3pnTkV0NE5XdnhUYytlY2VyVUc0YUV1NGRXWE1ocnNtMm5vOEZmcGh0b003cVE1T1IwcEJBampmdjNxVW4vcVRNd09CYzllNVQ2Wk5YTFRIU2Z5N3RVa3VDbjJoQWFnQkxFYnBRMVdVczRsamZqUmdsZTczSy9GY3A2citvdER3SzVUMjZDNEY5bTgrSmpvVjJNRmZRMUpLSENCVDZlVHNxOFBHVW45VWs0aG5VY2htMDdrSnQvQzIiLCJtYWMiOiIzMDQzYTY2NmI0YzFlOTM5NGYyZTM4YTA4ODAxNDhkNjQwYTI1NTdhZmRiYWMyZmQzNWRmODUyMDI1NDAyOTJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-770886015 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-770886015\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2063334011 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBRemxwNFlPaUx2cUxOcEs1VzJpUFE9PSIsInZhbHVlIjoiSjdwU1F4ZmtTL1hodUEyalNvRUE2QjVHUGVZWnFnY3hKMVhDNjZ4Y2txaDBSV1FlM3dHL0RvU0ZEd056b1FDRTZyWTNoNnQzb0MwbThBQUk3ZGZqKy9sSytJdHpmNS9TYzJsRlJuZlpuc3ozYmhWOHZIRFdOUmFtVnJFWWRDOHFzc3kydkJCQ2VtS3grRjd6VmxXcUV1SFpaRmNCRVFPRzhiclE5STZnWC8yTFVIT3Z4YWtlU2JpRG5XaU5UbFY1OGhMSWliUlNQWU1OcTlRS2IveUo0THZUVWFQa1VqWWxOS0tHTndNak5XM0dzSXUxd0wzS1N4by85bWx2SnRuSm5TRFRXN0hrcEUvZVRpTnRBcnM5Y1A4bzRzMTBIOGlJVGN4akpIbWRHODBndkY2SzFvQUk2OXV5a0JKbUswbGk4NmMrME4vZDNDdm9wM2x1d2VZNzBDNjUvZ2pLRzVyT2dJbVJDdDF5eDdLQUw5djVlV1pTZlRSS2V4MENLL2JhYUpVUWNTUmlvWm02dTIyWGNMTFAzc2dxS3hUeEM0MUplSC9xVzNxUDFMallWdlRTdkRmSmliQzUzSDc1WHMvQjdHcjFVOHRmRnlnY2kyS0hQZ2JXS1o4LzgrbnIyUStCeEZYOStsQ0ZQdVBEL3lhb0xEWTJpR2FKNHlsZEkxdHIiLCJtYWMiOiJkOGIxOWYyZmZkMWViMTM0NzczMmI1ZmU5Nzc5NGRjNGUxNTgzNDJiMmEwMGMwNjQ1NTA1NmNkMjNmZGVjOWU1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlR4N0hEWmF0ZndYR1dpQ1JkcmpLTmc9PSIsInZhbHVlIjoiQTlWRmlvMG41bkVxbHJ0QmNVV0VTVUZLelkzQ0g0YmdXMG1PZHNtNDJ6LzRlWUI0bjBIMFh6THhZdTlBOGwrai9hVjNiOXJlWTBTNEZVK3cvM1VRN2NZYkhpUnhXRXdZNjhLd2NTa3JwaGVmaEFmWjI1bzhqNHhZYUxkNWxVVjdMb1ZWaUdkdTQyZEM0b2hncVFVNXBxQ0FUZlRuL2RSREdqT1VyS2Z5TVRZUWg0VFFKT3RVU1J4eUhiVll0enJDVUhPa0ZtQkU0clZtN0pOMGl3NUJUeDBQMzF3UzJYZWdYYzRwaDd4eWxYcGk5alVVYVBWSUxhdEswVkJkc3N5ZHZSbDVBODk1VWEzRFJ3R2hmaUpSMy8xSStwQzRYMGx2ekp6bGwxYitxSkpQbzhUUm1JRjFwVlM0aUlTYjlMTmNrYS9ISU4vb3A2TGp0Q0VGL3hNczdMclNaZC9PaXBrQU5wdDBJRURRYXFGeVljOFRxcDE2bUZ5SThqY1o2NVhGNERwL0R3cW1sME40STh6WE4xVWtIUnN5b3MyOGQyZnNSUXZVSDBEVVdrN3plcHlNNlIwQ2pjZEhaWFFDVjZGbkplOWtHOE9XemtjZDVqTHBueGJld0hCWVdZajkyRFdzaVd0aG9EN1hlR1ZFaVBGSUJnY203enMrK05EVTZaaUYiLCJtYWMiOiJkMmFmN2I1Y2I0Y2NmNGFmNWQwYzBlODVkYjEwMzZhOWRjMTcxZWVjNDI1ZmVjNWM5MWY3M2Q4NjBmZGNiZjdiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBRemxwNFlPaUx2cUxOcEs1VzJpUFE9PSIsInZhbHVlIjoiSjdwU1F4ZmtTL1hodUEyalNvRUE2QjVHUGVZWnFnY3hKMVhDNjZ4Y2txaDBSV1FlM3dHL0RvU0ZEd056b1FDRTZyWTNoNnQzb0MwbThBQUk3ZGZqKy9sSytJdHpmNS9TYzJsRlJuZlpuc3ozYmhWOHZIRFdOUmFtVnJFWWRDOHFzc3kydkJCQ2VtS3grRjd6VmxXcUV1SFpaRmNCRVFPRzhiclE5STZnWC8yTFVIT3Z4YWtlU2JpRG5XaU5UbFY1OGhMSWliUlNQWU1OcTlRS2IveUo0THZUVWFQa1VqWWxOS0tHTndNak5XM0dzSXUxd0wzS1N4by85bWx2SnRuSm5TRFRXN0hrcEUvZVRpTnRBcnM5Y1A4bzRzMTBIOGlJVGN4akpIbWRHODBndkY2SzFvQUk2OXV5a0JKbUswbGk4NmMrME4vZDNDdm9wM2x1d2VZNzBDNjUvZ2pLRzVyT2dJbVJDdDF5eDdLQUw5djVlV1pTZlRSS2V4MENLL2JhYUpVUWNTUmlvWm02dTIyWGNMTFAzc2dxS3hUeEM0MUplSC9xVzNxUDFMallWdlRTdkRmSmliQzUzSDc1WHMvQjdHcjFVOHRmRnlnY2kyS0hQZ2JXS1o4LzgrbnIyUStCeEZYOStsQ0ZQdVBEL3lhb0xEWTJpR2FKNHlsZEkxdHIiLCJtYWMiOiJkOGIxOWYyZmZkMWViMTM0NzczMmI1ZmU5Nzc5NGRjNGUxNTgzNDJiMmEwMGMwNjQ1NTA1NmNkMjNmZGVjOWU1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlR4N0hEWmF0ZndYR1dpQ1JkcmpLTmc9PSIsInZhbHVlIjoiQTlWRmlvMG41bkVxbHJ0QmNVV0VTVUZLelkzQ0g0YmdXMG1PZHNtNDJ6LzRlWUI0bjBIMFh6THhZdTlBOGwrai9hVjNiOXJlWTBTNEZVK3cvM1VRN2NZYkhpUnhXRXdZNjhLd2NTa3JwaGVmaEFmWjI1bzhqNHhZYUxkNWxVVjdMb1ZWaUdkdTQyZEM0b2hncVFVNXBxQ0FUZlRuL2RSREdqT1VyS2Z5TVRZUWg0VFFKT3RVU1J4eUhiVll0enJDVUhPa0ZtQkU0clZtN0pOMGl3NUJUeDBQMzF3UzJYZWdYYzRwaDd4eWxYcGk5alVVYVBWSUxhdEswVkJkc3N5ZHZSbDVBODk1VWEzRFJ3R2hmaUpSMy8xSStwQzRYMGx2ekp6bGwxYitxSkpQbzhUUm1JRjFwVlM0aUlTYjlMTmNrYS9ISU4vb3A2TGp0Q0VGL3hNczdMclNaZC9PaXBrQU5wdDBJRURRYXFGeVljOFRxcDE2bUZ5SThqY1o2NVhGNERwL0R3cW1sME40STh6WE4xVWtIUnN5b3MyOGQyZnNSUXZVSDBEVVdrN3plcHlNNlIwQ2pjZEhaWFFDVjZGbkplOWtHOE9XemtjZDVqTHBueGJld0hCWVdZajkyRFdzaVd0aG9EN1hlR1ZFaVBGSUJnY203enMrK05EVTZaaUYiLCJtYWMiOiJkMmFmN2I1Y2I0Y2NmNGFmNWQwYzBlODVkYjEwMzZhOWRjMTcxZWVjNDI1ZmVjNWM5MWY3M2Q4NjBmZGNiZjdiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063334011\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1622629771 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1622629771\", {\"maxDepth\":0})</script>\n"}}