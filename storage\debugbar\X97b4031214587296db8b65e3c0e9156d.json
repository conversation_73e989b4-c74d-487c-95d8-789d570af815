{"__meta": {"id": "X97b4031214587296db8b65e3c0e9156d", "datetime": "2025-06-28 15:26:36", "utime": **********.792501, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.369743, "end": **********.792521, "duration": 0.4227778911590576, "duration_str": "423ms", "measures": [{"label": "Booting", "start": **********.369743, "relative_start": 0, "end": **********.736918, "relative_end": **********.736918, "duration": 0.3671748638153076, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.736927, "relative_start": 0.3671839237213135, "end": **********.792522, "relative_end": 9.5367431640625e-07, "duration": 0.05559492111206055, "duration_str": "55.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45617800, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026899999999999997, "accumulated_duration_str": "2.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.763815, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.03}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.773583, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.03, "width_percent": 15.242}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.779174, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.271, "width_percent": 16.729}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6InlMUXMvKzE4VnU2eGg2TjJIQXFDOFE9PSIsInZhbHVlIjoiaVlTZVRtZGNldnEvdC9aUDlla0NwZz09IiwibWFjIjoiYjc1NzA0YjY2NjhkZTBhOTU2M2U5YzYwOTRlNzA4NWQ5ZWM4NGU1YzlhNDAxMmVhN2E0MGMwNjIxZThiZmJkZSIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1240115289 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1240115289\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-708564623 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-708564623\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1212824039 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212824039\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1438531765 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InlMUXMvKzE4VnU2eGg2TjJIQXFDOFE9PSIsInZhbHVlIjoiaVlTZVRtZGNldnEvdC9aUDlla0NwZz09IiwibWFjIjoiYjc1NzA0YjY2NjhkZTBhOTU2M2U5YzYwOTRlNzA4NWQ5ZWM4NGU1YzlhNDAxMmVhN2E0MGMwNjIxZThiZmJkZSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124394397%7C16%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImV6VTJmeENrNVYxTGlJc3N0RWYvSFE9PSIsInZhbHVlIjoiZlBsWnk0NTRRZnhVWCtkY3d5bU5rVW9BYXdVcSt4Z0R2alh2ZzJySmlqdGQrTkQ1aHFSUUN2Y1F3ZDU4bWl2MU9zc09hUGEvU1p3YmViVG5CbnNNVGhGeDV0NHdTdzNxOE9tc01FSUNpc2ZFVTQ3cm51TjZsVVZmajg0MHpkMFNIeklwc24zQUxNOUZqSlloMnhPbUFzOVlpRGhBRGRjeUEwQkhtSXFzUlB5aXVSTm5EclFHUGl0YTZDWHo5R0FLZHFSYlFiWGFRRkdiRU50eUhBRlkyVVNDZlZzWmRHbHp2Ujl0MmhmeGo3WnVKVFE1WjBpbkgxZWFUOE1TVEJGNXJiZlp2aXpDWVFxekwwd2FMK0pkNU5wZGdHbFd2U0loM3ptT2RyM2R0b0I2U2JlSHZKSWxseS9JZVA1M2RLVm1hWE9RMlh0cGFmOGNNblpRZ1N4SkI1U0RncEc5bWNIRitPb0lZczBUb0dnbEY2NGJrQkY5K3BJODlmSzVzTkFOS0dYeFhjTUpLSXFaV282OHppYmJ2OTlZQTRaczhXcFZuaWZOcEdOQ1dZYnVodURsQ0dYRDRqUWZTazlWbjQzL1ErTG1mZDdMS0FDUDVEUEZQZXo2SDJUblRXNG5INDhOWHhPNWFLQUNIT2R3aTlXWWZ1aGpDbisvWi9sVW4rR1MiLCJtYWMiOiJmY2VjMmNiYjA3ODhmZjFhMzI1ZTA3ZDI3NGRhZWExNjVkODkwNjJlMDA0YzlhNDhiNjQxZDc5YTFjNzE5MDlmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhqQk9DeUh0dkpzbEw1OGpJMm5tcEE9PSIsInZhbHVlIjoic3RLa0YxNTRkUDdUMEtwbTgrNjJuM0lJak5vb2g0SGYxc0dxS1FtejAvRnJVbGExYzlXZ0RSaTU4dEJna3oxYXlzWXhJNHF2dUI5TUlET3lXMnV1OGFBckZiNlo5SnZrZWpLZmFCWjJmcVk4bEV5YnFsV2NlTUhNOXhxbUdvckxDUVE1WVhxcW4zV1pSWk0xNmR5T25QN0FyeG1LVXc5bjZtTjltaVp1cVhvYldxdW9qaFhzYlpiU1JSZVZuY3o2MnBiTEQrdEYxMDgxcGNwaFU3djRaNVY2ZlVIYTBvV0xzS1o2OVJsU1pvaVQ4ZEdzYy9DWGNXTDBLSDFqYkhENUFPNXB0YzYrTEUyMW9FVEFBSWJCN1cza1lGU3hBL1hoNzhiZUxDSVN4Z1E1VUxRUDVKdTg2WTYrZnMrWTdWM09UdjJhLzNDZ3d5RUJaL05JS2dyYzF3VUUrT1d2bWQyVGJRSHhKeDU4dUcxbFM4NkltVlVBdmlQNS92cVZRSlBLSGFtbGhvQjBrV01rb1FXaUFjbWNpRjlOZStjdGJldC9XMXI0Q21FS2xoMmRnTmhrWWY3WVJOTUxQeVpuQ1lOOGtDZTRVZGx4dk5aWWRJcDFZajVyVU9RYzJ3SG1qa1REV0szU3NjdU5SejN2SWp2aWJXUkNZSzNQNmVmY2xQelYiLCJtYWMiOiIxNzlmMmFkYTE5YjIwNWE3MjYwN2M3MDk5MGY2OTQxMjczYTJiZmQwNDlmZDdiMzhhOWIxOWY4YzFmZWJlODU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1438531765\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-870204105 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-870204105\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1769623592 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:26:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZXWE1PaEdERmFFb3FsL1NaRUFocWc9PSIsInZhbHVlIjoiNThWVTY5eUloV3B6alRDMU4vcWh4QkFCS0pNNEdDY2UvWkYxQ3N4VnBLK3VZcm5wTGk4UWFQSGJxK2RBbHBOUkE5YmVRM1VGN0ZxcXRJUGR2S1ltRzVodW50OUU3R0V3bmpGN1VockwxK2hKNjZCTGZQK0VNSzVvRmxDZ2FteXhMcEZRd0VLM2kxR1JFMnlzalBrZjdhUEZPdlhLT1RHZ0tGQzNTUFZQa21JOTE3akpzVGdtY3YwRU1mSGMrbzlpYzhsU2QvandPTFJEZUVxUWFmLzY5WVgwWXlXK2VJR3c0Y2xDbzdZMmV3WktOaVl3YkVybzkxUmhYQkZjcG1GenlKWnRSSXV3bmZ3VUtZMWxBN0VuK1hSdjVHS1dCWFNjcDNjTGhQbDBqRHFteDFrd0o0dEtaQWNNNFA3ZEpuSk93S1RuMEFmT3hHT3lNakJMazFjSitoMlBJVFlnTWZEQ2t1Qm9SUkFkREdsYk1aV25mZkZnVXJWWjl2aEZ6YlNZSkRseEJFNk1MUVdHd2IyZkFWd29lRDdkYURxUGJMZTVaTE9ocElxZFpRNXF6NGs2SWlPM1grK0pyYjl2QkI3RUt5UXV1S0puNElqK2lWMEN0L3E5RXJwWUtjT2VsclJIRytlNVJLQncrUllPUmRlQjZkZG96Yk9LQmxmcEdZa1giLCJtYWMiOiI4MjM3Y2UxN2NlZTc1MWY4YjNjMzE4ZWQwMjg3NmEwOGMwM2UzMzY4NTRlMmVhM2JjNzY2YjJiODFmMjU0OTlhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikd5MWxxdER5ZE1xQ1MyUDJDbUU2NVE9PSIsInZhbHVlIjoiT2doZFdEOTFDd2tORkt2MHpobDlMdTZVMmlJYktrMVk2bGN6WGR2VDNZSkRuN1Q1TTZhK0gyK1ZULzhHakRjMVMwTzk0VGpvYTFFcTJSTlJQYXdCcVFUY1JSREs4eVFEYWFyUFdsU050RTh3RHFsaFBYc3JxWjRHZjVqSEJOOVQzUHE2QjJmeDY3WitzeWxlV242bFFSVGlLME1HeVdpWk1lSGxldUlRRmZnQUErUlpjS1BlSHdlcGxoMG5JQlk2NHVjUVdWazVTQ1ZEQkJPOEdZRmEwMmd4RGh3QTBWQzNISG5BRTZRU0s0am85L01IMkJEVGhYT1VBeStlR1dIdEZHZjBHWWNjb3loTzdKZUNCYU5yVEo2dThQRU5CYUxKN0F4MW5jWHZSbHg4WjJGbWo1Qlc5dExtczFPQzlWekpTQWZycytyYmpLRFZBb0JsbVhCUzdieEQvVU1YOW1CdFlGSk1jUUJKeERhcXFmL2htelN0TTZ3RkZjL0FGY2czc3pUV0kzRU5velNmZjBQbVNKMVN2ME1kK0E0UER5c0JMczlnM3RKRjNtSm41U29SL2dDWEtoVkdSa0RZQUVCdE9oWnFLN2VmOEV3UWRQNmJXcElBeVdsTmVjc20ycElpWlJ6a2tlZUVSK1BWTWJTdjZ3RDhMMHgxUi9YeEc3aGEiLCJtYWMiOiI1NDhjYmNhZmYwNzY0ZTNlZDQzZDIxNGFmMzM4YzM5YTU2OWYwNTMxMTY1ZDI0ZGU4ZDNiNTg5MGRmNjBiYjZhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZXWE1PaEdERmFFb3FsL1NaRUFocWc9PSIsInZhbHVlIjoiNThWVTY5eUloV3B6alRDMU4vcWh4QkFCS0pNNEdDY2UvWkYxQ3N4VnBLK3VZcm5wTGk4UWFQSGJxK2RBbHBOUkE5YmVRM1VGN0ZxcXRJUGR2S1ltRzVodW50OUU3R0V3bmpGN1VockwxK2hKNjZCTGZQK0VNSzVvRmxDZ2FteXhMcEZRd0VLM2kxR1JFMnlzalBrZjdhUEZPdlhLT1RHZ0tGQzNTUFZQa21JOTE3akpzVGdtY3YwRU1mSGMrbzlpYzhsU2QvandPTFJEZUVxUWFmLzY5WVgwWXlXK2VJR3c0Y2xDbzdZMmV3WktOaVl3YkVybzkxUmhYQkZjcG1GenlKWnRSSXV3bmZ3VUtZMWxBN0VuK1hSdjVHS1dCWFNjcDNjTGhQbDBqRHFteDFrd0o0dEtaQWNNNFA3ZEpuSk93S1RuMEFmT3hHT3lNakJMazFjSitoMlBJVFlnTWZEQ2t1Qm9SUkFkREdsYk1aV25mZkZnVXJWWjl2aEZ6YlNZSkRseEJFNk1MUVdHd2IyZkFWd29lRDdkYURxUGJMZTVaTE9ocElxZFpRNXF6NGs2SWlPM1grK0pyYjl2QkI3RUt5UXV1S0puNElqK2lWMEN0L3E5RXJwWUtjT2VsclJIRytlNVJLQncrUllPUmRlQjZkZG96Yk9LQmxmcEdZa1giLCJtYWMiOiI4MjM3Y2UxN2NlZTc1MWY4YjNjMzE4ZWQwMjg3NmEwOGMwM2UzMzY4NTRlMmVhM2JjNzY2YjJiODFmMjU0OTlhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikd5MWxxdER5ZE1xQ1MyUDJDbUU2NVE9PSIsInZhbHVlIjoiT2doZFdEOTFDd2tORkt2MHpobDlMdTZVMmlJYktrMVk2bGN6WGR2VDNZSkRuN1Q1TTZhK0gyK1ZULzhHakRjMVMwTzk0VGpvYTFFcTJSTlJQYXdCcVFUY1JSREs4eVFEYWFyUFdsU050RTh3RHFsaFBYc3JxWjRHZjVqSEJOOVQzUHE2QjJmeDY3WitzeWxlV242bFFSVGlLME1HeVdpWk1lSGxldUlRRmZnQUErUlpjS1BlSHdlcGxoMG5JQlk2NHVjUVdWazVTQ1ZEQkJPOEdZRmEwMmd4RGh3QTBWQzNISG5BRTZRU0s0am85L01IMkJEVGhYT1VBeStlR1dIdEZHZjBHWWNjb3loTzdKZUNCYU5yVEo2dThQRU5CYUxKN0F4MW5jWHZSbHg4WjJGbWo1Qlc5dExtczFPQzlWekpTQWZycytyYmpLRFZBb0JsbVhCUzdieEQvVU1YOW1CdFlGSk1jUUJKeERhcXFmL2htelN0TTZ3RkZjL0FGY2czc3pUV0kzRU5velNmZjBQbVNKMVN2ME1kK0E0UER5c0JMczlnM3RKRjNtSm41U29SL2dDWEtoVkdSa0RZQUVCdE9oWnFLN2VmOEV3UWRQNmJXcElBeVdsTmVjc20ycElpWlJ6a2tlZUVSK1BWTWJTdjZ3RDhMMHgxUi9YeEc3aGEiLCJtYWMiOiI1NDhjYmNhZmYwNzY0ZTNlZDQzZDIxNGFmMzM4YzM5YTU2OWYwNTMxMTY1ZDI0ZGU4ZDNiNTg5MGRmNjBiYjZhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769623592\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1262251317 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InlMUXMvKzE4VnU2eGg2TjJIQXFDOFE9PSIsInZhbHVlIjoiaVlTZVRtZGNldnEvdC9aUDlla0NwZz09IiwibWFjIjoiYjc1NzA0YjY2NjhkZTBhOTU2M2U5YzYwOTRlNzA4NWQ5ZWM4NGU1YzlhNDAxMmVhN2E0MGMwNjIxZThiZmJkZSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262251317\", {\"maxDepth\":0})</script>\n"}}