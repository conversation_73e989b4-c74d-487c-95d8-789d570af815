{"__meta": {"id": "X6bb746fcc5b3eba6374e540bd311d292", "datetime": "2025-06-28 15:09:24", "utime": **********.584434, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.120032, "end": **********.584459, "duration": 0.46442699432373047, "duration_str": "464ms", "measures": [{"label": "Booting", "start": **********.120032, "relative_start": 0, "end": **********.527125, "relative_end": **********.527125, "duration": 0.407092809677124, "duration_str": "407ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.527136, "relative_start": 0.4071040153503418, "end": **********.584461, "relative_end": 1.9073486328125e-06, "duration": 0.057324886322021484, "duration_str": "57.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45068592, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0031600000000000005, "accumulated_duration_str": "3.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.557234, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 56.329}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.568325, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 56.329, "width_percent": 26.266}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.574876, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.595, "width_percent": 17.405}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-964841222 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-964841222\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1854802425 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1854802425\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2105810066 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105810066\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1217872047 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123070655%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZZYUFuWFc1TFN4UHZwODBwK0M1dFE9PSIsInZhbHVlIjoiK2Y2RU13T3ErZ1hnTG00ZnovZmNyOVhhUklCNHlWb1lQTmdKbnRET0ZkUGRJKzFBa3Z2VzNPbXhyc2hEZ0pKYUEvVWJuZkcyL1dVam1nTU5VNFRnZjFIZ1lvZmoxd241Sm1RVTFNUnZvc3F6ckVoZXRwTnV6WVhJeU5hUVlCNmp1ai94N0dQS1d6NmJKNTNVWHZ0eU1VQjhRclVHSGZjcStmOElqUWY1MzdjQVZkL09VbmhTNU9ZT1RqODZmTU55NHVKK1g5eUhVT2g5MUxoM2lqamlxbldzWEJaTUtqR2hkdkl6UWNLZkd1MFVkdDdDYWJVZVNWSHZlKzIrOU5VbnZtcDhSSVlEVEJpUVNGTlFiK2M3NDNTUldueE41UC9RVjRCWExURnFySXBxalgvL2xmUnhyQk0zL2Vrc3RGRnV2cVN2UEQ3SlozRlMwRmRmc080dVF5Z2MxT0VvNEJtUi80ZGlDaTBJTStvMlJYakkrYlVFbnRQZ3FGcWtDUi9LS1lJZGNrekFHWVN6c2o5emw4VllzRVpCQjhvdGdCaCsxcWRTc1kyem1rSUltSG56NjhzbGlxMWJubStwRHJpd3dGQXhEaUlsTjBDcWFlSjZzcXFVc3FyUWpZWG5UaHBEak10bFdGUWRaRG9Da2RRZ1JnWjdHT21vUlhYMXlrUUMiLCJtYWMiOiI4ZTVhMzMxNGM4MGMyNmIzYTMyYjcxNzI0MDk1MDdhOGI2N2MyZDMxNTY1NzU5OWM0YWI2Nzg0YzZjMjlmYWFmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFUVnhZc3U2QmpYb01mUmJhVnk4VHc9PSIsInZhbHVlIjoiZWR4UjNDenNHVUZaWnVXdFNKTm1JZERGc0xhNVJkNnlzV0xiU3FpOVljZW5PWGVJRkVjeG80Sy8wL20zMVN1TmFlOWQzUHdOZy90VzRHc0lMVytoQUZ4VXRQWDUreG5YWUtzRHh0U0VMVDByK0h3cHF2WUlCcWRCSTR2U3NsY0RleDU1MkRBalJlRWZGdjJlbG5vSmpLb2E5Qll4MzM0ZXUySkQ5c1dRZHNoK2lFS1pMQWs1RlBMTkdJNThYOWFDdEd6MDZBREU5LzZSN1hYVDFyeWtQUURnZmZWR1pJQWpwOG5lK3V6d3pyZHRVL20wa0IrbXJFdVdOd3BQazUxZTc5L1RmdE5XQTRRMndFZjZiZzJRWHdxU0habnBFc1B0b2V5VFlpeW55dUN3eEIwMVNmSkFqUzdwR2E4SFE4RERxYWhsb1NRRGRFUmdHT2hTMWtBa0doQnNQOVQ4QWxVWkFRWDJVRmw4MU5rcEFNN2VxbHkrZ2ZIeS9jaVlhbDNCaWxLWEhEWHA0N0U1dGJQYXVNQUZHb2grTytRUnFZS1V0Yk80b2xTTlpvazFpeEJBdmhLSy83SEdoMnNNUWhMbFhqUGtIOHBhNEY3RFNaOTMrYnJxbS94QklyRnpRcHpVdUV3UkNOd3BVLzlYbURPaHJRZmpkS0Q0QUkxdVJIK0EiLCJtYWMiOiIzYTM0OTgzMmM5MWNiNzQzMTAwM2U1NWIzY2ZiZDE1ZGI3YmVlMjA2YWZiZDM5YjYyMTRiNTIwNDJkOTdiYmJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1217872047\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1049079837 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049079837\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1816595902 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:09:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZJaTZtY1RkSVkxYTVqdEZ3RlBvSEE9PSIsInZhbHVlIjoieThXZjAwTW13WUhQRDU5R1VsNlJmVEl6bWJqT3MyOFZrODFHMjU5ZkRjUmlHdUg1bWQ1VVNIbGxxSjZUbUVvbGF5WFB0bFhNYk1HQktVSnBMQ2JIZTlqWUl6Rjc0OW1XTDBrMmExTFJ2OUtKb1R2YnBpcW5tdkhHUUxhWGwwazgyeXNXKzFVQ3o2SHIvaHBwMnpWOGkxcVQzOU40WUl3Ti9xV1VvR3lIcWhQVEZGdEczTWpZMlhETG5VSHcwTmkxbEZmOWh6NFlITy93bWtBRWRrWFUwTzFJb3hYUnRUSjlFaG5tUjh3YWlJVWk5ekY3eGxLTXkweVYvazg4OFlyVDdiTmtTTVFjZWJIRG45YWdVYmhGSElDWWdndkp6VE00dWg4RG9mYzhOSXMxVFRyUkZIZ1BiNUhGS28weUd1U1dWYlBkWStGcGVmMnFibFYwMVJ6TGVFcE4zblJUMlpaQlJaTnZma202QlppdDU4bnluUlpndUtMaG8rRUJSTHR2QStZUG5kZHl0UEhBTklhd2xUV1FWMnpKNi9aeng0NHk5VzZBdGZCSTYvVnJyd1lIVUhjTjRPUGw3ZGFzVEZDS3FMekpGVDBYRXVpK2szenh5YXcvRzIrT0N0b014WXlWUDlMNW1SeUZzVmZnenR6bXkrbThuY2hMakNiQ242Y1kiLCJtYWMiOiJiYmJmM2YzZWUyYjNlZWY4OWFkZWM4YmQxOWIxZmFjYjMzYjhjNWMyMzlhNzIxZTI1MmYxOTEzM2QyZDNkMTZkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:09:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlFidGxrQ081M1NHT2FHaDVUMHRQN2c9PSIsInZhbHVlIjoiZjNjaVZEUmNmdGd0S2RaNXloMng2S3NScDlvM1ZDMlJIVUJ1cEtOdGtlOENQdEtDU3dSVDM2emFCNUpXWG83Tmh6a29aeTlGYThhZEhwTGtXQlh6WXNCbGc3RGUrYnJ5R1hMcHlZa0xXb1Zuc3VuNXlyTkdka3FOUkI4ZG5Mc1VxaEdnMXVhRWFJNDFWYnMzUFJ5MktabjBESHFVRXZmRTlvZi9EYUw2TzhtZkd6bks1UnpwYXVRS1o1cDl3a2sxMWI2VGdGdTRxU3d2WitvdE9ObnpOWGpKa2xVK3NyVm9qQUlDb0YwUC9NMmN1NC9IVnhTQkQrZGxyT1hPMWF0ZmFmcU91QlNXRjBGY3JmN2RYWXltRjFiZXk2aDIxSzdVQXE3RFJSTTRwaGlCelErbjk1SFpNeTRTS3RQenNDVXNqNm10Q0ExajFQbzVKR0g0Q3RzY3diamFWTmc5eUhnSWNrNlNkb1YvdFdablZlSy9SQVQyN093L3J3NjhSWjNFMUtzVDJybENqUjdWMVBGTzJxNHc2UkozRDJFWjhEUTNIZ21zSm8waWRkdnBNMVU2T1doSWMrbWpMbkcxTWduQzd0ZCsrT2VtY3JreEp5cmcvb0w4T2NrMU1hNit3U0xRUU1NKy9aL2dlOWVMRTdOMHhvNUFPQXZGbFJmTjJCaXgiLCJtYWMiOiIxOGVmYzk0YTdmYzA0YzRmNWMwZDQ4YmFlZTlmNjBiYzBiNTQwNTk3MDRjMzYyMzdjZGRiMTVjZmQ5NzQ1MDRjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:09:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZJaTZtY1RkSVkxYTVqdEZ3RlBvSEE9PSIsInZhbHVlIjoieThXZjAwTW13WUhQRDU5R1VsNlJmVEl6bWJqT3MyOFZrODFHMjU5ZkRjUmlHdUg1bWQ1VVNIbGxxSjZUbUVvbGF5WFB0bFhNYk1HQktVSnBMQ2JIZTlqWUl6Rjc0OW1XTDBrMmExTFJ2OUtKb1R2YnBpcW5tdkhHUUxhWGwwazgyeXNXKzFVQ3o2SHIvaHBwMnpWOGkxcVQzOU40WUl3Ti9xV1VvR3lIcWhQVEZGdEczTWpZMlhETG5VSHcwTmkxbEZmOWh6NFlITy93bWtBRWRrWFUwTzFJb3hYUnRUSjlFaG5tUjh3YWlJVWk5ekY3eGxLTXkweVYvazg4OFlyVDdiTmtTTVFjZWJIRG45YWdVYmhGSElDWWdndkp6VE00dWg4RG9mYzhOSXMxVFRyUkZIZ1BiNUhGS28weUd1U1dWYlBkWStGcGVmMnFibFYwMVJ6TGVFcE4zblJUMlpaQlJaTnZma202QlppdDU4bnluUlpndUtMaG8rRUJSTHR2QStZUG5kZHl0UEhBTklhd2xUV1FWMnpKNi9aeng0NHk5VzZBdGZCSTYvVnJyd1lIVUhjTjRPUGw3ZGFzVEZDS3FMekpGVDBYRXVpK2szenh5YXcvRzIrT0N0b014WXlWUDlMNW1SeUZzVmZnenR6bXkrbThuY2hMakNiQ242Y1kiLCJtYWMiOiJiYmJmM2YzZWUyYjNlZWY4OWFkZWM4YmQxOWIxZmFjYjMzYjhjNWMyMzlhNzIxZTI1MmYxOTEzM2QyZDNkMTZkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:09:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlFidGxrQ081M1NHT2FHaDVUMHRQN2c9PSIsInZhbHVlIjoiZjNjaVZEUmNmdGd0S2RaNXloMng2S3NScDlvM1ZDMlJIVUJ1cEtOdGtlOENQdEtDU3dSVDM2emFCNUpXWG83Tmh6a29aeTlGYThhZEhwTGtXQlh6WXNCbGc3RGUrYnJ5R1hMcHlZa0xXb1Zuc3VuNXlyTkdka3FOUkI4ZG5Mc1VxaEdnMXVhRWFJNDFWYnMzUFJ5MktabjBESHFVRXZmRTlvZi9EYUw2TzhtZkd6bks1UnpwYXVRS1o1cDl3a2sxMWI2VGdGdTRxU3d2WitvdE9ObnpOWGpKa2xVK3NyVm9qQUlDb0YwUC9NMmN1NC9IVnhTQkQrZGxyT1hPMWF0ZmFmcU91QlNXRjBGY3JmN2RYWXltRjFiZXk2aDIxSzdVQXE3RFJSTTRwaGlCelErbjk1SFpNeTRTS3RQenNDVXNqNm10Q0ExajFQbzVKR0g0Q3RzY3diamFWTmc5eUhnSWNrNlNkb1YvdFdablZlSy9SQVQyN093L3J3NjhSWjNFMUtzVDJybENqUjdWMVBGTzJxNHc2UkozRDJFWjhEUTNIZ21zSm8waWRkdnBNMVU2T1doSWMrbWpMbkcxTWduQzd0ZCsrT2VtY3JreEp5cmcvb0w4T2NrMU1hNit3U0xRUU1NKy9aL2dlOWVMRTdOMHhvNUFPQXZGbFJmTjJCaXgiLCJtYWMiOiIxOGVmYzk0YTdmYzA0YzRmNWMwZDQ4YmFlZTlmNjBiYzBiNTQwNTk3MDRjMzYyMzdjZGRiMTVjZmQ5NzQ1MDRjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:09:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816595902\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-794234992 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-794234992\", {\"maxDepth\":0})</script>\n"}}