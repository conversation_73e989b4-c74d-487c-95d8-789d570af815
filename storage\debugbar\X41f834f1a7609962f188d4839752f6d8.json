{"__meta": {"id": "X41f834f1a7609962f188d4839752f6d8", "datetime": "2025-06-28 11:22:31", "utime": 1751109751.027299, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.46453, "end": 1751109751.027313, "duration": 0.5627830028533936, "duration_str": "563ms", "measures": [{"label": "Booting", "start": **********.46453, "relative_start": 0, "end": **********.851137, "relative_end": **********.851137, "duration": 0.38660693168640137, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.851146, "relative_start": 0.3866159915924072, "end": 1751109751.027315, "relative_end": 1.9073486328125e-06, "duration": 0.17616891860961914, "duration_str": "176ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48136992, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02255, "accumulated_duration_str": "22.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.928782, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 7.361}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.939852, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 7.361, "width_percent": 2.173}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.954251, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 9.534, "width_percent": 2.262}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.9562688, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 11.796, "width_percent": 1.907}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.961123, "duration": 0.01813, "duration_str": "18.13ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 13.703, "width_percent": 80.399}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.981802, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 94.102, "width_percent": 5.898}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-188791990 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188791990\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.959963, "xdebug_link": null}]}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1743179262 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1743179262\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1148510470 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1148510470\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-756859557 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-756859557\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1603785506 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IkJDVWdnYnBHd3MxY1NxZXR1QmtzSVE9PSIsInZhbHVlIjoiZnNVWlVFMHJCeC9kMEp5SWhoelF4V3BybWJva0ZNZ21FL2F0Zm50YmxJMzAvbXl6dXlNR2lGRkNOcVY3WnU0Um1iYjIzUURmZVhmMkFQZTV3V2E5MEx6cmFHR3BLbzFIeVZ4TU9qeWRiMFEwQm9SOVhHZlFYdGdqWkZiaTJJZE5JYlJoQ0lxL1Z4L291UXJ0SEs5YUZTSlNRRjYvWER0cHJZYmQ3cUgvZ0RBRk5WTTRXUmxTWUhURUlyZ3VYYlR2cm1uOFpvbm5ianZWYWQxSzltRFBNdWMxSWhIWWhicGhjZHpmdmU3Q0JZV25XZHFFMStLd3hhQjFLQ21xTUlJa1BaVmMwVmlWQlpUM3RHV0c2WG43K3BMMzQwczJmYXN5ZkRwQTZQdnplU0R6QWsvTnFCTFZncW10OXlhV3VweklFcEFncmVUS3JNTVE0UjFTbWx0WGg3VmRUQXF3bjkzcGFzeU1KMXlNNlVKNUVxYmJPMzdwTTVlcHF0a05BREN3eWhlSjhFaEVpQmtqbUtEUFgxeUZaUEt4YUZ1Z1JUb1BYZjVYRGtRY2ZKN1QyUHVLenovL1RRNGFoS2gvc01CZWwyR1BrR1NzV1pobjh0ZG56K3NjcDVNQnFtWWxtMC9DelJNaHpIb0lESFVmb0xvZXBNUVRGVXZIRFFRQXpnREQiLCJtYWMiOiIwYjExZWE5NGYxZjY2ZjM2OGQzNGRhYjMyNDY3M2M1MjFlNDg0MTQwODQzOTc0N2YyYjI0OTk0ODUzN2RjNjJlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRscjdrMWJ0VGMvY0h0Sm9EZ1N6VlE9PSIsInZhbHVlIjoiWGxKOEs5Q1ZHZGFYWml2UWtNODJNaDlERVdWTkdhZXJKL2U0Vjg4Tmw5VktmQ1FIa2pJdlVxN0VLQWQxNFVFWVdXRHA0S2FqdXN6emhvMHhkdlZJWEprTGVFVGE3YXR2MDBndFZzSlkvWFlpNnNiTW1UcHBsa2J3YUpQUi92clI0dUJiSzQyZnVCb1htdXY4N29tOEVNeGgwQjUrSGxKd2xPMG55bkV6c3hKV1Fid293c283YnNhNzZWZTdWNEgrdXBjcVpGN1Z0T1E1alduWENaNnpYVlRLVkxtWkdZSlFEdXlxYVFDeW9zTjFXa1hKTTNpU0c0S0N4S2NOZDF1U2krZWY1ZUtGNXluTUpPanFSZWJrejBzaFZKWlBKWmhTbXFkTTFzL3pzYTUxUmFmWE9ERG5OcGxWendaZFczMUE0WFVJL1BMMG04NjJNVHErVVJISVB0L0trV2lUWm9jNFBqQm56UlVoOTRqYjdUS1RkSFo3ME0rQWhnMXNTUTd6NndPSVJnVHRyMWttL2UvWlFrNFRTZEdpb08rWnJ5bjkyQkwxWlg5ME04KzFHaEFDSDI0Y1FzWUF4aGJRQXVhTUJjWGxwOVVpckxuWjFsbTRPMjY5dFJWVlJON2tRY2MrS0FnMFJQYW1pbk5OTmNtNEc5aHJyUUpLeEhzVThHZmEiLCJtYWMiOiJlYjVhN2I2NGRhZjVhMjE0MzdmZWRlZmVkZjk4YmFkMjVjMzgxMmNhYjU4ZGU5NzRiZjdmNDhhY2FmZjc0NzFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603785506\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-60263894 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60263894\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1756478269 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:22:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhkRXlrN3hPUit3TUtlRFhvYWZZSVE9PSIsInZhbHVlIjoiZHF1MjdoMytrcmlycUdOLzdBWEd6ZmFZY2NJbmNHOWdiU1hXQ3NGMzV0ZWRkdzNjY2YzWWFpcmU3Z3VKaE5TNXh2WFZ0TkFUNEhsK1dQNDI5N1dhQ2NIY1lBMWpLMnlMbnQ0bnM3N2tTQklxWUZLUmJZSUhJMHhBaDk4ZzBqZkdSKzNzUzJDaVF5S0V2Vld2VW1OajJPTk9jWmdzcTJab3hkelRjdlQwcE9iQVlTRkVTTE9HVWFlRnRqMmJWZllkS2hLeG1xUlRJSjZCVmtJZS9kc0hzcVcwTHQyM3Rxc1ZtSVJPOXlSeTQvV0tqVSsyODhHNnVsQTVQQWZKN21pZ3cwOE5wVjV1UjdVZTc1MzcvMVhVbzhJeUlaYUtnY3VpMmhqeUV2MS9VR2JpUVlhV0VtOVlMcmp4S1pGbU1PT24wUFJKS2xuaGdYTm1WMnVXdXdTVXBQZkhpNmNHQmRUaUtuV0t6Qmg5Ylhua0d4cU1KL1NkVDdHZFIyTTdmRHo5ZU9oOXkrb2pXTElsbkxETU83UnRKQlp6T2M3L3BJMFFuRXNnTlFqdDdvV3d4NEt5ZWhOUG9Pamp6UWxKZWU3NXFHM0ZvdktNSW05MWpTOXZFb3h5akR6OVJlZGxJbEs5T2xFUTFtcUdXNHN1ZUdlNUZ2aVp6Vm9yZUQ4eVd5Y0YiLCJtYWMiOiI4Yjg3OThmMzNmN2UyZmMwYTM0MDQ4MTFkYWJiOWJiYTAyZDk4Y2NlZjk4N2NlZDdmMTM0MjJkNzVhMDRjNjY4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImhPSmw4T3JyWEJHTCtEazRKdmpFUnc9PSIsInZhbHVlIjoiTC9GczR2MDRBU1lLWGEybHNQTUdQM1MxV2FFTHNGYUdZSGFEbnNFMmVFU0tZTVlYTSs2ZEkrMzg1eEtkaThUMHEwM1hsbE9jK1F0TjN0U3dBQk9KUytqUG9ta3pHOEptNWhPTUZoenVWT1kyYW01QUZHT2Jyc1RReW16SEVxQUlHZHI1ZS8ycjhmcUlRYWVWY1lJcTF4S2VvUlYrZ0JQRk50bVFGY3pWbndZQXNPNWFpTUx1cWFoOTVTUTE1dGdVVTNhb3gydnNjd2VPMWVUdnBQZHBUbmdOVFJadnordUdkRDJUalQxK0RVWjFSUmg4K2VQMUpQbFVXWUt5ZktIRStydC93ZzFVbGhlV3BJK1JLNTUzamxjaDNGL2hpSWcrSVBtL2N3WmNBb1gyOTZSTUlsNVZUVVUrTko3K2czUlVMalBRRWJGc0toUGE1ZWJnZ0x6TVhZRDFRc0hEWXZIL01tczhKeHlvQjdiOFc4MGFWWUNVL0pGSlFjOGZhSzhlSnkxMkRmUVFXM0tVc2lwZXZlYzlEbzlLUHlvdmp0TmF0OHl1TTl1TTFWNFBFZ1diblU3MGRDMlZ0ZVMyQ1hrSFY4Z0kyK015ZTJwbituOHErbUJhTFM3ZnZwd2EzSHRPK2hjZDBjUnRCMlFncDdibSt0T05FRytKNzZ5R0gzT24iLCJtYWMiOiI5OWY5MGYzY2Q3ZThkMmQyMDExYWViNGI3ZWViOTE0Y2UzYzc4OThiMzk4YjYyOGYzY2JjZDBhZWQ0ZjMwNWQxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhkRXlrN3hPUit3TUtlRFhvYWZZSVE9PSIsInZhbHVlIjoiZHF1MjdoMytrcmlycUdOLzdBWEd6ZmFZY2NJbmNHOWdiU1hXQ3NGMzV0ZWRkdzNjY2YzWWFpcmU3Z3VKaE5TNXh2WFZ0TkFUNEhsK1dQNDI5N1dhQ2NIY1lBMWpLMnlMbnQ0bnM3N2tTQklxWUZLUmJZSUhJMHhBaDk4ZzBqZkdSKzNzUzJDaVF5S0V2Vld2VW1OajJPTk9jWmdzcTJab3hkelRjdlQwcE9iQVlTRkVTTE9HVWFlRnRqMmJWZllkS2hLeG1xUlRJSjZCVmtJZS9kc0hzcVcwTHQyM3Rxc1ZtSVJPOXlSeTQvV0tqVSsyODhHNnVsQTVQQWZKN21pZ3cwOE5wVjV1UjdVZTc1MzcvMVhVbzhJeUlaYUtnY3VpMmhqeUV2MS9VR2JpUVlhV0VtOVlMcmp4S1pGbU1PT24wUFJKS2xuaGdYTm1WMnVXdXdTVXBQZkhpNmNHQmRUaUtuV0t6Qmg5Ylhua0d4cU1KL1NkVDdHZFIyTTdmRHo5ZU9oOXkrb2pXTElsbkxETU83UnRKQlp6T2M3L3BJMFFuRXNnTlFqdDdvV3d4NEt5ZWhOUG9Pamp6UWxKZWU3NXFHM0ZvdktNSW05MWpTOXZFb3h5akR6OVJlZGxJbEs5T2xFUTFtcUdXNHN1ZUdlNUZ2aVp6Vm9yZUQ4eVd5Y0YiLCJtYWMiOiI4Yjg3OThmMzNmN2UyZmMwYTM0MDQ4MTFkYWJiOWJiYTAyZDk4Y2NlZjk4N2NlZDdmMTM0MjJkNzVhMDRjNjY4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImhPSmw4T3JyWEJHTCtEazRKdmpFUnc9PSIsInZhbHVlIjoiTC9GczR2MDRBU1lLWGEybHNQTUdQM1MxV2FFTHNGYUdZSGFEbnNFMmVFU0tZTVlYTSs2ZEkrMzg1eEtkaThUMHEwM1hsbE9jK1F0TjN0U3dBQk9KUytqUG9ta3pHOEptNWhPTUZoenVWT1kyYW01QUZHT2Jyc1RReW16SEVxQUlHZHI1ZS8ycjhmcUlRYWVWY1lJcTF4S2VvUlYrZ0JQRk50bVFGY3pWbndZQXNPNWFpTUx1cWFoOTVTUTE1dGdVVTNhb3gydnNjd2VPMWVUdnBQZHBUbmdOVFJadnordUdkRDJUalQxK0RVWjFSUmg4K2VQMUpQbFVXWUt5ZktIRStydC93ZzFVbGhlV3BJK1JLNTUzamxjaDNGL2hpSWcrSVBtL2N3WmNBb1gyOTZSTUlsNVZUVVUrTko3K2czUlVMalBRRWJGc0toUGE1ZWJnZ0x6TVhZRDFRc0hEWXZIL01tczhKeHlvQjdiOFc4MGFWWUNVL0pGSlFjOGZhSzhlSnkxMkRmUVFXM0tVc2lwZXZlYzlEbzlLUHlvdmp0TmF0OHl1TTl1TTFWNFBFZ1diblU3MGRDMlZ0ZVMyQ1hrSFY4Z0kyK015ZTJwbituOHErbUJhTFM3ZnZwd2EzSHRPK2hjZDBjUnRCMlFncDdibSt0T05FRytKNzZ5R0gzT24iLCJtYWMiOiI5OWY5MGYzY2Q3ZThkMmQyMDExYWViNGI3ZWViOTE0Y2UzYzc4OThiMzk4YjYyOGYzY2JjZDBhZWQ0ZjMwNWQxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756478269\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2068061443 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2068061443\", {\"maxDepth\":0})</script>\n"}}