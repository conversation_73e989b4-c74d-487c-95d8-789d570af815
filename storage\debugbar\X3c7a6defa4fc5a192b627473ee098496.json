{"__meta": {"id": "X3c7a6defa4fc5a192b627473ee098496", "datetime": "2025-06-28 16:02:38", "utime": **********.568535, "method": "GET", "uri": "/pos/1461/thermal/print", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 163, "messages": [{"message": "[16:02:38] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521477, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521666, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521788, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521888, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521991, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.522112, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.522218, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.522325, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.522436, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.522545, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.522646, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.522743, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.52286, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.522966, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.523053, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.523144, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.523244, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.523351, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.52345, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.52354, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.523635, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.523727, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.523845, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.523938, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.524027, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.524121, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.524219, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.524307, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.524398, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.524489, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.524703, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.524815, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.524922, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.525019, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.525119, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 80.60000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.525223, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 84.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.525337, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 84.20000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.525446, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 87.60000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.525634, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 90.20000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.525785, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 92.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.525915, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 95.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.526103, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 98.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.526231, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 98.60000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.526331, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 100.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.526437, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 102.20000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.526536, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 105.60000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.526634, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 107.00000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.526725, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 109.20000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.526821, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 110.60000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.526911, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 114.00000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.527002, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 115.40000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.527087, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 118.80000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.527182, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 119.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.527273, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 122.40000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.527373, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 123.8000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.527466, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 127.2000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.527561, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 129.8000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.527656, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 132.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.527754, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 134.60000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.527845, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 136.80000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.527942, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 138.20000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.528034, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 142.80000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.528129, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 143.00000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.528219, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 145.20000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.528313, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 149.00000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.5284, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 152.40000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.5285, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 152.60000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.528591, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 154.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.52869, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 155.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.5288, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 158.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.528908, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.529012, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 164.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.529119, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 167.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.529224, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 169.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.529336, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 169.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.529435, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 171.6 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.52954, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.53939, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.539586, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.539736, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.539875, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.540009, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.540118, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.540238, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.540469, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.540607, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.540744, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.540861, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.540998, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.541114, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.541224, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.541328, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.541442, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.541553, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.541655, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.541755, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.541868, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.541972, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.542064, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.542169, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.542274, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.542365, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.542477, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.542589, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.542691, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.542804, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.542928, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.543048, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.543168, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.543277, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.543381, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.54354, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.543704, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.543841, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.543968, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.544107, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.544223, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.544342, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.544462, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.544617, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.544793, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.544938, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.545074, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.545201, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.545322, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.545445, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.545569, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.545694, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.545812, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.546003, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.546154, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.546295, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.546439, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.546565, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.546688, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.546815, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.546932, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.547035, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.547131, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.547232, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.547343, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.547433, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.547534, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.547619, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.547724, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.547853, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.547962, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.548042, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.548124, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.548222, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.548314, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.548409, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.548491, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.548596, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.548698, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.548797, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.548897, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.548995, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.549078, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.549174, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.549292, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.549391, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:38] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.549487, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751126557.960695, "end": **********.568789, "duration": 0.6080939769744873, "duration_str": "608ms", "measures": [{"label": "Booting", "start": 1751126557.960695, "relative_start": 0, "end": **********.397001, "relative_end": **********.397001, "duration": 0.4363059997558594, "duration_str": "436ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.397012, "relative_start": 0.43631696701049805, "end": **********.568792, "relative_end": 3.0994415283203125e-06, "duration": 0.17178010940551758, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52448776, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.thermal_print_clean", "param_count": null, "params": [], "start": **********.515617, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/thermal_print_clean.blade.phppos.thermal_print_clean", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fthermal_print_clean.blade.php&line=1", "ajax": false, "filename": "thermal_print_clean.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.thermal_print_clean"}]}, "route": {"uri": "GET pos/{id}/thermal/print", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@thermalPrint", "namespace": null, "prefix": "", "where": [], "as": "pos.thermal.print", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1709\" onclick=\"\">app/Http/Controllers/PosController.php:1709-1768</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.009710000000000002, "accumulated_duration_str": "9.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4470181, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 24.511}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.460198, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 24.511, "width_percent": 6.282}, {"sql": "select * from `pos` where `pos`.`id` = '1461' limit 1", "type": "query", "params": [], "bindings": ["1461"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.464005, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 30.793, "width_percent": 7.003}, {"sql": "select * from `customers` where `customers`.`id` in (10)", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.470979, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 37.796, "width_percent": 8.136}, {"sql": "select * from `warehouses` where `warehouses`.`id` in (8)", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.474121, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 45.932, "width_percent": 7.415}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` in (1461)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.477424, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 53.347, "width_percent": 28.012}, {"sql": "select * from `product_services` where `product_services`.`id` in (0)", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.482914, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 81.359, "width_percent": 6.076}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1737}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.502585, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 87.436, "width_percent": 6.9}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 121}, {"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 21, "namespace": "view", "name": "pos.thermal_print_clean", "file": "C:\\laragon\\www\\erpq24\\resources\\views/pos/thermal_print_clean.blade.php", "line": 279}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.530292, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 94.336, "width_percent": 5.664}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\PosProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPosProduct.php&line=1", "ajax": false, "filename": "PosProduct.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1461/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos/1461/thermal/print", "status_code": "<pre class=sf-dump id=sf-dump-944531192 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-944531192\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2144153691 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2144153691\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1827336031 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1827336031\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1714467830 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126544321%7C12%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImwzK0dNNnhoNHAzTmtxbzhpWHdVZEE9PSIsInZhbHVlIjoiZ3FZUnpQS1pnRWx2NkJMZnBGS2JRY0w3YjF6RjBtVjB1Vnl5NGxYKzZrZ1dBUmNvbE5mN1g3QjBnRmI3SzR5aE5raVdRa3pCSEZvZU9MNmZKZDRRUzlqWUhLcjdNZ0ZjQUN2dXVYdWtJS29HNkp0dW1ubDV1UFNqdFp4UEY2NmJTQzFpVUo1aWZPK2hEdFNBZ3hEWEc2RVFSODgzL1RSVmE5L25tbmZDczM3ZisyTURJRithOVB3TExqLzg1ejYwWC9CekYwWVp1UkM2L1VlUGtybnlWb0FOREZPYmIrOFhmNGo1dnpZTHBSeVhDeVpLRGRHb2hTeWtlU09QdzBJV0hacDkweStaMEFFYmNWR0ZFVlVPM2ZaTVVOQUJzcUhsVk9yaGNHU1JsTUIzSXJLOUltMTdORGFsVEs1K2EzM0JDR3pYVTdOL2s1RzFSU2ZkTEdKa09vR0VxRjN5YVlyMzBIS1dkV08rME5iUjFsdndWMGltNmkxQk1USGtDbm4zaE1URGZmaXowVnhINC84MWUzaWZ5Y1BRRHJsNEFIMGk4dkFDQ0s1SFpwT3pIeVVDN0trWXE4ZG4vaHZvN2dsak5maUxqeW5uZnF1eVJ0Tm5icThBMUpnVGwrcU1idGNBWGdYRUh0bUx1bk5lbDlmQ09Bb3pKbWNJS04rVGRBeCsiLCJtYWMiOiJiMDI1MDE0OTYyMTc5ZmJlYzMwYTM2ZjIyYzk5MzZiNTIwODVlZjQ4M2E4ZmJkYjJkNjA2OGY1YWQ1YmU2NjUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkNGWkhnWGNGYW1udVpncVNIVXlpb0E9PSIsInZhbHVlIjoiYXQ0azQzMTh1b3lRV0Nob0hNZGprVm5GKzArbzRwM0hqTUplZ09Zd2J1cGNwZHlCQWZWNkd6N2FLa3dleXFxeUVkQjk0QWRxTGRFeThBWldmNHN5cHRsQ2VXZ3dJY1BrWDVBRmRrd0EyQ2wrNHVmeW1hMk5BQ2VzeEFmSys4bVNOVWRFbStkWmJkYXI5RFpEMTdSZnRZYXRrSzExUFZjWkcra0Y1UkRDc0o3VTNxZkdpQ0VsSm5KNkpIaXErTlRreGJwcmtXdnczT0pQZlV0elhmY1E0Qy9MUGRjdno2WU4vWjlvVzkwR3BjcWF3TUVFVkhKK01QUVJoU1Q4cGxlbFl3bmxFTW9NYzE5WFp5ditwWCsweXdQTi9qenhHaSs1REU3U1JYdDJnenVKbVRvK2VORWtLMlUwN1Q3Mi8vMVJyUDBCQmVqSWRWWlNrc3NJWmpnVjcvYTBKRFR6Uk1uQWZxUWEwUEgvSHBPNTVKNnNMaU51TmF6ZmszMUp1OTdqL0dOUWk5N2RtVzAxRFd1YkY4amNMSktBRExYZUM5MzJUUHVWdHp6b2VZRGIyVVdZUFpBVE5JYVNQc1A2ZnllTURhNGpBZ0k3dXpDSHhTSjJjS3A4WEhWUHZSRmpFWithcUU1Njh3dFlVVXRnSlBWcXZMMkxPRnloOENOWGNYQzgiLCJtYWMiOiJlOWYyNzZlNjQ1NjBmMjYxYTcyNjI5MWI5NDJkMzBiMGViOGE2ODg2ZDE2NDJkZDdhOGUyOTk2YmRmZjcyMjQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1714467830\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1975628727 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1975628727\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-865275987 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitxbCtuYjNKQ3ppbklhZnIyOGRCVnc9PSIsInZhbHVlIjoiU3luV0dpOW1kWE1BK0syRlJlT0RxYVlYUFZXaFN6cnllRUJMNEU5QTFCbWhCSmlkT0xwN3JSdXlTUndMM1RnZ2JOYmx5M0JJNzlXYm9ldkZnY3d3ZkRNMWtscjcvalB2YXIzTTN5c3RpRFdBRVBhMkJHQTN0MWljZVNZQWxyN0Z4ZGJyNEFvbUNDZXhwdk0xOVk5UlMrL3JWTTlLcDV0dTNYV05vYi9pOUR5bk5Ra0twSXpyYklhWFhGOFVVMU5pSktBY1MweE1sdENnY0ZrSjdTbHVrb01FZVlzcGx3eTUxWjJNaVRwL2RRRHV0QVp4cDhvNnZUTUh0QTAwT094YU9NeURyZXRaZ1JxQ0ppK2pReG5CUTJVeWVjMkZLYjNnRWtCTlFtVHVvSGxHd2JUZkoreGRPc001M09aUm8rUmhMTW4vaXhrZVJLYXoxOGF4d0xwZU1aVXBoZ1hYS1JCTFUzaHNNcW1tS0hvckhiMFJLZEpidk1iT29ZUmI5Zkt2WmdlVTZBRVRXRUNPOUhZTmQ0cmZLejdUajdheEJGOWVUOEVHRmlrUG1pRFNyeGNqVnpiZ2M0aC9hZzM0K1JpWHFkbEkvNWp5cUlyVHZHVXNYbU53SUxSMVlac0pXbk9rVm53bnNiNUNQM0NkVDhDYTduanYzU3NnSkp1MEdsSE8iLCJtYWMiOiIxZmMxNmMxN2U2MDI1NmU4NTY2NGZkMDQ4MTg5YjFiNjJiZGU1YzNjYjgxOWZhNzEzY2U2ZTI3YjA3MTdjMzZhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJ2Z3ZHZWExMGEzd3VCaWJHQVN5M3c9PSIsInZhbHVlIjoiM09Xak5EcW8yT041LzRMZ21reE9kSWEyS2lMTDNidWJINnZ1TUc5T3BlV0tza0JlY0hoRUt6OStLUUtmQ1JiYmdMcmYwMndvbk5KeGthTEJ4UTRBK25PQm54V3ZScmZMVmt2b3VwU2FLYVlhNU1OMUIvajBFOWVUSnZ4QzBFNlFTWnJ5NXdXSzdHVW80dXRTMTNPME9kMHhvaUkxUFFSREh5WlZ4LzhlL1VmZjVRZmVZSk10MnBlOTRyN2UwUHhYTU5wNExaVHgrajdHbmJWUzBOcy81dXpQQVA2OHh2WndnVmpHQ05kMzZIc21rL1o1U1EwVjVWWklqWm5Pai96dHQ3eFpzU1Y5cEFkc25oQk15U2RFdUlHc3F2MkZjQTJCeDc5OHJZQWZhZWdoVzRQNTRuaENjZUtRYXpIZXBoaEJ5dDd5a3FJb0FaR0lqeXRFelVuek1CdXUzTldkTFlkWFVQUXplVVlPUVozSmYxZFN4OGZCNCs4a3B4aHo0eU5BaU1UNDB5eFZCWDdXK0lZRjk5a1A1V2RtWkhETmRHZGtGS0VORms0MlJjY2hlOVpyRWxHQVkyTmxJQ1FBd29UaWRlY1ZJQkJOQ3Y2OExKZU5iMEJkUVVkcnViczY2dnJJWkZsZmhxWmVoTnJaTFdCeEdlbHVoWHZtMXVzcXhLeFMiLCJtYWMiOiJhNWI5MDI2Mzg0OTk5Mjk0OTIxYzk0MjgxNDg2ZmIzYWQ2MWJhN2NhNWM3OTcxMGQ2YzZhMTNjZGJkMmM0M2ZiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitxbCtuYjNKQ3ppbklhZnIyOGRCVnc9PSIsInZhbHVlIjoiU3luV0dpOW1kWE1BK0syRlJlT0RxYVlYUFZXaFN6cnllRUJMNEU5QTFCbWhCSmlkT0xwN3JSdXlTUndMM1RnZ2JOYmx5M0JJNzlXYm9ldkZnY3d3ZkRNMWtscjcvalB2YXIzTTN5c3RpRFdBRVBhMkJHQTN0MWljZVNZQWxyN0Z4ZGJyNEFvbUNDZXhwdk0xOVk5UlMrL3JWTTlLcDV0dTNYV05vYi9pOUR5bk5Ra0twSXpyYklhWFhGOFVVMU5pSktBY1MweE1sdENnY0ZrSjdTbHVrb01FZVlzcGx3eTUxWjJNaVRwL2RRRHV0QVp4cDhvNnZUTUh0QTAwT094YU9NeURyZXRaZ1JxQ0ppK2pReG5CUTJVeWVjMkZLYjNnRWtCTlFtVHVvSGxHd2JUZkoreGRPc001M09aUm8rUmhMTW4vaXhrZVJLYXoxOGF4d0xwZU1aVXBoZ1hYS1JCTFUzaHNNcW1tS0hvckhiMFJLZEpidk1iT29ZUmI5Zkt2WmdlVTZBRVRXRUNPOUhZTmQ0cmZLejdUajdheEJGOWVUOEVHRmlrUG1pRFNyeGNqVnpiZ2M0aC9hZzM0K1JpWHFkbEkvNWp5cUlyVHZHVXNYbU53SUxSMVlac0pXbk9rVm53bnNiNUNQM0NkVDhDYTduanYzU3NnSkp1MEdsSE8iLCJtYWMiOiIxZmMxNmMxN2U2MDI1NmU4NTY2NGZkMDQ4MTg5YjFiNjJiZGU1YzNjYjgxOWZhNzEzY2U2ZTI3YjA3MTdjMzZhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJ2Z3ZHZWExMGEzd3VCaWJHQVN5M3c9PSIsInZhbHVlIjoiM09Xak5EcW8yT041LzRMZ21reE9kSWEyS2lMTDNidWJINnZ1TUc5T3BlV0tza0JlY0hoRUt6OStLUUtmQ1JiYmdMcmYwMndvbk5KeGthTEJ4UTRBK25PQm54V3ZScmZMVmt2b3VwU2FLYVlhNU1OMUIvajBFOWVUSnZ4QzBFNlFTWnJ5NXdXSzdHVW80dXRTMTNPME9kMHhvaUkxUFFSREh5WlZ4LzhlL1VmZjVRZmVZSk10MnBlOTRyN2UwUHhYTU5wNExaVHgrajdHbmJWUzBOcy81dXpQQVA2OHh2WndnVmpHQ05kMzZIc21rL1o1U1EwVjVWWklqWm5Pai96dHQ3eFpzU1Y5cEFkc25oQk15U2RFdUlHc3F2MkZjQTJCeDc5OHJZQWZhZWdoVzRQNTRuaENjZUtRYXpIZXBoaEJ5dDd5a3FJb0FaR0lqeXRFelVuek1CdXUzTldkTFlkWFVQUXplVVlPUVozSmYxZFN4OGZCNCs4a3B4aHo0eU5BaU1UNDB5eFZCWDdXK0lZRjk5a1A1V2RtWkhETmRHZGtGS0VORms0MlJjY2hlOVpyRWxHQVkyTmxJQ1FBd29UaWRlY1ZJQkJOQ3Y2OExKZU5iMEJkUVVkcnViczY2dnJJWkZsZmhxWmVoTnJaTFdCeEdlbHVoWHZtMXVzcXhLeFMiLCJtYWMiOiJhNWI5MDI2Mzg0OTk5Mjk0OTIxYzk0MjgxNDg2ZmIzYWQ2MWJhN2NhNWM3OTcxMGQ2YzZhMTNjZGJkMmM0M2ZiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865275987\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-55241748 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1461/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55241748\", {\"maxDepth\":0})</script>\n"}}