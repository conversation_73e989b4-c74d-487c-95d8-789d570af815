{"__meta": {"id": "Xfaf1e312936d23403ff07b949ca03670", "datetime": "2025-06-28 16:03:36", "utime": **********.065644, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126615.630446, "end": **********.065669, "duration": 0.4352231025695801, "duration_str": "435ms", "measures": [{"label": "Booting", "start": 1751126615.630446, "relative_start": 0, "end": **********.012028, "relative_end": **********.012028, "duration": 0.38158202171325684, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.012036, "relative_start": 0.3815901279449463, "end": **********.065672, "relative_end": 2.86102294921875e-06, "duration": 0.05363583564758301, "duration_str": "53.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45699752, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00253, "accumulated_duration_str": "2.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.041507, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.427}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.051445, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.427, "width_percent": 14.625}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.056785, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.051, "width_percent": 20.949}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1919460120 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1919460120\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-602479277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-602479277\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1673985289 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673985289\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126613193%7C18%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNBblRWN3RSalVBaEgrV0RWbHROU2c9PSIsInZhbHVlIjoiTzAzaTJreXZNNkZjVmdzL2k1Ykl0RVNjSmlNL2FSMGUreUhMY3NFRjh2SVBoNHFLVFFsVVhmaXpiM0R0UWxWODJQV1gvNE1YaG41aUVjMVlrd0Nvc2FrZHhVRzNnSStwbXNvYmhIQ0tGUUdESjZRQXg2M2J0Q05LN1RiWktmU0YveitEK2VkZWhBN0kwNzVtQzZlZGVlTlM3MEpOYXpua2xEUGVKWEFkaURoaVBva2xLRllNMWNXTnRtNWxSMjJTYUxJMzlpMTgzcW01NXRacjU3ODM4ajhtaGYzUGhKOUxhV3JTM1Voc2RoSzk4ZTBJRGlsMjM2SmpDZFhtVWtoUy9rYVBEQmhldkNzbnhzeEdMTjVuUWJKdDl5OU93Lzd2V1B2RDdLV3BtMUl0UVNyRVUxemViWmJWQ3NRRnNocmMzVGRSN0VRTUZRZXVEN0dKSlpTVUkveDVhSC83cXdOZU5RRlNFS1VWdDNCNU0zdVVyZlhiR29ZVjFGNkZJR3RhOGFpck4rTlkveE1WamN2VUhBVjdQdUMvWVJhRXJzVVIvVlVrYm1zRHRFMEV5bEwzc3pVUjByRm1jeDd0V2Rpb2lhKzN1ZVFveGtRbXc2ZmpVOFMxaXd0S1RxT1dFK1JuWVE4UjUyRTBPamhOaE4vM3lFVzduWThMOUF0eGM3TVIiLCJtYWMiOiJkOWNiMzA5NjhjYmQyZTliYTZlZTg5ZjZhZDFjZDE3MTE0YzMyNjc5ZmVhNjE2ZWI2ZGU1NzNmMjFlNzdmMjFmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpvNUlKSUhXdG5YdUJRZ2pYcGFzdnc9PSIsInZhbHVlIjoiN2ErYzdua3ZOWkpzSGlyd1lQKyt3RFZhbmZsTDRUVng4Z0tUeENuOWJzbUx5SlpvaEJTbm9xN1N6ZkFtT2pSMUFjNlp5VW0zdGIyVUNrZW55SnExNjl3N2dyU1EzOXZMcHZkb3RUZFRJbytkSUhROVg1bmRYQ1hpVjlLRjJVeDVRTkZPZDk5bzRCSExHRVVyWEVsbktWbDlvcnZYcm5uYzYzcVdDUVhPaXdsL3FkQkRpdXZFSU1UOHptT3lKemF0aVEyVEhtRW9YZmZoRm04bmN6dFpXak0zTmVramlZWjdMNUg4Rk4rZCtjcDcvUklBR0VlN2JkN2hTdkNNTHdJZDFBUWhySXJrY3F5Nk9JU2R1TnRsQTAybWVJV1BiVWhBbXdRRkltMVN4V2taSEVSWU5jM3ZranhSOTJ1NkdReUhvMDV5U21xQ05WUkQyaU83bkZ4WkZpRGI0QlRPbHhpRFpMaEs3VVVocXBhQ09tNXplYXVwSlpPU0Y3QTNmNytQbFpEUEh2aE5DTDdTbGZRWmlMQk9Ja3lwR3g2WXAzWndha0N2VnExVmFEU2t5a3dsdHA2YWZSSHRyVlc1eFcyV1hpdTZaMGVZV1diY0JtWnFvSERFNG5teW5FN1Q4M3RDank5alcxSEZFcUVtaW1YUHNKQkRXOUZzeGtUb1UzQkMiLCJtYWMiOiJhOGU3Yzk5YTBiZmQ3ODI3NDkzZGYxN2E3MzlkMjkzYzAyMzVkYTUzNGZjOWZhMzQxYzEyZDc5ZDJlODVjN2UzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2010634280 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRHcmV5ZWMrYXZCL1hWTXFDOG1IRkE9PSIsInZhbHVlIjoiU3AvczNSOXRyNElwMkRGWElrTXF0anlQc3JjK1E3Q3hZa0tDZ203L3JlenYvbkdVOVRTdENFdThjaHNzTmc5dVBoQ1ZQQWJCNnI3QnN0SWVCcTBhOUdKU1JmRVlHSldTT0lLVU92OVNTenppM3VHWDJ0MWIway9oaFBwd3F0Ym9aWElTWjRyUGxseThobEs4TmN5Tkl6Z3p3Tk5QVkxaUERJdlpCQnZidGtwYllXRndkVTY2K29hTS9NZ3pTeHpQMEtJOHNHUTlYdjd2c25qenVlUjhsK3VQNVFwR2Z1WWNNdWYxZ3JETDIwdEx5Qm4wejBDaVBIS2hUL05oL282TTU5Y0czQ2R4R2ZRWWlUeitvZTJMRkJrbFpPTDl0cElNUnd3c1c5Qm1BQmdVTW8vZ01FUEdPK29OeDcyWlFubnZxdGRBcSsyOXNURGdHZ0c1L0FOalhjSU5jbE42SG1sR1dZRDh5Y28zaDRoeXgwbTBSUndHWlBrcTVUcW5kY3FHQ1ZYemRPTlNBWHNSRU0rZHZzdVlmNFVqZllxWDNSbWt0aEhoeFhXcXhwKzI4dXhMZ2dnU25COEplVFVrSVM3ZHh0Z0JRUGEya25LNGl5VUFtMEdtV2c5emdXZkkvREJPeDJhaitHeFdIenZwNUdzMGF1U1AzYUJiVGJ2Z2N2aGIiLCJtYWMiOiIwNzJiNjA0NzAwOTM1MmRhMTU1YjFhN2Y2ZTAzNTI2ZjQ3NTYyNWU5YTUwYjQyZmMxNjI2ZDc2OWI5YWFiMjlkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlMxZlQwcm9wU1l4Smo3eXBkRnM3R3c9PSIsInZhbHVlIjoiR1B1YlV1clNxWWZJckx4a0FsRFE1TUUrdElGUEJ5TUVvMi9WVlg3a1F5VHVBOC94N3Z0STlBQ0ZJYURkRG83S2gwOW5SRUxsdTN2UHIxYS84Rm56NE8wOWVPSkthUVFUaVpkWGMzaWcva3ZVT243Um96ZUxPcVl0bkdPNFgvV2h1NEVFWFBuc1VEc1FuT2pXd1RhMDRjTUt1b1NlSG04bFdtS3R2QVk5VnRNckEyZUt6T2FyaEcrMHRWbXY4QmNway9FZ3dWZU96d05Wb2F6diszOXhUbi85U1ZkOURmOEJtK2RIWnJBSmhJMkRsbmtHcUlJdGJRVTZZcnpFTGdqUi9EdTY1emJKQmdaODNsZXZNOXdnWmZpR1dQdFAwZkZBR0NLWjVtZVBYdzFKWDZxai8yMXJCMDRyZ1dXd2pJd1ZESjIwY0VyNDVmMzNQQVdFNEF3MHpDZkZRd0pZaW1tK05XeHJyNVUzZnlqRy9VSmZycks2emtMeFphcVJyRU45eDFzbHFacFFrT3VrVUE5UUVvQXJpVlRFRzBMNUJVV3BURElHQXNtS203ZlBlY3pFdEc1OVhXL0RIMlVPUSsvbkg3S2c0TGhLd09jeFZwUUF6a1lFUHY1K1E3UGdBQ0tXdVV2MzI3aytFYVYzZ3hKdytSZXlYQ2RrbTRKNmhZSmciLCJtYWMiOiI1ZDZlZjdmNDQ3ODJjODY0N2Q0ZTJkODhiZTJiNzA2MmM1NDZkZTI5NTI2N2I5M2YxZTcxOWIyY2QxYzFmNDI2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRHcmV5ZWMrYXZCL1hWTXFDOG1IRkE9PSIsInZhbHVlIjoiU3AvczNSOXRyNElwMkRGWElrTXF0anlQc3JjK1E3Q3hZa0tDZ203L3JlenYvbkdVOVRTdENFdThjaHNzTmc5dVBoQ1ZQQWJCNnI3QnN0SWVCcTBhOUdKU1JmRVlHSldTT0lLVU92OVNTenppM3VHWDJ0MWIway9oaFBwd3F0Ym9aWElTWjRyUGxseThobEs4TmN5Tkl6Z3p3Tk5QVkxaUERJdlpCQnZidGtwYllXRndkVTY2K29hTS9NZ3pTeHpQMEtJOHNHUTlYdjd2c25qenVlUjhsK3VQNVFwR2Z1WWNNdWYxZ3JETDIwdEx5Qm4wejBDaVBIS2hUL05oL282TTU5Y0czQ2R4R2ZRWWlUeitvZTJMRkJrbFpPTDl0cElNUnd3c1c5Qm1BQmdVTW8vZ01FUEdPK29OeDcyWlFubnZxdGRBcSsyOXNURGdHZ0c1L0FOalhjSU5jbE42SG1sR1dZRDh5Y28zaDRoeXgwbTBSUndHWlBrcTVUcW5kY3FHQ1ZYemRPTlNBWHNSRU0rZHZzdVlmNFVqZllxWDNSbWt0aEhoeFhXcXhwKzI4dXhMZ2dnU25COEplVFVrSVM3ZHh0Z0JRUGEya25LNGl5VUFtMEdtV2c5emdXZkkvREJPeDJhaitHeFdIenZwNUdzMGF1U1AzYUJiVGJ2Z2N2aGIiLCJtYWMiOiIwNzJiNjA0NzAwOTM1MmRhMTU1YjFhN2Y2ZTAzNTI2ZjQ3NTYyNWU5YTUwYjQyZmMxNjI2ZDc2OWI5YWFiMjlkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlMxZlQwcm9wU1l4Smo3eXBkRnM3R3c9PSIsInZhbHVlIjoiR1B1YlV1clNxWWZJckx4a0FsRFE1TUUrdElGUEJ5TUVvMi9WVlg3a1F5VHVBOC94N3Z0STlBQ0ZJYURkRG83S2gwOW5SRUxsdTN2UHIxYS84Rm56NE8wOWVPSkthUVFUaVpkWGMzaWcva3ZVT243Um96ZUxPcVl0bkdPNFgvV2h1NEVFWFBuc1VEc1FuT2pXd1RhMDRjTUt1b1NlSG04bFdtS3R2QVk5VnRNckEyZUt6T2FyaEcrMHRWbXY4QmNway9FZ3dWZU96d05Wb2F6diszOXhUbi85U1ZkOURmOEJtK2RIWnJBSmhJMkRsbmtHcUlJdGJRVTZZcnpFTGdqUi9EdTY1emJKQmdaODNsZXZNOXdnWmZpR1dQdFAwZkZBR0NLWjVtZVBYdzFKWDZxai8yMXJCMDRyZ1dXd2pJd1ZESjIwY0VyNDVmMzNQQVdFNEF3MHpDZkZRd0pZaW1tK05XeHJyNVUzZnlqRy9VSmZycks2emtMeFphcVJyRU45eDFzbHFacFFrT3VrVUE5UUVvQXJpVlRFRzBMNUJVV3BURElHQXNtS203ZlBlY3pFdEc1OVhXL0RIMlVPUSsvbkg3S2c0TGhLd09jeFZwUUF6a1lFUHY1K1E3UGdBQ0tXdVV2MzI3aytFYVYzZ3hKdytSZXlYQ2RrbTRKNmhZSmciLCJtYWMiOiI1ZDZlZjdmNDQ3ODJjODY0N2Q0ZTJkODhiZTJiNzA2MmM1NDZkZTI5NTI2N2I5M2YxZTcxOWIyY2QxYzFmNDI2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010634280\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-975414064 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-975414064\", {\"maxDepth\":0})</script>\n"}}