{"__meta": {"id": "X95fe28f9f9f3ebd19b70608b52457192", "datetime": "2025-06-28 15:28:00", "utime": **********.368311, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751124479.926484, "end": **********.368324, "duration": 0.44183993339538574, "duration_str": "442ms", "measures": [{"label": "Booting", "start": 1751124479.926484, "relative_start": 0, "end": **********.312173, "relative_end": **********.312173, "duration": 0.38568878173828125, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.312182, "relative_start": 0.3856978416442871, "end": **********.368326, "relative_end": 1.9073486328125e-06, "duration": 0.056143999099731445, "duration_str": "56.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46307392, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2335\" onclick=\"\">app/Http/Controllers/PosController.php:2335-2369</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00229, "accumulated_duration_str": "2.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.350898, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.039}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.361653, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.039, "width_percent": 20.961}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1286164237 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1286164237\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1909577422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1909577422\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1867953677 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1867953677\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-331408532 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1881 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; XSRF-TOKEN=eyJpdiI6IkxDaURONkhWK2RaUVNlcDh2eWRIV1E9PSIsInZhbHVlIjoiWlliMm9XUjMycDlSWlpmeG9XQ3dqdmo1UXNHRmtjUEZKc2ZJejlZNHlnMTZHRnA4aGU4YWo0ZjJHMDdJRnZ0QmRvMGZweVRPTlZrR3dvS2cveXQzNFgvZ0tJZ1M5ZkNXV3piMlJKaWRHYzZvZ0hLVnNaNmxhVDFYa091d0lYazdMRnB6T0lRUHk2eXZlSzBvb0tseXI4Y05BWlk3M1oyWEprMWRDT1BJNWEzbFllY25ya01EbEFOYi83b0tSY2FibUxJU1VpRFlwM3Vqc3hmTExoaldYdEVHVURlUTlvM3BvNlJpOVZoTE8yTmhOc05xQ0k1RFhtTkI3Q0ljdkQrWWdYQS9hUGd2UEg3QXh4dkpQd2s2ZWczQVFjZVVESHVrcnpENm4rNm1QSm1GMHFkTHVaMGJORUlhUlhXZnF2Q1A0MUszeUNEQlhnQy9Fa2NGbk56L2xWcDdWSHpOekRHTjlzMlMwbjkrWWV5ZmZTbVQzc1lwREN3T3dmV3RibUN4aXMvczdoL0xiVHNpNWhhUGpFSVFaRjBVcERXakcvbm9FT080eDQxRDkyUEJ4dUU0UEVKdkJlQzFCb0EvSVovNnRURlhscmpqYlp1TUNPbUtXOVpFUGkvUG1MSzIrb1FDb2prTUo1TVN4anVqOXpOVTBwYlBTMlJ1S0lHemQxY1IiLCJtYWMiOiIyNDQwNzBjMWIyZmIyMTJlZjE0ZGEwNTNmMzI0YWUyNDk2ZTYxN2Q5ODRhZjc4NmMzMjYzYjdhNGFiYjUwMGIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBjRGlqZ1dESjAxTUc1STByZXVJUXc9PSIsInZhbHVlIjoiamJNelhqUDJPVTBNSFkwbnlWMS9tN0tsQ1NqL0FnSHZPMG94TDBUZUtGVUcrVXQ5OHV4UWhuM2NmY01WcXlaU3NHbmdOcm5BclpDMVNzNXArbXIzZXlremdUd3ZtYWF4MzNDbVZWWldrUTdOaGRmKzRuNC9lMjBJb1A2YUU2UmhRbVdNNFdObk41SzBwcDBQTW4yVU5MM3RDWTRyL0lkUCt3UnFtMWhWLzFWOVhFRC9OU2R0WnVwSkVmU1lJRWM1cXhvYTFRdVA3QXhodERoMEJkaDJ5NjBVaGVvOTJFM0RoVFNzTE1iQ0ZXR3JOc3dkR1p5UVh6b09iQkIzZC93ZS9CSEdPeWh3bzd2RjJrNFRlOUowU2pyMFBYbWNtaHhSSnlZdmtTbXN1SW9wZCt1MGtYdmJYZnB1ck12L1pwZjFia0xNWlByNjJBdXpYRk9rRUZ4MlhHWmRFRVdwUy8zMGRmTG94M0NLYzhPZ0VFVW12N0M3b0hFNmhtRklyOXZ5UmRXVklVVWl2b0dGSUhqNVV6TkFSbUdYbENrbGw2cXY5ZFRZZ2N2T0htZlV0TWxnbkFicm4xdzQyeDlpQnk4TzVUZ2RERDZTSHpSRW82L3VHMnhlODNyU0I5UVlSN1pXYndpay9iZTI3d21aQytGOGFjUEFtR1ZzZWUyZFJFcWMiLCJtYWMiOiJkMjgwNzEzMjJlZGVlZWVmYTM3OTZiNmVhNDM2ZWNiZDYwZTY5NTliYzE4Njk1MDZlMjk3Y2ViNDYzNTE3MTJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331408532\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1439372644 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439372644\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-19272104 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:28:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndWUjdoZkFMZ0hzUjBhdE5jOWxPdnc9PSIsInZhbHVlIjoiZ295QWtTSzlKdmF5RUpKc0VuL25KOWo5YlZCendhNGpHV2pra0JwVi95OWVuU0dzVi9POUdKY2hkN0Z0d3lNYk1ISmRCQ2VqQnkwU05SYStpTGorUHhpd0F4cUdvM2FOR3UxYys4WGlobFEwcG85RGZudWxiUE1COFZPNXVCRnlDWWJCL0NYelBPbGFpN1FaNWZocXR0OVZwb1RvdHUrdU12MlBZVjRZNmNySEJFcjcyY0g3akZpUGRpSHJEZjdET2hHanJnT0o5eTNkYXNQOWVsdlh0N3ZuYTVkb25PTVJOTUYzSUc3akNaMzgzZ2JGVFZKN0tvSzlncnhid1phaDQ4M0ttYXAycCsyWG90SWQwMmEwZ05Ed3Iwb1k3RW1vYTI5Z1lWWXdzMlpRZnJTREh5bWwzNVNTR0Y5NmdqN2l1RGY3MHNHb25Ga2gvZnJYRUl5eVdSS1Vmb1FZbDJMY3BWbnV4c2c2dndYNEFLeE9zVUVqQmxqN2FndDFHV21HM2ErKzAycDhJZ1VUYUhLNEhrRngxajRZS0t1SFUrUG1rZW1TS0tjT2VXaWtQcWJBcXdid3JxUk0wNXZyQ3JDclA0cXFYU3d5dGkwK1FkdUlTVjI4KzNJclo3VTZNcFM0R2l0eTRDcS96eWxmVU84cGdRVGZCWG9zR1FkckxXeFMiLCJtYWMiOiIzYzYyMDQxY2E2MTdiNmViYzk1MTA0MDg1YTJkM2UxMDc1YTBmNDkzZjU0MDMzNTJmNzRjOWFlODA3YWE3MGIyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:28:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZXY25wZnBtZVNLQUYrcUl1NEZqS3c9PSIsInZhbHVlIjoiV1JtYjZOblVIdUhOSUtWL1BJL2VCS0lySCtKWE9WU0RGU3ZTT0xIWk1PVHEyOUFNRExPY2ZtbVN0Z0JqNmd6cUZkS2J6OGhPQzZQYSsyT01GNjlzWWRvV1l4NndvMGE2K05NamVFMjNQQlRpeVErdHQ2dFZYVXloNGU2dFN3ZlJ6ODZIdUFJT3ZoY3VuVTBWclVyL21SR3Z6bGRGYW9EZVAvRzFQeDVlOEk1VmdmMGFIZGh0QkxsR2pXTGF0VlBmMTgzQWI5T05kMStTZk0wUkRPWHpXOTdNMGE3VkpMQXc0L1o5MGhTVUtqWjJFNDR1K3BOeWhzR3FWN2RVYWEyV1FLMWxWVzJCUk1hSTRQNmU5YWl0anNMbjlvWTlGQlR0TEFSMlFoMlZwNXN0bEZOTWd1ZzNNQmZjTXJOWkxwOXlrZ2tuNzQ2QjMrVDhTekJWSkxVV1UyT0k0cWxEQWFTQTdmY3JDUURVeWlRblREUEpiRWxyaXBMaVVqMGFoYm9CT1NYa1BOblgwaUVWN3ZvKzhlSGZQV0ttMUxES3RMSnBkNUxuVnhOVzFHTkFVa0p5NnU3UDBCL2cvU21EQTI0clBQTFNUNjVNUHBJcWFtK2I0dUs2aHY5bGphaGI3cXc5R1NFVUw3bmtQWGxPblJzcG5OSExJRXBNZ1RtUEZPTTUiLCJtYWMiOiJhZDlhZmU4YTk0NTEwZjY5YzRmNzlhNTcxMDI4Y2M5NThjNTdkZTFiZjZlN2IyNzU2NWYyM2IzMTQ2ZDdjMTgxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:28:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndWUjdoZkFMZ0hzUjBhdE5jOWxPdnc9PSIsInZhbHVlIjoiZ295QWtTSzlKdmF5RUpKc0VuL25KOWo5YlZCendhNGpHV2pra0JwVi95OWVuU0dzVi9POUdKY2hkN0Z0d3lNYk1ISmRCQ2VqQnkwU05SYStpTGorUHhpd0F4cUdvM2FOR3UxYys4WGlobFEwcG85RGZudWxiUE1COFZPNXVCRnlDWWJCL0NYelBPbGFpN1FaNWZocXR0OVZwb1RvdHUrdU12MlBZVjRZNmNySEJFcjcyY0g3akZpUGRpSHJEZjdET2hHanJnT0o5eTNkYXNQOWVsdlh0N3ZuYTVkb25PTVJOTUYzSUc3akNaMzgzZ2JGVFZKN0tvSzlncnhid1phaDQ4M0ttYXAycCsyWG90SWQwMmEwZ05Ed3Iwb1k3RW1vYTI5Z1lWWXdzMlpRZnJTREh5bWwzNVNTR0Y5NmdqN2l1RGY3MHNHb25Ga2gvZnJYRUl5eVdSS1Vmb1FZbDJMY3BWbnV4c2c2dndYNEFLeE9zVUVqQmxqN2FndDFHV21HM2ErKzAycDhJZ1VUYUhLNEhrRngxajRZS0t1SFUrUG1rZW1TS0tjT2VXaWtQcWJBcXdid3JxUk0wNXZyQ3JDclA0cXFYU3d5dGkwK1FkdUlTVjI4KzNJclo3VTZNcFM0R2l0eTRDcS96eWxmVU84cGdRVGZCWG9zR1FkckxXeFMiLCJtYWMiOiIzYzYyMDQxY2E2MTdiNmViYzk1MTA0MDg1YTJkM2UxMDc1YTBmNDkzZjU0MDMzNTJmNzRjOWFlODA3YWE3MGIyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:28:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZXY25wZnBtZVNLQUYrcUl1NEZqS3c9PSIsInZhbHVlIjoiV1JtYjZOblVIdUhOSUtWL1BJL2VCS0lySCtKWE9WU0RGU3ZTT0xIWk1PVHEyOUFNRExPY2ZtbVN0Z0JqNmd6cUZkS2J6OGhPQzZQYSsyT01GNjlzWWRvV1l4NndvMGE2K05NamVFMjNQQlRpeVErdHQ2dFZYVXloNGU2dFN3ZlJ6ODZIdUFJT3ZoY3VuVTBWclVyL21SR3Z6bGRGYW9EZVAvRzFQeDVlOEk1VmdmMGFIZGh0QkxsR2pXTGF0VlBmMTgzQWI5T05kMStTZk0wUkRPWHpXOTdNMGE3VkpMQXc0L1o5MGhTVUtqWjJFNDR1K3BOeWhzR3FWN2RVYWEyV1FLMWxWVzJCUk1hSTRQNmU5YWl0anNMbjlvWTlGQlR0TEFSMlFoMlZwNXN0bEZOTWd1ZzNNQmZjTXJOWkxwOXlrZ2tuNzQ2QjMrVDhTekJWSkxVV1UyT0k0cWxEQWFTQTdmY3JDUURVeWlRblREUEpiRWxyaXBMaVVqMGFoYm9CT1NYa1BOblgwaUVWN3ZvKzhlSGZQV0ttMUxES3RMSnBkNUxuVnhOVzFHTkFVa0p5NnU3UDBCL2cvU21EQTI0clBQTFNUNjVNUHBJcWFtK2I0dUs2aHY5bGphaGI3cXc5R1NFVUw3bmtQWGxPblJzcG5OSExJRXBNZ1RtUEZPTTUiLCJtYWMiOiJhZDlhZmU4YTk0NTEwZjY5YzRmNzlhNTcxMDI4Y2M5NThjNTdkZTFiZjZlN2IyNzU2NWYyM2IzMTQ2ZDdjMTgxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:28:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-19272104\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1950159465 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950159465\", {\"maxDepth\":0})</script>\n"}}