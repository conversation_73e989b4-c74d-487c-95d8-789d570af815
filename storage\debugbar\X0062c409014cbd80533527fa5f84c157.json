{"__meta": {"id": "X0062c409014cbd80533527fa5f84c157", "datetime": "2025-06-28 15:49:31", "utime": **********.488984, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125770.954479, "end": **********.488999, "duration": 0.5345199108123779, "duration_str": "535ms", "measures": [{"label": "Booting", "start": 1751125770.954479, "relative_start": 0, "end": **********.420218, "relative_end": **********.420218, "duration": 0.46573901176452637, "duration_str": "466ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.42023, "relative_start": 0.46575093269348145, "end": **********.489, "relative_end": 1.1920928955078125e-06, "duration": 0.06877017021179199, "duration_str": "68.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45163840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00339, "accumulated_duration_str": "3.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4590752, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.192}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.473363, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.192, "width_percent": 17.994}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.480454, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.186, "width_percent": 16.814}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1028049565 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1028049565\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-515177169 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-515177169\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-375224700 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-375224700\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2105683772 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125735645%7C25%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndCa2Z5QytJMVJialhianBrQUNFQXc9PSIsInZhbHVlIjoiL2tNLzAvRHVDd3QzcGVvd0lUd0pFRFhybGZ3Z0ZEN2dEZ0RsMDR1ZGcwU2Y5Wml6cUkrNmNpOTBHc1VYUzR6djV4RU1wRVVDODFIVjZ6c043YnNZMWhSUk1tdjQrcjdjVndEWjdHT0V1MzFRQVQrOFJseDk3UGRoT3Y0anp2R2lVdkp2TnZMVTVMOHdvOUloaTZIZWZyeEIxWDhJdGdWTDJEUmVqK2ozS0dMTndHYTh0ZzhlM3lSY2ZvYVY5MnVONmxnOTA0ZHZ0QmFkd2QxLzdtQ1h4eE5yaVRqVTgxZmZITk1DQzcwUFVRT1pCeGhmRkZCUEI3ajJ2cXZ0SllYSFdaSXJKRW1IT09vZ2RPOUM5MFczVHJURDFlcldOOEN3YXpONis2cG5kVzVPRDlFU29lL3Y3VHBNRXdmVkZOeUVQOFkrMVl0Y0liUkNhczFMRUIybFJ3YmpvZHpkSVcyMnZYTldhaDNTMU83dEVLbCtOS2NkNmhKeFpRUkpUNEx0WkVqcERwQW8zMzRXN0c3cnMyN3pSa0VhQ1dJK3lEMkdRTEhTVTZoN0pBNkRNYk1rRS9ialp4SE9ValNObU5sTU1lVnpBdENwbkI1L1J0NVBCYnlkRVBIZXhBcnloeXpxQkNHMHYvOHZDVGJhUlZ6dm5ZeFpwOG94MlpzV210SlQiLCJtYWMiOiJiY2U5YjJkMjRjOGEwMmI4YjEyMDM1NGY3YWU0MTE2MTYzZjRkZDE4YmU4NmQ3NGNkM2Y5NGQ1NmY0MzI3MGE4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InNZZnJ2eUN4US9rejYyTEwxN1M5ZVE9PSIsInZhbHVlIjoidVlPYzRWaTNxNkVvYzRjeEJLZWh1ZHAycyt6Y3ZobjE4MkFuQ0RtVklkeDh6czNwRk42cktNM1VscGp4YytQUldjdzR4bzFONUZWV0xJajRVU1VIZkZxWktidHlIR2lVYkFxVFA3VUJVSm1FcWZsaGJoRG9pSlNZNkcrZ1ZFQWxwSWNhK2c0eCtsNE5SMHhTU2thSjZ0M2NLazJwSUVhalk4Ym1vbW91ZGFobVkvcXpUTjgvYkduMGp3YytkVGFFcnNsb3VqVDlCUWtTaGt3Yy83bnJzNFIrenlkWHYrZjVDL0pXWnZhc1pmYmtpbEhGNE1vZ0Vkc2hMK0JBWkNEVHRBRmlHT0tCa3pVWkduMnVMYXlXTG5iZktFOWlxT3lOZ2JQMDR3TnJXa1FCRlpOK3Ayak15UXF6N2J4Nys3SGt3TitDNFJhVkdXZHlKSDhhVDNyTHppWHR3TENHTHVxUzFVSkNZQ3VUdDc5czA2RWpVMThBMlJnNlYxRmlycEk5Mk5hZFhIUGpZbHFJL0lQY0xKM3JHZ2g5UGZLTldCaFp6bStEZjhKdTY3SmdqY2xDOFN6bVUxVDZVSENWNThqNUUxSjlSb2RUdTYwWldMSUpzSDhGczBWMUxNSXFnRjJQNDF6MGkrRE81UDVZdXJVR1ovTzA2T2tsZmlIZ2hRemQiLCJtYWMiOiJmNWRmOWQ0NjIwYzk2Mjc4MjQ5ZGNkMzIzNDEzNTBmMDJkYTJjZTk2ZjFmOTZhNDgzYmIzN2E3YzliM2QzYjcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105683772\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1206363833 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206363833\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-420055220 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlGTU5JSi9RK2VTTEhicm5CYnhBa0E9PSIsInZhbHVlIjoiL1AycGFSS3JBODBOak9YcVRaemwycHcwTW95aXBUTWQxeGNYeHlVQUhEUGNIMnIxWkg2NWw5NHpKYVdJNnlpL2Q3K2RwOEM0ZkY0WnZpeVFWeVhJQVpqUFdVME9KdExxbjkzNXd4K1ludzNmZHRxVnBjeXZFZm9LTjNrbGIzeDBDdkJ4TWlQN3dxNEw3aW0yTEVCejhYclRDZ2lpMkFpSGh0ZldOYlRVc0sxRFZpdTQ5aFRRWHd5dk1ZV0R1QW9YTTFZWlVLQ3dKdTlBTER6eWFlTDdtZU5KRG1zQm9KZWtLam5FVUV1YnZFazhOblNoeWc3ZFlUWkcrT0hUYk1tQkRMek13YU5wOWhqWmtlck9DUGd3UW5PS3VZYlVGc0M1Z1hIWCtlWFlxRVkwK0NBS0dodGhHaHg5cWNhS295dTJrbmFWSVJoMHlpRUg4T2dkMWtqY21TWHg4Qm9MT0EycE8yb1FhK3JXSGw3cVBMOXM1ckhjaXBydzEvdkI1SGhWTU9QakZ3MmlUMDNqQ1RlUlZiUW5LS2J2Q0ZKajJUcVplRDNTVE9tTENONyt6Y2VJRGZsL2RJMWk1TnhVakZKb1NhK3ljRGFPRHpseTdCVGltR3NmWEU4bllhQTVRc0plWVkvdy8xM0xpcW1TZEhyVW1OR205dUx5ZktHZkpGeU8iLCJtYWMiOiI3YTNkNTQzNDgzMTVlN2IyYzE2YTY5OTJiYTNjOTkzMjNkNjhjZjhiYTJhZDFiMmRlYTk5YTc3YjU5MzY1M2NkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InMvUHJHV1dKSDlMTTVLTjVXQklwZHc9PSIsInZhbHVlIjoielFTRW40V1NNNW1hc0p2aG8zUG1PdXlCM3pxKy9TTlN2V1FNTXFkbitBVmg4WGlINHJQV1RGdi9oZXpRcVdnNk1vU1B2Mjc5aVk3aFozajRRbkVSSXdhRTVldDFRdFF1UzFnc1h1N3N2WE5pUkFtb0RPQ293ckRLMitTcUIrN2hYKzJqa1V4eDBndmZFSDNteTR1UzBnRXRrTE5GVUJCcmRpM0tqVktkMG9NclhoSmRteDVtZG04YTMxWEJ6TTlZeTh5L1FVSXlkNVl6SUZQQTRieGpBUjQzZ0JxK1kvTXlnMzVGRVNKYldsSm16bDZ1bDlVa3FkMk92NlZRTS8zMHdCU3Y1MHl5VXZCZTZVN0FHc2o4SnlCb2VZbVpXM21ISUJneEIyUlg2K3MweEpnV3J6OWhwdUVrTkpiU1ZTRjhOS25INS8zU3pLMXhmb1N5a1I4YzQxNVY0WGlqSG82NDRSczgydC9aQmJVN1hrbHFaaTUvNWh0REQwTm1hYWRKVE4vcVR0SzFOMUFlKzdLRytBL0tsN3ZzUitrblhpVVpheTVLRHQ5ZG5ka2hkMUpTRjJXQXlOVldUQ3NldDI5bUJCVTNuVy9VcytLR0w5Sk5MZStXRmNxdE5ueXNmY01QVGp2cXg2dzNrVmFGQnZRY3hsaTBOMThaWlhsS1lrdmYiLCJtYWMiOiI2NjdmMGMwNjM4OTVkYWRkNjQ2ZWRjOTZhNmZlMzAwODA0ZDM1MTE4MmJmZDkxMjI5ODhjMTIxNzg3M2NhZmJiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlGTU5JSi9RK2VTTEhicm5CYnhBa0E9PSIsInZhbHVlIjoiL1AycGFSS3JBODBOak9YcVRaemwycHcwTW95aXBUTWQxeGNYeHlVQUhEUGNIMnIxWkg2NWw5NHpKYVdJNnlpL2Q3K2RwOEM0ZkY0WnZpeVFWeVhJQVpqUFdVME9KdExxbjkzNXd4K1ludzNmZHRxVnBjeXZFZm9LTjNrbGIzeDBDdkJ4TWlQN3dxNEw3aW0yTEVCejhYclRDZ2lpMkFpSGh0ZldOYlRVc0sxRFZpdTQ5aFRRWHd5dk1ZV0R1QW9YTTFZWlVLQ3dKdTlBTER6eWFlTDdtZU5KRG1zQm9KZWtLam5FVUV1YnZFazhOblNoeWc3ZFlUWkcrT0hUYk1tQkRMek13YU5wOWhqWmtlck9DUGd3UW5PS3VZYlVGc0M1Z1hIWCtlWFlxRVkwK0NBS0dodGhHaHg5cWNhS295dTJrbmFWSVJoMHlpRUg4T2dkMWtqY21TWHg4Qm9MT0EycE8yb1FhK3JXSGw3cVBMOXM1ckhjaXBydzEvdkI1SGhWTU9QakZ3MmlUMDNqQ1RlUlZiUW5LS2J2Q0ZKajJUcVplRDNTVE9tTENONyt6Y2VJRGZsL2RJMWk1TnhVakZKb1NhK3ljRGFPRHpseTdCVGltR3NmWEU4bllhQTVRc0plWVkvdy8xM0xpcW1TZEhyVW1OR205dUx5ZktHZkpGeU8iLCJtYWMiOiI3YTNkNTQzNDgzMTVlN2IyYzE2YTY5OTJiYTNjOTkzMjNkNjhjZjhiYTJhZDFiMmRlYTk5YTc3YjU5MzY1M2NkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InMvUHJHV1dKSDlMTTVLTjVXQklwZHc9PSIsInZhbHVlIjoielFTRW40V1NNNW1hc0p2aG8zUG1PdXlCM3pxKy9TTlN2V1FNTXFkbitBVmg4WGlINHJQV1RGdi9oZXpRcVdnNk1vU1B2Mjc5aVk3aFozajRRbkVSSXdhRTVldDFRdFF1UzFnc1h1N3N2WE5pUkFtb0RPQ293ckRLMitTcUIrN2hYKzJqa1V4eDBndmZFSDNteTR1UzBnRXRrTE5GVUJCcmRpM0tqVktkMG9NclhoSmRteDVtZG04YTMxWEJ6TTlZeTh5L1FVSXlkNVl6SUZQQTRieGpBUjQzZ0JxK1kvTXlnMzVGRVNKYldsSm16bDZ1bDlVa3FkMk92NlZRTS8zMHdCU3Y1MHl5VXZCZTZVN0FHc2o4SnlCb2VZbVpXM21ISUJneEIyUlg2K3MweEpnV3J6OWhwdUVrTkpiU1ZTRjhOS25INS8zU3pLMXhmb1N5a1I4YzQxNVY0WGlqSG82NDRSczgydC9aQmJVN1hrbHFaaTUvNWh0REQwTm1hYWRKVE4vcVR0SzFOMUFlKzdLRytBL0tsN3ZzUitrblhpVVpheTVLRHQ5ZG5ka2hkMUpTRjJXQXlOVldUQ3NldDI5bUJCVTNuVy9VcytLR0w5Sk5MZStXRmNxdE5ueXNmY01QVGp2cXg2dzNrVmFGQnZRY3hsaTBOMThaWlhsS1lrdmYiLCJtYWMiOiI2NjdmMGMwNjM4OTVkYWRkNjQ2ZWRjOTZhNmZlMzAwODA0ZDM1MTE4MmJmZDkxMjI5ODhjMTIxNzg3M2NhZmJiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420055220\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1099561459 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099561459\", {\"maxDepth\":0})</script>\n"}}