{"__meta": {"id": "Xa6bf9fd207ba218a3592d8ac0d04e520", "datetime": "2025-06-28 16:35:17", "utime": **********.962265, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.484663, "end": **********.962293, "duration": 0.4776298999786377, "duration_str": "478ms", "measures": [{"label": "Booting", "start": **********.484663, "relative_start": 0, "end": **********.882685, "relative_end": **********.882685, "duration": 0.398021936416626, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.882694, "relative_start": 0.39803099632263184, "end": **********.962296, "relative_end": 3.0994415283203125e-06, "duration": 0.07960200309753418, "duration_str": "79.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45731568, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02025, "accumulated_duration_str": "20.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.911184, "duration": 0.01864, "duration_str": "18.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.049}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.943289, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.049, "width_percent": 4.049}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.95078, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.099, "width_percent": 3.901}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1281802877 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281802877\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1458726541 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128513642%7C55%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFSMkNRT2tqMklNOHZDNDhsZmRQOFE9PSIsInZhbHVlIjoiaXM0cUhLeUd4T0taOG9rQ0Rxd0IxRFdGZ0dvQlNXQUdsbmVmV1ZTUGN3a3UvaTlMMEZuOTR1NDc2SWtuWUowZ2ovejZMbUlKa0p1WVZzajl3OE1qMzNZMnZINVhJcFAvdHNkdDJ1Y1U2M0dhbFpFdXUrSXhNb3kxNytXVHpKa1dwd0hWaER0RFN3SjljZjdWT0J6M2ZabnBMdDNUQVF6YlI4VEN4aG0wQVJyTklIcHdCOFQyd2xFaFM4QllSNmtuemQzcGtnTDBKMUh3bExLWnpqTHo2SEFFb1hlcjFpY0JoTDdxTXp1YlFNRm5KYmV2ZjExZytOTEpPUzR5MmtDK3RCZHpaMWRUWThWNkVUWGRFTUdUZGlyYm9hbHNrajlibjRwR2NBYnBpb2d3ZUVzTTcySUw5MEtXRGxWdkJtZFBlaTlaSXorRFRjcTI3aE00THJ0ai9JQ09JYVZDZ3FEMnEzU0hpRjNtMkVINGtJY0JBNG85NktHQ1VJZkxTUldsMytSZzBUTGZWOFJlb3NqTVpha1NHTGlMeWhNazhxNE9SSW1sZTRzSHdsS2pTdXp4SnI5enRHTmIyTUh2Y05qZ1ljamEwSHVRZUdjMHhTSUlOcDBaUnByd1BhWFlmN1pBdjNiKysyTmFIelV6cGNrZjQvRnFtVFZzb2VRVHJOVi8iLCJtYWMiOiI5NzhhZTA0Nzk0Zjc4YTMwZjhhMDAzM2YwMGZhM2E1NzVkZGIyYjI1YmQ3ODk0ZDYwNzczZTk4OTY1ZTgyNDkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklncUxUOGszRnV5MTdaN3VHNEtPS0E9PSIsInZhbHVlIjoiTGdPcmNTVTRyMmVRN25ZQ00weW5PTkRUdk9hbFZweGhEdUt5SVRHWjRDS0d1aG0wL09ieklHL1pXMTczeUFHK2tJcURFK0tsLzFpTko0OUVEWGkxaUx6bUI3WU00ZkZiL0c5eU53ZDV3Q2VWdWMxbjBXd21odC9jNXlPTUVPMFdmcXZRQURlcFlpaEhDWXEvb2hnL1g3a2V3ejQ2dERNTHA4ODBpUThVRXVtQldsckgydGltYjJ6VEIrL0lodkxkRkp2K2ZqK3cvWE9DamhVZFIvcDJDUldaaE45cUV5bENvMkdUVGRjVTVqSDE3N2hCbDZQMExyU1I2WWVlcHBTaVdTQVBZa0tOQ21vNUU1SFdXb3BHQ2U4bkRLZGJicGp2cVA2Zm1XN2RQcHJWYzRZMXdmNXVKbzQxMFFmenVWWUovWVRNVmhWK2p1cXNodGlrQVpURUJ4bWpBdXdiVytWdlNYU3dtQWJXS215aXBESHFUQ0kvYjVBak1SS3VlcWIzY3JJZkw2UHJnMjlyTUFhRkJYbmlXVGZmSnliNCthbnZTVlJZcXRHc3A3ZTkxYk1mRnhOKzIwTW1QSXhKR3hvcXc2c0tKdUMxZE8yVHlITmZqaGxQKzJLT3Aya3l1WmZMVFM4cDY0TWNvY21seEo2UnNyZ1ZjTGx4U3pXdDUvKysiLCJtYWMiOiI2MmE0MzRkN2I1ODQ4ODhhZjk2YTZjZDY1ZTgwOWM2NTcyM2Q0MGM4YjQ3ZWFiNWNmOTNjNDBkNDgyODYwZTViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458726541\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-150719692 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150719692\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1126949073 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:35:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkN4RlBXN0VaODNrWkprRnBQMml0UEE9PSIsInZhbHVlIjoiUytYS05wcUxmRytiL2QxOURMdzNEcE9iRnh2VjdiV0pCbll0ZUJZeTZFcVFtNkZySnl6V3lraDlwY1l2UFFyMEFOK2IzaHlVWEQwb2hqTnpRcUdtd1AvQkhQMzlNMCtrY1p4dVRrbVdLcFFmekN0R1N6N083RXVEWWgvb3ZXVkY0WkVkeHJxdjkweUc3Zm5RSDI4bHRqRjRmVW9xd0JXMGozZTFUTThPYWxrdXVXQVNQVi93YmY0ZUpoWVdSeXpKalEybUl6MWRqd1BmZ3ZWQllWampua0NxeVU3WmM2V0FXZHFBSmI1Mm9mNmxzb3U2TFRuTkt1eVdPZFVEUm02Sk9CYkJIVWZUbTA5NkxiZWJ4bkc2TERtMUJjL0dmeFNQTVQreDhtUWdGQVl4T1pSZEwvTTR2V1U1dTg5QU1iTU1kQWdBSVN1QjNRc2pqOThDNkgwRWpyRU9DKzA1N3hYOVdoTUl4NDZXRGlReG1mOHNqRTZjZEcvWit4dEprTG1wZkJ2dXlhdTQwRnBKTFZCcVkzakUweFE1NTE5U0VTc0lkNndZTzZVOXg4c255bHdtT0J0MVNTQ2Q1ZHRTZG5DM2JXRWlBWW9DMlplNEdzUktjLzdlSW10bDVZS1RQUHVYNStsTDJPWTFQbjdBNHE0RlNqWUpjaVZCMWJ4Z3d3SjkiLCJtYWMiOiJkY2Q3ODY3OWYxMTVkYjYyZGY4MTYwYzQxZGJjYjE2YWY4N2NlZmUyNWU4OTc1NmFjYWVlMzY3ZGU1NmUzMDVmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijd0NWdJRjZ4SzZMbXE3MDdNbUhPdVE9PSIsInZhbHVlIjoiQ1Y4a1NqdDJSb0lzSVczL3duUHlMcEswSUV6cjBCMml0TFhXRkFWb0d2ZFRSL3hQdHB6cUx6T3hxWVBnbHpqNmRvVWhiSk9aR3VVRkQwYSs3MTNzbXJUblUwYUwrL3FHNDZ3VWp0RzE2UUhSZTFjc2JiODVmbk9JV0JUelk2TXFmUGpBMW5ScnpHbjljUGxyUE9QSjVUUkJZSzhWSnZ6TG9yMXlGcDJzUDI4VWE3SmNxd2FycG8xbExIRGRPTE1tYlZ0eFczRTdIV3NPTVZxcVdVKzE4SkJIdElUWXdncTVlWnRPTHFkYURNSUVqL3JnNzdLOTIzRW1hdkJRd015Y2g5Q3V5dkFOV3RPZW9YclJQMFdtK2NEcWZ6cENWekYyaHhaeTZtVFBDOThDdEVnRWxhYjZrZWJidUlJa0h2NlhtbXo2TlR4NjNQdS9XWWdZeXlRajJXMUtibXRqTk14WmdBQWIxdUdUdzc5Mzg2bmZPTXl1TEhnL2UxNzk0NExHU0g3TGFneE9MdndWSy9GWXNQZFhhUmZHWXFoSEFYazBURVA4YVpUTUlaOWlMdytWOFQwenpOZm9PZXFhR2FxMURnd05WaUR2dHdXUXh4NzIwMHFlSi9ERHRLT0F5bjAzR1VtMUY0NDRhbHdKd1Q0MjYycFdrQTcyOG13UVI0R2UiLCJtYWMiOiIwOWNlM2JlMjZlY2VkNDIyZjY5YjRjNWIxZTg5YTkzNjc3MjgyOTM2NjM3ODZmOWJlYzg5YjhlMzYzYmRkOTI2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkN4RlBXN0VaODNrWkprRnBQMml0UEE9PSIsInZhbHVlIjoiUytYS05wcUxmRytiL2QxOURMdzNEcE9iRnh2VjdiV0pCbll0ZUJZeTZFcVFtNkZySnl6V3lraDlwY1l2UFFyMEFOK2IzaHlVWEQwb2hqTnpRcUdtd1AvQkhQMzlNMCtrY1p4dVRrbVdLcFFmekN0R1N6N083RXVEWWgvb3ZXVkY0WkVkeHJxdjkweUc3Zm5RSDI4bHRqRjRmVW9xd0JXMGozZTFUTThPYWxrdXVXQVNQVi93YmY0ZUpoWVdSeXpKalEybUl6MWRqd1BmZ3ZWQllWampua0NxeVU3WmM2V0FXZHFBSmI1Mm9mNmxzb3U2TFRuTkt1eVdPZFVEUm02Sk9CYkJIVWZUbTA5NkxiZWJ4bkc2TERtMUJjL0dmeFNQTVQreDhtUWdGQVl4T1pSZEwvTTR2V1U1dTg5QU1iTU1kQWdBSVN1QjNRc2pqOThDNkgwRWpyRU9DKzA1N3hYOVdoTUl4NDZXRGlReG1mOHNqRTZjZEcvWit4dEprTG1wZkJ2dXlhdTQwRnBKTFZCcVkzakUweFE1NTE5U0VTc0lkNndZTzZVOXg4c255bHdtT0J0MVNTQ2Q1ZHRTZG5DM2JXRWlBWW9DMlplNEdzUktjLzdlSW10bDVZS1RQUHVYNStsTDJPWTFQbjdBNHE0RlNqWUpjaVZCMWJ4Z3d3SjkiLCJtYWMiOiJkY2Q3ODY3OWYxMTVkYjYyZGY4MTYwYzQxZGJjYjE2YWY4N2NlZmUyNWU4OTc1NmFjYWVlMzY3ZGU1NmUzMDVmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijd0NWdJRjZ4SzZMbXE3MDdNbUhPdVE9PSIsInZhbHVlIjoiQ1Y4a1NqdDJSb0lzSVczL3duUHlMcEswSUV6cjBCMml0TFhXRkFWb0d2ZFRSL3hQdHB6cUx6T3hxWVBnbHpqNmRvVWhiSk9aR3VVRkQwYSs3MTNzbXJUblUwYUwrL3FHNDZ3VWp0RzE2UUhSZTFjc2JiODVmbk9JV0JUelk2TXFmUGpBMW5ScnpHbjljUGxyUE9QSjVUUkJZSzhWSnZ6TG9yMXlGcDJzUDI4VWE3SmNxd2FycG8xbExIRGRPTE1tYlZ0eFczRTdIV3NPTVZxcVdVKzE4SkJIdElUWXdncTVlWnRPTHFkYURNSUVqL3JnNzdLOTIzRW1hdkJRd015Y2g5Q3V5dkFOV3RPZW9YclJQMFdtK2NEcWZ6cENWekYyaHhaeTZtVFBDOThDdEVnRWxhYjZrZWJidUlJa0h2NlhtbXo2TlR4NjNQdS9XWWdZeXlRajJXMUtibXRqTk14WmdBQWIxdUdUdzc5Mzg2bmZPTXl1TEhnL2UxNzk0NExHU0g3TGFneE9MdndWSy9GWXNQZFhhUmZHWXFoSEFYazBURVA4YVpUTUlaOWlMdytWOFQwenpOZm9PZXFhR2FxMURnd05WaUR2dHdXUXh4NzIwMHFlSi9ERHRLT0F5bjAzR1VtMUY0NDRhbHdKd1Q0MjYycFdrQTcyOG13UVI0R2UiLCJtYWMiOiIwOWNlM2JlMjZlY2VkNDIyZjY5YjRjNWIxZTg5YTkzNjc3MjgyOTM2NjM3ODZmOWJlYzg5YjhlMzYzYmRkOTI2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126949073\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1120594502 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1120594502\", {\"maxDepth\":0})</script>\n"}}