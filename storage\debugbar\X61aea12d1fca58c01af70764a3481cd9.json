{"__meta": {"id": "X61aea12d1fca58c01af70764a3481cd9", "datetime": "2025-06-28 16:04:27", "utime": **********.593154, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.195871, "end": **********.593168, "duration": 0.3972969055175781, "duration_str": "397ms", "measures": [{"label": "Booting", "start": **********.195871, "relative_start": 0, "end": **********.541179, "relative_end": **********.541179, "duration": 0.3453078269958496, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.541187, "relative_start": 0.34531593322753906, "end": **********.593169, "relative_end": 9.5367431640625e-07, "duration": 0.05198192596435547, "duration_str": "51.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45713824, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00245, "accumulated_duration_str": "2.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.568378, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.939}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5784712, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.939, "width_percent": 12.245}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.584161, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.184, "width_percent": 20.816}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-508771528 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-508771528\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1745884495 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1745884495\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1730229280 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730229280\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126658203%7C26%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBxU2RoZ0lUeEhtYU94THVJeEpSRkE9PSIsInZhbHVlIjoiVUJtVGY1UXoxUm1Lc1lNV094cEhjR0Q5ZnFRV0J1S2diU1VvNFR5U1VrbFE3cVhFTEU2allzWnpBNXUwSFdRVXk2Y0tFeTdPS1htS3lGb0ZBZkhlNGpoNGxWZytMOG90bVQrNUY4TUtlZmEwWGgrbnRpR05BUDVDSW1FV3U0ODNEL0lFeW5lQlUxeS9nUkgzZXM5ZW1ZR2cxQWtvZnAvLzhsaFZyMFliMStGNzdXQ0s0ajlsSnlRRXBXYzJHeFFkbTBvL0l4dk15Qm9wUWNTWlVKYUg3WnlhcnJWNXdvdUMzSFN3OE9NWXU0ZXE3TlVQdkEyR0RENlU3MUswS3BvSm9LQXFnNmNIWlRyL2FhNUs3VUNtbjMrOW9sTE5IR3FPY2lPRFFJaXBaWmpPMC9PM3VPRWE2T2UyemdJVWMrY1BxSjNpakgvWFo4YUxJQno1aG9qZ3N1MzhablpMOHhHbmdaUFF6OFhNOGVvdTFhYURwOU5sYWpPakh2Zk5KZDJoMVBFTVVuZ3NnK3JYem1HdnE4cTRPemV0SGdrUTk4MkgxRHFUSjZxd0FNUTdlaiswQUNJdWZxaXNIUUM2cDBRU2lmZ1ZZMkVnMUd6LzBRTzBZbElaaU9iNDJvSUFjQXlES3Qyc2paYlhJNW5SellYNmN6bTVEMVgvamNDVmIrdmgiLCJtYWMiOiJiZjE4OThmYjUxZGE3N2VhY2M4Y2QyYTVmZTEyOTk0OTAzNzNjMDA5Y2ViZWEyNGM0N2RkZmRhMjVmOTQ1ODFiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJUemNVa084TWdBL0Z4K09oTnFHRFE9PSIsInZhbHVlIjoiaWNWeURIRVpPRXgyR1ZIT3AybGRIM2trYlpxT1ZoS0wzMW0yRG53bmx6N2ZVQS9VMnBiZ3ZLU0RsMjZWdFpiaXhkZWV1WjBwNjB1TUR4bFMxV0dYdGhtUmxFUFBCZXFreVNwdUhlNVJ0c3VuemkvWnArY0xWRzNDTThuUzBjOFdpNXNDc3FHdk9pWGFkVS9RbnlXR1FPUWtCNEdOaW1yOGlDRXczV3IzcmlDZlNmL2RsZ203WHVLdkRuMnBINnRnZnFWSUtocXlrdzFLSWNQMFBFMHdNSDdFK1k3eERva2lmeExsNUJpeGcvcC9WcG5yZGNUQVh1NUQ2RVVOVkFnZ3BmeFhsd2xlQjh1K3lFTTVWdk1JcG9hcFd2R3dnRlpaWDlPRG5tbWRLakVxN2xvRjdFbUp4cUZsTXJzMU83bGRhdElIaDdOTiszMSt2OXJxV2c4UThiVVY1WlpDODBkWW1nNjV5WnJXbTI4VnVTa3MvUllURXhyOW4xdjd0SWRwN0taOUs1UityckZlM0xhR3JvMTB5RU9iRTllSUhna0VFYTZaeXpTcWI2TU8wa2FJYXRyTE85cGlad3l0V1NjWjBYRDRyMVljMmFqYWJObDFwNTFWSURRRnBQZFZwQ0VQaGlpdHlHMkpUN2V4NVF4WVZ3SXVMWnFJblJNd1Jxc20iLCJtYWMiOiIzYTY2YmM0ZjUwYTg4YWFhN2Y4ODk4YzVmODI0MWMzZDU1MDdmYTEyZTI2MzZiYWQ5MWMxZDUxYzQzMzg5MTBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1369524558 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369524558\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2045406981 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:04:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilp1cUd6KzN2dS8yaFJpMXpBZkdRS1E9PSIsInZhbHVlIjoieis2T2NDWlRUVVpva2V6MWhCOUVLZFdnM08zbmJtdlFBdnpiYUtDL0RMYnlWM2dtaDBkd3NCR1ViUHZIMVVLWDRteG81VElUWEY0Ym1Cei9hMlpLcVgwUnFCS3ZZQkR5Vitia0xQbVlsTTBxYy9oRDBNL2RHZUdKME5ubC9RZ0ZET3plR2VLK0RMZzVRMEFUaFl3WWlLek5NbGZJbEt3Q01yQWMzZFczSTZ2dDdNNTJqSjRVZFZiZE4rK1ExMFFTS2ZWSTNLWEg4Sm0vTFBUN1NxYXRZMG5aU3lHeWFJYWk5c1lxaGRnL2plRjdMM2pVVDBaQXhHMzZsNGZqUjRTcUh2WVQ5ZVVieEhkOHF2REJkZzBsMFZLOE90WDU4eDZwMXl1RU1QMktJS015RnZqOXVNV1JKTWJ1bnFabXpZZy9veS9rRFpvOUltSTRXYXZ4bnhPY24zNVppaDVyODdjdXBsYkxtdlFjQytyRDNqNlkxL1NrMCtSbDRIM2FEbUMrRXdpeS93djV2eGtSMkZUek0xMGpZTzFGeU5vOE1QMnF3bHA3V3p3ZVgvOWtjRWZrSlp0aXJmUmoxT3hBTFo4TDhPb21SWWt2cFpnUXZiVHZidjlpOTh6UmFlem9NVTBjOURMZWJnWk1WSlFNZ3YvNFczd1dvYUMySDdOVUtFU2EiLCJtYWMiOiJlNjBkZTBlNTgzMjM3ZGI0MWVhOWNiNTNiNzZkMDQ3MGU3NDE5MTYzNmM1ZTQxYjIxYzczOTFkNjcwYWY3NGFiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJhRFpETFBPQlNPUlZhK0pUSWkycWc9PSIsInZhbHVlIjoiSldqcXh1bzA5UVkzWU5lOGFuMTJFdnRyeDg0dlRwaC8zNk1xTlJmNXlTNnhmeTUySXh6TnpmcnB6MkVwRXMzL3p0V2xoc0hkTlpVMlk0U2xKMUMwcnluMkwyam5Ua2xjUW9SYjdpcDQ5ais3YVpQUlkrLzJCVlNwVE1KZmx1NUFqYndqY3Y3ZWthemsrc0FkQWM2OHBCV2loK0NKb3FrT2g0QnhvNEdHMWJIU2xYbzZGR0gvTTh1WnVDaFJ3VnZpaFZkbzFwWGpRWlpKN1kvWjhKWEMzVktDWWVrMU50c1BjNmdHM0RJbXdNRWZqWEs3QmFIaGlmVFBVeDBHM0tIazBJYzI4aFg2QUpmTkdkdDJyVGdzUG9CUS9Sd0ROL1QrOVYvR1g5ekZqK09TRDJZakQxc0RSRWd4dVNUTUpaZ2I3Vk9GeFpubC9NeDdoMCt3K3B1UDNFSTAxZ2Fha1g4c3hSVmRqWmMveXQ3QU56U0RBYWtxNmhOak11SjFHRFd1UWpRdHZ5MmNuZGNKY21sTjUzZ3dPSFphd2lNS2xGS09NUlBBQzJ1eERYWjNFT0hnbkFaRDYyZ1B6emNINkMzcmNyWnZOaVlxQS9tdzFFd0xQeE1oZ05MZGNVbjlHL0JYL21zYXV6em5sSmY0VlppRDVDUE1CR2ZjTWV0YlI0VWkiLCJtYWMiOiJhOThhZjRlNWFmYzI4NDA0ZTU0YjUzYzUzZmE1Yzc2YTczZmQ1MjBhNDgxMWNmNTBiMTAxNjUzYmNkNTExODQ3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilp1cUd6KzN2dS8yaFJpMXpBZkdRS1E9PSIsInZhbHVlIjoieis2T2NDWlRUVVpva2V6MWhCOUVLZFdnM08zbmJtdlFBdnpiYUtDL0RMYnlWM2dtaDBkd3NCR1ViUHZIMVVLWDRteG81VElUWEY0Ym1Cei9hMlpLcVgwUnFCS3ZZQkR5Vitia0xQbVlsTTBxYy9oRDBNL2RHZUdKME5ubC9RZ0ZET3plR2VLK0RMZzVRMEFUaFl3WWlLek5NbGZJbEt3Q01yQWMzZFczSTZ2dDdNNTJqSjRVZFZiZE4rK1ExMFFTS2ZWSTNLWEg4Sm0vTFBUN1NxYXRZMG5aU3lHeWFJYWk5c1lxaGRnL2plRjdMM2pVVDBaQXhHMzZsNGZqUjRTcUh2WVQ5ZVVieEhkOHF2REJkZzBsMFZLOE90WDU4eDZwMXl1RU1QMktJS015RnZqOXVNV1JKTWJ1bnFabXpZZy9veS9rRFpvOUltSTRXYXZ4bnhPY24zNVppaDVyODdjdXBsYkxtdlFjQytyRDNqNlkxL1NrMCtSbDRIM2FEbUMrRXdpeS93djV2eGtSMkZUek0xMGpZTzFGeU5vOE1QMnF3bHA3V3p3ZVgvOWtjRWZrSlp0aXJmUmoxT3hBTFo4TDhPb21SWWt2cFpnUXZiVHZidjlpOTh6UmFlem9NVTBjOURMZWJnWk1WSlFNZ3YvNFczd1dvYUMySDdOVUtFU2EiLCJtYWMiOiJlNjBkZTBlNTgzMjM3ZGI0MWVhOWNiNTNiNzZkMDQ3MGU3NDE5MTYzNmM1ZTQxYjIxYzczOTFkNjcwYWY3NGFiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJhRFpETFBPQlNPUlZhK0pUSWkycWc9PSIsInZhbHVlIjoiSldqcXh1bzA5UVkzWU5lOGFuMTJFdnRyeDg0dlRwaC8zNk1xTlJmNXlTNnhmeTUySXh6TnpmcnB6MkVwRXMzL3p0V2xoc0hkTlpVMlk0U2xKMUMwcnluMkwyam5Ua2xjUW9SYjdpcDQ5ais3YVpQUlkrLzJCVlNwVE1KZmx1NUFqYndqY3Y3ZWthemsrc0FkQWM2OHBCV2loK0NKb3FrT2g0QnhvNEdHMWJIU2xYbzZGR0gvTTh1WnVDaFJ3VnZpaFZkbzFwWGpRWlpKN1kvWjhKWEMzVktDWWVrMU50c1BjNmdHM0RJbXdNRWZqWEs3QmFIaGlmVFBVeDBHM0tIazBJYzI4aFg2QUpmTkdkdDJyVGdzUG9CUS9Sd0ROL1QrOVYvR1g5ekZqK09TRDJZakQxc0RSRWd4dVNUTUpaZ2I3Vk9GeFpubC9NeDdoMCt3K3B1UDNFSTAxZ2Fha1g4c3hSVmRqWmMveXQ3QU56U0RBYWtxNmhOak11SjFHRFd1UWpRdHZ5MmNuZGNKY21sTjUzZ3dPSFphd2lNS2xGS09NUlBBQzJ1eERYWjNFT0hnbkFaRDYyZ1B6emNINkMzcmNyWnZOaVlxQS9tdzFFd0xQeE1oZ05MZGNVbjlHL0JYL21zYXV6em5sSmY0VlppRDVDUE1CR2ZjTWV0YlI0VWkiLCJtYWMiOiJhOThhZjRlNWFmYzI4NDA0ZTU0YjUzYzUzZmE1Yzc2YTczZmQ1MjBhNDgxMWNmNTBiMTAxNjUzYmNkNTExODQ3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045406981\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2069034505 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069034505\", {\"maxDepth\":0})</script>\n"}}