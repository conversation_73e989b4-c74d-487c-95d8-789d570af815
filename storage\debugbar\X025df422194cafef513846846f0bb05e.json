{"__meta": {"id": "X025df422194cafef513846846f0bb05e", "datetime": "2025-06-28 16:02:24", "utime": **********.417872, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126543.981631, "end": **********.417905, "duration": 0.43627405166625977, "duration_str": "436ms", "measures": [{"label": "Booting", "start": 1751126543.981631, "relative_start": 0, "end": **********.339951, "relative_end": **********.339951, "duration": 0.3583199977874756, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.339961, "relative_start": 0.35833001136779785, "end": **********.417908, "relative_end": 2.86102294921875e-06, "duration": 0.07794690132141113, "duration_str": "77.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46420112, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2428\" onclick=\"\">app/Http/Controllers/PosController.php:2428-2462</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02667, "accumulated_duration_str": "26.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.375007, "duration": 0.02604, "duration_str": "26.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.638}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.410478, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.638, "width_percent": 2.362}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "[]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-581797247 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-581797247\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1633403984 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1633403984\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-565386268 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-565386268\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-365605283 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126540952%7C11%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImU5SWlXY05teXBNQkd4M2xHaERORFE9PSIsInZhbHVlIjoieWVxaDNjYkJmU08rL09aWkg0M1dmMUd2S1RxTXRZU09PRXBiNXJQeFkvS0R3VU85OVYzQlBSRmhoamRMSE1NbnFZSXI4ZUJyWi95MXNHeHp3VnNpWkp5UEd3VmhIUWpqWGs4RkN4VGREZFdpSXBGdS84dmRhcEtmQVMxVGxMb01vUXk3V3pxUWFQTTV6TURnbUhNc05zK1krVmNlMmFFczFuRjVhajJrcGNVZ0tnVk52eUthSXVaQllrMTJhY1J6RGhoVDJwcklWTWw2YXhuUEpqY0FWcHN6YzV1YmFweDhkZ01wVnZWeUhha2E5L2NMaGZNa0tyTzg2b3BWU0Q1a2o2Z2txK1FEYmJPVDV6KzByYmNTV09KZy9ETStFck1LV2pDQmhMd09FN2VvUm9VSTNuOU1xbUxxWVVuT2kxNXY5ZmNNY3I5c0h5Y2YyN1BKNTNQcXdnZDlRV0VEZEQ1UGNjaCtpZkdIZTU5ZHVFckEwakxPRWIvUExUV094SnBwT2RtKzdqVzN5ZlpOUm8zN2ZZWldJS29xSVBlUHVyRWIyUk9wUmVJRldRN0tkVTlyamliQnowUkxlOFdwY3UzSURBOWdnZHVxMlNRS2luUGNTUVdoU1g4aDR4Rng3SmZNSTFsNGt2dWFIQXkyUG9Wc2ZxYXN0eFdWQTkzOXB1NkYiLCJtYWMiOiIwODI4MTYyODNkN2I2Y2MwYmE2MjZmZjFlZTAzZDcwZTVhYjlkYjc5YzMwYmJkN2Q2MmUzZGVjZDJjZTNlZGMwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkU4b0RMcHdMVUZlK3R3UVBrYThlYUE9PSIsInZhbHVlIjoiUmhmNVNtMHpMUnZTRWliSkwrVzRSM0k1SEgzN2RKeG14YWRoNi9Sd2kwdFpWV2o2NFgxS0FYOVBqeG85aXdJa1BaVVJhb2ZtckY5Z1dyczlSeUFXUE93Y0FtZUtyZjhWMTFPYnljWUdBTFY5dGJpRkNwNGpRblpqSkdxd1poMlVuOFJxQjJ2RU40YTNBUytsVW1KQ0Q0bTNKd2Z2WVY0dHF6UHdncU12NWdaSDlsM2RHcVFtVDQ5M1k5aU4zYnVHMFBKcERTQW5BYk0zdktpWG5lR1grQUhGN2dvZHRWb0dnMTdwcUg3YWFZbTQ5eDBKNk00OEUwdmd6NjdLRUcyaEhHZzF5MWEwTDNyYTR0NDVvTHBDckhlUFpPSHJZTzAxcVJITWZUc1hkZUZYaElZbUx1ZXhvNExLaitJbncxSkExb080S0tNc3p4WG4vZGN5OTZLK2Z1cHlNY0lZeEFGeVhuNGlYb3BlUGtjU1JPV1NLTXRvcEdRcWdOTzd4NDJhREMzVWNDNGh1MzZyUzJ4VStaRExmM0kwMGMvOWovVWRiemNIZFE2OHhoRjh5NkdxaGU2UGFTMnZXS3ZoaDdRQ0dBYnM3S01OK1JDdkZQb0tONWYwTjJmcWFSR0FBTzFldStrVXpEcWY0MmM5RkNuajhUQW9nSnduVTZkTWdYYnQiLCJtYWMiOiJiZTVlOTVkZjYzZDQ0YjQ2Mjc4MDZmYWYzZWM3NzRjMGM1NzZhNzY1MmEzMzk0NGYyZTczNjFjNzBkMzQxYzU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-365605283\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1147044238 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1147044238\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-311783255 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iks2Y1V1d0lmRXZxaUN4VGsxSW8zaWc9PSIsInZhbHVlIjoicGpEeGJIZUdVYnpYajljU2JZdk9oT0t6cDMvV2JSNGtUWm9pVDFRTWx5aXdqUEowMzlPMVRxWkJkNlBBN0pKdmFPYTlWWU5kNFBWTUtMMnBMR0tzcys5Z2pENHJaN2V2TGZkOEVJVWlVT3FGNTd1Rk8yc3JEcXJvZGVYUXl1a3I5MGY3Sm90bmU5eXpNSkZjSHpuSE42QmxkaTJyeFBTNnhvT2Q1NXhLb2pBV2RiMC8rdGVGQko4eUp3bjBpdjRFdXZ5Ym5zKzdlaW9FdGFJUDMyVzhPdWxKdU9abkh5d2hkNEduZm1lMFppempDQlBtc1VMZzlzK3YwQm04Z2pvUmFyYlZEVVdLUldnUGxlWENpVlBUWUE2RGR5bk5PQU4yT2syb05jRms1VjcwSjVmUjJBQ0JKZUtpUEFCWnBYVlFBUmdjcTBiYjArWW1DWFZwd2NTeWVxcVFKaytRQVZub25aZWhDZ2dveW9UcVFoWTFzRU5oNGxmZXV0RFE1ckVNd01DeFFxRGtTcHptaTVLcSt2Zy9TRXR4YWZWRGFGc2h0OEF6VzM2Nm1INlVlTTlHakl2MVpHWTJ5cEh2anlpdXNFUnR1UmhUZkVWbzlFaG9lTW96OGRXekNBMEs2SWFpcU5GZmxocS92QktmY2VtZktJRGhmLzJ4eE5jc1plZVUiLCJtYWMiOiJjMTFkNWQ2OTVjZjE1MDM1ZjJlNWVjZTBhNDFhMGJjNzE1NTBjMmM2NTM2ZTg2YWU5YjMyMzg3MDI0YTQ2YmI1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkR5YksxQlcrVWhMMTJVVGZGRjRFRkE9PSIsInZhbHVlIjoiWUZBWGpWMlFwWDBqNXBaZXVWZ3YyMzUyNGk5VmZGSXQvdFA2T2lMVzdWbWtOeUZhZ3RzUjdQVis1QU5tTEUxVytyYnJFallZcXpHMUFxZGlpTlBDSXhmSFkreUxTL0FzMkx6Y1h3Szkwa00xMEJyUms5UGVEN3NYdzVtZ2pYZktzZC9jWkRHWXE4SzUzMmVpRzExV3dqU1Zwa0lMNTNMb1JrbEcrMjNvMGNhQjE5cnJ2R0dpUmZmUmhkSnBOekowenlUMnh6KzFkTThhTFZ5NFBOQS9hMDNBWlpNS3VMVWFQSTVSc1VaUTdQMzZLR1U0aUhXdDk4SDV4bFdpRWMvRVIxRVVGUkVHamQ2WW9oMU8vUjNjeitkazNMNFV5UWZaVVFYR3dqSzZyenRsUm1CdlA0Y1N0SUdCTVJOOW9lcHdoNFh3REdLZFR2aWRSSmN4NWw5UWpaY3gwOVZXL0Y4bytEdmFJNzVFK21WLzFyVk0rTXNwekZJN0trcHBRYmh0UENya3I3bG01LzVxUjR3bTVzNHhpTllFWWtEL0JQVDlYeG9NNXh3V1ExYU9zaG1CZi9NQXBSL2dTQ2J5cDE1M3hVZk8yL3ZPL1FSaXZod2NsZ1pDdzVJWkVuSFVHQmZQZVlLVkZNSXQyYTRtT3lPUlhnUVduNHN2OFQ2S3hyRTgiLCJtYWMiOiJjMzE2NDM0YzFkODlmMjI3YTUzNjFkZDEwZDY5MjgxMWFmMjczYmExOGUyMWUzZGE1MDA2MzE0MzA1ZWExOGEyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iks2Y1V1d0lmRXZxaUN4VGsxSW8zaWc9PSIsInZhbHVlIjoicGpEeGJIZUdVYnpYajljU2JZdk9oT0t6cDMvV2JSNGtUWm9pVDFRTWx5aXdqUEowMzlPMVRxWkJkNlBBN0pKdmFPYTlWWU5kNFBWTUtMMnBMR0tzcys5Z2pENHJaN2V2TGZkOEVJVWlVT3FGNTd1Rk8yc3JEcXJvZGVYUXl1a3I5MGY3Sm90bmU5eXpNSkZjSHpuSE42QmxkaTJyeFBTNnhvT2Q1NXhLb2pBV2RiMC8rdGVGQko4eUp3bjBpdjRFdXZ5Ym5zKzdlaW9FdGFJUDMyVzhPdWxKdU9abkh5d2hkNEduZm1lMFppempDQlBtc1VMZzlzK3YwQm04Z2pvUmFyYlZEVVdLUldnUGxlWENpVlBUWUE2RGR5bk5PQU4yT2syb05jRms1VjcwSjVmUjJBQ0JKZUtpUEFCWnBYVlFBUmdjcTBiYjArWW1DWFZwd2NTeWVxcVFKaytRQVZub25aZWhDZ2dveW9UcVFoWTFzRU5oNGxmZXV0RFE1ckVNd01DeFFxRGtTcHptaTVLcSt2Zy9TRXR4YWZWRGFGc2h0OEF6VzM2Nm1INlVlTTlHakl2MVpHWTJ5cEh2anlpdXNFUnR1UmhUZkVWbzlFaG9lTW96OGRXekNBMEs2SWFpcU5GZmxocS92QktmY2VtZktJRGhmLzJ4eE5jc1plZVUiLCJtYWMiOiJjMTFkNWQ2OTVjZjE1MDM1ZjJlNWVjZTBhNDFhMGJjNzE1NTBjMmM2NTM2ZTg2YWU5YjMyMzg3MDI0YTQ2YmI1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkR5YksxQlcrVWhMMTJVVGZGRjRFRkE9PSIsInZhbHVlIjoiWUZBWGpWMlFwWDBqNXBaZXVWZ3YyMzUyNGk5VmZGSXQvdFA2T2lMVzdWbWtOeUZhZ3RzUjdQVis1QU5tTEUxVytyYnJFallZcXpHMUFxZGlpTlBDSXhmSFkreUxTL0FzMkx6Y1h3Szkwa00xMEJyUms5UGVEN3NYdzVtZ2pYZktzZC9jWkRHWXE4SzUzMmVpRzExV3dqU1Zwa0lMNTNMb1JrbEcrMjNvMGNhQjE5cnJ2R0dpUmZmUmhkSnBOekowenlUMnh6KzFkTThhTFZ5NFBOQS9hMDNBWlpNS3VMVWFQSTVSc1VaUTdQMzZLR1U0aUhXdDk4SDV4bFdpRWMvRVIxRVVGUkVHamQ2WW9oMU8vUjNjeitkazNMNFV5UWZaVVFYR3dqSzZyenRsUm1CdlA0Y1N0SUdCTVJOOW9lcHdoNFh3REdLZFR2aWRSSmN4NWw5UWpaY3gwOVZXL0Y4bytEdmFJNzVFK21WLzFyVk0rTXNwekZJN0trcHBRYmh0UENya3I3bG01LzVxUjR3bTVzNHhpTllFWWtEL0JQVDlYeG9NNXh3V1ExYU9zaG1CZi9NQXBSL2dTQ2J5cDE1M3hVZk8yL3ZPL1FSaXZod2NsZ1pDdzVJWkVuSFVHQmZQZVlLVkZNSXQyYTRtT3lPUlhnUVduNHN2OFQ2S3hyRTgiLCJtYWMiOiJjMzE2NDM0YzFkODlmMjI3YTUzNjFkZDEwZDY5MjgxMWFmMjczYmExOGUyMWUzZGE1MDA2MzE0MzA1ZWExOGEyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311783255\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-16236372 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16236372\", {\"maxDepth\":0})</script>\n"}}