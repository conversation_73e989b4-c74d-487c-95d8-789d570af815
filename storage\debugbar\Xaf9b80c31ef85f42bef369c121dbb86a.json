{"__meta": {"id": "Xaf9b80c31ef85f42bef369c121dbb86a", "datetime": "2025-06-28 16:21:45", "utime": **********.1682, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127704.721466, "end": **********.168218, "duration": 0.44675183296203613, "duration_str": "447ms", "measures": [{"label": "Booting", "start": 1751127704.721466, "relative_start": 0, "end": **********.105485, "relative_end": **********.105485, "duration": 0.3840188980102539, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.105493, "relative_start": 0.38402700424194336, "end": **********.16822, "relative_end": 2.1457672119140625e-06, "duration": 0.06272697448730469, "duration_str": "62.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45549888, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00319, "accumulated_duration_str": "3.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1456032, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.323}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.158364, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.323, "width_percent": 17.868}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1611469, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 81.191, "width_percent": 18.809}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127660348%7C43%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkV1ekdLYWM5MUlkb3VhOGlSc2pxK2c9PSIsInZhbHVlIjoiSGYxL3JVRUk3TUkzMGQvWjlHd0N0TlVPaXNaSlNOZXQ3RklTa01aaDRERzVCZ2R4MTR6QnZTd0ZRbmo1NXBKTkIyTFV2dTVITzl4RjY1TG1HNElLaUdrWG5MTkhVenhnaXl1QzhPQnNtRjhhWmJkdGRORDkza1F0a0MyV3NSUDBjMDJwTVlYdWN3ZXIxYlJMdng5WSt0WHB1R2hXZUpKQlExS2xpcitCa2FldjNqOTRTcFA3N1ZyY3JKbzErMDJFWlUxTHRKZ2pPNmtacFJ4NG8yQmdvRW1CRXB0VU5JTEpTdFd3Tkl0OGZldlBsbmpIdm9aa0RuTlR5eU1vQjZoNnFCTUtMSW9CZU1Jc2FHVXdDVWVTODJya1lLd3E1dVl6Tk15bG5NcFhRK3daTFZFRzRhR0ZmQjMrNnk0NzJ4ZVFZR1JVaDUzYjB6eCt4YzhtVXdaMUFPYm0rdEU3VkVvWW1zMHhTNS9zV1lwNjdaY0g3UUFQd3ArakhUMFdQQU1jN0NzNkoySWdVbTloQytPdGUzUnhDYjdMc0x2emxPSW1Ud0U4SSt4dmk0K0ZPKzdablRnU2N0bnAxWFAveWxKdW16UFFUTnQrejY2Z1UrVjZTdHFCV0Jtd2ZFNVorcU1kQUdoZnRUdk9lc3J4YVNEQmEyODdHQkQzYi9wSExwNUIiLCJtYWMiOiI0MDJkOWY5YmYzZWEzZDk4MTY5YjFmMzBiYjk4MDk0MDI0ZWRkOTRkN2JhNjVmY2Y0NzI0ZTdmMjA4OWNmMDExIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpMS2IzREZTYTJTT1Zza1o4RnpGZ0E9PSIsInZhbHVlIjoiMlJaR3ZDYXpyd1cwM3h2dU9WYi9OeU9aWjhjK1hkQUFBK011MS83RVNQbmE1ZjgzcUkvRW5ZZEpnQ1dEeGZ0bThGQ0RmbEdoL1d6ZEh2Z05xT3RMblZhYUlKVU5WbUh3MTJKMVplalN1bDh0NFR2VHJCSWZzeThjUzgrVlJNcE5zbGV1aWhncVp2MFc4Y1YrL0NyVGVuQ0RuSWJYS0ZZY29ReWdqNTJLaUQ2NktyWlVnbzNJUEltak42Y1o0M0p1ZDRIU1R1K0lsRU5KN2J5NkJXKy80WE12ZjErdTYyN1lqU2J5UTZERnFKMXc0bUdxd2M3WkVxbFZJQjNkL2FYcEtQUTdhVmM5L0pzbndpR2c1anpWeStVU0xzMk55QmFRcHlGaFFWQVZGZU1DSjRhN1Q2MUphSjhZeUNGcmZscityVEJJVTFYVW1FSUZaUnd6QnBtMnBtZUxsNnNOREpzeWNGTjUyanpRRWl0Y3JTL1hvbHlXMkk0ayt0M2JvZmFDNENVMTVRMEo0N3M4eWJReU5IdkFWR1kzZE1DaStLdzk1eklETUljeG5KSkFvZEMzOG9RZGJ1SzZRNC9aaW9ldHhoNGpxTzZ4d1pVWWJqdU9ZU21jbDBwK084bTFKQ3d3TkIwMnp4YVVUKzVjMXJpSDlFd1RQdWh1alE0OS93M3MiLCJtYWMiOiI5MDUyYTg5MGIwYjBkODE4ODRlMzg3ODllMTk1ZTg2NmQwZDQwYTBjNDkyN2FkNjE0ZTg3MTMwZTA3NTA1Y2NhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-467300367 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467300367\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:21:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBDZ0pBL2Z0bUFkQ0Nna3FQd0hmR2c9PSIsInZhbHVlIjoiMnpkQm5UUTNMOXNIbkNCREJheUZ3WVJGL1dtVjlSMCtXNktIM0xKSDkvVEJKTGtBY3JkNU5mQ29WMXVsdU1YYWw4Zncvai9nVVpCZTlrUFRvUHZ3VVBBdkxjUEpQMjIwZ1F3aFl4UlJyUldKVkNXVndDNFBYbWlwSGMvc3lYTFJFRmlRV2ZmaGd2Mjk4MlBvVTRCRzE2WWJ2YWR3cTZkYUYzMVBCaGF1RUhLN3pOSmtKUWc4dld6djIvNXFFbkFuNkI4Z1pkZ3BQSVNoaENLR1NaUU0rZzFFUmhWeFNkUGVIRUpBSmF6eUNvcnkvSEJYd2lpeG56bFFZWkNnVlFkMytzMmxCN2FzeGdleTIrdmE2S0lUVWJvSnVxMnY4ZjZwSUVZbks1c1RoUzhsS0pLV0wrT1ZDYVp5WkZBS0lOK2Jad0h2am00cmFwQ1hTd25iR0g2MVVzNzF6Q1FGd1owa3h5YnA0dmRsbzR2WGNQNndNWTFTbEhabDNMY0FUem50MlFwWkpjb1hZNDhub0RINnM2ajFKdXh1cndYNkFkSHNNUXZuV1UvVEc5dXNiWHYzOVN1Z2Y0K081a2srUGhiOTVtRVlkZWt1eXNXZHc1eFhLUkdrZUJCSzBiemRUaDNzdTM5ZzhZb25kZ3N5UVlnUTJ2ZFJhSXB4RWt0SlVwbFciLCJtYWMiOiJmY2MzNDQ4ODUwZmY3MWY5N2U5YTQ4MzRlNGY2NTVlOTBmMTQyN2RlMWI3MWQ1OGEwZTBiMmE3YTI3NDdjOGM5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1Oci82OGtJZHROTjh1c3V3NnlkSFE9PSIsInZhbHVlIjoiUENZN2cxcU1kVVJDS1FIUzAxbTB4cWZJTjA0NC9xZ0lNTC9Fem01V3NUSjRTbCtyTjJ5SnRmeDBPa1A4enFxTUhsUEZWZ0pzaENPYWpRUlVhdnNwcHRsM1k2MTlSYVVORkJ5a1Nsald0R29jc0ZjTlY0RzhCNXArL2JwSlI0TzdYbGRVTTc0R0xYbFUzWEphTnlXWkZNc0pPV3ZmN0s0VTRkQXRkbDlNaHRlMk1wdjBwZkU2c0hTMU9IWmNKazVFRlVhdkNMV3JraURVdU9uUDVCZkN0czhZK1FvM1VHT0dnbWpZaDVkM3pWQTlJbzQxUE9QMjdwU0FoakpZNnJpNWIrYm96ZmYvdURGb0lJZFhrVDlYSmZzUVRPUkdheDg2MFpNTUFEMU1kS3E4VFlvbWVPVFpGQlRIMkZVUzA5SlhpSEo4dzd1MEJBekt5bkxKODNqVXUybnZuT21nWllUM01pMURlZFAwb0NHSWZpMUtQbkh2NUtCZ0dTbG11QWx2QVk5V3k5MEgrRWM2TlkwU013RC9nQmN0L1BtZjR6U2Q4dW9kcWU4ZTNMQzdjM2tBQ1dPTTJXV1o1S1BLQzBpUnozOXE5U3JBMStSaU1qYU1HaWhvWmhPTlA5VmJySm03ckZ6SDhqUHRQMVEzMXRPdGpQOWVpRWtWeWxIY1NnSWwiLCJtYWMiOiIzZDdlZjA0ZGQxNjViN2QxYTg5ZGI3MGQwZTViN2MxNGY3ODYxNjczZjMzMGQ0NDY1YjdjNmNlYjMzYzNlZDEyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBDZ0pBL2Z0bUFkQ0Nna3FQd0hmR2c9PSIsInZhbHVlIjoiMnpkQm5UUTNMOXNIbkNCREJheUZ3WVJGL1dtVjlSMCtXNktIM0xKSDkvVEJKTGtBY3JkNU5mQ29WMXVsdU1YYWw4Zncvai9nVVpCZTlrUFRvUHZ3VVBBdkxjUEpQMjIwZ1F3aFl4UlJyUldKVkNXVndDNFBYbWlwSGMvc3lYTFJFRmlRV2ZmaGd2Mjk4MlBvVTRCRzE2WWJ2YWR3cTZkYUYzMVBCaGF1RUhLN3pOSmtKUWc4dld6djIvNXFFbkFuNkI4Z1pkZ3BQSVNoaENLR1NaUU0rZzFFUmhWeFNkUGVIRUpBSmF6eUNvcnkvSEJYd2lpeG56bFFZWkNnVlFkMytzMmxCN2FzeGdleTIrdmE2S0lUVWJvSnVxMnY4ZjZwSUVZbks1c1RoUzhsS0pLV0wrT1ZDYVp5WkZBS0lOK2Jad0h2am00cmFwQ1hTd25iR0g2MVVzNzF6Q1FGd1owa3h5YnA0dmRsbzR2WGNQNndNWTFTbEhabDNMY0FUem50MlFwWkpjb1hZNDhub0RINnM2ajFKdXh1cndYNkFkSHNNUXZuV1UvVEc5dXNiWHYzOVN1Z2Y0K081a2srUGhiOTVtRVlkZWt1eXNXZHc1eFhLUkdrZUJCSzBiemRUaDNzdTM5ZzhZb25kZ3N5UVlnUTJ2ZFJhSXB4RWt0SlVwbFciLCJtYWMiOiJmY2MzNDQ4ODUwZmY3MWY5N2U5YTQ4MzRlNGY2NTVlOTBmMTQyN2RlMWI3MWQ1OGEwZTBiMmE3YTI3NDdjOGM5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1Oci82OGtJZHROTjh1c3V3NnlkSFE9PSIsInZhbHVlIjoiUENZN2cxcU1kVVJDS1FIUzAxbTB4cWZJTjA0NC9xZ0lNTC9Fem01V3NUSjRTbCtyTjJ5SnRmeDBPa1A4enFxTUhsUEZWZ0pzaENPYWpRUlVhdnNwcHRsM1k2MTlSYVVORkJ5a1Nsald0R29jc0ZjTlY0RzhCNXArL2JwSlI0TzdYbGRVTTc0R0xYbFUzWEphTnlXWkZNc0pPV3ZmN0s0VTRkQXRkbDlNaHRlMk1wdjBwZkU2c0hTMU9IWmNKazVFRlVhdkNMV3JraURVdU9uUDVCZkN0czhZK1FvM1VHT0dnbWpZaDVkM3pWQTlJbzQxUE9QMjdwU0FoakpZNnJpNWIrYm96ZmYvdURGb0lJZFhrVDlYSmZzUVRPUkdheDg2MFpNTUFEMU1kS3E4VFlvbWVPVFpGQlRIMkZVUzA5SlhpSEo4dzd1MEJBekt5bkxKODNqVXUybnZuT21nWllUM01pMURlZFAwb0NHSWZpMUtQbkh2NUtCZ0dTbG11QWx2QVk5V3k5MEgrRWM2TlkwU013RC9nQmN0L1BtZjR6U2Q4dW9kcWU4ZTNMQzdjM2tBQ1dPTTJXV1o1S1BLQzBpUnozOXE5U3JBMStSaU1qYU1HaWhvWmhPTlA5VmJySm03ckZ6SDhqUHRQMVEzMXRPdGpQOWVpRWtWeWxIY1NnSWwiLCJtYWMiOiIzZDdlZjA0ZGQxNjViN2QxYTg5ZGI3MGQwZTViN2MxNGY3ODYxNjczZjMzMGQ0NDY1YjdjNmNlYjMzYzNlZDEyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}