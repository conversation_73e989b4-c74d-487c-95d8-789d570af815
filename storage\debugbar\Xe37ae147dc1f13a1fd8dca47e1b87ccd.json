{"__meta": {"id": "Xe37ae147dc1f13a1fd8dca47e1b87ccd", "datetime": "2025-06-28 16:03:42", "utime": **********.771205, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.357983, "end": **********.771219, "duration": 0.4132359027862549, "duration_str": "413ms", "measures": [{"label": "Booting", "start": **********.357983, "relative_start": 0, "end": **********.718046, "relative_end": **********.718046, "duration": 0.360062837600708, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.718056, "relative_start": 0.3600728511810303, "end": **********.77122, "relative_end": 9.5367431640625e-07, "duration": 0.053164005279541016, "duration_str": "53.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45697592, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027500000000000003, "accumulated_duration_str": "2.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.745386, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.364}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.757137, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.364, "width_percent": 17.455}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.762419, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.818, "width_percent": 18.182}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-442436213 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-442436213\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-380249592 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-380249592\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-502169513 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502169513\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1509540703 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126618953%7C20%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitoQXIrZUF5OGJKSisrQmRYY2hVVkE9PSIsInZhbHVlIjoiT1hIdlNrVERpenZDOWxxdDVZZmdjUngxNmhpMG5DZFdrVzB6dDZPRG1scDdENS9rQmc0OVZOTGxYOEZZRm9YaTg3R1g1N2tIODFSc2E3UlNtd2Z2ZDNjTEhJZXNHd3hIMHNMMktGa3hKK0N2NVczRU9UOHdtbUtsY211NGw2bDN1NlNlWlJXeHBsR2FQUWZTRU9aRmlOamU4VHpKQk1QS0hZZ2g1aHNLVDBpdWtZbThRWkdxcnhaNklPZ3QvQThoV2RwNFVtb2dUWU85d1hQMDEvSGxPSnFBV2d3QnhQcTJlMkJwRlFNUWpXK3pPdFFNZ2NNYjBrbldPVjhrSUxxdnhpWEUrQWYySUNPUDJnMkZ3YzVtdkhGTWU5eWd5YjYxNXNoYW5kZnJUaldQMGpJbStuMmFiR3h5Wmt6dTNTY3BtVHF5RFRUMFVwb0lQaHBLMk9FRWFUYmhkWCtRU1UxTVlkeHE0Tmk1VXdpVXBnNUZIdEFZeEFRQVowNXFhUXFiL3RNNWJLRmVKVDBxWkFERDBkeEU5RmFrVG52NWJIcSs4N3dGVEhTek1aVVRoY0k4cDBoY3Iyd3hRa2p6YmYzdWFmRVBSVEl6aW9kNGwzOE04NndtWjdTdDdObzZ5MXlNb21RMk1BcEpRcVBQK3RDYUZIUmtQWjdyNlpyWExGUmIiLCJtYWMiOiI5MzAwZjI4MDc5NTYwZTg5MDU1Y2Q2OTMwZGE4ZTQwNzRiM2FmNzgxMGVjY2IzMDBjOTdhZTdhYTY3YWEyNGU4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVXS29pUnd2bkVxcDRKRWgwanlwVkE9PSIsInZhbHVlIjoiZERPNkFWWmNlMEQwQ2tldEY2WmhJc0xPVzNCQW0wTWE1ZDU5eHpaMUpkU0owS3Z0UW84Wi80dysrdWpMQlhWZ3hiaWF3SDlDUHZVNnRVQkg3OWVWdjNjWTZuZ3dFSVZYTTQvL0ExWVd6ekt1TkhLZ3VXL3pXN1ZQSkUxNk9XaFNsZTVQQURWWlh6UTRqd0J1MGttUG4wMjJTWmN1b3FsazRkMEZEeXZJeDZ6N3VTWUMyUW1tUXRDbWk2WjRwdjlRaEpuZXByaUJ1dCtrbjBBbW93QUIyZzkwNURDQXpUM0Roa3NjNkJnZEFZS2o2QTJ4d0NEUFRQcDZuRXN6cHN1WkhwUkZ5czFUY3VyMGhxRlFxODhZREhqZVYrVENNRW5iRWJPSWVTK1pHR1JHZG1EUXBycEhlQXhGeWNwMG5OQzRxaGlWUjNOdGI2WUE0REZOakt5UkEyRFgyMFQweFdSc1FiKzBWb3RBUm1pSnIxUUdIMDFjTGNnNTBMLzdua3JTSm5sajIza0Rla0JpL3grdk9LN0ZjN1ZFUDhrcHhobHdMcDhscGs0VStubW1MYUlycDlqMS9JUzVZLytTV0xkamlHZDZHdzViNzZsS2JHbmpjUFc2eFU1U2tmR2R3K0k5SVNreGsrSGkyT1JLVlBoY1pZUnl0QStoQ2pqL2VIZWEiLCJtYWMiOiJlYjI4YjFkNTRhMDQ4YzMwMDkwNDVhMzFmOGVjNWFiZWVmOTdiMzA5OGRhYzAxZDQ5NWRjZThhZDAzYzU2NzkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509540703\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1417560945 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1417560945\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1462884971 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZ6NkpZanFiZko2dXErTWJ6WkdqTWc9PSIsInZhbHVlIjoibE00QU1FVXpBb1FjUUYvU3YxTng3cWtITHZaZFZVdmhsTE8yM2FybTRrUzFxbm5TNlZzNEhBTkxpakNlQi9xeU5BcHRyc0pmczQrMVpUVXBJeEZTeWdhaEhDTWZkc3Yyb1QwaVI4cXRXejhVUVlyUGN3MjBXSkpUVzFvU3YzTkc4S1pFUXBWSWV4R3N2YllQL0gzczNpWFZZbDA1NEh2VEVRUW04Y0xUdW1LMEZPNFVGeGhYc3kxT2RxdWZXakFHKzhFbUF1bUlZUzk1RFRKRXhoak0xdXZtWkg3b2twZjR2YkZOMW9CU1BPU0RBbWg1Nmk0Mi9uQ1BySUFzTXk1d1Y3VzFvYVhka3RUVEFtekErSW9SZ0JBd1R3N2h0cnkzM1hlUUphRHVxNERTeGpNc1dxQi91WnlPLzZDWFJxY29HSlpnV0w1UzlQQmxpZmhIT3dVb1p0Y1ZjUGxhUHY1RzRSOFdYbmRvTmJUMzhPUlluRFoxc1FER3VITCtCWVRSRkhFVGNCTzc5TXdyOEE5MXpCTVVjQk1ITS9lbUYrOG1paU9hak9mR3RZRFJnYTFyWHpBZzNWbzNjNEdmZ0dudVhOanBaMkR4V1grQlFwblNMd0lERVlhbngzcVVicG1UT251QU9Lanp2QXBtdTY0WkxXMmN5blZCa2NEcnk1RlEiLCJtYWMiOiI2OWQ5YzAwNzQ4YmM3Yzg4NWU5ZjM1ZmMxOTFlNzMwNzVmNzg1MzEyZTVmYjM1YWU0ODhiYTQyNDNjMjllMjBjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkVwbElHWW9XN1I5bVdxMGJGYXdqd1E9PSIsInZhbHVlIjoibXdUOXphRkowaFJDQkxNbmxXK2RUODlDb29pS1VLdUZ1NEswOCtlT3RlS3pvN1RxV1dZd1JieDYwazZIMmdmYjhhVVBvQ3M2T0hMS2R3bzZYYTg0dEpqVHJRVjd4SCtXN1VjaS9qdUMyZ1ExbHlpbGRsVzdzTDFwbVk1cUQ0c216Q1BJSjBEV05kOXI5NHRGVXEvcnN4d2hOS0lmQ2NkaG8xQW53cVV6b0hOQ2hqWG5RaVIrTVpMbFNnTmJQTy8rUENQcDY5QXE3OS9UT1hxUjVkNkZBbjRORFdNOFVlZHJZYSt2MWp6eEtjL3lGVVNVSmt1Q09YOWR3NGp3bDhQRGNKd3lnWDY5VXBIakg3b2l2cGhycVg1UGphNEQ4Qk5kM3dRVHpHWVhVdDYvRDFCajhmT1R0d0RWQ0l2b3FpYy9rdHl1QlJzSzhDQ3hrZzI4S2xxQm9CaDlYNlZvUm1jMnRieExEUnlvNitWUWVvOVdPWjBTZlJKWThUdDk2R0hEdjBDdmEyZmxJNmVPeWV4ODNaYllaWUIyd09UdENpdnEzSWx6M1R5ZFBuWkhWTXpxZFZuaE5CNk1vZ1FLdDdLRDJiM2xzUVBOUFlmS3hHdEJxT1doaGxUK0x4OVo1ZFdFWTBxRVExV2xiKzhaSW9nMXQ2Z2dvWDBFYVdGOGNNbmIiLCJtYWMiOiJkYjZmZDRiMjVlMWJhNmE4ZjUxZDg2YzA3Y2JmNTNlY2RmNTRkMDI0MjJiMjdiN2JmNmE2MDY3OGViZTQ1MmM3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZ6NkpZanFiZko2dXErTWJ6WkdqTWc9PSIsInZhbHVlIjoibE00QU1FVXpBb1FjUUYvU3YxTng3cWtITHZaZFZVdmhsTE8yM2FybTRrUzFxbm5TNlZzNEhBTkxpakNlQi9xeU5BcHRyc0pmczQrMVpUVXBJeEZTeWdhaEhDTWZkc3Yyb1QwaVI4cXRXejhVUVlyUGN3MjBXSkpUVzFvU3YzTkc4S1pFUXBWSWV4R3N2YllQL0gzczNpWFZZbDA1NEh2VEVRUW04Y0xUdW1LMEZPNFVGeGhYc3kxT2RxdWZXakFHKzhFbUF1bUlZUzk1RFRKRXhoak0xdXZtWkg3b2twZjR2YkZOMW9CU1BPU0RBbWg1Nmk0Mi9uQ1BySUFzTXk1d1Y3VzFvYVhka3RUVEFtekErSW9SZ0JBd1R3N2h0cnkzM1hlUUphRHVxNERTeGpNc1dxQi91WnlPLzZDWFJxY29HSlpnV0w1UzlQQmxpZmhIT3dVb1p0Y1ZjUGxhUHY1RzRSOFdYbmRvTmJUMzhPUlluRFoxc1FER3VITCtCWVRSRkhFVGNCTzc5TXdyOEE5MXpCTVVjQk1ITS9lbUYrOG1paU9hak9mR3RZRFJnYTFyWHpBZzNWbzNjNEdmZ0dudVhOanBaMkR4V1grQlFwblNMd0lERVlhbngzcVVicG1UT251QU9Lanp2QXBtdTY0WkxXMmN5blZCa2NEcnk1RlEiLCJtYWMiOiI2OWQ5YzAwNzQ4YmM3Yzg4NWU5ZjM1ZmMxOTFlNzMwNzVmNzg1MzEyZTVmYjM1YWU0ODhiYTQyNDNjMjllMjBjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkVwbElHWW9XN1I5bVdxMGJGYXdqd1E9PSIsInZhbHVlIjoibXdUOXphRkowaFJDQkxNbmxXK2RUODlDb29pS1VLdUZ1NEswOCtlT3RlS3pvN1RxV1dZd1JieDYwazZIMmdmYjhhVVBvQ3M2T0hMS2R3bzZYYTg0dEpqVHJRVjd4SCtXN1VjaS9qdUMyZ1ExbHlpbGRsVzdzTDFwbVk1cUQ0c216Q1BJSjBEV05kOXI5NHRGVXEvcnN4d2hOS0lmQ2NkaG8xQW53cVV6b0hOQ2hqWG5RaVIrTVpMbFNnTmJQTy8rUENQcDY5QXE3OS9UT1hxUjVkNkZBbjRORFdNOFVlZHJZYSt2MWp6eEtjL3lGVVNVSmt1Q09YOWR3NGp3bDhQRGNKd3lnWDY5VXBIakg3b2l2cGhycVg1UGphNEQ4Qk5kM3dRVHpHWVhVdDYvRDFCajhmT1R0d0RWQ0l2b3FpYy9rdHl1QlJzSzhDQ3hrZzI4S2xxQm9CaDlYNlZvUm1jMnRieExEUnlvNitWUWVvOVdPWjBTZlJKWThUdDk2R0hEdjBDdmEyZmxJNmVPeWV4ODNaYllaWUIyd09UdENpdnEzSWx6M1R5ZFBuWkhWTXpxZFZuaE5CNk1vZ1FLdDdLRDJiM2xzUVBOUFlmS3hHdEJxT1doaGxUK0x4OVo1ZFdFWTBxRVExV2xiKzhaSW9nMXQ2Z2dvWDBFYVdGOGNNbmIiLCJtYWMiOiJkYjZmZDRiMjVlMWJhNmE4ZjUxZDg2YzA3Y2JmNTNlY2RmNTRkMDI0MjJiMjdiN2JmNmE2MDY3OGViZTQ1MmM3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1462884971\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1115649010 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1115649010\", {\"maxDepth\":0})</script>\n"}}