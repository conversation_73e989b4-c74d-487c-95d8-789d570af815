{"__meta": {"id": "Xb5b9c32c6b311af08470bd18422ba679", "datetime": "2025-06-28 16:01:28", "utime": **********.123834, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126487.57346, "end": **********.123849, "duration": 0.5503888130187988, "duration_str": "550ms", "measures": [{"label": "Booting", "start": 1751126487.57346, "relative_start": 0, "end": **********.035337, "relative_end": **********.035337, "duration": 0.46187686920166016, "duration_str": "462ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.035349, "relative_start": 0.46188879013061523, "end": **********.123851, "relative_end": 2.1457672119140625e-06, "duration": 0.08850216865539551, "duration_str": "88.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03083, "accumulated_duration_str": "30.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.073861, "duration": 0.02634, "duration_str": "26.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 85.436}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.109349, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 85.436, "width_percent": 1.654}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"pr%' or `sku` LIKE '%<div class=\\\"pr%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;pr%", "%&lt;div class=&quot;pr%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.112688, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 87.09, "width_percent": 12.91}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-2103408332 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2103408332\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1912256981 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1912256981\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-388082847 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&lt;div class=&quot;pr</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-388082847\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-138660727 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">41</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZjU3Q4TFZzVldmQllGRlkwK3hSL1E9PSIsInZhbHVlIjoiRzB4d0RaeWcrOS9ZaktTdzhySXJLZkhOWHIyZnpBYUIwT0tJa0VCUHQ1V20vLzFoMDE5ZFovdjl2ZWtCOXRVNmRGRlVwb25hS1hvb0sxeDd2aDhKUi9vMC8wRHk2dWtId2RKdnZKaG1RSGRjai9HV3l5N2VoNXRWc0h5MDljSGpZVEh5SFBlTFhQOE5aV1N2d0tQK0w5Q3c0dW1GeldtOE9YMVp4Nmt4QURNVkRtQTgyTGRoNUtjcU91c2tjSEowSmNXT2t5bzVVNGRtU1QzN0l6WWh5UWZFQmJGRkxmR0JpaHYvcDlZZjRhYitmbzNBS0hmUXhRZFl6Zkp1VkdURHhjYVZ5elc3aUtDcHJ6VXJqNFlVaDZQRWVDVzFsQVRIUURrcFdvTEhxTnp0TWdxZzBWWEZMSndJLzREdUJjNlhDYUk3NlNOaXJnYzFzMzNlZE02ZE9jeVVqcHpQdlE3cE1kY2lmOVVnYnN0UC9vUFJ3VUhBK0tYeE5DSTMwb3pMbERTSnJXbjhCVDdScWZuMWh4eW9TOGo3cjFYdkxNYnI3RVRBVFRsT3RGMVBUcWlRWnNoQTlZMm9FWXZXWXVKT05mMlVmenc2MkJsN0YxYnUxaUVmSWtiYXN1djVrUXpwSUIyYmNiY0tCZWR2eGRrNlYwUFI4bVovTjdJOTYwVVAiLCJtYWMiOiJjN2YzOGY2NDBmMzYzZTM2ZDMwYTM4NTkzMGQ2MzlhODdlNmQ5ZDk3ZTkyOGY4YTEwZTA0ZjM4MDRmMmIzYjdmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtyK2hBR05mUVVuOHhMWVpvVmlyOUE9PSIsInZhbHVlIjoiNjdwcVpIenB1VU54cHowN1huWForMEtCMjJJdVRnV21JbFZKcjllUFh0MXZORHNOTHE5WU13SzF6cTZxSHhqL2hrcXRMV1NFTnlNTmpydkYvWVdZUmkwQkFaUWdrZTVYc25aZWpIVU8wSlhQbCtKK0ZlaCtyb3BFZ1B0SlJHY2RjSzV5WkwwZXdTb3F6UjhNSHRLT2hZVVRRT2tkWUhqbmR2Tm0vYUh4SnlzM1lSS29oTkZzTmxqTlZCaFJGMXkzMjRVaFZVQjNLSlJvTEp2K1Fla0VhbFlTZ25Jbkh0WHRSUkEyblBib2dNQmg2NUN3YWJiVlVIZ21qaFVlY09lZ24xN0JoRkJEOTRsM1Q0QWtiRzBMQU9QeWdYRjNEOEU3eGx3eUhhMFpsUlVEZ2d5VjVFczE1S3I5clc1MUYzOTd5cmRRSGZ4Um5GUTRmY0VQT0pvYWhYbzRJQU0xbldQeHVFRVpRWlBaNTBEVzVHWjJVaEVzcXZFZ2RQVklnR0hNRmNGVDdpWVJsTUNObXdGNktLL0VSODBVdFBhVE5OZjYwREdrY0xweWhabVU5NkVvQ09Ldk9idUVITG91RCtybzhGVW9oSVFCZkRlb2tqaEtxOHhjMzZSdklXTWlaMTJrTmF4Q2hXVnlzMTRkZEF6NjlmYnZtR1ZBL0pPWVMzZUUiLCJtYWMiOiIwMjU2ZGFjYmI2NzJlNmJjNWNmNTllYTg2MzEzODVlNzRjYTQwYmUxODkyODUxYmQ3OTFjOTY2MWFjN2JlNjZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-138660727\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-837953554 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837953554\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-503999265 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBQN2pmaU03blByUnpMSm1SdWVMV0E9PSIsInZhbHVlIjoibVROU280MTgvcXNUYnpXeFo4dExUMGd2MithSWJ1QnlzNG5GZFNNc0xTZ3FDV0YwMzNnV1Rodi9oOHVzTm1tWTRrc0tRWXdLaW5mNFU2SysyRHNZemZTMjhJTXB5blFKSXVDWFdYdUdWc3huM2hZU2tqcXA3K0V6N3NQUHNuU3ZQMzI5d29leGFuOXBrRlVhV2JCeUdWVk5KcGpFdmc3MXRwRzhxczM4S2JSbTJiNStkUCtHa0o2RXowelQ2OFlBeFg3MDVLZEJFbG9qSUZSTDJ3Smljd0ZDMEE3Wi9mWENBcy9yczVMK3phMXA4ZzNUQ0ovU1ljNWdUUStOenRLNkhhM3l4bm00dzFIU0ptUlQ0OTA2emRRWENUbSs4Qm9HRmJxNDcxbUIvSGhwTmxtNUUwNXVxNnRCZVpES21idkY3L0F4SDU1R2lXR2xUTkFWc1VJVmRVbGxrdlNnakNpRjcwS0NBcll1dW44VEthbDk3MHgwc1psNnYyNlQxRmZwQnQyNEhjRXBlWW53ZDltS2FMYk9nYVhLZkhLUHJDcWh1ZXd4Ykk2UEd3QUJEeW9hWnhyanhlcVJBN1U2S1R6S01CL2E5UTQwOWozSFFiZmhJK044OXRVSlVTU1dGbFZSUW4wY2Fmc28rcVpYcWRhYkM4eWhiUHdkdHVZMUdQMnIiLCJtYWMiOiI3ZWMwYTY4ODE0MGE3MTg1M2JmZTMwZTdmYzY1ZTkyZGIxYWZkYWNhYzQ0ZGJjZjBjZTFiY2I1ZGVhNDg3ZGM5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IllaMDdtQVhOcXJDdXFzS1NUOHdqNWc9PSIsInZhbHVlIjoiWUNPbklFM2lDejN2N0hJK2VrdTBSNFVjeVRZTDFQSXdtcnJRQjdyeG5Fd29NZW5iS1pZcDlFVmswTGNia0x3SFBueUw5ZkJOTE95QmJlM284T3JTeUhQdlN6ejgrMEtwbkJkWlV3RG1hNTV3SzJGWUYvZWFKMWVoNWkxL00xVW9HM29ER2JoL2RkZFdkL2xmZDQ1azlxd3hjYzdBbkpGdUVGOTA3Tlp2MkZTcXMwdHFvTFc3ZUR6OCs4TzdhcFZMWUFsUHg0Ni8xb0h2OXFFNytWSk9iNnVUVkRXK0FEbkIyRnZjc094Z05KQlk0Zm5INys0UklVb3RuM3VpdlE2WjFiVDhoVGtZanBOb0dCYkY1Zzhod0ZjT1NVTXVkR3hIMDdPakxzaG1TMUJTTEViNC94Snl5Mmp0T1M5SURCZ3Y1NThPZFVpbzdFRXNLSlZJQVdNc0VuNUlMUnlPL3phaEErVmhXcUVjbGhyazdHb29xdWxPN2pON1oyL1dBcmo0ZDNCWmY2R0xRTms3dThjdml3MmMzaW5KbUdnZ3dOY0tMRHVIcm5YcXNFQlYxNFlCUmZTSjlmY2dkT3N6d1EzOG9Ca1UyMytPRHFUZWwxaDAvMFo1SjhNTmZPcW9SU25MaWdhM2pFbTRPK2QxR3BBaEtLaHhRQVBRSE55dEdFT2kiLCJtYWMiOiIwMTZmY2ZlY2RlYjUxOWY3MjYzMjQ3YTYxYzQyZTZiYTM4YjNkNmNmY2EyNjZhMmI5ZTdhMjhlYWJiM2M4Nzg1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBQN2pmaU03blByUnpMSm1SdWVMV0E9PSIsInZhbHVlIjoibVROU280MTgvcXNUYnpXeFo4dExUMGd2MithSWJ1QnlzNG5GZFNNc0xTZ3FDV0YwMzNnV1Rodi9oOHVzTm1tWTRrc0tRWXdLaW5mNFU2SysyRHNZemZTMjhJTXB5blFKSXVDWFdYdUdWc3huM2hZU2tqcXA3K0V6N3NQUHNuU3ZQMzI5d29leGFuOXBrRlVhV2JCeUdWVk5KcGpFdmc3MXRwRzhxczM4S2JSbTJiNStkUCtHa0o2RXowelQ2OFlBeFg3MDVLZEJFbG9qSUZSTDJ3Smljd0ZDMEE3Wi9mWENBcy9yczVMK3phMXA4ZzNUQ0ovU1ljNWdUUStOenRLNkhhM3l4bm00dzFIU0ptUlQ0OTA2emRRWENUbSs4Qm9HRmJxNDcxbUIvSGhwTmxtNUUwNXVxNnRCZVpES21idkY3L0F4SDU1R2lXR2xUTkFWc1VJVmRVbGxrdlNnakNpRjcwS0NBcll1dW44VEthbDk3MHgwc1psNnYyNlQxRmZwQnQyNEhjRXBlWW53ZDltS2FMYk9nYVhLZkhLUHJDcWh1ZXd4Ykk2UEd3QUJEeW9hWnhyanhlcVJBN1U2S1R6S01CL2E5UTQwOWozSFFiZmhJK044OXRVSlVTU1dGbFZSUW4wY2Fmc28rcVpYcWRhYkM4eWhiUHdkdHVZMUdQMnIiLCJtYWMiOiI3ZWMwYTY4ODE0MGE3MTg1M2JmZTMwZTdmYzY1ZTkyZGIxYWZkYWNhYzQ0ZGJjZjBjZTFiY2I1ZGVhNDg3ZGM5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IllaMDdtQVhOcXJDdXFzS1NUOHdqNWc9PSIsInZhbHVlIjoiWUNPbklFM2lDejN2N0hJK2VrdTBSNFVjeVRZTDFQSXdtcnJRQjdyeG5Fd29NZW5iS1pZcDlFVmswTGNia0x3SFBueUw5ZkJOTE95QmJlM284T3JTeUhQdlN6ejgrMEtwbkJkWlV3RG1hNTV3SzJGWUYvZWFKMWVoNWkxL00xVW9HM29ER2JoL2RkZFdkL2xmZDQ1azlxd3hjYzdBbkpGdUVGOTA3Tlp2MkZTcXMwdHFvTFc3ZUR6OCs4TzdhcFZMWUFsUHg0Ni8xb0h2OXFFNytWSk9iNnVUVkRXK0FEbkIyRnZjc094Z05KQlk0Zm5INys0UklVb3RuM3VpdlE2WjFiVDhoVGtZanBOb0dCYkY1Zzhod0ZjT1NVTXVkR3hIMDdPakxzaG1TMUJTTEViNC94Snl5Mmp0T1M5SURCZ3Y1NThPZFVpbzdFRXNLSlZJQVdNc0VuNUlMUnlPL3phaEErVmhXcUVjbGhyazdHb29xdWxPN2pON1oyL1dBcmo0ZDNCWmY2R0xRTms3dThjdml3MmMzaW5KbUdnZ3dOY0tMRHVIcm5YcXNFQlYxNFlCUmZTSjlmY2dkT3N6d1EzOG9Ca1UyMytPRHFUZWwxaDAvMFo1SjhNTmZPcW9SU25MaWdhM2pFbTRPK2QxR3BBaEtLaHhRQVBRSE55dEdFT2kiLCJtYWMiOiIwMTZmY2ZlY2RlYjUxOWY3MjYzMjQ3YTYxYzQyZTZiYTM4YjNkNmNmY2EyNjZhMmI5ZTdhMjhlYWJiM2M4Nzg1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-503999265\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1693365453 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693365453\", {\"maxDepth\":0})</script>\n"}}