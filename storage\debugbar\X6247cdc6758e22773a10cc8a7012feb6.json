{"__meta": {"id": "X6247cdc6758e22773a10cc8a7012feb6", "datetime": "2025-06-28 11:23:35", "utime": **********.098405, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.538249, "end": **********.098422, "duration": 0.5601730346679688, "duration_str": "560ms", "measures": [{"label": "Booting", "start": **********.538249, "relative_start": 0, "end": **********.875517, "relative_end": **********.875517, "duration": 0.3372678756713867, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.875526, "relative_start": 0.3372769355773926, "end": **********.098423, "relative_end": 9.5367431640625e-07, "duration": 0.22289705276489258, "duration_str": "223ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50180256, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.08274999999999999, "accumulated_duration_str": "82.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.905277, "duration": 0.01927, "duration_str": "19.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 23.287}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.932023, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 23.287, "width_percent": 0.628}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-28 11:23:35', `closed_by` = 22, `shifts`.`updated_at` = '2025-06-28 11:23:35' where `id` = '50' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-28 11:23:35", "22", "2025-06-28 11:23:35", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.020761, "duration": 0.060149999999999995, "duration_str": "60.15ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "kdmkjkqknb", "start_percent": 23.915, "width_percent": 72.689}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-28 11:23:35' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-28 11:23:35", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.082437, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 96.604, "width_percent": 3.396}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "success": "Shift Closed Successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-2035861457 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2035861457\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1805183989 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1805183989\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1018398346 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">50</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1018398346\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-173390773 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6InB2K2FnY3FXUmtnM0dObEJmM0RvNnc9PSIsInZhbHVlIjoiSmZ6ZVVqdC92UmdsUW0yZDZCd3ZoZW44OGplZHJKaVdheG81Y2o0N0VIVzB0NVY3bjFYU0g1U0JBdkJDQ3ZrQmlJRTVLOWZjS29tWjFFN2J0elBKQmVHcjJUOE0xWW5ocVVZM09DVStxZzRUQm5KY0Y5Y1IzN2phVUJTb0MwSHV3a2xIcGxEZTI3SDU4TXlhSXZjMWplbEZmUjJTRDM3TkQvVnFyZWpEUlBQdUxBZWoyblFYNm92a0FURitqa2EzZWp4ZVViK2lIKzN2TUk4aHJIbnF3MlB3cnFJRjcya1psaWpjK2J3bDV0SDdMbDJablF1anAvdUlNaGgrNUNjU2tDUWxRNDI2bDBrZ2NQUUlWcVA1WlcrWjdhazJwR2NENTByeVEzTVJBMVd1Vmh5bk5VYlBHRDEyTFk2M3RyRmY2V2JzanIzZWM0OHlVYXRhdk0renpMMnZNZFNERk9wRkFIQWZZekxQai80eWx4M2F0QmxEUXR5UFF1TGRHY2pBdnd5ZnhmeGh5bWtYOC83TzJySldvRGJEWjVnU2puRmVKb3N5V2pSZjdNcmp4bmtqZHBNZmZ5ZGhxaUhocWdPSnFjbmZyRU5IdWdkdjFYdE5VUWc1ZGV3K3kzTEMrUG9iSUpySkNTQ3c5amliQ1llSXZPWGVlNVp0T01vb2xMMk0iLCJtYWMiOiJkNDczMDg4NTY5Yjc5ZDIwMTAwZTRkOWNkYTgzZDRmMDFmOGM5YzU5MjIxMDVhODBhMmExZjhkZjJiOWQ5NTI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJLNWpPZHNwQjFXeHBrMTNOUS9ONVE9PSIsInZhbHVlIjoiK3RjWW9LdmZDdDk3QkVRQWppbWxjaWRwZ1RYYzlTamxUMEwrZUp5aUZ1d25ySElLQjVLbG9OY1ZBaTgrMFVrTXNsaGQ3QmFNMFZvT1lCOTVTZmxLbzBxZkpnZG42WlIvZ0hhWldOSmxyNkdweGVGQ2M2Zm9PVjcwblNSVWh6WlBhOG9ncFIzc1FaeHpqWDZkbVBpSlR6UWVaRzV5QjRWRWR1OFFUTVJ2ZmNwK2pKaVZFaVhiQ3pMa1lGa3BIb2xhUDlMVWxRaFFKVFN4UHIxSTRjZUthYU1EQmhsK2pZcmJLTWZMaXFmRUdzVDVpNDlqclZ4b2xaUmhiQzF3cmhhRnBYMG16NFkzdVdraENKWHE5TVYxRFBjbzgyVTVZVXVnay9UWlRMK3FJRUlwTlAwdjRWR0xqcEx2alFISUFld05pc0hIa2s5UDd4NEt3UG9oR2JCYUtKUEVhM2hMbDFNY1I4R0RVSFZoeUJ3eENpekxuTE9Zei84YjJYY1RldGo2WU9iZTJCYnoyNnJrZDVwWmkyQ1VDUEU4RjNpMXRvOVpQakdHRU8ydFJlSVBwalJFdk5HcWkzNzB6SkU5ZVl0ZGtxdlpudkgzdjhJcnJnaGU1MmNMYndJcXQxdzhTMWI3eG1Ga2tZVDBtb25JOUZiU0NrczAxQkdZSlFxUWNnVzMiLCJtYWMiOiJiNDM5YzBiZjM3M2U5ZGZkOTQyYzczNjAxZTdjMGExOGNmZGZjYTQ1YTAyMWE4NTJjNDEyYzIxMjZkNjBhNGNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-173390773\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1620830436 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620830436\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-111808432 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlI2YUswSXJxbzZKV1hkSlQ5OTFGcVE9PSIsInZhbHVlIjoidnJicDA3U0JMN0p1OTVtSXkzR0diK0hYMmZSQ0RSOC9BTVN6bUhlZXMzd2QrQysrZ3QxQnhBdlBIQ3Z0cmZ4QndVaVAwU3c0ZjM3N0g0TWp6cTlQOTNTQ1M0amIzcEFaWFlxZ1VyZllhKzA0aWRzRWErb1lDcnNXVzhrZ3dTdVo5NGE5cmVjUUw4TldEaERGbytoSFE4YTR0Y2RJYU9nK3djcUhydnhKdDZacHlwY0xXL0NwQ2lmcXErRGdTQTVZTnhQQ3NGNWp2bGgrYTMwQ20yMTFEMm4xa0w0Q24rTDZuM0FOTENFKy9BejZXMTYyaXBIVkRvZFZiU0dFdGUyLzYrdEs3K2NHaUxaWDBXWGVCcTZURUc5QVEwSTVCazZwU2Y2dXBSV1N0UkVadGZoT2FBK3NZRmtUSTYvOXdsRzYyU0l4MTBOQjJTbXltUDFpazhxb2RFcTlJYXZyaWx2MURxTU5YeUtITmlPY083RHZZNmpXelYzUVkyZExLQTNoaTVWc28zMEtTVUxVOHBMbmw5b3FaaU1UTGRuZzR2U1FzZThTK0NRYUsyVTZGcm1IMWRVL1NuNzI2WExPLzBnckkwZ3BVYXBZNFVGSEQ1RWJPeGFlSkEyZllCVmhGSm94aWI4Vlhyb2tuZTdvenlieUZxQ2haeVJVL0hmMVE0TXIiLCJtYWMiOiJlNDE3ZjhkNzkxZjNmMjI4ZTc3NDhmYTE5Y2E5ZmJmZjc4MjFhNzFlMTVmOGE1MTQ0OWI2YjU1NjVmOWM0MjJjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkoxcXd3d2F1Um5MR2FlUzE1QXB1UHc9PSIsInZhbHVlIjoiWHAzVDlsd1V3S3lyV1FMYzJmbDh2WHhVRUZYaUNVOXVMYlRJcXIyV29VbDdYUkJ0dGs0SkVYYkIrZ3FPYzNFaG1hbWdQN25vYUtBL3lReEJXL1RXazQzTjNxSHNmaUF4bEpxYVQ5TGtqSDlqeTFFN09xcXY5WWlIdnEva1ExK2JrSlBYR2RqcGlGTXhVL3YrYnV3Nm45SDV3cFVqdlhNSlh6V2JHb21GaFZqenV3eWtvdGhtWHZNQ0tmMFJEYmgvaWU2VW8vdGFLMng5cWl3eC9oaERPUnlJSHFNdlQyeEppWm5mNWt5NFpwWXFnbVBUMjlUS3I0RmVRMmZXNXFCK2FGanZIOUdHYXFYbFRUQlNaSmVvOWhwa2g0U1JINUtZQ3NCZExTWHppYlFjN0N4ZWg3L1ZkNVZseXJJQXNHazYzUWdYZkpPUEFIRmZTdkE1eENaN093bDg4cFlJN253SUtlWUdremtZRllvTld5R21vc2FLdnZEQXlaSzErVlpSciszUXdlLzBiMFRQZVViMW5qUE9XV2t4cHo2T0Q2ZzlBUGRqVkQ3UXJGU1ZKRTgwT3BOQm1IMzNLaUpsazJFaDlrVzZYdmgyazNQSDNyRmFUd01sNTg0S0JlN2MxeThGYkkvWkFoVVNzeHRtVEF1T2hrR0lEOGRzdzFGMnBoQ1IiLCJtYWMiOiJjNjMxM2ZkNWJiYWUwMGFmNjY1ZmVhM2UyYWM4NmI1YTA5Y2M1YmFmMDRhNGY2YjJlZjc4NDE5NzBkNjVlODQ2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlI2YUswSXJxbzZKV1hkSlQ5OTFGcVE9PSIsInZhbHVlIjoidnJicDA3U0JMN0p1OTVtSXkzR0diK0hYMmZSQ0RSOC9BTVN6bUhlZXMzd2QrQysrZ3QxQnhBdlBIQ3Z0cmZ4QndVaVAwU3c0ZjM3N0g0TWp6cTlQOTNTQ1M0amIzcEFaWFlxZ1VyZllhKzA0aWRzRWErb1lDcnNXVzhrZ3dTdVo5NGE5cmVjUUw4TldEaERGbytoSFE4YTR0Y2RJYU9nK3djcUhydnhKdDZacHlwY0xXL0NwQ2lmcXErRGdTQTVZTnhQQ3NGNWp2bGgrYTMwQ20yMTFEMm4xa0w0Q24rTDZuM0FOTENFKy9BejZXMTYyaXBIVkRvZFZiU0dFdGUyLzYrdEs3K2NHaUxaWDBXWGVCcTZURUc5QVEwSTVCazZwU2Y2dXBSV1N0UkVadGZoT2FBK3NZRmtUSTYvOXdsRzYyU0l4MTBOQjJTbXltUDFpazhxb2RFcTlJYXZyaWx2MURxTU5YeUtITmlPY083RHZZNmpXelYzUVkyZExLQTNoaTVWc28zMEtTVUxVOHBMbmw5b3FaaU1UTGRuZzR2U1FzZThTK0NRYUsyVTZGcm1IMWRVL1NuNzI2WExPLzBnckkwZ3BVYXBZNFVGSEQ1RWJPeGFlSkEyZllCVmhGSm94aWI4Vlhyb2tuZTdvenlieUZxQ2haeVJVL0hmMVE0TXIiLCJtYWMiOiJlNDE3ZjhkNzkxZjNmMjI4ZTc3NDhmYTE5Y2E5ZmJmZjc4MjFhNzFlMTVmOGE1MTQ0OWI2YjU1NjVmOWM0MjJjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkoxcXd3d2F1Um5MR2FlUzE1QXB1UHc9PSIsInZhbHVlIjoiWHAzVDlsd1V3S3lyV1FMYzJmbDh2WHhVRUZYaUNVOXVMYlRJcXIyV29VbDdYUkJ0dGs0SkVYYkIrZ3FPYzNFaG1hbWdQN25vYUtBL3lReEJXL1RXazQzTjNxSHNmaUF4bEpxYVQ5TGtqSDlqeTFFN09xcXY5WWlIdnEva1ExK2JrSlBYR2RqcGlGTXhVL3YrYnV3Nm45SDV3cFVqdlhNSlh6V2JHb21GaFZqenV3eWtvdGhtWHZNQ0tmMFJEYmgvaWU2VW8vdGFLMng5cWl3eC9oaERPUnlJSHFNdlQyeEppWm5mNWt5NFpwWXFnbVBUMjlUS3I0RmVRMmZXNXFCK2FGanZIOUdHYXFYbFRUQlNaSmVvOWhwa2g0U1JINUtZQ3NCZExTWHppYlFjN0N4ZWg3L1ZkNVZseXJJQXNHazYzUWdYZkpPUEFIRmZTdkE1eENaN093bDg4cFlJN253SUtlWUdremtZRllvTld5R21vc2FLdnZEQXlaSzErVlpSciszUXdlLzBiMFRQZVViMW5qUE9XV2t4cHo2T0Q2ZzlBUGRqVkQ3UXJGU1ZKRTgwT3BOQm1IMzNLaUpsazJFaDlrVzZYdmgyazNQSDNyRmFUd01sNTg0S0JlN2MxeThGYkkvWkFoVVNzeHRtVEF1T2hrR0lEOGRzdzFGMnBoQ1IiLCJtYWMiOiJjNjMxM2ZkNWJiYWUwMGFmNjY1ZmVhM2UyYWM4NmI1YTA5Y2M1YmFmMDRhNGY2YjJlZjc4NDE5NzBkNjVlODQ2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111808432\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-45159871 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Shift Closed Successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45159871\", {\"maxDepth\":0})</script>\n"}}