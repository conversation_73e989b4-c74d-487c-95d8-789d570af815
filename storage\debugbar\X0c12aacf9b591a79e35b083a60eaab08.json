{"__meta": {"id": "X0c12aacf9b591a79e35b083a60eaab08", "datetime": "2025-06-28 16:03:47", "utime": **********.160633, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126626.756989, "end": **********.160648, "duration": 0.4036591053009033, "duration_str": "404ms", "measures": [{"label": "Booting", "start": 1751126626.756989, "relative_start": 0, "end": **********.107805, "relative_end": **********.107805, "duration": 0.350816011428833, "duration_str": "351ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.107815, "relative_start": 0.3508260250091553, "end": **********.16065, "relative_end": 1.9073486328125e-06, "duration": 0.05283498764038086, "duration_str": "52.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45697616, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00287, "accumulated_duration_str": "2.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.134933, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.157}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.145264, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.157, "width_percent": 19.861}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.151937, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.017, "width_percent": 14.983}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1799474010 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1799474010\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-416816254 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-416816254\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-20625396 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20625396\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1729702414 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126622716%7C21%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFsUnJRVi9vdnU0T2dxUjhoVEJKR0E9PSIsInZhbHVlIjoiOCtoYVJMWG8rWWJBSE1IN1RwaTVFaTJpSW5CK05NMXEveHNpMno3a3B5VzNhdkRrU0RXUVJBK2lPWnpERWMxblcvZUVNN1F0UktGV1E4UHNaVDNVd1dIUmFoeURIcXN4YmUvUjgvb1ZSZHliUEY4YW9qSENLazRoOVozTng4YkdLNzVCSWI3K3Qwa3RSWk9COVpYdjU2a1p1TFVKK0JEWURJUTJjUDFJMjEvcDB2V1NyNjZLelBiQUhnMHRzRnA4aUlOajFZMFBDWlpxeElyNTc5QkhCTlFzZFNFTk9BRXk0a3ZINjR5R3ZuRnpJajBIN0lDSVlaSlZ6SVgxSll3SE40NW5DaUJJRkt2WmNCMUI2RjdoVVJIWnVVYVRJU1dKY0g4a2ErQmo2cEdidDQ1MkJNT3FtcTRUSllWN252L1hCaVdWS1pCVnhDRmN3Q3pkelVsZEV6NmMwTE1yNjJ6SFRJNXFSSlgrOS9WMng0ZnVMVFNwQXBXUnRjZWQyMUNmR2RtQjVERVRhbUZrMmlOYWdydnpncjhTOGx1RVNhTGVsdFMrWk82eVdjSGdGZFQ4MTZHNDgrSE15THpNd1V3a2dFTm1nM3k5NTZaSmVvVklsSTZNc3JvaXkwYVVOaU9ZczA1QUZaaVZvbXBFejJrM1IrT1ZwYWgrQTJ0NjZ2TjQiLCJtYWMiOiJlNDhhYTg2M2QyMjk1MDkxMzBjZjVmYTcxMmZjYzBhYjkwOTcyYzNjM2Y0ZWQwMjQyYzBkYjU3ODM1ZWUwZDMwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklGWlNHaXpYL3lrUlB3TExZeHpCVGc9PSIsInZhbHVlIjoiMzV5bEtVL3d3bDJnUEd6YlVTZGp2eExleTlkbDVDY0R0RXVIczJmRnpTLzN0UmFVb0EvdkpMRTdGM2RLandVa0ZYVGZPekV5ZkFrcUY5MFg2bnFpK2xDbEVINDRlZTdyUFhkQUgrR2xNOHBGbmc2Yzd6UExjL0dHVVdhYWx2T0hoOHB3aHA4TDJiZC9JSmQvdVlHTjNZMTRTMyt2MVpVRWxGeUlsaGwxSWdEYm1TeGdlR1NJV2JvV0E3SXJQVm45SW5kYjhzRFNpSGRJVFhNZ09pVU55QWxQai91YjZPUnl4Z2ZWLytiYkxpYWEvUHlWWXRnWkZpUzZ2MUVuVGc1cUV4blhteWdDejBYMXhFL1pwdkd1bWVVYlRNRTJKMEZwYWY2RGNRWmVxbGxzSTNXUWw2ODBZYVhhc01QVk01TXpGUzFVa1Y5Y0hERU1Hc08vZHovcXBQZ2VhK3VQR252SVVGK2YrV0NKNVRkenk5d2d5OEJ3aUxEWkdVMnllMlB6QS9DV2dnbnZLejJ1VEVJSVE3eEZqQytnT2tPT2VidWRkZG8wMkhsYm5ibzdMU2RvdDlESWhTakVpY2FPTWptVW1JNFc1TisyMCtqQTJWdjE5TTlsaXN0L2RDaEhna2d1Y2h1ZkI0WjFLN085aVVvMmxCYkZXZ0hCZHJXbWpZOUgiLCJtYWMiOiIzNDkzODU0NDIwNjVhYmY5NzkxOTkxZTJmYjk5MDdhNTliNDIxNTM5NDFiMTA4NWIzYzhiMjFiNjNjNGU3ZmRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729702414\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1342426713 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342426713\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1863876534 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBsWktUTkRaTUw0YkQrbVpRbWpQSVE9PSIsInZhbHVlIjoiZjhwaTk4dlo1T0FJalNjZlZxTnNSVEFwdUxXZ0QrZGRzc1Z6MVR2RWt1S3NsbWZPZndMZ1kraG9LNGlHUUxJYXorR3ZSYlpsT1VBcFFxdWtqK3IwOUx1cTllYkMyM1RjRE9sZEhncnA4MTdCWGc4WTRvRW1nMHYvYzRZcEdrRTJKbUcyZWgvTjZjWDBhSzFkYmNhYURhWDVhYktDSWZXSkg0UTI5Q1dwV29OTHhidk1QZnI2MXhGck92WU5oZjVUYmZ1U1hlOVFDNDdqRkh6NThidkliRklYQVhLNkpzUVNwUDVKMTg0Z0EveUJpczE4YXhSNXhaN0M1eU9GZFBuSkIyLzF5WHUzY1A4ZHEweGxEQW9FT2d0dGdsbU9lV3dLMHpaQ2Z6OGVBNHkrK2xtS0IyUXkzaVd5WFB4RnNvR3RObWlYWVNTMERYZEI4THU4aFNNNzhTWXRvMHBpMUtGTHhsR2o1UzBoanFYY0Jxd21PK3BSTDFHTm9uRVd0QXY3OGhrQmdtOFIyVG93WXRudVVrMzltWHd4c1lKZTZORWhnNVpHZnBXeHhIdjZtL3ErNmhXeThZUGZORW5Rdzc5TENoZC9xM3IvVVJsQVQ4NUVvTmlJTTJLMXYzTWZzckRvS25RazFINXVWV0lsbXJ3NXNyMVlBdEF6cjZUNzVsTUEiLCJtYWMiOiJiZTgyMzFiZjMzNWE3NWE2NmVmYzRjMThmNWQzNjYzOGVhYjI5N2Y2NWFkZmVjNmM3ZDcwYTA3M2NmMmEzMjBlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InR3RFQzeVZDanVZSzFMUUM0bWJOZUE9PSIsInZhbHVlIjoiUEFDWmRmMXRaa1lkTzl6eDZ6WVdyd1BtT09ZOG54L01hZlpBemJ4WkhaTHg4V2hpK2VsQWxleWM1N2N2WHI3TUJzL2gyWXZVS3cxd2RiZ2sybUdqVUFzS2JhZkhxYWY2ZVVPTGpSdEwxU1pIUndEWEZDNy9PQXhZK1ltZ1BPdlo1cHpORUJ4NUtYcEIwajVQVTBXbFpmbGw1UWhJOTE5amVvaEtrQjFNSEZJQ2VVaEU1eUtSTzN3SzBkNldUNkdZSjc5SGZPak1CWTZUSDFlVE1FL0ljdHFTdHRRaUJ6TnJuVTNJM0pRdm9xei9neDhobVEzS2FFak5mejJhSTBnejhVR0toQ28vRGNhU3dwS2tremk2UjNoSUVZUFFvY1dmRGE4QVdqelFORFc1TEwxRHdYRkNwR3Z1ajZqZmhTOFh6aFUwY1QreHo3QVhtOU1HN25tRXdhNzdZUjMwbkVQNlI4MHB5QnRac3NnbVVJckNmbzl6bEVoUGt3UEd3cFEyQklwL0p3WmZSSG45VjdhNS9XQW92RGE1Rkc0NkViaEhPQTVjUHVYUEZqdHQvaEphODEyV25URlc1Wit6MmxxNUJ6eDdEUVBhV3A2NTdaa3U5U0ZwWHVydWtabnVjSWxHMDRRZ3RiUUJTWWtzMG9pRXBhQjd3RkMrQ0JZNG90ZFQiLCJtYWMiOiJiNzgxNGI1NjEyNjcxZDAyZTBkZmE3MzViMzJiMGNhMTM1MjlhNDJkYWE2NTg4NjY5ODM3ZmZkYTc4NTk3NDhhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBsWktUTkRaTUw0YkQrbVpRbWpQSVE9PSIsInZhbHVlIjoiZjhwaTk4dlo1T0FJalNjZlZxTnNSVEFwdUxXZ0QrZGRzc1Z6MVR2RWt1S3NsbWZPZndMZ1kraG9LNGlHUUxJYXorR3ZSYlpsT1VBcFFxdWtqK3IwOUx1cTllYkMyM1RjRE9sZEhncnA4MTdCWGc4WTRvRW1nMHYvYzRZcEdrRTJKbUcyZWgvTjZjWDBhSzFkYmNhYURhWDVhYktDSWZXSkg0UTI5Q1dwV29OTHhidk1QZnI2MXhGck92WU5oZjVUYmZ1U1hlOVFDNDdqRkh6NThidkliRklYQVhLNkpzUVNwUDVKMTg0Z0EveUJpczE4YXhSNXhaN0M1eU9GZFBuSkIyLzF5WHUzY1A4ZHEweGxEQW9FT2d0dGdsbU9lV3dLMHpaQ2Z6OGVBNHkrK2xtS0IyUXkzaVd5WFB4RnNvR3RObWlYWVNTMERYZEI4THU4aFNNNzhTWXRvMHBpMUtGTHhsR2o1UzBoanFYY0Jxd21PK3BSTDFHTm9uRVd0QXY3OGhrQmdtOFIyVG93WXRudVVrMzltWHd4c1lKZTZORWhnNVpHZnBXeHhIdjZtL3ErNmhXeThZUGZORW5Rdzc5TENoZC9xM3IvVVJsQVQ4NUVvTmlJTTJLMXYzTWZzckRvS25RazFINXVWV0lsbXJ3NXNyMVlBdEF6cjZUNzVsTUEiLCJtYWMiOiJiZTgyMzFiZjMzNWE3NWE2NmVmYzRjMThmNWQzNjYzOGVhYjI5N2Y2NWFkZmVjNmM3ZDcwYTA3M2NmMmEzMjBlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InR3RFQzeVZDanVZSzFMUUM0bWJOZUE9PSIsInZhbHVlIjoiUEFDWmRmMXRaa1lkTzl6eDZ6WVdyd1BtT09ZOG54L01hZlpBemJ4WkhaTHg4V2hpK2VsQWxleWM1N2N2WHI3TUJzL2gyWXZVS3cxd2RiZ2sybUdqVUFzS2JhZkhxYWY2ZVVPTGpSdEwxU1pIUndEWEZDNy9PQXhZK1ltZ1BPdlo1cHpORUJ4NUtYcEIwajVQVTBXbFpmbGw1UWhJOTE5amVvaEtrQjFNSEZJQ2VVaEU1eUtSTzN3SzBkNldUNkdZSjc5SGZPak1CWTZUSDFlVE1FL0ljdHFTdHRRaUJ6TnJuVTNJM0pRdm9xei9neDhobVEzS2FFak5mejJhSTBnejhVR0toQ28vRGNhU3dwS2tremk2UjNoSUVZUFFvY1dmRGE4QVdqelFORFc1TEwxRHdYRkNwR3Z1ajZqZmhTOFh6aFUwY1QreHo3QVhtOU1HN25tRXdhNzdZUjMwbkVQNlI4MHB5QnRac3NnbVVJckNmbzl6bEVoUGt3UEd3cFEyQklwL0p3WmZSSG45VjdhNS9XQW92RGE1Rkc0NkViaEhPQTVjUHVYUEZqdHQvaEphODEyV25URlc1Wit6MmxxNUJ6eDdEUVBhV3A2NTdaa3U5U0ZwWHVydWtabnVjSWxHMDRRZ3RiUUJTWWtzMG9pRXBhQjd3RkMrQ0JZNG90ZFQiLCJtYWMiOiJiNzgxNGI1NjEyNjcxZDAyZTBkZmE3MzViMzJiMGNhMTM1MjlhNDJkYWE2NTg4NjY5ODM3ZmZkYTc4NTk3NDhhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863876534\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1061933714 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061933714\", {\"maxDepth\":0})</script>\n"}}