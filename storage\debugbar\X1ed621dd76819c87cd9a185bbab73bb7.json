{"__meta": {"id": "X1ed621dd76819c87cd9a185bbab73bb7", "datetime": "2025-06-28 15:26:59", "utime": **********.483642, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.028448, "end": **********.483656, "duration": 0.45520782470703125, "duration_str": "455ms", "measures": [{"label": "Booting", "start": **********.028448, "relative_start": 0, "end": **********.403694, "relative_end": **********.403694, "duration": 0.3752458095550537, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.403703, "relative_start": 0.37525486946105957, "end": **********.483658, "relative_end": 2.1457672119140625e-06, "duration": 0.0799551010131836, "duration_str": "79.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49506128, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02356, "accumulated_duration_str": "23.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4392078, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 7.81}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.450006, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 7.81, "width_percent": 1.91}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nAND p.warehouse_id = 8\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`warehouse_id` = '8' order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.452965, "duration": 0.02127, "duration_str": "21.27ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 9.72, "width_percent": 90.28}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-1081755671 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1081755671\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1819511227 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819511227\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1834484233 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1834484233\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1135193873 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751124360710%7C13%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZJeFJ1WGl0Z1hKcHJiN1AyVXhVbHc9PSIsInZhbHVlIjoiWkhPT1B2TkQxTk1QS24wZEl4UkNxQnBtQVBZNWFtdzR5M0R2RmRnMG9oZEQrcUgvSXZNWXJPbWxUOTlvbVYxdzBhL1hNOWVUWEU4SDZEUDZpSXZHZ3Z5eUtUdXpra2hPQitoL1hjbVpBa2ZNOVdXWThWM1RiejdLM3RUdm55RjFPc3NqSW02TnZvbkRHYXByaGJwbFBpdm1NNitPMGw5a0FtWXRaWDMzdTVJRG9DbytKdGFESlNGWDFlTHZvU2t6UnJqcm1yN1daKzFzazNxTlIwY0RJNHJpYkhmSm5MRUJPY3RUUHliMFRObU1IOVpkeUh6VkN1cHV4RExGVFQ3Y2FadndNRVp2bEozaU1COHB3K2JqQlcyYi9GcysyQTBPRGRyODBPMitVSnFIazVlTFRVUjl0RGJwVXpMNGUyV3BRZ3ZYampNbVp2OERYTXRzL3RmQ1VESkV5QWt5a3E5c2l0UVdxaDRXano4VnNEelBCQ3B4WVc1MzVmaEFnczJKZldQZkhsWlZFRVVuSmF5ZzNlMS9IM2RMcVlDNi9qbi9EQnRUUVhXYjNnMHU0RTFKVzI0QzFPcHNBcXV0ajZWR2R0WkJkNnl1YTY3eCtEWnMzbk1jMlBSNlZUWDltVWljZHkzME5yekhVWGNFbmJSYnM4K0tBeVZQNEhha1UvV2wiLCJtYWMiOiI4ZTExOTYzZWVkZmY4YzIwODlkNzAzZTE5NzkyNTExZmVlNmZmYTBjNzRlYzA2Y2Q4YTYxMDJhODcxZDdkODZiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNieDRaYWUrNjFZYVNOdGhTaUtXZ3c9PSIsInZhbHVlIjoiSkgyM1Z6dGozbENKOTZ6NUZLTkxkb252dEJneTllbUlJOHpZZHk4dFRNY1o3UjlvMERZdjNCYllhMHVZcmp6ektzeFJWK2MyanNLWEliS0RXd2N1bkxXWnlZdExrSkovbUp1SnFVSDZ1ODRSL2ZEQlVoTGNKc0ozMXNBejUwOE1JaVRFWDg4OXZhdnVwMUQ1bTYxUWNnV1lQS2ZjSmlkQTg5dUFwZ2p2K0pDUWg3dFk0VlcrSExqaEk4L0lVd2Z0QllDdTJacWVHTVY3NTRoMy9xQWtnQXgzYkZtdE5mdVBGMERxMG82MXRwenEwNUpVSzZ3QkFhc05YVThtUDB3US9hb2FGaU81WHBRT0NxMkltV0tualJyaXFYNkpMK1pQVE04WU1sSnpMQU9YelU2WWdhSkMzSkpyanFHWWhsV2NZK3BRWXRUcktRSm15bVQwQ2M5VnZiemorMFRmZlRhUkZ0MHh1aE1COUtXZXBUMUl5WDZWeDg0R2FhVW1FdU11V0MyTExPMEFtTVpNci83d2VGN1lOMmhZVTk1TVh5UHBFb0FJTExGcndMQTBGVldjeEJlYVNnQWtqNHNiNmpvSmNORjU1TlljSnRPczNmMzQrUHJsRGMrTjUyNGV2Q0RKbGRJakQ5cDNBQWtYYnpHRmRHSGgwUnFZVlQ0dVNLa2ciLCJtYWMiOiIwMTAxMTEzZDBlMTc4YThjNDk3MzA3ODBjNzcwYzJkYzBlYWY3MDU3MjIzYWU3ZGFjOTMzNjY4ZTQ0YzdlYWY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1135193873\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1315115428 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315115428\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:26:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNKTm9ZMVZWeGxGK29TdW1OSldQYlE9PSIsInZhbHVlIjoiRWQ1UkUybER2b3NNeEljWEh5Yk5wTUNmcVlLaTFBSHVtdjRlUGowSEc4WWxOU0xFUGFlQmoyMjA5SG1FVzJoUDJVSEhORksxSktjbHRRaS9uR0E4Mmg1MWJaenJNdWppYk1NNUwwbVQ2K2FQckdzTlhBRFduZFprcXhYY2doVmN3dE1RT2t0UFlERS9JY3gyVTVqZW5rbG5HV1RsMmh6eTVsZlhhdmJ2RVVoRWVQcWhhbU9QeFNrTGFCVGo0ajY4eXYrL2ZnUGVMMUZNOFVGZjRaN250MEJtdXhWVDZHZzlNQ2M4QkRLYTZtMVFQekRvVXRvaWE4b3BxajJKbWxwTXQvZVZvNDJQY0lGVlhDUlQ2ZUYraEU1U1FUWjFTTWRTTWM4bzRRS042d0VTKzdnU0dkOExwdUVNTFAvQ2gvdk9BUHF2NjhYL3N6TjJQYUxER1FUVVJoSVIrclVhZmhvbUVTY202SG5jSnJ2SldETit6emcxT29pMmhHdDFqVTd2Uk9zZGNwWkJKL2ltcmI5QUtNTi9kdk0wWGhsSVZNNjJTMVRCbnU4aGlIT0NBR0VFYmhaeC9UM1Z5c0pDQlhwVjZQOXNrOHVSZ200TlZaUUdEVWFHaDN5RDg3VlBaQjB3eU1jSExnTGxJOTk5U1dRVkpNOW1nOVB2dGkvbmN5eTMiLCJtYWMiOiIyNjJhZjY1ZjI4YWQxYzJiODQ3ZDNkMDcyOGNhNWY1OGJmMDQ0ZmFlZjcwNzBjYzc5OTVkZDJmYzY5Njc0NzUwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBubVI4Z2FWSXIxM0MwbVlIQy9rL0E9PSIsInZhbHVlIjoiOVBkMG9TNmZCOXNIMk5YMkZtMTNIN0RRMituclFOUWQ2ck9IQlJnQzZtT3pqWlI1SEVGcWtsWFNOL0NXakhWMXA3cUZkZkVBb0FYL0R6bnBWZ1crVzBNZmtnRVFXWEF1dGcyZHFkbFNhaDQzZ2xHSUhmVEd5VDFHakFLOGJXRjVyZFlyVVcvaDdmT1hBcXNxbStjdkVwbkZlVGFYVGhWOUZ3eDFUVThaUTdyTVV3ei8rWitHRHY1aVNEZExRWUNEakU0MExOQ0ZoeEtodExPdjR1ZEorWTlXKzNwWlhZSXBESGRic1kvTTJWY3RCcUR4SGdFbXh4Qjh4QjlZU1ZGRUtWMlBWbWpad3UxNXJZdWtBVE1tbkdmS1QzRDIrRFJiTFZYZWZubWJhZXNpYUVoRHpESnpvODM4NG9KdkJkSkxlVDRFYXZYdjhnaDZmek5NeHowVjQ4dzRaMU9aZmtrQjdpdUw1U0orejlNOHVvdkpvcFQ5Q09ibEVoZ2p4ZE5QUXo3TzY0UmYrK0VnZ2ZKQmk1MTBXQS8wcTBxUXhtLzgram9LVURTblRzemdHczBMOVNkL2tlTTExdWtjanpLT25yRjdGWHUyaVA5UjUwUnpnRXFVYTVpTHdLdkVKVmVFNFh0YUxRckFpT2hFSE54MTdDeEdUaS9mcEIwWWlOdzYiLCJtYWMiOiIyMTdhYTJiZjNiYzgwOTk3NGFiYzM4NDMxMjRjNDJjNDcyNzUwNDY0ODlmNDM5Nzc0OTRmZWJiZWIwOWE5MTI3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNKTm9ZMVZWeGxGK29TdW1OSldQYlE9PSIsInZhbHVlIjoiRWQ1UkUybER2b3NNeEljWEh5Yk5wTUNmcVlLaTFBSHVtdjRlUGowSEc4WWxOU0xFUGFlQmoyMjA5SG1FVzJoUDJVSEhORksxSktjbHRRaS9uR0E4Mmg1MWJaenJNdWppYk1NNUwwbVQ2K2FQckdzTlhBRFduZFprcXhYY2doVmN3dE1RT2t0UFlERS9JY3gyVTVqZW5rbG5HV1RsMmh6eTVsZlhhdmJ2RVVoRWVQcWhhbU9QeFNrTGFCVGo0ajY4eXYrL2ZnUGVMMUZNOFVGZjRaN250MEJtdXhWVDZHZzlNQ2M4QkRLYTZtMVFQekRvVXRvaWE4b3BxajJKbWxwTXQvZVZvNDJQY0lGVlhDUlQ2ZUYraEU1U1FUWjFTTWRTTWM4bzRRS042d0VTKzdnU0dkOExwdUVNTFAvQ2gvdk9BUHF2NjhYL3N6TjJQYUxER1FUVVJoSVIrclVhZmhvbUVTY202SG5jSnJ2SldETit6emcxT29pMmhHdDFqVTd2Uk9zZGNwWkJKL2ltcmI5QUtNTi9kdk0wWGhsSVZNNjJTMVRCbnU4aGlIT0NBR0VFYmhaeC9UM1Z5c0pDQlhwVjZQOXNrOHVSZ200TlZaUUdEVWFHaDN5RDg3VlBaQjB3eU1jSExnTGxJOTk5U1dRVkpNOW1nOVB2dGkvbmN5eTMiLCJtYWMiOiIyNjJhZjY1ZjI4YWQxYzJiODQ3ZDNkMDcyOGNhNWY1OGJmMDQ0ZmFlZjcwNzBjYzc5OTVkZDJmYzY5Njc0NzUwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBubVI4Z2FWSXIxM0MwbVlIQy9rL0E9PSIsInZhbHVlIjoiOVBkMG9TNmZCOXNIMk5YMkZtMTNIN0RRMituclFOUWQ2ck9IQlJnQzZtT3pqWlI1SEVGcWtsWFNOL0NXakhWMXA3cUZkZkVBb0FYL0R6bnBWZ1crVzBNZmtnRVFXWEF1dGcyZHFkbFNhaDQzZ2xHSUhmVEd5VDFHakFLOGJXRjVyZFlyVVcvaDdmT1hBcXNxbStjdkVwbkZlVGFYVGhWOUZ3eDFUVThaUTdyTVV3ei8rWitHRHY1aVNEZExRWUNEakU0MExOQ0ZoeEtodExPdjR1ZEorWTlXKzNwWlhZSXBESGRic1kvTTJWY3RCcUR4SGdFbXh4Qjh4QjlZU1ZGRUtWMlBWbWpad3UxNXJZdWtBVE1tbkdmS1QzRDIrRFJiTFZYZWZubWJhZXNpYUVoRHpESnpvODM4NG9KdkJkSkxlVDRFYXZYdjhnaDZmek5NeHowVjQ4dzRaMU9aZmtrQjdpdUw1U0orejlNOHVvdkpvcFQ5Q09ibEVoZ2p4ZE5QUXo3TzY0UmYrK0VnZ2ZKQmk1MTBXQS8wcTBxUXhtLzgram9LVURTblRzemdHczBMOVNkL2tlTTExdWtjanpLT25yRjdGWHUyaVA5UjUwUnpnRXFVYTVpTHdLdkVKVmVFNFh0YUxRckFpT2hFSE54MTdDeEdUaS9mcEIwWWlOdzYiLCJtYWMiOiIyMTdhYTJiZjNiYzgwOTk3NGFiYzM4NDMxMjRjNDJjNDcyNzUwNDY0ODlmNDM5Nzc0OTRmZWJiZWIwOWE5MTI3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}