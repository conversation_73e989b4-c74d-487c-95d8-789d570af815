{"__meta": {"id": "X10e80d3bea85ddd96834ff881198c39e", "datetime": "2025-06-28 16:20:22", "utime": **********.886387, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.401357, "end": **********.886407, "duration": 0.4850499629974365, "duration_str": "485ms", "measures": [{"label": "Booting", "start": **********.401357, "relative_start": 0, "end": **********.803671, "relative_end": **********.803671, "duration": 0.4023139476776123, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.803685, "relative_start": 0.4023280143737793, "end": **********.886409, "relative_end": 2.1457672119140625e-06, "duration": 0.08272409439086914, "duration_str": "82.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49611552, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024560000000000002, "accumulated_duration_str": "24.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8407679, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 7.451}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.851007, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 7.451, "width_percent": 2.565}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nAND p.warehouse_id = 8\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`warehouse_id` = '8' order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.854764, "duration": 0.0221, "duration_str": "22.1ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 10.016, "width_percent": 89.984}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-1974702154 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1974702154\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1992884347 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992884347\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1354560739 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1354560739\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-785428641 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751127457580%7C32%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkUxNEQrMzZEMmVsN2Rna0huMFp6OUE9PSIsInZhbHVlIjoiYXl4cFNFRWgvM090QnNSeTY4d1Qrb3UzM01DcVRpTE9OYjJVOEphVndZTHdENkVMVEFOR21ScHJwZkNsMmtaUG92Qk50RFBZVy8wZ1ZGOEdxclNUMzdPdGN2UkNQbjFrK1VYS0k5VFJncDRhQ2FJRTExRWVwVS80cjNHSUhaZTdtT29wRHhLc0dCMHNSQmdoRmlrSXFpVVRiZTQva0NNaTM1TFBWclovMWJOTmZSR3BGSmUwQStBQjRxMjFlMnl5MUZmNWFYN2ZDZTVlTFZROENvQXQ2M1dMdm0ybHcvOGF0eXNCNzJOZDhQdnltK09pMnpQWmpPbjJnRGZ1cWJVTmNFa2FvSjR5b29uQzFDaitTaFVIbythMUR4THExM1U1SWNVUDR1L0VqazdvNGhOdlhhV0dFeW5NdStHejFzUUZBdTlkOExoak1PMWFhd21halloS2dDdVNvZ0FacU1DS3FZS0ZTRkY4NldMSWZsQTlvZVl6Z04xSGJvbDB4eXBXZTdaVytlT1ViQUlWcWNhdU1lRzRpUFpUd0FIUkJrQ3ZkNmJJUDRPVWZwR3EyTU03Ly9DQzlOU2lVRGNJQ3Z2YUN6Zk9MdjJEOXJoaXRnTjFmekw3UjIvT0dFRk9sbVdEU015bVJDTWo2Rkk5c1pvT0lLbEdpdXdwdjc3TGtwbS8iLCJtYWMiOiI5NWFlZGZjOTA0YmU2MGZjZjc1OWNhNGNjMzIwN2UzYTY4NmE3YjU5OThjNmY1ODZlYTJhODIzZjFmNWFjMDcxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InYrSXN4NkFvNjBHalNrUkE0SnBnVnc9PSIsInZhbHVlIjoiblpZOG1UeUdLbGljdGpqaEd4SVJhT1hWb0pSVmZSblVNTENoLzd3VE1DcHQzRGdpNzJseTJySDNpczBEdlpWeHJ5S3g1TmlRR0hUUkRpOUR0c0h4UFJSWGUwVk93b084NlNUcW42UlhYdS9oUFZjd24rVmFGZVZqODY1WFl3dlBBQUVuci9hbVBOdHUrNDBVWnNuSUg0Y3VQRGNnVVZGL3hyaXJxQmhOTm55M3BFTURaTCt1WDBqMzAzNDM1YnI4MkpEMHlwMTYvbk5Ea09JU2JWOWR4VXVMQ204Z1QwcHdEbEZnWmUraUF6ZGNSaGVOTjVLQ3NySE9nTlF2UWVJYzNNVGNENDFSYVU2a1dPTkowdXcvc0tRbHd3a3k5cis2aWRLTFhicUNyZWZVc0EzRnc3eTRBSG41Q1pBQnhYOCtLNDFUdjFyalJGUTVtMERHRFRPd1Z5aE1WSmRBTGdQWTdlb0xtR0Rrak1yTGdKeHFQR3ZtZnJoNnRoMk4yR0dSbk5oR1c5YzN4NnVPSU1XQmxzeHdsUE1YNytvTExRSHZORDFReFl0ZSszNXYxeWlFRHFHY0pqSlZKcUVCOG9kYXNucVlMOUNNK2o2NWdKTSs1dU9zTjRzNENrdDdaYzI5d1FNY1NSQ3lRanJpY3ljai9KdWQ5NHJNM1RDemtidm4iLCJtYWMiOiIwYjgxNDI3NTU3NjIwY2Q5NmFjZWY0MWJmMDJhYTgxM2UxNmU3MWU2YWZiNDc3YzkyNzAzNTI5NGY3MTQ2MzBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-785428641\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1687596531 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687596531\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-146088572 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:20:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imo2RnBmWjFaRGFRbHRteTIxeU1vbHc9PSIsInZhbHVlIjoiUnhMRk1uaDdWZzFjejBLTHlmWlIvdG10NlRIZE1uemxyZ3J4emVRaUQ2aFZCbjNKNTVLMlBLdmdLZlc4c1hNOHd0ZWpNSCtjRm5SZUR5cnNYbW9uTXJOMnR4MGcxMnR6cG9zRURMaG1CRGZYalJhVWMwTnZ1MmxjSUpmS0c3VU1TVHE0RW4yMGh3Z2ExZm1NM1o5dEYrcjJiM0JTN3RVRmhpWkozMG1DWVc4SXBRV2hVc2NhelpIVUlucVppdDU0cnNiRWh0R1BGcGxOV0tqSzgxL3JlcFZBbHAxRFFDK3RLMHBNMXFGZmJMTW1UdFo2OWt1SXFhTkduWDZwUkw5NkJEbzJ5cjQyalBaVk1MSS9KVmp2bWVBMld3QnZ5NWhrUGhXSkNJT255Q0E0Qk5jdEJuNVcrd2g3a2UzWWdSeVBkenRXejh2QnA1SGdVKzBvQzd0NHl2SXQzaTdvV0lka2IvSmp2eDduei9aV2xGamd1b1gvZ2N3RTlNU1FYc242WGxTbzJWNzhvdytVWFRKcUozaG9GdHNkdnlaejFlVktSUVp5QmpWeFZFTkF5L28yZmdyNjNmcVViV1g0N2F2N2hYYkxVdEprVTFsMHdtVTdYRDRjUTJHbWdERGxrL2ZXMkRHeU5VdWlFWHhUWERlVWhkdjQ2S0tGdkUxQ3R6QWMiLCJtYWMiOiIzM2VhMzI5YTI1ZmFhMzRjNGMwYjVlYmNmMWMyYTNmMWI3MGJlMGY1MDcwNzlkNzdhMDhiZjk5OGQxMGVlZGQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:20:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdqOGpnY3hVcHhFdDVIQmZqNCt2eWc9PSIsInZhbHVlIjoiODF1MHF6UXlMK1BiZklYOXRld0FDamhjaUpRUE54aGt5NFlzUzQwdEpSMDNCaUFFdWF4d1lmQ0hZOERGYkcvV2pwUDVzMlpXQVlsa2xUam00UklSdEJ1RXJoeWxhRlRTcHh0T29odlY5MTFrSkl2OUJWbVlBcXExd3oxdkFYeStqa2ptYTNPZGpMN1Z3Z2xOejltSEtMS1dDN0dOTWVXT0dkWjhHamdLN2QxOGgyWWt2cGk3Q2RUMzlkcjhGNFJ4b1NWVDBFd0tFa1IzcEVoc1l6UXNSYUhPeEVWbzBnRS9VTzFlRi9GajlSZGQ2djZOcHZMUVhIYmI3SGxFaU5kbHY4c3U4UE9HaHRpYWo0MHkwcU9GUVNrVWtpZTZZcmNTT0hESzJ4cmNOQ3B2dlJPOElvdE1GRkhNdC9GbEF0VjZGRngxQnBpVVVPamJJOVl2ZTZPQ0krNnM2UnpWakh3QXZPeFcyMFFGM3RBZEFBd1FpZW9pK2d1RkFMWGZoN2trNlNBSlJVb255YmJpaVRHdWhMWFd5OFlDWXlGR0dhcjREYlA0dGhIYi9UOFgwc2ZvMDZJVVNhOXo4d0IvcEQwb3RlOE9FaThYakc0b2JpYi9GZ2ZDZlJZWlZWM1RTUUlRNTNXVks4YzEzUXpibXlJYUpoNCtKNG9FRDJCM3M0eEEiLCJtYWMiOiIxNDhlZjQzMzc5YjNkY2ZjY2ZhMmM4OGRlY2NlNjliNGYyMTEwYWFmZTg5NTVkMTNhY2ZmNWMyZDdhYTA1OTg0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:20:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imo2RnBmWjFaRGFRbHRteTIxeU1vbHc9PSIsInZhbHVlIjoiUnhMRk1uaDdWZzFjejBLTHlmWlIvdG10NlRIZE1uemxyZ3J4emVRaUQ2aFZCbjNKNTVLMlBLdmdLZlc4c1hNOHd0ZWpNSCtjRm5SZUR5cnNYbW9uTXJOMnR4MGcxMnR6cG9zRURMaG1CRGZYalJhVWMwTnZ1MmxjSUpmS0c3VU1TVHE0RW4yMGh3Z2ExZm1NM1o5dEYrcjJiM0JTN3RVRmhpWkozMG1DWVc4SXBRV2hVc2NhelpIVUlucVppdDU0cnNiRWh0R1BGcGxOV0tqSzgxL3JlcFZBbHAxRFFDK3RLMHBNMXFGZmJMTW1UdFo2OWt1SXFhTkduWDZwUkw5NkJEbzJ5cjQyalBaVk1MSS9KVmp2bWVBMld3QnZ5NWhrUGhXSkNJT255Q0E0Qk5jdEJuNVcrd2g3a2UzWWdSeVBkenRXejh2QnA1SGdVKzBvQzd0NHl2SXQzaTdvV0lka2IvSmp2eDduei9aV2xGamd1b1gvZ2N3RTlNU1FYc242WGxTbzJWNzhvdytVWFRKcUozaG9GdHNkdnlaejFlVktSUVp5QmpWeFZFTkF5L28yZmdyNjNmcVViV1g0N2F2N2hYYkxVdEprVTFsMHdtVTdYRDRjUTJHbWdERGxrL2ZXMkRHeU5VdWlFWHhUWERlVWhkdjQ2S0tGdkUxQ3R6QWMiLCJtYWMiOiIzM2VhMzI5YTI1ZmFhMzRjNGMwYjVlYmNmMWMyYTNmMWI3MGJlMGY1MDcwNzlkNzdhMDhiZjk5OGQxMGVlZGQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:20:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdqOGpnY3hVcHhFdDVIQmZqNCt2eWc9PSIsInZhbHVlIjoiODF1MHF6UXlMK1BiZklYOXRld0FDamhjaUpRUE54aGt5NFlzUzQwdEpSMDNCaUFFdWF4d1lmQ0hZOERGYkcvV2pwUDVzMlpXQVlsa2xUam00UklSdEJ1RXJoeWxhRlRTcHh0T29odlY5MTFrSkl2OUJWbVlBcXExd3oxdkFYeStqa2ptYTNPZGpMN1Z3Z2xOejltSEtMS1dDN0dOTWVXT0dkWjhHamdLN2QxOGgyWWt2cGk3Q2RUMzlkcjhGNFJ4b1NWVDBFd0tFa1IzcEVoc1l6UXNSYUhPeEVWbzBnRS9VTzFlRi9GajlSZGQ2djZOcHZMUVhIYmI3SGxFaU5kbHY4c3U4UE9HaHRpYWo0MHkwcU9GUVNrVWtpZTZZcmNTT0hESzJ4cmNOQ3B2dlJPOElvdE1GRkhNdC9GbEF0VjZGRngxQnBpVVVPamJJOVl2ZTZPQ0krNnM2UnpWakh3QXZPeFcyMFFGM3RBZEFBd1FpZW9pK2d1RkFMWGZoN2trNlNBSlJVb255YmJpaVRHdWhMWFd5OFlDWXlGR0dhcjREYlA0dGhIYi9UOFgwc2ZvMDZJVVNhOXo4d0IvcEQwb3RlOE9FaThYakc0b2JpYi9GZ2ZDZlJZWlZWM1RTUUlRNTNXVks4YzEzUXpibXlJYUpoNCtKNG9FRDJCM3M0eEEiLCJtYWMiOiIxNDhlZjQzMzc5YjNkY2ZjY2ZhMmM4OGRlY2NlNjliNGYyMTEwYWFmZTg5NTVkMTNhY2ZmNWMyZDdhYTA1OTg0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:20:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-146088572\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-585801511 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585801511\", {\"maxDepth\":0})</script>\n"}}