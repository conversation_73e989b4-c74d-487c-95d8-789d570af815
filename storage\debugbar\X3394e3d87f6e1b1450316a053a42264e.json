{"__meta": {"id": "X3394e3d87f6e1b1450316a053a42264e", "datetime": "2025-06-28 16:01:34", "utime": **********.512444, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.094575, "end": **********.512456, "duration": 0.4178810119628906, "duration_str": "418ms", "measures": [{"label": "Booting", "start": **********.094575, "relative_start": 0, "end": **********.434734, "relative_end": **********.434734, "duration": 0.34015917778015137, "duration_str": "340ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.434742, "relative_start": 0.3401670455932617, "end": **********.512458, "relative_end": 2.1457672119140625e-06, "duration": 0.07771611213684082, "duration_str": "77.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45846552, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.02457, "accumulated_duration_str": "24.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.465955, "duration": 0.01899, "duration_str": "18.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.289}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.493435, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.289, "width_percent": 1.587}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*************%' or `sku` LIKE '%*************%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*************%", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.495941, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 78.877, "width_percent": 10.094}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5015352, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 88.97, "width_percent": 9.687}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1995}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.506476, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1995", "source": "app/Http/Controllers/PosController.php:1995", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1995", "ajax": false, "filename": "PosController.php", "line": "1995"}, "connection": "kdmkjkqknb", "start_percent": 98.657, "width_percent": 1.343}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1271007469 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1271007469\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-366280374 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-366280374\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1938038957 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938038957\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-390201030 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNFVnVtUVRqUkFkZTVQL0VDdzE4L3c9PSIsInZhbHVlIjoiOERUS2Jtd3FiN1lCUzJ0ZThoc2g1dm4vUm1aRUY4WENhMXR2WXNUVVgrNUIzdDlXZEI1cURSbWx5aU5EYmxKTTMrbXRBL1ZJKzVlOE1oV2tZQ1RKa01zUloyekdaN2dqbVZpcDk2bjF4U2RuR21IZGEwTVY5Nk1mQStLNC9oTW9HUlovSnNteUQvRWhKVEcwa3QyYWhqQmVQRktPMkZrbTNrOER6T1lVMHE2emprYzJjWTF5TjJDQVpQU25vR1ZYMnFPb2xzTDQ5dTdMSVp2c3BLR1pNUlR5NWVlT1VWaGtXNkFzUm11NHNEUEYvOHlsUkNtam4yM2w4eFBudnpsNEREKzdQMlZja05uZmJiY1lUUUZLZzZ5QTFXL2FmR2V5T3lsSkhIaXVFOUtqdW9XMjZhanNQWlBzU0pjcWN1NkNCTkVzMkEzaEJDa1EwUlV1NlJQaENmeHR1MC9YL2xMRHZRc2VieUViUTZXUVkxY25SRUcrMTVaSjdHT0xGTXNwVC9iSlRJcmFYNGRQTVdEbVpEby81cDNHQXdmZ0RQSTZOcnRtTmF4d2gyNVkxYmxtYXhvMUU1c0lCZWdVcmkzbG85U1JaOVhrb0VqVXdqZzlER0NBb1Y5SkcrRncyVzFhOFVIVVNxMCt0VFp6SVRDbTV4dmFKeGV1QWVFQ0phTXoiLCJtYWMiOiI4ZWQ5NGM3YTBmZDI3NTIwY2VjYTU1ZGVmMzJlZDhkYTdhM2VkMzk0YTE2NDc0OWMxZDc1OTA1YTZkMThmYjgzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkNTaWhwdmJHOWQzTTkvNUNaQ1E0NkE9PSIsInZhbHVlIjoiMVcxOUxKYnp4Nzg5K2d0cWQwM2xZR3BseUt4bUhRU09GN0lSUGtiQTBPSmFEVmZTa2V4YTljVEdGRU81b1F6M0o3Znp5U2Z2MVFOaHo3UXl3c1c4VG56UHh5ZnM4TWdUSzhVbkJrYzl1VjY1c1U3ZUdWOCtNYk1ZQUlJOVJHdGxSZDlOVTk0Q2h2TE9QUFcxaERSc2w1SklFalI1TXdsNk1CdTNKNnpnNHZRd2FtTytGYnpCOFNLdkgybE5lUjh5WlZuaFF1L09raG1KUVE2eXVLM0FqbDFWWlIya1ZENGcvQkwydG03KzgxcUdCekJ1WHluUk41TWh1YWFwVXpienVqQlJzckN1aE5GbFg5czJZUDljSnU0UGhTMzYwQkhjNzU0MTRrWXFxSlg3WTBGWE02WlJiOUh6Y1p0aVVMaFV3WVRFQ25sWEhheU1VamxOaWpGOExCdERYcVhaR1hyRjU5M2NVa0VGNDdibnFxRGdpM3VXZmczTEF6NmNrMVFvNHJuWktEZnExbDVhS1lFRUNKTjBRNThiYi9MdGJKZGFCTlJucHFZY2hPUFdRVi9LRWJpNHFqVXZkZ3ExNGRKcFc4VXoyUWkydWxPNHQyVCtaNit0MVNzQUdKbDFRUEd1NHhNd09Md2x3YVhNUmliY3lveHlOUUdIdytMTkg2cjIiLCJtYWMiOiIzYTg2NmJkMTBhMzdhNGFkYzYxOGE5NmYzZjdlMGZhYzEzMzUxZGQ5Yzk2MDJhNWZhOTM2ZmY3MzExYzEzMGYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-390201030\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1105059354 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105059354\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFOdE9hQU5NYlpGaEd5eHo2L2c1eXc9PSIsInZhbHVlIjoiaG4yWjJ1c01HVGpndWRUeFpvQVk5OEFzcEZIcGtad1A1TVFhYzR1U3JVWHkvNDI0RUVjVUd4WlpHK3piU1dzUkQ4cStaQjQ1Ymd3ZkhHMzRuSkl3WWFMazdobEdISHhja3hUb2g5SDdzbGxoT055eExiZHpxekM1M01tUFc0cHViMWdrZTl1d0lWZUZKK0FVMWtJVHJYOUt5Y3IyMjBjVUszT3VjZ1d6TWFvK05USTZVaVBGOS9hUFZOR2tWZFhlZGl2ZG1WaE5oY3RhVGQ2VWNHWE9Cc0VGazVwRFBNSkttcC9QNXZMVDVHRmpPYlpEeERaU3pCUlJSdjlzcTY2WlhCZGM3bmx3REE1VURaRkorNUJKQkgvVDJHT0E2c0EySExZOEhsbHVoVktnSjZST3Vqd1JjY2k4QUhBVjBydFJLb0t5S1RqSnZOWkVrSmhiTHpuVk5FN2g0RjhpdkxKbWN2Q05rZnlEcXNSaWgvVklhblZ1SmRvZTJVY3ZMM1NZZlhFZnZPVnd1VjNWdkw1T2RDdmpEREo0ejF2c2RDTytpTUlvKzlEU0tuVjVwL2F5NnEvS3lUaGNhRnFkL2RwMFNieFZ4NEl5azB2YkVCTjZtRllsTDN0Mk9za3BoNnFyL3FpTzZUNnE4Q2QwVGxxMWNIMm90bGg4ZTN4V3dGN00iLCJtYWMiOiJiNzYwY2Q1NWM4ZGRmOTkzN2YxNDM1ZmVlMDg4ZWYwM2I2Mjc0MDgwMjViMmUxYzY3ZTc1ZWQxNjNhNjlkYzE2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilc1a1R5NVRURER0YndPR2dPdm5jRXc9PSIsInZhbHVlIjoiRG9ONDRudUNaQUdUSHNFL2pkVDR2cUxYMU8zbE80T0haZXM5S2FrbWdyQjJvekR0RlZvYkptZzRwMjBUNTdQMEtQTmhkQmp2N2RUdmNKZHJMUFpFMVpEaUJDMVdmekRzOWJXYTJqNnE1d0FRVFJLMGhleDMvNkhyUzBTZ2diREhHMk1iMnlzRVB0bkNDTWtLSGxqR1BweWM4VFMzcHVuSlFEZVFNWGJBdzZiMTJ3dkVpZ3V4b3VkcWgvVGtjeEs5eEFwdHR3VEtPMk9icEhQQXlqblJVbmZlYlE1dTR2NHNGLzM5bDhja0FSS2ZLbHlRaVhENHF6MzgvZFJYS25GbmxuYkFMbHplT0xkdmJDZWRkdW9ldTBsWTFnajVNMjBqaDVCa0czOWhtRXZaNnFORGNNaC82MG95ajNpWUk4NldyNlBCSjdGMCsxREt4WmhkbmFHYnVzN2ZpNm9ManJ3ejlxc2lON0R5a0RLRFRJQzZldXA4VVZPeWpjVWg0cmR4T3RydGdteU1PVklvMUkyTFVyZTIzSWJQUktBMVFmdEdEczRkVk85S0RtaG40VVlwUnVVTHlSMVIxa2x2NHp4c1ZHOTZmQjJBcjZNcEozZi9jVVdaMDdZeUFFMDV5TnE0eWVFZHQwaWVlTWtjeCtDZVNEakMrYTNHbERKR3Z0MjMiLCJtYWMiOiIzMDI5OGE5NGMzNzczZjBhZTZmMjAzY2U4MDljMzczNzYxOWRiYzI3NzRkYTBmOTk3MGFjMDI2M2RiOWFkZTQzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFOdE9hQU5NYlpGaEd5eHo2L2c1eXc9PSIsInZhbHVlIjoiaG4yWjJ1c01HVGpndWRUeFpvQVk5OEFzcEZIcGtad1A1TVFhYzR1U3JVWHkvNDI0RUVjVUd4WlpHK3piU1dzUkQ4cStaQjQ1Ymd3ZkhHMzRuSkl3WWFMazdobEdISHhja3hUb2g5SDdzbGxoT055eExiZHpxekM1M01tUFc0cHViMWdrZTl1d0lWZUZKK0FVMWtJVHJYOUt5Y3IyMjBjVUszT3VjZ1d6TWFvK05USTZVaVBGOS9hUFZOR2tWZFhlZGl2ZG1WaE5oY3RhVGQ2VWNHWE9Cc0VGazVwRFBNSkttcC9QNXZMVDVHRmpPYlpEeERaU3pCUlJSdjlzcTY2WlhCZGM3bmx3REE1VURaRkorNUJKQkgvVDJHT0E2c0EySExZOEhsbHVoVktnSjZST3Vqd1JjY2k4QUhBVjBydFJLb0t5S1RqSnZOWkVrSmhiTHpuVk5FN2g0RjhpdkxKbWN2Q05rZnlEcXNSaWgvVklhblZ1SmRvZTJVY3ZMM1NZZlhFZnZPVnd1VjNWdkw1T2RDdmpEREo0ejF2c2RDTytpTUlvKzlEU0tuVjVwL2F5NnEvS3lUaGNhRnFkL2RwMFNieFZ4NEl5azB2YkVCTjZtRllsTDN0Mk9za3BoNnFyL3FpTzZUNnE4Q2QwVGxxMWNIMm90bGg4ZTN4V3dGN00iLCJtYWMiOiJiNzYwY2Q1NWM4ZGRmOTkzN2YxNDM1ZmVlMDg4ZWYwM2I2Mjc0MDgwMjViMmUxYzY3ZTc1ZWQxNjNhNjlkYzE2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilc1a1R5NVRURER0YndPR2dPdm5jRXc9PSIsInZhbHVlIjoiRG9ONDRudUNaQUdUSHNFL2pkVDR2cUxYMU8zbE80T0haZXM5S2FrbWdyQjJvekR0RlZvYkptZzRwMjBUNTdQMEtQTmhkQmp2N2RUdmNKZHJMUFpFMVpEaUJDMVdmekRzOWJXYTJqNnE1d0FRVFJLMGhleDMvNkhyUzBTZ2diREhHMk1iMnlzRVB0bkNDTWtLSGxqR1BweWM4VFMzcHVuSlFEZVFNWGJBdzZiMTJ3dkVpZ3V4b3VkcWgvVGtjeEs5eEFwdHR3VEtPMk9icEhQQXlqblJVbmZlYlE1dTR2NHNGLzM5bDhja0FSS2ZLbHlRaVhENHF6MzgvZFJYS25GbmxuYkFMbHplT0xkdmJDZWRkdW9ldTBsWTFnajVNMjBqaDVCa0czOWhtRXZaNnFORGNNaC82MG95ajNpWUk4NldyNlBCSjdGMCsxREt4WmhkbmFHYnVzN2ZpNm9ManJ3ejlxc2lON0R5a0RLRFRJQzZldXA4VVZPeWpjVWg0cmR4T3RydGdteU1PVklvMUkyTFVyZTIzSWJQUktBMVFmdEdEczRkVk85S0RtaG40VVlwUnVVTHlSMVIxa2x2NHp4c1ZHOTZmQjJBcjZNcEozZi9jVVdaMDdZeUFFMDV5TnE0eWVFZHQwaWVlTWtjeCtDZVNEakMrYTNHbERKR3Z0MjMiLCJtYWMiOiIzMDI5OGE5NGMzNzczZjBhZTZmMjAzY2U4MDljMzczNzYxOWRiYzI3NzRkYTBmOTk3MGFjMDI2M2RiOWFkZTQzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}