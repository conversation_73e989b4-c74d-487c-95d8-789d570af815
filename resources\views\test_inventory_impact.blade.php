<!DOCTYPE html>
<html>
<head>
    <title>اختبار تأثير البيع على المخزون</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background-color: #ffffcc; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>🔍 اختبار تأثير البيع على المخزون</h1>
    
    <div class="test-section">
        <h2>📊 حالة المخزون الحالية</h2>
        @php
            $warehouseProducts = \App\Models\WarehouseProduct::with(['product', 'warehouse'])
                ->where('warehouse_id', Auth::user()->warehouse_id ?? 1)
                ->get();
        @endphp
        
        @if($warehouseProducts->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>اسم المنتج</th>
                        <th>SKU</th>
                        <th>المستودع</th>
                        <th>الكمية الحالية</th>
                        <th>نوع المنتج</th>
                        <th>حالة المخزون</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($warehouseProducts as $wp)
                        <tr class="{{ $wp->quantity <= 0 ? 'error' : ($wp->quantity <= 5 ? 'warning' : '') }}">
                            <td>{{ $wp->product ? $wp->product->name : 'منتج محذوف' }}</td>
                            <td>{{ $wp->product ? $wp->product->sku : '-' }}</td>
                            <td>{{ $wp->warehouse ? $wp->warehouse->name : 'مستودع محذوف' }}</td>
                            <td>
                                <strong>{{ number_format($wp->quantity, 2) }}</strong>
                                @if($wp->quantity <= 0)
                                    <span class="error">❌ نفد المخزون</span>
                                @elseif($wp->quantity <= 5)
                                    <span class="warning">⚠️ مخزون منخفض</span>
                                @else
                                    <span class="success">✅ متوفر</span>
                                @endif
                            </td>
                            <td>{{ $wp->product ? $wp->product->type : '-' }}</td>
                            <td>
                                @if($wp->quantity < 0)
                                    <span class="error">سالب</span>
                                @elseif($wp->quantity == 0)
                                    <span class="warning">صفر</span>
                                @else
                                    <span class="success">موجب</span>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="warning">
                <p>⚠️ لا توجد منتجات في المستودع الحالي</p>
            </div>
        @endif
    </div>
    
    <div class="test-section">
        <h2>📈 آخر 10 عمليات بيع وتأثيرها على المخزون</h2>
        @php
            $recentSales = \App\Models\Pos::with(['items.product', 'warehouse'])
                ->where('created_by', Auth::user()->creatorId())
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get();
        @endphp
        
        @if($recentSales->count() > 0)
            @foreach($recentSales as $sale)
                <div style="margin: 20px 0; padding: 15px; border: 1px solid #ccc;">
                    <h4>فاتورة رقم: {{ $sale->pos_id }} - {{ $sale->pos_date }}</h4>
                    <p><strong>المستودع:</strong> {{ $sale->warehouse ? $sale->warehouse->name : 'غير محدد' }}</p>
                    
                    @if($sale->items->count() > 0)
                        <table style="width: 100%; margin-top: 10px;">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية المباعة</th>
                                    <th>الكمية الحالية في المستودع</th>
                                    <th>تأثير البيع</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($sale->items as $item)
                                    @php
                                        $currentStock = null;
                                        if($item->product_id > 0) {
                                            $currentStock = \App\Models\WarehouseProduct::where('warehouse_id', $sale->warehouse_id)
                                                ->where('product_id', $item->product_id)
                                                ->first();
                                        }
                                    @endphp
                                    <tr>
                                        <td>
                                            @if($item->product_id > 0 && $item->product)
                                                {{ $item->product->name }}
                                            @elseif($item->description)
                                                <span class="info">{{ $item->description }} (يدوي)</span>
                                            @else
                                                <span class="error">منتج غير معروف</span>
                                            @endif
                                        </td>
                                        <td>{{ $item->quantity }}</td>
                                        <td>
                                            @if($currentStock)
                                                {{ number_format($currentStock->quantity, 2) }}
                                            @elseif($item->product_id > 0)
                                                <span class="error">غير موجود في المستودع</span>
                                            @else
                                                <span class="info">منتج يدوي</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($item->product_id > 0)
                                                @if($currentStock)
                                                    @if($currentStock->quantity >= 0)
                                                        <span class="success">✅ تم خصم المخزون</span>
                                                    @else
                                                        <span class="error">❌ مخزون سالب</span>
                                                    @endif
                                                @else
                                                    <span class="error">❌ لم يتم إنشاء سجل مخزون</span>
                                                @endif
                                            @else
                                                <span class="info">ℹ️ لا يؤثر على المخزون</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <p class="warning">⚠️ لا توجد منتجات في هذه الفاتورة</p>
                    @endif
                </div>
            @endforeach
        @else
            <div class="info">
                <p>ℹ️ لا توجد عمليات بيع حديثة</p>
            </div>
        @endif
    </div>
    
    <div class="test-section">
        <h2>📋 تقرير حركة المخزون</h2>
        @php
            $stockReports = \App\Models\StockReport::with('product')
                ->where('created_by', Auth::user()->creatorId())
                ->orderBy('created_at', 'desc')
                ->take(20)
                ->get();
        @endphp
        
        @if($stockReports->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>المنتج</th>
                        <th>النوع</th>
                        <th>الكمية</th>
                        <th>الوصف</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($stockReports as $report)
                        <tr>
                            <td>{{ $report->created_at->format('Y-m-d H:i') }}</td>
                            <td>{{ $report->product ? $report->product->name : 'منتج محذوف' }}</td>
                            <td>
                                <span class="{{ $report->type == 'pos' ? 'error' : 'success' }}">
                                    {{ $report->type }}
                                </span>
                            </td>
                            <td>{{ $report->quantity }}</td>
                            <td>{{ $report->description ?? '-' }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="info">
                <p>ℹ️ لا توجد تقارير حركة مخزون</p>
            </div>
        @endif
    </div>
    
    <div class="test-section">
        <h2>🔧 اختبار وظائف المخزون</h2>
        <div style="background: #f9f9f9; padding: 15px; margin: 10px 0;">
            <h4>اختبار وظيفة warehouse_quantity:</h4>
            <p><strong>الكود المستخدم:</strong></p>
            <code>Utility::warehouse_quantity('minus', $quantity, $product_id, $warehouse_id);</code>
            
            <h4>اختبار تحديث WarehouseProduct مباشرة:</h4>
            <p><strong>الكود المستخدم:</strong></p>
            <code>$warehouseProduct->quantity -= $item['quantity'];</code>
        </div>
        
        <div class="info">
            <p><strong>ملاحظة:</strong> يجب أن تتطابق النتائج من كلا الطريقتين</p>
        </div>
    </div>
    
    <hr>
    <h3>🔗 روابط مفيدة:</h3>
    <p><a href="{{ route('pos.index') }}">نظام POS</a></p>
    <p><a href="{{ route('pos.enhanced.index') }}">نظام POS المحسن</a></p>
    <p><a href="{{ route('dashboard') }}">لوحة التحكم</a></p>
</body>
</html>
