{"__meta": {"id": "X8a4e74c71d60232da8236bef09e564e2", "datetime": "2025-06-28 16:19:34", "utime": **********.560192, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.153259, "end": **********.560207, "duration": 0.4069478511810303, "duration_str": "407ms", "measures": [{"label": "Booting", "start": **********.153259, "relative_start": 0, "end": **********.51112, "relative_end": **********.51112, "duration": 0.3578610420227051, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.511129, "relative_start": 0.35786986351013184, "end": **********.56022, "relative_end": 1.3113021850585938e-05, "duration": 0.04909110069274902, "duration_str": "49.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45714440, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025700000000000002, "accumulated_duration_str": "2.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.536454, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.479}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.545993, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.479, "width_percent": 17.899}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.551433, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.377, "width_percent": 20.623}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-54312683 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-54312683\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1721950204 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1721950204\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-274778956 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-274778956\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-940701922 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127556646%7C37%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhTN2RWblBXaE1WLzg3V0tQQWhvQmc9PSIsInZhbHVlIjoiWS9GQ0NuTXBaUWNvdGd3VXo2SUJYWkNQVFpOYWpMbUpGOGdBZkhoQk1wMVdvV0ZaRVpKbEd0Zk5ZZHRsSGpyS3oyZFFzVUhCRkhFSGlKS0FLSThCSWVESzIyUjN3Q09nQWJrVnU0VlRYSzJsaWh1TnhML1FBbVFyNVBkMGJSRnZjU0E4aXVaSm1OZ3JGL0F1NkhXc1BJTDExTFBiUFR5N2VXSGlaYjl1a2FNSWVLcUxMYVF4Q2MyeHlibExjMndzTDlyMEVoSVBLakIxVXhpK3ZHVmh2Y01FelVlVEtzR2VxWkFkb016S0NSOWFJWjVUNUNHVE9manVpRGMwcXBYRkR3em50N2piMERhZExiN281bDB1STZldVRnRlM3aHNYUlY1bCsrSldoQ1l0SFhvem1iZ3RKZ05yUmNaUzdLZnlrQlM5b2lVY3dkRVZBRHNGdlAzbUNKZzVab0QrZlcvVDR1UkJUQmNObWpzejN1ZlJkY3YweXYzc0xkcWNhQkJUVDJVL2x5ck5NRVpxSkh4N29YZStkVFgyRklCVGd2ZklZcFFRdTZDcGk5VSs1Z2pDYXJtOWd1dHRBUzV6WHp3aWkxRkxHaW50dzRtaWExZkd0alZIUGNzVHZzVjFmVlNhb3p5eENxcm9uZDBQWjlXbDdSQ3A1T0hWbkRVVGd2NTIiLCJtYWMiOiI5Mjg0ZjcyODE1MTI0M2I5MDNiZThjYTQwZjQ0ZmI4YzliZTMyMzAyNmFiNTNlMGNmMTQ2MjRhMTc4MmRlOTA5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdtbjVDWnEvUjh2Sm9QL3lqRTZ2T1E9PSIsInZhbHVlIjoiZlhhRkJKdGwvZkxCSVQ1UHQ5S2JUczloU3laV0NlMXpHOHViMjh0WC9QbjBoR2YrVGxyMS9ybUpTandiSkJ4T0I1dEM5dUYrTzFxVWNCMWtld28xZEtpMy9uRGdvblozbitYa2xyRUMzaG16azEyWmIvRHpiYkFrSzlIdmtOTDIrbkdOeUtRUEcxQWZiVUVkTVFOYWFucG9jVkJyQ3ZXV2pVbHRUV0pwZ0xrZk03aThoa29xQ241dWNnWWsrSi9VaWxQcTg4bFIrSVgyUE5lT2FtdVRxMis4c0pnR3dVcU1LT2lwMC83WUdTWHlLcFZRS0V5V3ViOHJLMm4rZk9DL2g0bmZwZW5MQzFtN3k0aFBDT1pQZmVQY2hTdjVTczc0SlZDc0F1ZDBNMlY5eHFpRmtVMTFpdDl2aU9rVGsrV0ptWlpXdFVFSlEyUTllWG5TOVBReGs1aFRHWUtoL3lwaEdkOExmSDhGUTNGOEluMVIxU1dzK08xNkZ4ZjZFMU1hekxWSWFMUDFsZWpiUTRpaGJDcDB5U0RmZyt4dmJsdEJpT3dZZzB4cXluUVNRTWhscE9jTzBvdHMrYUIvK2RLVmNlYVlwNGJxblYvMmdSZnkzc2hwTUN2YmRocTlFMGNLdGEzSmg5RnZNUVArUyt3bkxxUTJ0L3dZZG05TjRQODkiLCJtYWMiOiJiZjc4MGIxNjFmMzdlNzgyYjkwMWUzYjY4YjBlNmRkOGE4MjNmMjRhYmZhMjQ0NDNmMThlYjIxYzBlOWRkNWI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940701922\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1935645525 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1935645525\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1209080380 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IngrK1lLNlB6TUFOK0FGVTN3dklZNnc9PSIsInZhbHVlIjoiYW5DVzg1b25VRnM4ajRLejZ1Q2FFeDB4TUdEK0trbFR3WkFvT0JJdWxObjlCOC9xM01xb1VRUStjMDBaNCsxTUFDejB4NFJXeXVxRFZGdzlxR2M0N2dkQkd2VTMvRFJjQVV4QmpsMUVON2tSZVBZTzR6L0lRa1V3eW1NYmhvaFJyTkVYQWhMRHBUSW9lWnF4eEFKWWFPQzZpY1hHL1RBbldOQVVxM2xRdVpXUDNKZlIwTXZGT2FZYThKd3ZJWmwwNlVsNkNLWnFuUlkzRFhuZFNaQnpXbUVLejZXS3ZKOWVZT2FhQitBd3VaUDJSUHYwc1VFVEFoaHdMM0RkRkVrVjBZMEo5blRocVhtUFNlNDhTTE42MzJOOGRHUzltYkVDRVJyYk9VblgrU0ZDZE1aYjMvM0JZdlFXSXZ2VTQrTEx5Y1I0TUI2dFFUaW5Tak5zUlFzK3pxUkdWY1dKaStZUUUzQU1yWDZWdjlzdlNDMk9CbU9wSWFCQUQwYXZIcktBb0VjcnBWMjBPTEVkaXIyYTRiOTNiY1dqUERQK3NzWkhxYnBIcVMxRGxaemxpaHdGMnoySnFtcGpqMjlNbWs2aVhjS0xRZzZOSWx0QTJJSm1FdGt5Rjk3UHh6TWJ1eGI2YUUzSmp5ZnRjRW1pUk1WeEVJdm5OckRtRnVJV1pPRTQiLCJtYWMiOiIwYzU1M2VhMjRiMTlkZDQ1MTdlODQ3MDEzMWRjMDI4YjI2NDQ0ODRhZDI2MzAzN2I2ZWUwODNlOWM0ZjU3ZjNkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlFFa24wT3B6MVVOMFVmYzRZSmh6emc9PSIsInZhbHVlIjoibC9XRXp2NnZ6TUhwMVZRTDFXRTRlZkZSUGF2bXdoUi9vNzFlbjA0ZjRzT3VtYkgxOTZjUnNDcWVUTnVSMXpKaVBOL3prQU9ienJsRkZ5M3luTXZubmZ6N3REaHBKendMRHc4cXViTG9mSUk2UkJrZlpZRkVtT1pnT01xbGV4eW5QdkhFTEdxSThTMWFiNGpNcCtEekxnZmdLUDkyb2VPaWdVaGEzVjhraWFCV2l6amVjMEI5ZVhmS1lOV1dkdDFUTkY5R05pcnhIeTV5L2REVjVhMGQ3YVNHN1FzN1RjTnUvSUxkWlFJdmVqbHRsdkw3ZEY5M2QzdCtNUnlMamxsM1ZLSDdoUGowRitVNjRtbG9qb3F6Umt5ZXM4UEovRWZneTFDS0NvaHU4Z1FsNER3T1pGWDJNMW9kRWdKVS9sM2wvam4wZzRMc01DM210RXREUEwrOG5Hc3hBUWdUY2xxM0xGYUhFdnNKeHhpeFYzVGRXWGpYTWZQNjVDNkxtVXdLRmNpalRudE00K0ZkclJpa29mdUcrZjFjWFNjOTlDV2pWRjBuTlNuMGVBRzBKU3N5TkliZm5qMDVQckFYMVNaTmlCMno3N3FTM3Z4bFF4U1lhMFhGUnI5UlBKcmtyUlg1N3cwZGVGN01hZFpXM3pBeEM2YkhRa1lja1dIWXBzdzAiLCJtYWMiOiI2ODM0MjNlZjFhMzE3ZTBlYzk2MTk2ZWU5OTVjNzllNDc1ODU5ZWJjNzhhYjhlY2EzMjgxMGY5YWRjNmRkNTQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IngrK1lLNlB6TUFOK0FGVTN3dklZNnc9PSIsInZhbHVlIjoiYW5DVzg1b25VRnM4ajRLejZ1Q2FFeDB4TUdEK0trbFR3WkFvT0JJdWxObjlCOC9xM01xb1VRUStjMDBaNCsxTUFDejB4NFJXeXVxRFZGdzlxR2M0N2dkQkd2VTMvRFJjQVV4QmpsMUVON2tSZVBZTzR6L0lRa1V3eW1NYmhvaFJyTkVYQWhMRHBUSW9lWnF4eEFKWWFPQzZpY1hHL1RBbldOQVVxM2xRdVpXUDNKZlIwTXZGT2FZYThKd3ZJWmwwNlVsNkNLWnFuUlkzRFhuZFNaQnpXbUVLejZXS3ZKOWVZT2FhQitBd3VaUDJSUHYwc1VFVEFoaHdMM0RkRkVrVjBZMEo5blRocVhtUFNlNDhTTE42MzJOOGRHUzltYkVDRVJyYk9VblgrU0ZDZE1aYjMvM0JZdlFXSXZ2VTQrTEx5Y1I0TUI2dFFUaW5Tak5zUlFzK3pxUkdWY1dKaStZUUUzQU1yWDZWdjlzdlNDMk9CbU9wSWFCQUQwYXZIcktBb0VjcnBWMjBPTEVkaXIyYTRiOTNiY1dqUERQK3NzWkhxYnBIcVMxRGxaemxpaHdGMnoySnFtcGpqMjlNbWs2aVhjS0xRZzZOSWx0QTJJSm1FdGt5Rjk3UHh6TWJ1eGI2YUUzSmp5ZnRjRW1pUk1WeEVJdm5OckRtRnVJV1pPRTQiLCJtYWMiOiIwYzU1M2VhMjRiMTlkZDQ1MTdlODQ3MDEzMWRjMDI4YjI2NDQ0ODRhZDI2MzAzN2I2ZWUwODNlOWM0ZjU3ZjNkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlFFa24wT3B6MVVOMFVmYzRZSmh6emc9PSIsInZhbHVlIjoibC9XRXp2NnZ6TUhwMVZRTDFXRTRlZkZSUGF2bXdoUi9vNzFlbjA0ZjRzT3VtYkgxOTZjUnNDcWVUTnVSMXpKaVBOL3prQU9ienJsRkZ5M3luTXZubmZ6N3REaHBKendMRHc4cXViTG9mSUk2UkJrZlpZRkVtT1pnT01xbGV4eW5QdkhFTEdxSThTMWFiNGpNcCtEekxnZmdLUDkyb2VPaWdVaGEzVjhraWFCV2l6amVjMEI5ZVhmS1lOV1dkdDFUTkY5R05pcnhIeTV5L2REVjVhMGQ3YVNHN1FzN1RjTnUvSUxkWlFJdmVqbHRsdkw3ZEY5M2QzdCtNUnlMamxsM1ZLSDdoUGowRitVNjRtbG9qb3F6Umt5ZXM4UEovRWZneTFDS0NvaHU4Z1FsNER3T1pGWDJNMW9kRWdKVS9sM2wvam4wZzRMc01DM210RXREUEwrOG5Hc3hBUWdUY2xxM0xGYUhFdnNKeHhpeFYzVGRXWGpYTWZQNjVDNkxtVXdLRmNpalRudE00K0ZkclJpa29mdUcrZjFjWFNjOTlDV2pWRjBuTlNuMGVBRzBKU3N5TkliZm5qMDVQckFYMVNaTmlCMno3N3FTM3Z4bFF4U1lhMFhGUnI5UlBKcmtyUlg1N3cwZGVGN01hZFpXM3pBeEM2YkhRa1lja1dIWXBzdzAiLCJtYWMiOiI2ODM0MjNlZjFhMzE3ZTBlYzk2MTk2ZWU5OTVjNzllNDc1ODU5ZWJjNzhhYjhlY2EzMjgxMGY5YWRjNmRkNTQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209080380\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1673740398 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673740398\", {\"maxDepth\":0})</script>\n"}}