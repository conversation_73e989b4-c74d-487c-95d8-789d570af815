{"__meta": {"id": "X8877079d54b768b40f9437ec8f75dbd8", "datetime": "2025-06-28 16:21:07", "utime": 1751127667.011093, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.596815, "end": 1751127667.011105, "duration": 0.4142899513244629, "duration_str": "414ms", "measures": [{"label": "Booting", "start": **********.596815, "relative_start": 0, "end": **********.963241, "relative_end": **********.963241, "duration": 0.3664259910583496, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.96325, "relative_start": 0.36643481254577637, "end": 1751127667.011107, "relative_end": 1.9073486328125e-06, "duration": 0.047857046127319336, "duration_str": "47.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45405384, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00189, "accumulated_duration_str": "1.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9931998, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.836}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": 1751127667.003365, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.836, "width_percent": 21.164}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1712828149 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1712828149\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1093689951 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1093689951\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1599196383 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1599196383\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1492420362 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127660348%7C43%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZvMjg4Qy9zS3RGd1R2ZFRGclhwR3c9PSIsInZhbHVlIjoiTm5Mb2lkM1pLVVZDUmpacGtyTjFhRHdVMnlIUXoyRXEzZkgvZkZXb0dtZVlidkY3ajFPRWZlZFMrVW41RmJzOEZESzBOUDhYcXBXWFlSZ2VwM3JxWXJwcnkrOTM4djk3MkZlalhKMlREb0RsL0NaaUpiNkt2dk1EaGs0WlU3YU5FMmFzZzhPNFY1ZVVGNzJLcnJjNEZlZlkrQ2E4OGZTd012T1FqOXZON1BNaFEzMjNHMm9UM2JxSEpzdkNWNm40WTUyWDhhTS9xUXJtNjduelJSYUR5U294bUxVLzRFQWUyak42V0NwcXdnQVpHNVpLeVVYTzY4UDlFb3dtaGFIT1hDY0FFcFFlUFF4UXd6cUxreGpReXJ3RE0ySmp3YzZaanhqaTRqaTg0eWxPWkMraXV1TEVMM25McGc1TjVPWXBDSm94RHA5bEZESThNMlN1VGI1ODYvOUhVN2I0MjlqUDh0ZlcyNHJQYzlMdmVMUElMeWl2SzVmNmxzWiszRGs5WDJ4aWgrbVdlb2NKbkdxT2ExOUVJR3FCS3kzMjdLcDZqSFhpSVlPbFIwRkJyVUtDL09LQTJ4aFZQMEhSYmhmSVk3aTg0TWxnN1I1MjJXZzhEa1NqMTMvTC91ZGcwN010V1FpWENSTisyUjRCWmVHMFIzbWI5MCt3dFZGR1dzSFoiLCJtYWMiOiI3ZGZhYTVlYmYyYmRhNDQ2ZjdkNDk3MGYwYjBiM2Q3ZTg3MTljMzU5NzcwZjc2M2IxM2Q2MThmMTg3YzU1MmEyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ims1UHBNUGxWdWZIQUtmQlgrZ3JRRWc9PSIsInZhbHVlIjoiK1ljQ25KWC8xdmp3VGh5clpwZll1UTdzN2ZKa21Bam1OSFYvUWt6MlArN0ZDc1hudmdTUkJ4aUdMeEw0MzAzZks3ZnhOYytqeWZqc0FDTW5qeUlCczM5WWg3SmxoWkhJbllOZDlyV25lS3FPL3VsOExLLy84Q09zOFNTdkcvUnV2SXZKNktBN3hmOEVpZFgzUUZISHBFREk4ZzBSbTZGNFBTYnNTcysraFBrdlFoWTB3ak5walFCaUh4WGY5Q1JkNWtRd1ZvZjlwd3UvNDZhdG5mQmMyL2lsSGNPbUt5WG1Gek0yTnE2Ym5jMjk3OG9pOUd5MkxLK1pQcHRRWEV6UlZ2M2RxYkNyb25DR1BpTXpIZjlZWVN4eWd6aGljNWloNWFVV09XM3ljRVF1ajRjUzFYZFdXUWNHb1BLSkVuajJ3Y0RKaUhJNUdtWWNmQzZzWXM5TTZBK0RFUUc4aGR2VGVYd3pSTmwyNVFBbHdPSG1VK3EzQy92QWtOak9uSVFUaFJ5Wm1CTzBCSGJMOEcrZ1ozRHg1RWZKVGx1Y0haU0FmdUlKMklYVndOYmRucjd3MXdMdmRBSmFBQmY0TGc1VHBpSnhnRkRUSEFXZHlWTVFxQlFFL1h6SklSOVR6aEE4UC9ZbEN1MTI3UkxmaEQ2ZEtTR2FLaG5CM0FybVR2T0ciLCJtYWMiOiJmODllMjQxOTAxNDcxZjljM2FlOTc5MzYzZWIyOTk1Mzg1ZGQxODU0NGYzMWFmMTVlM2QyNzRhMmUwZTE4ZGE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492420362\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1459352550 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459352550\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-251436371 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:21:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZiNUlKQmFWREZiOFhJbWZPOHN5T3c9PSIsInZhbHVlIjoiM1JZZnhYek05MDlBcUJndjBoOUJsaVQ1T01KWFdBSXdiREpqdy82MXNrbHFkVkNPejJMT2NIRkFidVV3TTJPYTFnZWdaZHcwOXFEM0lzczVqMFNCazJPTFpGUTZVVU1rTzE3R0EzcnlDdFd1MnRJa3RXd1VxdzZTK29uNEZ1QVpnMjh6RU5TalptcEcwalJMSXBzdnhwMW1qbU9Pa1NNVXkyWFFkWDl3ODJ2bmI5MmVKRTdVK1h2RVd1S3JxdllNcVU3OXlmYU9vcmk2RE5DdGhxSlVNOU5acDMvQUU1UE5DNU54ZFdsRXJvRGRLMkM0bmFxRU9vMWcyZFhscWgrZ0RBQjdMVTVDRzMrRWJobzZYTFl5VGxMelh4YnN3dGN3Sm1HallPNSt5RlNwdGdnK1FaMGd5SC9qV0JvYm9MZjE3emlCSUNTbEwwa2VYQzBFTTR1TCtSL0o5dkJXS2lqQk8wdGQ0djNVcWtuUE1rNkxUNk91dHNWeU5yQVdvZ2FtMnplOHhWcUZWZ0REOXh6NHV1R0RKbkl3a1B2Ymp3dC9qenVJUlEyKzZlTFZFS1F3U0lDYzc3MVdYRDc3bGczUDQyUTRxeEpEdTNUaHdvYXU5ZDVKK3JwWFFkR2o2THd5WVpNMDJjUUU4aUwxbFpRMHM1Q0k3bTFkK0tXNTJ5VWciLCJtYWMiOiI0NTJkNzZkMDZkMjE4YmQ2ZmU2ODhlMTkxYWMxMzQ4MmQ3MTQ2ZmJiZDMzNDk4NmRmYTgzNDhlMGEwM2M2YzlkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNJUWFvbm1YWDFNZkpLdDRlRnYwaGc9PSIsInZhbHVlIjoiZWRZYzdkWU1MWDhUYlBRQlA3bGlaMWQrQlZJOXFhcElla2xIaGNxRlBldXZwZ09pZWtKelFTV3NvaE1wdlFGSkl4cktVbm42MlRocmpaZmlDOGNxdDBnQzErckREMVBJc3BXR281MHRXb1dFSE5ZVlc3T1hvOFVwT2RURmV5S0N1QlhhN1NvSFd0UlZnajJsRUdnQm05YmVyUm1LSHRsN1BxZ3EzQVF5NkZEdnREMWcyQTFjRG9LQktjZkUreHNvaUJJRTZmZ3BSbjFOeWxvTmJFL0NMNm5UV05PSCt6VFN2V2NlUE45S0R5OW5sS2VVS2F2ZkM0NVo5MnVvMmNxRkkxcWZmcUJqc2JqMWlyMmtzb2ZkZzMzUFVqVGExMHYvZGkxS1pkRjgzaVZNMHBnMm5SMXhsQnA1YU05VWJBSlplc05mTEg4U1N1aEFGaVhrdm01VGdQekJzdmYyOERmTmdwSCtleFFjZ041WHU3SU9FNlhhay9pdE5xbXRNTHNSdEFmUk1jZjE5dzR4RVlDQzFzTHZOV3paQ1ZESTRLcnZFOXNzMHU4dmpvdENaYnB4Y09kRmlMb040Q2xhd3c2aVdtVkxyN1dnYkorRG5xYmJyYVVqNFcveGZFODZwYUFVd3JrY0lhWFRxU0QzUG8veDNObnA5aFZrc2IyYXY3ZWMiLCJtYWMiOiJjYjZjZDY0ZjM3Mzg5ZWUzNWJmN2Y2OTA5MWYxZDBiNzUzNWY1MzhkM2RjY2VlMWYwMjZjZTI1MTMwZGEwNzM3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZiNUlKQmFWREZiOFhJbWZPOHN5T3c9PSIsInZhbHVlIjoiM1JZZnhYek05MDlBcUJndjBoOUJsaVQ1T01KWFdBSXdiREpqdy82MXNrbHFkVkNPejJMT2NIRkFidVV3TTJPYTFnZWdaZHcwOXFEM0lzczVqMFNCazJPTFpGUTZVVU1rTzE3R0EzcnlDdFd1MnRJa3RXd1VxdzZTK29uNEZ1QVpnMjh6RU5TalptcEcwalJMSXBzdnhwMW1qbU9Pa1NNVXkyWFFkWDl3ODJ2bmI5MmVKRTdVK1h2RVd1S3JxdllNcVU3OXlmYU9vcmk2RE5DdGhxSlVNOU5acDMvQUU1UE5DNU54ZFdsRXJvRGRLMkM0bmFxRU9vMWcyZFhscWgrZ0RBQjdMVTVDRzMrRWJobzZYTFl5VGxMelh4YnN3dGN3Sm1HallPNSt5RlNwdGdnK1FaMGd5SC9qV0JvYm9MZjE3emlCSUNTbEwwa2VYQzBFTTR1TCtSL0o5dkJXS2lqQk8wdGQ0djNVcWtuUE1rNkxUNk91dHNWeU5yQVdvZ2FtMnplOHhWcUZWZ0REOXh6NHV1R0RKbkl3a1B2Ymp3dC9qenVJUlEyKzZlTFZFS1F3U0lDYzc3MVdYRDc3bGczUDQyUTRxeEpEdTNUaHdvYXU5ZDVKK3JwWFFkR2o2THd5WVpNMDJjUUU4aUwxbFpRMHM1Q0k3bTFkK0tXNTJ5VWciLCJtYWMiOiI0NTJkNzZkMDZkMjE4YmQ2ZmU2ODhlMTkxYWMxMzQ4MmQ3MTQ2ZmJiZDMzNDk4NmRmYTgzNDhlMGEwM2M2YzlkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNJUWFvbm1YWDFNZkpLdDRlRnYwaGc9PSIsInZhbHVlIjoiZWRZYzdkWU1MWDhUYlBRQlA3bGlaMWQrQlZJOXFhcElla2xIaGNxRlBldXZwZ09pZWtKelFTV3NvaE1wdlFGSkl4cktVbm42MlRocmpaZmlDOGNxdDBnQzErckREMVBJc3BXR281MHRXb1dFSE5ZVlc3T1hvOFVwT2RURmV5S0N1QlhhN1NvSFd0UlZnajJsRUdnQm05YmVyUm1LSHRsN1BxZ3EzQVF5NkZEdnREMWcyQTFjRG9LQktjZkUreHNvaUJJRTZmZ3BSbjFOeWxvTmJFL0NMNm5UV05PSCt6VFN2V2NlUE45S0R5OW5sS2VVS2F2ZkM0NVo5MnVvMmNxRkkxcWZmcUJqc2JqMWlyMmtzb2ZkZzMzUFVqVGExMHYvZGkxS1pkRjgzaVZNMHBnMm5SMXhsQnA1YU05VWJBSlplc05mTEg4U1N1aEFGaVhrdm01VGdQekJzdmYyOERmTmdwSCtleFFjZ041WHU3SU9FNlhhay9pdE5xbXRNTHNSdEFmUk1jZjE5dzR4RVlDQzFzTHZOV3paQ1ZESTRLcnZFOXNzMHU4dmpvdENaYnB4Y09kRmlMb040Q2xhd3c2aVdtVkxyN1dnYkorRG5xYmJyYVVqNFcveGZFODZwYUFVd3JrY0lhWFRxU0QzUG8veDNObnA5aFZrc2IyYXY3ZWMiLCJtYWMiOiJjYjZjZDY0ZjM3Mzg5ZWUzNWJmN2Y2OTA5MWYxZDBiNzUzNWY1MzhkM2RjY2VlMWYwMjZjZTI1MTMwZGEwNzM3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251436371\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-364951861 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364951861\", {\"maxDepth\":0})</script>\n"}}