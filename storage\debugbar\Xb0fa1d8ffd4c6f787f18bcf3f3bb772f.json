{"__meta": {"id": "Xb0fa1d8ffd4c6f787f18bcf3f3bb772f", "datetime": "2025-06-28 15:28:00", "utime": **********.425364, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751124479.927705, "end": **********.425379, "duration": 0.49767398834228516, "duration_str": "498ms", "measures": [{"label": "Booting", "start": 1751124479.927705, "relative_start": 0, "end": **********.364482, "relative_end": **********.364482, "duration": 0.43677687644958496, "duration_str": "437ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.36449, "relative_start": 0.4367849826812744, "end": **********.425381, "relative_end": 1.9073486328125e-06, "duration": 0.060890913009643555, "duration_str": "60.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45615312, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00254, "accumulated_duration_str": "2.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.397563, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.228}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.409693, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.228, "width_percent": 13.78}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.415405, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.008, "width_percent": 12.992}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-124891096 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-124891096\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2077528124 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2077528124\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2086276487 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086276487\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1901588177 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1881 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; XSRF-TOKEN=eyJpdiI6IkxDaURONkhWK2RaUVNlcDh2eWRIV1E9PSIsInZhbHVlIjoiWlliMm9XUjMycDlSWlpmeG9XQ3dqdmo1UXNHRmtjUEZKc2ZJejlZNHlnMTZHRnA4aGU4YWo0ZjJHMDdJRnZ0QmRvMGZweVRPTlZrR3dvS2cveXQzNFgvZ0tJZ1M5ZkNXV3piMlJKaWRHYzZvZ0hLVnNaNmxhVDFYa091d0lYazdMRnB6T0lRUHk2eXZlSzBvb0tseXI4Y05BWlk3M1oyWEprMWRDT1BJNWEzbFllY25ya01EbEFOYi83b0tSY2FibUxJU1VpRFlwM3Vqc3hmTExoaldYdEVHVURlUTlvM3BvNlJpOVZoTE8yTmhOc05xQ0k1RFhtTkI3Q0ljdkQrWWdYQS9hUGd2UEg3QXh4dkpQd2s2ZWczQVFjZVVESHVrcnpENm4rNm1QSm1GMHFkTHVaMGJORUlhUlhXZnF2Q1A0MUszeUNEQlhnQy9Fa2NGbk56L2xWcDdWSHpOekRHTjlzMlMwbjkrWWV5ZmZTbVQzc1lwREN3T3dmV3RibUN4aXMvczdoL0xiVHNpNWhhUGpFSVFaRjBVcERXakcvbm9FT080eDQxRDkyUEJ4dUU0UEVKdkJlQzFCb0EvSVovNnRURlhscmpqYlp1TUNPbUtXOVpFUGkvUG1MSzIrb1FDb2prTUo1TVN4anVqOXpOVTBwYlBTMlJ1S0lHemQxY1IiLCJtYWMiOiIyNDQwNzBjMWIyZmIyMTJlZjE0ZGEwNTNmMzI0YWUyNDk2ZTYxN2Q5ODRhZjc4NmMzMjYzYjdhNGFiYjUwMGIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBjRGlqZ1dESjAxTUc1STByZXVJUXc9PSIsInZhbHVlIjoiamJNelhqUDJPVTBNSFkwbnlWMS9tN0tsQ1NqL0FnSHZPMG94TDBUZUtGVUcrVXQ5OHV4UWhuM2NmY01WcXlaU3NHbmdOcm5BclpDMVNzNXArbXIzZXlremdUd3ZtYWF4MzNDbVZWWldrUTdOaGRmKzRuNC9lMjBJb1A2YUU2UmhRbVdNNFdObk41SzBwcDBQTW4yVU5MM3RDWTRyL0lkUCt3UnFtMWhWLzFWOVhFRC9OU2R0WnVwSkVmU1lJRWM1cXhvYTFRdVA3QXhodERoMEJkaDJ5NjBVaGVvOTJFM0RoVFNzTE1iQ0ZXR3JOc3dkR1p5UVh6b09iQkIzZC93ZS9CSEdPeWh3bzd2RjJrNFRlOUowU2pyMFBYbWNtaHhSSnlZdmtTbXN1SW9wZCt1MGtYdmJYZnB1ck12L1pwZjFia0xNWlByNjJBdXpYRk9rRUZ4MlhHWmRFRVdwUy8zMGRmTG94M0NLYzhPZ0VFVW12N0M3b0hFNmhtRklyOXZ5UmRXVklVVWl2b0dGSUhqNVV6TkFSbUdYbENrbGw2cXY5ZFRZZ2N2T0htZlV0TWxnbkFicm4xdzQyeDlpQnk4TzVUZ2RERDZTSHpSRW82L3VHMnhlODNyU0I5UVlSN1pXYndpay9iZTI3d21aQytGOGFjUEFtR1ZzZWUyZFJFcWMiLCJtYWMiOiJkMjgwNzEzMjJlZGVlZWVmYTM3OTZiNmVhNDM2ZWNiZDYwZTY5NTliYzE4Njk1MDZlMjk3Y2ViNDYzNTE3MTJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901588177\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1145879844 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145879844\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1535541784 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:28:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJCSVlwMUtPZytKWmliWFVQblRzbEE9PSIsInZhbHVlIjoieUcwZmdabjRBOVVPVGVXNW1JOUFjYU11ODB0UlB6UDFvWUNYeWE0UnJkOHVFR2RnQy9KNURNUEhOUDlUYnNXbE4vRnFZLzRpUFRacUcrMzE1TWRVV2UvQ0J1TFk1dnl0SUwvRFZMMmhVOWR0QVFjam43WDRsendZWFh3S0JoUTA4S3dDS1dnZFVXVmRBeXRMMkg4UGx5cEJVdHNkZEpjeHUxMGhzVjcvd3ZJZnU5cWwxQ1FESDArSzgzdStnbUs0WlN4ODlSbHJNR25jc3crc1R2Qml6Q1pYeks0RHBtWEFxNjBiSHY0ZVVVeDZPTUdzbUY2QzFSdUQ0c25mWXpTNHZjUzFZUWY1MWkvbmdLRmtEMjVTaFpySG94c3RvQ3k2WFA2Y2FVTkJUc0g0UjdqbGE4ZWUzYmJTd29kc08wV0V2Ni8rdTZObEpLOXNSRDVDNWs5Lzl1RWUweFl0ZCs3UURHSng1SmhlcVVvaHhpOXR4RHkwczY5NURnREllSXVETk5mNTJWNEQwWWdabzZFS2hvcVI0QVViVHRQcUp3ZjRBeDBFN0tTazZqS0VoaDZUcmlmaU1jT1ZkWEJGT3ZUUjZqVUxqWUZNMDhlL3FWVUdzb2NnQWlKNVVlTGcwUFhiQUJqL1U5QW9ySFJySnZ4K202V2huTlBnMkdRa1QrQzgiLCJtYWMiOiJjOTY5ZjIxZmNmNDFhNWJjMjM4Y2JkOTlhYWIxZTg4MzA4MTFjYTU5YTc5ODZhYTZiYWVhZjIzMGJlYmVlYThmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:28:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVuYWNIaEY4UkdROHU5aFQrRG5Qb1E9PSIsInZhbHVlIjoiMWFhMmYxR2Q2TElTMWxIbFU0TWFLRkRjYksvSFJTTUJ3OTZIY2g1MXZETkd5MDJvQTR3RGJpenJJZTU3bkY0eHE0TXNKZG5QcGJtMndOWHdHZVBheWVralZUcTJoN1RxcWZHM2c5SXNqTGJyZERsaUlSalMvaHlVbnNLamRvVDhRNXJHWjkwUXFWUWw1cUpTY1cwb1FUUzNTbFVNbHkvdEVtaytnMjdWSEE5M1d0WURUWFFRa1MzQVBvVXFaTENyQkdkTkxibFZhMjYxNlFYbE03MTY0WHYzcEdSUzE0bXZFREFHMytIaG9VRlZVRmFQbkhsa1poNloyUGFEd1YxKzZlTk1jdWREWHFJajN5U0xXWFF5S2JvQnoweWZ3elNvYkwwbm1LUk5MbUFVVVFaS1RtaXBwVDZsOEQvOVFTbjRaeXNtRW9UZEJZWDdudU1NNGNuYTZKL2J1NkVCbjluSmRnczM3QzhVd2FZcGQ2OU1SVnlxdy9vZHhPK1VYUFljUEZUTjVkdlVJcjJvNGxiRWV5Q1pvN3RxeGU0Z1VMdVlvZlVTZUZHNElMbnZPMFZhRnVkZThIZEZnd3RmZWpURitnL29xOFFmY215VTUxL0YyTEhLd0N4MnFjNGtvTklDQ0xuc0taT1ZFVnF6VjFwdFNnS0ZBa1hsQkJaejZmVzgiLCJtYWMiOiIwZWI1ZjA5NzkyZTFlYzZiZDJkNDNkZjllYTU2ZDc2ZjA5OTY3NzYzZTNiZDgxZDcxYjEyNTI2NDkxMDllNjQyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:28:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJCSVlwMUtPZytKWmliWFVQblRzbEE9PSIsInZhbHVlIjoieUcwZmdabjRBOVVPVGVXNW1JOUFjYU11ODB0UlB6UDFvWUNYeWE0UnJkOHVFR2RnQy9KNURNUEhOUDlUYnNXbE4vRnFZLzRpUFRacUcrMzE1TWRVV2UvQ0J1TFk1dnl0SUwvRFZMMmhVOWR0QVFjam43WDRsendZWFh3S0JoUTA4S3dDS1dnZFVXVmRBeXRMMkg4UGx5cEJVdHNkZEpjeHUxMGhzVjcvd3ZJZnU5cWwxQ1FESDArSzgzdStnbUs0WlN4ODlSbHJNR25jc3crc1R2Qml6Q1pYeks0RHBtWEFxNjBiSHY0ZVVVeDZPTUdzbUY2QzFSdUQ0c25mWXpTNHZjUzFZUWY1MWkvbmdLRmtEMjVTaFpySG94c3RvQ3k2WFA2Y2FVTkJUc0g0UjdqbGE4ZWUzYmJTd29kc08wV0V2Ni8rdTZObEpLOXNSRDVDNWs5Lzl1RWUweFl0ZCs3UURHSng1SmhlcVVvaHhpOXR4RHkwczY5NURnREllSXVETk5mNTJWNEQwWWdabzZFS2hvcVI0QVViVHRQcUp3ZjRBeDBFN0tTazZqS0VoaDZUcmlmaU1jT1ZkWEJGT3ZUUjZqVUxqWUZNMDhlL3FWVUdzb2NnQWlKNVVlTGcwUFhiQUJqL1U5QW9ySFJySnZ4K202V2huTlBnMkdRa1QrQzgiLCJtYWMiOiJjOTY5ZjIxZmNmNDFhNWJjMjM4Y2JkOTlhYWIxZTg4MzA4MTFjYTU5YTc5ODZhYTZiYWVhZjIzMGJlYmVlYThmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:28:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVuYWNIaEY4UkdROHU5aFQrRG5Qb1E9PSIsInZhbHVlIjoiMWFhMmYxR2Q2TElTMWxIbFU0TWFLRkRjYksvSFJTTUJ3OTZIY2g1MXZETkd5MDJvQTR3RGJpenJJZTU3bkY0eHE0TXNKZG5QcGJtMndOWHdHZVBheWVralZUcTJoN1RxcWZHM2c5SXNqTGJyZERsaUlSalMvaHlVbnNLamRvVDhRNXJHWjkwUXFWUWw1cUpTY1cwb1FUUzNTbFVNbHkvdEVtaytnMjdWSEE5M1d0WURUWFFRa1MzQVBvVXFaTENyQkdkTkxibFZhMjYxNlFYbE03MTY0WHYzcEdSUzE0bXZFREFHMytIaG9VRlZVRmFQbkhsa1poNloyUGFEd1YxKzZlTk1jdWREWHFJajN5U0xXWFF5S2JvQnoweWZ3elNvYkwwbm1LUk5MbUFVVVFaS1RtaXBwVDZsOEQvOVFTbjRaeXNtRW9UZEJZWDdudU1NNGNuYTZKL2J1NkVCbjluSmRnczM3QzhVd2FZcGQ2OU1SVnlxdy9vZHhPK1VYUFljUEZUTjVkdlVJcjJvNGxiRWV5Q1pvN3RxeGU0Z1VMdVlvZlVTZUZHNElMbnZPMFZhRnVkZThIZEZnd3RmZWpURitnL29xOFFmY215VTUxL0YyTEhLd0N4MnFjNGtvTklDQ0xuc0taT1ZFVnF6VjFwdFNnS0ZBa1hsQkJaejZmVzgiLCJtYWMiOiIwZWI1ZjA5NzkyZTFlYzZiZDJkNDNkZjllYTU2ZDc2ZjA5OTY3NzYzZTNiZDgxZDcxYjEyNTI2NDkxMDllNjQyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:28:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1535541784\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1160668117 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160668117\", {\"maxDepth\":0})</script>\n"}}