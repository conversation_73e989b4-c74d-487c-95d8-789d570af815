{"__meta": {"id": "X8ebc0e8f7ead1a80b1c13cda4fb8686f", "datetime": "2025-06-28 16:34:32", "utime": **********.390659, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128471.853409, "end": **********.390685, "duration": 0.537276029586792, "duration_str": "537ms", "measures": [{"label": "Booting", "start": 1751128471.853409, "relative_start": 0, "end": **********.317908, "relative_end": **********.317908, "duration": 0.46449899673461914, "duration_str": "464ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.317918, "relative_start": 0.4645090103149414, "end": **********.390687, "relative_end": 1.9073486328125e-06, "duration": 0.0727689266204834, "duration_str": "72.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45716856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00353, "accumulated_duration_str": "3.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.355146, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 56.091}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.370282, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 56.091, "width_percent": 30.028}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.377945, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.119, "width_percent": 13.881}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1923864550 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1923864550\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2057722328 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2057722328\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1601883373 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1601883373\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128238007%7C50%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllXeVRneTRSN2Z5d2VVV3Z1TGtTQVE9PSIsInZhbHVlIjoidEtvcFJWMlhpTHJmYmwxMTJJSWlNMHg3eGFkQ1RhOWNES0hibW9IMFdKZGVFSkFaRVV1dzQrSCsxS3lRTEx5RUw1TEJnZ2twWE94MmFYdXRPdERNSUEwMllGUWQxbFZsZVBsN28xaGFKaGgyNGlrUFU4YWcrMnVhYlVrbm5OUGJpZWxvU0lDa2M4eEpVZkFKTDZ5SXFCcFFaYm1oa1QvK28vMExuWjdiRGZlZUtPeUZta0ZlK3lwdWZ5a2krcFNudEgxSjJZeGE3T3plM05SY2JDbVZFQnF5Q3FVZklmandqeEhRc1p3RnRRM0t5dEY0OGZieFBMMHhCaFp4aTE3b0VxWlI3THRuRTVzVFFMWXJwNmh6eGY4MCszTVRmNk5ZOUU4czhlZlBhU1ZXUzBNQkhoSGVtdHF3Z2huU0x5V1VOSXdwWDlWc0psd1d6c0Y0emJDaVpvRUU5UnlxbzJrVFRLRytndHdheVZjalZqY2d1QmdEWHdiMVF3bU1hMCs3Nmhxcks4b3QwZWQ1Q3JHc0RyeUJZM2NnbDc4cnA0WjNHRm1QWC9YWEpNNXJEK2VSMldMa1UyRmhKQitmUy8yOUFXMnhvN085ZDBJTlExTTJuN0VvV3BnSFFPUnliSk1yUGtQSGpXKzZZSjVyREwwYUEvNWhqRlM0RTFwSHhUcVQiLCJtYWMiOiI0NWM2Y2M0NzExZTY4MGQ5NWJmNTg4NDFjNzczOWRlMmNjOTFjMjI3NTFhZWMxMTA2OTMxYTg5YTU5MTNkNGFlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjN6WXBORGtzN3FoeUMvaWIxalFVREE9PSIsInZhbHVlIjoiQVZ6a05hcFJMWEtqZE8zZFdwSFNDTWVKZGw0Yisrc1NOQjFhd3Z2NmhNSStKOCs4NnM4dHhLd1E2cGQ2VFJJbVVWQ3dYNEFEbjFVMmFIU0hJc01FV2NlZmV5SG5EM2ZBdHRucFVUOUFrM1JMbWNGcHQ0bWEyOHk1bm1wT3JEVFRySlhJNWJzTXJ1MksvMXRNZ2MwYXdRbWQ2dWVWcnhPUkJoekJ0NDZwNXRvSDBHK1ROR0RFbHExL21wajdhclVpam1GTEVEMVhycThZZXJRWFZIaTg4VVY5L2ZpMG9HeWFWMzV5M0RYWXdsNW5NT29ja0I3VTVtR1J4R2ZoM3dWWHYwR3ZHWTYxTThXbHhzMk4vQ05HanpZTW9Ea0xtcDJtYUJJbnlMZ3BRMkhsUUx1anpiejdFTWhJTGNuS3lJb0VGVWYvekdPbWdIcTVSWFlPUzFQb3h6WFE4TENPS0lybWJDUVFIQUxxaG81WVlYUUNkY0VRRlJva2dPMWZOZHV0NkZTYmY5RCtxR0EyWWYzQUl3eE5WTm5BRnlJQVFaclgyMEJScExUbmRYdDZaZ2NmVC9KTUZ0NVlONjdVYkJUdC9oemVRUkxFMzQrYTVjTlFTVTRGRlphbkNtcExRcENBTTh3cGdHS2l5Vm1JU2VyR1U0VkYrSDF4WnRaT3FXREsiLCJtYWMiOiJiYjJjZjA4MzA0ZTc5MzI4MjE1NTFjZWJmOTMwMmY4ZWVmMmQ1ZmNhNjFhZWZjNTY4MGNmYjg2YzVmYWUxOTdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1448011975 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imo3T0N2Uy8zdm5ZVjdweFA2Z002RXc9PSIsInZhbHVlIjoid3Fsc1lUU0V2dzczdHBtYnVhMWdHY29sbG50Tlo1ejVQMURjMis1ZkVJK3YvZ21nR01LOXhsemxIVGFEcjBlYnFnNGxxdE5MLzUrRm9wK3Z4ak5LRzBaR2JZamFNQmoyZThuQnRpL2ZuT2k5RXVHYjExUnQvcDhoZlpRRkFnaGRVUTVFRDhaRDZBOWgzQ1JWMmFJYVhqZVQwNWhvMU0zdDdMbzFEVUlaOGFQemVHRDVDdXJoY1RKM2d1eW5panNZNnozeDFha2xmNEFNZnJ6dWR2c2JTdTRuNlNOZGZWWFBsK3YzeVQ3eWRhME9kTFcwTWREQ1JyQTYzcENOZjlZdDJFMXNwQ1QxWE5tVmNPYTBuZzhsWUg2WnRqSGx5c25kMEc2TEE3MlhiUnhuekt5cnJ2dlZQenVPWVEyUlZRaEFUTm1EYjhMbHFMQ3RPWUh5T3RUR0w2YXlneGJNQ0xwRVZrL0NoQ3kxVlZyUnNoUEpJckJVcVQzWHdlVG9ZMDRPOExjd0VJakdHOURBa0E0dnB4YmF6UzZYL0Vjd0V3TEM2MzlVQnltcjl5ZHcwc25yRmsxSG9BRFZCS0JLV0hIanVLSVhrOGQrRnp2dU5ueW9rL3ptZEZjd0JtblgwNkVWY3hwVVQyVWdFQTNMZm9sTmhtWGllQ1BZeHJJc2x2SkkiLCJtYWMiOiJiM2EyZWM5MzlhNzhhYzg3MDVkMjRkOGUwMmEyY2VlNjI4ZDhiMDc0YjhjNGVlOTE1ZTNlNThmZWQ2ZmRlNDBiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii80TkpVYWxhNEZ4Z1lvWU9EK0JETWc9PSIsInZhbHVlIjoiQWJaLzg0ckpabVhRNFBnY3J5WnJFODdYTitDbVVyZ0wvNjh0OWNHcldZSjQ5K0RseGFxdStKSHR5aC81QnpWd0h3VEdPMWwvY0RySXowZEhjWHJPK1pSb0pnUG4rRWZLR3ZKSUNBbjkwM2thQ0FlbUcyalBuNE5CclFoWDlKOEZTQ3F4N09kV1BER21aNHBLZHJtcW1Gakt2RWxHRlRralBTSHJEOGVlTmpSUDVUTDRVa3hOZ2FPWGxpSXFCc2d5c0h5Y0Y4WGh6MFg0YzlESytneGJRRG5Va2VkU2h3L1F6WlNxeHdxMXp4U01iQmtXT1daWitmV2ozbzBhcmJuRnRJVUF5OHNrczFraEVTTXRqelBmRkdYdjRlNndSSHdjMjlNaERIY0tGMk9LOGh4S0NDSWVYREVKUmVTb2xWeW55ODR0ZlAyZjgzQlZqdVpWQWEvSWJIRlFZcHFZS1I5QWpuU214WFpONjFJQmFNNXVCd1dsQkU2dXBYUW10ZmNKb0UvRllNNUhhTDl0d3BGVU5PVTY1MDk2cEhQTGJKY29paHlhUmE4dG9FRTBFak1YYUdNRTA5T1kxRGNIU0dZckd6c3NiL29IdEx6SEVJaDVBYVhZNHlaeXJaWjg1K01nK0tyUE90MmpMREV5RFcyUVh1aHBwbms0VlJsc1dxQ1kiLCJtYWMiOiJlODA1MWY4ODU1OGRiNjY0ZjliNzE0MDVjNWIyZTRiY2FkZDRlOTBkZmZjOWUzOTZlNzhiMGZiYjhmY2YyNWJlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imo3T0N2Uy8zdm5ZVjdweFA2Z002RXc9PSIsInZhbHVlIjoid3Fsc1lUU0V2dzczdHBtYnVhMWdHY29sbG50Tlo1ejVQMURjMis1ZkVJK3YvZ21nR01LOXhsemxIVGFEcjBlYnFnNGxxdE5MLzUrRm9wK3Z4ak5LRzBaR2JZamFNQmoyZThuQnRpL2ZuT2k5RXVHYjExUnQvcDhoZlpRRkFnaGRVUTVFRDhaRDZBOWgzQ1JWMmFJYVhqZVQwNWhvMU0zdDdMbzFEVUlaOGFQemVHRDVDdXJoY1RKM2d1eW5panNZNnozeDFha2xmNEFNZnJ6dWR2c2JTdTRuNlNOZGZWWFBsK3YzeVQ3eWRhME9kTFcwTWREQ1JyQTYzcENOZjlZdDJFMXNwQ1QxWE5tVmNPYTBuZzhsWUg2WnRqSGx5c25kMEc2TEE3MlhiUnhuekt5cnJ2dlZQenVPWVEyUlZRaEFUTm1EYjhMbHFMQ3RPWUh5T3RUR0w2YXlneGJNQ0xwRVZrL0NoQ3kxVlZyUnNoUEpJckJVcVQzWHdlVG9ZMDRPOExjd0VJakdHOURBa0E0dnB4YmF6UzZYL0Vjd0V3TEM2MzlVQnltcjl5ZHcwc25yRmsxSG9BRFZCS0JLV0hIanVLSVhrOGQrRnp2dU5ueW9rL3ptZEZjd0JtblgwNkVWY3hwVVQyVWdFQTNMZm9sTmhtWGllQ1BZeHJJc2x2SkkiLCJtYWMiOiJiM2EyZWM5MzlhNzhhYzg3MDVkMjRkOGUwMmEyY2VlNjI4ZDhiMDc0YjhjNGVlOTE1ZTNlNThmZWQ2ZmRlNDBiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii80TkpVYWxhNEZ4Z1lvWU9EK0JETWc9PSIsInZhbHVlIjoiQWJaLzg0ckpabVhRNFBnY3J5WnJFODdYTitDbVVyZ0wvNjh0OWNHcldZSjQ5K0RseGFxdStKSHR5aC81QnpWd0h3VEdPMWwvY0RySXowZEhjWHJPK1pSb0pnUG4rRWZLR3ZKSUNBbjkwM2thQ0FlbUcyalBuNE5CclFoWDlKOEZTQ3F4N09kV1BER21aNHBLZHJtcW1Gakt2RWxHRlRralBTSHJEOGVlTmpSUDVUTDRVa3hOZ2FPWGxpSXFCc2d5c0h5Y0Y4WGh6MFg0YzlESytneGJRRG5Va2VkU2h3L1F6WlNxeHdxMXp4U01iQmtXT1daWitmV2ozbzBhcmJuRnRJVUF5OHNrczFraEVTTXRqelBmRkdYdjRlNndSSHdjMjlNaERIY0tGMk9LOGh4S0NDSWVYREVKUmVTb2xWeW55ODR0ZlAyZjgzQlZqdVpWQWEvSWJIRlFZcHFZS1I5QWpuU214WFpONjFJQmFNNXVCd1dsQkU2dXBYUW10ZmNKb0UvRllNNUhhTDl0d3BGVU5PVTY1MDk2cEhQTGJKY29paHlhUmE4dG9FRTBFak1YYUdNRTA5T1kxRGNIU0dZckd6c3NiL29IdEx6SEVJaDVBYVhZNHlaeXJaWjg1K01nK0tyUE90MmpMREV5RFcyUVh1aHBwbms0VlJsc1dxQ1kiLCJtYWMiOiJlODA1MWY4ODU1OGRiNjY0ZjliNzE0MDVjNWIyZTRiY2FkZDRlOTBkZmZjOWUzOTZlNzhiMGZiYjhmY2YyNWJlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448011975\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1900958271 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900958271\", {\"maxDepth\":0})</script>\n"}}