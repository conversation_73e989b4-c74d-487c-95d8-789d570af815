{"__meta": {"id": "Xe0cd6b318be0b395506b0bbc6627d10a", "datetime": "2025-06-28 15:19:46", "utime": **********.539314, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.119016, "end": **********.539332, "duration": 0.4203159809112549, "duration_str": "420ms", "measures": [{"label": "Booting", "start": **********.119016, "relative_start": 0, "end": **********.473529, "relative_end": **********.473529, "duration": 0.35451316833496094, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.473539, "relative_start": 0.3545231819152832, "end": **********.539334, "relative_end": 2.1457672119140625e-06, "duration": 0.0657949447631836, "duration_str": "65.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45614536, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00266, "accumulated_duration_str": "2.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.504837, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.038}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5155401, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.038, "width_percent": 17.293}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.521115, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.331, "width_percent": 17.669}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IjBkYlFseWZHSStDSmFUNGtWd2hWNWc9PSIsInZhbHVlIjoiR2ZwTVkvNkh5RFY5Skdtb1AwbVo4dz09IiwibWFjIjoiNTY2ZWJhZWYzZDQ5NDE2MmYwYWQ5MjUyN2IwZTIzNGQ5ZWQxOTk1YzkyZTVkZTc0MmZlYjhlODM4M2M3Y2VkOSIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-708555679 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-708555679\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1647607754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1647607754\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-950240717 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950240717\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IjBkYlFseWZHSStDSmFUNGtWd2hWNWc9PSIsInZhbHVlIjoiR2ZwTVkvNkh5RFY5Skdtb1AwbVo4dz09IiwibWFjIjoiNTY2ZWJhZWYzZDQ5NDE2MmYwYWQ5MjUyN2IwZTIzNGQ5ZWQxOTk1YzkyZTVkZTc0MmZlYjhlODM4M2M3Y2VkOSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123984397%7C12%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVKcFVFZlhKMmxEYmRSQTZYVEpnZXc9PSIsInZhbHVlIjoiUGxWNFBlOFhDR2lSKzUzdmVpOVB0aGhpakJDZUhmY004NW8ySUJwcnZCdkljZWllUEJtTldzWVNKcHpaMVozRlJPbUtUQ3pWNDFNcy9ncmVKQmxUQkdQcVZhK0dzMnp2UjdLaXF3Q1pjRUNKd1FIQW1sUHpLek9xQ3A5ZEhRcUtJYVF6RnduMFg0eUlQNGdyYUErKzB4b3RveEtsSHNGQWxsY2JmSWNjMTJ1ZWcxcEV5U2VpcWJMaW5ZbXZ1NU5ZUDFzRTlLajdEY3JYc1I5MjU0N2t2Z2R3NmVWTFZoN2tUSW0rbUtyRXdUTThtUGQ1U3hrREdmRmdWdFpnL1ZvV1k4clZnQUlVRDlsMm9TeGt6c2tWbldpbWR5NitTVFJIc0hzaStRcDBKY1ZVenlPazExNE9aWk5PVUp2ZmhtZTFGRXEyb2xSQmZGTm5BMHY5TjlkRlRpZnpmSm4yRkNwaUVmYnAzeFBUYmpLVVBjb2c5ZEluSE9uSzZaamNDdURXazFoQUU2SmZKc3htcTFWY1NjMXBIWktZMzVNTitISnRRamVOZUxDM256Q0Q4d0dCUFZxTTY5WHhibEtvRVR6Y1NUdHRrUzEwNElqclhLRy93bmlieUgxYlgvTzFZSlhsU3ArdmNDQ09JSHczS0NnVVl2eTZiWDlaZ1ZKcUlpa3MiLCJtYWMiOiI2NGYyMWNkNmM4YTE3OWQwYzYxODY3YjBkNDA3NTZmOTUxODg4NjQ2NGMyMTQ5MGJmZGZiNDUyODY3ZjNkZDcwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IndHbzNadHVaeDdabVVYd2N0MDZXOVE9PSIsInZhbHVlIjoiUktDWE9qU3FaMm5qN2RjQmg3cXRZUzZlZFBHRTJ4NG5UOXJpdjgyS0o4SEtQSTM2bUVFbVZEWjlva2hVdFRUeGlMYk51N3FudU1MTm9NM1k5azRDeU16ZVhVa3dWMTNtV3RVUEF2d2J5clZXR3pwVlVqRSt5Y0s3bG1DRjFnMUE3b3B2YjNtRW10WUlBTWJZR20vY3kxVUw5cm43eG5INUZDME5veXljelBhRk90MnpkOXp6Mll1Y2RiMENKQnhMU21qbTVvWDcwaEl3NHlYNGFObnNFZTFnMnk3WU9JZVRiWGdZS2ZyZ0R0aCtodXhGZC9FSlJYc2hMTU9OV2ZRdWZZcTVlcHhFY3dMaG1aMmFCZEpPTHNpV29FeWVjSTdOeFpzQit1ZjQ0dTlsY0crSVIzRUZmTWNic0k2ekhhUlJ1MHRzZWR2enRXUHBSSm1KenlBZ0VVcENVTENGMytVZnhvRUt0U3FWWVlWOTRjcDZHMVRUQXd4Qjh0WVJJSXJ5ZXVtaGVQODNsL3BzaTZiaGoxcW9lUDdLL3pJRkF4U2VMNWtjYzR5b3ZTczlPSEJsc3RCRmJOL1FtdUpHZFlsQTNBKzlxT0Y0dUNRMGVHS3FsWnU2ZDBoS1AvUmk5MWZBYm9ncWI5NEIvMzNNT2p2eUZLWjd3bG1sZllGVXRON1UiLCJtYWMiOiI0NjhmM2RhMTNmOWU5YzE5MDk2NWVmNWFjMjRlMWNlZjMzMzg4ZjYzOTdjNzc5MTc5ZmI2YzU5ZTFhMDRhNjZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1562104518 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1562104518\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1149367508 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:19:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhMYU9ZMzZES2pHTGVlcHJINmVrdFE9PSIsInZhbHVlIjoiNW1mYmFjbHFnaU02QmhPRnI3QXBhazhralg5amZqcW1sWGNYUUFSS0g3c2tHVm51SjVZWSsySGNEMk5YZTN1MmZiTUtzM3BpcHJNaGJESGgvcnpwOXBjck41M1RyV3lVSGYzVGlqM1AyYlo4c3pUenp4b09oZnRrOUxQRStqRU14TzFlR1BQMW5hQTJoZmJ5TGNFVUgyOG1sWVJiTWRKS0ZsSlQ1T1dxeXo5dCsxYUFXeVpqWWhHbnJHSlBpeVRqRGhIc1haMjFQSE96VHdGV2xoQ1pMUzg3Rmw3SWIrOGNZNDF3QmVjSTJtVFhHbUt5djFWZ1g0UDhuSW00OVhaT29XOGZFNitpRUNFS1Uya3ZacFBHOGpZNFI1b29SREtRaUljN1BWYUtkd1B4WWJ4TUpaMG1JSDRvcHQ4ZDFuS3llYkl0RXFKL20wbE9GUFRBc1hTaXZUY0xMaWtzY1oyZW1JL2Fab2tBcjNKeC93YUdiVDZzNHc1UkRPZjBIWnhidDJ6a3V3cVNwTEJoalVsZVN5bldCOEQwWldDSDF5dkxiSlRITlNSRHg1QjlRK3QzbXlnUWNyVi95Mll1K2VqNGFDZzBxRGdaUzZSMFBqRGtFMStTb2ZJZWpoM1gyemd6MHE2ampqRTdvNTdKa3l1WWVPSFpzMU5sMERia2h1TE0iLCJtYWMiOiJiZWI4MWMzZGE1Mzc3NjRhNjEwZmM4NWVlMmZlNWU1ODg4YTlkMWQ0NTA4MWQwMDFmMDM0YjJjN2VmOTQ0MTRkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlN6Z21JWGwvT3pMTTRQMzBPeFFSREE9PSIsInZhbHVlIjoiWDEzdDdzMndGNFdNK1gxTERFUFp0d2xuUlNUVVR2N2daUDBPTkFzQzI3ZklwbWIxbFB2OTRldUM5NTFOdklEVm5YOHl2VEhnZk52aVZKVEtUM2xDVWw1aE1vcFl3TzlBcFl6c01OY2x5VW4xZU9yNlVuSWk1UFpBRURjM2hHSzBTSEoyTUY1YWZ2MUlHTkY1OFdZSVFzL1JqUE5sb0YyVzBvMm1WckZCSVJkWHRoNGFuc1FueUFSMXV5RHhoY0luNGpNTTRtNSs4eC9tWlhhL0toNmZmTFd1Q2E1aldYZmxNMzhVMmxvYndBeGtaSFB5eGRDaU5rYnQyT0pNUjk2VHdXbXg2YlU5SGVldExMZXpPMzQ0bWdwRWNWd0xNZzRJUWpyaUhjR2RiZm9sVFVQZk96UXJjeE9HK0tYWTNSRnBCd1lnL3oyRkRVRVBQV1dRUC91WURwMlRlZlFIZ3pSM082THJzUDdaWk1HL2wyaEt2eVZES2VNb2Y4RUhDYzdLejlQYXdGTGFpMTBJM1RtS25NU3JnSUkzMm1LRXNOVllRUmxWUmJ1NnMyRE1BU3NhaVdSTmF1bVBtSER1N0FhNS84bmQwWWg0Mk1BeENNTkZYcHZySGo2MFBQL0pVait6dUNocmFXZ1c2YXVwWG1ZTFpkVlVha0hYUkJNMk9DTGwiLCJtYWMiOiI4ZWZjMWE1MjE1OTk1MzEzZTE0N2M5ZmM2NzM3NGQxYjg5ODhhNmNhYTdjNzA3ZmE1NWFkOTY4YTRkYzcyNjYzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhMYU9ZMzZES2pHTGVlcHJINmVrdFE9PSIsInZhbHVlIjoiNW1mYmFjbHFnaU02QmhPRnI3QXBhazhralg5amZqcW1sWGNYUUFSS0g3c2tHVm51SjVZWSsySGNEMk5YZTN1MmZiTUtzM3BpcHJNaGJESGgvcnpwOXBjck41M1RyV3lVSGYzVGlqM1AyYlo4c3pUenp4b09oZnRrOUxQRStqRU14TzFlR1BQMW5hQTJoZmJ5TGNFVUgyOG1sWVJiTWRKS0ZsSlQ1T1dxeXo5dCsxYUFXeVpqWWhHbnJHSlBpeVRqRGhIc1haMjFQSE96VHdGV2xoQ1pMUzg3Rmw3SWIrOGNZNDF3QmVjSTJtVFhHbUt5djFWZ1g0UDhuSW00OVhaT29XOGZFNitpRUNFS1Uya3ZacFBHOGpZNFI1b29SREtRaUljN1BWYUtkd1B4WWJ4TUpaMG1JSDRvcHQ4ZDFuS3llYkl0RXFKL20wbE9GUFRBc1hTaXZUY0xMaWtzY1oyZW1JL2Fab2tBcjNKeC93YUdiVDZzNHc1UkRPZjBIWnhidDJ6a3V3cVNwTEJoalVsZVN5bldCOEQwWldDSDF5dkxiSlRITlNSRHg1QjlRK3QzbXlnUWNyVi95Mll1K2VqNGFDZzBxRGdaUzZSMFBqRGtFMStTb2ZJZWpoM1gyemd6MHE2ampqRTdvNTdKa3l1WWVPSFpzMU5sMERia2h1TE0iLCJtYWMiOiJiZWI4MWMzZGE1Mzc3NjRhNjEwZmM4NWVlMmZlNWU1ODg4YTlkMWQ0NTA4MWQwMDFmMDM0YjJjN2VmOTQ0MTRkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlN6Z21JWGwvT3pMTTRQMzBPeFFSREE9PSIsInZhbHVlIjoiWDEzdDdzMndGNFdNK1gxTERFUFp0d2xuUlNUVVR2N2daUDBPTkFzQzI3ZklwbWIxbFB2OTRldUM5NTFOdklEVm5YOHl2VEhnZk52aVZKVEtUM2xDVWw1aE1vcFl3TzlBcFl6c01OY2x5VW4xZU9yNlVuSWk1UFpBRURjM2hHSzBTSEoyTUY1YWZ2MUlHTkY1OFdZSVFzL1JqUE5sb0YyVzBvMm1WckZCSVJkWHRoNGFuc1FueUFSMXV5RHhoY0luNGpNTTRtNSs4eC9tWlhhL0toNmZmTFd1Q2E1aldYZmxNMzhVMmxvYndBeGtaSFB5eGRDaU5rYnQyT0pNUjk2VHdXbXg2YlU5SGVldExMZXpPMzQ0bWdwRWNWd0xNZzRJUWpyaUhjR2RiZm9sVFVQZk96UXJjeE9HK0tYWTNSRnBCd1lnL3oyRkRVRVBQV1dRUC91WURwMlRlZlFIZ3pSM082THJzUDdaWk1HL2wyaEt2eVZES2VNb2Y4RUhDYzdLejlQYXdGTGFpMTBJM1RtS25NU3JnSUkzMm1LRXNOVllRUmxWUmJ1NnMyRE1BU3NhaVdSTmF1bVBtSER1N0FhNS84bmQwWWg0Mk1BeENNTkZYcHZySGo2MFBQL0pVait6dUNocmFXZ1c2YXVwWG1ZTFpkVlVha0hYUkJNMk9DTGwiLCJtYWMiOiI4ZWZjMWE1MjE1OTk1MzEzZTE0N2M5ZmM2NzM3NGQxYjg5ODhhNmNhYTdjNzA3ZmE1NWFkOTY4YTRkYzcyNjYzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1149367508\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-807002158 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IjBkYlFseWZHSStDSmFUNGtWd2hWNWc9PSIsInZhbHVlIjoiR2ZwTVkvNkh5RFY5Skdtb1AwbVo4dz09IiwibWFjIjoiNTY2ZWJhZWYzZDQ5NDE2MmYwYWQ5MjUyN2IwZTIzNGQ5ZWQxOTk1YzkyZTVkZTc0MmZlYjhlODM4M2M3Y2VkOSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-807002158\", {\"maxDepth\":0})</script>\n"}}