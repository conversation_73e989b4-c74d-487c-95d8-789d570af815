{"__meta": {"id": "X2bdbe3346bf463f91034c487f39e35f7", "datetime": "2025-06-28 15:25:51", "utime": **********.066884, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751124350.636104, "end": **********.066898, "duration": 0.43079400062561035, "duration_str": "431ms", "measures": [{"label": "Booting", "start": 1751124350.636104, "relative_start": 0, "end": **********.011137, "relative_end": **********.011137, "duration": 0.375032901763916, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.011148, "relative_start": 0.3750438690185547, "end": **********.0669, "relative_end": 1.9073486328125e-06, "duration": 0.05575203895568848, "duration_str": "55.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45742576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00567, "accumulated_duration_str": "5.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0434, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 43.386}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.054445, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 43.386, "width_percent": 9.171}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%التأمين الطبي للمنشآت الصغيرة و المتوسطة%' or `sku` LIKE '%التأمين الطبي للمنشآت الصغيرة و المتوسطة%') limit 10", "type": "query", "params": [], "bindings": ["15", "%التأمين الطبي للمنشآت الصغيرة و المتوسطة%", "%التأمين الطبي للمنشآت الصغيرة و المتوسطة%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.057216, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 52.557, "width_percent": 47.443}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-3166083 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-3166083\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-682148552 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-682148552\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2012746458 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"40 characters\">&#1575;&#1604;&#1578;&#1571;&#1605;&#1610;&#1606; &#1575;&#1604;&#1591;&#1576;&#1610; &#1604;&#1604;&#1605;&#1606;&#1588;&#1570;&#1578; &#1575;&#1604;&#1589;&#1594;&#1610;&#1585;&#1577; &#1608; &#1575;&#1604;&#1605;&#1578;&#1608;&#1587;&#1591;&#1577;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2012746458\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-817514540 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">236</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124349103%7C15%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhSdlZZNEN5QTkwekhSeFdzT0M1MVE9PSIsInZhbHVlIjoiOGdJeG1UbE5JanlKWmJaNWpBRVcvMWlRNFRNa0NLMkF4cnFDSjd0SzgyM0V3UENYT0gwYTlBd1B6OW91M3NyN3puUG52dGlxT2lJaGRQZjhhNytpYXdXYXFkMmRXV0RWV1E4QmM0NkhBZmh5TGJ5MTc1M3FkSklkbHVlWXI3SXRiUktEOHQwOXdXWU5qc01YdWJ5TTJqNnE4OXlLUVR6akxPYnk0QzlodldWcWlJM2RBdjZrdzcrenhQamVPSGNqVyt2eEJGWkVpVGFuT25nWmhqYzY3eUZKZ2xKQ1NQNWo3L2ZUQ0xjUnkraDVIZjhDdkkyTFQrazl4ZHFWNlBjeisxZVFwc2dZczNsYnI1NEE3QjJseFRTWldhTllBaXNXY2QvbGpXdlR4TUh3T1lwL1ZYQVN2S2licldsaml4SldkNDdSTTE2ZDkybFQ5UDF1LzhWcFc5U2YwMmJvNUh2aGhPSXliYmZaMURIR1RUTkV4TndwT2tidkpqVEYzZytxbXZ6VUprZ2VtczlpeVBSVmo3Mm1ybDZyWG9EcjRpRFlVajcvQXRYcFlmSkkzQUhOVGgvNXZQeVdzbUI3UFc2bnFDUnNOVTkvcVRLMXBoU3J4R0hPeUt2Mm10bWpmVzJPQVRCWkFHWFYxbVV3WWxCRDcwYXZnVkdKdFZCRUE0U1QiLCJtYWMiOiIzYzA2ZGQxY2ZmMzNkNDNhODYzODI2YWNiMjgxNGZmOTE0OWZhNmExYjM2ZWY5ODQwYzNmNWJlZjM4NGRhMjI0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJNb01ROFM0dkhwY1JudHluV05HNXc9PSIsInZhbHVlIjoiUnNxYUtjOGNTcE9Zcll3YllEZnh3blhtZ21EYVVnV1JDaDljL1pRWmhEalhzaFZZOVJ0aFpPVjB6ODVmaWFQSHNnY1YrY25wdjJaTy9XV0pHbWluczVaeEhVQzA0TExrZnhkNDhvdE9sUk54UnRCaWoyN1NMM2RNU0ZVUFBSUTV3ejNKckV6RUFtcTBSb0dqb2hiS09UNVRacXBOSnRZZndWOU9zRUVaUnJka1hZeWZyUDJtaVBVeHU2RHFwaEl5Qmp2cTJhOXBpWFdJV0NQSktSNkxlVGsyUHFqNWFSYTBGV20xb2RTdE11OTJYNVJodG1ZRWdVeWRKYStHWGczMFdJS2g3dU1mSkhPdkcySTV3VjZ1VGtxNjY2VDFQaWlCbnk1c3pUSnNMYWpoSU1zc2NRcENydVY1a3dZd0s0c0tzNDVLcS9Cd1VRSm40VGcyLzA4ZUZMQlJ0NnR1Tm1CVHhkZy9GV1VST1NTWTlmNDluK1p2cFpVcmdhRXRWZzBaR0VvenJDdnNzVFVNVU9PNWd5YXpmMkhkWkZwODhSMEhwbFU0RG1xR1d4R2h1T0Zyck5YRmdFWVpyQ2FXV0JyWE5kcDZTdGtmMDQ2VEdJYjJOczBTMEE4UlVMVnNDNTFMNVYxNWM2c3k3aWVSUG9ya2ZsOXRkNGRBckpRVmYrZWQiLCJtYWMiOiJlNDYwOTlkMzM3ZDY0ODI5YjdkNzU1MzEyZTc3OTBjNTYyMThiMGRjMjhjNWNlYzRjZmY4NWY5MDc3ODA4YzEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-817514540\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1923726658 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923726658\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1234025864 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:25:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ildkc0hxS0NHU1hpOW9JeWMvM2hHRXc9PSIsInZhbHVlIjoiVWlOMlVkU2U2N2lmVk5Id24vYWYxTVo0YWh2QjJ5MXBGcVNGOVFBbFQ2RUFjalpMaWo5cjhuVWl4Uit1OVVxUjJQdzlsTVVTLzhVWFlaSVhheUpjNVNkcW5MSXZsdUZSdHZRb0lGWmRuak0yUmNNUlIxRnJncHJQSWpRSzM5NlRlLzRTaUU3cTZMMnpPTWY4TlN1MEMzazlqRllhYnZZSlJBUmpWRlorUnI5UUNXVlJjVmNuS1JSQU5jaHBCTGFmd1lRMkRDZDdmTXA2WnRYY0VYL2x1NUdnQS9VMzZHdzNReHl1TTUxcGpSWVZjcWFlUjlVOWE5ZnRSbTVDTkxxRVpYWXpJdzVEMXQ0emRwbE5YYlNCbWlCL282dTJpMGdWSkh1OVkwTG9YcWhqRThkc2hDclBNVFlHcWgvSDdaaUkrdEV6MGFkWUJoc2lKMWloa0M2dkMzTzgvQ2laQ0FzNDBVWGIvN1ozMHlodTM2dk9KbStKVVVjT1pIdGVTUXlRSm9vMDhVUHNoSzd5TUdxS3hLZW40dUZQWEw5MjZIZ0c3bnRJb3ZDcGpHS2F5ZWVoNW5yZ3VPWjlXVk5SdjNhQmVWNE9Lc1NyUWRkQjlaWlZuemdDRG1tQXVjV2ViWXNLbVVIU2NTMWRlQnlieERRVnprZm5JbjBoM2dVb1hBSXkiLCJtYWMiOiIwZjU1Yjg4ZDk3NDgxZDQ5MmUzYWE0MDc2MzI1YWNhZjE0YzBiMzg5NzliY2ExYjcyNDFmODgzNGNmYjUxMDE4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:25:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik0yR2YvWUdQMnhHUmFaRFErUVdCeGc9PSIsInZhbHVlIjoiQWxjMGpRSlhVKzRFaU1Jc2krSXlSSFlWVkQ4V3dENkplNGxMdHg2S3p0Si8rOW5ud0MvQ3I3VE0vMHFxMG9rQU5wSHh2cmxJN1F6UWtJbVMwNXJVZkpiL24yU3R2eG9XWFNMWnVRUUdyaXlqdXpuVVlEU2JuTFFsclRMN2VubDhGT1FwclR1Z2lZR1BwQnkwc2JBaWhJVlJIRVZoWERIQW8ySXo1KzQyZWZtc1VxZUNoTUMyejNSVXJiY3JoYzlYRXNtdXora1pHcUw1Ny9IWDNJWHQ1U3M2MUpQUmt5SVFGVkpYR3dOb050TzB3dU5XdzNFK1Jhc2cySkJFNjdlZSs1S0o3ODhkTGJrOUtrVXBZU0hJdURSWUMzdTk2TTVSYlpROVhmc25CeTQvcWVPVmZGMFpySFZSb2plWFdVTmJEdjV4NDNOYzI5dE94K1hZSXBRckFHTWJhYnUzVjVraGRKVHZtMDgxNUNBZmxRMnBEeDd1KzZWOFN1Yldqc3BiQ1VENFpTVW5xaXhXUjVsck1Iak1jWFV0NjFLOURWbTJOTW1PTjI1YkQwVGFVaVNHbURDUFhBdC9GNDN4QzBlTnVCdGNEY2loS3NTRjhwbkRZLzNxLzdYbFQxd25OZ2dsdlZxUzJUenBVVFZEaFdkVDA5bkJTdFZBOHAvZXVRa3UiLCJtYWMiOiI0MTlmN2Y0MDkwN2ViZjVkYzQzNmM1YmVjZDg4MmRmZGQ1OTNiYzM1NDA0MTAyOWM2NjY3MDQwMDI4OTQwMGZiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:25:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ildkc0hxS0NHU1hpOW9JeWMvM2hHRXc9PSIsInZhbHVlIjoiVWlOMlVkU2U2N2lmVk5Id24vYWYxTVo0YWh2QjJ5MXBGcVNGOVFBbFQ2RUFjalpMaWo5cjhuVWl4Uit1OVVxUjJQdzlsTVVTLzhVWFlaSVhheUpjNVNkcW5MSXZsdUZSdHZRb0lGWmRuak0yUmNNUlIxRnJncHJQSWpRSzM5NlRlLzRTaUU3cTZMMnpPTWY4TlN1MEMzazlqRllhYnZZSlJBUmpWRlorUnI5UUNXVlJjVmNuS1JSQU5jaHBCTGFmd1lRMkRDZDdmTXA2WnRYY0VYL2x1NUdnQS9VMzZHdzNReHl1TTUxcGpSWVZjcWFlUjlVOWE5ZnRSbTVDTkxxRVpYWXpJdzVEMXQ0emRwbE5YYlNCbWlCL282dTJpMGdWSkh1OVkwTG9YcWhqRThkc2hDclBNVFlHcWgvSDdaaUkrdEV6MGFkWUJoc2lKMWloa0M2dkMzTzgvQ2laQ0FzNDBVWGIvN1ozMHlodTM2dk9KbStKVVVjT1pIdGVTUXlRSm9vMDhVUHNoSzd5TUdxS3hLZW40dUZQWEw5MjZIZ0c3bnRJb3ZDcGpHS2F5ZWVoNW5yZ3VPWjlXVk5SdjNhQmVWNE9Lc1NyUWRkQjlaWlZuemdDRG1tQXVjV2ViWXNLbVVIU2NTMWRlQnlieERRVnprZm5JbjBoM2dVb1hBSXkiLCJtYWMiOiIwZjU1Yjg4ZDk3NDgxZDQ5MmUzYWE0MDc2MzI1YWNhZjE0YzBiMzg5NzliY2ExYjcyNDFmODgzNGNmYjUxMDE4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:25:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik0yR2YvWUdQMnhHUmFaRFErUVdCeGc9PSIsInZhbHVlIjoiQWxjMGpRSlhVKzRFaU1Jc2krSXlSSFlWVkQ4V3dENkplNGxMdHg2S3p0Si8rOW5ud0MvQ3I3VE0vMHFxMG9rQU5wSHh2cmxJN1F6UWtJbVMwNXJVZkpiL24yU3R2eG9XWFNMWnVRUUdyaXlqdXpuVVlEU2JuTFFsclRMN2VubDhGT1FwclR1Z2lZR1BwQnkwc2JBaWhJVlJIRVZoWERIQW8ySXo1KzQyZWZtc1VxZUNoTUMyejNSVXJiY3JoYzlYRXNtdXora1pHcUw1Ny9IWDNJWHQ1U3M2MUpQUmt5SVFGVkpYR3dOb050TzB3dU5XdzNFK1Jhc2cySkJFNjdlZSs1S0o3ODhkTGJrOUtrVXBZU0hJdURSWUMzdTk2TTVSYlpROVhmc25CeTQvcWVPVmZGMFpySFZSb2plWFdVTmJEdjV4NDNOYzI5dE94K1hZSXBRckFHTWJhYnUzVjVraGRKVHZtMDgxNUNBZmxRMnBEeDd1KzZWOFN1Yldqc3BiQ1VENFpTVW5xaXhXUjVsck1Iak1jWFV0NjFLOURWbTJOTW1PTjI1YkQwVGFVaVNHbURDUFhBdC9GNDN4QzBlTnVCdGNEY2loS3NTRjhwbkRZLzNxLzdYbFQxd25OZ2dsdlZxUzJUenBVVFZEaFdkVDA5bkJTdFZBOHAvZXVRa3UiLCJtYWMiOiI0MTlmN2Y0MDkwN2ViZjVkYzQzNmM1YmVjZDg4MmRmZGQ1OTNiYzM1NDA0MTAyOWM2NjY3MDQwMDI4OTQwMGZiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:25:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1234025864\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-988494829 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-988494829\", {\"maxDepth\":0})</script>\n"}}