{"__meta": {"id": "X3f722e2fedd68616537ef84151f1201c", "datetime": "2025-06-28 16:21:00", "utime": **********.41343, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127659.978333, "end": **********.413445, "duration": 0.43511199951171875, "duration_str": "435ms", "measures": [{"label": "Booting", "start": 1751127659.978333, "relative_start": 0, "end": **********.361137, "relative_end": **********.361137, "duration": 0.38280391693115234, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.361146, "relative_start": 0.3828129768371582, "end": **********.413447, "relative_end": 1.9073486328125e-06, "duration": 0.05230093002319336, "duration_str": "52.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45700192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00288, "accumulated_duration_str": "2.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3880522, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.236}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.39779, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.236, "width_percent": 18.75}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.404097, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.986, "width_percent": 17.014}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1147786275 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1147786275\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-157266440 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-157266440\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-877896365 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877896365\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1678454510 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127652854%7C42%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJ0TnNFa1VOdTN1T3VKRFdXUnFhd3c9PSIsInZhbHVlIjoiR1pmVkh3ZC9OZ1BpcitJZjZEc09FS25CbnN4LzNqWi95Zkh0b2w3cGtUZlh3dUpTRDM4NGJuTVpsVE9IZGs5STZ1RG5EaE5ONFUvK0xRbko4Ry9GSyt0a1N6cDRUakFzRjlydlNFZ1NzUmZLdkEycjZlUCtXREszNzNYSmRCR0F4dFdpQU10V3c1OFVDQnk0TmNpVC9sY3JudDhnSUFwRWRoNVhRV3BxTW56dWhaVmx5TGFVTG5UVGh1b2FWczBIRzBwK1h5VkJKUE9LUXdRSzdZSkd4c3J6Wk1WVHhEVW80TG9GbmVZQmh1Lzlka2VqU0NNOTcrSnUrZWdCUlRQbUkreTRodC8zSjd2YU5RVFRGb0ZjUm1Tb1pVNGcxQllQaEF3bFlZN3hHVXBZYitxYVRMVmZJK09IemhBVjJsS1EyTTJSaG5Ea0R2QlJuL1huYXVTNFh2NzN2TDNZbHp1QzVOTEVRYzNyb1pRbHF5ZFRjbGNFQnBPTHJzd3NTVjd0TVpHS255YjRHQVN4WHVuNHZNblZONzhUU0gvUTB4VmdEUWpaUnZBL3hPUUVzY2FvekF2c3Q0eGlmVUFUSmZabEZISW55K2NXV3JpSzVReUoyS0RBSHZLaDNoUXNjcStBS2hMSUEvMmx1bXp2bFN3OGxaN1BkTDBNVE1jajd1MEsiLCJtYWMiOiJlYzJiMWFkODRhMzA3MmE0YzAwZjY5ZGNiNGFhOGI3Mzg5MmIxYWExN2RlNjBmMmQ1NTE1MmI2NjE2MzEzMzhiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklPc2VBUzJRNVFvSWQ2MEZ2blo3dXc9PSIsInZhbHVlIjoiZjA3amRuN2dPMlZQU2V0eFRURGp0ZzFsTEN2Y0NacVNjRW04cjdmNld4a2QvOWZ3RHBtVnJzOThJazhrR3BOS3JUZXpmUXpKVEM3c2xFQ0cvRkZ6aW1jd2kwNGdKclp2NHFnOExvMk1oaithT1F1U281dVFSZDU3bGlibXZtbjRUS2hWQlJibGk1bjFhNHlIdU1JY0RPb0R1aFhiRmZjT1FMRk5WZWhQS3VsTW1YM0lOTmVuaUhwcW1UU1Fzc1ZldmZqbGpPc09mV1N0ZHhXRk9tcHA4alQrQ2dXK1RLKzFrRWRCRngrL2twcGxkcTlKQU9vOGZ2c1lxemg2Y1Z0TTlqYm1BZ1VGVWhmWmpieGkwbm5oS2YxUEdTaW5JeVRrZEo4NU05VDBuVVVKMkVYb0JPR3NVVkd0Wm1SZEtqcU50ZHNYZi91TWhQWHF2TXoxSFNyZHFOUnlRWFRNOXpCUWFjK0hGbGk2MGl5Q09VL0lmc0Rhc0ZyVjM0SGtYRnFFNDBpZlkveHcrVW9UbTV5MGIzNTdMTmNRQW5iaVJFV1lQd05POTU1Q05pd3dHczNpWGxFNkM2YWJpYnBwYnR3cmZTelpZRWFJUVE5dGtIRUVabHFxUkZLVXZGR1dVT1dCWU5nS29jYUV1blU4OVh0QnhvNVBlZGtxb01xQ0I4TVciLCJtYWMiOiIyMDliNjRlMDAxMTIwMzAzNzMyMjg4MmI2MDVlZjJlMjc2NjBjNzhmZmQxYzdmNzM2ODc5NTY2MmUwNWMwZjI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1678454510\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1897152601 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1897152601\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-41379901 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:21:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBTS1B3SnBuS3MrYWpKOTVrRXBEK2c9PSIsInZhbHVlIjoibE9LMzNXbEg4SzZMbGtDWjBQRjV3UnFMTjNvR3JNemcvakFhVWtNYXJpQmlvMlpiN3BneVZsaXlHQzN3eFFaa1RaSWZISHcyNHFWUVBTakxuZ1Mrd2plMlNiTWpWclVLWVNEa0VBYVQxQm1wb3IxaVMvaVh1WjZ0TUU4QkRHci9FS1V3NHB4Tmg2OFJrNlZqcmR0MmNyWllOQ0c3K2J3NGpvc0JBdzJSR3V3UDU5YW1MRTcvbE9rY29JZzJjb2wzNVkvcE1FVEZHOCtBMXRQc3YxYzE2RzJXcXN2ZFgzOXZpSzB3VGVLaVgwOC9EMzRuTTZXZUR5bzM4YzB5YnYyc0srdWV3Tm4wcWlleXd0V0ltL08yQklLU1RMMm1jRW9qZjU2a3IrNDU2N2I4QmZ1MFFJeWowaC9lKy94OEY5QVh5akdkMVdxRWVwdWlXL2FSMEdGMHZuUkNKdUhYMlgxTnB0SXBBWmRGcWtOMUZjUkp3VVhnNDhiVGRXWTNCWG0zSFU0ZDBMMi96RzhyUFFmQWdrc0ZJcnc1UkpMZU5hbEZzUHdZdWxFMW1HTG15OG5Kc01jNTlaMnpwUlRvcnYwa3l2emlRL3ZpcFQ3UzMwR0JuVEkwNCs4cmpJOHdLNk5GeVhZZytEYWJLOGFpb0lhQVI2WjVTMWxRV1FlT2l6QkUiLCJtYWMiOiIwN2Y1MDA1ZGJjMTc5YzBkZGUxMTZhMTA0MDgwY2IxNmM3OWZlZjJkYjhiNTliY2NhZjVmYTUxMzEwZmI1MDUwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik82akMyeXZPZUd3VFRIOGt1Q1dzb1E9PSIsInZhbHVlIjoiV3V6ZEV6UDVOQW5OSXdhSXJocm9LZUVFeHRubGpmK2t4RCsra0hMOCs5NG1FNTdlWU1GV1NrYklWWHdnNTYrUUk1a3ArT2V3MjEyY29PT0dpY2FtdFJIVVVsc0hmQlR1aWZsb3Y5aHVpalU5cWZtNW94c05uRitSOHVHcndoSTA5UTVOU0ptSVBMMDJSazI1SmZzdUFWUkxMNnp6V0dKN281Y05rQjk3OFNNd3Vkc2szejJkOUlXRTlVcmpyZDlTMVlhMFR0SlZvcDU5cFNKTmRDZE9JVDhSSzBmSUhWbmFZUGN3QTRZQWZEWi9LUHVCU0drVnAzeitNUlZwNTNMdVp4TVNqNUFhMkkzWnFJS3laMzY3aUtab3Q2dkVWTG1wTTRjenM0Q1BDY1Y4cFBMRERIN09YS2M3TXJZdHZuNWtNbE5PWWRUakZkMExMWnFlNXhmWisydjNhOEkvcEpJZ1JWY1pITFdiMzFKL01QL3hYR2FDOE9VU3JVTk15WVlmOHl4Sm1DNWtsa2o1MWYybnVlUXowbXZiTWE0ZWd2Y1hTWXBYejV5anQ0dXhRVzRvODJ5MG42amtLbkVFUGJ0eHFKZk5pdE0xK3IrMTRSVUw0bHArVlFVWVIrQ0FOVW8rUVpPZEpwcEJkejBndDQ1ZTQ4VHhJTUY4d2g1QWRXUmoiLCJtYWMiOiI3ZWNmZWFiN2UzNTI1ZTgyYjA4ZGZlN2Q0M2Y3MjEyZDg5OTRjZDAzZjBlOWJmZDg1NWEyM2RhNzRmNmQ1OTZhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBTS1B3SnBuS3MrYWpKOTVrRXBEK2c9PSIsInZhbHVlIjoibE9LMzNXbEg4SzZMbGtDWjBQRjV3UnFMTjNvR3JNemcvakFhVWtNYXJpQmlvMlpiN3BneVZsaXlHQzN3eFFaa1RaSWZISHcyNHFWUVBTakxuZ1Mrd2plMlNiTWpWclVLWVNEa0VBYVQxQm1wb3IxaVMvaVh1WjZ0TUU4QkRHci9FS1V3NHB4Tmg2OFJrNlZqcmR0MmNyWllOQ0c3K2J3NGpvc0JBdzJSR3V3UDU5YW1MRTcvbE9rY29JZzJjb2wzNVkvcE1FVEZHOCtBMXRQc3YxYzE2RzJXcXN2ZFgzOXZpSzB3VGVLaVgwOC9EMzRuTTZXZUR5bzM4YzB5YnYyc0srdWV3Tm4wcWlleXd0V0ltL08yQklLU1RMMm1jRW9qZjU2a3IrNDU2N2I4QmZ1MFFJeWowaC9lKy94OEY5QVh5akdkMVdxRWVwdWlXL2FSMEdGMHZuUkNKdUhYMlgxTnB0SXBBWmRGcWtOMUZjUkp3VVhnNDhiVGRXWTNCWG0zSFU0ZDBMMi96RzhyUFFmQWdrc0ZJcnc1UkpMZU5hbEZzUHdZdWxFMW1HTG15OG5Kc01jNTlaMnpwUlRvcnYwa3l2emlRL3ZpcFQ3UzMwR0JuVEkwNCs4cmpJOHdLNk5GeVhZZytEYWJLOGFpb0lhQVI2WjVTMWxRV1FlT2l6QkUiLCJtYWMiOiIwN2Y1MDA1ZGJjMTc5YzBkZGUxMTZhMTA0MDgwY2IxNmM3OWZlZjJkYjhiNTliY2NhZjVmYTUxMzEwZmI1MDUwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik82akMyeXZPZUd3VFRIOGt1Q1dzb1E9PSIsInZhbHVlIjoiV3V6ZEV6UDVOQW5OSXdhSXJocm9LZUVFeHRubGpmK2t4RCsra0hMOCs5NG1FNTdlWU1GV1NrYklWWHdnNTYrUUk1a3ArT2V3MjEyY29PT0dpY2FtdFJIVVVsc0hmQlR1aWZsb3Y5aHVpalU5cWZtNW94c05uRitSOHVHcndoSTA5UTVOU0ptSVBMMDJSazI1SmZzdUFWUkxMNnp6V0dKN281Y05rQjk3OFNNd3Vkc2szejJkOUlXRTlVcmpyZDlTMVlhMFR0SlZvcDU5cFNKTmRDZE9JVDhSSzBmSUhWbmFZUGN3QTRZQWZEWi9LUHVCU0drVnAzeitNUlZwNTNMdVp4TVNqNUFhMkkzWnFJS3laMzY3aUtab3Q2dkVWTG1wTTRjenM0Q1BDY1Y4cFBMRERIN09YS2M3TXJZdHZuNWtNbE5PWWRUakZkMExMWnFlNXhmWisydjNhOEkvcEpJZ1JWY1pITFdiMzFKL01QL3hYR2FDOE9VU3JVTk15WVlmOHl4Sm1DNWtsa2o1MWYybnVlUXowbXZiTWE0ZWd2Y1hTWXBYejV5anQ0dXhRVzRvODJ5MG42amtLbkVFUGJ0eHFKZk5pdE0xK3IrMTRSVUw0bHArVlFVWVIrQ0FOVW8rUVpPZEpwcEJkejBndDQ1ZTQ4VHhJTUY4d2g1QWRXUmoiLCJtYWMiOiI3ZWNmZWFiN2UzNTI1ZTgyYjA4ZGZlN2Q0M2Y3MjEyZDg5OTRjZDAzZjBlOWJmZDg1NWEyM2RhNzRmNmQ1OTZhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-41379901\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1089347211 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1089347211\", {\"maxDepth\":0})</script>\n"}}