{"__meta": {"id": "Xed08d48eec7017fcd6981441b2704813", "datetime": "2025-06-28 16:01:04", "utime": **********.347488, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126463.824458, "end": **********.347506, "duration": 0.5230481624603271, "duration_str": "523ms", "measures": [{"label": "Booting", "start": 1751126463.824458, "relative_start": 0, "end": **********.168617, "relative_end": **********.168617, "duration": 0.3441591262817383, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.168626, "relative_start": 0.34416818618774414, "end": **********.347508, "relative_end": 1.9073486328125e-06, "duration": 0.17888188362121582, "duration_str": "179ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50401312, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03224, "accumulated_duration_str": "32.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1986978, "duration": 0.02536, "duration_str": "25.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.66}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.232286, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.66, "width_percent": 1.489}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-28 16:01:04', `closed_by` = 22, `shifts`.`updated_at` = '2025-06-28 16:01:04' where `id` = '51' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-28 16:01:04", "22", "2025-06-28 16:01:04", "51"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.325335, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "kdmkjkqknb", "start_percent": 80.149, "width_percent": 11.507}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-28 16:01:04' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-28 16:01:04", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3305519, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 91.656, "width_percent": 8.344}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "success": "Shift Closed Successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-104258127 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-104258127\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-837615256 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-837615256\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-162721096 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">51</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-162721096\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-81028326 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126454152%7C5%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVMZmUvK3ZRRGd0aXhPMGRBbEFmdnc9PSIsInZhbHVlIjoiSk5qN1d1bnZUT2g4eTVIeVZsYnFCZU1kNWZ1SnpNWHVpU3F2Y29LVVd4TXNBZmN5MHU4V3pPNTB0VndKMjBEZS94RGR1NERHWEhaL0QyNFdESjlmQ0U5NEd0YUdLNGN0cTc0cUlzSkd6VkpkU1pPcVpzUEdCa2s1MDBzS3hTYlhUK1diRFZOQ21xdk1ONGpXajBkM0FqRG82akk5bktZd29RMldDQTlSVlBRVWxvOFcyNlhnMGozNWs2cVpQUmJWK0N1NVhFcUNUbnRlaVlBRGxNVkQ3STNjWkhBc3ZxYmxOZi9xSCt3U2tBZHdqZmFyL2h6eFB4MTlIZUlLejhDZFhKL0pUVEVmUjZqSE54clNyVTlSOEJlRGFnb1RXckNweHZKcXBlaTVJdklFTFk2NXV3K1FwMnBRTE9DWjhwTVdaemtFRlB1bVJMQU1ONU9HVUhzVURyNERGY1d0M2F2WkllakxRYnAwaGxtdDlBRTJ6aStKdnNnYkIzWFVTb215eHhySEhrOUliZVVrZjFpc0Y1K2UvdFVYK25wQlRmUkZMbkJjMjhxQjFvV1BLc0lVVFptUmJlU1hXL2Q5bVFHbEFtbkpTYUwwbERVV2ZadE9XN3RNS01SMWJFMWwvQ0VQYmZnbXhyaC90U0VDcDlScWV2eCtDcXZnbk9UVHZzOTIiLCJtYWMiOiJkMDhkN2FkNWRjMWNiYjUyZTkxYThjYjZjYzk4N2Y1OTk4NTEzZWFhOWY2NTI4ZTAzZjkzODI0MmVhZDc2YzZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkxSWnVPWldNMUVQWU4zcjB2S1hqdkE9PSIsInZhbHVlIjoiTEdmTW9OZVVjbmdwYlNNZ2RhdnBFSFZIQVB6dXJCV29RN3g5RHI1VEQzVVdieUozQjhBVGd2ZnlnT0Q2VzI3djVDR0JucDRpS3BndC9WK3l0ZnByU2F6WHJTNkYwdkRFWEozMWxkTkFwNmdNN0REOFlGMXA1TTRFYU9ac3lNS25acmFVZmFGeXFzNXJWUFExS0hUbnBXY0ZZUCtibTBJdEVKdU1ocWxNNlBQTFc4c0xhSERJSm5rK1U1QlJhMlc4MW9rN0MrSzZSUHlyb0lQOUxvS0NYc1FMTDVjMlZrVTJ1aDg1QXY1N0VpbnFlOWlSbE9aalZXMjNYcW9BUmUyOExoTmpiRUM5Vnd2bFQ2am0xYWcyNzNxOHV3azNPVXZnRjlKcEVtbHVFZDBlL0k0YTROelNKb0paTTNBV29uaUJjdXBzNjBFWlBBMmRBakg4Nlc2eUpGY1dob0JzTDhiYXBJY1FoT0x4Q2MrWUhBR3p5TzlsczZtUXNBV2pIQlFRNnZFQzYrYkIzdE05RUpNYS9nUnhBc2lSb2tDaVN3cml6U0k5UHQ0dkk3WVdXc3JpRHY3Wi9iZWd3anF0VnBaQWExQ0d5SVhHOXdDV040VnlIWWhONS9aQWFRVTIwV1phYnZ0VFR6U2Q5dC9QR1JMSTlIcndMMjkwRlBmek1jUW0iLCJtYWMiOiIwOGQwODExMzBmODFkZDA1NzIyZmJmNWMxZTA3MjYyODgxMjYxNzUyNmRhYzliNDcxOTMwZTkyMTA0YWNmZDdjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81028326\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1464224097 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464224097\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-201948468 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5CTjArV3VOOTdFcEUrUVRlSUcwc2c9PSIsInZhbHVlIjoiRDU5SEYycUJWQ3hZZXdNNGJ2K3RzRGNPeXU2aXpjb2d0T3pHRUtpd09QS2JHMHFEdlV2TGpob0VPQlBvVzArcVlOTGRkZEFsSG1ueWFHOVpHVUlsbkR5cVNxTlFTM2NBdmIydW9xeUJIZHpaN2xuNk56WDBESTRNMlRRSHlDc056S0ZYOGQya0tXMUc2NFFpOWdOUmFZU2ZBWkUwR0lJcmp5N0V2V2t3RjllU052VW9hN2RpTGpwcDd5RGZFNVlmNmtXNWg2RWxzZXViZjM1MlNlY1N0MHE2cDlUaXhQN3R5Ky9lWXl2QWVDckIxUDR2NEZXeDh6b3o4SXpZOWlHN1NnSDVpSTZ3Q1htTnVnVWtSUWlFQ1hXNG9vZlRpeXhOK1dxN1M5alFZZEFSZ2VuK2V4SXJCa1VLdGNDVVBLbUt2ekJTaTk1ckVvOC9HNUhaOStwaHVYZnFOS2xwdm1lamdDaUtwbThvWnJDRkUzV1Urb3ozbVM2aXdKNmNIOEs3MnVJMGRQd0ZMa2xtME1lQ0MwYVkyYlZmcUlldmhTVFZOSDlDSjcrajByaTFkV3lnT1NUbXJKOXN1VTY4NkRYbHBLK1BFTmh3Q0kwcVFmeHpPOVJEMkdTM0JIeDhscjNmNXV3WlZma2VyZFhaaC9jb0I3eWJVSEFIdUg1NllIUGciLCJtYWMiOiJhNGE4NjQxY2JmNjkyNmYzMWJhMWI1ZGNhZTQxZjg2NGFjNDVkNzkwY2U4MTlkNjhmOGM2OTM2OWM4NzM4ZTFkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVGTis1N1IrWXgzNjFob2o0RHQyblE9PSIsInZhbHVlIjoiME1VNmdoMmZCWTdsTGJwOHQrUXRIRUttKy9FbW9SbG55T2NVb1BUVTVsWlZ3UFk3djhZYldBdDFqR1daZFdJUXVDWG9RNnl1UDZjQi9qdHJmR0ovYkpNSlRBbjhFTFRVcmtSbjVpRkN1OTJDYmFJNlZpYzNGQU9tdFRrdlFtb1JNWHU2V1FUMUxEYjRXcVhyWW5lMzArQWp3d1NhQ1FGTVVQTkdqVVo1T1NuR045RFYzbHpZdmp1eFdaYXlwa1JSSWhndGtXMlRYOGc1NXZzc2VyVmVlWUpiSEZvMENTSzJtbDRHejBjWG82MEt6N1JURENzYmZqR0h4eFRJSGRlUEpzdEUyWStQZ1R5NmZQaW1sYWVGNURWVFM3OFk0ZkRQUHNCRUd2MFRtekJzR0ZjYTNwVXpZRkpxMTY1OHdCYlNJZEszNE5sTWEwd2tHaW1kYzNITmhIU2s0VkRleFh5RzlOdGNTa1ZHS2V1RFlPK3dmZTlQazNoNnRycWlsWkVoTllmL1FSTThWNEZrRGZ1UHQzWi9Cc0s0TEQ5NW5NaDZNQmhIT0dTb1M4TUJxbFA2SWl5Q1VmY0k2L1d2bFZqOXJQV2hNTXFJL1lpYXBCSWhDL1ZOK2lCamZDNTNYTy9qaXVNRisvLzZlSXkveDVDV0UyRWhXb2d6dURpS3Q3TkYiLCJtYWMiOiIwMzMyN2YwYjcyZGI4ZmYyNzI5ZTNlZmI4YjRkZjFjZWI4MmQ5NjZkNGEzOGQ4MGNhMzg4OWJjYmMzMjY2ZTIwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5CTjArV3VOOTdFcEUrUVRlSUcwc2c9PSIsInZhbHVlIjoiRDU5SEYycUJWQ3hZZXdNNGJ2K3RzRGNPeXU2aXpjb2d0T3pHRUtpd09QS2JHMHFEdlV2TGpob0VPQlBvVzArcVlOTGRkZEFsSG1ueWFHOVpHVUlsbkR5cVNxTlFTM2NBdmIydW9xeUJIZHpaN2xuNk56WDBESTRNMlRRSHlDc056S0ZYOGQya0tXMUc2NFFpOWdOUmFZU2ZBWkUwR0lJcmp5N0V2V2t3RjllU052VW9hN2RpTGpwcDd5RGZFNVlmNmtXNWg2RWxzZXViZjM1MlNlY1N0MHE2cDlUaXhQN3R5Ky9lWXl2QWVDckIxUDR2NEZXeDh6b3o4SXpZOWlHN1NnSDVpSTZ3Q1htTnVnVWtSUWlFQ1hXNG9vZlRpeXhOK1dxN1M5alFZZEFSZ2VuK2V4SXJCa1VLdGNDVVBLbUt2ekJTaTk1ckVvOC9HNUhaOStwaHVYZnFOS2xwdm1lamdDaUtwbThvWnJDRkUzV1Urb3ozbVM2aXdKNmNIOEs3MnVJMGRQd0ZMa2xtME1lQ0MwYVkyYlZmcUlldmhTVFZOSDlDSjcrajByaTFkV3lnT1NUbXJKOXN1VTY4NkRYbHBLK1BFTmh3Q0kwcVFmeHpPOVJEMkdTM0JIeDhscjNmNXV3WlZma2VyZFhaaC9jb0I3eWJVSEFIdUg1NllIUGciLCJtYWMiOiJhNGE4NjQxY2JmNjkyNmYzMWJhMWI1ZGNhZTQxZjg2NGFjNDVkNzkwY2U4MTlkNjhmOGM2OTM2OWM4NzM4ZTFkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVGTis1N1IrWXgzNjFob2o0RHQyblE9PSIsInZhbHVlIjoiME1VNmdoMmZCWTdsTGJwOHQrUXRIRUttKy9FbW9SbG55T2NVb1BUVTVsWlZ3UFk3djhZYldBdDFqR1daZFdJUXVDWG9RNnl1UDZjQi9qdHJmR0ovYkpNSlRBbjhFTFRVcmtSbjVpRkN1OTJDYmFJNlZpYzNGQU9tdFRrdlFtb1JNWHU2V1FUMUxEYjRXcVhyWW5lMzArQWp3d1NhQ1FGTVVQTkdqVVo1T1NuR045RFYzbHpZdmp1eFdaYXlwa1JSSWhndGtXMlRYOGc1NXZzc2VyVmVlWUpiSEZvMENTSzJtbDRHejBjWG82MEt6N1JURENzYmZqR0h4eFRJSGRlUEpzdEUyWStQZ1R5NmZQaW1sYWVGNURWVFM3OFk0ZkRQUHNCRUd2MFRtekJzR0ZjYTNwVXpZRkpxMTY1OHdCYlNJZEszNE5sTWEwd2tHaW1kYzNITmhIU2s0VkRleFh5RzlOdGNTa1ZHS2V1RFlPK3dmZTlQazNoNnRycWlsWkVoTllmL1FSTThWNEZrRGZ1UHQzWi9Cc0s0TEQ5NW5NaDZNQmhIT0dTb1M4TUJxbFA2SWl5Q1VmY0k2L1d2bFZqOXJQV2hNTXFJL1lpYXBCSWhDL1ZOK2lCamZDNTNYTy9qaXVNRisvLzZlSXkveDVDV0UyRWhXb2d6dURpS3Q3TkYiLCJtYWMiOiIwMzMyN2YwYjcyZGI4ZmYyNzI5ZTNlZmI4YjRkZjFjZWI4MmQ5NjZkNGEzOGQ4MGNhMzg4OWJjYmMzMjY2ZTIwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201948468\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1510329436 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Shift Closed Successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510329436\", {\"maxDepth\":0})</script>\n"}}