{"__meta": {"id": "Xf7f5b6076a989f0d99c3fda6526d1eb6", "datetime": "2025-06-28 16:19:16", "utime": **********.692648, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.277836, "end": **********.692665, "duration": 0.4148290157318115, "duration_str": "415ms", "measures": [{"label": "Booting", "start": **********.277836, "relative_start": 0, "end": **********.638543, "relative_end": **********.638543, "duration": 0.36070680618286133, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.63855, "relative_start": 0.3607139587402344, "end": **********.692667, "relative_end": 1.9073486328125e-06, "duration": 0.05411696434020996, "duration_str": "54.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45699888, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00301, "accumulated_duration_str": "3.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6659439, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.133}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.678136, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.133, "width_percent": 21.927}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.683698, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.06, "width_percent": 17.94}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-749715566 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-749715566\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-227366875 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-227366875\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2077615241 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077615241\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-862060262 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127552721%7C36%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlI3cjlXdXQ0ZXBDOXE3UGdGMW5JS2c9PSIsInZhbHVlIjoiOXY3bnRETUFmMVJQR3dkNml3b3RYVHZkSm5PVGpNdVNXbTMxa2NJZEpZOW5PalR3MWhJcE5DdkR1cVhkeis5VTgzL1g2cTFCa0F1ZW8wcUorR3dDYVNUd0RGRWdVbTFOdUErbXR5WlRyamcrVUZtVUtFd3o2cFRZZ1prUXc5RzNzMTZvRjdoTHp5c2tSK1lGZDlKS1g5NHNMT2dhbTFod2ltMVY2bGp2S1dlYkRBZGZQcjQ3ZFY1SmtFWm9USlgydWRkTG8xSHVUY002dFlSajRsQ3pMQmRDZnpZMExqWHFzYU9MUG9EZ2t0aVpXc1ROY0wzZkRjOU5TdlNpL2Myc2Nab1FxL3dsZnZRaEo5RCtKR1hBdDZWQVQ0U3hNenpNMk5VUVdhWWZVenhoV0xuV1YvUWdnaVoyWXlVSkRkeW12d3d4QmxoWTE4OWhKTFdJWFRPMkRNTVoyaDAyeUEyM2JPVjRxUndFSFN6dVlhQS9JRmVVS0RRUXBYVlZVUWxwSkpaQWl3aHVvcXY4QmtNTUxxNXhpbE12eUw1MlFtNmVlRWV0bEdFSFFvM1pFZjNjRXB0b1pNOTRwNTJzZnJGUzVmTFFpcTcyL1A0MGdQRHh1SWVnZ1lhNDJVR1RRTllEWU1LSnlRZHhINHNsdURtMDRFRzY5aDR3MFc5Y3YrYk8iLCJtYWMiOiI0MzVlZTE0YmRkNWU2MTliMTdiMTNhOGY1ZWZjZGEyNGNlNjRiYTM5MGE3N2JlOWUzNmI3OTc2ODY4NDI4MjMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFJbnFRMmVMSm9IVHJsMUFpVURlcEE9PSIsInZhbHVlIjoiTCs0aUV5eWdUcTBOemtxdUVSVmo1U0RUVDVtSGlvVkhTSzNBbEhNZFYzTTNraWlGUURxTk9UMDdIaTIzWlc0bWtkVEE2SlUrVnN0MG55UXZpQW5TdWRFOUJ5RnE1NXBYcGw2VU5rajhadjE3aWdIL1hUVFZPN0s5ZlkzU1ZLcmRvSVJkc1A0WXNiYTJaUlJ5WWlzMFdPdUtPNTZJQ2V2UmdBbERSQlBueFFleTVPVzlRditXWWkvM0V3bTBiUExlZGRYYll4UmFuS0FZblo1QVNFWmt1ME9oNDhnbEVnZEl3UFJqZDBmWDIvdUN3c1N2T04vUFA2eG1DVmNWdGV2dU93QnZMMHBZR2x2azFqS3FsQitoQVh2UWtqWXNsL1luWG03c0U0bjVtUmtBeVdHK005UGw0M1hvNndFa0lTNWhBVXRvUnZZREF1VkdjQURuZzhMWWp0WXJmVWM1N21qamxTazdHdGtvZ203SnR1VEZPWU1LSFUzbGRmTUROakxBQUVXMThBMlEwNnVxN1oyY25SRHJYSHlqRWdBbEZVN0lLTVNTVmNmU3JKVmtDUGtFRG9OUkUwZUNwN2xKRDNBRUVZVDJjbThSOTdiUFBoWXlXamRhUSt3K3BSWUw5Q3ZvSmxTTUFac3BTNzFBT3JCd3VTOUFWQVRNbUZKNGc3Y3ciLCJtYWMiOiJiNzJhNTJmODQ1NzMxOGY1MjU3OGFlOGY4MjZhMTZhNTE3NTM4YWZjZDBmZThkMWExNzA4NjkyYzllY2JmZTM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862060262\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-606216289 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-606216289\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-639651403 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVES205cXVFN3JzTk1Xc1JNSzY4TkE9PSIsInZhbHVlIjoiZnhORGl6a2JLTmE0YldYSXc5aXEwaVR5ajQyMU5UNEtWVXpRVlQwcWs0anRrOXFpQTBNMVJCQ001SHpSbDFSSmQ1NTdxOTNhbHNVTTJDVzZvUnRYM004V2ZveHVhVHRlZy91b05rdndvYTZOc200b1B6WmdiZ09Cc0tWd3RveE02MUFQQlliM3hNa3RGclZiSlJvSWNKVjBZczVaYUp5dTlrQ281TFYvRk9kN3AxLzhjL00xdTY5WUM2dmJHbm5vVTNJWEJnTDVVamtVang1bXVOSnhSVlFmbU1NbUs1bDFMalJkZFE5MHlUUVBadEVoNVVzSW1WbWF1Vm44dmJhTFRkdGZ2OW81WFM4ZURnRU11TlVDVExlS1NXT0NLUkZXNUZueGs1OEl1NUNnaE5VM0gwditzK2F2b3FrOHZiaWROaFdVWjdySmtwNDdkcHM2YkoyU280RjRVWUxTVm54c20rakN2LzRWcUtSdUlmMFlRakhVcTQrcWRkL0V6RDJhTXdISHlMOEROOWZ5Nmx5RDAra2FzYno4NmxLcmIxTEhlSlVhTWF5eSs4L3lHcGNOdlB6NWVkbnAvcnNjajdPNmZhYTAzK0o3Z3NUak9MN0gxR1ZlN2dBaVNRNzg1RDZsR0toOU9reHUwcHNMS2w3RCtzVGZ6bFFwNHBSQmR5U3QiLCJtYWMiOiJkNTQyNzcyMGExMmIwOTJmYjU2MjY1MjQyZGEzMTg4MjFkMzQ5MzA1NTM1OTZiOTkwNTkxNjM4ZWUzM2ZlMGMxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhHNStWcDhNenB3V1RJZ2xMUHlKZUE9PSIsInZhbHVlIjoiaStDTzY5SDBjc3lYcXB2OFhCcUxjSnBuaWFxNERmOFZHdkhnUTAySkY3bWRiOVo1a3JRaTM5ZFhDUE5INzVGT3hROFBXbUxubkpVblZQZlg1ZTlDL055bVorMVpRYno0MnVBaWNYeUFhRERBSzhMQTI2anRQaUt3NzhRWUQ1RlpRZXdWek5wSjduZGplRXRvdmZHSEVEYWZneXRFYTllSFAyNHRyQllpQlA1TkdwOWVUQWV4dmZTajBxb2VIV0huRWtxVTZzMjNRaFRuLzhIUzRKWTFMK3NCWXhLVW5yUVBsT0YrZ1JKSWVKTCtvaWtUVmVoa3N4cDhLM0xKazRNYmhPcmp4ZmRKMExUTjROaGEzaDVoOWRVS281RWNoUmRVMGxOTFYvZ3pBeG9GR2FRc2FOWEN5SFZOTGNOZ2NwU3N0dFppcHBVWmdRRXRQMHU2MHdTRWNuVVZEME1NT3NkMVhpWE1rU0xYQjZ2d1Frdk1SVXc4cGRGTFZnRzU4YjJ6UlJuM0RjOFFsM1czVkFoL3hQYS8rcyszSVhyMFNRTG5GTVNNMHFlTEhoSDlEYnIxb3I2ZEd5UFYxenJzWksvbjFjeS81VUVwaDJUWXZsWTk5TXBOeWpOZkF3MGxGak16MlJPNGZNeC8yN04yWWNzNUJvQWpuZ3lWc2VYRFNRNm8iLCJtYWMiOiI5NmQyYWNmNGQ4ZmVhYTZhZmQxYmM2ZDE4ODE2ZDg3Nzc0NTg4ZGUzNzAwZDVkZWQ1YjFiOWEyMTk4M2MwN2Y5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVES205cXVFN3JzTk1Xc1JNSzY4TkE9PSIsInZhbHVlIjoiZnhORGl6a2JLTmE0YldYSXc5aXEwaVR5ajQyMU5UNEtWVXpRVlQwcWs0anRrOXFpQTBNMVJCQ001SHpSbDFSSmQ1NTdxOTNhbHNVTTJDVzZvUnRYM004V2ZveHVhVHRlZy91b05rdndvYTZOc200b1B6WmdiZ09Cc0tWd3RveE02MUFQQlliM3hNa3RGclZiSlJvSWNKVjBZczVaYUp5dTlrQ281TFYvRk9kN3AxLzhjL00xdTY5WUM2dmJHbm5vVTNJWEJnTDVVamtVang1bXVOSnhSVlFmbU1NbUs1bDFMalJkZFE5MHlUUVBadEVoNVVzSW1WbWF1Vm44dmJhTFRkdGZ2OW81WFM4ZURnRU11TlVDVExlS1NXT0NLUkZXNUZueGs1OEl1NUNnaE5VM0gwditzK2F2b3FrOHZiaWROaFdVWjdySmtwNDdkcHM2YkoyU280RjRVWUxTVm54c20rakN2LzRWcUtSdUlmMFlRakhVcTQrcWRkL0V6RDJhTXdISHlMOEROOWZ5Nmx5RDAra2FzYno4NmxLcmIxTEhlSlVhTWF5eSs4L3lHcGNOdlB6NWVkbnAvcnNjajdPNmZhYTAzK0o3Z3NUak9MN0gxR1ZlN2dBaVNRNzg1RDZsR0toOU9reHUwcHNMS2w3RCtzVGZ6bFFwNHBSQmR5U3QiLCJtYWMiOiJkNTQyNzcyMGExMmIwOTJmYjU2MjY1MjQyZGEzMTg4MjFkMzQ5MzA1NTM1OTZiOTkwNTkxNjM4ZWUzM2ZlMGMxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhHNStWcDhNenB3V1RJZ2xMUHlKZUE9PSIsInZhbHVlIjoiaStDTzY5SDBjc3lYcXB2OFhCcUxjSnBuaWFxNERmOFZHdkhnUTAySkY3bWRiOVo1a3JRaTM5ZFhDUE5INzVGT3hROFBXbUxubkpVblZQZlg1ZTlDL055bVorMVpRYno0MnVBaWNYeUFhRERBSzhMQTI2anRQaUt3NzhRWUQ1RlpRZXdWek5wSjduZGplRXRvdmZHSEVEYWZneXRFYTllSFAyNHRyQllpQlA1TkdwOWVUQWV4dmZTajBxb2VIV0huRWtxVTZzMjNRaFRuLzhIUzRKWTFMK3NCWXhLVW5yUVBsT0YrZ1JKSWVKTCtvaWtUVmVoa3N4cDhLM0xKazRNYmhPcmp4ZmRKMExUTjROaGEzaDVoOWRVS281RWNoUmRVMGxOTFYvZ3pBeG9GR2FRc2FOWEN5SFZOTGNOZ2NwU3N0dFppcHBVWmdRRXRQMHU2MHdTRWNuVVZEME1NT3NkMVhpWE1rU0xYQjZ2d1Frdk1SVXc4cGRGTFZnRzU4YjJ6UlJuM0RjOFFsM1czVkFoL3hQYS8rcyszSVhyMFNRTG5GTVNNMHFlTEhoSDlEYnIxb3I2ZEd5UFYxenJzWksvbjFjeS81VUVwaDJUWXZsWTk5TXBOeWpOZkF3MGxGak16MlJPNGZNeC8yN04yWWNzNUJvQWpuZ3lWc2VYRFNRNm8iLCJtYWMiOiI5NmQyYWNmNGQ4ZmVhYTZhZmQxYmM2ZDE4ODE2ZDg3Nzc0NTg4ZGUzNzAwZDVkZWQ1YjFiOWEyMTk4M2MwN2Y5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-639651403\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-93450422 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-93450422\", {\"maxDepth\":0})</script>\n"}}