{"__meta": {"id": "Xa59c197b0819a27d6ee1f93d8175a41a", "datetime": "2025-06-28 15:38:26", "utime": **********.371726, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125105.954869, "end": **********.371739, "duration": 0.4168698787689209, "duration_str": "417ms", "measures": [{"label": "Booting", "start": 1751125105.954869, "relative_start": 0, "end": **********.308846, "relative_end": **********.308846, "duration": 0.3539769649505615, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.308855, "relative_start": 0.3539860248565674, "end": **********.371741, "relative_end": 2.1457672119140625e-06, "duration": 0.06288599967956543, "duration_str": "62.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45793408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00785, "accumulated_duration_str": "7.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3418539, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 20.892}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.352098, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 20.892, "width_percent": 6.369}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*************%' or `sku` LIKE '%*************%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*************%", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.354914, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 27.261, "width_percent": 33.121}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3605602, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 60.382, "width_percent": 32.866}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1995}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.365568, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1995", "source": "app/Http/Controllers/PosController.php:1995", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1995", "ajax": false, "filename": "PosController.php", "line": "1995"}, "connection": "kdmkjkqknb", "start_percent": 93.248, "width_percent": 6.752}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-366367083 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-366367083\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-65515852 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-65515852\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1758174103 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1758174103\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125105407%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ing5bFpvT0J3bHlxUTAvWnpYb3hwclE9PSIsInZhbHVlIjoiQkI0eUJ1TWdTVWZjaDRqc0xSUU5Cb0gwdFE0WnVSbXlBSkhvUVYyVzRZTkR0bUYxd21kK2djNGRyVytGNWZFbFBZY3I0VlUwMnFyUnZQN3dwd0hOZWRFRFpWUk1iazZ5WjI2Y1VLYTYzdm8vNW1UZGd6TksyR3hlZkttZEVNSkdzcFpxT1dKclE0NmZvTFZzZzJZYTVSUC8xRDNHekROMTIycVFLZnc1aDFscVl0dDhkd2E3bHAyZjlRc3E4clphamFWdTZWUUFEUUE1YituUkFKMjdabXlmcWlrSXhDL2dWWWthbkJEMjVaVi9Xem5MWHo3aGI4cEFuN09IZ1pONEVtR09XMmFvajErNGhwZGxuZXFSSGI1TktEclNhZXNyRFEzeUR0VEJ1dWx1SHdoVWNRS1c2aEdvcWZUWCsxbFR3UjNwdFRsQ0lER3B0NnpobjJwdzhWVkxhUnR3RDJIMDNhYk04aHJLbmlrbFk0Z253TWhVSVhBc0RxSnMzN0R0aFpEOEwwbzk0WEV0U0FaZTV5Y1Fhb3ZvNG56Z3pkVVc3bWJ6dHVqcllHcVBSZVdBTWc4dlJ0YlVSZjZWWCtTQnN1MGJaT3huVXV5bE9RME1GaVNnTTBNQ2RtSEg3VDh1NGNNck5jSW5wRzhLNHFJWnFqcVpDQVdJaURrSzJ5MUYiLCJtYWMiOiJlYjY3MDU5NzJkMTJkMzdiYTNkNjhkYTBjYjA2MmJlNTRmODRjNTAxNzk1NGFlM2JhODA5NmIzNWYxZDdhY2IzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNSRzFveE1raGQxVzdwd2QwZUtUdmc9PSIsInZhbHVlIjoiZXg1UGxoL2g3U0RKUVhWNjVDNkJmc1N4ayt2akkrYy90aWVUQ0Fod1pQTjZhSjU1WUxDS3ZnUCtweXZsRGV0cWpqQ3gxNTRDLzJ1YWZiQjdmdGxnNVhhZmtFN0pwWHhCWnlyUEI3dnJjMXhxeVpCcHJucGRVU3FGUTN0MFYyNWhERUIrVFVDSUhMUWszVHNEQWducjEvS1VkRFJCWW4yZlNXZWFNYzhUN0MrU0lhVHFuVkFidlRsSEtGci9qeGo2L2MrblJtT2RCaEJyMDlKMVB5dWdiTitua2I4NXFLVHpHV2xLWHNQNmlTSk9JN3N1VFdpQ09PdlI2NTlPUUgxL2hvaVMxTEZFbDYxSTdYdE0wM3R3YUFwUFYwVHBMUDZSREhWWDZaWC9XenFmblUrUmdMYUVnNjZocWRoVXNQNXQycWs4aTMyZFJ4RjcyVENpS3ArK2ZsUXU1NVlRTmlyVmJPVUtWcmp6ZUVjV3B3WEdwNXZKbTNOb3VhaytNTmlwa28yUW5Xa2FQVVpMRUJoUklqUmlXdUw0a0xuQ3RNRGNwVEtFaEp0d1ZNYkVpZUt6bWtrVHNDeEdiSFRUQ2xJejduelNtRm1MRktKMHFwM2VseWt3ZHhzZ0t4dm9FOWdnL1lWK0dVK1lPYXJLTVVUc0xLc09uYlRBY2laUDhSa3oiLCJtYWMiOiI4NzQ1MGJhZWUxYzMwMWM5MTczNjUyM2NmNzI5ODhjYWNhOGJkNjk2NmY2ZjYxZWMwZjUzNGYyNGY4MjY1MTdjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1529950802 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529950802\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1636021514 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:38:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImMxTW5kNlZkSWs4ZUpsMXlyTlNGK2c9PSIsInZhbHVlIjoiU012S0h6OTg5dHQwMXdRUWowWjlZcldWcisvUklyM3M0UFB4Ry81eElQWUw4bUNscHRwekFxaExUaThmZDhtTEQ5VCs1MHpFa0FKWlBLUTJiZ2ttZ3kvNFFMNlMvZWh3ZHI1Z0ZISlk1Rk1IcXdnSHh3WFBWU1Q4aFVlRDJpT2psMUs1VUF1cjA2cU52RzBuT0I2Q2MwOXNxL1piSzdXckh0d0N2OWJvU2JIbDBHdHBJNEt3Sk1COVdnaEoyMzA3Q20vU1M5YUY5ZkEwR1pQNi9PbWxZMkVqYk5uSXMwUExHUXFOZ2VjUDV0S2x5WEtxTjhWdm10RUpjZzJkUytpcWd0Znl4WGFCRXM3dW1tQTFVaWVuNVl4ZXpUWVdrWDRpMkd1ZkJoS291RVdwSzRFQjM4SnFBbDlyWWN2TEpUMjE2Q1ZSeFdKZTJhLzFSU0hnS1hLYjZ3Y296Y1c0bEF5T1lZRzVFdUNkOEpDbHBIcW5PY2s4NDRBWXIyTUl4OUxyTVVBWG5maUJwNnVnaG5OZjBOWEM1YWZxUC8xdXN2QTJWeVpwZ21Pb0kwUE9kMGNIQmZNR0hKMmZKbmJabWI1eGI5bWxBY1Z4d2NscnVyLy9Yd21TVG1WSDNNVWFDenMvVTFDWkJlZDFDNE9OdjlTbVNXSy9GcnR2YVFxNHFpclgiLCJtYWMiOiJmZTgyMjE4OGNiM2RhNDYwZWI0YWUzODQ1ZGZhOTMyYzY5YWM0N2Y5NDA5MDVhM2MxZTVmNTlkYTNkNzMxYTc5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFOL3M2cEpjLzliaGhXR2JlaHpEOUE9PSIsInZhbHVlIjoiOU5SVVd2MFNwOENGT0M2VEV2K0NnQTJkajV3Q3QrQXB3Uy9teG9JajRVcFR4QTJtQ0ZZS3N6Z0cvZmNxYjdqSkJMenNyZ2xBTVhMZ2lXR1Zqek5DMDRuVUlMcDRZOFc5UFBodWZJNklOUTVXUkRtZlBYK0ZZTG5wenRhb2JXWTgrTGx6K21aRUh5aUtUeTNqcmI5cmx4QWwyZjZ3QWlwTTZLa1MyL1d0L0hIc29zMGdYY1ZSNnBmR1F1WVQzRUxxZTVIN0hkU2pqUExBdmkyNktRb1E3VGFSczRkUW94THB4LzJWYnFGejhsQ2lvY1p5SG93Y3VDZFVUMWJKRW5LYnRiMms1Z3BQRkovWGhka3czZlFXMFA3Wk5STXBnbzFTOG5ta0hCRHA1UGlENFhQME90UkRLVjc4TGI2Yjhsc0ljZkhQL0hoOXNQQnJYajdoNmJsZDVuUWlrOHBMUWRFcUhITlcwQWtHYXZJTW5uWjhMaWE2eFhaTVBhbFdaYVRmYVYrK01SNzBCRkxWRklRaWZDdlJaN3F6d0RTUlhMekpmeUYxQzZsckJLMXI4eC9rVDRiVUxlSTB4U1pmT3FUTEdBSG8zVkw1eTJialZLSmxGemcxR0RlYUs2UzFVUnlJSHUxOHVZT0VsOWFpTFd6SzMyb2tRU3RpNkVFZHA0RU0iLCJtYWMiOiIyMjYzMGVkYzgwZWYxNjZhYThiZWEwNTE3NzJjYTdiODg4YmZjZWM1NDM1YWY0NWM3NDJiNzhhNTg2MGU1OGVkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImMxTW5kNlZkSWs4ZUpsMXlyTlNGK2c9PSIsInZhbHVlIjoiU012S0h6OTg5dHQwMXdRUWowWjlZcldWcisvUklyM3M0UFB4Ry81eElQWUw4bUNscHRwekFxaExUaThmZDhtTEQ5VCs1MHpFa0FKWlBLUTJiZ2ttZ3kvNFFMNlMvZWh3ZHI1Z0ZISlk1Rk1IcXdnSHh3WFBWU1Q4aFVlRDJpT2psMUs1VUF1cjA2cU52RzBuT0I2Q2MwOXNxL1piSzdXckh0d0N2OWJvU2JIbDBHdHBJNEt3Sk1COVdnaEoyMzA3Q20vU1M5YUY5ZkEwR1pQNi9PbWxZMkVqYk5uSXMwUExHUXFOZ2VjUDV0S2x5WEtxTjhWdm10RUpjZzJkUytpcWd0Znl4WGFCRXM3dW1tQTFVaWVuNVl4ZXpUWVdrWDRpMkd1ZkJoS291RVdwSzRFQjM4SnFBbDlyWWN2TEpUMjE2Q1ZSeFdKZTJhLzFSU0hnS1hLYjZ3Y296Y1c0bEF5T1lZRzVFdUNkOEpDbHBIcW5PY2s4NDRBWXIyTUl4OUxyTVVBWG5maUJwNnVnaG5OZjBOWEM1YWZxUC8xdXN2QTJWeVpwZ21Pb0kwUE9kMGNIQmZNR0hKMmZKbmJabWI1eGI5bWxBY1Z4d2NscnVyLy9Yd21TVG1WSDNNVWFDenMvVTFDWkJlZDFDNE9OdjlTbVNXSy9GcnR2YVFxNHFpclgiLCJtYWMiOiJmZTgyMjE4OGNiM2RhNDYwZWI0YWUzODQ1ZGZhOTMyYzY5YWM0N2Y5NDA5MDVhM2MxZTVmNTlkYTNkNzMxYTc5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFOL3M2cEpjLzliaGhXR2JlaHpEOUE9PSIsInZhbHVlIjoiOU5SVVd2MFNwOENGT0M2VEV2K0NnQTJkajV3Q3QrQXB3Uy9teG9JajRVcFR4QTJtQ0ZZS3N6Z0cvZmNxYjdqSkJMenNyZ2xBTVhMZ2lXR1Zqek5DMDRuVUlMcDRZOFc5UFBodWZJNklOUTVXUkRtZlBYK0ZZTG5wenRhb2JXWTgrTGx6K21aRUh5aUtUeTNqcmI5cmx4QWwyZjZ3QWlwTTZLa1MyL1d0L0hIc29zMGdYY1ZSNnBmR1F1WVQzRUxxZTVIN0hkU2pqUExBdmkyNktRb1E3VGFSczRkUW94THB4LzJWYnFGejhsQ2lvY1p5SG93Y3VDZFVUMWJKRW5LYnRiMms1Z3BQRkovWGhka3czZlFXMFA3Wk5STXBnbzFTOG5ta0hCRHA1UGlENFhQME90UkRLVjc4TGI2Yjhsc0ljZkhQL0hoOXNQQnJYajdoNmJsZDVuUWlrOHBMUWRFcUhITlcwQWtHYXZJTW5uWjhMaWE2eFhaTVBhbFdaYVRmYVYrK01SNzBCRkxWRklRaWZDdlJaN3F6d0RTUlhMekpmeUYxQzZsckJLMXI4eC9rVDRiVUxlSTB4U1pmT3FUTEdBSG8zVkw1eTJialZLSmxGemcxR0RlYUs2UzFVUnlJSHUxOHVZT0VsOWFpTFd6SzMyb2tRU3RpNkVFZHA0RU0iLCJtYWMiOiIyMjYzMGVkYzgwZWYxNjZhYThiZWEwNTE3NzJjYTdiODg4YmZjZWM1NDM1YWY0NWM3NDJiNzhhNTg2MGU1OGVkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636021514\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1485867129 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485867129\", {\"maxDepth\":0})</script>\n"}}