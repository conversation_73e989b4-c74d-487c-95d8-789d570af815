{"__meta": {"id": "Xc6a6b3e460222e94dc7d04e8709b1f6e", "datetime": "2025-06-28 16:01:39", "utime": **********.710284, "method": "GET", "uri": "/enhanced-pos/get-invoices?page=1&search=&date_from=&date_to=", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.200656, "end": **********.710297, "duration": 0.509641170501709, "duration_str": "510ms", "measures": [{"label": "Booting", "start": **********.200656, "relative_start": 0, "end": **********.588508, "relative_end": **********.588508, "duration": 0.3878519535064697, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.588519, "relative_start": 0.3878631591796875, "end": **********.710298, "relative_end": 9.5367431640625e-07, "duration": 0.12177896499633789, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46600808, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-invoices", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedInvoices", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_invoices", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2435\" onclick=\"\">app/Http/Controllers/PosController.php:2435-2501</a>"}, "queries": {"nb_statements": 17, "nb_failed_statements": 0, "accumulated_duration": 0.02123, "accumulated_duration_str": "21.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.623705, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 7.537}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.633537, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 7.537, "width_percent": 1.602}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2468}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6359951, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2468", "source": "app/Http/Controllers/PosController.php:2468", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2468", "ajax": false, "filename": "PosController.php", "line": "2468"}, "connection": "kdmkjkqknb", "start_percent": 9.138, "width_percent": 2.355}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2468}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6379728, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2468", "source": "app/Http/Controllers/PosController.php:2468", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2468", "ajax": false, "filename": "PosController.php", "line": "2468"}, "connection": "kdmkjkqknb", "start_percent": 11.493, "width_percent": 2.685}, {"sql": "select * from `customers` where `customers`.`id` in (10)", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2468}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6425998, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2468", "source": "app/Http/Controllers/PosController.php:2468", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2468", "ajax": false, "filename": "PosController.php", "line": "2468"}, "connection": "kdmkjkqknb", "start_percent": 14.178, "width_percent": 1.978}, {"sql": "select * from `pos_payments` where `pos_payments`.`pos_id` in (1448, 1452, 1453, 1454, 1455, 1456, 1457, 1458, 1459, 1460)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2468}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.644696, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2468", "source": "app/Http/Controllers/PosController.php:2468", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2468", "ajax": false, "filename": "PosController.php", "line": "2468"}, "connection": "kdmkjkqknb", "start_percent": 16.156, "width_percent": 2.449}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1460 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1460"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2477}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2470}], "start": **********.676103, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 18.606, "width_percent": 8.243}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 121}, {"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2477}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}], "start": **********.67918, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 26.849, "width_percent": 1.083}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1459 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1459"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2477}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2470}], "start": **********.6804419, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 27.932, "width_percent": 6.83}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1458 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1458"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2477}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2470}], "start": **********.682824, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 34.762, "width_percent": 6.642}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1457 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1457"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2477}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2470}], "start": **********.685161, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 41.404, "width_percent": 7.442}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1456 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1456"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2477}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2470}], "start": **********.688042, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 48.846, "width_percent": 10.221}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1455 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1455"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2477}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2470}], "start": **********.691504, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 59.067, "width_percent": 12.106}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1454 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1454"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2477}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2470}], "start": **********.6951818, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 71.173, "width_percent": 6.971}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1453 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1453"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2477}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2470}], "start": **********.697785, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 78.144, "width_percent": 6.783}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1452 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1452"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2477}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2470}], "start": **********.7002401, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 84.927, "width_percent": 6.5}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1448 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1448"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2477}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2470}], "start": **********.702584, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 91.427, "width_percent": 8.573}]}, "models": {"data": {"App\\Models\\PosProduct": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPosProduct.php&line=1", "ajax": false, "filename": "PosProduct.php", "line": "?"}}, "App\\Models\\Pos": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\PosPayment": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPosPayment.php&line=1", "ajax": false, "filename": "PosPayment.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 33, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-invoices", "status_code": "<pre class=sf-dump id=sf-dump-2134828037 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2134828037\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_to</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-56171057 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-56171057\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inc2ekx1cXA5R2JzT0lKQkRrUThqdFE9PSIsInZhbHVlIjoiejluYXFnanJrcXhhMHkySGIyUmhmTHAxYktpUmQybk9CZGorTXJnYWIyQVl5VHAydWFFa3dTVC9pMExPQmlsQlk5ZFVvRVZIUXFJY0N3NTU4MzBWMk5nOUVrZ3lPL3NiVGhKR1NJc0RJcXFBVjRxNFhhbHZFNHBvOWZna0lZVEJvMkw0V01NNGdlMTVucWZTWWFGRGtCMi8zNG5CQ0NlTFFzbmNoMCt5eURqZkRlKzdncDZEOFc1S0JIT1pyNDh6eDVkRnJuaXRjYXdFeko3eUFOc2JERjV5cHdSZ0JYUkpwa1N3bVhjVUtsbGlidkpMVFZDbDVOWjlrQlhqRXZGZUo0Wk94R1pWK2Jna3pxcUQrYkJONDd6R0Zja1pFeHBQaGtWdEVQL1BZSmJ3ajdqNndPMUpBNUJBME5tNjc2bTlUaEdib0hRbU9Fd2ppbE80SWRIcFdjb3huRjJLVlg0RHFDT0w5OHVxMUNITG9RU1FxenRvVmVkY3R4SnZienFNV09JcnVwZXhtUitrczdKeDZzRnZXWHpoSzVHRGl3TlBqWUtUeTZqTlB3TVpkcXhCcEdFMVFWUmg1aFE1OGlyMWtiK0xuSU1BdlpjMS9yWHZtOHlSNk1oTjdpdDFaOFFGNkRlZFVyQStacWVNNjd5UEhGTEhNY1lqTVQvQUI0K3UiLCJtYWMiOiJkZWQxNDZiOTc4YjNhNWJlODlmZDg3YWU4ZmI4ZDI5OTlmZWJlNGM4NmFjNzk5YzllMmY1MTk4YjM3NTg1MzY4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikd4cmE4VXlzV0svS1RtSWp2RzNUeWc9PSIsInZhbHVlIjoiY2VWZlA5SjVRQ2l5UDZ0QmRpQ0xtVnp1RElCb3o1OGNqQm0yM2V1T2xBREZUMFhYVVp5dXdGbWw5a1M5NzluTEFGT0ZJMHdINjlqMGpJTXFmVHBqcndMQnMwQ0c1dytvQjBvaE1xU3NMWjQ5cVIvK0lHYWJHZUw3UWV0TDQ2RWQreS81L3Nib2RST05NVWNpVUJqOFFlU2dlc3NIWjkrb2IyaGRLK2ZMTTVFNzl2VTFwMFZqMTliR2tETHIvdmdQd3ZGMU1uZWFIMjltSXRRV3pabUdNUXF1dmVjNUpmWmFua1Q3Vnpnd3RCeHhLaDk1MlBPc0gwVlczbnU1QUdLYTNpVWw1dnA5V1BrWUNQMmMvZUpLNzRJM3RGbmVnaENyTGQ0ZllQM1lTZFlkQkMva1gyNXJScjB3WGg5T3RBdXF6aExVcFhrQ05BTDlkVDhrOU5acWVmeUxlRStpRk1zZXdmVDhtcXlqRWZmbHhsZk5BTEMvd3lybFgvMThVRFpvY0I1SjZobm4wVkE3QXNHRGdrSnBEV3NBSUR1cHVSZmJ6SCswaUdmM0VoeDIvbCtMQldJVEUwdk94N0d4RFF1SXczQStMRTAra0JFcWV5QXkvM2k1M2ZBQWpVUXVPOXh6OTB5QkRYVW9ocHE1ZzN0UWVPVWJoSUk1N0JyYzc2SGgiLCJtYWMiOiIyYjA5Njk0Yjk3NzkxMDk3NWM0ZmFlNWFmNjA4NjQxYWU0YTBiNmI4ZjQ5Mjk3ZWU1MzBlNjhjNTM4ZGYzNDk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1160894472 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdXRDJTODNweEFsU0JWR1UwczVDRVE9PSIsInZhbHVlIjoieTlXNUM2SFBGTnExSytqbE9peWVDMXFGdlFZNkVYa29IdlRrb1RlT0hBbXFtZExmenFMTDl2Y0N2ZW1GbHQ1czU0OEkyMHdpa2pxc3FQdE1qbFNnWUFuQ1JnS2lLcmFCT0tDZ21nZVhtbVN0ejZhSnZJLy9DbFFKMzR4WnlvQnVxejRNU3krYW1Mb3dCMFNNK2R2aDRMM3g5L1llY3o2TXlFcjV4d05yUnlwa3FObzZ3NXUrMG1jU2dXcWRhN1VUS2hMVDZ1a3VzVGNLZEkvdnVCbE5reTY0Y0pZY1k3SzM5MGF5UitDOVNIR2JRR2MxVUFGZ2FtVmVhVXgrdXc0WG1VWVFTODh0eUptSVpPWFFBR29DdkUrbXRjM1B5SHdsTk1JUGZyaEN2MkNabVZoR1BCVFZMZVhHZTdGTzBOZjc2M3R4ZDVudXRkZk1SQUNFV09SZXlyMHoreWwwMVJzQklKNFNtK0tGbjMxM3ZnWDFjSThEdXFOR2tGdXlac3cwb0FYYitBSHFzelZUWVBSVHRFOGtDdkQrNytTTUVNeVd2bDl6aHNpMFB5S3JEYWs0SFdzNkJSdVRrT241WHF4MDAwYnhCQXBCN3NyM0I2U050ajllbElSTHFYczdvekdrMzQ2MmJhVVNORTBvV0VKZDMydVlKTlVnMlhjam5iV1IiLCJtYWMiOiI1NTIzYWRmYzQ3MjQyZDkyOTIzODMyNjlkNjIwMjM2N2VkNmQ2ZmQzNTRhOWNmMmY2MmM1YTU0YWU1YTZjZTZhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNaRHhKVzUxa1V6dEUycGFlSHcrWmc9PSIsInZhbHVlIjoieEhueEl5bGpGeFY4eHNWQS9Lam9MZStKQVFST3JmOHpEYkhPdXJwYTAyNDA0UFRuS0llWnBrSWdkbWplbHcxbTltcG4yckhCbDFITEFaRE9sa28rYisrdzdxaGdqM3ExaE5OaTRobFBKcng0OGZycW5qK2NkVnM4c0ZhZ21zRGlpMGViNWpoQnNKV2p5UDY2UFQwb1hEU0lsakZLbnlxRzVBa0dKdG1OSnVSclRXNllCZmdVNEl1emJHditKTEpxdllpTU9IY1F4UXdNQ2RCdHpzemEyS2R4SG43cjRQVkhNWU56ekZvVktXRzhtVkRyYVhQa3VVMXdxTFNhekdpalRmbEVobERMcmFyVUlXc0ZuQXZIWmRFK0lldEJFOFk1MnhkYnVuOFpoU1MydDhpVzZPbktEM2svZ1BIbHhQaHpnOExDRzZkOVM2MlRjWWtmb3lub2NWQ0srWkhOMWFOOHQrcTh6ZkcwWXR6MlhCdlpqNVdnTUdBWTBWOXdBR1pjV0Z3cm5ERnhNdkFSQ25rNFR0S1RNV2FrNFNxQmZZRVNkWWZmOUg2QWwxVVFWSEFxWWQ3RFR3WW5VV2daWVNYT01BanFZejluREFoK3dVWEhGTHNmNHZEK0tEd3NtNUZhaitOa1NpdHRWWlFrR2pvcHBUWHRDK1Q2YVF1T0NJVmIiLCJtYWMiOiJkODFjYjRlMDNhNmUzZTY5ZDA1YWYyY2U0NjE4MzQ1NzNkY2FhNWZiZjFmZDRiN2Q4MmNmYjNkYmRiMzk4ZmZkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdXRDJTODNweEFsU0JWR1UwczVDRVE9PSIsInZhbHVlIjoieTlXNUM2SFBGTnExSytqbE9peWVDMXFGdlFZNkVYa29IdlRrb1RlT0hBbXFtZExmenFMTDl2Y0N2ZW1GbHQ1czU0OEkyMHdpa2pxc3FQdE1qbFNnWUFuQ1JnS2lLcmFCT0tDZ21nZVhtbVN0ejZhSnZJLy9DbFFKMzR4WnlvQnVxejRNU3krYW1Mb3dCMFNNK2R2aDRMM3g5L1llY3o2TXlFcjV4d05yUnlwa3FObzZ3NXUrMG1jU2dXcWRhN1VUS2hMVDZ1a3VzVGNLZEkvdnVCbE5reTY0Y0pZY1k3SzM5MGF5UitDOVNIR2JRR2MxVUFGZ2FtVmVhVXgrdXc0WG1VWVFTODh0eUptSVpPWFFBR29DdkUrbXRjM1B5SHdsTk1JUGZyaEN2MkNabVZoR1BCVFZMZVhHZTdGTzBOZjc2M3R4ZDVudXRkZk1SQUNFV09SZXlyMHoreWwwMVJzQklKNFNtK0tGbjMxM3ZnWDFjSThEdXFOR2tGdXlac3cwb0FYYitBSHFzelZUWVBSVHRFOGtDdkQrNytTTUVNeVd2bDl6aHNpMFB5S3JEYWs0SFdzNkJSdVRrT241WHF4MDAwYnhCQXBCN3NyM0I2U050ajllbElSTHFYczdvekdrMzQ2MmJhVVNORTBvV0VKZDMydVlKTlVnMlhjam5iV1IiLCJtYWMiOiI1NTIzYWRmYzQ3MjQyZDkyOTIzODMyNjlkNjIwMjM2N2VkNmQ2ZmQzNTRhOWNmMmY2MmM1YTU0YWU1YTZjZTZhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNaRHhKVzUxa1V6dEUycGFlSHcrWmc9PSIsInZhbHVlIjoieEhueEl5bGpGeFY4eHNWQS9Lam9MZStKQVFST3JmOHpEYkhPdXJwYTAyNDA0UFRuS0llWnBrSWdkbWplbHcxbTltcG4yckhCbDFITEFaRE9sa28rYisrdzdxaGdqM3ExaE5OaTRobFBKcng0OGZycW5qK2NkVnM4c0ZhZ21zRGlpMGViNWpoQnNKV2p5UDY2UFQwb1hEU0lsakZLbnlxRzVBa0dKdG1OSnVSclRXNllCZmdVNEl1emJHditKTEpxdllpTU9IY1F4UXdNQ2RCdHpzemEyS2R4SG43cjRQVkhNWU56ekZvVktXRzhtVkRyYVhQa3VVMXdxTFNhekdpalRmbEVobERMcmFyVUlXc0ZuQXZIWmRFK0lldEJFOFk1MnhkYnVuOFpoU1MydDhpVzZPbktEM2svZ1BIbHhQaHpnOExDRzZkOVM2MlRjWWtmb3lub2NWQ0srWkhOMWFOOHQrcTh6ZkcwWXR6MlhCdlpqNVdnTUdBWTBWOXdBR1pjV0Z3cm5ERnhNdkFSQ25rNFR0S1RNV2FrNFNxQmZZRVNkWWZmOUg2QWwxVVFWSEFxWWQ3RFR3WW5VV2daWVNYT01BanFZejluREFoK3dVWEhGTHNmNHZEK0tEd3NtNUZhaitOa1NpdHRWWlFrR2pvcHBUWHRDK1Q2YVF1T0NJVmIiLCJtYWMiOiJkODFjYjRlMDNhNmUzZTY5ZDA1YWYyY2U0NjE4MzQ1NzNkY2FhNWZiZjFmZDRiN2Q4MmNmYjNkYmRiMzk4ZmZkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160894472\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}