{"__meta": {"id": "Xbda6e9ee74ca72e9a48939986b825b55", "datetime": "2025-06-28 15:35:06", "utime": **********.039782, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.471326, "end": **********.039799, "duration": 0.5684728622436523, "duration_str": "568ms", "measures": [{"label": "Booting", "start": **********.471326, "relative_start": 0, "end": **********.913246, "relative_end": **********.913246, "duration": 0.44191980361938477, "duration_str": "442ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.913258, "relative_start": 0.44193196296691895, "end": **********.039802, "relative_end": 3.0994415283203125e-06, "duration": 0.12654399871826172, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48234656, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02446, "accumulated_duration_str": "24.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.970333, "duration": 0.01805, "duration_str": "18.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.794}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.996896, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.794, "width_percent": 1.84}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.0154212, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 75.634, "width_percent": 3.352}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.01774, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.986, "width_percent": 2.208}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.024273, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 81.194, "width_percent": 12.388}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.029851, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 93.581, "width_percent": 6.419}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1027770272 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027770272\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.023089, "xdebug_link": null}]}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-687171692 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-687171692\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-364969638 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-364969638\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1964514691 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1964514691\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1604623929 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751124481231%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRyOURTd1dYZlZjcTBUOFVOL2xTQlE9PSIsInZhbHVlIjoiVHhlTXpYd2FDbE8yYktZSzJUM3A0U3Jwc1lNd1dwdWMzWTl2WlRxbktFZkVlN2VtWEFLeDhUaEdtMm9CTlJSZXQzWE0zcHh1YmxTTlp1M2tVdVBXVks4bUJmRlJ2K0lYSUNVQVJMbjkxeFFmUmJpS2JJeDhDZkMrcnpQZEtTY3NNZ0lXRmN6MlN5bG43WFVBeE1KYWk3S1J1WTduVDE3c3JLSGZ5c0VNVWtlbklxRGFlY1Q3eFU5Z0FEVzZsUkVuL1g3SmVHSzhqNTcrN2kwT2VyUmFENE81eVRabGZuN3lVQ2lVTS91dDE4NjF5UXhEQTJobkpOQTNkZ0FyYXR3andZUzZQMzV0UVI2UHg2M0NtT2FkRUorTDV6T3JVRlNKaWhuZ1RoR1NHVGs0YW1EN0Q3eWxkcnhjU3RqN3RyZDhoTEZUNkpYekdQckh4Z2VleS9KNEJxTlZENWl0M21NTDQ0d05HdTdhVHNFQUhJSXd3cU5RbXptemxvTnAvNVpwZ3hLT3grN3FsNWxkaVRmVjNIQUZSQlZWUEhRck9pbC8vVHVLN2dEOEI0UVcyaVNIOFZCNEU2WlFTTmZJV01uT0gvaldvM3daZ2hhMWdrS0VONlArc3crVGZ2TUhDQlpZVyswSmtpOTBxRzhaZk0vWnB0VXA2aXd6c3picWdGSy8iLCJtYWMiOiI4MDNkZDVlODZlNmUwMjFhYWZhOGIyODg0MzJjM2Y0NWNiOTk1MDA2NWEzM2NiYWJkYmVmNjQ5ZTQ4OGRkYWMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhvN1B5cHN4Q2Vza3BCVHRFZjd3SEE9PSIsInZhbHVlIjoibVBHS2pEbEIxSWE3eG81R1E3QlZSazJkSkpoYWFxOXp2d0VjN2F4TmdyQjFablVqRDB1UzVGZGNVYzlvZjNnTUl4TzVNV2s4bEhkS1JkWjlkSVUwMFBGakZDYkk2K3M3aTdFQnZDbGJQRXRza2E1dklCUlhHQVRVWCtnSFBDbmg0QWt6VFVvY0FhSmhFUzZJUzFNcWhQamxGQnFLNkxtckxUZnptS0RXMVJaemoyUk1PVUZoM1ZwR2NXR0xJV0hodzVLeEJYNDdYNHc4eUNDT0dET2pGQVY5Y1QyeTNvQ3BxYmtJQlBKeC9jNDlsVVZtcHlHSGttdUF4c1k3ZWhybklhZ0gwaTAvQTlSUXRBR3NoM2pid0luZTN6UWppdnNFc01vMTJyajErKzRSOXZqUFNOemRtVGZDTFpVSzJkMDdSZEcyL0MybFg0cGhYQkp3SENmMzV1MWxmWitCcTZiYlBURjBOSVFPaFg4THBodUlSVE8zdEF4Y3NOdlZGYnpYQitHczZteXc0U3p4NXpxY2FqYnE4bmE1dVBaUHRMdTRBN3lTb1BmOERyd0R1OHhXYkVzTXUwUHh1NnFYUDRWQWM0UzRER0VRcTczTG5OWXBMMVhBN2t4U0Z5MDcxL3hQUUxsRElpdk5wVkIvZDMxdXRIdlJRVU5GSnY5WEtoamIiLCJtYWMiOiI2ZjY1NTE0OGZmNWU2MTczNDEyYzkwNTc1MTk1NDEzYWQ5ZWYxYWFlNTYxNGMzZGNiZTE4ZTgxMTA2ZGRhNWU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1604623929\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-159888058 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:35:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imw0c3V3WXp0NWJvTlpkbFRDVE9mYlE9PSIsInZhbHVlIjoiemRwSTRFR1BodkdQM2h2ZTlKM3lJQUhkVERKd1ZhSENmanJJamRUVXBEdlVkS2dtZW5VNlo3RlcrdGFnYnYyME16MjFtQnBEaVZyRm03dVNxdlZBbkpjUGdrUmROajlkN1o5ZXpIWmN5c0VqK0pwVDE2STJ3M1JFcmNpOHNuOU1Ea1BpUW50V1pUN3QydXM3bjA3TkxXMUJ4bGN1RFN2Qm41c21ES2FBM0FqRzVsamcvZU5heG45TWZ2QkdhM01tRWNuOE1jMHFEZGNsV3Fqd0Rab252aDFLTWNUcHdNdFkwb0ZjWDViaC9LdklxaXRSazhQOEZVR2xsMkZpMjM2SXhXQ0NoOUQ2SjE2VTd6L1RqaXNCc29La0kyK0pVZjRKWEdCcDIxWEpqcnE1VmtETFdMVmxJVjBDRlRtTjAzdGtjUkZQWkQySnBoUHJvQ1dCNERsdDE4RlptT2JxZTJjSER0SVBwMjlZR3Nna2VMTVhNeXc3ZWRBUVp3UnNXUG03d0hjd0tCaUh1Q1lXUHZvVmIxbEhwemdCZ3FVeFNIbkNtNlBnaTRlOVQ1SUhaNkxSNGUwa25IZ0RoWTRCVFUzcU53RTZPNmhnenVpNVppYS9oNTNkUTlFaE12b3c2Mlc3VnJvOXlLUjNTRHlLOGhMRndRMVlqTVBzdUIrSHBCS2MiLCJtYWMiOiI2YmQ3ODdhMzk1Mzg1YjRjOWNhZmQyMmM0M2Q4MTQzMGNlNmMyNTRhYTIzZTgzNDUyYjIzMjcxMDgzYTRhMGQzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:35:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1EeWQza2dBUG1pU0FSZ2dwS0RKM0E9PSIsInZhbHVlIjoibEIwSXBHRkM5VjFKOGhwMlZ0VnhCZ25NejFPcmhOT1M2NDFEVTN4eDQyR29LT25wTW45SlhPeGhZYnhFVktaM0V1TGdhaE5IUGRvUWdYRnhONU0wd3dCTkJDQ0tvMGVlV2ZrQXY4R2hPNXN3azdvM1dsQWRLandEbzIzdHJrRzNuMjN3T0JURzhTT3VHdnNDcFVkR1R5ZHVXTUl2YWMwWGdnT1hHdHZVQW9DMk9xOUZsdzVYNjYyU1FIdmRpemZpTU8vUlNFWTBXQXZGa1BxL01wQXVkTitLQ1k0eEovL05Xc1pVa3U4YUwwUTREeTdDbkQvSTF4Q1pJUC9tamJzSnZ5MVFhcWVuUTJnZVhtWHgxNURjbGJnc1V1d0FJcSs1dHc0aFhHWGIyc0psVkUxbnNhZzcxcTZ4NE05dFVGK2RKcnk4M2tqdFdjcDRLMzUzaGZlQVdtOTN5VFpnVHN6Z0hlczdRNDJhRGsyb2g3SUQzU0xwQi9VRnRFTWVGTkY4M09HNUFjdTgvSzVLMnJiR2w0QllybGdNOEl5NHBLa3Z0NzRPcFp4ZkxVN095dllZdlVSY3JhZm03Uk94RDNlbk5JWFZGSjFCemFkVjZiNWZPcDQwamRrYVNILzRDK2E0MEdQMEVBN2o0SU1FekFCK1RzVDV3djVHdUFqWWxzMm8iLCJtYWMiOiI0NmJhYTkwODRlNTBmZWM2MjFiYzc0ZmE4NzM0ZjhmZWE0NDMxMjc4YjkzN2IyNTI1MTJlZDU0Nzc1Njg4YmJjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:35:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imw0c3V3WXp0NWJvTlpkbFRDVE9mYlE9PSIsInZhbHVlIjoiemRwSTRFR1BodkdQM2h2ZTlKM3lJQUhkVERKd1ZhSENmanJJamRUVXBEdlVkS2dtZW5VNlo3RlcrdGFnYnYyME16MjFtQnBEaVZyRm03dVNxdlZBbkpjUGdrUmROajlkN1o5ZXpIWmN5c0VqK0pwVDE2STJ3M1JFcmNpOHNuOU1Ea1BpUW50V1pUN3QydXM3bjA3TkxXMUJ4bGN1RFN2Qm41c21ES2FBM0FqRzVsamcvZU5heG45TWZ2QkdhM01tRWNuOE1jMHFEZGNsV3Fqd0Rab252aDFLTWNUcHdNdFkwb0ZjWDViaC9LdklxaXRSazhQOEZVR2xsMkZpMjM2SXhXQ0NoOUQ2SjE2VTd6L1RqaXNCc29La0kyK0pVZjRKWEdCcDIxWEpqcnE1VmtETFdMVmxJVjBDRlRtTjAzdGtjUkZQWkQySnBoUHJvQ1dCNERsdDE4RlptT2JxZTJjSER0SVBwMjlZR3Nna2VMTVhNeXc3ZWRBUVp3UnNXUG03d0hjd0tCaUh1Q1lXUHZvVmIxbEhwemdCZ3FVeFNIbkNtNlBnaTRlOVQ1SUhaNkxSNGUwa25IZ0RoWTRCVFUzcU53RTZPNmhnenVpNVppYS9oNTNkUTlFaE12b3c2Mlc3VnJvOXlLUjNTRHlLOGhMRndRMVlqTVBzdUIrSHBCS2MiLCJtYWMiOiI2YmQ3ODdhMzk1Mzg1YjRjOWNhZmQyMmM0M2Q4MTQzMGNlNmMyNTRhYTIzZTgzNDUyYjIzMjcxMDgzYTRhMGQzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:35:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1EeWQza2dBUG1pU0FSZ2dwS0RKM0E9PSIsInZhbHVlIjoibEIwSXBHRkM5VjFKOGhwMlZ0VnhCZ25NejFPcmhOT1M2NDFEVTN4eDQyR29LT25wTW45SlhPeGhZYnhFVktaM0V1TGdhaE5IUGRvUWdYRnhONU0wd3dCTkJDQ0tvMGVlV2ZrQXY4R2hPNXN3azdvM1dsQWRLandEbzIzdHJrRzNuMjN3T0JURzhTT3VHdnNDcFVkR1R5ZHVXTUl2YWMwWGdnT1hHdHZVQW9DMk9xOUZsdzVYNjYyU1FIdmRpemZpTU8vUlNFWTBXQXZGa1BxL01wQXVkTitLQ1k0eEovL05Xc1pVa3U4YUwwUTREeTdDbkQvSTF4Q1pJUC9tamJzSnZ5MVFhcWVuUTJnZVhtWHgxNURjbGJnc1V1d0FJcSs1dHc0aFhHWGIyc0psVkUxbnNhZzcxcTZ4NE05dFVGK2RKcnk4M2tqdFdjcDRLMzUzaGZlQVdtOTN5VFpnVHN6Z0hlczdRNDJhRGsyb2g3SUQzU0xwQi9VRnRFTWVGTkY4M09HNUFjdTgvSzVLMnJiR2w0QllybGdNOEl5NHBLa3Z0NzRPcFp4ZkxVN095dllZdlVSY3JhZm03Uk94RDNlbk5JWFZGSjFCemFkVjZiNWZPcDQwamRrYVNILzRDK2E0MEdQMEVBN2o0SU1FekFCK1RzVDV3djVHdUFqWWxzMm8iLCJtYWMiOiI0NmJhYTkwODRlNTBmZWM2MjFiYzc0ZmE4NzM0ZjhmZWE0NDMxMjc4YjkzN2IyNTI1MTJlZDU0Nzc1Njg4YmJjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:35:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-159888058\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-748316999 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-748316999\", {\"maxDepth\":0})</script>\n"}}