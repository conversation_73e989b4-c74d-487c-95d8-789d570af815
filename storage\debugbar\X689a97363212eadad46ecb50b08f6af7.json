{"__meta": {"id": "X689a97363212eadad46ecb50b08f6af7", "datetime": "2025-06-28 16:19:19", "utime": **********.522801, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[16:19:19] LOG.info: Enhanced POS Add to Cart Request: {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"price\": 2.99,\n    \"name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\",\n    \"is_manual\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.514298, "xdebug_link": null, "collector": "log"}, {"message": "[16:19:19] LOG.info: Manual product check passed {\n    \"is_manual_value\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.514551, "xdebug_link": null, "collector": "log"}, {"message": "[16:19:19] LOG.info: Product found successfully {\n    \"product_id\": \"2299\",\n    \"product_name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.516805, "xdebug_link": null, "collector": "log"}, {"message": "[16:19:19] LOG.info: Product added to Enhanced POS cart successfully {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"cart_count\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.516926, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.134925, "end": **********.522819, "duration": 0.3878941535949707, "duration_str": "388ms", "measures": [{"label": "Booting", "start": **********.134925, "relative_start": 0, "end": **********.468146, "relative_end": **********.468146, "duration": 0.3332211971282959, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.468154, "relative_start": 0.33322906494140625, "end": **********.52282, "relative_end": 9.5367431640625e-07, "duration": 0.05466604232788086, "duration_str": "54.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46016504, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2015\" onclick=\"\">app/Http/Controllers/PosController.php:2015-2120</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027099999999999997, "accumulated_duration_str": "2.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5013561, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.742}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.511778, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.742, "width_percent": 17.712}, {"sql": "select * from `product_services` where `product_services`.`id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2053}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.515281, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2053", "source": "app/Http/Controllers/PosController.php:2053", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2053", "ajax": false, "filename": "PosController.php", "line": "2053"}, "connection": "kdmkjkqknb", "start_percent": 87.454, "width_percent": 12.546}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1588830728 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1588830728\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127556646%7C37%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imw3ZWpXQ2RFenVBbVZ5dm5WLzFvMlE9PSIsInZhbHVlIjoiM0J0ZTgxS1RBdDE0OHJVeHluT2NIOXdVV0VPbnpOUkVIWGRGVU9vRFNSQ0VwOGwyb1pZZ3lvMDRxOTdSVjF1djQxTGY3L1FxalM4TTROMWZNQi9JTWpNOXNMV0ZVOTQrN3ptUUE3Q29LeC9JSlVTN1EyanhLeHZjejRYWXYxR3hKVXExdllZUUhVeW1rbElCRGFIQkFEZVNwSDJEOXhjcEJkZDRVN2lZSWtwVExEaGVBdjZJdG9iQnBMVUhiSW1MK3lseVJ3UUtmVFdRVm1WN2VTclJ2V0Rrekdkb2tOOFlUOUMwSUlqU0ZwVHhIOTJOUjluMmJRRjdkTFFjbmpIK000VVlxVVFSemo3Z01naXhQcW1RV2t5alYvam5BOXN3MjdBZ0lFS2pyNHB3NEVVenJSeHZHZDZ3LzR4VTZUcGxTbUNwT1NTdGNaUFltdVVqRXMvTTdWUzJsV2xOWXZScXlFb3ZNcVh2b3NrbUZwdVJCOGU4RXYvNjQraDdTRlkybU9ETWVlMXVvY2tYcGFrcXJ0aEh4a2RyYTNkcmNjYVFyWDNDOTNsa3RqUmNDZUNzeEVnckh6aExmK2pmS3BGRGNPek5pa1gzT0RqKzY0OGxodk5zSmxaYVJxRzBBUkpHYjZSUmc3RDVtSS9vTHV4Q1JEWmtFMlNMbHBTYk1nTjciLCJtYWMiOiIwYTM4NzllNGE0NmQwNWVjNGU5OGMwNzFjMjdmNTAzZGE1NThiNTFiY2QxNGRlMjczNjdkY2IzMjUyZTk0YWJhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJqa2k4bGRSRTJHaHBSdFVHVkdES0E9PSIsInZhbHVlIjoibzBXQk5YUE9iV1NlV25Wb053YituR042eUFNcVlqOW80ZExjMjBNYVJDWmVxdW1Eb0ZTNGJTeWtIZytTMXRSRThLMUtlYVB0TDlQVHFKbmxFemVnaDJZSUJnM3B4bG1xMVJsSjBybE5DS09EWUcrMnRBNmVocnFDb3BVMnVkaVFROUpJdmQ0NXFpcGdvQ0RKSnFrRklXVmJuYWtwQk5FRmhHRXN1S3ZrMWd6QmMwZkFjcHBzcXBoUzQ5U3ZjSHQ3N21IQ0RhbHU4M0tjMTQ0YkpDdVhLOHlSSUtIM09hdVRtalBFaVZPMnE0YTZ2a3hDYXpGTlhib3BrVk04ckVQNGtaeGxCSXo2K2VwQVFYczhydHRXdDZEQmVIU2VDL1RxMkVueXhBWTRGZVh5bm9XT010WEsxVk5ua2ZVV0JBRkZOYVU0U1V6aDJzMHovRW0yclNrN1MxbStDV3plNFkwWktlWE1YUlA5RjJ3Qjg4b2VJV3BaVkRPaXFYb1dOVTVZK29NRkNMZUtDVFArR0dnOU5lbnlnSzc5dkx6djR6cHhJaXZSY0VFbUJ3amdJQzhweTlYTFErL0IvZGU1cG0yMU1DNytodmplYXVjTHM3U2c4RXdmVjZpVGNQb1ZtK283ems5SGRZZlZ2dUNMZUpVc2pLZEVsejg5bE82azFaWWEiLCJtYWMiOiIwNDE3Y2JkNmYxM2ZiNGU5ZDVlYTkxM2YwZTI5NWJmZmIyNzFiYThiYzNhNjNhMTMyOGRkODRhNDY2ZWNkNzc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-51607348 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-51607348\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJmeDlrRWxNcWZOVzQ2MUUwTVNSNUE9PSIsInZhbHVlIjoicEsvQU1FS01vaEY3a3FFbFo5dDJIdlpLV0ZEVG4zb29Qc0YwU09LbHVPMHNMZGNqbFZPL2JWZWxqSWVrOU1NZlZRQi9wOENPcjBLNlExZ2Rzd1dabzdETDVSK25LbmJKZEQwcmRjZVdjaUxjcnZLQzFCMkNyRjRFTi92cDFYSmNmUXhjYmRQK1Q2Z0R3cUlLN2xmcGtKRFlaQXBNTzN2c25xMlJTa1B1QVpIWWxwRGh5dU1oYUdVR01CRkFsbWk4Tm9nWFFubGFpTk5mV1dUT3pnek1RNlhmN2ExQzBMWWR5SSsxWmJMZll3bUJJeUcwZFUvWlNQcTlQQWJKeDJBR1VsYnMwTGc3V2tCdk5FRGsxa1FRTUhkZHJFQW9kS0wwUWlHajh5cXJtUWx1c0hXNDFJQ3dQbVQ5b3NESS9CbHl3MzNHMkNrZHczcmpQVm8yVWFrME43RjU0NWp6V0ZrNGlvUTk0N0ZaYWJvOE0wVEpBVmR0Q1F3UnVxVFR6SmZtRlIwdFBVVEk5Q1dHS0ZyeWs5eFI3bkNsUGc4SUxHOTh2dlcxTzJpeng2QzF1bEduTFhPU3FYOWhITndGODJqWVJscXVkY3NmLzZQdUlwNWszNWRJNUdCNXNNTVRTZ0xIUWQxVXFuNDczOFdHVlltZTR1RXNrbGV3TEpoakdPcjkiLCJtYWMiOiI0OTRlMjA2NDM5OTJlYTQ2ZmUzNjk3MjNjYzhhOWIwNzNkM2NjMmNkOGZkMTUyNDY0NGRlNWFkM2U5MTkwZTA2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJZaitUMGlQbzBsaGxON1FrWnNVMVE9PSIsInZhbHVlIjoiWFdsK2FIdUZwTU96YnFBMEJseXBXTnZtS3czZDFsRXpBQThFbWtuM3hPSU5IMjZkRGhtSkwxT2VqTHhQbGxicVJydFFubE1qc0lXNEIyaTdvZkRJaVAvNG4xTDEySVArSEhoUnR3V09PSTZYSGpBRytXV0ExckRIMVNMNmh6WVo0aHE1bUVVU2JpOXN2eHBCK1J0RGw3Rkg3TTVYaUlkcTZJbUpGVTFnMi9TQTU4Y0VnTmdVT2MyQ2pOQVFPcTRGZmJjbHhScHpxbVNMYWkxclBFZzFXL29NZUpHcDRWS1hsaVRjZ0M3Z1FOL2cwZmM4WjhTaUZLdlNqUTdxc2lQZHhibEVmUDRDTjJGbzljZTNOTzlqMm9tZlFZK1FOZk93VGpPVjVXRll1Y2tMU255T0M2dlBGMEtTRXh5TytqNU9TOGNKN0FrbHlLV0tPWUZ2RERnQnIzdG90dVRmYXFiaG9SYzk4UjQ0cmM0ZlZJQWszMW02NEtidmVzckR3NTNRZzZxdVhYbjZMTG9DZDd0SGo0T0NPTC80bE0xSzdUQ3Q2SEcremdVcE1SMGJrVWdJS0hGQXBMUjdUckZhbkxRaW5RUmlEWkJSV0QrSEpPeENlS0MzRnQyb2kwcGMrdzVteXovVjB1QmZqTk9DSE5qOVNOcXZpT2J5em1rVjFUYVUiLCJtYWMiOiJkZTIzN2Y4OTFiNDU3ZTcyMWNjNTBjNDAwYmJjNjM5MmZhYWMxZWE5ODUyNTVhODIyYmE1YzEzZjc5ODczZDBiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJmeDlrRWxNcWZOVzQ2MUUwTVNSNUE9PSIsInZhbHVlIjoicEsvQU1FS01vaEY3a3FFbFo5dDJIdlpLV0ZEVG4zb29Qc0YwU09LbHVPMHNMZGNqbFZPL2JWZWxqSWVrOU1NZlZRQi9wOENPcjBLNlExZ2Rzd1dabzdETDVSK25LbmJKZEQwcmRjZVdjaUxjcnZLQzFCMkNyRjRFTi92cDFYSmNmUXhjYmRQK1Q2Z0R3cUlLN2xmcGtKRFlaQXBNTzN2c25xMlJTa1B1QVpIWWxwRGh5dU1oYUdVR01CRkFsbWk4Tm9nWFFubGFpTk5mV1dUT3pnek1RNlhmN2ExQzBMWWR5SSsxWmJMZll3bUJJeUcwZFUvWlNQcTlQQWJKeDJBR1VsYnMwTGc3V2tCdk5FRGsxa1FRTUhkZHJFQW9kS0wwUWlHajh5cXJtUWx1c0hXNDFJQ3dQbVQ5b3NESS9CbHl3MzNHMkNrZHczcmpQVm8yVWFrME43RjU0NWp6V0ZrNGlvUTk0N0ZaYWJvOE0wVEpBVmR0Q1F3UnVxVFR6SmZtRlIwdFBVVEk5Q1dHS0ZyeWs5eFI3bkNsUGc4SUxHOTh2dlcxTzJpeng2QzF1bEduTFhPU3FYOWhITndGODJqWVJscXVkY3NmLzZQdUlwNWszNWRJNUdCNXNNTVRTZ0xIUWQxVXFuNDczOFdHVlltZTR1RXNrbGV3TEpoakdPcjkiLCJtYWMiOiI0OTRlMjA2NDM5OTJlYTQ2ZmUzNjk3MjNjYzhhOWIwNzNkM2NjMmNkOGZkMTUyNDY0NGRlNWFkM2U5MTkwZTA2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJZaitUMGlQbzBsaGxON1FrWnNVMVE9PSIsInZhbHVlIjoiWFdsK2FIdUZwTU96YnFBMEJseXBXTnZtS3czZDFsRXpBQThFbWtuM3hPSU5IMjZkRGhtSkwxT2VqTHhQbGxicVJydFFubE1qc0lXNEIyaTdvZkRJaVAvNG4xTDEySVArSEhoUnR3V09PSTZYSGpBRytXV0ExckRIMVNMNmh6WVo0aHE1bUVVU2JpOXN2eHBCK1J0RGw3Rkg3TTVYaUlkcTZJbUpGVTFnMi9TQTU4Y0VnTmdVT2MyQ2pOQVFPcTRGZmJjbHhScHpxbVNMYWkxclBFZzFXL29NZUpHcDRWS1hsaVRjZ0M3Z1FOL2cwZmM4WjhTaUZLdlNqUTdxc2lQZHhibEVmUDRDTjJGbzljZTNOTzlqMm9tZlFZK1FOZk93VGpPVjVXRll1Y2tMU255T0M2dlBGMEtTRXh5TytqNU9TOGNKN0FrbHlLV0tPWUZ2RERnQnIzdG90dVRmYXFiaG9SYzk4UjQ0cmM0ZlZJQWszMW02NEtidmVzckR3NTNRZzZxdVhYbjZMTG9DZDd0SGo0T0NPTC80bE0xSzdUQ3Q2SEcremdVcE1SMGJrVWdJS0hGQXBMUjdUckZhbkxRaW5RUmlEWkJSV0QrSEpPeENlS0MzRnQyb2kwcGMrdzVteXovVjB1QmZqTk9DSE5qOVNOcXZpT2J5em1rVjFUYVUiLCJtYWMiOiJkZTIzN2Y4OTFiNDU3ZTcyMWNjNTBjNDAwYmJjNjM5MmZhYWMxZWE5ODUyNTVhODIyYmE1YzEzZjc5ODczZDBiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1145635212 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145635212\", {\"maxDepth\":0})</script>\n"}}