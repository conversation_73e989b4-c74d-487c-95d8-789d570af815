{"__meta": {"id": "X0328dfcb7c395587ada60854979345df", "datetime": "2025-06-28 16:01:27", "utime": **********.943806, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.428666, "end": **********.94382, "duration": 0.5151538848876953, "duration_str": "515ms", "measures": [{"label": "Booting", "start": **********.428666, "relative_start": 0, "end": **********.875802, "relative_end": **********.875802, "duration": 0.44713592529296875, "duration_str": "447ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.875814, "relative_start": 0.44714784622192383, "end": **********.943822, "relative_end": 1.9073486328125e-06, "duration": 0.0680079460144043, "duration_str": "68.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831872, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0052, "accumulated_duration_str": "5.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9202, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 43.462}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.931416, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 43.462, "width_percent": 10}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"prod%' or `sku` LIKE '%<div class=\\\"prod%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;prod%", "%&lt;div class=&quot;prod%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.934176, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 53.462, "width_percent": 46.538}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-55504911 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-55504911\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1294944864 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1294944864\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1916946374 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"16 characters\">&lt;div class=&quot;prod</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916946374\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1MaklyVFBad2VSTDh0VXhKWDI0clE9PSIsInZhbHVlIjoiVmgxVm43TXFpYTZXWW8ydndUREJWZGdMZnJzakwwMUR3RmFJS3hsKzZkenZQTnF2OXc2U2FlYzlZSHdqNXlVTm5ybWxuMEI2OG5EUGhXOU45Y1VNV0RUSUZSYjdrcVBEYi94NUp6QlU2VE9tYnFCb1BaeHRkNVR2YWVYT0NIQ1ZSblk2Mi9GQ01raC8vbng2Z0lwTy9qc1d4VjZmSFU3aUJhb3pWMzFGUjRFdEJKQ1JwbTdHcXh1aWZFNzVibzBiVkxzbDdDalpObkgwK1FER0FKaVNOSFFBN2VBVHhmOGh0NFpvVjNTZHN6KzdsVTY2VU1RL1VLd3FzNENqT2pPUFI2ZHM5RmNqcjArdFc4bjR3aTh4VXhtbi9ZV1ZJN1lqczN2bEZLMTJaS1lHMHZzQ1I0WlgyM3VSV0ovOXV0VTZDOXUzWmprVm13YmtuQWY0ZHFVYXltMWxrckI4QXFvdGFTWHZiZU92RUd6K1p0MzRtUE5rc1BaZU1sREZMM2dqQ0JFT0dHNnBPWE84Uk10UnVDcisrZjNoOXFPclU2VGcvdVdWanB5UVNLYVU0Vjk3U0ZwOUxQcTY3Q0pqbmk4YWNWKytjU29QYjFOUWtPdjBYWkhYYmI4QzRrNm8yZUVZdG9NMnFxejRGS3ZOZHI0VkZMblMzNTAzeHV0Z1R2a1YiLCJtYWMiOiIwNWMzODkzNWI0M2RiY2U1ZWIyNmQ2MWIzZjAxMzExNDg3YjE2OGIxNzViMzk0NDMzOWE0NDQ4OTNhNGNkMzYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InNPalhZa2x2YWNOZGJLR05TWnFJSWc9PSIsInZhbHVlIjoick94MEpiWCtHQjAvQ1BScVZSdEVDUlhBcFZwd3JHRHJpTS90OSs5UFNLSERFR1BYMHNod1BlTjIyWExQUU82UnRQc2lrTVJMYXpjU2RTUjY2b045clZCK1NwQWozaFhOcXlNT2k2R09uUDlISkY1R0tuTHlQN2F2ZENOU29XVThGdkxyQXBVSnlSVElvUlZwUGhuenc2YjBVQUNTOTM2NTdjNTNTNm9LNmd4VTNPeGQvR0ZRUkNQY0tBQUZvWG9tOTZkMzdzZmZETlM2bHZmank0a1BMWG1ySmJOanFRaHByM2IzbnIxR25pUGZSZ0FCRUFJS3VxTTI0b0pEcTI4Y25KWFR2WG96QXBMSm10ZGlvcnZUd21JbHltOVZ5U29NWnBZQ2paUEZVMXdpaHhJaUpOd2FVNVlBNjhxdnNaeHY2eW1VL2F1cEJtcVgyMnRRR1pYL3JoMVVZWmZCT0t3b0paYmJCd2xUUlo4WlIrWkZiemhLa0V1cnJTdmpXMWMzZktKSHdpUFZCcG5jckNpa3NxSDhjcU4wVTI3bGFZclEyNnFpYVdkbTQyOTZQbkIxRmdtVkxnTTJsdWVXYlFuT1AxaFNHNjlzNmZKM2w5NSsxU0lBQ2pLY0VWYVowRUhqSUluS1BIeldKYkF5dHk2MXRyZkliZW1QZWRtUHkrSzUiLCJtYWMiOiI1YTdkYWU0YzVjMDQ5NmVmMDllMzA2NDVhNWRjNTNmNTc0MmRmZmI4YTQyZjA3YTU0OTFkYzlmNTZkMTAwYTI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1992601814 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjY1MmMyWVZIY1orU09lcEtOZ1FhV0E9PSIsInZhbHVlIjoielZDbGdaSEhzRm84UHYyNDFWU2hLbGM0dVVRbEswZGx6dnZSL2lDUk55MGUzV0J2VjlLR2UxY0tmemkyMEE0VGl5anBYK3k2OWlZMUVtNUQ2OFBjeXBiOHR6bnBJeUNzY0lVMkpZS0NibEs0SEd5RG5objhmRjJGQUh4bThqK3J4cXBIRXVCdFRkdm52MG5BS1Y4V0ZlYlV6K1RMOVBNRkdBV1lwZ2NxNmZNYXgzSHhiY3plTmpJU1hrVVk1V214NUdrdGFvZDIzRU8vcFVTT2haL09KR1h4NXRRYWxnZlRZblE2a2dPWjJ6VmF0dWZ5aFVVYWdMaHZLbWM0aE9UWk5NRWJCZHJFc2NjWmdsbVhWRmZjRklQQ0llV1AxSnAvTUEyRW40eHYzcFY4U1RHYUNqaVpteWxLN2FUWDRjdVdLTUdmUzZPdThENmVqdWEveWZidHBML3V2L2Vhcjh0WVRaRXphQkZZREZHK2xqMEpuWmE5VlJybVpFZ3d1TVI0bjAvQmxIRExqMHVLdVdDa0NNN2Rtd0FKRThDeEJSdXRDRmJ4R1VkVWhCOTk5Sk9nNGtIMjVNb2FWbW5ablZZdU1ibHl6Q0VZby9WeittZW5XK2QwVW8vcjlybFZ4R2lnTVZSenNaS2ZIRmpJb0JHeFFGRTZEK1JmdVo1TWsrajciLCJtYWMiOiI1NWIyY2QzMWIxOTY0OWVhN2RmMGYwYmRiMjhhODAxNmVlYzAxMTQxMTFjNDVjN2IyMzY5NzJhOGU3YWY5ZDMzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9OTThnNnZ5aGh0L2lIR0RJYVVXeVE9PSIsInZhbHVlIjoiaUU1MUVQL1lxNDhlcTNRSVptcEVFcVhaYVN2ci9SMUt0MWphdC9reUJKbCt1VVdDZllYM0MzQTJsVnQ5RXhTaDNzS0JCVUJ1SURyaVVKTkw4bzltdWhjVlA5RE5hd0JPMktOMzNsUHRSS0VPVkFxZXRZUk5wS2swV1BqcW1kbDNuOW9kSGErSCswa3llNXBlYmNpUGJXSTMrVERaZHM1elpGZ2hhay9HUkZXcnEyMTA3VzNGVGg5Y3RBdXFUa0tKVUF4RGJDTEJja1djZXBDSjl0MXFZQlNMbExsc2FySmd5SFBGVHB4a1dSMUJIUnJFdnZBMWdJWDZVeTFMWk1sVDkwTElUcXJnVEYwRGxsb0VBRUhaZ0I3L3JmQkw4cS9sQUNHZW8vdHlUQ0dzZ2gvSmVuU2Z6c0JvYzFqYXdieDVXM24rT1lmbVdBL3E5RkdFL1lkTXgrcjBkMnZRZnNrMENCdE5IRGRPWjIxdlk2czhacElMOVdoVExKVkliaHhRQklaVEtrajhMUllBaWhTYnFvNlRGbUkrVWIxRXRIbzUxSXVEcFkvVVY2SVlMdWMrZnZGc0pnTkVmT0dTL2FuWEpUNHc2RGc1ZlRxN0gveHVLbU84cWVaVzE1R0tYVU9mcTY0L0xtSS9CRlJBTERHZkJuZUxTbERBWk91REtqd3kiLCJtYWMiOiI2MzliYTBiYzg0MjQ5MzRhZWFmN2JjMzlkZWFmMDEwN2E3ZmUxNGU3MzE0ZjllMjA3ZDlhODdhNTAzYmEzYTRlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjY1MmMyWVZIY1orU09lcEtOZ1FhV0E9PSIsInZhbHVlIjoielZDbGdaSEhzRm84UHYyNDFWU2hLbGM0dVVRbEswZGx6dnZSL2lDUk55MGUzV0J2VjlLR2UxY0tmemkyMEE0VGl5anBYK3k2OWlZMUVtNUQ2OFBjeXBiOHR6bnBJeUNzY0lVMkpZS0NibEs0SEd5RG5objhmRjJGQUh4bThqK3J4cXBIRXVCdFRkdm52MG5BS1Y4V0ZlYlV6K1RMOVBNRkdBV1lwZ2NxNmZNYXgzSHhiY3plTmpJU1hrVVk1V214NUdrdGFvZDIzRU8vcFVTT2haL09KR1h4NXRRYWxnZlRZblE2a2dPWjJ6VmF0dWZ5aFVVYWdMaHZLbWM0aE9UWk5NRWJCZHJFc2NjWmdsbVhWRmZjRklQQ0llV1AxSnAvTUEyRW40eHYzcFY4U1RHYUNqaVpteWxLN2FUWDRjdVdLTUdmUzZPdThENmVqdWEveWZidHBML3V2L2Vhcjh0WVRaRXphQkZZREZHK2xqMEpuWmE5VlJybVpFZ3d1TVI0bjAvQmxIRExqMHVLdVdDa0NNN2Rtd0FKRThDeEJSdXRDRmJ4R1VkVWhCOTk5Sk9nNGtIMjVNb2FWbW5ablZZdU1ibHl6Q0VZby9WeittZW5XK2QwVW8vcjlybFZ4R2lnTVZSenNaS2ZIRmpJb0JHeFFGRTZEK1JmdVo1TWsrajciLCJtYWMiOiI1NWIyY2QzMWIxOTY0OWVhN2RmMGYwYmRiMjhhODAxNmVlYzAxMTQxMTFjNDVjN2IyMzY5NzJhOGU3YWY5ZDMzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9OTThnNnZ5aGh0L2lIR0RJYVVXeVE9PSIsInZhbHVlIjoiaUU1MUVQL1lxNDhlcTNRSVptcEVFcVhaYVN2ci9SMUt0MWphdC9reUJKbCt1VVdDZllYM0MzQTJsVnQ5RXhTaDNzS0JCVUJ1SURyaVVKTkw4bzltdWhjVlA5RE5hd0JPMktOMzNsUHRSS0VPVkFxZXRZUk5wS2swV1BqcW1kbDNuOW9kSGErSCswa3llNXBlYmNpUGJXSTMrVERaZHM1elpGZ2hhay9HUkZXcnEyMTA3VzNGVGg5Y3RBdXFUa0tKVUF4RGJDTEJja1djZXBDSjl0MXFZQlNMbExsc2FySmd5SFBGVHB4a1dSMUJIUnJFdnZBMWdJWDZVeTFMWk1sVDkwTElUcXJnVEYwRGxsb0VBRUhaZ0I3L3JmQkw4cS9sQUNHZW8vdHlUQ0dzZ2gvSmVuU2Z6c0JvYzFqYXdieDVXM24rT1lmbVdBL3E5RkdFL1lkTXgrcjBkMnZRZnNrMENCdE5IRGRPWjIxdlk2czhacElMOVdoVExKVkliaHhRQklaVEtrajhMUllBaWhTYnFvNlRGbUkrVWIxRXRIbzUxSXVEcFkvVVY2SVlMdWMrZnZGc0pnTkVmT0dTL2FuWEpUNHc2RGc1ZlRxN0gveHVLbU84cWVaVzE1R0tYVU9mcTY0L0xtSS9CRlJBTERHZkJuZUxTbERBWk91REtqd3kiLCJtYWMiOiI2MzliYTBiYzg0MjQ5MzRhZWFmN2JjMzlkZWFmMDEwN2E3ZmUxNGU3MzE0ZjllMjA3ZDlhODdhNTAzYmEzYTRlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992601814\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-759009481 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759009481\", {\"maxDepth\":0})</script>\n"}}