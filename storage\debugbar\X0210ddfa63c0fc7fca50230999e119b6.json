{"__meta": {"id": "X0210ddfa63c0fc7fca50230999e119b6", "datetime": "2025-06-28 16:01:23", "utime": **********.882448, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.342812, "end": **********.882464, "duration": 0.5396518707275391, "duration_str": "540ms", "measures": [{"label": "Booting", "start": **********.342812, "relative_start": 0, "end": **********.792426, "relative_end": **********.792426, "duration": 0.4496140480041504, "duration_str": "450ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.792436, "relative_start": 0.44962382316589355, "end": **********.882466, "relative_end": 2.1457672119140625e-06, "duration": 0.09003019332885742, "duration_str": "90.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45832048, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.030760000000000003, "accumulated_duration_str": "30.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8319478, "duration": 0.026940000000000002, "duration_str": "26.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.581}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8690982, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.581, "width_percent": 2.113}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦 المخ%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦 المخ%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦 المخ%", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦 المخ%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.872747, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 89.694, "width_percent": 10.306}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-931630186 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-931630186\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1217236324 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1217236324\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-648102615 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"51 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;&#128230; &#1575;&#1604;&#1605;&#1582;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648102615\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1502870254 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">119</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFSWW83NzR0WnliTGVoRXVraVRyaWc9PSIsInZhbHVlIjoiTVp1L2dnRDI4Y244dmp0SEszbmZrcUZ2MjAycEh3NENuWFVJU3F4RVo5MHg2ZThTbXg2SE13R1JZYlFvcEZmUzE5U3ozdHoyQlZQWk5kWU40MGdKMklqMUdyclhDNW44WG9naDd6NmxqY2lHd3hzNS9Wb1ZVcnMvR1JZa3lJa25pYUpkcFV6ZTkrVVRENnJYb1FhS2xNNDdpWEw0cTVUU2tIbzFjcUV2ZGo4dVRPNk1VVU01bjNHZDMweVFGU1dIVS9sVGdUenVVMUY5SFErQUFlUDJpS3BmZm1tanlmV0pZd0VKb2ZvUkkvRWdtcjRjcys2OWRIOG5hWUZzeGtmakZLUkQ1cVE2WUNQWE95clhBQ0VtWjR5bE8zdUpMWnIzbi8vUG9QQWFCNmloSzJmbzE4cDdsWVNmVnVIbTNkUTNKa0FQL0xGMlMvNFBKSEU4bWNMT05ScS9Jc2pJRllXd2VaRGFHUDJzeWpuZ0k3SHJjZk1HUnIxVDFJaDlHRjBMMzdGakJ3SUdyS3BIbVBTaVZsRmt0ampmOFF2bVRqbWdkMWNpclhNMnZpNElTMTJ0aGl3S2NnZVNWWFFtZXZYYlFNcE44RVZBWUp6VjJ4MUx2RHM5L3ZqZGd6U1dER2VWSytEamxIZHNadHppV0x4bmJxQXl4QUhLMGJ0ODY3cGMiLCJtYWMiOiJiNzkxZjU2NmQ0MmU2MWYwMzUxNDRmNTZiOWIyZGUxZTlhNmM5MGIzYTMyZTU3M2YyZTMyZjRjMjYwNzUzMTgxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhGY3RvS2RxQmIySTFRS2RTd3FXaHc9PSIsInZhbHVlIjoiVE0xK0VVcTRmNkcrZW9wWktOWVI4eFlDcHM3OU9HVGwyak9FeDIxQUMvZUVNdDRRRjI3T2QxN3R0cjFaVHdkaVdJMGJCbkhpMVp2WTNUZDZodmpKamQzTE91ZGFscDR6VExzUGFhOXhVOVlIYWNnRGVlS2g2cDkzRmx3RWJLRk9za1RYWitNOWVkRG5KK0V0MGRQVy9xMkxRNGpEM0VaaGd1T2dCV3JoSnA1a2NjdEJ5aXpUNEN0aVdtLzNMajEvRmkxNzU3RndDd29zUk1hOXd2Vmo4N3NQTjV3c2Y2eXlaM2hZMWRmOWJXNGlndE1NQ29HdmhXdS9XTE9OZkQrMjk5RW9IVWhYRXpZUUk4SVNiQm90ZUl0MVhwUFJtTTdOczlkdjFROU5FN1hMZ1dyVUZNN0xORzB5ZGxSU2RMcldnc0JWclJyQ3ZvdVcrVVZSdTFtMitvNDA1NmlLSzRrbXVFTEVqTG5HTDZhMlp6NjBVeEs5NHByOFUzaGZNZ2ZUamZPdEQ0UVJrTXJUUGlWUnd1bmxNYjdDK2tqYnFLNWxZaFlWNDNGZjNqcFkvQjVtNkhPZ3ZLN3hHZmljSkpSaUdvdWZKQ05NVUNrUWdxckxlTXRLVXFPZ2dIT2VjSUdCTkljbThrd0RWL1RTSkRTcXFHZlBMcVJNd0w5TU40RjMiLCJtYWMiOiJhMmViZjAzYWUzNjU3YTM3Nzc4MDMzOGYwZjNmMjRiODU4YzZjZTc3YTgyMGNmYWRjZDFhMWZiNjY0NDFiOTg5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502870254\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-493769519 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-493769519\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-22441428 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlQ5TVlrV3QyNmdSb1M1QTJxRXRMNnc9PSIsInZhbHVlIjoiWitkNDN3Sm5YdEd0Q2FhR2lzNE53djc5SmIzeXVodHg4emJHRVNDMW1VQ3M0Q2x2aWtycXlOSVVzdWhjOXBudVNrWDlmbEJTbzNyb3AyMWgyaGlCaEN5TGRybE1GTUNOT0QyTk53OFhuOURKblEwc2tzOXo2NWsvR2t2ZHR1SEZwVXF5cGZwK0hkUmNpWHdyTnd0U1p5QmhSa056clBaYmREV0pwbE8rYW9yR2FvL0hsbkZ5WHlIeVlJbW9xdHoreEZweUxBck1ZMDdjQWNCbTE1S0ZLNkRXQ3dlVVF5VXBLamJtcmMrSmhJNUE5WE1Ob2NIUm1OeHUyenc4VW5WNEFYZUtEdXNrZFl2ZVZQUkgzZmhuNjlScGRrYTRhVG5PWThxN0ZhMzdWQU0zK3h6a2hZS25RRnh0R1VaYU4xd3ovU3FxeDVWKzJHWGo2dVdEcUFPREJ6eG84L1g1bllhS0NaOTh2cysvQS90d1J3MzdibXhQUkUrZVVDOG4wejVUVVh0bnN6MkoxUHJUTG5mTWdnY3dQMW1LSkM0amI0eHlTVGVSK0tCYzRZMjl6TittSWowYW12MUx3b09zRmM2QUs1NURpaVBraXRNSnhJRXY4dC9WUHIxNCtKYjcySWNEZEoxdDBTUEptaFNLaDdOWmdNSDYvS2pQZVFmZTJkVHQiLCJtYWMiOiI2ZmI5NDA4ZmIxYzRkN2FmOTI0MmNjMTIwMjkxMWUyOWQwNzQ3M2U5N2IwYmMyMWUzMzU2M2M4YjhmMDM0OTA1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjdFMlRLS2kwS3pxWmN4cmVmQzlKY2c9PSIsInZhbHVlIjoiS2VCaTQzM3JoM2kyaFR6QlpIWUlMYVBrblRBbHh5NEQyUkdIdzZWS1hyRE1kdllhclh3YlNwZC91dkVaNDVDTFUzbjZha2VjV284K0tuajJ3b202am9BT1RNWlRpK1VSbnNGby96Z3NIQ0YrTGpRdXVld01idU5KY3NpRnBGUjliOHlqR0xoTmlZaGxOOHFaRXhEQUJGTkdkd0JZOHFyRmx3Q2RZc3FCY0NyQVNDOG5LcHFBQmRnVEdDdnNZYjllRHUrWjM4QjRLTk9xTjF1R3pxU2hmeEJOM25XWDlzOTJzNzZkT09YbEZvU0F1ZDRvNTBRZm9ZT0owM3AzVVJQUDYwN0NtMzlWK1krVDBJM1NIWlIyTWgyREFLRDF0bTdjZkRwU2psYjY3b1FTcDhoYmpHL1VXaE10MFRBaTJoZ2tBL1JOL1Z1UWZWb1JSckt0b2JaUHBvbjBaMmZuSy8yUGVnTUVKRDN6ZmFSZzl0QWc1RmlqS004MVBVS3BORWhNNm5ibG9lSVpqbklYUXhZZnN5aHNkbzhwdkZtSmFUL2pZbmc3NDNvRXI5VkkyOHRUQStyZ0VMUmVMZHJSNU9DWnluQlQyZGhSUnhsKzdKa1VJcmtVS09UeUxEc2c2Z1VrU09pcitQWVd4V3NpendoTm16RW1XdzMvZzlhMTJwWmYiLCJtYWMiOiI5MWU3MDU1YzVkYmE4MmQyMDI3NTdkYzM5YmE2NmViNWQ4MDQxODczN2FiZWQ0YTk1ZWQyZTQwZjczZDQ1ZTgzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlQ5TVlrV3QyNmdSb1M1QTJxRXRMNnc9PSIsInZhbHVlIjoiWitkNDN3Sm5YdEd0Q2FhR2lzNE53djc5SmIzeXVodHg4emJHRVNDMW1VQ3M0Q2x2aWtycXlOSVVzdWhjOXBudVNrWDlmbEJTbzNyb3AyMWgyaGlCaEN5TGRybE1GTUNOT0QyTk53OFhuOURKblEwc2tzOXo2NWsvR2t2ZHR1SEZwVXF5cGZwK0hkUmNpWHdyTnd0U1p5QmhSa056clBaYmREV0pwbE8rYW9yR2FvL0hsbkZ5WHlIeVlJbW9xdHoreEZweUxBck1ZMDdjQWNCbTE1S0ZLNkRXQ3dlVVF5VXBLamJtcmMrSmhJNUE5WE1Ob2NIUm1OeHUyenc4VW5WNEFYZUtEdXNrZFl2ZVZQUkgzZmhuNjlScGRrYTRhVG5PWThxN0ZhMzdWQU0zK3h6a2hZS25RRnh0R1VaYU4xd3ovU3FxeDVWKzJHWGo2dVdEcUFPREJ6eG84L1g1bllhS0NaOTh2cysvQS90d1J3MzdibXhQUkUrZVVDOG4wejVUVVh0bnN6MkoxUHJUTG5mTWdnY3dQMW1LSkM0amI0eHlTVGVSK0tCYzRZMjl6TittSWowYW12MUx3b09zRmM2QUs1NURpaVBraXRNSnhJRXY4dC9WUHIxNCtKYjcySWNEZEoxdDBTUEptaFNLaDdOWmdNSDYvS2pQZVFmZTJkVHQiLCJtYWMiOiI2ZmI5NDA4ZmIxYzRkN2FmOTI0MmNjMTIwMjkxMWUyOWQwNzQ3M2U5N2IwYmMyMWUzMzU2M2M4YjhmMDM0OTA1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjdFMlRLS2kwS3pxWmN4cmVmQzlKY2c9PSIsInZhbHVlIjoiS2VCaTQzM3JoM2kyaFR6QlpIWUlMYVBrblRBbHh5NEQyUkdIdzZWS1hyRE1kdllhclh3YlNwZC91dkVaNDVDTFUzbjZha2VjV284K0tuajJ3b202am9BT1RNWlRpK1VSbnNGby96Z3NIQ0YrTGpRdXVld01idU5KY3NpRnBGUjliOHlqR0xoTmlZaGxOOHFaRXhEQUJGTkdkd0JZOHFyRmx3Q2RZc3FCY0NyQVNDOG5LcHFBQmRnVEdDdnNZYjllRHUrWjM4QjRLTk9xTjF1R3pxU2hmeEJOM25XWDlzOTJzNzZkT09YbEZvU0F1ZDRvNTBRZm9ZT0owM3AzVVJQUDYwN0NtMzlWK1krVDBJM1NIWlIyTWgyREFLRDF0bTdjZkRwU2psYjY3b1FTcDhoYmpHL1VXaE10MFRBaTJoZ2tBL1JOL1Z1UWZWb1JSckt0b2JaUHBvbjBaMmZuSy8yUGVnTUVKRDN6ZmFSZzl0QWc1RmlqS004MVBVS3BORWhNNm5ibG9lSVpqbklYUXhZZnN5aHNkbzhwdkZtSmFUL2pZbmc3NDNvRXI5VkkyOHRUQStyZ0VMUmVMZHJSNU9DWnluQlQyZGhSUnhsKzdKa1VJcmtVS09UeUxEc2c2Z1VrU09pcitQWVd4V3NpendoTm16RW1XdzMvZzlhMTJwWmYiLCJtYWMiOiI5MWU3MDU1YzVkYmE4MmQyMDI3NTdkYzM5YmE2NmViNWQ4MDQxODczN2FiZWQ0YTk1ZWQyZTQwZjczZDQ1ZTgzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22441428\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-627720425 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627720425\", {\"maxDepth\":0})</script>\n"}}