{"__meta": {"id": "X2911f9b6f1a28398e2d587986ea45227", "datetime": "2025-06-28 15:19:44", "utime": **********.489274, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.088907, "end": **********.489292, "duration": 0.40038490295410156, "duration_str": "400ms", "measures": [{"label": "Booting", "start": **********.088907, "relative_start": 0, "end": **********.4294, "relative_end": **********.4294, "duration": 0.34049296379089355, "duration_str": "340ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.429409, "relative_start": 0.3405020236968994, "end": **********.489293, "relative_end": 1.1920928955078125e-06, "duration": 0.059884071350097656, "duration_str": "59.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45616096, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00289, "accumulated_duration_str": "2.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4574041, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.706}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4680529, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.706, "width_percent": 18.685}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4737391, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.391, "width_percent": 16.609}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1204458938 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1204458938\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-813903753 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-813903753\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-744651061 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-744651061\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1803607344 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123960877%7C11%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inh1dVdHcGc1WGNjc3ZsQXN2OVlkaEE9PSIsInZhbHVlIjoicG5zekk1NEI2L2Q2SThTZldmZU5MNUpXWG5TYU9SSzMwVDBuUmY5Wk1LYTdxWHp4VTByTmNwL1NDSnVjV3VzNjMxcU9ndTY4blVYYmx2OTBpTEFVanRrQU9kRWE2NHMwQytpT2l3VTNNUUxLZng2Zlhoa2JJdG5wVzlQa05xTkJ4MG5kWHp1b3F5dVRKZDVPcWdBeGdyRHhRVDh3V2lQc3BMdWxab0xYbmFpeXFoNUNYbmg2RnJmb3ovNWRuMjhUTGFlZFA1VW82THZ6dmRjN045RGE2My85QXBOVkJENlNHL1Z5aWlMRDQ0aVpLUmFGUVN2cEcwbm5pcUlnUVNDQWljQkdOZm5HOUFNTWFNWnFMZ3ZZVS9LLzZWSml0dlRSdC93NndWbXZESy9UYVlIT3NqVWhsR1BQWDQ3T2hIZ0Vram00VGVYcDNLNlhjWXBCaEVJTGZUQ3JEWUVYTXVJTEg1ZGZueGVrMnJHOUp3eDlKZ2xhZDZQWmJJZGhrVFNSdHJSR0tnL1E0TFM2RCt3YWg0Sllja202RjF4aGVKMGZ3dVV4YjF0WDVrb1BPWFpSK04rbmtxZFRnRU5wZzUyZzI4QWpZMDZOWlFmZ2RqVFRpdWlENzl2elN2NTRsMGtLOFRvTGVjUDJuU2VoSDg2OGdQVGZCV0J2YUJyRWIrZEYiLCJtYWMiOiJmYWY0ZWUzMDNkMjFiMTk0NmMzMmYwOGNlMDg1MWViZTE4ODJjNWY1NTFiZGEzNDMwYjAxNjgxNDA3NTc5M2E5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjUxV2ViRnRIYWhmTHIxcEtjam53Qnc9PSIsInZhbHVlIjoiME1MRmx1VEJvL0dUMzZzVnMyTXhvTkt5Z3lnOXhQelFtbmV5UUFER1l6NjhQcEhmQXNMbmZNY0Z1VjhmNnByM0E2dThsSVVnOGtGMnhJc3Y0YzBWb0lyRWZJZ0pLN2xydXNyY2RWN2V2VTJuNFkra0tqMU53RHhLeVg2TVFYT2hyWGZaeEY5VDI5cTBEdU5BUkowNXNkcEwvUUNTM3dpd3RDa05NazUrOVEwN052UVRKK2tTSkhuWjQ0dmNsUWtMc1N4alg5NC9KVmFmMUw2bUdQai9zMDZQQkUyNlMvaGMvaDU4MWJMQ2EyU2RCTUtRemI2dWp1dnFLV3ZLQ210YW5yVlZmWEcwWWZNeE90M0tPV1YyaG1oZGFCdVJ3azBqTXMzcUhxQlNaNXF3MkhOLzk1NVZnU2cxZVp0cGMyRlE3TVNsMUxLV2VGc2dVS0JnbWd2YVNhWWVXaitzYzVTUkdLdGhHbzhTNkFOR3NKUmxEeHNBaG91T043cVNiaEpIMHpVcmo5bW5OK2paL3ZSSWs5TEQ0N2tybmE2dEpzRGZhd3BRUEF4UlJZQkpoa0Q3bUFSMWJ3SmY1eE9FaHVPMDZ6KzVNUjBpZXpLRmhlK1E4NXQrOXFzdUdiSG9IQmMyS2xnS3BBOFdUWTFNZVBQUUduVVhhZnZzR0pQSi8wYWwiLCJtYWMiOiIwODRjNGY4NTcxMTMxMzg3ZThiY2JjZWViOGQ3ZDZiMDg4MWFjN2RiNGYzZDQ1ZWIwZjI0NGM0MzdkNmUzNGE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1803607344\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1954541527 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954541527\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-125108176 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:19:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im53TTVhYXppekhkdFR2eGdkN0Mzemc9PSIsInZhbHVlIjoiRms5c0NIQXRjU3diYWdMRVRJY2RVVTBxNEc1NkZPeXluZ0hDZ3Njcyt6SjFpUldnYzdSa1NUak5mUitFYm83MTdnaExkQ29Sc0w1MEVtRGhENmlkTklYZDR2UVJhVDlHWldaU2FrQmRsZC9VWHRrYkNhQVVXYi9NRlhlU25Sdytyb2hBYzlCbmRwSWFXaE1rcFJkMW9LeEF0dDZSTXQ3cUVvS1MzbGJJVkRRSlpCWnhNZU9iQ0ExVGhCc2NlWHpxRzJicVpSa0ZxOG8yaUIzcjVpQTBGaFY0Rk4zTlhrZUpqRFZYcUdIckQ4MDd5V3dkVGFUc0pXOWVONDcvZmg2OVJ2NDllamVNd21CZFYxT21SNnJXbzkyZmdtdVhDQzJlYzhab1VSREtkbjNIcW0zcDZzYVhmLzB5elFTcHhsUHRjY0hlQTVSUHZLeXR4ZFBsaDM3R0JwWk9qQ0h0ZEZYME9EN2JWaGpYWnJHQWdwZ0xuMC9OS2RrNmF3TTlIUERsaDBpcFBKb1dLMTUwU2xlT0RpTmU3ZmJtMk5XTlNSeW1JUWZpbE5XQklaY2k3YVZrS09ITGJzSE92akhUK3JaazdLczBGdURBeXVGM3BpKzRzRzZJTVVxYTZucGVybFBxWi94dW0rZGxtQ29OMDdjUFlZYjUyelcrZTdiTVNpU1MiLCJtYWMiOiI1YWQwN2ViZWI4MmQxMDc5MDc3Nzg1Nzg4MWRkMTYyNzAxOTY3ZDhlNmIyMWIyMDE3ODFkOGI3YTlmYWVlMmY2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFIUG9KM2dqOU92UmtXWjB3UGgzc0E9PSIsInZhbHVlIjoibWREdmY0MS8wNmQxWEtsQmQxTytZK0tOZ0sya2hWNXVrb3gvdFJrR0luRnA3TGtzNnFIb3JsM256WFVkS2UrTzhpdHRveGR0MVd1WENrczVpczEwMFZrNFpoTXhwbFpQRXovWGQ1KzBKaU04eEFvV2R6bnd0RlZ0Y29Cb3BpN3JkVDF3MEc4TEhiNGtzUDlYZVZ2Ty9ray9JdDV3ajBwNzhkbTB3dWNhKytxNnNYRkM5bFBXcGVxcFBPZllyNkttTmJRZS9SbGxNZkd5UlY2dnZOYjFBVjgyZzcxUU14TmFQaGdkTGluVHEvMGhzb2VJYUZnS01rdllLc2RhTGMvOStrNUtSSWFlYnpNMTdwZS8vaFZtN2ppckd5eWhmOXBsaDBVSUl0ZERIeHRNTVdjZmxRdStGeWIzZkhRKzNGTHZ5NWM3LzVuQXB2NnBTS2NZMEV6TFdBTE9xc1JhcUJqejlNY2lDTlBBZWM1T0xTL20yYVNDTGJLTTlwVXpRcUc5RG1hUGViTE9EbXlRNkFJMXZTVFhnNmVSSzUveUY0T1ZQSmcwUllpbjQ0QlgzdmpKeWhGWkRrSCtnZFF0cXpXbDNNT2gvSTZZRVEvdUZCYThDTy9sOHZPNlJjU2taU2tJaTZGaUdrTUtQVzZldW8vVElVbTUwMUl3cVRyMmxLdlQiLCJtYWMiOiJkZDQxN2E3NjRlMzVjMzJkMTBiN2EwMWQ1NTM5N2VmNGExOWZhNTEyOThmODE2NWU2YjQ3NTBhYjNkYjc5MTQwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im53TTVhYXppekhkdFR2eGdkN0Mzemc9PSIsInZhbHVlIjoiRms5c0NIQXRjU3diYWdMRVRJY2RVVTBxNEc1NkZPeXluZ0hDZ3Njcyt6SjFpUldnYzdSa1NUak5mUitFYm83MTdnaExkQ29Sc0w1MEVtRGhENmlkTklYZDR2UVJhVDlHWldaU2FrQmRsZC9VWHRrYkNhQVVXYi9NRlhlU25Sdytyb2hBYzlCbmRwSWFXaE1rcFJkMW9LeEF0dDZSTXQ3cUVvS1MzbGJJVkRRSlpCWnhNZU9iQ0ExVGhCc2NlWHpxRzJicVpSa0ZxOG8yaUIzcjVpQTBGaFY0Rk4zTlhrZUpqRFZYcUdIckQ4MDd5V3dkVGFUc0pXOWVONDcvZmg2OVJ2NDllamVNd21CZFYxT21SNnJXbzkyZmdtdVhDQzJlYzhab1VSREtkbjNIcW0zcDZzYVhmLzB5elFTcHhsUHRjY0hlQTVSUHZLeXR4ZFBsaDM3R0JwWk9qQ0h0ZEZYME9EN2JWaGpYWnJHQWdwZ0xuMC9OS2RrNmF3TTlIUERsaDBpcFBKb1dLMTUwU2xlT0RpTmU3ZmJtMk5XTlNSeW1JUWZpbE5XQklaY2k3YVZrS09ITGJzSE92akhUK3JaazdLczBGdURBeXVGM3BpKzRzRzZJTVVxYTZucGVybFBxWi94dW0rZGxtQ29OMDdjUFlZYjUyelcrZTdiTVNpU1MiLCJtYWMiOiI1YWQwN2ViZWI4MmQxMDc5MDc3Nzg1Nzg4MWRkMTYyNzAxOTY3ZDhlNmIyMWIyMDE3ODFkOGI3YTlmYWVlMmY2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFIUG9KM2dqOU92UmtXWjB3UGgzc0E9PSIsInZhbHVlIjoibWREdmY0MS8wNmQxWEtsQmQxTytZK0tOZ0sya2hWNXVrb3gvdFJrR0luRnA3TGtzNnFIb3JsM256WFVkS2UrTzhpdHRveGR0MVd1WENrczVpczEwMFZrNFpoTXhwbFpQRXovWGQ1KzBKaU04eEFvV2R6bnd0RlZ0Y29Cb3BpN3JkVDF3MEc4TEhiNGtzUDlYZVZ2Ty9ray9JdDV3ajBwNzhkbTB3dWNhKytxNnNYRkM5bFBXcGVxcFBPZllyNkttTmJRZS9SbGxNZkd5UlY2dnZOYjFBVjgyZzcxUU14TmFQaGdkTGluVHEvMGhzb2VJYUZnS01rdllLc2RhTGMvOStrNUtSSWFlYnpNMTdwZS8vaFZtN2ppckd5eWhmOXBsaDBVSUl0ZERIeHRNTVdjZmxRdStGeWIzZkhRKzNGTHZ5NWM3LzVuQXB2NnBTS2NZMEV6TFdBTE9xc1JhcUJqejlNY2lDTlBBZWM1T0xTL20yYVNDTGJLTTlwVXpRcUc5RG1hUGViTE9EbXlRNkFJMXZTVFhnNmVSSzUveUY0T1ZQSmcwUllpbjQ0QlgzdmpKeWhGWkRrSCtnZFF0cXpXbDNNT2gvSTZZRVEvdUZCYThDTy9sOHZPNlJjU2taU2tJaTZGaUdrTUtQVzZldW8vVElVbTUwMUl3cVRyMmxLdlQiLCJtYWMiOiJkZDQxN2E3NjRlMzVjMzJkMTBiN2EwMWQ1NTM5N2VmNGExOWZhNTEyOThmODE2NWU2YjQ3NTBhYjNkYjc5MTQwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125108176\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-600540325 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-600540325\", {\"maxDepth\":0})</script>\n"}}