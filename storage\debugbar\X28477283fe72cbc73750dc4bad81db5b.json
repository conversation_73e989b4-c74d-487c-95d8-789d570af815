{"__meta": {"id": "X28477283fe72cbc73750dc4bad81db5b", "datetime": "2025-06-28 16:02:00", "utime": **********.469231, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.007115, "end": **********.469256, "duration": 0.4621410369873047, "duration_str": "462ms", "measures": [{"label": "Booting", "start": **********.007115, "relative_start": 0, "end": **********.410586, "relative_end": **********.410586, "duration": 0.4034712314605713, "duration_str": "403ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.410594, "relative_start": 0.40347909927368164, "end": **********.469259, "relative_end": 3.0994415283203125e-06, "duration": 0.05866503715515137, "duration_str": "58.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45711592, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025499999999999997, "accumulated_duration_str": "2.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.440133, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.843}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.449725, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.843, "width_percent": 14.902}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.456026, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.745, "width_percent": 17.255}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1205286213 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205286213\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-8433378 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBvL2EwczVhSW5vTFRLL290ank2aFE9PSIsInZhbHVlIjoiNU5DNjIwNER3Zy9TalZjT3ZFVDZLdWh1U1dYY25WMlNqVWh6cUc1ZG82VWlsaW5YTmlsUUtObis5WUFocDE3NjlqNlRvcnl0NkZqSU5VWXZuazdrQitCUmlUU015YWxBcEg0dEhBN2xEYy85WFdsaWk1eGM5bjZFZXZGZFdNL1pSQWxSSGtBUWlyMm4zSlprUUpSakJhZFhQL2lMZXk1YUhiSmxyVXUrYTk1ZVgyenl1V3pHWEE3QVhIQzIzMERiT0MrbTMrYW5idFg0TkxWelVnZ0Q4RExkT1VONU53TlFCRTYyZEhkaXdzakhHNGdrZjQvRG5YZlNOV0pHZXRnSVN1cDhtVmMwd0pzZjBJazNkYW4vOGwyeHNSSGlhTFplblp5S1V0Wmg4VDVtS2ZzVFIwMWhXa0liTHpwOVdGTnFIVlZjazRCMUlVSTU4YmxyUVF0UDVDV3RvOE1NOGVDU0UvcVE2WDJ4c2lUN2tXT0RrU0dQNnFCZmIyTEVLM29YMDRZSnlVTlhDWFdwTkdwd1FTclFZbXJia3FiTmwxWkh0bWY3bjdlNlYvK2pnV0kyV1Z6NFFUY0FmZzNtSXUrREpOUUdHQTdMQ3RIQVgrT2VoVnUwcmNTRGFMZmh6Q3B2ekc3OHB6YTNoWHVnYWtkUkEyekp2blNCd1J5eFZBdlgiLCJtYWMiOiJmMjZjZmExNzA3MmE3YzQ0ODkwMDM4MmEzNzU4MjhmOWY1MTZmNjJkNDIwN2ViNzJiZWJmYWIzMmQ2MThmMzdmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlvQnlRaFNXdkdDcW1qVFBGWmpKZGc9PSIsInZhbHVlIjoiMkZsOGU5SkN5bUFLcG0vSk1qK3N0RitSUXdzQThtK1FkSllhR0pXVjE1WkN4cXQyR1UyTDhVd21Va01zNkhzVy9UWGV4UHQ2SjVkLy9lRUl0NlhDWjU2VnVIeXRRb0d6S0tmZXhybnZ3aXJsZTl2bWN5ZGQvN3pDenlIVWlRT29jSGt3Qm9icDVPcmZxVk5zTzJOTk1UdDFvN3ZyMThaN2lZM2hnYjBpbGFZSkU1Q0NWa2pINEtTMzdONlFjM1JzWmZQRVZoNmxOUHpXaFBnVm1nRGFGaFlNVWhRSG9uenFHbWc4cVJWc3htRlNaaGdtbFVHMWtkVUM5S2R0Q1lZRGFMbTlTUXVDZ05jQytPNDhiRUZRbS9mcEFFNHpIdDVvUlllY252cEhlRzI5Zlg2ZzBDTDVCdzB0bkJJbFRzT2t1c3RiSFBVN3prVFlwYUczSWp1d1pPZ1N3aGcwNitpdGdsM2tWaVJhbG9wZjN3anVkWXQzVFZWbG5QQnlWbjRueUltRjR1ZEZwTk13K0I4cmNCT1kxc2ZHQXB3akp0dUR3UDJuenlvYW80Z3F6WFJmcFd5ZCtBb3NBUkRwTUlqSVMyVTRZa0paQkl2SzY1QVFtTmY4L3Ftc3hPUlV4akRCcjZ5M2RCSVZKWHdUaHZ3dkFGZVdkVmhRUVd6OFZ2ZmUiLCJtYWMiOiIzYzk1NDg1ZmE3NjkwNjEzODc1NjFhYmIzNGJlMGQ5NWNiOTEzYjQ5NzY1ZjExMGMxYzQxMWQ2ZTBjZjRhOGM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8433378\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-431258986 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-431258986\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-930637062 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik80ZUlieTZBR3pqN20weXhBMTAya0E9PSIsInZhbHVlIjoiclp4ZG9IYlhNZ3dxMGZOMXFBdHlUTzlsL2JHRk5pV2JDT3JnaG5wbkNBNUgvNGRPLzlLOE9JMWE0T2tkaGtZU1YrVmZaU0JXZStvdEFFSTJNdXRPQUZTS29HbnZaeVQrSmw5Tk5NRytjbEZDa1hjQ0c4Ni9MbkxhMmRGQ3Fmamk4d0JKdEkvUkhNWWowTE1mT2NZRitDKzdKOHVNOW1tWHhSVU1PYk1BVjdMTUV1UlRYTmVnMDhUN0c5cGNxSDlHK0paOHMvTWZwcTRyRFM1d3YwNlJUK1JaVTd4U1BFZW1WVkJYL2lJRUZDaUd1NS9KUCtaYmRFNXZwdTZzMHZmeE1jb09rU1lSbTJyeDI2NzFIM2JDdDVESnBNZ1l3MHd1eGYvU0NSSTF3bXB2bjVwQlRLWTRJNEpnSjRuYjJCb2FhUWhLZkR0MkVaVnUwTWRocXBQVTNjL1J3eE5wTk5TdWI0U0FIQUNsVDQ0OHVFNmRsV0VUdGRzSE5lbjZONWUzQXA4N1lzREJTQXVWdjBvWHdpeDlZY3RCaGpRSmtrOUNVUU5aZk5FcDJDWlRiSnNNWXBnOUlRRWNRaWpVUTNBc040YzlDeEZKYTdwZ0QxdHdUTmlXNzV4bUY5NUR2dlh1ZUpHQVY3UUZTMEpCU1RhZGJmU3VXVEhVVmtXaXI0L1EiLCJtYWMiOiJjZTEzMzBmYTdlYzBhNzQ5MDk2NGNlZmMxNDk3MTVjYjQ3ZDk2MzcyN2E5YjY0OWNkMWJkZGQyZjc0YTMyNGU1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJZWUorWGx5YVAra0hDVEhuZXBXVEE9PSIsInZhbHVlIjoiVUdQc21pV09vREQrVGYzM1BNdXM1VjBjN0xveUhYZFBYOXR3cVcwejFvSFlob21wZWJJOHFRRkxiK2Fla1RCTTdoclNmRVN0Sm1LRmRNRGZHeHdUbFdvSXRSWkRwQ0J1eDJGRTVrY2tQVUx0TFBGTmMxcWhTdThEaWg4dGVqYWdaZHcxTDg5bjY3S3BWOWFENER5empaWHUxemlNK2ZEZFZYUjNJbXdycWJwenYyNUlKMmkrUVdVOWJDTVhtMDd0SzlTV3ptL29XWFhENkRuY2VRUjRZS1p2dEZ1dGdXMGhTSU5jSFE1OWZWSzZQN1FiNXZnYnBuZkJ6SDdERVdEQWZqRWxNb0hnMTJ1UUtsa2xaK3p3TUt5eW52VWJlK2JjdEtzby9oNUxPSE0yRTBUelBON3JJQXhnOFpyL1RnaThKVHRoSXM3T3RVbHMvY3l6SWxMaCtyWmQ1ajM2OTJ1ellLOGRKY3owZjh5aXVCem1Ka3VWQVBOMGt1V3hJNDl6ZXpSejY5eFVXMHlQeGpBUUVBazFpTUlDYlRWUGIwTWtwYzF3U21nb09yQUtRbVRVcG9rUHA4cUFSUVNUQkJTaFVkdkcxNDZBSjQwN0lxYTZuL3IrRjFsR09ySml2VzkwMHI2Zk1vNHZkSDNDR1hwODZqeHdGejQvUWpHdjdKbkgiLCJtYWMiOiIxNjJjOTNkZDEwNGY2YmY0Zjc3ODI2ZTYwOGYwOTQ2NWI4NDk0ZTBmMDg1ZGUxZmViNWE2OGZkM2FiZGZhYTVlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik80ZUlieTZBR3pqN20weXhBMTAya0E9PSIsInZhbHVlIjoiclp4ZG9IYlhNZ3dxMGZOMXFBdHlUTzlsL2JHRk5pV2JDT3JnaG5wbkNBNUgvNGRPLzlLOE9JMWE0T2tkaGtZU1YrVmZaU0JXZStvdEFFSTJNdXRPQUZTS29HbnZaeVQrSmw5Tk5NRytjbEZDa1hjQ0c4Ni9MbkxhMmRGQ3Fmamk4d0JKdEkvUkhNWWowTE1mT2NZRitDKzdKOHVNOW1tWHhSVU1PYk1BVjdMTUV1UlRYTmVnMDhUN0c5cGNxSDlHK0paOHMvTWZwcTRyRFM1d3YwNlJUK1JaVTd4U1BFZW1WVkJYL2lJRUZDaUd1NS9KUCtaYmRFNXZwdTZzMHZmeE1jb09rU1lSbTJyeDI2NzFIM2JDdDVESnBNZ1l3MHd1eGYvU0NSSTF3bXB2bjVwQlRLWTRJNEpnSjRuYjJCb2FhUWhLZkR0MkVaVnUwTWRocXBQVTNjL1J3eE5wTk5TdWI0U0FIQUNsVDQ0OHVFNmRsV0VUdGRzSE5lbjZONWUzQXA4N1lzREJTQXVWdjBvWHdpeDlZY3RCaGpRSmtrOUNVUU5aZk5FcDJDWlRiSnNNWXBnOUlRRWNRaWpVUTNBc040YzlDeEZKYTdwZ0QxdHdUTmlXNzV4bUY5NUR2dlh1ZUpHQVY3UUZTMEpCU1RhZGJmU3VXVEhVVmtXaXI0L1EiLCJtYWMiOiJjZTEzMzBmYTdlYzBhNzQ5MDk2NGNlZmMxNDk3MTVjYjQ3ZDk2MzcyN2E5YjY0OWNkMWJkZGQyZjc0YTMyNGU1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJZWUorWGx5YVAra0hDVEhuZXBXVEE9PSIsInZhbHVlIjoiVUdQc21pV09vREQrVGYzM1BNdXM1VjBjN0xveUhYZFBYOXR3cVcwejFvSFlob21wZWJJOHFRRkxiK2Fla1RCTTdoclNmRVN0Sm1LRmRNRGZHeHdUbFdvSXRSWkRwQ0J1eDJGRTVrY2tQVUx0TFBGTmMxcWhTdThEaWg4dGVqYWdaZHcxTDg5bjY3S3BWOWFENER5empaWHUxemlNK2ZEZFZYUjNJbXdycWJwenYyNUlKMmkrUVdVOWJDTVhtMDd0SzlTV3ptL29XWFhENkRuY2VRUjRZS1p2dEZ1dGdXMGhTSU5jSFE1OWZWSzZQN1FiNXZnYnBuZkJ6SDdERVdEQWZqRWxNb0hnMTJ1UUtsa2xaK3p3TUt5eW52VWJlK2JjdEtzby9oNUxPSE0yRTBUelBON3JJQXhnOFpyL1RnaThKVHRoSXM3T3RVbHMvY3l6SWxMaCtyWmQ1ajM2OTJ1ellLOGRKY3owZjh5aXVCem1Ka3VWQVBOMGt1V3hJNDl6ZXpSejY5eFVXMHlQeGpBUUVBazFpTUlDYlRWUGIwTWtwYzF3U21nb09yQUtRbVRVcG9rUHA4cUFSUVNUQkJTaFVkdkcxNDZBSjQwN0lxYTZuL3IrRjFsR09ySml2VzkwMHI2Zk1vNHZkSDNDR1hwODZqeHdGejQvUWpHdjdKbkgiLCJtYWMiOiIxNjJjOTNkZDEwNGY2YmY0Zjc3ODI2ZTYwOGYwOTQ2NWI4NDk0ZTBmMDg1ZGUxZmViNWE2OGZkM2FiZGZhYTVlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930637062\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1609666058 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609666058\", {\"maxDepth\":0})</script>\n"}}