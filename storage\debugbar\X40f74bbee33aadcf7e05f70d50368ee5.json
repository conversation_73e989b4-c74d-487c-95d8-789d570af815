{"__meta": {"id": "X40f74bbee33aadcf7e05f70d50368ee5", "datetime": "2025-06-28 14:59:01", "utime": **********.916332, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.547652, "end": **********.916346, "duration": 0.3686940670013428, "duration_str": "369ms", "measures": [{"label": "Booting", "start": **********.547652, "relative_start": 0, "end": **********.880887, "relative_end": **********.880887, "duration": 0.3332350254058838, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.880896, "relative_start": 0.33324408531188965, "end": **********.916347, "relative_end": 9.5367431640625e-07, "duration": 0.03545093536376953, "duration_str": "35.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43616824, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00196, "accumulated_duration_str": "1.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.907057, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-196600980 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-196600980\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1871297501 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1871297501\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1940415220 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1940415220\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2061216250 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122734939%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ims4Z3R4VkN0ZTBvaFVSVlpLUTB3Z1E9PSIsInZhbHVlIjoibTFYTC9BWjJCcGZzV2lld1VhWWlORlNxNUxpUjRNeGtMQVN1eld6cG8rMldGTEd3cEVSRFgyQkY2ZkVsMVVLVVNUTXhGcXhGTVJhOHVPbmp5RHZSZVNIZ2JLWm1XY0dCeCtBdGxhRjFLRmxZeDVnQTc0WmJJYXQ1Skh1b0o0WXRpWFJBcHMvcWJOMjUvWlM0ZHJEZE5pUDRFOW1qNHR4MnY1VEVJUnRxelllK2Q5ZlcyZUQ4bmhnQVQ2YmhhZnorV0dTYzlrQ0l3elkrTnIyRWxGZGhZZmJWWWhRZ1NwMFpKU3BFNEtlMHFaV0RtZXlaUXZrd25xblpydzFTVmljYmE2U1cyaDYxbTVwTEdSK0pVNnJtMllRQmZUZ2ZiQmwrN2pOeHRXVEVhU09GV2k5ZmhTYnRsZ3Nxci8yWDlzMExjQStnUDV0UkEwY29aNDJNUDhHblE5MFprMnFMUmZ1OWVHZ3R1M0p6aW4xYlJ3NG9lNWs0NWQ1QlVtZlVEQ2RpNTN2alhOOVdLdkdvQXJkaFEyTWRCRE9QbUhBeFZ6RGpzVjlWUkd3YmZKL3pqck96M0NHWmYwQm1zWmpZbzFrcTBMM2tmWEkxOHNVWWQvR1pvUVZGWHJPVHdrMUVYUEFFcEVYRndsV091Mko0VVlCeFJxZlgzYXM2ZWNpdWhmNTQiLCJtYWMiOiI5YjM5YjIyYmEzNjU1YjJjYzQ1NTdjOTA5OWU0ZmU0MzllN2I0MTQyOTdkZmQ5MzNkNzNjM2IzYWI0NWJhMDIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlkzZ2IxZHdGQUpMMGhVWFpFTldLNXc9PSIsInZhbHVlIjoicHRXREZTQk5tWEM3VkVEMjhsYjRtaGc1WmRYbHljRHpHN1RnemtjRG4wZ1NNTU9JTnlxTDRrQVBaMGd3cTV6VjV0a3lhMU90SWpOakU2bENQMWhPS25zMkdiRFYxOENaR0Z0ejEzQXMwOWw0QTA1TnR3YkZNNDBqK2NOTm51ZERWU3dDL0tGelVEQ0I2WnRJVUZIMmI1U2hHaVQwZnorRHB6ekN5NVdaSGhJM3RlM3I3U3RLTCtjeklyYVFsSXZSSndKN1B3WWR6dDJDKzVpQW56UnVGYkJhaWtxaW1uMDJCb2dTSEF1QWJyKzQvSDBpL1pHampVK05xcjcxSmIwOC8vZzl4UEh4QXB3c1JaWExsdmx6STRtRVNOdVR4QjBKUnJGYmlpbXFybDBiaXQ0emg0eHJVT3BRdUxiNTNPY3dpT1RqdWpJOVRYMTRpNUdTU0s0emFzTkpqMEQxVXA4UnA2WU9QUXY0dFlMSmwrWnZFRnVJaXlxcGhjRzdxeGJwRm91N3puR2pCd0QrMWhiVXY3QmdVVkk0MEgrSUpqUmltOURkZVdndTgxSGdDT2FTZXVVWEJPc1VPZkVMcE1CTnJhVHpoMEFvblR3R0ZGaHNud3labnh5dXpBck9uUWQ0YzZMWnFXVXUwWU9VbkFzMlNKTFVhc05iNU0zL2VpU1ciLCJtYWMiOiIwZDE1N2IzMTBlMzQ0NmM0ODE3MDc2ZmNjMGI0YmY5YzBkNjcxZDllNWNmMzg4ZDNmNGZiNWIyMWZlZjA5MjIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2061216250\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-857968410 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6nyGbgT6n8t00JbJ5CSgj7vkpXnxnyISs5XS6j6J</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-857968410\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1427732200 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhvaTMwSkJTczFRbEJpMW1EdzlNWEE9PSIsInZhbHVlIjoiSlVOZEt5Z094czNtWDRmeFZqd3ZreU9NWHlKUmRBcHE0RmRNb0huY2IvSldtQitYSnpTeUwyRnF0Mm5qakEySXVQSEhuVExsRXlCTVUrZGN3T3oxV0RJVVoyai94SmViYTFqT0RwM1FyeGhjUVR5TnVmWTJSK0hGUXpiN3J4STFCY0cvTWRSaytRdE9RUHo0dWQyaVM4SSszeklmZ1lQL0tGall5cXhhSS9qWDRJNnhaTjdweVd1WGMxZW55ZC9SK2xHYUl1bkMvVnpIVkZYR2NqSGFOaDh1cFJJR21DSEF6aWx6VmJSRVlXc1hhVE9LaVNWRzBuL2JNNFNVMElCV2lRa3F6SkdEUHgzSDZQWVgrNmlqdEZaS3BjUmd0ZmI2Y2hTc29UYXNXanduZGxSbVVlQzgyNzFYbDkyU3Y5MkZtOVZ0N0M1ZjdtQW5SRGdnZE1zQzAzUGVSbWg4ekl2NVM0enlMSTNnOHNlb1dtQ2VvZHRpSU1uN3BVc0k3c0hCT04rN0RrS0k3MnZNWGxoTlYzcCtSQjFHUGxvNHlwUVE1ZVBiNlYwVGVJckFEWDZ5M1p3bTJEUExQWG5uaDBhSGh5WGs0bERlallZcjNVOTdidHUraTJpYm5sZHFBOW9YQTFOSzlzcXJ5SFplOVovNkdkc2Jvb2NiU09Eb00ybGQiLCJtYWMiOiJlOTNmZmM3Y2JhZWI2NDM5MzM1MWU1YjFjOTM4Y2JiMDI4N2JjZTA5MjZiYjUyYjZhMWJiY2I1MWFlNzI4ZDdkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJMVTU2ZG82dHBGYjFnR3FJWU84Umc9PSIsInZhbHVlIjoiTWkyWlJDblZ4NzlycU82Q05DTThpdUVJWWNWZ0cwdmpwVk5aZkRvNlZybzJZOHVKUmhtZHhSQllTV3BZUjhXUzB6UmxubC82MmhvaEorLzhjK3ByZjNlNUxmNlhUWWZ3OWh1YVZ3OSt0WFpVY2FHUUtPbmcyNWxhVTN2L1pINlJzSlhBMHpBaDVTVnpVNnpKVFdTTW80N2lreHYxWWhvRWZoNCszQjVPVFk0b3lzM21uYXExWDF4Y2FjYzBSd2IzRzhtZXVzVkQrajROK3BNMnZRUjZLUjFLZDFlTDhIRStMTVd0L0loR1c5VDAxQ01vakFWTE52T0dmLy9rMDVnaG83Y2JNcklQcGhvUndJbFhiU0k3QnFuWmtoNzJzcWZaR1U1MGxXYkJOMGMvckZQTVZCVTJnVExaVENpbzYwQWxHMGpjWllKK1NaaGFXYkU3UUZNTEZLRHlJWnJDTjFhZDVHRFlNRXpPbDljZ2tRYkhGalZQME45VlZWSEpjcmZmbGJ6RHUrNjZvbzY3b1FCUldDQ2srQ1V2MWNibWxSMENrRXlOcHBaTVVrdyszd04wZUs0ZzVkdUwwNXJRU2prVlJqQmhPakRRc0xsNUVXaWtNOWF4eHlrV2VyS0FMdi9LaVhWaG9FZXdWdVlFdFVhV001TVY0dXhKOG5iNnVBdUEiLCJtYWMiOiIyMmQxOWZkMWM0MjYxYzliZGY2MTM5ODgzNGUyMWNmNTVhYjdlZmRkNDcwZWFlM2FkNjgyMTNjNWIyZmQyZThlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhvaTMwSkJTczFRbEJpMW1EdzlNWEE9PSIsInZhbHVlIjoiSlVOZEt5Z094czNtWDRmeFZqd3ZreU9NWHlKUmRBcHE0RmRNb0huY2IvSldtQitYSnpTeUwyRnF0Mm5qakEySXVQSEhuVExsRXlCTVUrZGN3T3oxV0RJVVoyai94SmViYTFqT0RwM1FyeGhjUVR5TnVmWTJSK0hGUXpiN3J4STFCY0cvTWRSaytRdE9RUHo0dWQyaVM4SSszeklmZ1lQL0tGall5cXhhSS9qWDRJNnhaTjdweVd1WGMxZW55ZC9SK2xHYUl1bkMvVnpIVkZYR2NqSGFOaDh1cFJJR21DSEF6aWx6VmJSRVlXc1hhVE9LaVNWRzBuL2JNNFNVMElCV2lRa3F6SkdEUHgzSDZQWVgrNmlqdEZaS3BjUmd0ZmI2Y2hTc29UYXNXanduZGxSbVVlQzgyNzFYbDkyU3Y5MkZtOVZ0N0M1ZjdtQW5SRGdnZE1zQzAzUGVSbWg4ekl2NVM0enlMSTNnOHNlb1dtQ2VvZHRpSU1uN3BVc0k3c0hCT04rN0RrS0k3MnZNWGxoTlYzcCtSQjFHUGxvNHlwUVE1ZVBiNlYwVGVJckFEWDZ5M1p3bTJEUExQWG5uaDBhSGh5WGs0bERlallZcjNVOTdidHUraTJpYm5sZHFBOW9YQTFOSzlzcXJ5SFplOVovNkdkc2Jvb2NiU09Eb00ybGQiLCJtYWMiOiJlOTNmZmM3Y2JhZWI2NDM5MzM1MWU1YjFjOTM4Y2JiMDI4N2JjZTA5MjZiYjUyYjZhMWJiY2I1MWFlNzI4ZDdkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJMVTU2ZG82dHBGYjFnR3FJWU84Umc9PSIsInZhbHVlIjoiTWkyWlJDblZ4NzlycU82Q05DTThpdUVJWWNWZ0cwdmpwVk5aZkRvNlZybzJZOHVKUmhtZHhSQllTV3BZUjhXUzB6UmxubC82MmhvaEorLzhjK3ByZjNlNUxmNlhUWWZ3OWh1YVZ3OSt0WFpVY2FHUUtPbmcyNWxhVTN2L1pINlJzSlhBMHpBaDVTVnpVNnpKVFdTTW80N2lreHYxWWhvRWZoNCszQjVPVFk0b3lzM21uYXExWDF4Y2FjYzBSd2IzRzhtZXVzVkQrajROK3BNMnZRUjZLUjFLZDFlTDhIRStMTVd0L0loR1c5VDAxQ01vakFWTE52T0dmLy9rMDVnaG83Y2JNcklQcGhvUndJbFhiU0k3QnFuWmtoNzJzcWZaR1U1MGxXYkJOMGMvckZQTVZCVTJnVExaVENpbzYwQWxHMGpjWllKK1NaaGFXYkU3UUZNTEZLRHlJWnJDTjFhZDVHRFlNRXpPbDljZ2tRYkhGalZQME45VlZWSEpjcmZmbGJ6RHUrNjZvbzY3b1FCUldDQ2srQ1V2MWNibWxSMENrRXlOcHBaTVVrdyszd04wZUs0ZzVkdUwwNXJRU2prVlJqQmhPakRRc0xsNUVXaWtNOWF4eHlrV2VyS0FMdi9LaVhWaG9FZXdWdVlFdFVhV001TVY0dXhKOG5iNnVBdUEiLCJtYWMiOiIyMmQxOWZkMWM0MjYxYzliZGY2MTM5ODgzNGUyMWNmNTVhYjdlZmRkNDcwZWFlM2FkNjgyMTNjNWIyZmQyZThlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1427732200\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2130859456 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130859456\", {\"maxDepth\":0})</script>\n"}}