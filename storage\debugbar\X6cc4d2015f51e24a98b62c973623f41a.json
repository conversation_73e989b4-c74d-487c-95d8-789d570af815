{"__meta": {"id": "X6cc4d2015f51e24a98b62c973623f41a", "datetime": "2025-06-28 15:00:39", "utime": **********.372557, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122838.978949, "end": **********.372569, "duration": 0.39362001419067383, "duration_str": "394ms", "measures": [{"label": "Booting", "start": 1751122838.978949, "relative_start": 0, "end": **********.29746, "relative_end": **********.29746, "duration": 0.3185110092163086, "duration_str": "319ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.29747, "relative_start": 0.31852102279663086, "end": **********.372571, "relative_end": 1.9073486328125e-06, "duration": 0.07510089874267578, "duration_str": "75.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45707344, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1910\" onclick=\"\">app/Http/Controllers/PosController.php:1910-1968</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.02426, "accumulated_duration_str": "24.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.328048, "duration": 0.01873, "duration_str": "18.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.205}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.35443, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.205, "width_percent": 1.237}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*************%' or `sku` LIKE '%*************%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*************%", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3568752, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 78.442, "width_percent": 9.934}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (21) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3621392, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 88.376, "width_percent": 10.511}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.366816, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 98.887, "width_percent": 1.113}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1919590945 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1919590945\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1234168568 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1234168568\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-666545272 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666545272\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1976192073 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751122833995%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVWR1Rkc2F0cTMvNjZKUHYwVVVFWnc9PSIsInZhbHVlIjoic0FuT1J4elFKbFVqaUI1NEI0ZXQxSVlWdmo0Z2tDY0JuM24rZk1jZEJlT2ZNcEw3TktDd0htbzhkV0NjWFFNdzFORVNKUzN6U2RETlk3MGRubkxaVmFaTU14QjNXZXZ4MThTb1ZvdVorck9aRDNtQTdqVUlVZmpRVnRXMlgyREtEeW16WWpISk5TZGQrMVlHekN0bFExVDZaYzUzMmdWSmRDbkpwY0xlMWRiUWUxNzVHbUZockVubHZlQUtTUkVsY3lsaCs2S1FKcklHMDhSYU94dElPUk40aDBtY2VQRXR1c0ZQOTZVODBTL2krbFAwZXZPNmFLZ2UzOHpQWmVuN1cyYXREZCtkblVyU01oZ3l4aW04N2FyYkduL3cvQ2hPbzYwUVJsWFQ3bUx3SXVBN3FCTWw0R3NtNTJDUE9PUVF5ZDV1dmFJenJTK3Uxc2hZc0psSXJ0Mk9QMHlGTGdta3RNRXhMdmRDUFhGOWJRVkRXWk9CdWtFOTFQUXlQSDhGWEFMTEN5SzJzd0pQaXFEUFNIT0pDOEpCUGpkVVFaM1pSaHg5QkJCVG9OK1A2SjZnZXg4aHZscjc3UzBpZzJTZWRYb28wUC9wdURDZFh5TituKytacXZqckErVTdhbHRWVVg0ZEZIQXY0WWZFUjdZM3p3bXE3bm5yZTBuUHQ3YU0iLCJtYWMiOiI2OGE5MGJlZmI4YzhhZGM2ODkyOTE5MTQ5NDEzOTNhNGMzMDEwYzA4ZmUzMGUyZmQ0NmEyZmU4YTMyYTNjZGU4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJKN0VlL05XQjJNNDROVUNSVFZXYWc9PSIsInZhbHVlIjoiMzFKRDlyMDl2dzlHOWk2MlZUTldLakcxMWx6b296cHF4bW8wNmN6bzJOZlRKdk5McmZ1bTd2c0Z1LzhKbXI4Q0pJSmEvdzNpeUxGekoxamdZdFdDRWs3alB0WWhBdW54Z2I2elNJUWhFc3Q0dmdBY2RTdEhsUGhuelF2U08yK3htL293azBhTThSWHQvWFA5WlhHZERMbDU2bXZscnNqMWozWVYrQ0VHdFhVM2w4VTJ5K2UycWhWTkxsVzkvYXRIdFk2dHBBQ0FzbG54b3NmWklxa2xldUliVytmV3FQZlQ1MkNJMjZ2dXF0dDJ3TWxpM3VZVERQbGY0UjZLaWNsbVJWRU5tb1JOeTUvblRKQnFNQ3Joakw2VDVvaVNxWEFPcFFuUmo1VVVmSjJQZjBmODRwZWRsMTRXNVViNGozZzdIU0lLaHhxSk41SnNOZ0ZYTHdiQlphRTUxL0lkUmZwNURVSzVCeHVBSFhaZFNHeUdlS3RHNjZBSlhlcldYZkR5T25pK2tmeVYvSTNTSG80aW9LOGErcGsyQ2V1bm5nbjlJMUxzTmtGOEFkVmxpSVlUc2c1SXhkNis5TW1LTDZIdE5KVjZGRFVkN2JQbXBjQTRqMk84RzkwR3lXWmxuSVNwWWpFQXRKY21FNTJwbkFtM3pPNnJDNW8ydGF5R2EwbkIiLCJtYWMiOiJkNjg2ZDFkZjY4Y2RkN2JhOTM4OGEzNWNjYmZkY2UzNzUzZTM3ZjliZDgwYTlkYjQ4YmUyNzVjOGIxYzYwZDE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976192073\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1046381259 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046381259\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-789446014 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:00:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imh4WHdXQVFZbnZPYjYybFlWTXg5MEE9PSIsInZhbHVlIjoiNi9IQmNKMWxWbVdwZ2luWlQ1UmVkS1doZTRDMjlUa2h4LzVQM29KaHFOTE5qRHR6bFh0cHdmR04waFlHeStwSHVmb1VtalAveDZxTHNmT21rZUh3bXJjQjloUGJsRExwdWcxbmw0MHJLL3p6V1hUeFI4NGd2blMzalJ2YnA3NUR5K0tsVFVrcFhuS2JMUzBvaFAxMWtIcFRuSFZGUHFKalFSUDA0c2tJbTdna2dESjBXdFB4cTVMRWszczg4Q1VGRk05YTE5SEJHUFFtNlF6UWVaYjd3Nm9WekRPdC9DRFZ1bkdtc2JITUFSK2JZVUR1UWMxeGp5OE0raER0YjBScndmcVg5YzBpa2x3QXBGb0x0RUg1RkoyY1hsVXlKVm1FUFB5QW1NQnVOSkVxc2pVazVYUmZHbDh6dFYvWnFaMUR4a3pWL2FpcTVIRVRvYjJWUmpLQ1VYMEJYVTFCNmV3ekVaQWloVG9xZWVBK3FOQjV5RXUxRFd3SlJic2dlaEVFSmVIcEtCZzc3NFhvTThyeXJqd0Rnd3FrM0Z6Vk45aGdOa1NuWnNvQkZpdDBmVG5hSnVML2FhTG5JYTc3aVFtTk9OazBaNHJJSjFickF1cXRqZTRrWWlqaVhjZUQyUXEvOU9mRzJJR1N6dnY2b2huMmc5ZWU2WmhrNkx2ZTVmY0YiLCJtYWMiOiIyMTQ5ZmVjYWYxMGIwNjZhOTY5NDk4MjI3Y2MxNDYzOTQzMzAyOTdlN2E0ZjAzNzU2MGI0MTExY2RkZGNjNGFjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVoTlhadGR4QTRGbmhOa3VEL1ZyV2c9PSIsInZhbHVlIjoiSVhibDdLdTdHZzM4MEU3TXZsd3VVR1phcEFISFlDaUZwWE5iSytnbFY4dVhGZlZLeHM5dVBJT0VUMUFMQWtmaWRiby90SXJ4U1lxa2l5eTA5ek02dTNac2NrZXVFcUdMcFpjZ2JnRkUwVU1XT2RCN1A2Z2xpODJ1SGUrcnVDaGVwK3VrZkZuYnJjclNmQ0g4aDJBN2FmeWpTdzlyMFA0YUJYSktRb3AxNjB2ZjdXVjYvV0IveCtlc3grRmVua3htTFFTa1NZVjlUeGdPN3Y3UFJDT1lkQ2VqbFQ4bkZFNjVVeVJxSVJIcTRFaUtTbkFuRE1Yc0Z4Y1JRb2piVkhLanc1OFhUcTNrRUw2KzU2UjcydWNXSTVPeUVWYy9qS2xsTjlvKzBiNVZvcEdLZ3RrZGtVaTRhN1JFTGV5RTNtYWQ2aWx0eWhQdjJ1NEJIdzhxWllkM2tYTnEyMGJZTmh1MTZXWUFJTmpVeXpRTnIycFNMRndKWTlxSDFsVXdHaUc3aFFmWmhBUWxHT3pJMUNIRnZxcVF1eXUrVklTUDhrNWRvem5Oelp4cnljNG5kWUZ0bTlYc3V3YXV3S2l5aXBDS2tmOHpGT3JhV25NVXNNSmFrejB5U3hpRndQQS81SGF2cVF1N04rOUFlYmdDeHBRWVdOYUdxUTRTeHp5OGIrKzciLCJtYWMiOiIwNWNiZTAwMjkzZjViNWVjNDRlYjJjODM2MDc5NWM3NDFlZTMxM2E3NzY2NDdjZjJmNDUyNzJkZTY0ZDU2NTljIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imh4WHdXQVFZbnZPYjYybFlWTXg5MEE9PSIsInZhbHVlIjoiNi9IQmNKMWxWbVdwZ2luWlQ1UmVkS1doZTRDMjlUa2h4LzVQM29KaHFOTE5qRHR6bFh0cHdmR04waFlHeStwSHVmb1VtalAveDZxTHNmT21rZUh3bXJjQjloUGJsRExwdWcxbmw0MHJLL3p6V1hUeFI4NGd2blMzalJ2YnA3NUR5K0tsVFVrcFhuS2JMUzBvaFAxMWtIcFRuSFZGUHFKalFSUDA0c2tJbTdna2dESjBXdFB4cTVMRWszczg4Q1VGRk05YTE5SEJHUFFtNlF6UWVaYjd3Nm9WekRPdC9DRFZ1bkdtc2JITUFSK2JZVUR1UWMxeGp5OE0raER0YjBScndmcVg5YzBpa2x3QXBGb0x0RUg1RkoyY1hsVXlKVm1FUFB5QW1NQnVOSkVxc2pVazVYUmZHbDh6dFYvWnFaMUR4a3pWL2FpcTVIRVRvYjJWUmpLQ1VYMEJYVTFCNmV3ekVaQWloVG9xZWVBK3FOQjV5RXUxRFd3SlJic2dlaEVFSmVIcEtCZzc3NFhvTThyeXJqd0Rnd3FrM0Z6Vk45aGdOa1NuWnNvQkZpdDBmVG5hSnVML2FhTG5JYTc3aVFtTk9OazBaNHJJSjFickF1cXRqZTRrWWlqaVhjZUQyUXEvOU9mRzJJR1N6dnY2b2huMmc5ZWU2WmhrNkx2ZTVmY0YiLCJtYWMiOiIyMTQ5ZmVjYWYxMGIwNjZhOTY5NDk4MjI3Y2MxNDYzOTQzMzAyOTdlN2E0ZjAzNzU2MGI0MTExY2RkZGNjNGFjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVoTlhadGR4QTRGbmhOa3VEL1ZyV2c9PSIsInZhbHVlIjoiSVhibDdLdTdHZzM4MEU3TXZsd3VVR1phcEFISFlDaUZwWE5iSytnbFY4dVhGZlZLeHM5dVBJT0VUMUFMQWtmaWRiby90SXJ4U1lxa2l5eTA5ek02dTNac2NrZXVFcUdMcFpjZ2JnRkUwVU1XT2RCN1A2Z2xpODJ1SGUrcnVDaGVwK3VrZkZuYnJjclNmQ0g4aDJBN2FmeWpTdzlyMFA0YUJYSktRb3AxNjB2ZjdXVjYvV0IveCtlc3grRmVua3htTFFTa1NZVjlUeGdPN3Y3UFJDT1lkQ2VqbFQ4bkZFNjVVeVJxSVJIcTRFaUtTbkFuRE1Yc0Z4Y1JRb2piVkhLanc1OFhUcTNrRUw2KzU2UjcydWNXSTVPeUVWYy9qS2xsTjlvKzBiNVZvcEdLZ3RrZGtVaTRhN1JFTGV5RTNtYWQ2aWx0eWhQdjJ1NEJIdzhxWllkM2tYTnEyMGJZTmh1MTZXWUFJTmpVeXpRTnIycFNMRndKWTlxSDFsVXdHaUc3aFFmWmhBUWxHT3pJMUNIRnZxcVF1eXUrVklTUDhrNWRvem5Oelp4cnljNG5kWUZ0bTlYc3V3YXV3S2l5aXBDS2tmOHpGT3JhV25NVXNNSmFrejB5U3hpRndQQS81SGF2cVF1N04rOUFlYmdDeHBRWVdOYUdxUTRTeHp5OGIrKzciLCJtYWMiOiIwNWNiZTAwMjkzZjViNWVjNDRlYjJjODM2MDc5NWM3NDFlZTMxM2E3NzY2NDdjZjJmNDUyNzJkZTY0ZDU2NTljIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-789446014\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-338181209 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-338181209\", {\"maxDepth\":0})</script>\n"}}