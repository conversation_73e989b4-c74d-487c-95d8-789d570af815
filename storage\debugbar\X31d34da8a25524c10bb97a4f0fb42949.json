{"__meta": {"id": "X31d34da8a25524c10bb97a4f0fb42949", "datetime": "2025-06-28 16:01:13", "utime": **********.015827, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.548163, "end": **********.015841, "duration": 0.4676780700683594, "duration_str": "468ms", "measures": [{"label": "Booting", "start": **********.548163, "relative_start": 0, "end": **********.935348, "relative_end": **********.935348, "duration": 0.38718509674072266, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.935358, "relative_start": 0.3871951103210449, "end": **********.015843, "relative_end": 1.9073486328125e-06, "duration": 0.08048486709594727, "duration_str": "80.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45529336, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02669, "accumulated_duration_str": "26.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.96951, "duration": 0.02555, "duration_str": "25.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.729}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.006794, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.729, "width_percent": 2.623}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0101242, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 98.351, "width_percent": 1.649}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1268017319 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1268017319\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1531586591 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1531586591\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-899362414 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-899362414\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-54547156 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126468701%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJFREo5SXZzbFZiWk5tMFVEL0dTd3c9PSIsInZhbHVlIjoiajFSaS9qdDFsbWIrUkI2dHlQK0xJU2ZKaEVTdGdhWTVUN01VY0NDV3Mza3Nxa3N6Z3dJZXlnYWpOVTlwWVJCc0ZXWW9VTG9qNVhGNXE1MzJGUnRBM0Zmb25PM1FxemtKTlZaUUZQWWNPZEZZMGdaRCtkUDJHMVd0U2FlWncwNGRsemZpcVZ5QkFoeTVBUEo4d28vZWN6bG85L0VpRWhXVWFMb3pLRFRSd1VycnJrdTJxenRoaDk0SXNCZkRaNE1ybExRblIxdHNKckZWVzhMWjc5b3JmWnhZZTZYYzVEcVdlOVJidEMxZnpUcnlKUHJRYlZTUXFUdW03bGg1Ym81ZG4yRmt6RmVzV0N3ZXFoZ1NWeVJrMHlVYWE0QkVuaXZOT0ViT2Ixb2FMMXJqTlRtUFZRZXVIWDRtN05aZ1A4WnZXU0d3R1ByN3I2YU4wQmJtTEV0Wmg1RkI1cFJrczJwUDhjSnowMzRDQXVtSTBVaWlxRTlHZGlmOEF6V0JRTURjQis4TzNiRHRKZkN6T0gydkF4SENXRFY5WWpSVUNTVCs0cy9TZzk5VHJTcHZ1ako3WmxVVjliUkRaZVlrNUhoWkV4eUtzM3MycWE5eVNRbk04ekJKZTZvYklkK3hKOXFWRm9uVm5sdGZ1OFRtQlJNNlM3ckoxaFBUUUowL3pPSkEiLCJtYWMiOiIwNmI0NzZhNzBhYzZiODdiZDE1ZDY2NGU3NTk5MGNjZTQwNWFiZTMxYmNmMjZhYjIxMzhkMTdjMmMxYjA1MmVhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJVdUdDbFRmRDdLbjZaTW1ENjhMaFE9PSIsInZhbHVlIjoiSTNRVUtDVnhwSE1VWmRVZ1VFOFZKeTcxaWxacWVGSDhyMXFzQTRkckhGcFJhVkt2cktTQVRoazhoMG1abndPbXBwN2hpM2YzUlNyQndEdkZpWVN5SmZUcGF2QlhsT0UxcVkwaW5ielcvaCtwcUxTcXY2eGo5V1BKSnNqczNlV2ZxL3diV1Q3dExtUUpPS3p0YjdzRU9oRGhka00zUFhrdGN3Zkh6V0FvWWxaQWZJaXFsb2gyZ3lGU0hmbHlJQnFGVm1mWDhvMGphK0draEN6bmpGMGd6K3A4UkV0Vmx5Rko4QUIvMHlTYU9JS3NMNEJNWEhheTNTaHpxdEFxWTlQd2FrZEJFaVlORlNWNVFVdG82enZwaUtwelJjMVgrZC9RTThpL0xOamJUTmFieEdVeEsvWS94TlBVNVFCMXJScFJ5Z2xmaXBnMENsbTJlaWxqV3BOTTJpVzdXRDFrSzY3VFh5NjlvVkloS2R1SnZtdXRId0xXalpxbzRoa0ljUU03dUNySmhzWUlMYm1CWjhDd0k4WHV6TVNPRTlUZHJLQmNKY0FGRGp1a0xFNWtKMkYxZkV4RW5ybW5uM2kzWVkwSmJ0NXZldjcxN3NYY21NNFpoQmd0M2NJdXkzTTl2eFZ3eDJIRHNmNDExM0QwVjM3WFJhbG9zbnptWmxodFNKeXAiLCJtYWMiOiI0NDU5MDgzNDFlOGU2YjEwZGIwZGNkM2RlNTc4M2FkYmNjMjM2OGYzN2Y5NDA5MTg2YmNmNGM3NDgwMWI2ZDRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54547156\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-474122872 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-474122872\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-106720053 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBpbC9xZXg3aElpZHRBVy9ONVo1QkE9PSIsInZhbHVlIjoiTE5kNlpMc3BGNTFVY2xJVXdxSHB5eUkzTG1seFlaM2lKazkvZGNjNzcrdG5adk9qelhsall3V3YwYVJUelM5aEZSS0lqTTFJQnVGU1NEemhMandoTFpGWUtwd3owWWdNb0hqdFpwZ203QU4wMGxCSW9OcXRieGxFQnVyaDdZbHFkNG9GRjREd0owU3o2b3p0N0drbHRoV21aWEZFOWJvRXJZTlFxOS9IalJ2ZitMYlN4a1ExU2dBdWdaS3l3ME10WjZnTXJWREU3K0daNVkyVGx3ZDVTNGFRK2RIYStBZ01xUko4TmNUWnU3ZzBLM1ltdTNHL0xzYVhlQ29FaEplcnYyQmpDb081SkxvRlEzQTlZS0tza0d2Y1pza1QwQ1Z0cWlPRlNsVjZZZzZueGZXKzhOVWJEbmJXODJQeHVDVkhPS1I0Z3plNWJuSVJkZEthaEtxVGZqb1ZKY1h6WTRKcEUvNCtFZ3lnWUwxbVZxdlhoQlhtanp0ejNaNUwxZk9kMlFRUHRVYmUzdW9QQmZ5WWNlb3VjODN0SExHSGxRMTVjVjRXaFRQb1JKeG9UQzBZaHdQYW85RURaL1AwU1h6TmR1MGhpZmE3V1BZYXBFdUdaRUJVQVVtRUJKaUEwMmxBUzNJeFZPamN1dmtzREJjT0pmSTN0NVlIeE9yV1BXNDUiLCJtYWMiOiJiNzFmODBlYzBlYjUxN2E1ZWI2NGJiOWQ1ZTEyMTg3NjgwNjQxNTA0MWE2MGZlZTQyNmY1MWEyMmJjZGNhYzdiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5zdGxOUlU4cmVNeFBGOEU4b3ZTZHc9PSIsInZhbHVlIjoiNWEzdWlKYjlVaTNycTJnYXhRRFhHbXJGRnliS3RYT1EzZGxWdGdFRy8xOGZ5aDVFV0ZVQllJQ3FiT2RaYVRVL0xyVWc5bTVDbGNyUkdOVTQ1L05ncjc2Zm5vTDhVMmV6Zno4aUhBdHo2cVZMWkxnMjZpa2lkN3EvQlVaY3lOZWZDYVhaY04rdm4xKzhLSlFEdXV1NkhXeStwSjNLQlFjZmpVQWhGWVZSWnVGdUNyMDVnT3prR1RlTXVnUlN6NnV6Z0pzNy92VjlHMFA4WmZmakJwaXlQSnBXTCtSTFdyb0VFQjhHUEtvcWszVGJuakZyNTdRWHlrMEFzK0FHVHlmazZKaitoejdNTzRBTVlCV3VnSkZrb0xYbXhuKzArdXQvRjdsTUV2dHJwc0E4bFpGbWtoeVhMOGc1VHRvdjhpK1dhNUkxK1JIRFpUL1N3TmttNEhpQmcxbjJtbURRMERKYS9oeXpvVGdId1FUdXk4UWFWZ2lHT1VkcGpzZCsvZFpHV2RVTE8yZ201RldReHJ2UEp2UGtUcmhMSUpiZ05TOTIxSzRPTVFLeHA1RksvMGxFbDIwbGYzdDRTVWhHQzh5ZjFCVG5QWk5lWXZzQUVxV1BBb2hoMXhtdlZZVGE2dUI2OExraGhyRXhWUXlhL0M3ejVkM1Rma1MzcVpmL2V1ZXAiLCJtYWMiOiI0Nzg5ZGExMzAwYTlmNWI1ZWJjYmQyMmY2YmVmMjhkNWQwMzNhYjE0MWYyOTM0YTBiMTIyY2JkMmE4NTAwZjkwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBpbC9xZXg3aElpZHRBVy9ONVo1QkE9PSIsInZhbHVlIjoiTE5kNlpMc3BGNTFVY2xJVXdxSHB5eUkzTG1seFlaM2lKazkvZGNjNzcrdG5adk9qelhsall3V3YwYVJUelM5aEZSS0lqTTFJQnVGU1NEemhMandoTFpGWUtwd3owWWdNb0hqdFpwZ203QU4wMGxCSW9OcXRieGxFQnVyaDdZbHFkNG9GRjREd0owU3o2b3p0N0drbHRoV21aWEZFOWJvRXJZTlFxOS9IalJ2ZitMYlN4a1ExU2dBdWdaS3l3ME10WjZnTXJWREU3K0daNVkyVGx3ZDVTNGFRK2RIYStBZ01xUko4TmNUWnU3ZzBLM1ltdTNHL0xzYVhlQ29FaEplcnYyQmpDb081SkxvRlEzQTlZS0tza0d2Y1pza1QwQ1Z0cWlPRlNsVjZZZzZueGZXKzhOVWJEbmJXODJQeHVDVkhPS1I0Z3plNWJuSVJkZEthaEtxVGZqb1ZKY1h6WTRKcEUvNCtFZ3lnWUwxbVZxdlhoQlhtanp0ejNaNUwxZk9kMlFRUHRVYmUzdW9QQmZ5WWNlb3VjODN0SExHSGxRMTVjVjRXaFRQb1JKeG9UQzBZaHdQYW85RURaL1AwU1h6TmR1MGhpZmE3V1BZYXBFdUdaRUJVQVVtRUJKaUEwMmxBUzNJeFZPamN1dmtzREJjT0pmSTN0NVlIeE9yV1BXNDUiLCJtYWMiOiJiNzFmODBlYzBlYjUxN2E1ZWI2NGJiOWQ1ZTEyMTg3NjgwNjQxNTA0MWE2MGZlZTQyNmY1MWEyMmJjZGNhYzdiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5zdGxOUlU4cmVNeFBGOEU4b3ZTZHc9PSIsInZhbHVlIjoiNWEzdWlKYjlVaTNycTJnYXhRRFhHbXJGRnliS3RYT1EzZGxWdGdFRy8xOGZ5aDVFV0ZVQllJQ3FiT2RaYVRVL0xyVWc5bTVDbGNyUkdOVTQ1L05ncjc2Zm5vTDhVMmV6Zno4aUhBdHo2cVZMWkxnMjZpa2lkN3EvQlVaY3lOZWZDYVhaY04rdm4xKzhLSlFEdXV1NkhXeStwSjNLQlFjZmpVQWhGWVZSWnVGdUNyMDVnT3prR1RlTXVnUlN6NnV6Z0pzNy92VjlHMFA4WmZmakJwaXlQSnBXTCtSTFdyb0VFQjhHUEtvcWszVGJuakZyNTdRWHlrMEFzK0FHVHlmazZKaitoejdNTzRBTVlCV3VnSkZrb0xYbXhuKzArdXQvRjdsTUV2dHJwc0E4bFpGbWtoeVhMOGc1VHRvdjhpK1dhNUkxK1JIRFpUL1N3TmttNEhpQmcxbjJtbURRMERKYS9oeXpvVGdId1FUdXk4UWFWZ2lHT1VkcGpzZCsvZFpHV2RVTE8yZ201RldReHJ2UEp2UGtUcmhMSUpiZ05TOTIxSzRPTVFLeHA1RksvMGxFbDIwbGYzdDRTVWhHQzh5ZjFCVG5QWk5lWXZzQUVxV1BBb2hoMXhtdlZZVGE2dUI2OExraGhyRXhWUXlhL0M3ejVkM1Rma1MzcVpmL2V1ZXAiLCJtYWMiOiI0Nzg5ZGExMzAwYTlmNWI1ZWJjYmQyMmY2YmVmMjhkNWQwMzNhYjE0MWYyOTM0YTBiMTIyY2JkMmE4NTAwZjkwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106720053\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-911598927 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911598927\", {\"maxDepth\":0})</script>\n"}}