{"__meta": {"id": "X0ea92d3cea59d3b58124f2385e7de542", "datetime": "2025-06-28 16:03:06", "utime": 1751126586.008932, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.567546, "end": 1751126586.008946, "duration": 0.44140005111694336, "duration_str": "441ms", "measures": [{"label": "Booting", "start": **********.567546, "relative_start": 0, "end": **********.95546, "relative_end": **********.95546, "duration": 0.38791418075561523, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.955471, "relative_start": 0.3879251480102539, "end": 1751126586.008948, "relative_end": 2.1457672119140625e-06, "duration": 0.05347704887390137, "duration_str": "53.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45712304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00305, "accumulated_duration_str": "3.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9842432, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.967}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.994123, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.967, "width_percent": 15.41}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1751126586.000007, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 77.377, "width_percent": 22.623}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-809457601 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-809457601\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-43481366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-43481366\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1729316656 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729316656\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-198457976 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126566681%7C14%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRMNUM1UTdYUzc2L1hWTHVOM0JURUE9PSIsInZhbHVlIjoidEIrMy9UTlpDdjRxZ0ZUNDV4dS82MWkwY0dmVEl3VncvTkJkc0VJUTYvOTNHUGhoaVpzQWJCQ0t6elEwb2hFK01tSFVKSVBDWmU2dDU2Z1phNSs2bHdQMGN2aVJrT3doMzdSVm9PZ3E1ZXB2RUg4S1RCUDdXdTZLWEpoYnlzc0JnMFpUSnlQVkJGVnhVRjRudDltRHM4RTF3Tk15eTFnV0lsNjQ4MkxocVk4UmVTQkJBUGV6R2h4MlNLdXU5NFVYUzN3aTdPVDMrQTV3MWxSZ2VOTkR1LzVqQTBYTENMR3p4K1ZBMFVTS1VxcVErRUI3K3FNUGdGRitLMlEwamx0RlQ0RnlyTlByYUpVam9nL2YvNHRCdDdTekYrWWNvNHVpUGV3eXRvMkVxUFZsUkpUUTZGbGZ3RHBZbk1Gb0QwdlJMMnBWNTMzbm9jcngyVVJyT0ZrRWl2cUwzTVZmbGtPNVJRZm9kMDBCMW9xWGozK0NEWUV0dFM4T2RNNXQ4eittZitrbVJ3aysyazRJM0NIdnRuRktrWFZhRFFUZEk5MDYvRHd5RjQwY3A5VW0xV2krbVhOa1dEcmZYUE93R2NKWVpmK2V6L0tUazcwbnMyeXc3Y0JjWmE1SjRwT2xUN3ozdTN2ZmM5cDJJNHdZTWlDMlEvczZHaGptM1hvWWQ2WUciLCJtYWMiOiI4M2M3YTI5NmZjMGI1NGFhMmE3MWEzNjUzYWU0ZmY5ZGQyZjkyYzRlOGU5NjlhZjIzMzkxNzYxMjhmOWQ4ODA2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImN2dzNJNEVZR1hWTGxOYUwvVGFGT1E9PSIsInZhbHVlIjoiamZTTm1jMXUxNWpYUDllNlJCdXR4U3VFbkx4VWNOUGxjSjRpVm15ajFPTGZnZndrZ3YyWVp3UlozMnJRTXpUUUh2WEN2YVQ1em1kV2FpYzVWYnNBejhVbEpEWjhQZjgrSW5Oc0hDOUVrWUdUdXlYZ01RZWVSVEVVUW1SZWI4QlVUVFE0Tmtuajh0NlRlS01mTG9KTEphQlRGcWdYd2tzclZhUWpvanNhQTFtZ09RQzJZTm1BLyt4Ui92SS9Od1RnR0QzMDIwSU8zeEpCMk0vZWJ0eHlNUzVPSXJCUDg4ck5zYnJGT2orNzJnU2hMQU56UWpRelZEbXdFTmRCRzJGR1RMS3BUa3I5Q3RlUDlZbURlbmZzQitqTkVLa2JiYzFveXJBWHN6bG0xTVh3bmRqVFFtRGo4NVZlb3c0MHdoaTh4blFWMW9JTmRDOGFrT0lnQ24yT0xHVktxc2xnYlFqeGF0ZzBBOHZHb1JFNFFaR01kdUF3dFJWbi9iM1V6SEsraUYvRmhiVTBhZldGZzVMNm5oS1lFTlNKc1QrMlRaMGVRZXRRTkIzTUwrcWJQRGNUZ2FoWGtWWmV5Q2Z6dkVwMXBkdUN1WXpESTdzR0ZtcUF4MzZzdEgxcjJUckt0REVJRVpKYXRhQ1ZwUFFySFNyTmVkazVVNnBUbU50ZUtJby8iLCJtYWMiOiI2NThjZDZlYzFjY2NiYmM0ZTkzM2IxNmMzODE4M2MxZmU4MWViYTRhZWI2YWM2Zjc1M2VhNWNjOGY2NTQ2NTQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198457976\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-208444527 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-208444527\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1029162800 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjA5eVUzTkc2UUtvazlRKzJSTkdkM2c9PSIsInZhbHVlIjoiellDUFl2c2NiQ2k3cmRjbmEyUUt6QitoZExCY1Y4c1grT2JwQ0lkc01VRk44N05qWHNSQmZGZFUxN2ZLWG81U1EvVUptenZ3N0ttSG1uNC9hbm91NGpIMGF3bFltcW1obHRlNVBjZm4vYlpZRzA4ZUJwWmdyaXJ5ek9SU0kvcWtVMFpSMTc5M2dnUnNmL0piKzNBejVkbENpUFJnczBwK1pIbzE5T2lFczcyK253V1Avb0F5NXFWYjAzYWNBZlU5QXF2dUdzU2FmazZPNGNHdGxkNW4wVjdqUTZTaW9NZ2NjWlVRTk16cmdua3NxSWhjUXFtdjN3OWJibnlpYzM3d2lyY3JNcEUvYlFFRTFUSHorc3FkME9mYkRwUUw5NlVmMVcwRlhMYWZYcVpiRk8rejRiTC9GSnd4cmFyK3FCVGkxYjRFc3JTWml4ZkJiOWsxWUUvQTlKVU15MEhsdEsyQlhXSGNDUlAwZDhJTVBxc21kNEZYWXhQdGVRKzRpZWVZZVdsbDJsOW9BQ0Z3RGtsOUY1N3I1cHBRazRJWFBub2ZBY3dlVzBqbEJHSjJWemdMUldxNnRGanUwRk10cmdDdzhnNGl6MStqVzRCdkZHL2ZGTDRQNEhNeWc3SkRYblJWbEVkVjk0WGd6VEhYRVZldVFRNTRVVnR6cHA2dUxRQmoiLCJtYWMiOiIzZDcwMTY4OTFmNTdjMjk5NjI0M2RlNTVkNTUwZDA4YzdiOTkwM2I2NGVjNTcwMzM2ODQyNzE0ZDdhMjc1M2YzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZKUXBGR004UTMwL250S3dkT0NnclE9PSIsInZhbHVlIjoiQmlBckJHRlUwdkQxeHo5UjVGUlA0SmdUZStiemZOQ1l4QlBkRGpYMVo3ZTh3ZHk4V3N0QUI2MFk0blNaM1FVbHlxcGtDZS9yWnBCNUdFKzloaFY1SDdOM2ZhMzBVUzAxZlVtbWQzWFNiMDBQRkovanJTbnRYU1NjMXdvNUhnUHVBZXpHNGZBbmd6cjROck5oNFdwMjlmZGhpV2tmMWh5Z3JGa1lrazhQaGdPenp2ak8zWGtQTzNPM1JmMEh3UkoyV0dSNkVoR1I5RjZGRDlHb0YrNW1BTHhOclZiaEVJSlQ4QzlnOVhoOHBMamxieitzTmIvQWRaak44UDdQNlNxK2UwVWFEbHE4QlVIZk9CbE85NTZ0SHBvMVVWWC9LSkUzMWhoMHZRNzlzYU1WUUMrR0xJZ1EyRWE4RFhXUUVNTi9KeFg4UWxCWTMrVHRKc3gwRzA2T1JlVXZTMVF5bkdpdWpBYXZLaHV4Y2RORDFGUnBaS0FxQzczcHJoSDBTUCtlTTVmVERqd2pZSVorL3ArelJJMDd3alQ3SzBHNTNKbDlETGZHc2gwa1B5TDMwR2VSWkl1Y1NhVGJvNXRPMElVKzkyMkw4MzYxZmMvSU4vWDVxVS80eXBqbFEwaXozSW9FcEVLdVVVdkdKb0tCUHBhVVVyaDlHSlg5Vk5vRVVGWFYiLCJtYWMiOiI0ZGRhZjE5NDdmYTBlNmQ3YTJiNDIwMjIyNmYzN2IyYzhlNjIzMDQzOTBlNzJlZWMxZDI3Nzg5MmJiNjlhZjhlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjA5eVUzTkc2UUtvazlRKzJSTkdkM2c9PSIsInZhbHVlIjoiellDUFl2c2NiQ2k3cmRjbmEyUUt6QitoZExCY1Y4c1grT2JwQ0lkc01VRk44N05qWHNSQmZGZFUxN2ZLWG81U1EvVUptenZ3N0ttSG1uNC9hbm91NGpIMGF3bFltcW1obHRlNVBjZm4vYlpZRzA4ZUJwWmdyaXJ5ek9SU0kvcWtVMFpSMTc5M2dnUnNmL0piKzNBejVkbENpUFJnczBwK1pIbzE5T2lFczcyK253V1Avb0F5NXFWYjAzYWNBZlU5QXF2dUdzU2FmazZPNGNHdGxkNW4wVjdqUTZTaW9NZ2NjWlVRTk16cmdua3NxSWhjUXFtdjN3OWJibnlpYzM3d2lyY3JNcEUvYlFFRTFUSHorc3FkME9mYkRwUUw5NlVmMVcwRlhMYWZYcVpiRk8rejRiTC9GSnd4cmFyK3FCVGkxYjRFc3JTWml4ZkJiOWsxWUUvQTlKVU15MEhsdEsyQlhXSGNDUlAwZDhJTVBxc21kNEZYWXhQdGVRKzRpZWVZZVdsbDJsOW9BQ0Z3RGtsOUY1N3I1cHBRazRJWFBub2ZBY3dlVzBqbEJHSjJWemdMUldxNnRGanUwRk10cmdDdzhnNGl6MStqVzRCdkZHL2ZGTDRQNEhNeWc3SkRYblJWbEVkVjk0WGd6VEhYRVZldVFRNTRVVnR6cHA2dUxRQmoiLCJtYWMiOiIzZDcwMTY4OTFmNTdjMjk5NjI0M2RlNTVkNTUwZDA4YzdiOTkwM2I2NGVjNTcwMzM2ODQyNzE0ZDdhMjc1M2YzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZKUXBGR004UTMwL250S3dkT0NnclE9PSIsInZhbHVlIjoiQmlBckJHRlUwdkQxeHo5UjVGUlA0SmdUZStiemZOQ1l4QlBkRGpYMVo3ZTh3ZHk4V3N0QUI2MFk0blNaM1FVbHlxcGtDZS9yWnBCNUdFKzloaFY1SDdOM2ZhMzBVUzAxZlVtbWQzWFNiMDBQRkovanJTbnRYU1NjMXdvNUhnUHVBZXpHNGZBbmd6cjROck5oNFdwMjlmZGhpV2tmMWh5Z3JGa1lrazhQaGdPenp2ak8zWGtQTzNPM1JmMEh3UkoyV0dSNkVoR1I5RjZGRDlHb0YrNW1BTHhOclZiaEVJSlQ4QzlnOVhoOHBMamxieitzTmIvQWRaak44UDdQNlNxK2UwVWFEbHE4QlVIZk9CbE85NTZ0SHBvMVVWWC9LSkUzMWhoMHZRNzlzYU1WUUMrR0xJZ1EyRWE4RFhXUUVNTi9KeFg4UWxCWTMrVHRKc3gwRzA2T1JlVXZTMVF5bkdpdWpBYXZLaHV4Y2RORDFGUnBaS0FxQzczcHJoSDBTUCtlTTVmVERqd2pZSVorL3ArelJJMDd3alQ3SzBHNTNKbDlETGZHc2gwa1B5TDMwR2VSWkl1Y1NhVGJvNXRPMElVKzkyMkw4MzYxZmMvSU4vWDVxVS80eXBqbFEwaXozSW9FcEVLdVVVdkdKb0tCUHBhVVVyaDlHSlg5Vk5vRVVGWFYiLCJtYWMiOiI0ZGRhZjE5NDdmYTBlNmQ3YTJiNDIwMjIyNmYzN2IyYzhlNjIzMDQzOTBlNzJlZWMxZDI3Nzg5MmJiNjlhZjhlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029162800\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-691207749 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691207749\", {\"maxDepth\":0})</script>\n"}}