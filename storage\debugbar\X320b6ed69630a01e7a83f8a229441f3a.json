{"__meta": {"id": "X320b6ed69630a01e7a83f8a229441f3a", "datetime": "2025-06-28 14:59:01", "utime": **********.543071, "method": "GET", "uri": "/inventory-management", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122740.836658, "end": **********.543089, "duration": 0.7064309120178223, "duration_str": "706ms", "measures": [{"label": "Booting", "start": 1751122740.836658, "relative_start": 0, "end": **********.206601, "relative_end": **********.206601, "duration": 0.36994290351867676, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.206609, "relative_start": 0.3699510097503662, "end": **********.543091, "relative_end": 2.1457672119140625e-06, "duration": 0.33648204803466797, "duration_str": "336ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48236592, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET inventory-management", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=20\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:20-34</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.02195, "accumulated_duration_str": "21.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.246078, "duration": 0.01923, "duration_str": "19.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.608}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2736008, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.608, "width_percent": 1.959}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2832189, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 89.567, "width_percent": 3.508}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 23}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.393907, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 93.075, "width_percent": 3.872}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.502414, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 96.948, "width_percent": 3.052}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage inventory, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-644778983 data-indent-pad=\"  \"><span class=sf-dump-note>manage inventory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage inventory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644778983\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.505842, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2073135858 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073135858\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.53244, "xdebug_link": null}]}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/inventory-management", "status_code": "<pre class=sf-dump id=sf-dump-720994954 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-720994954\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-977443445 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-977443445\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-555907444 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-555907444\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-349060566 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122734939%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNVUGhsTXo2cmY4MnFmOEFrZkgrdVE9PSIsInZhbHVlIjoiMHdXT3p1WCswa0hjQkdtV2FCNGtjT2xLbTVoV2ZndXBLN1lpVXJMaVY3dVdNYTd3RGpwVTkvd1RuTWRLUi8yZGQ3NzBweTdzZHppZjdxemF2SGsxdW1KT040MFJtaTBFenYrcDJpYkNCcTVZOHZGQXB2K1FpTGp5TE5iZk1NRlEyRTZUM0pLelREYVVnVDlKaTJINlNBUzdwUFBxOUNYdHpjOTVtc3dOak1aRWJ2NjlTL3RiNnhNeFpZMXljTDdkMVRBT2QxOFJtTk5vZXl6MWpZUWhjbFFwWGszaHZUK3hCR3VWcUVmd0dqVmtEZnI0SlMvS0hGdW1uVVB0N29ZZE1yTU02M3k1QjJ3RDJUOXY2b1NObW1TM2ovY05ncTRiZlZJZHY2S21wQ09MVkE2VTh2bStUcGJRYUNJVUpDTXFjZlFkYkJxOU9aRy8wZkZnemFjT243MU5saXUzTHp5T2xxYjEyM1FCQWIrbEM2anZrSllDYXlGK3NxSFViR1g3dVZKdzRGSGs0aURuS0VqOHBqSUNVNk5wYW5BWkhrNFU4MTNzVTBWem0wNG1TeXFQRERweFJxd0NKdngzQi9EdGFrVHdWUWJWU2MvYll4UWdFem9WTEtSem15M3UycG9Ea2IzMjVwUVI0T3hyR0p1cnY3YmxuVVRjMEZKNU11RkUiLCJtYWMiOiJlNTI4ZTM0YzhlZjM2OTUyODc3NTI2OWQxZWY4MzUzMTRjZTNkNTk4OTdjNjRkYmQ2ZDQ0ZTY2Nzk5NDY2YTZiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFqeUExR2VsNXJjTFBTL0l2bm1EQ1E9PSIsInZhbHVlIjoiL3hpZjRVNkVnYm1wSXAyQlRIM3hJMmsrUnk1N2ZLZU1SQ1lJNXJ0WHBDVFY2R1NJa1Y5U2pVZFk0YlBBYit0V1g5NzUwYUo2VzRJYSsxQldDTnZnT0hQMjNRSkVzRktXai80czZxaC90aDRHRjJScytLMzF3M0dNUENORW95dTQrdDRIVm9LVnhOSTI5T3Z4Q01aSGZrOWZVWHl3SEhmNytEU0hlQkpPa0RsQXBhRHFsbS8zVFZsVFltRzdPbExMZEhNK2xhdUpsWHBmUm9HRnFybCsxSDRoeGxFL0xabDZubTdVNmh2T2srWFo5WFQrUjJqWnUwTDRXZVZQTk1FQ0JSOU5IcndRWUsyZkdnWHZJVkRZbW5aOHBUUlVBbWF6SEFmU1RYOTRJNXhiT2tCcHdHeEdNY21vaWp4d3kyUXJZZURsd0hWemdpTXl1Yi9qZHFCdEVmV0NWcExZdC9lUmkzdmtsdXpudXo5eEpsSXN2cEhEMTI5Z0FFaU9oYm9ya2Z5MkJIS3VXelVaNG1obksrZ0NSQjAyalZvTVZqK1NJQlhqT0ttZXYzQno1SE84RjU1a1kxY1I4RHlnSnY3bzVvVU1nczRlZXBkejFFdVdvbzZjdFlDUlVKWVovR0lzYmQrRm5vYjdwNXVzZFQrVlg2V2tOQkJNOFFMUVE1VDYiLCJtYWMiOiIzZjc1Yjk2YjIzZWEzYTM3OGI1MWViMTA2OGQ5ODk0ZDE5ZmM5MzI1ZThkOWM1M2FkYTI4NjNiZjM1MzY2ZTQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349060566\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-597933932 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6nyGbgT6n8t00JbJ5CSgj7vkpXnxnyISs5XS6j6J</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597933932\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1426234567 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ims4Z3R4VkN0ZTBvaFVSVlpLUTB3Z1E9PSIsInZhbHVlIjoibTFYTC9BWjJCcGZzV2lld1VhWWlORlNxNUxpUjRNeGtMQVN1eld6cG8rMldGTEd3cEVSRFgyQkY2ZkVsMVVLVVNUTXhGcXhGTVJhOHVPbmp5RHZSZVNIZ2JLWm1XY0dCeCtBdGxhRjFLRmxZeDVnQTc0WmJJYXQ1Skh1b0o0WXRpWFJBcHMvcWJOMjUvWlM0ZHJEZE5pUDRFOW1qNHR4MnY1VEVJUnRxelllK2Q5ZlcyZUQ4bmhnQVQ2YmhhZnorV0dTYzlrQ0l3elkrTnIyRWxGZGhZZmJWWWhRZ1NwMFpKU3BFNEtlMHFaV0RtZXlaUXZrd25xblpydzFTVmljYmE2U1cyaDYxbTVwTEdSK0pVNnJtMllRQmZUZ2ZiQmwrN2pOeHRXVEVhU09GV2k5ZmhTYnRsZ3Nxci8yWDlzMExjQStnUDV0UkEwY29aNDJNUDhHblE5MFprMnFMUmZ1OWVHZ3R1M0p6aW4xYlJ3NG9lNWs0NWQ1QlVtZlVEQ2RpNTN2alhOOVdLdkdvQXJkaFEyTWRCRE9QbUhBeFZ6RGpzVjlWUkd3YmZKL3pqck96M0NHWmYwQm1zWmpZbzFrcTBMM2tmWEkxOHNVWWQvR1pvUVZGWHJPVHdrMUVYUEFFcEVYRndsV091Mko0VVlCeFJxZlgzYXM2ZWNpdWhmNTQiLCJtYWMiOiI5YjM5YjIyYmEzNjU1YjJjYzQ1NTdjOTA5OWU0ZmU0MzllN2I0MTQyOTdkZmQ5MzNkNzNjM2IzYWI0NWJhMDIwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlkzZ2IxZHdGQUpMMGhVWFpFTldLNXc9PSIsInZhbHVlIjoicHRXREZTQk5tWEM3VkVEMjhsYjRtaGc1WmRYbHljRHpHN1RnemtjRG4wZ1NNTU9JTnlxTDRrQVBaMGd3cTV6VjV0a3lhMU90SWpOakU2bENQMWhPS25zMkdiRFYxOENaR0Z0ejEzQXMwOWw0QTA1TnR3YkZNNDBqK2NOTm51ZERWU3dDL0tGelVEQ0I2WnRJVUZIMmI1U2hHaVQwZnorRHB6ekN5NVdaSGhJM3RlM3I3U3RLTCtjeklyYVFsSXZSSndKN1B3WWR6dDJDKzVpQW56UnVGYkJhaWtxaW1uMDJCb2dTSEF1QWJyKzQvSDBpL1pHampVK05xcjcxSmIwOC8vZzl4UEh4QXB3c1JaWExsdmx6STRtRVNOdVR4QjBKUnJGYmlpbXFybDBiaXQ0emg0eHJVT3BRdUxiNTNPY3dpT1RqdWpJOVRYMTRpNUdTU0s0emFzTkpqMEQxVXA4UnA2WU9QUXY0dFlMSmwrWnZFRnVJaXlxcGhjRzdxeGJwRm91N3puR2pCd0QrMWhiVXY3QmdVVkk0MEgrSUpqUmltOURkZVdndTgxSGdDT2FTZXVVWEJPc1VPZkVMcE1CTnJhVHpoMEFvblR3R0ZGaHNud3labnh5dXpBck9uUWQ0YzZMWnFXVXUwWU9VbkFzMlNKTFVhc05iNU0zL2VpU1ciLCJtYWMiOiIwZDE1N2IzMTBlMzQ0NmM0ODE3MDc2ZmNjMGI0YmY5YzBkNjcxZDllNWNmMzg4ZDNmNGZiNWIyMWZlZjA5MjIyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ims4Z3R4VkN0ZTBvaFVSVlpLUTB3Z1E9PSIsInZhbHVlIjoibTFYTC9BWjJCcGZzV2lld1VhWWlORlNxNUxpUjRNeGtMQVN1eld6cG8rMldGTEd3cEVSRFgyQkY2ZkVsMVVLVVNUTXhGcXhGTVJhOHVPbmp5RHZSZVNIZ2JLWm1XY0dCeCtBdGxhRjFLRmxZeDVnQTc0WmJJYXQ1Skh1b0o0WXRpWFJBcHMvcWJOMjUvWlM0ZHJEZE5pUDRFOW1qNHR4MnY1VEVJUnRxelllK2Q5ZlcyZUQ4bmhnQVQ2YmhhZnorV0dTYzlrQ0l3elkrTnIyRWxGZGhZZmJWWWhRZ1NwMFpKU3BFNEtlMHFaV0RtZXlaUXZrd25xblpydzFTVmljYmE2U1cyaDYxbTVwTEdSK0pVNnJtMllRQmZUZ2ZiQmwrN2pOeHRXVEVhU09GV2k5ZmhTYnRsZ3Nxci8yWDlzMExjQStnUDV0UkEwY29aNDJNUDhHblE5MFprMnFMUmZ1OWVHZ3R1M0p6aW4xYlJ3NG9lNWs0NWQ1QlVtZlVEQ2RpNTN2alhOOVdLdkdvQXJkaFEyTWRCRE9QbUhBeFZ6RGpzVjlWUkd3YmZKL3pqck96M0NHWmYwQm1zWmpZbzFrcTBMM2tmWEkxOHNVWWQvR1pvUVZGWHJPVHdrMUVYUEFFcEVYRndsV091Mko0VVlCeFJxZlgzYXM2ZWNpdWhmNTQiLCJtYWMiOiI5YjM5YjIyYmEzNjU1YjJjYzQ1NTdjOTA5OWU0ZmU0MzllN2I0MTQyOTdkZmQ5MzNkNzNjM2IzYWI0NWJhMDIwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlkzZ2IxZHdGQUpMMGhVWFpFTldLNXc9PSIsInZhbHVlIjoicHRXREZTQk5tWEM3VkVEMjhsYjRtaGc1WmRYbHljRHpHN1RnemtjRG4wZ1NNTU9JTnlxTDRrQVBaMGd3cTV6VjV0a3lhMU90SWpOakU2bENQMWhPS25zMkdiRFYxOENaR0Z0ejEzQXMwOWw0QTA1TnR3YkZNNDBqK2NOTm51ZERWU3dDL0tGelVEQ0I2WnRJVUZIMmI1U2hHaVQwZnorRHB6ekN5NVdaSGhJM3RlM3I3U3RLTCtjeklyYVFsSXZSSndKN1B3WWR6dDJDKzVpQW56UnVGYkJhaWtxaW1uMDJCb2dTSEF1QWJyKzQvSDBpL1pHampVK05xcjcxSmIwOC8vZzl4UEh4QXB3c1JaWExsdmx6STRtRVNOdVR4QjBKUnJGYmlpbXFybDBiaXQ0emg0eHJVT3BRdUxiNTNPY3dpT1RqdWpJOVRYMTRpNUdTU0s0emFzTkpqMEQxVXA4UnA2WU9QUXY0dFlMSmwrWnZFRnVJaXlxcGhjRzdxeGJwRm91N3puR2pCd0QrMWhiVXY3QmdVVkk0MEgrSUpqUmltOURkZVdndTgxSGdDT2FTZXVVWEJPc1VPZkVMcE1CTnJhVHpoMEFvblR3R0ZGaHNud3labnh5dXpBck9uUWQ0YzZMWnFXVXUwWU9VbkFzMlNKTFVhc05iNU0zL2VpU1ciLCJtYWMiOiIwZDE1N2IzMTBlMzQ0NmM0ODE3MDc2ZmNjMGI0YmY5YzBkNjcxZDllNWNmMzg4ZDNmNGZiNWIyMWZlZjA5MjIyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426234567\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2019459204 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2019459204\", {\"maxDepth\":0})</script>\n"}}