{"__meta": {"id": "X916ea80fb0e6c761f3ac052d419b4a5e", "datetime": "2025-06-28 16:02:43", "utime": **********.473568, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.037047, "end": **********.473583, "duration": 0.4365360736846924, "duration_str": "437ms", "measures": [{"label": "Booting", "start": **********.037047, "relative_start": 0, "end": **********.40707, "relative_end": **********.40707, "duration": 0.3700230121612549, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.407079, "relative_start": 0.37003207206726074, "end": **********.473585, "relative_end": 1.9073486328125e-06, "duration": 0.06650590896606445, "duration_str": "66.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45710072, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01865, "accumulated_duration_str": "18.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.43369, "duration": 0.0177, "duration_str": "17.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.906}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.459279, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.906, "width_percent": 2.681}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.464363, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.587, "width_percent": 2.413}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1704611314 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1704611314\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1504505615 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1504505615\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-813839698 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813839698\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1594811894 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126544321%7C12%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImQwSTFTdmd6ZHkrY0NVdmZQV3RwNGc9PSIsInZhbHVlIjoiUVN6UTh5Z0liN253ZXFBVUkydkV5amg0eW1aWFpvVWdXNTF3NEZoQTQwSitzRGlDeFJLNXVRbWVkMUEvS1JDNFphL1NzWU5rY0NqRC9ka1cxUU4rbFNzZDlzYXFnN1p4dC93NEpQdUlQb3RTL1JqZ2RieFNwTGtyR0VuNDBLa1lQOGlMNkdZYnBCTkpYUmwrdlJaWUw1R2hTN2x0dVFvaEtFby9ta3gvNTNBdVR2NG1TandUMXgvVzloOG96S2ZMNVg5U0I4ZEJScURQdEcrQzdaTnE1ZzBjbEJ4UitVSHhGU3kxWGFQQVBXeWsxeTNuaXpodDRzbXpZbk5Rbmp3alQ5blZkSUFCWTR6alRUeGIzYk5SalFic1dxQ3F5VG03U2hTK21Xc2ZjN00ydlNuVlpuRS8vK2hSZFo1bmNaYWQ2MlFiK3kyKy9YS2xRWTkwbnhCVVBVSkpTYkh3US9RS2lFTUxpc1ZkRHJsSFBiQ2xpdTlPWERHSHFUSkZYbWR6dGZtSXgzY09DdEpacHo5NE8wT1NwQ0ozNjV4NHJaYjUwUkdMdTdZR0NXV1ZtNmt5VS9CbDA4MFp6ZXhRTDI4TGkwMmU0QkxscklWSVlZVDIzMnRRNnpOMG10RWdPWjVPa1dLeEFRZVNKQ2VGcUJQS3ZPU2hGTTVnRFp3ZEN4TWkiLCJtYWMiOiIxYzUzYWQ1MmEyNGQ5ZDAwODMxNjcyN2ZjNGRkOWViOGVhZGZmNTVhN2Y3NzUzNGZiNjEzNDRjMzNjNTE4MjIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Inlzc1d0ZDdwZVRrNHNWdFJRdGhvY2c9PSIsInZhbHVlIjoiSG84Z3R4a3VKT2VJandaYWFNQ0laOTg2dUZZL1hCUmJIaEQya0dRcWtnNXp4dHRNOHZta1l4ZlFNTGZHaG5WVHcvaWE4SEVRb0htRVg0MnE2UVBYZk1qbzBDZkljUHA2TGNDR2hVWWFGNUJzZ0VldWpHejhyT0dnOHVRbm9wd3dpZUVTcDcyRUY0WDZoQjF3eU9KaVlDbytvRFlndjFVVFluNm9jZ2dBRTg0VUtsWnE3K1BFWGhCY0FvWDlJRHNzOGV0Y01FNE9xbkJ6dzdBa2poaEhud1BybitaSmtvUjN2enZZc1daZ0pOT1o3L1VUcEFMU2Q3bjFVaVM2WnVjU0h2YlIxS2lEcy85OTJSUzN2WXlyTXowQWRGbTdNRUptdG5lMnJJZzk2WUhEVnFUZjY3R2I2bG9oa2RIdGxXWTJZTytRWFF2QTNaVUdIdWF0R1RmZW1wVmdDZmYvUEM4VGx6aXpoakZ1NU9MMzlRcERHZzZsQkZtc29DQUc4MmR3b1hCQ0RZZ0g3L01VbTFTeUdWTUVGaGd1WldJMEhrTXhja1VYem0xNzY4d29WOXRRd0NseUlWNUw2c1FnQXk0ZE5pbWU4VXFIc213bGVsak1hMEw3THJhaW5VVWtvNXdJeU5WVnFQaWVnbFhQSGlZR2dHTTVXQ0g1V3p1cmhuZnYiLCJtYWMiOiJiMjhjYmNmYzc1MjY3YzExN2U1YmRlMGZkODFiMDhmZmIzMGVlMjQ1YzBhMjNiNDkxOGQ4ZTYxMzhlYzFmZmIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594811894\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1273414942 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273414942\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9MUWY1TXdxV0p5NU9QQlhoVjR2MHc9PSIsInZhbHVlIjoiZFFzZjNDdWtuZWlibEwvcVlzam1OT1ZBL1c4RHNNdFlQWVQrTVBIN0RSMVUydVhSelhyYzVuU0N1YVIrSWNPUnh1Wkl6dVp0WGEydzF0eitWTnhBbE15MnlwYlkvNG9VZklvZTJQSjM5c2phYy9jYzhJNTM3WUxDSlN4M2hERGJtZ0NnK1FTYjNOZWM2aDVvcWYyR0NxVkZYN0ZsbVlMVzh5QTJtMG0rd250ejlscUtzeGlWZ01obVRZaTZXNlRiMXFQTkZNWjZaUTZVNzlYUHpxazNabEs1bzU4TGg4YUVsci9IK2dBUXNvVUQ4ejkvdFoxTUtIT291Y25OSzJ3TjdOV1ozdzFUcmlIR3hCTVpOS0tpNTluS0tiMExhUXBEbTM3Y3RQRE9rbnVlNjdFQUk1M1VPMVFpWGRIaENXMWJPTDhyZUtmT1Q5cFF4RllxTjNrcHNheUl4VDFJY3VLS0JnWWtlRW5IVE5aek5Tck4vYndNVGNaWEpHMWxPdy9xalBDMEtreTEvT2dLek5wUWYyVUdGbS9LZEtYaE0yeWJkaDBXT2kwUVB6a2NjNmVTM2VROXFUOTI2Ui9hUU5Pc3V3R2IvcFdlVExaSHkwV09FUFRVaTFaUjNuU1lIejkwZ0lrSlN5ODdUVVB2MjdCWU9nbHNWV0MzNTZEOEQyTnYiLCJtYWMiOiJmNGU3NzMwZmY0OWNlMjRhN2Y3Y2M3MzdkYzljNDZmYmQwOGM4ODNlM2M1NjBlZTBkZDUwOGZkYTM0ZTcyYTg5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhEaE1lMzZUeCtwOTMrRlFyZVBnR2c9PSIsInZhbHVlIjoiSUhxU0E2WmY3eVcydWdyZE9kaUt5aTBvLy9TcjZ0MlY4b2doYUJaQVlUcjN2bUFCZE1Rd1dOYldvbVF6Sml4VktoK2FXL3hGOG50UkYrQW92QlhZTjlNaFh1TmRHMVhFM2JvQjhqT0R5dE1WcElqK2FjYVZQcXN5SVZSREVuSkNKTjljK0o3TUJXRjVwWkhDeDFMWnF1SXlHcVlUSjQ0dnpCaHpJYStqQ2J3K1pPQWs5cXFobTVDL3Y1R1RnR2xpTUVXNDVPSkFjbjU5bXV1aWsyMmFFdE9qT0lmb0V0cHBiYzdBT3NiQ0p6UWxrZkdiTm1QeHB6aGxNWlBlZ0QyQnpZWGpJSURsQWdvL29BQzIyZGdRMVNtV2diS205RWZ6cXp6OGVJTHRVMlVIRlNTRFNGcitmUTdudUNJbW01aTd2YnQ2VzVGQ0hrdWRxb3lUNzNUOHFHVWRVckRSdFcwbjdzQ0NGWVVYMVRNckRFMWUyQmxSQ2t5S2RjNXRVajlCUjREZmREKzNTbktucFRrNnRLb09hcnNuV1J1SGh5cHhrQlZBWmV4aE0zUFBhYWdQcitzdDAvaVo3N1RGV3BmSU94R25valpDeWZlTFZLRTBKcHFXLzM3ZEZCMU1lbEVhKzFRK3hhM0lhb09MblBpK05kY2lQT0FXdlVIV0U3eHEiLCJtYWMiOiJiY2U1ZWYzODE4N2EwNWU2Zjk1M2I3ZDQxOTA5NDU1MDhhNDYwMTk4OTRkYzEzMDI0ODg5ZjU3YzcyY2UwZTA2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9MUWY1TXdxV0p5NU9QQlhoVjR2MHc9PSIsInZhbHVlIjoiZFFzZjNDdWtuZWlibEwvcVlzam1OT1ZBL1c4RHNNdFlQWVQrTVBIN0RSMVUydVhSelhyYzVuU0N1YVIrSWNPUnh1Wkl6dVp0WGEydzF0eitWTnhBbE15MnlwYlkvNG9VZklvZTJQSjM5c2phYy9jYzhJNTM3WUxDSlN4M2hERGJtZ0NnK1FTYjNOZWM2aDVvcWYyR0NxVkZYN0ZsbVlMVzh5QTJtMG0rd250ejlscUtzeGlWZ01obVRZaTZXNlRiMXFQTkZNWjZaUTZVNzlYUHpxazNabEs1bzU4TGg4YUVsci9IK2dBUXNvVUQ4ejkvdFoxTUtIT291Y25OSzJ3TjdOV1ozdzFUcmlIR3hCTVpOS0tpNTluS0tiMExhUXBEbTM3Y3RQRE9rbnVlNjdFQUk1M1VPMVFpWGRIaENXMWJPTDhyZUtmT1Q5cFF4RllxTjNrcHNheUl4VDFJY3VLS0JnWWtlRW5IVE5aek5Tck4vYndNVGNaWEpHMWxPdy9xalBDMEtreTEvT2dLek5wUWYyVUdGbS9LZEtYaE0yeWJkaDBXT2kwUVB6a2NjNmVTM2VROXFUOTI2Ui9hUU5Pc3V3R2IvcFdlVExaSHkwV09FUFRVaTFaUjNuU1lIejkwZ0lrSlN5ODdUVVB2MjdCWU9nbHNWV0MzNTZEOEQyTnYiLCJtYWMiOiJmNGU3NzMwZmY0OWNlMjRhN2Y3Y2M3MzdkYzljNDZmYmQwOGM4ODNlM2M1NjBlZTBkZDUwOGZkYTM0ZTcyYTg5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhEaE1lMzZUeCtwOTMrRlFyZVBnR2c9PSIsInZhbHVlIjoiSUhxU0E2WmY3eVcydWdyZE9kaUt5aTBvLy9TcjZ0MlY4b2doYUJaQVlUcjN2bUFCZE1Rd1dOYldvbVF6Sml4VktoK2FXL3hGOG50UkYrQW92QlhZTjlNaFh1TmRHMVhFM2JvQjhqT0R5dE1WcElqK2FjYVZQcXN5SVZSREVuSkNKTjljK0o3TUJXRjVwWkhDeDFMWnF1SXlHcVlUSjQ0dnpCaHpJYStqQ2J3K1pPQWs5cXFobTVDL3Y1R1RnR2xpTUVXNDVPSkFjbjU5bXV1aWsyMmFFdE9qT0lmb0V0cHBiYzdBT3NiQ0p6UWxrZkdiTm1QeHB6aGxNWlBlZ0QyQnpZWGpJSURsQWdvL29BQzIyZGdRMVNtV2diS205RWZ6cXp6OGVJTHRVMlVIRlNTRFNGcitmUTdudUNJbW01aTd2YnQ2VzVGQ0hrdWRxb3lUNzNUOHFHVWRVckRSdFcwbjdzQ0NGWVVYMVRNckRFMWUyQmxSQ2t5S2RjNXRVajlCUjREZmREKzNTbktucFRrNnRLb09hcnNuV1J1SGh5cHhrQlZBWmV4aE0zUFBhYWdQcitzdDAvaVo3N1RGV3BmSU94R25valpDeWZlTFZLRTBKcHFXLzM3ZEZCMU1lbEVhKzFRK3hhM0lhb09MblBpK05kY2lQT0FXdlVIV0U3eHEiLCJtYWMiOiJiY2U1ZWYzODE4N2EwNWU2Zjk1M2I3ZDQxOTA5NDU1MDhhNDYwMTk4OTRkYzEzMDI0ODg5ZjU3YzcyY2UwZTA2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}