{"__meta": {"id": "X4a5911662a8c2e3256ed853d2964a3e0", "datetime": "2025-06-28 16:01:27", "utime": **********.063858, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126486.594478, "end": **********.063875, "duration": 0.46939706802368164, "duration_str": "469ms", "measures": [{"label": "Booting", "start": 1751126486.594478, "relative_start": 0, "end": **********.002691, "relative_end": **********.002691, "duration": 0.40821313858032227, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.0027, "relative_start": 0.4082221984863281, "end": **********.063877, "relative_end": 2.1457672119140625e-06, "duration": 0.06117701530456543, "duration_str": "61.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831872, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006379999999999999, "accumulated_duration_str": "6.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.039418, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 29.624}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.050096, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 29.624, "width_percent": 7.524}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-st%' or `sku` LIKE '%<div class=\\\"product-st%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-st%", "%&lt;div class=&quot;product-st%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.052905, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 37.147, "width_percent": 62.853}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1746388346 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1746388346\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2046720262 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2046720262\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1491960284 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&lt;div class=&quot;product-st</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491960284\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-252151807 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">49</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRCWE9OTGNwalI0dFluMjJocUR5T1E9PSIsInZhbHVlIjoiUllSZ09pMTgzaHFNNis5MGRNNkRjOExLM25CTWdNQjZITExFa1lwYjgxRTRYa3dPQnByU2I2LzV0bUlXYTVpOGF2blgxaUlrQnFFeUdabW5BeERRd3hKMUJRQWt1ZXROUFlWVDNwWFB4UEZiT3lIcWN2b1hRWThKQnAvb1JLTWIwRFZoN0lEaU95NDJ0MG9UbXlGcGlvZytSVHJTb0dlaHVGUE5sVGZyREZWSnlpVWtaVWp6eEh6VVFsbG80RGJVRFVGdUE2QzhyM0hVY3EzY1NueGk4bWwrUHNGc2xvTGJHREZENVdpZjMwNis2cU8zNzZUNnZ2SjA2UU5Fb1BaVEV0Rkpzam4zb1Y1UWRueTdidGVOdEtEbzZKUzZxRVZuZmd1YnQ0WjNhMFBqaUh2RjJvU21pYklubC9KUmxOM05mVzZWMUdzSmU4Umk2OWhYSTNJbGZOK3MrTjd4WWFzd0JlRUgvUEJudzRsRmZaUEJFd3J1d1lnbnBPNWVkU2JMVXI5eTF6RzYvV2QwV0hGd3VBQWJWanROeU1OY1htMmlWQzFvczB3RUxmY3FMbDUxbElwOU9jZTRzOWxYdytjWFJMcE9paVEzNFBFSndVZEVDZEdWRFpRTm9UaEZNVVg1ZlhTeEFUSWxoc3J0VlovTVFCdnhDcnJrNjRnTDJGeXgiLCJtYWMiOiIwM2MyMTI3ODJjY2Q4MzY4ODQyZmU5YTc5ZjRmZTQwMjlkODUyYTI3YmM5OWIwMTdkZGFlZWUwZWE3NzI4YjUyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtkVzBabStGMFdxaFRoK2EzNnN3UFE9PSIsInZhbHVlIjoiVW5QejYwOGN4eTMrRWJjbGd2YzJCcU1FZnBFQUJEMUp2eGMzSnBQbFJlMkhodmlhNUs0UHdhTWlraHI1bXJWVkV3ZTZJWUhwZmpOa2J4MmcyMUUxcmtqWWtlZTFPaDh4MkNvM3BiRFhTY1dQVGp0OHc0aGNicGRFalRjWTl6aHkvbmsrKzZiRndKbTkrTkVYbmN1SC9hMlVOVUJsYXpoN2ViSXRiVnJ4MWQ1TUtmcjNvRFVRTUZIdzZhOG0zSWs1Z0x0WTZXeUdiSHk3c0htSXlyT29BeGNKQjJWdURBeHRTRmw1Snh3M1poTXZCUEQyd0R3QzA4Z293REZWUmxGdU9iVHhCZDFUR3lHdVBJMFlZY0d0bnpTcVpFaWQwWnJVcGpJaGR3aWdTdHNwR3RscUNwejlJRnpBcWI4cjh4cGdnRVd4TDltZnhQRmpJQjRoT0pRU2JCOER5ck9acFRJYVhhMzlIYTQxS0dpalBRTmhZWk8wd1M3YW1pd2xBSVpMUzBZOXhxU3Rlam9pd1pqOGFIdkVtbjg3bTcvclpFUUpVYTZna2c3aE0yQ3JwU0xRTndaYlA3WmRnSGhxemlyVHk3UWVFVW9zazVIcHp1Vk53S09zYlB0dEZqZlpBQWlsVnRkSGswMGZFMFVPV1Q3a1BEdmFZVGMxR1h0RTV3NkoiLCJtYWMiOiJjMjY5NzE4ZjY5ODk4MmU5MjcxYmZmZGEwZTE4NmZiNzZkNzIxYjUxMzM3NjYyN2ZhZjQ4ZDVjMWFmZGQ1OWRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-252151807\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-803225955 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803225955\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1512407212 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijd6eXgwQWM0dE1DQy85TUdYaHV4UGc9PSIsInZhbHVlIjoiV3JlQlBKeWxkTXUrL2RCRnVWY2k0WVZjemVZVzV2S20zTVpiQ3BBUmplK1dma3Z3NlVXUUV0VVAwb1N5UzNjYjhiOHVYbjFRb0lvSkxFdG9sMlRNRlMyRXp1aFFSdjQrVlR5SEhmOHhrcFlRRU0zUEZieHlaejJtWk9rQ3RLaTZqUVQwN1c2cERmWmJtS3NBVGRPb3F5dCs0RnZDRmJhUnNoeXo2SkhLTHA2andSdDJRbndVZi9mQTAzMFBVaVZ1UG9QSmc4YXhsWnI5WDBhK0R5b0cvdnl0Z2FGL3FEbXhaUHRrOVBqbE9EYjZ3SGxmUnNvVG12QmQ0YkNUU2lhamwrQTZieGxjREkybGRGNnd6UDlpbUNHanVJaXFPUmRNZThyYlRxd1VwbDNwNjFCOEM1Wk01Mm5iRS9kYzNNMzg0STFKSFN6aEM0MEc2VjIyYW9TZ3Fqd0VGVS9Xd2xHNlk4cUI5MEdZVkQvaVoyQ0Z0eVpzQmszZmhQTy9UMzFWUGxsVkdSVzlTM2J4RXpqVWcxWnBCQmVKMTdvVVVlQWhJbDJYK3F5Wkc2S0psRTlHdGtteFZSRGw5Z24zdllkSFJFRWxuYTQ2OE1Nb3BhcFNCdzJzU0JtT3V6UzJieW0zNUUwNGd0TmlGbmh5VUZpVnhUUGRmUlYrckJSejcyc2UiLCJtYWMiOiJlNTc4MDZjN2M0ZWRmYjczMDVkN2ZkZDI1ZDViNDIzMWI3ZDNmYjE5MDZiNmQ3OGZjZDJkMGRjNjIyNzMzNzg3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjI3MU1nSFhlY2FUdW5BTkdvMWZxK0E9PSIsInZhbHVlIjoiajYyTVYxQWx2OGd6MXdFMTR4Q1Y4NC9sa05HcUl6U3k3azl6NHVMZGhCanFlb05ZMVBReTlzcWFtT0ZleUNJOUdZcStBSldPWXJyYkNKVzBSVGJtY3Bibm9USEI1UFlLdzc0bE5QVVBRSGFvWHg5Y2ZaM0wxTkpQTkFOVkppSmJ0c2pWMEdqWmpoR0hIa0V5V21pTjM4ZVVaTWt6dVBnQzVnMFZXSHpvWjRPaFZQTFFPRm0wT1l2YzYvM1JqMHNabWwybTZEOHNrbTR3Y2hMdStvNmRPL1AwcFo5QUtWdzV1SG1xODlTOTF5MGRnSVNlTC9wUWZ3My9qUHBjbzFUckNvSUVZcGRqZHpKODFuWHdodVJ1UWpNNytPL21ZTEVrcTc0MzJqMVNMOEo1ODFFOVNDN1VpbDVvcEc2SkRUSU94UW9wcWZ1YlUyT1Y1OFlpOHZRYmd4WTg2MEo1YytpN3A2V0htWVd4YWk4cUlsWkRPNDJQSnp4aFZKaWE0SzdUYldxUkJsQ1VwSTVQKzV4c2o1ZUFlVUc4VXh3U1pRMzB1ME03bU5meFN4WllFZkhOVi9xVXMrekFEZGxpZm1LNTg1bzdzVmhZMWgvci9OeGV0SEE5NVBSc2hiVmoxNmx1UlhPQW1OZ05SM0FhOElzSkUrUTNuUlJLSDY1MFZsMisiLCJtYWMiOiI3ODdjMTUwMzFhOWVlYWM3OWYzNjdhOGI0ZGYyYjIxMzRhNTUzZjk3NTlkYzNhYjgwNWZkN2E5NjQzNjE4OTg1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijd6eXgwQWM0dE1DQy85TUdYaHV4UGc9PSIsInZhbHVlIjoiV3JlQlBKeWxkTXUrL2RCRnVWY2k0WVZjemVZVzV2S20zTVpiQ3BBUmplK1dma3Z3NlVXUUV0VVAwb1N5UzNjYjhiOHVYbjFRb0lvSkxFdG9sMlRNRlMyRXp1aFFSdjQrVlR5SEhmOHhrcFlRRU0zUEZieHlaejJtWk9rQ3RLaTZqUVQwN1c2cERmWmJtS3NBVGRPb3F5dCs0RnZDRmJhUnNoeXo2SkhLTHA2andSdDJRbndVZi9mQTAzMFBVaVZ1UG9QSmc4YXhsWnI5WDBhK0R5b0cvdnl0Z2FGL3FEbXhaUHRrOVBqbE9EYjZ3SGxmUnNvVG12QmQ0YkNUU2lhamwrQTZieGxjREkybGRGNnd6UDlpbUNHanVJaXFPUmRNZThyYlRxd1VwbDNwNjFCOEM1Wk01Mm5iRS9kYzNNMzg0STFKSFN6aEM0MEc2VjIyYW9TZ3Fqd0VGVS9Xd2xHNlk4cUI5MEdZVkQvaVoyQ0Z0eVpzQmszZmhQTy9UMzFWUGxsVkdSVzlTM2J4RXpqVWcxWnBCQmVKMTdvVVVlQWhJbDJYK3F5Wkc2S0psRTlHdGtteFZSRGw5Z24zdllkSFJFRWxuYTQ2OE1Nb3BhcFNCdzJzU0JtT3V6UzJieW0zNUUwNGd0TmlGbmh5VUZpVnhUUGRmUlYrckJSejcyc2UiLCJtYWMiOiJlNTc4MDZjN2M0ZWRmYjczMDVkN2ZkZDI1ZDViNDIzMWI3ZDNmYjE5MDZiNmQ3OGZjZDJkMGRjNjIyNzMzNzg3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjI3MU1nSFhlY2FUdW5BTkdvMWZxK0E9PSIsInZhbHVlIjoiajYyTVYxQWx2OGd6MXdFMTR4Q1Y4NC9sa05HcUl6U3k3azl6NHVMZGhCanFlb05ZMVBReTlzcWFtT0ZleUNJOUdZcStBSldPWXJyYkNKVzBSVGJtY3Bibm9USEI1UFlLdzc0bE5QVVBRSGFvWHg5Y2ZaM0wxTkpQTkFOVkppSmJ0c2pWMEdqWmpoR0hIa0V5V21pTjM4ZVVaTWt6dVBnQzVnMFZXSHpvWjRPaFZQTFFPRm0wT1l2YzYvM1JqMHNabWwybTZEOHNrbTR3Y2hMdStvNmRPL1AwcFo5QUtWdzV1SG1xODlTOTF5MGRnSVNlTC9wUWZ3My9qUHBjbzFUckNvSUVZcGRqZHpKODFuWHdodVJ1UWpNNytPL21ZTEVrcTc0MzJqMVNMOEo1ODFFOVNDN1VpbDVvcEc2SkRUSU94UW9wcWZ1YlUyT1Y1OFlpOHZRYmd4WTg2MEo1YytpN3A2V0htWVd4YWk4cUlsWkRPNDJQSnp4aFZKaWE0SzdUYldxUkJsQ1VwSTVQKzV4c2o1ZUFlVUc4VXh3U1pRMzB1ME03bU5meFN4WllFZkhOVi9xVXMrekFEZGxpZm1LNTg1bzdzVmhZMWgvci9OeGV0SEE5NVBSc2hiVmoxNmx1UlhPQW1OZ05SM0FhOElzSkUrUTNuUlJLSDY1MFZsMisiLCJtYWMiOiI3ODdjMTUwMzFhOWVlYWM3OWYzNjdhOGI0ZGYyYjIxMzRhNTUzZjk3NTlkYzNhYjgwNWZkN2E5NjQzNjE4OTg1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1512407212\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-734474243 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734474243\", {\"maxDepth\":0})</script>\n"}}