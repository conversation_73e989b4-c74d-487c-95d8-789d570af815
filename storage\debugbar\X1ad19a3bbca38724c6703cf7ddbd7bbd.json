{"__meta": {"id": "X1ad19a3bbca38724c6703cf7ddbd7bbd", "datetime": "2025-06-28 11:23:58", "utime": **********.945068, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.482554, "end": **********.945081, "duration": 0.4625270366668701, "duration_str": "463ms", "measures": [{"label": "Booting", "start": **********.482554, "relative_start": 0, "end": **********.862394, "relative_end": **********.862394, "duration": 0.3798401355743408, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.862403, "relative_start": 0.3798489570617676, "end": **********.945083, "relative_end": 1.9073486328125e-06, "duration": 0.08267998695373535, "duration_str": "82.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45704928, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1910\" onclick=\"\">app/Http/Controllers/PosController.php:1910-1968</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.02509, "accumulated_duration_str": "25.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8968792, "duration": 0.01941, "duration_str": "19.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.361}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.92453, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.361, "width_percent": 1.754}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%123521%' or `sku` LIKE '%123521%') limit 10", "type": "query", "params": [], "bindings": ["15", "%123521%", "%123521%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.927188, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 79.115, "width_percent": 10.442}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (893) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.933672, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 89.558, "width_percent": 9.008}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.938533, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 98.565, "width_percent": 1.435}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1177832791 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1177832791\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2060400237 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2060400237\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1728695506 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123521</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728695506\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1416625530 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">27</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6Im9HOWw0NW1CTk5BV3BKQm1SWnVFdmc9PSIsInZhbHVlIjoiUG1qaTl6dkp5YzlVV0dJN1YrUFVvN1VQVlI2M2lBQ1VCNE0rNEUyQVFUQmZlbjF5TGQvbkdoYXA3NGlRaWxHZnRxY0NFTUYzcWRMVy9zR21WYm5FcVFVcjd2d1d4NjVpMlNuUXgrK1YrM0lDQWhHelVrZDRmNnNBem5PcmZIRm15MkE0bzR6ellPN2hrMEtLUzBaSklVcUNUYjlNcURDR2FSWEJ6NEl0UUZFWTladjRwUysvY0F2ZnNZU1F4djBuYm5UM3JPZGV1bERIVU1EV3dUYTVkN0s3RVRtTEpzcFFGZFcxZ1FiN3dWdXRFVndUVFp6R05sZjhxUEp0Z1dQTndFZkZCa3BOM2tLWlFvMUxrRGRXZTVqb2RyT3h6T3hnNktFSytPZFlzU3hETUY3d3ZocWpqOFk1RFZuYXFQSWNjNmowTWEvb0JJaUVPSEpJYUg3bzFXZUZYaUh4TGhicUZEb2t5aGFjUUxBbWhXdy9sN2tMQzU5TkVNbkxwTVpDVUNxMHh4dFVPT1VNRzhVOExNeDRjR0o2VmZVQVVpL2FuRS92eVZXNUlQTGRGMXdQcjVNbTB6TnFIc0FvaTZqNmlyWTd1ek9lQjExQTQ0bWlreGdmQU5CYzFTYjJEenBEYmw3ay8rNFIxUFlIbm81MmhDUUoxaFBoL3YzNEtlajgiLCJtYWMiOiJiZDY2ZjM4OTNjY2EzMTkyODJiYTcyOGYwODljOWI0NGZkOTc2NWRhODY0Y2FiYWFmNmVkODU5NGM1MWJhZTdkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImE1c0ZQZ29laXpFdVdaaXNLL2Mzc0E9PSIsInZhbHVlIjoiQUpXWStIS1FhUjVjTk1DdEdLTkM3SlFjd0htK2pxVG1mVXpRZlBsclAyMkxTY3B0THhEeHo3N3ZUZWh2K0xkS2h2UGNqRHRobTJ4WnhYRDZLSmZKcEt4MXJkOUt6SXBVZTR5WWVuenp6aUVLZ2s1b1hscHlDcjlOS09odmlMOVdaQUlza1QxWHFKak1SNCtGR0x5MGJDVThYbGZQU29MMEFvT3l5amJHWHFweHhjT3hVTjJJdU1hUHZ0WDNhL3NMWG9wMzdVeGM0UlltaWNwdVc4akFJdk93MzNST1ZRUXFnVzdXcmJqRHF1c1pSVDY3bjJuWXZQQzFiaTRSMzVjd3ZGZSt4Q0xHTVZ0Z3I0QTBYMDZhcE4ySHZ5OThnNzJvUUZTY2ZFQjlYU3VmRVR6bStKZi9Uby9ZTkphZ0Rpb3paYk1kQkxWMTB5UkxNZVdrY2JYTHo5QmwwUU5zR05RV1BlVjVQZEZ5MWJjUnIwTlpqMmJZSjJrTmw5MUEwL2VnQ2RyKzl3TG1rSjAwRzVCZGd6Z3djT1dOeWpNSnM3b25IR0ZXaTJXUFA2eEQ0Zi91TXZWcUYwWjh4Um0xS0E3RTdSV3EweDd2amtxUU56SjJIUlRKQUd2NmZwbTh2enM2ZGswdW9MODYzUWhROGFGN0x6T3Jkb29MMWthcUthdloiLCJtYWMiOiIxYjgwZDFiYmZlNWYzMzc1MmUxYWFhMDFmYTMwM2FjMTkwNmZjMDk1YjBlOGViYmNjYTY5ZDU3MGVhMWE3OTQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1416625530\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-510264542 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-510264542\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-191937965 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImUrWmRHT2hNNm5lbkV2emFDanlPVUE9PSIsInZhbHVlIjoiZWpUWGJtL1I4cXRWalYyVTBiUjFjM211dXg0MDJmZ0QrMWVaMXlQTzdMSnIwRE5USGYrWjhJVGxzK21yZ0tyV1gzcGRwVVFUS25tUHZDTTVCV1VWeU1oSUJQOWR5MHAyeXg0eXczeXNYVlJRNHVZM0NNSVNZZjd2ZnF4WkJjNjRkazI4SVNQQXU3V2NRc0pIUHhzMGhNQmx3SHI0Zk1ISUFjZWE1ZlpFV1NFaUp2QXJVTVlVcTIzZ2JrM2JqVFp3NlIyMCtqWHo0UGovandJQURDdm9VK0E2V3I2cC8zOUJVU01wMTFSYmhHdTZlZWVraUhzVUxSTnZySCtQNGJuMysvYzJ3N0JnSVRMUFdjK2hTQThPdExvbE14VXpzbjl1MC85TkZSeEF2eXNNMWpITDhOOUNrbzEvcHlqK3c1NXNjOUdOU3pXdE90L2RQQUx1RDR0MUFkaUMzc3VuQnd1RFltZmFEWE5vNW84VHVtMGVZTkcyTWtzS3dNUjNaYUxUZlAyS3d0VXJnWnc2TTBNUFF0SUJMdm1BTmdtVFFJVE8rcE5UdEVDaW1XOXFoNUhxK1VhZk1LVlc4TGN1MUt3T2pkcngvOEdJTHRTd2srS2VrdHlrcWd0NlljRXh2THl6SXNIZ1ZuOHZFRndBdHRGMTdLSTJGK1dmMmQxdEM0VkUiLCJtYWMiOiI4ZTQ2MTIwYTIzOTkwMjgzZTBmNDNiODY2NzVlMzI2MDZmNjM2MTA5ZWQxYjQyNjMzZGM4MmFiOTRlODA2YzQxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlIxR0o3UHFGeEtPelBVODRtNFRJZlE9PSIsInZhbHVlIjoiWE9wSmQ5MGxvaDkxbkpUb2taemNnK2JqUUJOaHcxcC9vMFZCOERSaU5uc0xwbkllUlI3UytrY3RXYnhtUXE1VjFnWi9hbXpObVVWNnJuY3dCYStZQzZSOUR6ckRSbFZXamttdE0wYnVlaEpTVmZGd21HdU5NRE5FeXV4cDZjZjR0dUtBY0U1dFhkZUZVaG5jOEt2OVhJNzJTS0pmVHY4YXJVNWd5ZXdBTjdhOFg0YmVqSW5oejlGbDlKRFVUMUVrZm1qcVkwRWJ3azdpZXdnUnIwMGxNVTlTY0drcThyLzc4cnlXYVlCTlhVaEVRTGN2dXNUZC9qSEN5amt3M3NpcXdNc3pBd2lKWEVmb3dnZldTakt2OVc3RlpNamNRNzNTVGQxZzM2bU5XYWVCeVQvTWZESnZVV3VwQXc0Z1NYenNPeDZUSFZic3RjeHhDOGcvWHIvWUEyc3JEU0NjamdOcUc1UHhFZHFxTVBWWUg2UHF1b3JZK3l2NkN5RU5JaGJ1WFNpSEtEdlEyNmVabkhhMVR3REEyamk3ekFJYlNpQkt6SzF2Q2hzRkpHR1ZPNGlxcDFVNlpoRU4rYlhXbW5vay9tZU9kUFlBL011ZVFZZ3B6RmgxQ0UrOEhTWjhqbDVXbGd1MUlyWE1BQml1WUEwN0h5cmhDMGxIUGlaVnc3WFEiLCJtYWMiOiI4YTA0OWZjN2M3MWJiZjc5ZTBiZmE4Zjg3ZDM1NjY4Y2JkM2EzOTY1MTAzYzc0ZDBiZWQyODAyNWUzMmVhMTgyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImUrWmRHT2hNNm5lbkV2emFDanlPVUE9PSIsInZhbHVlIjoiZWpUWGJtL1I4cXRWalYyVTBiUjFjM211dXg0MDJmZ0QrMWVaMXlQTzdMSnIwRE5USGYrWjhJVGxzK21yZ0tyV1gzcGRwVVFUS25tUHZDTTVCV1VWeU1oSUJQOWR5MHAyeXg0eXczeXNYVlJRNHVZM0NNSVNZZjd2ZnF4WkJjNjRkazI4SVNQQXU3V2NRc0pIUHhzMGhNQmx3SHI0Zk1ISUFjZWE1ZlpFV1NFaUp2QXJVTVlVcTIzZ2JrM2JqVFp3NlIyMCtqWHo0UGovandJQURDdm9VK0E2V3I2cC8zOUJVU01wMTFSYmhHdTZlZWVraUhzVUxSTnZySCtQNGJuMysvYzJ3N0JnSVRMUFdjK2hTQThPdExvbE14VXpzbjl1MC85TkZSeEF2eXNNMWpITDhOOUNrbzEvcHlqK3c1NXNjOUdOU3pXdE90L2RQQUx1RDR0MUFkaUMzc3VuQnd1RFltZmFEWE5vNW84VHVtMGVZTkcyTWtzS3dNUjNaYUxUZlAyS3d0VXJnWnc2TTBNUFF0SUJMdm1BTmdtVFFJVE8rcE5UdEVDaW1XOXFoNUhxK1VhZk1LVlc4TGN1MUt3T2pkcngvOEdJTHRTd2srS2VrdHlrcWd0NlljRXh2THl6SXNIZ1ZuOHZFRndBdHRGMTdLSTJGK1dmMmQxdEM0VkUiLCJtYWMiOiI4ZTQ2MTIwYTIzOTkwMjgzZTBmNDNiODY2NzVlMzI2MDZmNjM2MTA5ZWQxYjQyNjMzZGM4MmFiOTRlODA2YzQxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlIxR0o3UHFGeEtPelBVODRtNFRJZlE9PSIsInZhbHVlIjoiWE9wSmQ5MGxvaDkxbkpUb2taemNnK2JqUUJOaHcxcC9vMFZCOERSaU5uc0xwbkllUlI3UytrY3RXYnhtUXE1VjFnWi9hbXpObVVWNnJuY3dCYStZQzZSOUR6ckRSbFZXamttdE0wYnVlaEpTVmZGd21HdU5NRE5FeXV4cDZjZjR0dUtBY0U1dFhkZUZVaG5jOEt2OVhJNzJTS0pmVHY4YXJVNWd5ZXdBTjdhOFg0YmVqSW5oejlGbDlKRFVUMUVrZm1qcVkwRWJ3azdpZXdnUnIwMGxNVTlTY0drcThyLzc4cnlXYVlCTlhVaEVRTGN2dXNUZC9qSEN5amt3M3NpcXdNc3pBd2lKWEVmb3dnZldTakt2OVc3RlpNamNRNzNTVGQxZzM2bU5XYWVCeVQvTWZESnZVV3VwQXc0Z1NYenNPeDZUSFZic3RjeHhDOGcvWHIvWUEyc3JEU0NjamdOcUc1UHhFZHFxTVBWWUg2UHF1b3JZK3l2NkN5RU5JaGJ1WFNpSEtEdlEyNmVabkhhMVR3REEyamk3ekFJYlNpQkt6SzF2Q2hzRkpHR1ZPNGlxcDFVNlpoRU4rYlhXbW5vay9tZU9kUFlBL011ZVFZZ3B6RmgxQ0UrOEhTWjhqbDVXbGd1MUlyWE1BQml1WUEwN0h5cmhDMGxIUGlaVnc3WFEiLCJtYWMiOiI4YTA0OWZjN2M3MWJiZjc5ZTBiZmE4Zjg3ZDM1NjY4Y2JkM2EzOTY1MTAzYzc0ZDBiZWQyODAyNWUzMmVhMTgyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-191937965\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1484988197 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1484988197\", {\"maxDepth\":0})</script>\n"}}