{"__meta": {"id": "X8312ec10027a4cc2acba929c4bd3c185", "datetime": "2025-06-28 11:23:09", "utime": **********.791557, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.411439, "end": **********.79157, "duration": 0.3801310062408447, "duration_str": "380ms", "measures": [{"label": "Booting", "start": **********.411439, "relative_start": 0, "end": **********.742809, "relative_end": **********.742809, "duration": 0.33137011528015137, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.742817, "relative_start": 0.3313779830932617, "end": **********.791571, "relative_end": 9.5367431640625e-07, "duration": 0.048753976821899414, "duration_str": "48.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46266688, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0021, "accumulated_duration_str": "2.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.775151, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 82.381}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.785059, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 82.381, "width_percent": 17.619}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1145921894 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1145921894\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1421734582 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1421734582\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-832489251 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-832489251\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1539137265 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IkExT2dLSWhmQXZxTmhIME5NUXV2U2c9PSIsInZhbHVlIjoiS2FCSlFZODVFYk5EeU01bjM3MDhFYW0zTVZvZTJ2SzVvRmhzRzlEcGt0Znp3SGlSZU9LLzZjOVVJRWIxUTNkY2pQSzhvK2JLS0ZtYk5DOEl3cmZMdlZ4RnZ6M1d4TnVpOUYrVjZLZG5LdXJFL0VKT0h3OW1qZjF6T3g2eUcraXVhajV3ZmQxN1hDeERnN0QvUXpzNzBvQ2U0UHVxOEFyanh1UzExS3NuQktxc0FyQ3M5dTFLcExCdzJzVEJQbncrWkpvQ0ZVaTZiNi85QnNlM2Z2MWVBbVIxVXRZS1lXdTBoTEhTUXR2aTFVMTg5cFQwN1JCY0VLRjRwcDBGS0E2bWQrQkZ2MWgxRmhBeGNrbjBwQWV1MkdFVWtGb3VLQWw5T0dQMTZYTHh4ZUlSYjk1LzF3RWZRcDB1cDQvWjNnYk14Y0NqTzdzL1orZmdjMzFnL1Z6Z3NYOGp4MEVPZy9NTlppTkR5NUlRYzFMT25GblE1elkvc2ZvcDVua3BGejVEOFhMYVhjVWlTS2pOd3QxME4wTlduOGtyb2hVelZQWUt4SmZYdmNMelNhczkrQ0I0N1M3RnJLVWZ5MUR0UUdCNGxDRXRNSFlOWlRUcEZ0WXNqdlN4MDFDWVdCaTdWNFJHQnhaWTVtNENZZFZEdmFML1JZbmtGam9qMnRMb2d3L28iLCJtYWMiOiI0ODMzZTdiMzIyZTQyNDkyZWNjODM5NDljYjE1NDU3NWQ5YTU0ZjQzZDUzMTJlNTdmNDEwNjFkZWUyNDEwODFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImkwUjFmTHVoeW9ZU05PVDQvekNBUkE9PSIsInZhbHVlIjoiazhmTHd2b1VqZmNtTjhqUkNISmF5TENic1M5cGhSbHp3UU10aGxJb0hvbVdSNWhMSTBid00yYTU0R0lERFN4L05XWHVzT1B1YWN2L1B5S2hLQ29CM2NoK2tZa3BTNUhhUlFhOFpUZ0J1R083TU0xME9lMGdBbjNtM1NveWQxMDRmMnc3Zk9IS01vbXZQVXkrWS80cW12ZjkwZWdDTlRJMlh2Q25hejRDRHAxOGNROWtQT1VwY0tYRlBUVUViR3ZjV05rQTE2SVNYOUV0c2lXN2ZNWVFXcy9CU1JWWGluaE9yYTNxNE55dTZ1cGRDZVJmRmpEbExLeUR2MHRscjB6eTUvT2x3dHpmSXdmWU9Ed2h5djg0dkNQU1VyTERpRWxoaUV5T2VpVjBSQVhkYk82ZU11bzlwVHRkOCtheFN0bnhXc0t2VXF0Z3JZbU0wblZ4VjI2akc2T0toODhwMTgrY3p2S0xqYTFyU2ZDUVk4WllmMEtXRjFPY0o0b0llMTc1RzhZcUZNWFdDM3ZsbjNRZ3RtVklkeVc2RlFPTGJ3eS9WZkVoMDNKTnM1NjVrNjIzYnVpZnh0cjNQdUZLQW00TGJkaG1JMXlURklSL05YN2NvZjQveGx1aWdvRzZSV3UwNlpJRnNmQVkvRnc0bFh1MFRpNGZSeE5VMlZZSXhONVEiLCJtYWMiOiI4NjFjNmE1Zjk2M2YxZTJkNzJlMmZiYjUzZTgyMTBmMzhlMTc5MTJiNDZlMTE4ZGYyYmY4MjNhOWUxMmYxYTVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1539137265\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1906625691 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906625691\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-289581490 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRhTW80OUp0bkRwUi93aGFTakZFZWc9PSIsInZhbHVlIjoiMlFoUDJrMVJsZ20xQTFMaGt0UXBZSmh3anlWRldBS0lHRExyQU1ySk56RzBHQWEvQ0svL3pJNmdtOEkza0dPK09pc1NBZngyQXpNUDV5NkczczFmWWEzME9hNVp1eFQ2bnJBcGl4MzFqUGJuM0JVTkhhNWp1Z2xneDlXaCtJSHIwdWM3bVArc3JTY1NQVE1wOUVyN3J1WHM0T08wTFdtNjZUUnJBTStDUkRRNnJ5U0tPTHN3Zko4SXRrK1V0UG0xdkRhUnhuWFQ0ZEk1OFFLT2JZekZNc3VoV2ZBeG5NN09CcDhIR3ptaHFQcGcvbEx4dmlqeEJpbEFoTHRUUVBYQnQvY084MjJSNEZWbGxWbVZqMlkxR2NnSXZVb1NBNmF6dXpaZUVVc2VoSFJnYzZUV0hiS0hvTjJZRWwreFAraTJmTU9pWnJLYW04S09uV2JFQjI0Yk9jampac1owRklQNlR4cG5uME96QjBtNGV4RWR4dTlPSHB0dHB0YWJlcUtXTm5lcDBHbkUzN1h3SlZrM1BQOUFhK2dGbmk4d2d1L05ITW9qdnNhSXZWUWRPbVYrdDh6UWI0V0Y5V0wvdFgxUXZtMUVwOEdlb0JBTVpFelpuVEFicE1VbGRmUTA0TVRTNC82VVVtdUs4MHYyNlNBaDl2NUM3b1FYTDZ2enVTLzciLCJtYWMiOiJhMTk1NWI1YzViMTBlNTIzNjJjMmEyNDc2NzI1YmNjMWI3NzU3YWYzOWY3OTU1ODMwZjNjMjUzZTZjNmExMTYzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlFFQjBkdTRyVjBXTkRlOWo2dHhWNnc9PSIsInZhbHVlIjoiQ29sN2FGZ2IvY3dCOGpQMUExN0VOWUxVb3V2eDR3aVpQQWFHeW1EZkd5OEIvMVd6d0xhalp4OTdqcDZ1Q2hzRXZFRHB0QWs3cmp6a3F3ZUNQclAvSTl5cDJya0FuZGFwSENUMW13b3BmekNLcWkyQ3VodUdycm1KdU5mQU1yNGk0YW5oQkJYODFDSktrSXhGd25GWkg4VmZNS1FHRmRHYzBGR3FMM1NEL0k4MTlmSUZaWmNLdFg1SzIxeXFGRlBOVEtWYkhITm14a29aU0hqY05nN3lCbXRMc1JyVlpISmRqdkV3TWp2Q1BIV3gvV2JTZGltdFd6Z2crenVBTldHTGZEenhKQ1NyazZEajdxZ3ZRZjI1clJtTjFiQTBkN2JKWWdINkFFSXpoTGJzWWpVcWloand5UngzbjRPeXdINHBDbTE1VDBPUnZXRzJMYXVEUG4vSUhSSllXMXdiU0ZwcWtVL1cyRHRZMElCZ1o1Yld3bVVlQVVZcnFBUG42bHhORWZ6RnkrbW94aVlOQ3ZUUytJenRHZVFkZ29RK053Tnp6NFNMSm9Ganl2RVF2MDNmQnJwTHJWT2VGTDJMVmpwQjN1Y0cwSmJQTWl0UlE4Qng0NkF0RGlkd0N1WmxTUVJEV0k2ajNGdDhXRi9wTy9PR0xSQUlTS0M3ai9VRS9sTEkiLCJtYWMiOiJhNTAzNGU0YjMzYzBmNjMxZDhiN2RhMmYzYTQ0ODhiZDY1NmU2NTJjNDlkNDFlZjc1Y2NhZGUwZWZiOWU5ZDlhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRhTW80OUp0bkRwUi93aGFTakZFZWc9PSIsInZhbHVlIjoiMlFoUDJrMVJsZ20xQTFMaGt0UXBZSmh3anlWRldBS0lHRExyQU1ySk56RzBHQWEvQ0svL3pJNmdtOEkza0dPK09pc1NBZngyQXpNUDV5NkczczFmWWEzME9hNVp1eFQ2bnJBcGl4MzFqUGJuM0JVTkhhNWp1Z2xneDlXaCtJSHIwdWM3bVArc3JTY1NQVE1wOUVyN3J1WHM0T08wTFdtNjZUUnJBTStDUkRRNnJ5U0tPTHN3Zko4SXRrK1V0UG0xdkRhUnhuWFQ0ZEk1OFFLT2JZekZNc3VoV2ZBeG5NN09CcDhIR3ptaHFQcGcvbEx4dmlqeEJpbEFoTHRUUVBYQnQvY084MjJSNEZWbGxWbVZqMlkxR2NnSXZVb1NBNmF6dXpaZUVVc2VoSFJnYzZUV0hiS0hvTjJZRWwreFAraTJmTU9pWnJLYW04S09uV2JFQjI0Yk9jampac1owRklQNlR4cG5uME96QjBtNGV4RWR4dTlPSHB0dHB0YWJlcUtXTm5lcDBHbkUzN1h3SlZrM1BQOUFhK2dGbmk4d2d1L05ITW9qdnNhSXZWUWRPbVYrdDh6UWI0V0Y5V0wvdFgxUXZtMUVwOEdlb0JBTVpFelpuVEFicE1VbGRmUTA0TVRTNC82VVVtdUs4MHYyNlNBaDl2NUM3b1FYTDZ2enVTLzciLCJtYWMiOiJhMTk1NWI1YzViMTBlNTIzNjJjMmEyNDc2NzI1YmNjMWI3NzU3YWYzOWY3OTU1ODMwZjNjMjUzZTZjNmExMTYzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlFFQjBkdTRyVjBXTkRlOWo2dHhWNnc9PSIsInZhbHVlIjoiQ29sN2FGZ2IvY3dCOGpQMUExN0VOWUxVb3V2eDR3aVpQQWFHeW1EZkd5OEIvMVd6d0xhalp4OTdqcDZ1Q2hzRXZFRHB0QWs3cmp6a3F3ZUNQclAvSTl5cDJya0FuZGFwSENUMW13b3BmekNLcWkyQ3VodUdycm1KdU5mQU1yNGk0YW5oQkJYODFDSktrSXhGd25GWkg4VmZNS1FHRmRHYzBGR3FMM1NEL0k4MTlmSUZaWmNLdFg1SzIxeXFGRlBOVEtWYkhITm14a29aU0hqY05nN3lCbXRMc1JyVlpISmRqdkV3TWp2Q1BIV3gvV2JTZGltdFd6Z2crenVBTldHTGZEenhKQ1NyazZEajdxZ3ZRZjI1clJtTjFiQTBkN2JKWWdINkFFSXpoTGJzWWpVcWloand5UngzbjRPeXdINHBDbTE1VDBPUnZXRzJMYXVEUG4vSUhSSllXMXdiU0ZwcWtVL1cyRHRZMElCZ1o1Yld3bVVlQVVZcnFBUG42bHhORWZ6RnkrbW94aVlOQ3ZUUytJenRHZVFkZ29RK053Tnp6NFNMSm9Ganl2RVF2MDNmQnJwTHJWT2VGTDJMVmpwQjN1Y0cwSmJQTWl0UlE4Qng0NkF0RGlkd0N1WmxTUVJEV0k2ajNGdDhXRi9wTy9PR0xSQUlTS0M3ai9VRS9sTEkiLCJtYWMiOiJhNTAzNGU0YjMzYzBmNjMxZDhiN2RhMmYzYTQ0ODhiZDY1NmU2NTJjNDlkNDFlZjc1Y2NhZGUwZWZiOWU5ZDlhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289581490\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1144091796 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144091796\", {\"maxDepth\":0})</script>\n"}}