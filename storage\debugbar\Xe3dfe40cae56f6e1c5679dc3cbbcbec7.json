{"__meta": {"id": "Xe3dfe40cae56f6e1c5679dc3cbbcbec7", "datetime": "2025-06-28 15:00:18", "utime": **********.632694, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.189332, "end": **********.632713, "duration": 0.44338107109069824, "duration_str": "443ms", "measures": [{"label": "Booting", "start": **********.189332, "relative_start": 0, "end": **********.556011, "relative_end": **********.556011, "duration": 0.36667895317077637, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.556021, "relative_start": 0.36668896675109863, "end": **********.632715, "relative_end": 1.9073486328125e-06, "duration": 0.07669401168823242, "duration_str": "76.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45981664, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.598517, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.604207, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.621617, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.624861, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.00732, "accumulated_duration_str": "7.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.582373, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.137}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.585655, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 25.137, "width_percent": 37.978}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.590738, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 63.115, "width_percent": 4.372}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.599101, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 67.486, "width_percent": 6.967}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.604948, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 74.454, "width_percent": 6.284}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4090}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.613926, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4090", "source": "app/Models/Utility.php:4090", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4090", "ajax": false, "filename": "Utility.php", "line": "4090"}, "connection": "kdmkjkqknb", "start_percent": 80.738, "width_percent": 6.967}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4091}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.616826, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4091", "source": "app/Models/Utility.php:4091", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4091", "ajax": false, "filename": "Utility.php", "line": "4091"}, "connection": "kdmkjkqknb", "start_percent": 87.705, "width_percent": 5.191}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.618536, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 92.896, "width_percent": 4.098}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq24\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.6225538, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 96.995, "width_percent": 3.005}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8bGCesGqaoMPQwzhQFUSVF6PpqMDYWhqjXrguRvH", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://localhost/enhanced-pos\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1612752546 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1612752546\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1481756858 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1481756858\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-263480408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-263480408\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2011720448 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1846 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImJicERja2pUWmY1NHBnUStzQ2hBTXc9PSIsInZhbHVlIjoiQmVlaFVIYkJoOVBIOTdSVUxTY09OOVA1bmpQU0NIUTgydjdaZnd4bzZmTjgrRWgxSVdoOCt6dDNIWE52TGQwRWF2SlJvMVJHNk13dStuSjFZZVRsZ1ByNEtyVjVCa3ZFWEYxK0ZZVGMzMlpxYU9OeXJZWlhldklUUGRFN3VZMVlSWHVCdDBKZzhtR2V3QlUrbXVhN094dURLRDMwY2haeG5zUDBzanVCN080b1ZvQzBGclVzYW5Ga05iK1o5eVh5dEdsbUo3VkJvaS9xNFc4S1FpZXg0YUlsSVRlZnI0WHg4bkQ5ZDNacjAzK2p0azNsb0o0all6NmNMalRRbU1pQ3VzQmxwVXFnandlMm8wU2pSWmtkbnE5cUpSbmV2SS8xTGxYelN0VGxFS29scSsxaUYzMTc0dHZZamNIVk5oek1va3U5RmkvWmxONXVJd25FbXgzZnJYOStFcGFyRGFSTkUzcjVML2prUmlFSkpWTW5YRGJNZUp4MW1zSERGWkgrbG5jYlZDSS9UR0VFYkFFMW1JVTRtNnFaTnArK3N1UUFHYkVXaFpRbndYQVhRSlFNNFdBaFVmVnpVYjRhNDdPMjN0QjZLS2JkUjF4djJSbFFBZDRYSloweXlGSjVSc2VQaGhZUUlVQzBYeCs5UCtMK3B4RE5OdVNkYUtvWFdBelciLCJtYWMiOiJjOGI3MjdmNmQ3MTgxNTliNjU1OWEwNTM0Mzg0YjIzNmU5MmY2ZWZkYWFmYjRjNTg3OTZhYjgyZTI2YjQ0NzI3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpVMmEvOGF0Vnh4Ry9xMHpaTWZzL3c9PSIsInZhbHVlIjoiSWxMbVNWRWtuV1pTbGhPb0dhMEUyb3FDY01ONXpCa1lQYTE1YU9mTjZ4ejdGa3dmM3VYSWREdW1YSHB0bUZLa1RSZDJXbDhpaS9XR0tnQkdpbmpoay80eWZQb01aaTNJMlNha1o3Q1l5QncxVG1vNkwzNmU5NWVKTW1nT0RPSTVWbTNXUmNrUUVaR0I1djZmclc3RFNwN2ZBdUhNK3V6SUVUWG1FdDh5b21nQTdFWlp0bEtuQ3RCN2E5ZG1MRm55R045YUZDVFpzRVcxaTZlN2wyTnFXZzh1L01hZ3dBRXdjSnM4UmdOcU45c1RQMDIzNkY3S1ZkTnI3UTQyWlBKcUJodjNCTTFMU2xtRy9sU3o4dGprblhGSHlUVGZ5K1BHcHRkN0FKbjBscGhmeGFiNWMyZ1FVc215M2M1SFR4dUh0U2FUeGErUEtVZi9FODNvQWhVUFNkTzhLVEdkU2RmSVV6cUxIRENybGhlajBlSUxWZytpbmZvNzdYc3VCbUFNcXZGdUlXbDdocHZHc0ZQUnlxVXNRdmk3ZEhRdGJmZy9YREJEbjlzb3RXTVJCbGZxQkxHYkRPdmRaM1FSdTRGWW80cmFOYW5xcVQ4dllVbDhKc2daY1V1UUNYbkpRRUVEMWpTUHJiUUxsWkZ0VVpoeEsvb0MzVU8zMmNDUDB5TEUiLCJtYWMiOiIxZDQyMTcwY2Q0MjFkMzhmMzNjZjg0ZjhjOWJjOTZmNTlhOWJjZjk4Y2JiYWI0OWY3YmIzYWY5OGI4NmZlMzFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2011720448\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1623119125 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8bGCesGqaoMPQwzhQFUSVF6PpqMDYWhqjXrguRvH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Yv3t2F3Cuf8tBFeQi1W4f7pI0PV2AdnhhHyXe6dW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623119125\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1243397611 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:00:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNBajAvaGh6QWdxZ2lrbG5Ldmk0Q1E9PSIsInZhbHVlIjoiRVR0WFkzSTFiNjF0bllVQTl6cUsrTmMxYjhpUUw5VVpGNE1GK0xSMjlRZnpKZDRFUi9HLzhpRExvcWt1WTFENm5oK0ErSDZKZnE4WTBCd0NiRlNUS1VWdVluWEx2WUMydFNrdzg5a2hBeCtJbjBWNklvbVlKUHltZ0FpT2thRC9UZ0pVVGtFUDEwRnM5RENSb2Y1OTk1OTJVeFoxV1I0K2J0VWhnSkQzbHdvS29YQnplV0UySHllV0pjQ2lSdkh6bngwVHZaOEphS1kzMURyUWFWQkV6ak15R3ZvTWxkVzFTcWdUSjNubEc5YlFKZlBUNVJnbjBmRTNmdTRUMm9Qd3FkbkhydDBOR1paVjNYR1FOTjc0QkdVY05tdldka1AvTjVqTnNleEFGcjRkUnJ6TlhMcDVUT25PSVpxRUsxWC9yY1d2STl1a0cxTS9lV3E0YjZxem5Gekd2MGZ2cFZHRTJRZGxTb1dPam54a0orRWY0MVNSVjBvNFFWdE4zMEdyS1hpS2xXd0gzdG5mWnMrMmtDTnRqTGwrQUZCNWZSdXdFN3FmMkplTFFzYTEyS3ZMdk1MMmJrVDJ3ZDJ1UFl2Q3p4RjJhMWl5STAxOXM5OUJaa2dSUS9xTEtNeURGN3RjM3AzUUdEUEVFTFFFWWRkZzFJWmIyZGhMR216ckpsVTEiLCJtYWMiOiJhMjY3YmUzMGRhZDM3MjQ2ODM5NWRhMDllNDBmMmM4ODY0NDlkZjEwYjFiYzkxOWE3YTFjZjY1NzU4ZTMyZDMyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ims3dlZpNGVKTTJYR3ZVZmtKNUhXK2c9PSIsInZhbHVlIjoiVlhBaGZnOUROQUk5NW0wSk1vRVc4dm5HK0ZLK0NQR3dOejBHaHByOExoL1NFS04rOVdWcCsyc281am9ydjN5ek5KdUIwbThCTEppc25lU2x4ZXhKdVUvZTBCRFdhU21NR2NDSS9TWjVFQWNsMHllVkpzR2l6ZlZaK1daU3Frbm1KWE5EVEloSXJvVUtqR0xIWDUvbU16d2tMREc0WjRsTUlYbkgrN05TOFkxYzRDaHBFUWR6THVnQWdLd3JaZ2VnSHNrR01UUVBuWGt4MENzOXlqbUpRYU5SNlBPZTlETmVyM01GZEVjM0ZtQVluZjcvVWI4MmNJS0xGWFRVYWwvSHdzK21qNnRaSUtDN25GWjVxY3dqZGRESS9qUXI0S3hlOTllR0NZL1JhUHdaUkdXTnl4ckhHS1V2VE1oRmttMlZvNFBKeEhKK2ozQko3ZXJlK0xDY0N5V3ZzdE1QQk41NkgwS3Qvakk1U0tObDQzNkZBWHRVRXVhdG15aU9peHVtMkZ4eTBkaEtPZnFlYzVPcUFrZjRybDlTYXJJWnpsNXFoNlNTZklSdjVLcHBBTWpkNnRZQ3M3OFRBOUFTMzNSaHYyK2c0U1Y3eDBzQmtNTkZUNEZQYTRhUW9CelRTa3hFTmVtTkZsb0JGVU9pNDl4SHZ3ZWt2UmVKdUsreDR1V3QiLCJtYWMiOiIwNjYyMWM4NDg2NjY3Y2Q2NTQzNTE5YjI2ZGI4MmIyMzkzY2I3N2U3OGRjYmVmZmYwNWI4MWE0OTdiNzIwNDk0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNBajAvaGh6QWdxZ2lrbG5Ldmk0Q1E9PSIsInZhbHVlIjoiRVR0WFkzSTFiNjF0bllVQTl6cUsrTmMxYjhpUUw5VVpGNE1GK0xSMjlRZnpKZDRFUi9HLzhpRExvcWt1WTFENm5oK0ErSDZKZnE4WTBCd0NiRlNUS1VWdVluWEx2WUMydFNrdzg5a2hBeCtJbjBWNklvbVlKUHltZ0FpT2thRC9UZ0pVVGtFUDEwRnM5RENSb2Y1OTk1OTJVeFoxV1I0K2J0VWhnSkQzbHdvS29YQnplV0UySHllV0pjQ2lSdkh6bngwVHZaOEphS1kzMURyUWFWQkV6ak15R3ZvTWxkVzFTcWdUSjNubEc5YlFKZlBUNVJnbjBmRTNmdTRUMm9Qd3FkbkhydDBOR1paVjNYR1FOTjc0QkdVY05tdldka1AvTjVqTnNleEFGcjRkUnJ6TlhMcDVUT25PSVpxRUsxWC9yY1d2STl1a0cxTS9lV3E0YjZxem5Gekd2MGZ2cFZHRTJRZGxTb1dPam54a0orRWY0MVNSVjBvNFFWdE4zMEdyS1hpS2xXd0gzdG5mWnMrMmtDTnRqTGwrQUZCNWZSdXdFN3FmMkplTFFzYTEyS3ZMdk1MMmJrVDJ3ZDJ1UFl2Q3p4RjJhMWl5STAxOXM5OUJaa2dSUS9xTEtNeURGN3RjM3AzUUdEUEVFTFFFWWRkZzFJWmIyZGhMR216ckpsVTEiLCJtYWMiOiJhMjY3YmUzMGRhZDM3MjQ2ODM5NWRhMDllNDBmMmM4ODY0NDlkZjEwYjFiYzkxOWE3YTFjZjY1NzU4ZTMyZDMyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ims3dlZpNGVKTTJYR3ZVZmtKNUhXK2c9PSIsInZhbHVlIjoiVlhBaGZnOUROQUk5NW0wSk1vRVc4dm5HK0ZLK0NQR3dOejBHaHByOExoL1NFS04rOVdWcCsyc281am9ydjN5ek5KdUIwbThCTEppc25lU2x4ZXhKdVUvZTBCRFdhU21NR2NDSS9TWjVFQWNsMHllVkpzR2l6ZlZaK1daU3Frbm1KWE5EVEloSXJvVUtqR0xIWDUvbU16d2tMREc0WjRsTUlYbkgrN05TOFkxYzRDaHBFUWR6THVnQWdLd3JaZ2VnSHNrR01UUVBuWGt4MENzOXlqbUpRYU5SNlBPZTlETmVyM01GZEVjM0ZtQVluZjcvVWI4MmNJS0xGWFRVYWwvSHdzK21qNnRaSUtDN25GWjVxY3dqZGRESS9qUXI0S3hlOTllR0NZL1JhUHdaUkdXTnl4ckhHS1V2VE1oRmttMlZvNFBKeEhKK2ozQko3ZXJlK0xDY0N5V3ZzdE1QQk41NkgwS3Qvakk1U0tObDQzNkZBWHRVRXVhdG15aU9peHVtMkZ4eTBkaEtPZnFlYzVPcUFrZjRybDlTYXJJWnpsNXFoNlNTZklSdjVLcHBBTWpkNnRZQ3M3OFRBOUFTMzNSaHYyK2c0U1Y3eDBzQmtNTkZUNEZQYTRhUW9CelRTa3hFTmVtTkZsb0JGVU9pNDl4SHZ3ZWt2UmVKdUsreDR1V3QiLCJtYWMiOiIwNjYyMWM4NDg2NjY3Y2Q2NTQzNTE5YjI2ZGI4MmIyMzkzY2I3N2U3OGRjYmVmZmYwNWI4MWE0OTdiNzIwNDk0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243397611\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-763944249 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8bGCesGqaoMPQwzhQFUSVF6PpqMDYWhqjXrguRvH</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-763944249\", {\"maxDepth\":0})</script>\n"}}