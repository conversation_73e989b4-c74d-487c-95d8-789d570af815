{"__meta": {"id": "X8c2c325a97e8189c10e191b197d5d01c", "datetime": "2025-06-28 15:19:20", "utime": **********.443084, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123959.943802, "end": **********.4431, "duration": 0.4992978572845459, "duration_str": "499ms", "measures": [{"label": "Booting", "start": 1751123959.943802, "relative_start": 0, "end": **********.364849, "relative_end": **********.364849, "duration": 0.4210469722747803, "duration_str": "421ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.364857, "relative_start": 0.4210548400878906, "end": **********.443101, "relative_end": 9.5367431640625e-07, "duration": 0.07824397087097168, "duration_str": "78.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45599640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020920000000000005, "accumulated_duration_str": "20.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.397514, "duration": 0.019940000000000003, "duration_str": "19.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.315}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4275181, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.315, "width_percent": 2.247}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.43353, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.562, "width_percent": 2.438}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1415460107 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1415460107\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1245057230 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1245057230\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1510495489 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510495489\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-993172604 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123956711%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikt3VXpLVkZrRmQwR0lYNGwxZ2N3Znc9PSIsInZhbHVlIjoiODZRZERzUkRvU25zeDN1SWorbkhTQUI2dWl0Qm02MUJ6T0lTR0RnaFQzT0RDVzRSY3k0TWVxUnNmVmpSOEpSaWJPR29lRVMvMVpZR2dKUjNnTzduNEl2VEpxTUFvYlAvWEQ3QTdVMUFlZkRJV1BPMkpLbFhwUFozTStlbGN5TWIwdC9xOUZxRnFINGxjU2lMV1BmZExKZlpWV08rUktqdXQ3YXJKZ0VndmRXN05NdEdLejdXVEFPb2JXRGFFTDMwMnBDWmVPdW0yUVAwMFY5eDRmbHNLZTFEbHZrYzVUTHpPNnVRUXVWTmNUeEtHS2xScHFnMDQvVmhjblozVW5PSWhQMzdCa2oxS0kzNE1ZWEQyaE0xSVNJdVVycGZHV3EyMkpTcmhieXR0cWlxblF0Z2E5ekt4Rnk5Q08yclcvcUdaZ2F2UCt0TTNHQldwVnNkT095Q1pkbFhkdldNMkxMVjdTZHFtamlabmFwQUxGdFVpR2JXUTc4SFc3em0vVVV3NVZvWlpkZUtobGsrS0R2eXZzM2NLMUZYdDI0VXFXdzZ3RUJBTnIwUXBsWjJmUUcxVkN4MTI5UWhmdTlWN0NsYzd2SWdFMVdRZ0d6YVcxeFBIU2RmUW1MYkhhNnd4RTlNYWs0a1ZsMk5NdnphQkRQbGZOd01tbWF6ekV4dFZxR0EiLCJtYWMiOiJlNGQyM2QzNmRhODZkNDFlMzNjYTM2YWU5YWYyMGIzOTJmMTk5ZmUzOTdlNzA2MDEwOTljMDVkZDgzZjE2NGIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlPN0ZPMzd5RC8zQmpOaTVYSytoVEE9PSIsInZhbHVlIjoiWjgwa2JVOVp0Ky9qU1BIcU5PZGtzWUtjcityU3lqT3JDTlVjY0Q1dTROSkV1NWM0WTJGRFJRWHBndzFmM0RTdzRiNnFuSVBuWnFTK1I0MVBRNjJDZGpHemlkVzYrelNnd3RqWU0yZ3ZvWXh1dVdOVElFTzdQOEoxUm5tek9FeGpZeHlicFZ6N09JaFRrZzB2RHBIalBycHd3SzliaVdBeW5oVnhEUU1wcGFld3p5VHErVmM3azQ5OXNRRW5TZUJteWFJVzBQL0ozVjU2MFd5TW8yRXAraG5WTkdmUUVGYThTYlBQVTAzRWxLak1XSFl5b2djenhZbDQ3L3Bxdmx2TUM4L24wTjZUa3kvS1ZCeDFPV3lKZzhjeVh6VkE3U3BVM0FIVFZPS0ZMOUxmRXhGT2xaNGNCc05UWG1vanl3K09obFk0eUVsajdtdnlmYUlpMU9SYnJsOUp3UXh5ZlpPNGI2ZXBwc0JINVY5Z2cycWJDNWlLUGp1QXRZOEg2dXNlLzM5ekZtLzk2STViWWNCMlRLeG1nNy9oWTQ2c2ZjOWM4RUY2ZUZuNkxiTVVObFdoRFBmZzNqV0VITTRsd0NQd0F0d1dPcldQU2tYS01TTDJqYnZpd01pK1lFeDh5TXFaeXpON3VQZlJPbkRNbm5ONUpDQUlmRXFNUCsrM0gyUG8iLCJtYWMiOiJmMTgyM2U0ZjExYjRiMWIxZGU0NWFmMWJhNmJkMmFiZmUyOGRlZWNmMWYyN2QxY2Q5NzU1ZjM0OGE0M2YwMmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993172604\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2073664328 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073664328\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1116034325 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:19:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikc2NnJwVzBaWjNxeWVHY2FvTGE0dFE9PSIsInZhbHVlIjoidmxIS2FTNTYzVWVEcGs1QU5QaTRuNmRpRjQyTDU3alJXNXdiTDMzRHZ3M09mbkNYMFdlWE00ZmEwazJyRSt5eklGY0VobW85cWtLeFJ4OVlDUCtjSGFSYU1jYVltaGtBVDd4S3BhK3o2UVluSXg1UnMzZmsrVmlOYlJOQVJ6aTZMazV4T3VNNzB3dU9tZU84aUtrRFlVRURNK3p2MW1mUUdVUzhmUkQ2WTl3bi9kbGd4dzBqZlZwd2ZYcU9UdGRFRU5aU2gwV1RWaTUvU2laUWpiTjFyQUlyS2Urc3ByeG40Qkl0RE1mTDIrVjUrK3A4RFhBWGlXSU1kbURBY250SGFFNS9vcnFJVFZmQzNJY2hFbXpNbE9weE1HVS9vTFJhQzBhSUJPSzBGQU5WQm5JTks3Ky9Ld3FTbGUwL29rNnZRL01aUGVBcGdDa1pmbXFuT0VLLzFoTkxmeHRSUW9TampnUExkaDYxT2hLSjMyZEMzOFRNTzFIRUpUL0dFeTN3T0IrL0lzRFdHaHBnMWFyV0ZORGgybUIrSEY5VWdSV0NjMlRudGdqNkpWbmEwRzNMOVc1YjZtUUdYOFBmQms0dHExRTRhdUJ3TDZXWUZPZHZuV1FjVTZxeGJWSkNQY2VFeXlHckpNa2Z1MmhKWmU0SCt6ay9TbzhZVHJkUUltU0wiLCJtYWMiOiIxY2Q1OTYyZDg3MjM4ZTc0ZDVhNTFjZGNkMjJhZmM2ODhiNDJlM2RiNzA0MjFlZmJlMmE5MDBkYzZjNzEwMmRhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJ3elhIOG1LbDJIR2FkckkzMVdkeEE9PSIsInZhbHVlIjoiRm0wamNVNmdMUXlNdS9oMFNsYnRodTZZYTFwSCtkUDU0YnRzdHFFRlpPT2ltYVZIY2hBQ0hUSGk4dTRtR0lHcjFJRldTY3YyV3pJUTg3ZzFYRkUwVW81eUMvOWF1QlJoY3Blb3FpSW9kMm1nRHJIeGdvWmRRa0x6WFViMU9TZjhCRklyekRzMnpHMVcwd3VPd2ZyZkJXTmNnaVYvU0oyUDI1a09OMnZ4MnduRWt6VkpZZ3ZzeWxpbHpJejcwSDY3NnhnSTlBcmlSSkhzNWw1YWNodmxFYUdBNXBYZ2VTeUYrUW05bDdzTU1wNUFyWkthN05oeWNnV3BQUkdhMTVXUjZhQm55dVhxN2dwbEs3SmFNMzNrcUJVeDVQQXVWUnBBT2wxZ0NoemdpSko1NHp6RUNhaHJST1hUNlhNL2QwSWgyMzVQbTBwTVpjMEFuTkx5OFVPejRyVnA1R1c4K09UdEVqdW5HY3JyUUlBdEFxSmg1UjZjV05pbHd0cUhZUmFPZkx0bTgySjdJUU5lQWhjSWcyeWZkc3c3NjNYMnVKZzIvOXFRUkcxODhxZHZZbDdBR3hvOTArTkdNNFlMbERORDA2R2h6c0ZTNy9hNHdpWXVPdTZBL2JuNHQwdEY5OXZHV3JlVXZRK1Y1bVF0cElvNlNJRHpYRkR1M3c5NTFBYXMiLCJtYWMiOiIwMzU2ZTMzMmVlMmRhMmZiMjQ3YzdkNTZjMzhkZDFmOTRkYmNlOTY4ZmViZjMxMDllNGFkMzkxZWI3Y2Y4Y2ExIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikc2NnJwVzBaWjNxeWVHY2FvTGE0dFE9PSIsInZhbHVlIjoidmxIS2FTNTYzVWVEcGs1QU5QaTRuNmRpRjQyTDU3alJXNXdiTDMzRHZ3M09mbkNYMFdlWE00ZmEwazJyRSt5eklGY0VobW85cWtLeFJ4OVlDUCtjSGFSYU1jYVltaGtBVDd4S3BhK3o2UVluSXg1UnMzZmsrVmlOYlJOQVJ6aTZMazV4T3VNNzB3dU9tZU84aUtrRFlVRURNK3p2MW1mUUdVUzhmUkQ2WTl3bi9kbGd4dzBqZlZwd2ZYcU9UdGRFRU5aU2gwV1RWaTUvU2laUWpiTjFyQUlyS2Urc3ByeG40Qkl0RE1mTDIrVjUrK3A4RFhBWGlXSU1kbURBY250SGFFNS9vcnFJVFZmQzNJY2hFbXpNbE9weE1HVS9vTFJhQzBhSUJPSzBGQU5WQm5JTks3Ky9Ld3FTbGUwL29rNnZRL01aUGVBcGdDa1pmbXFuT0VLLzFoTkxmeHRSUW9TampnUExkaDYxT2hLSjMyZEMzOFRNTzFIRUpUL0dFeTN3T0IrL0lzRFdHaHBnMWFyV0ZORGgybUIrSEY5VWdSV0NjMlRudGdqNkpWbmEwRzNMOVc1YjZtUUdYOFBmQms0dHExRTRhdUJ3TDZXWUZPZHZuV1FjVTZxeGJWSkNQY2VFeXlHckpNa2Z1MmhKWmU0SCt6ay9TbzhZVHJkUUltU0wiLCJtYWMiOiIxY2Q1OTYyZDg3MjM4ZTc0ZDVhNTFjZGNkMjJhZmM2ODhiNDJlM2RiNzA0MjFlZmJlMmE5MDBkYzZjNzEwMmRhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJ3elhIOG1LbDJIR2FkckkzMVdkeEE9PSIsInZhbHVlIjoiRm0wamNVNmdMUXlNdS9oMFNsYnRodTZZYTFwSCtkUDU0YnRzdHFFRlpPT2ltYVZIY2hBQ0hUSGk4dTRtR0lHcjFJRldTY3YyV3pJUTg3ZzFYRkUwVW81eUMvOWF1QlJoY3Blb3FpSW9kMm1nRHJIeGdvWmRRa0x6WFViMU9TZjhCRklyekRzMnpHMVcwd3VPd2ZyZkJXTmNnaVYvU0oyUDI1a09OMnZ4MnduRWt6VkpZZ3ZzeWxpbHpJejcwSDY3NnhnSTlBcmlSSkhzNWw1YWNodmxFYUdBNXBYZ2VTeUYrUW05bDdzTU1wNUFyWkthN05oeWNnV3BQUkdhMTVXUjZhQm55dVhxN2dwbEs3SmFNMzNrcUJVeDVQQXVWUnBBT2wxZ0NoemdpSko1NHp6RUNhaHJST1hUNlhNL2QwSWgyMzVQbTBwTVpjMEFuTkx5OFVPejRyVnA1R1c4K09UdEVqdW5HY3JyUUlBdEFxSmg1UjZjV05pbHd0cUhZUmFPZkx0bTgySjdJUU5lQWhjSWcyeWZkc3c3NjNYMnVKZzIvOXFRUkcxODhxZHZZbDdBR3hvOTArTkdNNFlMbERORDA2R2h6c0ZTNy9hNHdpWXVPdTZBL2JuNHQwdEY5OXZHV3JlVXZRK1Y1bVF0cElvNlNJRHpYRkR1M3c5NTFBYXMiLCJtYWMiOiIwMzU2ZTMzMmVlMmRhMmZiMjQ3YzdkNTZjMzhkZDFmOTRkYmNlOTY4ZmViZjMxMDllNGFkMzkxZWI3Y2Y4Y2ExIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1116034325\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-475649815 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475649815\", {\"maxDepth\":0})</script>\n"}}