{"__meta": {"id": "Xa41f5679d338b68b48ee6e6ccb8766c9", "datetime": "2025-06-28 16:17:47", "utime": **********.293255, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127466.795397, "end": **********.293271, "duration": 0.49787402153015137, "duration_str": "498ms", "measures": [{"label": "Booting", "start": 1751127466.795397, "relative_start": 0, "end": **********.207479, "relative_end": **********.207479, "duration": 0.4120819568634033, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.207487, "relative_start": 0.4120900630950928, "end": **********.293272, "relative_end": 9.5367431640625e-07, "duration": 0.085784912109375, "duration_str": "85.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49596504, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01925, "accumulated_duration_str": "19.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.249747, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 8.935}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.260612, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 8.935, "width_percent": 1.766}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nAND p.warehouse_id = 8\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`warehouse_id` = '8' order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.264205, "duration": 0.01719, "duration_str": "17.19ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 10.701, "width_percent": 89.299}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-1653211394 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1653211394\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1481859970 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481859970\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-660463185 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-660463185\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-285787191 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; XSRF-TOKEN=eyJpdiI6IjIvQTV5VTFnMTM1b2FKTlFVajVaY0E9PSIsInZhbHVlIjoiYk1xc0xGVUdtS3k0REtFT2kvU0NYcHlrVTlUUDc5V3pCeGZoNlNnT1U0S1dmNGh1TFN6UjhNTjl3aTZ6cEpGc0JuVEpsLzJBdzhwVVdyVEJmNUNxdm5EZFNVdjI1ZldBZ2xOT3NydmdsMUp6NW9vS24zNThacDI5eThtNC9sVTNqRzUvcjJ6L3l5SXdvcDNTaGVDWEpGcFREZGRCMlQxS1dPNlcvbitacHg4RTM5R3RWanJmdVd3ZCt3U1FGS3Q0TGxrSWk5S3F1OHNJWGZjcHJtZ0FQaGhUS1RDaFYzUVZDSmtWRTlDbC9HV0FmbmtCNjBkR3VubklRU2w3bXJSaCtGZGVnOFp6RitHNmt5VHhDVVZWZHZOTFo3c1IyZWpCUUViVFlFc0FZcmR0RDZIWitjY0VDWDk1dEdHU0t3RE1KUXJOendKcUdUM0djUFRxS2VCdmdIMmNGaGtRRkNNZTE3U2ZPSmRLcUVaN0dLY2IwRHJWdldpaU5ubnBnZWl6SFpkWFFPTGRhSVRGVHZzMWowUUFrTk0yb2lVMmlxRE82WXNjL0tyaVJKR05QZHVqZzNUVUt5ZHNCdjlNNkNVak1nL0FDZXZ3czZ0NGQ5SkxPaDVBSklzYVAvNWw1eDFWdGFmcU9mYjNldmtTblU0b0JJZ2o2Z1duUnd0VStLN0kiLCJtYWMiOiIzNjE4YTk4NjIyODVmM2ZiMDc1ODEyMGUzNWI5ZmNkOGZhMzk2ZmRlY2EzMmU2OTkzZTAwMDkzMmEzZTdhZjc1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRBQzNJRlRHUHdVK3NaTVhhWlNhVXc9PSIsInZhbHVlIjoiK0pOZGlENUpKVTJUbGsxQmxlK0RaeUZrU283Q3drUVo2SXFxWHlrWTR0WUJUT29OOEN5QmsvNnkwd2dXbC9OMmhRU2dXcEZQMHBnMkl2aVFEdDdCT3hZditWNkFNSHpUait2UzkrNit5KzNoMHo3cHZ5RklaZ29DTHU3SXF1SzREOW1STWFlcTRBZ3FTaS9GdVFIdGJ6VGE4dTNiOFRmVE5aOHpGbG9tUUNOT0ZIVjNBaG1GTS94ZlZDNVlYWGFGTU5EN0ZZM0NRVi9TUWVITUI5ZmJNaEhuRUY5WTlzRG9SbXF1SGdQY2xXUVpzTkwrZG9LMXp1bXp5UTVaS1lmUXJlRk5zOEgyTE1VR1hFM3poSWRYZGlqWHZUYzA5ejZJcTg4NVJsbnh2cnBPTVBiVERCUHlFMHh0KzRuOGJtOEUwZGx5c0tnQmRvOG80UldncVpSb1NGMnF3WFhweEpXY09jMmVDSjBmanhrRlBnNkk2MWJXMzI4VkdKRk5DNWtXS29yUS9vamUxSnNLdXBJY2lwZllZcUN3U3NvUldnUzRjSHlRUFFKOWNJT2E2SmpJN0pzMVZXVTBBZ01pUmZPN2xFTGRlamdKYzN1aGxYeUsxZnJCNE5FS0VEN3hzWG56dTFxUkF0b1ZwWXp4cTlRakpFdGs2N1M1SzBlNC8xcU8iLCJtYWMiOiJhMDUyODkxODRlNmM2ZTRhZWU5ZmIzNThlODNhYTM3MjEzZTI0ZDA4Y2ZkZGE4MjJkZGMyNzJiNTE0MTg5YThkIiwidGFnIjoiIn0%3D; _clsk=mwmaeg%7C1751127457580%7C32%7C1%7Cz.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285787191\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-109299 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-109299\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2034133791 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:17:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhpKyswSlhmQ0x0ZjBlUlRFQW8xeUE9PSIsInZhbHVlIjoiZFhJcFpvRnNxMVpPYnRrazhiVXZtNG9OVU0rTTNVSi9aNVlUbEMvZGovRkF1NHlsK2xmNUk4WTdKUE50ZCt0cHo5THJDRDFFdlg5WERuK2ZiYTFUNGs0OUg0NWxUWm16d0kwSGJMTWJHNi9tbjhCdDRXVC9lRXdEODlwNTRsUkM5c0Q1ekpIQkY0eS9mWFlpTWk1M2xOTDRMOGV2QVpuTUtYMlQrVWRod2E0aXRFaDNZSUZ4cFd1N2xQZGNZUCtBcXBLTkFkZCs5UUJia1RCbTFnN2VMbHNYQTNoQnVsK2ZNNzFObXlOS3RoUkpnUlZlcDRkcWg2c3d3MHVGb0tva1JGNCtZRlVFQy82ekpON3R2a2FpM1ptbDNYV3ZTM1B2MEV0OGtrN2Z6TjcxdGhPa1kwNkJ2SnZJMGU1OERidXRIMTVjTlVaTjkyeEM1RmpCMFBOZDB1VVhjRXlkWUVMQ3c4ZHJ1NFNPZ3FtMEFTRi93SnQzdXoyMy9xdEwzVnd3a1ZCNTJJL0NxdElmc29XMHBKOEpNeWpIWXJORHRDb0pZMGMwWENTcng4aXRnK0tOQmxyUXpRVjVicXJVNnhoUjRpWGw0L1B3bE54UXJIUG90YVpMVnFKd09LdmM5M2VTU0tXOGhkUStuVURNYjBHdnBsR2hGTjVsdUpXMnE2eVoiLCJtYWMiOiJiMDE0NjY1MGYwNjE5OGE2OWUxOTdhMGIyYWIyNWJlZDRhNWJjOTkzZWQ5ZTM4NjdhZGJkZjNjMDcwYTVkNDcwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImpLVkZWc3JBdnFGQ3cvNVhMOFpiNUE9PSIsInZhbHVlIjoic2ZmN21vSTVESGJkYlRvdWI1OXR2WE5ZLzlITDNvNUZwdzUvQ1UxZFdCcnlqQWRYZmVCRG1kUFVsNktzcFpvNUxPUFlaS2Vtdko5ZVZPS2VscWoxTDBRRStYcGhLeWdTYjQxSWxVNVRaenpBOWpjRXgxdGk1NWRGZVZ3Z2d5YmE3RG1Vb0VkY0hlUkxrT2VDdDJxb3RZZXlQSkZVMnkwK3B2ZkJFV24rNTVsVVQxUytaWkN6Wjc3VStSMENSRG1sQzlOTXMwTVQ0QnVQNXNnRmNBWVhka1NFZFZIUm1VK21YSG55TmtXMklVNFoyV052MkIwakhBeWkyY3VmdC9Jbzd4c1F5VG4zdm1LVkNiWlo0T2pkVkg4U2JPZ2xDeVYyRkxVcFh2UmQ1aUJlaW95NmZtUFNaV0k3NHI5MHRkMEtMU2pDc3lQK0NyNzVUZ0lDVjZ2aVgvMTFwUWNwZXkxK3V5RWR2cm9DOGpZVG5hR3Vab1lsMjBZSlpEeDZGOVAyVUxqMFlHa3psMUVGU3h1RFZXVWhKVk15bmRaeUFXS2RUMUo3TlNkSlRXT0FxVk43cDlDUGYrTDFiLzkzcjI4OFlDanBHUERrVmR3K2xtbk5CTnRLRmd5WnlYTG9KOUlLNUR3MlFmQzZwYmpsbEp2dWY4K1U4QzFYSDV3RUhsZ28iLCJtYWMiOiI3ZTgyZDYzMGU4MmRkZjJlYWI4ZDNiYmRiMDBjNTdiNTJjMTZkMjMzZGE1MDhiZjUzOGM4ZTNlNWEwZDBkZjYxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhpKyswSlhmQ0x0ZjBlUlRFQW8xeUE9PSIsInZhbHVlIjoiZFhJcFpvRnNxMVpPYnRrazhiVXZtNG9OVU0rTTNVSi9aNVlUbEMvZGovRkF1NHlsK2xmNUk4WTdKUE50ZCt0cHo5THJDRDFFdlg5WERuK2ZiYTFUNGs0OUg0NWxUWm16d0kwSGJMTWJHNi9tbjhCdDRXVC9lRXdEODlwNTRsUkM5c0Q1ekpIQkY0eS9mWFlpTWk1M2xOTDRMOGV2QVpuTUtYMlQrVWRod2E0aXRFaDNZSUZ4cFd1N2xQZGNZUCtBcXBLTkFkZCs5UUJia1RCbTFnN2VMbHNYQTNoQnVsK2ZNNzFObXlOS3RoUkpnUlZlcDRkcWg2c3d3MHVGb0tva1JGNCtZRlVFQy82ekpON3R2a2FpM1ptbDNYV3ZTM1B2MEV0OGtrN2Z6TjcxdGhPa1kwNkJ2SnZJMGU1OERidXRIMTVjTlVaTjkyeEM1RmpCMFBOZDB1VVhjRXlkWUVMQ3c4ZHJ1NFNPZ3FtMEFTRi93SnQzdXoyMy9xdEwzVnd3a1ZCNTJJL0NxdElmc29XMHBKOEpNeWpIWXJORHRDb0pZMGMwWENTcng4aXRnK0tOQmxyUXpRVjVicXJVNnhoUjRpWGw0L1B3bE54UXJIUG90YVpMVnFKd09LdmM5M2VTU0tXOGhkUStuVURNYjBHdnBsR2hGTjVsdUpXMnE2eVoiLCJtYWMiOiJiMDE0NjY1MGYwNjE5OGE2OWUxOTdhMGIyYWIyNWJlZDRhNWJjOTkzZWQ5ZTM4NjdhZGJkZjNjMDcwYTVkNDcwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImpLVkZWc3JBdnFGQ3cvNVhMOFpiNUE9PSIsInZhbHVlIjoic2ZmN21vSTVESGJkYlRvdWI1OXR2WE5ZLzlITDNvNUZwdzUvQ1UxZFdCcnlqQWRYZmVCRG1kUFVsNktzcFpvNUxPUFlaS2Vtdko5ZVZPS2VscWoxTDBRRStYcGhLeWdTYjQxSWxVNVRaenpBOWpjRXgxdGk1NWRGZVZ3Z2d5YmE3RG1Vb0VkY0hlUkxrT2VDdDJxb3RZZXlQSkZVMnkwK3B2ZkJFV24rNTVsVVQxUytaWkN6Wjc3VStSMENSRG1sQzlOTXMwTVQ0QnVQNXNnRmNBWVhka1NFZFZIUm1VK21YSG55TmtXMklVNFoyV052MkIwakhBeWkyY3VmdC9Jbzd4c1F5VG4zdm1LVkNiWlo0T2pkVkg4U2JPZ2xDeVYyRkxVcFh2UmQ1aUJlaW95NmZtUFNaV0k3NHI5MHRkMEtMU2pDc3lQK0NyNzVUZ0lDVjZ2aVgvMTFwUWNwZXkxK3V5RWR2cm9DOGpZVG5hR3Vab1lsMjBZSlpEeDZGOVAyVUxqMFlHa3psMUVGU3h1RFZXVWhKVk15bmRaeUFXS2RUMUo3TlNkSlRXT0FxVk43cDlDUGYrTDFiLzkzcjI4OFlDanBHUERrVmR3K2xtbk5CTnRLRmd5WnlYTG9KOUlLNUR3MlFmQzZwYmpsbEp2dWY4K1U4QzFYSDV3RUhsZ28iLCJtYWMiOiI3ZTgyZDYzMGU4MmRkZjJlYWI4ZDNiYmRiMDBjNTdiNTJjMTZkMjMzZGE1MDhiZjUzOGM4ZTNlNWEwZDBkZjYxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034133791\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1516941799 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516941799\", {\"maxDepth\":0})</script>\n"}}