{"__meta": {"id": "Xa4b5b887840b363381510ed15dd898b7", "datetime": "2025-06-28 11:22:34", "utime": **********.686874, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.251674, "end": **********.686888, "duration": 0.4352140426635742, "duration_str": "435ms", "measures": [{"label": "Booting", "start": **********.251674, "relative_start": 0, "end": **********.632839, "relative_end": **********.632839, "duration": 0.3811650276184082, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.632848, "relative_start": 0.38117408752441406, "end": **********.68689, "relative_end": 1.9073486328125e-06, "duration": 0.05404186248779297, "duration_str": "54.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43908536, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01043, "accumulated_duration_str": "10.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.667465, "duration": 0.01007, "duration_str": "10.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.548}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.680542, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 96.548, "width_percent": 3.452}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-440953764 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-440953764\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1294805669 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294805669\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1061308445 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1061308445\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-297024863 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IkJZbXBuNDdScDFYV1R6bStNTXRHdUE9PSIsInZhbHVlIjoidnV3S08wWmlNZ2RJR2xGZzBJSWdnK1pNSWlIdHZPdGFFbFlWSTcvQ0gzb3hGNWNHL1BVWlNNKzNCMjltVlJSL0o0TFhaWVhXTHFRQUZDUDBFZ2FvME4vNmRIaVZvRUlxQWJ4MElEY0luQ0hZR1JZR1UrRFpqYTMxVGQvaHloanViZkRpMkg3T2lYRFU0cjd0bDczWjBqa2Z2QlV5ZHZoWXF1ZU1hKzNCTG9aZ0k2VWl1NndsdGcvT3NXOFduR3lPbWJWRnJMQ2twVWhjSEtNUTdSOGZEYyt0T2Q3TFhQQW9RZVQ2NXVURHFGaWI4RkI4Njl3QUl4MXJ0QndZeHR2K2p6ZlpQc0NldjVoYXFIZmMxZHNzTU1GZE1NWWZNZVAyTUNFQTkyWEZaMGhZOXBEczJ6SVJkd2wrVFRUMzRPb2N1bnlYNUw0d2VpVHFjZXlvWGk5c0ZvVXhrUUZMR2ptWFZyL0ZMQTVPMTc0WFNWREZHcVBpNmt1QmxHeG1jQXBuSUI3SERkZktQemlrUWNlSkN1M3JnbDJ5TTRZZUdWQmxZaHlJOEhHZEFrb21jdEM5T1NUNXhGY1VlR1RHSmdpdGs3UllUVCtWM2Z5cXlIZ1VTbWtHcEpLVG44VDExK3pTVFlxOTNUeSs4OWxKL2NlQUhMUnF5NStSU25MWGdTUTYiLCJtYWMiOiI5NTAyYzgyYWEzOTNjMDg1NWZkZjA3ZjU3MzQ5ODAwYTcxODQ0YmZjNWM3NzM2MDc5YTQ4YTZiZmIyNTZiYTc4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkNGZXNCV3NINXdCaU83Y3hnVWJSUkE9PSIsInZhbHVlIjoiRmo5L3B5ODY4cjkrWWdNeGo1UXlQMmh0SkVyc0pseEc3eWFhZ2hrYkhoWWt6RU9uWm1xdjZTelAxOXdac3dmTUNKUzhUSmdidDlXTkJuVjg4YksvRXA1dTBRaVVHVDhENlJNUDkxK25tNVFWZ09rYXdvL1UzNXdKd3o2aEdWTm50d2k2eG0wSGpTMWNtQXNQRS83OXJDTUxXUW1OTTJIdHVnaXo4S29UT3ZtTVBwTjRxYmJ6aTZxSlE0d0t5QmNNM3FjOENxUGpLbkVyanVEWjRxcHFHZzZLNmpBTDlZREUxYXpjRVpOYUk4TUFKMDFudHF0QmoyazdMWlNhOGpQV1hEYk5uT29GdFBiWmcyVXUxQ2VjNGtxZmdMSElYWUVjQUc1aGtWR0RSNWR5Wk90VWh4YnBMSHZjdlZxd1dISzczUmkzdGp4V0NacDc2K04rZ21XUjM0d0RwSjlMSktHSDR6a2xoYUJOSEVtS1Y3SEdxZW5pRUJnckZYb2ZZWS9XK2IwOG55SXhsVU1CRlZpN0IrM0txcWxLQWFCN2E2cHlQZU1QMmp0NXkzZUdsMTRJUXBROUo5UEl6OUc1N2p6eXNEWE03akNQSzhsbHdxN1pnaUEybUFkNU01VDZScXlWNGhKNG9HM2NUTDNFeDllUUVXNVM0WnJiY1ZFQzNYeFMiLCJtYWMiOiIxMTM3OTgyMmVlN2Y2YWM3MmY2N2JlMGIzYTRmMTBjZDZmYjJkMDA2NTZjNjgwNWZlMmFkMzRlNDMzOTI2YzI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297024863\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1768840235 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1768840235\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1876462328 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:22:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktIN3JleEJHQWxzN29EMzVlMWN5NWc9PSIsInZhbHVlIjoiVU9vR2VvTVlYMzJNRUZJWURIMjR2bnhaeHZ1cUo2eVFpL1hIRlk3b3VnSm9IYVdnR2hpaCtRaCt4ZVZ3VHEybUg1cy9ZT1dSOWsrZkJMZ1lSYVNRcXB5RXRrcVhjOTI4aW1tUU1BaWV2bnhLUTFjc2FUa1hidkZRR3ZaeTNCdEtwRmw1aVFhS09LbVNxRXBiajRkTW56TzNlRUpPeW14UVVMVTJwYjVpSmR2ZnpSQWhGVHpiWHNvQUlnV3JvU1c0U254QWJSb0tQazJ4dU93NUtpdnhJK1pwd1gzbVlSUjFWam1hc3ZoZmNYWUg2dVk1a1VwdEU0azF1QkxuUGJscEd6NVA1ai9BWUw5ZHBma3VCZDNSK3YvdktCSGFNempkN01tNnpZS3kybjFmUDRyd3BSZkx0Q3dLcnJUOWVJTW91VjI1TlBudHoyZTFBUVF5eUhpYnJYQ1BGODkzZW9INjQ5NEQySUtDNXlMNHl1cFVZeXVXK1YxUXRvdEpHS1AwK1ZaMGgwUk0wRzhxUjZ4Z1ZxMHB0OGVHQm5VeUg2UUx0Q29wbm1aOXpsb1pYU0FUSWRWUnl4dnRzeDVYY1JBNWZPbEpjUkc0Tk1jN1Iwak5uY0ZFcE9tUVNQVEkzcS82VGhMdmIrUkUrOGFPclNDUWE1VmJsWlR6dUw3c3ZKQkgiLCJtYWMiOiIzYzdiNjM5YmI0Y2E4ZjljZjVhYzQxZDA1MjM1YjM5NmI4Yjk2NGI2MTllYjMwOTQwMTlkMDhiNTcxZGIzZjhjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVhY0pKZ21xOTNoeGdWNlNWRHlDUWc9PSIsInZhbHVlIjoiTlVOcEh4RjY3Q2RQbjNEbEs0eFFSNnlFUzNETERJVUVTTW1oYmVBYVByZTh6T25SUmtCWHJsNDBVYlJuUmREOGdaMlhnYzdjVDFRazNnaEZoTXVydlM3T1RtNkl3N1QwRkZOV0pPKzgxQWozTkJkRHNSbnFNQURuMXV3WElxSy93Mk1Gc1FUbHpQZVMwOXQ4dXJmYjY4YXNqd3F3Um1oYzhaQmdxUyt5WndycGV6d3M4TWp0NktmTEZDb1JyUUxTclE1UjFuWXluTWVqaUdWaUJaL250NTE3SnpHWGozWEdITnAwMXZMekJESklqMkF4Qk9IRUwwYVN5NUdwZnVXYXpEVDgyM1gvMWlRVnZiTVJzU3o4RG9pNW8yeUoweTlnWllCWGk5U1FpM3RkNEhycHpiM2g0V2x6WDk1V1QxdDcxUTJUcytJdXVSK0Y4RXA1ZUZ6RWxVS1FsZHdnM2YvcHgvVk43SmkwSjY3cURMSVkyMkl0RG1CalFEajhCdW1SMWlMcSswZ3V6Ly9hTUFPUEx4QmNVM1VxUmpZUXZ2WGN3Y3dsZ0tjVDgzQ0c1Z3ljanZ0YUVWWjAzMlduV2xhbHY5MGh1cXlaem1mSlQyU0JDeWZrTmFEQ2QxUzN4WFpESU9HdmdPMDVPM2tLZ0EzNjZ1MldHVzJFK09Rb0F0R0giLCJtYWMiOiIwNjIxOWUxNmU0ZjdhOTc3MzYxZDliMmEzMTY1YmYzODkxNGQ5NDgwZDdmZjQ1ZmM0NzFlMTI0OTgyZjE4ZWNkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktIN3JleEJHQWxzN29EMzVlMWN5NWc9PSIsInZhbHVlIjoiVU9vR2VvTVlYMzJNRUZJWURIMjR2bnhaeHZ1cUo2eVFpL1hIRlk3b3VnSm9IYVdnR2hpaCtRaCt4ZVZ3VHEybUg1cy9ZT1dSOWsrZkJMZ1lSYVNRcXB5RXRrcVhjOTI4aW1tUU1BaWV2bnhLUTFjc2FUa1hidkZRR3ZaeTNCdEtwRmw1aVFhS09LbVNxRXBiajRkTW56TzNlRUpPeW14UVVMVTJwYjVpSmR2ZnpSQWhGVHpiWHNvQUlnV3JvU1c0U254QWJSb0tQazJ4dU93NUtpdnhJK1pwd1gzbVlSUjFWam1hc3ZoZmNYWUg2dVk1a1VwdEU0azF1QkxuUGJscEd6NVA1ai9BWUw5ZHBma3VCZDNSK3YvdktCSGFNempkN01tNnpZS3kybjFmUDRyd3BSZkx0Q3dLcnJUOWVJTW91VjI1TlBudHoyZTFBUVF5eUhpYnJYQ1BGODkzZW9INjQ5NEQySUtDNXlMNHl1cFVZeXVXK1YxUXRvdEpHS1AwK1ZaMGgwUk0wRzhxUjZ4Z1ZxMHB0OGVHQm5VeUg2UUx0Q29wbm1aOXpsb1pYU0FUSWRWUnl4dnRzeDVYY1JBNWZPbEpjUkc0Tk1jN1Iwak5uY0ZFcE9tUVNQVEkzcS82VGhMdmIrUkUrOGFPclNDUWE1VmJsWlR6dUw3c3ZKQkgiLCJtYWMiOiIzYzdiNjM5YmI0Y2E4ZjljZjVhYzQxZDA1MjM1YjM5NmI4Yjk2NGI2MTllYjMwOTQwMTlkMDhiNTcxZGIzZjhjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVhY0pKZ21xOTNoeGdWNlNWRHlDUWc9PSIsInZhbHVlIjoiTlVOcEh4RjY3Q2RQbjNEbEs0eFFSNnlFUzNETERJVUVTTW1oYmVBYVByZTh6T25SUmtCWHJsNDBVYlJuUmREOGdaMlhnYzdjVDFRazNnaEZoTXVydlM3T1RtNkl3N1QwRkZOV0pPKzgxQWozTkJkRHNSbnFNQURuMXV3WElxSy93Mk1Gc1FUbHpQZVMwOXQ4dXJmYjY4YXNqd3F3Um1oYzhaQmdxUyt5WndycGV6d3M4TWp0NktmTEZDb1JyUUxTclE1UjFuWXluTWVqaUdWaUJaL250NTE3SnpHWGozWEdITnAwMXZMekJESklqMkF4Qk9IRUwwYVN5NUdwZnVXYXpEVDgyM1gvMWlRVnZiTVJzU3o4RG9pNW8yeUoweTlnWllCWGk5U1FpM3RkNEhycHpiM2g0V2x6WDk1V1QxdDcxUTJUcytJdXVSK0Y4RXA1ZUZ6RWxVS1FsZHdnM2YvcHgvVk43SmkwSjY3cURMSVkyMkl0RG1CalFEajhCdW1SMWlMcSswZ3V6Ly9hTUFPUEx4QmNVM1VxUmpZUXZ2WGN3Y3dsZ0tjVDgzQ0c1Z3ljanZ0YUVWWjAzMlduV2xhbHY5MGh1cXlaem1mSlQyU0JDeWZrTmFEQ2QxUzN4WFpESU9HdmdPMDVPM2tLZ0EzNjZ1MldHVzJFK09Rb0F0R0giLCJtYWMiOiIwNjIxOWUxNmU0ZjdhOTc3MzYxZDliMmEzMTY1YmYzODkxNGQ5NDgwZDdmZjQ1ZmM0NzFlMTI0OTgyZjE4ZWNkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876462328\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1950027491 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950027491\", {\"maxDepth\":0})</script>\n"}}