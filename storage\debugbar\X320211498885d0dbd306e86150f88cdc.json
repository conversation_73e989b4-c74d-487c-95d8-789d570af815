{"__meta": {"id": "X320211498885d0dbd306e86150f88cdc", "datetime": "2025-06-28 16:17:57", "utime": **********.967877, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.480534, "end": **********.967893, "duration": 0.48735880851745605, "duration_str": "487ms", "measures": [{"label": "Booting", "start": **********.480534, "relative_start": 0, "end": **********.892396, "relative_end": **********.892396, "duration": 0.4118618965148926, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.892406, "relative_start": 0.41187191009521484, "end": **********.967894, "relative_end": 1.1920928955078125e-06, "duration": 0.07548809051513672, "duration_str": "75.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45855360, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.010159999999999999, "accumulated_duration_str": "10.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.926353, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 16.732}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.940671, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 16.732, "width_percent": 7.776}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*************%' or `sku` LIKE '%*************%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*************%", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9453259, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 24.508, "width_percent": 42.028}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9537199, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 66.535, "width_percent": 26.575}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1995}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.959636, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1995", "source": "app/Http/Controllers/PosController.php:1995", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1995", "ajax": false, "filename": "PosController.php", "line": "1995"}, "connection": "kdmkjkqknb", "start_percent": 93.11, "width_percent": 6.89}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-269118371 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-269118371\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-350665936 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-350665936\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-94749530 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94749530\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1006948840 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127449834%7C33%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBPem1ZSkU1bEU5YUJIaHBhU1ZhY2c9PSIsInZhbHVlIjoiWHVxeVF5ODFJUy9sYUNGUVAxS1VOSGZvRFVPOUxTRHRlS3ZDSUQ2YU5IUlV3clZCVG9Tb1hiUHdnOFVXbUFTODJlV2ljOG9mZzN0S2JzL0FxQ2pMYjFWRmlDSG80dUx3SmlqTW5SRlU0SCt1MVdXd0VyeW4zajBVYysrRHZuTjYxRVd6Y0lkV3kwZkpJU1RRcERnZU9tamZhWVNweGhEVHlHQjEzSG1sY0pIbEgxWllsMkkzeWFlOW1sUnI0L3hwdDZpbllqVGJqSWZRRTB0UGVtdFJnZW9mUklOc2xYZFFiVk1McHJVS2ZlcWdUQnljZUphekFWeEZnTFA4NC9aZTBESjl2SHQxdmJFdnY5a2tTaGE4YlE4YTliZy9Gc0RnSlFtT1kzMTBHYk00ZWRNb0xCbllzdnJmUDFPNThVUnU0WjdzUG5wU293UlhvYUdFUktldFJHWUdrWERKNXF6azRTZHVuUUlXQ1RidTEvSU1EdlNqaUMva3Q4ZDVqQ1JueGNPdUhBTzU0T2krNER4d25abTBEZzZmMG9jSnc2SmlISU1ZaGtCTjM0cCtKUkZ6TThKVE81QjdxTlNFY294RU1xNDU5b2Zqb0kwNHZFSGZBZ05HRVhSUVJqVTVZZW9oYVZNL1hDaDh0RFdPMTh1NDV2K0JvMkY2WGszQUV0eDciLCJtYWMiOiI0ODhkNmVhY2EzYTUwMmU2N2MwZDlmM2RkOTZmMmJhMGJkMTEzMGQxODViNmVmMGY1MWI0YmQ1MzI3ODdhNDI1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlNGTnNNSEtWMWlLcFRaUlFFVFRQN1E9PSIsInZhbHVlIjoiZGY1SHhRVFArWGhlVHhHZldqbkFwZWZmZXlteC90RXZNOU9RTjhaeUcxeFNtTnVCOFJScjVFWExqMmlVakkvVkFBUzk4VjVVdHc1MHZzWkI4VzUwV0o0bGVCWmRRZzhlN1lLVnpKOGMrS08wZ0VJbllEVDdFOS9UdnIvQm5qTGNyWThCbUFrcVZTUXFJNGNlcEs0dDZXVmdkNU8wQlRCMDV6TTVoTzF3VkUvOE1pMVp1K1R0L1hlM3JNYkkwZG4veUVZc0FsMUQ1MHhLQXlJcSsyNlJBcURiOTRQSG56UnVETGVKN3dpUWdWSkl1ZklkeXRnOHVQWVVKbDNqTlI3b3BMaXJnaHlnRGdteTQxOHl6OXovWHJhTXVSMitIS0tvVTdZVzUxV25yS1c1anBqdEM2OW1INHZPUzRXM3NOeENBUU5zL0xZZm9PVy9aSnhJL0gvYllRemtpUWpjVU5jRElsa1ZsY0x0M0ltdk0xNVJ2Qm1BWmhsT3hoWThLV3dpd2g4T2FlOW5mU3hrUERYbU10WHNyQ1VjUElqcmwwMjZHcVM3WEoySyszUytxRFhodEVGRlg4TTVMcmhPYWxxY0kyTDhnOWRGV2txczhDbUdVQjBHdlg3SXRjVVpsRVJFbUtEQlBPY0VobEJDWUNlak5BN1VJZmtkdFEyUzFkcGIiLCJtYWMiOiJhOWNiZWFjNGMwYTM3MzBjNDYwYmVjNWZiNzdlZTViODE3YzIzOGZhZWUxYTQ1YzU4NzJmODI4MmIxNTY5YzQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1006948840\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-630653697 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-630653697\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1068039864 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:17:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFCdFJoaFN0TGV6N2FCbkZ2TmlZOGc9PSIsInZhbHVlIjoiQThEYms1N25LcldLVkN4Q3R3VU9IMXZnMm5TNDdMclJRcjQzbENZS0RIZWswWGw3V0wrYTRuWnNZcmkvRXYrZFVVdUhxRy9uYm9nTVlFTGwraEg0RU5hdmxadU1kSkxMTzBlT0tIVFdmQ0pZdXMwSE1ZRGlqZmx5cWwyNGFYV2x1dUdETnZ2ZlEwak9OQmJQNnVmVTJqdW5pbkZoNE5LUGszZUd1OXNMUysyTEVwTUhqOTR0NFUvT1ExSEJaSDkyTVQzYzJyOXNGVW4zT2Z0YUhPUExGQy9TdGRTVEdvRW9hZ1lScUl6OUNxSHVkOElyaTJENnJTV1kvNEpoaTc0RjE1a1BUMkIrVHFDTnpsK1duaXRHYk1qbGdKTWNiKy9jd1I4dVhXb3NoRHI1andMdmQ3KzhzR0YxUHhRNXZDbXM1M0ViT3I3YWFJL1EzdzlOTnhGN3pwMGpIdDNNNXA5NU9qWXJFT0FwdGZ0eldjSlViQlhoY1UxVGZmY0xFOEVPd0V6NTgvMThNMGJ3cTZKNUFPTjN1Znpaak5Vd0VBU255Tld4emJqNUx4aktmZURmdDdlclVxOUFnZmVQc296WjJJRmR3aThjZ2FrTm9jTG9rbC9CMzgyR05RN21yelVZU3Aydjk3QUU1RGlnTGNmSUt6ZXhaNmdKTHlqMkdOSUoiLCJtYWMiOiIzNTFhOWM2NDgwYjA4OTMwMjE0YmZmMjI3YmEyZTJlMjNjMDMyZjkyNGFmODJjOTY0NWZjMmQ1NTEzN2EzOTZkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1UdFhDRjNVM0tGOCtIdFRNTDNCL3c9PSIsInZhbHVlIjoiejFQR1UxNUtTdyt6aDc4S1A0Mi8wa25iMGJaZDFCNVp1ZUN4NFR6Y09VYkVsY3FETjJaNGxCRDlrclNsM2hpcHJaZ21vV3NScThNdmFvOVVsbEJkenR0bHpKYWhQRW11RFE4bnUyVmlZbythTDhKL1NxQVZ6NktCUmNZZ0dBd3lJTDRPN3BQaUNUNmlHaSt6VENGbEJ3U2ZDNjVoRzZpRVpYQlNUWFZEM2JDbUlkbjhSRXVocWJ2M0RWY2hTcjVybWdhM3R6b0Y1b1RjeGlkTEE2YmUzcmdsT01KQk52VVNUV3RIZ0ZmVFlOOWRaWHNqaVprNXU4OURlZWpXbHRBL0ZkRVN0NXJWM3NqMy95UHc1RnRBSEZueStZVkZ4VVFyUEVxdjRlSDJHTzc1U2txOURQVEx1Q2MvRVhOUk1FUjVZOXp3alJLRVdwWHRvejFNcTdDeXp5WTdlMDlhaGlZSzFZcVNZN21WNElrYTVkZG14SDUydUpqQUtscXpxQ2RnWktQa3B3MmdkNVB1SkZ6emcwY3VhRnlwUjMyaHFKekJTS0Z5ZnJGblVNaWxITzNDaVVVWlNnNk9sL3h3YWU1QUdHbzhjMkhSS3NZbm9LRGhoazUvbkRiR2EwUkxQUEtMRmxZK211MUNLY2ttZG1tQUVndCs0Wmh1bzBqSXJKclUiLCJtYWMiOiIzZGZhZjdiNzZlMjRkN2RjNDRjN2UwNDhjNDNiYTE4OGRmZTY3Yjg0Nzc5MGE1OWI5MjM4ZWFjMWEwYWRlMTVkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFCdFJoaFN0TGV6N2FCbkZ2TmlZOGc9PSIsInZhbHVlIjoiQThEYms1N25LcldLVkN4Q3R3VU9IMXZnMm5TNDdMclJRcjQzbENZS0RIZWswWGw3V0wrYTRuWnNZcmkvRXYrZFVVdUhxRy9uYm9nTVlFTGwraEg0RU5hdmxadU1kSkxMTzBlT0tIVFdmQ0pZdXMwSE1ZRGlqZmx5cWwyNGFYV2x1dUdETnZ2ZlEwak9OQmJQNnVmVTJqdW5pbkZoNE5LUGszZUd1OXNMUysyTEVwTUhqOTR0NFUvT1ExSEJaSDkyTVQzYzJyOXNGVW4zT2Z0YUhPUExGQy9TdGRTVEdvRW9hZ1lScUl6OUNxSHVkOElyaTJENnJTV1kvNEpoaTc0RjE1a1BUMkIrVHFDTnpsK1duaXRHYk1qbGdKTWNiKy9jd1I4dVhXb3NoRHI1andMdmQ3KzhzR0YxUHhRNXZDbXM1M0ViT3I3YWFJL1EzdzlOTnhGN3pwMGpIdDNNNXA5NU9qWXJFT0FwdGZ0eldjSlViQlhoY1UxVGZmY0xFOEVPd0V6NTgvMThNMGJ3cTZKNUFPTjN1Znpaak5Vd0VBU255Tld4emJqNUx4aktmZURmdDdlclVxOUFnZmVQc296WjJJRmR3aThjZ2FrTm9jTG9rbC9CMzgyR05RN21yelVZU3Aydjk3QUU1RGlnTGNmSUt6ZXhaNmdKTHlqMkdOSUoiLCJtYWMiOiIzNTFhOWM2NDgwYjA4OTMwMjE0YmZmMjI3YmEyZTJlMjNjMDMyZjkyNGFmODJjOTY0NWZjMmQ1NTEzN2EzOTZkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1UdFhDRjNVM0tGOCtIdFRNTDNCL3c9PSIsInZhbHVlIjoiejFQR1UxNUtTdyt6aDc4S1A0Mi8wa25iMGJaZDFCNVp1ZUN4NFR6Y09VYkVsY3FETjJaNGxCRDlrclNsM2hpcHJaZ21vV3NScThNdmFvOVVsbEJkenR0bHpKYWhQRW11RFE4bnUyVmlZbythTDhKL1NxQVZ6NktCUmNZZ0dBd3lJTDRPN3BQaUNUNmlHaSt6VENGbEJ3U2ZDNjVoRzZpRVpYQlNUWFZEM2JDbUlkbjhSRXVocWJ2M0RWY2hTcjVybWdhM3R6b0Y1b1RjeGlkTEE2YmUzcmdsT01KQk52VVNUV3RIZ0ZmVFlOOWRaWHNqaVprNXU4OURlZWpXbHRBL0ZkRVN0NXJWM3NqMy95UHc1RnRBSEZueStZVkZ4VVFyUEVxdjRlSDJHTzc1U2txOURQVEx1Q2MvRVhOUk1FUjVZOXp3alJLRVdwWHRvejFNcTdDeXp5WTdlMDlhaGlZSzFZcVNZN21WNElrYTVkZG14SDUydUpqQUtscXpxQ2RnWktQa3B3MmdkNVB1SkZ6emcwY3VhRnlwUjMyaHFKekJTS0Z5ZnJGblVNaWxITzNDaVVVWlNnNk9sL3h3YWU1QUdHbzhjMkhSS3NZbm9LRGhoazUvbkRiR2EwUkxQUEtMRmxZK211MUNLY2ttZG1tQUVndCs0Wmh1bzBqSXJKclUiLCJtYWMiOiIzZGZhZjdiNzZlMjRkN2RjNDRjN2UwNDhjNDNiYTE4OGRmZTY3Yjg0Nzc5MGE1OWI5MjM4ZWFjMWEwYWRlMTVkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068039864\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1103351292 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103351292\", {\"maxDepth\":0})</script>\n"}}