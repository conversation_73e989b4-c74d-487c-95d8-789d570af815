{"__meta": {"id": "X079d4aeb62aef20861207b2d5d37505d", "datetime": "2025-06-28 16:09:22", "utime": **********.522343, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.035828, "end": **********.52236, "duration": 0.48653197288513184, "duration_str": "487ms", "measures": [{"label": "Booting", "start": **********.035828, "relative_start": 0, "end": **********.469273, "relative_end": **********.469273, "duration": 0.4334449768066406, "duration_str": "433ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.469282, "relative_start": 0.4334537982940674, "end": **********.522361, "relative_end": 9.5367431640625e-07, "duration": 0.05307912826538086, "duration_str": "53.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45403248, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00232, "accumulated_duration_str": "2.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5018559, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.017}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5129411, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.017, "width_percent": 21.983}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-931038821 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-931038821\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1105465154 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1105465154\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1814514786 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814514786\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-913347576 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126882941%7C30%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJ0bDlQekY2YWF4aExHaXd1U1NROGc9PSIsInZhbHVlIjoiSm5xN1lyUHB4bUZCakJXN2trU1E3bEJJSU5PYURiSGw4cGx2ck9zZk9nN01WS2hBMGNNalFUQndzZzNwb1NFb0VMVjgxS0g0ZFRzL3lPRUtIRUwxYVpQM0lvSzdUQmpSektURVZMTEtDQlk1WFpCczNpUFJnbE9CYlJzM2dWUXFOcHErWUJ4aElKbXUxeTJmSjlKd3ZpcXNCY2VmS2srK2JGQXVLdjhoNjk4MjdQa2xDWGYrbHFndDRvK2JnZDhpM3prRW5QdjlSNHo3TXY5SjVST2xxenkwYTJzSTlkeVJXOGlkb1pxV2tsa1lmdkpCdFI3Ui9YK2psUVFZeU1ETGRhQndyL2ZwanJGTTFYTDgyYWJ1UWFlNzhXYW43emdwTmloYnRpbzF6cm5HRUhmMXdmdlNTSU56NXhCQk9NVzZHdGtSdC93QmROcmlGRnhpVHk3OCt3V1I5bkM1bXltWk9Hcy9JMFAydENZMGpvNU9uU2ZWNWJUK285N0NweFJoZ3R5WWMrT3ZrWHdWcElnb3d4REhaL25kVGtvZWIwU0E3RTliNllHamVxSFF0dzJlZmhMZEdZWk9yWk1VZEU1bTd3azJxREdJN05meU1jMGY3Tml1enNDeWsrV2JJNnFZMGNDMDYvb2pmNUVqWm11aGlKZDI5L1ZPcVhXMTNhaFEiLCJtYWMiOiIwOWVkMGEzYjZlYjU5NDM3M2RiNDRkN2IzYzEyZDQ3MTE2NGQ5MjhkNzE2YjYxYTdlY2I3MzllODNhZjJjMzE2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1JbWVVck5UK0g0WXdPZXAxcmZPMGc9PSIsInZhbHVlIjoiNWFOaFUwQTlOUXJZWGYzM0lLR0hkL2RvWWlJclNlVFdYdzU4cFBkWk5GeGtaQ3FMeG9aOStrREZmZHc5b0t5UUROcEVDU1N3YXRHTVQ4bzBJS1F1L1J5RDlORzdheWk3WGVFWVZsVG43dVZoVGlGc0JVM08zYXVJanpTSm5YM1U1NHVBNGkwbG9aeHYya2w1dUo3NjJDVXF0Wm8zaVBmNVJnbVhMNFd5VXB2Q2w0UWRkUzh1dzRzOERQbVI4a2crTlV3RDY5WjE4allzSXBiWG5TNUFJcDgrS1NwN3UxeGtTa3QwUkFKUWhManZxcXgrdTVxNEZuM2x6aFVXaVJ4WDVXenZBOGlWNitQaXJlWlhmZ2lGZk5MQWtFZEJxVGNJRkt3SFA2d3pLSkxKQVdiSFdBb09CaXFLdmxsZTRRd2UvV2tWSUhUdEp5aElGZG56c1hrVFpkQmNLbzNEVHU0NU81TStrMmk5LzBWWDQ2cWZUSlE5ZEUzMjJVUUtHb004UTE4N25VcWR1MW5mdldVUjU2MVJjdXdqRFYwT3ROWVhsN1FLY1Y0WG1IOTlBVVRNSzJXQkJEN3U2eGU3akRIQTZnOGpQaTlHeHY3dktKZDhQTEM4ZFFSTlMydmxNOWRIS0lNVDUxeFhkNVFBVDJHNXpvb0ZwbGJNRWkxZzVJOFMiLCJtYWMiOiJhNThlODY5ZmE4NGUwYzUxZTA0NDc1MzRkYzkzODdhMzJkNTA1ZjEyNzY1Yzk2NjQ3ZDczNDg4YjdmNGU1ZTg1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913347576\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-436997284 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436997284\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1488088818 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:09:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkllT1h4aExIR08rOHRGYXlpdVRDUXc9PSIsInZhbHVlIjoiY0NIU2ZLOE01NlJaVmRxNDFiTkkyRDZUU3RYKy9mRmk1R1ZjSmRZZjRvd2ZrTUdWMkl2VzgrMlV6eUpWRHhraS9LQU11bXNMTXhuNE1VTlhzUUYxZUdxbFRJSUZWNlo4MGUwM1pSenJoK0NhRW9WTE54TE5CQ0NHY1NsdmhhaFBxUnF0WGFKRC9MNy9ZVktpYXVHZzY5VDFXelcvbGVuZDRab0p5cDdWbDlzNThjTW9TTnlNN3JQcXVNN21YQUttRzRVZXdoaDlGblh1NjBIWHE5aWRFS1l1d1JpOXQ4clVFS0kwTXo0aCsrRUVVM25nV0FkY3BXZ0dReVA1U2w4SGdNQmdWWVI3UFpubC90MHlxdWVUWVFpQkNyL0U2bmJabkE5eGxyNnJjNmI0by8zeVhJakJLM2JkZThmRnR2dTZNWDJxNXYwTUlaMHQzdnJkWm5HS1dOWjA5MUtYamlQcmhJeHFDKzZXb0hmREwraGd1NmZpOENCZnZOcHNaS3p5YlU1c0hFSjJ3RHJYWHZhUlQyNTlPZDh6R1NRV2NpUktwS3JSQ2RjOFphN0N2QStRY1hEOE5oMHF4d2JPa3RlbkJPZ0tNTzlXZE9XVURULzRJWThscHY1cTlTbWhjaml6aDY5c3Q0bEJ0ckZzYVdzc1c1ZDR5MUpvSTNEbzVJTVAiLCJtYWMiOiJmMWI5OTM2YWQ4NjJmNDVkYTgyMzI2NGMxOTVlZjgyNWFmYmE5NzdiMmY5MTBjMWRkMDk5ZDQ4ZTU3YTE0NGRiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:09:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImR6Y1N1VC9qT3NlZlE2MGVwUHZyZXc9PSIsInZhbHVlIjoiMTE0NS81ZTJ3L1VHcHNQdC92VThtYXpvYWxGREN3RnBDaWs2VGZwVm50cVpKbU1IVHEzVnA1WmZEWGkwOHdXNjhXbzg3VmYzK0E0ZVpvUEk2dFFjcXVTRkpZSVo0cmRzdWU0Y3Evc0V2VFl3d05EUFBhbXQrVG9uMlRlOWt2L2Rua3NYZFFTa0Y1WTBFK1krTC9vZ2ltRmc4dnNjZk5XVk43QjN0RFJSL3hwb2llci9mMnowME45OGpnbXdQM011Yi96WVBYNFlVTm1HdUU0K0s2djJmT1lVck1GWG9tTFplQ0oyTVpOcG0rQ3JoOG1VTHVuZWpNaXpCQVpnVVJNV1BTOGczMmYvcGx1amRVNHZqTXVXVWtqY2N0bXhXdVNsL3VjQnlDRUVpZEJBWjBhTlM1V29ueUNzYWpibmtoSGdWSk9wQTBrMEh2Y0srK3g1dmdoZjVOM2hMaFdNdXpRTkZaSC9oT0hlTzFUR0ZBczBjazdJb2tESGkvSlZmY2JzOVBLTlVmZUVoMm1IWi9TQitCTDNLZE5tRFk4MXJIelF1Z0RudnUwcGwrdkIwRkJLTTlaRkpOdVdrZkVIcVF4MEltZldiYnlCSFJrSlI0R0JDaFg0dWlKRThDNFgwMy9jL25iWjdIZEpzcHVpeXZSeWFlbjBzVWhjUy8xV1BSdmQiLCJtYWMiOiI5ZTc0MWNjZjQ5NDBkOGZkYmU4N2ZiZjFjOGM2YzQ4YmIwMTFkYjEzNzNlM2E3OTM0MDY3OWVlMWY3MjMzNzgwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:09:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkllT1h4aExIR08rOHRGYXlpdVRDUXc9PSIsInZhbHVlIjoiY0NIU2ZLOE01NlJaVmRxNDFiTkkyRDZUU3RYKy9mRmk1R1ZjSmRZZjRvd2ZrTUdWMkl2VzgrMlV6eUpWRHhraS9LQU11bXNMTXhuNE1VTlhzUUYxZUdxbFRJSUZWNlo4MGUwM1pSenJoK0NhRW9WTE54TE5CQ0NHY1NsdmhhaFBxUnF0WGFKRC9MNy9ZVktpYXVHZzY5VDFXelcvbGVuZDRab0p5cDdWbDlzNThjTW9TTnlNN3JQcXVNN21YQUttRzRVZXdoaDlGblh1NjBIWHE5aWRFS1l1d1JpOXQ4clVFS0kwTXo0aCsrRUVVM25nV0FkY3BXZ0dReVA1U2w4SGdNQmdWWVI3UFpubC90MHlxdWVUWVFpQkNyL0U2bmJabkE5eGxyNnJjNmI0by8zeVhJakJLM2JkZThmRnR2dTZNWDJxNXYwTUlaMHQzdnJkWm5HS1dOWjA5MUtYamlQcmhJeHFDKzZXb0hmREwraGd1NmZpOENCZnZOcHNaS3p5YlU1c0hFSjJ3RHJYWHZhUlQyNTlPZDh6R1NRV2NpUktwS3JSQ2RjOFphN0N2QStRY1hEOE5oMHF4d2JPa3RlbkJPZ0tNTzlXZE9XVURULzRJWThscHY1cTlTbWhjaml6aDY5c3Q0bEJ0ckZzYVdzc1c1ZDR5MUpvSTNEbzVJTVAiLCJtYWMiOiJmMWI5OTM2YWQ4NjJmNDVkYTgyMzI2NGMxOTVlZjgyNWFmYmE5NzdiMmY5MTBjMWRkMDk5ZDQ4ZTU3YTE0NGRiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:09:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImR6Y1N1VC9qT3NlZlE2MGVwUHZyZXc9PSIsInZhbHVlIjoiMTE0NS81ZTJ3L1VHcHNQdC92VThtYXpvYWxGREN3RnBDaWs2VGZwVm50cVpKbU1IVHEzVnA1WmZEWGkwOHdXNjhXbzg3VmYzK0E0ZVpvUEk2dFFjcXVTRkpZSVo0cmRzdWU0Y3Evc0V2VFl3d05EUFBhbXQrVG9uMlRlOWt2L2Rua3NYZFFTa0Y1WTBFK1krTC9vZ2ltRmc4dnNjZk5XVk43QjN0RFJSL3hwb2llci9mMnowME45OGpnbXdQM011Yi96WVBYNFlVTm1HdUU0K0s2djJmT1lVck1GWG9tTFplQ0oyTVpOcG0rQ3JoOG1VTHVuZWpNaXpCQVpnVVJNV1BTOGczMmYvcGx1amRVNHZqTXVXVWtqY2N0bXhXdVNsL3VjQnlDRUVpZEJBWjBhTlM1V29ueUNzYWpibmtoSGdWSk9wQTBrMEh2Y0srK3g1dmdoZjVOM2hMaFdNdXpRTkZaSC9oT0hlTzFUR0ZBczBjazdJb2tESGkvSlZmY2JzOVBLTlVmZUVoMm1IWi9TQitCTDNLZE5tRFk4MXJIelF1Z0RudnUwcGwrdkIwRkJLTTlaRkpOdVdrZkVIcVF4MEltZldiYnlCSFJrSlI0R0JDaFg0dWlKRThDNFgwMy9jL25iWjdIZEpzcHVpeXZSeWFlbjBzVWhjUy8xV1BSdmQiLCJtYWMiOiI5ZTc0MWNjZjQ5NDBkOGZkYmU4N2ZiZjFjOGM2YzQ4YmIwMTFkYjEzNzNlM2E3OTM0MDY3OWVlMWY3MjMzNzgwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:09:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1488088818\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2000815167 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2000815167\", {\"maxDepth\":0})</script>\n"}}