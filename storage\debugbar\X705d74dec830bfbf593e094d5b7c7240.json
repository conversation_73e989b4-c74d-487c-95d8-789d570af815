{"__meta": {"id": "X705d74dec830bfbf593e094d5b7c7240", "datetime": "2025-06-28 16:02:43", "utime": **********.493198, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.037047, "end": **********.493217, "duration": 0.45617008209228516, "duration_str": "456ms", "measures": [{"label": "Booting", "start": **********.037047, "relative_start": 0, "end": **********.414828, "relative_end": **********.414828, "duration": 0.3777811527252197, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.414837, "relative_start": 0.3777899742126465, "end": **********.493219, "relative_end": 1.9073486328125e-06, "duration": 0.07838201522827148, "duration_str": "78.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45697520, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02553, "accumulated_duration_str": "25.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.44131, "duration": 0.02425, "duration_str": "24.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.986}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.474717, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.986, "width_percent": 2.193}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.48134, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.18, "width_percent": 2.82}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-434785150 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-434785150\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2074679592 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2074679592\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-60721872 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60721872\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2072436853 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126544321%7C12%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImQwSTFTdmd6ZHkrY0NVdmZQV3RwNGc9PSIsInZhbHVlIjoiUVN6UTh5Z0liN253ZXFBVUkydkV5amg0eW1aWFpvVWdXNTF3NEZoQTQwSitzRGlDeFJLNXVRbWVkMUEvS1JDNFphL1NzWU5rY0NqRC9ka1cxUU4rbFNzZDlzYXFnN1p4dC93NEpQdUlQb3RTL1JqZ2RieFNwTGtyR0VuNDBLa1lQOGlMNkdZYnBCTkpYUmwrdlJaWUw1R2hTN2x0dVFvaEtFby9ta3gvNTNBdVR2NG1TandUMXgvVzloOG96S2ZMNVg5U0I4ZEJScURQdEcrQzdaTnE1ZzBjbEJ4UitVSHhGU3kxWGFQQVBXeWsxeTNuaXpodDRzbXpZbk5Rbmp3alQ5blZkSUFCWTR6alRUeGIzYk5SalFic1dxQ3F5VG03U2hTK21Xc2ZjN00ydlNuVlpuRS8vK2hSZFo1bmNaYWQ2MlFiK3kyKy9YS2xRWTkwbnhCVVBVSkpTYkh3US9RS2lFTUxpc1ZkRHJsSFBiQ2xpdTlPWERHSHFUSkZYbWR6dGZtSXgzY09DdEpacHo5NE8wT1NwQ0ozNjV4NHJaYjUwUkdMdTdZR0NXV1ZtNmt5VS9CbDA4MFp6ZXhRTDI4TGkwMmU0QkxscklWSVlZVDIzMnRRNnpOMG10RWdPWjVPa1dLeEFRZVNKQ2VGcUJQS3ZPU2hGTTVnRFp3ZEN4TWkiLCJtYWMiOiIxYzUzYWQ1MmEyNGQ5ZDAwODMxNjcyN2ZjNGRkOWViOGVhZGZmNTVhN2Y3NzUzNGZiNjEzNDRjMzNjNTE4MjIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Inlzc1d0ZDdwZVRrNHNWdFJRdGhvY2c9PSIsInZhbHVlIjoiSG84Z3R4a3VKT2VJandaYWFNQ0laOTg2dUZZL1hCUmJIaEQya0dRcWtnNXp4dHRNOHZta1l4ZlFNTGZHaG5WVHcvaWE4SEVRb0htRVg0MnE2UVBYZk1qbzBDZkljUHA2TGNDR2hVWWFGNUJzZ0VldWpHejhyT0dnOHVRbm9wd3dpZUVTcDcyRUY0WDZoQjF3eU9KaVlDbytvRFlndjFVVFluNm9jZ2dBRTg0VUtsWnE3K1BFWGhCY0FvWDlJRHNzOGV0Y01FNE9xbkJ6dzdBa2poaEhud1BybitaSmtvUjN2enZZc1daZ0pOT1o3L1VUcEFMU2Q3bjFVaVM2WnVjU0h2YlIxS2lEcy85OTJSUzN2WXlyTXowQWRGbTdNRUptdG5lMnJJZzk2WUhEVnFUZjY3R2I2bG9oa2RIdGxXWTJZTytRWFF2QTNaVUdIdWF0R1RmZW1wVmdDZmYvUEM4VGx6aXpoakZ1NU9MMzlRcERHZzZsQkZtc29DQUc4MmR3b1hCQ0RZZ0g3L01VbTFTeUdWTUVGaGd1WldJMEhrTXhja1VYem0xNzY4d29WOXRRd0NseUlWNUw2c1FnQXk0ZE5pbWU4VXFIc213bGVsak1hMEw3THJhaW5VVWtvNXdJeU5WVnFQaWVnbFhQSGlZR2dHTTVXQ0g1V3p1cmhuZnYiLCJtYWMiOiJiMjhjYmNmYzc1MjY3YzExN2U1YmRlMGZkODFiMDhmZmIzMGVlMjQ1YzBhMjNiNDkxOGQ4ZTYxMzhlYzFmZmIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2072436853\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-504994828 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504994828\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-333268805 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im45RkJ6TEVYK01UWXpWNVl3S2k4UUE9PSIsInZhbHVlIjoidnltaytZQ3B5QXlQRFRtempIclBacnJTRlk2dEhnSytUNGRId0FmdHYwSGFvY0dQOTYzZGF6bWJza0tOeFBTd2pXMmFBcndwSmN0SDJ2S0FsemV4QmVtbVlNbHZyamY0cE92SVhHVTMrVEc5R25WeXY1THZQWjdvK1llRDYyMC8wSDBJbDVGc3ZnWkg2MkNESHFoV2F5U0s0NGh4a0p0Rm5PdEpxNjhqditWNXNQSSsxRVhUemFISDRuL1VwTW1ya3VKYXcxQWZvaDdJT2RxdU9mbjZnMTI0dFZQYmkzMWJJcklGMm5VMTV1U2RHTTkxUXg2NzBRYjlQenZ3M0QwVk1pKzZnc3gvQ0pzN21RY1hlVytSQU00MzZXNXVrcVJ4KzhKeXpaelU1QUY1MFlDeWhUOUE3SVk4enB2ekwyMzhzVzYrNFZKTkhhSmkzM2VicHFFcVk2ZUNjUWlsYnAwSmxXaU1MMVd2MjQ3WGRIcm81L2VscTJjK3ozMXh1ZVRpWU5oL1Nwclg3SzBTTngvM2hCNG1BWlg2T25NNFlTRW1oNFJ1RFREdVcrbGxJZGlFMnlMdC9haVJ6MFBROEhUK2FQaEJoQnI5VHBFMWtxakk1d2Z0eTFhWDZ5R2hGSnF2ZTBYTzYzSEdKZ1FsWWZPbC9oUlptWUhnQmRJL0RaMW4iLCJtYWMiOiIxMDRiMzUwOTEzYmI5N2VlMjYzMmUyMGQ3OTlhZWViYzI3ZTMxZGM5NWRlMWM3M2YzN2Y5MjdjYmZiOTQ2ZDQ5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRJNjNVNkZ3MVN6TFZLdmZSTHNjeHc9PSIsInZhbHVlIjoiREdpYTloSE5tbkd3NUJqMkFqMjNiQlFDckRsRTcyTWhxS2RsMXdtaTkwaS9qbjhhZzROdUpWS3FXRG1lWGQwMXVPN2dydUZZNm9laldjZUQzSFNicnhvRCtCZU9ROWVqYUpROTByWWN3SExCUFdxbUZsSjNtdE1wazFML0t4NHdmc2doYUI1N0ZhTDdEemk3T3RGVlZISk1RZzd3K28vM3RHRVBNWTM1b003RjBKb1dxREhIbVhnSVN3TEFSb3NXUHZQcjNaS29DODJUSUpLaTd3ZDZ6c25IMDhDQlk1a0FEWUxhYjV1aVJ4WWhUaENWNEVLSHltTzhQRTJ2ekJkQ3c4bHN1UndyZVpJYmlxRGV1QWg1UmEzai9Tdm1tTjl0Y3d6RkVGd3dqU0VMZXpJbkJsdGdNa0E0Vi9RWitjd0pyNkJXNVZORjFReGwyYURvSFFiR2tjUUlkUlJ3VzhwUDdscFROV2xIUDQ2VWlBdTQzRkI0NnM4clQ5V0pyQWtGTHVKMUpLNXZhQ2JZSzUxaEk5bTZtMExhRmZiM3lWTFZMRnc2K2IxcERLbW9SdDZJVEVzQ2RhbEJKQXZoOE5UcmJqSytKSlFQQU8yY3VtZTdGMS82dE5iYVBIeFMyMVpsZ09qOXlycGtKc0ZKWmNMUXFYUVNmdE1VWE9qV3kvUFIiLCJtYWMiOiJhOTA5OWI5NTMzODU3YzljYzRmZjAzZWI0Mzk5MDhkM2Q5ZTA4YzVhNzEwZTQ1ZmJhY2RmNTFkMjQ4ZDM5ZmM1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im45RkJ6TEVYK01UWXpWNVl3S2k4UUE9PSIsInZhbHVlIjoidnltaytZQ3B5QXlQRFRtempIclBacnJTRlk2dEhnSytUNGRId0FmdHYwSGFvY0dQOTYzZGF6bWJza0tOeFBTd2pXMmFBcndwSmN0SDJ2S0FsemV4QmVtbVlNbHZyamY0cE92SVhHVTMrVEc5R25WeXY1THZQWjdvK1llRDYyMC8wSDBJbDVGc3ZnWkg2MkNESHFoV2F5U0s0NGh4a0p0Rm5PdEpxNjhqditWNXNQSSsxRVhUemFISDRuL1VwTW1ya3VKYXcxQWZvaDdJT2RxdU9mbjZnMTI0dFZQYmkzMWJJcklGMm5VMTV1U2RHTTkxUXg2NzBRYjlQenZ3M0QwVk1pKzZnc3gvQ0pzN21RY1hlVytSQU00MzZXNXVrcVJ4KzhKeXpaelU1QUY1MFlDeWhUOUE3SVk4enB2ekwyMzhzVzYrNFZKTkhhSmkzM2VicHFFcVk2ZUNjUWlsYnAwSmxXaU1MMVd2MjQ3WGRIcm81L2VscTJjK3ozMXh1ZVRpWU5oL1Nwclg3SzBTTngvM2hCNG1BWlg2T25NNFlTRW1oNFJ1RFREdVcrbGxJZGlFMnlMdC9haVJ6MFBROEhUK2FQaEJoQnI5VHBFMWtxakk1d2Z0eTFhWDZ5R2hGSnF2ZTBYTzYzSEdKZ1FsWWZPbC9oUlptWUhnQmRJL0RaMW4iLCJtYWMiOiIxMDRiMzUwOTEzYmI5N2VlMjYzMmUyMGQ3OTlhZWViYzI3ZTMxZGM5NWRlMWM3M2YzN2Y5MjdjYmZiOTQ2ZDQ5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRJNjNVNkZ3MVN6TFZLdmZSTHNjeHc9PSIsInZhbHVlIjoiREdpYTloSE5tbkd3NUJqMkFqMjNiQlFDckRsRTcyTWhxS2RsMXdtaTkwaS9qbjhhZzROdUpWS3FXRG1lWGQwMXVPN2dydUZZNm9laldjZUQzSFNicnhvRCtCZU9ROWVqYUpROTByWWN3SExCUFdxbUZsSjNtdE1wazFML0t4NHdmc2doYUI1N0ZhTDdEemk3T3RGVlZISk1RZzd3K28vM3RHRVBNWTM1b003RjBKb1dxREhIbVhnSVN3TEFSb3NXUHZQcjNaS29DODJUSUpLaTd3ZDZ6c25IMDhDQlk1a0FEWUxhYjV1aVJ4WWhUaENWNEVLSHltTzhQRTJ2ekJkQ3c4bHN1UndyZVpJYmlxRGV1QWg1UmEzai9Tdm1tTjl0Y3d6RkVGd3dqU0VMZXpJbkJsdGdNa0E0Vi9RWitjd0pyNkJXNVZORjFReGwyYURvSFFiR2tjUUlkUlJ3VzhwUDdscFROV2xIUDQ2VWlBdTQzRkI0NnM4clQ5V0pyQWtGTHVKMUpLNXZhQ2JZSzUxaEk5bTZtMExhRmZiM3lWTFZMRnc2K2IxcERLbW9SdDZJVEVzQ2RhbEJKQXZoOE5UcmJqSytKSlFQQU8yY3VtZTdGMS82dE5iYVBIeFMyMVpsZ09qOXlycGtKc0ZKWmNMUXFYUVNmdE1VWE9qV3kvUFIiLCJtYWMiOiJhOTA5OWI5NTMzODU3YzljYzRmZjAzZWI0Mzk5MDhkM2Q5ZTA4YzVhNzEwZTQ1ZmJhY2RmNTFkMjQ4ZDM5ZmM1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333268805\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1342530947 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342530947\", {\"maxDepth\":0})</script>\n"}}