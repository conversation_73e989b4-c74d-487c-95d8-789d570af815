{"__meta": {"id": "X08d08839c085d2beaa760261d61aaaa7", "datetime": "2025-06-28 15:26:27", "utime": **********.97392, "method": "GET", "uri": "/pos/1459/thermal/print", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 163, "messages": [{"message": "[15:26:27] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.931319, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.931576, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.931698, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.931786, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.931859, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.931938, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.932041, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.932164, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.932266, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93237, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.932465, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.932564, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.932661, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93276, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.932849, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.932953, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933049, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933146, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933241, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933322, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933415, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933513, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933714, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933853, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.934035, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93417, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.934266, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.934366, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.934457, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.934559, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.934669, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.934777, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.934892, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.935005, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93514, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 80.60000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93525, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 84.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93535, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 84.20000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.935449, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 87.60000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.935558, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 90.20000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.935646, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 92.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.935746, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 95.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.935839, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 98.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93594, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 98.60000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936038, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 100.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936144, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 102.20000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936249, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 105.60000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936349, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 107.00000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936434, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 109.20000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936531, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 110.60000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936616, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 114.00000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936719, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 115.40000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936835, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 118.80000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936943, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 119.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.937155, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 122.40000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.937309, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 123.8000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.937421, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 127.2000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.937512, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 129.8000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93761, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 132.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.937707, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 134.60000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.937779, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 139.20000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.937845, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 139.40000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.937917, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 141.60000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.938009, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 143.00000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93812, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 145.20000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.938224, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 145.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.9384, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 151.20000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.938552, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 151.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.938681, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 153.60000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.938796, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 155.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93889, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 158.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.938998, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.939092, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 164.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.939206, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 167.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.939305, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 169.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.939407, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 169.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.939509, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 171.6 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.939613, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.948595, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.948787, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.9489, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.948996, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.949125, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.949245, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.949362, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.949471, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.949566, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.94968, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.949797, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.950001, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.950143, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.950268, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.950386, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.950509, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.95064, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.950744, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.950852, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.95096, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.95107, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.951173, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.951278, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.951384, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.951496, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.951624, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.951751, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.95186, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.951959, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.952137, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.952301, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.952426, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.95258, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.952697, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.952781, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.952891, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.953013, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.953161, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.953379, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.953564, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.953695, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.95378, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.953861, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.953945, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.954037, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.954152, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.954267, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.95438, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.954493, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.954595, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.954691, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.954796, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.954878, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.954968, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.955057, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.955167, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.955271, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.955377, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.955493, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.955619, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.95573, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.955815, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.95589, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.955981, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.956092, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.956198, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.956304, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.956405, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.956514, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.956607, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.956742, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.956911, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.95704, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.957145, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.957225, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.957327, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.957449, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.957546, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.957627, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.957703, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.957802, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.957887, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.957961, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.958327, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.9586, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:27] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.958747, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.391097, "end": **********.974092, "duration": 0.5829949378967285, "duration_str": "583ms", "measures": [{"label": "Booting", "start": **********.391097, "relative_start": 0, "end": **********.812192, "relative_end": **********.812192, "duration": 0.4210948944091797, "duration_str": "421ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.812206, "relative_start": 0.4211089611053467, "end": **********.974094, "relative_end": 1.9073486328125e-06, "duration": 0.16188788414001465, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52339904, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.thermal_print_clean", "param_count": null, "params": [], "start": **********.926289, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/thermal_print_clean.blade.phppos.thermal_print_clean", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fthermal_print_clean.blade.php&line=1", "ajax": false, "filename": "thermal_print_clean.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.thermal_print_clean"}]}, "route": {"uri": "GET pos/{id}/thermal/print", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@thermalPrint", "namespace": null, "prefix": "", "where": [], "as": "pos.thermal.print", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1709\" onclick=\"\">app/Http/Controllers/PosController.php:1709-1768</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.00786, "accumulated_duration_str": "7.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.865525, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 27.735}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.879576, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 27.735, "width_percent": 9.16}, {"sql": "select * from `pos` where `pos`.`id` = '1459' limit 1", "type": "query", "params": [], "bindings": ["1459"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.883522, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 36.896, "width_percent": 6.743}, {"sql": "select * from `customers` where `customers`.`id` in (10)", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.889682, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 43.639, "width_percent": 8.142}, {"sql": "select * from `warehouses` where `warehouses`.`id` in (8)", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.892454, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 51.781, "width_percent": 6.489}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` in (1459)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.895293, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 58.27, "width_percent": 24.555}, {"sql": "select * from `product_services` where `product_services`.`id` in (0)", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.899256, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 82.824, "width_percent": 5.725}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1737}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.915039, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 88.55, "width_percent": 6.616}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 121}, {"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 21, "namespace": "view", "name": "pos.thermal_print_clean", "file": "C:\\laragon\\www\\erpq24\\resources\\views/pos/thermal_print_clean.blade.php", "line": 279}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.9403749, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 95.165, "width_percent": 4.835}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\PosProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPosProduct.php&line=1", "ajax": false, "filename": "PosProduct.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1459/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos/1459/thermal/print", "status_code": "<pre class=sf-dump id=sf-dump-887305310 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-887305310\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-468554058 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-468554058\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-657092823 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-657092823\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1631813542 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124349103%7C15%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkY0Tk9ZMG42bHprNGx5K2IyVjZYeGc9PSIsInZhbHVlIjoiUytnVGJyTUVnRzB1dmpoeDNQMzEreVBpWnBUVnhsK01XdnN5dGxkSzN1Um04dW8xY1ZTbkFOdFFOeHhzZ0pSTDgvRk5icmtyc0tRY1FMa04yWktubnBaSW1GRUdlSWdldHkwdWFPWUJoVmtwSUR4VVhpMG56L0NuQldjTmNIUkN5RkwvdVUrMHJiQ0VpT2ErNC8rUFVqY1VVR3BKNlY2TVhZSCs0WXI4c0tCaVA2ejZkeU9zZnpFWnE0dzdOV1dobStjamdtZXJzMUFValpMZVRLWUF1QlhHTlpEd1ZNVWVOaVFLUzAwaFZBenRYV3FwMXdzaU5KOS9wazNnR3BLcnkra3F1REFSekY3aWV5V1A2eGh6QUJtNDgvWmN5K0V6NWt4OWNpdnFYNDd6MU5ZTU1qT25MM2RkL1lody9NenhMejFlbkNqYlZGbzRsU3NMQUV3YmF5a2orSTNjdVVEMjZyNWp0RnIrbzN5eFFGOEVZV0hRVGxGbytwVHFiWXR3ODBEM3pnRGxDc09sdVhlc1YrdGw0YjVEVHVDYnlwVlNNbThpd0xlbE9udEtDZnk4Qzh4ZkVGZkovWVBRdDB6TU1jTlFTZGtXaTFNaU5TTGY5MzRPajMxVHZ2OEJsTktnWVRxOHlSQml4cWVuY0UrMVcrMXNldVR5Z2ZyQjNjQnEiLCJtYWMiOiIzMzBhNzRiOGEyZmM2MGZjOTg0NzZiMDk2MDJhOWYwMWU4Mjk0MmI4MzVjN2RkOTg2ODg0MmY2Y2IwMDFjZjNhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImRoZ2p3TnB3ZlMrNGJpMkNZalNMSUE9PSIsInZhbHVlIjoidTF3c0R2UE5FRDZ4T2FZUlRaYWtuN2FTMXVmcDN4bVFEai93RkZ1YXl6dWxiTG0zamIyTEs3ZTNsb28zWFNrdnZLQWNMd0JPam9iNm0zSDN1NjYyQTdjNHdOa2RvWTl2aFpqQ29VSUEzOVVUZE5rbldPVHVRMlNyUUpybzV2MXkwSVVHS0w3YjVnK2lkQTRPWDdjK3piYjdkVzRvU2JRVEMvSkprTG9FbDN4d1JHdEkzdWtvWDllUGh1c1JJTFpBUVdlcDBxY2lOWTFaRGFpSGtmYkZuR0NyWlVqY296cGdmemtXOVY4VzhvVWFobWVwZjY1SGdpd2JZbmx4Q3NmSnM3NUpmaGNjU2xRclpHNUNRYTdPZ1lZYlJLZWsrUVY3d0VuckwwZm1YaEpYRmRIWGsyYURhNTliUHpLZzNRSUY0UFhlTnZYQ3pUMEF4UjU5QnhWMWtjZGo1d3pnVEQ0eC9ZanFUWTRVVWxKMHQ3WmRGL3lMN2lINEwrUktpQ0ZXNE51Z050aHNLUjRKOGhPNzNXQjYvYnpXY2FRMVl5MmEwSUNBSmZUWm5zZ0piYzF2YUM2SCtxeUYzMEpvL2h6cTY1T1FSRzJMVnFaRktXZjRKTENPUFRTZjhBUjhrQ1JaaGRFckxYNEVmWVV4RURLQkF1b0ZRVmFGZmJPZzJJWEMiLCJtYWMiOiJjODUzYzg4NDVjMWFlMmJjZjAxYmIzNDBmN2MzMDc5ZGI5YmRkYzBiZGQ0NzJlODg4ZDU5YzkwODg3ODU3MjA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631813542\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1797441805 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1797441805\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-861499197 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:26:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im8rc2FYMnlTUHpIalRvcTBNbWp1elE9PSIsInZhbHVlIjoiMHRWNllrYXoyaG9jVUlLYXpIOWNuK2NxYTkrVk5QajJhY3dxbmlwMkM5YnE2Vkc0TE92ZEcxUlExV3FMb0pwUEl0WGkzQ3FYVHhkQjI3cDl2dE9iejZDV29VQXh2S3hKU21DbmpPYy95RGYveGEwc2NJdHpYNk5Lb1hwbm5zU0gyR3pCSHhDNEtzOXBMdUJSRURoejArZHJiQldTdCs1dGpkcG94MHZzUXZHcCszT3lmT2p0eHM1T09qMC9sREVRZDkvY09iQWtEUXdtamJFMTMxelh1MWhwa29DcVdPcTRnajN1ZjVUSDlmUXdWeFRHN0pOQWJ6T0tVMDNhdGRLdWluOXRRcFhqTzRMQ0U3OGkvYWR5SE1KcFBqR2lRVlB2MW4xaU5FbTB3amxxazVEbnluVUl5MEhrYk9WU05hY0FIZVRmRmRjM3lKaFJIYXpNaDBiN1VZRnk3MlNHWEloYzZKYlA2aGVVdmUxWVJ3NzVLbEFPaCtFZmYxRWZnaDkxYTRmcHVkQzBIVlBXWi94ajRYbUNyV081UlRBcm5od1czOEpvaEdQSFJ3Q0VrV3IrSkdTTDJ5T0JnOGhCSkFWT2VJbXl2c1pRVU5jVExncUVwZ2E2Y1FndUJjcXRObjh3QzQ2RDl5RnVxZ1FJUUVKQnZRc0FWK2IrNWp3WTBwZnUiLCJtYWMiOiI2YTJjNmZlZmRhNDczYmVkOGJhY2FmMWIyYWQ0MmZkYTg4MGU2ZDQ0ODUyNDUzMDg1MDA2ZDljY2I1MWI2MTU0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5QSG00ckVNT3B6Q2h0cEZ5dmN2Umc9PSIsInZhbHVlIjoiS0IvQ3ZOY3MyS0RNa1poUC9EN2RpOStraEJLLzEwTWYrdkFkaGtHMnFMM2pIeVBsNTZlVGNxNTNWeWRjTitLRjJIMDA2NnJVL0d1TlV5bGZYUFRsSFNJNW5qY2NEOVk3cEgvNHd6QnduR0dtZVVMT1FLTFdRNkUyc1daOTBFb0tzbldncGlGQmViRlQwQnNWTkVXNi9JY3FEY3AwZlFUT3Vodmw1RE1Yb2tPdDVtcDEvdlh1ZkYraDhpaE0zYUNGeUtFVXgyL3pzQzNJcWxtWHRhVmVxbnRaQkRrN0hmOEY2NU0xWTVHS2pMZ2JMS002bVNyT2x2Vm5ZbldnNWdIak8xOXU0U3FIRDVtaHNpT00zWHJzMXl1VVZ1Nnk3SGhoeUs0MzdodURrVDIvdEIvYmsyVkhReTBGaXBaa2JydHhYc1lCUnlwQXMwU1I5TC9GaUhDbDZ0dHE5amhPeXRvci90d3JWbmR4dXVQZ0g3UDRyaUtiRGxnN0VaVTAvbGluN3BIaDcrTmdma3RNeFJTWmx2WlJDazhKWnN1d0ROVHVidVlVcDBFVGFwMmc1ZHVQcVFENVhFNzgzSEt4TkZuTWp3dTEyNW1pOVQ2aWRFYnllMzlTRFN3ZGV3NHVVYVdyeFdPZWlDV3V6eEd3V2dKMXp2QWQxRFFlaGJ1bkJ5U2IiLCJtYWMiOiI3YjMzYTcyYTZhZDhmYjRiZGFkYjNkYjlhMzIzMDAyZDMwYTBlZjllNDJiMDUxNTg0ZmY2MWZiZjQzNzE0NjlhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im8rc2FYMnlTUHpIalRvcTBNbWp1elE9PSIsInZhbHVlIjoiMHRWNllrYXoyaG9jVUlLYXpIOWNuK2NxYTkrVk5QajJhY3dxbmlwMkM5YnE2Vkc0TE92ZEcxUlExV3FMb0pwUEl0WGkzQ3FYVHhkQjI3cDl2dE9iejZDV29VQXh2S3hKU21DbmpPYy95RGYveGEwc2NJdHpYNk5Lb1hwbm5zU0gyR3pCSHhDNEtzOXBMdUJSRURoejArZHJiQldTdCs1dGpkcG94MHZzUXZHcCszT3lmT2p0eHM1T09qMC9sREVRZDkvY09iQWtEUXdtamJFMTMxelh1MWhwa29DcVdPcTRnajN1ZjVUSDlmUXdWeFRHN0pOQWJ6T0tVMDNhdGRLdWluOXRRcFhqTzRMQ0U3OGkvYWR5SE1KcFBqR2lRVlB2MW4xaU5FbTB3amxxazVEbnluVUl5MEhrYk9WU05hY0FIZVRmRmRjM3lKaFJIYXpNaDBiN1VZRnk3MlNHWEloYzZKYlA2aGVVdmUxWVJ3NzVLbEFPaCtFZmYxRWZnaDkxYTRmcHVkQzBIVlBXWi94ajRYbUNyV081UlRBcm5od1czOEpvaEdQSFJ3Q0VrV3IrSkdTTDJ5T0JnOGhCSkFWT2VJbXl2c1pRVU5jVExncUVwZ2E2Y1FndUJjcXRObjh3QzQ2RDl5RnVxZ1FJUUVKQnZRc0FWK2IrNWp3WTBwZnUiLCJtYWMiOiI2YTJjNmZlZmRhNDczYmVkOGJhY2FmMWIyYWQ0MmZkYTg4MGU2ZDQ0ODUyNDUzMDg1MDA2ZDljY2I1MWI2MTU0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5QSG00ckVNT3B6Q2h0cEZ5dmN2Umc9PSIsInZhbHVlIjoiS0IvQ3ZOY3MyS0RNa1poUC9EN2RpOStraEJLLzEwTWYrdkFkaGtHMnFMM2pIeVBsNTZlVGNxNTNWeWRjTitLRjJIMDA2NnJVL0d1TlV5bGZYUFRsSFNJNW5qY2NEOVk3cEgvNHd6QnduR0dtZVVMT1FLTFdRNkUyc1daOTBFb0tzbldncGlGQmViRlQwQnNWTkVXNi9JY3FEY3AwZlFUT3Vodmw1RE1Yb2tPdDVtcDEvdlh1ZkYraDhpaE0zYUNGeUtFVXgyL3pzQzNJcWxtWHRhVmVxbnRaQkRrN0hmOEY2NU0xWTVHS2pMZ2JMS002bVNyT2x2Vm5ZbldnNWdIak8xOXU0U3FIRDVtaHNpT00zWHJzMXl1VVZ1Nnk3SGhoeUs0MzdodURrVDIvdEIvYmsyVkhReTBGaXBaa2JydHhYc1lCUnlwQXMwU1I5TC9GaUhDbDZ0dHE5amhPeXRvci90d3JWbmR4dXVQZ0g3UDRyaUtiRGxnN0VaVTAvbGluN3BIaDcrTmdma3RNeFJTWmx2WlJDazhKWnN1d0ROVHVidVlVcDBFVGFwMmc1ZHVQcVFENVhFNzgzSEt4TkZuTWp3dTEyNW1pOVQ2aWRFYnllMzlTRFN3ZGV3NHVVYVdyeFdPZWlDV3V6eEd3V2dKMXp2QWQxRFFlaGJ1bkJ5U2IiLCJtYWMiOiI3YjMzYTcyYTZhZDhmYjRiZGFkYjNkYjlhMzIzMDAyZDMwYTBlZjllNDJiMDUxNTg0ZmY2MWZiZjQzNzE0NjlhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861499197\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1353005252 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1459/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353005252\", {\"maxDepth\":0})</script>\n"}}