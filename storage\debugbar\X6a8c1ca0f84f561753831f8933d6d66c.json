{"__meta": {"id": "X6a8c1ca0f84f561753831f8933d6d66c", "datetime": "2025-06-28 15:43:06", "utime": **********.819671, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.242142, "end": **********.819692, "duration": 0.577549934387207, "duration_str": "578ms", "measures": [{"label": "Booting", "start": **********.242142, "relative_start": 0, "end": **********.745092, "relative_end": **********.745092, "duration": 0.5029499530792236, "duration_str": "503ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.745105, "relative_start": 0.5029630661010742, "end": **********.819694, "relative_end": 2.1457672119140625e-06, "duration": 0.07458901405334473, "duration_str": "74.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45163768, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00286, "accumulated_duration_str": "2.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7919621, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.629}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.805169, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.629, "width_percent": 16.783}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.811442, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.413, "width_percent": 12.587}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1410556251 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1410556251\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-396297141 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-396297141\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-245132449 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-245132449\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-816794194 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; XSRF-TOKEN=eyJpdiI6Imt5T1dOT0p2R3FUTytkR2YxUWY2NXc9PSIsInZhbHVlIjoieGFRVHBCWUxZZ2M4VFhuVjZmdGJ0Nm9LVU05bVp4dUxURUNCTWdqVzlnUTVxV1BnM3FqTjdmRDNTbXkyanlCMTFtdlM3K0o5dVRBTGdORTBQTkhMUHZZNFRFelZmRE5pdDdRb1ZqVWZoMW5yTFpHR0ZWUGRCU2JKRmxzdVovYjN2Zk1qMzBYZDRSNWk5UTlRazd2SkRwaEZ3dmppY1JudUFzMUFuWFR2UDJHRUJDYVZxOWZHNWNab013ajFEaDFDVG11SzlpM2V3TEpmbGdQYnRMZDhTYmtnRHNOWmduMFR2ZXVON2JyWEFoN1FPbW1MZDJxSGxuOSs4ckVCT2NvWUgrWDBIYjFsaWNvSEMxRDVpV3FYMkV3Slp0QzZkTklIYjJYWlEwbHQ5UGxSaTdMbkhSOHNrWWF3R1NCUVFiRjh3YS8yZFVKbEo1T2o0RTVENGI5NHhzdnJlSWl2bjlpeUtRcmVPRlJyVFljdFU2cnp1UHF2MWtEQm9XVUI1UHJ6N1JXdHpxQmtmOGZ1MHFJLzhNTGZPL09DNEdBL0RDNXBvUldNVWxUY0tmZ25TYm1YeGYvRGxRKytOU0FXN1B2bXBqWUREZ1ltVGU1WWZXb0IxdWMrU29mdkhrdXVvTFNnNk5PN0hHU3poUnpBWS9qblRucittSFhyU1NvL2ZqaDQiLCJtYWMiOiI5ZWJjOGY5NmE4NzI3NDU4ZTFmM2Q5NDhkN2QzNzJkMTk3NmZjZmFmYzE2ZDM3MGI4ODJkMzAzZmM0YjEwM2Q2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBaa094L0ZwRW9CWHJOUkRpSFZqa1E9PSIsInZhbHVlIjoiaVFCMHc5TWo0YUFkemlFZ04wUDBRZjdQUlpmVXBiMjY2VlNCLzVxZmtVT0VTalNtUnZvaElGak5jYTRZYTBKM2RhMkdqTXl2TXdRUW00ZjdzcXp4RkpHbnI1Y0gycE1NcVVSbnRRQmpCM2IrZzhwUHIxNm1zejRHNjZ1b0NTWDRhcVByVnptdXVGcVp2Y3NaelBQOGN0aFJHekNXK1QzZVBCVzcyNC93TUNyV2swdGova3R5YWphcUREOW9UZ1RGQThkMVd1cTU5c1A2WEswRlljTFF5Yjh5cTVYYmVJME5HcVZHZkVpcDQ0RFNaODVpK3FGTm5qOVpiM1FtTHBHQUlVaTN0OG1uZXFsZHQ3OHNrcnNBeko4TGN3b1k2TWpmNVFxc0xCY01LVGZZTDVkTjEvd2xYNjVjMU9iODVYLzdIV0tIZ2s3SitNQ1VTUUZJMUI0Q3M0TS9mOWZvTkVXYlh5dEFpaUJMMHZzZ3o4ZnVUa3Q4NXJhdDNBMDBoa0FtYVN4NVYvM0tTTHdiamd1Mk1la1JZTTZqemppVUNraFJpcDNjZ1l1bVdIbk9hckhBQ21kUkgzTUcrVGpESlBVL1BPYm01VjQ0UHFCUWlHQithbFhFQThQdVltbDRKU0cwM05CMkJVZEZhWjJYS2owNldLK1FFbENYa3RUNTBtczIiLCJtYWMiOiI5ZTYxMDBiYTUzZmM2NjhkMzE3MjY1M2M1YmU5ZjJlNDBkNGI2NTZkNzQ4ZGU5MjE3ZDgyYjk1NDhiNDg5MTA5IiwidGFnIjoiIn0%3D; _clsk=mwmaeg%7C1751125383583%7C16%7C1%7Cz.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-816794194\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1034315928 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034315928\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-781516922 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:43:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZmSktWcjd0ODJUWDF2QWlSeXRiSWc9PSIsInZhbHVlIjoiM2NOLzhnSHMyL3FhYXMzdGxJVG5ZUzI3ZWNCZjZYSHRrNmw3NnQrMVNxVm5kWkxNVnhOcDRUUlJKZkJJVmFJSDNUclpHSEVUbGlhOTA2ZGQ4MUlaaGhVWGxrL1JTa1U4OE1jWmF0d2lKOXdaSXpaWm5HUlc2WSs0ZFlQS2dZbW82T0MrZ1VOT3BmWmRjQmRmWnZMQjBMbE9xems2VUpvc0U1RER0ZjJBYU9FOVBQYWVkNjdDNk9DUlRnUVB4dlU2R2ZEWXBMdjA2YStrOUtJY2lKSjZpWmN5K1NqMHVWMzJ6RkE0M0FkS25vSmpEd1dscUFkSXM1b3F0d01GcHNnNlFvei9CZzVOUzNqdkdvdFNWQ3ZDOEtjMlIvYnJaS0VuZjhIM2YwR2tQU0p4aDc1Ry9LMFRPUitQZ1pQZitrSFpOQXZ3WnNYdFA2aWdodTczMGlYTUJnY2xScE0yVXR5bEVoMjc4WFI3QVk0TEl3bVI1MVdiVEZ2YUxBVXFZMVVTL2dER0picEFwSCtPNm81eUZudkFCVDVicWdUeWJMcDZHa0xaR0wzTXRiQXEwOWNFVkYvODVBdkpZWStqV01CemQxZDBsVy9kQW83UjVDOWd5UXBmdG5zVW0zbElUT0RadlhlMGVYNnRTUHdjZkpVcnFMVEIzVS9mTHVPNU4rMVgiLCJtYWMiOiIyODgzODkzZTJmYjZjMDM0NjZkNjQxNTk2Y2JlZTJiZDc4NWQ0MzM5MmRlMTFlODBhYjQzOGM3NDI3MDg3M2RjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJHVDE4RWZSNXdsamxkMEZGZG8zMkE9PSIsInZhbHVlIjoiWSt2MUJ2MGpnbTh4REJFRzVhSTR2bS81UVlqeDFXQzBsenYvRUJqMmFQNTVpKzhuS0pBRkhkbnQ4MERxMnJnbytiV0YyMUY4elVLeTV2OHRkdnA0YzFkOGJyK0lIdWlUR1dUTlg1LzZWR1A2dmZEclcxM0xsOGJuSDdYN01UVDVJa05ncm1acCsyOTJQMU1DOUYyOVZjaXNDZTVnRjJXdlhNa3VtZTlENmNESmRScXc1S2M4LzU5aHZKbjBWa2RwNVhVWVFVdkJ5S1ljczhIQUh0ZTdLVDlJKzc2bXNid1Y3NlIxWVVWS2V2YzN0SWhkS1hUOTVHcW9iSTlsTzhTb29rbzNPREFRTExQZ25rQlRscXptTFZwSjZUL2pXOGs5RzBrNXJab2ZiZk5KN3prN250YTA2U0F6LzNqYm82YXZ1VmJ5T1lsWXNvNWZoS281Nnk2QXMzRVJtejlwVzd2Nm56SWVFMEp5NEI5MndDQmJFSkdMdWZqcU9LbHNsMk00dUlncGJpTUNKdzE2ME91T3JGbDZMUVRmU2dHRHczeGd4V3lqSHN1eVRDMjFyeGhUTkx4aFBUTmowd09TblF3RG5rUStaQm9ncFBkYjU4dHRIcUVWYU15L0hmb2luM2RiNGVqTnpSL2EzeURiWDRVSVN0Zkg3cURlV3ZXV2hMaHciLCJtYWMiOiJkMzcyNmRiMWYwNjNjMDEwOWNjMTNhNGMzNGU4NDI4YWY2YmY5MTIyNDQ1YWRmMDIwNGYyM2VhOTllMjMwN2ZmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZmSktWcjd0ODJUWDF2QWlSeXRiSWc9PSIsInZhbHVlIjoiM2NOLzhnSHMyL3FhYXMzdGxJVG5ZUzI3ZWNCZjZYSHRrNmw3NnQrMVNxVm5kWkxNVnhOcDRUUlJKZkJJVmFJSDNUclpHSEVUbGlhOTA2ZGQ4MUlaaGhVWGxrL1JTa1U4OE1jWmF0d2lKOXdaSXpaWm5HUlc2WSs0ZFlQS2dZbW82T0MrZ1VOT3BmWmRjQmRmWnZMQjBMbE9xems2VUpvc0U1RER0ZjJBYU9FOVBQYWVkNjdDNk9DUlRnUVB4dlU2R2ZEWXBMdjA2YStrOUtJY2lKSjZpWmN5K1NqMHVWMzJ6RkE0M0FkS25vSmpEd1dscUFkSXM1b3F0d01GcHNnNlFvei9CZzVOUzNqdkdvdFNWQ3ZDOEtjMlIvYnJaS0VuZjhIM2YwR2tQU0p4aDc1Ry9LMFRPUitQZ1pQZitrSFpOQXZ3WnNYdFA2aWdodTczMGlYTUJnY2xScE0yVXR5bEVoMjc4WFI3QVk0TEl3bVI1MVdiVEZ2YUxBVXFZMVVTL2dER0picEFwSCtPNm81eUZudkFCVDVicWdUeWJMcDZHa0xaR0wzTXRiQXEwOWNFVkYvODVBdkpZWStqV01CemQxZDBsVy9kQW83UjVDOWd5UXBmdG5zVW0zbElUT0RadlhlMGVYNnRTUHdjZkpVcnFMVEIzVS9mTHVPNU4rMVgiLCJtYWMiOiIyODgzODkzZTJmYjZjMDM0NjZkNjQxNTk2Y2JlZTJiZDc4NWQ0MzM5MmRlMTFlODBhYjQzOGM3NDI3MDg3M2RjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJHVDE4RWZSNXdsamxkMEZGZG8zMkE9PSIsInZhbHVlIjoiWSt2MUJ2MGpnbTh4REJFRzVhSTR2bS81UVlqeDFXQzBsenYvRUJqMmFQNTVpKzhuS0pBRkhkbnQ4MERxMnJnbytiV0YyMUY4elVLeTV2OHRkdnA0YzFkOGJyK0lIdWlUR1dUTlg1LzZWR1A2dmZEclcxM0xsOGJuSDdYN01UVDVJa05ncm1acCsyOTJQMU1DOUYyOVZjaXNDZTVnRjJXdlhNa3VtZTlENmNESmRScXc1S2M4LzU5aHZKbjBWa2RwNVhVWVFVdkJ5S1ljczhIQUh0ZTdLVDlJKzc2bXNid1Y3NlIxWVVWS2V2YzN0SWhkS1hUOTVHcW9iSTlsTzhTb29rbzNPREFRTExQZ25rQlRscXptTFZwSjZUL2pXOGs5RzBrNXJab2ZiZk5KN3prN250YTA2U0F6LzNqYm82YXZ1VmJ5T1lsWXNvNWZoS281Nnk2QXMzRVJtejlwVzd2Nm56SWVFMEp5NEI5MndDQmJFSkdMdWZqcU9LbHNsMk00dUlncGJpTUNKdzE2ME91T3JGbDZMUVRmU2dHRHczeGd4V3lqSHN1eVRDMjFyeGhUTkx4aFBUTmowd09TblF3RG5rUStaQm9ncFBkYjU4dHRIcUVWYU15L0hmb2luM2RiNGVqTnpSL2EzeURiWDRVSVN0Zkg3cURlV3ZXV2hMaHciLCJtYWMiOiJkMzcyNmRiMWYwNjNjMDEwOWNjMTNhNGMzNGU4NDI4YWY2YmY5MTIyNDQ1YWRmMDIwNGYyM2VhOTllMjMwN2ZmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-781516922\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1831720609 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831720609\", {\"maxDepth\":0})</script>\n"}}