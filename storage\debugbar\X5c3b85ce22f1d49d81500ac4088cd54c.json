{"__meta": {"id": "X5c3b85ce22f1d49d81500ac4088cd54c", "datetime": "2025-06-28 15:50:10", "utime": 1751125810.011531, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.60324, "end": 1751125810.01155, "duration": 0.4083099365234375, "duration_str": "408ms", "measures": [{"label": "Booting", "start": **********.60324, "relative_start": 0, "end": **********.95343, "relative_end": **********.95343, "duration": 0.3501899242401123, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.953439, "relative_start": 0.35019898414611816, "end": 1751125810.011553, "relative_end": 3.0994415283203125e-06, "duration": 0.058114051818847656, "duration_str": "58.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46389472, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2388\" onclick=\"\">app/Http/Controllers/PosController.php:2388-2422</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00246, "accumulated_duration_str": "2.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.993252, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.049}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": 1751125810.004428, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.049, "width_percent": 21.951}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1467020106 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1467020106\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1702041047 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125486476%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjExOHNWamtQb0VjUXJ5ZGNmRTZOU0E9PSIsInZhbHVlIjoiS2U1REdPOW5MTmQ5RkJYY3J1ZHZ3SUpCUjJzK2NqK2JGcjVWNkpvdGgvaEs2T1BzS3QyMkhNN2xkUnNTOXhhUk9IN1BVWm9WMFg4RkhaRXJJU1dTU2tjYlE1R1ozNlkrRC92UndUNXdrOFZnWU8vV0JEcm9DVXVTSTYwU29QODNUa3lZQlZmUzgvU2NoYWRvWUxYdXYwTWdsajR4blJhMG9lSk9MNEVzTDc2a3JCeFlsN25pMDFjMnlyZjVSdTBQMHBid2lnTHFnZ0xRbDJvMDFhNUNqcHlIc1czNHNzZ01nWXZLT1dVYy9CVUtlZ25UUm5nYW9CZnliSjgrSyt2ZzNnclYyZUJCdXJKMjB0RDdOM1k3RWRNWXZPVll6dDZ2Q2FubGUvRXBjcUFTQ1R4MVZxQzJUNzYvMWs2a3paNlV3UDZWbFhBODRJV3ZYdStYU2FFOERzZnI5akNuRU0yaVV4RGFFdjFSQXp5Yll1Q1BJaVpuY1o5U1hZV25zNUpWUkYxNkkxc3UyZWJNcURPNERHSzJ3Wk51VTFramVXWWRJZHdWOVBkZVdvU2JNa0pta3RRS2xGTXQybTBsS25Iclo5R1M0a2Mvb1RoeWNNUzFUblpMTnltZlEwMVdnc3NsdUwvVEJBWEJIV0dVb0RBMkV1cTZDR2ZWQnhSZ3NkVXMiLCJtYWMiOiJhNTc0NzIwOTljZjhiMzZiZmE5M2I1ZWI2ZGE2ZWIwOTEzNWViM2JkOTVkYmJlY2RiMGNlYmUwYzNhOWIxMDIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVmSTFTZFNXRHk3SVJzU0VEcTJSekE9PSIsInZhbHVlIjoiaFY2T09NcjRSc0JtQmdDSk54U1JkSi9vbWc3bTZ5d05XcC9oQXBaeXc4QWMyNDFSanFRMFRHUURVMEJpSTlWNkgzNUY1ZmRzblA1cGQzenlnektFaTBDSnV2SjU4QjdWTTN3b2dXaGRxTnZKRDFISnFDNmVDbWg2a1g1WVJxZ2RlcUVSS1o0SnhHWkpCdDZTT2J1aExIK2lhT1p1SlBrZ0pNOHVqdmVlL09DeHZRWU5oSzlDR1dWNTdvaUJGRjF5QTdJTGlHNjZQcHY2TXhueksyelR1TW5Na1BXM0xjaHRBb0J1SXBXNlJkNDcrMU82SktqZ3I2YWozL0RMQ0U2V3V2RFZtRjY5QXVub0FxVElYbmxaQUI5U3Nmd0psQTFuUVJtRUJLNTVDUFdsS1ZzdVIwZU16MWdjTnZ5NnZwUXhtRkg4VkxHUWozVlpWSjJkd3NJTUhmTTBCMzVkd2ZRamhDK0lQTUhhTnZJTk8rYlhEblRwNmtqaVVNcGtrRFlERGIzbWIxTTFBaUZhZnJ6TjNCK2dHeVhnajhSUU1HWEVBRGNwZjZETVp2MHdFSi9yOWJ4dzBEdXIyeU91SnJEVk5xV1kvMTNKTzgwRUdKQVVTdWY2RTRoVy94b2FOUFpkQXVtazJWN0tQWEVVQTNjdllRZXN5NHFQYkJCbVY0azYiLCJtYWMiOiI5NTQ4ZDVmMWI0NjI1ZjI3NDZjZTdhNWRmNTM0OGJlMjAxMWJjYjA3Mzc5YThjZGM2ZmY0MTdkZDI2Y2QyNTA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702041047\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-953196108 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953196108\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-26318285 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:50:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBvMlFnNGMvWUMvNnZVUFZxMStLMVE9PSIsInZhbHVlIjoiN2Z0RFAvU0RKWlVVQk1VUWZpUVJOdnphTnpiMGUyWWNjbnpXWlhKRjJxQi9kNjJVd2VST3lXOWhaTlp5TlhSWGxldzBWd2o2alJNWVV4RXNDWUNjL3U1akdnT3VMT1R1elMrMVNsOExtZmpNYzQzS3lTbWRSWnhsV0VIM2pYYnJYdUNlTWhlYllXb3RCOWJwbmhPTGt4RlVxK2t5M05RYWU0aXNNd0NPTElJdE9qSkhTOWVwVW9TMUxES1JTREI1WXFuQ1FwZGd2L0x1UVQ0Um1QWDNhZWt1TE9jbzR0WkxhTExDRGpVSXJYaDNkK2pqUG9VeHRwL3NKeWRWR0hwMWFBb2lFZWZHVTJjZHRnUzZ6aDBLY240VldSV0RQekx1ZFJrYXRmajRDYyt1RG8vK2pBeGlKRXlhZEQ2ajE3MjVHVXBZa2UwOUdLWVU1eGhIeTd3WVN3ZXRhVDU2NWM0ZEM5ZDkrMHVLeHVlUklhM3dXZmU2bHNLK3lLbzE4M0lia1UwbUo3MFFGRHBiOGZHcHVNQTBUdWVtWnZGV01TSVg2UCtJVmE1OVl3OVR1N1B5QTdkU0dnQ0c4RnlKVnN0Ymh6ZUE0LzRyc3hZTmR0WXpUdzNZQTdmNUFsd0NjV29Tc2RZMVdubkdNT2VZY2NXdzZWYmZPQnFUK0lYWU1OaGUiLCJtYWMiOiI2NTEwNGE1ZmViZWJiOWNiNzlmYzU4MzJlZTExMjMzNDRkMWQ5Nzc3OGZkNDYzYzRlNGI1ZDczNzEzNDYyZTFkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:50:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBzRmpkN3J6VTdDN25IMlhkZzBNZHc9PSIsInZhbHVlIjoiZUEzak9FMElEaFBHMzBNczFRa0UvMTRYVjJzdUoxdFdXVDh4b2lkM2FWU0dhSkZOdG9rK2s3aUF6NHNuZnNiSlh2dFlqNWExcG9DRm9pMTA1ek5NYktTdElkUFZaY2F6WnlLVEM4TXZnR2F4b01DcmpZdUh0T0xUL2J5Yld6MEVieE1ZS3NsdGN2Ly96dHZlY1FLbHRCZ3lIcjZoNm96cjgyU3FNbEE5VGkrUmZpdGRmd1VwWkRLaGpLVWZSelhiYlVnRTBaSHNmV285TnNLTTF5QnJGdHJHejNya2NRUUJSL2FHaVJqK1NFK2tqcWxqNWFqWlhySDk2ckcwcFZuZjFsS2hXbVFMVFIrQWNoUERGc2w5N1dHOTdYWTFvQ0lxOFF6alROMDZKV29sMU5rQVdmbktPK2QzUkppRWFHQXR4NnpEYnFkTjVISWR2SlJvZjQxQTdkSTQ4a0hTa1V1NnBuR1lkZ1JLR3VKTHl5ZEd2OEhvMzk3aVYrN28wcW5LVksycDFPZnRiUktaMDNiclN0RTlEZHp6NWt2T24vNzZGTExFaEJZQTY2RlpLY21BZ21FSDhEbXc5TWxnL3RLdS93RllzS2VkL2ptZ1FtR3U3UTBhSjQ2SXpHZmxCVTErb3JzOWlVMi9ud0JJVCtzSHNhUXdKd1AwOVpyZkVuVysiLCJtYWMiOiJmMDFkZjhiZDA0M2RkMDE5YmE0YTM5YzdlYTIxNWI4OGRiZDA4Y2YzYjNlYzJjYjVhNWYyZWFhMWUwMjk2NjNjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:50:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBvMlFnNGMvWUMvNnZVUFZxMStLMVE9PSIsInZhbHVlIjoiN2Z0RFAvU0RKWlVVQk1VUWZpUVJOdnphTnpiMGUyWWNjbnpXWlhKRjJxQi9kNjJVd2VST3lXOWhaTlp5TlhSWGxldzBWd2o2alJNWVV4RXNDWUNjL3U1akdnT3VMT1R1elMrMVNsOExtZmpNYzQzS3lTbWRSWnhsV0VIM2pYYnJYdUNlTWhlYllXb3RCOWJwbmhPTGt4RlVxK2t5M05RYWU0aXNNd0NPTElJdE9qSkhTOWVwVW9TMUxES1JTREI1WXFuQ1FwZGd2L0x1UVQ0Um1QWDNhZWt1TE9jbzR0WkxhTExDRGpVSXJYaDNkK2pqUG9VeHRwL3NKeWRWR0hwMWFBb2lFZWZHVTJjZHRnUzZ6aDBLY240VldSV0RQekx1ZFJrYXRmajRDYyt1RG8vK2pBeGlKRXlhZEQ2ajE3MjVHVXBZa2UwOUdLWVU1eGhIeTd3WVN3ZXRhVDU2NWM0ZEM5ZDkrMHVLeHVlUklhM3dXZmU2bHNLK3lLbzE4M0lia1UwbUo3MFFGRHBiOGZHcHVNQTBUdWVtWnZGV01TSVg2UCtJVmE1OVl3OVR1N1B5QTdkU0dnQ0c4RnlKVnN0Ymh6ZUE0LzRyc3hZTmR0WXpUdzNZQTdmNUFsd0NjV29Tc2RZMVdubkdNT2VZY2NXdzZWYmZPQnFUK0lYWU1OaGUiLCJtYWMiOiI2NTEwNGE1ZmViZWJiOWNiNzlmYzU4MzJlZTExMjMzNDRkMWQ5Nzc3OGZkNDYzYzRlNGI1ZDczNzEzNDYyZTFkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:50:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBzRmpkN3J6VTdDN25IMlhkZzBNZHc9PSIsInZhbHVlIjoiZUEzak9FMElEaFBHMzBNczFRa0UvMTRYVjJzdUoxdFdXVDh4b2lkM2FWU0dhSkZOdG9rK2s3aUF6NHNuZnNiSlh2dFlqNWExcG9DRm9pMTA1ek5NYktTdElkUFZaY2F6WnlLVEM4TXZnR2F4b01DcmpZdUh0T0xUL2J5Yld6MEVieE1ZS3NsdGN2Ly96dHZlY1FLbHRCZ3lIcjZoNm96cjgyU3FNbEE5VGkrUmZpdGRmd1VwWkRLaGpLVWZSelhiYlVnRTBaSHNmV285TnNLTTF5QnJGdHJHejNya2NRUUJSL2FHaVJqK1NFK2tqcWxqNWFqWlhySDk2ckcwcFZuZjFsS2hXbVFMVFIrQWNoUERGc2w5N1dHOTdYWTFvQ0lxOFF6alROMDZKV29sMU5rQVdmbktPK2QzUkppRWFHQXR4NnpEYnFkTjVISWR2SlJvZjQxQTdkSTQ4a0hTa1V1NnBuR1lkZ1JLR3VKTHl5ZEd2OEhvMzk3aVYrN28wcW5LVksycDFPZnRiUktaMDNiclN0RTlEZHp6NWt2T24vNzZGTExFaEJZQTY2RlpLY21BZ21FSDhEbXc5TWxnL3RLdS93RllzS2VkL2ptZ1FtR3U3UTBhSjQ2SXpHZmxCVTErb3JzOWlVMi9ud0JJVCtzSHNhUXdKd1AwOVpyZkVuVysiLCJtYWMiOiJmMDFkZjhiZDA0M2RkMDE5YmE0YTM5YzdlYTIxNWI4OGRiZDA4Y2YzYjNlYzJjYjVhNWYyZWFhMWUwMjk2NjNjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:50:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26318285\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1371840064 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1371840064\", {\"maxDepth\":0})</script>\n"}}