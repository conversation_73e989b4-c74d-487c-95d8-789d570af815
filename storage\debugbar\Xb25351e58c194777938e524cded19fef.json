{"__meta": {"id": "Xb25351e58c194777938e524cded19fef", "datetime": "2025-06-28 15:49:50", "utime": **********.476643, "method": "GET", "uri": "/financial-operations/product-analytics/stagnant-products?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125789.992307, "end": **********.476661, "duration": 0.48435401916503906, "duration_str": "484ms", "measures": [{"label": "Booting", "start": 1751125789.992307, "relative_start": 0, "end": **********.380365, "relative_end": **********.380365, "duration": 0.3880579471588135, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.380374, "relative_start": 0.38806700706481934, "end": **********.476663, "relative_end": 2.1457672119140625e-06, "duration": 0.09628915786743164, "duration_str": "96.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49274256, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/stagnant-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getStagnantProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.stagnant-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=513\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:513-582</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.039569999999999994, "accumulated_duration_str": "39.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.412978, "duration": 0.01739, "duration_str": "17.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 43.947}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.439466, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 43.947, "width_percent": 1.82}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, ps.purchase_price * wp.quantity as stock_value, DATEDIFF(NOW(), ps.updated_at) as days_since_update, `ps`.`expiry_date`, CASE\nWHEN ps.expiry_date IS NOT NULL THEN DATEDIFF(ps.expiry_date, NOW())\nELSE NULL\nEND as days_to_expiry from `product_services` as `ps` inner join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT DISTINCT pp.product_id\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nAND p.warehouse_id = 8\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`quantity` > 0 and `sales`.`product_id` is null and `wp`.`warehouse_id` = '8' order by `stock_value` desc", "type": "query", "params": [], "bindings": ["15", "0", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 561}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.44257, "duration": 0.02146, "duration_str": "21.46ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:561", "source": "app/Http/Controllers/ProductAnalyticsController.php:561", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=561", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "561"}, "connection": "kdmkjkqknb", "start_percent": 45.767, "width_percent": 54.233}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/stagnant-products", "status_code": "<pre class=sf-dump id=sf-dump-1217021132 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1217021132\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1046205585 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046205585\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-51238136 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-51238136\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-955395502 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125788432%7C28%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1kNnppVjNQWlEvbkNtMEZKV0h6aVE9PSIsInZhbHVlIjoidnVLOFczRU5sWkRWWEQ0T2RNWXVudjMxejZVQUYzcXg4R0hKbDZHZEd2aXl2N2ZzK2RCWlUyblJpOCtvdzNqbHY0QlBzUkFZSStYNjdMY2FYL0J3MlhzVWxxdWlNbHd4cFhGWlprUlVFTVlaMml2SXArK0xVQk9ETmJtUEZCOTVIN3FJcmsxbzFpRmtkUTdqZlZmNGU5anQ5bWJUd3BhbkpKWWhvM2NTWDMyQ2JUYVAvbi9UWk45QXZQRGozckYyQVkzR1Rub0J3VkJhM0dhUVhFM0ZyTzhLUUpPOEdyZDRQOGtIWEM5c2JUcmlMd3g5NFFKODhzZWpSZWVUWWwyM3dMUTMzaXErZ0tYVUhiU0t0cURycUNFQ096aDFsYmo3T1BTbXF5eWZnbFY2R1RteVhDR3RtYnVEREFWZ1lzY2hVd2dBS3VrRUl3bU5sRzJwQy94QnllOXpXM0M3V2c2MmdpZjg4Wi9xRlFiZytHMzRhbjFqMXhQUTVJMzEwMnNUeHFSZ1hYUjhHOFd2VCs5YzZtbDg4VDFCWXdKS0FlckhMR25uSDdjQlZEdkQ3c21GTVBQV2ZlQ1FoOGgyd2N5MVZIczBMa2kwN2JCZFExQXFOOExYK2Z2VTBua09XS0tTK3k5b2V6ajhPWWJac1ZDT1lmS0RFNWRCRGFQRGtFY28iLCJtYWMiOiJlYmI1M2M5MGM5M2JlNGRlNTY3NTc0NWJlZWZjZWJhOTk0MDMxNWRjNDM4Zjc4ZTU1ZTNhYWU0ZTVjYzUyNzhjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVtcU1pZzR1ZnZrK3FDNmpVNjU5YUE9PSIsInZhbHVlIjoiMEo0N295Ym1uTXE0WWgzSkU2TmJRTEp1WWFSWnI3blBUY09xQ09WT2NoMHpodVVxZThuSGsyc3Y1ZG1MNmtCeXluMDVOcXgzMnE0UnpMZGFCUGVETnJEcWFwQmF0bU1ubVB1bXZ4OCsrM00xSm5mTUV2WVVNR1RrYmxZRnlFME05VnFqdFIyU01LVGRqWEZ0UlprdDJ0Z1BoRVRTZHlEK052QVFJdlcyZW5xVTlpY0t2U3FBbisvSzVXaVFkcHY1QVF5ODRETVV6cXBIRysyZ0U3SVloT1BNTFZYclQzMlpqczhsOW1GSmJuZkVldUhlWWV3Y1hhOFF6QlN1bHo4dm4zc1Nsc0VKY1RSTjdQaHJtajhCWmtGZ1lhZXB1YmE1dUhwVlhKd0NiM3dFWjdWWm9NUlhwS3NReXJOdWoyRmRkcll0OC9IaXRqbU81RXVmZkVVWXdVVlBZQUhHR25GN3B5REhENFRhNU5qVHJ0bEdhSVhOTEtsY2pqbGFxaWFrQ1A4Z1RzL0Zpdi9HdzlFdjBUWlc2WFhKYjk3T3crUzlpUlI2WDQvSmNINkF6OEFCd0xHWUJmVHdPY2xaNEJUaFZtaFFSQzlMSzZ0NU9aQmp4OHYyN2FreDJDL0ZXVnBiUW11SThPR3FyN0VRVDFHb0tDL1pTSEdrSnFTRTVyRVYiLCJtYWMiOiJjOWFhMDkyNGE0NzlhNzJiOTk0MWZjOTlhOTM5NmU4NDEwZTBlYmRjZGI4ZWEyOTg1YmNiMWU2ZmMzMDczODRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955395502\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-192811966 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-192811966\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-164695490 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1jeXlXMWNQSGpnSkdYUmhXak5hZlE9PSIsInZhbHVlIjoiUExPVVRFdEVDWEl0WS9KWGIzZGFqT2xhZ3BLcFNLTlVKLzdkK0s1TWtaZzBrQ1FZajdEODJwQzh2VDlycUNlcm0wMzJacFpLRmtnNG1rNnpPZnN4SStWWGZFR095dy9CY0hFcXhUcVB6VWFDL3FKVENSWHdVbld4OHZ3bkVFTlZlK1BabDFJbzdFYXdWL0ljMjFVNHBEdy9rVTh3dHpocDdQQXoyMU56S0hqWDMwZE41RmcwNnJhK2VkeXhOalh3ZmZaM3hFdFBOenJhS2RGTnZWaEE4SWRhYlg4OHZvRVJrUCt3bnZpZDNwTy9tUzhoeVRtamtFcTI0ckRSMUNtbitkcitOUk9yb1JpSTVlbW56WUpwMGdUQnhodjBtem1tT2NkZVFVYTE1QThkbWdLVW15Vzk3VlFzZnE5ZEpacGZRM1pkWU9XREpFL205NWZqaHhwRDJQUGZyamRFZnZDdklQbjlkZzF0U2tkb1JaTW9nL0NUWUlqdEFLdXZXOGZXMCt5YW1WYWZCSzNpTk1GQUQ2TkVnTmdBZlRYZUxqdXQrSVM5RTJyc1hybklIZnFqSHRDN0RJZUEvR3cvcFJFQUxndC9wKzBOSyt4TTFjY2Fud2F6bTdVUHB4aDJNT2VReW5sUFRDRXVIYXNVeTRxSGp5MDhGUGFYVnBoMEtEODQiLCJtYWMiOiI2YWJiMTIyMTczNjMwZTBhMTlmNmRjNjYyOTI2N2Y3ZjRhYTVhNzkxNDEwNTQxMTJhMzIwMTM5ZDAwMmM2NDBiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZQNUFBWXU3OFpVdGdQbmNSUnZRQUE9PSIsInZhbHVlIjoieHo1RXdUVVkvd0dJSndPS21tYUt6L3hTVGtwL1hCM2VTYWpteE05bmlkOG9SbndlVjBxaWRYdElaNk1MeGphaThUbDROOTVpVm54c29aaCtQYUNsR0ZsSndsMlpMb0huZ3E0T2RCZGpab3I3YkRYVnRDU2VqV2MvcFgzN0RHK0xiRFV3eEh3ZUNoNm9NUEtVTXpIVGUzbmZHWGp1SmlRY2JGTEx1ejUyUjRKQ0Ztd01LcSt2MGpuZC92ekhnZ2ZyVDhQUVM1RHdHR2QzU3k3T2FkR1FRVkk1NE5SZjcwaGI4Zk85OXhhWUpLTHVsOGY3U2hEelpaR0JPZGM1TmJ1L1NqdzlhajhQTnFnc3FKcVprdXRublN5YURLWW90ZzdlN2d2cEg0Zkg5RGdLM3liSWU5ZmdSYVJJamNwY0tBUFRqUS9nQU5VeTBoU1o0TzJCdmo4c25pSUFtZUVnQkV4d25MSVhjcFZqL0NJWjlveUZLaThJQ2NCZEt2VlpvZXVrTTJwWlAwZFdWRFRQak9YcFJDcjB0VEdDd2NSYUtlU3FGY2x6N3JzcUFpeDFMOVJZbzNwK1NVZnMrcEdnSE9PdUs3QXM5TWFnbjlTTkZvTWpxckdvWDJXb29mbGtLdFh5Nmxsczh3eitXYjFxZHQ3OXRldnFXdTFYRWh2Z3NhYVMiLCJtYWMiOiJkNTdiNDdmYTdkOTU5YTJlYTcxYmIyN2ZjYjgxZWU1ZTI5NDUxMzAxYWRlNjM1ZTc2NmM4MTg4ODhhNDBkNDRhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1jeXlXMWNQSGpnSkdYUmhXak5hZlE9PSIsInZhbHVlIjoiUExPVVRFdEVDWEl0WS9KWGIzZGFqT2xhZ3BLcFNLTlVKLzdkK0s1TWtaZzBrQ1FZajdEODJwQzh2VDlycUNlcm0wMzJacFpLRmtnNG1rNnpPZnN4SStWWGZFR095dy9CY0hFcXhUcVB6VWFDL3FKVENSWHdVbld4OHZ3bkVFTlZlK1BabDFJbzdFYXdWL0ljMjFVNHBEdy9rVTh3dHpocDdQQXoyMU56S0hqWDMwZE41RmcwNnJhK2VkeXhOalh3ZmZaM3hFdFBOenJhS2RGTnZWaEE4SWRhYlg4OHZvRVJrUCt3bnZpZDNwTy9tUzhoeVRtamtFcTI0ckRSMUNtbitkcitOUk9yb1JpSTVlbW56WUpwMGdUQnhodjBtem1tT2NkZVFVYTE1QThkbWdLVW15Vzk3VlFzZnE5ZEpacGZRM1pkWU9XREpFL205NWZqaHhwRDJQUGZyamRFZnZDdklQbjlkZzF0U2tkb1JaTW9nL0NUWUlqdEFLdXZXOGZXMCt5YW1WYWZCSzNpTk1GQUQ2TkVnTmdBZlRYZUxqdXQrSVM5RTJyc1hybklIZnFqSHRDN0RJZUEvR3cvcFJFQUxndC9wKzBOSyt4TTFjY2Fud2F6bTdVUHB4aDJNT2VReW5sUFRDRXVIYXNVeTRxSGp5MDhGUGFYVnBoMEtEODQiLCJtYWMiOiI2YWJiMTIyMTczNjMwZTBhMTlmNmRjNjYyOTI2N2Y3ZjRhYTVhNzkxNDEwNTQxMTJhMzIwMTM5ZDAwMmM2NDBiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZQNUFBWXU3OFpVdGdQbmNSUnZRQUE9PSIsInZhbHVlIjoieHo1RXdUVVkvd0dJSndPS21tYUt6L3hTVGtwL1hCM2VTYWpteE05bmlkOG9SbndlVjBxaWRYdElaNk1MeGphaThUbDROOTVpVm54c29aaCtQYUNsR0ZsSndsMlpMb0huZ3E0T2RCZGpab3I3YkRYVnRDU2VqV2MvcFgzN0RHK0xiRFV3eEh3ZUNoNm9NUEtVTXpIVGUzbmZHWGp1SmlRY2JGTEx1ejUyUjRKQ0Ztd01LcSt2MGpuZC92ekhnZ2ZyVDhQUVM1RHdHR2QzU3k3T2FkR1FRVkk1NE5SZjcwaGI4Zk85OXhhWUpLTHVsOGY3U2hEelpaR0JPZGM1TmJ1L1NqdzlhajhQTnFnc3FKcVprdXRublN5YURLWW90ZzdlN2d2cEg0Zkg5RGdLM3liSWU5ZmdSYVJJamNwY0tBUFRqUS9nQU5VeTBoU1o0TzJCdmo4c25pSUFtZUVnQkV4d25MSVhjcFZqL0NJWjlveUZLaThJQ2NCZEt2VlpvZXVrTTJwWlAwZFdWRFRQak9YcFJDcjB0VEdDd2NSYUtlU3FGY2x6N3JzcUFpeDFMOVJZbzNwK1NVZnMrcEdnSE9PdUs3QXM5TWFnbjlTTkZvTWpxckdvWDJXb29mbGtLdFh5Nmxsczh3eitXYjFxZHQ3OXRldnFXdTFYRWh2Z3NhYVMiLCJtYWMiOiJkNTdiNDdmYTdkOTU5YTJlYTcxYmIyN2ZjYjgxZWU1ZTI5NDUxMzAxYWRlNjM1ZTc2NmM4MTg4ODhhNDBkNDRhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-164695490\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1915594928 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915594928\", {\"maxDepth\":0})</script>\n"}}