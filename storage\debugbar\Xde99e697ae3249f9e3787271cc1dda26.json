{"__meta": {"id": "Xde99e697ae3249f9e3787271cc1dda26", "datetime": "2025-06-28 16:04:35", "utime": **********.765938, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.348277, "end": **********.765952, "duration": 0.4176750183105469, "duration_str": "418ms", "measures": [{"label": "Booting", "start": **********.348277, "relative_start": 0, "end": **********.713749, "relative_end": **********.713749, "duration": 0.36547183990478516, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.71376, "relative_start": 0.36548280715942383, "end": **********.765954, "relative_end": 1.9073486328125e-06, "duration": 0.05219411849975586, "duration_str": "52.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45714464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00269, "accumulated_duration_str": "2.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.740098, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.428}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.750838, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.428, "width_percent": 18.216}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7564611, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.643, "width_percent": 16.357}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1579291530 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1579291530\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-299642898 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-299642898\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1632157978 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632157978\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2001475834 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126669684%7C28%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVJMzNZVms0MzU2L3NGU1JORTlPYWc9PSIsInZhbHVlIjoiYnNWTGJVSDRyTjhxRUJZSXFMR29SMUNvb1BsYXRLVXJhU2hwcVUyOUNaOFdXanh2djBWMHhDRXMxNHNNQ0xpcDB6VzNQV21MaUJ1dml4R2dsRlVzdXNqZ3lXRThXeENYS0pxTVFFOXM3em56YStZdGFhcHZCQ1E3TDdFUC9VZWR5QkFjVFBsekgvWGRLVGVSWHlDVTZaWEFNYThVUmFpSENJVFBJaFhwL1E4QUtoencxY3duZzJmQStOU0xEc2xaU2RUcmh2cGdFWlhYL2d1STJBNkV5UDVhM1F2MFpVNW9ubXcxZXkzTW0vc3FuWDJ3VDE5M3lUQ2czOHRMQVpVa3o0U1FleXc5aWo5UHRWN1lJTWRDMHhEajM5M1NCYkVKZHpaN1VpektHNmFnMnFRQlgrTEsvRm1WNldlOFRtWGovSjY2b1BJSHQ0WEQ4WnhLLzJjUityTzlmeW5lSDdveit4bXdGaGsvNG9WOUhyUXBIQ01DdGE5ckV6WFBYdURDTkxCa3lBN3ZTL3FZVnB1dk9VV0JkdnVXNG5QREozOU9FZlpTSk92WTA3QWFnK1BET3dSYS8xUWZlUkJBRk16MFNoZXFxQVQvMDEvdTlDTWVEeUY3empHWlhYTWJPREtPNXA3Tm82VFAvamJOVE1icGtiaVo2a2ZxdHFVZGlURkEiLCJtYWMiOiJhMjI1YWU1MDIyOWI0OTRjNTUxMDg4ZTY1OGFmMTFiNzFlYjZjN2QzOGJjNmZiYmI5OGIxZmQ4OWM2NjZkMmIzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFpb2Rab0lJVFFSUDU4VkMwcGNWK3c9PSIsInZhbHVlIjoiQm1QRXpnN013a25taUZBUjN1WEFWTmxJY1QvZWtNdHVyTC83Q096Mjc4Z242UThaV3h5bHZTamdkSWlFbm5zV1daQ0VkcUsra3VYQU1DQkZScmRJZ1JZUkRJblBmclRLbEVsVFRjQ3VGZE1XdUZyTlFiR2laMXoxUTc2VmpiTFludURXYUxPZkh1Nk5mekU5WCtVOStHa1E5QUFlVFR1b1FMK3ZldFZvdXQ0STFXd1NjcmpSdStrdHhCY3FKZDBOMWVScDRRREtXQWc1UXQxQ240eStIUGlaVHJlb3JvaVJmVUl2aFg4Rmt3bnRlYkJoL3F6eE9mRHQvTldaeS9DUXlIdXczR1JrOFFvSWZjd0xkUlI1RXBXMk1YTjhkWE5ldFhqK1BqUTNDYk52V2cydlkxUDJaUHAybldYalN1cUYxWTNmUk9FRlJldlVZaC9ja1ZzczFaOXdyRndjMUlIem0vZ3hjRWF2Q3RWNlZGWWo4TVFaci9kSFBLcW1vVjVHMk9tQkJqdzVMRlNRZkV4dmx4LzB6UmVRMUpHY3hEbG1TT0QxR0JodVFNWHE0SDR0ODdnQ2txc0tRdWVBelJNaWNoSjE1aXc0YjJZTGNKOVNIVzBOWE8zM2VlbTcwajJZdVM5S0xzbW80Um1EMXk5QnJFazZldExHVFl4T0JmVTkiLCJtYWMiOiIxNzc3MmNjYTAyYTFiNjcxM2U2MzE0OThkMjhlMzc3MTczYTgzMTQ0ZWU0ZTZiMzA4OGQ4ZTNlOTlkNjJmNDNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2001475834\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1427050215 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1427050215\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1393415593 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:04:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InovelRtYklnQUI5RU9KUzBjaUs2TlE9PSIsInZhbHVlIjoiaDJLWVpKTi9qK0pZUlEvTklib25DM1FUdnAxUFVvYlMvS3pvYm80Q1h5U1J3VUJ0ZndOUDkvQXpLVThUK3VCUU1JUnE2N3JLa2NtU0JIZ0FmcmI2NTRBVjlLdUhaWW1DVHIwYmI0UVNsU2RHRUtkdlFYTmtzRm02ZFlvTy9BcElUOHg1VlhBRWV5UWhEdllqQkhoV3Q4aG9wL3ZsUUd0WGxhOW14ZEZVWHdremMwanMySHhCOHB1OHFPOXhuM3BQeDVxYTVaeUg5TFREN1d2Y3NzYUpHV2Nvd0R3UjlUMVBVeFZOY0krUVFveDI3bm9mc1UxbEtjdkduQmdPTWlXSkFteEMxT2hKSGE5aDN1TXBTRG9mK0lMQjJvSnVHc3dIbEUzVnJaWmx1dUc0SFR6eXlwd2h0MkYzZ2FEQjhWY2xXaVM1VXMrZmlMdUNzUVF5cTAzUzVHK3BNMW9GSFZSZU1SekdoVndRSWVhM0JIK05QWTdBWTZKRHJxVHZtbTlONWV4OER1RDB1YUZhc2ZCOFROZWFqYTJOT0QzRE5qaC90czhvTEhzMDhRSXcyNkJtNVJZczkvdndSeW00UHRsekdNVktwa1NZMlB0eitPY1VhenpRVVBzZlljTU9TanllbkE1bWdzQ1hLTTY3QjB3bjlaam83VHprNEhNOGR6c1oiLCJtYWMiOiIwOTlhNzczZjE3NjkwMzk1MzA4N2NlZmVlNzg1YmMyMWFiNTY5YTRlMGJkOGRhZWMwMTc1ZDgxYTg5ZjViMmNkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjE5eFY4VjBQVlpGajhzQVpRdU9WV2c9PSIsInZhbHVlIjoiUzRrVTZhcmkxRkhUSDBTWlFrSnI5UnhTak5OWmlJcWxqTjRnbm5VTE50OVJ1YU9FU1l3dk1zT20wS2oxSnRUbWNMZGQycHp6SDI1V0RydGMwT3VTc3lpNkZVc1NhZWo3eXk1UzFSTStoQ3k1YW9SVndsY1llcFE3ZXN4UHFmN2VUR25yWHNSemlLUUN6RlhMSTV2NkNBUmloUVNTcGN6N1hiL1BENlZTbmdsTHYvaFVQK0l1Z1l0WVByZGxUeHRLY0J3SFphWjJIU3JFWGtmL2FHenZyMnptS1dyZzRLajJsRUp4bW1teHB2TC9EQVBoZ1ZQQ1pZdFdGZFpTcmFnbEdTblJmRkUwL2tNOVVPKzZTZk5xV2pPWndIZ25BYlowcm83dm4zZUtYcU9jV212THY3KytqdFlWTDJVTm9Sd2VYOEsvcjJVeEdwWXdqNEZ5MTNPd3dOYUxpNUZoQkJObk82UCtremVlUUhRMGk1MnF3SWVNUm5FSjdEbXZ3K2xJaU5vekJMaU81dUtYcCtPd0J4OVRXV0FlT2wreGE0MTVLZkloaElkRmRCbmFSdUVSS2VJNkxWZHF4T1VvT0pOZU5zaWI3T3lhWWlVbzR2d0dqTWtyS1NDYlo4aHZKclFodDRCNERHdW9zcEpBdHhEcVZFNGd4Zm1TTjc5cXZyUlEiLCJtYWMiOiI5NDI3ZjE5MGJmYjMzN2E0Njg0NGQ2ZjAxNDA4NGNmYzNhYTQwZWEwM2NiYjNmMGEyNmY0MzQ5NzI2MjJkYWY1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InovelRtYklnQUI5RU9KUzBjaUs2TlE9PSIsInZhbHVlIjoiaDJLWVpKTi9qK0pZUlEvTklib25DM1FUdnAxUFVvYlMvS3pvYm80Q1h5U1J3VUJ0ZndOUDkvQXpLVThUK3VCUU1JUnE2N3JLa2NtU0JIZ0FmcmI2NTRBVjlLdUhaWW1DVHIwYmI0UVNsU2RHRUtkdlFYTmtzRm02ZFlvTy9BcElUOHg1VlhBRWV5UWhEdllqQkhoV3Q4aG9wL3ZsUUd0WGxhOW14ZEZVWHdremMwanMySHhCOHB1OHFPOXhuM3BQeDVxYTVaeUg5TFREN1d2Y3NzYUpHV2Nvd0R3UjlUMVBVeFZOY0krUVFveDI3bm9mc1UxbEtjdkduQmdPTWlXSkFteEMxT2hKSGE5aDN1TXBTRG9mK0lMQjJvSnVHc3dIbEUzVnJaWmx1dUc0SFR6eXlwd2h0MkYzZ2FEQjhWY2xXaVM1VXMrZmlMdUNzUVF5cTAzUzVHK3BNMW9GSFZSZU1SekdoVndRSWVhM0JIK05QWTdBWTZKRHJxVHZtbTlONWV4OER1RDB1YUZhc2ZCOFROZWFqYTJOT0QzRE5qaC90czhvTEhzMDhRSXcyNkJtNVJZczkvdndSeW00UHRsekdNVktwa1NZMlB0eitPY1VhenpRVVBzZlljTU9TanllbkE1bWdzQ1hLTTY3QjB3bjlaam83VHprNEhNOGR6c1oiLCJtYWMiOiIwOTlhNzczZjE3NjkwMzk1MzA4N2NlZmVlNzg1YmMyMWFiNTY5YTRlMGJkOGRhZWMwMTc1ZDgxYTg5ZjViMmNkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjE5eFY4VjBQVlpGajhzQVpRdU9WV2c9PSIsInZhbHVlIjoiUzRrVTZhcmkxRkhUSDBTWlFrSnI5UnhTak5OWmlJcWxqTjRnbm5VTE50OVJ1YU9FU1l3dk1zT20wS2oxSnRUbWNMZGQycHp6SDI1V0RydGMwT3VTc3lpNkZVc1NhZWo3eXk1UzFSTStoQ3k1YW9SVndsY1llcFE3ZXN4UHFmN2VUR25yWHNSemlLUUN6RlhMSTV2NkNBUmloUVNTcGN6N1hiL1BENlZTbmdsTHYvaFVQK0l1Z1l0WVByZGxUeHRLY0J3SFphWjJIU3JFWGtmL2FHenZyMnptS1dyZzRLajJsRUp4bW1teHB2TC9EQVBoZ1ZQQ1pZdFdGZFpTcmFnbEdTblJmRkUwL2tNOVVPKzZTZk5xV2pPWndIZ25BYlowcm83dm4zZUtYcU9jV212THY3KytqdFlWTDJVTm9Sd2VYOEsvcjJVeEdwWXdqNEZ5MTNPd3dOYUxpNUZoQkJObk82UCtremVlUUhRMGk1MnF3SWVNUm5FSjdEbXZ3K2xJaU5vekJMaU81dUtYcCtPd0J4OVRXV0FlT2wreGE0MTVLZkloaElkRmRCbmFSdUVSS2VJNkxWZHF4T1VvT0pOZU5zaWI3T3lhWWlVbzR2d0dqTWtyS1NDYlo4aHZKclFodDRCNERHdW9zcEpBdHhEcVZFNGd4Zm1TTjc5cXZyUlEiLCJtYWMiOiI5NDI3ZjE5MGJmYjMzN2E0Njg0NGQ2ZjAxNDA4NGNmYzNhYTQwZWEwM2NiYjNmMGEyNmY0MzQ5NzI2MjJkYWY1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1393415593\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-639189399 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-639189399\", {\"maxDepth\":0})</script>\n"}}