{"__meta": {"id": "X9bc11bb2489300ec4f321fa3126a470e", "datetime": "2025-06-28 11:21:04", "utime": **********.457945, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109663.9393, "end": **********.457973, "duration": 0.5186729431152344, "duration_str": "519ms", "measures": [{"label": "Booting", "start": 1751109663.9393, "relative_start": 0, "end": **********.340924, "relative_end": **********.340924, "duration": 0.4016239643096924, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.340937, "relative_start": 0.40163683891296387, "end": **********.457976, "relative_end": 3.0994415283203125e-06, "duration": 0.11703920364379883, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43410776, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.062079999999999996, "accumulated_duration_str": "62.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3746772, "duration": 0.062079999999999996, "duration_str": "62.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DbkmW0B1QB6x4DfsCQT5q6jxXald2UMQHCx201Ej", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-549196663 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-549196663\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1535669733 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1535669733\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1374982206 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"258 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=lntfvz%7C1751069310598%7C1%7C1%7Cz.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374982206\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-938221557 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-938221557\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:21:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijg3ZDc1SFFvZC9GTWNsdkMreExBU2c9PSIsInZhbHVlIjoiUkQ2bkV3S2hzS0IrdURiK0xpaU1QVEJIQ1liNmI1YnJEc3BmVDkwTDlHc1JIdzd1VHZoSG1GN2FBOXFJYktIUDdpMUFGeFpKWFdsbWV0bEsya1YzMkoyTUFaTk14VTIweU81ajJnQ3ZTTnBYTmNTeUkyM2dQeWlHeWFDZlhNMzduNFp6SkhBcmdhUmVINUZtV2ZWSVFFK3J6MXRmaUpxTDdESll4OTlUUENqSWdEbXJ4Unh6VTNiZ1VFaEJUelVCbXZkS3ZhK080aVZlZzVmUTNWVkVmNWtUUVJRc2p6VGJjUzBGZlJmSjVBa2h1dG9GdFpETHBoN29JcXFZQ3JVYXVaV3J2UGxFeitGQjVwUTM3dy85ZDV1L25aT05jbXl3ZVZuZVFoQnRTdllrUXY4M1cvUzhwbEFsRGJwRy9GZnVSMlR2SFplUnhJbDc4Ujhlb0U1MXJZNGdpdTA4RDBqRTlQQWRrY3F5aE00TVRVczdEZVgzbTFTd2dTQy9GV3hzdUZTQmFuZmY2dU5peExoTDdFRCtWU3ZnSUl4WklpdmV1Ym55VldONGpoU2tJWFRtVXVkMlRjMHpHMHVYa2tqQSt4TmNvdUtMWmNLaExsRGc3eTJxQ0ZnK2lFUFZtOU1HNjV1MkR2ZENTUCtqUVVyeWRudkRTcFBMQjlkN0xxZ1IiLCJtYWMiOiI4NWM0YTJmNTNhZDEwZDQ0M2Q5OGY4NzJiNGJjMzVjODJkOWUyNjZmMGMyZjUxYWI4YThmMzM1OWEzNjhlYzQxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:21:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJud3kwdFFYSE4vYU56WElJUU92NFE9PSIsInZhbHVlIjoibFR5QU85TFNqdjJiN2xIaGo5VzFYKzhBQzhSTXlVeURHaldoMU80UTFDY0EyMHp0aGt2bUc3bnY2bUdoQUJ6SWlQMTNGQk53amVjMlBFK2UyZFZ3YkJwOWxEOEFJdkttdzg4ay81K1hEQXVOdlc4UnR2Z0JyRmNnQ3pZODdUY2VHTmwweHZqQVNwcmVMVVgvRno2SGZvQ2R2dFhzQkZTdHRWUU1DRzVlOU4rSi9hR3l6Y2NpYXRUN1UvcEFpbFVoVHVQalFuTmZCSHhwNGo1MXM2NHRTOHp3SysrNDhaaHJnZzdnQUdQS0dUWWd4QkFqTUw5NTRMcG1BSytKUXYrSDlXZnV4cmlpU245K3YzUHJkMU5LZWxlbWpRSXpOaGhhOVNCWnFLL1k3S01rbTdLN0VHOHFKL2tsZnhidWZwZmhpN2dFVjVsenZFYkE3QXZacW41TjFwT1c3dEl0ZDdIdjA2UUo5cE5GcE0xMkNLTlE3eWdOSDZpZmZLNHNHUVQrQW9nWkI5MDdGNEV1Ty93VDUrNVRxMFplOHVWU25uUEp2dFJaNWFZVEI0Z0JoeCswVzJWMDVjQWl1MTE5OGVsWjMxQUNCNXhmOU1wWlJCMFR1SGxSbEw0YUQycm5VTmtvRjl0cWFHZWRudHRxMXNEbUFiYTJScFlpYjhjdEk3OXUiLCJtYWMiOiI3N2U0OTM0MzU0Y2VkOTgzNGZlNzVkZTM4ZDNkM2M1NzFjODFiYzY5M2YyNDgyM2JkYzgyMGE2MDNmN2NkNmEzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:21:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijg3ZDc1SFFvZC9GTWNsdkMreExBU2c9PSIsInZhbHVlIjoiUkQ2bkV3S2hzS0IrdURiK0xpaU1QVEJIQ1liNmI1YnJEc3BmVDkwTDlHc1JIdzd1VHZoSG1GN2FBOXFJYktIUDdpMUFGeFpKWFdsbWV0bEsya1YzMkoyTUFaTk14VTIweU81ajJnQ3ZTTnBYTmNTeUkyM2dQeWlHeWFDZlhNMzduNFp6SkhBcmdhUmVINUZtV2ZWSVFFK3J6MXRmaUpxTDdESll4OTlUUENqSWdEbXJ4Unh6VTNiZ1VFaEJUelVCbXZkS3ZhK080aVZlZzVmUTNWVkVmNWtUUVJRc2p6VGJjUzBGZlJmSjVBa2h1dG9GdFpETHBoN29JcXFZQ3JVYXVaV3J2UGxFeitGQjVwUTM3dy85ZDV1L25aT05jbXl3ZVZuZVFoQnRTdllrUXY4M1cvUzhwbEFsRGJwRy9GZnVSMlR2SFplUnhJbDc4Ujhlb0U1MXJZNGdpdTA4RDBqRTlQQWRrY3F5aE00TVRVczdEZVgzbTFTd2dTQy9GV3hzdUZTQmFuZmY2dU5peExoTDdFRCtWU3ZnSUl4WklpdmV1Ym55VldONGpoU2tJWFRtVXVkMlRjMHpHMHVYa2tqQSt4TmNvdUtMWmNLaExsRGc3eTJxQ0ZnK2lFUFZtOU1HNjV1MkR2ZENTUCtqUVVyeWRudkRTcFBMQjlkN0xxZ1IiLCJtYWMiOiI4NWM0YTJmNTNhZDEwZDQ0M2Q5OGY4NzJiNGJjMzVjODJkOWUyNjZmMGMyZjUxYWI4YThmMzM1OWEzNjhlYzQxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:21:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJud3kwdFFYSE4vYU56WElJUU92NFE9PSIsInZhbHVlIjoibFR5QU85TFNqdjJiN2xIaGo5VzFYKzhBQzhSTXlVeURHaldoMU80UTFDY0EyMHp0aGt2bUc3bnY2bUdoQUJ6SWlQMTNGQk53amVjMlBFK2UyZFZ3YkJwOWxEOEFJdkttdzg4ay81K1hEQXVOdlc4UnR2Z0JyRmNnQ3pZODdUY2VHTmwweHZqQVNwcmVMVVgvRno2SGZvQ2R2dFhzQkZTdHRWUU1DRzVlOU4rSi9hR3l6Y2NpYXRUN1UvcEFpbFVoVHVQalFuTmZCSHhwNGo1MXM2NHRTOHp3SysrNDhaaHJnZzdnQUdQS0dUWWd4QkFqTUw5NTRMcG1BSytKUXYrSDlXZnV4cmlpU245K3YzUHJkMU5LZWxlbWpRSXpOaGhhOVNCWnFLL1k3S01rbTdLN0VHOHFKL2tsZnhidWZwZmhpN2dFVjVsenZFYkE3QXZacW41TjFwT1c3dEl0ZDdIdjA2UUo5cE5GcE0xMkNLTlE3eWdOSDZpZmZLNHNHUVQrQW9nWkI5MDdGNEV1Ty93VDUrNVRxMFplOHVWU25uUEp2dFJaNWFZVEI0Z0JoeCswVzJWMDVjQWl1MTE5OGVsWjMxQUNCNXhmOU1wWlJCMFR1SGxSbEw0YUQycm5VTmtvRjl0cWFHZWRudHRxMXNEbUFiYTJScFlpYjhjdEk3OXUiLCJtYWMiOiI3N2U0OTM0MzU0Y2VkOTgzNGZlNzVkZTM4ZDNkM2M1NzFjODFiYzY5M2YyNDgyM2JkYzgyMGE2MDNmN2NkNmEzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:21:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-980533687 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbkmW0B1QB6x4DfsCQT5q6jxXald2UMQHCx201Ej</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980533687\", {\"maxDepth\":0})</script>\n"}}