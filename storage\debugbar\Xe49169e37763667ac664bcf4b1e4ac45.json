{"__meta": {"id": "Xe49169e37763667ac664bcf4b1e4ac45", "datetime": "2025-06-28 16:30:41", "utime": **********.276434, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128240.866468, "end": **********.276448, "duration": 0.40998005867004395, "duration_str": "410ms", "measures": [{"label": "Booting", "start": 1751128240.866468, "relative_start": 0, "end": **********.208971, "relative_end": **********.208971, "duration": 0.34250307083129883, "duration_str": "343ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.208979, "relative_start": 0.3425109386444092, "end": **********.276449, "relative_end": 9.5367431640625e-07, "duration": 0.06747007369995117, "duration_str": "67.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46430376, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2513\" onclick=\"\">app/Http/Controllers/PosController.php:2513-2547</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.017040000000000003, "accumulated_duration_str": "17.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.245274, "duration": 0.016640000000000002, "duration_str": "16.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.653}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.270135, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.653, "width_percent": 2.347}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1244795258 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1244795258\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1797307127 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1797307127\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1162614732 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128238007%7C50%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlcrTms2WUZYem5jeW1PaHNjZ1F5c0E9PSIsInZhbHVlIjoiZUMvUENFWjlnaUQrajExZ1JiVTkza1BHZlduRjN6N1RRU0c5bXFVUnBQdXB4Wm1lY2FpQkY2SGFEY1JhSm9DVGwxV2ZsZk9STnRaNVg3U2NFYkZRcmhGRmZ3MXBud1hTQ2t3TExYYW1mWFFkS1BVVnBsajV0V0Q4dkd4Mkh1YmZTcitDR0I1MXJkbUZWR2VCOEQ0SUFzM2Z4aEF2ZUlwamRrdWhHWVBaUFEyZXV2SXBhOUpYbWFBL1Bwc0x1VnhyZ3VmbFd5UmprSDd2TkFmWk92V3ZvSkFCNzFIZlB0N1d0NkZQZVQ4WG1kekRReGViMlplcElkU3lvak5OakEwb0ZrMUpmNGF3aHBHT3J5RnZnTm5xVDZtdFZEK0gzSW5mRGdJbHkxd2hZQk5UeTdPSTNVb2cwMlY3RDNubDU5dHdQdU9CT0VCcmxJUlJBQTJjWVRMcC9PaXFkMmNQVTFqOFN3ZVlkdHlBaVZoL1VFR1NyT25URmF6RUdxMEU4UjR1RWFIcFQ4RHU0dVc5RFNJUERvRXdHYUdmdFFDKzY3MVJXQmlORVU1QmZEanFoYXJYTUh0VFhGUEFsRjg1MGpSaktxZ1ZiUGlSeHRSTE4ybkF2T1VrQTdnUk5nMHlUYzFsaUVjVm1JS1Y4K0k4bXJucWoyM09nc2tScWFkUUkzT24iLCJtYWMiOiJjMWExZDUxYjJiMDA1OTljNjZmYTA1MDQ1NmY3NDcyYTBhODk1OGY2NTk0N2Y4ZDBiYzFhNjk4MTI2NzM0ZmNkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRNM0d1YkNnVEtSUlpMV0VsSStqdWc9PSIsInZhbHVlIjoiU1Y3STVTblQweUt5Sm0weU11c2piTjgrY3VKbnBkVHd3NnJkWHFyVlhta0hwS0s5WlpaSitvSUUrcmJtRUlDNHJoY2RqdFMyZmZMTEJHaEVpWHZPSXlkQWVackVwcGcvMHl3UzFMdHpJK1FYMFZxUHUveFZ3NVFXZWR0NDAwZ2xtbVRqUGpXbi82aWNFckwzL0txeDhIRWg2MlQvK3lCNWJoU01tS1pNdXhWZTNZNjNVWlNqWjJmMUxLcXRjRE5xcFNHRHlmbVpkTVVyVytCaDg3ZlFQTmQ2Qjg2elZQT0t2bit4YlMzVjQ4MEhxZUc3d2tFQkJUT1UvVGJSYmpzL3hES20wM2l1MEJRWTd5Q0FRQ2o0NytiU0JKcEF5VHVCZGhBOTRRa2RmWkZkejdDNG96amVvRVZ0b3BFTkF1L3NhWFZ2d3JMenRSZU00YTUyRVNjZW01aTkyRVpsRHA4L1duSTZhNjk5cGVNUS9EdmRWdERIVnJMckRzVUxhZzVpSlZnU1ZuSVA4aC9iM2M2dkdKdndDK21lSTFQVnJ5ZTFYMURaVktrWmNaVkxoWU1pbm5CY296YmJMRXVtWjlIckJDenRPVEEyRWJRTkVRcENvVGR4OWNFSzhXRUhSZXlJU0hMUERsTlFsUU0wdVlMKzNSSlE2N0p2SnhzdVRMcGgiLCJtYWMiOiI1MTQ2YWFiZjM0ZWQwMDdiZDI0MTM4NzQzMTc0YjVkYzllMGQ5ZDQzZTU5OWFlZWQwMmIyYjg5MGZjY2M1NjQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162614732\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2087917503 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2087917503\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-221355709 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZpMmdQTHcrREttVGVnc3hXelN3ZGc9PSIsInZhbHVlIjoibzFhMVdSbDRVNHEzeVR3Zk9QRktYYVpESGZBOU5wU3F0OWV1RWl2THQ0ZWdUaDFuaVBubkhkK2pZTFZoNlMwMWpXRm5ZUFZkNHJyTFUvQVpUUjczZGh1QlBsZWNEMnhkQ1c0WlFNNzVHWWpwanpXUU5DM3lRSE43M0ZNMytQMWZNQlQ0Nmd6bnUxczQ2ZWtUb1d6eFhIbWdPazlGRC80WThQc1dUQ08xZFpyem5nMTF2TlpzM3MyQ2ZGcG1QdUc4TCtMbzYrNExtczhoZEM5czFxRmhvQis1bVlNM0pGRktCbWUrdGJlRnhqS3BZSVdvL2NrbVZoL2NGaHhUZVBXMFQyL3ZZS2EvdHVlVlpyeUN0bVFEWUt5NFNzQmVlNDZTZG9jb3RYRkdtNFlTa2kxZDJIeFZHZDRCSEFSZjZwWG9uRytMNUhCaUZtMHBoTHRGOENCSDA3QnF4cGxyaU1MeE5qZ2xBSjhKZDhEeE0xWDhxLzQ4NWVvN2NjalFyK2Y2QWlObmtmTXpUckdnalNSa0tteFVMQllwdUZMVjFzMk9BRWZtZnJ4Y0xZcWxuUENSMEY2OUF2RUVmY0t2TGZvTm5MVWwrODNOUTVsQ2YvTmwxaXJiNVR6Z0RXalRFWHkyNm10OC9GSURPVzZteXVZVllPK25GU2E3MzhDdllzOFYiLCJtYWMiOiJlOGU0ZDg0NWM3ZDRmYzQ0ZmQwMzliNzhjZGNmNmZjMWI5NTBmMjQwMTcxNTgyMDcyNGQwMmY0NTU5NzVkNTAxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFSekcvYUJXbGxlM0pyeFdyUTBZQUE9PSIsInZhbHVlIjoiN0lIaUFSQlV5VzZFYXZtMGVxVG9HRGU1YlYyNG1LMUF2WnQ2S01rUnJVTzZScjNwQytFTCsvdituT0JCTUxYSlo0Zm1qaWREbDFOOXBtc3FPclVOQkpYVzlGNE5EYWRiN01RRTA4Q1hVajdVZGJYaG1lcmxTN0EzWjE4eFhUZkphMDBMTjlza29wSmtvRUEzRUxnN1pCY2dyKzJIYTlPWDhmcFF5VnZPWERQL3RNemFIMm1uRXJQbkg0VzJmLzlhY2FrRU9iNXY4Tkk5eTRSUHRrRmY4M3lHelBkSU82VGRGeXhCZnVsM3I0KzdBTXhoMmg3cWNTbGs3ZEtWdm9IUFBLTkNrVThUSTRSWFhIcUlnZFhzQVFJSW5KQm5FMUd5SUtBVGsvYWFLTFMyL2ljMHNlYlpsOHJaUGIvWkdpK2FPZ2V5ek5neGRkcUN5VS9OTjBCYkd6SnFuU1pEQmpNRHBLbEFFN3pscFNvTExXNU9rRW9tdFJENGt6RXZKYVJFM3c5cnRsdVBiZnFoSmJRQVRZRUpsakNlL2dSdkRkSkpta3pTRVdLMkkxTWZWYlppU2FSdmNGV1MvK0NyelI1RlpibXk1MTVESmV6YnZ2U3JKM3UxQlRlL25ZSmFrZnptNlBUVnRSSkhpQlVyYWxpcUF0K1UrZm0vUDEwYzZiWEoiLCJtYWMiOiJkNTBmNDUwMWExODI0MzBkNWY1MDg3YzBlNGNiNTYxMjlmNGIwYjllNDc2YzY5NGY1OGU0ZWE4Nzk2MTRiZDExIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZpMmdQTHcrREttVGVnc3hXelN3ZGc9PSIsInZhbHVlIjoibzFhMVdSbDRVNHEzeVR3Zk9QRktYYVpESGZBOU5wU3F0OWV1RWl2THQ0ZWdUaDFuaVBubkhkK2pZTFZoNlMwMWpXRm5ZUFZkNHJyTFUvQVpUUjczZGh1QlBsZWNEMnhkQ1c0WlFNNzVHWWpwanpXUU5DM3lRSE43M0ZNMytQMWZNQlQ0Nmd6bnUxczQ2ZWtUb1d6eFhIbWdPazlGRC80WThQc1dUQ08xZFpyem5nMTF2TlpzM3MyQ2ZGcG1QdUc4TCtMbzYrNExtczhoZEM5czFxRmhvQis1bVlNM0pGRktCbWUrdGJlRnhqS3BZSVdvL2NrbVZoL2NGaHhUZVBXMFQyL3ZZS2EvdHVlVlpyeUN0bVFEWUt5NFNzQmVlNDZTZG9jb3RYRkdtNFlTa2kxZDJIeFZHZDRCSEFSZjZwWG9uRytMNUhCaUZtMHBoTHRGOENCSDA3QnF4cGxyaU1MeE5qZ2xBSjhKZDhEeE0xWDhxLzQ4NWVvN2NjalFyK2Y2QWlObmtmTXpUckdnalNSa0tteFVMQllwdUZMVjFzMk9BRWZtZnJ4Y0xZcWxuUENSMEY2OUF2RUVmY0t2TGZvTm5MVWwrODNOUTVsQ2YvTmwxaXJiNVR6Z0RXalRFWHkyNm10OC9GSURPVzZteXVZVllPK25GU2E3MzhDdllzOFYiLCJtYWMiOiJlOGU0ZDg0NWM3ZDRmYzQ0ZmQwMzliNzhjZGNmNmZjMWI5NTBmMjQwMTcxNTgyMDcyNGQwMmY0NTU5NzVkNTAxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFSekcvYUJXbGxlM0pyeFdyUTBZQUE9PSIsInZhbHVlIjoiN0lIaUFSQlV5VzZFYXZtMGVxVG9HRGU1YlYyNG1LMUF2WnQ2S01rUnJVTzZScjNwQytFTCsvdituT0JCTUxYSlo0Zm1qaWREbDFOOXBtc3FPclVOQkpYVzlGNE5EYWRiN01RRTA4Q1hVajdVZGJYaG1lcmxTN0EzWjE4eFhUZkphMDBMTjlza29wSmtvRUEzRUxnN1pCY2dyKzJIYTlPWDhmcFF5VnZPWERQL3RNemFIMm1uRXJQbkg0VzJmLzlhY2FrRU9iNXY4Tkk5eTRSUHRrRmY4M3lHelBkSU82VGRGeXhCZnVsM3I0KzdBTXhoMmg3cWNTbGs3ZEtWdm9IUFBLTkNrVThUSTRSWFhIcUlnZFhzQVFJSW5KQm5FMUd5SUtBVGsvYWFLTFMyL2ljMHNlYlpsOHJaUGIvWkdpK2FPZ2V5ek5neGRkcUN5VS9OTjBCYkd6SnFuU1pEQmpNRHBLbEFFN3pscFNvTExXNU9rRW9tdFJENGt6RXZKYVJFM3c5cnRsdVBiZnFoSmJRQVRZRUpsakNlL2dSdkRkSkpta3pTRVdLMkkxTWZWYlppU2FSdmNGV1MvK0NyelI1RlpibXk1MTVESmV6YnZ2U3JKM3UxQlRlL25ZSmFrZnptNlBUVnRSSkhpQlVyYWxpcUF0K1UrZm0vUDEwYzZiWEoiLCJtYWMiOiJkNTBmNDUwMWExODI0MzBkNWY1MDg3YzBlNGNiNTYxMjlmNGIwYjllNDc2YzY5NGY1OGU0ZWE4Nzk2MTRiZDExIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221355709\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1137817492 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137817492\", {\"maxDepth\":0})</script>\n"}}