{"__meta": {"id": "Xd3ab65cc3f427ec5e81016674dc668fa", "datetime": "2025-06-28 11:23:04", "utime": **********.92409, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.457757, "end": **********.924102, "duration": 0.46634507179260254, "duration_str": "466ms", "measures": [{"label": "Booting", "start": **********.457757, "relative_start": 0, "end": **********.835374, "relative_end": **********.835374, "duration": 0.37761712074279785, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.835382, "relative_start": 0.3776249885559082, "end": **********.924103, "relative_end": 9.5367431640625e-07, "duration": 0.08872103691101074, "duration_str": "88.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48135768, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01703, "accumulated_duration_str": "17.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.868503, "duration": 0.01156, "duration_str": "11.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.88}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.888778, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.88, "width_percent": 1.879}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.90339, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 69.759, "width_percent": 4.345}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.905571, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 74.105, "width_percent": 2.466}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9107819, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 76.571, "width_percent": 14.797}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9154, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 91.368, "width_percent": 8.632}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1342377811 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342377811\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.909304, "xdebug_link": null}]}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1946572297 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1946572297\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-71988188 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-71988188\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-985189408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-985189408\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6Ims3MzRSTHpxNmlXc0xuemFHVitDeHc9PSIsInZhbHVlIjoiZDNybktHOVBOL2NyTzZUOVA2ZEs5eis0d1VIKzNwUWZueE1qMUo0b1AzVmpJU3E3dEFZVUNjbGpETGlIMFc3VXJYeDBmYmEvZ2pyZmRob0lPQW9xaFZqNlBneGh6YmFDN0VxTDNwU3ZYNGxMb3hzZG5xSEticEt6ZUszVXFndWFlc3hpNFA0dlhlWFlGcVlNZ2ZCOXlDNGh5bkNTZU1wNWJyYnZhcGJINmsveExnTDVUV0ZlSlZ2RzBoczhUZzhqbWdTYUNyeTh4YjVWZmlCRnpiMW1GRW9QdTBoRmROTVV5ZEpHVm5jenRlQXZERWVCR0QzVmN0RDVlSXRDaE9wazVzSUVHYjErVjhnYmlvTEdHSGRXMzdrcWNSdDEvOFpNck84azY0UkJxZzYxS3ZVaVYxako0WUNLdStCM2RXUFZrZkdLR1Z2SVFPcHgzQXJyNEF4aGtLbG1pWTQzdFlPVkFHcjdmTjVDdXpmbVVHdENnTnB2SCtzSHRqM1NRbmpzYWJWNlc0ZGRwenA4TDExN0JoZU02R1pUMlpQU3BacnU1UjA1aFZZMnVCRFhzeDcyMHBONUtocFRrYXRkeGJrZkloaUQyM0hsRkgzTEVEV3c3UWVucUltd0NSV1lnaW43YXpQQnhtY0NHV2hNano2bFNKTnhZS3ZoWkdwRG5YTGoiLCJtYWMiOiIyYmRiZTk3OTcxOWI1ZTczOGRmNWE1Zjk1NTNjZGZiMGFkNTFmNjA1MWQ2YjY4NmQ3M2I2Y2U0NjU3NzRlZDJhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1pRnpIajBmcmpEUW9BWmFibFlLTEE9PSIsInZhbHVlIjoiQWpTOStJUmNnRy8vTlUrbzZnQlphQ0VJUG1CdVBQcFFzUXNPQVJjMHpNTGlXR3pvOWROT0JEMVhNK2lnZmFuOVpvVUFRb1ZHd28rMXNaUWxqSHRpbExHYVlmSlNCQXgxQVhjME9DeWNsUTk5ZXN4Y3lJeERHZnFZTFR0QlVyOXEzMk0vamdhTktNelcxY2xoYmFyMFR2RmZnTUd0U2hGV2hEbUZyaXAyK3pOVVErbStYanhxOU0vNmVTZk9haERja0VBNFFiVzdZQmZlR2o0ZEQ2N3ZOV0VHczZaZFhPLzJ5THFoaXlWek1YYm5jOFhFU2h4eWt5VWdLVThRdVlXOFg3UFluYTdHTjFuOXgwWGdCL3dyQS9iYkZSWnpuNHJQVGZJc3pyV0xpNXR5UHdOTTVVQlViT241NUt0dmtaellINGpwMDE1bjNUTVEwMDlSUGZiVitRY3NCaXpHUDFPMHV0TURObnhnblZiYTJtL0NGYTJNeXVTakdrcFphWnowVGkwMUMxOTNsbE9NYXhqcHNBMSsyRnBFVnZSYTFxcU9PeDFNM0RQY2VDa2thcWpGcVU5Rnp6dmR2TXZnNkNSQkgwNmhOa0J1OG9BQ05kK2IyVFc4Q3pFMmRmelJDWSsrQURCUXpsWEFVZFladjBBaXY4SE9BK085NFVMV2V0bmwiLCJtYWMiOiJiNTg5YzA5NDc2MTdiMmQ4Nzc4MjY3Mzg0MTUyNWNkODhhNTBkMzdjNDIzMjUzNzdlYjY2YWIyMjcxYjE2NmU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1250572977 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250572977\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2059740163 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhCSkkxU2VoWXJCcUpCaHh5dmlrc0E9PSIsInZhbHVlIjoiSkhKT3FLZGlUL2RqZkt1eDV3ODUvRnBzMGdjdG55WTRFZDQycEZydFA5Tmd5bGJYemJ0bmhVTU0zWXVHd2MwcCtyRWVnQ0kvM01oNitxdTlOQ3hPTXpiOGVMZTlHSkNyR1RyUTFCQlNnQjJkMGNMeVpURjFrekZ0d2hGdytVVm9ZMHBjdnJJUkZTRHZRSXozdjJ0UkVYbjAxSm5MdXZDSXZUTUZoSFNDa3ZPbjNwUkFNVktyS3MzMWYrYzFHYlZILzlWL3lnK3djZExvMndrWjd3TXR5eUlNblJLWVl1encyTE1YQkhzdmQvVjlWOFFzbzlEcDlIbnRhV1JtY2ZUcHQ3S25tMW1uWFh1RjNMT3ltbnh0ZUVJYVliVGFGSTJ3eUt6eWptMHg0S25MODkyR0pRUk5VYjRubG1Wd2dBS2J2WmlBMWpUM0dxMVY5VTFnZWNvcE1vS1Q4emgxWEd0R3c1YllUNUhUdkxvUW1CQlR5ZWFOL1ZXVVIvTW5VbDA1ak41ckNYWVQxejlmSllmUmxHMWo1cUFEbUcxL05jMzh6ZGxlYWRLdllRNFdmbVNxRUVLT0RSUmxkUEJzK2tuK3BTeXVPYmt5dDhVVFR2dmVBaU0ydlJ6R1c0MFdtbzM3bUc2UkdNajk5ekR0SFNHUFF3cFVaQitGUWc3OUN6NDEiLCJtYWMiOiJhODVkZjRlNjU3ZTZkNzEzNWY4ZTc4ZGJkZGQxZTZlZmU3MjUwMTBmNGVlMjE5Mzg2MjA5MjU4MWMzMmRmZDI2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InVyQmdLRlJTbXdGT1FHRG9FSk8zdVE9PSIsInZhbHVlIjoiTG1CeGp3a0lBdm1qYktQb0dBWnltQVRtYmRaVzgveHpXUElrTTdnQjl4cWpwWE0wYU9OenJFb280d21zc3B5U1Q0Mll3SUxUZUxsRnh6c2J4MzQxR1pRWHAzc0tJKytMRVFyK1VpaWdJSlNsQWZFblY2ZThDeFJZYnErNTVJcGdMcnpjNGZzMzYyYVpWTnZ2RUFjVFFlVWtwNEUvRHlzOStwRjF6RDN0VTZxbzg0V25OQkM5VTFqay9IZWxnQnJVQWdrckZ3L3M2d1NOTitrM2NBWTVzTVk2TnVIZnlLajJWSGpicGRuYkRHTWgzNEEzSDFFZ0kxY1NoRm9MYUxSQzJRbDl0ZGJrSEQxa2xXQWg1dmRlaE11L1lvaEo5Nis0MTlYTEUvTXJnWmhWQ3BHOHo4a2FyWDNHY2xZQVh3YWFiOTBZU21MT0Nva1BvVEt1RGRtdC9mekRkNkRsRE5YZ0dzNTVlYXQxVTB3MHJUQlM5WnRvVzB4UFRkQ0J5ZmhON3p4dm5MZGNxdDhyejR5NFIrNkNyWnBIeWJkd0JQRVhkeFh3WGxDcWNoejNtSzBOeTVONjdaNkhNRGtmWTN4ckNRbkpIUktBNzZsNko1THowZzdWTTBTa1JObnQ2cTNYUEwrWWh6c2Q2b0tmSytBNkVHaXJGS1ZFSTVwRmV4SzIiLCJtYWMiOiIwYjIzMGM4ZGYyZDA0YWI1MWE3ODkxMDE5NDI5M2RiNTRjOWI4YjUyZTM1YjE2NjMxYWRhMDkxMTA0M2JiOTAwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhCSkkxU2VoWXJCcUpCaHh5dmlrc0E9PSIsInZhbHVlIjoiSkhKT3FLZGlUL2RqZkt1eDV3ODUvRnBzMGdjdG55WTRFZDQycEZydFA5Tmd5bGJYemJ0bmhVTU0zWXVHd2MwcCtyRWVnQ0kvM01oNitxdTlOQ3hPTXpiOGVMZTlHSkNyR1RyUTFCQlNnQjJkMGNMeVpURjFrekZ0d2hGdytVVm9ZMHBjdnJJUkZTRHZRSXozdjJ0UkVYbjAxSm5MdXZDSXZUTUZoSFNDa3ZPbjNwUkFNVktyS3MzMWYrYzFHYlZILzlWL3lnK3djZExvMndrWjd3TXR5eUlNblJLWVl1encyTE1YQkhzdmQvVjlWOFFzbzlEcDlIbnRhV1JtY2ZUcHQ3S25tMW1uWFh1RjNMT3ltbnh0ZUVJYVliVGFGSTJ3eUt6eWptMHg0S25MODkyR0pRUk5VYjRubG1Wd2dBS2J2WmlBMWpUM0dxMVY5VTFnZWNvcE1vS1Q4emgxWEd0R3c1YllUNUhUdkxvUW1CQlR5ZWFOL1ZXVVIvTW5VbDA1ak41ckNYWVQxejlmSllmUmxHMWo1cUFEbUcxL05jMzh6ZGxlYWRLdllRNFdmbVNxRUVLT0RSUmxkUEJzK2tuK3BTeXVPYmt5dDhVVFR2dmVBaU0ydlJ6R1c0MFdtbzM3bUc2UkdNajk5ekR0SFNHUFF3cFVaQitGUWc3OUN6NDEiLCJtYWMiOiJhODVkZjRlNjU3ZTZkNzEzNWY4ZTc4ZGJkZGQxZTZlZmU3MjUwMTBmNGVlMjE5Mzg2MjA5MjU4MWMzMmRmZDI2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InVyQmdLRlJTbXdGT1FHRG9FSk8zdVE9PSIsInZhbHVlIjoiTG1CeGp3a0lBdm1qYktQb0dBWnltQVRtYmRaVzgveHpXUElrTTdnQjl4cWpwWE0wYU9OenJFb280d21zc3B5U1Q0Mll3SUxUZUxsRnh6c2J4MzQxR1pRWHAzc0tJKytMRVFyK1VpaWdJSlNsQWZFblY2ZThDeFJZYnErNTVJcGdMcnpjNGZzMzYyYVpWTnZ2RUFjVFFlVWtwNEUvRHlzOStwRjF6RDN0VTZxbzg0V25OQkM5VTFqay9IZWxnQnJVQWdrckZ3L3M2d1NOTitrM2NBWTVzTVk2TnVIZnlLajJWSGpicGRuYkRHTWgzNEEzSDFFZ0kxY1NoRm9MYUxSQzJRbDl0ZGJrSEQxa2xXQWg1dmRlaE11L1lvaEo5Nis0MTlYTEUvTXJnWmhWQ3BHOHo4a2FyWDNHY2xZQVh3YWFiOTBZU21MT0Nva1BvVEt1RGRtdC9mekRkNkRsRE5YZ0dzNTVlYXQxVTB3MHJUQlM5WnRvVzB4UFRkQ0J5ZmhON3p4dm5MZGNxdDhyejR5NFIrNkNyWnBIeWJkd0JQRVhkeFh3WGxDcWNoejNtSzBOeTVONjdaNkhNRGtmWTN4ckNRbkpIUktBNzZsNko1THowZzdWTTBTa1JObnQ2cTNYUEwrWWh6c2Q2b0tmSytBNkVHaXJGS1ZFSTVwRmV4SzIiLCJtYWMiOiIwYjIzMGM4ZGYyZDA0YWI1MWE3ODkxMDE5NDI5M2RiNTRjOWI4YjUyZTM1YjE2NjMxYWRhMDkxMTA0M2JiOTAwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059740163\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-659471067 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659471067\", {\"maxDepth\":0})</script>\n"}}