{"__meta": {"id": "X38617be7828d6ec7deb20e609e1309db", "datetime": "2025-06-28 16:04:14", "utime": **********.899058, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.472456, "end": **********.899072, "duration": 0.42661595344543457, "duration_str": "427ms", "measures": [{"label": "Booting", "start": **********.472456, "relative_start": 0, "end": **********.821141, "relative_end": **********.821141, "duration": 0.34868502616882324, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.821148, "relative_start": 0.3486919403076172, "end": **********.899073, "relative_end": 9.5367431640625e-07, "duration": 0.07792496681213379, "duration_str": "77.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45700352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02714, "accumulated_duration_str": "27.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.84843, "duration": 0.02597, "duration_str": "25.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.689}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.883981, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.689, "width_percent": 2.211}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.890594, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.9, "width_percent": 2.1}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IlcwbnA1QzRLRXU0dWNUclJteE9TOEE9PSIsInZhbHVlIjoiRldBWXdUTEJXcFh3WnhYbEgyQjROZz09IiwibWFjIjoiMThkNDVlZTdmMWZmZTY0OTVkNTMwNzVlNzIxMTY5MzhkNDgxMWI1MWE1NjAxZGIyYzMyZDgwZTNkZmFiODZkNSIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-458667755 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-458667755\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-82967192 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-82967192\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2053450646 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053450646\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-587479334 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlcwbnA1QzRLRXU0dWNUclJteE9TOEE9PSIsInZhbHVlIjoiRldBWXdUTEJXcFh3WnhYbEgyQjROZz09IiwibWFjIjoiMThkNDVlZTdmMWZmZTY0OTVkNTMwNzVlNzIxMTY5MzhkNDgxMWI1MWE1NjAxZGIyYzMyZDgwZTNkZmFiODZkNSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126652708%7C24%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ind5NFRwY3JoNGpZNXFIditORHRzSWc9PSIsInZhbHVlIjoiZi9ldG5jdGtvNTlDR3ByVFhrcFFtV3R5aDR5MngxODFMMURQcTZnRDMvL1pUN1BONTZ4eVdNY20yNmpqdEVzOWptZ3F6MzRRQ1hFbXFnTUNEWkFJMUNNR3N3UzNYVFBVakNHOGRWL3lITzBORndydVJYak5iT1FPMU5LQ0l4UXdEYlZxZ3lKN3QvYlNtckJCU0RvdGRSVDBJdlVGZWZKc29VeWgzOUsxak9jMzAwV1NwNVljVVdwVlpRR0pSTUVRMkhsUlpUVndyQlh4QzVkalh5aVZRd3I0cURoYnZGMVlMYmcrcHFPZGxXb1BPbHBvN05vQlBHOUtuMTE1NVR6QXdvalhJVU92TmpUQWR2NzdGQ3RGVkF1L2VTdWViZVlmZ3lYYWNxMVlEQ0pFbTZyMFV2Tkx4dEU4dy8xOEhjc3pKMFlhWjZiMFptZ0VUUUlCOGRQNVFRYU8zQjJhSVlsSWdDM1Q5TmRMYUE2aEdFV0Jtc2c0U1JPQjl6OFhoRDhMWCsvN0dybENxdU9lZHVJYzRONC9pYVlTajJmaDRUOWJENjZtZDc3cTZFcDNVVHNCWGxjQzNZbHgvcmcxdXlZWmMvVTY0Mm1YVzZjRXUzVExHb1UzZkpsNWc3YVAwWkNJOWZFZ3pPOTQ1TkxpcnNVc2RPUWFiOG91SmRzU01mWDgiLCJtYWMiOiIwYmNkMGU0NTA1ZjIzZWZmNGNjNDE4ZDY0MTU1ZWRkYTIyZGY4YjY1MjIyYzRiZmNiYjgzNGIwM2RlNGQyNGYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImF0ay80N0dVdnYzMExhN2cvN1NFbGc9PSIsInZhbHVlIjoiZVZIR2lQNG9TS1VmZDJXcEpNSklRN0dWMDQ5NUx3aWt1Y0pESFFFdmpTSjdpY2FIcVVoNVNnM1AzeWFmS0RPL295MU0yMGp0WktkQzJ0ZTIyYU9KdzJnamJoUVJPcnNockgwRHc3ZmR5MkFnZEVlZkU2bm93T2hwZk5pR095RE1YQzh1TE14bTJvMUV5TkdRcHA5Qlg4dC92WmhwT3gvdUJEa3NTaDdjdHBqSndrUXZ2WTM1OGRsN09mL1JEMzJySlVhTHdFbTQ5UlkzREtRSmRxWS9CL1RHM285ZzJBbHZjSFZ1OEtDRFRCN2FCTW5VdjdYQmNzSUpHU0RCY1F2dEVYKzJWRzdBZ3hUcFEwS2d1M21pU2Q2Vzd4eVR1UGpDanpBMnFubHlTWlhNbGF5V3VnQ3ZzRlVyV2hsbmZKK1B2RldJZzVza0N0V3JxK01MdUhUSlhPclpBZjNpY0dYb0RJWTdDUjVvL2oyUGJ6eGZyMGhBWGN5K2VVSW1DY2t1VkVmR0wrNHFkVzUraFFuRVRTUG1xaGFOQlRNelAva0VkbTQ0dmhJNjlUNUMrNldrZG9NQ2xEZ1pwMkFZMm5oTkNBdEVEMko5bXUyZ2t0Z296UmRGYXZvWG5Cak9XaldnUFR3c3lMdDU0c0FHdENoVS9QdkV5L0hwQ0NNNGsrSzQiLCJtYWMiOiI4YzFlZGE4ZWI3Njg2YWU1MDk5NzZkMjE1OTVkZDEyZTk1NTA5OWM4NTIzZTY5ZTVjOGNhMzhmNmM2YmJhZDNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587479334\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1864605149 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864605149\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1552441462 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:04:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZyNHI2YldRL09CdXhiSFRGUGEvZkE9PSIsInZhbHVlIjoiUk1tamN5UWRPWDFnVjRjSUpHQkU4djgwWWVuVk53dExqT0I4ZUpRZmhDT3M3c3NjamlTUU9oVlBlWld3VUl5SldXcW5Tczl4dTl5L2M3TEhuWjhKbjl3K2ZjZVZFa0RsSjFvSmdRNjByZEQ4emRxcXg1RGRaOXpBb0UrT0tqN3RuUHlGVTRROU5sSVF3TGJpS24xUDJrQ0NOUGczZ2N2TSt6a0pqUjNnejBZVGZRLzJLbm9qekFYdUdwQTJ4YTdKS0lYQkdPN2lmT0ZZcTc5UkhiUVo2UjU5UE5kcW9Zb2dzMmxUdm11WFZFaG9BWFpLSmhYSWk2dEJScFZCMm1xTXlrbXR2UTNQUStWSkpSYWwyRnhoU2kyT09pbm1YcytMZmxqOTlVZFJXZDdGN3oza1lwc2VYMGpmNGVGQTFQT0FmUlQzd2R5dnp2OWlrYUVMeTlHWHBqUDhOZU9JOUE5dGxZN1BIeGxqTkhDMkdwM1RmZ2ZVbWEyWGpCTjlsbHp6OFV3N3JxdUJZc2ltc0h4WnhRQWM5M3BJV2JmajRyMWsvNmF2dFY4N2Vid296U3plenFMa3BhZWhranV0NHFZUjVLeU1zc0ZldzJlTkxDTTAxUm5aMzFIRkNoYUV5SldjQkZjM3VEZTFKSlM0OE5FQThtTXVVQkh0ckdWN2YwOUoiLCJtYWMiOiJkNGU1OTlmNGQ4ZTFkNjkyNjM2MTM0Njg1M2FkZDJmYmFlZjFjNjM2Yjk0MThmZjEwMGU0ODkwNGY0YjlmZjJkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjkvQkxUa2w0WmVvYlI2ZklnZlpMMGc9PSIsInZhbHVlIjoibjhaZ05uNjE5UkRqdllvRm54YlBteldTUTUrU2FkemFqc2swTm9pOHB2Vy9LTWtHOEgzdzh6djdmRW54cGZBbmdGTURwOWV6OHBKeFZlVlB5WFlRelpaQTQ3a3dFa0lGQmcwejBDT3BWMjhRZ1Q3eVJyR05Tc2JVbC9VMmFGYU9tR3o2Q0VzdE9LMXFLNTJVN0tpZzZMUXpSY0dOVzcwekN3bHZXRUdCdWdEQ2pDV3ZTLytDVVhZbm00QjUyZDArakRxcU1hZ0pNckl0UG5xaW9zWGhzQmtZaWUydWNKMVRiU1dsV1lMVXdvSmNnZDl1cm9ROVlNMmJJdWxhMTNqYnlKaXcxTEJrMm9BZmkrdTBSNkZiQlRIZGlJVXNjby9tcEJUOXJ4OVVPazRGU0p4ejVyZkZSdHhhZnorZjhOVGRSRFlNRmdDdHZ6YWNvNGlLUXdSMzJnaHUwZmFNMXY5UThDOW9QdmZ6MXFGa01neVJlRy95Z2ZaTE95NHAxMmpocGxVZVpVY0crWDNKRnpFS2ExQ3NHZ204UVo1Z25MZmRZM3R2Z1lRaE9tRE5zcXN4UEhHUUl2OHFrZ2dEQ1ArZ0M5MURzU2RucWQvdzc0YkRHaE5ieWNkR2lWZXpENG1ndVBJaGluNHJlOE9tSU83WmZPbmZ5ZktYaWtYdEpHZkUiLCJtYWMiOiJjNzcxZmUxNjEzODUzOGFkN2M5ZDliOTgwNDlmYWRmOGJkMjM3MGNhNWE3YmEzMzNmZmU5MGE0MGEyYWYzM2I1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZyNHI2YldRL09CdXhiSFRGUGEvZkE9PSIsInZhbHVlIjoiUk1tamN5UWRPWDFnVjRjSUpHQkU4djgwWWVuVk53dExqT0I4ZUpRZmhDT3M3c3NjamlTUU9oVlBlWld3VUl5SldXcW5Tczl4dTl5L2M3TEhuWjhKbjl3K2ZjZVZFa0RsSjFvSmdRNjByZEQ4emRxcXg1RGRaOXpBb0UrT0tqN3RuUHlGVTRROU5sSVF3TGJpS24xUDJrQ0NOUGczZ2N2TSt6a0pqUjNnejBZVGZRLzJLbm9qekFYdUdwQTJ4YTdKS0lYQkdPN2lmT0ZZcTc5UkhiUVo2UjU5UE5kcW9Zb2dzMmxUdm11WFZFaG9BWFpLSmhYSWk2dEJScFZCMm1xTXlrbXR2UTNQUStWSkpSYWwyRnhoU2kyT09pbm1YcytMZmxqOTlVZFJXZDdGN3oza1lwc2VYMGpmNGVGQTFQT0FmUlQzd2R5dnp2OWlrYUVMeTlHWHBqUDhOZU9JOUE5dGxZN1BIeGxqTkhDMkdwM1RmZ2ZVbWEyWGpCTjlsbHp6OFV3N3JxdUJZc2ltc0h4WnhRQWM5M3BJV2JmajRyMWsvNmF2dFY4N2Vid296U3plenFMa3BhZWhranV0NHFZUjVLeU1zc0ZldzJlTkxDTTAxUm5aMzFIRkNoYUV5SldjQkZjM3VEZTFKSlM0OE5FQThtTXVVQkh0ckdWN2YwOUoiLCJtYWMiOiJkNGU1OTlmNGQ4ZTFkNjkyNjM2MTM0Njg1M2FkZDJmYmFlZjFjNjM2Yjk0MThmZjEwMGU0ODkwNGY0YjlmZjJkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjkvQkxUa2w0WmVvYlI2ZklnZlpMMGc9PSIsInZhbHVlIjoibjhaZ05uNjE5UkRqdllvRm54YlBteldTUTUrU2FkemFqc2swTm9pOHB2Vy9LTWtHOEgzdzh6djdmRW54cGZBbmdGTURwOWV6OHBKeFZlVlB5WFlRelpaQTQ3a3dFa0lGQmcwejBDT3BWMjhRZ1Q3eVJyR05Tc2JVbC9VMmFGYU9tR3o2Q0VzdE9LMXFLNTJVN0tpZzZMUXpSY0dOVzcwekN3bHZXRUdCdWdEQ2pDV3ZTLytDVVhZbm00QjUyZDArakRxcU1hZ0pNckl0UG5xaW9zWGhzQmtZaWUydWNKMVRiU1dsV1lMVXdvSmNnZDl1cm9ROVlNMmJJdWxhMTNqYnlKaXcxTEJrMm9BZmkrdTBSNkZiQlRIZGlJVXNjby9tcEJUOXJ4OVVPazRGU0p4ejVyZkZSdHhhZnorZjhOVGRSRFlNRmdDdHZ6YWNvNGlLUXdSMzJnaHUwZmFNMXY5UThDOW9QdmZ6MXFGa01neVJlRy95Z2ZaTE95NHAxMmpocGxVZVpVY0crWDNKRnpFS2ExQ3NHZ204UVo1Z25MZmRZM3R2Z1lRaE9tRE5zcXN4UEhHUUl2OHFrZ2dEQ1ArZ0M5MURzU2RucWQvdzc0YkRHaE5ieWNkR2lWZXpENG1ndVBJaGluNHJlOE9tSU83WmZPbmZ5ZktYaWtYdEpHZkUiLCJtYWMiOiJjNzcxZmUxNjEzODUzOGFkN2M5ZDliOTgwNDlmYWRmOGJkMjM3MGNhNWE3YmEzMzNmZmU5MGE0MGEyYWYzM2I1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552441462\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1138570839 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlcwbnA1QzRLRXU0dWNUclJteE9TOEE9PSIsInZhbHVlIjoiRldBWXdUTEJXcFh3WnhYbEgyQjROZz09IiwibWFjIjoiMThkNDVlZTdmMWZmZTY0OTVkNTMwNzVlNzIxMTY5MzhkNDgxMWI1MWE1NjAxZGIyYzMyZDgwZTNkZmFiODZkNSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138570839\", {\"maxDepth\":0})</script>\n"}}