{"__meta": {"id": "Xdf2e52d9fb0fabfb969de01ae1ab89c5", "datetime": "2025-06-28 15:03:48", "utime": **********.430545, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123027.989357, "end": **********.430558, "duration": 0.44120097160339355, "duration_str": "441ms", "measures": [{"label": "Booting", "start": 1751123027.989357, "relative_start": 0, "end": **********.317742, "relative_end": **********.317742, "duration": 0.3283851146697998, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.31775, "relative_start": 0.32839298248291016, "end": **********.43056, "relative_end": 2.1457672119140625e-06, "duration": 0.11281013488769531, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 62562040, "peak_usage_str": "60MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.046509999999999996, "accumulated_duration_str": "46.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.353915, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 3.505}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.364119, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 3.505, "width_percent": 1.355}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.367656, "duration": 0.04425, "duration_str": "44.25ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 4.859, "width_percent": 95.141}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-1810651735 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1810651735\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-390209000 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-390209000\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-142117946 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-142117946\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-553500332 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122996219%7C8%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdyZ1hXWFJQbjU5R3dZUURaenZEOXc9PSIsInZhbHVlIjoicEdTRzVEMmJwczIyTjF0VFdSZm5nVUZtOHFOTk5Wam5ZRWhFUFJtSkNDTlFIMzBSc0hPcGNpbkJKcXY1R1lOTmZLUWhXZm1JVnNibTVzZW1raDF0RWFTUWN2V3ljTm5uS2RpQ0dBdHQxZkx5bkNWTHpRb0hQYnFpVEV0NTI1RnhZKzJTZm9aVS9qTnlzc29VSWIxN0hSMEI4ZU1wT2wxNy9iU2ZwVTlpRHhSUGJnSmJhVjB0VFNFcVNtR2Jvb2xBbVVkaUxtbkdSQk1VL0JCSTQwOWdRQWw0cjdhU0V6M29WK2tSWEtWRmhldnJkTkNmSWxpdU5PTm1hcnpzQlVtOXFFaGtycUNxZExzYm1VYjFXOGlXNnVWZ1Q1c1A3NkljalU3d2JCMzBuVVFEVU5FdFNxdE9FMCtycmNZREF6RzU2dy9FaWJrNURmSTVCRjl6UmMwMTUzYWJrcEc5N1g5MXVsVDVNSXNzRWQyVnI5UHZxRmNlMVhUVlRuNDZVVWNyRXJTQzRJUzRCbEtHV2c5TW5makc1VUxmMVJkb0JPRCtMZXdyZ1BHY3VqMnlwUysxaTFPcUZRMG9KSUpuREYyQUNmVHpsL0k1WDIyc2tubS8zSWc2U0lVYTJLWDVhblRpendZMDlkWW04d0pvTlBTUUZUVDBsVldGb0xrcVIyREIiLCJtYWMiOiIyOWYzZGVhNzk1MWNmOWUzZmQyYjVkZGZlYzg0MTFlYjI4ZmQzZTY4N2UzODU2MDMwNzJmYjZhZWMxMzIzOGVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjB3Zm9rY2UxKzBwRy9YWWVzV0ZMY2c9PSIsInZhbHVlIjoiZTV2aHRZbDhvMUNleVZwckpUNjZpcFloMUV0KzdzYkN5ZUxSck50RlhFdlM2elpncnU0a2toazVlZGhJb3ZIek0zaGdtclBTVGN1WTFBRVh2aEFTQ2NlVWhZUyt4aG9KTHlGVHdkTXY5bXhoTFZHVjVlckgwUUxIdFRmS3lSeHdYYWVUbXBIVk5mKzRQdkJwdWZTcGhCTllkKzRlTGJ6NUdiVDNieWFzUTQrWFRoZVhkMEF2dGs5SUpwbXp6bThxUmlCTFdhUW5XeVBSYUpvVWh2R0hwaGpMVGRkdkZDL1lYczdxeUVRWHd4ekV6S2lZUjVLVXdBRmU1bW5uZ01yN0l6bmxlWDlSa2UvNHI0VGlIK3ZRS0FNbEVnNlI4cElnZnlveGh6NmlrV3dZRml6dmxBSlh5VjNBa0pHM0Q5YnV1R0dyU2xyUmx2T3IrZ0gyY1p1dHlCejJDOWJkL3hxd3RaZEdieTdHQTJLOGJVUjdqb29GSzdQZm9iSitpcWt0YlRBcWd3eHo2eGhFM2Iza1I1UW9hSGZ6WkVuUDdVSVBFTXhjSEpzR3dyVkhBcHcvNDV1QlBwSjY5RWE5a0tnbmkwc2FNRUhYVUdaUEFaNW5KVTBDQXRYU085aFUyOG9aMDZ5M3M1Nno4eHhITTNyQmo1QzAvZDBaak1MaHd5cjciLCJtYWMiOiJkMGJjNTBiY2MwZWJkNjMwMTYzYWZkMGZjNTczOGFkODNmM2VhOWE0YjgxNWYyMThmMGJiZmE1MmNiOWI0YTg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553500332\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1603848911 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603848911\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-727632661 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:03:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFOUy9lQjNuNVhKZmlHOHVxMDNNZkE9PSIsInZhbHVlIjoiY0ZvRjcwei9HeWFzWXh5Y1AzTUlFZ3JwYU1sUk5FUlZJanpQMWJzYVVzbExYTFBLWjI2cHZPNG9JT1hsaG4wV0JJamdZS1JkeUJRUkFEeFJtYWtwTkFCZGNUN2F2ejVJY216Vm50WWpnQjRKOWNmb0lmdXY1T2RLeUdJNXVOOU1ZZHYrU3JqYVNtN0E3K3dUT0xnb0JHMTk1elNnQkZxSzE3d1UzMlNqdHVkT1R4anlUdThvNW9vMzdNd3g5Q3BlV1hnWWxlUlJwWDhYS2lyRXVESWE0QUFQSFBrajBUNjN3QS9TS2NNaVlqOUZSYm1ibTEwNEJ3NEphSVdtOGp1Ym53TkVkT3I5Q09UNEI3WkNOR2s3UHM0K1ZOa05UNXVWMUU1cHRsOEFjeTZza3grTzRHeHN6dFM4MlFib0V3Z2MwdHRTc3lUQ0tXWnFsK2ZWYVJKM2FScDdBRW9abjJKMnhpenBhalRhNVMvQXZTdHF1Ny9kcFMzYUsyMklzekFqZkh0NFVLUHJ6ekFuUWE0Vmk5RFZJNVlWdFhaRDZNVUFMKzR3VTFuVTN3Y01BZUVpK1VRMndsRnBLK0VSNFRjQ01TbmFMZm1VcTM5OWl5dlZQUkFyRzV0VTM5WDFpYTllQURlamF5TzBmSnhnMHV1NU1nNENlY2QrTkVYU2xnZ1oiLCJtYWMiOiI4YjcyZmIyNmYwOGU3MTAyZWUwZmU5MGQzMzI5ZGJiMGYwNWE2NWI3NGE0Yzc3MjhjNGRkNjRkYWY3OGE2ODM2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJUSDZoOFN3ZzlyaUdpTlY2Rzg3blE9PSIsInZhbHVlIjoiNEM1MHlpeTNOMkNpS0QyL01YMm4ybUd4TTY2dTJxRXFua3JDWXRkejNFZGdmamEvVFZlclM4cFNDUk5hZlcrNDVlYmlMRVZ5UEVIdEZHc1gzQkJPcXJHMXdKRlZPQ3NSaWVOUHNXUmJlVE9mN2xLVnpaVGpxSnp4cXVsQkovc1ZoY1RNYnIzOHd4Yk9SS0RZU3I5K0hBQ2gvRmpHQ0lUajNxNEZUbUZJT0tjU3U4TE14MHdzeHZoSnduaytKNmZHYjF3ZTdQVHFBcXh2b1hlSEdtU0FDcURhWjB1YVRYU3NpNzE4L2RZdUZMa3V1SVo5VVR6U003dXVKZUZRLzhTUXkxamFKRnREcHdFNU9rSUhKYWRiVDNWUS9PNzJNd3dsMCtXeEpESHJWcVJSamJKUDVEOUhLT1NmbjhyNjFreTN2bksvbkJjbTlETnM1eUJIWW1yNnlJS29seUxKZ0MvckJjbkZMVXBNblhJeE83ak5rTUxtenkybWVjREF6K0dOMjk4d05sRnp3WkFvNTAvci9hdHgyUEZSRlN4OTdhc3MvL013SzdxbUgwZ2ZjL2FwVGNFcjVIblMvQWNHNHBnNCtSUjFocUlEb0RzdTlORDRUSkZiWlpuWlYwUlRRM3BJMFRwR21hZG1kZWI5M2FGYmpPd0YwSnp1b0lTbzR0Q0oiLCJtYWMiOiI4MzM4YTA3ZDBjOTY3YjViYTNiYTVjM2FhZGNmMGQ2ZDFiMTJlZWRkMGQwZjEzYTQ2NjUxYTY0YWFmMWM4MDU1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFOUy9lQjNuNVhKZmlHOHVxMDNNZkE9PSIsInZhbHVlIjoiY0ZvRjcwei9HeWFzWXh5Y1AzTUlFZ3JwYU1sUk5FUlZJanpQMWJzYVVzbExYTFBLWjI2cHZPNG9JT1hsaG4wV0JJamdZS1JkeUJRUkFEeFJtYWtwTkFCZGNUN2F2ejVJY216Vm50WWpnQjRKOWNmb0lmdXY1T2RLeUdJNXVOOU1ZZHYrU3JqYVNtN0E3K3dUT0xnb0JHMTk1elNnQkZxSzE3d1UzMlNqdHVkT1R4anlUdThvNW9vMzdNd3g5Q3BlV1hnWWxlUlJwWDhYS2lyRXVESWE0QUFQSFBrajBUNjN3QS9TS2NNaVlqOUZSYm1ibTEwNEJ3NEphSVdtOGp1Ym53TkVkT3I5Q09UNEI3WkNOR2s3UHM0K1ZOa05UNXVWMUU1cHRsOEFjeTZza3grTzRHeHN6dFM4MlFib0V3Z2MwdHRTc3lUQ0tXWnFsK2ZWYVJKM2FScDdBRW9abjJKMnhpenBhalRhNVMvQXZTdHF1Ny9kcFMzYUsyMklzekFqZkh0NFVLUHJ6ekFuUWE0Vmk5RFZJNVlWdFhaRDZNVUFMKzR3VTFuVTN3Y01BZUVpK1VRMndsRnBLK0VSNFRjQ01TbmFMZm1VcTM5OWl5dlZQUkFyRzV0VTM5WDFpYTllQURlamF5TzBmSnhnMHV1NU1nNENlY2QrTkVYU2xnZ1oiLCJtYWMiOiI4YjcyZmIyNmYwOGU3MTAyZWUwZmU5MGQzMzI5ZGJiMGYwNWE2NWI3NGE0Yzc3MjhjNGRkNjRkYWY3OGE2ODM2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJUSDZoOFN3ZzlyaUdpTlY2Rzg3blE9PSIsInZhbHVlIjoiNEM1MHlpeTNOMkNpS0QyL01YMm4ybUd4TTY2dTJxRXFua3JDWXRkejNFZGdmamEvVFZlclM4cFNDUk5hZlcrNDVlYmlMRVZ5UEVIdEZHc1gzQkJPcXJHMXdKRlZPQ3NSaWVOUHNXUmJlVE9mN2xLVnpaVGpxSnp4cXVsQkovc1ZoY1RNYnIzOHd4Yk9SS0RZU3I5K0hBQ2gvRmpHQ0lUajNxNEZUbUZJT0tjU3U4TE14MHdzeHZoSnduaytKNmZHYjF3ZTdQVHFBcXh2b1hlSEdtU0FDcURhWjB1YVRYU3NpNzE4L2RZdUZMa3V1SVo5VVR6U003dXVKZUZRLzhTUXkxamFKRnREcHdFNU9rSUhKYWRiVDNWUS9PNzJNd3dsMCtXeEpESHJWcVJSamJKUDVEOUhLT1NmbjhyNjFreTN2bksvbkJjbTlETnM1eUJIWW1yNnlJS29seUxKZ0MvckJjbkZMVXBNblhJeE83ak5rTUxtenkybWVjREF6K0dOMjk4d05sRnp3WkFvNTAvci9hdHgyUEZSRlN4OTdhc3MvL013SzdxbUgwZ2ZjL2FwVGNFcjVIblMvQWNHNHBnNCtSUjFocUlEb0RzdTlORDRUSkZiWlpuWlYwUlRRM3BJMFRwR21hZG1kZWI5M2FGYmpPd0YwSnp1b0lTbzR0Q0oiLCJtYWMiOiI4MzM4YTA3ZDBjOTY3YjViYTNiYTVjM2FhZGNmMGQ2ZDFiMTJlZWRkMGQwZjEzYTQ2NjUxYTY0YWFmMWM4MDU1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727632661\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1980884985 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980884985\", {\"maxDepth\":0})</script>\n"}}