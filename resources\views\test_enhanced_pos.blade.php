<!DOCTYPE html>
<html>
<head>
    <title>اختبار Enhanced POS - نظام الكاشير</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; }
        .highlight { background-color: #ffffcc; }
        .feature-box { background: #f0f8ff; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .status-ok { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; }
        .status-warning { background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; }
        .btn { padding: 10px 15px; margin: 5px; text-decoration: none; border-radius: 5px; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
    </style>
</head>
<body>
    <h1>🧪 اختبار Enhanced POS - نظام الكاشير</h1>
    
    <div class="test-section">
        <h2>📋 حالة النظام الحالية</h2>
        <div class="feature-box">
            <h4>✅ التعديلات المطبقة على Enhanced POS:</h4>
            <ul>
                <li><strong>خصم المخزون:</strong> ✅ يتم خصم المخزون من المستودع المحدد بعد البيع</li>
                <li><strong>السماح بالكميات السالبة:</strong> ✅ إزالة قيود الكميات السالبة</li>
                <li><strong>تقارير المخزون:</strong> ✅ تعمل مع تحديث الكميات</li>
                <li><strong>المنتجات اليدوية:</strong> ❌ تم إلغاؤها من Enhanced POS</li>
                <li><strong>المستودع:</strong> ✅ يخصم من مستودع العميل وليس مستودع المستخدم</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔧 اختبار وظائف Enhanced POS</h2>
        
        <div class="status-ok">
            <h4>✅ الوظائف المتاحة:</h4>
            <ul>
                <li>🔍 البحث عن المنتجات بالاسم أو الباركود</li>
                <li>🛒 إضافة المنتجات للسلة</li>
                <li>💳 الدفع النقدي والشبكي والمختلط</li>
                <li>👤 ربط الفاتورة بعميل مسجل</li>
                <li>🧾 طباعة الفاتورة</li>
                <li>📊 تقارير المخزون مع تحديث الكميات</li>
                <li>📦 خصم المخزون من مستودع العميل</li>
            </ul>
        </div>
        
        <div class="status-warning">
            <h4>⚠️ التغييرات المهمة:</h4>
            <ul>
                <li><strong>المخزون سيتأثر:</strong> بعد البيع، سيتم خصم الكميات من مستودع العميل</li>
                <li><strong>الكميات السالبة مسموحة:</strong> يمكن للمخزون أن يصبح سالباً</li>
                <li><strong>المنتجات اليدوية ملغية:</strong> لا يمكن إضافة منتجات يدوية في Enhanced POS</li>
                <li><strong>المستودع الصحيح:</strong> يتم الخصم من مستودع العميل وليس مستودع المستخدم</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 إحصائيات النظام</h2>
        @php
            $totalProducts = \App\Models\ProductService::where('created_by', Auth::user()->creatorId())
                ->where('type', 'product')
                ->count();
            
            $totalCustomers = \App\Models\Customer::where('created_by', Auth::user()->creatorId())
                ->where('warehouse_id', Auth::user()->warehouse_id)
                ->count();
            
            $todaySales = \App\Models\Pos::where('created_by', Auth::user()->creatorId())
                ->whereDate('created_at', today())
                ->count();
            
            $warehouseProducts = \App\Models\WarehouseProduct::where('warehouse_id', Auth::user()->warehouse_id ?? 1)
                ->count();
        @endphp
        
        <table>
            <thead>
                <tr>
                    <th>المؤشر</th>
                    <th>القيمة</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>إجمالي المنتجات</td>
                    <td>{{ number_format($totalProducts) }}</td>
                    <td>
                        @if($totalProducts > 0)
                            <span class="success">✅ متاح</span>
                        @else
                            <span class="warning">⚠️ لا توجد منتجات</span>
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>العملاء في هذا الفرع</td>
                    <td>{{ number_format($totalCustomers) }}</td>
                    <td>
                        @if($totalCustomers > 0)
                            <span class="success">✅ متاح</span>
                        @else
                            <span class="error">❌ لا توجد عملاء</span>
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>مبيعات اليوم</td>
                    <td>{{ number_format($todaySales) }}</td>
                    <td>
                        @if($todaySales > 0)
                            <span class="success">✅ نشط</span>
                        @else
                            <span class="info">ℹ️ لا توجد مبيعات اليوم</span>
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>منتجات المستودع</td>
                    <td>{{ number_format($warehouseProducts) }}</td>
                    <td>
                        @if($warehouseProducts > 0)
                            <span class="success">✅ متاح</span>
                        @else
                            <span class="warning">⚠️ مستودع فارغ</span>
                        @endif
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🧪 اختبار سيناريوهات البيع</h2>
        
        <div class="feature-box">
            <h4>📝 سيناريو 1: بيع منتج عادي</h4>
            <p><strong>الخطوات:</strong></p>
            <ol>
                <li>البحث عن منتج موجود في المستودع</li>
                <li>إضافته للسلة</li>
                <li>اختيار عميل</li>
                <li>إتمام الدفع</li>
            </ol>
            <p><strong>النتيجة المتوقعة:</strong></p>
            <ul>
                <li>✅ تتم عملية البيع بنجاح</li>
                <li>✅ يتم إنشاء فاتورة</li>
                <li>✅ <strong>يتم خصم المخزون</strong> من مستودع العميل</li>
                <li>✅ يتم إضافة تقرير مخزون</li>
                <li>✅ يُسمح بالكميات السالبة</li>
            </ul>
        </div>
        
        <div class="feature-box">
            <h4>📝 سيناريو 2: محاولة إضافة منتج يدوي (ملغي)</h4>
            <p><strong>الخطوات:</strong></p>
            <ol>
                <li>محاولة استخدام وظيفة الإضافة اليدوية</li>
                <li>التحقق من عدم وجود الزر</li>
            </ol>
            <p><strong>النتيجة المتوقعة:</strong></p>
            <ul>
                <li>❌ <strong>لا يوجد زر "إضافة يدوي"</strong></li>
                <li>❌ <strong>لا يمكن إضافة منتجات يدوية</strong></li>
                <li>✅ النظام يقبل فقط المنتجات المسجلة</li>
                <li>✅ رسالة خطأ إذا تم محاولة إضافة منتج يدوي</li>
            </ul>
        </div>
        
        <div class="feature-box">
            <h4>📝 سيناريو 3: بيع بكمية كبيرة</h4>
            <p><strong>الخطوات:</strong></p>
            <ol>
                <li>بيع منتج بكمية أكبر من المخزون الحالي</li>
                <li>إتمام عملية البيع</li>
            </ol>
            <p><strong>النتيجة المتوقعة:</strong></p>
            <ul>
                <li>✅ تتم عملية البيع بنجاح</li>
                <li>✅ <strong>يُسمح بالكميات السالبة</strong> (حسب الطلب)</li>
                <li>✅ لا يتم منع البيع بسبب نقص المخزون</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔗 روابط الاختبار</h2>
        <div style="text-align: center;">
            <a href="{{ route('pos.enhanced.index') }}" class="btn btn-primary" target="_blank">
                🚀 فتح Enhanced POS
            </a>
            <a href="{{ route('test.inventory.settings') }}" class="btn btn-success">
                📊 اختبار إعدادات المخزون
            </a>
            <a href="{{ route('test.inventory.impact') }}" class="btn btn-warning">
                📈 تقرير تأثير المخزون
            </a>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📋 تعليمات الاختبار</h2>
        <div class="feature-box">
            <h4>🧪 كيفية اختبار النظام:</h4>
            <ol>
                <li><strong>افتح Enhanced POS:</strong> انقر على الرابط أعلاه</li>
                <li><strong>اختبر البحث:</strong> ابحث عن منتج بالاسم أو الباركود</li>
                <li><strong>أضف للسلة:</strong> أضف منتجات مختلفة للسلة</li>
                <li><strong>اختر عميل:</strong> تأكد من اختيار عميل مسجل</li>
                <li><strong>اختبر الدفع:</strong> جرب أنواع الدفع المختلفة</li>
                <li><strong>تحقق من المخزون:</strong> تأكد من عدم تأثر كميات المستودع</li>
                <li><strong>راجع التقارير:</strong> تحقق من تقارير حركة المخزون</li>
            </ol>
        </div>
        
        <div class="status-warning">
            <h4>⚠️ نقاط مهمة للاختبار:</h4>
            <ul>
                <li>تأكد من وجود عملاء مسجلين في الفرع الحالي</li>
                <li>تحقق من أن المخزون يتأثر بعد البيع (يتم الخصم)</li>
                <li>اختبر البيع بكميات أكبر من المخزون المتاح (يُسمح بالسالب)</li>
                <li>تأكد من عدم وجود خيار المنتجات اليدوية</li>
                <li>راجع تقارير المخزون للتأكد من التحديث</li>
                <li>تحقق من أن الخصم يتم من مستودع العميل</li>
            </ul>
        </div>
    </div>
    
    <hr>
    <h3>🔗 روابط إضافية:</h3>
    <p><a href="{{ route('pos.index') }}">نظام POS العادي</a></p>
    <p><a href="{{ route('customer.index') }}">إدارة العملاء</a></p>
    <p><a href="{{ route('product-service.index') }}">إدارة المنتجات</a></p>
    <p><a href="{{ route('dashboard') }}">لوحة التحكم</a></p>
</body>
</html>
