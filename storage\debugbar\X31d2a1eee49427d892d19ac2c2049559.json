{"__meta": {"id": "X31d2a1eee49427d892d19ac2c2049559", "datetime": "2025-06-28 15:46:42", "utime": 1751125602.021877, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.559257, "end": 1751125602.021899, "duration": 0.46264195442199707, "duration_str": "463ms", "measures": [{"label": "Booting", "start": **********.559257, "relative_start": 0, "end": **********.896007, "relative_end": **********.896007, "duration": 0.3367500305175781, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.896017, "relative_start": 0.3367600440979004, "end": 1751125602.021901, "relative_end": 1.9073486328125e-06, "duration": 0.1258838176727295, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 62667136, "peak_usage_str": "60MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.06247, "accumulated_duration_str": "62.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9286149, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 3.218}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.940349, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 3.218, "width_percent": 1.377}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9441068, "duration": 0.0596, "duration_str": "59.6ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 4.594, "width_percent": 95.406}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-1219323487 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1219323487\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-452393253 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452393253\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1187376184 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1187376184\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1594096019 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125597951%7C21%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImgrZ2xod1h4VlZTWUFsNENnSm44aUE9PSIsInZhbHVlIjoiZEZ3VlpNUVFBNzc1emUrL3lCUTllYTRRTXBqNDRObEsva0ZuNEdXMjcxYk5lR3lsMDRpZmMyVXRiTitiQXZKZVh3ejU3L09xV3N4dTBiRzNzbklML2hYakxldE5UY095ZXJ2eFg2K1MzSWFBQWZLRXRTTVFMci9DN2NER3EvNTBVeHBEK0FQRWhpZk0xa3FEMmg0MTVHZ2dGdVB0MGU0ZVZVd0pCakszVlpSZXQwSnByaU9sYW1zYzhZYkd1YVZLUDloTjdaTTNGREZmQ3hGT2FPa0lSaDBlWE5qUVJ5SEd3aWwrUHoyYm55YlhXdEdJeVdpUTk0WjAvRnN2STQ1cXROYjJPL09TM0syU1VzWTloK0JXOG5nSm5Eb3RKSGorKzR5bDcyM3NDWlg0VjBGaVhVZXNSeGpIZ0tHSi93YWx5MmVyZW82Yi96UGlDNkU3RUlLZWNWVUNBZ0JTcE5DcTFwVVQ0QURlTVUrUzJER3I4UG9aMVl3MFpxQlI1TUhmL0IyUVcvRTB3TXZrSkJEQUcrL1JsWGNUcmk0K2VVNGFaaHVhelVvdHFpeGFzb2diZC8zOHl3Zk5jL3Z6RE9qOWRBKzd1RGNWL1lyckx0b0d0OW93blRtdmJYb2dxbUpjNDZRaytENTRTUjlROHhmY3NmaGE4M0hZOFI3VVBPZW0iLCJtYWMiOiJmNTI2NWU3ZTM3NTNhZTY1NWFkZDJhNTFjNzc4YzE5MmE2MzNiOTVlYmY3YTc5MzIxNjMwNzZkZDQ3MDM5YzllIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5uamovd0ZsbVUrQkVRTlVQRFJCcGc9PSIsInZhbHVlIjoiZ2MxZW8rRVY4RStpcEYraGFXWWRZcXJmdm5ZRjB2ejBZUmpWTzdqRCt2eUpmOVpCbFlVeUY0cWJnRTBYczIwb2thek4yVjFheURYMUVZVm9rc3hhR3BQaDVKZURZNzNKVWdEY1lkMEFSWG5pZ1V5VWpGUWNlaFR0L1ZzMlRGRzlSNFJ3VVkwTE5sbDYvR2ttN1AvNjdTWndXdTNqeEZiSHEyM3ViSEEyaC80NS9VMWE0Y3QrSklkTkhBL2k5d1RtYXBZamNFY0NlUWpQcC9nSTI1YVFvcGc4M2dmU0Q0YzhuZWhmVmI1V3NBcDJmRXBpNW91RDVLN3I4MG9SSlQ4K1dHR1ZQRGdUak5pVFEra2QyTTNyYjZESWNFY21CaktDK3RwNE5OblJGK0VEUFRaeXdyNlFWMjBYMnB0eXZicnpDMDZhazdDWEtWVW9SZ0NySlUyV1Vpa2JWVlJFa3drRGhJOFp6UG5NZVI3dkk2UEh4N0xhb2pkR2lDejd6R1pzZEZ5OHlCdUM2MmFENjBrSnZ4NDRlcmFhVFB1OC9PT2lncU5BMitxK0JCRjJ3VEJRWUprQm9HUEZPYWNZclU2QThiSHFWdWNsL3hzMUZYcjF3eko5QmJsN01oRTRzay80RXBVd01wUEo0NXBOWDBNWlQ0ZVB2ZHVsaEl4emNlZVIiLCJtYWMiOiI5N2EyM2YyYzZlMjdjNTI0ZGFjN2NhNTVkNjEwZWQ4MGI2MmExN2ZmNWEzNGY3MGFiNzFlZGE4NDViNmZjYjhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594096019\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1716033061 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716033061\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:46:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFMajFMWXRDYjBkQkF0M2VLaUVocVE9PSIsInZhbHVlIjoiNW1YM3ZGdGlKM2JoeG04SDk2NkorYUlwZXFUT0Nja1Q2K3VjNVhWWmdLaTlTNU9sek00aXVQanRVM2RPTmJmY2xSczk3c0phWUs4akliS21DRksrUzdGbjNWM2djUHZiVm8yZkdRNWhjN0JnaUpBdnlGdzRGYWpOOTdXQVEyYytzcGZubEE0Z0xZZTVIc2Y5cURmYzVRaTNqaDFqMk5wMUMxc3BVY3M5cDhOQmNNMThaUVlWQkM2dC9MOE9pd1p1aWo4RlNQcG5RRHZMQlg3c2xMQnBsWEZFOUdxblI5Vmw1RzRNT1l1ZS9ydEwvRzF4R2xIeE80bEVqMEdWajJBSXczUVZjK3ZSR0JsU2podUk1R1BPT3RvbkVRbWFzMkpDOFlGcktiaVBJY082WTlJbG9aeXZmSHhqcHdXNisxZ0Nac3BBZ1ZJb3ZTOFk4dDl1S3o1Sm4yYUF3Qk5FTTEySXprRWtjdWlZSDAxWkVWZEZhSmgrTWVpY0xkYlhlZ3ZrZFFBWjZZdUVuQlpDK0E1eE5kQ2NjNytlMGo5UVBmWlFsT1A0THIvU24yRTB5NXhJSUVmcFp6ZDh2SGczTklyTWJmdUhkTnBaYlpsM0lvZEdJYTNFcXJMNXlTUVNHRk03bW13ODFSMzUyaWZYSUJDZlh5RHI3RnV6dExqa080dGwiLCJtYWMiOiIwZTc1Zjc1MmM4ZjM0YmViYTY1MzJkNThmMjc2ODQ4YWJjZjkwMjI3NmU0OTU3YWNjODM0NDEzOWJkN2MyOGY1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktyeFlHNXRnNmdSUEtWZnI5eFpGOEE9PSIsInZhbHVlIjoiZ2VzUUVFVDk3cDMySkNqankzVStmY2FCaUp4OURRODhtaDRzTkx0cHVyUERFY2lDbExqMXZ6cURFQ0hMdW5WRVNXd3ZBOTBwajU2WStFNCt4Q20zUjZFMHBScXgveVVZZkl5bElaOHdVWGg0cU44TXJyUzg1NGszNmVFeGwzQndHMFU0VE9BZWQ1U01yWjZPV1NVWUlBYy9RaTdIck4xRVlIOXNOQ1p3MlY0NXN4blJqWlB4SEFNczJhMEg4N3hZOHVCb2UxUDNkTzVWSlN3U2JsRXBzOUwyMUsxZkNwaEVjVVY2QjgyWVRmR1pVeWZ2S3pOLzViai9BSUFEOW9MWHhodldDOURjZi9Dd21ncTBJcE5CVC9FR0hENE9EMGV2cGRoc1BETE5KRVZMTGFHbXk5NDhkb1g5S2NPUWNqei9qNHdQeXFaM1ppYnBwdE1NY3pBTlBRQWRaV1laWS9xcGwyRk9BV0pnTkhlNUZ4SjUrMmgvZ1dHUzJxb1ROZlhVRkUzM1liQUwyQmlCVmtmWXpLSVpnRlJTY1djZDRNWEd1WEkzTlZXalRacjZxYjJDTDRHUE9LNE9jL1ZPSjczMFRSV2hNYjh4REhrRVowNk9weXp2YmgyV29ERzlybFVZd3lGaFJKZDFnVDA4UDI5RWtDUmdwbUdwYUt1RXY2OU8iLCJtYWMiOiI5ZjQ1MTc3NjA0MzhiNDUyY2Q4MzUwMTI1ZDBkZWE4OWQ0ZDkyYmRkM2U5NWUyMDU0YTAwYWI0MmMxYjdlNTYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFMajFMWXRDYjBkQkF0M2VLaUVocVE9PSIsInZhbHVlIjoiNW1YM3ZGdGlKM2JoeG04SDk2NkorYUlwZXFUT0Nja1Q2K3VjNVhWWmdLaTlTNU9sek00aXVQanRVM2RPTmJmY2xSczk3c0phWUs4akliS21DRksrUzdGbjNWM2djUHZiVm8yZkdRNWhjN0JnaUpBdnlGdzRGYWpOOTdXQVEyYytzcGZubEE0Z0xZZTVIc2Y5cURmYzVRaTNqaDFqMk5wMUMxc3BVY3M5cDhOQmNNMThaUVlWQkM2dC9MOE9pd1p1aWo4RlNQcG5RRHZMQlg3c2xMQnBsWEZFOUdxblI5Vmw1RzRNT1l1ZS9ydEwvRzF4R2xIeE80bEVqMEdWajJBSXczUVZjK3ZSR0JsU2podUk1R1BPT3RvbkVRbWFzMkpDOFlGcktiaVBJY082WTlJbG9aeXZmSHhqcHdXNisxZ0Nac3BBZ1ZJb3ZTOFk4dDl1S3o1Sm4yYUF3Qk5FTTEySXprRWtjdWlZSDAxWkVWZEZhSmgrTWVpY0xkYlhlZ3ZrZFFBWjZZdUVuQlpDK0E1eE5kQ2NjNytlMGo5UVBmWlFsT1A0THIvU24yRTB5NXhJSUVmcFp6ZDh2SGczTklyTWJmdUhkTnBaYlpsM0lvZEdJYTNFcXJMNXlTUVNHRk03bW13ODFSMzUyaWZYSUJDZlh5RHI3RnV6dExqa080dGwiLCJtYWMiOiIwZTc1Zjc1MmM4ZjM0YmViYTY1MzJkNThmMjc2ODQ4YWJjZjkwMjI3NmU0OTU3YWNjODM0NDEzOWJkN2MyOGY1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktyeFlHNXRnNmdSUEtWZnI5eFpGOEE9PSIsInZhbHVlIjoiZ2VzUUVFVDk3cDMySkNqankzVStmY2FCaUp4OURRODhtaDRzTkx0cHVyUERFY2lDbExqMXZ6cURFQ0hMdW5WRVNXd3ZBOTBwajU2WStFNCt4Q20zUjZFMHBScXgveVVZZkl5bElaOHdVWGg0cU44TXJyUzg1NGszNmVFeGwzQndHMFU0VE9BZWQ1U01yWjZPV1NVWUlBYy9RaTdIck4xRVlIOXNOQ1p3MlY0NXN4blJqWlB4SEFNczJhMEg4N3hZOHVCb2UxUDNkTzVWSlN3U2JsRXBzOUwyMUsxZkNwaEVjVVY2QjgyWVRmR1pVeWZ2S3pOLzViai9BSUFEOW9MWHhodldDOURjZi9Dd21ncTBJcE5CVC9FR0hENE9EMGV2cGRoc1BETE5KRVZMTGFHbXk5NDhkb1g5S2NPUWNqei9qNHdQeXFaM1ppYnBwdE1NY3pBTlBRQWRaV1laWS9xcGwyRk9BV0pnTkhlNUZ4SjUrMmgvZ1dHUzJxb1ROZlhVRkUzM1liQUwyQmlCVmtmWXpLSVpnRlJTY1djZDRNWEd1WEkzTlZXalRacjZxYjJDTDRHUE9LNE9jL1ZPSjczMFRSV2hNYjh4REhrRVowNk9weXp2YmgyV29ERzlybFVZd3lGaFJKZDFnVDA4UDI5RWtDUmdwbUdwYUt1RXY2OU8iLCJtYWMiOiI5ZjQ1MTc3NjA0MzhiNDUyY2Q4MzUwMTI1ZDBkZWE4OWQ0ZDkyYmRkM2U5NWUyMDU0YTAwYWI0MmMxYjdlNTYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}