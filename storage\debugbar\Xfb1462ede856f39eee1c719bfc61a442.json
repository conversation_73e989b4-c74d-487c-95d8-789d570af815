{"__meta": {"id": "Xfb1462ede856f39eee1c719bfc61a442", "datetime": "2025-06-28 16:34:57", "utime": **********.439135, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128496.977518, "end": **********.43915, "duration": 0.46163201332092285, "duration_str": "462ms", "measures": [{"label": "Booting", "start": 1751128496.977518, "relative_start": 0, "end": **********.386542, "relative_end": **********.386542, "duration": 0.4090240001678467, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.386554, "relative_start": 0.40903592109680176, "end": **********.439152, "relative_end": 1.9073486328125e-06, "duration": 0.052597999572753906, "duration_str": "52.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45715000, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00281, "accumulated_duration_str": "2.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.414324, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.057}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4249961, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.057, "width_percent": 17.438}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.430457, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.495, "width_percent": 18.505}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-79687150 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-79687150\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1414056737 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1414056737\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1008388502 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008388502\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1733914046 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128494675%7C53%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVHNVpUV3N2bHNiNFVNbk43d0hmY3c9PSIsInZhbHVlIjoieGpXZDN1dTMzcU9VK1pBU1U0N3pMeTRURTVoYkVQUU5IMnhRc0lWWGZYdG5lVWhqa0o4MzZHV2daSWZtODk0S0txMllEeHV0cEREN1E0SlJVUFYzczUrREVjTWVtbWpzSUZFclIxeGVaN0RFQjd0VE81MFUzOUcrUUUwUWEvWFVPSUNYRFpVNFpvcU14eGpQcnJYTTVjUVdnaGxuT25JS2RzY1FMWEVTUUVVQ0hiQ1NYUlI1S2V5WVNVZWk5WDd2dTR4RGpWWmFZVGh1M1pLZktXNUdLSWVTZ05PT0xSL3I5MnRLY2NyRXFKbzJEZXFKVFZOU25adzlaK0E5US81T0tqcVZGWVRya1ZPVk9IZGc0cUtqZmVJZjJQS1VHcisxS05oRy9NOTl6RWpMbjMzSUhZSGZjSmNaK1o1N1hwRWZFN1g5QitKT0Y0NHpPSGpCaGxDQi9nRnpsK0Q1cWd1M05KQU94NlU2Y2Fud1kraUI2Nkl0MHFwUTNqVEtpa3BHT1JZUFRQMlRKczdSL29mdDMxcGtlcHdmSnBjQ0lnMjNuRXVZakZaUDVRVVpsR0Nza2psL1IwUGNnUVZSOVNPU2NQRDdKRmMyQ0JnYU5RYmt4VCttUEkrSjBiNFRvaC9vQ3Vac0FJSGE0NjlPeFJkTFl2Q04wS0RQTW8xb1YyR0giLCJtYWMiOiIwYjk2NjA3YmZlZGQxNTZiNjYxMDUxYmQ5MDg4ZDJjNDI4MmVlODQyNDAwMzFkZWRmNzA3YTk3ZmVkNDgyNDFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFNNTRScFIwSktaQTlsTGp2YWtoNGc9PSIsInZhbHVlIjoid1Z5ZnlhOE8zdlV2cjBkOUFGR3Q5TFQ4bmY3LzVQa0x6YVRlbEQ1aURoR1NTbjJXR1ZRMnMzZ0VoZ05EU1MzK0tkZlhtY0JPeFBReUY5YmIyeENXa1JOblVIY0NwNk9BTWIyQktJcFdxazI5dDFBc25TZFRqbXVQQnA5anJOMnM4YlBhbEFMLzBVRHJPaitKVTVKMzhqUTFQQkhaZlRDc2o5c0poRHJra2s2RitRZHJ4ZDdKeDVOZDBmQi9PcnlhTWtYSVV0cWltd25tUlFwZTBTWTVKNHZhVklraE9nYmxQa2RiNHM1dFVVWDI3V0NwNFpsYjhkcjVOcGFScXp5NTBKdEJEbkNtNXU1N0RDb0RBaFdFWDhqTHFJanNmdUoyT0c1Tk1xeFBkYXhDSHYzQno0YkJhdHJFMU5zRytJWFJZM1pUam83UXhPOExIMjJadWFyeXQxamV1MXBlRlVhSmxCa0ZQOE8xZkpnM1dvSXpsNzVvUVFtVnZWWXlFYTNPb1d3bzVTZmMrcjBpMlRvb21GbEhVcVVlUS9xRHlEdlh5Sm1tWnVXL2Y0cS8rR3BpbyszVWRFd3k0dmZCY0Fnb2xPeXd3cmllZGhBOWdBdnRYdW5NZE9KRTIycU51N2xROWtMZUo3S2ljcmR0TDk0VVlmMTdaTGUzSnRrdDdKVlciLCJtYWMiOiJhMGE0NWFmMTcwNTBiZmM1OTZkYjMxZTUwZDJiYjdmZjAwYzZmZDg3MDAwYjRlNmEyMGExM2JlM2FkZjM1N2M1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733914046\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-538394567 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538394567\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRKUnpHd0VucjVJOXdJSlhiSWhqV1E9PSIsInZhbHVlIjoiTldxdkNMTE1xSFRGWk55QTBYNVFoSXR6Z091K1dSeURDQ3ErZ1NJZUtBOEJUWUF0dUV1RVIvZk9wWW5vL0NPOHRiZUNrS1pCcWFWblRabWdHdzVWaUVRVTFXYmd2Z1JSK2lOYlg2VjVhc3g1MmF6Tzc3VEJlazhCRm9oazBCZVNxMS9tZy9TbERTaTZTYmFWOFpRa0VxYUZiYnorK2ZJblVsTnNxaXdqS0VINGQrdmZDN0pTK1JFOXlRQ1hINGVqbS85YWxRQVkrKzBQcnZtUnJMdjJIQ3lSVytQV2o1RkNVMS82VksvaHI3VW9oZ3h5NmF2SjRpM2tReXpZbnQwUzlhS3JMSlJnY0tkYlBYY1dzeE1TWmpXZDJtbVJWUFZ3T242UEF2RlBEaVRlWEJaZHV6Mmc4TExHY3Fud2JxYW8zWDBOOU9tM25JZDZtOFBVYWZuT0dTV0lHYWpaTUhkaE8xd2dqTkFFM2dDQ1NaQ1NUYkNNTzQzazBWMVFNRkdnbFZRRnRERWx4UUNwRnFXRTR0RVJHdUtoMGZpVVZTdkhNZmhwU0VObExPWm13R2JqQmZIS0F3eU80UDlOVmhmKytIbjZpc2pJUEJWU3lPaFF6QUg5anNURjRTeXRxZVgvYzBsdEJjUXcvcDJXMS9uelNWakhHcDJVdGovRVFpRGciLCJtYWMiOiI2OGZlNDUxNDgwNzZjMWEwMWZiZDA0ZTJkY2JlNTY3YTM3MDNmMzYwYjY1ZTdhMzEzOTdkOTk4ZDYwNTJiYmE2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InlwMmNWUkkxanVOcFkzNXpBcENEZFE9PSIsInZhbHVlIjoiYzFZUnJXSmYwL3JXZnk2NWFCZEdZbnZTYkdoQTcyT08rUHhnbEdUZGpFRm5mL2ZGb1o5TFR0ellTTithRFJUVWJsTDd2Y0NZdnZoSGxscUlad0VYRitFSC9WTE4yNUZyN2ZRcjdNSStGQWxYYy9HYTJldW1RT25QaTRCcTd5RURHV01XU3FJSW5QWUhCY3NzQXAwbFdTRzZkcVRZc0hwcjlBVGF3OXFmVy9STk5mSmFyNjBKODRhSUlpUTh6SVpzeVA1WjF6STUwTHhINFpzcTFrVXBXa1paUk1tbnBJUzIrL0huanFudURDaDEvb1VRZ3ZhOE5DVTBwVTNmTFlTaUNwdC9LdVZhOTBIdkdpb2E1REpnUjhrVlpWS1AxQThROW1jenlsdkRPVFNPRnFBdlEybkIzMS9QUklna3F6OWluWFd4N3AybnIyQWpJRUtBWWhmSURaMnFPdFFoa1l4S1ovQnNoSjhXeTg1aGFibnVkWnV3SlU1ckdudzhmTHRDaVVXcUxVS25jWTVwa1RqVWhDSFhOa25BZENWTUZ5ZmdINllCclYwd3l5TUhkMUs3RStTQnF2eVVMUWMyb3RrWDV5N0VLbWhSaXFNNVBxU09Tb25zaHZmSUlacHFmTmxOUmF4VWdLOUlwbm05TEpjWTJqcWpXT0MvV3Q5cVFHaE8iLCJtYWMiOiI3YmQ1N2RkYjk3NjI1ZTBiZGI5NzllNTg4MDA3ZDBiODNmZTgzNTI3NWM3YmIxNDZkNzRkMTBmMWVjOTI1MmM5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRKUnpHd0VucjVJOXdJSlhiSWhqV1E9PSIsInZhbHVlIjoiTldxdkNMTE1xSFRGWk55QTBYNVFoSXR6Z091K1dSeURDQ3ErZ1NJZUtBOEJUWUF0dUV1RVIvZk9wWW5vL0NPOHRiZUNrS1pCcWFWblRabWdHdzVWaUVRVTFXYmd2Z1JSK2lOYlg2VjVhc3g1MmF6Tzc3VEJlazhCRm9oazBCZVNxMS9tZy9TbERTaTZTYmFWOFpRa0VxYUZiYnorK2ZJblVsTnNxaXdqS0VINGQrdmZDN0pTK1JFOXlRQ1hINGVqbS85YWxRQVkrKzBQcnZtUnJMdjJIQ3lSVytQV2o1RkNVMS82VksvaHI3VW9oZ3h5NmF2SjRpM2tReXpZbnQwUzlhS3JMSlJnY0tkYlBYY1dzeE1TWmpXZDJtbVJWUFZ3T242UEF2RlBEaVRlWEJaZHV6Mmc4TExHY3Fud2JxYW8zWDBOOU9tM25JZDZtOFBVYWZuT0dTV0lHYWpaTUhkaE8xd2dqTkFFM2dDQ1NaQ1NUYkNNTzQzazBWMVFNRkdnbFZRRnRERWx4UUNwRnFXRTR0RVJHdUtoMGZpVVZTdkhNZmhwU0VObExPWm13R2JqQmZIS0F3eU80UDlOVmhmKytIbjZpc2pJUEJWU3lPaFF6QUg5anNURjRTeXRxZVgvYzBsdEJjUXcvcDJXMS9uelNWakhHcDJVdGovRVFpRGciLCJtYWMiOiI2OGZlNDUxNDgwNzZjMWEwMWZiZDA0ZTJkY2JlNTY3YTM3MDNmMzYwYjY1ZTdhMzEzOTdkOTk4ZDYwNTJiYmE2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InlwMmNWUkkxanVOcFkzNXpBcENEZFE9PSIsInZhbHVlIjoiYzFZUnJXSmYwL3JXZnk2NWFCZEdZbnZTYkdoQTcyT08rUHhnbEdUZGpFRm5mL2ZGb1o5TFR0ellTTithRFJUVWJsTDd2Y0NZdnZoSGxscUlad0VYRitFSC9WTE4yNUZyN2ZRcjdNSStGQWxYYy9HYTJldW1RT25QaTRCcTd5RURHV01XU3FJSW5QWUhCY3NzQXAwbFdTRzZkcVRZc0hwcjlBVGF3OXFmVy9STk5mSmFyNjBKODRhSUlpUTh6SVpzeVA1WjF6STUwTHhINFpzcTFrVXBXa1paUk1tbnBJUzIrL0huanFudURDaDEvb1VRZ3ZhOE5DVTBwVTNmTFlTaUNwdC9LdVZhOTBIdkdpb2E1REpnUjhrVlpWS1AxQThROW1jenlsdkRPVFNPRnFBdlEybkIzMS9QUklna3F6OWluWFd4N3AybnIyQWpJRUtBWWhmSURaMnFPdFFoa1l4S1ovQnNoSjhXeTg1aGFibnVkWnV3SlU1ckdudzhmTHRDaVVXcUxVS25jWTVwa1RqVWhDSFhOa25BZENWTUZ5ZmdINllCclYwd3l5TUhkMUs3RStTQnF2eVVMUWMyb3RrWDV5N0VLbWhSaXFNNVBxU09Tb25zaHZmSUlacHFmTmxOUmF4VWdLOUlwbm05TEpjWTJqcWpXT0MvV3Q5cVFHaE8iLCJtYWMiOiI3YmQ1N2RkYjk3NjI1ZTBiZGI5NzllNTg4MDA3ZDBiODNmZTgzNTI3NWM3YmIxNDZkNzRkMTBmMWVjOTI1MmM5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}