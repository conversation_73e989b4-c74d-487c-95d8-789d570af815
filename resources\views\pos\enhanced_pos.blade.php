@extends('layouts.admin')

@section('page-title')
    {{ __('Cashier System') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Cashier') }}</li>
@endsection

@push('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

@push('css-page')
    <style>
        .pos-container {
            background: #f8fafc;
            min-height: 100vh;
            padding: 20px;
        }
        
        .warehouse-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .warehouse-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .warehouse-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .warehouse-details {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .search-section {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }
        
        .search-input {
            font-size: 1.2rem;
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }
        
        .search-buttons {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn-search {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-search:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .product-section {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        
        .product-header {
            background: #f7fafc;
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            text-align: center;
        }
        
        .product-display {
            padding: 30px;
            text-align: center;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .product-card {
            background: #f8fafc;
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 40px 20px;
            margin-bottom: 20px;
        }
        
        .product-found {
            background: #f0fff4;
            border: 2px solid #68d391;
        }
        
        .product-name {
            font-size: 1.4rem;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .product-price {
            font-size: 1.2rem;
            color: #38a169;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .product-stock {
            color: #718096;
            margin-bottom: 20px;
        }
        
        .quantity-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .quantity-btn {
            width: 40px;
            height: 40px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 50%;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .quantity-btn:hover {
            background: #667eea;
            color: white;
        }
        
        .quantity-input {
            width: 80px;
            text-align: center;
            font-size: 1.1rem;
            font-weight: bold;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 8px;
        }
        
        .add-to-cart-btn {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .add-to-cart-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
        }
        
        /* Manual entry styles removed */
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #4a5568;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            border-color: #667eea;
            outline: none;
        }
        
        .cart-sidebar {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            height: fit-content;
            position: sticky;
            top: 20px;
        }
        
        .cart-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        
        .cart-content {
            padding: 20px;
        }
        
        .cart-item {
            border-bottom: 1px solid #e2e8f0;
            padding: 15px 0;
        }
        
        .cart-item:last-child {
            border-bottom: none;
        }
        
        .cart-item-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .cart-item-details {
            color: #718096;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .cart-item-actions {
            display: flex;
            gap: 10px;
        }
        
        .cart-summary {
            border-top: 2px solid #e2e8f0;
            padding-top: 20px;
            margin-top: 20px;
        }
        
        .summary-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .summary-total {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2d3748;
            border-top: 1px solid #e2e8f0;
            padding-top: 10px;
        }
        
        .payment-buttons {
            margin-top: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .payment-btn {
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-cash {
            background: #48bb78;
            color: white;
        }
        
        .payment-network {
            background: #4299e1;
            color: white;
        }
        
        .payment-mixed {
            background: #ed8936;
            color: white;
        }
        
        .action-buttons {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn-action {
            flex: 1;
            padding: 10px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-action:hover {
            border-color: #667eea;
            color: #667eea;
        }

        .btn-disabled {
            opacity: 0.5;
            cursor: not-allowed !important;
        }

        .btn-disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }
        
        @media (max-width: 768px) {
            .warehouse-info {
                flex-direction: column;
                text-align: center;
            }
            
            .search-buttons {
                justify-content: center;
            }
            
            .quantity-controls {
                flex-wrap: wrap;
            }
        }
    </style>
@endpush

@section('content')
<div class="pos-container">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Warehouse Header -->
            <div class="warehouse-header">
                <div class="warehouse-info">
                    <div>
                        <div class="warehouse-name">
                            🏪 {{ $warehouse->name ?? 'المستودع الرئيسي' }}
                        </div>
                        <div class="warehouse-details">
                            👤 {{ Auth::user()->name }} | 📅 {{ date('Y-m-d') }} | 🕐 الشفت مفتوح منذ {{ $shift_time ?? '08:30' }}
                        </div>
                    </div>
                    <div class="text-end">
                        <div style="font-size: 1.1rem;">🏪 الفرع الحالي</div>
                        <div style="font-size: 1.1rem; font-weight: bold; margin-bottom: 10px;">{{ $warehouse->name ?? 'غير محدد' }}</div>
                        <div style="font-size: 1.1rem;">💰 رصيد الشفت</div>
                        <div style="font-size: 1.3rem; font-weight: bold;">{{ $shift_balance ?? '15,250' }} ريال</div>
                    </div>
                </div>
            </div>

            <!-- Search Section -->
            <div class="search-section">
                <div class="row">
                    <div class="col-12">
                        <input type="text" 
                               class="search-input" 
                               id="productSearch"
                               placeholder="🔍 البحث عن المنتج بالاسم أو الباركود..."
                               autocomplete="off">
                    </div>
                </div>
                <div class="search-buttons">
                    <button class="btn-search" id="barcodeBtn">
                        📷 مسح الباركود
                    </button>
                    <button class="btn-search" id="clearBtn">
                        🗑️ مسح البحث
                    </button>
                </div>
            </div>

            <!-- Product Display Section -->
            <div class="product-section">
                <div class="product-header">
                    <h4>📦 إضافة منتج مباشرة</h4>
                </div>
                
                <div class="product-display" id="productDisplay">
                    <div class="product-card" id="emptyState">
                        <div style="font-size: 3rem; margin-bottom: 20px;">🔍</div>
                        <h5>ابحث عن منتج لإضافته</h5>
                        <p class="text-muted">استخدم مربع البحث أعلاه للعثور على المنتج المطلوب</p>
                    </div>
                    
                    <!-- Product Found State (Hidden by default) -->
                    <div class="product-card product-found d-none" id="productFound">
                        <div class="product-name" id="productName">اسم المنتج</div>
                        <div class="product-price" id="productPrice">💰 0 ريال</div>
                        <div class="product-stock" id="productStock">📦 المخزون المتاح: 0 قطعة</div>
                        
                        <div class="quantity-controls">
                            <button class="quantity-btn" id="decreaseQty">-</button>
                            <input type="number" class="quantity-input" id="quantity" value="1" min="1">
                            <button class="quantity-btn" id="increaseQty">+</button>
                        </div>
                        
                        <button class="add-to-cart-btn" id="addToCartBtn">
                            🛒 إضافة للسلة
                        </button>
                    </div>
                </div>

                <!-- Manual Entry Section Removed -->
            </div>
        </div>

        <!-- Cart Sidebar -->
        <div class="col-lg-4">
            <div class="cart-sidebar">
                <div class="cart-header">
                    <h5>🛒 فاتورة رقم: #<span id="invoiceNumber">2025001</span></h5>
                    <div style="margin-top: 10px;">
                        <select class="form-select" id="customerSelect" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white;" required>
                            @if(isset($customers) && count($customers) > 0)
                                <option value="">👤 اختر العميل (إجباري)</option>
                                @foreach($customers as $customer)
                                    <option value="{{ $customer->id }}"
                                            data-warehouse="{{ $customer->warehouse_id ?? 'عام' }}"
                                            title="المستودع: {{ $customer->warehouse->name ?? 'عام' }}">
                                        {{ $customer->name }}
                                        @if($customer->warehouse_id)
                                            ({{ $customer->warehouse->name }})
                                        @else
                                            (عام)
                                        @endif
                                    </option>
                                @endforeach
                            @else
                                <option value="">⚠️ لا يوجد عملاء مسجلين في هذا الفرع</option>
                            @endif
                        </select>
                        <small style="color: rgba(255,255,255,0.7); font-size: 0.8rem; margin-top: 5px; display: block;">
                            <i class="ti ti-info-circle"></i>
                            عرض عملاء الفرع الحالي فقط: {{ $warehouse->name ?? 'غير محدد' }}
                            <span id="customerCount">({{ count($customers ?? []) }} عميل متاح)</span>
                        </small>
                    </div>
                </div>
                
                <div class="cart-content">
                    <!-- Cart Items -->
                    <div id="cartItems">
                        <div class="text-center text-muted py-4">
                            <div style="font-size: 2rem;">🛒</div>
                            <p>السلة فارغة</p>
                        </div>
                    </div>
                    
                    <!-- Cart Summary -->
                    <div class="cart-summary d-none" id="cartSummary">
                        <div class="summary-line">
                            <span>المجموع الفرعي:</span>
                            <span id="subtotal">0 ريال</span>
                        </div>
                        <div class="summary-line">
                            <span>الضريبة (15%):</span>
                            <span id="tax">0 ريال</span>
                        </div>
                        <div class="summary-line">
                            <span>الخصم:</span>
                            <span id="discount">0 ريال</span>
                        </div>
                        <div class="summary-line summary-total">
                            <span>🏷️ الإجمالي:</span>
                            <span id="total">0 ريال</span>
                        </div>
                    </div>
                    
                    <!-- Customer Validation Message -->
                    <div class="alert alert-warning d-none" id="customerWarning" style="margin-top: 15px;">
                        <i class="ti ti-alert-triangle"></i>
                        <strong>تنبيه:</strong> يجب اختيار عميل مسجل في الفرع الحالي قبل إتمام عملية الدفع
                    </div>

                    <!-- Payment Buttons -->
                    <div class="payment-buttons d-none" id="paymentButtons">
                        <button class="payment-btn payment-cash" id="payCash">
                            💳 دفع نقدي
                        </button>
                        <button class="payment-btn payment-network" id="payNetwork">
                            💳 دفع شبكة
                        </button>
                        <button class="payment-btn payment-mixed" id="payMixed">
                            💳 دفع مختلط
                        </button>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="action-buttons d-none" id="actionButtons">
                        <button class="btn-action" id="invoicesBtn">
                            📋 الفواتير
                        </button>
                        <button class="btn-action" id="saveBtn">
                            💾 حفظ
                        </button>
                        <button class="btn-action" id="clearCartBtn">
                            🗑️ إلغاء
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cash Payment Modal -->
<div class="modal fade" id="cashPaymentModal" tabindex="-1" aria-labelledby="cashPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="cashPaymentModalLabel">
                    <i class="ti ti-cash"></i> الدفع النقدي
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">تفاصيل الفاتورة</h6>
                                <div class="mb-2">
                                    <strong>العميل:</strong> <span id="modalCustomerName">-</span>
                                </div>
                                <div class="mb-2">
                                    <strong>عدد الأصناف:</strong> <span id="modalItemsCount">0</span>
                                </div>
                                <div class="mb-2">
                                    <strong>المجموع الفرعي:</strong> <span id="modalSubtotal">0.00</span> ريال
                                </div>
                                <div class="mb-2">
                                    <strong>الضريبة:</strong> <span id="modalTax">0.00</span> ريال
                                </div>
                                <div class="mb-2 border-top pt-2">
                                    <strong style="font-size: 1.2em;">الإجمالي:</strong>
                                    <span id="modalTotal" style="font-size: 1.2em; color: #28a745;">0.00</span> ريال
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="cashReceived" class="form-label">
                                <strong>المبلغ المستلم من العميل</strong>
                            </label>
                            <input type="number" class="form-control form-control-lg" id="cashReceived"
                                   placeholder="أدخل المبلغ المستلم" step="0.01" min="0">
                            <div class="invalid-feedback" id="cashReceivedError"></div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="changeAmount" class="form-label">
                                <strong>المبلغ المرتجع (الباقي)</strong>
                            </label>
                            <input type="text" class="form-control form-control-lg" id="changeAmount"
                                   readonly style="background-color: #f8f9fa; font-weight: bold;">
                        </div>

                        <div class="alert alert-info">
                            <i class="ti ti-info-circle"></i>
                            <strong>ملاحظة:</strong> تأكد من إدخال المبلغ المستلم بدقة لحساب الباقي الصحيح.
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x"></i> إلغاء
                </button>
                <button type="button" class="btn btn-success btn-lg" id="confirmCashPayment">
                    <i class="ti ti-check"></i> تأكيد الدفع
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Invoices Modal -->
<div class="modal fade" id="invoicesModal" tabindex="-1" aria-labelledby="invoicesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="invoicesModalLabel">
                    <i class="ti ti-receipt"></i> الفواتير المباعة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Search and Filter -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="invoiceSearch" placeholder="البحث برقم الفاتورة أو العميل...">
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="invoiceDateFrom" placeholder="من تاريخ">
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="invoiceDateTo" placeholder="إلى تاريخ">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary w-100" id="searchInvoicesBtn">
                            <i class="ti ti-search"></i> بحث
                        </button>
                    </div>
                </div>

                <!-- Invoices Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>العميل</th>
                                <th>المجموع</th>
                                <th>نوع الدفع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="invoicesTableBody">
                            <tr>
                                <td colspan="7" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <small class="text-muted">عرض <span id="invoicesCount">0</span> فاتورة</small>
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0" id="invoicesPagination">
                            <!-- Pagination will be generated here -->
                        </ul>
                    </nav>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x"></i> إغلاق
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script-page')
<script>
    // Cart data
    let cart = [];
    let currentProduct = null;

    // CSRF Token setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize
    $(document).ready(function() {
        initializeEventListeners();
        loadCartFromServer();
    });
    
    function initializeEventListeners() {
        // Search functionality
        $('#productSearch').on('input', handleProductSearch);
        
        // Quantity controls
        $('#increaseQty').on('click', () => updateQuantity(1));
        $('#decreaseQty').on('click', () => updateQuantity(-1));
        $('#quantity').on('change', validateQuantity);
        
        // Add to cart
        $('#addToCartBtn').on('click', addToCart);
        
        // Clear functions
        $('#clearBtn').on('click', clearSearch);
        $('#clearCartBtn').on('click', clearCart);

        // Invoices functionality
        $('#invoicesBtn').on('click', openInvoicesModal);
        $('#searchInvoicesBtn').on('click', searchInvoices);
        
        // Payment buttons
        $('#payCash').on('click', () => openCashPaymentModal());
        $('#payNetwork').on('click', () => processPayment('network'));
        $('#payMixed').on('click', () => processPayment('mixed'));

        // Customer selection change
        $('#customerSelect').on('change', checkCustomerSelection);

        // Cash payment modal events
        $('#cashReceived').on('input', calculateChange);
        $('#confirmCashPayment').on('click', processCashPayment);

        // Enter key in cash received field
        $('#cashReceived').on('keypress', function(e) {
            if (e.which === 13) { // Enter key
                $('#confirmCashPayment').click();
            }
        });
    }
    
    function handleProductSearch() {
        const query = $(this).val().trim();

        if (query.length < 2) {
            showEmptyState();
            return;
        }

        // Real AJAX search
        $.ajax({
            url: '{{ route("pos.enhanced.search") }}',
            method: 'POST',
            data: {
                query: query,
                warehouse_id: {{ $warehouse->id ?? 'null' }}
            },
            success: function(response) {
                if (response.success && response.products.length > 0) {
                    // Show first product found
                    showProduct(response.products[0]);
                } else {
                    showEmptyState();
                }
            },
            error: function(xhr) {
                console.error('Search error:', xhr);
                showEmptyState();
                showToast('خطأ في البحث', 'error');
            }
        });
    }
    
    function showProduct(product) {
        currentProduct = product;
        $('#emptyState').addClass('d-none');
        $('#productFound').removeClass('d-none');
        
        $('#productName').text(product.name);
        $('#productPrice').text(`💰 ${product.price} ريال`);
        $('#productStock').text(`📦 المخزون المتاح: ${product.stock} قطعة`);
        $('#quantity').val(1);
    }
    
    function showEmptyState() {
        currentProduct = null;
        $('#emptyState').removeClass('d-none');
        $('#productFound').addClass('d-none');
    }
    
    function updateQuantity(change) {
        const currentQty = parseInt($('#quantity').val()) || 1;
        const newQty = Math.max(1, currentQty + change);
        $('#quantity').val(newQty);
    }
    
    function validateQuantity() {
        const qty = parseInt($(this).val()) || 1;
        $(this).val(Math.max(1, qty));
    }
    
    function addToCart() {
        if (!currentProduct) return;

        const quantity = parseInt($('#quantity').val()) || 1;

        const requestData = {
            product_id: currentProduct.id,
            name: currentProduct.name,
            price: currentProduct.price,
            quantity: quantity,
            tax_rate: currentProduct.tax_rate || 0,
            is_manual: false
        };

        console.log('Enhanced POS - Adding to cart:', requestData);

        $.ajax({
            url: '{{ route("pos.enhanced.add_to_cart") }}',
            method: 'POST',
            data: requestData,
            success: function(response) {
                if (response.success) {
                    loadCartFromServer();
                    clearSearch();
                    showToast('تم إضافة المنتج للسلة بنجاح', 'success');
                } else {
                    showToast(response.message || 'خطأ في إضافة المنتج', 'error');
                }
            },
            error: function(xhr) {
                console.error('Add to cart error:', xhr);
                showToast('خطأ في إضافة المنتج للسلة', 'error');
            }
        });
    }
    
    // Manual product function removed
    
    function loadCartFromServer() {
        $.ajax({
            url: '{{ route("pos.enhanced.get_cart") }}',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    cart = response.cart;
                    updateCartDisplay(response.summary);
                }
            },
            error: function(xhr) {
                console.error('Load cart error:', xhr);
            }
        });
    }

    function updateCartDisplay(summary = null) {
        const cartContainer = $('#cartItems');
        const summaryContainer = $('#cartSummary');
        const paymentButtons = $('#paymentButtons');
        const actionButtons = $('#actionButtons');

        if (cart.length === 0) {
            cartContainer.html(`
                <div class="text-center text-muted py-4">
                    <div style="font-size: 2rem;">🛒</div>
                    <p>السلة فارغة</p>
                </div>
            `);
            summaryContainer.addClass('d-none');
            paymentButtons.addClass('d-none');
            actionButtons.addClass('d-none');
            return;
        }

        // Display cart items
        let cartHtml = '';

        cart.forEach((item, index) => {
            const itemTotal = item.total || (item.price * item.quantity);

            cartHtml += `
                <div class="cart-item">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-details">
                        ${item.quantity} × ${item.price} = ${itemTotal.toFixed(2)} ريال
                    </div>
                    <div class="cart-item-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="editCartItem(${index})">✏️</button>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeCartItem(${index})">🗑️</button>
                    </div>
                </div>
            `;
        });

        cartContainer.html(cartHtml);

        // Update summary from server or calculate locally
        if (summary) {
            $('#subtotal').text(`${summary.subtotal.toFixed(2)} ريال`);
            $('#tax').text(`${summary.tax.toFixed(2)} ريال`);
            $('#total').text(`${summary.total.toFixed(2)} ريال`);
        } else {
            // Fallback calculation
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const tax = subtotal * 0.15;
            const total = subtotal + tax;

            $('#subtotal').text(`${subtotal.toFixed(2)} ريال`);
            $('#tax').text(`${tax.toFixed(2)} ريال`);
            $('#total').text(`${total.toFixed(2)} ريال`);
        }

        // Show summary and buttons
        summaryContainer.removeClass('d-none');
        paymentButtons.removeClass('d-none');
        actionButtons.removeClass('d-none');

        // Check customer selection and show warning if needed
        checkCustomerSelection();
    }

    function checkCustomerSelection() {
        const customerId = $('#customerSelect').val();
        const customerWarning = $('#customerWarning');
        const paymentButtons = $('#paymentButtons .payment-btn');
        const customerOptions = $('#customerSelect option').length;

        // التحقق من وجود عملاء في القائمة
        if (customerOptions <= 1) {
            customerWarning.removeClass('d-none');
            customerWarning.html('<i class="ti ti-alert-triangle"></i> <strong>تحذير:</strong> لا يوجد عملاء مسجلين في هذا الفرع. يرجى إضافة عملاء أولاً.');
            paymentButtons.prop('disabled', true).addClass('btn-disabled');
            return;
        }

        if (!customerId || customerId === '') {
            customerWarning.removeClass('d-none');
            customerWarning.html('<i class="ti ti-alert-triangle"></i> <strong>تنبيه:</strong> يجب اختيار عميل مسجل في الفرع الحالي قبل إتمام عملية الدفع');
            paymentButtons.prop('disabled', true).addClass('btn-disabled');
        } else {
            customerWarning.addClass('d-none');
            paymentButtons.prop('disabled', false).removeClass('btn-disabled');
        }
    }
    
    function removeCartItem(index) {
        $.ajax({
            url: '{{ route("pos.enhanced.remove_from_cart") }}',
            method: 'POST',
            data: {
                index: index
            },
            success: function(response) {
                if (response.success) {
                    loadCartFromServer();
                    showToast('تم حذف المنتج من السلة', 'info');
                } else {
                    showToast(response.message || 'خطأ في حذف المنتج', 'error');
                }
            },
            error: function(xhr) {
                console.error('Remove from cart error:', xhr);
                showToast('خطأ في حذف المنتج من السلة', 'error');
            }
        });
    }
    
    function editCartItem(index) {
        const newQuantity = prompt('أدخل الكمية الجديدة:', cart[index].quantity);
        if (newQuantity && parseInt(newQuantity) > 0) {
            cart[index].quantity = parseInt(newQuantity);
            updateCartDisplay();
            showToast('تم تحديث الكمية', 'success');
        }
    }
    
    function clearSearch() {
        $('#productSearch').val('');
        showEmptyState();
    }
    
    function clearCart() {
        if (cart.length === 0) return;

        if (confirm('هل أنت متأكد من إفراغ السلة؟')) {
            $.ajax({
                url: '{{ route("pos.enhanced.clear_cart") }}',
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        loadCartFromServer();
                        showToast('تم إفراغ السلة', 'info');
                    } else {
                        showToast(response.message || 'خطأ في إفراغ السلة', 'error');
                    }
                },
                error: function(xhr) {
                    console.error('Clear cart error:', xhr);
                    showToast('خطأ في إفراغ السلة', 'error');
                }
            });
        }
    }
    
    function processPayment(type, cashReceived = null) {
        if (cart.length === 0) {
            showToast('السلة فارغة', 'error');
            return;
        }

        // التحقق من اختيار العميل
        const customerId = $('#customerSelect').val();
        if (!customerId || customerId === '') {
            showToast('يجب اختيار عميل مسجل في الفرع الحالي قبل إتمام عملية الدفع', 'error');
            $('#customerSelect').focus();
            return;
        }

        // التحقق من صحة العميل المختار
        const selectedOption = $('#customerSelect option:selected');
        if (!selectedOption.length || selectedOption.val() === '') {
            showToast('العميل المختار غير صحيح', 'error');
            $('#customerSelect').focus();
            return;
        }

        const total = calculateTotal();
        let paymentData = {
            customer_id: customerId,
            payment_type: type
        };

        if (type === 'cash') {
            paymentData.cash_amount = total;
            paymentData.network_amount = 0;
            paymentData.cash_received = cashReceived || total;
        } else if (type === 'network') {
            paymentData.cash_amount = 0;
            paymentData.network_amount = total;
            paymentData.transaction_number = prompt('أدخل رقم المعاملة:') || '';
        } else if (type === 'split') {
            const cashAmount = parseFloat(prompt('أدخل المبلغ النقدي:') || '0');
            const networkAmount = total - cashAmount;

            if (cashAmount < 0 || networkAmount < 0) {
                showToast('مبالغ الدفع غير صحيحة', 'error');
                return;
            }

            paymentData.cash_amount = cashAmount;
            paymentData.network_amount = networkAmount;
            paymentData.transaction_number = prompt('أدخل رقم المعاملة للمبلغ الشبكي:') || '';
        }

        $.ajax({
            url: '{{ route("pos.enhanced.process_payment") }}',
            method: 'POST',
            data: paymentData,
            beforeSend: function() {
                $('.payment-btn').prop('disabled', true).text('جاري المعالجة...');
            },
            success: function(response) {
                if (response.success) {
                    const customerName = response.customer_name || 'العميل';
                    let successMessage = `تم الدفع بنجاح للعميل: ${customerName}! رقم الفاتورة: ${response.invoice_number}`;

                    // إضافة معلومات الدفع النقدي إذا كان متاحاً
                    if (type === 'cash' && cashReceived) {
                        const change = cashReceived - total;
                        if (change > 0) {
                            successMessage += `\nالمبلغ المرتجع: ${change.toFixed(2)} ريال`;
                        }
                    }

                    showToast(successMessage, 'success');

                    // فتح الطباعة الحرارية مباشرة
                    if (response.print_url) {
                        const printWindow = window.open(response.print_url, '_blank', 'width=450,height=850,scrollbars=yes,resizable=yes');
                        if (printWindow) {
                            printWindow.focus();
                        }
                    }

                    // Clear cart and reload
                    setTimeout(() => {
                        loadCartFromServer();
                    }, 1000);
                } else {
                    showToast(response.message || 'خطأ في معالجة الدفع', 'error');
                }
            },
            error: function(xhr) {
                console.error('Payment error:', xhr);
                showToast('خطأ في معالجة الدفع', 'error');
            },
            complete: function() {
                $('.payment-btn').prop('disabled', false);
                $('#payCash').text('💳 دفع نقدي');
                $('#payNetwork').text('💳 دفع شبكة');
                $('#payMixed').text('💳 دفع مختلط');
            }
        });
    }
    
    function calculateTotal() {
        const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        return subtotal * 1.15; // Including 15% tax
    }

    function openCashPaymentModal() {
        // التحقق من اختيار العميل والسلة
        const customerId = $('#customerSelect').val();
        if (!customerId || customerId === '') {
            showToast('يجب اختيار عميل مسجل في الفرع الحالي قبل إتمام عملية الدفع', 'error');
            $('#customerSelect').focus();
            return;
        }

        if (cart.length === 0) {
            showToast('السلة فارغة', 'error');
            return;
        }

        // حساب المجاميع
        const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const tax = subtotal * 0.15; // 15% ضريبة
        const total = subtotal + tax;

        // تحديث بيانات الشاشة المنبثقة
        const customerName = $('#customerSelect option:selected').text();
        $('#modalCustomerName').text(customerName);
        $('#modalItemsCount').text(cart.length);
        $('#modalSubtotal').text(subtotal.toFixed(2));
        $('#modalTax').text(tax.toFixed(2));
        $('#modalTotal').text(total.toFixed(2));

        // إعادة تعيين الحقول
        $('#cashReceived').val('').removeClass('is-invalid');
        $('#changeAmount').val('');
        $('#cashReceivedError').text('');

        // فتح الشاشة المنبثقة
        $('#cashPaymentModal').modal('show');

        // التركيز على حقل المبلغ المستلم
        setTimeout(() => {
            $('#cashReceived').focus();
        }, 500);
    }

    function calculateChange() {
        const total = parseFloat($('#modalTotal').text()) || 0;
        const received = parseFloat($('#cashReceived').val()) || 0;
        const change = received - total;

        if (received > 0) {
            if (change >= 0) {
                $('#changeAmount').val(change.toFixed(2) + ' ريال');
                $('#cashReceived').removeClass('is-invalid');
                $('#cashReceivedError').text('');
                $('#confirmCashPayment').prop('disabled', false);
            } else {
                $('#changeAmount').val('مبلغ غير كافي');
                $('#cashReceived').addClass('is-invalid');
                $('#cashReceivedError').text('المبلغ المستلم أقل من المطلوب');
                $('#confirmCashPayment').prop('disabled', true);
            }
        } else {
            $('#changeAmount').val('');
            $('#confirmCashPayment').prop('disabled', true);
        }
    }

    function processCashPayment() {
        const total = parseFloat($('#modalTotal').text()) || 0;
        const received = parseFloat($('#cashReceived').val()) || 0;

        if (received < total) {
            showToast('المبلغ المستلم أقل من المطلوب', 'error');
            return;
        }

        // إخفاء الشاشة المنبثقة
        $('#cashPaymentModal').modal('hide');

        // معالجة الدفع
        processPayment('cash', received);
    }

    function showToast(message, type = 'info') {
        // Simple toast notification (can be replaced with a proper toast library)
        const toast = $(`
            <div class="alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);

        $('body').append(toast);

        setTimeout(() => {
            toast.alert('close');
        }, 3000);
    }

    // Invoices Management Functions
    function openInvoicesModal() {
        $('#invoicesModal').modal('show');
        loadUserInvoices();
    }

    function loadUserInvoices(page = 1) {
        const search = $('#invoiceSearch').val();
        const dateFrom = $('#invoiceDateFrom').val();
        const dateTo = $('#invoiceDateTo').val();

        $.ajax({
            url: '{{ route("pos.enhanced.get_invoices") }}',
            method: 'GET',
            data: {
                page: page,
                search: search,
                date_from: dateFrom,
                date_to: dateTo
            },
            success: function(response) {
                if (response.success) {
                    displayInvoices(response.invoices);
                    updateInvoicesPagination(response.pagination);
                    $('#invoicesCount').text(response.total);
                } else {
                    showToast(response.message || 'خطأ في تحميل الفواتير', 'error');
                }
            },
            error: function(xhr) {
                console.error('Load invoices error:', xhr);
                showToast('خطأ في تحميل الفواتير', 'error');
            }
        });
    }

    function displayInvoices(invoices) {
        const tbody = $('#invoicesTableBody');

        if (invoices.length === 0) {
            tbody.html(`
                <tr>
                    <td colspan="7" class="text-center text-muted py-4">
                        <i class="ti ti-receipt-off" style="font-size: 2rem;"></i>
                        <p class="mt-2">لا توجد فواتير</p>
                    </td>
                </tr>
            `);
            return;
        }

        let html = '';
        invoices.forEach(invoice => {
            const statusBadge = getInvoiceStatusBadge(invoice.status);
            const paymentTypeBadge = getPaymentTypeBadge(invoice.payment_type);

            html += `
                <tr>
                    <td>
                        <strong>${invoice.pos_id}</strong>
                    </td>
                    <td>${formatDate(invoice.pos_date)}</td>
                    <td>
                        <div>
                            <strong>${invoice.customer_name || 'عميل عادي'}</strong>
                            ${invoice.customer_phone ? '<br><small class="text-muted">' + invoice.customer_phone + '</small>' : ''}
                        </div>
                    </td>
                    <td>
                        <strong>${formatCurrency(invoice.total)}</strong>
                    </td>
                    <td>${paymentTypeBadge}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-primary" onclick="loadInvoiceToCart(${invoice.id})" title="تحميل في السلة">
                                <i class="ti ti-shopping-cart"></i>
                            </button>
                            <button class="btn btn-info" onclick="viewInvoiceDetails(${invoice.id})" title="عرض التفاصيل">
                                <i class="ti ti-eye"></i>
                            </button>
                            <button class="btn btn-success" onclick="printInvoice(${invoice.id})" title="طباعة">
                                <i class="ti ti-printer"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        tbody.html(html);
    }

    function getInvoiceStatusBadge(status) {
        switch(status) {
            case 'paid':
                return '<span class="badge bg-success">مدفوعة</span>';
            case 'pending':
                return '<span class="badge bg-warning">معلقة</span>';
            case 'cancelled':
                return '<span class="badge bg-danger">ملغية</span>';
            default:
                return '<span class="badge bg-secondary">غير محدد</span>';
        }
    }

    function getPaymentTypeBadge(paymentType) {
        switch(paymentType) {
            case 'cash':
                return '<span class="badge bg-success">نقدي</span>';
            case 'network':
                return '<span class="badge bg-info">شبكة</span>';
            case 'mixed':
                return '<span class="badge bg-warning">مختلط</span>';
            default:
                return '<span class="badge bg-secondary">غير محدد</span>';
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    function formatCurrency(amount) {
        return parseFloat(amount).toFixed(2) + ' ريال';
    }

    function searchInvoices() {
        loadUserInvoices(1);
    }

    function loadInvoiceToCart(invoiceId) {
        if (cart.length > 0) {
            if (!confirm('السلة تحتوي على منتجات. هل تريد استبدالها بالفاتورة المختارة؟')) {
                return;
            }
        }

        $.ajax({
            url: '{{ route("pos.enhanced.load_invoice") }}',
            method: 'POST',
            data: {
                invoice_id: invoiceId
            },
            success: function(response) {
                if (response.success) {
                    // Close modal
                    $('#invoicesModal').modal('hide');

                    // Load cart from server
                    loadCartFromServer();

                    // Show success message
                    showToast(`تم تحميل الفاتورة رقم ${response.invoice_number} في السلة`, 'success');

                    // Update customer selection if available
                    if (response.customer_id) {
                        $('#customerSelect').val(response.customer_id);
                    }
                } else {
                    showToast(response.message || 'خطأ في تحميل الفاتورة', 'error');
                }
            },
            error: function(xhr) {
                console.error('Load invoice error:', xhr);
                showToast('خطأ في تحميل الفاتورة', 'error');
            }
        });
    }

    function viewInvoiceDetails(invoiceId) {
        // Open invoice details in new tab
        window.open(`{{ route('pos.show', '') }}/${btoa(invoiceId)}`, '_blank');
    }

    function printInvoice(invoiceId) {
        // Open thermal print in new tab
        window.open(`{{ route('pos.thermal.print', '') }}/${invoiceId}`, '_blank');
    }
</script>
@endpush
