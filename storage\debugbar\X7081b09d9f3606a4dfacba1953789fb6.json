{"__meta": {"id": "X7081b09d9f3606a4dfacba1953789fb6", "datetime": "2025-06-28 16:30:18", "utime": **********.429393, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128217.955218, "end": **********.429409, "duration": 0.47419095039367676, "duration_str": "474ms", "measures": [{"label": "Booting", "start": 1751128217.955218, "relative_start": 0, "end": **********.362857, "relative_end": **********.362857, "duration": 0.4076390266418457, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.362883, "relative_start": 0.4076650142669678, "end": **********.42941, "relative_end": 9.5367431640625e-07, "duration": 0.06652688980102539, "duration_str": "66.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46429480, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2513\" onclick=\"\">app/Http/Controllers/PosController.php:2513-2547</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00237, "accumulated_duration_str": "2.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4097989, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75.527}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.421277, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 75.527, "width_percent": 24.473}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1934775299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1934775299\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128216412%7C46%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdRL0xlSGN6Z095aDJlejBQYVhCQ2c9PSIsInZhbHVlIjoiKzVidllxaGdxdTdBMUdmbDlFa09kVUxuRkgvSVQzcGhUQ2Vtb3VWSjZseFJweVhhYVR1NENLR3paWHBSUzBxRGwxR3pHWXZQYjJvQXhhOC83SG5WSUFleDk0bkFmVnBmL09ORGdBVGhUSW54WjYrTEFFUzlWcDAvYWdadUpucHRKQmZFVlZRNjloVm1obkkva2dFcHpSdUNkeXhFWkdXZzQyVGdSMlFRRXZwbkNBMEpzMHFEOXVVOGJrbndnQXU2SjRhM0Z0Y1hNU3Fxcm1ITVNEOXRmWnRPYUYrWHduM3ZzSGdyck12K1FWb05sTHVxYjVYMDBrbFAwdE9jOFpnT2VqQzNrb0tHNlVQZ3M2UHlqQklmdldRM3kxWnJwbmU2YWJ5alNkQjYraURpOW1QRVVveFZQc0NRTXQzNzhmbXNpUUE1bW5Zd01pUmtldVl1SHphNnNLSG5Jdmthemk3R3lGbzRGNE5KTUo0WGp6aEw4amgyaHFvZUYrOHVQeXUxZThBREdmZWRtYmpiSDRqMFpOTFEyTEdvSjFUam5KQ2JlVlpBWW95eE9NdUFaN25vY091elFTZi9saFlHTmZLa0ZOYUd3Q2NhM3J0d1c2K2N0Rms2NHE1RVNYR0VObzk0Qm85SWw4S3NpTnRiUDVKN3FiZld4cEprOVFEbUpVcCsiLCJtYWMiOiI0MDgxOGMwNGZkMDhmYzc2MjRmODQ0YWY2OGZhNTc3ZTdiOGRhYmU2YzJjNjYxMzViNzhmODVkZWUyYjNlODA1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFkZzAyNmdlbTEvcUxBSWVtNmM0UWc9PSIsInZhbHVlIjoiWUdzbERzcGlxMDUrQ2ZqczNzaENqT3E5L29NTE52bEpqVjhMVEdXa0VDOHd0aS90ZC9FWkJqZmlFejBTTXUzZjRpdUN4Z0Y5K01IUUpVaXhJV0p0WURFUVhhY1FLQlFoRUJES1ZvR3VubURVOWhUMDcwZ1BHL1pYVGUzUjNvbGJUSVlZMWN3c09LRjJ1ck1TcUEwdUxQcy9kR2VkNkgxbXRpWkFSNkdDVGJucHJCRnNJNmdSdUMwNXZ3QlhMQUt5TEFvbkQ0Nmx5YTFZM1p6TzE2YTNCU3J2Y0V2U3NJVjhLcndLbnliQkQxeDNyaFFVakc1KzV5dDRUSVJlWkNIRUdXVitiL1VOZVpYSU55YWlqc0NOdkNMVGtGQkNZVkY3WElQalpyekJwNUZaaXlxc2FWNVR6SjhKVkd2disreXI0Ui9YQnRFRHNzOTAveHlQRWhkcEVMaVArSlQyeXNmT0I2MkJidHRWRExyWGZ4cDcvUTFxT3d0SGJmRExOdWFYRFliWEdWMDFPSThFVWoxUkVZd3RqUG9Dc1JLU3IxWStHKzNYejZEUTljeXF1TUJhUDFRQStCQTFpdVZYMmNBRVppSWQ4TmVFTGlXYWFDajFjQTlzVHJoSnpZYVlJaXVVSGVkVTZSUVo5MVlKOUlKaStWSzd1bTF6QzByODJVNmQiLCJtYWMiOiIyZWFiY2I1ODc0NzZhY2IwZmNhNjY3NmJhYWI3NDRmYmExNDI4MjNhODFiNjU1YTBmNTE2YTYzMzg0ODNiZDBmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-530311313 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-530311313\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1735565982 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlN5MEdYSC9hUFZzWm0wRGNla0hlT0E9PSIsInZhbHVlIjoiZG44Rml1dGFQRThIYzdaRDNrMDVuVkhIWE92YmkrU1RJWTdxM20rMlVGVUplZ3NjVGJHdGFlbFRVVUJrWDRLUWFLaFc5KytJNG9LeUZOZ0hwNU9JeXF3R1ZidmRqVmluT0pqV1p3VU1nMFl5L1JDOW4vNGI2MWtWc3FJZ2ZyZHo4TzhYN2I5emtOVExWTFpEQ1VyalZ6a1lKRVJuL2JrOGF1M1V0bXFRY3JNYkdGUXRPMFRzTDZrVm9mUFdwT3hnZVBYeC9KMXBRVlhHUkJjQ3NpdVF6aW9QNlZiOHFDSnVqSlhBM29lOStpcFd4TDl5Zk0rQkJlbHBGY1FyTytQSm1kV09LVzZJVU5pdE02eTZYa0owNjRmRURSZkFrQm45NjRyd3NTYkVUMzhDSURRUzFjZlhESVBteW4yeFpPYlhuY1RmL3NkTDJ5NHg2VnkrTjF5ckRjU04vem92dHk2cG16cTRYYmNNaHBXc3luVmcvQTRPRFVTQk9BL0xtVDhIMVhuMGhpUWl4UnV0bTNJN21HYWtQL1Uzek10WEdXT0N6MXRodHJRQ2NyOFY0WGUydEZDV0kzQXVxU241WVFQRTUyN0ZWV3o0Q1hwaFhCVmxHQUQ5U0NtWWdGTVIxOEpaT3FoYkVsNng3NVoyc3A0ZWRvckhFb29mcEVveDJqanAiLCJtYWMiOiIxMGU0YWYxZjlmYjQxYjczOGUyNzFjNzQ2ZTRlOGQyMTUwMTFmZWNiMWNiOTYzMzc5YTdlZWU0YzAyNGU3OWRkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1VZi80UVZUQ3Fnbmg2V0c5RUdzQkE9PSIsInZhbHVlIjoicGdDaFd3dkZpeGxKd20zb0xqTXZiZkw0bnVuOVVIL3Zla0VqdXpDV293cmNOZ1dEdzd0QTUza2tWM0V1RU9qYjV1RDhoRXB2c0lOWjcxbjFpQVNtc05FNnBGZUtZd3c4a0paSjJvcDRxT3EwVU5RdmtxdWNQaEF6V1lBelk2aGlvUDZXdHJhRUZxcGVBeGdFNWtNOW9PSElsNDJwTWd3Z2gxeFNuY2lMZFN1Y2RpUXkwZXIwR1FNa2VnWmRiT1htQzN5OXBVUFpWcTdyYXM1VlZ0b1ZTcGx0YkcrWkRIRkF0S0UxUUw3bUhzS21hL0VQbExUMUNRTy9lS0ZxRVpONmZqUjk3NkNOZjkxaEFaSUJnQ2pXVldkVklUSHNxTkFyRGQ4S3VDZTEzNytYckJya0NKbkgyOU5sMTNRRHJGNnp0UUlBKzJjQVp4SXpEZzhWMTZ3M2UvSnZ3U2FmU1hkdkpjYXhNWDNjWUNzWjhYaGc1Rmd5TEtHK0puZ0FRQmFhVGVqb3lUY0cyMGRzNjdGMnZ4aUUzbEFJNTU0ZEhEUGp6SWpZMGRTM3VqOUVhdmtVM3N4QVZZeWVrRmhHTXdTeVlMaFhoMnBaZGJMU2VkK0VjY3dTcXc1T3ZxUTZqQnFxN0NhbXVnajd5djZPTHNJNW0zT0RQa3pvZ2FPL21iU3giLCJtYWMiOiJkY2Q4ZGU3ZjY4MDMwNDRmNTJkNTVlZWRiODI0MzU5ZTBjMTMyNDU5MjlmZmEzZjIwOGE0MDU3ODFkMjhmNDlkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlN5MEdYSC9hUFZzWm0wRGNla0hlT0E9PSIsInZhbHVlIjoiZG44Rml1dGFQRThIYzdaRDNrMDVuVkhIWE92YmkrU1RJWTdxM20rMlVGVUplZ3NjVGJHdGFlbFRVVUJrWDRLUWFLaFc5KytJNG9LeUZOZ0hwNU9JeXF3R1ZidmRqVmluT0pqV1p3VU1nMFl5L1JDOW4vNGI2MWtWc3FJZ2ZyZHo4TzhYN2I5emtOVExWTFpEQ1VyalZ6a1lKRVJuL2JrOGF1M1V0bXFRY3JNYkdGUXRPMFRzTDZrVm9mUFdwT3hnZVBYeC9KMXBRVlhHUkJjQ3NpdVF6aW9QNlZiOHFDSnVqSlhBM29lOStpcFd4TDl5Zk0rQkJlbHBGY1FyTytQSm1kV09LVzZJVU5pdE02eTZYa0owNjRmRURSZkFrQm45NjRyd3NTYkVUMzhDSURRUzFjZlhESVBteW4yeFpPYlhuY1RmL3NkTDJ5NHg2VnkrTjF5ckRjU04vem92dHk2cG16cTRYYmNNaHBXc3luVmcvQTRPRFVTQk9BL0xtVDhIMVhuMGhpUWl4UnV0bTNJN21HYWtQL1Uzek10WEdXT0N6MXRodHJRQ2NyOFY0WGUydEZDV0kzQXVxU241WVFQRTUyN0ZWV3o0Q1hwaFhCVmxHQUQ5U0NtWWdGTVIxOEpaT3FoYkVsNng3NVoyc3A0ZWRvckhFb29mcEVveDJqanAiLCJtYWMiOiIxMGU0YWYxZjlmYjQxYjczOGUyNzFjNzQ2ZTRlOGQyMTUwMTFmZWNiMWNiOTYzMzc5YTdlZWU0YzAyNGU3OWRkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1VZi80UVZUQ3Fnbmg2V0c5RUdzQkE9PSIsInZhbHVlIjoicGdDaFd3dkZpeGxKd20zb0xqTXZiZkw0bnVuOVVIL3Zla0VqdXpDV293cmNOZ1dEdzd0QTUza2tWM0V1RU9qYjV1RDhoRXB2c0lOWjcxbjFpQVNtc05FNnBGZUtZd3c4a0paSjJvcDRxT3EwVU5RdmtxdWNQaEF6V1lBelk2aGlvUDZXdHJhRUZxcGVBeGdFNWtNOW9PSElsNDJwTWd3Z2gxeFNuY2lMZFN1Y2RpUXkwZXIwR1FNa2VnWmRiT1htQzN5OXBVUFpWcTdyYXM1VlZ0b1ZTcGx0YkcrWkRIRkF0S0UxUUw3bUhzS21hL0VQbExUMUNRTy9lS0ZxRVpONmZqUjk3NkNOZjkxaEFaSUJnQ2pXVldkVklUSHNxTkFyRGQ4S3VDZTEzNytYckJya0NKbkgyOU5sMTNRRHJGNnp0UUlBKzJjQVp4SXpEZzhWMTZ3M2UvSnZ3U2FmU1hkdkpjYXhNWDNjWUNzWjhYaGc1Rmd5TEtHK0puZ0FRQmFhVGVqb3lUY0cyMGRzNjdGMnZ4aUUzbEFJNTU0ZEhEUGp6SWpZMGRTM3VqOUVhdmtVM3N4QVZZeWVrRmhHTXdTeVlMaFhoMnBaZGJMU2VkK0VjY3dTcXc1T3ZxUTZqQnFxN0NhbXVnajd5djZPTHNJNW0zT0RQa3pvZ2FPL21iU3giLCJtYWMiOiJkY2Q4ZGU3ZjY4MDMwNDRmNTJkNTVlZWRiODI0MzU5ZTBjMTMyNDU5MjlmZmEzZjIwOGE0MDU3ODFkMjhmNDlkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1735565982\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}