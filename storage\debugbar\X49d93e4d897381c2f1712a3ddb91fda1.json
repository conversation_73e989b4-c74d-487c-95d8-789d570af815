{"__meta": {"id": "X49d93e4d897381c2f1712a3ddb91fda1", "datetime": "2025-06-28 11:22:40", "utime": **********.652407, "method": "GET", "uri": "/add-to-cart/2281/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.178948, "end": **********.652433, "duration": 0.47348499298095703, "duration_str": "473ms", "measures": [{"label": "Booting", "start": **********.178948, "relative_start": 0, "end": **********.550997, "relative_end": **********.550997, "duration": 0.37204909324645996, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.551005, "relative_start": 0.3720569610595703, "end": **********.652437, "relative_end": 4.0531158447265625e-06, "duration": 0.10143208503723145, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48609040, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.016629999999999995, "accumulated_duration_str": "16.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.590321, "duration": 0.012119999999999999, "duration_str": "12.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.88}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.612886, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.88, "width_percent": 3.067}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.627312, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 75.947, "width_percent": 2.886}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.629387, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.833, "width_percent": 2.405}, {"sql": "select * from `product_services` where `product_services`.`id` = '2281' limit 1", "type": "query", "params": [], "bindings": ["2281"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.634135, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 81.239, "width_percent": 2.165}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2281 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2281", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.638758, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 83.403, "width_percent": 14.973}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6427681, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 98.376, "width_percent": 1.624}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1831764761 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831764761\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.633185, "xdebug_link": null}]}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2281 => array:9 [\n    \"name\" => \"دايجستيف بسكويت شوكولاتة-12×37 غرام\"\n    \"quantity\" => 1\n    \"price\" => \"65.00\"\n    \"id\" => \"2281\"\n    \"tax\" => 0\n    \"subtotal\" => 65.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2281/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1791838653 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1791838653\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-51542039 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IkpEN1I3R09kbmNXVTdEclNQNDZuaFE9PSIsInZhbHVlIjoiQmxGR3kwOWw1bElBbmxYazhiSnhMWW53MVZGeVJJM1VIc1lTUlZuRmk3aGhpeitLRjBjYkQ4Q3JHR0FndXNmSUZxTzEzbmlFVGZMbWZ5cjh5MnpCZmRCQzQreGNsSm91MFRlc216eVVySEl4UzBiWm0yN1ptUzQxa1NMcjBSeVdaNEhlTGJabjFxcERrSHRPZUNlWmFkTE16eEE4ZEI5NFFjQ3NxY056SHYwQWNoQ1htNWNJTGhCWDhoZnRQeU80SkxjeWxoS0M3U0tVYS9RQWJ4OS9qck1vQnVWamV2eUVpZHYrYVpHTFJ4R29FRzFFbTJmMjkvYTJNMjNacjFRV3JnLzFpaU5LY3BNa1pHcWpKcVpreG82L2ZTVW1BQ29SazBCbHhtMWNrdnRKZ2tMQnZkTk9EZUtpS1g1VEs1dUJxUjVFNlJlUlY4YzBpUzB0dlNCVldXTldtZHdTOGFqd0hQZy91SGRqT2JxeUg2ZUZTSWJ5enpKSVBLNmZ5R1h3dlNsT0lZQ0Vhd08rNjF1V0s5M0tqUm8vcFNhYlpKdEw4RUl2T2FlNEdmQ3FqTHluTlZRNDYyRWZSK1NaNDVrTERjM2IvZXJDSTM5SHVpL3NjZGtyTFNJV1hlSDZzbFpYOS9RRytCNU80VVFSS2grU2RPVE14VmtHb2hXWTlEdDAiLCJtYWMiOiIzZjJiMzEyZmQ5ZTk4NzQyZmNmODlhOWFlMGU4ODYzMWUwZTMyZTIzY2ZiYWFmNTY0NDBlMGNiNjhlMGJhMzNkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkxXZUJpZ1FOUWJFTXVkWHF2RXB0U3c9PSIsInZhbHVlIjoiUGE0ZHdTS2xWV3NtdjFjNUNKSVMrdW94VUdKc2Y2aEtvNVhBSCtDeWxkTktTenFkR1ptTitiVHVQbkpBdko1ZHB3a2YvUTlLclBsSWYwdXcxaGJwUjRtaDZFNGZ4SFdya1plaE03UDVhOHRUYjVWZnZ5SGhWTW9oT0ZDSW4wTDcrRTZSalUvZ3FoVEdqTE9qRm9NcWtCTlVqckl2UG5rZHp4RTdic05aT09aREVHcEpNWnNGaERJRUpoblFDS21vRExQQzBVRnlmWGVLM0NpalkwY2g2VEJ0U1hZTDNMekdkcVJXRjJzWE9iSldPTDVnaDRJaWVWdUUxdzNwM00xYjkwV1VEZDJmRWFLWkhRNlc4d1R5ekJwaUN0MjU4K0J6djFlSy9wbDV6L1ZpUWJTWkNDUGdWSm9tdmxCU3EvdUZHTk91MjQvRTNsNUtUZGNtZmV5WEhsMDlnVzRFVDdxdm9SSDdwaWZVakU5VEQ2c2RpYndXN1RSNGt6U253a3BhN2g1QUNwMGdMUndNTFE3TEx3NTlqWVE0S1QxN29MOTZvTTIwQlBSNmdtSm11VkhGazZ5KzRtUXphTmM3WGlMZUZjQTZJNXd0SElCWGpPZnRieW14TTNad0FiSXFCZVpiaHl5UGhvOGNKQWtKOXR0K2g3bGthalArUzl5Z3hEaTUiLCJtYWMiOiJiOGI1MDUzYmUxY2I3YWQzYTY0YzJmMTM5YWMxOWM1MzA5MzdjZmU1ZjJiNmJiMTBmYmY4NmM0NDQ4MTUwZjI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-51542039\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-795353049 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795353049\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1839103001 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:22:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdWb0RLVVJiZzBZbTJhWEtDaVowN2c9PSIsInZhbHVlIjoidWwwZW1rS0VOWkMvU0Y1VEZXNkdZWFh4K1NjcGpVa0VNdnNEU3AwaXJ5dGw2VTlabHFVMmp0VmZTZytlaWpnQkxIRUM5M0ZpTXk1RCthbG1DUzVoSzZkUHpPdjN6TlR0NHlKYzR5UXRFVnVNRVpPeVU3cnUrR1FmaUtvUHh4SHk1RGFydmhyR21KUzJxWmRxWTI5L1JVamZVdU1LTTFlUlNXKzY3UHU3YTcwMmNVdkxYcXFMQlk5c09JcmFmSTdjc0NWYzNQZitWYmhTY0cvQkFGcERENFAzbTk1ay9HenZhMU5wT3B4Y2ZsSGxpc2l0TlArQU5laFBkeUhVeEVYMzdGM3lPd0pwd2wxMjJSRDUwMzNZUFlDZko5SmtGcS9BWkxsbVV0WkRmUHVQRGFHSmM3dklVMkp3R291UU93Wk1wSnhnQ2w3VW5HT3pGbm5zbytqekpaK1VKZWtJYWlYcitVWXo0M2l5ekRIMXVBcXhGSURudDFFZi9nYTEvTDlCUWY4Mzk4c1psNHBoeDN4b0NLeUV5dDJzYVdvb0lhcTlWRndEbXo3TGdHMGJJY3Y1cHRWKzJwZmdVNEEycUY0c2ovRHp2bkE5TzNDblYyZ0xEMXFkOVFpcFdqWXJlb2VwY2JtWmpmZlFPWGROVnBNczBibGtuTmprR0JZcDlIUzUiLCJtYWMiOiI5ZDNhYTNlMTBiZGM5ZDZjYzAyMGU2YWJjNzA3YTU5ZGE3Yzg3N2MyMzNmOGYwMjU1NzMxZWQwY2NlNWY3ODlmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IklENFNNN0VzQTVycGNTeXg4N3pUa1E9PSIsInZhbHVlIjoiZTUrWDl5S2t4TGgzbTNib0tSelVMekxpZ09TMlhKaXBlZWREYlNWNEhYaHkzRzlIbmcvblFGWU9UTzhaTkY1NkVaMlNacUdCbGhES0JGSHNsRnBqeUxvTElhTjJwdkYvSno5L1pVMDdMY01WdTFnWmZ4MEtKNEFtaFFzRVp1UjZSbkFGZHdKNGxRZURoR01YbXdkZmdxajRaT3QzR0JLK3JFYThJbWhTNngvOWJHTGZlQzBYdVkvaDliN3R2TTNRQjZPditTS2JRNVRjWUFqbzBPM3VSc2JzVERoVTY0a1NwY3ZtRHkxVWpSNGZwYTl6U1haSW9LeldUMkFDMW1kTVRmRzlGdEU2eFR3RnRVMEhXYnpRaENPUGl4TGVESlFqak9IWUdLSFV0N2pnMGwzT0djcTlwSk10RnhuNXN1TWdNaDdZaFVuZ0kxMFFSa1cwcVQ2dk9hZkZpVkUxOUYrckk5UFV6T0dJS29KNzVpdEs4YVVza1M0Z3lLdmpJMTdnbkhUTmtHRk9aRkxObFpUU01CMjJJdmVmRzZQTnNFZmdyR3g5emFWenFSeW4yVFpuY2paOXgvR2ZLa0VvMExZVFlObCtocWgxYnFOTUhYMkkvdmwvc05oSHozU0JuUnI2MElBYXNJMFE3b1pzYU9DcUhDN1Zoa2xyZ0RzT0JQV0YiLCJtYWMiOiI1MjZmOTc4MGE2NjIwNmFiODM0MDk3MWVkYmQzMWU4NTlhZWYxM2VmZDk4ZGUyYjYyMmMzMWU0ZGI1YjI5MGJkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdWb0RLVVJiZzBZbTJhWEtDaVowN2c9PSIsInZhbHVlIjoidWwwZW1rS0VOWkMvU0Y1VEZXNkdZWFh4K1NjcGpVa0VNdnNEU3AwaXJ5dGw2VTlabHFVMmp0VmZTZytlaWpnQkxIRUM5M0ZpTXk1RCthbG1DUzVoSzZkUHpPdjN6TlR0NHlKYzR5UXRFVnVNRVpPeVU3cnUrR1FmaUtvUHh4SHk1RGFydmhyR21KUzJxWmRxWTI5L1JVamZVdU1LTTFlUlNXKzY3UHU3YTcwMmNVdkxYcXFMQlk5c09JcmFmSTdjc0NWYzNQZitWYmhTY0cvQkFGcERENFAzbTk1ay9HenZhMU5wT3B4Y2ZsSGxpc2l0TlArQU5laFBkeUhVeEVYMzdGM3lPd0pwd2wxMjJSRDUwMzNZUFlDZko5SmtGcS9BWkxsbVV0WkRmUHVQRGFHSmM3dklVMkp3R291UU93Wk1wSnhnQ2w3VW5HT3pGbm5zbytqekpaK1VKZWtJYWlYcitVWXo0M2l5ekRIMXVBcXhGSURudDFFZi9nYTEvTDlCUWY4Mzk4c1psNHBoeDN4b0NLeUV5dDJzYVdvb0lhcTlWRndEbXo3TGdHMGJJY3Y1cHRWKzJwZmdVNEEycUY0c2ovRHp2bkE5TzNDblYyZ0xEMXFkOVFpcFdqWXJlb2VwY2JtWmpmZlFPWGROVnBNczBibGtuTmprR0JZcDlIUzUiLCJtYWMiOiI5ZDNhYTNlMTBiZGM5ZDZjYzAyMGU2YWJjNzA3YTU5ZGE3Yzg3N2MyMzNmOGYwMjU1NzMxZWQwY2NlNWY3ODlmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IklENFNNN0VzQTVycGNTeXg4N3pUa1E9PSIsInZhbHVlIjoiZTUrWDl5S2t4TGgzbTNib0tSelVMekxpZ09TMlhKaXBlZWREYlNWNEhYaHkzRzlIbmcvblFGWU9UTzhaTkY1NkVaMlNacUdCbGhES0JGSHNsRnBqeUxvTElhTjJwdkYvSno5L1pVMDdMY01WdTFnWmZ4MEtKNEFtaFFzRVp1UjZSbkFGZHdKNGxRZURoR01YbXdkZmdxajRaT3QzR0JLK3JFYThJbWhTNngvOWJHTGZlQzBYdVkvaDliN3R2TTNRQjZPditTS2JRNVRjWUFqbzBPM3VSc2JzVERoVTY0a1NwY3ZtRHkxVWpSNGZwYTl6U1haSW9LeldUMkFDMW1kTVRmRzlGdEU2eFR3RnRVMEhXYnpRaENPUGl4TGVESlFqak9IWUdLSFV0N2pnMGwzT0djcTlwSk10RnhuNXN1TWdNaDdZaFVuZ0kxMFFSa1cwcVQ2dk9hZkZpVkUxOUYrckk5UFV6T0dJS29KNzVpdEs4YVVza1M0Z3lLdmpJMTdnbkhUTmtHRk9aRkxObFpUU01CMjJJdmVmRzZQTnNFZmdyR3g5emFWenFSeW4yVFpuY2paOXgvR2ZLa0VvMExZVFlObCtocWgxYnFOTUhYMkkvdmwvc05oSHozU0JuUnI2MElBYXNJMFE3b1pzYU9DcUhDN1Zoa2xyZ0RzT0JQV0YiLCJtYWMiOiI1MjZmOTc4MGE2NjIwNmFiODM0MDk3MWVkYmQzMWU4NTlhZWYxM2VmZDk4ZGUyYjYyMmMzMWU0ZGI1YjI5MGJkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839103001\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2027179544 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2281</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"35 characters\">&#1583;&#1575;&#1610;&#1580;&#1587;&#1578;&#1610;&#1601; &#1576;&#1587;&#1603;&#1608;&#1610;&#1578; &#1588;&#1608;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577;-12&#215;37 &#1594;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">65.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2281</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>65.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027179544\", {\"maxDepth\":0})</script>\n"}}