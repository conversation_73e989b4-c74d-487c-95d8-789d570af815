<!DOCTYPE html>
<html>
<head>
    <title>اختبار استرداد الفواتير - Enhanced POS</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .feature-box { background: #f0f8ff; padding: 15px; margin: 10px 0; border-radius: 8px; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; border: none; }
        .btn-success { background: #28a745; color: white; border: none; }
        .btn-warning { background: #ffc107; color: black; border: none; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 اختبار استرداد الفواتير - Enhanced POS</h1>
    
    <div class="test-section">
        <h2>📋 الوظائف الجديدة</h2>
        <div class="feature-box">
            <h4>✅ الوظائف المضافة:</h4>
            <ul>
                <li><strong>زر الفواتير:</strong> استبدال زر الطباعة بزر الفواتير</li>
                <li><strong>شاشة منبثقة:</strong> عرض الفواتير المباعة مع البحث والتصفية</li>
                <li><strong>تحميل الفاتورة:</strong> تحميل فاتورة مختارة في السلة للتعديل</li>
                <li><strong>إدارة النقد:</strong> حساب الفرق في المبالغ عند التعديل</li>
                <li><strong>تتبع التعديلات:</strong> ربط الفاتورة المعدلة بالأصلية</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔧 اختبار الوظائف</h2>
        
        <div class="feature-box">
            <h4>1. اختبار جلب الفواتير</h4>
            <button class="btn-primary" onclick="testGetInvoices()">اختبار جلب الفواتير</button>
            <div id="invoicesResult" style="margin-top: 10px;"></div>
        </div>
        
        <div class="feature-box">
            <h4>2. اختبار تحميل فاتورة</h4>
            <input type="number" id="invoiceIdInput" placeholder="معرف الفاتورة" style="padding: 5px; margin: 5px;">
            <button class="btn-success" onclick="testLoadInvoice()">اختبار تحميل الفاتورة</button>
            <div id="loadResult" style="margin-top: 10px;"></div>
        </div>
        
        <div class="feature-box">
            <h4>3. اختبار السلة الحالية</h4>
            <button class="btn-warning" onclick="testGetCart()">عرض محتويات السلة</button>
            <div id="cartResult" style="margin-top: 10px;"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 الفواتير المتاحة</h2>
        @php
            $recentInvoices = \App\Models\Pos::with(['customer', 'posPayment'])
                ->where('created_by', Auth::user()->creatorId())
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get();
        @endphp
        
        @if($recentInvoices->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>معرف</th>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>العميل</th>
                        <th>المجموع</th>
                        <th>نوع الدفع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($recentInvoices as $invoice)
                        <tr>
                            <td>{{ $invoice->id }}</td>
                            <td><strong>{{ $invoice->pos_id }}</strong></td>
                            <td>{{ $invoice->pos_date }}</td>
                            <td>{{ $invoice->customer ? $invoice->customer->name : 'عميل عادي' }}</td>
                            <td>{{ number_format($invoice->getTotal(), 2) }} ريال</td>
                            <td>
                                @if($invoice->posPayment)
                                    <span class="badge bg-info">{{ ucfirst($invoice->posPayment->payment_type) }}</span>
                                @else
                                    <span class="badge bg-secondary">غير محدد</span>
                                @endif
                            </td>
                            <td>
                                <button class="btn-primary" onclick="quickLoadInvoice({{ $invoice->id }})" style="padding: 5px 10px; font-size: 12px;">
                                    تحميل سريع
                                </button>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="warning">
                <p>⚠️ لا توجد فواتير متاحة للاختبار</p>
            </div>
        @endif
    </div>
    
    <div class="test-section">
        <h2>📝 سيناريوهات الاختبار</h2>
        
        <div class="feature-box">
            <h4>📋 سيناريو 1: استرداد فاتورة للتعديل</h4>
            <ol>
                <li>افتح Enhanced POS</li>
                <li>انقر على زر "📋 الفواتير"</li>
                <li>اختر فاتورة من القائمة</li>
                <li>انقر على "تحميل في السلة"</li>
                <li>تحقق من تحميل المنتجات في السلة</li>
                <li>قم بتعديل الكميات أو إضافة منتجات</li>
                <li>أتمم عملية الدفع</li>
            </ol>
        </div>
        
        <div class="feature-box">
            <h4>💰 سيناريو 2: اختبار إدارة النقد</h4>
            <ol>
                <li>استرد فاتورة بمبلغ 100 ريال</li>
                <li>عدل الفاتورة لتصبح 150 ريال</li>
                <li>أتمم الدفع</li>
                <li>تحقق من إضافة 50 ريال فقط للنقد</li>
            </ol>
        </div>
        
        <div class="feature-box">
            <h4>🔍 سيناريو 3: اختبار البحث والتصفية</h4>
            <ol>
                <li>افتح شاشة الفواتير</li>
                <li>جرب البحث برقم الفاتورة</li>
                <li>جرب البحث باسم العميل</li>
                <li>جرب التصفية بالتاريخ</li>
            </ol>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // CSRF Token setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        function testGetInvoices() {
            showResult('invoicesResult', 'جاري اختبار جلب الفواتير...', 'info');
            
            $.ajax({
                url: '{{ route("pos.enhanced.get_invoices") }}',
                method: 'GET',
                data: {
                    page: 1,
                    search: '',
                    date_from: '',
                    date_to: ''
                },
                success: function(response) {
                    showResult('invoicesResult', '✅ نجح اختبار جلب الفواتير!', 'success');
                    showResult('invoicesResult', 'عدد الفواتير: ' + response.total, 'info');
                    showResult('invoicesResult', 'البيانات: ' + JSON.stringify(response, null, 2), 'info');
                },
                error: function(xhr) {
                    showResult('invoicesResult', '❌ فشل اختبار جلب الفواتير!', 'error');
                    showResult('invoicesResult', 'الخطأ: ' + xhr.responseText, 'error');
                }
            });
        }
        
        function testLoadInvoice() {
            const invoiceId = document.getElementById('invoiceIdInput').value;
            if (!invoiceId) {
                showResult('loadResult', '⚠️ يرجى إدخال معرف الفاتورة', 'warning');
                return;
            }
            
            showResult('loadResult', 'جاري اختبار تحميل الفاتورة...', 'info');
            
            $.ajax({
                url: '{{ route("pos.enhanced.load_invoice") }}',
                method: 'POST',
                data: {
                    invoice_id: invoiceId
                },
                success: function(response) {
                    showResult('loadResult', '✅ نجح تحميل الفاتورة!', 'success');
                    showResult('loadResult', 'رقم الفاتورة: ' + response.invoice_number, 'info');
                    showResult('loadResult', 'عدد المنتجات: ' + response.cart_count, 'info');
                    showResult('loadResult', 'البيانات: ' + JSON.stringify(response, null, 2), 'info');
                },
                error: function(xhr) {
                    showResult('loadResult', '❌ فشل تحميل الفاتورة!', 'error');
                    showResult('loadResult', 'الخطأ: ' + xhr.responseText, 'error');
                }
            });
        }
        
        function testGetCart() {
            showResult('cartResult', 'جاري فحص السلة...', 'info');
            
            $.ajax({
                url: '{{ route("pos.enhanced.get_cart") }}',
                method: 'GET',
                success: function(response) {
                    showResult('cartResult', '✅ تم فحص السلة!', 'success');
                    showResult('cartResult', 'عدد المنتجات: ' + response.cart_count, 'info');
                    showResult('cartResult', 'المجموع: ' + response.total + ' ريال', 'info');
                    showResult('cartResult', 'البيانات: ' + JSON.stringify(response, null, 2), 'info');
                },
                error: function(xhr) {
                    showResult('cartResult', '❌ فشل فحص السلة!', 'error');
                    showResult('cartResult', 'الخطأ: ' + xhr.responseText, 'error');
                }
            });
        }
        
        function quickLoadInvoice(invoiceId) {
            document.getElementById('invoiceIdInput').value = invoiceId;
            testLoadInvoice();
        }
        
        function showResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            
            container.innerHTML += 
                '<div class="' + colorClass + '">[' + timestamp + '] ' + message + '</div>';
        }
    </script>
    
    <hr>
    <h3>🔗 روابط مفيدة:</h3>
    <p><a href="{{ route('pos.enhanced.index') }}" target="_blank">Enhanced POS الرئيسي</a></p>
    <p><a href="{{ route('simple.pos.test') }}">اختبار Enhanced POS البسيط</a></p>
    <p><a href="{{ route('dashboard') }}">لوحة التحكم</a></p>
</body>
</html>
