{"__meta": {"id": "Xe80e3eb0b531837eee55acc45c39dbed", "datetime": "2025-06-28 16:08:02", "utime": **********.852244, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.370637, "end": **********.852258, "duration": 0.48162102699279785, "duration_str": "482ms", "measures": [{"label": "Booting", "start": **********.370637, "relative_start": 0, "end": **********.771844, "relative_end": **********.771844, "duration": 0.40120697021484375, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.771852, "relative_start": 0.4012150764465332, "end": **********.85226, "relative_end": 2.1457672119140625e-06, "duration": 0.08040809631347656, "duration_str": "80.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45699752, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02399, "accumulated_duration_str": "23.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.80543, "duration": 0.023190000000000002, "duration_str": "23.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.665}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.837736, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.665, "width_percent": 1.542}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8436208, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.208, "width_percent": 1.792}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2116237266 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2116237266\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-211534660 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-211534660\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2026346633 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2026346633\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1508601557 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126675710%7C29%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVpaG14V1NncGRZT3l4NmZvcWxFWlE9PSIsInZhbHVlIjoiRGlwWmthSXhjNFllQVl3Ukt3ZXJsTW5yZnNQRk9kSHpvOWI3Q3pvY3Y2SVdrd3RXUGFzYkczaWhxUVVhWXZ2TFAxM3NUdlBZd0NuazFIM25GaVRCRzNvZ3gvNXR1Uk8rSjB3MlpOci9yenZwdENaUm5EZFJCTXNRMDhuT0ZUSktDOWd4SzFRV2V1RVlacURhTXM2SUpPaCs2Vk1OaW1Wc3Yzc1BvUDZxWVV4eGlKcENwVUZ2c2F1ZXhrQ2svRWRxUElZWWtFVFhZcm5saFZzL1NBbTF1S0NXaGF6WCtFWFhnVU1FdGE2dlEyc0pxQlFFZWJ6MGdiMkRFTGhZZ2NIR214a2lsTGR6MlliM3F4a1l4TFJDTEh4ZHB4by9iRk9zSVM0VmthSkRzL2c2Mk1tZ3V4MUZrbHRRWVpuUHk0cEdqU21yQXlYeWJqM2d1TnNmRVhTay80NmNCeVZKRmpyZnpxQWhKWER2WFFzV21KZVRWejZySW92N0g0MGdCam5ZeWYzRytod083alNPdmVpOFpwdHphNTFibXp0dzBncHBKaFIranFxQlNDRnUxcFI3KzRpMXRabFMwc0QrUjE2MWpmazlFV3FnRjM5eTFIM2RtSHZQaGFvSnZqeVc5SXZidXhpSEtWUjEwZ1RGUDY5NkpHdEZSdzYvR1dlb3BXVUIiLCJtYWMiOiIwZjY3MTZkZTQwZTg5YzBkZDhhNDhhNzFkMTdlZjY3NWM1MmE2MTRjNzA2NzJjYWQ0MmZmYTgwYTIwMWI3NTBlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImUvU2NPcnhuWXFqOWErb3Z2eEpMWEE9PSIsInZhbHVlIjoibHlMVFNQdlU1OVZ2SGx0WW9YcDhuOG1CMnlML1BNRTlnN0lIQndnU2pkYitWWjhlN1pDTGkzRXAwK2VOdjlZS2VWNUlOMlRySVUwR1BXUmFCUmFpSFZKdThlQ3lPNm9McGxqSEduaC85dDZNd3g4dVRQUklsWHovblBwclhESG1rN2hMc3FMN2k3dVMwVGRuOHZrTmFTeGc3Nm04dkxtcENXcVdic1NCMUtabUl1MmNxd0tnUmhxK21WL1dNRHkvRTBGSzgxcVp1QlpDYTZseW1YWW5wbEVTUlptd3ZFcGxJWXl6M0Nub0RSOThPcG9LcnBOTDRjWDVoNVJzVk92NDVwVWhtVlhzUTVCcHNpcUlzb3RiZzUreWc4WWxKREZId1Z5SGdCWURMMHRnTlB0RnNPaEM5SlNhbXU4aGZGc0VIRGNCNWlJSHJsSHZGcVRuRjZadEc5WWJZdThJZDFZejl5V1ZQTDZBOGw2ZzJkd1loaVh3b1huSm1YMVB4V2xTaTc5ZEo4a0hhck9GSWJXQ1J6aENJMWJ3ZDFBeXF5U0xwL204K2R1cjQ2cDQ3TU5nYWIzMk1JYVJTdUMwOFBrY01lemF5Ujk5QWZSMlA2NW1BK01tL1ZwcDJENDdpaUhPNmNVN0gvYUhrbEw4ck9zZ3BuVTRMUHFOUVBDR2ZLR0IiLCJtYWMiOiI2MWE2MmM1Nzg0NDc5NWQ1ZTRjNGIxZmJlNWM3ZGY4OTUyYTRmNzIyZDU2YWJmNTQ2ZjZlMDg3MTMxYmY1ZmZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508601557\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2017899138 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017899138\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1243937990 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:08:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ2VCtsRWdPZlp3NzIwcGFkYkpGWmc9PSIsInZhbHVlIjoiRlV0Nlh5dU5pWGloUUwrRWJ1UWtBMjBTTDlXSkQ1QlVJV0RiUFJMc2g2bWtYL3c3MWpzY3M5aHFWU3hOTXdHZnlyc1VrbTY1WHZnYTBHQ3g5bkphdS8vWWFkNmFSUzlrMFlHMHJWMjNsZEJJeHFkenpsc2xsU2JQblpCc3B5NnYvZkRmaDZ1cFBZZGJlV0dMUW93QUU4NkpCeUhzamMzVjViNW5PeFJ5TnpKeHFQWnJyNEt6VHdnU3ZCV1BkWWhQOXRzUGNZc1JibWJ2Slh6OGJsblFOZGFGQnN3RTRzeExmV0FGdnRVcVRJR1Y3SGJwbVpRNmJJM3ZLT2x1cjJaVjVXVTVUT1pwUUxMcEhSaDhOaXZXOHNQY3ZmZDlIZjFoK0VDNVRFQXEvVlJwMFBucUFMSGVWVy9hUXM2NTN5Y21lZ2RXZytUaUNXOGgydHQrOGxtZFppYlVHaS9LR2s1NnJ5Yi9BM0JwWFJvZEdmM3E2VWJJWXBDZUJ0RHVLcERIR3pxaU55UW96L2doRHhUMk90cCtMeFZTaTQydDd0NHE4MXF0d1luTU4wa1RoTlBBbFBhYTFrWlA0c0VnYnZWVGN2TEsvcHUwYk9SdTRSMTlaSkhMbDJTZk9UQ3JiUGhiQ1JpUW54aWRXekVxcHkwWExwbnAzQU45aFdZNnd1Zk0iLCJtYWMiOiJmNTk5OTVjOWM3NWVjMGVhN2Y3MWQ5OWIxMmI0MDFiMDQ5Yjk4NTExMmU2YzQxMzM5MWQ4ZWNjNDQ0MThlOTJhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:08:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRHQUlNZ3FDTis3U3NWSzlBblRrQ1E9PSIsInZhbHVlIjoiYlZTeGhKbWhmS050eVJVV201ckloUUE4REZkVTBzOXM0N0lGL0xsbzdnK1h5K2dYemlmRE90Z0RsN2RUT1cwcHk2QUwyOWJuWDdPL1E1NlQ1UFhwd1A4bHZZNS9qTm05SENmZGVkZ1U2OTFHRmQweXJMMWZSbE1lUzF2Y1lLZ0VjNHBLN1VGRmFsc1gxZXRQWS94Y3hqcnZ6RU9hUnZ6V1BseVd2R1N1ajlOWm9OWHNGQUczczEzdXZTTTg1TlNOTm9WdFk2c1N2MkdJUXlGenBSLytWMllxUDduTUFQS2VsL3lKU1RWRE1IZTV4VGxycjNyblA0UWN4VlBJS1pBa0pBTUhnZldReXFYb1A1cDVURmtpemdmNUI5cTZvazczSW5oRDZkSUpGQWh6bm43dEFPcDlGRlgrcDBUY0ZxM0VWejRhZ2VlZkRjU2dab25jK24zZmIzRG1xbFVXSzBnM1Z4Ui9TVi8zUisxa1g3OHc3cEV1M1hZWWp4eWhmRzR4c09SR1cxUC90aEhndnpVY1UxMGE3SHhsbXFtTE56TkkzZ2QxRURBcnFSQzdKaGpvUnJidWhEVjRRTXI5WXIyN0pTaDQyRlBUaXhVQVl0b0RJMjl5UkJBY0xkeEVrR2JGZDRDWVBOSnFIYlRLWk5KaURUMHBEd0FBdmVWNis0WVQiLCJtYWMiOiJjMGIzMzYxYjViOTdhODc1NTg0ZmVkNjMwMDVjMGMzOWU0NmRhOTc0N2M4MTYxNDgyYmFkMGVkZjkxYzQ3NThlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:08:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ2VCtsRWdPZlp3NzIwcGFkYkpGWmc9PSIsInZhbHVlIjoiRlV0Nlh5dU5pWGloUUwrRWJ1UWtBMjBTTDlXSkQ1QlVJV0RiUFJMc2g2bWtYL3c3MWpzY3M5aHFWU3hOTXdHZnlyc1VrbTY1WHZnYTBHQ3g5bkphdS8vWWFkNmFSUzlrMFlHMHJWMjNsZEJJeHFkenpsc2xsU2JQblpCc3B5NnYvZkRmaDZ1cFBZZGJlV0dMUW93QUU4NkpCeUhzamMzVjViNW5PeFJ5TnpKeHFQWnJyNEt6VHdnU3ZCV1BkWWhQOXRzUGNZc1JibWJ2Slh6OGJsblFOZGFGQnN3RTRzeExmV0FGdnRVcVRJR1Y3SGJwbVpRNmJJM3ZLT2x1cjJaVjVXVTVUT1pwUUxMcEhSaDhOaXZXOHNQY3ZmZDlIZjFoK0VDNVRFQXEvVlJwMFBucUFMSGVWVy9hUXM2NTN5Y21lZ2RXZytUaUNXOGgydHQrOGxtZFppYlVHaS9LR2s1NnJ5Yi9BM0JwWFJvZEdmM3E2VWJJWXBDZUJ0RHVLcERIR3pxaU55UW96L2doRHhUMk90cCtMeFZTaTQydDd0NHE4MXF0d1luTU4wa1RoTlBBbFBhYTFrWlA0c0VnYnZWVGN2TEsvcHUwYk9SdTRSMTlaSkhMbDJTZk9UQ3JiUGhiQ1JpUW54aWRXekVxcHkwWExwbnAzQU45aFdZNnd1Zk0iLCJtYWMiOiJmNTk5OTVjOWM3NWVjMGVhN2Y3MWQ5OWIxMmI0MDFiMDQ5Yjk4NTExMmU2YzQxMzM5MWQ4ZWNjNDQ0MThlOTJhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:08:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRHQUlNZ3FDTis3U3NWSzlBblRrQ1E9PSIsInZhbHVlIjoiYlZTeGhKbWhmS050eVJVV201ckloUUE4REZkVTBzOXM0N0lGL0xsbzdnK1h5K2dYemlmRE90Z0RsN2RUT1cwcHk2QUwyOWJuWDdPL1E1NlQ1UFhwd1A4bHZZNS9qTm05SENmZGVkZ1U2OTFHRmQweXJMMWZSbE1lUzF2Y1lLZ0VjNHBLN1VGRmFsc1gxZXRQWS94Y3hqcnZ6RU9hUnZ6V1BseVd2R1N1ajlOWm9OWHNGQUczczEzdXZTTTg1TlNOTm9WdFk2c1N2MkdJUXlGenBSLytWMllxUDduTUFQS2VsL3lKU1RWRE1IZTV4VGxycjNyblA0UWN4VlBJS1pBa0pBTUhnZldReXFYb1A1cDVURmtpemdmNUI5cTZvazczSW5oRDZkSUpGQWh6bm43dEFPcDlGRlgrcDBUY0ZxM0VWejRhZ2VlZkRjU2dab25jK24zZmIzRG1xbFVXSzBnM1Z4Ui9TVi8zUisxa1g3OHc3cEV1M1hZWWp4eWhmRzR4c09SR1cxUC90aEhndnpVY1UxMGE3SHhsbXFtTE56TkkzZ2QxRURBcnFSQzdKaGpvUnJidWhEVjRRTXI5WXIyN0pTaDQyRlBUaXhVQVl0b0RJMjl5UkJBY0xkeEVrR2JGZDRDWVBOSnFIYlRLWk5KaURUMHBEd0FBdmVWNis0WVQiLCJtYWMiOiJjMGIzMzYxYjViOTdhODc1NTg0ZmVkNjMwMDVjMGMzOWU0NmRhOTc0N2M4MTYxNDgyYmFkMGVkZjkxYzQ3NThlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:08:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243937990\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1691262337 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691262337\", {\"maxDepth\":0})</script>\n"}}