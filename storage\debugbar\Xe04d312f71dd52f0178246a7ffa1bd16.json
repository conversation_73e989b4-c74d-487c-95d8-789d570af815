{"__meta": {"id": "Xe04d312f71dd52f0178246a7ffa1bd16", "datetime": "2025-06-28 16:00:54", "utime": **********.097518, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126453.629947, "end": **********.097547, "duration": 0.46760010719299316, "duration_str": "468ms", "measures": [{"label": "Booting", "start": 1751126453.629947, "relative_start": 0, "end": **********.036296, "relative_end": **********.036296, "duration": 0.40634894371032715, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.036306, "relative_start": 0.4063589572906494, "end": **********.097549, "relative_end": 1.9073486328125e-06, "duration": 0.06124305725097656, "duration_str": "61.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45710096, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0030099999999999997, "accumulated_duration_str": "3.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.064738, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.106}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.075608, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.106, "width_percent": 15.947}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0811372, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.053, "width_percent": 15.947}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-299021962 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-299021962\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-67960101 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-67960101\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1034827431 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034827431\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-133328280 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126449611%7C4%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNGTUFXL1k4ak94MWZzZVZET1JsOHc9PSIsInZhbHVlIjoiR1F0NTZxOG5TN01JUUhRUlJCUlJSVmdTZjUxU1dSTXNMajhZRTBHd3lUanZYajFJbHdUdWh2dGhkeFNXeUdkYnhxcG1YMG1PWk85QURFOFZ5UzFhb1J0SEUzSzZjNitmdlpjTWJYSXVJSjFYdGxyVXQzclpkemx6NmFuN1IwWEovSDdTQmdyVTJIbG4vQnRUN3FaVlpOY2tSY3Irb0R0L0Q1WjFtRHU3b0Y3T29jd1VJc3NyejE0SXlmR2xuT2VlUmdoRlhjVUh4YXhpRnlab3VkeEczZXN2RUd0cTBBV0dGUzl4RVFXVTN4ckY0TE9VL0t5ZkhabFRzd0dIMkMrSWtVMk94OTc5OG1mMTR6ZXVtRUJVSFNlYi9CQkpJVzhxaTZ2WVBDeWhiT3lUSmcyT3BqODdUM3JQVFRDR0s1SlFFU0ZlZXcrY2RHa29sTGhSTk90RGFIWHlpaHNaVjdiYmpEdHJZZURaK0Y1ZDFlRFd3cDNidkJPS1ZwUWozdUh1ekxtUEVPRUxIZllTUnFxbHJ3bm8vOVkwb2RaL1V0ZDJjb2J5VDdhTTVabENENENlZ2ZYNU5pN2NBaGNWUytPZGRMdE40YlpEN3QxSW5WT3c3UTY1VlcvTzBBd2xVZUVMQkJ0ODBXU29GdUZZOXd3cTZEaTZTUHB4ai9DVjBPdHEiLCJtYWMiOiIxY2ZiZTg5ZmMzN2U4Y2U2MDk0NTY4OTk0ZTI2M2JkZGUzZjE4NDA4Zjc3OThjN2I0YzAxNTc3ZjNiNDZlZThiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJsTzVsSnFERzAzejBmMFFjblFSM2c9PSIsInZhbHVlIjoiaVZzOERQRHR6cXB5ekY0dG82eXY5YjR6a2FlbW9XU2FXRm51VlYzUDhVeWlCTGFMWm9uLzhxLzFwSHozWS94YkJaZXk5WWhPRTBGWnI1MmdObTdLRHd5dThoa2NDSlpiMkU5Zm5QOW55NE1rTmU3NjhVRDcwbStPdzFxTGhVbERWYzhMeDBJZDVhU1VIM0JBaUZjdkRiM2FGUTluYkpnZW1seVUrQTNBSWdCaDk5TXUvUmh6a3hnOGFoTUlKYnRaOXYybFBzSGQ1K1lWNnArMW1MZFh0OTVZTkl5eUY0aXAvTkhXdGY1ZlVxSnVhd2ttcllKbG85K2RMK08vQmp5Nllsc1V3Q2Z6QlFLNGlrYVBoT01Zdy9tK2JQdmg5Ym9JMGdRWXBrMXZmRXN0TVVUanc3YUs5UGptdjlIWDFRa3BSQ1lFMlNJZUk2RnpsM0xtUGZBQWRaVGUzZHl1MjFCeXlkTFVqeW9DK0ZsYW9sZ0xFTG9Tb251M3V2Tnc0NzM1THdrbnFTaDUwSm53MVRmai80b04zZmk2b0p6cGhmUmwvT1htY3Z5TVJwOGkwSEc4UFNQMCtlZHhvL3lOZEJiR0J0N0NYdk9MUm96bkVna2dLMU84cHVESG9kdXptY1dJNEx4V0poRjhsL2t5dDlZVy9ZZVBFaFNuaTJSZ1hzK0QiLCJtYWMiOiJkNTYxMDY1Mzg0MmEyNTFkYzUxZjM0YTU1ODI3MmZiMjU5NjY3OTBjMjY3MmU0Y2VmZDExYWYxMGU5OTBlMzNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133328280\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-328648212 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328648212\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1938131720 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:00:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBVRWUwb3locXFWL2tRWW5BL1F2RFE9PSIsInZhbHVlIjoiMG96ZDhqYXNBN0poUnYxN0dNZ1VHQllDQ0laNzFrTTFObGtlZnRQcTkvbk1OZHB6Zkp3ZHlGU3VVZHhiUWdsMEdPd0R0NlQwZTdTOFFDMWRTbWp3UEdtUEoyNXdMa0lMUlpyS29HNGJ6K0dWR3BaUE9taTlZYXhvaVlZKzJUakFGTGk1RXhZeEIrWG1ZZWNVbThISmF1NFgyMXRMcENVemg3Y3R4cWl5dzdVT1lXaVY2cFc5Wndaa1pOVUVaakkyS0pqMVA1TDNBY2tyaVJkZHB5ZWJMTGt5dkYrR2RYZlIydFBRMUhCM3JOTmN0QktGQ1lZRVR1cGNwakRuTmE2NDdsL05ONlRTcGZpWTBhZWlGaG00NWVpZS9kRmlNTjlHaEwzejVON0hvNno4TG52MmY1Mmk2VXpGV09kVGVEbFpRdVJ2VllDRzQyZTlRMEZrYVFkUFFmVzVRV1gwc2JlcUxZQ2U4bDhpUFpkRWt4MEVKM3FqZnRsUzlrRUdaUk01MDl5aGR1UnNpUElNNGxUNllRTzNCWlN0bkV5bzRpN0JzRDFxc2lia09OSG1RQTdZcWg3ZnNtcGNjOGU0aG12UXpLbDZ2T043RTVSYk13SFdkdEdSSjkvcGVHRUFmb3RjU3F1QzlEVUcrZ3RIc2t4N3J5ajlYTExTcDg0RThCRjciLCJtYWMiOiJjNTdlZjhmYzkxNzRjOGI3ZmM3NTc1ODExM2U0NDg1ZjY2YmIzZWIwN2ViMmRmMmZlM2Q1ZGI3YjQ5MTgwNTNhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:00:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjhUNEpFeS9CYzFrYWF3cmkzU2dqaUE9PSIsInZhbHVlIjoiOTNPOWtjY0dqSkl3WElWdUIzdXI4WEFRYmZiYkFMN3ZNbkJkWEFCdWRDd0xFdjBzM3hjR3B4dHZrWnpMZEdHcVlQZnFGTk40NksxYjFheTYyenJCeWtiT1NOVzBObU9LQTQ2bjZoNDdlK2Zsb3ZGQ1lxMzRxK3lWK01UK0NCOGd5b0lCcVY4MHNBNVJSb3NKSGRPYzdKMS9OZ1ZneStjbTFoYzJHQ0VmcW5uMU1oVXBMV1VzQkNhV0UwTS8yZVZmMVBaRDF1aDByVWhGa1pUajZzOHRYTVBwNXJJaTg2NGthYnFsbE5taUx2MWtxOXJEWVd3SkZ3bmE3Z2h2UTU4RDd5bUxocDRMV3RmeUc0d2JvTlcyeCt3cWIrWGZTZlVrV0FaNXpWWjQ5L3cvZ0d6OUZHVjlTeHVRYlBWQnlYSVhQbmkwQ2RNU2dDSVhDd3NHTzNRVjNxcGNKNlhiWUJpeml3QVI2NENKTDBQRWRUdG42V0RkYmRvV1lWSnpUMGpYU1BreXp1OFE2V0pDZEtkbnNmTzl2WW9MV2ExdlRncjJKVTArLzNsU3NkUE9mRXlBNHRTQjhSOW85TG0yZ0Nsd1Q4Zy9XYk9sVEFJeU9OVTR2SHZveTNvOTZQV2RkazBvVEhQUEQyVytwSlNrYnpMcVZtQ04wa1dRVSs3WXlQWTUiLCJtYWMiOiJhOWJkNjFiOGFkY2Q5NTg0MjU5ZWNiYjFiZjBjMjVkMTZlZTBmNDg1OGIyMzUxMDAwNTgxNTM1NDI5YzJhMTgwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:00:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBVRWUwb3locXFWL2tRWW5BL1F2RFE9PSIsInZhbHVlIjoiMG96ZDhqYXNBN0poUnYxN0dNZ1VHQllDQ0laNzFrTTFObGtlZnRQcTkvbk1OZHB6Zkp3ZHlGU3VVZHhiUWdsMEdPd0R0NlQwZTdTOFFDMWRTbWp3UEdtUEoyNXdMa0lMUlpyS29HNGJ6K0dWR3BaUE9taTlZYXhvaVlZKzJUakFGTGk1RXhZeEIrWG1ZZWNVbThISmF1NFgyMXRMcENVemg3Y3R4cWl5dzdVT1lXaVY2cFc5Wndaa1pOVUVaakkyS0pqMVA1TDNBY2tyaVJkZHB5ZWJMTGt5dkYrR2RYZlIydFBRMUhCM3JOTmN0QktGQ1lZRVR1cGNwakRuTmE2NDdsL05ONlRTcGZpWTBhZWlGaG00NWVpZS9kRmlNTjlHaEwzejVON0hvNno4TG52MmY1Mmk2VXpGV09kVGVEbFpRdVJ2VllDRzQyZTlRMEZrYVFkUFFmVzVRV1gwc2JlcUxZQ2U4bDhpUFpkRWt4MEVKM3FqZnRsUzlrRUdaUk01MDl5aGR1UnNpUElNNGxUNllRTzNCWlN0bkV5bzRpN0JzRDFxc2lia09OSG1RQTdZcWg3ZnNtcGNjOGU0aG12UXpLbDZ2T043RTVSYk13SFdkdEdSSjkvcGVHRUFmb3RjU3F1QzlEVUcrZ3RIc2t4N3J5ajlYTExTcDg0RThCRjciLCJtYWMiOiJjNTdlZjhmYzkxNzRjOGI3ZmM3NTc1ODExM2U0NDg1ZjY2YmIzZWIwN2ViMmRmMmZlM2Q1ZGI3YjQ5MTgwNTNhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:00:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjhUNEpFeS9CYzFrYWF3cmkzU2dqaUE9PSIsInZhbHVlIjoiOTNPOWtjY0dqSkl3WElWdUIzdXI4WEFRYmZiYkFMN3ZNbkJkWEFCdWRDd0xFdjBzM3hjR3B4dHZrWnpMZEdHcVlQZnFGTk40NksxYjFheTYyenJCeWtiT1NOVzBObU9LQTQ2bjZoNDdlK2Zsb3ZGQ1lxMzRxK3lWK01UK0NCOGd5b0lCcVY4MHNBNVJSb3NKSGRPYzdKMS9OZ1ZneStjbTFoYzJHQ0VmcW5uMU1oVXBMV1VzQkNhV0UwTS8yZVZmMVBaRDF1aDByVWhGa1pUajZzOHRYTVBwNXJJaTg2NGthYnFsbE5taUx2MWtxOXJEWVd3SkZ3bmE3Z2h2UTU4RDd5bUxocDRMV3RmeUc0d2JvTlcyeCt3cWIrWGZTZlVrV0FaNXpWWjQ5L3cvZ0d6OUZHVjlTeHVRYlBWQnlYSVhQbmkwQ2RNU2dDSVhDd3NHTzNRVjNxcGNKNlhiWUJpeml3QVI2NENKTDBQRWRUdG42V0RkYmRvV1lWSnpUMGpYU1BreXp1OFE2V0pDZEtkbnNmTzl2WW9MV2ExdlRncjJKVTArLzNsU3NkUE9mRXlBNHRTQjhSOW85TG0yZ0Nsd1Q4Zy9XYk9sVEFJeU9OVTR2SHZveTNvOTZQV2RkazBvVEhQUEQyVytwSlNrYnpMcVZtQ04wa1dRVSs3WXlQWTUiLCJtYWMiOiJhOWJkNjFiOGFkY2Q5NTg0MjU5ZWNiYjFiZjBjMjVkMTZlZTBmNDg1OGIyMzUxMDAwNTgxNTM1NDI5YzJhMTgwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:00:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938131720\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-651638621 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-651638621\", {\"maxDepth\":0})</script>\n"}}