{"__meta": {"id": "Xb93b9c09c64612d221bd5b292820ba43", "datetime": "2025-06-28 16:30:19", "utime": **********.976667, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.558436, "end": **********.976684, "duration": 0.41824817657470703, "duration_str": "418ms", "measures": [{"label": "Booting", "start": **********.558436, "relative_start": 0, "end": **********.924397, "relative_end": **********.924397, "duration": 0.36596107482910156, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.924406, "relative_start": 0.3659701347351074, "end": **********.976685, "relative_end": 9.5367431640625e-07, "duration": 0.052278995513916016, "duration_str": "52.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45874768, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0046700000000000005, "accumulated_duration_str": "4.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.954988, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 32.548}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.965079, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 32.548, "width_percent": 10.064}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦 المخزون المتاح: 2 قطعة</div>%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦 المخزون المتاح: 2 قطعة</div>%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦 المخزون المتاح: 2 قطعة&lt;/div&gt;%", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦 المخزون المتاح: 2 قطعة&lt;/div&gt;%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.967808, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 42.612, "width_percent": 57.388}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1449689174 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1449689174\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"75 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;&#128230; &#1575;&#1604;&#1605;&#1582;&#1586;&#1608;&#1606; &#1575;&#1604;&#1605;&#1578;&#1575;&#1581;: 2 &#1602;&#1591;&#1593;&#1577;&lt;/div&gt;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1562595923 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">216</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128218636%7C47%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlgxS1JUTFU0bGdBbWlnaDk2TDlKdGc9PSIsInZhbHVlIjoiRDkrem84RHNIZmNBbnI1NGpuQ25UcDZ0Q0dGN3FzMzI1MGtFbUpESUtsQlBNQTVCZnpleDdEWTM2V0tUeW9EWXVNYXZTRjRTRCtzWFFqV2hXZHdiZXVwV2lEeGllMDFjSCtCa1FxVi81cVkxbU1uYkRydVljTVBzZ3VSd2I1Q1ZhQ2YwR3gyQXNMV3NJZVd0RmhvK3RyZTVuYTkybmEwcEdrZlBmY2NOTURGZ085VHliZjJZWFkxb28zZlRwUlJ6aTlkWnROTWJVSTBaMHNEa001aDAvaERpVVNKU2lDMk1udVhVTGtuT1VLVFRzd2xzRXhCWGYxQnRZMU5SRUZxM29TZTBnbk9wdktWYldjRjZzWWxGSnRIMHp5bktmYVpGYVhmcnZqZmVwQ1h1b0ljaytiUHdQQm1UMXEvMU1hcGxNK0VoSGFNYTFGUXUzdFdsekNxMlUydzIyamhlSWoyK1pTYU0zNlRaYmx5bDNBSUxjUXBIRTJ2UDFxS21DL1JMekROYjlkRjVwdkFhY0NJT3VrOWExeVNyY3BpV1pVaUJKRGY1eG5KNmt1b1VXQWQrQ3M4b0IyZE80MTNQRHkvOGQ4bHhxVnBrTm4weUlqMmZMNU9vOEJaZ3dEZVNuY2QrNnlxMHZab3p6ZVY5dlZ6OVJJNGtrTEFqY2FDNjJaQUoiLCJtYWMiOiIxYTU2OTdmYTRhZmUyNjk4YjIxNDE5MzEwMzgyOGExNDVjMmQwZjY2OWFiMTRlZjgwMjdlZjExMjBhYzk2YmY2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVmY0Vyejd3VC9XU2JhL2VneFRoZ1E9PSIsInZhbHVlIjoiMkZRdS9ScWFkVGJKQWV0TkYybFdEZDJYZDM5Z0x5akd6TjBRMU1NZjlLc0pOaE54d0hJUzJ5Nlk3TitIRk5EajUxekpwbWJCL1o5NzRwZVpQZmd3RXNIUVdNQmUyU1NIcnNUdUQyQUg3TGNYUXdETUs0WGpZWXFUWVhIODJMaTRRUWJGeFZXZ2grbDRzWWxvWG96SmZMUmRxVnhFWHJQcUUxbnNiZGpPRE9QMWF2WHREVXZNL0FHbG1DVTNjcERZS2d1YTlmb3dEeWdQa2FONVF6dXExUE5UVDE0RVI1WXN2cExMaGhtemxUVlJDaHBvOUx1ZEJpRDdxdFkyTUpQamM4akRpY0FNSncrcmZ4SkROTlY0Ymxib21vblZ0N2RvOWc5YkdLbU1jbWtiWlFkb1c1WXllMFUvMVllTnpHUFZIWUFFeGhYVU5jOUpmWXZtZlRSOFNDb3RXSEZLUDFaRU1qRnU3M3l5MUhWTzJVaGIvNlVtanJhRG1rUmVxV1pQWmNEano2Z2J3UDVYeUdWREVUS0VHOWdtYXFLSkxldE5halhiNkN6NDFMZDNzRjhHZThlZDNBbGdZcjc5bHlyNzBjQlRuUE9BMGpoV1p6czVHYUNMa1JwVTlvcDRwMnBKQjdpanN5S3VFT25oUEMwNWQ3c0IrbG9ZRzFxQW1JZisiLCJtYWMiOiIxYmExZjAzZjc0ZGY1ZTBiZTUwODZmNzFjZjhkZGQ1YzVmM2ZhMzVhOTZhMDhjYTM3MDFhNzcwYzllNzNmMDAzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1562595923\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-715787879 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715787879\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-153252298 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBmbUtiTEVGMTc0SWE3ZjRVZ3BaWVE9PSIsInZhbHVlIjoiL3NuSWVtbXN5QXpvSzBGYXFXRG5ML2VyNGttajhBMzN4SzRpWmNjV3ZQRTc4T1lIMWlYUmt3QXlwSGRFRXJUU3pBTVBzTmpnQnFQMS91alIrM2hvY2hvUUtoUGlTZUtOZ2x4S2xuMTlwOFRSM2VHSDFJZVF0ajV6REJkQjJHeDdub1J6cjVQaVU2M0xFV1NyWVBEeFEyOEo0VnRwN0prRDV4VmxuMk04ZzN5MmhnWGx0YVRENXpjbldkeEhPdktZbGdkSnovN0JsbC96UHBzUUJrOUp6K1RWVUlEclBmS3pjMDAzRzI3b2EzblN2bUdxRmpTOXRmeVIvRFBHRmZ3eVJoR3F1VUJIYTkraHZrbk5PdjBNRGVqTERQSHljWGJ6dENQSEdGMEFlUHdJTnlYdUV3MDFQQXQzSzZuc0UwUWVOdEhSdTVlaHRzZHdpOFNJaVZUU3M4L0FpUG1LNG5GZTFrYWF5U3JpZjN5Qmt5UnhWNWNUTHgvdjlKVUxmQzgzTGdkcXBCTkhLVWpPT3ArMWx3eDRJWUY5MjNMRGx0U3JzMVduYWVDcFJGaDl4N1FKdTlGZnoxcXJDVVNHOFpQWHFxeDU5MDRyTDk1cWVodFg5Zm9PYlA1TWRmeGxsU3A5SEpJQm1rSThhMGw0RDZndENyelhoMk54cm03S2JOcUsiLCJtYWMiOiJkZGI1YmE3NGM5OTI4NDA4NjY3YzQwNzI4NjE2MDRkZmFiM2FlZWUzZjllMzJlYTA3YmQxMTMwODdjOWQzYzYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVvSVhhMzNsZ0R2VnpsYll4YVErV0E9PSIsInZhbHVlIjoiRGJxczBRTVNIbDR1RndDR3RkZUpkaDlQM3FoQStYaS9NS2FuNGFpSlFnYXBIbnRkWDdZTEpFNHJlZGp2T3FQMVJYSTAzNVc2NWxFVllhcktZRmttcmhKSlNmdEtmN0xFcTdrcjRuOVR3cGswWGlLbklVR3NCbGR3T0JuT21yTTBPdUJtQTB4L0xnNWlXTmV3TVNZRm1LOURudkJjQ2dWOVp4eThGcnc3UFZaOEdwNGJuU3FqQ3lPU0VxUTcrUmpnWmlvR3pQYzVvL0pRR0JMYlFHcmNrSHcyaU5sQUtwbFNvQVorQ1c3OFdtaGhTZVRCbk96M3JOWUU1UWQvMDl6YVRNdS9OOGZaaXVNMzhzV2dPQlJJei9RbVZpcVNCWVE3TmtzTDkrYXhjTjlwdW5DdDhRcUMxWHV3YWNoUFRVdDd5V0ZERzZqcUR3T0FuLzg1Q3BqVk4xM2FzSUwzdGZDSHFHVStMQlZkYjFyYzRrVzZieUM4TitPdXpKbHA5d0xmNmlXUDF2RHErWjBoRVRMcXB1NFNoZVJkVFI3OGlCRE9vZUMwTmtJelkxbnkyQXdpSGdLcElnOU5CQXVCSlp3dnpkVHZGaUNhakVTQlN6REJ0d2dtRGlPdWx2aStRS3drM0lxTmZzVFM3RlRYZm1pY25kcHliVjZUWGNSS1VaRXIiLCJtYWMiOiJmZmE5ZTU3Zjg5YTRmYjlhNzk2MDA2Nzk0MjY2NDhhOTAzNzkwMzVkOGYwMDU5ZDI3OTc3YWQyOTY3ZDM3MThhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBmbUtiTEVGMTc0SWE3ZjRVZ3BaWVE9PSIsInZhbHVlIjoiL3NuSWVtbXN5QXpvSzBGYXFXRG5ML2VyNGttajhBMzN4SzRpWmNjV3ZQRTc4T1lIMWlYUmt3QXlwSGRFRXJUU3pBTVBzTmpnQnFQMS91alIrM2hvY2hvUUtoUGlTZUtOZ2x4S2xuMTlwOFRSM2VHSDFJZVF0ajV6REJkQjJHeDdub1J6cjVQaVU2M0xFV1NyWVBEeFEyOEo0VnRwN0prRDV4VmxuMk04ZzN5MmhnWGx0YVRENXpjbldkeEhPdktZbGdkSnovN0JsbC96UHBzUUJrOUp6K1RWVUlEclBmS3pjMDAzRzI3b2EzblN2bUdxRmpTOXRmeVIvRFBHRmZ3eVJoR3F1VUJIYTkraHZrbk5PdjBNRGVqTERQSHljWGJ6dENQSEdGMEFlUHdJTnlYdUV3MDFQQXQzSzZuc0UwUWVOdEhSdTVlaHRzZHdpOFNJaVZUU3M4L0FpUG1LNG5GZTFrYWF5U3JpZjN5Qmt5UnhWNWNUTHgvdjlKVUxmQzgzTGdkcXBCTkhLVWpPT3ArMWx3eDRJWUY5MjNMRGx0U3JzMVduYWVDcFJGaDl4N1FKdTlGZnoxcXJDVVNHOFpQWHFxeDU5MDRyTDk1cWVodFg5Zm9PYlA1TWRmeGxsU3A5SEpJQm1rSThhMGw0RDZndENyelhoMk54cm03S2JOcUsiLCJtYWMiOiJkZGI1YmE3NGM5OTI4NDA4NjY3YzQwNzI4NjE2MDRkZmFiM2FlZWUzZjllMzJlYTA3YmQxMTMwODdjOWQzYzYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVvSVhhMzNsZ0R2VnpsYll4YVErV0E9PSIsInZhbHVlIjoiRGJxczBRTVNIbDR1RndDR3RkZUpkaDlQM3FoQStYaS9NS2FuNGFpSlFnYXBIbnRkWDdZTEpFNHJlZGp2T3FQMVJYSTAzNVc2NWxFVllhcktZRmttcmhKSlNmdEtmN0xFcTdrcjRuOVR3cGswWGlLbklVR3NCbGR3T0JuT21yTTBPdUJtQTB4L0xnNWlXTmV3TVNZRm1LOURudkJjQ2dWOVp4eThGcnc3UFZaOEdwNGJuU3FqQ3lPU0VxUTcrUmpnWmlvR3pQYzVvL0pRR0JMYlFHcmNrSHcyaU5sQUtwbFNvQVorQ1c3OFdtaGhTZVRCbk96M3JOWUU1UWQvMDl6YVRNdS9OOGZaaXVNMzhzV2dPQlJJei9RbVZpcVNCWVE3TmtzTDkrYXhjTjlwdW5DdDhRcUMxWHV3YWNoUFRVdDd5V0ZERzZqcUR3T0FuLzg1Q3BqVk4xM2FzSUwzdGZDSHFHVStMQlZkYjFyYzRrVzZieUM4TitPdXpKbHA5d0xmNmlXUDF2RHErWjBoRVRMcXB1NFNoZVJkVFI3OGlCRE9vZUMwTmtJelkxbnkyQXdpSGdLcElnOU5CQXVCSlp3dnpkVHZGaUNhakVTQlN6REJ0d2dtRGlPdWx2aStRS3drM0lxTmZzVFM3RlRYZm1pY25kcHliVjZUWGNSS1VaRXIiLCJtYWMiOiJmZmE5ZTU3Zjg5YTRmYjlhNzk2MDA2Nzk0MjY2NDhhOTAzNzkwMzVkOGYwMDU5ZDI3OTc3YWQyOTY3ZDM3MThhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-153252298\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1277429761 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277429761\", {\"maxDepth\":0})</script>\n"}}