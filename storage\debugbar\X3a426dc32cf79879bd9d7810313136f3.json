{"__meta": {"id": "X3a426dc32cf79879bd9d7810313136f3", "datetime": "2025-06-28 16:01:08", "utime": **********.275546, "method": "GET", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126467.792106, "end": **********.275562, "duration": 0.4834561347961426, "duration_str": "483ms", "measures": [{"label": "Booting", "start": 1751126467.792106, "relative_start": 0, "end": **********.126272, "relative_end": **********.126272, "duration": 0.3341660499572754, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.126281, "relative_start": 0.33417510986328125, "end": **********.275563, "relative_end": 9.5367431640625e-07, "duration": 0.14928197860717773, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52312464, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.202721, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@index", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=33\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:33-84</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.004569999999999999, "accumulated_duration_str": "4.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1609201, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 40.481}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.170964, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 40.481, "width_percent": 16.63}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.174988, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:37", "source": "app/Http/Controllers/FinancialRecordController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=37", "ajax": false, "filename": "FinancialRecordController.php", "line": "37"}, "connection": "kdmkjkqknb", "start_percent": 57.112, "width_percent": 9.628}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.187953, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 66.74, "width_percent": 21.882}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.19069, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.621, "width_percent": 11.379}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 3, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1937471702 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937471702\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.194424, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1573121584 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1573121584\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.1956, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-719819677 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-719819677\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.196351, "xdebug_link": null}]}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-1214290532 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1214290532\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-250602466 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-250602466\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2022367870 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2022367870\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1415931409 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126465405%7C6%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitReE1LVzNzdmF0S3pVMkJ0c1o1Qnc9PSIsInZhbHVlIjoiVUZwTFF1Tlo2WjBDTHN5ZXpHOVhTTmxHNmQzaThNeDV6N29lT2gxU3QyMzVrSVoyV3pCT1RuNDI0bWkybGtSaWFOb1pweTRiRXJWZC9nc1A3d1diSDJ5OTk0dUtFQkplMm1kQlFRbW5NWitzWlNVVVYxdWdGSVFqNkk0bWlLbTRvOThEZ2xoT0VXeHhaV0plUHZVR3g3bkZsMUdyVjlBS3ppMDZ3NmhJWWw4TE5ROHhHLys0OTRaOGwvTmRrYXFjQU8xdzk1NjZ1YVZVM0pnUEliQUhnTTFtdFZpUk1aYjRFaGhMaGpzTnZ3MWlxWHZpMFNjRjJHbTZ2aXM3dUFLV0FHeHpHakpZclV3RlVzSTl3WmN6SDMzV0hEZTJCbnRoTHlrWWFZbmcyWTU2WmJlQ2xJdVRaR1RrV0dHcjUrUWRnTFd4VTQ2K3hIak50M1RWNGpyeHRISnp1UWsrUDNtbVFuTDhUdHNqcElxYVViRzFKbldBQlhrWCsyZEM2YXdOTVQrS0JUQUJCM21lbG5vL2dGSTRBQUJXOGlrNlFDaGpGanRkeHJ0cVVUeUJvcHIzc0oxTmVrbTl4c0dOcCsrMHozbTJ6YjhrazVBRVFaaEpFVFp3dGJHVGlMZTNvdThWa3UycjlBVWF0RVdYVWRRczZMb1NRQVVwYmVVRGgwUGgiLCJtYWMiOiJlZGM5NWM2MjVjZGQ1MzRmNDk4NzQ3NWY5OTc4YjQwZjQzNzgyODJkNzA1MWJiYmEwODIxMTZjOTA2M2IzNWE3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5RVU1OemJFbU1RVWpmekY0TVVFaHc9PSIsInZhbHVlIjoiV2FjUXFndTRTeVhZekxNQmFMblhsbkpXWkg0dlRDeVN5L3VmUHQ3aXlheWlqdG1XSURLZnVUOStJOFpnVTU1d1M5RytOSnJWcGZqZFdOY0ZDN3hoOEFObGtjWnIrVGtlS0RhTmNQVVdiTUhoS21tekVSZElEbFU0TDNCc2xHdE5OU3c0UGl6VjFLcGl1WW9oclZOY1Nrbnd1ZTlSN3R5alE5VEUyR0VtUHNET0Nha2c3V0JyVzFlYWlRMlFDK0tuZzNTNUxkdXQ5Y3daTlkxS3FrMUFwYlJETXRac3dwbmpldmRXeVVqeGozMEd3Z0ZTM2o3U0ZzTE05dGZnTzAxejJhWVlnWmR0MERmUFA3dVdIc0c2V2F1QkNWaVh1elpHei9LWmFYVElKcXgyWUdGa3h4ZnpGNER6Y1JJcmZJTFZuNXlYRE8xUDJpWDVlakxEWUxmV05jKytWaXUvWTV6bERVTlpiejVGR1hZZ2MzRUNuVCtjQ2c4WU1acFZiWVJoNUdMdXdhSm9RUlRLYnVqT292OThXQ0NEbGYzQWtDZWowWGlQQld3Ty84TmhGa1ZnLzBkRjJQT1NTUUdyTzdQUDRKOC93RXZLVWlMVE1KVHUwWXBrQjJKZTVFT0hHaEE3eVVvckdOdTVPSyt6ZG1UbUJIZXhqaDJXVVhyOFd5bFUiLCJtYWMiOiJkMTNhYWRhNDg5YjY3OTI2MWU0MmM4YzNmMzUxNzI1YjU3NWY2ZTc5MjFhNjBlZjNmMTNiOGI5ZDU0NzM5MjExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415931409\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2098105181 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2098105181\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1HMGJ1anhKblV0NkwrQ241Q2t6U0E9PSIsInZhbHVlIjoiRG9QQVFjanI3Z3ZNWjJxWkhENHBzVk9odDIvZ3FqYnliWHRXTmRLT0I4eENLbTRPYTVIQ3hDZnhLVkpKWGJtbzRmSFAzL3VhWkl1bGQ4ZTd5czBKamRXTm9VT2lWdjlCMDVrT1BycmtPSjhvZERiQXg4YmlablliYXc2cVZVUGhGWmo3N3FUcEZPSlFaOEVtdk5ubFg2RW9kRkpQZEl5WkZ2b3IrR29WVi9XaTVFWTRmaW9aVTJibThDWFdOVmNHbHpHWU1zbHMwZkpLMUFjbUR3QjErMUZFdGdkeDJvaERmdHVobXRxNHk3SmxBK09BZmNReWFnWmZtdXRaeHpMbjdWTENITk9yTW8wdHRneGxPRHJLTUZRSVhWWXBINDF5OVFjTit3bmlYTXROdFJkZHFRNVpDdEt1dlVFbTU3OWg1YlNnUjBDdDdrTmtkcFEzTGxpUjY0dUxnczdaQlczZmRPcVBGb1Rubi9HTkFQUHhxZjFwRnZyQ2ZjbDlNaVU2cm10Z1dYWElhWHRHcGkrVWJZL3dkc3BrOTBTYTZYZU1tSy9RZjhXNU9ZSXRrQlk1azRMR0x0WWN0ZWI3ZlVUcE5tdS82ZlkrM3hNZlFHajBvbWlDS29yZzRvRVFNQWJ0RXZVRUZvR2JHSzRrWXk1bnpBNmVTNHc1bG9xSzFDcmsiLCJtYWMiOiIxNTczOWMyNTAwMzMwODRhNmM3MmE4OGFlMjNlMDVhYWM2YTNlNzI5ZTY3Njk0MDk4YzhmZTVkZDEyM2NhOWQyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImM0NDU4UFRSUDV1K05lUWI1L3dPcGc9PSIsInZhbHVlIjoiN0ZIY3d0SEx0aWRyWHlLWXhjRWVaR1pQQjkranBjVE9HbkpKbTErMnc4ZUpsaHhRY1N2V2xUa2tsL3hRT3QydUFFRTE5TFN5T3RpSHlkbzk5NkNvdTBvU01GSzJSVG8xZ2VRTE9pTkp2M1ppRkpMT2tKbXN6ODNVdWRWWVdzMnZUMlcySWQ5cUNjNDNRemEyaEZDemxRdXVzS256OXR5ZUhqUGtsKzJVVitZQnZMVU82TDg5dzd3aElmcGpCWnJsakN3NlRpR3ZFU2N2TEdCaWtpOEs4dE9ZV1ZiTmhBSEJJVjl3NG1WUTl2K3ZYQ2cxTnJHM2hnZ0NaWDJHRm9ucHEyNWlZVkVuRUdwSjYyQ3czNGl6OTlTOFg2a3Rhd09ZTTFuMGtXYVZoWWtWVXNBbDZ6aUV1c09xQTRrNzhGcWt2TC9Vd3NsZzQ4Z0Fhc0hKc3JteTJiQU9KQ3pCSkYzMDEwNnp4TmVJcmNiMm1DaU9zUFZuTzFBNGdPMVZLYzNnaDAyUC9pSCttVExyaUpvcjNBM3U0YUNlQ016VEttL1R0K29KeHZFclpGVlIxTlprS0FoZ3VLZ2IySjg4bG50eEJjQ0hMQnp1c3hvRWVxdGh1SER2VzVNbHdZRDd6ZXpXS3FucFJ4ekQvNFllWkxNb0dDQytBUUtXVGVGYVJsS2ciLCJtYWMiOiI3MjdmNzY1NGRkYWUyNjcyZDI2ZGRiMGE2Yzg1ZTMzNDBhNzk3MmMxNTI3MjdmNzc3ZjY1YWQxZDJiODcxNjRjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1HMGJ1anhKblV0NkwrQ241Q2t6U0E9PSIsInZhbHVlIjoiRG9QQVFjanI3Z3ZNWjJxWkhENHBzVk9odDIvZ3FqYnliWHRXTmRLT0I4eENLbTRPYTVIQ3hDZnhLVkpKWGJtbzRmSFAzL3VhWkl1bGQ4ZTd5czBKamRXTm9VT2lWdjlCMDVrT1BycmtPSjhvZERiQXg4YmlablliYXc2cVZVUGhGWmo3N3FUcEZPSlFaOEVtdk5ubFg2RW9kRkpQZEl5WkZ2b3IrR29WVi9XaTVFWTRmaW9aVTJibThDWFdOVmNHbHpHWU1zbHMwZkpLMUFjbUR3QjErMUZFdGdkeDJvaERmdHVobXRxNHk3SmxBK09BZmNReWFnWmZtdXRaeHpMbjdWTENITk9yTW8wdHRneGxPRHJLTUZRSVhWWXBINDF5OVFjTit3bmlYTXROdFJkZHFRNVpDdEt1dlVFbTU3OWg1YlNnUjBDdDdrTmtkcFEzTGxpUjY0dUxnczdaQlczZmRPcVBGb1Rubi9HTkFQUHhxZjFwRnZyQ2ZjbDlNaVU2cm10Z1dYWElhWHRHcGkrVWJZL3dkc3BrOTBTYTZYZU1tSy9RZjhXNU9ZSXRrQlk1azRMR0x0WWN0ZWI3ZlVUcE5tdS82ZlkrM3hNZlFHajBvbWlDS29yZzRvRVFNQWJ0RXZVRUZvR2JHSzRrWXk1bnpBNmVTNHc1bG9xSzFDcmsiLCJtYWMiOiIxNTczOWMyNTAwMzMwODRhNmM3MmE4OGFlMjNlMDVhYWM2YTNlNzI5ZTY3Njk0MDk4YzhmZTVkZDEyM2NhOWQyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImM0NDU4UFRSUDV1K05lUWI1L3dPcGc9PSIsInZhbHVlIjoiN0ZIY3d0SEx0aWRyWHlLWXhjRWVaR1pQQjkranBjVE9HbkpKbTErMnc4ZUpsaHhRY1N2V2xUa2tsL3hRT3QydUFFRTE5TFN5T3RpSHlkbzk5NkNvdTBvU01GSzJSVG8xZ2VRTE9pTkp2M1ppRkpMT2tKbXN6ODNVdWRWWVdzMnZUMlcySWQ5cUNjNDNRemEyaEZDemxRdXVzS256OXR5ZUhqUGtsKzJVVitZQnZMVU82TDg5dzd3aElmcGpCWnJsakN3NlRpR3ZFU2N2TEdCaWtpOEs4dE9ZV1ZiTmhBSEJJVjl3NG1WUTl2K3ZYQ2cxTnJHM2hnZ0NaWDJHRm9ucHEyNWlZVkVuRUdwSjYyQ3czNGl6OTlTOFg2a3Rhd09ZTTFuMGtXYVZoWWtWVXNBbDZ6aUV1c09xQTRrNzhGcWt2TC9Vd3NsZzQ4Z0Fhc0hKc3JteTJiQU9KQ3pCSkYzMDEwNnp4TmVJcmNiMm1DaU9zUFZuTzFBNGdPMVZLYzNnaDAyUC9pSCttVExyaUpvcjNBM3U0YUNlQ016VEttL1R0K29KeHZFclpGVlIxTlprS0FoZ3VLZ2IySjg4bG50eEJjQ0hMQnp1c3hvRWVxdGh1SER2VzVNbHdZRDd6ZXpXS3FucFJ4ekQvNFllWkxNb0dDQytBUUtXVGVGYVJsS2ciLCJtYWMiOiI3MjdmNzY1NGRkYWUyNjcyZDI2ZGRiMGE2Yzg1ZTMzNDBhNzk3MmMxNTI3MjdmNzc3ZjY1YWQxZDJiODcxNjRjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}