{"__meta": {"id": "X45fc1035f449cb4a85aca0f1f359040b", "datetime": "2025-06-28 16:03:11", "utime": **********.708505, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.217154, "end": **********.708521, "duration": 0.4913668632507324, "duration_str": "491ms", "measures": [{"label": "Booting", "start": **********.217154, "relative_start": 0, "end": **********.646419, "relative_end": **********.646419, "duration": 0.42926502227783203, "duration_str": "429ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.646431, "relative_start": 0.4292769432067871, "end": **********.708523, "relative_end": 2.1457672119140625e-06, "duration": 0.06209206581115723, "duration_str": "62.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46423072, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2428\" onclick=\"\">app/Http/Controllers/PosController.php:2428-2462</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0026999999999999997, "accumulated_duration_str": "2.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.687385, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75.185}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.700535, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 75.185, "width_percent": 24.815}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-586965349 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-586965349\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1782171677 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1782171677\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2046591410 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2046591410\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126585944%7C15%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhzNzRNV0ExRTZGV3ZsdzN0bmNyUVE9PSIsInZhbHVlIjoiYk9NNVBuYndzcG1lTWVvSXlqVXBGM0ZmSHdPUGxyRUVVK3lRYktZRXB0NjZkU0xTbEJieTc5NUgwc3pTMkFjWWprWEdlOEpKdlk1dkVOR21FbmQ4L3l1K1djRGRLZE5vL2JHaXBzbHVhWjJRc05zaExUaVIrb0hnS3JMREVEb3dGcDNKWHd2M2g2ZWYyRFZLZXY3aStZRkVTTUVUWXV5UHNBbHBSMCtsQ0wxOGNxVnFuTDk3K203NmpxWGVRZjlLR3pnNUpUUnpUNlV4SFhUU0l5d1p5YldNeVUrWXNreGNReHZWRkhmeE5FdGlrZTFIWE0zK0s1eTlIaTZIYjN6VzNFUWM2Vlhzb0IvQVgxOUpYdTVmYXN0SWxaZHN4bVlNajFIRHZNL29aRWZqeUwwRklROFVUL1dQTWZKZE5XSGJMNGRMZTZMcFBmejE5TUk4QlJsS0hSUzhzWUFRbC9iWmw5WE1CQ3JLSmJidkpSRnRtSkhzMmxiYXpEWngvZzJLK3l2ZXkvYnFpSlYrem1ZMmdjYldsUjMrc29CN3gxVFJvbXZ5NTNHL1FjMU9ROTh3dFJkWlBMR00yTktLWS9LWWVWVVI1R3J0amtpeVBxNGwrNmcwMmY2VUU1WUYwUWRBN21KbHd5MlhkREkrY2czMGh2Uzg0QkNaQmdFU2RmSmMiLCJtYWMiOiJmZjQ0MDZiNGI0OWU1NmEwOTQ0YWE3ZGQ4MGE3YjljYjY0MDFjYTZjMWFkMjM5NzQ5YjJlN2M3NDAxOWM4M2IyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZjZkhzVUVMTVludFN2aWFNQWJzb3c9PSIsInZhbHVlIjoiWUxRNzNHRVV0bExIcFh5RE15Sy9EaXhRUHM1aTkwMTVzQVpwMVhmdkRFSWc2YzBNQXNLaXBYeVVVQWI0d3Q5V3hydGtpVDVKRndVOXcwOGZHK3NodTgvMUFqQ1NQdmEvRnF4UFRkRkhPdTg2aGl3dVh3MjEvb2ZlU1dKWFlkTEVGS0lpUG1MbnFzTWFlOW9xNi9iWnRuNHZlSWF2anFtZ2I2bWxFYi9FcmkwTnhWZW9pNUZuT3J1SGdDRjVVeHpTMEM3NjZNT1ZRWlhJTEZUMGN3Ui9PVU5mekI3VmFBc2piVHFuTFg0bGp5ZkEwYjlUbngwT3NEeEtadHJ2cUtUbzNZY3h3SDh3K3FMYzdGTHRuQnNhSnBJeTJtd2dLRWhQeUNpN2svWXUxSTJWNXVCbTVnTXBNRVFQRHlkdVVaUTErSjAvelBSQ2dKWjlUMzRRdFFLbHhpK1FwRjI2cHBYZFZ1WUhrRitIMzdQTHptbEFnS3AxU0JwNmt0RFBlUmhUTGFpcDQ4emxCcFVqTjB1YU9WcDdlaWtpSnBqN0tYWjhlTEFqRjV0eUlFVjdNT0M0OGgxMGU5Z204YjhQaDNpUXpRSUpXYmY2Q0hvMjZtZiszRHVwNE52dnZUQVdoeXJ0c0svT2lYOGZKQllJRGhTNDg5dFQ1STZ2RzhjU3RzZFkiLCJtYWMiOiI3Yzg4NTdjMGQyNmMxMzAzY2NiZWYwZTM4Y2YzNzRiOTg0YWFjZWQ2MmJlMGFmZDdmY2U4MmMzNDExZGM0OTI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1485374132 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5wOFY0UndnMFRYNmZHOUkyc2QrUEE9PSIsInZhbHVlIjoiNkJxV25Gb3U0c3BCQm4vRmh4Wis0SWlSajdWSkxIQVY3M05tVmIzdmxDZWVSbUtDQlByb2d2UStCZXRzVGk5MFJVUlBNNk1RWUpGQ25TNTJsdHdtRlZjb0Y4VTR1WDd6OW5Za3l3aWxxcGlBcVNPdzlYSkx2Q0NkdlpJL0UyRlBhbVRBMEJ0eHZ0K3IyQ3pMUy9sWFk3MzhsWDhVT3VKVENVdUJzQ3dpaCt1dHV2UjFaOWcxaXVhTXlpTkF1cElKNlB1S1NMVUU0Zkx5OTI5SzlIQld6S0ppMk1DaHc1SmQwN0F1TkxDekpJTFY0Vml3QytVdVZicEtxTEF3bGNRb2ZiMEhsZDlzNittaVFOTitMdERZcUhGTVFEbDJ5eXdOUGpxNitxVU13ZVdQclBSRUpobFZhV1M4czgvU3V3NDhvb0xUa0pWTVVNSmVaaGk0bGFnaDFOMlFwR1VtV2RTYWNvcnRLcFdaMDFXSituMmhjQlRES2F2MGJ2bDRNeDRaa1cwR2w5QjF5Ym5jSitMbnFlZVBlcGk0VklRWXRmdVlHcGNmQW5uTFM3RHUybVlZSER0NHRvWTkxUjRqN3B4ZWRHYlJ3KzlJdzRuQURVOWZNaXl0UWY5anJWWTF1UDdPUWlLcGR4aDkyVHJqemxYVGVFOUJocXBMMnQ1WVQ2SjEiLCJtYWMiOiI5OGE3NGFmYzcyNDA3NzM5NTkyNzE1MDI4YTI0M2FkZGIzOWIyNTU1NTY5NjExNTlmNWVlY2ZjYjgxMTllNTZhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBiQUVnbjhiSTBoMnhTRjF5K0o4VkE9PSIsInZhbHVlIjoiUTF5Y1JQYkpDSGFzN0RuVDhNWTBseEp2VEE3ZzhhTTYxbGI4ckV6bTdMWnA5QXcxbTBSR0xjV0RNcUdpQXJQdGxyL0Y5TkFoMWg5em1NTk5WbGtXdlpmc2ZJUUpUUDQ1YWMxajBVOUY4eVNLS081MVNvZVB0KzB5dTRSWVY1Um5KUG5EbWlLZzlGdEU1R0NST1orY29NZnpkWmErZmE5L254QVFkUUZwU1RxTmk5ZHJscU40M0lqV0orRmdFV0M3dDFMWlRRRk1MeUJ0bWZmaFM4eTJaY3lDTmlIcnNROXlBNjd3bzZJOUxDZGNQaU82eW10ZjAzREYzNFJ6cXBicWJBNWcwblE5ZFFGbW44Y2Ntdkh0TG1KMjF1VWhzRnd4RWppc2xZODBtd29jOXRMdjl4cU9xMlJqMXRqVHQ5TjRiVFhzR1YrMHhkYzZmSlplVUpaSHExVVRETDIrRmR2NkNRNnZKWnZaTm9KN1c1ZWRUV3NReklqeG4rYVpMK3hOZXpSWVZkS0NvTEdUOUZPYXpNTk1VUnlnaE1pbVY3dU0vQTVLWGhCOUp2cno3ZjFnSnlkOFVkUmVlK3pnbzJ6NkJYb0MzMXhiNTJNMFVpa09LVS9GNzVtWU9ZaUo4cytRUmJRS0hKRzJvUVVrRk1XUVZxa3hreFdHOTdjNEphK0UiLCJtYWMiOiI2ODc2YThjZjRkOTczMmNhOGI2YmM1MTY5OGUwYTRmODhiYjAyNjRiNjYwMzFlOWYyOGJkODZkOTg4OTQ5OWE0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5wOFY0UndnMFRYNmZHOUkyc2QrUEE9PSIsInZhbHVlIjoiNkJxV25Gb3U0c3BCQm4vRmh4Wis0SWlSajdWSkxIQVY3M05tVmIzdmxDZWVSbUtDQlByb2d2UStCZXRzVGk5MFJVUlBNNk1RWUpGQ25TNTJsdHdtRlZjb0Y4VTR1WDd6OW5Za3l3aWxxcGlBcVNPdzlYSkx2Q0NkdlpJL0UyRlBhbVRBMEJ0eHZ0K3IyQ3pMUy9sWFk3MzhsWDhVT3VKVENVdUJzQ3dpaCt1dHV2UjFaOWcxaXVhTXlpTkF1cElKNlB1S1NMVUU0Zkx5OTI5SzlIQld6S0ppMk1DaHc1SmQwN0F1TkxDekpJTFY0Vml3QytVdVZicEtxTEF3bGNRb2ZiMEhsZDlzNittaVFOTitMdERZcUhGTVFEbDJ5eXdOUGpxNitxVU13ZVdQclBSRUpobFZhV1M4czgvU3V3NDhvb0xUa0pWTVVNSmVaaGk0bGFnaDFOMlFwR1VtV2RTYWNvcnRLcFdaMDFXSituMmhjQlRES2F2MGJ2bDRNeDRaa1cwR2w5QjF5Ym5jSitMbnFlZVBlcGk0VklRWXRmdVlHcGNmQW5uTFM3RHUybVlZSER0NHRvWTkxUjRqN3B4ZWRHYlJ3KzlJdzRuQURVOWZNaXl0UWY5anJWWTF1UDdPUWlLcGR4aDkyVHJqemxYVGVFOUJocXBMMnQ1WVQ2SjEiLCJtYWMiOiI5OGE3NGFmYzcyNDA3NzM5NTkyNzE1MDI4YTI0M2FkZGIzOWIyNTU1NTY5NjExNTlmNWVlY2ZjYjgxMTllNTZhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBiQUVnbjhiSTBoMnhTRjF5K0o4VkE9PSIsInZhbHVlIjoiUTF5Y1JQYkpDSGFzN0RuVDhNWTBseEp2VEE3ZzhhTTYxbGI4ckV6bTdMWnA5QXcxbTBSR0xjV0RNcUdpQXJQdGxyL0Y5TkFoMWg5em1NTk5WbGtXdlpmc2ZJUUpUUDQ1YWMxajBVOUY4eVNLS081MVNvZVB0KzB5dTRSWVY1Um5KUG5EbWlLZzlGdEU1R0NST1orY29NZnpkWmErZmE5L254QVFkUUZwU1RxTmk5ZHJscU40M0lqV0orRmdFV0M3dDFMWlRRRk1MeUJ0bWZmaFM4eTJaY3lDTmlIcnNROXlBNjd3bzZJOUxDZGNQaU82eW10ZjAzREYzNFJ6cXBicWJBNWcwblE5ZFFGbW44Y2Ntdkh0TG1KMjF1VWhzRnd4RWppc2xZODBtd29jOXRMdjl4cU9xMlJqMXRqVHQ5TjRiVFhzR1YrMHhkYzZmSlplVUpaSHExVVRETDIrRmR2NkNRNnZKWnZaTm9KN1c1ZWRUV3NReklqeG4rYVpMK3hOZXpSWVZkS0NvTEdUOUZPYXpNTk1VUnlnaE1pbVY3dU0vQTVLWGhCOUp2cno3ZjFnSnlkOFVkUmVlK3pnbzJ6NkJYb0MzMXhiNTJNMFVpa09LVS9GNzVtWU9ZaUo4cytRUmJRS0hKRzJvUVVrRk1XUVZxa3hreFdHOTdjNEphK0UiLCJtYWMiOiI2ODc2YThjZjRkOTczMmNhOGI2YmM1MTY5OGUwYTRmODhiYjAyNjRiNjYwMzFlOWYyOGJkODZkOTg4OTQ5OWE0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485374132\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1624801165 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624801165\", {\"maxDepth\":0})</script>\n"}}