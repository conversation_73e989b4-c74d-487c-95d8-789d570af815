{"__meta": {"id": "X3c38b6a49df4f4b2dd331696057db773", "datetime": "2025-06-28 15:08:56", "utime": 1751123336.030893, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.611227, "end": 1751123336.03091, "duration": 0.41968297958374023, "duration_str": "420ms", "measures": [{"label": "Booting", "start": **********.611227, "relative_start": 0, "end": **********.958023, "relative_end": **********.958023, "duration": 0.34679603576660156, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.958032, "relative_start": 0.3468048572540283, "end": 1751123336.030912, "relative_end": 1.9073486328125e-06, "duration": 0.07288002967834473, "duration_str": "72.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46270200, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01672, "accumulated_duration_str": "16.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.993344, "duration": 0.01578, "duration_str": "15.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.378}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": 1751123336.02259, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.378, "width_percent": 5.622}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1955396800 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1955396800\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1017754448 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1017754448\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123332501%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkQ1U0VDbkwxaG4rSmtyNk9EOEVoUlE9PSIsInZhbHVlIjoibGNCZ2pNVE9QRi9HdUM4UmYwWHd2bi92bkoyODJMWFZFYUh4aUdiQUgvMkZjSlBLV3MyV0ltaGhjVWwrVlZpRGFvNEo1Z2NkRTR5MXdKaDdxYUY1bjRJc1VqM2tVU1htU0FheFM1L3Y5em5FOTkxK3pDa0N4OVdraHJYby8rQWxmdk1mVDBETjVlMUgrcWZwY3E3VzZCek5tMU5lVWV6bVd2OStiNkt6QW1tT3ZnVS9ZWTBrcWVrMGQ1RlVPL3M1eUpIVVJsOHZuVGM3cFdHODZock9zeFMwUzdmNGFqa1h6N09CVTdmdFU3aGNpSmFHYVpNUm12YjNGL3UwMFdvV0g4QXNiYXdXYzArZ3N4ekp6UTgvOTNGZTNOQTBWYVFrMzBOT3Nlbk1tUkxDa2xTOXpJdHliVXhNZUJ1ZnlOTXNJSVY3S2ErbFFMMnBPeG16dy9tNitkbmVOUURXM3V3eGtLb0hhK3p0OUZGSWRiUUpMWU5IMmllTXl5R1JGN0NuUHRrbDRmWGRFaEdIM1RFRm9NS25XM2RtdTJnTHRaL0h4RmprTXpDLzlPSThvL2EyNUZwOVJDZUl2L1Bkb21oOUlPcTdBRGdWTU5lYkd1OUNOZmlQM1UwaGVOSEwxVS96QjdIeW1hcEgvMHpuTlRqNnFjOXlHWHR2eG1rUm0xM08iLCJtYWMiOiI2NmRiZGZkMjViNjFmNzAzNTZmMjY5MzY5Y2I0MTZkOWVlZTkxNTY1Y2I5YzZjZGY3ZWE1ZjE2NjIzZmM2NTc2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkxoVlpEK2szbURsMmIrOERLL1dOeGc9PSIsInZhbHVlIjoiNndIQW1xU2xsL3dweFdENlNJWG9uQlFWUFhzNFBRb0d5UXQyMUhsYlB0ZnEvL1hKT2dUeVI1RFo2dlIzQmtTRWZLYVl0Q0NkNTF2a1lEankrY0FMQXJWeTZpRXJRMUlVUDhMVmU2aGRsNDhKbkZ3MVNhZGU5Ynl4bjZkdjBGb2VJV1ZqUGxETEp1QUlwOS9VNDcvQncyS3JjS3M3S2VzcVp1VDMwdVhVL3c5TmExVlhQZE9Jd1FNbVdENDZPUkhWaEhhbEE4dnZVOXVodWpUclZTWEIzZ0dmTmpucUlHakR0Tko4UE1LMmxUYktwMWZnQjdaWGlmV1hYdjVoWnJhN1l5eHVwQktNaGdoTlV2VnFneFB5MktzbW5GT1VsaVlBbDE4VDZFekZITjQxUjlpVlhtZUNVd1YrTjJ5UW5FNkxEeGJ1SEE0VGZTeW1id1dydHJyUHFDNE5SSUN6UUY3RlhQRUVQdWJxRC9KWDhQK3d2TVAwUUtkWHo3aUdJOVRLL1h2MGE0Q2NESXdCczYrUWJuMHpSWXZVOW1tRm5ocE11a1JvQXREcDBHeHZxYXI4c2lLY0hBTGltNXVJNHl6NEpBRncvMXFhNFhycDhGYWxJVWEybHUrV2pUaUNKc01lWnhiblpDMVFFOUU5TDRrd210b01tMUNHekVMdkFmakoiLCJtYWMiOiIwMDdiNDc3MzMxM2UwMDE2ODIyMjE0YTA1OTNmYTAwNThiZmNlMTc1NjY1MTIyZmVkMDJiYTU1Y2JhNzhkMWZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1375065095 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375065095\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-310714417 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldRWEVtQitydTlNQWNWd2FKYitUcEE9PSIsInZhbHVlIjoiZVBqSXYvMTVYdGEyVVVVOFhyVU1hL0t3dythcCs4d3RJcHo3ZTZwVHVyd3orL0ptUDFvakd1eWcxZjBKMkRtTUFxSWIyYytZRW91aWszM2hzVG43VzN4cVlkQ3RCdUVjVGN3dVI4WGc4RDZqUGFCdnNGNXdqQWxXcjFoK05HRFVKUkJnWUlqdzZYQ0JVTGtpVmxEdmM5K2ZQUkpqR2ZPbDZwUVN4d2c5TDBrN0thaHl3dUdyZGVDNXRyRDdLeFlmSHdyeklsb2Iza3RTYXNsdVhINDFQVkJQdXZrVTFHdG85T2hUc3ppZHpFQUxINVhBdHJPUFI0UUVJczcvaWRxQWZ1RjVqR0Z0QjFxQXZKSnJidHVoMlkzUUw0cHlwSnB0bXVKRnpaVmJNWm9xR0ZtckJmakwzUURMNjRveUlrWmpNTHZaenpDbzVENVRhRGVmak9QSWZrbXNxTmVRT0N3b0c3Q2FUTFd6QnJqelhwUFV6SXRZOUJ6dWx4V1BiSnp4dUFCL2N6SjB5R1RFZW9yajB3V2p5RVY5YWh2SWo0bHRvKy9vY2xUOWRXQjhjZnZWS2dzcU9LMVNVeGV5ZHJZNzFLcnAyall4eUFBU1BPdEVaQXJoR01IYmZTT1lyVHZxNjBiOU9DNnJUNTdYcVpzbnE3OU50R0dyZXI4YlhnVnUiLCJtYWMiOiI0YzM2MGRhOWJiMDc2ZWE5ZGJkNDBkMDM5YTU1OGYwYzNiZDRkNWY0YmFhYTA0ZjYzOWNhZjBmZTc5NTU4MmEwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFZWGJDZVZtNEE3Z0hrSWhsbEllZ2c9PSIsInZhbHVlIjoiYVhqOFhVR1VlSzVzWWI4MVBQcWpDOUZOTjNYemxQWjAvdWErYWNhZkc5Kys4MFFMRDVtOVZRSlVkK2dXUmNNZHBvelc1a2NzUzBIbGNuTXZNeWJSN2J2eGE5V251YlF4OFZXZ0R3VEsxWHk4Tjk4Z2xNTExZRmdiakZNWm1JSGU4UXlDZENLaVdVWXJGdUltTFkvN0psR3dRYmV0dHh5SExmMDBIMHIvemNOZmVCdkhFMHJIUUhOYzIwMFdnWUJhdmZxRnpKR1BSanB0QnNsS2MvMkVOY25yakk0dWo4TGRlTGdRNVkxVU5aSUw1OUxUT1R6aFRDUllYQk9USTk3TEsyQUY2cnEvSEVTcVBOczF5UXFOSGtOUEI1QzJvSE9UTDBNK0R3cWIvRXFhL3pFMXF2YUI0STZwZy8va3IweFdZTFBsNkFYMy9BZGRaL1hkeXdIWWRjVnNJdzMrK1Arbm1GMXJRSE1Lait4WFJHWWZIai9ZT3J3UUJnUU5lVlVRTVRxQ3lxa2ZZOEtha3o4SThwTkRtZTV4RExVd0FYQVJEbVV5UVg2eURMQ1hMUGxVRC91QkkwRy9RRWN3UVI5UzUzWm9xVWtCdkl1ekZpU3UwNjBUVVVWbTY4U1J5c1RwejRKUU83L2F5VENMdkQ1WnBLZTFKT2lUczlMcGJMcjYiLCJtYWMiOiJhYWI4ZWU1M2U1NDllNjU4ODA3ZWJiNzRiYmZmOGI1NThhNDU5NjliMWRhYTJlN2E5YjUxNTQ4N2EzNTZlN2M3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldRWEVtQitydTlNQWNWd2FKYitUcEE9PSIsInZhbHVlIjoiZVBqSXYvMTVYdGEyVVVVOFhyVU1hL0t3dythcCs4d3RJcHo3ZTZwVHVyd3orL0ptUDFvakd1eWcxZjBKMkRtTUFxSWIyYytZRW91aWszM2hzVG43VzN4cVlkQ3RCdUVjVGN3dVI4WGc4RDZqUGFCdnNGNXdqQWxXcjFoK05HRFVKUkJnWUlqdzZYQ0JVTGtpVmxEdmM5K2ZQUkpqR2ZPbDZwUVN4d2c5TDBrN0thaHl3dUdyZGVDNXRyRDdLeFlmSHdyeklsb2Iza3RTYXNsdVhINDFQVkJQdXZrVTFHdG85T2hUc3ppZHpFQUxINVhBdHJPUFI0UUVJczcvaWRxQWZ1RjVqR0Z0QjFxQXZKSnJidHVoMlkzUUw0cHlwSnB0bXVKRnpaVmJNWm9xR0ZtckJmakwzUURMNjRveUlrWmpNTHZaenpDbzVENVRhRGVmak9QSWZrbXNxTmVRT0N3b0c3Q2FUTFd6QnJqelhwUFV6SXRZOUJ6dWx4V1BiSnp4dUFCL2N6SjB5R1RFZW9yajB3V2p5RVY5YWh2SWo0bHRvKy9vY2xUOWRXQjhjZnZWS2dzcU9LMVNVeGV5ZHJZNzFLcnAyall4eUFBU1BPdEVaQXJoR01IYmZTT1lyVHZxNjBiOU9DNnJUNTdYcVpzbnE3OU50R0dyZXI4YlhnVnUiLCJtYWMiOiI0YzM2MGRhOWJiMDc2ZWE5ZGJkNDBkMDM5YTU1OGYwYzNiZDRkNWY0YmFhYTA0ZjYzOWNhZjBmZTc5NTU4MmEwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFZWGJDZVZtNEE3Z0hrSWhsbEllZ2c9PSIsInZhbHVlIjoiYVhqOFhVR1VlSzVzWWI4MVBQcWpDOUZOTjNYemxQWjAvdWErYWNhZkc5Kys4MFFMRDVtOVZRSlVkK2dXUmNNZHBvelc1a2NzUzBIbGNuTXZNeWJSN2J2eGE5V251YlF4OFZXZ0R3VEsxWHk4Tjk4Z2xNTExZRmdiakZNWm1JSGU4UXlDZENLaVdVWXJGdUltTFkvN0psR3dRYmV0dHh5SExmMDBIMHIvemNOZmVCdkhFMHJIUUhOYzIwMFdnWUJhdmZxRnpKR1BSanB0QnNsS2MvMkVOY25yakk0dWo4TGRlTGdRNVkxVU5aSUw1OUxUT1R6aFRDUllYQk9USTk3TEsyQUY2cnEvSEVTcVBOczF5UXFOSGtOUEI1QzJvSE9UTDBNK0R3cWIvRXFhL3pFMXF2YUI0STZwZy8va3IweFdZTFBsNkFYMy9BZGRaL1hkeXdIWWRjVnNJdzMrK1Arbm1GMXJRSE1Lait4WFJHWWZIai9ZT3J3UUJnUU5lVlVRTVRxQ3lxa2ZZOEtha3o4SThwTkRtZTV4RExVd0FYQVJEbVV5UVg2eURMQ1hMUGxVRC91QkkwRy9RRWN3UVI5UzUzWm9xVWtCdkl1ekZpU3UwNjBUVVVWbTY4U1J5c1RwejRKUU83L2F5VENMdkQ1WnBLZTFKT2lUczlMcGJMcjYiLCJtYWMiOiJhYWI4ZWU1M2U1NDllNjU4ODA3ZWJiNzRiYmZmOGI1NThhNDU5NjliMWRhYTJlN2E5YjUxNTQ4N2EzNTZlN2M3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310714417\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-13107418 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13107418\", {\"maxDepth\":0})</script>\n"}}