{"__meta": {"id": "X9dcb48aad9539a4f6615ae5f970b5163", "datetime": "2025-06-28 15:01:32", "utime": **********.687278, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.249711, "end": **********.687298, "duration": 0.43758702278137207, "duration_str": "438ms", "measures": [{"label": "Booting", "start": **********.249711, "relative_start": 0, "end": **********.607397, "relative_end": **********.607397, "duration": 0.35768604278564453, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.607408, "relative_start": 0.3576970100402832, "end": **********.6873, "relative_end": 1.9073486328125e-06, "duration": 0.07989192008972168, "duration_str": "79.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45611472, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02366, "accumulated_duration_str": "23.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.633043, "duration": 0.02278, "duration_str": "22.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.281}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.664193, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.281, "width_percent": 2.156}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.669335, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.436, "width_percent": 1.564}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImpEZWxnQjVWTmdHYXNiTytmTktuZkE9PSIsInZhbHVlIjoia2hDbk1xUk5UM2FjYU1rM1JyWDQwZz09IiwibWFjIjoiNzU4NjZkNTI2MGU1ZWQyYWM4MTAwNTM2MjZiOWFiOTM2YTIzODUxOGFkMDlkOTllYjA3MDFmNzVlMzg1MjQ1MiIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2066270557 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2066270557\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1458399746 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1458399746\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1435268899 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435268899\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1768583819 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImpEZWxnQjVWTmdHYXNiTytmTktuZkE9PSIsInZhbHVlIjoia2hDbk1xUk5UM2FjYU1rM1JyWDQwZz09IiwibWFjIjoiNzU4NjZkNTI2MGU1ZWQyYWM4MTAwNTM2MjZiOWFiOTM2YTIzODUxOGFkMDlkOTllYjA3MDFmNzVlMzg1MjQ1MiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751122878310%7C4%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imc2QXlHbHRuakFhdGZLeHY0OXYya1E9PSIsInZhbHVlIjoiOE9rSDcybGRJQ3R3S0dhaCtMN3Q4K3Y0alVRSzRQUDRpdkZrMlNJZkxobFB3SE5LOS9scWNSNUxBMkZnamlzUVZEam9pSmQvZXc1UnQ1VmxhU05WVG1seU9Qa2VUbVhyaGhKeVhFR0plbEFWN0VEYXdyT0gxU0FRVnl0UkVmQ3lGM094VlRoNmY3SXg0R1Nmd2NlZ0RSMzUvbzN0d28vQ3lXbE1iaVdFblozcjFGT1p1TmFKaUhqUjlRdDBuZGFKc0NqSnRteWdBK0dSNGhualpSUWQveVFlR3k0WGZOd1Fma091cTNESE9VUnkrYTZvdXVxeXg1QXBRZGpxTUlGTGE5d1dWaEtFQW5pa01UdnF4bFR1NFk3YzRxMEFpR1NZYVZKK3FNT043WmdaUVVGSFZQMWZEcGZrSHdRRXREM3BWN2FGZFNZU250V1ozMkU1WTBDVlNVa3k1MjR5dDdWdndoa21TM1VYY0FNUkpqdzc4bXJBbWsxQXlUTkZQM1U1L1RyVk4xQjBINjRLeE1lRGxzOGJXbnZETWc4Slo4RUxGaTBmK05VUTk2UVZBRlpJQ0QvQjZoUGd0SkpydnJid2N0eHhZOHU2WTE1Q0NoeGJyTkI2WFNIdCszS1AxNXhtVGY5WUZGS1VraVNIOFRPKzlJckYzN1Z2TmR0YVFRSE4iLCJtYWMiOiJmZjUxODRjOTBkMDJmZGM4NDZjODc4ZGQ0Nzk5M2Y4NzQ2Nzc0M2ZiMDAxNjcxM2IyMWM3ZjhmYjg3MDE4MGRmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5uWEMyTkt0RTVReHZxbzkzNDV5NFE9PSIsInZhbHVlIjoib1AwRlBweStoR3BKRDRlWVhnRVlWNTlvbjNHVHhDNW5mL1JzS2tHRkp4MmNJa1BXZDEwWWlVTElkczY3SE1OQ08yNEd1STFEZ1lzVnpoTTh0UThzM05QSDhCV3lDd3lrcnJOWTZiN1RSSXhpK0N2d0lESE5FeElXOWt5VmYwNk9IbnJ6Szd1ZTNqbU9lY1B1TXMvelhjS1BRSGc0ZldCQitETmp6b0ZYcnRCK0NwTDYvV0Z3OUkyUjY1ai9la0VnVVRSTHZzMzdSaCthTXYrWkw4VUNUeEYyaTQ1bUNIWmNBd0lsb3FXc3cyN09ka01MVVhWZDNnQ3ViRXFYdWltaXg5Q0xVUmlmK1M1MzF5TjU1ZnZmTmtEbFFOaWl6R3k1M1hBVVRYb3g3NVZCYm5lRW9vdVZaZ2FybUh1ZUI5d0JCbHkrVktHVEdRZmQxRU9vMkhINUZoTUJhUVJMckRaWlJJT1hzSHhlbGVrc0VaT0ZSM0o2UU91d1dSWTVTWHBOSGE3MTZoanRZTWtTSnJ6dnY3cmd5MkFVbmZjMWU4azR4ZUxCcmxBdHpGNUZwTnZKV0gzSGxhcE4wU2dscmJ3VEp3ejU0NXpHZUx3UXJwN2Nzak9hbkxqdGRiamhIV0R6MXowcDNTMzZYV3pYUFNVNmhXMEViRnluYk00aUFLS3ciLCJtYWMiOiI4YjZiZDM1N2YzOWE3Mjk5NDAxMzBkZDE5YTdmNzJmNjg2NmY5YjdmY2VkYmQ3MzA3Y2ZiZThjZmJkMDJjYzdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1768583819\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-53671104 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53671104\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1281522151 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:01:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IisxbDNVSjIwWjJtdENmQjkzcVJTTXc9PSIsInZhbHVlIjoib1lHWkN6T2FuQm5RZ0FpTlcvRGVMYmR0eEZUZThMNEFLMStRMW41YXRyeExJNCs0UXY2MFFwUm5oT1RLTUJnd3Vwcm9QSkxhZDlXMk5nUUxwNlMyNUdKMFlmQUpVbDZ5WktBZ0dnUStYUWdtUDdhZm1oU3RRb3ZTTHZ2SExuVlUyS090ZFNPWTRlTVhQNldOS3dwWGtXeHhyWlk3ME9ZZ1pZc3pTRi9neFdRSlhJNm9vR3QycTE4RXZlTkNReG9FdEhiTkNnL05CR09adnMybElNN0RidzdybTNkN3FWVnY2ejZsbXVLcXdwTGFuRzFGbDVEYjFBbFYyYmx6djlIR1NuRkNoV294dzFxNStkU0ludHJXMlRCS1JVYTEwRU1QMUt5Z0xPQkdwL2FCU25qK3VwTEtsaXFtUjllT3g4MVNtUmlsenBMWHZOYVRrLzBrRXBXUFlpVEZVNXpMM3lMRXJ2NWRheEY2YTNoRzVpaXZiRWtOQnJLelQ4M0RLaitTVnhEVjVTUGZCZ1JtZkFKeEFneURyRktQREwrdy9GOFFQdEs1RzVWZHJ2V2FrMFdMWUJpSjBvVi84UXB1UnpRTmplM0VoT1Zmd0N5aGk5dkZmYzliUEVQdVA2OTlrOUQ3UjQzZlNtUnhXaXZvK1RYK1YzZWpIVTliNXZvNHBERy8iLCJtYWMiOiIxYmUzNTI5ZjJlZTQ1ZTMyZGY0ZjIxMTdjODJmYjMyMDg0M2I3NTAzNDE4YWViM2RhNWVlOTI1YWE4MDkwMmIzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:01:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBuc2tVRE1xeis4cDlTN3lnT1Y4U2c9PSIsInZhbHVlIjoiOXZTTGUwYlpCQ1hVRGZBaXkxejBqejVZbnZhd1V6ZE0vVGowUnFheWE4bk9yRzd6bFc0dlNNRE9JZkdCZ29ZMUtHVTBMZTJjWkFsekRNbWNtMkFHcWNCSFVITmhGeU96T2ZBa1I2NkpCWEc0SVFlVHBzWnpkTXorMGswbkpIVk1mR2J4SG1ETVZXNWpXM3ZWUGpmci9Cd1dVcDRRREtaeTgzQkVHOFpxY05LREFKeHFidHlDajlMTzFzVTNmQUpMM2JwUFdlZWl2SXlEdXdySVhieE50YWRJbVZjNDBhODl0NXVKN1BxWFh6Tmp6QzRRc2ZwU2dOOWZCYXJZVVhWUmZMK1ZJWEZsUWJ3VVh0MHNZRE1zdGorOTBEYys3TVVha21FVW8xMVhpNFJ2Sloxc3lQVnZiMHgrcVBBdTM1UFRrWUxSUXFKK0RoNzh6KzV1empIS054MFB4UXF0SDdJY01RbUFDNExuV0poQW9vd1lEM05FQ1BhMTN2enltZUlRNEQ3Rkt4VXNVOWx1OStybmFEaEJYN0xEVHNNdjZsVkdDZVpHemJ3WVRWS2FOU2Q1c1BFNTB1ZW85V3BLZEx6dVlPQzJrTXVZZHYzTUhLekRiZTFLclVYSlA5VGJ0UkQ5V3hZWHFVVjdpNmlRWG91UTY0c2tsWHRKck9JaGFVMFciLCJtYWMiOiJmZTMwMWE5OTMxNzU5MTZiYWY1MjcxZGEzMDYwYWM1ZmYyYWNlZWI5ZDcwZGUxZjQyMTdlOWUwMTkwNzYzMzU3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:01:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IisxbDNVSjIwWjJtdENmQjkzcVJTTXc9PSIsInZhbHVlIjoib1lHWkN6T2FuQm5RZ0FpTlcvRGVMYmR0eEZUZThMNEFLMStRMW41YXRyeExJNCs0UXY2MFFwUm5oT1RLTUJnd3Vwcm9QSkxhZDlXMk5nUUxwNlMyNUdKMFlmQUpVbDZ5WktBZ0dnUStYUWdtUDdhZm1oU3RRb3ZTTHZ2SExuVlUyS090ZFNPWTRlTVhQNldOS3dwWGtXeHhyWlk3ME9ZZ1pZc3pTRi9neFdRSlhJNm9vR3QycTE4RXZlTkNReG9FdEhiTkNnL05CR09adnMybElNN0RidzdybTNkN3FWVnY2ejZsbXVLcXdwTGFuRzFGbDVEYjFBbFYyYmx6djlIR1NuRkNoV294dzFxNStkU0ludHJXMlRCS1JVYTEwRU1QMUt5Z0xPQkdwL2FCU25qK3VwTEtsaXFtUjllT3g4MVNtUmlsenBMWHZOYVRrLzBrRXBXUFlpVEZVNXpMM3lMRXJ2NWRheEY2YTNoRzVpaXZiRWtOQnJLelQ4M0RLaitTVnhEVjVTUGZCZ1JtZkFKeEFneURyRktQREwrdy9GOFFQdEs1RzVWZHJ2V2FrMFdMWUJpSjBvVi84UXB1UnpRTmplM0VoT1Zmd0N5aGk5dkZmYzliUEVQdVA2OTlrOUQ3UjQzZlNtUnhXaXZvK1RYK1YzZWpIVTliNXZvNHBERy8iLCJtYWMiOiIxYmUzNTI5ZjJlZTQ1ZTMyZGY0ZjIxMTdjODJmYjMyMDg0M2I3NTAzNDE4YWViM2RhNWVlOTI1YWE4MDkwMmIzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:01:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBuc2tVRE1xeis4cDlTN3lnT1Y4U2c9PSIsInZhbHVlIjoiOXZTTGUwYlpCQ1hVRGZBaXkxejBqejVZbnZhd1V6ZE0vVGowUnFheWE4bk9yRzd6bFc0dlNNRE9JZkdCZ29ZMUtHVTBMZTJjWkFsekRNbWNtMkFHcWNCSFVITmhGeU96T2ZBa1I2NkpCWEc0SVFlVHBzWnpkTXorMGswbkpIVk1mR2J4SG1ETVZXNWpXM3ZWUGpmci9Cd1dVcDRRREtaeTgzQkVHOFpxY05LREFKeHFidHlDajlMTzFzVTNmQUpMM2JwUFdlZWl2SXlEdXdySVhieE50YWRJbVZjNDBhODl0NXVKN1BxWFh6Tmp6QzRRc2ZwU2dOOWZCYXJZVVhWUmZMK1ZJWEZsUWJ3VVh0MHNZRE1zdGorOTBEYys3TVVha21FVW8xMVhpNFJ2Sloxc3lQVnZiMHgrcVBBdTM1UFRrWUxSUXFKK0RoNzh6KzV1empIS054MFB4UXF0SDdJY01RbUFDNExuV0poQW9vd1lEM05FQ1BhMTN2enltZUlRNEQ3Rkt4VXNVOWx1OStybmFEaEJYN0xEVHNNdjZsVkdDZVpHemJ3WVRWS2FOU2Q1c1BFNTB1ZW85V3BLZEx6dVlPQzJrTXVZZHYzTUhLekRiZTFLclVYSlA5VGJ0UkQ5V3hZWHFVVjdpNmlRWG91UTY0c2tsWHRKck9JaGFVMFciLCJtYWMiOiJmZTMwMWE5OTMxNzU5MTZiYWY1MjcxZGEzMDYwYWM1ZmYyYWNlZWI5ZDcwZGUxZjQyMTdlOWUwMTkwNzYzMzU3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:01:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281522151\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1064651234 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImpEZWxnQjVWTmdHYXNiTytmTktuZkE9PSIsInZhbHVlIjoia2hDbk1xUk5UM2FjYU1rM1JyWDQwZz09IiwibWFjIjoiNzU4NjZkNTI2MGU1ZWQyYWM4MTAwNTM2MjZiOWFiOTM2YTIzODUxOGFkMDlkOTllYjA3MDFmNzVlMzg1MjQ1MiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064651234\", {\"maxDepth\":0})</script>\n"}}