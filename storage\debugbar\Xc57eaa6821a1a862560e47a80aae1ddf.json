{"__meta": {"id": "Xc57eaa6821a1a862560e47a80aae1ddf", "datetime": "2025-06-28 15:38:37", "utime": **********.392377, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.001258, "end": **********.39239, "duration": 0.391132116317749, "duration_str": "391ms", "measures": [{"label": "Booting", "start": **********.001258, "relative_start": 0, "end": **********.341491, "relative_end": **********.341491, "duration": 0.34023308753967285, "duration_str": "340ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.3415, "relative_start": 0.3402421474456787, "end": **********.392392, "relative_end": 1.9073486328125e-06, "duration": 0.050891876220703125, "duration_str": "50.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45884424, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2015\" onclick=\"\">app/Http/Controllers/PosController.php:2015-2087</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00206, "accumulated_duration_str": "2.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3735569, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.155}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.384433, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.155, "width_percent": 21.845}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-1899372486 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1899372486\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1356681806 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1356681806\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1462259252 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1462259252\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125105407%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpGMFo5MTB3TGo5R1RtT3FFSmRvR0E9PSIsInZhbHVlIjoiWjd0MXE3U2QwSGcrREhzV3R1OHV2ZWxTVHJBT1pZTS9oMTB1MDgzUDhjZ0owa1FxRzNHbVBTRVBDUDVBaWw3WDJlY3E2Nno5cmVoYmtYMDBYUUh1WmNENFl5NzZBMCthbThtcmtOb0d1UTAyR1NUaUhBbEk3M1BMYVlNR1VmQTBzelJvQ0ZISDVsOVRHbzJmanhtUTRqdGZSaFNRZVNjZkIwQkMxaU1sVU1KRFNndlhWa1FDQXRoRXJhdllKaFN3K1ladWw0ZU9mb20xODQySlkyWE9RS3hWZWhiV1BVandFNFM3QlYvaERMaUZUMVl5ZmlycFJKRG5EK1cvVGZXa0xCN096M3AwQzhKZnlyRGRzOU42S3FRSE4wQzZnb1dOdzJrWVBrbWROU29xVHZFYTFLUkh4NE9MbWxGQy95YVVoZHowQjhHcnhYNXFJUlFhVkhlaEJyVk1lVE1YdHdUbkJ4WDhhZWJ6RUU2MVFXV0N6NHhwMkpvSTIyQ2g1SUg0MFFTRUFDd1VNUzF6T2Z1ekUyZENkQUordFBJQmsrLy93QnpObWVjWVN1Q1ZvR0Y3eEFOaVBRQVVlK0lDZDV0ZlJYeW9mT1NhRXpVS0g0MWphYjN0cDl2NlRNQVRnRFFPSmRtVVBJZmwyZVppZ0FURTZzb0w2c3FBbXE4V3ZRYzEiLCJtYWMiOiJlMDI4ODc2NmY5OGRjOWRiZGY5OWIyYmEwNmYxOTUxOTVlMTY3NDAzYTc0OTQ0N2NhMzQ0Y2QxODYzZjQ3YzBhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im81VjJPL21NYWdkMG1RZGpDQTlBNUE9PSIsInZhbHVlIjoiRUNMclBlL0dhWEVXQTFSTGNERHo4UURxTnJ0bmVCekV0aW9jKzlrRi9ZZVhFdExucVcxdndGMmVUVlBwS3V3Nm44ME9nLzdxaUFQd3preGNXek1xbTFVTEFpN0tqNUludFRJa1VZN0ZxWUYwT3huQWJ6MTJMU094RlFraDdGeW9rSGZPLzZHYW0xK3h6VmN4all4UEJuM1RiYy9VYUMrOXcrRkZseFIvd1UrMHhLWFYwQTdsQXRncEJ6K1pJQjFBU1MvNDEwREpSZ3FzTGdpalYvaGlmVzY1OEcyOFZYdzg1T2xEMER5aUJPT0xvT2IwQlVJMDkwdW55WlVBditWOE0rKzg3S3VCYWVmbUd0aVlzUXhxY1NoV3BxWmVPNXhVRUNrUHNxN3dxbkZEYm8rL0lzOFlGbk5ZL0hsa2tKVW1nTmNXMVN5MTNZbXluQVF1alRTd09jRUl6aWcxbWVNNGRCNzd0R01CbjNnZFJYeUdoZEN2eUt1Y3lQandyQlZob1FvZE1TS2hhUWt1UGhrY1NNT2pKdWkwU1RRc1NJNytxZEdGSG02RzF6YVRaSGNMTjVFYmdhTDR4Vys0TEkxVDRiN0hKRDhCbkFrdndVbDUxcDlhYWpmampaaXM1V1BPb25FS1pqYjByOE94Y3dScDZZSmZ4MU1jV3IvT3BwOXUiLCJtYWMiOiIzNDFiNjcyYzYwNmJkZjQxMGZkZTBjOGQyZWUzMzBmNGViM2MwY2QxOGQxNDg4MTY5M2RjZTI1ZjM4OThlZDMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2401363 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2401363\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2081236586 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:38:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVrU1pPY0ZnMTdyTGtWNVdZVDZXalE9PSIsInZhbHVlIjoiSVQzRzdSSU14T2oxSHFuL3doNy80UVNxcEtRVEZ0cGVwcURBay9QVytKS3UrWHY2VU9TT21YWVM0QTFGaHlPbUxRTCtvRjc5VktVSmxaRjg3LzJFZnA3cmlRTzZLQTNNWmtNck9TRnJrQitsY0NtVXNucXduSVd1M3N6U2g2R0lBeU4rVFNCM3lRT0pXYmR6S3FWdEJvU29uQnh5eDNNaEJnWXBwQW10Ym9wY2k2MzBhZ2tCOGlGUDF4ODJ3alE2ODZSN3lZYUpwbUhuRVRWZHE0akZ4Q2QyTnZiTFBoMEhTbVNsQTRYM2lCRDBudVJGRDBjZU5Jb3g3Z2tVT1ZoN3FZMGhiR1NwbCsrR2Z3Mlc2K2lwSmhsWXZhOVI0LzVEM1M5VURUZmk0MUMvVmRmb3ZDZUI3Y0p5cVV3QjBjTG9ZZnhZazQzbER6ckNveVdEdFVHYVU1SE5IWEttbUpIcXJOSE5TTlV2eXdOM2RjMW5qQTdtaEw0WWp2L3c4S0dmQUIrNFJ2bzE5MElwTTVENGRzRGtNaWp2bWFoQnZzT1g5dHJMakRHSFFhakNCYzViKzduL21xc21HN0F4UmZIcDA3NitRbjltaTh0Ujg1cUI5WnN4RVNxUmhQRzk1M1RtUjdTMHgzVTNZTm9RV2p5anduSVNIdFlmWm5DNDBqL0EiLCJtYWMiOiI5MDMxYTk2MDk1NDIxMDY3NGNhYzIzMGJlOTEwMzg1MWM1NTJjMDcxMTgyNmRhZWM4MGFhODc0YWEwZDM2Y2U5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9GejdNOGRNSytRTlk2Zk1ybk1PZ1E9PSIsInZhbHVlIjoiaVBDNExmb2hGOVc2em9yWkVzTVBBM25LZjJqaG9MQ21JSzlxNE8xSmpFby9iWlN6TmlaRlhscFd3Y0xKNzRPL2xhQkVlcDRsZ29mRExJV25JSXZGRWlkd1pTODd4ejd0RGlxb0kyV1prdW9RbU9mclNPQTBBcEJPbzRQQXdXN3pQUi9lTld1WlpsSGNONnFZNmV6NS80YWFJcVNXZS9LZUUvMHRyWDloaXVLQ0JuTk1jZUw1b0ZxcFlOS3ZGUFVUczdRa1RKN25UNEZadVROQzBlQTdNY1YycVpwUlR0QjZCU3oyNDg3QjlLSitjZGxGRFdROGZLZ2RZQm5EN21TUlZYb25CUFlYZjcwZUlkU2VNSGJ5akVKU1VWajMycnFES3lDVE9ncmhvQ29DT29HeDljNDN0LzEyQXdDdHozdmJWYWFOaEVOVjdoWWpmaEEzVTV3SHZSY0h5cWtTWlB4TXZDdHpEUTVROWtFWGY2TG5kNVM0aVNxbzBPU3E4U0FnMjdOZEY5TCtFVjVhbk5vdWpTTDhnOSt5bTcxc29hOVFGTXpWMm5PRnJzYWV2VGhXUFd3b2ZROGYzMVJFaHhVN1ByaFR0Szd4Y1ZqM0RHQkd2Q2xnMm0zaDlDOGdZZWMzeXZJUm5xdHIzcm92SThUT0xmY1FtU2tlN2orS0R5MVciLCJtYWMiOiI4YzZiNzkyNjExZmZhZmRiZTRiY2JhODFlOGMzNDM3NjA5M2UzMWJmOTJjNTQ2MzNlZjdjYzZiYmYxNGJkMDhlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVrU1pPY0ZnMTdyTGtWNVdZVDZXalE9PSIsInZhbHVlIjoiSVQzRzdSSU14T2oxSHFuL3doNy80UVNxcEtRVEZ0cGVwcURBay9QVytKS3UrWHY2VU9TT21YWVM0QTFGaHlPbUxRTCtvRjc5VktVSmxaRjg3LzJFZnA3cmlRTzZLQTNNWmtNck9TRnJrQitsY0NtVXNucXduSVd1M3N6U2g2R0lBeU4rVFNCM3lRT0pXYmR6S3FWdEJvU29uQnh5eDNNaEJnWXBwQW10Ym9wY2k2MzBhZ2tCOGlGUDF4ODJ3alE2ODZSN3lZYUpwbUhuRVRWZHE0akZ4Q2QyTnZiTFBoMEhTbVNsQTRYM2lCRDBudVJGRDBjZU5Jb3g3Z2tVT1ZoN3FZMGhiR1NwbCsrR2Z3Mlc2K2lwSmhsWXZhOVI0LzVEM1M5VURUZmk0MUMvVmRmb3ZDZUI3Y0p5cVV3QjBjTG9ZZnhZazQzbER6ckNveVdEdFVHYVU1SE5IWEttbUpIcXJOSE5TTlV2eXdOM2RjMW5qQTdtaEw0WWp2L3c4S0dmQUIrNFJ2bzE5MElwTTVENGRzRGtNaWp2bWFoQnZzT1g5dHJMakRHSFFhakNCYzViKzduL21xc21HN0F4UmZIcDA3NitRbjltaTh0Ujg1cUI5WnN4RVNxUmhQRzk1M1RtUjdTMHgzVTNZTm9RV2p5anduSVNIdFlmWm5DNDBqL0EiLCJtYWMiOiI5MDMxYTk2MDk1NDIxMDY3NGNhYzIzMGJlOTEwMzg1MWM1NTJjMDcxMTgyNmRhZWM4MGFhODc0YWEwZDM2Y2U5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9GejdNOGRNSytRTlk2Zk1ybk1PZ1E9PSIsInZhbHVlIjoiaVBDNExmb2hGOVc2em9yWkVzTVBBM25LZjJqaG9MQ21JSzlxNE8xSmpFby9iWlN6TmlaRlhscFd3Y0xKNzRPL2xhQkVlcDRsZ29mRExJV25JSXZGRWlkd1pTODd4ejd0RGlxb0kyV1prdW9RbU9mclNPQTBBcEJPbzRQQXdXN3pQUi9lTld1WlpsSGNONnFZNmV6NS80YWFJcVNXZS9LZUUvMHRyWDloaXVLQ0JuTk1jZUw1b0ZxcFlOS3ZGUFVUczdRa1RKN25UNEZadVROQzBlQTdNY1YycVpwUlR0QjZCU3oyNDg3QjlLSitjZGxGRFdROGZLZ2RZQm5EN21TUlZYb25CUFlYZjcwZUlkU2VNSGJ5akVKU1VWajMycnFES3lDVE9ncmhvQ29DT29HeDljNDN0LzEyQXdDdHozdmJWYWFOaEVOVjdoWWpmaEEzVTV3SHZSY0h5cWtTWlB4TXZDdHpEUTVROWtFWGY2TG5kNVM0aVNxbzBPU3E4U0FnMjdOZEY5TCtFVjVhbk5vdWpTTDhnOSt5bTcxc29hOVFGTXpWMm5PRnJzYWV2VGhXUFd3b2ZROGYzMVJFaHhVN1ByaFR0Szd4Y1ZqM0RHQkd2Q2xnMm0zaDlDOGdZZWMzeXZJUm5xdHIzcm92SThUT0xmY1FtU2tlN2orS0R5MVciLCJtYWMiOiI4YzZiNzkyNjExZmZhZmRiZTRiY2JhODFlOGMzNDM3NjA5M2UzMWJmOTJjNTQ2MzNlZjdjYzZiYmYxNGJkMDhlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081236586\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1260154797 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1260154797\", {\"maxDepth\":0})</script>\n"}}