{"__meta": {"id": "X2ad523566ee7e9f3cfb3e01037c3067b", "datetime": "2025-06-28 16:17:29", "utime": **********.833317, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.337921, "end": **********.833333, "duration": 0.49541211128234863, "duration_str": "495ms", "measures": [{"label": "Booting", "start": **********.337921, "relative_start": 0, "end": **********.769098, "relative_end": **********.769098, "duration": 0.43117713928222656, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.769107, "relative_start": 0.4311861991882324, "end": **********.833335, "relative_end": 1.9073486328125e-06, "duration": 0.06422781944274902, "duration_str": "64.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45700192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0028599999999999997, "accumulated_duration_str": "2.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.805485, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.986}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.817477, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.986, "width_percent": 16.084}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.823613, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.07, "width_percent": 19.93}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-177316451 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-177316451\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-31560127 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-31560127\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-536039252 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536039252\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-708916507 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127024092%7C32%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ing3bkZFUkloSHBmazFSRTJuanpqamc9PSIsInZhbHVlIjoibnlXOFVmbFVobFJrSEVvWU1UQ2JRNjdHektQWERLU21nVmc5Wi9oMjZwd2ZiMUQrNUtyZ1AzLzEyS2hWbzZyd2RMRXBtM2JVYU1hb1k0K0dDcHZCVzRvcXhZMXR4eGdjZ2s3aHFTMXpYcDNYTUNzZFhMbDJtdWo3NEV3WENXeTNPYlhtclkydVRQRUY4ZGEyZVBobFNoSDNYZkV1TDRXSlFyYzBwVUVlbjMyQkJkMlUzVHFLem40MHpVdnFaWW1waFVNUk56bWdmTFV5M21POVJnLzFXaUlLYWVKYTNKVUtMd3J4N3BMSmZrOXBLVnVLSVlXek8yVTJheHFxeGZPcnA5bWhPcnFpRzV2cWxPdE9zZDVQcVVTajhFL1BnSCs5VXFtMWhrL1czQmo3VGFrMGVhbXl6dnZ1WWRWTlZqMXpMZUd5UyswT3EyeDE3ZVV4UlFkdGluZ0N3UmhkM3VDNjZlalY2TFNaeG9IMlA4c0hxSm1Od2xXRHlOdUtjaWxsb0d0S3FVZy9JNVVGR1V6STJvS0FFSjloK3JtRjZiLzhIWEprR3piZnRiVGFFSE9TdHY5cmhmTi90NFU0QURXTHpKQ3M4T09uczF1R1EzZVpqSk9MdG1SMG83Zmc5RTk1YTNnTUQ3TVpkNnAxL1NpeitPQTNObFJVcFVVeVl1SWQiLCJtYWMiOiI5OWY4MmIxOTNmYTM0OWYzZWI4NTY3ZjE1YjM1N2Q1ZjZkNGY5NTFiYWNmMGI4OGRiM2RiOWVjYzYxMjEwYmM0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhqVm56c2t6eGQvRndnQzlNNTlGemc9PSIsInZhbHVlIjoibEVoOGNSUk1pbno4bUtodmkwcjRDOEswREZSd3JQT1h6SkNVMThtOHRQRkx3UjQvR3VKS3pxZXpwbnQ0K3hGU0swTjJjMFFjQzVQTXR6R0M1dEt4WXEvUWFqZzZPR0NyaUpQN3B5Y21iRjZmS1UyKzJHRjlobnFsV0JSa2tSSEp4VDkwZVlweVR1d0NsbjZVSTk2LzhWcW9yRk1SYlNwWVZzcGkxU0lweDAwa2JMSjZyT2o2aGNMWjNSd2VSNmNleGRvay84Mml1M0E0NXJVVExESm1TYzhNeHkwcG1TaUNaVTNVMFVjeDZiZS9iWWx6RU5lTEVEUWM0cWVQQk1ubnR6Nm9KTkFZUGZCWDhSZFd6WG91SWZ1Ym5FWFNwLzZBWjFtOUdHVkwwamxUT0ZndDhNajhLY2ttUks1d0pKdzI4SG1iZFI5QjhWTGkrallTVndFb1FEWkNzakt3YzRiU0Y3V05iTXUyUzNaQnRNOHduWGZ4OTJBQktWVjROU0JMNjcrSlZEYndzZWQ3VEVPWlA4T2RxN2JHbDJnMVVTWDFUa0N3NTloOFhvUXN2aUdxa1VnV2tFaTZUSngyM1J0QllHUHJsQ0pPUTFaZE9EN0pKWHZyQVA4V1I2dTU2aWtkUVVUcW9QbzlnY04zczQxVjhLSFRQWTJZU29qWDBqeUsiLCJtYWMiOiIxNzYwNDUxOWI5NTcxMTQ0OGQwZTRiNmE4YTQ5YzYxZDMzNjNiNGVjZDUwNTJjMTA1YTNkZjhjNWI2Y2YwOTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708916507\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1818906416 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818906416\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-104845583 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:17:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlAyVUJYVGM0dXNwWG9VKzZhMDZxQnc9PSIsInZhbHVlIjoiZW9JbVVpb1RqTFV0OFA1a1NnaityNlVNTFdMOTR2anduVFZFcmUxMUV1Nk54cVVtLzY1T0h4UmZmU1dFUTN2NHpyQTJDL0plWktlY0ZBVjFRbTU0eTRiTm01cWhVY0VtWThVZHdhQXBpbnZsV0tFaXhEWG1EbHgyRTg5SlNWemY5d1pZcER0cWdYTGJSdk9RaE5zU21TZ0VVQlRndGNoY1hSdVFZdDdsLzdMQTA1YjlwMkNROHNVMXI3SGVVNG40YkhXZE80RGVjWnYzVjc2dGlqL2JqRE9QY09HR0VBazdnSVlwK2FiMUg3bURZUlZ0Z3FQN1dLdml2bVpwb3NKLzNwU1NQSDZsU0xadm5ubUg3SEluUTFpMGEzcGRUWDgwYXFJWlk4NzF2TE02L3pzaVh4dWlWNmVmbG94VmVGK1lSVGJjbDhGYldOTGlGem1rZ29uWHh5NEtPNmdNSU9UOG83Yzd1U3JOSlFaZXZ5MUUxY0cvTXk1LzZGRTBCNU5XTDdoMXAwTlZxUXpha3c5UkV4WXlnNFZRZnJvb0N5Z0hMSTVUeWd2YnA0Mm10M3k0QkNIV3RIRGxlbHpvWU9OVVZmZUFvOEEzZnp1TFh4Z1QzYWlmcUpnd1ZhTk44T2FpQVVONHB5M2pDTUN1Um0zMHZhWjAwR1VRalFlU0F4VlYiLCJtYWMiOiI5YjQzY2Y1ZGU4ZTI3M2RmYzAzMDk0MzdhNThiODcwYmU5NTZlNWJjYmNjODdhZDBiYzRhZTExOThhNTI1NWRjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImtZQmJ6dHBRbGRGTXlGSGdjWFZyVVE9PSIsInZhbHVlIjoidUx4a285bUVUc01iNzB0alc1bGlqaTZxQXpvZTRyOUF3MEpMQ0syY2pOQnk1eGRycWZLV1lib29KZTUxYUZrZWtFdDhDbkc0SHp0cWtPNFNvcFZIZnN0S3VoK0pRL3RjcUF4UGFjZUg4ZDdzWFVGV3VPb2JYamIybWJWaDFwVFZiREUyY3BxVUttaThFQVpoUG9ZZjZrK2dFRVV0R2NYMzNZMjBYZFVTWmc1M0o1bmo0bEZYa2d3YWlNQWx2cUVEZ2VxZWU5NDhTSzZ3SnFNQnZJRHpTWmlrbWNaNjVQbzlVUE5ZUzFXc3AzZ1BMbUZZSmFxenRlK0d1WW9ybDdjUjBsQ0VwVFVFSm1oa1JLaWNzSFlTb3FmOG9xdzhEU0VPTkpLdGtrRllCWXBPR09iQ1FYVUpRQXNKa3JXaXBENXV0Rlc3cFFLYnpvZXAxcStUREJyYVhtQTNINFp6WE82NG1IbTNWK3hmRUhSbTdsYkR0SHBQRE1XdVRudGFNcDlwRHhBS2RiWHVURHNpaXhNRTBSeXprcjFhc05OTWs1R2czMHNtL29veGphN3hJMHhTTWRSa3poajZCWVdyTFc5OC9GNFRUR2pkY1BId1dhWUhHOVZrWjNmRytoS0tLNGdkWm5IQ3NTWU1WazBOdVhUdi9rVk4wVzd2c21sUGJHenkiLCJtYWMiOiIxOGJlMjU0YTZjNTI5MzVmZDcwZTgyMzgxZmZlZWQyYjIzYzM5YmZjMzQ3Y2E3ZDM3MDYyY2YyZDJmYjc1ZjJjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlAyVUJYVGM0dXNwWG9VKzZhMDZxQnc9PSIsInZhbHVlIjoiZW9JbVVpb1RqTFV0OFA1a1NnaityNlVNTFdMOTR2anduVFZFcmUxMUV1Nk54cVVtLzY1T0h4UmZmU1dFUTN2NHpyQTJDL0plWktlY0ZBVjFRbTU0eTRiTm01cWhVY0VtWThVZHdhQXBpbnZsV0tFaXhEWG1EbHgyRTg5SlNWemY5d1pZcER0cWdYTGJSdk9RaE5zU21TZ0VVQlRndGNoY1hSdVFZdDdsLzdMQTA1YjlwMkNROHNVMXI3SGVVNG40YkhXZE80RGVjWnYzVjc2dGlqL2JqRE9QY09HR0VBazdnSVlwK2FiMUg3bURZUlZ0Z3FQN1dLdml2bVpwb3NKLzNwU1NQSDZsU0xadm5ubUg3SEluUTFpMGEzcGRUWDgwYXFJWlk4NzF2TE02L3pzaVh4dWlWNmVmbG94VmVGK1lSVGJjbDhGYldOTGlGem1rZ29uWHh5NEtPNmdNSU9UOG83Yzd1U3JOSlFaZXZ5MUUxY0cvTXk1LzZGRTBCNU5XTDdoMXAwTlZxUXpha3c5UkV4WXlnNFZRZnJvb0N5Z0hMSTVUeWd2YnA0Mm10M3k0QkNIV3RIRGxlbHpvWU9OVVZmZUFvOEEzZnp1TFh4Z1QzYWlmcUpnd1ZhTk44T2FpQVVONHB5M2pDTUN1Um0zMHZhWjAwR1VRalFlU0F4VlYiLCJtYWMiOiI5YjQzY2Y1ZGU4ZTI3M2RmYzAzMDk0MzdhNThiODcwYmU5NTZlNWJjYmNjODdhZDBiYzRhZTExOThhNTI1NWRjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImtZQmJ6dHBRbGRGTXlGSGdjWFZyVVE9PSIsInZhbHVlIjoidUx4a285bUVUc01iNzB0alc1bGlqaTZxQXpvZTRyOUF3MEpMQ0syY2pOQnk1eGRycWZLV1lib29KZTUxYUZrZWtFdDhDbkc0SHp0cWtPNFNvcFZIZnN0S3VoK0pRL3RjcUF4UGFjZUg4ZDdzWFVGV3VPb2JYamIybWJWaDFwVFZiREUyY3BxVUttaThFQVpoUG9ZZjZrK2dFRVV0R2NYMzNZMjBYZFVTWmc1M0o1bmo0bEZYa2d3YWlNQWx2cUVEZ2VxZWU5NDhTSzZ3SnFNQnZJRHpTWmlrbWNaNjVQbzlVUE5ZUzFXc3AzZ1BMbUZZSmFxenRlK0d1WW9ybDdjUjBsQ0VwVFVFSm1oa1JLaWNzSFlTb3FmOG9xdzhEU0VPTkpLdGtrRllCWXBPR09iQ1FYVUpRQXNKa3JXaXBENXV0Rlc3cFFLYnpvZXAxcStUREJyYVhtQTNINFp6WE82NG1IbTNWK3hmRUhSbTdsYkR0SHBQRE1XdVRudGFNcDlwRHhBS2RiWHVURHNpaXhNRTBSeXprcjFhc05OTWs1R2czMHNtL29veGphN3hJMHhTTWRSa3poajZCWVdyTFc5OC9GNFRUR2pkY1BId1dhWUhHOVZrWjNmRytoS0tLNGdkWm5IQ3NTWU1WazBOdVhUdi9rVk4wVzd2c21sUGJHenkiLCJtYWMiOiIxOGJlMjU0YTZjNTI5MzVmZDcwZTgyMzgxZmZlZWQyYjIzYzM5YmZjMzQ3Y2E3ZDM3MDYyY2YyZDJmYjc1ZjJjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-104845583\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-325963218 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325963218\", {\"maxDepth\":0})</script>\n"}}