{"__meta": {"id": "X6e078b09e8b4eedb301fd70616952d54", "datetime": "2025-06-28 16:04:18", "utime": **********.273127, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126657.863132, "end": **********.273139, "duration": 0.4100069999694824, "duration_str": "410ms", "measures": [{"label": "Booting", "start": 1751126657.863132, "relative_start": 0, "end": **********.218652, "relative_end": **********.218652, "duration": 0.35552000999450684, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.218661, "relative_start": 0.3555290699005127, "end": **********.273141, "relative_end": 1.9073486328125e-06, "duration": 0.05447983741760254, "duration_str": "54.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45714488, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00257, "accumulated_duration_str": "2.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.247848, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.981}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.258379, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.981, "width_percent": 13.619}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2639709, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.599, "width_percent": 21.401}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1209582513 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1209582513\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1938850142 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1938850142\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2088532455 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088532455\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126654835%7C25%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlB1OTBYWjlQUWRGOGxrMEcwa2dvMUE9PSIsInZhbHVlIjoiWjVHKzFOUUtialBtUlpubHdScmpjTzZlWEIvblM4d0pmTG5qM04zVU9Ud1ZYQlIzaTFHT1QrTldXamJreEJZSjJndG1GSnBpL3NmVUhEL1RnVUJESzlwU3Y5WjBOYWZhZjYyMW1iUGhNeXZsODMvS2NtQmJZK0xQUExFeFJpNzVrRVdUQ1VjMHd0bFNxSkFwa0tweGwrOHdMazA0ZkRwS3RBQnQrajIwQzhYaGl0eDZjU3Flbk9vKzUrMVdDblg5NDYyTis1eDJjbGFVUzAvSnhtc013eTA2Q1BNNndMcjV4M3pUMUYvNHBFWkNmMk9pRGY5YXB1QlBKbEhCV1F1WmVtOE1OaE92VjcrU0N0Vmo5Ui9sOHlkQkRLMVlqWFFvZmFrTkcrdkk5b1hGMDJPQVZwMGpDQ3lwUUdhMjFWV0RoZDdJL1ovaTFFWmJrZkJvbTJDK3dhUjNROGpuRG5rN2JJbXg4QWZqaE5jdG9TMnVhSC9NRGIzaWZvRFM3RlBZQkxtRDRiV1Voa2lHZmxVSUMwRjMwYkcwRE9OVXIwbVJDQ2FCMmNTK292dVE5UExEQkhXZjRQUWZmRVhmV0R3bGtuQldVemdkNkpnYXBvQjQrWUNwaWJvTWc1RUxhUGJJSHdjckFRcVc2aUJ2Q05ldGI2SmdVNUs3VWJNWUJ4WFgiLCJtYWMiOiIwZDQ2MGIyZjk2MGIxZWQzZDRlZjI4ODdkMTBmMzMzNWI0OWQ1MWM2NTE5OTljYzZkMGM2OWQ0MzAzYzlhNzVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhYSXQ3eXk3L203SFl5THAvWTRxZWc9PSIsInZhbHVlIjoiUUc1RjhwQmhnUE1GZFMwckdFMzc5aFh2SGwzWTdkdDcweVRvZS9KNVhiL29ueHl5UG1mZ1MrMnBvZ2NsQWhUQWo4WENsK3c5ZDBaNkx5SG9IckhYbERNazdJc29QaXNHV3Q1WkdrOFMzS1psNGRubjNBRzJhWW5LVXNlY1dqOFFLRHA0em4zRTBrZHVBVUVEbFJ6TGp6M0VLWlFxdThpK2o4RytKL3czUkg4MXJUV0RGbk5VQmpwR0ZhT3B4eTZxYzRsQkJ5WHp0MHh0TnBwQzhiWENwWFZQMXdqVmxwVnhha3dwZUJIYXBHQjhxMS8xZ1VpeHZjTFJYQzllcFk4MHN4M0ZMUVRuUTMvYlh0Q3ZIMFNoNEZqVnp0Yjg4d3hXQVRtZjJqdkNNc2dGU0paeG5DTEUxWVlrWWgwc1A5Y3YzWnI0aUZEeXk1R1ZXcG1Ebjk2TmZmNFZYMGV3Y3c0YlpKMVpnalBzR0h1OHVQQll2dHJxVmErZzZHWkNLOXMzT01rZzMwN3pDTEE3WkNBc0NsbmJkcXNJNnV0VUR3WS9yaEVhZEVGcFdNUkwwTThFRm0yVHdPRkhaRWNCNUVuQ2FPaGovN0taZXhGT1hJRjV5L0lQMnVlMkxlb1hLSUZRY1dIVnRFb1p1UFlrMG02SUVHaHEwTDlSNmtlMGRxTTMiLCJtYWMiOiI3NmEwZmRmY2JkYmQ2MTcxMTBjMjU5NzY4YWEwNWVhMzA1MWY0OTg4MmM4MDcwNGY1MjgyZGY4MWE5ZjJlY2UyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1678505499 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:04:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpnOFhvd3dSUm5pcnh3d1FMandFZkE9PSIsInZhbHVlIjoiY0hiUXZXR0pTMERrL2YxL2t1andyOWNXYlhmbW9zSWhXZ2tJUk4renlVRnR1QVVaamhUVjNIQ2NEUmFoamZSb09UQWowNWpjL3dUbmVzN2FSOUZVMmVpQWRobjB2S0xIbzNuaWNnZjgxYWhFeEs0eDRiZ09TbjNqdUkxVTh5MUNpOE9IRTV3R2ZnZGNMNFZKQUxLVUROZUl1aTNDQXRiQ2RpSEVSYU1TZ0hsbWlWczhUMHRLclhZWU12M3FxZUwvZjJSY3BYbnJjNzZSNWlGZVVJTmVUNE9nbkQ5Y0lWSjJFWXFzVlByNjk5WmNEZnFudDQ0RzFKVGNiZTVxNjRxQ2RoNURnSCtTeHdRTG9DdjA5c3Y2VUZWYW5SUmtMM1QrekdsdmNjak1vQXpsMUJPRzhYeXBleGtGWmZmSmR4OElkenRXOXBXOWs5WVBJcFM0SzFVNi85Vy9IK2lvbkluVkdqNEpkUTdqd2FzckZtKzlRcHUrTnBPVFdJWHZhcUM3K2tsbXJrZ3AxNHZWeXRnUEVrMHExcVVxTHdRcHFhZXUzZi9pNzMvNGt2YUhRTUpoM20xMXBWcFp2ajFsaFZubmplNCsxa2ZHWGJRL2YxSkFqMzBlQnlGRE5nWExUbXNFZzJyWWF5Zm5iS2JLSm1tTXNyditDR3d1NWhxR28zMDciLCJtYWMiOiI4YTRiYjliY2QxNThlOWExZGYwZTgxMGU4ZjhlNzM1OWZmOGQxY2M2MTY4OWUzNWNhMTNkYjVmNTdiNGU0NDA4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Inl1d0RaQjZaSnVWRlpHNHY2ODk1RUE9PSIsInZhbHVlIjoiVVBobzh3Q09maGliVGFTMU5udmNTaWdSZGcvN0NPTVM0b0c0aFNtb2gvVmxvS201QTZYeUtveE5pZll1SHFPcXcwazRxemVBeVFabytxMmxPeEluUks4c1BaelNQeDhRWkU3cnZoSGJDK1VYa1o4a2czTktvdElVdHl2N2FtaVBrSCs5MGJ4bEdjYmNjSHdyUlMvMEtNYlJ1cURnamtYQnJMTW11c05zaGNKQ1F0ZmhzTXlkRkFrdkxScmJaRlVoS0JKaDdoNFJ4bWVuSkk4Z0tvUkpMa1hpZ3dnaEhxYmEyTGxMR3ZGcWpUS2dXcE9pZ1Y2Vk9vek16QXg2MGFHUU1DVTIvZU50OXNUNjVuaEpYSlBnaENUTUNXMG56cThqd1NubXJXM2pqaWlzbzFGVHBQeVA1NXZub2RURGI1Z2c4ZDloa2xjNVhYWGovOFdrSWhQRmJFY3RjUU9kdHdRZXg3Mzl6TitXUk5ONXBtK2RmWXFIcUIxbkkyN3ptdXVFSmd6OWIvTThYbU1TelNBMTM0Qm9mV0d3YnZTcll0dURzUlFnNm10UmJDUkg4VE9BZk5KQ3k3WjFTYWVDY1labWZkNG45QWVPVTJxcE1HNkVaMjA3Zm9HQ25JNXRpK3RZUWZhWDkwM0dXdnhiYVlNUXhwbU96N0k3S3FEandjSmUiLCJtYWMiOiIxZGM2YjdmMmVlOTVhNWY1YjMzZDc5ZTZmNmFjOGY5OGI4NTMxNzNkYmFhNTc0YTUyZTBkMmM3ZDk3NDg2MzJjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpnOFhvd3dSUm5pcnh3d1FMandFZkE9PSIsInZhbHVlIjoiY0hiUXZXR0pTMERrL2YxL2t1andyOWNXYlhmbW9zSWhXZ2tJUk4renlVRnR1QVVaamhUVjNIQ2NEUmFoamZSb09UQWowNWpjL3dUbmVzN2FSOUZVMmVpQWRobjB2S0xIbzNuaWNnZjgxYWhFeEs0eDRiZ09TbjNqdUkxVTh5MUNpOE9IRTV3R2ZnZGNMNFZKQUxLVUROZUl1aTNDQXRiQ2RpSEVSYU1TZ0hsbWlWczhUMHRLclhZWU12M3FxZUwvZjJSY3BYbnJjNzZSNWlGZVVJTmVUNE9nbkQ5Y0lWSjJFWXFzVlByNjk5WmNEZnFudDQ0RzFKVGNiZTVxNjRxQ2RoNURnSCtTeHdRTG9DdjA5c3Y2VUZWYW5SUmtMM1QrekdsdmNjak1vQXpsMUJPRzhYeXBleGtGWmZmSmR4OElkenRXOXBXOWs5WVBJcFM0SzFVNi85Vy9IK2lvbkluVkdqNEpkUTdqd2FzckZtKzlRcHUrTnBPVFdJWHZhcUM3K2tsbXJrZ3AxNHZWeXRnUEVrMHExcVVxTHdRcHFhZXUzZi9pNzMvNGt2YUhRTUpoM20xMXBWcFp2ajFsaFZubmplNCsxa2ZHWGJRL2YxSkFqMzBlQnlGRE5nWExUbXNFZzJyWWF5Zm5iS2JLSm1tTXNyditDR3d1NWhxR28zMDciLCJtYWMiOiI4YTRiYjliY2QxNThlOWExZGYwZTgxMGU4ZjhlNzM1OWZmOGQxY2M2MTY4OWUzNWNhMTNkYjVmNTdiNGU0NDA4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Inl1d0RaQjZaSnVWRlpHNHY2ODk1RUE9PSIsInZhbHVlIjoiVVBobzh3Q09maGliVGFTMU5udmNTaWdSZGcvN0NPTVM0b0c0aFNtb2gvVmxvS201QTZYeUtveE5pZll1SHFPcXcwazRxemVBeVFabytxMmxPeEluUks4c1BaelNQeDhRWkU3cnZoSGJDK1VYa1o4a2czTktvdElVdHl2N2FtaVBrSCs5MGJ4bEdjYmNjSHdyUlMvMEtNYlJ1cURnamtYQnJMTW11c05zaGNKQ1F0ZmhzTXlkRkFrdkxScmJaRlVoS0JKaDdoNFJ4bWVuSkk4Z0tvUkpMa1hpZ3dnaEhxYmEyTGxMR3ZGcWpUS2dXcE9pZ1Y2Vk9vek16QXg2MGFHUU1DVTIvZU50OXNUNjVuaEpYSlBnaENUTUNXMG56cThqd1NubXJXM2pqaWlzbzFGVHBQeVA1NXZub2RURGI1Z2c4ZDloa2xjNVhYWGovOFdrSWhQRmJFY3RjUU9kdHdRZXg3Mzl6TitXUk5ONXBtK2RmWXFIcUIxbkkyN3ptdXVFSmd6OWIvTThYbU1TelNBMTM0Qm9mV0d3YnZTcll0dURzUlFnNm10UmJDUkg4VE9BZk5KQ3k3WjFTYWVDY1labWZkNG45QWVPVTJxcE1HNkVaMjA3Zm9HQ25JNXRpK3RZUWZhWDkwM0dXdnhiYVlNUXhwbU96N0k3S3FEandjSmUiLCJtYWMiOiIxZGM2YjdmMmVlOTVhNWY1YjMzZDc5ZTZmNmFjOGY5OGI4NTMxNzNkYmFhNTc0YTUyZTBkMmM3ZDk3NDg2MzJjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1678505499\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-807346283 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-807346283\", {\"maxDepth\":0})</script>\n"}}