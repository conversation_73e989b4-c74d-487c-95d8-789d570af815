{"__meta": {"id": "X463d6cacfe250352cfb0b799d7f44f3d", "datetime": "2025-06-28 14:59:06", "utime": **********.337874, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.904035, "end": **********.337888, "duration": 0.****************, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.904035, "relative_start": 0, "end": **********.282635, "relative_end": **********.282635, "duration": 0.****************, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.282644, "relative_start": 0.*****************, "end": **********.337889, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "55.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029000000000000002, "accumulated_duration_str": "2.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.309107, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.172}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.320527, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.172, "width_percent": 10.69}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.330536, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 75.862, "width_percent": 24.138}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C**********018%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik90cyttQytSVmNUQ3UzaGN2b3AwZkE9PSIsInZhbHVlIjoibDduZXBodi9EYmRLTWk2NEdGTlJDMmdYRXNFTytDWUZzcHA0RGhhaXVnakl3UVl2a2l5bC94d1hYbkZwMlJOc2llWWs4NWRDR1phRlk1ZzNXditTdjYzcnVvN01HeXJlem5LcGIvNG5ld293N21nT1ZxU3pZY21mQ3pITDZFbjJPUXpMS3owTjdPeXhsSnFaNnV5VjRicVZubEhkUjVuNjE1WjBkZ1YvNG5mSnFsTXFRalZsTWs2TG01bWxzT1BDU2hWbEw1TDMxcWM1SzB5M29PakF4UXQxeFhiM0JaVktxcFp3Mk53ZGFpbjFDM3NJK1ZQWS9DUnZrZXlONk5wVFc3cWMrSjJIaEt3TmVqbG9BMzlPQStBblkrWklJR2JqcGdmR09QL0orMFRua0pjaDhsK1poR0Q2NnoxL1Bmck56bE10a3pybjhvdHdRTExiR0gyMTJSUCtSU201bWw2U0pidHh0aWM1c2U2M00zM3lYVjlJMVVzeDJjWWM2VGJlbEpnMVFKWjNNMzV6OWV5cHMzNmZEK1RGeGdiWkM1K3hyQmtLdGc0UU9IWktsY0c1TWQvZVZZVElUWWpaNVFuczUzWnJLU2MzRGhUdjlGamJJK1VjRjhDZ21iTFpaWGtob2V5L3Zxb0ZQVmw5ZlpoZXhPK1hKNitPclJ0S3MrYlMiLCJtYWMiOiJmZjM0NWZjZjY5ZTBiYWU3OGU2YmNiMDA4MjkxMDJiY2ExODE4MTc1M2FjMjQxYjc3ODY2N2MyNmVkOGZlZmYxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5nb1FOdWhBcSswNkRRRmhFUGJ3T1E9PSIsInZhbHVlIjoiT1grRmZPWEVzZ2huKzliRE5kc0cxZWI0aHdDN2xHVHZGdnZpN3dyeVBvTXlIMlZhTzViTVpPbzd3cXlrZllENm5IdUQzUUxaZ1NiYlhoR0IxbUEvenFRNTdiQk1rZktOb0dIdEwvNnRDQ0tHQnViOERkR1VVMWtEUS81dVo0THVONGhDbmZrMzIrZ0pJbmxQVitqcVlZblI5OEwzMWFUQUNFMWV4ZmRqNzZYdk5Ba1VGalpaOU45bEhQMis4QTRLbEtpcGZzOUl3eFdBZVVPOVA1MzNjRkRXdkZKbHN2QlcyUVFPbHF4WjcwUmdjYnI2Q2Z3aWFKNURWSGY1TFBPODhvYlFFd0pkTDJDZlg2RDVoNWY1cVRtZ0x5ZW1COTg3aGE2VE1pZ2lPd1RTNENwRVR6M1BrUTJMZlRpT2FaS1NnZXV0VnFHL3E0d1hjcDE2Rlg2eDNHbStkbmY2NnBrRG00WWVNWG9PejJlY3dpQzJKQ1hLK1dVVFAyOFYreWtWd0xKMnN5cTBTb2JuaXBXZ3QyUW53WmRxUWFlc3A4Vk04b1A5THRTQW1uVWthWHlwOVNhUDdvVVYvTWNjNkVQNFZGNlpEUVpwWEtzUHJMU0VlamJSOFhDU3FMN0hocC9Oa3NYbm52aDhvVnF2bGI2NXhTcnhyR29jc1ZEalhYd2kiLCJtYWMiOiJiZTAzYjlhMDhmMWViOGZlMTk3OWI2MWZhYThhMTMzNWZkM2UxNGRjN2U2YjhiNTMyNjQ0YmY3YjFiMDFjODFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-136878971 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6nyGbgT6n8t00JbJ5CSgj7vkpXnxnyISs5XS6j6J</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136878971\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1099530972 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImEvb2lHV0NRdnBxdm10U25OcHJCQ1E9PSIsInZhbHVlIjoiL0lYdmN5bWl1em9QdVMydlZNYU5abkhnUU5HcWF0MDFyT25nM09vaWdZRHg4OFNSZ21CUXV3MlJwSEpJWFFuamJ4UjBKRUluVEZmOUsxaGZxQUFiVEFYdE5PMXR5cEJKUDg1N1JiQmdqcnJpV3FNYklHdkorbjYycGpnRFFQNERIbG9WRGlkYktGNk1uUUhLU2JYTjdPUEtpUzlvVXZCSWlmTCtZenlKeUFGaG8xUmdBUmRRa0xqcCtwVktlNTdseXk4WFlHaDBTVzNkMGttd2Z2WnFzLy9NNmQxSHowQ3owaG1pOVVCY2RGTHd5RVU5MXB3dnNYTXNOZ0h5RTJ1alB2STgzSWhhZ1E5L3RySEpmWFBOckw0MmVJaW9kV25MUHlkbXdGbm5tSXEreEc0RzZlZmV5QVVHRkltVG1PZlJ2MVdmSzllU1pnVlZITW55VW91cDZMaFdOcDdlekQ1WnNXT3p3K0dTcExobjkvanB6RkpTeUtDN1hhQ1VSU0hLL0RyNVRqOEFwQllxRG5PbEhlWHFTdHcyZ1NUK2RaSzNPL1pyL3ArdlBtTDZlb2pWOU9RWTY2Sk9lSWVaTnRCTVNwWVQwZi9KNUhwbGlxWEhTQUNINXc1dGR0NENxN1l1NnczZXJYY1dicDRCWWRtSVpIVktxWUlFNHlWSUFCZWIiLCJtYWMiOiI3Y2YyNmUwNTdmZTg4OGUyYmY2Y2RhOTUzZWY2Y2IzOGUxNzVmM2Q3ZTk4NTQ5YTUzZTBjNzk2YTUxZjAyY2I4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik5iaHZ1Vm9HMmZCd0JWaTI1UzVUWkE9PSIsInZhbHVlIjoiY1hNR0xvTndLU3lZU0pMdU5HaHVyZXcwS2NFUFIyODRVR3g1VUI5L25nNTBDMDFteG9ZNEdQRVJCRy9Yc09iWVZaM3JNZzVTZUFLUUoxNlZRSmxNbVlPWlMvL0pMbDVJM1FEVXhIenhPSCt2eEtFOUlNZGRDckNBRVBKb3YwYks4eTMzdEJKZmVDVFViRHAxdXY5YU9LUXZiMGpZM3pZeWNNSHdnVHAyTGNuc0Z4dkV5WFBWNmZVMW0vaFBSYXAxUngzYkxTQjZCdEJTdVVJNzVVb29OTS9BdjR5NWM4SEQ2QU1QTURZVHcraE9WRVo4d29kTzhKYms1L0NqZjMyTUZ4ZmpZMXdveS9TcTBRN2tZdDFydEJ5QUU0a3lpVmV0VHhBYVVjMXJqWnJ0U0h5MVEzNy9SalBYUkhTUHFPdklvN255dFJ0Vk8rcmhETEs2OVcySW1RMWcyY1o4dHpwNnA4blVVWFJOZzJPQzVLRmpIZjdVeGZhVXV3YVloVzRkZDFrbXQrSGlMU2V5S1BXbmpKaURUZkwwd1B0RXFDMnpoNEROSm1sejAxU1ZWTTdOeDRRL3Z6UEYwMHJpclRiLy9sSEJqNTZseWZsYkhwVDc2bjVickRGL1lHT0o1RGRFVVZrOWtWdGt0cENBMWdrRlByVzZxZWF1QXN3LzBmb3kiLCJtYWMiOiI3NDc1MDA0OTdjNGM2Y2NlYmQ1NmM1YjlmNDY5ZGI2OTdhOWFmYjJhNmIyN2I2NDFmZWY5OGI0YWQxZTEzMzA1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImEvb2lHV0NRdnBxdm10U25OcHJCQ1E9PSIsInZhbHVlIjoiL0lYdmN5bWl1em9QdVMydlZNYU5abkhnUU5HcWF0MDFyT25nM09vaWdZRHg4OFNSZ21CUXV3MlJwSEpJWFFuamJ4UjBKRUluVEZmOUsxaGZxQUFiVEFYdE5PMXR5cEJKUDg1N1JiQmdqcnJpV3FNYklHdkorbjYycGpnRFFQNERIbG9WRGlkYktGNk1uUUhLU2JYTjdPUEtpUzlvVXZCSWlmTCtZenlKeUFGaG8xUmdBUmRRa0xqcCtwVktlNTdseXk4WFlHaDBTVzNkMGttd2Z2WnFzLy9NNmQxSHowQ3owaG1pOVVCY2RGTHd5RVU5MXB3dnNYTXNOZ0h5RTJ1alB2STgzSWhhZ1E5L3RySEpmWFBOckw0MmVJaW9kV25MUHlkbXdGbm5tSXEreEc0RzZlZmV5QVVHRkltVG1PZlJ2MVdmSzllU1pnVlZITW55VW91cDZMaFdOcDdlekQ1WnNXT3p3K0dTcExobjkvanB6RkpTeUtDN1hhQ1VSU0hLL0RyNVRqOEFwQllxRG5PbEhlWHFTdHcyZ1NUK2RaSzNPL1pyL3ArdlBtTDZlb2pWOU9RWTY2Sk9lSWVaTnRCTVNwWVQwZi9KNUhwbGlxWEhTQUNINXc1dGR0NENxN1l1NnczZXJYY1dicDRCWWRtSVpIVktxWUlFNHlWSUFCZWIiLCJtYWMiOiI3Y2YyNmUwNTdmZTg4OGUyYmY2Y2RhOTUzZWY2Y2IzOGUxNzVmM2Q3ZTk4NTQ5YTUzZTBjNzk2YTUxZjAyY2I4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik5iaHZ1Vm9HMmZCd0JWaTI1UzVUWkE9PSIsInZhbHVlIjoiY1hNR0xvTndLU3lZU0pMdU5HaHVyZXcwS2NFUFIyODRVR3g1VUI5L25nNTBDMDFteG9ZNEdQRVJCRy9Yc09iWVZaM3JNZzVTZUFLUUoxNlZRSmxNbVlPWlMvL0pMbDVJM1FEVXhIenhPSCt2eEtFOUlNZGRDckNBRVBKb3YwYks4eTMzdEJKZmVDVFViRHAxdXY5YU9LUXZiMGpZM3pZeWNNSHdnVHAyTGNuc0Z4dkV5WFBWNmZVMW0vaFBSYXAxUngzYkxTQjZCdEJTdVVJNzVVb29OTS9BdjR5NWM4SEQ2QU1QTURZVHcraE9WRVo4d29kTzhKYms1L0NqZjMyTUZ4ZmpZMXdveS9TcTBRN2tZdDFydEJ5QUU0a3lpVmV0VHhBYVVjMXJqWnJ0U0h5MVEzNy9SalBYUkhTUHFPdklvN255dFJ0Vk8rcmhETEs2OVcySW1RMWcyY1o4dHpwNnA4blVVWFJOZzJPQzVLRmpIZjdVeGZhVXV3YVloVzRkZDFrbXQrSGlMU2V5S1BXbmpKaURUZkwwd1B0RXFDMnpoNEROSm1sejAxU1ZWTTdOeDRRL3Z6UEYwMHJpclRiLy9sSEJqNTZseWZsYkhwVDc2bjVickRGL1lHT0o1RGRFVVZrOWtWdGt0cENBMWdrRlByVzZxZWF1QXN3LzBmb3kiLCJtYWMiOiI3NDc1MDA0OTdjNGM2Y2NlYmQ1NmM1YjlmNDY5ZGI2OTdhOWFmYjJhNmIyN2I2NDFmZWY5OGI0YWQxZTEzMzA1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099530972\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1872746084 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1872746084\", {\"maxDepth\":0})</script>\n"}}