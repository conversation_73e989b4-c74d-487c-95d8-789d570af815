{"__meta": {"id": "X26a638371f044a6b2dcfedfd3cbd3b6e", "datetime": "2025-06-28 15:03:11", "utime": **********.594487, "method": "GET", "uri": "/financial-operations/sales-analytics/product-performance?warehouse_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.161919, "end": **********.5945, "duration": 0.43258094787597656, "duration_str": "433ms", "measures": [{"label": "Booting", "start": **********.161919, "relative_start": 0, "end": **********.517333, "relative_end": **********.517333, "duration": 0.35541391372680664, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.517345, "relative_start": 0.3554258346557617, "end": **********.594501, "relative_end": 9.5367431640625e-07, "duration": 0.07715606689453125, "duration_str": "77.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46178256, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/product-performance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getProductPerformance", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=493\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:493-600</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.025759999999999998, "accumulated_duration_str": "25.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5508611, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 6.095}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.560426, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 6.095, "width_percent": 1.63}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` as `category_name`, SUM(pp.quantity) as total_quantity, SUM(pp.price * pp.quantity) as total_revenue, COUNT(DISTINCT p.id) as order_count, CASE\nWHEN ps.expiry_date IS NULL THEN \"لا يوجد تاريخ انتهاء\"\nWHEN ps.expiry_date <= CURDATE() THEN \"منتهي الصلاحية\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN \"خطر عالي\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN \"تحذير\"\nELSE \"صالح\"\nEND as expiry_status, CASE\nWHEN ps.expiry_date IS NULL THEN NULL\nELSE DATEDIFF(ps.expiry_date, CURDATE())\nEND as days_to_expiry from `pos_products` as `pp` inner join `pos` as `p` on `pp`.`pos_id` = `p`.`id` inner join `product_services` as `ps` on `pp`.`product_id` = `ps`.`id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` where `p`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` order by `total_revenue` desc limit 10", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 536}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.563108, "duration": 0.02249, "duration_str": "22.49ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:536", "source": "app/Http/Controllers/SalesAnalyticsController.php:536", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=536", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "536"}, "connection": "kdmkjkqknb", "start_percent": 7.725, "width_percent": 87.306}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` as `category_name`, `wp`.`quantity` as `current_stock`, DATEDIFF(ps.expiry_date, CURDATE()) as days_to_expiry, CASE\nWHEN ps.expiry_date <= CURDATE() THEN \"منتهي الصلاحية\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN \"خطر عالي\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 15 DAY) THEN \"خطر متوسط\"\nELSE \"تحذير\"\nEND as risk_level from `product_services` as `ps` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` where `ps`.`created_by` = 15 and `ps`.`expiry_date` is not null and `ps`.`expiry_date` <= '2025-07-28 15:03:11' order by `days_to_expiry` asc limit 15", "type": "query", "params": [], "bindings": ["15", "2025-07-28 15:03:11"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 566}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5871608, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:566", "source": "app/Http/Controllers/SalesAnalyticsController.php:566", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=566", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "566"}, "connection": "kdmkjkqknb", "start_percent": 95.031, "width_percent": 4.969}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/product-performance", "status_code": "<pre class=sf-dump id=sf-dump-990697947 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-990697947\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1271485820 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271485820\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1832132000 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1832132000\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-205384410 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122972644%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxJbWxZL1A4U1crQ1BNTzIxbWNFZWc9PSIsInZhbHVlIjoiMmh2Mk8yaUJJOEhZUUE0ekJnZHBBbzJYaFlxdkJ4UDZLOThRN3BKc0tNRTZ4RnR4TDZ3WnVmRFVuSitiVWRrSVZycVBuTVBpOTYwQ09zOXQxdjBrSUV3Q0pKb0QzZmxIWFlqQ0hvNGxya2FwWWZabXB1cDlJaUs4WERzTDFzOForUFBWY3djV2xISThjeStab3dDbWp5V0ZXWkpVVm9aMU1OdHVoK2ZFWEJudVp4VmcyNHdWMlpFOFZkTjV5WG9SNmdwWWpjKzFLaE83eGg5Tm1HdTZISnVEOE1ENmJ1aE9scFM4Y2hieFBNckEvNzc0NzdzbW04MEFYMHhyMW1QRTNjSDB1eUM5bVRaVGlRRjNQcWlXU2hsVW1EbzR0VFpvdTNSbXJIZ1gxWVd1N25MV2J1ZS9Hd0ZYSEYwOE5VWWVHQ0d1RFZDUUpLaVF2TTBxMUhiekVpWWJDWkptTFVHTkhsODNLU29tZ0VERzZacmZVTHZRZXo2TmZXdDNMaHpOKytBcm1CMS93MjBCOExzMXQrdldNaEJIWERqeVlZWCtYdkhBYnBLMUV0aldiSjRRU0xqRFRpaTRRVk9jUWErdHEzL0hNWExoRDkyUkw2cnNUaHdQUy9UdnkwNVVMaWRkRm4wWnBuZlVaSDQzN2p1TXl4bkJiYm03cTFPOUtDYXEiLCJtYWMiOiI3MTliM2YyODQxZGQ0ZGMxZDQzYTA4ODc5OTdhNTg4N2E5YmJkNDQwOTZhYjhiNTQ0MzBjNDFlODc5N2M2OThmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlAyZXNLVkV1ZFIyOTU1Mzd5eXFEMGc9PSIsInZhbHVlIjoiYmNnRTJ5Ri8vazIrLzE5a0ltZzM2YnlncTRyUUxOaS9GOFVhZHI5blJnclgyQ05hRFo1YVd6amFDR0ZLYnZOMGxjajVERkQzNGtRTkF2TEVBd09UOW1lMldMMDY4T3ZCNk0rd1pYY2hCYjFIaHdNUkphVlNadXJTaWFSVmtHVjFka1VycU1IRGFGc0l4UDl0aEpaR1JETU9BMDh1dEpZYkNvdmo1UExGczBaYzVMYkc3ckpyUEluMmpEVk05ekNqczNyVk5pWXVrUUhYYW1IcFNQaVNMam1KcjZBVjcrUzJZT3pTMXE4YS9qbjVGWUd3ZGJaWVFYZlFadlZ6cGtmajg5R3N2dmdOOTFvOTdUTTB1Z1BBclk0RENsQTRPeXNRNm9oeUxDblpKdmU3Y1dmSkZlNWh5ZGRWYkFVTm5KRFU2N3lGbGtxMlVPanZSU1prQ01XZHRGUjJSNm5QQ2J0cHY3czJLQUNXUTAydEpqekQyNnJGeGlYY0ZBbDJDSTNaUGszeHBSdkVlcXlzN05ZRlozNzVkbWZqMEh0bHpBS3RyY0JlQ1VLN3FrQ2xWL3hRZ1plYlcxYU5HQi8xV0xEdEVZYmNUOHU2bTVZMEhhQkhXVXkzSE1xV29aNVZxSjR1S0FqNnIwUkhHbWxTMHFvOGQ1aktSdGNrVXN6Mk9PT0wiLCJtYWMiOiIyYWJhZDA0NmZlYjMwMTYzN2NlMjI3NTY0NjE5ODJjZmUzOTE0MzExYzI1NTFmYTE5NTUzMTQ4Y2FkNmNiZDc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-205384410\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-614287744 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614287744\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1952141358 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:03:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZxV3VERDNFdjNiQjF5WEtLUnpjRkE9PSIsInZhbHVlIjoiN0t2SE1wTnNKRmpKdUZQdVRFejVvVW9vc1VpWkRSQ0N2NlhsV3dmRG9aTG1uVTZKYThyVUY3VFM3SFlEQVBYZTRYOG12ZFF5NS9ha1hoMGtLdDA2ZUdYNDNXM2xvZFJKMUlmQjZNbHRISncvOWEwRi84U1Y3NVNrQys4aXlQVlo2blU3WkhWN2ZNUTRtL3VscWZ1c1pxdllwUGdQT3Zma2NzRlBHcHJJS0Y0dFNKTjJkRk5XYWhVSVN4TnV3VFh2emszTTlMbUZxRitkcjJKcFRTaFRQQ3VFQ0xPR0pUS1JqNUh0aWNoMXY4clJuVnV0eGdxcmpLM3B5U2tWQ2tkK3p2d2l4TDNNdGxTWGp3dDNLVnhWZjJxRWU5WEFDdVJkV1Jja1ZMelZ0aWFjQlkzejNyL1FLMUw0dHo3bjRlRjN0d2xEbHZPeGNFdmNUWDQxeVRCUUZnaWZScGp1SXBQU21jNDFFYWE2clltcjBvT0hyY1ViWlM3ZG4vZE9oZ2JVdzZYSElEQkFRWmc3TnowbEpNaHlZQ2syWXBmMzZ6c0FMVTBTVk51SDBSVktkOW9nUmNldFNCdWF3N3dzdkd1WFdSUUVETXg2dDNFQWZLWXVRTjc2ajNoRW5wSzh1Y1Rma0JUS2psNGVrZ01QZGxIMG1acGJoclAyVzRiaG9UeVYiLCJtYWMiOiI4NDVjODRiMWIxOWRiMzMxNmUxODc1ZTc1YjlkNDIzN2Y2ZTBkM2Q4OWU2YmNlMzc0ZGUxOGI2YTcxZGZjZjFlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFMSjlEajU1TENDN094RnI4UDR6cmc9PSIsInZhbHVlIjoiVm9xV3RKRnRDVGtCcy9Fb2tJK1NhQ1BXQUppNzBFN0xQYkpoM1MxYUYxRGZhd0xXbDhhTjZLQTdtRmljRmpYWjA4SW5iNkF3bC9qS0RWbjhxcUF5WndhZkk3RG5VN3I1ZE9DaG1YR212b25zYStUYkV3VkFwV2htZnJ4WS9YbG5VUHpuUWJXNllsbG5QOWh5ajlkTkdPSzJwcjRGYTEyczI4QTZUYkZuN0w4NVVuelQ0NGkrSHV6UTgwY0JkOTBRWURWN0J2NU5DV1JicXFvUE11OGU5SmRQcFRNdHdxSHBoUGhraExFT3B5VzhTSitKYi9UTzlGK0FvS2RZdWQrdEZCOW8ybDZEeEs0OEl5RUZiSlNHN0ptQjR5VUVXT2p4aHAwVytmSGtXeVV2ZTdhNHZVY0VqNjRPVDV0WVZWWExLRTJuVmN0dmF5VVFzRkdCa3hybSsrQysrcFB3enBVSjBJK3RqVm5WMmN2b3pzVEFnQ2R1Mlk4NS9xRkFDMDBpSG0wbkpKVlVHcGl6TGE3N09uNDBQWDUvT09IaDFiZ1owZEVOY0Q3OVRtdndUNGRrT2pFbERldGN1OHdyelYzcFdFRUU2M1V5cVEvTVROM1o0U28zWFRmZGl0Uk9kbGRIQTd6b2M1NTF4Nm1uVW9Vc0t2ZXpOZ1hma21EWFY2ZEUiLCJtYWMiOiIyYWMwN2IzMjUyMTM3ZjlhZGU3Y2NkYWVlOTZiMmEzNzVhMWE3MzA2ZTI5ODYxN2VlZDMxNTZkMjJlZTM5MDAyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZxV3VERDNFdjNiQjF5WEtLUnpjRkE9PSIsInZhbHVlIjoiN0t2SE1wTnNKRmpKdUZQdVRFejVvVW9vc1VpWkRSQ0N2NlhsV3dmRG9aTG1uVTZKYThyVUY3VFM3SFlEQVBYZTRYOG12ZFF5NS9ha1hoMGtLdDA2ZUdYNDNXM2xvZFJKMUlmQjZNbHRISncvOWEwRi84U1Y3NVNrQys4aXlQVlo2blU3WkhWN2ZNUTRtL3VscWZ1c1pxdllwUGdQT3Zma2NzRlBHcHJJS0Y0dFNKTjJkRk5XYWhVSVN4TnV3VFh2emszTTlMbUZxRitkcjJKcFRTaFRQQ3VFQ0xPR0pUS1JqNUh0aWNoMXY4clJuVnV0eGdxcmpLM3B5U2tWQ2tkK3p2d2l4TDNNdGxTWGp3dDNLVnhWZjJxRWU5WEFDdVJkV1Jja1ZMelZ0aWFjQlkzejNyL1FLMUw0dHo3bjRlRjN0d2xEbHZPeGNFdmNUWDQxeVRCUUZnaWZScGp1SXBQU21jNDFFYWE2clltcjBvT0hyY1ViWlM3ZG4vZE9oZ2JVdzZYSElEQkFRWmc3TnowbEpNaHlZQ2syWXBmMzZ6c0FMVTBTVk51SDBSVktkOW9nUmNldFNCdWF3N3dzdkd1WFdSUUVETXg2dDNFQWZLWXVRTjc2ajNoRW5wSzh1Y1Rma0JUS2psNGVrZ01QZGxIMG1acGJoclAyVzRiaG9UeVYiLCJtYWMiOiI4NDVjODRiMWIxOWRiMzMxNmUxODc1ZTc1YjlkNDIzN2Y2ZTBkM2Q4OWU2YmNlMzc0ZGUxOGI2YTcxZGZjZjFlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFMSjlEajU1TENDN094RnI4UDR6cmc9PSIsInZhbHVlIjoiVm9xV3RKRnRDVGtCcy9Fb2tJK1NhQ1BXQUppNzBFN0xQYkpoM1MxYUYxRGZhd0xXbDhhTjZLQTdtRmljRmpYWjA4SW5iNkF3bC9qS0RWbjhxcUF5WndhZkk3RG5VN3I1ZE9DaG1YR212b25zYStUYkV3VkFwV2htZnJ4WS9YbG5VUHpuUWJXNllsbG5QOWh5ajlkTkdPSzJwcjRGYTEyczI4QTZUYkZuN0w4NVVuelQ0NGkrSHV6UTgwY0JkOTBRWURWN0J2NU5DV1JicXFvUE11OGU5SmRQcFRNdHdxSHBoUGhraExFT3B5VzhTSitKYi9UTzlGK0FvS2RZdWQrdEZCOW8ybDZEeEs0OEl5RUZiSlNHN0ptQjR5VUVXT2p4aHAwVytmSGtXeVV2ZTdhNHZVY0VqNjRPVDV0WVZWWExLRTJuVmN0dmF5VVFzRkdCa3hybSsrQysrcFB3enBVSjBJK3RqVm5WMmN2b3pzVEFnQ2R1Mlk4NS9xRkFDMDBpSG0wbkpKVlVHcGl6TGE3N09uNDBQWDUvT09IaDFiZ1owZEVOY0Q3OVRtdndUNGRrT2pFbERldGN1OHdyelYzcFdFRUU2M1V5cVEvTVROM1o0U28zWFRmZGl0Uk9kbGRIQTd6b2M1NTF4Nm1uVW9Vc0t2ZXpOZ1hma21EWFY2ZEUiLCJtYWMiOiIyYWMwN2IzMjUyMTM3ZjlhZGU3Y2NkYWVlOTZiMmEzNzVhMWE3MzA2ZTI5ODYxN2VlZDMxNTZkMjJlZTM5MDAyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952141358\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1994609188 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1994609188\", {\"maxDepth\":0})</script>\n"}}