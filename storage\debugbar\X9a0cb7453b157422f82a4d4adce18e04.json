{"__meta": {"id": "X9a0cb7453b157422f82a4d4adce18e04", "datetime": "2025-06-28 15:49:57", "utime": **********.191725, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125796.786056, "end": **********.191738, "duration": 0.405681848526001, "duration_str": "406ms", "measures": [{"label": "Booting", "start": 1751125796.786056, "relative_start": 0, "end": **********.120577, "relative_end": **********.120577, "duration": 0.3345210552215576, "duration_str": "335ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.120586, "relative_start": 0.3345298767089844, "end": **********.191739, "relative_end": 1.1920928955078125e-06, "duration": 0.07115316390991211, "duration_str": "71.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49601168, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018420000000000002, "accumulated_duration_str": "18.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.153586, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 9.555}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.16346, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 9.555, "width_percent": 1.846}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nAND p.warehouse_id = 8\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`warehouse_id` = '8' order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.166371, "duration": 0.01632, "duration_str": "16.32ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 11.401, "width_percent": 88.599}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-1483500592 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1483500592\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1354208146 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354208146\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2074741169 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2074741169\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-648209069 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125788432%7C28%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1jeXlXMWNQSGpnSkdYUmhXak5hZlE9PSIsInZhbHVlIjoiUExPVVRFdEVDWEl0WS9KWGIzZGFqT2xhZ3BLcFNLTlVKLzdkK0s1TWtaZzBrQ1FZajdEODJwQzh2VDlycUNlcm0wMzJacFpLRmtnNG1rNnpPZnN4SStWWGZFR095dy9CY0hFcXhUcVB6VWFDL3FKVENSWHdVbld4OHZ3bkVFTlZlK1BabDFJbzdFYXdWL0ljMjFVNHBEdy9rVTh3dHpocDdQQXoyMU56S0hqWDMwZE41RmcwNnJhK2VkeXhOalh3ZmZaM3hFdFBOenJhS2RGTnZWaEE4SWRhYlg4OHZvRVJrUCt3bnZpZDNwTy9tUzhoeVRtamtFcTI0ckRSMUNtbitkcitOUk9yb1JpSTVlbW56WUpwMGdUQnhodjBtem1tT2NkZVFVYTE1QThkbWdLVW15Vzk3VlFzZnE5ZEpacGZRM1pkWU9XREpFL205NWZqaHhwRDJQUGZyamRFZnZDdklQbjlkZzF0U2tkb1JaTW9nL0NUWUlqdEFLdXZXOGZXMCt5YW1WYWZCSzNpTk1GQUQ2TkVnTmdBZlRYZUxqdXQrSVM5RTJyc1hybklIZnFqSHRDN0RJZUEvR3cvcFJFQUxndC9wKzBOSyt4TTFjY2Fud2F6bTdVUHB4aDJNT2VReW5sUFRDRXVIYXNVeTRxSGp5MDhGUGFYVnBoMEtEODQiLCJtYWMiOiI2YWJiMTIyMTczNjMwZTBhMTlmNmRjNjYyOTI2N2Y3ZjRhYTVhNzkxNDEwNTQxMTJhMzIwMTM5ZDAwMmM2NDBiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZQNUFBWXU3OFpVdGdQbmNSUnZRQUE9PSIsInZhbHVlIjoieHo1RXdUVVkvd0dJSndPS21tYUt6L3hTVGtwL1hCM2VTYWpteE05bmlkOG9SbndlVjBxaWRYdElaNk1MeGphaThUbDROOTVpVm54c29aaCtQYUNsR0ZsSndsMlpMb0huZ3E0T2RCZGpab3I3YkRYVnRDU2VqV2MvcFgzN0RHK0xiRFV3eEh3ZUNoNm9NUEtVTXpIVGUzbmZHWGp1SmlRY2JGTEx1ejUyUjRKQ0Ztd01LcSt2MGpuZC92ekhnZ2ZyVDhQUVM1RHdHR2QzU3k3T2FkR1FRVkk1NE5SZjcwaGI4Zk85OXhhWUpLTHVsOGY3U2hEelpaR0JPZGM1TmJ1L1NqdzlhajhQTnFnc3FKcVprdXRublN5YURLWW90ZzdlN2d2cEg0Zkg5RGdLM3liSWU5ZmdSYVJJamNwY0tBUFRqUS9nQU5VeTBoU1o0TzJCdmo4c25pSUFtZUVnQkV4d25MSVhjcFZqL0NJWjlveUZLaThJQ2NCZEt2VlpvZXVrTTJwWlAwZFdWRFRQak9YcFJDcjB0VEdDd2NSYUtlU3FGY2x6N3JzcUFpeDFMOVJZbzNwK1NVZnMrcEdnSE9PdUs3QXM5TWFnbjlTTkZvTWpxckdvWDJXb29mbGtLdFh5Nmxsczh3eitXYjFxZHQ3OXRldnFXdTFYRWh2Z3NhYVMiLCJtYWMiOiJkNTdiNDdmYTdkOTU5YTJlYTcxYmIyN2ZjYjgxZWU1ZTI5NDUxMzAxYWRlNjM1ZTc2NmM4MTg4ODhhNDBkNDRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648209069\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-911735452 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911735452\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijk1OEMrMVhGWDFoWFkzcHRyR1NJUWc9PSIsInZhbHVlIjoiUUtFZndXSkhVcElNVUkwVkRaU21hdEU2R2dhbGU0YTRZS0kyQzZMM0FhQ1JTY1BkMVQ4UlhEM21MajhMZXVTSGdTbXdVL2dVbFJyS3BOV0thYmtvODFJYmdwMDBHSVl3a29ma0lOcWwzbU5pRUhLa0RORmdGZGxLOTVSaGppVldKbkRMTVBkeDRER2NxYmVCd25mMFBablppeTNxczZvQkFPNTdjZnE1WTMvWmZJY0tkTEZLcUxJQURkRGxRdE0xSjZTZy9uV0lWUlBRZW1EVDhOTWVTM0dtNEEzak92V3RwUzJkeFNMR29kWlVKSkhnR0lsMEFaNEFGNzZzMThsbmx2eGFhUlJDcWlkeXl4UnpSQ1ZVdW5hMWR4TnNJSU9FMGR4U3Q5T2g4R3lDUWd6clJNemtxSGtBSkN3dXYyL3h0eEY0MmNrREtvYzdmSGF2b2ZqYlFkYmNDQ3NWTzBFR1lBeE5SWFljUEZlUXp2V3dXNnpRT2h3cXJKaXRCbEtGY01maWJlY1ZsV3ExUk5ub2w0LzhHZGE1V3BWemtycTBLa0xvKzlqY2dtYjREZUpuSWtUQytQbU1UU0V5clVoRWJuUnVUMzI4eGpsbmxLRDltTWJRVVUwbU5sSXBwYnhOVEhYcExVYXd3ZndiRmNndmQ3OVFaTUJORnVjM0RzOUMiLCJtYWMiOiI2ZGVhYjRmZGJkMDgzYzE2MzE1NmNlYjM4MWYxYmMxZWYxNmQzN2YzNTU2Mjc4MmVhY2E2MDhkNjc5MDU5YTRmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InIzbFh3NEZpZlRhMVJLMGcvdXI0Z1E9PSIsInZhbHVlIjoiWGRsM3B3SGp3bFNnTzJNQnNsZ2h3N3FmNDh4WUhhalQ5VmxCZkFqUkVQNVgxUjFlOWFaWFJhd0pzTHJPaUJoVUtFOVhtUzJFa0VuWUVwZDR5MWhPcVNha1o1ekdEVzhkMWQ1alJEWjB4RGtyNFdYd0RqaFZEKzEzWHYzU3FOWU5rcmM1N2lHWTdraGF1QTJCZXJtOEZ3Q3h0WGVkVDBSNXA3VnFFTmdxYjQwM1kydjkwdjZDRTFNamNFdndLdW5uaHY5WXEweWFTNytqMGZySG1ET1poRUFudU9qeUM0K3EzQ2x5Vmk2VHlFM1JodWlrT0hPTEFIWWFnSWVsbm9rNVZaSTF6TFc0eUF5SmdhZjdvdnQwZGxqTHU5cHQ5anpHMHl5ZE45ZTJVajc2VW4vSWVKMzY4UUJVaytac3BSNjNmaEFtUCthZTk3TjdCbEk3OVFKS2xBK2sva2lQQ1I0TmV5K3hSV0x2UkprUno1R0VmSU9nZm85Q0RPZEREaW0wSnJaVE5vUnJGVkNCbDNIQ293bHAzdGJoalh1YTFicG5qZ1lLdE5jZXFIMmVEMEFmTXBUVEhTMjdzS3hjRnBnSjVjYWRHT2ptcmo5V3o1U1hIMW9pT1RMY2dVdDNWRHdEdiszQlRqNTl6dzF1MkdsdWJNMnZ2TlRPbHNqdkhLTGIiLCJtYWMiOiI0NTg2Njg1ZmI1NjY0ZDcyYmZlNTE5ZmRmNzQ5NGNiMDFjNThiMTg0N2MzYmY5YjY5NzM1ZGMwZTU2ZGVmMTM1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijk1OEMrMVhGWDFoWFkzcHRyR1NJUWc9PSIsInZhbHVlIjoiUUtFZndXSkhVcElNVUkwVkRaU21hdEU2R2dhbGU0YTRZS0kyQzZMM0FhQ1JTY1BkMVQ4UlhEM21MajhMZXVTSGdTbXdVL2dVbFJyS3BOV0thYmtvODFJYmdwMDBHSVl3a29ma0lOcWwzbU5pRUhLa0RORmdGZGxLOTVSaGppVldKbkRMTVBkeDRER2NxYmVCd25mMFBablppeTNxczZvQkFPNTdjZnE1WTMvWmZJY0tkTEZLcUxJQURkRGxRdE0xSjZTZy9uV0lWUlBRZW1EVDhOTWVTM0dtNEEzak92V3RwUzJkeFNMR29kWlVKSkhnR0lsMEFaNEFGNzZzMThsbmx2eGFhUlJDcWlkeXl4UnpSQ1ZVdW5hMWR4TnNJSU9FMGR4U3Q5T2g4R3lDUWd6clJNemtxSGtBSkN3dXYyL3h0eEY0MmNrREtvYzdmSGF2b2ZqYlFkYmNDQ3NWTzBFR1lBeE5SWFljUEZlUXp2V3dXNnpRT2h3cXJKaXRCbEtGY01maWJlY1ZsV3ExUk5ub2w0LzhHZGE1V3BWemtycTBLa0xvKzlqY2dtYjREZUpuSWtUQytQbU1UU0V5clVoRWJuUnVUMzI4eGpsbmxLRDltTWJRVVUwbU5sSXBwYnhOVEhYcExVYXd3ZndiRmNndmQ3OVFaTUJORnVjM0RzOUMiLCJtYWMiOiI2ZGVhYjRmZGJkMDgzYzE2MzE1NmNlYjM4MWYxYmMxZWYxNmQzN2YzNTU2Mjc4MmVhY2E2MDhkNjc5MDU5YTRmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InIzbFh3NEZpZlRhMVJLMGcvdXI0Z1E9PSIsInZhbHVlIjoiWGRsM3B3SGp3bFNnTzJNQnNsZ2h3N3FmNDh4WUhhalQ5VmxCZkFqUkVQNVgxUjFlOWFaWFJhd0pzTHJPaUJoVUtFOVhtUzJFa0VuWUVwZDR5MWhPcVNha1o1ekdEVzhkMWQ1alJEWjB4RGtyNFdYd0RqaFZEKzEzWHYzU3FOWU5rcmM1N2lHWTdraGF1QTJCZXJtOEZ3Q3h0WGVkVDBSNXA3VnFFTmdxYjQwM1kydjkwdjZDRTFNamNFdndLdW5uaHY5WXEweWFTNytqMGZySG1ET1poRUFudU9qeUM0K3EzQ2x5Vmk2VHlFM1JodWlrT0hPTEFIWWFnSWVsbm9rNVZaSTF6TFc0eUF5SmdhZjdvdnQwZGxqTHU5cHQ5anpHMHl5ZE45ZTJVajc2VW4vSWVKMzY4UUJVaytac3BSNjNmaEFtUCthZTk3TjdCbEk3OVFKS2xBK2sva2lQQ1I0TmV5K3hSV0x2UkprUno1R0VmSU9nZm85Q0RPZEREaW0wSnJaVE5vUnJGVkNCbDNIQ293bHAzdGJoalh1YTFicG5qZ1lLdE5jZXFIMmVEMEFmTXBUVEhTMjdzS3hjRnBnSjVjYWRHT2ptcmo5V3o1U1hIMW9pT1RMY2dVdDNWRHdEdiszQlRqNTl6dzF1MkdsdWJNMnZ2TlRPbHNqdkhLTGIiLCJtYWMiOiI0NTg2Njg1ZmI1NjY0ZDcyYmZlNTE5ZmRmNzQ5NGNiMDFjNThiMTg0N2MzYmY5YjY5NzM1ZGMwZTU2ZGVmMTM1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1554433409 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554433409\", {\"maxDepth\":0})</script>\n"}}