{"__meta": {"id": "X5afd13e71cb7f391342d3923478063b8", "datetime": "2025-06-28 16:03:52", "utime": **********.800332, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.412571, "end": **********.800345, "duration": 0.3877739906311035, "duration_str": "388ms", "measures": [{"label": "Booting", "start": **********.412571, "relative_start": 0, "end": **********.741316, "relative_end": **********.741316, "duration": 0.32874512672424316, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.741328, "relative_start": 0.32875704765319824, "end": **********.800347, "relative_end": 2.1457672119140625e-06, "duration": 0.05901908874511719, "duration_str": "59.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45853280, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00766, "accumulated_duration_str": "7.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7714338, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 21.671}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.78114, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 21.671, "width_percent": 8.225}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*************%' or `sku` LIKE '%*************%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*************%", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.783917, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 29.896, "width_percent": 32.376}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.789191, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 62.272, "width_percent": 33.42}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1995}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7938871, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1995", "source": "app/Http/Controllers/PosController.php:1995", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1995", "ajax": false, "filename": "PosController.php", "line": "1995"}, "connection": "kdmkjkqknb", "start_percent": 95.692, "width_percent": 4.308}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1813055436 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1813055436\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1732519215 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1732519215\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-772536126 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-772536126\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2014492224 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126629815%7C23%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhneklKUmRPNGlveWRBK1laUzlCVUE9PSIsInZhbHVlIjoiQTJ1TTk5bTdtZ1dtTFlYai9GSS9TcmFXK2NKZHhOYUhFd00rVVpITkhqRjJpSEhqMndaTjZDaFlPOEJoVTA0NU9HY2krVzJ3YzNUTmNaRVBzS2RMNzVIVGx2M2pTWTZvS0hTTU5BUlVLQzdPV2t6aUtmSEd6Skg2VHk5Z0pDd2liSlNZV3JVbnA2ajlWNWwyS0tCcXVkbzlmQzBHT3RSWDFQTE5PVFpob1pSU0IwNURUTVo4OHB5SncxVXMwMEdTT2xPUjZtU1JxUkM1TWRNU2ZEdVBBTXhQOGxrb25GaXErbG5hc1NFb3lzcEdSd2cxQ3l3R25lcVZsY2ptQ3NTL1NqeUE3VWtMM1lBV3VJQlFlVDlyTlFRVzA5eVFqSkpmdjFrdnpPa3lZZkY1dGRWUElBSUdVQ2o5T3Roc3BWZnNrQ1RoMmhEWG1taXZNWDZMWk9pV0ZKQng0SDJvcm9UMWs2WnBRODRwOXhGdzd2L2VyMGozSGF5U2haNE5Qb3VhZE1yQ2gvUS9CYkVZWDFJYUlDS2lWWjlaa3UwLzJWZUs5aWU4UHNRUmk1SXpEVTdEd2N4UG1RRzhzdHNrclhuNXZzZGN1emgwUGVkVDJ0cExMRXZYQ3hudjRWb3V4aldqaUlXSUFOTXlxNHJkTDZFQkNPb1JEL2xyYkt0bURWR0QiLCJtYWMiOiI0NmIxZjM0MmVhYjBkZjEwZTZlMWEyOTY3ZWNjNGU0YjVhZjA5Y2RhZjIwOTdiZTEzNDI2OTJmNmIyNGI0MjUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InQxRVdGTmF2cWM0ai9rbjlSNk9Pa1E9PSIsInZhbHVlIjoiWmNiZkVNK2wzclgrTzY2VExIZ1JodTQrbDUvZjFNQTRSNUxVS2ppYThHVlZWelVDM3k5eWh4VnNScXVVdWlxMkZEVEt5YnY1RVdpYzJMeGw4NVprMU8zeXJmTlBTelBwQVF1cWJvSEhCWFhXU1BRSlhzMDVUQU9aUFRtNkJwSTRTcUVneHhkbXZYcW41NjFjMVFaeGRtNHQ3c2RPTXRYUlU1OXRsZ0RRTHJ3M2RuWS9BS3FiLzkrOUJLbjlFeG0vbGRLVnVCWTRWY1N2QTJNT2tEcUNhaStTNUZpTFRtOUFHNkQyY3BHQS8zVEExOVVTSE5yUS91UW16VXdNaUg5T1Rpc3NGSm5BS3dzTzhpejZLVHFPMGo4bFhQLzREdDNHajNVbEJxYVZDMHdPZGZHUmR3dUpNWlFmOEY0dEF4MUhHbldzcGxwbVNaWXQ3YzBzZ2tXeTk5VXptbkVxem5FaG4wbHI4QTBRZnlvZTNNZ2JuSmlCQkhHRFVrQ2gyL0NEYmlVMkd6c21zR3R4eHRRWXJ4L01OcWFZOERFZ0FFMGhHQzFFUWlrc05QT3dFTDhQbDZtT2F1VllSUUJrTW50Vmp0bDZsckJ1U09GbE92TEJXajgwbGJ0STQrZ1IweVhiYnRWTWtFWUlkbkpXa0svUzJVZklwbXNYayszSnhxMGsiLCJtYWMiOiIxMmY4NjViZTgzZWZkMWQ2NDY0MjFlNDk3NDllY2ZjOGVjNDBlMGU0YWY4MjljN2M0NDg4YWJiZmU3NzYyMjM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014492224\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1211172810 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211172810\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1952659541 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRzYmFSV1RrWXU5K052TFNScVNWZ1E9PSIsInZhbHVlIjoidTVveDJBZ3RmK0p0OG9sbkMvVEVaUkIraVh1RktlQ05KMWduSWR0dkVoWW5mYVBTWi9DSWU2Z25XNDkxdG50Y1JrT0xCVjhDeWR6MUJqZXNsblVGSW9sRVUwK2RZdzBPWDF6YVRsa1lyS0pwWHlhdnp2OHF1Y01vY1gzTnVpeTdFaXg0M2UybjJ1dXNwZTFQRkRlTHhpY2k0dWFJaVZZMExCSXQ5QnJXeVZRTHRJZ3l3VEtaTTRzMElPUFdlRDFRenorQWJRYTNOYjBFM05kYVVqbHowdG44WUFwVmhIajhvY3RMRG5CSnVQSzV5aU1KOVRhYzRrSzdZUER0SEpwMjYxN2tLVVFFa2lrVG84anR6UngyZHBzY2dabG5LbkpCU0FBRnU2MXlLVjN2endneDNub0l3SnNic25nSWtqRmF2SFlvMlo1dHJvZklHbTBxcy9lb2tTRU9CN296enBlY0JaRjBkcVpuSjhKNU1LU2h0eUJXRlJVekJQdTNkTEY5LzlLbHVBRUZTVE95VzBrTTNHT2hUTnJYZnM2TkNFMGJJdmxHVG9FYndtZHVsbmRlSEF3ZlJSWlNZREJBdmdjc251WFVxOGxvSUk4OENPZkp3V3dSMlgvV3FSZVFTajRpQUpibzFBdjByMUpaMzdJMGJmL1NxWFVCYlFOUGVsbm8iLCJtYWMiOiIyMDBiMjZhYjcyM2YwODBjMjM1YjJkODFhOGJhOWY4OGQ1M2ZhNzI5MTEyZWM1MDFhNjQ5YTliODczOTJhZDhmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik02NWtqQzZyWm9xdHlSbFZTT0ZndEE9PSIsInZhbHVlIjoiYVFKall6ZXJLR0hXbFVibVB2SnRCeW5JZDFnWEQ3QUtlY0JsNHdENXI0RVNNb2RqbDU2VDVpY2MyRkErbDYvU2krWElVRm9tYy9NV0Q1dDNKZjkyM1J5U1QrMjA5V1FFc1R4T2x2MkhqdzViK2l4a0RLa3kvaU0reE1vRFM2RVMxUzEwdUZ0ZFRZenRPOU9mSjdBY0ordlY2Z2d0ZEl2MnRtUTZ4dlJ4VVMzWUdFazNOT2ZFNnF1SDg3S0NCRVk4eWxuYzYyZi9NdFUzUXBPYklUWVJHMURIVWhxbTlIdEQ5dDJ1SzUzWCs4MDgvVkZ5UC9MVnFKZk1hM1lPbDVSZkJQdWowMTFKT1llMnVNamJ2ZHBJMUlNZmNJMmNUS1gzZTB3dFpqZ1JnUmNtTUFETE1NcXBrQkJoY1dtWFVCVm82eThER0krSlZNeHhSbHVwNFV4MElPbzI0d01WN1Z6TEdpTW12U1JVYWxTM2RMWlBraU9CdEQ4VmM4ZUJ3UmdvSllaaW81aUNMVFl1L3BONGpYODBCVlFLRkRkUUNQMEE2VDArSzBQOUVjdGxQNTVKdzJ5bXdWNVBGQXJQTTVHSXcwVG03TU9XNlBVWU1GTnR2eVpNNnVsalYxSS9VYmptVVUwREJMamtuTVFMUDBNeGFXT1p5MVFvMTZ0dnFjbTIiLCJtYWMiOiJmY2Y3ODFhMTNhYWE3ZjMyZTBjNmUwYzczODM1Yjc2ZDIzNzU2NzI5ZjA2Yzg1NTc1ZGM0YzM0NzA1NDliNWQyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRzYmFSV1RrWXU5K052TFNScVNWZ1E9PSIsInZhbHVlIjoidTVveDJBZ3RmK0p0OG9sbkMvVEVaUkIraVh1RktlQ05KMWduSWR0dkVoWW5mYVBTWi9DSWU2Z25XNDkxdG50Y1JrT0xCVjhDeWR6MUJqZXNsblVGSW9sRVUwK2RZdzBPWDF6YVRsa1lyS0pwWHlhdnp2OHF1Y01vY1gzTnVpeTdFaXg0M2UybjJ1dXNwZTFQRkRlTHhpY2k0dWFJaVZZMExCSXQ5QnJXeVZRTHRJZ3l3VEtaTTRzMElPUFdlRDFRenorQWJRYTNOYjBFM05kYVVqbHowdG44WUFwVmhIajhvY3RMRG5CSnVQSzV5aU1KOVRhYzRrSzdZUER0SEpwMjYxN2tLVVFFa2lrVG84anR6UngyZHBzY2dabG5LbkpCU0FBRnU2MXlLVjN2endneDNub0l3SnNic25nSWtqRmF2SFlvMlo1dHJvZklHbTBxcy9lb2tTRU9CN296enBlY0JaRjBkcVpuSjhKNU1LU2h0eUJXRlJVekJQdTNkTEY5LzlLbHVBRUZTVE95VzBrTTNHT2hUTnJYZnM2TkNFMGJJdmxHVG9FYndtZHVsbmRlSEF3ZlJSWlNZREJBdmdjc251WFVxOGxvSUk4OENPZkp3V3dSMlgvV3FSZVFTajRpQUpibzFBdjByMUpaMzdJMGJmL1NxWFVCYlFOUGVsbm8iLCJtYWMiOiIyMDBiMjZhYjcyM2YwODBjMjM1YjJkODFhOGJhOWY4OGQ1M2ZhNzI5MTEyZWM1MDFhNjQ5YTliODczOTJhZDhmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik02NWtqQzZyWm9xdHlSbFZTT0ZndEE9PSIsInZhbHVlIjoiYVFKall6ZXJLR0hXbFVibVB2SnRCeW5JZDFnWEQ3QUtlY0JsNHdENXI0RVNNb2RqbDU2VDVpY2MyRkErbDYvU2krWElVRm9tYy9NV0Q1dDNKZjkyM1J5U1QrMjA5V1FFc1R4T2x2MkhqdzViK2l4a0RLa3kvaU0reE1vRFM2RVMxUzEwdUZ0ZFRZenRPOU9mSjdBY0ordlY2Z2d0ZEl2MnRtUTZ4dlJ4VVMzWUdFazNOT2ZFNnF1SDg3S0NCRVk4eWxuYzYyZi9NdFUzUXBPYklUWVJHMURIVWhxbTlIdEQ5dDJ1SzUzWCs4MDgvVkZ5UC9MVnFKZk1hM1lPbDVSZkJQdWowMTFKT1llMnVNamJ2ZHBJMUlNZmNJMmNUS1gzZTB3dFpqZ1JnUmNtTUFETE1NcXBrQkJoY1dtWFVCVm82eThER0krSlZNeHhSbHVwNFV4MElPbzI0d01WN1Z6TEdpTW12U1JVYWxTM2RMWlBraU9CdEQ4VmM4ZUJ3UmdvSllaaW81aUNMVFl1L3BONGpYODBCVlFLRkRkUUNQMEE2VDArSzBQOUVjdGxQNTVKdzJ5bXdWNVBGQXJQTTVHSXcwVG03TU9XNlBVWU1GTnR2eVpNNnVsalYxSS9VYmptVVUwREJMamtuTVFMUDBNeGFXT1p5MVFvMTZ0dnFjbTIiLCJtYWMiOiJmY2Y3ODFhMTNhYWE3ZjMyZTBjNmUwYzczODM1Yjc2ZDIzNzU2NzI5ZjA2Yzg1NTc1ZGM0YzM0NzA1NDliNWQyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952659541\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1354838173 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354838173\", {\"maxDepth\":0})</script>\n"}}