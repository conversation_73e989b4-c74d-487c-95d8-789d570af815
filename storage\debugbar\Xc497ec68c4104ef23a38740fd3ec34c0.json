{"__meta": {"id": "Xc497ec68c4104ef23a38740fd3ec34c0", "datetime": "2025-06-28 15:01:11", "utime": **********.47627, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122870.972542, "end": **********.476283, "duration": 0.5037410259246826, "duration_str": "504ms", "measures": [{"label": "Booting", "start": 1751122870.972542, "relative_start": 0, "end": **********.415691, "relative_end": **********.415691, "duration": 0.4431488513946533, "duration_str": "443ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.4157, "relative_start": 0.4431579113006592, "end": **********.476285, "relative_end": 1.9073486328125e-06, "duration": 0.06058502197265625, "duration_str": "60.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46269312, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0023, "accumulated_duration_str": "2.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.458443, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75.217}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.468931, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 75.217, "width_percent": 24.783}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1455/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-2061663478 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2061663478\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-979983737 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-979983737\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-609322134 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-609322134\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-693909905 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751122833995%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZOSnBkbFBjTUl2aldsYWJhdjVwTHc9PSIsInZhbHVlIjoiRnhvb2Z4d3IrUFo4ZjU1UlhSMm9Hd1hodUd1OVJtY3dvTDc4enFVNWVVejRCQ1FlTUwzT3RPY014d0dLNXdrSDd6VER0bDB2TW13RGZJajJ3NTgzTW5GM0NWSzFvVVpta1VQNW5VSXdzNlowZVU2NXU1R0RBeWhZSVFSSlBXZEQ4bmZVMmZwU0FUN0lWM2Y3ZHFVbnNqTVpBa2RpZmcyRHROaDNOUHo3RmVwSUo3Mks1SXJtUktYWlRycmlGY3NmTlBhWUs2ZkpkMUlNQnZMSU5PVEZnUGhzR2plRGx6OE9YQXk1MUg4VVVZbFkvSFp3bjZDeWpsZ25pSlRvUFNXYURYTW1kMTNTZ1JMaFZhZmdKNTVsZ0lMUkRCUm5YZms1UkxmSXNPVWhwMm1xR2krTVQxTGtjQ1BOdW4zUzJORFFzRitQVzV3a28wQWtBWEI5KzhQakdnd1FVSTQ5YzRZMFd4a3pDbm50eEhJV2wzTGIzOVA4dDdFeTZ2RUo3dHo0OGV2aUJYSEgzRk9RSlcrSHpObnpySk9oVldZY01IWkd4U1FEaVRFaDZTWUoyTFZVTUs1YnN4QjlRK3dnYjF2NVM0ZldLRmkybk45RFFQUzV3SFkvcUl3S0poUmZsMjNBQkRQcWNwb2hYclhRdWhOZmsxd3ZXMnRZakUvSW5LaUUiLCJtYWMiOiJlNWQ3ODVmNWU0MzQ4N2QwOTY0MjJjODFjM2Y1ODM3MDQ0OTY4ZTkzZjQ4ZDk2MjY0NWU4OThhNTFjMzcxYzVmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IndBRUFPSUhDZC9hRHFhMzBkaXRncEE9PSIsInZhbHVlIjoiS0JOM0pHR0VrRVhWVmtnVHRXMncycXpqYmVIbDJnYy94YzlQU1lyL2kwL0NRb3FvMTA1MWgyN1lQTUwvZEZSSE5IZnlOdjQvLzVFMW85T292N3lQalo2MXpRVkZBVnFIbHo0Q3BCUUR6R0Q3SUhjOG9scEYzTXB0bU5LZ3Z2enl5eUJqVXM1TUowT0gybUFOQVdGWWUxcEdUdWxhb2VLOVNWZUJxRXpuK1VTR2xjK0NuaEsrZURtM1FHOU1lQmg4RWE0aHdGYTZybkpIeEhzZk54ZmdZNlRWL29kd1VhMmV2ZWtkWlF6enRobGJBL0JjdlV0YnJ5amFIbG1mM1lRajBiU2UvZmw1RHFaaVJwbnhhOTFzQ2hQdGdFZGY3aDVTVFVxaUdHb0NscnE2ME5rR1NSa05yTENtUzIvcjBXc2FwZG1mN2tVM3J4MmFFM0xHVXpBbGZvWklMWDY3blBYVDhJTHBWV1RsUUxlTWVINWw4VEd0NXk5ZlQxbGlZSU1pK3BqcUJzaWhCZUxmV2tpSUg1YWQwM21ENzdWY1Y3T09aMU5WZGwyaS83M1FybGRSZE5IQ0kyemlsZnJRSmFIemF3a29rOCszRW9GeGwzRTFLUE9JclZDMEdqRngxL0tzZitEY2JCN09nazhhSnFmZFRVTk85QVUraDEvWjEzUHUiLCJtYWMiOiI3MGU5Zjg0YTBlNjA5YzVlOGI2MmZlNTQyY2VlNzViNGZhNWM5YzFkNDRhYjUzNzdkNTA2YWUxOWJjYTlmOTUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-693909905\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-333080751 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333080751\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1587707522 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:01:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ5Q3ovUmV2Q1YxV2RIQk5UY0phUGc9PSIsInZhbHVlIjoiVStmcS8zZ2lUWERkM1Q1ZGtiOU1jMmRJNC9wT0tPeC9Qb0Y3UEFBbHRvbi90ejJRTEQ2VGFqTHJRY21KRFZjRmhZNyszQ3hkc2ZxT2hXU2xkbUl1Q1ZaM3lyU2xBcFlGWm5TZW1tU2lxdDVrVWtPOUE3NzJoK25zZjhIOTVYTDlkVkFhd0VIUWdyVlg0SkhBTGsvQk1FSXE1U2ZQOHh6ZWJ1eFJOTWlSSnhvZVU3R1pOUjdPRnM4NnlsREpUb01FTVNhUUZKeGpJS0UyUFoxdkE0SldkcllCZVJramRJamFzWjZCL1I1NkdwTHVEbzY3cUdQZGZFM1NGMmQvMmZUSS81L0JsdmpsOWdrTnRpbGt1WDVPRWN3V2V5akIzVnkvb01YUGJ3cVcxRVV3aGdvd2pPRlc4UVprT2xxU3dOelNycVVYVURJR3B1Y3daL3ZxUHg0WENINFNNK0YwQ2tMRTJsQzRsSXBCM1JycTJWZjkyaWpQcVViaXcxYnJmUjhmeldVWlJhNTR6U3JvaDhRb2taV3N6ZFZVcVR6L2oxTEorNXhhUE5wQldha3U2dGhscnBiRW9NNzRpeFZ5TExQSTk5MnV2Ym80M25MT1NCUkhKOEF0Y2tiQ2R2Q0xvRC91VVlESzdZbVBQbFRjR3dmczQwa2hCd3lnclRiTEZIOTAiLCJtYWMiOiIyYzg2NDNlM2MwNmRkNTQ3OGY3OTYyYWM2YjhmNTYwNzc0YjdjMmRlZjcwOWMwZTJiYTkzM2I0MzBkOTM1NzM3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:01:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRUWUV2a2ZleGsvQ1pCdW5RWU9WZnc9PSIsInZhbHVlIjoiLy9yMVVoYnhiTnFFVUVLaDRUc05xbWxmbXM3Mm80em4zQ0tYY2ZyVHJsc01nR2Y3QVhCWm55bU04MUUzQy9MbFYvNUp1ZmgvT2JQMFpud0I2QVRTaU9LRndtWTNWTzhRMGFUeGN4a1JnZEhmdkZ5eFArTGdTMmZUbHRkRXZnaWRvdWRyaG1xWkl3MkpjZmc3bWFiL0hxSDFwL0hGUnREU0xqZ3NpazN1b0Q0emVadTNGYU1hUWJ6YVVQblg4d2hyWHVVWnhBaFVITnl2QXpJYTh6VVRBdk9ocmE4d05pTHlpV3IxZDNTYW1haUpkUFNIQjJ2SGdNWng2UXR3bit1cjh6UWU1aDhnaVdQR1AzcmtrT3Y2dVdUQTUwYnV1TzZsMEpLT1hwU0Q2RjVaYm44Zy8yRmpGYm1nYkZEdG1zTkZya2kra3pXT2pIOHUrTlhJc1pIMEZ2MkYxZG1KMElXYnFsRkYxRG9HSUtDUFd6OExvRHEwK2hNNHlXYWV1c0I1aUUxMzdzVkxSaTB6am0vcE9EYVo1VTZaRWMwWHBIYi9aNkhzaC9KSkFWUEJ4S210T0xEVjF2U0hiNkwzMEtacWR2QTlJMXNkbi91SXl6OEY5VHIxZ3dtY1dMbFczVEZHRklIcmk2NmZ1YTBTSGVtME9kVmN6WFZ3RHBlNWw0THUiLCJtYWMiOiIyMWZiM2RlMmUxNzIxMDUwZTQyOTc4MzUyZjVmOTNmODBiOWZhNGI5YmRkNTUzMDdjOWJkY2FlYzkzNDQ2MzQ0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:01:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ5Q3ovUmV2Q1YxV2RIQk5UY0phUGc9PSIsInZhbHVlIjoiVStmcS8zZ2lUWERkM1Q1ZGtiOU1jMmRJNC9wT0tPeC9Qb0Y3UEFBbHRvbi90ejJRTEQ2VGFqTHJRY21KRFZjRmhZNyszQ3hkc2ZxT2hXU2xkbUl1Q1ZaM3lyU2xBcFlGWm5TZW1tU2lxdDVrVWtPOUE3NzJoK25zZjhIOTVYTDlkVkFhd0VIUWdyVlg0SkhBTGsvQk1FSXE1U2ZQOHh6ZWJ1eFJOTWlSSnhvZVU3R1pOUjdPRnM4NnlsREpUb01FTVNhUUZKeGpJS0UyUFoxdkE0SldkcllCZVJramRJamFzWjZCL1I1NkdwTHVEbzY3cUdQZGZFM1NGMmQvMmZUSS81L0JsdmpsOWdrTnRpbGt1WDVPRWN3V2V5akIzVnkvb01YUGJ3cVcxRVV3aGdvd2pPRlc4UVprT2xxU3dOelNycVVYVURJR3B1Y3daL3ZxUHg0WENINFNNK0YwQ2tMRTJsQzRsSXBCM1JycTJWZjkyaWpQcVViaXcxYnJmUjhmeldVWlJhNTR6U3JvaDhRb2taV3N6ZFZVcVR6L2oxTEorNXhhUE5wQldha3U2dGhscnBiRW9NNzRpeFZ5TExQSTk5MnV2Ym80M25MT1NCUkhKOEF0Y2tiQ2R2Q0xvRC91VVlESzdZbVBQbFRjR3dmczQwa2hCd3lnclRiTEZIOTAiLCJtYWMiOiIyYzg2NDNlM2MwNmRkNTQ3OGY3OTYyYWM2YjhmNTYwNzc0YjdjMmRlZjcwOWMwZTJiYTkzM2I0MzBkOTM1NzM3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:01:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRUWUV2a2ZleGsvQ1pCdW5RWU9WZnc9PSIsInZhbHVlIjoiLy9yMVVoYnhiTnFFVUVLaDRUc05xbWxmbXM3Mm80em4zQ0tYY2ZyVHJsc01nR2Y3QVhCWm55bU04MUUzQy9MbFYvNUp1ZmgvT2JQMFpud0I2QVRTaU9LRndtWTNWTzhRMGFUeGN4a1JnZEhmdkZ5eFArTGdTMmZUbHRkRXZnaWRvdWRyaG1xWkl3MkpjZmc3bWFiL0hxSDFwL0hGUnREU0xqZ3NpazN1b0Q0emVadTNGYU1hUWJ6YVVQblg4d2hyWHVVWnhBaFVITnl2QXpJYTh6VVRBdk9ocmE4d05pTHlpV3IxZDNTYW1haUpkUFNIQjJ2SGdNWng2UXR3bit1cjh6UWU1aDhnaVdQR1AzcmtrT3Y2dVdUQTUwYnV1TzZsMEpLT1hwU0Q2RjVaYm44Zy8yRmpGYm1nYkZEdG1zTkZya2kra3pXT2pIOHUrTlhJc1pIMEZ2MkYxZG1KMElXYnFsRkYxRG9HSUtDUFd6OExvRHEwK2hNNHlXYWV1c0I1aUUxMzdzVkxSaTB6am0vcE9EYVo1VTZaRWMwWHBIYi9aNkhzaC9KSkFWUEJ4S210T0xEVjF2U0hiNkwzMEtacWR2QTlJMXNkbi91SXl6OEY5VHIxZ3dtY1dMbFczVEZHRklIcmk2NmZ1YTBTSGVtME9kVmN6WFZ3RHBlNWw0THUiLCJtYWMiOiIyMWZiM2RlMmUxNzIxMDUwZTQyOTc4MzUyZjVmOTNmODBiOWZhNGI5YmRkNTUzMDdjOWJkY2FlYzkzNDQ2MzQ0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:01:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587707522\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1036566951 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1455/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036566951\", {\"maxDepth\":0})</script>\n"}}