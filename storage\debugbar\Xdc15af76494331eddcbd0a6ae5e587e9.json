{"__meta": {"id": "Xdc15af76494331eddcbd0a6ae5e587e9", "datetime": "2025-06-28 16:10:09", "utime": **********.595583, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.17762, "end": **********.595597, "duration": 0.41797709465026855, "duration_str": "418ms", "measures": [{"label": "Booting", "start": **********.17762, "relative_start": 0, "end": **********.532037, "relative_end": **********.532037, "duration": 0.354417085647583, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.53205, "relative_start": 0.3544299602508545, "end": **********.595598, "relative_end": 9.5367431640625e-07, "duration": 0.06354808807373047, "duration_str": "63.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44030896, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02296, "accumulated_duration_str": "22.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.563276, "duration": 0.02264, "duration_str": "22.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.606}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.58944, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 98.606, "width_percent": 1.394}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2306 => array:9 [\n    \"name\" => \"ذرة بريتز\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"2306\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 51\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126882941%7C30%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imh3Tkt4bmFrNzR1WWFBdXhuV0lzdlE9PSIsInZhbHVlIjoiNWx0L3p4QnQ3R3luMnRZSU9SWXZhOXByYThpRjhEZ29rYjU2ZlBaaDdrWWVreGJsS1llaTFKZG1hcEJqQXZUUFRQZEFHdXNCNzZHenZ5dnRJYzFaeU9CNzhQTXZVS1JydnBtaUsrajFSVTl1cDhnRlhZYWt3d0N1dndRYlpVdFJYZUladk5ncVk5MmN1aHR2TmU4OE5yK0s1SUlPMFk2NzROdkdqV2VrODBkSXNmMENPdVFRU0VBWEVyNTN6SWdYQXRxS0VtTTZ5MmNSTzR0cnhyeXdBKzRWd2JTbUhpZzkxbjVJb2o0ZnNITHRDNkZyTTl4WDZXS1hHQ1dHdWIrQXdJZmZHUVBvVGpvaXRNTnBGaHhVSlVnd3FMUEhmeDZISHBobllIZTU1QU5TUHY0MjBBSlE1aUlCTlVDcjNYM1NTYmZwNDBhcTIzZi9uUjVTajlaZENsRFVUSFlVcHE2RzhTaENWNTRsY01sT3Q0ZXRHek0yN0h3TW0zUEdqWnlESTVoMWt0eGdubjBBZysrNlVkN1ZzTjN1bFJhV2lxYlBmbkNuQ05KZmZhdFhJVndHYkFySnByTEZUSVptR0ljT3diYWVSZk53WHNyMWVsUHlJci9EZTgrMWNSSDhTcXQrUENwQ3dwOTh0MUZFQ3FuQWlVenBHR1kxU0dUSFZsQ0YiLCJtYWMiOiJlZGVkMmY0MmY5OTlhNWNhMTUzYjc2Y2ExNmZmYzVlOGJmMDJhMzU3ODE1NWRlN2M1Y2U1MDY5YjBhMjExOTYyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhVUFRGcHlTOVg4RCsxSWE4c2l0WHc9PSIsInZhbHVlIjoicXFPTDJVUm55UE9sN1ZlMkp2Rm55KzZiYzhuUHlzTUo0TmFtK0pjajB0UTdmb2tTRTZQZGRmZmFjM1V3eW4zVmlTQXd0R0FONDNrNlg3c00xTlEwWHpXOCtZT056R1pFZWQvajZFZys1U0hRdXBVRllWYjZDZ1NSWlNEbFBDK3llcERTUXppSmFrdTBQK1dXejJaOTdoQld6VVhOZXNML3h0R3VUeEo3TDNkcGVFaVFpSE9qMUZQSlRMQzVzeWxSb3doaTg2cFZpRDlzZW9HY3p2UWVHVzNsUU9GKzlxc2FOVjIzYjJva3dLS2ZsS2hLSDJWWFJCWWY5WnJad1YrOE52VmhMZ1pSMGFNOHhXTS9VQktBb0EwRzc1WWhKeThXVkp2dUVGU0V4SnNUNVpsNklmQXRJYjdiS2I2WUU5MXVHOThuQnBhTkpnbXJrTndmcXRkY3dYZ0s1c0w1ZXZoVjFUa1BWc3RkbUhaRVhUMXBqbmFac2p0MHNwb1lHQ0lzc1Y0SERCUFFtazNHZGRueXY3eWM1amdQSlRoNUhqcW9XNVJkMm1naWc2VzNNcDdqV3hRSVU1RlBFSjhDaDJ5Q1pIeDlZNjB4NTdhNmZhcjBaUUtXcXg3ekRlanUxa1RERTg4QW9NbHBxK2VweExvRFptRFAzaDJhVDRzWC94MTAiLCJtYWMiOiI4ZTM4YWZhOTdlNDhjNzBmNzgwMDJkYzg5NTZjMmQ0MDVjZmEwZmMzOGQxNWI2Zjc2ODgzYjQxOWM1ZWU0ZGYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-993218569 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993218569\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1189046955 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:10:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVuNk96OTdxR0JvUUxUNXJpWmpwNEE9PSIsInZhbHVlIjoiODY3cVB6WGJIbEJUTXZlV1JVN2U5dC9lbTFvNHBTeXJXK0E4OWNCKzdTbWdJWUVKWEg0dVRjWFF6SERzWW5pWnpIeDFUQVp1emNzbXg0Qjd2UWh2QnJ1ckZ0V2J6SnV3UGVGZnZvbUxrZWkzTDV4cjc5bmY4c1FVQnRHNVIzMjQvQmZRcWFLcE8zak9rREY3VEtEOVdiNktDNWJwMUhCbDJHdnpVbkhQSnJzeTVTWUFpaGI2ZEZ6VzJEOHQxbCtId01sbDBHSmt5QXZrSVJlNUlyWkpvQ3NYbEhId3l2WEdTUDEwd2lLbi96UERrcUsrWFoxdmVJMXM5bXNMVXhSVG5kMU11N0xXemRrMjQ5L1JkMUVIZ00yczUvaVV5amNKcThvZWZ3bTR6UHBsNXAxVU54YmhBVGVVNWJUY1l1aVN5QjRVb3pxYlhqMEVaZExpcEtvclRaUk1GdkQ1Z3plbE94K2drR2tRSm1rODE1cUg2R3FRZjdvRFdLN3pMc0pEb0xGdkVOdWN4MnY3MmFEL3FkMHpPKzB4eTNDbjFHZUsvR3pyZnlBVStndDJCNWVHenJrd1IrWFpubEEycC9aUklQWUNXWndWR3o4YlpLcEFuVUJYM25MQXJtT1VmN2QxUUZBbWhJUjgyZ1IxNGs0Sk5qeEhSUXM4a3NMRE1VZVQiLCJtYWMiOiI0ZDcxODE2YmI5NGUyZTNhNjcwOGJmMmM0MzEyMDUzOWVhNDEwZDBhNjY3NTI5ZjQ3YjcxYzAwOWEwZjhlN2UzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdkSCsweXlEMHhEb3hwTVpWTWtERWc9PSIsInZhbHVlIjoibExDMGY3ZUNBU0lYeUVLWnVJMDFMOStuTzIxUE0ycnU1dDRDRllvTDVxajE0eXNCSjE3VHMwMHcrY20vTU1wQy83TmlaZVQ4VXpyb0Yzb2swL3RuTHplVXI1cmp0bkI1VzdsNncrMGRPa2NQd3pIeFpEb3IwdG9GSEdNMXV6R05jS3VkQ0hYVVNqc0ZxamdJNDF3RHkxTE1OMEJSNzlyc3VIS05ialhVNkdzVE11RGMwL0lzMm95SmJTVTFrVW56SUs1djdtRFcrODNpaEpncFl0bmpGNVR1UkpkV3F4U1F1NXg0YzZ4ZVZ5OGc0RVM4NXU5Z1htUGd1elY0SE9nVWE1Uzh0Y1N4OUM5bzFnazFZb2hSR2tVZGFVMlZpSlhxMmFFQXl0dDJqWnAyT2huVG9sVk5tQXYreFhqTXBESWRWajZLYjRDRGVrTUxFSVQ5RnFSajVMT2FNQ3Y3dTBxRFZIS1ozNVdFRk5JVEFOTFMvL1dOM1pJdXhodXhEVytsZ1FrYXlmRzNuaWdpQjQ4aERHc3MxNWNuRGhOSm93bjBoYWNmOTRqalpJL280bk1FY1UzM2hpRmltdVFHVG5TMFVHUFBVSTJNZi9CYjM2NTZpV2tYU3V1dWtIeHVtWDNxT1V6Z2gwaFpKbW5VMk01d0V2TnpxUzA3Nk1WMTRTWHkiLCJtYWMiOiJhYzY2ZGY3NGExNjcwOWFkNzQ4ZjZiN2JmOWFkNjM0YjU4NTZhNzVmMjk0NzU1ZWMxODVlOGQ0MGExYjJkMGM1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVuNk96OTdxR0JvUUxUNXJpWmpwNEE9PSIsInZhbHVlIjoiODY3cVB6WGJIbEJUTXZlV1JVN2U5dC9lbTFvNHBTeXJXK0E4OWNCKzdTbWdJWUVKWEg0dVRjWFF6SERzWW5pWnpIeDFUQVp1emNzbXg0Qjd2UWh2QnJ1ckZ0V2J6SnV3UGVGZnZvbUxrZWkzTDV4cjc5bmY4c1FVQnRHNVIzMjQvQmZRcWFLcE8zak9rREY3VEtEOVdiNktDNWJwMUhCbDJHdnpVbkhQSnJzeTVTWUFpaGI2ZEZ6VzJEOHQxbCtId01sbDBHSmt5QXZrSVJlNUlyWkpvQ3NYbEhId3l2WEdTUDEwd2lLbi96UERrcUsrWFoxdmVJMXM5bXNMVXhSVG5kMU11N0xXemRrMjQ5L1JkMUVIZ00yczUvaVV5amNKcThvZWZ3bTR6UHBsNXAxVU54YmhBVGVVNWJUY1l1aVN5QjRVb3pxYlhqMEVaZExpcEtvclRaUk1GdkQ1Z3plbE94K2drR2tRSm1rODE1cUg2R3FRZjdvRFdLN3pMc0pEb0xGdkVOdWN4MnY3MmFEL3FkMHpPKzB4eTNDbjFHZUsvR3pyZnlBVStndDJCNWVHenJrd1IrWFpubEEycC9aUklQWUNXWndWR3o4YlpLcEFuVUJYM25MQXJtT1VmN2QxUUZBbWhJUjgyZ1IxNGs0Sk5qeEhSUXM4a3NMRE1VZVQiLCJtYWMiOiI0ZDcxODE2YmI5NGUyZTNhNjcwOGJmMmM0MzEyMDUzOWVhNDEwZDBhNjY3NTI5ZjQ3YjcxYzAwOWEwZjhlN2UzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdkSCsweXlEMHhEb3hwTVpWTWtERWc9PSIsInZhbHVlIjoibExDMGY3ZUNBU0lYeUVLWnVJMDFMOStuTzIxUE0ycnU1dDRDRllvTDVxajE0eXNCSjE3VHMwMHcrY20vTU1wQy83TmlaZVQ4VXpyb0Yzb2swL3RuTHplVXI1cmp0bkI1VzdsNncrMGRPa2NQd3pIeFpEb3IwdG9GSEdNMXV6R05jS3VkQ0hYVVNqc0ZxamdJNDF3RHkxTE1OMEJSNzlyc3VIS05ialhVNkdzVE11RGMwL0lzMm95SmJTVTFrVW56SUs1djdtRFcrODNpaEpncFl0bmpGNVR1UkpkV3F4U1F1NXg0YzZ4ZVZ5OGc0RVM4NXU5Z1htUGd1elY0SE9nVWE1Uzh0Y1N4OUM5bzFnazFZb2hSR2tVZGFVMlZpSlhxMmFFQXl0dDJqWnAyT2huVG9sVk5tQXYreFhqTXBESWRWajZLYjRDRGVrTUxFSVQ5RnFSajVMT2FNQ3Y3dTBxRFZIS1ozNVdFRk5JVEFOTFMvL1dOM1pJdXhodXhEVytsZ1FrYXlmRzNuaWdpQjQ4aERHc3MxNWNuRGhOSm93bjBoYWNmOTRqalpJL280bk1FY1UzM2hpRmltdVFHVG5TMFVHUFBVSTJNZi9CYjM2NTZpV2tYU3V1dWtIeHVtWDNxT1V6Z2gwaFpKbW5VMk01d0V2TnpxUzA3Nk1WMTRTWHkiLCJtYWMiOiJhYzY2ZGY3NGExNjcwOWFkNzQ4ZjZiN2JmOWFkNjM0YjU4NTZhNzVmMjk0NzU1ZWMxODVlOGQ0MGExYjJkMGM1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189046955\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-46497517 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2306</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1584;&#1585;&#1577; &#1576;&#1585;&#1610;&#1578;&#1586;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2306</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>51</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46497517\", {\"maxDepth\":0})</script>\n"}}