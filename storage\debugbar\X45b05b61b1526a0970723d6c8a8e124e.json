{"__meta": {"id": "X45b05b61b1526a0970723d6c8a8e124e", "datetime": "2025-06-28 16:34:37", "utime": **********.620181, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.211087, "end": **********.620195, "duration": 0.40910792350769043, "duration_str": "409ms", "measures": [{"label": "Booting", "start": **********.211087, "relative_start": 0, "end": **********.542711, "relative_end": **********.542711, "duration": 0.33162403106689453, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.542719, "relative_start": 0.3316318988800049, "end": **********.620196, "relative_end": 1.1920928955078125e-06, "duration": 0.07747721672058105, "duration_str": "77.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45893072, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02883, "accumulated_duration_str": "28.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.57456, "duration": 0.02587, "duration_str": "25.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 89.733}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.609133, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 89.733, "width_percent": 1.838}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%enhanced-pos%' or `sku` LIKE '%enhanced-pos%') limit 10", "type": "query", "params": [], "bindings": ["15", "%enhanced-pos%", "%enhanced-pos%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.611839, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 91.571, "width_percent": 8.429}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1823858849 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1823858849\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"12 characters\">enhanced-pos</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-534266805 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">33</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128472755%7C51%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkI5WklBZWVnd3ZqYjljd1FjdXZJM1E9PSIsInZhbHVlIjoiTjJlY0J1K3BWejgwQ2F3d2cyc0orVy91NWpmRk4xVVJmTFV0dVl6VlJHczhEdm9qaEtZOFVkRU5VenBxcG94dEZWSW9OdEw1eU9CSWc0QklHaHRwOVhvTzZMOXZrKzBsZzExVWYxd1p5cUFGbEk5em5rUnRLN2FqcEFGT2ZKTndGcUQ0MW1lUUFuNy9EWGd4a1QxRDFFTFNkOFowSHExeG9DR1hqV3ltREExeWNrbFpiMGJHdjV3bXlLN3hTM3hDOXNUQTlISVpWd3NZZHZ4dlBpKytKalNRMTh4OWFHa1R5RjgyZkJkcUZpTFZjYWY2NWhhOU5KOExVWmhzdU1nUXdmemFpcC84RHNtVkxqenJHRy9zZi9OZStUaHZvMnU0QkYyZ2NRRngwQnRmZHltMGluemFCZy9iV1VXOUlPTGJid2FFQ3VFK29zbk5IbXUxUi9WeGJiYjNxNzREL0xsajhSOGtXMVZCS1FSTHBRTXBmQXcyU082ZXZsTmIxYmRKRUpaZGxITjllM0JKVDZwM2dZZFF3Wm9pU085M1VDdXJVWFFQOUVlc2dqbUM4V25UU2JhWmxiMUpPWm1Hb3pZL3V1OTVXTXhnTEh5NW9IRmRRak9aT0tkcUhuUjlvMVBLYXZnNDUrb1FhVWJSeVFLNCtvb3hucGRSM0hQbWV4ZDgiLCJtYWMiOiJlYzgwMzRlYzEzZGI1MGFjMWU0ODk3ZmRjOTFlMGM2YjNhYjk0YWNkN2Y0ZDBjN2JiMzMwMzhkYmIzMWM0NWU3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkxHeUN1dzhBRFdaZTRIbXN0bjBBblE9PSIsInZhbHVlIjoiZG5CRGkwUE5ibWZ3Y2FOVnVieDNkRGZlQXFORWRmbkY5QVZUbGRFOUdmOGhqMk1KWmQ1V1BTTXhzU1l0T0VpV1haOGlLeVRHRGtsTlphWmpkNkJ5cnpNSUNBN3pHLzZZL3g1dlNoV2tBZ3AySGZiYzNvUDZxYWxPZEtvWldGRzFoTHd2V1pldlNWditVS08wZk1PRUM2MTVuZjc3NmloOFNvNlduWVFWNkMwZHMxK1htaTRKVmhUc1l3b1RQeDBIQ2lvY2VUR2Uvc2Y3cmJrWExOYlUwNGhkdDNoQWphbTFYRUFkT1hjRTUwcEJyN1h6UHRRZjlpQkIwUWE5WTlHVEFDOERXenphMWpadGxtbVFhcXNUV0djZUg5a0NNa21tUUpFTCtRMVBKS3krWlNYRGgvejB4MFhJSDVPY1d0cW96NDJ6Yys4K1pIbnNCMm4vMXlLUVp2Lzk0cURYTGhML3FyazB5M0V6MEpMenJLU3p4VW9JSHMvemdBZE5ieGJpTTY3Ly9YRVVaNHB5VjVrZFd4Tk1vNFp0Zkx2bFFJMk1DTW95OGlVWTNwWFFUd1RoYXBZMGIzVmZFa1BDdEdONGYxOE1OYzg3SFNqcWlxMERiQ1V2UGlXMHVkMkVIbENWSVBQUzZuYUg0emtEcmo5OVkybG1xTkhQbE8vcVAxbVIiLCJtYWMiOiI3NDJjYjkwZTVhNzg0ZmIyMjFlNDY2YjQ3OTUzMzVhMzAyNmJkOGQ0ZjhiY2QyZmNmNjhlYjhhNDU4NGY1Y2ViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-534266805\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1951063857 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951063857\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1367125816 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikp4MWwxRnVQZHIwSUdrZXozYmRrTmc9PSIsInZhbHVlIjoiOTlRdFhRSER0ZzJTd2wvZ1FhSktRU1FZT3g3dnBUSExzdzhkbzQ1VTRSN1ZuemlkRWYxRjhVVVR5eTlWWEkxL3M3SGF2MkhtUEpSTlQvQ1FoamNWS2FYNjRIVVpIS0VrZSs5QzlJZ2NjSkl4aktSOGtGMW1QNEpoVjJOQzBqbG5pckFyOHBuZmlQVklGdjhRRlpJejRWTmx3TTlnaWY2WE9IT2xsT3Rzd2VJTE1BcmdVTW00UXdpQjhleXRVOGlFVUdaQksrQ29mS0ZhQVNjeGd1TE1FclVSRTAyQzEzaENDWTRiN0V2c0krbDF4ZU01TFJXWG8veWh0QVZzQzQvNENkYU0yUXVBNUxEZSt6cjdmVUtjcUlCTVBQUXlQRHdVWTlJNVJMQ1pRMjZzSEZSQWZtQnVuU0IvZWpkdklDa2dVSHBaaXRWQnFONlFFSHcwanlocDA0ckZNd0VCdW8walN4TVlyMTVUR1ZNSWJFWUk3eUY0ZVF2NS80MjlsRlhsR1ZuQkppV05RNE1Od3hiNWRKWGtPWnc4c0lDWGV1Mm5hUHMvVnpRVkhxQi80dEZtSDhnYWpGWmdKUGJiM3pFcjdza3lOdnY5M05HT0RhWURZTmlFcVJOaDJXa1Z6b1dPcWQ1dms3V21YamdpeUpseUFwNFNqN200YzhxWWxTUGoiLCJtYWMiOiJmZDk3MDMyOTBjMjI5NzAwNWIxYTU5NWQzN2U5M2QxMDQyYTIwOGYzMDJiNzgyOTVlZWMxZGYwNDMwZjYxZWQ0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Inl4WWllQStWWGdEUWk2WEtKYTVlWGc9PSIsInZhbHVlIjoiME1QSXZJQVVSNDNIUWJhYUJpMTI0RHNnVldGOHNtYndyUHNvWjVhbDRMeXZBRXEvejJzRFZMWkpUMk0wbHF2YmNUWTRUS2N0OXRLYVNsNVNRcmZUejlXU2NtcEEvT2E0Q1J0ZUdLUFNYNXVUYy9oVVhTZnVxZ1BCb1RXTHZ5N0c0Znl0VFJDNmhzSVY4OGE2NnpJVHB5OU5tU0VIemdEWkoydXhlRnVCbm1hZ0N1eGNvVU9hMnBmdmdweHZqSnBlS3k5Uk52dVVkUy9tTzAvd1dJTE9lTFl5Nm1pMEVkdUpCbFJqOFpta0hZaWhkMGRJeWlreVdNL0QzTnh3RmJjblFEclNqcFVMOHdua1I0UWozVjJTcVRKZUptNkoxbHIyZ0cwSzNId0cwSzBxYnd5a2NVZm5YbnhBMzhCVXRyY1Mwdi96dUh4SXExQ3VHbnVObkxta21DSzlKc0hROEI4ZE5rYzVTbHBqb3pjTGtsYk1qdzI0K0d6SHF1Z1kyckJ0MVJzVnRrNHhOWVRBNC9IWVJFejdhL2kzbzk4RkdrNjdHWVhjbnBxeURiWDJ4UTMrRUNLTWYwRmdHOXY3QkJpVlhpb3JPdktZU1l1RlB2RG91VUl1SHZQeXpkWUNHcG1Ja1RwcEE2RUhEVVYwaGNFdDltTUpYelJRWmhEOEk4dWQiLCJtYWMiOiI4YjRkNzUwNWU2NTMxNDkwN2RhNmJiMDU5NmU0MjdhNzY5M2I1OWY5YmUyMTBkMzg2MzM3ODk0NGQyMDYxMDIyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikp4MWwxRnVQZHIwSUdrZXozYmRrTmc9PSIsInZhbHVlIjoiOTlRdFhRSER0ZzJTd2wvZ1FhSktRU1FZT3g3dnBUSExzdzhkbzQ1VTRSN1ZuemlkRWYxRjhVVVR5eTlWWEkxL3M3SGF2MkhtUEpSTlQvQ1FoamNWS2FYNjRIVVpIS0VrZSs5QzlJZ2NjSkl4aktSOGtGMW1QNEpoVjJOQzBqbG5pckFyOHBuZmlQVklGdjhRRlpJejRWTmx3TTlnaWY2WE9IT2xsT3Rzd2VJTE1BcmdVTW00UXdpQjhleXRVOGlFVUdaQksrQ29mS0ZhQVNjeGd1TE1FclVSRTAyQzEzaENDWTRiN0V2c0krbDF4ZU01TFJXWG8veWh0QVZzQzQvNENkYU0yUXVBNUxEZSt6cjdmVUtjcUlCTVBQUXlQRHdVWTlJNVJMQ1pRMjZzSEZSQWZtQnVuU0IvZWpkdklDa2dVSHBaaXRWQnFONlFFSHcwanlocDA0ckZNd0VCdW8walN4TVlyMTVUR1ZNSWJFWUk3eUY0ZVF2NS80MjlsRlhsR1ZuQkppV05RNE1Od3hiNWRKWGtPWnc4c0lDWGV1Mm5hUHMvVnpRVkhxQi80dEZtSDhnYWpGWmdKUGJiM3pFcjdza3lOdnY5M05HT0RhWURZTmlFcVJOaDJXa1Z6b1dPcWQ1dms3V21YamdpeUpseUFwNFNqN200YzhxWWxTUGoiLCJtYWMiOiJmZDk3MDMyOTBjMjI5NzAwNWIxYTU5NWQzN2U5M2QxMDQyYTIwOGYzMDJiNzgyOTVlZWMxZGYwNDMwZjYxZWQ0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Inl4WWllQStWWGdEUWk2WEtKYTVlWGc9PSIsInZhbHVlIjoiME1QSXZJQVVSNDNIUWJhYUJpMTI0RHNnVldGOHNtYndyUHNvWjVhbDRMeXZBRXEvejJzRFZMWkpUMk0wbHF2YmNUWTRUS2N0OXRLYVNsNVNRcmZUejlXU2NtcEEvT2E0Q1J0ZUdLUFNYNXVUYy9oVVhTZnVxZ1BCb1RXTHZ5N0c0Znl0VFJDNmhzSVY4OGE2NnpJVHB5OU5tU0VIemdEWkoydXhlRnVCbm1hZ0N1eGNvVU9hMnBmdmdweHZqSnBlS3k5Uk52dVVkUy9tTzAvd1dJTE9lTFl5Nm1pMEVkdUpCbFJqOFpta0hZaWhkMGRJeWlreVdNL0QzTnh3RmJjblFEclNqcFVMOHdua1I0UWozVjJTcVRKZUptNkoxbHIyZ0cwSzNId0cwSzBxYnd5a2NVZm5YbnhBMzhCVXRyY1Mwdi96dUh4SXExQ3VHbnVObkxta21DSzlKc0hROEI4ZE5rYzVTbHBqb3pjTGtsYk1qdzI0K0d6SHF1Z1kyckJ0MVJzVnRrNHhOWVRBNC9IWVJFejdhL2kzbzk4RkdrNjdHWVhjbnBxeURiWDJ4UTMrRUNLTWYwRmdHOXY3QkJpVlhpb3JPdktZU1l1RlB2RG91VUl1SHZQeXpkWUNHcG1Ja1RwcEE2RUhEVVYwaGNFdDltTUpYelJRWmhEOEk4dWQiLCJtYWMiOiI4YjRkNzUwNWU2NTMxNDkwN2RhNmJiMDU5NmU0MjdhNzY5M2I1OWY5YmUyMTBkMzg2MzM3ODk0NGQyMDYxMDIyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367125816\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1719214312 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1719214312\", {\"maxDepth\":0})</script>\n"}}