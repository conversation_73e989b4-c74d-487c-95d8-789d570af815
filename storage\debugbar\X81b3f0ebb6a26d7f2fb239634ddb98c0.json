{"__meta": {"id": "X81b3f0ebb6a26d7f2fb239634ddb98c0", "datetime": "2025-06-28 16:03:49", "utime": **********.905154, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.451154, "end": **********.905168, "duration": 0.4540140628814697, "duration_str": "454ms", "measures": [{"label": "Booting", "start": **********.451154, "relative_start": 0, "end": **********.838908, "relative_end": **********.838908, "duration": 0.387753963470459, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.838915, "relative_start": 0.38776111602783203, "end": **********.905169, "relative_end": 9.5367431640625e-07, "duration": 0.0662539005279541, "duration_str": "66.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45714928, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014489999999999998, "accumulated_duration_str": "14.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.865309, "duration": 0.013529999999999999, "duration_str": "13.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.375}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.889986, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.375, "width_percent": 3.382}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.896173, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.756, "width_percent": 3.244}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1451659928 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1451659928\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-507418408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-507418408\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-581346181 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581346181\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1935034528 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126627120%7C22%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImI1ejgzdmEvSHJOa2R6TkF3M0JnOGc9PSIsInZhbHVlIjoiWVpPMy9qSXU5dkt1VHBRK01paWVVYkFSUzJBdFlIVmI2RGp3Y0RMajErOVk0N0EybUFlVm5KQnhyejd4bUJ4SzJSeTNyYUxEMHZ5TkR5em1Xdm0xTFRobEJWbWsyQjZEU3ZMU211RzFCakNoZitlN0EvV1JITEprazJZRmxYZkwzNTRpeG44cUV2Q0txd1g5alNPdkg0OUZTN1dvWFk3NW1zeGZoZTNCTHh2UkdMWFpjMDNaQ2VrWW83bDRSQ0d5M082SXdKZVV0bEZhOE50R3IyVWVHaVgxV09nNURtczNrc1B1TVhodjdKTlJaQjVwTThuZHpUbjNLS2dGcW9XZWF4bHpkbWFKeDRrbC9RdjZGNkRZU3NVQWlTbEVJRzRxQlFrTXQvd2Y5aC9VR0ZHdWhTYURXbk9ocG5DWmVoazNzME5iSnZSYllsR2trM2czN2tPdjJKUzF4VlFsM08zNTgycmxUSEl1M1phZnRmYUF5Z0ZIb0w1Y2N5MERuQTdoYUh5bklDbnp4SWxFNDhyUXV2Wnd5YlNjd0FKNVJQd1pwRWJrQ09NUVZOYWZaMmFWMmJ1ME80ZG51Mm5YRTVwaW1jZjh4Ym0zRE9hdXdpQlRqb3BkaXp3dlVadThJUzdFRnhveFBuQ3hKc1l4MUZhUHBKUGczSXNHVjNkQk1WUFgiLCJtYWMiOiI2Njk4OWZiMTM1NzQ4YWEyZGY2YWRkZmQyODRiMjkxMGYxZTRlN2YzY2IxNzk1NTM2NTliMjdmNmQ0NTBiYzIzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImY5WjVFcmNNVWNOclNaQ1VMQ0ExV2c9PSIsInZhbHVlIjoieFFCVXlybXNmR0NpVXJ6UmhpditKQi9UcGs2dlhzUmFQMkNKTGdiTytQc1FMcDJNT1ZXenplMjhUUnZ5WTB5K1Uyd0JDN0lSQlQ1M0wxSkdLa3pWa0ZQcTdNLzNzeDZvblpjNGs0YWlDZ2VWR0dvQmNNUlhoUzdXK0htVVB6clBEc25ZdU9HeUtVcEE0YjlITm5MTjFTUEMvK3RBbFpMV2hHVzVVL1ZwN2RqU3F6V3RHVVlTcGpGWGp3NU9Xb2RpcHpER0NBWkFranNzUFlzUzhheTBENHNGeDJNc1VzTG4vb2MvUlRmSm8ydi9CalgxNUFHd3dsYWZBR1Rwangwc3gzQjYvTGpwdmowNlFMMnp6VzFFVXRla1JoWTBDL0hTcDViQjZKZ1RjM2w1WExOMWhBNnJLM3BXSjJ4SGErQ2RvcTdqNzhxdjVYUW0xSGxvOUtlL1FwWXJWOTU1a2pKTFJCN0FpK0NRdVdRaVlqVzltamVYMXdtR09HTmJiSnRiSzZXbEJtQ245VDU3Vi9ES2hWRndYUE13c0tlQlNVNnVtL0tudFVvcjZCRFEwM3I1TCs2aUxoWU9nUGtjaVk4OXFDcTZTUkR4TXR4c2VmcWU2WUhxQVpreWNTdXlEQXdVUlpxWWs1SExXMDZJWmtvNWNTUUVBR3hYVWR0WUVwUVciLCJtYWMiOiIwNWMyODYzYjYxMzA2MGFjOWQyNWExMjdmYTQ1MzE1ZTI4MDQ0M2NjMTU0MDc2ZjA5MDhmY2JkOWZhMWFjODEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1935034528\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1313155667 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313155667\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1007218273 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjIzRVJpdi9ON0FSa1pwOTBBTTJDblE9PSIsInZhbHVlIjoidU1ibENCOVB1RTdEYlRGSXRpVmY4L053UHo5T0pnK1g0eDdBUDFEVG84LzdmWCt4VW9JY0pWVzUwNVppRG55ajhtMlByY3JIYnpZOTNDZE9iOWQ0RUVJUVQxSGZDYTdtYkNiK2FuSU9BbzJ0R0NCYWJEUmgvTnZ3ZFE2cURucVFYS3diZktIUERXa3hBWFR6WFFhdlp6Q292d0tVN3k5ZG82TTFRb0NGNGFzUFBnZS9MMWEvRzFoSWlTZm9YSkFVeC9RV2MvV1JiRDUwcW1uN2RHZGtWLzU5U3ArejJGend4dGYvazZVei9FbEpvYkQ2eXYzamh1TklPR0JHTkQzNXVLb3dzMmtOMnRBZEdiMGNBMTJ2TUcrRU1hS2xXdUQ4cVpTbkFmaUJCVE5BR1FhbDdCRk9xanJrNFV1QmRtY0h1K1A4MXFBZkZuaWdXbU5YSlUwSC9xMGhKVkt2TXJHMjh6eGNkWjREUDFGSmUzczFLZ3A4OEpISVVTM0RtUU9GK0ZEMmVGWjJhMWp3aFcza1BKSWM5cG9MdUZ2aksyK0w1czgzdERGa3dtd1JTQU1tR2F4OW15VzFtOTdFNDBTdStWdG82dU80Smt5WXpsSmphSzdwYXhNWnRWUzZUN2tQNmtFSUZ3dFVlOWdpWXlwQ3k4L2ZjWVNSZzZJZjdsZU0iLCJtYWMiOiJlNjFmYzg2ZGQyODIwYWJkMDcyM2IxOTM4MGNkMzY0NTc2YmJiMDYwYjA4MzBjYzM3Y2I1N2RkNmUzMDlhZmU5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFRaXh1emp0QlVMR3E2QmxKTXpBb0E9PSIsInZhbHVlIjoicmMrMFVPejJtYzlvL1lEbkVaMCtHV1prU0xNVi82c0RFVklEU2Nzb0IwcmVUVVNyODBUbDA0L0tnSUMvVk5lRW9KWFRwKzZMNklPUzdSb1VEQTQwZ2RIa3JzS1RnaU1wa0RXc3M1ZjN2V1VnTHBybkoxWmttbFpRcm9UaVpkWjM3UGw2aGhIMGNwYThxNjhQdkdUaDJ1d1kya1RmU2t5MWpRcU42RUp2S3dzTHNQZlBMTFpJaFRzdnhNd0hGa3B6aWt1RURtL3AxYWxUNkI2WVlzRTRrTitkdVZDMjYvRDMrWVJMQ2FhenhkTVR3MEUzb3ZQVmVnT0ZrTDkrd3MrQ0tOSEtOdHloREJESVNWUUtkbjgzbHpNVkk0aE1lVm1idnBOY0U4RlV3dWxDZHo5WFFEenpZekdkYWNkWkJ1VDVZMFMvZFhwUEVaci90UEE2SG04VDZPMDRFK201cktPcElQdWlFTXhlSFplOEI4Y2FxUWVVVmt1bFVrMFNTWnFIQUlzc05CS0thdjRZTWY2ZUVqN0t3bGc2ZXdHU2VxYmJIUVkyRjc4VlBMUEZJa0VOaEl5cWdMeXlLaldLVWZEUnBOV2pRRUdZU2ZTUjVsRUJVK0F1RzVWUWtBYXNnQmQ5NzV0NDAvWDNpYldRenVTUlpnaHV6WUhiYmJ5ZHlHUVkiLCJtYWMiOiI1MmQ0YzZhMzFkZGU4OTk5ODlkMjljYzJhZmE1OTUwNDc1YTQ5ZTViYzEzMjBlN2JhZTI5NDdlNjM5ODRmNGI5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjIzRVJpdi9ON0FSa1pwOTBBTTJDblE9PSIsInZhbHVlIjoidU1ibENCOVB1RTdEYlRGSXRpVmY4L053UHo5T0pnK1g0eDdBUDFEVG84LzdmWCt4VW9JY0pWVzUwNVppRG55ajhtMlByY3JIYnpZOTNDZE9iOWQ0RUVJUVQxSGZDYTdtYkNiK2FuSU9BbzJ0R0NCYWJEUmgvTnZ3ZFE2cURucVFYS3diZktIUERXa3hBWFR6WFFhdlp6Q292d0tVN3k5ZG82TTFRb0NGNGFzUFBnZS9MMWEvRzFoSWlTZm9YSkFVeC9RV2MvV1JiRDUwcW1uN2RHZGtWLzU5U3ArejJGend4dGYvazZVei9FbEpvYkQ2eXYzamh1TklPR0JHTkQzNXVLb3dzMmtOMnRBZEdiMGNBMTJ2TUcrRU1hS2xXdUQ4cVpTbkFmaUJCVE5BR1FhbDdCRk9xanJrNFV1QmRtY0h1K1A4MXFBZkZuaWdXbU5YSlUwSC9xMGhKVkt2TXJHMjh6eGNkWjREUDFGSmUzczFLZ3A4OEpISVVTM0RtUU9GK0ZEMmVGWjJhMWp3aFcza1BKSWM5cG9MdUZ2aksyK0w1czgzdERGa3dtd1JTQU1tR2F4OW15VzFtOTdFNDBTdStWdG82dU80Smt5WXpsSmphSzdwYXhNWnRWUzZUN2tQNmtFSUZ3dFVlOWdpWXlwQ3k4L2ZjWVNSZzZJZjdsZU0iLCJtYWMiOiJlNjFmYzg2ZGQyODIwYWJkMDcyM2IxOTM4MGNkMzY0NTc2YmJiMDYwYjA4MzBjYzM3Y2I1N2RkNmUzMDlhZmU5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFRaXh1emp0QlVMR3E2QmxKTXpBb0E9PSIsInZhbHVlIjoicmMrMFVPejJtYzlvL1lEbkVaMCtHV1prU0xNVi82c0RFVklEU2Nzb0IwcmVUVVNyODBUbDA0L0tnSUMvVk5lRW9KWFRwKzZMNklPUzdSb1VEQTQwZ2RIa3JzS1RnaU1wa0RXc3M1ZjN2V1VnTHBybkoxWmttbFpRcm9UaVpkWjM3UGw2aGhIMGNwYThxNjhQdkdUaDJ1d1kya1RmU2t5MWpRcU42RUp2S3dzTHNQZlBMTFpJaFRzdnhNd0hGa3B6aWt1RURtL3AxYWxUNkI2WVlzRTRrTitkdVZDMjYvRDMrWVJMQ2FhenhkTVR3MEUzb3ZQVmVnT0ZrTDkrd3MrQ0tOSEtOdHloREJESVNWUUtkbjgzbHpNVkk0aE1lVm1idnBOY0U4RlV3dWxDZHo5WFFEenpZekdkYWNkWkJ1VDVZMFMvZFhwUEVaci90UEE2SG04VDZPMDRFK201cktPcElQdWlFTXhlSFplOEI4Y2FxUWVVVmt1bFVrMFNTWnFIQUlzc05CS0thdjRZTWY2ZUVqN0t3bGc2ZXdHU2VxYmJIUVkyRjc4VlBMUEZJa0VOaEl5cWdMeXlLaldLVWZEUnBOV2pRRUdZU2ZTUjVsRUJVK0F1RzVWUWtBYXNnQmQ5NzV0NDAvWDNpYldRenVTUlpnaHV6WUhiYmJ5ZHlHUVkiLCJtYWMiOiI1MmQ0YzZhMzFkZGU4OTk5ODlkMjljYzJhZmE1OTUwNDc1YTQ5ZTViYzEzMjBlN2JhZTI5NDdlNjM5ODRmNGI5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007218273\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1980692329 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980692329\", {\"maxDepth\":0})</script>\n"}}