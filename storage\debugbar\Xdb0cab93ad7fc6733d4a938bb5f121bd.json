{"__meta": {"id": "Xdb0cab93ad7fc6733d4a938bb5f121bd", "datetime": "2025-06-28 16:35:13", "utime": **********.594531, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.184913, "end": **********.594544, "duration": 0.40963101387023926, "duration_str": "410ms", "measures": [{"label": "Booting", "start": **********.184913, "relative_start": 0, "end": **********.542151, "relative_end": **********.542151, "duration": 0.3572380542755127, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.54216, "relative_start": 0.35724711418151855, "end": **********.594545, "relative_end": 9.5367431640625e-07, "duration": 0.05238485336303711, "duration_str": "52.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45716392, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025800000000000003, "accumulated_duration_str": "2.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5707679, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.791}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5805058, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.791, "width_percent": 15.891}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.585974, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.682, "width_percent": 21.318}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-8546218 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-8546218\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2060959184 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2060959184\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1678030143 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1678030143\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128497517%7C54%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFsblZvOWRzY08rRjU0ei8yYkw4R0E9PSIsInZhbHVlIjoiTTRvYllQaCtpbEZacnFuOTB1V0xBVzBWT2ZTQzcyT3RUTjEzSHRkV2pXODlDRDdMY3A0Q0tnNGFwNlZES3cvMlk0K2hDK29HY0s3Q21uZDBYV0E5UW9TVjYrS0ovYXdiSzdxYjNqdi9xT2VkcnpaNWppeFVMTTJTalR0SmZMZyt5Tmd3Rk9Wb3NRQnlmVExYdHRHaUVFMURSVlBUdU1valI5NWlZK2JMM1AxZnRNQW1IR216TDV2RkVyWDZYcWI5RWRZNVVDeGJuQ05OYzBiTGhFZVRrVTIvUXpMajV5OWluOVovSGZmd0w4UTRnYUgzS0g0WU1yR0RBOEpHZUZ5L2NpeVBpejdGZU9TMFpSSTJZU3MyYXVsUlMyZ1JyaUl5dFVFUVZUb0YxVDBmYTBhZEdLekVXK0E1TkZ0a2RXTUtoclE2M2h1TkxMbHd4Tmw4ZFJ6ZGpSeEhlc0JqdUdXT0FmMk05a0N2NXB1UVgvR1lGMm9zQUJQeWtQNEo3NTBWdEpqRmZrbmU4bER1NkJzbjlDSnNNZ3lsNC83aEJlbFQxTUJyWTArUTkvK0tvVnRIYmFqamVablBpMFhXckIzMkhiT212eDFtUW41UGsrbXZ1NjdqcXhWSnRjMzVBU20vaFZ4SGNRbmpwbXk5cXFNZEFKZEt0TmlveEdEWXZJWm8iLCJtYWMiOiI4NzhiMDgzNGNmN2FhY2UzNTlmMTFiNTViNjVmZDY5NjM3MTY3NTMyYmE2ZmQzZGYzOTdkZjRmMzNlMjQwMTBhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdvbytCT0xsbzBCRmUyUGVURnR1Mmc9PSIsInZhbHVlIjoiNEhHUW8vU2ZqeVBORCtGSU1jRzJuQ0VlNmdxNVJWRHJxR2xrSHFIYTYyYkYzQ0NuNkhVWFBSa2drYWJMeVZyUi9MWUczOEpOUzJLSStCaFNqVU9hR2NOTmdkNjNIc0lMZzFjSGlzWXplK0d0YU5qemQ5aUJOOE9zcjVFMnJRSHU3Y2szaFNCRFRtN28vdDZ1TFVLRmpQcW9RakxtcGw1b2VMZlQ4RCtpMUpVa3RxaVp4K1lOdFg2U2xzVVkvSjlrcDZJY1FTTWdJMlJOWVlpVWNxRVo4aVRBSDMrb1RTaXEwRDRIZDJOZ0t1YzAxQ0dGMG9YODFBdGVzS045S2czUHROK0NLU1AwWDJCekJQYlplRU1yNzEwNW0yVEZmYmRoQzNoeThDekNkL1IwQ0VHdnN3d29HaklnV2lWeHdTS1JWbzQ4ZGdyNEhYVHpBa3NQaDExT0hObVUydmRuSzZTZ1l0U2l5SjJVUHBvUmRHZDJSV1lJZHZQTVZsWWtPNW1Xbm9KNXhIdU5PM2FqVzV0ZnhEYmY5TTIzWFZ6Vi94ZFRZa3ZZYUpORzdFa09pSXFBSWFEMTRnejBFOWx5THZFWUdVU3Z2UkNtaXBvcUFsR1lEeUpZWklYODdHc3RsNGFkMEdIUzhFT28yU2l1Y3F2c1VrVzA3MW14ekZiZGtBVVciLCJtYWMiOiJmYWE2MjljN2RkMDVhNzcwZmVjYzVjYTM3ODM2ODZjNDgwZGQ1N2M2NmYwYmI2YTI0ZGY1YTRjOTY1YTk5NDViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-17016683 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:35:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJQQm5FTHF2MFlDWXhMOEZGVmZLOEE9PSIsInZhbHVlIjoiVmxCS0Ixak5XQ0JZdWdXcFBzVmhraEpvSnZZa3Z6T2gxSE81KzZ5WVJoYWhjdEZzbXd6MTA5U0tOQ0x2cWt4ZmlUOWhZT2o4dmphcXZJNENuUXZONDNiMUVjZTU2REk4Sm9yNGh3b3piZzlxUkVZMlJHRlowclRxazY0OUNBS3JmMXJTWnpjZlZZdHhkbEVwSy8rZkYrTWl3OFN6WUtmQUJPTUVDdXlHTmxUVllsMFhpZ3NSLzA0bWRJa3lXTnBOOXR0UUFxdmsveFhOZFFUMzRtdFNhNHhWRTdCTTJqMUFaclFGc2tJLzc2bDNXVHp4ZGM4MFpTRzRYZ0dzYUVnbzREUXQ0dUxpeUhZc0VPcE1HMjloOEZvV3dWZENtalEwOHFoL05WOUtjM1N5clBKVm9TOEVuMkdQK3gxL3dDdUVNMXZ4cFNLWndHSUtCSm9ZQjNtaGVoTE14QnA4V1U5MkpCSWs2SDNZYVE1a0VYNkwwbzg2dW1HeVR4WTlLcGJPWXBObE43Vm5nNVlaMjh1a2tYR0YyeEg2djUzV0cwY0czWWMraFNDdTNUV3U3Ukw3SllLRmp1Z1llU2R3T3R0QWFIQnBwQW1NbzlvdEIzOTZmb1o3cnhHaUdoRVl0UDBnOHFtcVU4emJ1T0lnTEpRd2wrazAyUkVWU0JOTWFpTUciLCJtYWMiOiIzNDAxMjFlMDVlOTMxZWFmYjNjYjcwNzA5OTQwY2FkOWM3Y2U2ZDQzYTFiNTNiM2FmYzBkYTdiNzRjOTdlZWQ0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVUajBVb0NIS0VxSnpjSzVHby9XU0E9PSIsInZhbHVlIjoiOUZNOEpJQnBFZHpNaDhLeUFsZmZyMXJrME11ZElkY2RZRkdsbXVTSEVlaUVuc3pWUVpHOEJjalJtRGJEZkpzbXhuanNsZE4rczZtREZwVmdwNC9wTUZJQkVPaG5sMGtBWEw2bUE5UVpQZXROdlFvNjJMb2xuWnl2ZzJWWUUxWXRVWWYvSnJPWm5TUmV2Uy9Nb1A0YUYzejlBdW5oeGl6bDh1ZkR1Zzd2T2VLQXhvK2gyOERrUWlmOWY3Q2NUVUp0aDVUQmJTaFBmZWNYcjQxOVJPM3FGbGREYytKNTZraEtzR1dMS0VyT1piUDRLRThhS01CQkZTVlVsMmVaNlltc09VMjh2OFV0dzRVOXJSN3VhR2pqQUtyNnJRRUhUcmoxRmJCZ3hJMENlNk40b2hOM0txTWVPR1h4aXBFQkRrMDJxdFZWbmx0MjVUV0xZRGFCQW4vbUt6RnRieThhUG8zV2hwNEpuQkNIY3NBSnlkMC85WXdxYUZBdjVVLzlXaXc5ZURwNm1DYmlaNm1vQmtIczJ6ZTNpTDQwZnZLMVdNYUNaTUVCS01WU0xEYndpQndaVnZBeGpYWUFQOTZleDNNQStoWVdCd0NNbU1DUEVnK2NzWVB5VHFtSnJ2dXZCbmRxZTQ3MExxY0hpZDExallLaXM4bFJ3dy9IRGpXRGZzbWUiLCJtYWMiOiI5YmMyYjI5ZjlkMGJmOGI1OTA4NWMwYzViNjBjNzZiZTM3MmFhZTE5MWE0MDk1MDYwZjIyZjlhM2MxZjYwZGMzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJQQm5FTHF2MFlDWXhMOEZGVmZLOEE9PSIsInZhbHVlIjoiVmxCS0Ixak5XQ0JZdWdXcFBzVmhraEpvSnZZa3Z6T2gxSE81KzZ5WVJoYWhjdEZzbXd6MTA5U0tOQ0x2cWt4ZmlUOWhZT2o4dmphcXZJNENuUXZONDNiMUVjZTU2REk4Sm9yNGh3b3piZzlxUkVZMlJHRlowclRxazY0OUNBS3JmMXJTWnpjZlZZdHhkbEVwSy8rZkYrTWl3OFN6WUtmQUJPTUVDdXlHTmxUVllsMFhpZ3NSLzA0bWRJa3lXTnBOOXR0UUFxdmsveFhOZFFUMzRtdFNhNHhWRTdCTTJqMUFaclFGc2tJLzc2bDNXVHp4ZGM4MFpTRzRYZ0dzYUVnbzREUXQ0dUxpeUhZc0VPcE1HMjloOEZvV3dWZENtalEwOHFoL05WOUtjM1N5clBKVm9TOEVuMkdQK3gxL3dDdUVNMXZ4cFNLWndHSUtCSm9ZQjNtaGVoTE14QnA4V1U5MkpCSWs2SDNZYVE1a0VYNkwwbzg2dW1HeVR4WTlLcGJPWXBObE43Vm5nNVlaMjh1a2tYR0YyeEg2djUzV0cwY0czWWMraFNDdTNUV3U3Ukw3SllLRmp1Z1llU2R3T3R0QWFIQnBwQW1NbzlvdEIzOTZmb1o3cnhHaUdoRVl0UDBnOHFtcVU4emJ1T0lnTEpRd2wrazAyUkVWU0JOTWFpTUciLCJtYWMiOiIzNDAxMjFlMDVlOTMxZWFmYjNjYjcwNzA5OTQwY2FkOWM3Y2U2ZDQzYTFiNTNiM2FmYzBkYTdiNzRjOTdlZWQ0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVUajBVb0NIS0VxSnpjSzVHby9XU0E9PSIsInZhbHVlIjoiOUZNOEpJQnBFZHpNaDhLeUFsZmZyMXJrME11ZElkY2RZRkdsbXVTSEVlaUVuc3pWUVpHOEJjalJtRGJEZkpzbXhuanNsZE4rczZtREZwVmdwNC9wTUZJQkVPaG5sMGtBWEw2bUE5UVpQZXROdlFvNjJMb2xuWnl2ZzJWWUUxWXRVWWYvSnJPWm5TUmV2Uy9Nb1A0YUYzejlBdW5oeGl6bDh1ZkR1Zzd2T2VLQXhvK2gyOERrUWlmOWY3Q2NUVUp0aDVUQmJTaFBmZWNYcjQxOVJPM3FGbGREYytKNTZraEtzR1dMS0VyT1piUDRLRThhS01CQkZTVlVsMmVaNlltc09VMjh2OFV0dzRVOXJSN3VhR2pqQUtyNnJRRUhUcmoxRmJCZ3hJMENlNk40b2hOM0txTWVPR1h4aXBFQkRrMDJxdFZWbmx0MjVUV0xZRGFCQW4vbUt6RnRieThhUG8zV2hwNEpuQkNIY3NBSnlkMC85WXdxYUZBdjVVLzlXaXc5ZURwNm1DYmlaNm1vQmtIczJ6ZTNpTDQwZnZLMVdNYUNaTUVCS01WU0xEYndpQndaVnZBeGpYWUFQOTZleDNNQStoWVdCd0NNbU1DUEVnK2NzWVB5VHFtSnJ2dXZCbmRxZTQ3MExxY0hpZDExallLaXM4bFJ3dy9IRGpXRGZzbWUiLCJtYWMiOiI5YmMyYjI5ZjlkMGJmOGI1OTA4NWMwYzViNjBjNzZiZTM3MmFhZTE5MWE0MDk1MDYwZjIyZjlhM2MxZjYwZGMzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17016683\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}