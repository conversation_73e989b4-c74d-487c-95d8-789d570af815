{"__meta": {"id": "X9401fe149c12c2ea1ad51eb2b47742c9", "datetime": "2025-06-28 16:02:21", "utime": 1751126541.041822, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.613619, "end": 1751126541.041838, "duration": 0.4282188415527344, "duration_str": "428ms", "measures": [{"label": "Booting", "start": **********.613619, "relative_start": 0, "end": **********.964763, "relative_end": **********.964763, "duration": 0.35114383697509766, "duration_str": "351ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.96477, "relative_start": 0.3511509895324707, "end": 1751126541.04184, "relative_end": 2.1457672119140625e-06, "duration": 0.07706999778747559, "duration_str": "77.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45695408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02508, "accumulated_duration_str": "25.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.992936, "duration": 0.02389, "duration_str": "23.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.255}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1751126541.0252929, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.255, "width_percent": 2.233}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1751126541.0322502, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.488, "width_percent": 2.512}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "[]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1714688596 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1714688596\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1592716703 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1592716703\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-651016236 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-651016236\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-365446270 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126520354%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpzZ3VBaVZUOXA0cERKZmh3WkZkSFE9PSIsInZhbHVlIjoic1JiVUZ1Z3NzbGxHQ3ZWMVgvcUVZTHVYMmVLWTZtczRJWTlTUmtqd1AyRFc2NitsT1E1WkJ4TjBNQm1TYy8wcWhyaFJXWXlreDNOZDBPSVNEVi94Mkx0eGMxaStjMytNczZSMGZDUXJRcXBUdnM3YUI2a2pFeGZ6c2JMZDFTUmhCVXpEbUFlemp1ZFRTUk9mMCt1aGlVdG5RanRSMXBkVGFnNUt6S1dSSC9ZV1EzeVRLeGx6cFgxWm9OUTYreTEzR0JKRElSNzNZbEhueGwzek5GSVExRmFuVjM3clF4cStPZVY2NnBUTnc5YkorSHJuYVZoQlJib1RVMWdmMFJYZzRQRDhNMXZDRnVNUUtNa0hZMGhFTENad1ZZWlY0dWdQQ2hWYnVYWWhKN0t4b1hpT1NNSmtGdy9xT2JTWDdVZHdaaXR3dlVmNjdWWlRFN3Nzc0g3eE9jNVM1bXo2enBUK1R1SDFyZnkwU09KN1RZNDFOczF6bEJFeWY1WW1sZEgrQWpFRGFjTWNjd2ovRmd5Z2creTdIOEpqZGdRaG1lZE84TUlxY3RBNERoc3lPcHdlUXlRRUg2Q040KzBhZVQ3V2N4UDRLb29IeE5Pa04vNitXZVJZcU1XZUFKL0VqRUNldUliQzRvaHZOUUtnTHhrdER0RHA4cFF0blJlQmtCUlkiLCJtYWMiOiJlZjVlZTg3YWJkODQyMzU5OTI3YTRkZGJlOGM5OTBjODljODI0NmEyNWExYzk1YzdkODExNGRjZjQ4ZjA0NjUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlSR3ppb2xONjg0UzFwcEpaazl0MVE9PSIsInZhbHVlIjoiaEw1cUpvMmtYaFpUNHlQU2ptWVRrUUFaamZCK3lMRU1jU0N4bnM1eW8yYkZkWlcyWVYyRHE1citSWXlxbnM4OXBrZDBxVHlqWkozVE1PUFNkd3ljdGg4aUhYbExOZkN3RWdYcitzNlF3RG92RW9ra1lTT2p2WWYzWnV2M01TL0VPczBIelAxS1lNNEo3a0RkRkd2UGtLQU50UmVxM0t6QnBiOHliaXFFMjJoM0FGUjdhVE9LdW9Fck9ZMXRGekFkYksyNjdyMUIrU1p5dXVvaEdHQVVqUEZodjdhWEVZaGVZMkk0VGhBTkY3Q1F0QU1QeXpXK1NNUk5TdzVSZCs0UXZnZHIwTkE2V3ZYcEM1M0RBeUFyU2lJd3l1Zi9pUDlkSGlGTUNoaVZ5VEc2Mk5vQnZFYTl2OTlVTUxxM0ZjcXB1SHRGdG9KREFraTZjZndPOWtaWGpPb3YxY1pKdUU3a1hiNlEwUGtWSEFqTDRHSkRNUFFVMmNMU2gyNDNkUEhpYVlmTzl1emJFVmVscmhBNkI3YjZEcERpNzhyR0dLNnFyTEdWWlJ0VmpoMVV4b0lTVG9VV0JyTWVFTWNLZXU4TVBaY2FCMUpmRkR4ZnZiejhhdWNxN0VITTFLVDZqRWt1Mis5VDdKemdMd1ZGekt6Z0JwbVFMdUZoUmhQeWpmWUkiLCJtYWMiOiI5ZWQ5ZTdhY2QzYzFhNGIyZjE5N2RlYTkzMmRiYmQxNjRlNzJlZDE5OGIyZDZkN2M4ODNmNmY3ODU2MjQ4NzkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-365446270\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-419522285 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-419522285\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-946898541 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRmeW1OSFl6aWpDUFpNbmNoSjh6c1E9PSIsInZhbHVlIjoidnpxaXJGdGIrQS8vcmZlMWFOcTl1L0t6UkpDdDYwWGRXSW12c21kd2szQ0RKRHBja1JXSG5sSFdGaHhFMFo1QTdkNnpRMnZUMW5zYkNFQzEzWU5Mc1drQnZqN0RzMExUMmcycENGeHdweGJNMGMyeUZJazFPbFlpQ0VFODdpOUFOY0N1VVI0Zys0K2YxV2JXZHJub3dNanFuVytHSnhxaW9rT3JCK2h1YlA3dDd3R21rckRVa0hTZzhkWjhGcVp4MGlHRXNQeDRtY0wrd0pUZkd3dlNTRjVuQWttMDBpQlp1eTVkM2RSOHBnQVcrbFI3TWUyb3J5M3YxcE93a3R3bHB3VDJQaThWT1RoZGErZlZPQk1pME9JbCtXREU3ZUh6ZXFhQzBEMThVMzV3ZTBsVy9Ya01FZ0E1VmV6NjU2eWZiVUx1UkNST2xYcFV3ejc1bHg1TTFmQ3E1YUJtekdmQXBtaU41STZBTjJZK1gwaytGUy9FTzdJbmVVTlZwUlVQanNCMFRROXE2VUNid1NIdjA5MXJkSHkzbFQrV1lFa3A4RExxOW52QVE4WERTNUIrd29KR2RrOHdacXd2Yk4rNEd2aldXSmlGSFVPbmV3Q1oxZkxBK0I5cFhrWkc2Z285TllCMUJTNm1TMXE1aU8vTGtiVks4MjhKb2Z5ODkwaXQiLCJtYWMiOiI1MDU4MWM5ZWYwZGNhZjMzMWJhYzQzMWI3OGNiZTYwYWQwMjA5ZDVjYTAyZWExZDgyNmZhZTA4OGJmZjc5OGQzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFOSm9mbHBnUXc1VjRRa2h4WmJSVkE9PSIsInZhbHVlIjoiWGFGWEVLTUZYRVpZZE5rOVZQVmxlcnNCaDN3cS9vd3Jua0kreHpuU2MvT0xlYW9EN2o4K0dqOGI3Z1hlMmdqbG1DV0lYNjFZNHR1YzQ5UlZxeFhsTFoxSkJCNnVkdDcrMkVxcUpFWkljc3R3RTlxalF1YS9EdkJzaHdsRHNGRDBlc2tZL2FOWGZRbDloNnFXS1RuRERJeHVyMWYyS2pEakMzcEt5bzUrYTA2VVFmRlNrTHNzTU5LYTZyeHF1RFdmZ2J4WkJvRHNxWXFnaWJEN2tnc0V4ZGZUUUJEWU1welZ0S0NhRUhid2E3cWsvL2VYTjQxQUZkNkUzNXBpZEN3WTJGaFNnSmU0Z3RseTZoQlRiam84QS9UZVpHb1h4V0haTEI2UVpDNXVOTlBEVDVqSHcvU1BSWGI0WVUvYUpEL2NPdWdPaDRPRlVJUHJaU24vSFV5aS9mNWhTQXpYY1lhN2J5TUpUbzB0UnNKaFAyUEZMVlF5cWpVRmFjWVE1N2xuYjZMTWxNdzNrV21udXd2VitYdUVjTUF3cVpnVk9QTk52QmhzSytvNmlsZ2N5dDVvbWR3V25xajFPbklmcEx4NnU0R20waGZQcXo2M1FSa0IvQjJKdTBhMlFMejlTQ214TjRZQ0ZFOG84Z1EzcnVQTHFzNkpLZ1R6YllHZnJHZDQiLCJtYWMiOiJlMjM5Y2RkZDRjMDNiMmQ2NmRjYjk5OTU1ZThiMzc0YjdhNGU5NmZjMmNlNjRhOGUwMGJkZDI1YjMyZTQyYTM4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRmeW1OSFl6aWpDUFpNbmNoSjh6c1E9PSIsInZhbHVlIjoidnpxaXJGdGIrQS8vcmZlMWFOcTl1L0t6UkpDdDYwWGRXSW12c21kd2szQ0RKRHBja1JXSG5sSFdGaHhFMFo1QTdkNnpRMnZUMW5zYkNFQzEzWU5Mc1drQnZqN0RzMExUMmcycENGeHdweGJNMGMyeUZJazFPbFlpQ0VFODdpOUFOY0N1VVI0Zys0K2YxV2JXZHJub3dNanFuVytHSnhxaW9rT3JCK2h1YlA3dDd3R21rckRVa0hTZzhkWjhGcVp4MGlHRXNQeDRtY0wrd0pUZkd3dlNTRjVuQWttMDBpQlp1eTVkM2RSOHBnQVcrbFI3TWUyb3J5M3YxcE93a3R3bHB3VDJQaThWT1RoZGErZlZPQk1pME9JbCtXREU3ZUh6ZXFhQzBEMThVMzV3ZTBsVy9Ya01FZ0E1VmV6NjU2eWZiVUx1UkNST2xYcFV3ejc1bHg1TTFmQ3E1YUJtekdmQXBtaU41STZBTjJZK1gwaytGUy9FTzdJbmVVTlZwUlVQanNCMFRROXE2VUNid1NIdjA5MXJkSHkzbFQrV1lFa3A4RExxOW52QVE4WERTNUIrd29KR2RrOHdacXd2Yk4rNEd2aldXSmlGSFVPbmV3Q1oxZkxBK0I5cFhrWkc2Z285TllCMUJTNm1TMXE1aU8vTGtiVks4MjhKb2Z5ODkwaXQiLCJtYWMiOiI1MDU4MWM5ZWYwZGNhZjMzMWJhYzQzMWI3OGNiZTYwYWQwMjA5ZDVjYTAyZWExZDgyNmZhZTA4OGJmZjc5OGQzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFOSm9mbHBnUXc1VjRRa2h4WmJSVkE9PSIsInZhbHVlIjoiWGFGWEVLTUZYRVpZZE5rOVZQVmxlcnNCaDN3cS9vd3Jua0kreHpuU2MvT0xlYW9EN2o4K0dqOGI3Z1hlMmdqbG1DV0lYNjFZNHR1YzQ5UlZxeFhsTFoxSkJCNnVkdDcrMkVxcUpFWkljc3R3RTlxalF1YS9EdkJzaHdsRHNGRDBlc2tZL2FOWGZRbDloNnFXS1RuRERJeHVyMWYyS2pEakMzcEt5bzUrYTA2VVFmRlNrTHNzTU5LYTZyeHF1RFdmZ2J4WkJvRHNxWXFnaWJEN2tnc0V4ZGZUUUJEWU1welZ0S0NhRUhid2E3cWsvL2VYTjQxQUZkNkUzNXBpZEN3WTJGaFNnSmU0Z3RseTZoQlRiam84QS9UZVpHb1h4V0haTEI2UVpDNXVOTlBEVDVqSHcvU1BSWGI0WVUvYUpEL2NPdWdPaDRPRlVJUHJaU24vSFV5aS9mNWhTQXpYY1lhN2J5TUpUbzB0UnNKaFAyUEZMVlF5cWpVRmFjWVE1N2xuYjZMTWxNdzNrV21udXd2VitYdUVjTUF3cVpnVk9QTk52QmhzSytvNmlsZ2N5dDVvbWR3V25xajFPbklmcEx4NnU0R20waGZQcXo2M1FSa0IvQjJKdTBhMlFMejlTQ214TjRZQ0ZFOG84Z1EzcnVQTHFzNkpLZ1R6YllHZnJHZDQiLCJtYWMiOiJlMjM5Y2RkZDRjMDNiMmQ2NmRjYjk5OTU1ZThiMzc0YjdhNGU5NmZjMmNlNjRhOGUwMGJkZDI1YjMyZTQyYTM4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-946898541\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-391521762 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => []\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391521762\", {\"maxDepth\":0})</script>\n"}}