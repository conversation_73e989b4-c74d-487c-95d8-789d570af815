{"__meta": {"id": "Xcf08dd27868c5ea9df6850b58d00510e", "datetime": "2025-06-28 15:43:31", "utime": **********.947134, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.417366, "end": **********.947152, "duration": 0.5297858715057373, "duration_str": "530ms", "measures": [{"label": "Booting", "start": **********.417366, "relative_start": 0, "end": **********.872489, "relative_end": **********.872489, "duration": 0.4551229476928711, "duration_str": "455ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.872499, "relative_start": 0.45513296127319336, "end": **********.947154, "relative_end": 2.1457672119140625e-06, "duration": 0.07465505599975586, "duration_str": "74.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45161704, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023950000000000003, "accumulated_duration_str": "23.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.899654, "duration": 0.022670000000000003, "duration_str": "22.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.656}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9306881, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.656, "width_percent": 1.336}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9374628, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.992, "width_percent": 4.008}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics?date=2025-06-01&warehouse_id=9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1046209115 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1046209115\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-429420739 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-429420739\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-224129526 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224129526\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1738859160 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"84 characters\">http://localhost/financial-operations/sales-analytics?warehouse_id=9&amp;date=2025-06-01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125396906%7C17%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFacVo2Mk9jRmNFZ3N5MkM3K3JvTkE9PSIsInZhbHVlIjoiRGFZTHhRZWdJdnlVSU1jN1VJZzMzVDlJTHVJWGdidDdXV3ZQRnMzdWthL1RmM0tva1l0a3ROQ3o5bFMreHBLNWRIR3ZrRUtsd3ZkMCtCRFhHbm92T25HeE1nQTg4b0YxN2hXd3ZSdzlOMW9uQVE2Y3ErUmJFbWIwS1ZTbDIzalBWalpJZkVLYnBkdmhmVzdjQlloZ3VFaXRJQWRYSDhVODliNkQzL0duakh5TXhhUk9jeEd5bDRyb2NzR0liM000OFprM29JUUxIR3ZuRjBUQ3pOTE9UYmNpenNRYUhSSWdQZXR0dkowLzdVYUU2K25ReUdRKzlLaGR6ZWhWT1N2eHlvRWN1aERyM24wdEp3eUZiNXlURVNrWmRWbTZobjNKdmNzVnh1NXZ0alNUbXJ6cjkvaHZNWkNWc01FNC9iNXgrUVFtYUM4aDdlSmovYlltUkh2SWMxTFBpajdEUEg4RmVxRSthK3hub0FCMjJpcG1rZ0tuVG1WRzgrK1hIT0d4UStJaW9wbE5IMHIyU3hEazVYWTdwMGJVcnJQTUNhRzd4R3ZJcUpIenYxdW5lNmlNb01BdTRNam1Dd0FqQXI4Y0hwUDI3dGFzR0FySGRlUjUvajRyK1A0TThKV2FTQnM2UU5HWVJhd1psMmVaaEVuZzFGME5YL1F6V1dCaGNBaUMiLCJtYWMiOiIyNzY5MGVhYTQ1N2JhNGIyNDM1NDlmZDliOGZhNjZmNWYyMDFiMjM5MTE5NTdlNzUyYmNmZjI2NTE2NjcxMjUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZyblA2a2ZxVkttK1FZeVF6bmVvc3c9PSIsInZhbHVlIjoiWTRLRW8yaWE4TEgvZXpEcjcyVUg2eklUQllQM1h2RWxoRnNDMWFBZ2pLcUFxT0tLZ0JaWER3b2M4djBReDFSVW4zaWhtQlRhZEpvSkZVS1llOENKbGp2Z0NWS2tQcC9TNmtkbzQxajhLWmgzaFVFR3Qyb000T3c2dFBUZmlYT0QzSnJSVkwxcTMvbXRoOTd4MnM2SGIzNDJGVGY5MGUxRFV2bjdTUVNWT2tUQ3M2YXJ1REt4YTRlcElCZDdXWWd3WGtzNmpCRXhPWGQ0UnpKZk92c2MveUd2eXVjY00velpSU2ZoekMwaGRtek5tYjRrV281SHVIaEVzQjR0SHJKN2VoRFhyTGp1M3FiSjJmNXYxeDBkQ29hWEZvcEE4YVpkT0tjTUVERi9uMGNNY2JVM0dTZU5PcWdJUHF3OW56TWw4enptcUtZWVoyWEpuNzd2anBKWTlTeVFpdXZKSXZxeVd4ZmVlRjg1OGtvaDNneTBZVnF3N0N0YkJOMUZuTWphNkhTaHoxeTVMT0J0QTBXeHpONmJjUS9FNkdWZ2VLNnNsL3p4S1ZSY3dVUEtqMzFLMTdBZEtENC93SHU3QldydnlITTYzU1o3ejdKTmNNTHFNMUlHV1ZmeWJsUksrQ3JsZURyZmYzSzBXeEU1cFpLS1daQitwM3o3ZGUwYzJyWEYiLCJtYWMiOiJmNDVkOGM1ODVlN2JmNGIzM2Y4YjRmMWU4MDA0YjEyY2ZiNzUzMGQyNWRmNTIzYTUxZjU5YzI2ZmRjMTVhNGUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1738859160\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1356051669 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356051669\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1259428481 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:43:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9TNGs3R2pFK0xiQU5yZzFpNEdHcWc9PSIsInZhbHVlIjoiUzgzMDhXU2hjS2ZUZnIyelk0dzMzZmNxdmx6b1lXSStoWmJqaGl6SmxkQVlTNHlHNXUveE0veExCb0pwQ0JWcGxjTG5lVnhFMzdFejhDM1p5RU1ucGJCWXJTSVJtNjE2Z1RHdytGbXczTlVmVi9XQUh0R3hwaDRDK3oyL3owSi95eXZ2QTRDR0hQQTdPKzJqZ25PcXhqNzNmY2JUcTdpSFRtaVRSZEp3YWJsOTVYck05emFVT0NiaXJmY0E4eWpUVXBXQVA3Y1QxRCs2UjQrb1FjeXdaQ243cGRoZCtWOXc4RnlnRmsxbDRSKzlVRFVOaVFYbkc1WitFUCt6M29Bd1N6Z1BJbER6M2VGV3dOWHpKUkhaN1F6SllyU0FpS2ZoYVg3MGRuOGhiWHE2R3FKVnJvaFlmcGs5VWZOVk5LYTkxZEhyOXNuMGllbFh0ZktoeGpoVEx0S1pUQlZ3NEJHZzVKdGxrdTZ2WWc1SEwzSUNSYzc4YVE5U2gwMkJmU2Nhc0diK1ljSnQxVUxMbE41TUcvVUlpVzFKS2J2RDRFWkxrazVzWGwvMVhzREFROS95ZVU3T2QvTVhLMmphM091cGo4blZpZ1FOQUd3WU5tK25pRHhLWldjVGwwMEY0RUw5b3JtWklCVm80MlNicUJDQ1NjNEMzMWFrV2ZPRHY1M2IiLCJtYWMiOiJkZGRlNmMwMmVhNmU3NTU5Y2U4ZmY0MjI2ZTkyZDA0NzA0OWQ4NDRlMjJmMzkyZjdhNjA3OTc1ZTI4MTA1M2Y3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRRb0pNRDZlNXA5MWlKVGJZcW9mMEE9PSIsInZhbHVlIjoiNmRnNUZmMVhudmdjMWwzYlhQVTdTWDF0MkRycklka1hvVENYZjlzWlNOYUY2c1dwNnhuUklwRGFpY2x3WTdkQ0NhdGxTM0owM09JYXQ0bTRYWUVtZUtHMnkyMmlWOGVHem9SOHRUK3B0OEJJOS9Od0gzWTc4OUx6UkZlTHdFTmt1SWRwM0RkcEtTWTJQTHRpbjYvQ0FZbG80WUNObThBUzZUdXdURnpxRER6bmp5Qy9sTlFyL1owNjJ4VUxqMjFQL1FXSkVySmRmaDR2L1k0MVh3QjVSK1VRTE1QbGVxL0JDTzdOcVdnUkFRb3lWTlU5YytOUzZNa284d2g5KzB4MFpUTmdDYTNmbDdvVG9pV2hjMDk3cW9Rb2lDR1dGSnliVEphbkRkSDlBRWtvbjJVdlgvSlRBRFNaK1daMWNSZE4zbWpFaTMxNnNyUjhmN0t3SkNSbnRQVzZOZHRIVDY4eXBIRGFxRUIvWExDcDZ5Y3lKVUt5eFdsUWtjVzZoTUpxZDZMUVRvZnBrTVhFZDk5OWFNTGpoT1p5Zll0ZnBseTB4L1lVRUhmb3RkYzRDVzFnU3dyVitqS0YzNXI0OTBLcURIQnJrZHllK1QwUExGd2M5T3ROaWdVSUpvZmtCMWxnakg1L2pXYTRZbFFJQVhJVTR0MUREdzdSN0N5Q0J6RzIiLCJtYWMiOiIyZjI4MjhhYjdiMWE0YzEwMjA1ZjZkMjkxOWVkNGFkNGEyNzI3MTVjNTBlMDNlN2M4YWI2NzAzMjZhNTcyZDgyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9TNGs3R2pFK0xiQU5yZzFpNEdHcWc9PSIsInZhbHVlIjoiUzgzMDhXU2hjS2ZUZnIyelk0dzMzZmNxdmx6b1lXSStoWmJqaGl6SmxkQVlTNHlHNXUveE0veExCb0pwQ0JWcGxjTG5lVnhFMzdFejhDM1p5RU1ucGJCWXJTSVJtNjE2Z1RHdytGbXczTlVmVi9XQUh0R3hwaDRDK3oyL3owSi95eXZ2QTRDR0hQQTdPKzJqZ25PcXhqNzNmY2JUcTdpSFRtaVRSZEp3YWJsOTVYck05emFVT0NiaXJmY0E4eWpUVXBXQVA3Y1QxRCs2UjQrb1FjeXdaQ243cGRoZCtWOXc4RnlnRmsxbDRSKzlVRFVOaVFYbkc1WitFUCt6M29Bd1N6Z1BJbER6M2VGV3dOWHpKUkhaN1F6SllyU0FpS2ZoYVg3MGRuOGhiWHE2R3FKVnJvaFlmcGs5VWZOVk5LYTkxZEhyOXNuMGllbFh0ZktoeGpoVEx0S1pUQlZ3NEJHZzVKdGxrdTZ2WWc1SEwzSUNSYzc4YVE5U2gwMkJmU2Nhc0diK1ljSnQxVUxMbE41TUcvVUlpVzFKS2J2RDRFWkxrazVzWGwvMVhzREFROS95ZVU3T2QvTVhLMmphM091cGo4blZpZ1FOQUd3WU5tK25pRHhLWldjVGwwMEY0RUw5b3JtWklCVm80MlNicUJDQ1NjNEMzMWFrV2ZPRHY1M2IiLCJtYWMiOiJkZGRlNmMwMmVhNmU3NTU5Y2U4ZmY0MjI2ZTkyZDA0NzA0OWQ4NDRlMjJmMzkyZjdhNjA3OTc1ZTI4MTA1M2Y3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRRb0pNRDZlNXA5MWlKVGJZcW9mMEE9PSIsInZhbHVlIjoiNmRnNUZmMVhudmdjMWwzYlhQVTdTWDF0MkRycklka1hvVENYZjlzWlNOYUY2c1dwNnhuUklwRGFpY2x3WTdkQ0NhdGxTM0owM09JYXQ0bTRYWUVtZUtHMnkyMmlWOGVHem9SOHRUK3B0OEJJOS9Od0gzWTc4OUx6UkZlTHdFTmt1SWRwM0RkcEtTWTJQTHRpbjYvQ0FZbG80WUNObThBUzZUdXdURnpxRER6bmp5Qy9sTlFyL1owNjJ4VUxqMjFQL1FXSkVySmRmaDR2L1k0MVh3QjVSK1VRTE1QbGVxL0JDTzdOcVdnUkFRb3lWTlU5YytOUzZNa284d2g5KzB4MFpUTmdDYTNmbDdvVG9pV2hjMDk3cW9Rb2lDR1dGSnliVEphbkRkSDlBRWtvbjJVdlgvSlRBRFNaK1daMWNSZE4zbWpFaTMxNnNyUjhmN0t3SkNSbnRQVzZOZHRIVDY4eXBIRGFxRUIvWExDcDZ5Y3lKVUt5eFdsUWtjVzZoTUpxZDZMUVRvZnBrTVhFZDk5OWFNTGpoT1p5Zll0ZnBseTB4L1lVRUhmb3RkYzRDVzFnU3dyVitqS0YzNXI0OTBLcURIQnJrZHllK1QwUExGd2M5T3ROaWdVSUpvZmtCMWxnakg1L2pXYTRZbFFJQVhJVTR0MUREdzdSN0N5Q0J6RzIiLCJtYWMiOiIyZjI4MjhhYjdiMWE0YzEwMjA1ZjZkMjkxOWVkNGFkNGEyNzI3MTVjNTBlMDNlN2M4YWI2NzAzMjZhNTcyZDgyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1259428481\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1748824240 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"84 characters\">http://localhost/financial-operations/sales-analytics?date=2025-06-01&amp;warehouse_id=9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748824240\", {\"maxDepth\":0})</script>\n"}}