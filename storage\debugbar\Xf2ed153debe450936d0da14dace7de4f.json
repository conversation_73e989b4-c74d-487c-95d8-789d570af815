{"__meta": {"id": "Xf2ed153debe450936d0da14dace7de4f", "datetime": "2025-06-28 14:58:53", "utime": 1751122733.566157, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.136547, "end": 1751122733.566174, "duration": 2.429626941680908, "duration_str": "2.43s", "measures": [{"label": "Booting", "start": **********.136547, "relative_start": 0, "end": **********.528441, "relative_end": **********.528441, "duration": 0.3918938636779785, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.528454, "relative_start": 0.3919069766998291, "end": 1751122733.566175, "relative_end": 9.5367431640625e-07, "duration": 2.0377209186553955, "duration_str": "2.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45980784, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": 1751122732.646522, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": 1751122732.862448, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": 1751122733.471961, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1751122733.528943, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.06325, "accumulated_duration_str": "63.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.745481, "duration": 0.05631, "duration_str": "56.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 89.028}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.803987, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 89.028, "width_percent": 4.901}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.841836, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 93.929, "width_percent": 0.648}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751122732.784191, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 94.577, "width_percent": 1.154}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751122733.095968, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 95.731, "width_percent": 0.949}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4090}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751122733.39969, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4090", "source": "app/Models/Utility.php:4090", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4090", "ajax": false, "filename": "Utility.php", "line": "4090"}, "connection": "kdmkjkqknb", "start_percent": 96.68, "width_percent": 0.964}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4091}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751122733.4379928, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4091", "source": "app/Models/Utility.php:4091", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4091", "ajax": false, "filename": "Utility.php", "line": "4091"}, "connection": "kdmkjkqknb", "start_percent": 97.644, "width_percent": 0.917}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751122733.439928, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 98.561, "width_percent": 0.553}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq24\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": 1751122733.526201, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 99.115, "width_percent": 0.885}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PwomDNqtjAjw0WOXQYArRKuErsG2ZbMSUo0ei1cv", "url": "array:1 [\n  \"intended\" => \"http://localhost/inventory-management\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-689331063 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-689331063\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1791839370 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1791839370\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-677889973 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-677889973\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1685987916 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=lntfvz%7C1751069310598%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZiWmJuS1FkUnV4QmFuU2RzUDVxY3c9PSIsInZhbHVlIjoiNU5ybXVyREhyNjlCNkZ6M3UyelQybWFtQ2NiMmtuWWZyVnY2YVY1bkl5eHV5VGxSWW1lMXRmT3VFM0VPaVp5bi9MeWpzN09mOVNSVnpub0txNVJHUElEb3gwNW1YRXBMZDRLYjBQdHlhTklvUGZqWVJlNXZkWStFdEVEUGFaNmNOK0hyNlRHVXZobzZzYU9qdkFxR2gxUFZHWVFtRmcwaCtxY3l0Mm5VeFNMUnFxWVF1Z2ZlU0RwR3NOdnZ1aFRUenJrakdOeTlIUE1mMlRpYVRsVkpVdS94OU1VYWZwenpqaVQ2NGozekswVHVBNnJTMC90L3llMEtZYnA0aVNpSkNHUXc0WXRoTCsvb0JIUmx2UVpMQUNMTll0UU9rNHJjSSt3cGs0VHUxSWRtNDV1M3NrcFJBb1Boc2ZsUENid3FyTkEzcG1UTUZKT0VYRG5KMWkwTWhsMVg5UzFrc3AvRWFZRHpxQ0orMGxXME1jVVRLVkJJaUZFOEpBNU9JMGlPeGNTQ0l6SE1ZSXVaRTNkS3ZNNHQyQkFHNVc0NjBnV3hBMWNtN01BZTFOZXFtWHpBblN2NGJlTSs1OEljTHJ3TVZ5dmpMRDh5T3dzUkRPSnphQ0pGSEhNVlF6OGszemtnOUV6WjM4Z1p1TFh3bkdwSTZqQzJMYndBZ1dJNkdseGwiLCJtYWMiOiI4NGU0MWE0NTllMTM2Mzk0ZGRhN2RhYWNlZTg4ZTczN2JjYzVkZjdiYjhiM2U5Mzk3MGE2ZDQ1NWNhYjBjOWQ5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhXRXlxSHZVWEJzMlVCRWdycFdaZVE9PSIsInZhbHVlIjoiOGJYNDZpZHpRbE43WFFtdEkyMTlKYnFKTmF0M0prUXJzWTVNYk5rZm5tY21ubFV6VEF1V3VoWkV2SE04QytoMHU2TGJRWVl0aW40a2VSNUZLNkh4dG5SN3JKTGhYRnNqM0Eza3VWbmFKVTV0N3QzaFQyekV2S3VEMmZkUjR6UllMb2sxSURicjFlUGV0b1FBSXd3Y2NEdnVnMnd6bU9MaUZ6L3JGUHgxV3dQNTl5ejhuT3hrMWhjRE5Ba0Vhby9rbUUxLzJ3SFc2R2VsMXNza3lVZmhsL2M4UHZreDEvYmxFckJRYkc5c0UzdWFPbWt5dHV1dFdMQkV0NnJyMG5BOHIraUhMblBHK2R4MVpYUFV1Rm0xM3VZSUV0a3FsVVA5bnNHVjcxRnA1ZlNSWEViT1JYSzBFQnoxcFBva3RabERSNUVvYm1FdDVwcmlMRzdZU1I2dlYwblBYdU15UmZDUFpibG1lNXRsVExzemRZemRYUnlZN2dBZHZvNzZWUUc0V05EdElLd2pzbkx5aWNVNGxFZnNBSURhMGVWcVNRSzM5Z3JNRTRHZVM2R3N0alR5cVpzaDhvdzZaVFpBeUczWElGV216UW1OT0l0b1dvR1ZiYWYwbk9UcXVaWXhSU3dLdXN4NklsdFJkQkpWdG4wV3EreUYyaEtnOUhPZHB2bS8iLCJtYWMiOiJmZTNkYWE5NGE2ODhkNDA2MGE3Nzk4NmQ5MWM5NzExODU0ZDY0ZGQ5OTkxOGI3MTBmMjYyMDFhZWRkODY1ZjQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685987916\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1081651623 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PwomDNqtjAjw0WOXQYArRKuErsG2ZbMSUo0ei1cv</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">evJ8U8kdhK9vrjPHB0sH0Q9p7HHVBlhqIcIhIa08</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081651623\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1779232871 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:58:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImE3cytWQ2M4K1pOZ29jK1NjV0tjbEE9PSIsInZhbHVlIjoiMWdPalJJU3BSY2hzcDF3SzdIS056ZlAvYnpidWNLNFNHOXdoTjc2S2lxWHhmb1FSbXFKSm9kaEdIa2NlRjRDRUhKMUxibVR6Q0JxOEZkMDlSaTNTZVcrcCtIUGN3SVVNZ0I5TGNObnhxZGhzSEt5bmJLOTMvdkhPb3lZRFR2aEtCNWp2eUJ5YmxGSHNyZHlIWjFCclRQU0N0eElTUFFmSmVGWFBaNDQ0Z0M2OS9qcDZqN3kyVFpxbitZblRYekFxTDVEYlQ0ZFl0QkQ0K2FQVWYvVWdXMDRhRWJIeWVhanVUWmcwTkdCQ2xsZ0tOc1Mzd1ZxeTNkL1Y4amxXdGNHVGh5T3NSdUJSK0VEcjVMRUJ4RmYybTdYNXpKTlVpb3F1ZlFiNTZYZGpEcWNYQ1F2MG1rUjVDU0tPaHRQMk9nNUQ2UC93d3JNUDgyLzc0Vm00UXpvbmlzQXBTWjJVZGpoRXl0M0YrVUJRTVNuMVZST2kzekp5UmVjVnNmVENIeDNwN3F2ajZBeXNxSlBXUTkvdWJoUnppOTlXY2phdFBWbTF5QWtGTFRaU1I0Vndyc2lQNmVhdkNGdVZrem1mcmZvQzRJZU5KVHhZWGQ4OTZsKzkrK0dyUmhyaURJVzRJNHBwRUFnL25rOWRPSkdBZFB4M3U3REFJSWhLY29nU2cxUm0iLCJtYWMiOiIwNjI5ZTM5ZTZmY2EzM2YyYzQ2YmI0ZjQ1M2M5MmQwMzcwMDdjNmMzYjE5YjVkZDg0Yzg1N2Y3NmMwMDJiOGFlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:58:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IndlRXppZ0tuL2ZwTWZGRVU4eXY1UEE9PSIsInZhbHVlIjoiaDVadm1qT3FWOW5vSjdKRXRVbTlzQXhPUGloNHYyVVRTbDBnblM5LzhZa0lCSlhzaEpNNWRua0U2SzlYMEpybk95ajQyOGJDQVBhYVQ1UVZYVmtiVzBqOFRYYklNd3ZMRTVpcDNHbmsrdlJEQnRRelhpeWU1bXFxNGsyNldheER5K0tBWEYydTRsZ1V6ZVZBYmkwa3VQMjI5VDJ2dGxaMGFlQWJOK1gyZ09oZzc1TzZIUUk4QjhSV2pVYlFUK2ErZWJTZ2xlTHNlMzBjeTgyWENhcGN2Q3hzYmFVellvQ1RSY01wT28vTHk1eUlKTXNxOWMxMVY3THVUR3lIam94WDRVdXd0dXlMMDA0SnNNRGNMU0cvQkxQdDlaVlhzd0phVWx0SytwWDlhZkhRUXZtYzVOVkE2OUJGYjJGYWtuMGI2SzZYdHU1VHRKZTY5ZVpyQXkxdk1XQUp3dzVwaFpnR1JGSU5pRHdFZFlrdjdsSHZhSG51cnlrcm90YS9XZmhIMzVXdVRQdGEzMHpjRHIzVkkzWkV0ajlxVHNmdUY2YmFmSm8rRHpJS1ZRL3FtTkJ4akkzdnZiTElyV3ZVWWRnalJodmh5K0FMcmJjMDJlSnlxanc1dUxkWUs0MHJFSDJXb1NtdTM0WDJnc0pVamJaWGNId1Bnd2R2aUh1TW9ZbFAiLCJtYWMiOiI4ZDJhMzA3YWE1M2U5YmFhNDllM2NlMWYwYmRmMjA3NWJjYWIxMGFmM2Q3YmQ0ZmQ0ZDQ3NjlmZjBjODExODk2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:58:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImE3cytWQ2M4K1pOZ29jK1NjV0tjbEE9PSIsInZhbHVlIjoiMWdPalJJU3BSY2hzcDF3SzdIS056ZlAvYnpidWNLNFNHOXdoTjc2S2lxWHhmb1FSbXFKSm9kaEdIa2NlRjRDRUhKMUxibVR6Q0JxOEZkMDlSaTNTZVcrcCtIUGN3SVVNZ0I5TGNObnhxZGhzSEt5bmJLOTMvdkhPb3lZRFR2aEtCNWp2eUJ5YmxGSHNyZHlIWjFCclRQU0N0eElTUFFmSmVGWFBaNDQ0Z0M2OS9qcDZqN3kyVFpxbitZblRYekFxTDVEYlQ0ZFl0QkQ0K2FQVWYvVWdXMDRhRWJIeWVhanVUWmcwTkdCQ2xsZ0tOc1Mzd1ZxeTNkL1Y4amxXdGNHVGh5T3NSdUJSK0VEcjVMRUJ4RmYybTdYNXpKTlVpb3F1ZlFiNTZYZGpEcWNYQ1F2MG1rUjVDU0tPaHRQMk9nNUQ2UC93d3JNUDgyLzc0Vm00UXpvbmlzQXBTWjJVZGpoRXl0M0YrVUJRTVNuMVZST2kzekp5UmVjVnNmVENIeDNwN3F2ajZBeXNxSlBXUTkvdWJoUnppOTlXY2phdFBWbTF5QWtGTFRaU1I0Vndyc2lQNmVhdkNGdVZrem1mcmZvQzRJZU5KVHhZWGQ4OTZsKzkrK0dyUmhyaURJVzRJNHBwRUFnL25rOWRPSkdBZFB4M3U3REFJSWhLY29nU2cxUm0iLCJtYWMiOiIwNjI5ZTM5ZTZmY2EzM2YyYzQ2YmI0ZjQ1M2M5MmQwMzcwMDdjNmMzYjE5YjVkZDg0Yzg1N2Y3NmMwMDJiOGFlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:58:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IndlRXppZ0tuL2ZwTWZGRVU4eXY1UEE9PSIsInZhbHVlIjoiaDVadm1qT3FWOW5vSjdKRXRVbTlzQXhPUGloNHYyVVRTbDBnblM5LzhZa0lCSlhzaEpNNWRua0U2SzlYMEpybk95ajQyOGJDQVBhYVQ1UVZYVmtiVzBqOFRYYklNd3ZMRTVpcDNHbmsrdlJEQnRRelhpeWU1bXFxNGsyNldheER5K0tBWEYydTRsZ1V6ZVZBYmkwa3VQMjI5VDJ2dGxaMGFlQWJOK1gyZ09oZzc1TzZIUUk4QjhSV2pVYlFUK2ErZWJTZ2xlTHNlMzBjeTgyWENhcGN2Q3hzYmFVellvQ1RSY01wT28vTHk1eUlKTXNxOWMxMVY3THVUR3lIam94WDRVdXd0dXlMMDA0SnNNRGNMU0cvQkxQdDlaVlhzd0phVWx0SytwWDlhZkhRUXZtYzVOVkE2OUJGYjJGYWtuMGI2SzZYdHU1VHRKZTY5ZVpyQXkxdk1XQUp3dzVwaFpnR1JGSU5pRHdFZFlrdjdsSHZhSG51cnlrcm90YS9XZmhIMzVXdVRQdGEzMHpjRHIzVkkzWkV0ajlxVHNmdUY2YmFmSm8rRHpJS1ZRL3FtTkJ4akkzdnZiTElyV3ZVWWRnalJodmh5K0FMcmJjMDJlSnlxanc1dUxkWUs0MHJFSDJXb1NtdTM0WDJnc0pVamJaWGNId1Bnd2R2aUh1TW9ZbFAiLCJtYWMiOiI4ZDJhMzA3YWE1M2U5YmFhNDllM2NlMWYwYmRmMjA3NWJjYWIxMGFmM2Q3YmQ0ZmQ0ZDQ3NjlmZjBjODExODk2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:58:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779232871\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1011079414 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PwomDNqtjAjw0WOXQYArRKuErsG2ZbMSUo0ei1cv</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1011079414\", {\"maxDepth\":0})</script>\n"}}