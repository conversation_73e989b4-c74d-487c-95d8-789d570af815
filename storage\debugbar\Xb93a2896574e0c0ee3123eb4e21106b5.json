{"__meta": {"id": "Xb93a2896574e0c0ee3123eb4e21106b5", "datetime": "2025-06-28 16:01:25", "utime": **********.911631, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.399769, "end": **********.911647, "duration": 0.5118780136108398, "duration_str": "512ms", "measures": [{"label": "Booting", "start": **********.399769, "relative_start": 0, "end": **********.847263, "relative_end": **********.847263, "duration": 0.4474940299987793, "duration_str": "447ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.847272, "relative_start": 0.44750285148620605, "end": **********.91165, "relative_end": 2.86102294921875e-06, "duration": 0.06437802314758301, "duration_str": "64.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831920, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00639, "accumulated_duration_str": "6.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.885302, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.291}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.89694, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.291, "width_percent": 9.859}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"pr%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"pr%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;pr%", "%&lt;div class=&quot;product-stock&quot; id=&quot;pr%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8998709, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 36.15, "width_percent": 63.85}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-417191557 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-417191557\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-825929171 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-825929171\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-585604891 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;pr</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585604891\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1273704404 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">66</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRSWjlOakVoMGtKMjloR2dVNVU3alE9PSIsInZhbHVlIjoiZVVzck9ST3M4NnJTSnVGQTlaYlJ6NWpxbkRVYnBaQzV4bG05b3RnQW9QOFh3YVRhWjhodyt2QnR6dkRNOWxDNkJwZ2ZnL3laRjQrRFBkY2xaMlkrUTJaN0s2QzRYYm9PM3dXWXRPa1ZJRk16ZDFwVnpHRGxtbVhPTjJjanhZTzBFOEgrYVFtUEZmbGdFVmZ3NVVDTkRBd25RdlFEWDE1NkVLUmZZMm8zN0VuU2ZKcEhKMU5CSFRoNlFpUjRTN3A5RS85SUw5SmM3QzJXd0t4eWxIYm1VTURoMzN1eEdta3N4NkYwT3ZRU0xWb202WXd4NS9uaUdFZHo3Tm1vWG44RndPRVpFZitKM1JNMGFrTnUyUjFod1k3dm90RWFaTDdpcEtoUEdzWDhPdGZNMTAyZXRqTjFvUHNmM1BzT2NIQzhQMFpJWFJBM0hEMW5EcElZeDN6b05oamtHZHBKZmZxSGdnOHVUVHp3RVlXVHk2MFhuZ3RLYnduakp2Nk5MOGZPZWpTYWFXcU9PNU1yZUJOQ0M3dWJYY1QwTStkTENPcE5YbHRoM2Ezd2JUMEZ2T3M5L1FkMHlJSEdSWnlwcUdQRHVESVpyYkpjSnVPVE5Pd3BDMUdpMTdkTTRMTlFqWFNrbERhUmRIWCs1M1VjcTRJK0N2OWc0OUZvalpUOStvMEciLCJtYWMiOiIwYmUwZjU0NzdmY2JiY2M4NDJlMzAxZjk2OWZkMzUwOTY2OTA5YTI2YmE5MDY2OWUyNzg5NmY0NGRiM2Q1NGUyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkN6OXUxRFM3cDF5cExJQzdsN2NYTXc9PSIsInZhbHVlIjoiL3EvK1RBN2NkMHVaMXUyajhmZjB6Nk9OVXExZ1k3endKY2drMk1DbFljMkQyeG4yZTJNRFVXQURvRmNwUkVpVVB1SnNZNTJZL2pmSjZBeVdqMTd3eWN0T2ZYL1NVY2JqK3pYT0hkeitQd25GRVdNVlpZUVRXaU9UNksvaVVxVVdsakdWOWxITnJ1c1JsVDZpN2RaRGJ2bitTS2pHVUF3ZTlZczArWnN1eGVUTnVmSzhnampKOHR2UTFrU0UyNHZwV3dyUDdkMmlPNjNOc01tN3ltQXp6ai9QdW42WW1sdG1iSjJvdlR0YmVmUXNzOEtiaWlYUWU4UWdUMTk3UGpHRWxoRjBOTitPNEZTeHhsaDZIeVI0bk5WZGh0RE04ZWtTdVF2d09PcUcwWFNhc04xdDZkcDJXQ0E2ZGJtSnVCdGtFbUJPTDI4TklJdkUwa3RkL0dteHY2M1hlby9pZDlGWDlIVHp5WE5sY2c0RUJMLzFwSkNlQVp5NEY2MlBoamF2RzZ5Z0I1QnAwVDd0WFhXMjJZSW5LSUljd0RlNFdWOVhJRG8ya0VnZFk3UkhINUtlVGNxRll1ZWtEY3hab3ZCQlg4cFprNGxBM3R4OVAyVXpKS0F6NkE3RzdGMHFjbEhXNWpwRjBBYzdBSkR1L09QbTlxOHRIbUtnczE3M0RCT0EiLCJtYWMiOiI3ZTViMGU1ZWEwZWVmZWQ2ZjZlZmUwNjdiYmYxZTExMmY1ZDM3NTY1MWQyN2Y0NTY2OTg3MjdlYjY4ZDY4ZDI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273704404\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-88418071 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88418071\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1513069996 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktVMFBtZ3ZqRWVqVFprb1RsTlRPVWc9PSIsInZhbHVlIjoiWXhsaXQyMmJaVkNkYzZ3dTEzZ3hleG5lOWpScVMrSVY5MkNuN3BKQzRnZTZmcmtibnVJN1pZZ1dFWUdULy81cmRIcWI4VDloWC9sR2dZamxNSXp3SThZZTFPNW85dFZ2S3FPQW9xRVNvb1hUQUM1TDh0Q3ErWkR4NGo2Q3JQQ2RoeStYNGE2ckNhZHN4VlNldER1TEljeENmaDk0clBITWxRclN3eEJxTElKM2xPaTlQdXVlOFptTUtLMy9PV1p1SGU3TTNRK0EyM1BqWlJoaGh1WjVoMjdoeFhkSGdtMWd6b0NuTVlzNmFuN3YxeTlTZ3JCb09rMXZvNW45c2R3Y1oxMGNSTmNHQVRTR1ZsNWFOUW1kU2tpcW56ZThhOG16WkZnOHU0YVliRnJURFRWTUQzdjNRNldMVURkYWVoTWEzZ0p4cUt5SVRDdGY1YTFiK25WK2pYSC9lNHRMb3JVLzhxMDBLd2dLZzl0VS9wdW9hc2cvdGRIcHg2QXpvOE1EY29SVUJVcHM4bTFVM1FCeXUzdmZwSGxnRThvTWJ3b253YU5XVW5jQ3BPNHdUMGJoMHQ4TkJLOGNkOUdYT1lJUytoaDN4R0V1Slh1a1ZwY0JWM0JkaVg3K3BQMXQ2TThlYWQ4YlBZTlZBOE43QlFTTGRiNWtyUXZ2VzBuRmtPUVQiLCJtYWMiOiJiYWE1YTk5NzBlNTIxZTc5M2RlMjdhMTA2Y2FjYmNmM2E1NzNlNjM2OWVkMGIyMjM2NWYxNzk4ZWRlNGVmMmJmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IndYY0lPKzdHcDFBMnUvTDAzYU16Nnc9PSIsInZhbHVlIjoieGRna0ZiaEJUYmxSSUdLcUF1aTU2c0k0TndvQkN1Rm5ncXI0UXFqNFBTUnRUSTRqdmUydjdod3ZrUG4yMjVleE5tTzZUZG8zTGpHRVdpSDRmSFhYNkN3QWFNUzZ5NW03N1g0cE9FQ1lDTWY0S1BISjN2WkFsdktVckRoOEpnWFM5WEVJOFJWN0Y2c1R5SVBKT09Ca05pNHY5RDdKQ0tkekI4elloMVIzRTBIc1JQR240REJXU3M0SSsyTVFzdGs5L0RGSzFTZzlDbUpDVTRJWjFMdEJQbWNEMXZ5WldKRnhCeGhUSXJFR2hHU3JWTy9YalI0b1paQlNqU2dxM2UyTzBGNWEwdFJ2ZzIzWXpRcG81dDc4QmtwVUh6Nk5vR1lGK2VSa1dpb2VGazBBR2JaNElJd25ndnFXTTVQNE10NitJaEFscjZXdm95NkNqVGI0N3BHaTVpYnZ3clJnV3NGNFpwMXlXZVZCMmgwOWMxdDdaMlJUcFFhcisyaUFrME9GdmlkNVFkKzVzdCtoZlFmVXRPTFBaS0Zad2VITnc3WCtMNGExaGw1TEpQRGhSYmhCNEwrM0lYcVZFUjNmR0dtc3U0T3g3cGdjQXlkVUw4MXNueFlaeXFhN3o3R1ZUQlkrNlFaVnBkdWZXVmdIbE9YUG1ndUp5aWc4VnNRUS9zTXEiLCJtYWMiOiI5Y2NhODUxMjkxNjQ4OWFjNGFjNTJkMmE2NWZlM2NlN2Y2ZTAwYTUyMDM1MmYzOGZkZWNkN2Q2YjBjY2Y0MzY0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktVMFBtZ3ZqRWVqVFprb1RsTlRPVWc9PSIsInZhbHVlIjoiWXhsaXQyMmJaVkNkYzZ3dTEzZ3hleG5lOWpScVMrSVY5MkNuN3BKQzRnZTZmcmtibnVJN1pZZ1dFWUdULy81cmRIcWI4VDloWC9sR2dZamxNSXp3SThZZTFPNW85dFZ2S3FPQW9xRVNvb1hUQUM1TDh0Q3ErWkR4NGo2Q3JQQ2RoeStYNGE2ckNhZHN4VlNldER1TEljeENmaDk0clBITWxRclN3eEJxTElKM2xPaTlQdXVlOFptTUtLMy9PV1p1SGU3TTNRK0EyM1BqWlJoaGh1WjVoMjdoeFhkSGdtMWd6b0NuTVlzNmFuN3YxeTlTZ3JCb09rMXZvNW45c2R3Y1oxMGNSTmNHQVRTR1ZsNWFOUW1kU2tpcW56ZThhOG16WkZnOHU0YVliRnJURFRWTUQzdjNRNldMVURkYWVoTWEzZ0p4cUt5SVRDdGY1YTFiK25WK2pYSC9lNHRMb3JVLzhxMDBLd2dLZzl0VS9wdW9hc2cvdGRIcHg2QXpvOE1EY29SVUJVcHM4bTFVM1FCeXUzdmZwSGxnRThvTWJ3b253YU5XVW5jQ3BPNHdUMGJoMHQ4TkJLOGNkOUdYT1lJUytoaDN4R0V1Slh1a1ZwY0JWM0JkaVg3K3BQMXQ2TThlYWQ4YlBZTlZBOE43QlFTTGRiNWtyUXZ2VzBuRmtPUVQiLCJtYWMiOiJiYWE1YTk5NzBlNTIxZTc5M2RlMjdhMTA2Y2FjYmNmM2E1NzNlNjM2OWVkMGIyMjM2NWYxNzk4ZWRlNGVmMmJmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IndYY0lPKzdHcDFBMnUvTDAzYU16Nnc9PSIsInZhbHVlIjoieGRna0ZiaEJUYmxSSUdLcUF1aTU2c0k0TndvQkN1Rm5ncXI0UXFqNFBTUnRUSTRqdmUydjdod3ZrUG4yMjVleE5tTzZUZG8zTGpHRVdpSDRmSFhYNkN3QWFNUzZ5NW03N1g0cE9FQ1lDTWY0S1BISjN2WkFsdktVckRoOEpnWFM5WEVJOFJWN0Y2c1R5SVBKT09Ca05pNHY5RDdKQ0tkekI4elloMVIzRTBIc1JQR240REJXU3M0SSsyTVFzdGs5L0RGSzFTZzlDbUpDVTRJWjFMdEJQbWNEMXZ5WldKRnhCeGhUSXJFR2hHU3JWTy9YalI0b1paQlNqU2dxM2UyTzBGNWEwdFJ2ZzIzWXpRcG81dDc4QmtwVUh6Nk5vR1lGK2VSa1dpb2VGazBBR2JaNElJd25ndnFXTTVQNE10NitJaEFscjZXdm95NkNqVGI0N3BHaTVpYnZ3clJnV3NGNFpwMXlXZVZCMmgwOWMxdDdaMlJUcFFhcisyaUFrME9GdmlkNVFkKzVzdCtoZlFmVXRPTFBaS0Zad2VITnc3WCtMNGExaGw1TEpQRGhSYmhCNEwrM0lYcVZFUjNmR0dtc3U0T3g3cGdjQXlkVUw4MXNueFlaeXFhN3o3R1ZUQlkrNlFaVnBkdWZXVmdIbE9YUG1ndUp5aWc4VnNRUS9zTXEiLCJtYWMiOiI5Y2NhODUxMjkxNjQ4OWFjNGFjNTJkMmE2NWZlM2NlN2Y2ZTAwYTUyMDM1MmYzOGZkZWNkN2Q2YjBjY2Y0MzY0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513069996\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2005072640 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005072640\", {\"maxDepth\":0})</script>\n"}}