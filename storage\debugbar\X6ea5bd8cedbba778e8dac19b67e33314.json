{"__meta": {"id": "X6ea5bd8cedbba778e8dac19b67e33314", "datetime": "2025-06-28 16:01:16", "utime": **********.144069, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126475.679742, "end": **********.144086, "duration": 0.464343786239624, "duration_str": "464ms", "measures": [{"label": "Booting", "start": 1751126475.679742, "relative_start": 0, "end": **********.090303, "relative_end": **********.090303, "duration": 0.41056084632873535, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.090313, "relative_start": 0.4105708599090576, "end": **********.144087, "relative_end": 1.1920928955078125e-06, "duration": 0.053774118423461914, "duration_str": "53.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45695824, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00258, "accumulated_duration_str": "2.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1180701, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.729}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.128757, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.729, "width_percent": 19.38}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1346228, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.109, "width_percent": 15.891}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-994131052 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-994131052\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-662508929 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-662508929\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-545555107 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-545555107\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126473347%7C8%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFwZzZVVzVqQlVrdmVOdDBJSnROYnc9PSIsInZhbHVlIjoiTXRWdXBBQU84ZkZYYmcvMTV1aWd3WmdCUWJFWlFUZ05JZk5rMjRWK00xYXYycDNBdTc1UnJVM2R5SExISU1lWVVlODhSOWphaVpqeENFdzhsdFpoNnl6eko2T3B5NGltL1FjeTdGRDhlYzZFZTR0MXUzWnBybDdlb29uaGhmSlppSVRPMFNHTFRzQTlOWkxmSXVPOUNIMUliUHhFcGM2a29CZXhkci9SdzQ3bWVmcWJXM21YM0VhTjR6eVBOWnpOQ28rbmdoYjIzMlgrYU5QY1BmUkRwNENnaUJTbEM3QkJvVGtpN25MWnNOa1hSYkdHSHZ5SXkyWVZmMHFPUmUwaGZaRGlIT3dzVURkTzVUdzNtTGJqdUlGRTRnOWd4elpnRGMwQkZ3eW80TUNUbDhvNzF3aSswWXExWWsrckFubGdsNitSSW0xcld1eHNYSzdMeWNCUHN1TkN3RzBaT0E4bHRZVmNWcDFXZjI2V2QveklTeEhoM0pNSm9OdHFIVFN2V1dxWmlsUDZXWmV3UWI3V1V6S2ltZ0pUcklwakJOZUVOQXFJSDZQWUc4RnBjeGxTaG50ZWlvTjdrOWlSNDdvWWxjdU1sTVpydHhmWFNRbHRJdmFrK3Ewb080eXJjUmhlTE1aYTMvZitLQlppbVhhQm0xR1AyME9WRWNKZjdPam4iLCJtYWMiOiJjNjQ1YzEzODdhOGFjYTUxMjZiZThlMjg1YzRhYTAyNTUwNGIxZGEwMDUxNjk3MThkNjIzYzZjZjM4OGViOGU2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5TemV4bWFnZ0ZmM0ZzRk1TT1VoQ3c9PSIsInZhbHVlIjoiL3hud2pEMi9EMWtSR2U4NXJlSDZNMVI1bzI3Mmk2YTZRL0FacXNxeUd5eXhVUjkxUHVTUUlIT1M4R1paKzRqSFdjZXZuaHlkOWVMelU1K3BaQmRQbHdBRlI0M1FUSFhLeVhyVldqSXpKakZtWDBzTUJuOFNwVEpnZEd2cVFkdkNXNmQxZEtNNFp0UDVJVmorbjVPTldBdVlwOUJBUnduR2puM244c29LUlFwSGloUUFPNkY0V0t3R3VlQXprSi9uVnFmRU14aEpnZmljLzNLdWZ4UWxRRGIvMHRCdHJ2bHVnYmpJOUlkSUVZSEt4c3FQaHlxZWtFR2tEVkZsRE1ISkJoNG9ja1p2RXkwWktBNUtzYnRhWE9OdUVwR1Zrbk1JNjc2eThVNHFuSFk0UVdYTjk5VDRkYmN1cEhnNExsTEM4V2ZmVlNwSXBiMEZPNGJsSXJaU0R3NmJxNzFTc0JMQ0hWekNyRks1UFhxcHYrUjhzNVZac2dHczcyWnR3MEZia1RWbVlvT3lFWFFBRFIyekRpZ0tMOXNUVllDYlFvMHp1TWphTkpoK3M3Nk1Gdmp4aFo2V2Zqc2E0ZmYxaWxuQ1ljZkJWdE5NYjl5QVUxWXplejQ2MFZIOThEQVJ0M2VvMCtkcTB1SVRhNkJyR0JVeTNoc0d5MFp1ckhYRXpoS0kiLCJtYWMiOiJjMzFlOWU5YjA2YTVjOTA2YjU2NWM3MzVmMWU1OGQ1NDE4ZWYxZDI0ODlhMzc4NzI2NTY0ZmE3ZDc1NTEyYmE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1711024987 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711024987\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2342409 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlHT0o5Qy9id0dhSm9mVVZSSXVSTVE9PSIsInZhbHVlIjoiSVJ5V05xN0wyM0p6MTNXcmhtVHdSL2VlMUhTY080ZEFIN01Rd3dvU0dWWW4xM0Q2dmF2WktUZ2lmelQyS2l3b2grUjhpNVNjckdZYWFvL0lXS1hOTHpqTHlkc3E2VDFqZ1NLc1FSZUlMYjhjTmhHWmRCcFdVZjNSTkhBWFd0SkVqc2JPKy9raTBOc1RBWjhENUZSdnpURDJ2QjlTNkQ2K0xoS0MvZ0lUWWJPdWdDRmpxZ3lyWmJHdG9aNnVmdzBRQmROYmJTMTV2bVBSNnRWd054UHZtdHhORmgwcmRCOFdxbDFnQlRtaysxMTVjdUM0MzQwaFNxdFZIWVQ2SkNBcTA2RmNycEhXWHRUK0dEN2hBSW9HVGgyNGRrQllBV3AveDRpRDcxS05QcVFLMzN2cUU2YTZVa1U3cjNTTjdpcjhLdU8zSDVsVUFVTGMwVjY1Z3dnZFFQMjU2QVQzSTF1dnJhMnBXZ2ZrSWFtSXI2UEd5bkNwV0lmc1E0VEQzV2xDaHErV0ZFOWhaWWdCcEVjUktlRXVQTDdpaTJZd1pQWmtKODFqVWgzd1dtK1Z2NjBmTlY2RWJHUzBtVVJkc3ZYcnNObmNFYXMyeEVieW9QYnRwenlrODRJTXd6bkpQT24vb011TUNWZ0xVN1ZMSFhIRHZFTG4zWTFrNy94cW9NMUoiLCJtYWMiOiI0NjA2YzBkYTJhMjA3MGNhMTQzOWM2ZDA5MDQyNWVlMDhjNjIxMTk3ZTNmYjBjYTRmZmE4YWYwNTdkYzFmMTFlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJYZDQvS2s4Sk5FWmhjMTZsREhwTVE9PSIsInZhbHVlIjoicis1emoxMG1PdzF1TnNqbmhyd3lYZC9XN0lkMUdCc2FXMHYzNU4rd0s5VUpDcCtrSDc1VHBFK3diZjFuY0tKOFp1RzR0YTFCQzVEY2Rsbkc0TUd0SXBRZUx5L0RPT1J5SjJ1emMxdFdaQ0crcE56NGRtd1hMT3Q5dDQwS0k0bzVpWC9hU1BUYWRKcDE0eFQzdCtkSWtBcG1OVzhOVkgvWVM1OGZRMEcxVXpJUHdIRnB5bmtFMUUyMVk2WFhHTE83ODk3dTJzOE5sWCtnNzltYllDODBQdjFUbk1kRXYxanZiS3JpY0RqSElmZm0wb3d5bzNuZzVLcXVIb3ZoalZOY1c4TEk4S1dtcVM1S2JDcXRoOWJoQnBpUytjM1Y4Qm5mQzdSemV0NE9zOUt2NHhFaEtvYkhuRTA3eXVmeElETWFZdEtDRjI3U0RRY3RhVS9wRDhGMDh6bTZOUy9OdXdwSlE2R1daYzc0cVpObFREY2JleFZLcndlWTBTNjYyMXJTdDhXOTBiZEx2WWFpVUZPZ3VmN1Z1eFdOb1E1cXdpaHh1d3RQRGhCMGpYR3NyeU16Z1dqVTh0WUpyUTFJTHZMUDB6SlY1N3d2WXNuK0t4czVtY2dmTU9lNFg1ZlBqdGdCMkk2UHQ1VzR1aklpV1VFTnVQMmsxdVh3U0wzeUVoSlkiLCJtYWMiOiJiNWRjMjExODk5NGQwOTZkYThhNDQ2MjZiOTIxYWEzZDY2YzMyMTY0NTExMTNkZDEwNmMwNDZjOGQ3NGUzMmZlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlHT0o5Qy9id0dhSm9mVVZSSXVSTVE9PSIsInZhbHVlIjoiSVJ5V05xN0wyM0p6MTNXcmhtVHdSL2VlMUhTY080ZEFIN01Rd3dvU0dWWW4xM0Q2dmF2WktUZ2lmelQyS2l3b2grUjhpNVNjckdZYWFvL0lXS1hOTHpqTHlkc3E2VDFqZ1NLc1FSZUlMYjhjTmhHWmRCcFdVZjNSTkhBWFd0SkVqc2JPKy9raTBOc1RBWjhENUZSdnpURDJ2QjlTNkQ2K0xoS0MvZ0lUWWJPdWdDRmpxZ3lyWmJHdG9aNnVmdzBRQmROYmJTMTV2bVBSNnRWd054UHZtdHhORmgwcmRCOFdxbDFnQlRtaysxMTVjdUM0MzQwaFNxdFZIWVQ2SkNBcTA2RmNycEhXWHRUK0dEN2hBSW9HVGgyNGRrQllBV3AveDRpRDcxS05QcVFLMzN2cUU2YTZVa1U3cjNTTjdpcjhLdU8zSDVsVUFVTGMwVjY1Z3dnZFFQMjU2QVQzSTF1dnJhMnBXZ2ZrSWFtSXI2UEd5bkNwV0lmc1E0VEQzV2xDaHErV0ZFOWhaWWdCcEVjUktlRXVQTDdpaTJZd1pQWmtKODFqVWgzd1dtK1Z2NjBmTlY2RWJHUzBtVVJkc3ZYcnNObmNFYXMyeEVieW9QYnRwenlrODRJTXd6bkpQT24vb011TUNWZ0xVN1ZMSFhIRHZFTG4zWTFrNy94cW9NMUoiLCJtYWMiOiI0NjA2YzBkYTJhMjA3MGNhMTQzOWM2ZDA5MDQyNWVlMDhjNjIxMTk3ZTNmYjBjYTRmZmE4YWYwNTdkYzFmMTFlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJYZDQvS2s4Sk5FWmhjMTZsREhwTVE9PSIsInZhbHVlIjoicis1emoxMG1PdzF1TnNqbmhyd3lYZC9XN0lkMUdCc2FXMHYzNU4rd0s5VUpDcCtrSDc1VHBFK3diZjFuY0tKOFp1RzR0YTFCQzVEY2Rsbkc0TUd0SXBRZUx5L0RPT1J5SjJ1emMxdFdaQ0crcE56NGRtd1hMT3Q5dDQwS0k0bzVpWC9hU1BUYWRKcDE0eFQzdCtkSWtBcG1OVzhOVkgvWVM1OGZRMEcxVXpJUHdIRnB5bmtFMUUyMVk2WFhHTE83ODk3dTJzOE5sWCtnNzltYllDODBQdjFUbk1kRXYxanZiS3JpY0RqSElmZm0wb3d5bzNuZzVLcXVIb3ZoalZOY1c4TEk4S1dtcVM1S2JDcXRoOWJoQnBpUytjM1Y4Qm5mQzdSemV0NE9zOUt2NHhFaEtvYkhuRTA3eXVmeElETWFZdEtDRjI3U0RRY3RhVS9wRDhGMDh6bTZOUy9OdXdwSlE2R1daYzc0cVpObFREY2JleFZLcndlWTBTNjYyMXJTdDhXOTBiZEx2WWFpVUZPZ3VmN1Z1eFdOb1E1cXdpaHh1d3RQRGhCMGpYR3NyeU16Z1dqVTh0WUpyUTFJTHZMUDB6SlY1N3d2WXNuK0t4czVtY2dmTU9lNFg1ZlBqdGdCMkk2UHQ1VzR1aklpV1VFTnVQMmsxdVh3U0wzeUVoSlkiLCJtYWMiOiJiNWRjMjExODk5NGQwOTZkYThhNDQ2MjZiOTIxYWEzZDY2YzMyMTY0NTExMTNkZDEwNmMwNDZjOGQ3NGUzMmZlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2342409\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1662619635 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662619635\", {\"maxDepth\":0})</script>\n"}}