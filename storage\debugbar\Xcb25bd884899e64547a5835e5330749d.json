{"__meta": {"id": "Xcb25bd884899e64547a5835e5330749d", "datetime": "2025-06-28 15:26:28", "utime": **********.899677, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.381571, "end": **********.899697, "duration": 0.5181260108947754, "duration_str": "518ms", "measures": [{"label": "Booting", "start": **********.381571, "relative_start": 0, "end": **********.821445, "relative_end": **********.821445, "duration": 0.43987393379211426, "duration_str": "440ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.821454, "relative_start": 0.4398829936981201, "end": **********.8997, "relative_end": 2.86102294921875e-06, "duration": 0.07824587821960449, "duration_str": "78.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46307592, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2335\" onclick=\"\">app/Http/Controllers/PosController.php:2335-2369</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.026699999999999998, "accumulated_duration_str": "26.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.856184, "duration": 0.02624, "duration_str": "26.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.277}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.892606, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 98.277, "width_percent": 1.723}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1459/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-19632422 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-19632422\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1962092373 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1962092373\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1724825691 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1724825691\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-19835925 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124349103%7C15%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im8rc2FYMnlTUHpIalRvcTBNbWp1elE9PSIsInZhbHVlIjoiMHRWNllrYXoyaG9jVUlLYXpIOWNuK2NxYTkrVk5QajJhY3dxbmlwMkM5YnE2Vkc0TE92ZEcxUlExV3FMb0pwUEl0WGkzQ3FYVHhkQjI3cDl2dE9iejZDV29VQXh2S3hKU21DbmpPYy95RGYveGEwc2NJdHpYNk5Lb1hwbm5zU0gyR3pCSHhDNEtzOXBMdUJSRURoejArZHJiQldTdCs1dGpkcG94MHZzUXZHcCszT3lmT2p0eHM1T09qMC9sREVRZDkvY09iQWtEUXdtamJFMTMxelh1MWhwa29DcVdPcTRnajN1ZjVUSDlmUXdWeFRHN0pOQWJ6T0tVMDNhdGRLdWluOXRRcFhqTzRMQ0U3OGkvYWR5SE1KcFBqR2lRVlB2MW4xaU5FbTB3amxxazVEbnluVUl5MEhrYk9WU05hY0FIZVRmRmRjM3lKaFJIYXpNaDBiN1VZRnk3MlNHWEloYzZKYlA2aGVVdmUxWVJ3NzVLbEFPaCtFZmYxRWZnaDkxYTRmcHVkQzBIVlBXWi94ajRYbUNyV081UlRBcm5od1czOEpvaEdQSFJ3Q0VrV3IrSkdTTDJ5T0JnOGhCSkFWT2VJbXl2c1pRVU5jVExncUVwZ2E2Y1FndUJjcXRObjh3QzQ2RDl5RnVxZ1FJUUVKQnZRc0FWK2IrNWp3WTBwZnUiLCJtYWMiOiI2YTJjNmZlZmRhNDczYmVkOGJhY2FmMWIyYWQ0MmZkYTg4MGU2ZDQ0ODUyNDUzMDg1MDA2ZDljY2I1MWI2MTU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5QSG00ckVNT3B6Q2h0cEZ5dmN2Umc9PSIsInZhbHVlIjoiS0IvQ3ZOY3MyS0RNa1poUC9EN2RpOStraEJLLzEwTWYrdkFkaGtHMnFMM2pIeVBsNTZlVGNxNTNWeWRjTitLRjJIMDA2NnJVL0d1TlV5bGZYUFRsSFNJNW5qY2NEOVk3cEgvNHd6QnduR0dtZVVMT1FLTFdRNkUyc1daOTBFb0tzbldncGlGQmViRlQwQnNWTkVXNi9JY3FEY3AwZlFUT3Vodmw1RE1Yb2tPdDVtcDEvdlh1ZkYraDhpaE0zYUNGeUtFVXgyL3pzQzNJcWxtWHRhVmVxbnRaQkRrN0hmOEY2NU0xWTVHS2pMZ2JMS002bVNyT2x2Vm5ZbldnNWdIak8xOXU0U3FIRDVtaHNpT00zWHJzMXl1VVZ1Nnk3SGhoeUs0MzdodURrVDIvdEIvYmsyVkhReTBGaXBaa2JydHhYc1lCUnlwQXMwU1I5TC9GaUhDbDZ0dHE5amhPeXRvci90d3JWbmR4dXVQZ0g3UDRyaUtiRGxnN0VaVTAvbGluN3BIaDcrTmdma3RNeFJTWmx2WlJDazhKWnN1d0ROVHVidVlVcDBFVGFwMmc1ZHVQcVFENVhFNzgzSEt4TkZuTWp3dTEyNW1pOVQ2aWRFYnllMzlTRFN3ZGV3NHVVYVdyeFdPZWlDV3V6eEd3V2dKMXp2QWQxRFFlaGJ1bkJ5U2IiLCJtYWMiOiI3YjMzYTcyYTZhZDhmYjRiZGFkYjNkYjlhMzIzMDAyZDMwYTBlZjllNDJiMDUxNTg0ZmY2MWZiZjQzNzE0NjlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-19835925\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-908559363 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-908559363\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-728698768 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:26:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdNQmNycE1qQUszZGRkcC9XSG5SNnc9PSIsInZhbHVlIjoibEdralE4cmtTVjFCWnFCN0pqRVgwcUFJcXFFWVlSeU9zK0FBaVhkcTczdGFUblhaWFpHT2g0Yy9NOWlaQWlWaXBvOWRGMk1UdUY2NVhGbExFVU4wMzBNd3dsNXU0MlVOSTBXQWdQbklXb2NaclhOVjA3ZFN2STkvOTA4blJaVC9sZkRkN1g3TzFNeVlscnJyUENqWHhKWGlLWVFpa2xwOFZIeENjcmpVMjMwbWdYN09PMko2eGt2Yk9ENUEzKzFyMU1iamEvZUdzUnc5a09nQUZTRDRmZW5aNHlqeEJoZzhOcVlPQVpIa2t1cGwwMDRCQk92VmZDL1FraWZHcThjRzh5VXYzUXowZ09vTzF0bUxQQVBnSW5TVVVQY2FFWTgweXh2RlN4OWR4bkpIeUtNNHVNLys1cWd3a1RQS2U2TkhNQmY0Z21EanBnN0RDd3piR08wSVlVblJra1NiWUNEOWlKaG9zMGZPYzNid05PRUhGVjFaQ2lMcnJyNFhheksrdTV0Vk5wUmU3UWhPUjQ1ODVNSzg1U3VwakZTSy8xbjdmdDZzWEIzQjE1cGI3cmRQVXEvU1BaNmhLSElCUTM3dXp3ZkdiTWV2RXV5ZDRaN0ZsSk1JbFgxbVVzSmY1a0J5N1MyODZMM1NETU8wOHFTUE5Icy9ZWlQ0VUU4WXZoTDAiLCJtYWMiOiI0ZTgwMTRmZjNjY2ZiZjQyNzNiMjM4ZTcxZWNhYjBkODJhYTk0MDk3MjdkN2Y2MzQzYTYwMDYyYTA4OWJkN2NkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkppN3NwMENLbUxJY2diM0pLcDZqRnc9PSIsInZhbHVlIjoiN2tsNEY4TmVPUENUSk0zdW1PTVJuY1NVaGJjbmJ6eEt4RTFjTThSdUNRdFpuclc2RG96c010TjBGeDlRd0FNQWdSeEhqVklIanZzS2dQZklMNSs3VTZxSEdmeC9UYVU0QnZxNEcyais0MExOeEJGblJGTmRYMkljbTMwbVF1NXdBRXk4MGx1VG5ETDYxY2pYUVQxOXBQWUpIblpBc25Pbm5CaDVhOGdzcTFoKzBnT3QyRGJKcEtIWUwwc1phVXVXN2llbVYvMUdhNHlTY05FL29XNGdtOWtzWnVuUVNPY2pGb1JOTkgxNDJyU0p3UkVZNHkxZWhLY0JhdVNTOENibFdlUTJWdWFuSXUvQXpKRGViWG1YVlJ2T0dLN2VoVXpzUXlSUTcyT1NyVTRCaWtZbDVRK0ZKWXVvOGJvRHBaNVhaL2cyaGx5MC9VL09EQVFqbzhtdVpMWFArSGxtOGxDZE5PcUl1SW4rd0tKYlExU0NPdW9sSnJjbUdwdCtlRElHK1ZVb0RUZTJVbUc2VlBrTWw2QlpBOHl3ZkJXUjhHWkcvWHRsZStOQjNBeUlKdGtEQndmaWNQYmd1dERybnhmbkNBQkxOckp4NnRLbjMzNDg4bDZrM2J6KzlVdUlRdVBHSExvbFJqMDVIakJzZFNlWE96bVFhRnhMOU0yay9zUzciLCJtYWMiOiJkOGI0MWUwZTdlMzIyYWFhYzBhZDRhM2JhNjMyNGViNjFjODkwOWI5MTBlMDMxMDkyYTlhNzc2ZTJkMTliYWU2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdNQmNycE1qQUszZGRkcC9XSG5SNnc9PSIsInZhbHVlIjoibEdralE4cmtTVjFCWnFCN0pqRVgwcUFJcXFFWVlSeU9zK0FBaVhkcTczdGFUblhaWFpHT2g0Yy9NOWlaQWlWaXBvOWRGMk1UdUY2NVhGbExFVU4wMzBNd3dsNXU0MlVOSTBXQWdQbklXb2NaclhOVjA3ZFN2STkvOTA4blJaVC9sZkRkN1g3TzFNeVlscnJyUENqWHhKWGlLWVFpa2xwOFZIeENjcmpVMjMwbWdYN09PMko2eGt2Yk9ENUEzKzFyMU1iamEvZUdzUnc5a09nQUZTRDRmZW5aNHlqeEJoZzhOcVlPQVpIa2t1cGwwMDRCQk92VmZDL1FraWZHcThjRzh5VXYzUXowZ09vTzF0bUxQQVBnSW5TVVVQY2FFWTgweXh2RlN4OWR4bkpIeUtNNHVNLys1cWd3a1RQS2U2TkhNQmY0Z21EanBnN0RDd3piR08wSVlVblJra1NiWUNEOWlKaG9zMGZPYzNid05PRUhGVjFaQ2lMcnJyNFhheksrdTV0Vk5wUmU3UWhPUjQ1ODVNSzg1U3VwakZTSy8xbjdmdDZzWEIzQjE1cGI3cmRQVXEvU1BaNmhLSElCUTM3dXp3ZkdiTWV2RXV5ZDRaN0ZsSk1JbFgxbVVzSmY1a0J5N1MyODZMM1NETU8wOHFTUE5Icy9ZWlQ0VUU4WXZoTDAiLCJtYWMiOiI0ZTgwMTRmZjNjY2ZiZjQyNzNiMjM4ZTcxZWNhYjBkODJhYTk0MDk3MjdkN2Y2MzQzYTYwMDYyYTA4OWJkN2NkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkppN3NwMENLbUxJY2diM0pLcDZqRnc9PSIsInZhbHVlIjoiN2tsNEY4TmVPUENUSk0zdW1PTVJuY1NVaGJjbmJ6eEt4RTFjTThSdUNRdFpuclc2RG96c010TjBGeDlRd0FNQWdSeEhqVklIanZzS2dQZklMNSs3VTZxSEdmeC9UYVU0QnZxNEcyais0MExOeEJGblJGTmRYMkljbTMwbVF1NXdBRXk4MGx1VG5ETDYxY2pYUVQxOXBQWUpIblpBc25Pbm5CaDVhOGdzcTFoKzBnT3QyRGJKcEtIWUwwc1phVXVXN2llbVYvMUdhNHlTY05FL29XNGdtOWtzWnVuUVNPY2pGb1JOTkgxNDJyU0p3UkVZNHkxZWhLY0JhdVNTOENibFdlUTJWdWFuSXUvQXpKRGViWG1YVlJ2T0dLN2VoVXpzUXlSUTcyT1NyVTRCaWtZbDVRK0ZKWXVvOGJvRHBaNVhaL2cyaGx5MC9VL09EQVFqbzhtdVpMWFArSGxtOGxDZE5PcUl1SW4rd0tKYlExU0NPdW9sSnJjbUdwdCtlRElHK1ZVb0RUZTJVbUc2VlBrTWw2QlpBOHl3ZkJXUjhHWkcvWHRsZStOQjNBeUlKdGtEQndmaWNQYmd1dERybnhmbkNBQkxOckp4NnRLbjMzNDg4bDZrM2J6KzlVdUlRdVBHSExvbFJqMDVIakJzZFNlWE96bVFhRnhMOU0yay9zUzciLCJtYWMiOiJkOGI0MWUwZTdlMzIyYWFhYzBhZDRhM2JhNjMyNGViNjFjODkwOWI5MTBlMDMxMDkyYTlhNzc2ZTJkMTliYWU2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728698768\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-213152017 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1459/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-213152017\", {\"maxDepth\":0})</script>\n"}}