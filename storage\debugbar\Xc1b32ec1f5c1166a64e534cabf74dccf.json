{"__meta": {"id": "Xc1b32ec1f5c1166a64e534cabf74dccf", "datetime": "2025-06-28 15:03:20", "utime": **********.825221, "method": "GET", "uri": "/financial-operations/product-analytics/stagnant-products?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.312611, "end": **********.825233, "duration": 0.5126218795776367, "duration_str": "513ms", "measures": [{"label": "Booting", "start": **********.312611, "relative_start": 0, "end": **********.705482, "relative_end": **********.705482, "duration": 0.3928709030151367, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.705492, "relative_start": 0.392880916595459, "end": **********.825235, "relative_end": 1.9073486328125e-06, "duration": 0.11974287033081055, "duration_str": "120ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 56568592, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/stagnant-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getStagnantProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.stagnant-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=513\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:513-582</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0576, "accumulated_duration_str": "57.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.741632, "duration": 0.01692, "duration_str": "16.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 29.375}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.767509, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 29.375, "width_percent": 0.59}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, ps.purchase_price * wp.quantity as stock_value, DATEDIFF(NOW(), ps.updated_at) as days_since_update, `ps`.`expiry_date`, CASE\nWHEN ps.expiry_date IS NOT NULL THEN DATEDIFF(ps.expiry_date, NOW())\nELSE NULL\nEND as days_to_expiry from `product_services` as `ps` inner join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT DISTINCT pp.product_id\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`quantity` > 0 and `sales`.`product_id` is null order by `stock_value` desc", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 561}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.770212, "duration": 0.04034, "duration_str": "40.34ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:561", "source": "app/Http/Controllers/ProductAnalyticsController.php:561", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=561", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "561"}, "connection": "kdmkjkqknb", "start_percent": 29.965, "width_percent": 70.035}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/stagnant-products", "status_code": "<pre class=sf-dump id=sf-dump-1904789538 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1904789538\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1037025594 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1037025594\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1441927779 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1441927779\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-727405288 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122996219%7C8%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjIxd0hFSm05UFRnYmFsVVF2STc0dWc9PSIsInZhbHVlIjoibDA0U0xqVldjNzRBTU1MdXZua2VwUVV2VjRqbmc1S1dIZDhvd0txZk1yRWdyeEhvRXZVM1BObVRlTHFJbmlsbGx4TlZVTVRST2IwcncyTGcwcVpTaXlvQXZsSDN0OXNlS2lPMmVsbTB4UU9mRDVqU0JlYUtKNkRibExjNEg2OGhZQVl1dWNEZUllbHlEVS9zVm5TTElMQkxKZUQ3OHBLUEI1bjdxSFQvOFRHS0E0YmNTMHB0bXFyeXNxc2RiM3JDRmhaYUNhWnE5UU5lZkZYTFNrV3Rjd0huTENFVUxGeTZseGxnWmFBem1mL0FqMWxqTU8wY0VwVzEvelNEWiszQmxpRDBDMisrU0RjQXRkNjU1OHdvVnhpNEVqRjFxeHpWRHlEeDd6Y3FQOUl2ckpYZWE0Sy9YZGdwb29WbFM1NmNSRTBYMFBQL2UrL2dqdjFoc1QyQytLT0NiVG5RMEdzOWUyeWk5dlJpZStDTjcrUEk2QkE4N2hoYjFYbGZDYnFWei85Vm1lVitOUHpKTUR4WEgyRFN5VmpXOVlzWmhzSWU2dGtvT3BDbXlYRFVsMGYydGRxU2EwbUVReld2RVVRbWhqZnlZTkFaMGdKK05PTE96YkxjRFRBMEw3QTAyNXJmZUZabUJPQzkzY2xhWWJWVnp0M2EyZ2ZZTGxDdDVjT2oiLCJtYWMiOiIyZTNhNzljMzA4ZGRkOTM4M2ExM2E5MzdiNWMzMjI2ZmNkYTY5MWI5N2ZlNDQ0ZDVhYjA2YzZlYmEyMGRiZmY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdXRWw5dHk1bmluYjd4alZ4ZzA3MVE9PSIsInZhbHVlIjoiSFZvZGJBaEY3VFRkZldBQk4vdVlCQ2NmUnZFWjdXK3h1M093VW5nMXRFWnZwckJOTXVDTnNZcUhVVmUrUFc5THlRNEtJYlV4VzBoamw5ci83Um8yYnNaSUVOb0F2aUI2cmpseHgzK1pmZnkzYThHVjB1K3F3cW8vREhPcTV0aHRjTHVqY2dGdHFLWGV1YjJyZjdodzJ0RXJmc0oya0pHSW9kZ0NpaTl0S2RxbnZKZHYwdnc0Mk9JeWZEcTdXaWE2ODd3T0U3QnFOWi9HNmRUZnIrZEE1MGJmS1Q4Q1ovSCtmc3ZIaE1vc2VKcEpqR0N4RDRpZzVLOGpGUlRKeDB4VTBpYU1ZRTYyeGc0R3VwT055V21ZUHRVaXd3WEFZc3hZQkpkdnNsOXRUcVhCNTd1THprbG9kam96Y2pJNER4RTJjRDNQQzBuMit2QjBIOVN1WGdxZUNVa2I0UTlISi83OFlQY3JCVXV4QncrSWhRbklJWXhiMzNjdW9OckozVHJBaVlPd0dJUGY2RW84Zys5dzBYTmx1QUZNalBiUHJhMXFwZnFBTi9lQU9iUUF5czFMN01sbEJrUEp4UU5rTnRaL3hQMWFXdUNaNVVjbHZzWlNESGtHVTVhbk54UUtGSFJzTU1pcTc0QlUvMS9udURVSmtDb0JVZ1BIWXk0TWJZaHEiLCJtYWMiOiJkYzBiNDk4MDVmNzY0NjNiOWRjZTExNmVhY2QwNDFjNGQ2OTU0YzU3N2VlYTNmYzY1NjJjMmVjNDFjNTQ5MmJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727405288\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1237001898 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237001898\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-276532207 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:03:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdyZ1hXWFJQbjU5R3dZUURaenZEOXc9PSIsInZhbHVlIjoicEdTRzVEMmJwczIyTjF0VFdSZm5nVUZtOHFOTk5Wam5ZRWhFUFJtSkNDTlFIMzBSc0hPcGNpbkJKcXY1R1lOTmZLUWhXZm1JVnNibTVzZW1raDF0RWFTUWN2V3ljTm5uS2RpQ0dBdHQxZkx5bkNWTHpRb0hQYnFpVEV0NTI1RnhZKzJTZm9aVS9qTnlzc29VSWIxN0hSMEI4ZU1wT2wxNy9iU2ZwVTlpRHhSUGJnSmJhVjB0VFNFcVNtR2Jvb2xBbVVkaUxtbkdSQk1VL0JCSTQwOWdRQWw0cjdhU0V6M29WK2tSWEtWRmhldnJkTkNmSWxpdU5PTm1hcnpzQlVtOXFFaGtycUNxZExzYm1VYjFXOGlXNnVWZ1Q1c1A3NkljalU3d2JCMzBuVVFEVU5FdFNxdE9FMCtycmNZREF6RzU2dy9FaWJrNURmSTVCRjl6UmMwMTUzYWJrcEc5N1g5MXVsVDVNSXNzRWQyVnI5UHZxRmNlMVhUVlRuNDZVVWNyRXJTQzRJUzRCbEtHV2c5TW5makc1VUxmMVJkb0JPRCtMZXdyZ1BHY3VqMnlwUysxaTFPcUZRMG9KSUpuREYyQUNmVHpsL0k1WDIyc2tubS8zSWc2U0lVYTJLWDVhblRpendZMDlkWW04d0pvTlBTUUZUVDBsVldGb0xrcVIyREIiLCJtYWMiOiIyOWYzZGVhNzk1MWNmOWUzZmQyYjVkZGZlYzg0MTFlYjI4ZmQzZTY4N2UzODU2MDMwNzJmYjZhZWMxMzIzOGVkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjB3Zm9rY2UxKzBwRy9YWWVzV0ZMY2c9PSIsInZhbHVlIjoiZTV2aHRZbDhvMUNleVZwckpUNjZpcFloMUV0KzdzYkN5ZUxSck50RlhFdlM2elpncnU0a2toazVlZGhJb3ZIek0zaGdtclBTVGN1WTFBRVh2aEFTQ2NlVWhZUyt4aG9KTHlGVHdkTXY5bXhoTFZHVjVlckgwUUxIdFRmS3lSeHdYYWVUbXBIVk5mKzRQdkJwdWZTcGhCTllkKzRlTGJ6NUdiVDNieWFzUTQrWFRoZVhkMEF2dGs5SUpwbXp6bThxUmlCTFdhUW5XeVBSYUpvVWh2R0hwaGpMVGRkdkZDL1lYczdxeUVRWHd4ekV6S2lZUjVLVXdBRmU1bW5uZ01yN0l6bmxlWDlSa2UvNHI0VGlIK3ZRS0FNbEVnNlI4cElnZnlveGh6NmlrV3dZRml6dmxBSlh5VjNBa0pHM0Q5YnV1R0dyU2xyUmx2T3IrZ0gyY1p1dHlCejJDOWJkL3hxd3RaZEdieTdHQTJLOGJVUjdqb29GSzdQZm9iSitpcWt0YlRBcWd3eHo2eGhFM2Iza1I1UW9hSGZ6WkVuUDdVSVBFTXhjSEpzR3dyVkhBcHcvNDV1QlBwSjY5RWE5a0tnbmkwc2FNRUhYVUdaUEFaNW5KVTBDQXRYU085aFUyOG9aMDZ5M3M1Nno4eHhITTNyQmo1QzAvZDBaak1MaHd5cjciLCJtYWMiOiJkMGJjNTBiY2MwZWJkNjMwMTYzYWZkMGZjNTczOGFkODNmM2VhOWE0YjgxNWYyMThmMGJiZmE1MmNiOWI0YTg3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdyZ1hXWFJQbjU5R3dZUURaenZEOXc9PSIsInZhbHVlIjoicEdTRzVEMmJwczIyTjF0VFdSZm5nVUZtOHFOTk5Wam5ZRWhFUFJtSkNDTlFIMzBSc0hPcGNpbkJKcXY1R1lOTmZLUWhXZm1JVnNibTVzZW1raDF0RWFTUWN2V3ljTm5uS2RpQ0dBdHQxZkx5bkNWTHpRb0hQYnFpVEV0NTI1RnhZKzJTZm9aVS9qTnlzc29VSWIxN0hSMEI4ZU1wT2wxNy9iU2ZwVTlpRHhSUGJnSmJhVjB0VFNFcVNtR2Jvb2xBbVVkaUxtbkdSQk1VL0JCSTQwOWdRQWw0cjdhU0V6M29WK2tSWEtWRmhldnJkTkNmSWxpdU5PTm1hcnpzQlVtOXFFaGtycUNxZExzYm1VYjFXOGlXNnVWZ1Q1c1A3NkljalU3d2JCMzBuVVFEVU5FdFNxdE9FMCtycmNZREF6RzU2dy9FaWJrNURmSTVCRjl6UmMwMTUzYWJrcEc5N1g5MXVsVDVNSXNzRWQyVnI5UHZxRmNlMVhUVlRuNDZVVWNyRXJTQzRJUzRCbEtHV2c5TW5makc1VUxmMVJkb0JPRCtMZXdyZ1BHY3VqMnlwUysxaTFPcUZRMG9KSUpuREYyQUNmVHpsL0k1WDIyc2tubS8zSWc2U0lVYTJLWDVhblRpendZMDlkWW04d0pvTlBTUUZUVDBsVldGb0xrcVIyREIiLCJtYWMiOiIyOWYzZGVhNzk1MWNmOWUzZmQyYjVkZGZlYzg0MTFlYjI4ZmQzZTY4N2UzODU2MDMwNzJmYjZhZWMxMzIzOGVkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjB3Zm9rY2UxKzBwRy9YWWVzV0ZMY2c9PSIsInZhbHVlIjoiZTV2aHRZbDhvMUNleVZwckpUNjZpcFloMUV0KzdzYkN5ZUxSck50RlhFdlM2elpncnU0a2toazVlZGhJb3ZIek0zaGdtclBTVGN1WTFBRVh2aEFTQ2NlVWhZUyt4aG9KTHlGVHdkTXY5bXhoTFZHVjVlckgwUUxIdFRmS3lSeHdYYWVUbXBIVk5mKzRQdkJwdWZTcGhCTllkKzRlTGJ6NUdiVDNieWFzUTQrWFRoZVhkMEF2dGs5SUpwbXp6bThxUmlCTFdhUW5XeVBSYUpvVWh2R0hwaGpMVGRkdkZDL1lYczdxeUVRWHd4ekV6S2lZUjVLVXdBRmU1bW5uZ01yN0l6bmxlWDlSa2UvNHI0VGlIK3ZRS0FNbEVnNlI4cElnZnlveGh6NmlrV3dZRml6dmxBSlh5VjNBa0pHM0Q5YnV1R0dyU2xyUmx2T3IrZ0gyY1p1dHlCejJDOWJkL3hxd3RaZEdieTdHQTJLOGJVUjdqb29GSzdQZm9iSitpcWt0YlRBcWd3eHo2eGhFM2Iza1I1UW9hSGZ6WkVuUDdVSVBFTXhjSEpzR3dyVkhBcHcvNDV1QlBwSjY5RWE5a0tnbmkwc2FNRUhYVUdaUEFaNW5KVTBDQXRYU085aFUyOG9aMDZ5M3M1Nno4eHhITTNyQmo1QzAvZDBaak1MaHd5cjciLCJtYWMiOiJkMGJjNTBiY2MwZWJkNjMwMTYzYWZkMGZjNTczOGFkODNmM2VhOWE0YjgxNWYyMThmMGJiZmE1MmNiOWI0YTg3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-276532207\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1997811113 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997811113\", {\"maxDepth\":0})</script>\n"}}