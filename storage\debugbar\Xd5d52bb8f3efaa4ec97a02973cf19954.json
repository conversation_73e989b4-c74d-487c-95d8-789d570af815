{"__meta": {"id": "Xd5d52bb8f3efaa4ec97a02973cf19954", "datetime": "2025-06-28 11:22:05", "utime": **********.439725, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.002345, "end": **********.439736, "duration": 0.4373908042907715, "duration_str": "437ms", "measures": [{"label": "Booting", "start": **********.002345, "relative_start": 0, "end": **********.361954, "relative_end": **********.361954, "duration": 0.35960888862609863, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.361977, "relative_start": 0.3596320152282715, "end": **********.439738, "relative_end": 2.1457672119140625e-06, "duration": 0.07776093482971191, "duration_str": "77.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45977680, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.408865, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.414457, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.431419, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.433833, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.006940000000000001, "accumulated_duration_str": "6.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.390993, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.072}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.395027, "duration": 0.00283, "duration_str": "2.83ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 25.072, "width_percent": 40.778}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4001849, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 65.85, "width_percent": 3.602}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.40939, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 69.452, "width_percent": 5.187}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4151301, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 74.64, "width_percent": 4.179}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4090}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.423512, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4090", "source": "app/Models/Utility.php:4090", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4090", "ajax": false, "filename": "Utility.php", "line": "4090"}, "connection": "kdmkjkqknb", "start_percent": 78.818, "width_percent": 6.196}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4091}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.426872, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4091", "source": "app/Models/Utility.php:4091", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4091", "ajax": false, "filename": "Utility.php", "line": "4091"}, "connection": "kdmkjkqknb", "start_percent": 85.014, "width_percent": 6.628}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.42861, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 91.643, "width_percent": 4.035}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq24\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.432303, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 95.677, "width_percent": 4.323}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "oOcJ3vwijTOlUa5TvSag9RRj6ZaDejIzujCCr2fL", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-2098815901 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2098815901\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2047767822 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2047767822\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-406069380 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-406069380\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1435833300 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IkxqbEdBejNPSDhLSkprZUh4S3FqRmc9PSIsInZhbHVlIjoiVE1wSDJ6ODRpRzA0ZHA4MndDZHJtai9CQ0dhRXduZ0FiU2lleXZxaHVWMldZVTk1emhVNTdDWHpublZnOGhjeUErdHNGbHMvdHhnRm52ZXdyN2xDK3Q0UkFCdFVJK3dSdnlOVkhHNGRiaEppamI4N002dFhpc2VZakhCTmRDMVA1RTI1OWh0Wk9pMVBBNHo3SlZ2MHExbHZ4TEZiSWdVSXQ3TXo3S1RKWFRMN3J0Ujc0UEk0SGpZZmVxc2FER3k1NGtvcWpJNkNHK3A2S3UvaGRnWEpZT1M2VVJFcnRKb25EOGJubWYzdC9lbit3WEhEZm4rTC82R2VpQnFIQ0xhNDd0OWNSQW5TWnVGYmF1ak5jTndwZFlxZlZHUWludzBITjBJdS8zMDZodHBWeWkzempkc1Zrd2EvZ20rZDBGRGJGZjd6N0NXb1JKMTNTSFJxOUVHSUQraVZBMEFpcmZuT1lUSHNKU1VZenpreFNMQ0x6dzh3dnRkaTYvN2dFU1JmL2dKdFVZaFdTZC9NQjZNamtYODZpbnAvZExTWjdvanRpU1lLZytGandoVlhRRHhoOHIra3R3S0tHb2RtbEd5dXEvbmRaM1lhWHQvTFlEWDgxMGhEK21qeTlTNFgwOW1vQ1QvNm9DR1dSR3BURDc0RkJmRzJMOWc0WDFadURBZ24iLCJtYWMiOiJmNWNlOTg2Mjk4OTAyZWRlYTg0MjgxOGM2ZDg3NDdjNjlkYWZlNzYxYTY0ODEwY2IxYzcxYTc5MDBiYmUxMTBkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImxqV2pNWFdPSFB0dUNSZ0N2QkFaMHc9PSIsInZhbHVlIjoibGhXN0pBa3VhRHIyc09sdHE3SVc2eHI0eHdmSFlTa1d4YUdxNjhmVWRBZHlRRU5YajB4TVVnQjlaZ0NvWktwTFVQSHpMSUx4NU54K0lSOGU3dDJzQWt6Z25QbmdQT1YzVVFLZHg0Z2JpcmxNd05CQkFLZ2ZjQ1FmbkVYejlMT05kWVZDVVZpWWpBTDFZRWtmaUwwclliZEdzOW10WlVSTnpVY1k1aGdEeUVRY3Z6M2l3c2FMN2I0Sk1mS3N3SUI1ejVqamVPdzhMM2kyV1d5SXptUTdGb25xb1RLYzc3K2JhSzNYSHlPb0J3ZDBWTkFGQVhPaTB2TmxNRkM2YjMzT3pnS2ZyNzVUaWxtdDhoTW1ENXR5MncxaGxUTkROais3ejBrSVZRT2t2VXpIWjdwY3hOcE9JRlhOS1JUSDkyTDdKRXd1V3BJa1FLRUlaYXFjcnlnR2hRNEJTUmJ5VEUxT1BrZC9hZ3ZxQ0IrUnp4MXViMlFRNXhkdUhlMXZyVmRZaFR5RXE5NEozZWZFM1l3a2pzOGdWZXFGRktLdEt2K240WGUwczdkQm51VFFMc0VFTlh5OVNuQVVFdzFXaTlIVU5nZEFVZ0tJNjZpZCtEcFU2RlNYRkttYU01UFV6Q1N0aUVJUVN6NEl0UFkveTM4emhSbkFwcUFpQzdCNWZsaXMiLCJtYWMiOiIzOTYwMWJkMmNjMThiZGQ3MjE0ZjUzOGZiNDlmMjA3ZTY2Y2YzOGQ1ODUzMWVjNTFlODI3M2U3NDFjNTExZDIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435833300\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1747589066 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oOcJ3vwijTOlUa5TvSag9RRj6ZaDejIzujCCr2fL</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7PlEzhMstE30pNWGjlm1nNfEb0RVB8YmC8pn20PB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1747589066\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-485493317 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:22:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVQMHlKWVBaWXgyeWtqSk1IRjdiNVE9PSIsInZhbHVlIjoia2hZbTh6S2I2Z1pOUEpQZ0ZKS1dJRms1L29JN3pTclhIcCtBaHR2ZXlqR0dWbklQTCtRZVFpeVlpWUZWTTJIUkNZMTZLQTBNay9LWXFLcTU5amJTV1NNYXYxR01BSDFZaVBiSDliVTJKWmxid044MEhFR2FFMlc2d04rSG5nK1BvYXFRaWN0YnZqSVNiODdnT0ZFUWxKNXg4dmJ1K09xcUhqclBwbjhkWmlOd2tvckE5U2FFNncxOFFHU3d3MTdnUDVOem1aTUxkWitIQ04yZWtsNDVKUGkxTTZ1M1JZbjhSamhWSkZ6ZUpnN3BDbVlOZjJ2ekFLYUxEaTRnM0F1RGU4U09Db0dPTDdJbWp3aXpHZWxGbFZJWGFhK1JUSmQyb2dBcDNYWFhiQi9CVUJLWEpTQndISTIxMUsyUWprWkhQMW1hb21HNHB4Zmw2T0tSV0lweWVjTXUwZXg1RTJqdEkzRHlsOGRvWHZETDVuV2krSXd5bksvTGVKbnJtK1pQT2pEMThxQzM3b3NjemJGU1puY1lJUFJwUVRtYWdFWXN2TVpvaUNEbFpPWEhMMVh3TU95NldwajBpUGtEamdjY1dOZXlOTUxvMFhremVpc2RXbkNCTWs4b3JMenhSNFBabHVPQVFrVElIdzlMUXY2a2FSZzQrdHY1dDR3Sk5NRloiLCJtYWMiOiJhM2E1NzFlYTA5ZTc0YmE2OGMzNTNlZDllNTExOWQ0OGM5NTUxM2E3ODY3YjQxN2Q5YmU1ZmQzNTBiMjhlYmMyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZITytadmdPcUtJRGQveElQRE1UdEE9PSIsInZhbHVlIjoiVjJSZThXU3BpNTZxU2pyL052dGxQcXVueExWTkpQT0JrUy9qRHp6V0RPRWhIMEtpdFcySzczK3pGOEZpRHh3N0F4MmtwWnp2S0xHL05mSWhrM3JNMFNqWmE1bDZWdmZ1VWxlRHo0bXdFVDl5L3NiVHR4VVo3NjdIOGJxcllncmo3WitNTmIxeWxhZ0w2WFlrYkk4VFFYNm5VTWlaQzNqbFNPdFBmcEdraWM4b2gvVDNXRDNKdnZ4TnU3Sk52RGttTTcvM09BUkZESS9aNEMwTGxZOHZZdlkrSjh0MlB2WGdxcmZveEJnb1Q2ZTh6cEZXS1RCbnlOSm1lUlNSYlZHWDdsTlNZK0E3Sm1MU1NJQVRydXpDLzczN2o5Skd0K1p4T0owSFhVTHdNU05hblBEc0hqNFdlTEFDajI4YktubmRyNHBkbndRNGVTRkFCeFNpTEpTeElRTXoxNVVvN0ljL3hjN2k2dncvcmpOaUorSkFTTkNkd1dUOHQwOThoMEUyTkd5c2M5Ym9sUm8vQjBxV2pXOUpHWUJrTktGYThNTGtpVVAzZk1WdHFkb0U5TUpEU1huemc0UFpiRE9sSTViSGp3SUs5RjlEdCtyWVl0UGFPT01pNE93L0tUUUVRL0F4cjJpdFFmY0V3WlJKVkR6eFduTnJidGdoK0hTRHlOL2MiLCJtYWMiOiIwOWY1NzhlMWYyMmMwMzBlY2NjOWYwZTJjYmFkZTI2YTU1NDU3MmY3ZmU4ZjA5NWIwMWZmZjI5ZTNmMWRlNzdmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVQMHlKWVBaWXgyeWtqSk1IRjdiNVE9PSIsInZhbHVlIjoia2hZbTh6S2I2Z1pOUEpQZ0ZKS1dJRms1L29JN3pTclhIcCtBaHR2ZXlqR0dWbklQTCtRZVFpeVlpWUZWTTJIUkNZMTZLQTBNay9LWXFLcTU5amJTV1NNYXYxR01BSDFZaVBiSDliVTJKWmxid044MEhFR2FFMlc2d04rSG5nK1BvYXFRaWN0YnZqSVNiODdnT0ZFUWxKNXg4dmJ1K09xcUhqclBwbjhkWmlOd2tvckE5U2FFNncxOFFHU3d3MTdnUDVOem1aTUxkWitIQ04yZWtsNDVKUGkxTTZ1M1JZbjhSamhWSkZ6ZUpnN3BDbVlOZjJ2ekFLYUxEaTRnM0F1RGU4U09Db0dPTDdJbWp3aXpHZWxGbFZJWGFhK1JUSmQyb2dBcDNYWFhiQi9CVUJLWEpTQndISTIxMUsyUWprWkhQMW1hb21HNHB4Zmw2T0tSV0lweWVjTXUwZXg1RTJqdEkzRHlsOGRvWHZETDVuV2krSXd5bksvTGVKbnJtK1pQT2pEMThxQzM3b3NjemJGU1puY1lJUFJwUVRtYWdFWXN2TVpvaUNEbFpPWEhMMVh3TU95NldwajBpUGtEamdjY1dOZXlOTUxvMFhremVpc2RXbkNCTWs4b3JMenhSNFBabHVPQVFrVElIdzlMUXY2a2FSZzQrdHY1dDR3Sk5NRloiLCJtYWMiOiJhM2E1NzFlYTA5ZTc0YmE2OGMzNTNlZDllNTExOWQ0OGM5NTUxM2E3ODY3YjQxN2Q5YmU1ZmQzNTBiMjhlYmMyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZITytadmdPcUtJRGQveElQRE1UdEE9PSIsInZhbHVlIjoiVjJSZThXU3BpNTZxU2pyL052dGxQcXVueExWTkpQT0JrUy9qRHp6V0RPRWhIMEtpdFcySzczK3pGOEZpRHh3N0F4MmtwWnp2S0xHL05mSWhrM3JNMFNqWmE1bDZWdmZ1VWxlRHo0bXdFVDl5L3NiVHR4VVo3NjdIOGJxcllncmo3WitNTmIxeWxhZ0w2WFlrYkk4VFFYNm5VTWlaQzNqbFNPdFBmcEdraWM4b2gvVDNXRDNKdnZ4TnU3Sk52RGttTTcvM09BUkZESS9aNEMwTGxZOHZZdlkrSjh0MlB2WGdxcmZveEJnb1Q2ZTh6cEZXS1RCbnlOSm1lUlNSYlZHWDdsTlNZK0E3Sm1MU1NJQVRydXpDLzczN2o5Skd0K1p4T0owSFhVTHdNU05hblBEc0hqNFdlTEFDajI4YktubmRyNHBkbndRNGVTRkFCeFNpTEpTeElRTXoxNVVvN0ljL3hjN2k2dncvcmpOaUorSkFTTkNkd1dUOHQwOThoMEUyTkd5c2M5Ym9sUm8vQjBxV2pXOUpHWUJrTktGYThNTGtpVVAzZk1WdHFkb0U5TUpEU1huemc0UFpiRE9sSTViSGp3SUs5RjlEdCtyWVl0UGFPT01pNE93L0tUUUVRL0F4cjJpdFFmY0V3WlJKVkR6eFduTnJidGdoK0hTRHlOL2MiLCJtYWMiOiIwOWY1NzhlMWYyMmMwMzBlY2NjOWYwZTJjYmFkZTI2YTU1NDU3MmY3ZmU4ZjA5NWIwMWZmZjI5ZTNmMWRlNzdmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485493317\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-280018158 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oOcJ3vwijTOlUa5TvSag9RRj6ZaDejIzujCCr2fL</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-280018158\", {\"maxDepth\":0})</script>\n"}}