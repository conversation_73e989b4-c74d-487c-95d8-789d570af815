{"__meta": {"id": "X1b5b89cca7cca6a35a26b9a21385b829", "datetime": "2025-06-28 16:03:23", "utime": **********.069111, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126602.585953, "end": **********.069125, "duration": 0.4831719398498535, "duration_str": "483ms", "measures": [{"label": "Booting", "start": 1751126602.585953, "relative_start": 0, "end": 1751126602.982924, "relative_end": 1751126602.982924, "duration": 0.3969709873199463, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751126602.982935, "relative_start": 0.39698195457458496, "end": **********.069126, "relative_end": 9.5367431640625e-07, "duration": 0.08619093894958496, "duration_str": "86.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45853280, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.030240000000000003, "accumulated_duration_str": "30.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.014936, "duration": 0.024079999999999997, "duration_str": "24.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.63}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.047609, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.63, "width_percent": 1.554}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*************%' or `sku` LIKE '%*************%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*************%", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.051122, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 81.184, "width_percent": 9.226}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.057054, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 90.41, "width_percent": 8.036}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1995}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.061939, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1995", "source": "app/Http/Controllers/PosController.php:1995", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1995", "ajax": false, "filename": "PosController.php", "line": "1995"}, "connection": "kdmkjkqknb", "start_percent": 98.446, "width_percent": 1.554}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1546774980 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1546774980\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1672013649 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1672013649\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-380326905 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-380326905\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-494078953 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126591625%7C16%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZhdkx3WmE2eFRMVWZOL1YzYlliZmc9PSIsInZhbHVlIjoiVUVFWktuWU5wb2k5RjN4WDZFZ0VyOWJLeWVxRHdyZURCWTNGYUFJd0c0WWk3RFBGR3FOd1oxVmhQUE1YbkQ2VnhLQVJkTnl1TXR1blp5MXBPY1ZBZWhyUGpEMklOTXprYml6RXI0eTdHOURsbHcwYitFUlljWjNFbEU2WEJJNlpYaTU2RHlRenk5ZU1wMFFVQkFqbnBTQzg5RmhoclZRT3pocCtJWXJaYjB4ck1aeThpV28ydjA1TWpRNUdHbEFHWElxQzFzdUNrRU1Id1N2dTJJUk9LWm5LY0Z6TzZLank4NlFyT2JHaVNpUHM1d3FOT0lrcEswa215SVpzckhhK2RvcTdtcDN1RGhEVWJaRG9oZkdkVXJWYnlnRUVVRlRJQ29obUVud21FRC9vc3B2LzF1M08zc2ZZYVhHeDdiby81UjhvNUNNRStsbFFxTFhDL3Bkd25xRktrVk5weU4wSmIrcmNIYjk1R0Y1QTRBZ2d4SWNETVQ4WmE2SVBzQ0lEcUxnTWNBMkhmUTNDbGVwd1FMRFczTTNWcDdpSHExanRhL1FaNUFhdDlPdzFjZnBYN253ZU9jeWhZQmFGVjBnOXdaNFlZV1VQT1M5YXJuQXA4ck1rUmwvbHI5UDhmcGRhS0JNVy9LaXJvam12Q2p0ZXJBTjV6cG4reEZwMk8yeGQiLCJtYWMiOiJkODI4Yjk4ZjMyNzE5NzM4MDY1ZGE3N2E5NzQxODAxNzBiMWRmYzBjYWQ3N2ZiZTRjMGNjODA0ZGJhMjhkYzBjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImttclRCU2JNODNzLzBCTHM2ZmlvN0E9PSIsInZhbHVlIjoiTndSdDYxUGpHMFJjaitmbHdnS3paVzJ3Y3l1eUkzQm1PUFNCblZwQ04yWVM1TnVYUWF1YUsxa0tYY3ZzMmNndUx5aHZDVHlETEhLdFhTU0pKV1F4TFVCb0JMV3orNXpOcjVQRUh6TThhMWZYL1BMZU1Qc1hGSjhQWlU5YnZLcDBCa1lES3lPaGFjNWtTcFJnQTVEOXM5T25KVGlycUtnSGhvcTNoU1F0bVZQQzljSzRWWDVVTGhVYXBEaWdwZTNuWURBUW1zSEUrbXpBdVROeVlPSW12Y0tZVnlEdVpzejYxVGY1aHVTaml6blhYcTVWNDQxZ0lyeUVqd2RmdVU4MnRweFVVaUNFdDNESjk3VXFDMkNEZE1iRlBZTy9DRm5zOE1FOG1ycmovdHRzUGlCSVl1WkJxYlc0UW9VNEVVem9NWS9NSlBSaWk0Yit3RVpZU1VzM05jK3dQaFNsTm13V3JXY1k3M1VoaE5VSUYvYjAvMlRrblZ5bUxmdVZMNnBManZzVGNjL0dmUXVydzFTYVVmbERCdFB2b1p0MTcwOFpHY3NnME5XTXRnWHhEL2M1NVA0V0E3TFR3WDdSMGluempXUUNuTHRaZFNPT2t4aG5CajZkWldKREVlRE1DcEFnU2wzN3VDQzlMV2lMWTJ3SnlNaGQ1a2Jsbml0TUpxbUgiLCJtYWMiOiI4YTdhMjMxYTFlNmY0MzhkOTU0NzdiMjBiNGQ5ZTZkMjI3YTcxMTRiNWU1NDMxZDhkNjA1M2EzOWIxZDY4YjVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494078953\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1984185325 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1984185325\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2107779889 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxvbFg1RFVvM0VKd0ZpTTRHRUEwNnc9PSIsInZhbHVlIjoiVW1UVng5em9MRU93M2ZTdGc3RzJKOVlRSmJ2SDJEWXVMS3FKVS8zdTlhaU13d0JKWllIUEZZMnJQWGlhWVkxZExJOGh4Tk1saVJBdVJuYXJZZThyMzIySVN2Unp5QldheitpOSs0Zk12OGFUQlZVT0pFSVduWUU3ME1iSGw2WFRmdmhNOWV6Y3psem45aFM5dnk4U0oyOXBvT3Z6UG5RcFRrUldCQ1R4ZXIvSzFkWkJ6dlJjUWZDWUFRRVYxUFNDTy9tMWZGUW4wbXFRb1NZdlJVRUFqNlVZcjBxYURYNGxEV05nOFFsR0NCc0d2elFyTExueERKNS9lYWNtV1NUYmRLOUhxUkVuMDJ0UHFYbW1EMXVXNkcwL3R5UUJOeDJOd0dYcEgzTEJCcVhNRzExWU5ZY0UvUVg1QUFmdU9nbGd5UlYwaGVkWGRCRUgvZTlIVVY2RWk3Q1o1TjVLYWZ0OGoydTJwdllPSUNqSjFwaURtY3lnN0lZN2I0YWhyQXRvOEpUbFlqZGN1c1FIRDhxTEV2SlpFRFZYa0tmd05taXlVdHRGdDA2dWRCY2pZRFFzc0pWVFNHVFFEQmZGVWFDbW9CTkVGRDFLZkt6aDJHTFJRUFRHQ2ErNnVxbUhpbUdFVk5ZWVJwNnBqYWVTT2VmOHV0bkRUbDk4RzR4T0hBODUiLCJtYWMiOiJiNjZmZTAxOTEyM2NiOGViNGI4NmM5OTQ1MTg2ZjIxNTIyOGUwNmNkZjA3YmMxMDFkMjcxNjU2ZjIwMjIxYTIwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlNCeG03S0FqRVVWamI0aGVicWMyRWc9PSIsInZhbHVlIjoiOEtYSlJJakNXdndPU0Mzc1J1QUhBeWsxcmJ0Nk82MUxsZ3RwY2I1UkFiNUh6czBJY1hJOHMrajZrWndTUXROUEczc1pCaTFJck5ZUU1TVlZZYlVIVW1uOFprQmVVSWRjK3dTUzlNU0xxanFvN2d4cWZNbHBHVTc3OEUwSVBieTZrNGZTYTVHOEpDUzVCZmhnWURkZGpHVk51ejdZYk1remsyaDFUVjZTYVd0Qi9UZ2hKVHJtSlNGZlhhWklLNG03Q3ovY2o4YVpRV3ZNQ0trdzVWU1FETmhENktjeXl0NThWbE13Q05LUFFZbi9mUnNpOTFiTEJlZnBnanpEVy9QNmRSZFhlelpBMExjWDFrYWQ4am1SamY1RkdNdXJ5cHVzNkpXN2c1RmVvNWJnMUo0bldJbUlVb25Yb3JtQkhOWFBpemNuR2ZZNisrUW9hZGVKOVo2cytaQ2ZLdVlnb0FzMlN4YmVXKzNubjY2V09uc0IwczFRSUpjY29MOGVSZzB2NGM2YkdXZWFpTmNtbi9zaVBubWc0VTFIRWpJTFplMXpDSEQ2MnJpOWxxK1BPK1JxUmZocFlEQWh5MzVOOGpVVXFmeStzQlZwZVc3U20zOFRZOXdYdnUwMElpalkvamRvZ2dsMkhFUnNCVEZiNmkxanhIaHlVT2lLR25zUG5CNjIiLCJtYWMiOiJjYmYzMTczYjg2ODFhNjZmNDZiZjI1ZWQwMWY5MjE0ZjYzMDlkOTk1YTM0NGNiNGZjZmMxMWNiNWZjMDY3MmUwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxvbFg1RFVvM0VKd0ZpTTRHRUEwNnc9PSIsInZhbHVlIjoiVW1UVng5em9MRU93M2ZTdGc3RzJKOVlRSmJ2SDJEWXVMS3FKVS8zdTlhaU13d0JKWllIUEZZMnJQWGlhWVkxZExJOGh4Tk1saVJBdVJuYXJZZThyMzIySVN2Unp5QldheitpOSs0Zk12OGFUQlZVT0pFSVduWUU3ME1iSGw2WFRmdmhNOWV6Y3psem45aFM5dnk4U0oyOXBvT3Z6UG5RcFRrUldCQ1R4ZXIvSzFkWkJ6dlJjUWZDWUFRRVYxUFNDTy9tMWZGUW4wbXFRb1NZdlJVRUFqNlVZcjBxYURYNGxEV05nOFFsR0NCc0d2elFyTExueERKNS9lYWNtV1NUYmRLOUhxUkVuMDJ0UHFYbW1EMXVXNkcwL3R5UUJOeDJOd0dYcEgzTEJCcVhNRzExWU5ZY0UvUVg1QUFmdU9nbGd5UlYwaGVkWGRCRUgvZTlIVVY2RWk3Q1o1TjVLYWZ0OGoydTJwdllPSUNqSjFwaURtY3lnN0lZN2I0YWhyQXRvOEpUbFlqZGN1c1FIRDhxTEV2SlpFRFZYa0tmd05taXlVdHRGdDA2dWRCY2pZRFFzc0pWVFNHVFFEQmZGVWFDbW9CTkVGRDFLZkt6aDJHTFJRUFRHQ2ErNnVxbUhpbUdFVk5ZWVJwNnBqYWVTT2VmOHV0bkRUbDk4RzR4T0hBODUiLCJtYWMiOiJiNjZmZTAxOTEyM2NiOGViNGI4NmM5OTQ1MTg2ZjIxNTIyOGUwNmNkZjA3YmMxMDFkMjcxNjU2ZjIwMjIxYTIwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlNCeG03S0FqRVVWamI0aGVicWMyRWc9PSIsInZhbHVlIjoiOEtYSlJJakNXdndPU0Mzc1J1QUhBeWsxcmJ0Nk82MUxsZ3RwY2I1UkFiNUh6czBJY1hJOHMrajZrWndTUXROUEczc1pCaTFJck5ZUU1TVlZZYlVIVW1uOFprQmVVSWRjK3dTUzlNU0xxanFvN2d4cWZNbHBHVTc3OEUwSVBieTZrNGZTYTVHOEpDUzVCZmhnWURkZGpHVk51ejdZYk1remsyaDFUVjZTYVd0Qi9UZ2hKVHJtSlNGZlhhWklLNG03Q3ovY2o4YVpRV3ZNQ0trdzVWU1FETmhENktjeXl0NThWbE13Q05LUFFZbi9mUnNpOTFiTEJlZnBnanpEVy9QNmRSZFhlelpBMExjWDFrYWQ4am1SamY1RkdNdXJ5cHVzNkpXN2c1RmVvNWJnMUo0bldJbUlVb25Yb3JtQkhOWFBpemNuR2ZZNisrUW9hZGVKOVo2cytaQ2ZLdVlnb0FzMlN4YmVXKzNubjY2V09uc0IwczFRSUpjY29MOGVSZzB2NGM2YkdXZWFpTmNtbi9zaVBubWc0VTFIRWpJTFplMXpDSEQ2MnJpOWxxK1BPK1JxUmZocFlEQWh5MzVOOGpVVXFmeStzQlZwZVc3U20zOFRZOXdYdnUwMElpalkvamRvZ2dsMkhFUnNCVEZiNmkxanhIaHlVT2lLR25zUG5CNjIiLCJtYWMiOiJjYmYzMTczYjg2ODFhNjZmNDZiZjI1ZWQwMWY5MjE0ZjYzMDlkOTk1YTM0NGNiNGZjZmMxMWNiNWZjMDY3MmUwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2107779889\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-777012288 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777012288\", {\"maxDepth\":0})</script>\n"}}