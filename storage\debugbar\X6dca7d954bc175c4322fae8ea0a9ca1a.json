{"__meta": {"id": "X6dca7d954bc175c4322fae8ea0a9ca1a", "datetime": "2025-06-28 15:46:36", "utime": **********.282911, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125595.797477, "end": **********.282926, "duration": 0.48544907569885254, "duration_str": "485ms", "measures": [{"label": "Booting", "start": 1751125595.797477, "relative_start": 0, "end": **********.203161, "relative_end": **********.203161, "duration": 0.4056839942932129, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.203171, "relative_start": 0.40569400787353516, "end": **********.282928, "relative_end": 1.9073486328125e-06, "duration": 0.0797569751739502, "duration_str": "79.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46266576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0196, "accumulated_duration_str": "19.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.238635, "duration": 0.01397, "duration_str": "13.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.276}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.261174, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.276, "width_percent": 1.939}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.264644, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 73.214, "width_percent": 2.959}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2668629, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 76.173, "width_percent": 16.378}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.272209, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 92.551, "width_percent": 2.194}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.274172, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 94.745, "width_percent": 5.255}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-794175994 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-794175994\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-878224854 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878224854\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1967127508 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1967127508\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-121466824 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125587718%7C19%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImsyWU5lUHhHSjdHMEE0SWFYcEd6QlE9PSIsInZhbHVlIjoiM2Z6cGk0MmJxSE5wL21HckwyWEhJZE9Hdi9PSlBHUWpQY0ZwdWVwQ3pOVzRnTVJ3WlhUYXZpanZGdTZ3cVhYVmF6ZUEzUk1oYWtiMWFsaVF4VFlreEF6UWQzdXdHQzdQamx4c0FtNDRpc2tFTkxTTUw1bDZ0dk9qRTNEaEk4NTRkOGRPa3BydjZ4TGh2bm1pZHQ4NnlpUXo5WTNKNndIMGdKTFdtS2dUU3NuWlRzWkw3T25qQ2NoQW1oOHBFc0dEaGJ2SHVlWHN1NW5vbnNWR1lxamFnak80bnZYbTczaTNjZXpSaExJVy9xYmxMMEJONFd5YU9kd1psdXAralhNVHFnK3dBMXVsMTgxbXE1MzdYRGpWd0dQdS8yWVNBaFowZHlkRmh5cnMrbW8yVVptcE9yMWFzbGo5Mm1pV091RVFkVmFDUWxRMElQWHVWM3VPeWExV0FJekF6am5ObFZvdzJ5S0hGcTNGa0FQUWYzNEFzb0dwOU8xNG9TVUhoaXdPNnZBWTFDYjMwS3h3ajZ0LzE2L1A4UDUyc2hldUdLSnVORzdTSEQ5R21jdldKR1RGTGNaTWJyR0NxSDJUaS9TRHl4N2lCOHFmdjdUcnlETHhDR2hLd0hFK0xTYTBzZm9mRit5MXNOa2loUktZdGVQTnlWWURnc0tQSGdvY0F4bXQiLCJtYWMiOiJmOWEyMzgxNTVmZmNmNzI0OGFkZTkwOGRhNTY2OTNkMDNkYTk5NTM2ZTBhM2NhZDZjYTYyMTY2N2FjM2I2MDI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InA3SlF3SENNb0RHK0oxLzRNbkhUTXc9PSIsInZhbHVlIjoiZTFzNitod2VBdmYzTVp4TGJON2lYaEpDNHVub0dudWc2M3QxcmRJbFBGaENPTUlCcW9KRHlJNkJtQ0IwTkZKZ1lYWXRjSnFpUnI4Q3dTaURZcThUNXJqMUR6QTM1blI4ZDV6ZjFwRUl5amQzcjdoU1d5UWhMOGFJa1IwOTBmMllqcGdWOGxBZ1E0azR5d29WUUxVU2RuZjVsSllsdGwwUGY2S0E5dmRkbXBIR2VxdXNVa3g5Zkg0eTJQbHhhMXVmWU4reGFZUEtKbGx2cm1OVHVnN1FlQ281R1UyS2Y5VG1NMGttK0tWSnpsbTNEeFpoSVhtZXNWRHQrNVJORS93ekZVUVQrSU43UEtNQVF1Y0dqb2wwUkJUb0VnSVYwSnJxS0kxd1N2TGFxK1V1WS8reStYbWxBanNPZ29XVUFJbVEzdE9VOUlBNTJYYndLQnZMeTA2Y2xwUHlVVUFLaVg0bnhzRml0VUs4OWxaNS9mcVY1Y3lYZlFHOXl5R3hHTjlGM0VJT0p5UzZhREtyMC9Ga1hQdVVqTkxFVzNnZ2I0M21MbkNRRzFaY25QaDVYMEd3UnJvTkdmY2JxQ3ZrQVNFdnozYndUMVpCWkVuT2RGWGlseGZxYnAvSy83R3BJcDR5SkZ0d09xdHFKVzdoZFVPeWVSTllFYjRDdWtNaDhlTk8iLCJtYWMiOiJhMDdmYmEzYjVhYWZkYzljZjE1ZDEzOWE4YzBkMjU5ZWI3YWM1ZjgyZDdlNjA4YzgzMjA0ZDI5ODU2ZTNhODg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121466824\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2030777472 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2030777472\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1678500809 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:46:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjY5dUZOcEViaHdiYlhmTC80dVNrdnc9PSIsInZhbHVlIjoiR1JBUjBZUEh2Szgwa0VYQVk3S2xMVXNpTUtsZzZIeW80dXZOV2MyZ2ZkVGJncWFZdjB4aUhZcDVXTllPYVIxbmU3Yk5rVFZ5ZTM2M0NoblUrakkxNnljeGhVQmZMNUNNOWFVdFUwQ3BRM1RFNkgyakhCMlNxSXZsZ3FrTGFMN1JVOWhnbFlwTTBhNG1GdlgxV0cxWEpGM3pEaWxySjVtNU9Tc1NFbDc1d3ZLSkk4anNwQ0xXZlcxV0IyTjMxTlNuVzQ1NzJvNWNkK2wyUCt6eE5HeVR4SDdYdm9CTHd6b0lYQnN4eTlodTJYelJZZmxDTWNwbEk3VGl1R3lZVVVCalFYTERMQWNNbDNNVVYrd1RMcUhkNFAxQUZMeGVCcVJGZDB0bSttemxicVJCSTFEcVY1eEMyZUZkK3h1OEdlWHNQSDRGcXlkV2FqWG9La3p6WTdtaHJ4NGczMzhGalQxWGpnVWhtTVZ4RjN3VTRGcjRCS1JPaThBaVJaNS96TG90V3VsL3pzOUpQN3haaTZZd2FSaHZzbGpJNzNYcUt5TTVQQUlRb1RGWjNpd2V4OXEvaHNaSzN4cUJIUDFyRmc3RE9PWWNZcU82OGZycXhOaldhc2tkNjdhVzRzZVd3Z2o5WFlEN1VpTWFrUGxYaUMxdThRODN6M2s4OXI2d0RJckYiLCJtYWMiOiIzNzRlY2MwYTM1YzdlZjMzNDFlMzUxMDhjMjI1NWVhNWYxMDRkNzdiMmU1MmIzMGNiNDM3NWJiNGIwZTU1YmY5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9WZzA2VGNTRjY1aXdtcGhiVExOQ2c9PSIsInZhbHVlIjoibHFEaUJmZG1wZXhnR2FuS3lWaUtOTDR3b1RkNzRwYUQvcC9LZUZQcXAyRnlnajNYWUJkWUhTTzZRdExGVGhGTDlwRlkreHFvQ0V6d2V6RnAvZTVnOHR3d0ZlUTFVRUNEZHFXVU5rNVAxY2NRSkFTdmpsOGMyT3BMOEl4aDRlL2JsZERlQ3czKzNHR3UySGErNHZCeXRsbkJCNVVmZEk1TWltOVBRWjhIMUg4T3JlMEc0eXFSZmQ4YUQyM2VIdUJMU2hCR3dRNlYyK0g3WWlRNGp2aWovNFhjeG1WdURwNUhjczhoK0JTTjREM0VSamE1bVlRcU5nSEgzMzVDZHJCeU5rR1dlb1R5V25sWFcyZEZsTFhLSVQyNitobEh0cmJpRm0rOXdTT3VlK1hLM2xwTUtPYUNMRFB4TWMwWi9ueEhVK1hvUS9nZUpCWVVIY3hSOEcybWc4Z3RmZVZTL2YvbGZRWURwcjJDYkprcUFvLzBjdVBLZ2t0OXg4SUJJL1k2OUFHWTl6b0I4cElMLzE5NDFSWlJPazgwdnBYZ3ZTc20xUmxFSXhYZVZGcHkwbHE0Q3VDN05zbmkvRmphSVdNTDF3bVZqWnE0SVVYQVhhMk9yVHBIZ1pzRWFrRTZaN2ViaEZoRmFRRFR1NElyNnNZWmIxNW5BQUV5eVpJYnV2RkYiLCJtYWMiOiIzMzVkNGFlZGZiMDEwYTBlZTk4MjU4ODI1ZTkzMjJjMTYxMDBiZTM2NjhhMzY5ZTYxNjkxNDdjNzkyNTdkMmY3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjY5dUZOcEViaHdiYlhmTC80dVNrdnc9PSIsInZhbHVlIjoiR1JBUjBZUEh2Szgwa0VYQVk3S2xMVXNpTUtsZzZIeW80dXZOV2MyZ2ZkVGJncWFZdjB4aUhZcDVXTllPYVIxbmU3Yk5rVFZ5ZTM2M0NoblUrakkxNnljeGhVQmZMNUNNOWFVdFUwQ3BRM1RFNkgyakhCMlNxSXZsZ3FrTGFMN1JVOWhnbFlwTTBhNG1GdlgxV0cxWEpGM3pEaWxySjVtNU9Tc1NFbDc1d3ZLSkk4anNwQ0xXZlcxV0IyTjMxTlNuVzQ1NzJvNWNkK2wyUCt6eE5HeVR4SDdYdm9CTHd6b0lYQnN4eTlodTJYelJZZmxDTWNwbEk3VGl1R3lZVVVCalFYTERMQWNNbDNNVVYrd1RMcUhkNFAxQUZMeGVCcVJGZDB0bSttemxicVJCSTFEcVY1eEMyZUZkK3h1OEdlWHNQSDRGcXlkV2FqWG9La3p6WTdtaHJ4NGczMzhGalQxWGpnVWhtTVZ4RjN3VTRGcjRCS1JPaThBaVJaNS96TG90V3VsL3pzOUpQN3haaTZZd2FSaHZzbGpJNzNYcUt5TTVQQUlRb1RGWjNpd2V4OXEvaHNaSzN4cUJIUDFyRmc3RE9PWWNZcU82OGZycXhOaldhc2tkNjdhVzRzZVd3Z2o5WFlEN1VpTWFrUGxYaUMxdThRODN6M2s4OXI2d0RJckYiLCJtYWMiOiIzNzRlY2MwYTM1YzdlZjMzNDFlMzUxMDhjMjI1NWVhNWYxMDRkNzdiMmU1MmIzMGNiNDM3NWJiNGIwZTU1YmY5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9WZzA2VGNTRjY1aXdtcGhiVExOQ2c9PSIsInZhbHVlIjoibHFEaUJmZG1wZXhnR2FuS3lWaUtOTDR3b1RkNzRwYUQvcC9LZUZQcXAyRnlnajNYWUJkWUhTTzZRdExGVGhGTDlwRlkreHFvQ0V6d2V6RnAvZTVnOHR3d0ZlUTFVRUNEZHFXVU5rNVAxY2NRSkFTdmpsOGMyT3BMOEl4aDRlL2JsZERlQ3czKzNHR3UySGErNHZCeXRsbkJCNVVmZEk1TWltOVBRWjhIMUg4T3JlMEc0eXFSZmQ4YUQyM2VIdUJMU2hCR3dRNlYyK0g3WWlRNGp2aWovNFhjeG1WdURwNUhjczhoK0JTTjREM0VSamE1bVlRcU5nSEgzMzVDZHJCeU5rR1dlb1R5V25sWFcyZEZsTFhLSVQyNitobEh0cmJpRm0rOXdTT3VlK1hLM2xwTUtPYUNMRFB4TWMwWi9ueEhVK1hvUS9nZUpCWVVIY3hSOEcybWc4Z3RmZVZTL2YvbGZRWURwcjJDYkprcUFvLzBjdVBLZ2t0OXg4SUJJL1k2OUFHWTl6b0I4cElMLzE5NDFSWlJPazgwdnBYZ3ZTc20xUmxFSXhYZVZGcHkwbHE0Q3VDN05zbmkvRmphSVdNTDF3bVZqWnE0SVVYQVhhMk9yVHBIZ1pzRWFrRTZaN2ViaEZoRmFRRFR1NElyNnNZWmIxNW5BQUV5eVpJYnV2RkYiLCJtYWMiOiIzMzVkNGFlZGZiMDEwYTBlZTk4MjU4ODI1ZTkzMjJjMTYxMDBiZTM2NjhhMzY5ZTYxNjkxNDdjNzkyNTdkMmY3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1678500809\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-101072026 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-101072026\", {\"maxDepth\":0})</script>\n"}}