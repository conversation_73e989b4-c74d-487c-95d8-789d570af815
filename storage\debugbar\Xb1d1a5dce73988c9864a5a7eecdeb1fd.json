{"__meta": {"id": "Xb1d1a5dce73988c9864a5a7eecdeb1fd", "datetime": "2025-06-28 14:59:10", "utime": **********.641654, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.200305, "end": **********.641669, "duration": 0.441364049911499, "duration_str": "441ms", "measures": [{"label": "Booting", "start": **********.200305, "relative_start": 0, "end": **********.573594, "relative_end": **********.573594, "duration": 0.3732891082763672, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.573601, "relative_start": 0.37329602241516113, "end": **********.641671, "relative_end": 1.9073486328125e-06, "duration": 0.0680699348449707, "duration_str": "68.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45087448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0035, "accumulated_duration_str": "3.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.602396, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 38.857}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.61503, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 38.857, "width_percent": 15.429}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6276999, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 54.286, "width_percent": 31.429}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6343331, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.714, "width_percent": 14.286}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1324661193 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1324661193\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-584897202 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-584897202\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1444866627 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444866627\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1354000301 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122745018%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhrZFJDenRDQlpVWWdFWUk5VS9VNGc9PSIsInZhbHVlIjoiWHBPSmxhVjdDdHdkZkd3cW94V0xaWm53T0Y1TFQxOS9BUjY4NnZRNDNOL0h0anFWWE11MGR3MWl2cjFORngrUzRJOGI3REJWeXV4c1J4K3I4UTJpalhPT2pzTmpBRVpvKzdoaVhOU1hwNkM1OVNabWJIbTVYS1NvY2l0aVVMb0crRGhHUm4xcTA2a1BMUE1LQUpIZ1NiemUrYnUvaEtmb29lb0UyZTVOQnRrc1VXU3Y4N0g3RnoxYm1jcVZ1TnRvOW5YdElDeWV3VW1GZTQ4TTVwZjNSNTZyenJoWW42bWdpeDdMNHpKUUh1ZHJvSWxMTVI5YzFxWjNPa01Qd2huZjcvRVd3cjVoZklrbThZb1NhWVNWTlo3dmhCUWp6R1k0WC9mK1doZVJZSE10MVlmRmtoMVJxU2RlL0N4RExRWWpkOUZnYXFMemJWWGFOWno5KzVjZjZyVHN3YVY4ajljOHIvWEZwYmVqQzRPaEZmWTd4K1htV2x0MlZpMy9RVlRYNnRzWllzMVVDUEJUb3R1YzF6YTdqTkhOMlpSZGxwdWxrVVdMYmQzZ3hsbEx3eDNEcGdWWDlieGljTnczUGZXOTB6MWliZ2RjQkgyajBUNzZsMTkvd3JxTmV0YkdhcEgwMFJIOW83dnc5RDRZYk1QUWVCNC9DaS9sRENKNWwvL3ciLCJtYWMiOiJhNzAwOTE2MGZhNDMzN2Q5MWY5MGU1NGViNDIyZjJlZTIyYmY0ZjZkNjA0ODM1MjgyZjZiN2MxZDZjZTNjNmY4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJmQklyOHNnVXh2VkcrNlpiNDlXYUE9PSIsInZhbHVlIjoibDd5bnNnc3kzdkc1V3lveFg2aWlNaGpad0pvRm1EN0ZRNThLTjhDcFd5L2JISk5kdzFSckNhRzN1SGRWQmkxeHM4QjVuMk1NY3BLTVZQM1BRWHM4bE51dzFUZVJmUk90K2lKWWtvVHEzZUpPVm9iUFV1TysvcnBMNFc3ZzlTekFjKytSTG9KWDNWS0xrTlM1a3JSNmI4TVV6RzRIVjlGWkdQMDcwVXFJZlBkcmdvY29XQktjN3FzaXAzcDBIZFFyUEx3M2M3V3ZFdm44RFZKaDJsUE5ZbEFoOTVwcllSTHhpRkZJTWNhVjIvdEtzRTQyd3BIMjhUR0pCckM0QVFsajVHa3pBRHJpb3dBMUlxNmpxdURhNVU4OUFZSHVhUGw1UzQrVDIxYUFJRjJYMkxSbXhWY1laVEl5dXJKKzBiSWUzNFBlbm8vcGxIUTBQeVlQOFNwTElBU3hBcExETFhaSGFhNWNWQ2pudnN2RHBvNlVXNktFSTd1QVRQNGQzQ1Z6R1I0MW5Cc2c0bGE2bG9Oa3RZVFJxTys5Y2FHRUhzZ2NsbUU2Y3UxdCt3MDZyS2M0Y29rZUE0ZFBHV1ZYaTBSOWcwTHZ0bkRqeDNFMHlkZHRaMTRPWnV0UGFaVjB4aFJrQXNkS0l3alNvT0lFNWRUSW85RWYxMG1xQ1N5WmtOcmoiLCJtYWMiOiIyNGY2MWI1N2I1M2RmYTZmZGRlNzY4YmNiNGJjYTAxMzMwZDhhMWJkYmVjZTE1OWJiOGU5YTQxYmNiN2FiZGI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354000301\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6nyGbgT6n8t00JbJ5CSgj7vkpXnxnyISs5XS6j6J</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1971065117 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBBWWhuTXlhcXBRSVJoRU0vNnJDbFE9PSIsInZhbHVlIjoiNyt0WTZwMTJNNE9xb29YNjBuK1hZZGxWdWRxeGVxc1FOb2NaR3FncFFuK2pTVFdlRnQxVGx5SDFHdXYxY0Jud1FVNXBTT01aOFI1eFJUYWQ4SExCNmt2aDkrRHZ4S3VoSWRkeDBKNkZhRDZRb1FLRGlqanp6cVUyZlBsWnFHdGFtYnA0Q29pK01qTUpKOE1EaE1SUXpocVdHQk5Ka3gyU0VHa0l3QWcyNTZZbmozWm4vQVJBVXZBZXRzYkZrY0dabDNwK3lWRlpDSXZVNG9BcGZmMElEQXFXM2hVNWVCRW1aYnBQNjVRekhpeitSeEdyd21RcUdvWktQS2lOMUVkL2pFN2Zlazk2VTRJTE1WZVpKc0FIcWQ1Vkg5T3QzczFMclY2ZmdWOSs4WDU0RFNXb294SkZIYk1YRWtEamQxeElyWW80NEptNjU1Y3BSQ1hJQ3VYOVYvaVBXbVVHRy92TzVHV25zVFFOZmtMNktoUnRlQTN2ZUw0dHJqWWtGT3BNd3pPbDlkeUtUNm4xSEt2VThFdkVuN1lMa1dpZVI5cE04dzVuZEJ0cTBvRmdCYjVzdjhEWGp3SWVHcXdxUmZVWVBWOUwvUFB5ZmVTeG5EY3d2MUYvM0lsbHRQQTROUGNPZEE5QnJkRXlwa0xXRFR2d3MxNG1uaEJXQ1Y0aWZvTWQiLCJtYWMiOiJiODcyY2U5MWI1Zjg4Y2VjNDMyNmFlNzAzNjQ2M2I1MGVjYWJiZGY5MWIwMDE5NmNmYzk0YzM3MGViNTk4YzJjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik00NTdPK3hLSGtKNWZPZ0lDcWJsY1E9PSIsInZhbHVlIjoibUFuak5BNU5EU2xHZHFEMVFGSlJETmJnNHJMSmdSbEZwL3dHMlpSWlY0Zm1OSEIwUVc3a0ZjelpKS0pmQktrNTBFM3JNbkZwQVBwUmd1dktKR2pzMjBua2FSTjcwdlRSVnhUUnBpZ3Mrdk51VWh6ZU1WcEFyUkFvL0JncXZuRUowUGllcW03VFFtZWdKSEVKSTdtVzZsYnVBVksvRmJybEZrYVVHRHVQTGRFMllqTFlzT3lDVFVXL01hck55QTI3OFVBSmJQNmJtTTl1dVpzSHoyenFpb2wyampPSnA5VzhxNGVVUmkxVjdrZ1NuaHRyb2MyTkd5TVgvOHZUTktWdXZqTDF4cld2UlVIVUlEdGVGaFBzay9zeUVFbDZlUTB1THd2UTdoa1lQQUtoSFpZUjlsUmFOdTUwM2t4Z0kzQ28xazU2c3dRV3VoYlJTdXRqekdXL2d2ZTY5bE14ektzdnplZzRmaHl4MnBEVCs5aVpNSHVPWGVIbkErRms1ekg5aHlqVnF2RTVqSTh4bmZsQXdNNW10QVRaU2dQY3pCaGFwSDVJbWRaaDBFNGdCVmJscTFHNGJPamIzUm00OU1rS01qM2Q1d1JXMkhNN00zNjA5QlA0eW5DU014aGtEUjRxc1JkMFd1VnNhUWNmYmVvWnl5QSt4bStqUWkzM1hKK3MiLCJtYWMiOiI0ODhmY2QzODIxNzNmN2JkZDRhNjUwNmQzZDViMmYwNDlhMTNlYjdmZDFhNTE1Mjg1YjRiMjkwYTIxOGZjZTk1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBBWWhuTXlhcXBRSVJoRU0vNnJDbFE9PSIsInZhbHVlIjoiNyt0WTZwMTJNNE9xb29YNjBuK1hZZGxWdWRxeGVxc1FOb2NaR3FncFFuK2pTVFdlRnQxVGx5SDFHdXYxY0Jud1FVNXBTT01aOFI1eFJUYWQ4SExCNmt2aDkrRHZ4S3VoSWRkeDBKNkZhRDZRb1FLRGlqanp6cVUyZlBsWnFHdGFtYnA0Q29pK01qTUpKOE1EaE1SUXpocVdHQk5Ka3gyU0VHa0l3QWcyNTZZbmozWm4vQVJBVXZBZXRzYkZrY0dabDNwK3lWRlpDSXZVNG9BcGZmMElEQXFXM2hVNWVCRW1aYnBQNjVRekhpeitSeEdyd21RcUdvWktQS2lOMUVkL2pFN2Zlazk2VTRJTE1WZVpKc0FIcWQ1Vkg5T3QzczFMclY2ZmdWOSs4WDU0RFNXb294SkZIYk1YRWtEamQxeElyWW80NEptNjU1Y3BSQ1hJQ3VYOVYvaVBXbVVHRy92TzVHV25zVFFOZmtMNktoUnRlQTN2ZUw0dHJqWWtGT3BNd3pPbDlkeUtUNm4xSEt2VThFdkVuN1lMa1dpZVI5cE04dzVuZEJ0cTBvRmdCYjVzdjhEWGp3SWVHcXdxUmZVWVBWOUwvUFB5ZmVTeG5EY3d2MUYvM0lsbHRQQTROUGNPZEE5QnJkRXlwa0xXRFR2d3MxNG1uaEJXQ1Y0aWZvTWQiLCJtYWMiOiJiODcyY2U5MWI1Zjg4Y2VjNDMyNmFlNzAzNjQ2M2I1MGVjYWJiZGY5MWIwMDE5NmNmYzk0YzM3MGViNTk4YzJjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik00NTdPK3hLSGtKNWZPZ0lDcWJsY1E9PSIsInZhbHVlIjoibUFuak5BNU5EU2xHZHFEMVFGSlJETmJnNHJMSmdSbEZwL3dHMlpSWlY0Zm1OSEIwUVc3a0ZjelpKS0pmQktrNTBFM3JNbkZwQVBwUmd1dktKR2pzMjBua2FSTjcwdlRSVnhUUnBpZ3Mrdk51VWh6ZU1WcEFyUkFvL0JncXZuRUowUGllcW03VFFtZWdKSEVKSTdtVzZsYnVBVksvRmJybEZrYVVHRHVQTGRFMllqTFlzT3lDVFVXL01hck55QTI3OFVBSmJQNmJtTTl1dVpzSHoyenFpb2wyampPSnA5VzhxNGVVUmkxVjdrZ1NuaHRyb2MyTkd5TVgvOHZUTktWdXZqTDF4cld2UlVIVUlEdGVGaFBzay9zeUVFbDZlUTB1THd2UTdoa1lQQUtoSFpZUjlsUmFOdTUwM2t4Z0kzQ28xazU2c3dRV3VoYlJTdXRqekdXL2d2ZTY5bE14ektzdnplZzRmaHl4MnBEVCs5aVpNSHVPWGVIbkErRms1ekg5aHlqVnF2RTVqSTh4bmZsQXdNNW10QVRaU2dQY3pCaGFwSDVJbWRaaDBFNGdCVmJscTFHNGJPamIzUm00OU1rS01qM2Q1d1JXMkhNN00zNjA5QlA0eW5DU014aGtEUjRxc1JkMFd1VnNhUWNmYmVvWnl5QSt4bStqUWkzM1hKK3MiLCJtYWMiOiI0ODhmY2QzODIxNzNmN2JkZDRhNjUwNmQzZDViMmYwNDlhMTNlYjdmZDFhNTE1Mjg1YjRiMjkwYTIxOGZjZTk1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971065117\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-556909155 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556909155\", {\"maxDepth\":0})</script>\n"}}