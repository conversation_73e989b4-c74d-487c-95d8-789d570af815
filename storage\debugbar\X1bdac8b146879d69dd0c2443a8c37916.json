{"__meta": {"id": "X1bdac8b146879d69dd0c2443a8c37916", "datetime": "2025-06-28 15:26:34", "utime": **********.463485, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.044282, "end": **********.463505, "duration": 0.4192230701446533, "duration_str": "419ms", "measures": [{"label": "Booting", "start": **********.044282, "relative_start": 0, "end": **********.411901, "relative_end": **********.411901, "duration": 0.36761903762817383, "duration_str": "368ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.411913, "relative_start": 0.3676309585571289, "end": **********.463507, "relative_end": 1.9073486328125e-06, "duration": 0.05159401893615723, "duration_str": "51.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45629752, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00242, "accumulated_duration_str": "2.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.439134, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.942}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.449191, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.942, "width_percent": 14.876}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.454272, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.818, "width_percent": 18.182}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1692709668 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1692709668\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1165410120 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1165410120\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-831637075 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831637075\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1513432137 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124349103%7C15%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlQzbndpV01oa3hzeGY1M1VwRnBMVlE9PSIsInZhbHVlIjoiajRuR3dhMGNDT0ZUTldNb1VPNTNQVGF5a005ZFE1OVJ3NC9wM2taT0hDVjg3MnhuMXBubitwR0pxUnpjMktKc29NUWlLZUZXQWtGWmFNOWU2SWU4UHRKcmRIWVpSRUs0VUFsZDBrbUptWkdoUzNpd0ZVU3Ridk92NG8xenN1NjJWTzVqZWs1ZjhOZkdmakJYdi9MUTMyYVZuK1ZyZ0ZtcWg0ZUlJV2I1UXJCUk13cXV2Zm1JRWdYV2JIYnpiaTFPazYxcGNHUEk5Zk5JS2pMclYyUmxWMUo0Z3FpTDJZTWova0NQWEJ6TWY3aHV3YW1pZk5YZGVSNFVIOTAwVExidVdvQUVKUnUrMXhLMms1dkx6UjdtRG1ibHpOVzl0RWhwK05FSW1Rd21wdGt6V3FBTFVrc3orcXZYUGJlQXVYTlc3aWdSYURRZ05GM3RDRm9XWW9zRkVoL1lQM0NkZXB1NWpjd2xqemxvSzdJK2wxdHY1R0lud3o2UTliOW93aVlpTEdkblQ0N1dVUEtxTFBZWXdCS3RQQlhVaWxQRjJxbklWVzBCZkc0SmsrOEV3ZVkyUXBuSVpvUCtaZnh4NUViTW1GQ3p3RnNXNlNscm9tVWI4NHVFOXNkU1luTisvNWdUK29JZFdqNTZZTWt1a20vdW5JMlE5VVdPMEczODRVSnMiLCJtYWMiOiJlZTgwNzIzMzg0ZWUwMGE4YmRkMmUyZTJlODM4MjgyNTUwMzFjZWNjZWY3ZWFjOWZkYjg0YTMzMWY5NGJkYmUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImR2V3VJNDg1ZnNWOHFVaTVzUHZnT0E9PSIsInZhbHVlIjoiVkQ5bml4ODlJSktQVjRFSFJETEhoRThsRzZXMWpOMmlhNFI1UEwwVW1HNHhEVlFkZFY4OEZQeVg2ME5MeHpLd2xaK3JBeS82ejR6dFJwQ0JWVS9NN010WTU3emRQN2RacFRiM2V2NnJaOURNdE1GaEJ3Wi9RS2N6ekRyMStyU3gzT2xjOWJXNnR0SGNhNVNWN09PVnZzQUFaMzl0dDJ2d0tnWDRZMHZaVnl2SWpIaUxSbGhRalFkR0g0TG9oaS8vb1FLSEhmU1hvMVUreGRKM1BMaUxBV3VmakFnSTlwbUdDWW1lKzd5M0VyajVjdUc0V1hhM2hQYXB4QkZnS21ycDdmejJxa3VuVWR4MGJCMU1yNzk2czNqbmRFalZGV3lhNVU3ZmhxQlhLYlZha0dQdVVwNEZlUVVpSzlXVnZiUm03L0NZOU1RZE5DR0dzNlJPUW1XU0xzZ0g2U0JPcXVGSW5OY0xMM3lZOUpHSWVtSE5PS2tDZS9rNzg0MThHdVRHVU5vNGVxYzBNclJWT3dZVlhQSWpwNGRiZ0NtZ3dMOHI2akl3d1Y0S0s4S0VCT2htTGVqSWpxSjl3elcwTE4reDlGazhhWlRtOFlYVTNIdEVPUEQ1TmlVeUxaOWZpNGh6TGx0WkgxNnpRaVhyem9tRTJLRUhPMEI4TGJZLzJ3QUwiLCJtYWMiOiI3YjIyNDRmYWE3YzUzNGVmMTA3MWU0YWE4N2Q5MzIwMTdjMGNmNDkzYTJlYjBmNzlkNjRiODkwZDI3M2MzZTNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513432137\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-279478099 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-279478099\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1370769100 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:26:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5OOGZjSnoxZHc3THZkVXEwbzMrdVE9PSIsInZhbHVlIjoiZVBIN09zRDg4WEV5eW55M0N2eWlQUlV1aUpSNzBWRkg4VndHV1lHL1JieUlDREppRDlaSkQ5WWNIODZjbXVrRVdseTR4TmhrRVBKNTFhMndUTzZrd1F0aEFrc2lKR0pHWGFhZU80YU93TlkyOUN0TVA5SUdrRVdhaVoyVzF6VmNrTGk4eHBTZEg1NHVLczJBcTRLNlF0cFo5ZTFVa1RjNUQ4b2tUMkFsWUk0aTA4YnZ4eUp4OEJxaUxmZnN5cENMcERlQk9tSlZqSUg1ZXZZK2FoM0NYc1FidHpSdlIrNXh6OTlDT0l2R3lRa1RBTWUwWEtwc0NZcmpGOERmaUVIeGVTSU1MNm52RjdWbHpwb2wyRklQTFRuZ2FEOHFVRDVjMVVOY25LYklMb3NjZUFFWHlBWThQTE8yWFQ1elpYaXZWc3BPNXd4b3I2RDFwTWlJM2tORElhajM4M0VMSXFiL09jOXlvNy9mRk1GT2VLbTByTTUrcG9DUFd0NWpXSEtTTE0vclBUcFVSaXBKU1IwMWV1RGxZNHM3MzBEYW9GK3NiNUsyMk5XeUR6bGZ6RzFnayt3M0JQdVFTM0RtTFF3V2d4WW5ZZlptZVYzN3JaVFJWRElOK0NwMDlIM1J5R0tKRVRDQ25wL0dRYmdKaWdXbmJoOHFhemVBR3lmZGhXNzQiLCJtYWMiOiJkMTA3ZjRhMmNmZGY3ZGE4OWZkMjFiZmM5MWJhNjE5NmMxMzhlNGZkZTgwNDI3NTA5ZTRjMjFkY2Y4MTYwMjIxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkoyZUxuMEx1RU5NM2owR3F4NmdFanc9PSIsInZhbHVlIjoicjBnWDlUa3R5Y3hCV0xBQmhQaDlBcFV6dE0xcFVSS3ZTeFRyZ0Q4czhPSkc5TVBvUjZva2pheDhxVXQyNURNcVZGUEE5NzFmaTVxcnRDRWFxdWFqcDE2ZFo0MjFpNmJWa1IycFkxc2ltc0xydG5CQ1YvbmExN29FeVRkRG1nMDdTUDNJa3VLZmMrYjhJN0puQUM3R0VJclh2Q0xia25uV2JENUk4V01lWWlQckJ0OGJHa2tmYjVCaHdVQWx0U0lrYVJWVlJ1QVJtV3MvTkVMZjh4T0VySWc5SjAwTWZBK1hZM2NDWHM1MHlua2hDMFF6cmtaTnk0clN1d1MwVVdMWDBRaGlnR3pIMkhzcll5bUNBV3NDRFRCR04vMlFNQTI0bEdtNkNtT1FPaXVzWnNCNlVHUUhFUlEwVE5FTG5SYUtmMXdHbnoyVUNKNHlnY3IxZVc2QjlOK2hOY1dOKzJJcy9BOXlDRnNJd3RlVGxDdGI5L3B1NGw0cnVmYkk5RldtWjNkN3FoUi85bXZVVytncEF4VGVBZnRhQWltUGYxWlZuamJuR1JMTFJrYnRHS0RGMUQ0ZUdLRUFuOWo2bDhleEJjaGtHbC9xNmJvZ1grZXJSbHlYVjRqL3o0NmtCYko3ZGp4Wll5bFpDYm43OTJadDQ2TWFZdkh1elBIWWJtUkwiLCJtYWMiOiIzMjk4NmI3ZGUwMmYwZDU5ZmUxZWI1YTZjNzA3YjgwNWY1NjIwZTMyZTJhMDhhMDdmYjcyNDZmN2JlN2E5NmZhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5OOGZjSnoxZHc3THZkVXEwbzMrdVE9PSIsInZhbHVlIjoiZVBIN09zRDg4WEV5eW55M0N2eWlQUlV1aUpSNzBWRkg4VndHV1lHL1JieUlDREppRDlaSkQ5WWNIODZjbXVrRVdseTR4TmhrRVBKNTFhMndUTzZrd1F0aEFrc2lKR0pHWGFhZU80YU93TlkyOUN0TVA5SUdrRVdhaVoyVzF6VmNrTGk4eHBTZEg1NHVLczJBcTRLNlF0cFo5ZTFVa1RjNUQ4b2tUMkFsWUk0aTA4YnZ4eUp4OEJxaUxmZnN5cENMcERlQk9tSlZqSUg1ZXZZK2FoM0NYc1FidHpSdlIrNXh6OTlDT0l2R3lRa1RBTWUwWEtwc0NZcmpGOERmaUVIeGVTSU1MNm52RjdWbHpwb2wyRklQTFRuZ2FEOHFVRDVjMVVOY25LYklMb3NjZUFFWHlBWThQTE8yWFQ1elpYaXZWc3BPNXd4b3I2RDFwTWlJM2tORElhajM4M0VMSXFiL09jOXlvNy9mRk1GT2VLbTByTTUrcG9DUFd0NWpXSEtTTE0vclBUcFVSaXBKU1IwMWV1RGxZNHM3MzBEYW9GK3NiNUsyMk5XeUR6bGZ6RzFnayt3M0JQdVFTM0RtTFF3V2d4WW5ZZlptZVYzN3JaVFJWRElOK0NwMDlIM1J5R0tKRVRDQ25wL0dRYmdKaWdXbmJoOHFhemVBR3lmZGhXNzQiLCJtYWMiOiJkMTA3ZjRhMmNmZGY3ZGE4OWZkMjFiZmM5MWJhNjE5NmMxMzhlNGZkZTgwNDI3NTA5ZTRjMjFkY2Y4MTYwMjIxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkoyZUxuMEx1RU5NM2owR3F4NmdFanc9PSIsInZhbHVlIjoicjBnWDlUa3R5Y3hCV0xBQmhQaDlBcFV6dE0xcFVSS3ZTeFRyZ0Q4czhPSkc5TVBvUjZva2pheDhxVXQyNURNcVZGUEE5NzFmaTVxcnRDRWFxdWFqcDE2ZFo0MjFpNmJWa1IycFkxc2ltc0xydG5CQ1YvbmExN29FeVRkRG1nMDdTUDNJa3VLZmMrYjhJN0puQUM3R0VJclh2Q0xia25uV2JENUk4V01lWWlQckJ0OGJHa2tmYjVCaHdVQWx0U0lrYVJWVlJ1QVJtV3MvTkVMZjh4T0VySWc5SjAwTWZBK1hZM2NDWHM1MHlua2hDMFF6cmtaTnk0clN1d1MwVVdMWDBRaGlnR3pIMkhzcll5bUNBV3NDRFRCR04vMlFNQTI0bEdtNkNtT1FPaXVzWnNCNlVHUUhFUlEwVE5FTG5SYUtmMXdHbnoyVUNKNHlnY3IxZVc2QjlOK2hOY1dOKzJJcy9BOXlDRnNJd3RlVGxDdGI5L3B1NGw0cnVmYkk5RldtWjNkN3FoUi85bXZVVytncEF4VGVBZnRhQWltUGYxWlZuamJuR1JMTFJrYnRHS0RGMUQ0ZUdLRUFuOWo2bDhleEJjaGtHbC9xNmJvZ1grZXJSbHlYVjRqL3o0NmtCYko3ZGp4Wll5bFpDYm43OTJadDQ2TWFZdkh1elBIWWJtUkwiLCJtYWMiOiIzMjk4NmI3ZGUwMmYwZDU5ZmUxZWI1YTZjNzA3YjgwNWY1NjIwZTMyZTJhMDhhMDdmYjcyNDZmN2JlN2E5NmZhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1370769100\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1159704638 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1159704638\", {\"maxDepth\":0})</script>\n"}}