{"__meta": {"id": "X9488da065534a872bcef8f5c350ccfee", "datetime": "2025-06-28 15:03:52", "utime": **********.526979, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.072996, "end": **********.526991, "duration": 0.4539949893951416, "duration_str": "454ms", "measures": [{"label": "Booting", "start": **********.072996, "relative_start": 0, "end": **********.437072, "relative_end": **********.437072, "duration": 0.3640761375427246, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.437082, "relative_start": 0.3640861511230469, "end": **********.526993, "relative_end": 2.1457672119140625e-06, "duration": 0.08991098403930664, "duration_str": "89.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46176248, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.029879999999999997, "accumulated_duration_str": "29.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.475884, "duration": 0.02447, "duration_str": "24.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 81.894}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.508377, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 81.894, "width_percent": 1.238}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.511117, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 83.133, "width_percent": 2.242}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.513091, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 85.375, "width_percent": 10.442}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.518076, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 95.817, "width_percent": 1.774}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.51986, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 97.59, "width_percent": 2.41}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-565664794 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-565664794\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-89448019 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89448019\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-174383210 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-174383210\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1306752130 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122996219%7C8%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFOUy9lQjNuNVhKZmlHOHVxMDNNZkE9PSIsInZhbHVlIjoiY0ZvRjcwei9HeWFzWXh5Y1AzTUlFZ3JwYU1sUk5FUlZJanpQMWJzYVVzbExYTFBLWjI2cHZPNG9JT1hsaG4wV0JJamdZS1JkeUJRUkFEeFJtYWtwTkFCZGNUN2F2ejVJY216Vm50WWpnQjRKOWNmb0lmdXY1T2RLeUdJNXVOOU1ZZHYrU3JqYVNtN0E3K3dUT0xnb0JHMTk1elNnQkZxSzE3d1UzMlNqdHVkT1R4anlUdThvNW9vMzdNd3g5Q3BlV1hnWWxlUlJwWDhYS2lyRXVESWE0QUFQSFBrajBUNjN3QS9TS2NNaVlqOUZSYm1ibTEwNEJ3NEphSVdtOGp1Ym53TkVkT3I5Q09UNEI3WkNOR2s3UHM0K1ZOa05UNXVWMUU1cHRsOEFjeTZza3grTzRHeHN6dFM4MlFib0V3Z2MwdHRTc3lUQ0tXWnFsK2ZWYVJKM2FScDdBRW9abjJKMnhpenBhalRhNVMvQXZTdHF1Ny9kcFMzYUsyMklzekFqZkh0NFVLUHJ6ekFuUWE0Vmk5RFZJNVlWdFhaRDZNVUFMKzR3VTFuVTN3Y01BZUVpK1VRMndsRnBLK0VSNFRjQ01TbmFMZm1VcTM5OWl5dlZQUkFyRzV0VTM5WDFpYTllQURlamF5TzBmSnhnMHV1NU1nNENlY2QrTkVYU2xnZ1oiLCJtYWMiOiI4YjcyZmIyNmYwOGU3MTAyZWUwZmU5MGQzMzI5ZGJiMGYwNWE2NWI3NGE0Yzc3MjhjNGRkNjRkYWY3OGE2ODM2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJUSDZoOFN3ZzlyaUdpTlY2Rzg3blE9PSIsInZhbHVlIjoiNEM1MHlpeTNOMkNpS0QyL01YMm4ybUd4TTY2dTJxRXFua3JDWXRkejNFZGdmamEvVFZlclM4cFNDUk5hZlcrNDVlYmlMRVZ5UEVIdEZHc1gzQkJPcXJHMXdKRlZPQ3NSaWVOUHNXUmJlVE9mN2xLVnpaVGpxSnp4cXVsQkovc1ZoY1RNYnIzOHd4Yk9SS0RZU3I5K0hBQ2gvRmpHQ0lUajNxNEZUbUZJT0tjU3U4TE14MHdzeHZoSnduaytKNmZHYjF3ZTdQVHFBcXh2b1hlSEdtU0FDcURhWjB1YVRYU3NpNzE4L2RZdUZMa3V1SVo5VVR6U003dXVKZUZRLzhTUXkxamFKRnREcHdFNU9rSUhKYWRiVDNWUS9PNzJNd3dsMCtXeEpESHJWcVJSamJKUDVEOUhLT1NmbjhyNjFreTN2bksvbkJjbTlETnM1eUJIWW1yNnlJS29seUxKZ0MvckJjbkZMVXBNblhJeE83ak5rTUxtenkybWVjREF6K0dOMjk4d05sRnp3WkFvNTAvci9hdHgyUEZSRlN4OTdhc3MvL013SzdxbUgwZ2ZjL2FwVGNFcjVIblMvQWNHNHBnNCtSUjFocUlEb0RzdTlORDRUSkZiWlpuWlYwUlRRM3BJMFRwR21hZG1kZWI5M2FGYmpPd0YwSnp1b0lTbzR0Q0oiLCJtYWMiOiI4MzM4YTA3ZDBjOTY3YjViYTNiYTVjM2FhZGNmMGQ2ZDFiMTJlZWRkMGQwZjEzYTQ2NjUxYTY0YWFmMWM4MDU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306752130\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-753034993 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-753034993\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1616496129 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:03:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktVOXFWVU0zNElsWVZHeW5rSThYTUE9PSIsInZhbHVlIjoiRFBhZm1yMVkzMVpOa0ZVeTd6eG4wTEM3S2FSd0pqNWR0SWw5V2J4dXZRaGtpZUtBMVM4NmNIZ001MHJDNmErd0o5RC9USUlLV0RsTGJhckJnV01sMFc1TDg4eGQrbmV0RXZUVzROd0Y5dFV2cVVreU1GQUt0alNEVGJxVmY1a1lYTXhDU0U0bm1vMnRJdjZyODlobnh2TklVOUFuT0NvbytrVG82WEVBcFZvSktjZnRKaExvV3F3SXRsZ1c5Nm1mV042MjdJUWNVeWZ5bitkRFRJVHRiUTRUZzhKRWEzOC92TW1rcHFaRlN4OGlRSTM4bkpJUUhaWTdaNlNJK3RqMk5YM0o2bXVvd2E0S00yZlNpUVFpTEdlNXVPSkxpL0V4L0hlU1huaTBIRGRvWS9EZEF4MnJQNFE1bU5sOTBqMXp0RCtTcDNxV3pSVW51VVNXZi9kVTFDZll2VlhzTzZZTDMybm9MQkxMUVdvYXhHRngzdmlXM0piUTNkT0xKTVdDeFg5TmhzcXo5cWRFODhoRDVFOEVqRlhSbHJkYjc4QzVsTUtjOGVqdWtVVFpRa2tLTjRCaFU5cm9pT0djWElLVDJvY2pHUGtoL1JhWmJQcmhtZWhGYUJqaWhQdXdYb2JiTi9UcjlTbHk1NGpEZE5qSnZIbnhPdlNIWWtuRFo4VkciLCJtYWMiOiJiMGMwMzA4NzAzYjIwMzhjZmQwNDFmZDhjNGI5YWZjOWFkYWNmMTg0ZGRjNzk3Y2Y1OTIxMTA5OWMzMmUzNmQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJFK2RBcjNYL2tDNmlVd2V4U1ZmQmc9PSIsInZhbHVlIjoic2t1RlN5YU02RG85NHJ1Q1NYUkZHSzBqZVhjV0tTaXBZajlXL2pBb00rekJGRHRjU2ZmQm9ZMVNPWFFsRi9QelBVREtqbmV3NmE2SkRmYjhuTjBmclZKUVQrZ2Rqekw5KzgyNWtLUWkwbHI4MkNvSmhjaUR3bDIzYUN1K3dNNXN0STBlOXhPbWxYVDFjQWZYdlYvOGo2V0FLL0VQcWN3NWw2b3ZpUDB3c1dWdyttUlF6dXBzdkZjdjh6d0pDM1kxaEpYQVR1NENEV3B1MEo5UDNOUkw4NXhVNnZwVVBiUHhTcXdsOTk3SGFWVXZEd1lMQm12SlF6S2x1L1RLWk9qQzNDb1ZBYWhSYkJWM2hCYlZjMzBFeTJFQU9UdlRhS21DVExIUGJsbmJvc0J3U2tLckwvVDFrU2plOG9zOUdvN09YcDFrNFFYUDNmNFRsaURWWlBTdVFuRkdNUktnOWhSSDk2QTBqSDFsV09zQzVleVJRMWlKb0hRNmRhMG5rSVpHVkNNeCttOVpwSU9VZkFMU09WSmdnT0tOVjRYNFpvTEJqOVR3eGc4RXloQ1NSdWVJTXpkZnhicWxHQXlkSERXY1loREt1aXZFcFdyNCtvSHlsRXdNMkNpa1VXRFk2QUZTK1pSdEYxRE5mRnJpWVVHUFZWWWZpTmFZTVd4ZitlNjEiLCJtYWMiOiJiMDc1MTE1NWYwMDQ2YTgyYTY5OGQ4Yjg4YjYxNmQ3Y2VjNzY1YTA4MGMyZjk5MGU4NWMwM2E0YzQwOWE2NDc3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktVOXFWVU0zNElsWVZHeW5rSThYTUE9PSIsInZhbHVlIjoiRFBhZm1yMVkzMVpOa0ZVeTd6eG4wTEM3S2FSd0pqNWR0SWw5V2J4dXZRaGtpZUtBMVM4NmNIZ001MHJDNmErd0o5RC9USUlLV0RsTGJhckJnV01sMFc1TDg4eGQrbmV0RXZUVzROd0Y5dFV2cVVreU1GQUt0alNEVGJxVmY1a1lYTXhDU0U0bm1vMnRJdjZyODlobnh2TklVOUFuT0NvbytrVG82WEVBcFZvSktjZnRKaExvV3F3SXRsZ1c5Nm1mV042MjdJUWNVeWZ5bitkRFRJVHRiUTRUZzhKRWEzOC92TW1rcHFaRlN4OGlRSTM4bkpJUUhaWTdaNlNJK3RqMk5YM0o2bXVvd2E0S00yZlNpUVFpTEdlNXVPSkxpL0V4L0hlU1huaTBIRGRvWS9EZEF4MnJQNFE1bU5sOTBqMXp0RCtTcDNxV3pSVW51VVNXZi9kVTFDZll2VlhzTzZZTDMybm9MQkxMUVdvYXhHRngzdmlXM0piUTNkT0xKTVdDeFg5TmhzcXo5cWRFODhoRDVFOEVqRlhSbHJkYjc4QzVsTUtjOGVqdWtVVFpRa2tLTjRCaFU5cm9pT0djWElLVDJvY2pHUGtoL1JhWmJQcmhtZWhGYUJqaWhQdXdYb2JiTi9UcjlTbHk1NGpEZE5qSnZIbnhPdlNIWWtuRFo4VkciLCJtYWMiOiJiMGMwMzA4NzAzYjIwMzhjZmQwNDFmZDhjNGI5YWZjOWFkYWNmMTg0ZGRjNzk3Y2Y1OTIxMTA5OWMzMmUzNmQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJFK2RBcjNYL2tDNmlVd2V4U1ZmQmc9PSIsInZhbHVlIjoic2t1RlN5YU02RG85NHJ1Q1NYUkZHSzBqZVhjV0tTaXBZajlXL2pBb00rekJGRHRjU2ZmQm9ZMVNPWFFsRi9QelBVREtqbmV3NmE2SkRmYjhuTjBmclZKUVQrZ2Rqekw5KzgyNWtLUWkwbHI4MkNvSmhjaUR3bDIzYUN1K3dNNXN0STBlOXhPbWxYVDFjQWZYdlYvOGo2V0FLL0VQcWN3NWw2b3ZpUDB3c1dWdyttUlF6dXBzdkZjdjh6d0pDM1kxaEpYQVR1NENEV3B1MEo5UDNOUkw4NXhVNnZwVVBiUHhTcXdsOTk3SGFWVXZEd1lMQm12SlF6S2x1L1RLWk9qQzNDb1ZBYWhSYkJWM2hCYlZjMzBFeTJFQU9UdlRhS21DVExIUGJsbmJvc0J3U2tLckwvVDFrU2plOG9zOUdvN09YcDFrNFFYUDNmNFRsaURWWlBTdVFuRkdNUktnOWhSSDk2QTBqSDFsV09zQzVleVJRMWlKb0hRNmRhMG5rSVpHVkNNeCttOVpwSU9VZkFMU09WSmdnT0tOVjRYNFpvTEJqOVR3eGc4RXloQ1NSdWVJTXpkZnhicWxHQXlkSERXY1loREt1aXZFcFdyNCtvSHlsRXdNMkNpa1VXRFk2QUZTK1pSdEYxRE5mRnJpWVVHUFZWWWZpTmFZTVd4ZitlNjEiLCJtYWMiOiJiMDc1MTE1NWYwMDQ2YTgyYTY5OGQ4Yjg4YjYxNmQ3Y2VjNzY1YTA4MGMyZjk5MGU4NWMwM2E0YzQwOWE2NDc3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1616496129\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1960211164 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960211164\", {\"maxDepth\":0})</script>\n"}}