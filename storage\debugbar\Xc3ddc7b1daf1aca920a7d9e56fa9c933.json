{"__meta": {"id": "Xc3ddc7b1daf1aca920a7d9e56fa9c933", "datetime": "2025-06-28 16:19:36", "utime": **********.721526, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.312729, "end": **********.721543, "duration": 0.4088141918182373, "duration_str": "409ms", "measures": [{"label": "Booting", "start": **********.312729, "relative_start": 0, "end": **********.66512, "relative_end": **********.66512, "duration": 0.35239100456237793, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.665129, "relative_start": 0.3524000644683838, "end": **********.721545, "relative_end": 1.9073486328125e-06, "duration": 0.05641603469848633, "duration_str": "56.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45716896, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00269, "accumulated_duration_str": "2.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6907809, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.606}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.701256, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.606, "width_percent": 14.87}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.706939, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.476, "width_percent": 11.524}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6Iks1SUpiK0ZrMlZTLzRwUkZhY1pHcGc9PSIsInZhbHVlIjoibFRib2N3TUdFVUlnMXVZRkkxTjlLUT09IiwibWFjIjoiMzNiYTg1YTU2ZTNhMjFmNWMxZjRhZGRiOTcyMWY3MTY4OGY5YzNlN2M3YTk2MDk0MDMyOTdjMThmMWFkY2E3NiIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1848159629 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1848159629\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-268963386 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-268963386\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1281636474 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281636474\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-884124600 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Iks1SUpiK0ZrMlZTLzRwUkZhY1pHcGc9PSIsInZhbHVlIjoibFRib2N3TUdFVUlnMXVZRkkxTjlLUT09IiwibWFjIjoiMzNiYTg1YTU2ZTNhMjFmNWMxZjRhZGRiOTcyMWY3MTY4OGY5YzNlN2M3YTk2MDk0MDMyOTdjMThmMWFkY2E3NiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127574523%7C38%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJ0TDZzTXFyQ2NzTk5DeXdqWDdnZlE9PSIsInZhbHVlIjoiK3FMK1U1Z0xrSXYzcUtvSm9CZk1SRHVSZFNRNk1xaW14aVhiSUFQL3pGTmVUaHMwVnVaWDdSWEFYUm5Sc2JTT3ovOCt0aGp2U2lMc3cxekFGejlEQVg4VGN2Vm1iQlUrb1BxNzJQV1d6b3YyL2tUYWZEcXlLNzNVN1puRFJVRU5lcGVaNlVUWUNlUmxnMDMxYUtFcE44YUE4ckUzUGpTYmRzNkM5VGFKR1pLUmVJdDY1SWR0ZVhRY1NDR2pScWVEaUJMdnVpdzhDQjFwZ3Jva0diRFlrSzJXVDVUS3lMck5DTlV4YmZaNVE1U1h0b2hjMzdGRjV4dENGdnVNVHh0RXdUTzcvcFlLU2JkMGNJd0llSTRVMEF0bXRtYlNnVjFra2VEU0htbHVDWHUzamJCTjJXZ1J1UVZqRkJpYkh5emx3NThZZjlSWm8yTy9qMWZwdTNBVGhDSmc1QUx0VGlsdXpwUWJDWmQ1d1U2TXVrelRWS2N1NUdHVVI1RjR3eis3bG1HZE42a0dCTEJnQ3hZZ1JJbWpDSjNUVkx3WUliYlZQNVJ3SDQ4ek96ZDhkNU1OVlNCMFlyQlkvYW9SVll6QVpNZi9SVVcwMHNTRThKRml2bDQvYS9HSHJXdkdGY0xMWXl3ekptWXYzM1A5YVcrNmI5UWNhbXFtR3kxY01yOEUiLCJtYWMiOiJkZTBlNjZmMDQ3ZWI5YTUzOTk5OGVlMWRkMTA1MWNmOTEwNmI3NjQ2NTc0YjNjYWEwNTNiMDdjNDE3ZDdkMGJjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdBZHlJdVBXd2ZrZGpxY1FaQytoOHc9PSIsInZhbHVlIjoiWmIyeDRiNUQzK0U0cDhoemkvVUVZVEw2cldBWldnTmlrRDFWWUZodEZ0MlluYkQ2YVhEZkxud005eGFiK095b0Z2Q0FveHhCVGJUTkMrc2dIYm4yUzVWVWNZdHNoZUNyQU9nSWs4WnBoQm9aY2w4dEtKVEI3MjZ6ZmNldWJrbWxKTFpPSkp2MGJYQ2k0bEdpd1p1ME1mOWJXcmYvRCsxRXZjSi9nY3EvR1VINW5SM3VBSWdxbG9XbkVPdWRSTWFBaHJsZkJDRnBqS0ZpZ3NRMkV4WE1WVS9KYlpFemVxSUIxelpsMlptYXVNSFhadjEyczEzemVLclJ4TmR4aktuK2g0NXlvdnZ3N2RVUDVQeXdhdGtDS0NYVEoxaE9zdHp0VHVIc09FV1liaG5MSHEvZzhCR2xGR2srdjVtRTJwaFF6TklaVjNIWUFoZlI1ODJsL2sxVmEyWDYxa3IrUnRDSU8wRGNnSVRTZFhLWGx5L0RSL0dINkJZZ0sxZmxINk5kL0l1dHNRYWxicEFxOHJXVVkyaHRGWjJ4dm0rSk1VazZCMnRmejU5K3lydlB5U1h1UWMrUm1ka25TNlZiaklDU1c4VnJpQm9oUUY4c2N0OE5LZHN4NFExOFVGbE5ydkg2MDhSUFdVem1rRGNRdE5hZEhHbGhiblpKMlMrSHNPL0giLCJtYWMiOiJiYTQ2ZGIxODFkNjUyYzMzZjI4Y2QxMGRkNGE2YTYwODMwZGZlMzllMjkxYzdiYTA0YTUyOTlmOWZlOGU0YzlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-884124600\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1811030873 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1811030873\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1950772363 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNwMi9hcWJLbFV5K1VoRmZDeWdwTEE9PSIsInZhbHVlIjoiRHV4ekdCZlh5bEJoRWVObmdFNjhMMFVZZzVOU2dBR1VTMmxPaFdkdHlCNUszR2RNbWpyRnNOMHhva1ZnSHpKdUxJMEpzTmo0dVdKa3FDK1ZJQ0g3aGVyMm52SUgybnpiWFNTUzNNVUFTYUxKdjVXMGlham1VbmVZOG9wVW1QRThNRnR5Nkc1QlNveEViYUYrdEx5UjJFMG1GSjczUFdyVHdlWVFPVWt1Ylh6cFdabTk1WlRJYmtqclN1NzUxOU5MQVZpeklrQnN1MGR2S1ZkSTRWdWlhNUs0WTBraTJ2b091L0xTa0hzV0NZRWRjVEU4M3dhajNEbmxxSXoxR04rMlBMczA0bUNsWFpGWk9UVEpjN3Y4dEJLVm1oT2RYVWFBaWIxV3pCQjdnR29VeEc2VVMvSFdlL0dVcmN6VGhRdVVrbUdzWnpEMmFMNVNOYjNLc1JsSUhDT1FndDQyREkvUjhMSWF3V0tTQ3pWWmErVUZwNEdMaVh2WlFjdTNWNUtsVTJwaG1nc2lTMUxscC85S0NnRzQyQTF1VEtIY1RHdlRGUzNJVzY2YmovdXZBRFBXU0dEM2l6RXJkVFRaVXh2am5KMk9tNmlDejhKZ2xOQ2w4WHJHejJkVUJFNFdMalhjUTRJUERNTkNkdWd3NTlpSVM4K2E5czI1aDU5NDE4eXEiLCJtYWMiOiJjNzFhZmNkNDAzZWNmODlkMjIxNTE3MWIyYzY3ZGQwYzg3YzhjMzhmNjQ4NjM2YTYyN2VhMWE5NzZmZTU1NDNjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpRUWNGTEZRWDVhcDI3RC84ZUI5OFE9PSIsInZhbHVlIjoiVVZMaFdKSnRNblJpTTlnRXVMVk5PYXk1Qlk5S2kvUko0bitTdjV3NnFPTmdsUU0xUUpGcUphSEYreStSaHhRWURXVXY5UHBZTi9UajZ4NDRZb3NHL0VkZnNvdUNBblNTcGdDOURFTUhyRStPMWUyNnFzYlFEdHNtUVd6Q3FiNVZkeUdlditROUpNdUZ6MEowdGFsSkx2V01jZVFSWXZzOEhJcFBGaHFQQUM1NzQxeVFYaU9BR2wyVDk3NmlHSktKdmxTTHVVVW95UGRyOXlIK1djYnFVd0Ria2ErTHppTG5rUG9BWjJwZmJzdXJaS1FjV3VKcWpiM3orY2o2RTN1U0htMDNCL3djaHZ5YmRPMUdXNkI5dmRnaEZqT3lRcFNWMXlEVHNWT3pTWGFVN1pMbGQxbER6eFZ6WEE2N1VKdXpJdFBYYU9YVHJReHJBWUR4V0hVTGFhTTNDcnQrMlN5dGFjcE9xQVpTTlpaQXNvWXJ3cUtINVNCeE9Gc2pYSndsb2VidHJPRUp6SjVySldmdGQzTTRPY21LTGNLMENyTmtlbnhIVHBuV1lkeGlPTmljaldmNE0yd2VRQllYbDNJYlc3WDUzbFFMVElWRWpWaDhldEFybHJ3REJLVlA4Tm5XczlBc093S1oyM2V4UWRkcUtIMUVjekNQYnVaVWhUa1kiLCJtYWMiOiIxNzA5ZmM0N2Q5YmM1Y2IxOTkzM2JiMTk0MTliZGIxOTExNDAyMzc5ZjNjYzcxYmYyNmYwOTA3OWU3OWE1ZGYzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNwMi9hcWJLbFV5K1VoRmZDeWdwTEE9PSIsInZhbHVlIjoiRHV4ekdCZlh5bEJoRWVObmdFNjhMMFVZZzVOU2dBR1VTMmxPaFdkdHlCNUszR2RNbWpyRnNOMHhva1ZnSHpKdUxJMEpzTmo0dVdKa3FDK1ZJQ0g3aGVyMm52SUgybnpiWFNTUzNNVUFTYUxKdjVXMGlham1VbmVZOG9wVW1QRThNRnR5Nkc1QlNveEViYUYrdEx5UjJFMG1GSjczUFdyVHdlWVFPVWt1Ylh6cFdabTk1WlRJYmtqclN1NzUxOU5MQVZpeklrQnN1MGR2S1ZkSTRWdWlhNUs0WTBraTJ2b091L0xTa0hzV0NZRWRjVEU4M3dhajNEbmxxSXoxR04rMlBMczA0bUNsWFpGWk9UVEpjN3Y4dEJLVm1oT2RYVWFBaWIxV3pCQjdnR29VeEc2VVMvSFdlL0dVcmN6VGhRdVVrbUdzWnpEMmFMNVNOYjNLc1JsSUhDT1FndDQyREkvUjhMSWF3V0tTQ3pWWmErVUZwNEdMaVh2WlFjdTNWNUtsVTJwaG1nc2lTMUxscC85S0NnRzQyQTF1VEtIY1RHdlRGUzNJVzY2YmovdXZBRFBXU0dEM2l6RXJkVFRaVXh2am5KMk9tNmlDejhKZ2xOQ2w4WHJHejJkVUJFNFdMalhjUTRJUERNTkNkdWd3NTlpSVM4K2E5czI1aDU5NDE4eXEiLCJtYWMiOiJjNzFhZmNkNDAzZWNmODlkMjIxNTE3MWIyYzY3ZGQwYzg3YzhjMzhmNjQ4NjM2YTYyN2VhMWE5NzZmZTU1NDNjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpRUWNGTEZRWDVhcDI3RC84ZUI5OFE9PSIsInZhbHVlIjoiVVZMaFdKSnRNblJpTTlnRXVMVk5PYXk1Qlk5S2kvUko0bitTdjV3NnFPTmdsUU0xUUpGcUphSEYreStSaHhRWURXVXY5UHBZTi9UajZ4NDRZb3NHL0VkZnNvdUNBblNTcGdDOURFTUhyRStPMWUyNnFzYlFEdHNtUVd6Q3FiNVZkeUdlditROUpNdUZ6MEowdGFsSkx2V01jZVFSWXZzOEhJcFBGaHFQQUM1NzQxeVFYaU9BR2wyVDk3NmlHSktKdmxTTHVVVW95UGRyOXlIK1djYnFVd0Ria2ErTHppTG5rUG9BWjJwZmJzdXJaS1FjV3VKcWpiM3orY2o2RTN1U0htMDNCL3djaHZ5YmRPMUdXNkI5dmRnaEZqT3lRcFNWMXlEVHNWT3pTWGFVN1pMbGQxbER6eFZ6WEE2N1VKdXpJdFBYYU9YVHJReHJBWUR4V0hVTGFhTTNDcnQrMlN5dGFjcE9xQVpTTlpaQXNvWXJ3cUtINVNCeE9Gc2pYSndsb2VidHJPRUp6SjVySldmdGQzTTRPY21LTGNLMENyTmtlbnhIVHBuV1lkeGlPTmljaldmNE0yd2VRQllYbDNJYlc3WDUzbFFMVElWRWpWaDhldEFybHJ3REJLVlA4Tm5XczlBc093S1oyM2V4UWRkcUtIMUVjekNQYnVaVWhUa1kiLCJtYWMiOiIxNzA5ZmM0N2Q5YmM1Y2IxOTkzM2JiMTk0MTliZGIxOTExNDAyMzc5ZjNjYzcxYmYyNmYwOTA3OWU3OWE1ZGYzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950772363\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-43710044 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Iks1SUpiK0ZrMlZTLzRwUkZhY1pHcGc9PSIsInZhbHVlIjoibFRib2N3TUdFVUlnMXVZRkkxTjlLUT09IiwibWFjIjoiMzNiYTg1YTU2ZTNhMjFmNWMxZjRhZGRiOTcyMWY3MTY4OGY5YzNlN2M3YTk2MDk0MDMyOTdjMThmMWFkY2E3NiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43710044\", {\"maxDepth\":0})</script>\n"}}