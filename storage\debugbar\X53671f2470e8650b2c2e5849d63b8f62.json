{"__meta": {"id": "X53671f2470e8650b2c2e5849d63b8f62", "datetime": "2025-06-28 16:01:11", "utime": **********.975032, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.569181, "end": **********.975051, "duration": 0.4058699607849121, "duration_str": "406ms", "measures": [{"label": "Booting", "start": **********.569181, "relative_start": 0, "end": **********.900862, "relative_end": **********.900862, "duration": 0.3316810131072998, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.900871, "relative_start": 0.33169007301330566, "end": **********.975053, "relative_end": 2.1457672119140625e-06, "duration": 0.07418203353881836, "duration_str": "74.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50302368, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0099, "accumulated_duration_str": "9.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.930006, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 15.657}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.939974, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 15.657, "width_percent": 5.657}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('100', 0, 22, 8, '2025-06-28 16:01:11', '2025-06-28 16:01:11')", "type": "query", "params": [], "bindings": ["100", "0", "22", "8", "2025-06-28 16:01:11", "2025-06-28 16:01:11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9504101, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 21.313, "width_percent": 27.576}, {"sql": "select * from `financial_records` where (`shift_id` = 52) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["52"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.954808, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 48.889, "width_percent": 4.848}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (52, '100', 22, '2025-06-28 16:01:11', '2025-06-28 16:01:11')", "type": "query", "params": [], "bindings": ["52", "100", "22", "2025-06-28 16:01:11", "2025-06-28 16:01:11"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9565969, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 53.737, "width_percent": 23.939}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-28 16:01:11' where `id` = 22", "type": "query", "params": [], "bindings": ["0", "2025-06-28 16:01:11", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.959988, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 77.677, "width_percent": 22.323}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "success": "Opening Balance has been set successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-1892949171 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1892949171\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1357380402 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1357380402\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1984266647 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1984266647\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">67</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; XSRF-TOKEN=eyJpdiI6Ik1HMGJ1anhKblV0NkwrQ241Q2t6U0E9PSIsInZhbHVlIjoiRG9QQVFjanI3Z3ZNWjJxWkhENHBzVk9odDIvZ3FqYnliWHRXTmRLT0I4eENLbTRPYTVIQ3hDZnhLVkpKWGJtbzRmSFAzL3VhWkl1bGQ4ZTd5czBKamRXTm9VT2lWdjlCMDVrT1BycmtPSjhvZERiQXg4YmlablliYXc2cVZVUGhGWmo3N3FUcEZPSlFaOEVtdk5ubFg2RW9kRkpQZEl5WkZ2b3IrR29WVi9XaTVFWTRmaW9aVTJibThDWFdOVmNHbHpHWU1zbHMwZkpLMUFjbUR3QjErMUZFdGdkeDJvaERmdHVobXRxNHk3SmxBK09BZmNReWFnWmZtdXRaeHpMbjdWTENITk9yTW8wdHRneGxPRHJLTUZRSVhWWXBINDF5OVFjTit3bmlYTXROdFJkZHFRNVpDdEt1dlVFbTU3OWg1YlNnUjBDdDdrTmtkcFEzTGxpUjY0dUxnczdaQlczZmRPcVBGb1Rubi9HTkFQUHhxZjFwRnZyQ2ZjbDlNaVU2cm10Z1dYWElhWHRHcGkrVWJZL3dkc3BrOTBTYTZYZU1tSy9RZjhXNU9ZSXRrQlk1azRMR0x0WWN0ZWI3ZlVUcE5tdS82ZlkrM3hNZlFHajBvbWlDS29yZzRvRVFNQWJ0RXZVRUZvR2JHSzRrWXk1bnpBNmVTNHc1bG9xSzFDcmsiLCJtYWMiOiIxNTczOWMyNTAwMzMwODRhNmM3MmE4OGFlMjNlMDVhYWM2YTNlNzI5ZTY3Njk0MDk4YzhmZTVkZDEyM2NhOWQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImM0NDU4UFRSUDV1K05lUWI1L3dPcGc9PSIsInZhbHVlIjoiN0ZIY3d0SEx0aWRyWHlLWXhjRWVaR1pQQjkranBjVE9HbkpKbTErMnc4ZUpsaHhRY1N2V2xUa2tsL3hRT3QydUFFRTE5TFN5T3RpSHlkbzk5NkNvdTBvU01GSzJSVG8xZ2VRTE9pTkp2M1ppRkpMT2tKbXN6ODNVdWRWWVdzMnZUMlcySWQ5cUNjNDNRemEyaEZDemxRdXVzS256OXR5ZUhqUGtsKzJVVitZQnZMVU82TDg5dzd3aElmcGpCWnJsakN3NlRpR3ZFU2N2TEdCaWtpOEs4dE9ZV1ZiTmhBSEJJVjl3NG1WUTl2K3ZYQ2cxTnJHM2hnZ0NaWDJHRm9ucHEyNWlZVkVuRUdwSjYyQ3czNGl6OTlTOFg2a3Rhd09ZTTFuMGtXYVZoWWtWVXNBbDZ6aUV1c09xQTRrNzhGcWt2TC9Vd3NsZzQ4Z0Fhc0hKc3JteTJiQU9KQ3pCSkYzMDEwNnp4TmVJcmNiMm1DaU9zUFZuTzFBNGdPMVZLYzNnaDAyUC9pSCttVExyaUpvcjNBM3U0YUNlQ016VEttL1R0K29KeHZFclpGVlIxTlprS0FoZ3VLZ2IySjg4bG50eEJjQ0hMQnp1c3hvRWVxdGh1SER2VzVNbHdZRDd6ZXpXS3FucFJ4ekQvNFllWkxNb0dDQytBUUtXVGVGYVJsS2ciLCJtYWMiOiI3MjdmNzY1NGRkYWUyNjcyZDI2ZGRiMGE2Yzg1ZTMzNDBhNzk3MmMxNTI3MjdmNzc3ZjY1YWQxZDJiODcxNjRjIiwidGFnIjoiIn0%3D; _clsk=1mcm0n0%7C1751126468701%7C7%7C1%7Cz.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2053956729 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053956729\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1906545711 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFwc0RTME9tV3o5ZWI4Nk5QNEcxL2c9PSIsInZhbHVlIjoiYjMxNGk5ZXhXNFBJS1NtcXlNazhSU3M2SVpadUVka0lHQVpTSHpMWXhQZkFRWTRZblAwSk11V3pzM0NHS1pzNGRYckl2TUZzMnY1Z09rNnZoNnhFSE9nVDF1UjYrbm9EMEMrbm1hQXhXQW5ueXFkMzVLQVFxMENTWDNMWXhRbm1ERzRnemdSb2hwSG96T1Y2dC9vcnYxZUFpRllka3hMKytCcEQzRStoRGZWeDRlUHY0MjM3ak5FeVQzNWhZL3h6MDhYd3QvMWNKaEkrS2FyRDBHMGpTWGhhdkU4OEE3eHVyWWpibXJrTUVPellxTWhpdzdEd2ZxblB1UFRsNXBuSnhZOG0xbXYzeXVIVmFwMFFUYWQ0b1pLVzBSMFRraFlKWU93R1FpalpsdFIyR1VoYjdSaTJQcDFwNXJSUUFUWnQ0VW9QQUUza1R3S0N2ZGI2a1FjRTVtUlFXRWlZZnJvOE5SaUJiRWZ4aVNnb2Z5bVcyc2pmazRRNktpekhQWEFUMmhRNS8yWWU5QUpsdVgxZk03UUNMbE0zRnRPUUFrdEhzVHpCNDBoc3VLenVnc3ZyRC9Dd2ZvUGUrTjVwbXA5UnNCUFVFS01IRUFIRHlRcmplYmlTZThuKzZNTk8ralBvSmhGc1NoSWFJZnM0MVpVcEp6cmx6MUNNWis4LzNraDMiLCJtYWMiOiJkYTQ4ZWZjYWE3YWVmZmZhYTY5MjU1NjA1M2Y5OGVhOGYwYmQ2NjA3MTAyMmM1MWFlMTI0MzEzMWUwZTJhZGYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im45clYvT2RxU2NRRzUycmtlVzdLL3c9PSIsInZhbHVlIjoiRllEVmtEbFBFTmx3MXBkeXJjK2g4YTQ3UzFOZ2F2UHd6REJBM0YyOGVLOGU2a0xPWnpnaUE5MzRobHZmQzNENnNrcFAvTCt5WncrOE1BWWI0ZStISnZlRHAzV25lYm8vR0FEWnVoUTRWc3l6NkVqWFNZMUtkNUNjdFRyS0lBKzQ0QWVNNG5WaFVTaEQzZ2xudzk0dHE3RXJ2Y0x4YVZqV1p1a1VvNEszOGlhNFZGRXp4djMvMmZ1U2Q4MC91QTl3aVJ1RGZQV1BnUm1QNi9PZmtPUUNzTGdHV0NTQ3pqMzJJSHhJbDI0dklGWWNNUTdGeVlJVTA5Y3R6TE5kenFkWWhYZUY5MGJ1eVU0YUpnYWlGNEVoaFhZTEIyYXJKd3dXdHhSdW9hM2pwVzJLejIzY1QwNEwrRDlZUUFBQ0YzbzhnOTZFbXdaYzUwVThFd1ViWmxnTTlKeGJiZHZsT00yWHk0STVOZm55cGl4OStOUklWamFmSzMyTFlvVDJNYkJ6eEV3TWVTSi85c0pKZTQ5TGRNQ0dDM0FDc3o4WjVBVDM1a1RoZ3FPWUxCZ2t5eWE3eUNxNmR2VXBYWXJZYzlZcG94ZE05Qm1PNmlBSnF1c3U3ODREb3J1M2dnazBUaHlDYndPVWwrS2p3Mm1LZGJMMUhnNE1hUk1kK1lYM3g2L0MiLCJtYWMiOiJjOTdjODExODlhODNkODg3MTFhYmNmMzdlNDk4OTQ0ZmQwMDgzNjMyNjk1ZTM2MzIyYTk0OGU1NzdjNGZhNTlkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFwc0RTME9tV3o5ZWI4Nk5QNEcxL2c9PSIsInZhbHVlIjoiYjMxNGk5ZXhXNFBJS1NtcXlNazhSU3M2SVpadUVka0lHQVpTSHpMWXhQZkFRWTRZblAwSk11V3pzM0NHS1pzNGRYckl2TUZzMnY1Z09rNnZoNnhFSE9nVDF1UjYrbm9EMEMrbm1hQXhXQW5ueXFkMzVLQVFxMENTWDNMWXhRbm1ERzRnemdSb2hwSG96T1Y2dC9vcnYxZUFpRllka3hMKytCcEQzRStoRGZWeDRlUHY0MjM3ak5FeVQzNWhZL3h6MDhYd3QvMWNKaEkrS2FyRDBHMGpTWGhhdkU4OEE3eHVyWWpibXJrTUVPellxTWhpdzdEd2ZxblB1UFRsNXBuSnhZOG0xbXYzeXVIVmFwMFFUYWQ0b1pLVzBSMFRraFlKWU93R1FpalpsdFIyR1VoYjdSaTJQcDFwNXJSUUFUWnQ0VW9QQUUza1R3S0N2ZGI2a1FjRTVtUlFXRWlZZnJvOE5SaUJiRWZ4aVNnb2Z5bVcyc2pmazRRNktpekhQWEFUMmhRNS8yWWU5QUpsdVgxZk03UUNMbE0zRnRPUUFrdEhzVHpCNDBoc3VLenVnc3ZyRC9Dd2ZvUGUrTjVwbXA5UnNCUFVFS01IRUFIRHlRcmplYmlTZThuKzZNTk8ralBvSmhGc1NoSWFJZnM0MVpVcEp6cmx6MUNNWis4LzNraDMiLCJtYWMiOiJkYTQ4ZWZjYWE3YWVmZmZhYTY5MjU1NjA1M2Y5OGVhOGYwYmQ2NjA3MTAyMmM1MWFlMTI0MzEzMWUwZTJhZGYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im45clYvT2RxU2NRRzUycmtlVzdLL3c9PSIsInZhbHVlIjoiRllEVmtEbFBFTmx3MXBkeXJjK2g4YTQ3UzFOZ2F2UHd6REJBM0YyOGVLOGU2a0xPWnpnaUE5MzRobHZmQzNENnNrcFAvTCt5WncrOE1BWWI0ZStISnZlRHAzV25lYm8vR0FEWnVoUTRWc3l6NkVqWFNZMUtkNUNjdFRyS0lBKzQ0QWVNNG5WaFVTaEQzZ2xudzk0dHE3RXJ2Y0x4YVZqV1p1a1VvNEszOGlhNFZGRXp4djMvMmZ1U2Q4MC91QTl3aVJ1RGZQV1BnUm1QNi9PZmtPUUNzTGdHV0NTQ3pqMzJJSHhJbDI0dklGWWNNUTdGeVlJVTA5Y3R6TE5kenFkWWhYZUY5MGJ1eVU0YUpnYWlGNEVoaFhZTEIyYXJKd3dXdHhSdW9hM2pwVzJLejIzY1QwNEwrRDlZUUFBQ0YzbzhnOTZFbXdaYzUwVThFd1ViWmxnTTlKeGJiZHZsT00yWHk0STVOZm55cGl4OStOUklWamFmSzMyTFlvVDJNYkJ6eEV3TWVTSi85c0pKZTQ5TGRNQ0dDM0FDc3o4WjVBVDM1a1RoZ3FPWUxCZ2t5eWE3eUNxNmR2VXBYWXJZYzlZcG94ZE05Qm1PNmlBSnF1c3U3ODREb3J1M2dnazBUaHlDYndPVWwrS2p3Mm1LZGJMMUhnNE1hUk1kK1lYM3g2L0MiLCJtYWMiOiJjOTdjODExODlhODNkODg3MTFhYmNmMzdlNDk4OTQ0ZmQwMDgzNjMyNjk1ZTM2MzIyYTk0OGU1NzdjNGZhNTlkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906545711\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1768127468 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Opening Balance has been set successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1768127468\", {\"maxDepth\":0})</script>\n"}}