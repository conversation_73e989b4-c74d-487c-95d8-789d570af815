{"__meta": {"id": "X00f8512a99efb28339f22ffbaeb73959", "datetime": "2025-06-28 16:02:21", "utime": 1751126541.024221, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.613619, "end": 1751126541.024244, "duration": 0.41062498092651367, "duration_str": "411ms", "measures": [{"label": "Booting", "start": **********.613619, "relative_start": 0, "end": **********.964108, "relative_end": **********.964108, "duration": 0.35048890113830566, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.964116, "relative_start": 0.3504970073699951, "end": 1751126541.024246, "relative_end": 1.9073486328125e-06, "duration": 0.06012988090515137, "duration_str": "60.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45712280, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00236, "accumulated_duration_str": "2.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.992993, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.322}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1751126541.003997, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.322, "width_percent": 21.61}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1751126541.009289, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.932, "width_percent": 19.068}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "[]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-294601022 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-294601022\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1038513097 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1038513097\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1190390934 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1190390934\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-300586707 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126520354%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpzZ3VBaVZUOXA0cERKZmh3WkZkSFE9PSIsInZhbHVlIjoic1JiVUZ1Z3NzbGxHQ3ZWMVgvcUVZTHVYMmVLWTZtczRJWTlTUmtqd1AyRFc2NitsT1E1WkJ4TjBNQm1TYy8wcWhyaFJXWXlreDNOZDBPSVNEVi94Mkx0eGMxaStjMytNczZSMGZDUXJRcXBUdnM3YUI2a2pFeGZ6c2JMZDFTUmhCVXpEbUFlemp1ZFRTUk9mMCt1aGlVdG5RanRSMXBkVGFnNUt6S1dSSC9ZV1EzeVRLeGx6cFgxWm9OUTYreTEzR0JKRElSNzNZbEhueGwzek5GSVExRmFuVjM3clF4cStPZVY2NnBUTnc5YkorSHJuYVZoQlJib1RVMWdmMFJYZzRQRDhNMXZDRnVNUUtNa0hZMGhFTENad1ZZWlY0dWdQQ2hWYnVYWWhKN0t4b1hpT1NNSmtGdy9xT2JTWDdVZHdaaXR3dlVmNjdWWlRFN3Nzc0g3eE9jNVM1bXo2enBUK1R1SDFyZnkwU09KN1RZNDFOczF6bEJFeWY1WW1sZEgrQWpFRGFjTWNjd2ovRmd5Z2creTdIOEpqZGdRaG1lZE84TUlxY3RBNERoc3lPcHdlUXlRRUg2Q040KzBhZVQ3V2N4UDRLb29IeE5Pa04vNitXZVJZcU1XZUFKL0VqRUNldUliQzRvaHZOUUtnTHhrdER0RHA4cFF0blJlQmtCUlkiLCJtYWMiOiJlZjVlZTg3YWJkODQyMzU5OTI3YTRkZGJlOGM5OTBjODljODI0NmEyNWExYzk1YzdkODExNGRjZjQ4ZjA0NjUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlSR3ppb2xONjg0UzFwcEpaazl0MVE9PSIsInZhbHVlIjoiaEw1cUpvMmtYaFpUNHlQU2ptWVRrUUFaamZCK3lMRU1jU0N4bnM1eW8yYkZkWlcyWVYyRHE1citSWXlxbnM4OXBrZDBxVHlqWkozVE1PUFNkd3ljdGg4aUhYbExOZkN3RWdYcitzNlF3RG92RW9ra1lTT2p2WWYzWnV2M01TL0VPczBIelAxS1lNNEo3a0RkRkd2UGtLQU50UmVxM0t6QnBiOHliaXFFMjJoM0FGUjdhVE9LdW9Fck9ZMXRGekFkYksyNjdyMUIrU1p5dXVvaEdHQVVqUEZodjdhWEVZaGVZMkk0VGhBTkY3Q1F0QU1QeXpXK1NNUk5TdzVSZCs0UXZnZHIwTkE2V3ZYcEM1M0RBeUFyU2lJd3l1Zi9pUDlkSGlGTUNoaVZ5VEc2Mk5vQnZFYTl2OTlVTUxxM0ZjcXB1SHRGdG9KREFraTZjZndPOWtaWGpPb3YxY1pKdUU3a1hiNlEwUGtWSEFqTDRHSkRNUFFVMmNMU2gyNDNkUEhpYVlmTzl1emJFVmVscmhBNkI3YjZEcERpNzhyR0dLNnFyTEdWWlJ0VmpoMVV4b0lTVG9VV0JyTWVFTWNLZXU4TVBaY2FCMUpmRkR4ZnZiejhhdWNxN0VITTFLVDZqRWt1Mis5VDdKemdMd1ZGekt6Z0JwbVFMdUZoUmhQeWpmWUkiLCJtYWMiOiI5ZWQ5ZTdhY2QzYzFhNGIyZjE5N2RlYTkzMmRiYmQxNjRlNzJlZDE5OGIyZDZkN2M4ODNmNmY3ODU2MjQ4NzkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-300586707\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-571164269 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-571164269\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-147423961 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9JVEVUbldpa0l3QnRuN0Y0NzMrU2c9PSIsInZhbHVlIjoibGV1c0JybzAyNXJQYmVibFVNS1pMM0tja2E3TmRzMVZXL0Z1bWwxUkdqQ3E0MFcrbFRwWnVrZS9WNjNlNlhOckNrQ0ZtdGpmTFZWb0lGT3J5NWVBU2l5QTROKy9obVY4a3FXall5M1ZoZjVrVkMydXl2YnI4QlZGMStiYllra3packJLRmM0R0hJR1RGMDNqK0xUSENEcExmZ3VCd3hEK3Q1RHBMVTFzNVVwYTRaUTFWMTY5cVdoS2M4SVBnVHNhTnRHaHRPTk83UExkVWVKcTdGWUFaY3lOMkN2bTlBNEdqa01IekI4MlY5eDAzdTBTMnFacmY0MVRUZ2J6SWM0ZjQzb1lNVU0yNk1aYkN2WGFaam9XMDgxRU9XS3VpYlh2R2pWNlpFc2tLS0pCbnlrcFlXMFNSU2lJblIzZEJLOUdKNHc4YzJpZWNqSHA2dCtJa3ZNczBScnB6eURUSC9MMHdwd1QxOUpLRkNNbmdLakRvMkgrYXVCU2t4Y0pLdU1iY3U2aTlpcVdKWWk2NjFNa2h5SmJCZTl2YjMxOUZWdGdYZmpMTThZUjc5QkprQi9JVm9oNlBHOXJkMWtMTmdtL0k1bmdYb3RhSjV0bG5xMWlBUStraTkyNW1qYVhRaERxcWZpRE9ZUFpaRW5VUkZFK25xbEp6YjZySTFKTk91YTUiLCJtYWMiOiJmMTljODdiNzNiNDc0MGM4M2NhOGIyYTE4NDBhOWI0YzI4NjA1MDBiMjNmZDE2YzJjOGQxODBlNDQ3N2MyYjM4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii8rUVNod2xYamh4Nzd2dFNBTDBmMmc9PSIsInZhbHVlIjoiQUQybkFFdkttQmNPRER3bnAvdkJkNkVvU0thdWZIcXFBUC9uWE1rVVM4MFdSQ1ZTbm02YnpjTXVPWmJjS3B0Z3lvdnlGYzA3MXdsTS93bFpMVEtTMkduWEpuaGRmZ0JiaktESXZMT1F1TW1zclFZYUR1TXR4WlZCSnlDeTl6QjFmNyt5MGhFY2JtS3pzREszdmttRG1pRXZ6OFhmcnh2cm9Ec21lT0xmOEYwQnI0QVNPQ0QyakJDL01nY0k5TlRBUTZyMnp5UlFuYVhpMTlSNVI3NUxTb052TXc0Q25ZdUZhUEV1TE1mc2x6c2ZkWlVoNkgwZXBja2EwbnBHVlczTHRYVmNRcEErMEFtQnB4MGQxMzZjV3picm4za0dVRVZaQ0NvZGNOOGhqejJQdDNzbG5IZUJYN2dDT2c0ckdxME11UkxidmRnMlR3bFZPa284cFljUEcyVU5wb3NNaWFKd2dqVGdITVd6aDlpOWNWazlyTTBHdURScml6Nlg5a1F6MXFoSHBxTVRuM1ozcDNMZ2RQcFhuajF5aWVUeVRza0RLbTAycFY1ZGVEcnJLQXhJWUdCR1UzZUhQNTVDT0crVGpjdjJBVEduOCtMb3pqN1p6anpkaFoxZWlTK0VsL2NpRGlPS3I0VFpXYmF6VFk2QmJXQTVKTGloeDM1eDByRTgiLCJtYWMiOiI1MjliYzJjYWZmZjM5OGQ2YmIwZjc5NzJhN2EwYzQ3MTNkODBjYTMyZWJiZmU2Zjk3ZjBlYTBjODllZmViN2VlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9JVEVUbldpa0l3QnRuN0Y0NzMrU2c9PSIsInZhbHVlIjoibGV1c0JybzAyNXJQYmVibFVNS1pMM0tja2E3TmRzMVZXL0Z1bWwxUkdqQ3E0MFcrbFRwWnVrZS9WNjNlNlhOckNrQ0ZtdGpmTFZWb0lGT3J5NWVBU2l5QTROKy9obVY4a3FXall5M1ZoZjVrVkMydXl2YnI4QlZGMStiYllra3packJLRmM0R0hJR1RGMDNqK0xUSENEcExmZ3VCd3hEK3Q1RHBMVTFzNVVwYTRaUTFWMTY5cVdoS2M4SVBnVHNhTnRHaHRPTk83UExkVWVKcTdGWUFaY3lOMkN2bTlBNEdqa01IekI4MlY5eDAzdTBTMnFacmY0MVRUZ2J6SWM0ZjQzb1lNVU0yNk1aYkN2WGFaam9XMDgxRU9XS3VpYlh2R2pWNlpFc2tLS0pCbnlrcFlXMFNSU2lJblIzZEJLOUdKNHc4YzJpZWNqSHA2dCtJa3ZNczBScnB6eURUSC9MMHdwd1QxOUpLRkNNbmdLakRvMkgrYXVCU2t4Y0pLdU1iY3U2aTlpcVdKWWk2NjFNa2h5SmJCZTl2YjMxOUZWdGdYZmpMTThZUjc5QkprQi9JVm9oNlBHOXJkMWtMTmdtL0k1bmdYb3RhSjV0bG5xMWlBUStraTkyNW1qYVhRaERxcWZpRE9ZUFpaRW5VUkZFK25xbEp6YjZySTFKTk91YTUiLCJtYWMiOiJmMTljODdiNzNiNDc0MGM4M2NhOGIyYTE4NDBhOWI0YzI4NjA1MDBiMjNmZDE2YzJjOGQxODBlNDQ3N2MyYjM4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii8rUVNod2xYamh4Nzd2dFNBTDBmMmc9PSIsInZhbHVlIjoiQUQybkFFdkttQmNPRER3bnAvdkJkNkVvU0thdWZIcXFBUC9uWE1rVVM4MFdSQ1ZTbm02YnpjTXVPWmJjS3B0Z3lvdnlGYzA3MXdsTS93bFpMVEtTMkduWEpuaGRmZ0JiaktESXZMT1F1TW1zclFZYUR1TXR4WlZCSnlDeTl6QjFmNyt5MGhFY2JtS3pzREszdmttRG1pRXZ6OFhmcnh2cm9Ec21lT0xmOEYwQnI0QVNPQ0QyakJDL01nY0k5TlRBUTZyMnp5UlFuYVhpMTlSNVI3NUxTb052TXc0Q25ZdUZhUEV1TE1mc2x6c2ZkWlVoNkgwZXBja2EwbnBHVlczTHRYVmNRcEErMEFtQnB4MGQxMzZjV3picm4za0dVRVZaQ0NvZGNOOGhqejJQdDNzbG5IZUJYN2dDT2c0ckdxME11UkxidmRnMlR3bFZPa284cFljUEcyVU5wb3NNaWFKd2dqVGdITVd6aDlpOWNWazlyTTBHdURScml6Nlg5a1F6MXFoSHBxTVRuM1ozcDNMZ2RQcFhuajF5aWVUeVRza0RLbTAycFY1ZGVEcnJLQXhJWUdCR1UzZUhQNTVDT0crVGpjdjJBVEduOCtMb3pqN1p6anpkaFoxZWlTK0VsL2NpRGlPS3I0VFpXYmF6VFk2QmJXQTVKTGloeDM1eDByRTgiLCJtYWMiOiI1MjliYzJjYWZmZjM5OGQ2YmIwZjc5NzJhN2EwYzQ3MTNkODBjYTMyZWJiZmU2Zjk3ZjBlYTBjODllZmViN2VlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147423961\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1737954931 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => []\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1737954931\", {\"maxDepth\":0})</script>\n"}}