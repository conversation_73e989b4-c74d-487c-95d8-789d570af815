{"__meta": {"id": "X2ca21c66c1157e9931b445ef470fc42d", "datetime": "2025-06-28 16:04:14", "utime": **********.87192, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.472456, "end": **********.871936, "duration": 0.39948010444641113, "duration_str": "399ms", "measures": [{"label": "Booting", "start": **********.472456, "relative_start": 0, "end": **********.817633, "relative_end": **********.817633, "duration": 0.34517693519592285, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.817641, "relative_start": 0.3451850414276123, "end": **********.871937, "relative_end": 9.5367431640625e-07, "duration": 0.054296016693115234, "duration_str": "54.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45712904, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026799999999999997, "accumulated_duration_str": "2.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.845284, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.164}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.854366, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.164, "width_percent": 16.791}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.85951, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.955, "width_percent": 16.045}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IlcwbnA1QzRLRXU0dWNUclJteE9TOEE9PSIsInZhbHVlIjoiRldBWXdUTEJXcFh3WnhYbEgyQjROZz09IiwibWFjIjoiMThkNDVlZTdmMWZmZTY0OTVkNTMwNzVlNzIxMTY5MzhkNDgxMWI1MWE1NjAxZGIyYzMyZDgwZTNkZmFiODZkNSIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-971297804 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-971297804\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1167185070 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1167185070\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-741373913 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-741373913\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1756838629 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlcwbnA1QzRLRXU0dWNUclJteE9TOEE9PSIsInZhbHVlIjoiRldBWXdUTEJXcFh3WnhYbEgyQjROZz09IiwibWFjIjoiMThkNDVlZTdmMWZmZTY0OTVkNTMwNzVlNzIxMTY5MzhkNDgxMWI1MWE1NjAxZGIyYzMyZDgwZTNkZmFiODZkNSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126652708%7C24%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ind5NFRwY3JoNGpZNXFIditORHRzSWc9PSIsInZhbHVlIjoiZi9ldG5jdGtvNTlDR3ByVFhrcFFtV3R5aDR5MngxODFMMURQcTZnRDMvL1pUN1BONTZ4eVdNY20yNmpqdEVzOWptZ3F6MzRRQ1hFbXFnTUNEWkFJMUNNR3N3UzNYVFBVakNHOGRWL3lITzBORndydVJYak5iT1FPMU5LQ0l4UXdEYlZxZ3lKN3QvYlNtckJCU0RvdGRSVDBJdlVGZWZKc29VeWgzOUsxak9jMzAwV1NwNVljVVdwVlpRR0pSTUVRMkhsUlpUVndyQlh4QzVkalh5aVZRd3I0cURoYnZGMVlMYmcrcHFPZGxXb1BPbHBvN05vQlBHOUtuMTE1NVR6QXdvalhJVU92TmpUQWR2NzdGQ3RGVkF1L2VTdWViZVlmZ3lYYWNxMVlEQ0pFbTZyMFV2Tkx4dEU4dy8xOEhjc3pKMFlhWjZiMFptZ0VUUUlCOGRQNVFRYU8zQjJhSVlsSWdDM1Q5TmRMYUE2aEdFV0Jtc2c0U1JPQjl6OFhoRDhMWCsvN0dybENxdU9lZHVJYzRONC9pYVlTajJmaDRUOWJENjZtZDc3cTZFcDNVVHNCWGxjQzNZbHgvcmcxdXlZWmMvVTY0Mm1YVzZjRXUzVExHb1UzZkpsNWc3YVAwWkNJOWZFZ3pPOTQ1TkxpcnNVc2RPUWFiOG91SmRzU01mWDgiLCJtYWMiOiIwYmNkMGU0NTA1ZjIzZWZmNGNjNDE4ZDY0MTU1ZWRkYTIyZGY4YjY1MjIyYzRiZmNiYjgzNGIwM2RlNGQyNGYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImF0ay80N0dVdnYzMExhN2cvN1NFbGc9PSIsInZhbHVlIjoiZVZIR2lQNG9TS1VmZDJXcEpNSklRN0dWMDQ5NUx3aWt1Y0pESFFFdmpTSjdpY2FIcVVoNVNnM1AzeWFmS0RPL295MU0yMGp0WktkQzJ0ZTIyYU9KdzJnamJoUVJPcnNockgwRHc3ZmR5MkFnZEVlZkU2bm93T2hwZk5pR095RE1YQzh1TE14bTJvMUV5TkdRcHA5Qlg4dC92WmhwT3gvdUJEa3NTaDdjdHBqSndrUXZ2WTM1OGRsN09mL1JEMzJySlVhTHdFbTQ5UlkzREtRSmRxWS9CL1RHM285ZzJBbHZjSFZ1OEtDRFRCN2FCTW5VdjdYQmNzSUpHU0RCY1F2dEVYKzJWRzdBZ3hUcFEwS2d1M21pU2Q2Vzd4eVR1UGpDanpBMnFubHlTWlhNbGF5V3VnQ3ZzRlVyV2hsbmZKK1B2RldJZzVza0N0V3JxK01MdUhUSlhPclpBZjNpY0dYb0RJWTdDUjVvL2oyUGJ6eGZyMGhBWGN5K2VVSW1DY2t1VkVmR0wrNHFkVzUraFFuRVRTUG1xaGFOQlRNelAva0VkbTQ0dmhJNjlUNUMrNldrZG9NQ2xEZ1pwMkFZMm5oTkNBdEVEMko5bXUyZ2t0Z296UmRGYXZvWG5Cak9XaldnUFR3c3lMdDU0c0FHdENoVS9QdkV5L0hwQ0NNNGsrSzQiLCJtYWMiOiI4YzFlZGE4ZWI3Njg2YWU1MDk5NzZkMjE1OTVkZDEyZTk1NTA5OWM4NTIzZTY5ZTVjOGNhMzhmNmM2YmJhZDNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756838629\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-159783211 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-159783211\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-29568592 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:04:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhFa2FsQmtZai9WYXB3VWVHTVZiSXc9PSIsInZhbHVlIjoic1NoNVMxNzZFbmV0d2RLR1pqVFV3Z1g2UjJmYTFqKzBNTlNKMFNpZ3g3NDJyQjR0b0VtUXMwekdmNFBKeDRKS1BHcGwvOE5MRGIyRmw3T3BVZWlZenJxaVZUbHd3bXhTQ05SbytwNzY1c09TaVVTd3pNaHEzd0QrUWZLUjZmbXJIT1RqVGVyU21KSkhyWmJ3Y3hSWkdsUmhsNzl3cWg2ay9TVVBaR3NMUWtTaXBhWi96M3cycms2azVySzdJSFR2a3RacllMK0hnMUxTRmZMbG5icmx3LzZuM2tSYjV5cU5xSXNSME1YaUM1VmZZbmpGS0tRb1o0SUF4Zll0R2IvRXRNZ3phemR5OENYTUxha0tSVWRTWnRkN0hkMStraEdLR2Q2QS90VzNzNWxwZ1djT1k0L2laaXUwMFpUUllQZFZNTmxLVjdMVWpxcjdVTDU0aDRtWVhjR0dWb1A5eFdVUVpYQVN6RUNONE4xanlLam0zdlNUYjJkUUZUNnVlaGUwTEx1ZTVJRzdlNkxyL1U1TUFZWXVONEc5VWYwb2ZKMmFTSVlESXNQRUlWV21vRC9QSTJ2cG4yWktGMEN6cXVlSVhubGNsZm9UcS9JQmxNbDZSM3g3ZEV5enVSTk4waDJFRDdITElyWmVTM3hSUXloY1Z1djk3RWVGbVJTamRCbUkiLCJtYWMiOiJlNTA3MzY1ZTQ5NWZlNGEzNTkzNjY4OTkwNDBhNWE0NDljYmQyOWRkODJjZmU0ODA2NDlmNjEzZGI2YTAzOWVhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhEMkxoNEgzbUFnWThTWGNMNCtJVkE9PSIsInZhbHVlIjoiRzg0VXFzNkZyS09xeE8yTjU0MTJiUjI3cnJuWXlRam1OWjRVYUw0cTlJTnFMV0VCbTNpTlpjV3VGbGdPTTd1LzNpTFpuVWJSbkhyLzNBSlhrSWNJT2hyN2VkQ2h3OEorRzhUN1QyL2hkV3lVWHRlWXJVVGdwa2ErMFpsZ3A3QkVpN2REaVUvZU5wNUZDWThob1RtRWNrcVBROFArWDdGREd4dTY0NTFHSkhvU3Q4TUt0N1VVWkRWTnFzNkFqZ0w4L2dvdHJXN29lVlBsN3V3RGlZK3E0cXRVUm05V3RqN0RJYkc3MlhqdTVmanNlbXluYUhneTZvRUk1dlZHTm1vMFVndU9BSTIrMXBLN000YlpNbkFhY3QySDUxbnNMYTVlK3JxQlF3S3RCeEVIUUduUEpXOU5ITTdqc20yclhYM3VNalFnb3c3RUpiSHpSZEg3aHREc0dnYlNHc0J1NUQyMHRDSEFJNVJxQlpnaFpacnROUGZVczlHN04vcGJkZ1E3YXFFT0pqVTRxcEFxcS9xcTBWNUZkaFp1cUpINFhhbzc4Nm5hcTFLZ1EyTWhWK05xd0RuV3Bna3VlU0VCall0MkRQMlhOdnp2ZFBiN0RyWlA5YmJEeUhWMmNPZVRGSXJPclJNYm5UZFhJN3dZeTFQdTNaYjB1am5wT3g4bmhYVVIiLCJtYWMiOiJmYjM5ZmJhNTViYWM3ZjQwN2I4ZjU0NWFhNTRjMTMyMTc2N2Q1MjVlZTRkZDBlNmUyZDc0ZDI2ZDk2NzUxMDdjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhFa2FsQmtZai9WYXB3VWVHTVZiSXc9PSIsInZhbHVlIjoic1NoNVMxNzZFbmV0d2RLR1pqVFV3Z1g2UjJmYTFqKzBNTlNKMFNpZ3g3NDJyQjR0b0VtUXMwekdmNFBKeDRKS1BHcGwvOE5MRGIyRmw3T3BVZWlZenJxaVZUbHd3bXhTQ05SbytwNzY1c09TaVVTd3pNaHEzd0QrUWZLUjZmbXJIT1RqVGVyU21KSkhyWmJ3Y3hSWkdsUmhsNzl3cWg2ay9TVVBaR3NMUWtTaXBhWi96M3cycms2azVySzdJSFR2a3RacllMK0hnMUxTRmZMbG5icmx3LzZuM2tSYjV5cU5xSXNSME1YaUM1VmZZbmpGS0tRb1o0SUF4Zll0R2IvRXRNZ3phemR5OENYTUxha0tSVWRTWnRkN0hkMStraEdLR2Q2QS90VzNzNWxwZ1djT1k0L2laaXUwMFpUUllQZFZNTmxLVjdMVWpxcjdVTDU0aDRtWVhjR0dWb1A5eFdVUVpYQVN6RUNONE4xanlLam0zdlNUYjJkUUZUNnVlaGUwTEx1ZTVJRzdlNkxyL1U1TUFZWXVONEc5VWYwb2ZKMmFTSVlESXNQRUlWV21vRC9QSTJ2cG4yWktGMEN6cXVlSVhubGNsZm9UcS9JQmxNbDZSM3g3ZEV5enVSTk4waDJFRDdITElyWmVTM3hSUXloY1Z1djk3RWVGbVJTamRCbUkiLCJtYWMiOiJlNTA3MzY1ZTQ5NWZlNGEzNTkzNjY4OTkwNDBhNWE0NDljYmQyOWRkODJjZmU0ODA2NDlmNjEzZGI2YTAzOWVhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhEMkxoNEgzbUFnWThTWGNMNCtJVkE9PSIsInZhbHVlIjoiRzg0VXFzNkZyS09xeE8yTjU0MTJiUjI3cnJuWXlRam1OWjRVYUw0cTlJTnFMV0VCbTNpTlpjV3VGbGdPTTd1LzNpTFpuVWJSbkhyLzNBSlhrSWNJT2hyN2VkQ2h3OEorRzhUN1QyL2hkV3lVWHRlWXJVVGdwa2ErMFpsZ3A3QkVpN2REaVUvZU5wNUZDWThob1RtRWNrcVBROFArWDdGREd4dTY0NTFHSkhvU3Q4TUt0N1VVWkRWTnFzNkFqZ0w4L2dvdHJXN29lVlBsN3V3RGlZK3E0cXRVUm05V3RqN0RJYkc3MlhqdTVmanNlbXluYUhneTZvRUk1dlZHTm1vMFVndU9BSTIrMXBLN000YlpNbkFhY3QySDUxbnNMYTVlK3JxQlF3S3RCeEVIUUduUEpXOU5ITTdqc20yclhYM3VNalFnb3c3RUpiSHpSZEg3aHREc0dnYlNHc0J1NUQyMHRDSEFJNVJxQlpnaFpacnROUGZVczlHN04vcGJkZ1E3YXFFT0pqVTRxcEFxcS9xcTBWNUZkaFp1cUpINFhhbzc4Nm5hcTFLZ1EyTWhWK05xd0RuV3Bna3VlU0VCall0MkRQMlhOdnp2ZFBiN0RyWlA5YmJEeUhWMmNPZVRGSXJPclJNYm5UZFhJN3dZeTFQdTNaYjB1am5wT3g4bmhYVVIiLCJtYWMiOiJmYjM5ZmJhNTViYWM3ZjQwN2I4ZjU0NWFhNTRjMTMyMTc2N2Q1MjVlZTRkZDBlNmUyZDc0ZDI2ZDk2NzUxMDdjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-29568592\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlcwbnA1QzRLRXU0dWNUclJteE9TOEE9PSIsInZhbHVlIjoiRldBWXdUTEJXcFh3WnhYbEgyQjROZz09IiwibWFjIjoiMThkNDVlZTdmMWZmZTY0OTVkNTMwNzVlNzIxMTY5MzhkNDgxMWI1MWE1NjAxZGIyYzMyZDgwZTNkZmFiODZkNSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}