{"__meta": {"id": "Xcbb31ee04ea32d8afee7ec9bd6dfbd7b", "datetime": "2025-06-28 16:10:11", "utime": **********.124762, "method": "GET", "uri": "/pos-payment-type?vc_name=10&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127010.649148, "end": **********.124779, "duration": 0.4756309986114502, "duration_str": "476ms", "measures": [{"label": "Booting", "start": 1751127010.649148, "relative_start": 0, "end": 1751127010.98565, "relative_end": 1751127010.98565, "duration": 0.3365020751953125, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751127010.985659, "relative_start": 0.33651089668273926, "end": **********.124782, "relative_end": 3.0994415283203125e-06, "duration": 0.13912320137023926, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53169656, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.077624, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1452\" onclick=\"\">app/Http/Controllers/PosController.php:1452-1560</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.0051199999999999996, "accumulated_duration_str": "5.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0200882, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 32.617}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.029726, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 32.617, "width_percent": 9.57}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.043484, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 42.188, "width_percent": 13.672}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.045591, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 55.859, "width_percent": 9.18}, {"sql": "select * from `customers` where `name` = '10' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["10", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.050935, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1463", "source": "app/Http/Controllers/PosController.php:1463", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1463", "ajax": false, "filename": "PosController.php", "line": "1463"}, "connection": "kdmkjkqknb", "start_percent": 65.039, "width_percent": 9.18}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1464}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.052877, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1464", "source": "app/Http/Controllers/PosController.php:1464", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1464", "ajax": false, "filename": "PosController.php", "line": "1464"}, "connection": "kdmkjkqknb", "start_percent": 74.219, "width_percent": 8.203}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 559}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1468}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.055726, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PosController.php:559", "source": "app/Http/Controllers/PosController.php:559", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=559", "ajax": false, "filename": "PosController.php", "line": "559"}, "connection": "kdmkjkqknb", "start_percent": 82.422, "width_percent": 9.18}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1547}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0694718, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1547", "source": "app/Http/Controllers/PosController.php:1547", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1547", "ajax": false, "filename": "PosController.php", "line": "1547"}, "connection": "kdmkjkqknb", "start_percent": 91.602, "width_percent": 8.398}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1523976009 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523976009\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.049707, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1270386188 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1270386188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.055147, "xdebug_link": null}]}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2306 => array:9 [\n    \"name\" => \"ذرة بريتز\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"2306\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 51\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1621867819 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1621867819\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1599486916 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1599486916\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1250016426 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126882941%7C30%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVuNk96OTdxR0JvUUxUNXJpWmpwNEE9PSIsInZhbHVlIjoiODY3cVB6WGJIbEJUTXZlV1JVN2U5dC9lbTFvNHBTeXJXK0E4OWNCKzdTbWdJWUVKWEg0dVRjWFF6SERzWW5pWnpIeDFUQVp1emNzbXg0Qjd2UWh2QnJ1ckZ0V2J6SnV3UGVGZnZvbUxrZWkzTDV4cjc5bmY4c1FVQnRHNVIzMjQvQmZRcWFLcE8zak9rREY3VEtEOVdiNktDNWJwMUhCbDJHdnpVbkhQSnJzeTVTWUFpaGI2ZEZ6VzJEOHQxbCtId01sbDBHSmt5QXZrSVJlNUlyWkpvQ3NYbEhId3l2WEdTUDEwd2lLbi96UERrcUsrWFoxdmVJMXM5bXNMVXhSVG5kMU11N0xXemRrMjQ5L1JkMUVIZ00yczUvaVV5amNKcThvZWZ3bTR6UHBsNXAxVU54YmhBVGVVNWJUY1l1aVN5QjRVb3pxYlhqMEVaZExpcEtvclRaUk1GdkQ1Z3plbE94K2drR2tRSm1rODE1cUg2R3FRZjdvRFdLN3pMc0pEb0xGdkVOdWN4MnY3MmFEL3FkMHpPKzB4eTNDbjFHZUsvR3pyZnlBVStndDJCNWVHenJrd1IrWFpubEEycC9aUklQWUNXWndWR3o4YlpLcEFuVUJYM25MQXJtT1VmN2QxUUZBbWhJUjgyZ1IxNGs0Sk5qeEhSUXM4a3NMRE1VZVQiLCJtYWMiOiI0ZDcxODE2YmI5NGUyZTNhNjcwOGJmMmM0MzEyMDUzOWVhNDEwZDBhNjY3NTI5ZjQ3YjcxYzAwOWEwZjhlN2UzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdkSCsweXlEMHhEb3hwTVpWTWtERWc9PSIsInZhbHVlIjoibExDMGY3ZUNBU0lYeUVLWnVJMDFMOStuTzIxUE0ycnU1dDRDRllvTDVxajE0eXNCSjE3VHMwMHcrY20vTU1wQy83TmlaZVQ4VXpyb0Yzb2swL3RuTHplVXI1cmp0bkI1VzdsNncrMGRPa2NQd3pIeFpEb3IwdG9GSEdNMXV6R05jS3VkQ0hYVVNqc0ZxamdJNDF3RHkxTE1OMEJSNzlyc3VIS05ialhVNkdzVE11RGMwL0lzMm95SmJTVTFrVW56SUs1djdtRFcrODNpaEpncFl0bmpGNVR1UkpkV3F4U1F1NXg0YzZ4ZVZ5OGc0RVM4NXU5Z1htUGd1elY0SE9nVWE1Uzh0Y1N4OUM5bzFnazFZb2hSR2tVZGFVMlZpSlhxMmFFQXl0dDJqWnAyT2huVG9sVk5tQXYreFhqTXBESWRWajZLYjRDRGVrTUxFSVQ5RnFSajVMT2FNQ3Y3dTBxRFZIS1ozNVdFRk5JVEFOTFMvL1dOM1pJdXhodXhEVytsZ1FrYXlmRzNuaWdpQjQ4aERHc3MxNWNuRGhOSm93bjBoYWNmOTRqalpJL280bk1FY1UzM2hpRmltdVFHVG5TMFVHUFBVSTJNZi9CYjM2NTZpV2tYU3V1dWtIeHVtWDNxT1V6Z2gwaFpKbW5VMk01d0V2TnpxUzA3Nk1WMTRTWHkiLCJtYWMiOiJhYzY2ZGY3NGExNjcwOWFkNzQ4ZjZiN2JmOWFkNjM0YjU4NTZhNzVmMjk0NzU1ZWMxODVlOGQ0MGExYjJkMGM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250016426\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-22342408 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22342408\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-268160180 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:10:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InljbXM5WkZjMTlTcFIzMzQ3N251L2c9PSIsInZhbHVlIjoiVzNmbWhEbnBuWFFPZUE4LzF6NjFiNHUyV3NrVzd0TnA1SVltQlo1SS8zR1REQ3hFN0ZuWExGVDBsODQ3UzhRTHB5LzJVelFCempKeWJPb3kzN3dzYTR3dmpTUHM3RG5LTGltcXBGd1ptVjZ0L2lTejBmQ3pSWjVmcnYwcjB2S3hpOEZCOWNVQ0NKSjdvRzdFeUM0TytseHlUWnByMlVKZDFsOTJyeGxnekVYZmZZNEsrVTNkVHJnaTRsY28rU3FoU1J0ZkF0NXlwMzBZQnFRazRKTk14M0dFc0JaaElOVkZYSjB2ZVp3bmQzQmxvNjM1eXBiS1RzWTE4aURwcmhqc2F0UkZMR3ZyREZRVkZwN01YdStNcFhacnIzZE5oQXJEUFZ0RWNGS3FHSTA0d0FPbk1DM1d2cjBYN2Z1Zm0wS252U1N0enpRNDI0aDdxVWdOMFRXSm0xYWJjVHRoZW1RSUdsdmg3bU5MS0t4akdGVWxUQnBkdS9QTWJ1dTJvaE1uLzkrWjkwZ2d6UVo3VFZBMUFIRU5pQlV5dDBOYjZTU2Zja1VEM0xUR2hKTDJQcEhDNjNUUWp3Ky9BRWo4WkhPMWxzcEdwRkJqazUrU1hlZmxoS2crcitFMmFGMVBTUC9pSE5XbDYxcVROM2x2YUhWdS9mc2VHbEpQaTBMVEJQclEiLCJtYWMiOiJhMDA4ZjA5YjE4NGIxNmRkZjI0OGIxOTBkMmQ1NzEzMDgzMmRmOWQ1NDBhMzY2MzI0NjUyNTI1YTI4NWEwYWM5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InY3MjdjTjhueHVmWGk0ZUtIV1NYS2c9PSIsInZhbHVlIjoiQ0dUc20xWThzcnl5SDM0Y3VIemplV1hrWGlORGVnVktNakkvVU9oNmJ4R2gvQkZEVnM3R2dkL2R3aGtXcFVHQVAzUUJ6MFBmRThQeHgyTXphbHNLZFFzWWlvd1Brc1o4bnBwdWkzQktHaFZ5NUREdGViQXh6MWVWbVBhOEhOVCtzSmI4ZHoxYmtOWG1lUnp2aW1EWlgvcUNzOTBnSU95aE13UnVrVStiWVhicEdFUlhWVXNjc3dRVk9vRE9YbnVzMGxvY0N0STRGQVRaWHBHL2VadlJTNno3WDA5V1o1YmJGaGU0N3llNVAxWSs5V1RsckRvOFgvaXU3dW01Q2J1RFRyMmVVbXJPdjh2bnJBM3dpT2ZaRDl5cU00aWNrMmwzWkhLYjdXNDZlTnRORVN4cTF3eE5YVEhrU0ZrSXZpUUp1aEI2RlpoaU1KTmxhUkJQSlB6QzFJUlhtMEdXajdLZ3RBV3dpSUlkME0yaER6ME1WV3FOeVI5MXJzMTAvOWtDeW16RnhVdGErVG5FSjNDTGo3ZTgrTDNGYVdXRWxpTmg4ZllwdlRrU2pIbEpJbW50b2phTzdycjZMUFMwdXdQRkk5YWRCVmRTWlc4cEQzRXl2bW5SZUorSXIyRjhYWEJ4QXRTQWwza1V0T2p5Zmt5enRDcTE4cVB6RFpld0Jkb2wiLCJtYWMiOiJhZjUyMjY2MTAyYjU3NzdiYzVjOWFjYjg1OWU2MDAzZjgxNmZlZjUxMzkxYmYzZDBlNDgyMGQ5YTkxZDNkNzAyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InljbXM5WkZjMTlTcFIzMzQ3N251L2c9PSIsInZhbHVlIjoiVzNmbWhEbnBuWFFPZUE4LzF6NjFiNHUyV3NrVzd0TnA1SVltQlo1SS8zR1REQ3hFN0ZuWExGVDBsODQ3UzhRTHB5LzJVelFCempKeWJPb3kzN3dzYTR3dmpTUHM3RG5LTGltcXBGd1ptVjZ0L2lTejBmQ3pSWjVmcnYwcjB2S3hpOEZCOWNVQ0NKSjdvRzdFeUM0TytseHlUWnByMlVKZDFsOTJyeGxnekVYZmZZNEsrVTNkVHJnaTRsY28rU3FoU1J0ZkF0NXlwMzBZQnFRazRKTk14M0dFc0JaaElOVkZYSjB2ZVp3bmQzQmxvNjM1eXBiS1RzWTE4aURwcmhqc2F0UkZMR3ZyREZRVkZwN01YdStNcFhacnIzZE5oQXJEUFZ0RWNGS3FHSTA0d0FPbk1DM1d2cjBYN2Z1Zm0wS252U1N0enpRNDI0aDdxVWdOMFRXSm0xYWJjVHRoZW1RSUdsdmg3bU5MS0t4akdGVWxUQnBkdS9QTWJ1dTJvaE1uLzkrWjkwZ2d6UVo3VFZBMUFIRU5pQlV5dDBOYjZTU2Zja1VEM0xUR2hKTDJQcEhDNjNUUWp3Ky9BRWo4WkhPMWxzcEdwRkJqazUrU1hlZmxoS2crcitFMmFGMVBTUC9pSE5XbDYxcVROM2x2YUhWdS9mc2VHbEpQaTBMVEJQclEiLCJtYWMiOiJhMDA4ZjA5YjE4NGIxNmRkZjI0OGIxOTBkMmQ1NzEzMDgzMmRmOWQ1NDBhMzY2MzI0NjUyNTI1YTI4NWEwYWM5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InY3MjdjTjhueHVmWGk0ZUtIV1NYS2c9PSIsInZhbHVlIjoiQ0dUc20xWThzcnl5SDM0Y3VIemplV1hrWGlORGVnVktNakkvVU9oNmJ4R2gvQkZEVnM3R2dkL2R3aGtXcFVHQVAzUUJ6MFBmRThQeHgyTXphbHNLZFFzWWlvd1Brc1o4bnBwdWkzQktHaFZ5NUREdGViQXh6MWVWbVBhOEhOVCtzSmI4ZHoxYmtOWG1lUnp2aW1EWlgvcUNzOTBnSU95aE13UnVrVStiWVhicEdFUlhWVXNjc3dRVk9vRE9YbnVzMGxvY0N0STRGQVRaWHBHL2VadlJTNno3WDA5V1o1YmJGaGU0N3llNVAxWSs5V1RsckRvOFgvaXU3dW01Q2J1RFRyMmVVbXJPdjh2bnJBM3dpT2ZaRDl5cU00aWNrMmwzWkhLYjdXNDZlTnRORVN4cTF3eE5YVEhrU0ZrSXZpUUp1aEI2RlpoaU1KTmxhUkJQSlB6QzFJUlhtMEdXajdLZ3RBV3dpSUlkME0yaER6ME1WV3FOeVI5MXJzMTAvOWtDeW16RnhVdGErVG5FSjNDTGo3ZTgrTDNGYVdXRWxpTmg4ZllwdlRrU2pIbEpJbW50b2phTzdycjZMUFMwdXdQRkk5YWRCVmRTWlc4cEQzRXl2bW5SZUorSXIyRjhYWEJ4QXRTQWwza1V0T2p5Zmt5enRDcTE4cVB6RFpld0Jkb2wiLCJtYWMiOiJhZjUyMjY2MTAyYjU3NzdiYzVjOWFjYjg1OWU2MDAzZjgxNmZlZjUxMzkxYmYzZDBlNDgyMGQ5YTkxZDNkNzAyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-268160180\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1183953739 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2306</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1584;&#1585;&#1577; &#1576;&#1585;&#1610;&#1578;&#1586;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2306</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>51</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183953739\", {\"maxDepth\":0})</script>\n"}}