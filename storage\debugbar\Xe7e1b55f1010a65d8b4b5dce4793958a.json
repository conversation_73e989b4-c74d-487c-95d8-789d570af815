{"__meta": {"id": "Xe7e1b55f1010a65d8b4b5dce4793958a", "datetime": "2025-06-28 16:30:38", "utime": **********.116584, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128237.648229, "end": **********.116598, "duration": 0.4683690071105957, "duration_str": "468ms", "measures": [{"label": "Booting", "start": 1751128237.648229, "relative_start": 0, "end": **********.044652, "relative_end": **********.044652, "duration": 0.3964231014251709, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.044661, "relative_start": 0.39643216133117676, "end": **********.116601, "relative_end": 3.0994415283203125e-06, "duration": 0.07193994522094727, "duration_str": "71.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45715560, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01543, "accumulated_duration_str": "15.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.075787, "duration": 0.014539999999999999, "duration_str": "14.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.232}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0995789, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.232, "width_percent": 2.528}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1055791, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.76, "width_percent": 3.24}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1157906992 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157906992\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1686083804 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128229272%7C49%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imd0dy90UEZ1TnhQbi9zUHBVd1o1aWc9PSIsInZhbHVlIjoicDNvamFlWld0RmFVUDFKWjlwTThXVmRBak0xQXpBVE53eTJQYno2a0pMYmFjQ0ZjbUF4NE1rb1JaMjh6aEdEbmJwQlpHRDI0OTBRTVlhRkVhWVdVRmVQTmpKcVZyQnpFZ0laTm1sSmF0MUNnL0JYUmNBdWN3bXNadE9Ec3Q4RE9RcHFObmhQd0dLL1ZiV0hTeXVHYTA1bmlGZlFVS3QyRXZoTzhnTWRSK1lQOWFnZHBrY3Z6USsxWXhxZjYxK01EOVFnaGlqS1NwNFVYbnd0WFZKUzB1RytaRmg1RnhCVk00QnAzNVRjRml1MUlrWjlhY08yTVcxSXM2RHRMeG9hbzNFK3psYmpSbFlQcHovYW1wemh1VjJEcXk0d2JYdDhWWVVZTWlDTVE3cnJxcEdWZCt6cHBlNnp0QUhLRnZpenBlOXNZT2hoUmI3NXFoUWVxU0ppaTFKbUczaHJhdnM0SDllNXI2TnhNVzBqZHorREwrV2U4QTlsT2JLd1RXL0J2MDBNRWRqWGwzNGE2ODhQZzNiQTBkd3NZOTlJYVlPN05Sa3p3QlNnall3ZjVHSmo5RXB6WHFBSGE5c2lTOWgwcWVaS3hOTHk0dzhoUStaNzMvVjJDSUJWQ2E5WHloK01kUTZUa3d0eDhkT3ZaSGFrV1IvWnYwU0Y4cUpsdFo0NmciLCJtYWMiOiIxMGRiNTdlNWM1OTdlY2U0MWJjY2ZmMjBkNmY1ZjgyMGE2ZTEzMDg4NDkxMmVhZDc3OTRkOGIzZmI1NThjNjg3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjYwcWVYNE02eG5VYWVST0NITTdhNkE9PSIsInZhbHVlIjoiaThBTHJPVWVkdVRBOG9TTzI1SFYyWlFESy9ZSy9SellYVEdGY1ZrQ3Z3QTM0dXNWazRkTnVRRU5PbEJTenJCS1cva2Fya1ByUE1iQ1ZrcWd0QVlUZVZFS0JFYndaUWNmVldoS0hzN1psakZ2OHpqTTNVbmtmdU5ZSlpseFE1RGhFWk01MTA2WE8yNTR5YWozdW9NNEF0dXpSemd4QW1qWk12UlZVS0NBWHZycFlRU3JSUWlMR3hWTXdUUFlyMEFzM0RCaTlDdC9hdGFpOElqdTlOQkQyc3ZmUnJMVURUMVBBVTU0UFNZTkkwdkFBWGtoR3JDT0dWUmVPeGlKUVJuSklMTkgyRzM4NDlRLzU2SW1QQnErUTBybWVZOHhnc3laSUc3ZFhSMFd0TmVBbDZoWEFCaXJiZnRSOUhha1o5Z1g4RWoybWxLQXJQeXBUMVdraG9ZMkFKaVZKbmtIWlcrOFRDakU0VE9qVFVmcjVKWFlvZGZRdDZIV0F3QzFiL3BRamtiVHdmbDA5WkIxTG9xaStsY09JclVncVNIMUJWbTdPRXYzTDlObkNyNzFlSGZuRlo5azUxMHVsQW5aREgwbEQxZHk0N0xHUllmV3pjSkkyYmFJaURXdHJlUnE4YmhlUXZ0cGV5eXlpcTBTQUhJU1FST3VYTVlKTS8zaVlDRkkiLCJtYWMiOiIwYjFiYzU0YzJjM2M2ZDRiMzFlNDQwMzllYzQ3NmIzNDY2ZTU4MmQ0NWU2MDEwMTY1NTRjNTYxYWJmNTc4YzgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686083804\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1433986841 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1433986841\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1050860938 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjAxZzhNV2VYcUhHQWpKbmkvQlNxRFE9PSIsInZhbHVlIjoiTTJwclViZXNwREVqYWd0eVNwdE1mSHduMjRIRXdLOU9BdjduNjZNM1V4WTJRM2srK3J0SzBOd1RjT25XV0Q0NVVYLzloRmM2S1NGRUl1UnI5dFhIY29walhJMGJGL0dGZitBRmRWZGdxM05WeHk0UGVMeVVBNXBIRHd3d0l1dWp5TVZIK2JNMmpmMml2blRJekNZZ0RRZUpRQXN0d0Z5bkRJWVhua2Zac1NKYnJFZWM4UXFpTDEvTXNnc1RHZ0EvdFVJaDBXOW9WTVFzU3I5bjBJemVrWGJrdnVIelI3RjhwUTYwdzROY2Jqb1ZINzRTeDFpbUFVL09aV2VzTkdMY2pRSk0zZXlmZDNpU1lnVEs2c0tEYmIrL00xa1pHcHA0UXVqM1J0VVM0RG9tdFNXR0dHL0M2cFlQa0pLZ29lUWdxR01YV0VBUW1FdThjalBMZ0haNVlTL0ppNFRDMWJUY0VrWWUxckgwekVJZy81UWM4dmJ1RzhBQVVObURyQUZLaE43TW9RK08xaUpDdUE0RzQzRmNHWk1UeDhDRENBbm0vZlg4Q0FocHo1SlZEZlBTV3U2aHRId0JKVlJKMUNlUHZyMVZaclgxTnpyeHluMDhBWitVdnNQV3RPdzBHd2YxWVFya21rWkxBd3gxcHQvOG5YcUZMb0tUQ0llekN4VE8iLCJtYWMiOiJhOTQxNTI1MGM0NDA2M2Y3MTc3MGUyYWE5ODYzNTdmM2M1NjBiZDQ1MTlhZWI2MjVkNDc3ZGYyMWRmOThhNWZhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVMcENGSXRLdEcyQ1liaEZMZ2hYU3c9PSIsInZhbHVlIjoiaFR6cnE0OVRHWFVjek5BRGs4Y1grS0x5QkxsRXIzNkZEMlVsa1B3MzgyOE9QVVY3TktaUFNFbHN2aTBkUWN6RmlSbU1IdXkwZEhtRkhvU1NSOXIxYVlTblB4RUIvWHdCUis0cjdUMGlSMTZ5a3JRTmNqYlRmTEJTRUxQNzA4Qm93OUpzaVFBNFB4dU1mNnlCYUJCYVNlN1ZLbDRGZ1VNR1B0NGlmZUhYUUtJTlViT0lmZnNPWThRUUtiWmNOenlKZE9kbjhTa1grclFiRVVWTnFEZ3ZSK01BMkVYTktiUXdiS1RlZkpTczNnZ1ByRzd1TmpFQk5abzhOekwvZnFoS3N1WGhXdUVmbWZZSWNKUUtaYUNvRURtWjI4QVhIS05HZDFYSXhZakxYTnlZcUVFMlNkUWc0YS95T2l1MGNWMlRsNUY2VVJ6eUVsWmgvVWNabUpYOWdRckM3RS95Q1M5L3NxbXRZWUkxb3g1NGd0MDA5VzErYk1MSzlobm9Yc1d1SkoyK1JWd3hUSFF4NjZUOGQzVFBhKzlQSk1RMVFnaGhzc2xvelVMUzRUU2xKOGplRSsyTlFOWlNXZkdSOEJGMW5iYWVrcXErSGdDdnlpZkR4YTJJN2FoVFpuMmlBL3Y0cnR4SVFTMFMwQTQ2Wm9HQzgxeFhrNjVjc3poT1RqT1UiLCJtYWMiOiIxYTVkZDNiNWM4ODUzNDRmOGY3ZGYyYzg2NDhmOWI2MDU5MTc2MDQyYTg1NjI4YjE1YTdlZmVjZTY0ZWQ0YWE4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjAxZzhNV2VYcUhHQWpKbmkvQlNxRFE9PSIsInZhbHVlIjoiTTJwclViZXNwREVqYWd0eVNwdE1mSHduMjRIRXdLOU9BdjduNjZNM1V4WTJRM2srK3J0SzBOd1RjT25XV0Q0NVVYLzloRmM2S1NGRUl1UnI5dFhIY29walhJMGJGL0dGZitBRmRWZGdxM05WeHk0UGVMeVVBNXBIRHd3d0l1dWp5TVZIK2JNMmpmMml2blRJekNZZ0RRZUpRQXN0d0Z5bkRJWVhua2Zac1NKYnJFZWM4UXFpTDEvTXNnc1RHZ0EvdFVJaDBXOW9WTVFzU3I5bjBJemVrWGJrdnVIelI3RjhwUTYwdzROY2Jqb1ZINzRTeDFpbUFVL09aV2VzTkdMY2pRSk0zZXlmZDNpU1lnVEs2c0tEYmIrL00xa1pHcHA0UXVqM1J0VVM0RG9tdFNXR0dHL0M2cFlQa0pLZ29lUWdxR01YV0VBUW1FdThjalBMZ0haNVlTL0ppNFRDMWJUY0VrWWUxckgwekVJZy81UWM4dmJ1RzhBQVVObURyQUZLaE43TW9RK08xaUpDdUE0RzQzRmNHWk1UeDhDRENBbm0vZlg4Q0FocHo1SlZEZlBTV3U2aHRId0JKVlJKMUNlUHZyMVZaclgxTnpyeHluMDhBWitVdnNQV3RPdzBHd2YxWVFya21rWkxBd3gxcHQvOG5YcUZMb0tUQ0llekN4VE8iLCJtYWMiOiJhOTQxNTI1MGM0NDA2M2Y3MTc3MGUyYWE5ODYzNTdmM2M1NjBiZDQ1MTlhZWI2MjVkNDc3ZGYyMWRmOThhNWZhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVMcENGSXRLdEcyQ1liaEZMZ2hYU3c9PSIsInZhbHVlIjoiaFR6cnE0OVRHWFVjek5BRGs4Y1grS0x5QkxsRXIzNkZEMlVsa1B3MzgyOE9QVVY3TktaUFNFbHN2aTBkUWN6RmlSbU1IdXkwZEhtRkhvU1NSOXIxYVlTblB4RUIvWHdCUis0cjdUMGlSMTZ5a3JRTmNqYlRmTEJTRUxQNzA4Qm93OUpzaVFBNFB4dU1mNnlCYUJCYVNlN1ZLbDRGZ1VNR1B0NGlmZUhYUUtJTlViT0lmZnNPWThRUUtiWmNOenlKZE9kbjhTa1grclFiRVVWTnFEZ3ZSK01BMkVYTktiUXdiS1RlZkpTczNnZ1ByRzd1TmpFQk5abzhOekwvZnFoS3N1WGhXdUVmbWZZSWNKUUtaYUNvRURtWjI4QVhIS05HZDFYSXhZakxYTnlZcUVFMlNkUWc0YS95T2l1MGNWMlRsNUY2VVJ6eUVsWmgvVWNabUpYOWdRckM3RS95Q1M5L3NxbXRZWUkxb3g1NGd0MDA5VzErYk1MSzlobm9Yc1d1SkoyK1JWd3hUSFF4NjZUOGQzVFBhKzlQSk1RMVFnaGhzc2xvelVMUzRUU2xKOGplRSsyTlFOWlNXZkdSOEJGMW5iYWVrcXErSGdDdnlpZkR4YTJJN2FoVFpuMmlBL3Y0cnR4SVFTMFMwQTQ2Wm9HQzgxeFhrNjVjc3poT1RqT1UiLCJtYWMiOiIxYTVkZDNiNWM4ODUzNDRmOGY3ZGYyYzg2NDhmOWI2MDU5MTc2MDQyYTg1NjI4YjE1YTdlZmVjZTY0ZWQ0YWE4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050860938\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1347783603 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347783603\", {\"maxDepth\":0})</script>\n"}}