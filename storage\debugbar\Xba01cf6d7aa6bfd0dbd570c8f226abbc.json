{"__meta": {"id": "Xba01cf6d7aa6bfd0dbd570c8f226abbc", "datetime": "2025-06-28 16:01:26", "utime": **********.028475, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.506404, "end": **********.028493, "duration": 0.5220890045166016, "duration_str": "522ms", "measures": [{"label": "Booting", "start": **********.506404, "relative_start": 0, "end": **********.935844, "relative_end": **********.935844, "duration": 0.4294400215148926, "duration_str": "429ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.935855, "relative_start": 0.42945098876953125, "end": **********.028495, "relative_end": 2.1457672119140625e-06, "duration": 0.09264016151428223, "duration_str": "92.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831920, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02628, "accumulated_duration_str": "26.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.980696, "duration": 0.02237, "duration_str": "22.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 85.122}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.013005, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 85.122, "width_percent": 2.017}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"p%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"p%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;p%", "%&lt;div class=&quot;product-stock&quot; id=&quot;p%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.015904, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 87.139, "width_percent": 12.861}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-487557010 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-487557010\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-108345171 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-108345171\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-949103397 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"32 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;p</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-949103397\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1757894068 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">65</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRSWjlOakVoMGtKMjloR2dVNVU3alE9PSIsInZhbHVlIjoiZVVzck9ST3M4NnJTSnVGQTlaYlJ6NWpxbkRVYnBaQzV4bG05b3RnQW9QOFh3YVRhWjhodyt2QnR6dkRNOWxDNkJwZ2ZnL3laRjQrRFBkY2xaMlkrUTJaN0s2QzRYYm9PM3dXWXRPa1ZJRk16ZDFwVnpHRGxtbVhPTjJjanhZTzBFOEgrYVFtUEZmbGdFVmZ3NVVDTkRBd25RdlFEWDE1NkVLUmZZMm8zN0VuU2ZKcEhKMU5CSFRoNlFpUjRTN3A5RS85SUw5SmM3QzJXd0t4eWxIYm1VTURoMzN1eEdta3N4NkYwT3ZRU0xWb202WXd4NS9uaUdFZHo3Tm1vWG44RndPRVpFZitKM1JNMGFrTnUyUjFod1k3dm90RWFaTDdpcEtoUEdzWDhPdGZNMTAyZXRqTjFvUHNmM1BzT2NIQzhQMFpJWFJBM0hEMW5EcElZeDN6b05oamtHZHBKZmZxSGdnOHVUVHp3RVlXVHk2MFhuZ3RLYnduakp2Nk5MOGZPZWpTYWFXcU9PNU1yZUJOQ0M3dWJYY1QwTStkTENPcE5YbHRoM2Ezd2JUMEZ2T3M5L1FkMHlJSEdSWnlwcUdQRHVESVpyYkpjSnVPVE5Pd3BDMUdpMTdkTTRMTlFqWFNrbERhUmRIWCs1M1VjcTRJK0N2OWc0OUZvalpUOStvMEciLCJtYWMiOiIwYmUwZjU0NzdmY2JiY2M4NDJlMzAxZjk2OWZkMzUwOTY2OTA5YTI2YmE5MDY2OWUyNzg5NmY0NGRiM2Q1NGUyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkN6OXUxRFM3cDF5cExJQzdsN2NYTXc9PSIsInZhbHVlIjoiL3EvK1RBN2NkMHVaMXUyajhmZjB6Nk9OVXExZ1k3endKY2drMk1DbFljMkQyeG4yZTJNRFVXQURvRmNwUkVpVVB1SnNZNTJZL2pmSjZBeVdqMTd3eWN0T2ZYL1NVY2JqK3pYT0hkeitQd25GRVdNVlpZUVRXaU9UNksvaVVxVVdsakdWOWxITnJ1c1JsVDZpN2RaRGJ2bitTS2pHVUF3ZTlZczArWnN1eGVUTnVmSzhnampKOHR2UTFrU0UyNHZwV3dyUDdkMmlPNjNOc01tN3ltQXp6ai9QdW42WW1sdG1iSjJvdlR0YmVmUXNzOEtiaWlYUWU4UWdUMTk3UGpHRWxoRjBOTitPNEZTeHhsaDZIeVI0bk5WZGh0RE04ZWtTdVF2d09PcUcwWFNhc04xdDZkcDJXQ0E2ZGJtSnVCdGtFbUJPTDI4TklJdkUwa3RkL0dteHY2M1hlby9pZDlGWDlIVHp5WE5sY2c0RUJMLzFwSkNlQVp5NEY2MlBoamF2RzZ5Z0I1QnAwVDd0WFhXMjJZSW5LSUljd0RlNFdWOVhJRG8ya0VnZFk3UkhINUtlVGNxRll1ZWtEY3hab3ZCQlg4cFprNGxBM3R4OVAyVXpKS0F6NkE3RzdGMHFjbEhXNWpwRjBBYzdBSkR1L09QbTlxOHRIbUtnczE3M0RCT0EiLCJtYWMiOiI3ZTViMGU1ZWEwZWVmZWQ2ZjZlZmUwNjdiYmYxZTExMmY1ZDM3NTY1MWQyN2Y0NTY2OTg3MjdlYjY4ZDY4ZDI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1757894068\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1569838868 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569838868\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1885977240 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllEOU53YmVPa1I2bHVjZXlnd0pUTEE9PSIsInZhbHVlIjoiRTEvQ3Y4a3Y2NFNtNFF3YlZjaG5tcVFYMXRTdmlSR3dYd0VQU1JsWEMrRVcxM3VXQTRLWmhWamxHWUo0MFhSa29iVkdCd0Fjc0ZraXFPaE4rNWxGVUJoZUVmc0JjS0UxVitWWFdFYTVoaWZGckNxTllzRXFvV0tHQlNCWjN3WitnYVJZZnVlcTNJRHB3ajRsK0hseWd3blVwMkxINnZHSURQb2VVb1N4SWJkbUlDT2kwNkJPWlRjQ0Y3cEFDN1REQ25aWERzRXNMcFp5K3RrYnJlTFp1dWYyUFFETmlUR1NicFpGaG5tOXVSbmk1QVl6SmR5VC83ZllaMmFSNU15Vm5rSGh5clpFb2lTRzZ3YktaNEJUMWc4R0NVanErSytmV08vbXFlRHY0eFBJSWdHTVdxNVJLVkhySmtHV3MyVzBuY1I4WXZkZ0hiR0NxMzVMUU4xNVR2OTFxZVJqWFZKSkFRWk1PK2dnWityamdjNG9iZDE5NWk2QW5IcXBEOXQrc2F6L0FOc05DejRIUUNYVm4rS0wweWtNWjdSUEJ3bjBuTU5McEttS2RVL3hIcFZDclp6dFBNTUF3OTRBWE5jQ3V4WWtvMVZYQTlFVEhyWktyeFlOLzVmdlZHbVNWYjVUWTVObUJlbzh2bDJmRlo3NVQzNmcwc28xTnV5bHJwSWMiLCJtYWMiOiIzNDY1Njk1MTIyYjI4NGY0MTZkZDczNTkyNGYzOGU4YzJjNDllYzIzMDBlOTY1NTQzNjhjY2QyYjE0NjQ5MjJjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik55ak80RUN2TlN2UlF6NFZ1WjV3N1E9PSIsInZhbHVlIjoiUFlKdjhnWUhjODlHSm03UktpYmJ2YnVOT2doZ045cEplMGRsd1ZrVEJBMnVLa2lGUUY2L1V3OFRnUnVFUUR0SUdKemFGVi8zK1JXekNVQUdGM3lic3lkVmJoUVFCQzk0SXNUODhGMVJFRFc2QmJ2OUJJVWdqbXhNTEorSk5wUnJRNmMzQndwWmZDMk5Vc2t3VUd2OFJHNmdsYjNKQ3pJc2I3TE4yVWEzM2oxdW1WUXpYbTZBZkZ4MkZLcTBhV1p1Tk9FZDBqZkZENWV5QnBmeWVaeEFoWlVBcndlTVJWcmx6M1dCVVlvT3h1K0dVcVl0cnUvVkdEQ1ZrekY4emhaMTdvZGpjaTVHYTRseThxeC9JVExqOTVvWWtDRDBUZFhhTmdSTzk3bGZCbTZzY1lGSlVEMTV6RzFISDFIUVZLWm5ldkNsQlVaOFVETUJCb0lJZHVmT0d3bmxWa1hZbGZtRXN3ajhnbVNjdEdhL2w2Zkp2aGttTkZVRllZZHBHV2I0NHdadnhDbDJ6elYySXVoSUl5b0pkRFB4UVU2SFRnS2kvMUdESE4rUGdrMjY3YktlcFlmSlpVejJqM2V4dHZzMXg2akRwejIreXdxMWRlbml4UHJyWUNTaVpGMG81WHFuSEJMdEduUTVXSXFtM1dCKzlJTzhXVXYzTHFBVVZOOU4iLCJtYWMiOiIwMDU4MTVjM2JkN2ZjYTFkMTRjMmQwMTY5YWZkMTA5YTdkYzkzNGVhNGFhZjBiNjMxMmNmMjg1NDdjYmUyM2YwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllEOU53YmVPa1I2bHVjZXlnd0pUTEE9PSIsInZhbHVlIjoiRTEvQ3Y4a3Y2NFNtNFF3YlZjaG5tcVFYMXRTdmlSR3dYd0VQU1JsWEMrRVcxM3VXQTRLWmhWamxHWUo0MFhSa29iVkdCd0Fjc0ZraXFPaE4rNWxGVUJoZUVmc0JjS0UxVitWWFdFYTVoaWZGckNxTllzRXFvV0tHQlNCWjN3WitnYVJZZnVlcTNJRHB3ajRsK0hseWd3blVwMkxINnZHSURQb2VVb1N4SWJkbUlDT2kwNkJPWlRjQ0Y3cEFDN1REQ25aWERzRXNMcFp5K3RrYnJlTFp1dWYyUFFETmlUR1NicFpGaG5tOXVSbmk1QVl6SmR5VC83ZllaMmFSNU15Vm5rSGh5clpFb2lTRzZ3YktaNEJUMWc4R0NVanErSytmV08vbXFlRHY0eFBJSWdHTVdxNVJLVkhySmtHV3MyVzBuY1I4WXZkZ0hiR0NxMzVMUU4xNVR2OTFxZVJqWFZKSkFRWk1PK2dnWityamdjNG9iZDE5NWk2QW5IcXBEOXQrc2F6L0FOc05DejRIUUNYVm4rS0wweWtNWjdSUEJ3bjBuTU5McEttS2RVL3hIcFZDclp6dFBNTUF3OTRBWE5jQ3V4WWtvMVZYQTlFVEhyWktyeFlOLzVmdlZHbVNWYjVUWTVObUJlbzh2bDJmRlo3NVQzNmcwc28xTnV5bHJwSWMiLCJtYWMiOiIzNDY1Njk1MTIyYjI4NGY0MTZkZDczNTkyNGYzOGU4YzJjNDllYzIzMDBlOTY1NTQzNjhjY2QyYjE0NjQ5MjJjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik55ak80RUN2TlN2UlF6NFZ1WjV3N1E9PSIsInZhbHVlIjoiUFlKdjhnWUhjODlHSm03UktpYmJ2YnVOT2doZ045cEplMGRsd1ZrVEJBMnVLa2lGUUY2L1V3OFRnUnVFUUR0SUdKemFGVi8zK1JXekNVQUdGM3lic3lkVmJoUVFCQzk0SXNUODhGMVJFRFc2QmJ2OUJJVWdqbXhNTEorSk5wUnJRNmMzQndwWmZDMk5Vc2t3VUd2OFJHNmdsYjNKQ3pJc2I3TE4yVWEzM2oxdW1WUXpYbTZBZkZ4MkZLcTBhV1p1Tk9FZDBqZkZENWV5QnBmeWVaeEFoWlVBcndlTVJWcmx6M1dCVVlvT3h1K0dVcVl0cnUvVkdEQ1ZrekY4emhaMTdvZGpjaTVHYTRseThxeC9JVExqOTVvWWtDRDBUZFhhTmdSTzk3bGZCbTZzY1lGSlVEMTV6RzFISDFIUVZLWm5ldkNsQlVaOFVETUJCb0lJZHVmT0d3bmxWa1hZbGZtRXN3ajhnbVNjdEdhL2w2Zkp2aGttTkZVRllZZHBHV2I0NHdadnhDbDJ6elYySXVoSUl5b0pkRFB4UVU2SFRnS2kvMUdESE4rUGdrMjY3YktlcFlmSlpVejJqM2V4dHZzMXg2akRwejIreXdxMWRlbml4UHJyWUNTaVpGMG81WHFuSEJMdEduUTVXSXFtM1dCKzlJTzhXVXYzTHFBVVZOOU4iLCJtYWMiOiIwMDU4MTVjM2JkN2ZjYTFkMTRjMmQwMTY5YWZkMTA5YTdkYzkzNGVhNGFhZjBiNjMxMmNmMjg1NDdjYmUyM2YwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1885977240\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-549359802 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549359802\", {\"maxDepth\":0})</script>\n"}}