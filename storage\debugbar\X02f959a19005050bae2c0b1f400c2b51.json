{"__meta": {"id": "X02f959a19005050bae2c0b1f400c2b51", "datetime": "2025-06-28 16:19:41", "utime": **********.540936, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.121205, "end": **********.54095, "duration": 0.41974496841430664, "duration_str": "420ms", "measures": [{"label": "Booting", "start": **********.121205, "relative_start": 0, "end": **********.469199, "relative_end": **********.469199, "duration": 0.3479938507080078, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.469208, "relative_start": 0.34800291061401367, "end": **********.540951, "relative_end": 9.5367431640625e-07, "duration": 0.07174301147460938, "duration_str": "71.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45716624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02231, "accumulated_duration_str": "22.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.495421, "duration": 0.02105, "duration_str": "21.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.352}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5251741, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.352, "width_percent": 2.913}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.531442, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.266, "width_percent": 2.734}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1131137837 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1131137837\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1222926672 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1222926672\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-637133531 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637133531\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1786098082 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127576668%7C39%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImQwbW1lcFYwYXBUOWhyRkFNa1VJK3c9PSIsInZhbHVlIjoiZ1Z5NXZqb0lTZ2V6NzhPd0U1dEcxZjdiYnNMczlqai9wMzVINTlJSUMyWGZBZ1M4VjZPa05VRGpjcmd5Zi9oNGFnbGlWZzAxU000ZVhCOEY3Z0FmQU0vbnZGdTg3OG15bTJCTm54aGppNEdPTW1lcTczRUg1SEpoQmJ6U0dmdGFudTVxd3d6OGZvZlhWZzJGazJZV1MySVJsdDJseDFKRGZQSkY5YmNyR3JBdyswTlRFYXNmaFExT2ZXeTVYNDRrc3pPMkx3UitiSW0vRThHeXZBSENjV253ZWNiZWdpVisyY3doQ3R4REI2NWFHbm9veTlENkRjamRnZW42b2toSDFCT2drWmJ0OTBaeVdZYXpoYmRuYkl0RHU1bXgzTTZMU1ROK2VyT3h4enI2VjlrSkF1TDVmQ3ozeWRCRzdZTHZXblVaU1FQRndYeldPSHl0dkpOWGxPUlRNOHhMeHVXVk0yUmJjQmxCTnJrWHBURmJDUFVoZ0xjWlBDYnZ0dEg4K1NLQ2ZSbHVOaWFLUUQ4bkZoMExxdGdZMDYxSEpnalJEZ1N2SFgwcUdoaU10UW1ZYWhRa1JKUHpGMVA4WFdwZUV4RXRQQU55MTZIVDNWUmo5TzVmbG1tamlwUlRvRU5aNDdtejdMbHZYb3RDTS9aTUpNSy90UjR4MFlSb21qUVAiLCJtYWMiOiJiYzIxMGVlMjAyMTM3ZDQzYzNlN2JlNzg5Mzg1MGFhOTA0ZTcwZWQ0MWY1NzBhNDQ1N2Y3NWU3NWJjZmI1ZjgyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9zRlRRUUdMZEhhSll5dzZVOS9jS3c9PSIsInZhbHVlIjoiaTV2RVdPeGRSdis0aFl3a2diRENSTUVCQVFwZlVLVE1zZ25sTEhhdW03c2h2NWg1L3Q4T21ZWHRPMDRlN3cyaXAwVTBKVGJDQ1FxMjFrYXJmVlpQRTl1SW8zRGplYWdHMENER1FHYi91SFJ5S3JRZDdhUGhYL1Z0Yk51cWw5empya2MzSGhrcVhLK3ZVb3luSFk5UFp2MTUzRm81RkFkekxNcmF5M0hVWDVySmtOZ0xuOWcrUjJ2dlAvZXk2RXVBeE9icnh3MXFyQUFzTENRT0x0b1dlSGxWanpWYWhOMXk5NEN1d1hqYUZ1ZGlOT1hQVkx4dFBCSTczb0dDejY5b081N2F6VVpIMWRyOG0wTmVwcitLdCtBUGtMV3B2MHFTTTBpNENURmFyT1Q5aDJMRFo3N1p0SWNXTU9qVm9FMTVVV3NTUlp4S0sybzVselpSemRHZkpUWG5nRnJzNWZReE1uVGpBN1RvYytuNzRad21QL0VuaFluNGJtVndkVU9Hd1ZCcE41cktPbXFmQTlqcGlWQkdOdmVzaDMwMGJmZUV2Y2hTL2JobXkycUVtZlBkbURPZGM3NUU5VjV6Rm9raDk5RDNwcENDVG9LNDJWNlZvcUFPWnRQRWw2ZWNzb2pTdUFiS09tR2o4SXlheUVJOGFSOE9RM1VyZllUZDQ0U2oiLCJtYWMiOiIxMDU4MDQ3Y2NkZDc5MzNiMzViN2ViMTU4NzY4OTYxNGZjNTAzZTMyMmE2YTBkN2E0ZWQ1NjU4MDg5MmU0ZTk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1786098082\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-820833868 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820833868\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-461151619 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZwankxYWpOQ1hTTFdhd1hjbXIrRFE9PSIsInZhbHVlIjoiRnk0UVJFU0Fkam5meUpQbkszRWx5M2MrT1hLdEFWZUhCUFlYYkd5Skd4UUdNU29qRE5pZEdVYlBWd3Q1R05UM1hxNUdrVFkyRzBwS3FjaFFxSlVzNGQ1S0JYb3Y0a1FyKytjSDJoZTR5SHUwVk1TdSs3bFRBSVlsc2NqQ1l6Z1lNYys1RGpJSTAwR285M1M5VlV4bHhSUHpxQ21ueU5EcHFaV3hqK01GSWFLbnBlTDk4eTJ5SFMyZXI5YW5HZENIZkk1TXRqZStERUUxMFRDZnZ1ZG9sZlFTR3gxZ01XREJtTjl4SDh1WXpqZ0JlQVdpWFFtcEtxb1ZaM2xvNXhVMU03STQ0dXhBcnlKamYrUGVKZWs5VytTa0tmOGw1TW1FRTFGeGtVT3VCcG1EMCs1cWV4anRFeFdnSUd5K3BtNkdFL1RaRmRybDZRdFBPckozQW9aL3RLdnlXRlp3c2wyamVzUUNKaVZRa0ZJRXB4ckI3MVlaWTdqT1Y0OXh1TDhsUHRMNlAydWhsdG56ejNoMnFhRzI5M2VIY2IxLzdRWGNTaEhYZlAxcUlPdFkyaXBVQWxrS2k2bWg1TFdPalNockFIdExST3A0dUwvQ25URG1QQ2VsNjVFU2JGd1BhemVpS3dHajhMUlNwY1FIOU1hb0pWaFg5eVh6c1dHbFZPT3AiLCJtYWMiOiIzNzUzMTg0OWM0OGE0YjBjYzA0ZTg5MWJlY2RiYzZkZmQyMzUyYTk5N2E5NWFhYTQ3YjUwZWMxOWE4ZjE0MmYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlY4QzNzcmdZOW95dVJrcitxSUttc2c9PSIsInZhbHVlIjoiaEE2VlBWQjV3WVJZSTF4QTlmallQc0QrWEFDRXpidFU4ZUFud0c1azQ3Nlpyc3JmOHROTzcySDM2MHQ4RW9HekxuZEpFSVZnOVZzc1hJb040UHB0ekZMZ0xJWkw4aE1yUkZINU9EYzl0eWpkYzBxaHkydmVRVW15cXkwR0I3R1dMeStKZEtCYjFtZUVkQ3dpbEVXcHpQazc1aFZmNTdvWG9IVldEeDVKWlkrb3prS3NEZzRRMEZ2ZmVPTG9ZMHZEZyt6NDNvM1J1NHE5WE5XQnRkVzI4UktZcnVEZ052K0ZQMi9yd051UjI3L0RoMjRKK2lTQXVVMlpiR21WQ3JPTDRtMUJPMENiUytRTnQ2U1ZlWitkTXJoQUh3bEFZcGx6ZURJUVlkdGVtNkJmL0lrL3hCYTd2c3puQTd5UnMrQi9IR1lDWnBPaVB0Tm8rajJzcktFcERNRllHWTNUb1U1QXY3ejlnRmkzcm1ndkJPV3Yyd3ZFaGxicEpEQ25idXF1akcrODdkbThZd2U5N2pCRDhxMjZqaUV1YyswVzBFeGtVMVNUcGF2TVFFNjBLdXNocndzYUF5ZHBjNnRzRWlCMjRqQ0RQS1Y3THZpQk1vTFBBRTE5QmduSmJxODJtWjJIK1lJVkNoZ05aQ0tpVXpDRzMxcTh4Um9hSmpsQmc5TlQiLCJtYWMiOiI2ZjNkZjZmMWJhOTBmYzYxOWE0NjdmNDMzYzFhOTU4OWJjZDhlZTM2NTlmODNlNmY0ZTI3OWY4YTI5Nzc4ZGUyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZwankxYWpOQ1hTTFdhd1hjbXIrRFE9PSIsInZhbHVlIjoiRnk0UVJFU0Fkam5meUpQbkszRWx5M2MrT1hLdEFWZUhCUFlYYkd5Skd4UUdNU29qRE5pZEdVYlBWd3Q1R05UM1hxNUdrVFkyRzBwS3FjaFFxSlVzNGQ1S0JYb3Y0a1FyKytjSDJoZTR5SHUwVk1TdSs3bFRBSVlsc2NqQ1l6Z1lNYys1RGpJSTAwR285M1M5VlV4bHhSUHpxQ21ueU5EcHFaV3hqK01GSWFLbnBlTDk4eTJ5SFMyZXI5YW5HZENIZkk1TXRqZStERUUxMFRDZnZ1ZG9sZlFTR3gxZ01XREJtTjl4SDh1WXpqZ0JlQVdpWFFtcEtxb1ZaM2xvNXhVMU03STQ0dXhBcnlKamYrUGVKZWs5VytTa0tmOGw1TW1FRTFGeGtVT3VCcG1EMCs1cWV4anRFeFdnSUd5K3BtNkdFL1RaRmRybDZRdFBPckozQW9aL3RLdnlXRlp3c2wyamVzUUNKaVZRa0ZJRXB4ckI3MVlaWTdqT1Y0OXh1TDhsUHRMNlAydWhsdG56ejNoMnFhRzI5M2VIY2IxLzdRWGNTaEhYZlAxcUlPdFkyaXBVQWxrS2k2bWg1TFdPalNockFIdExST3A0dUwvQ25URG1QQ2VsNjVFU2JGd1BhemVpS3dHajhMUlNwY1FIOU1hb0pWaFg5eVh6c1dHbFZPT3AiLCJtYWMiOiIzNzUzMTg0OWM0OGE0YjBjYzA0ZTg5MWJlY2RiYzZkZmQyMzUyYTk5N2E5NWFhYTQ3YjUwZWMxOWE4ZjE0MmYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlY4QzNzcmdZOW95dVJrcitxSUttc2c9PSIsInZhbHVlIjoiaEE2VlBWQjV3WVJZSTF4QTlmallQc0QrWEFDRXpidFU4ZUFud0c1azQ3Nlpyc3JmOHROTzcySDM2MHQ4RW9HekxuZEpFSVZnOVZzc1hJb040UHB0ekZMZ0xJWkw4aE1yUkZINU9EYzl0eWpkYzBxaHkydmVRVW15cXkwR0I3R1dMeStKZEtCYjFtZUVkQ3dpbEVXcHpQazc1aFZmNTdvWG9IVldEeDVKWlkrb3prS3NEZzRRMEZ2ZmVPTG9ZMHZEZyt6NDNvM1J1NHE5WE5XQnRkVzI4UktZcnVEZ052K0ZQMi9yd051UjI3L0RoMjRKK2lTQXVVMlpiR21WQ3JPTDRtMUJPMENiUytRTnQ2U1ZlWitkTXJoQUh3bEFZcGx6ZURJUVlkdGVtNkJmL0lrL3hCYTd2c3puQTd5UnMrQi9IR1lDWnBPaVB0Tm8rajJzcktFcERNRllHWTNUb1U1QXY3ejlnRmkzcm1ndkJPV3Yyd3ZFaGxicEpEQ25idXF1akcrODdkbThZd2U5N2pCRDhxMjZqaUV1YyswVzBFeGtVMVNUcGF2TVFFNjBLdXNocndzYUF5ZHBjNnRzRWlCMjRqQ0RQS1Y3THZpQk1vTFBBRTE5QmduSmJxODJtWjJIK1lJVkNoZ05aQ0tpVXpDRzMxcTh4Um9hSmpsQmc5TlQiLCJtYWMiOiI2ZjNkZjZmMWJhOTBmYzYxOWE0NjdmNDMzYzFhOTU4OWJjZDhlZTM2NTlmODNlNmY0ZTI3OWY4YTI5Nzc4ZGUyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-461151619\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-796514166 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-796514166\", {\"maxDepth\":0})</script>\n"}}