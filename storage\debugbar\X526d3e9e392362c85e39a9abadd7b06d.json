{"__meta": {"id": "X526d3e9e392362c85e39a9abadd7b06d", "datetime": "2025-06-28 16:19:08", "utime": **********.135335, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127547.705666, "end": **********.135352, "duration": 0.4296858310699463, "duration_str": "430ms", "measures": [{"label": "Booting", "start": 1751127547.705666, "relative_start": 0, "end": **********.07769, "relative_end": **********.07769, "duration": 0.3720238208770752, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.0777, "relative_start": 0.37203383445739746, "end": **********.135354, "relative_end": 2.1457672119140625e-06, "duration": 0.05765414237976074, "duration_str": "57.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45716896, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00275, "accumulated_duration_str": "2.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.10638, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.545}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.116255, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.545, "width_percent": 12.364}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1210592, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.909, "width_percent": 17.091}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6InBrTlNWeXVVemg3QituQXNPY2tJVEE9PSIsInZhbHVlIjoiN053YlhQRSt4bTl2Ry9sMlFZMWVZQT09IiwibWFjIjoiMzU0ZTJlNWIzMmQ2ODhmOWIyMDYwNzZjNGRkYjRiMTVhYzIwYmQ3NWRlOTNmNDgzOTQ1ZGI0ZDc0NzBmOGYzMCIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-882007281 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-882007281\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1451215280 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1451215280\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-882513229 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-882513229\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1076629987 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InBrTlNWeXVVemg3QituQXNPY2tJVEE9PSIsInZhbHVlIjoiN053YlhQRSt4bTl2Ry9sMlFZMWVZQT09IiwibWFjIjoiMzU0ZTJlNWIzMmQ2ODhmOWIyMDYwNzZjNGRkYjRiMTVhYzIwYmQ3NWRlOTNmNDgzOTQ1ZGI0ZDc0NzBmOGYzMCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127544265%7C34%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVnSXNDMEdzREUxWjNlTnNIL2pCTXc9PSIsInZhbHVlIjoiK2l1aEZFMWYrVDVTZGhJK3lSRXFaWFEraDFmOCs0YlEvQjI0anZHUDlURTNTWCtKZlZDUWxZdGJmeWNFQmk4TUxHOCtZTzZPc1ZlakkxaWJQTzRXOCtqMG1majdCSjlJcnN1aFd4aVZ6a3VhaEJJTmQyTUFaQUdqdkw5NGEzMWVQZFl2MEowdmhPZkduVkFaWEpQUG91VDYzNFRXOU1zWVV1b1gwM0RTakRkQ3g0SGRFSkE1THE0d3RmMTc2RXgwWnFteEZFYVdhSWdENGMvYU44cTd2QkRvVmh2dGxJbjVISndqYk96T0Z1ZjJXL1VTTzNENitMVWNMRWltRFJDc0RZb1p1ODArU3M5Ky9YWFlJdFNDNXVkYi9NTGFTMUdsZW1KVFozODVIMTlTcWRWV3RxckRZOVA4bUk4VnZGVTVRQllyeEpPWExTS2VLQkUxaWlTeDBRWm9NTFczb2lJR0pyTWQrckU4a2E2WUpaSFZlaERZU2UwQkg5bnJMd1hPVlc4N2oxbWVTdkFpWHFwZ2RWd3F2Z0dqck9Fa2N5OXVuWVpTTU1uMXlRNHZoMnhnbG5HRFk2OGhJTVZ3NmpQRmlreldtcVBValRkVzRBd0p6QWJFOFJydjVsOUsvdHNxdmJaVGpYMlJINy81b1hoZHBsNVM4N2dUVHRSWXR6cUYiLCJtYWMiOiIyMjZjNDM0YmQ2YmMxNWQ0Mzc3Yjg4NmUyNDIwYTdlNGQ2MWFlZGQyZGU1MjI0NmZjZmVjNDA4MmQyNjJiNWRlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBTVlNxUWN6MDVySjU4RHRKNHRtNlE9PSIsInZhbHVlIjoiVWxvQUNaaGVDTmdmTnppV25uQ1llWnVvem1FWWU4dDUzeXhrRjdtRFF1b1NqMXFlaUdMcnQ0V2FpWG9lWWc5d1hTZmtMWERCZ3E4a2l2RnhBcXRiRUYyMnBNS0FsMEwvaGpwWEtublVWSzdtY2VpZ05LNnNMY01Wdzc4cmFHTGNqRHNtT1pIeXVFc3ZhQUx1M0JhK3QwNXRqcldiWGs2ejRSanUxRVNWbjE0RGk0QVkxeGVtY3ZxT3ZVODRqOURkM09BRkVSblMzeXdWemNVVnJtcFhHTjM2NjhkN2NVZnZvYTVYaXEyVHZwQTFEWWNrNUpzY3l3cjlWLyt6T3B6bFErUnNLSUZpVFBVZjFIVUhnaFVCemdFMlVFV1lmZzlTb244b1lCRnBQSmoxUGpDVUJvbjhUeGt6d0JESUdSLzZKb05ENVFpL0d2TlRCZHp5ZitYME0xMysvN0t2TXprQS9pV3J0VzEwdG9KdjhpNms3SUtwRFpnMFlQZ1hWTUhQZmYycTdTandKRVpjMHNCa1kzOUdHK2x4YVV4b29tODNGYXpYckErdGVibTVRNmlnQlFlMHRrMXRQc3kyM2VmYlJPN3hidHA2c0QyRFl5R0MvaHBoQWFYTzZiN2NMelhYTHdRMmlzY0EwcHFMNmYxbEhiVi9zVVIrSVVDNG1abTgiLCJtYWMiOiJiY2MzOGM4MDAxMjRkZjc1OTI0OTA3YjIwNTQ5OGI0NWViZWI0NTM1OTgzNTFhZTBkZDFmMjE3NTA0ZTU5MmQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076629987\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-780751495 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780751495\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1628767774 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNrK1pic2paRHBPWlFnaWhzWHp1anc9PSIsInZhbHVlIjoiNWlQNUhZT05XN0VTTjA1UDFIVEEwa3l0Skg4NE1IY0NUcUtmdzc4Zm5tbk1kSXV6cVJ2aEx6c2NQTlA4dm53b090RUtrcTN0aGpXSmYvYzlBNmdtNkwrTkl3a1pRZUZnKzVHZ05ydkZCNStBSW1udERoZjVmMmdNK296enBoVkV6Ky9pZXRHbFd4Wit2ZDVCSzJ4WlRIWW40L3oxODQrWUZPdkZFd0p3bHc5U0pjNFBEeVFjV28rT3J1ck5uTWhLMmRJdTVZMVBqSWVIWDU3SnhOaXFITXRzbnZuTDB5TkJQTUFiS3F2UnNmcEVxejcwa1lGU3BjVUt5eTRpUEtsL29DZ2M5a1ZZQTJkL29aRUhlU2VMU1VkS0svVlZmTjBzdFozdHlkOEVLQ1ZHVlZZVkpiY2szOEYzbXFtaHVRQSsxTzQ4K2l1S1h0TElyamdOdjB2SjN3QkQvMHU3c0pSanNqNE9iWG9UZWUzdjNobW1uaU51QXZGUmVVakNzWi9GenFXNGtXMHR5VVVKYTZDVGhoWldWWDNEblJ6Ym1YeDZNUEEyMTJxdExrTWJjNUs5TGZFZFVuR3BXQysvb0twMnFwKzNBME5hZm93RlpTS3Q0UXJhMjNTemkzTUxKOEJUNTd0bExZUmdtcytGVmgrbFB1dm1JNTFZL3E4aUxQakUiLCJtYWMiOiI3MmI4NWVkMTZmMjRjYTk0ZDYwODM0MDRkMTQ2NDAwMTNkMjJmZTZkMWNmYmE1YWNmMjg5MDNiNDM1OWQwM2FlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRibmVkM09ab0VON0hQM0JJQ3JiSFE9PSIsInZhbHVlIjoiaDVCd0dNWTVHWFNhWlhRM3ROb2pvaEsyOHQwYkRueFZmaTNncy9HbVdMdmo5ajdrcklHNFloVld6WTdLcm0wdGxoLzlJUGpxUmsxVU1GWjdSTngrbzBnVGxkM1pxMlNtejJueVNmWmt1djlDYnBWL0VFZ0txa2VNbXV6ZjE1dkFwOXVLVksxUmFiNVRxVWxCeEN5NjBJMW9jeXRrQkhETFJUOXQyZUI1SEltVkZJMW1LUUNFMVhCaGRsR2M0UzRIY1lyYjV3dGJMVHh4eU52VEZkZENNMUdTaEVob2dSYTJMVWRJME9nWEt3bi9MRXBWa002UXhHSU5melpDaW9xa1RFOVZVcWV5cDNTUytjVW1Hd081U3RIeVFLaUtVMEw3NmdVUVdybkg2d1lkbENSa3BlMGtnMGE4MGpJSG5QTDEyVTloNDI4MnhvR0FUZVkvd1hyY0xua0lBUkc1NGpTekFMRGJsN2pNYndBRDRkV0NQcnpuVXZobnc3WXJBeWRlTnlqOXpIZnhUUlFsQ0VNdm1ZYW80OE5qNW1uL0twUnFoTnFKblJncEZjVUlXY09oNitXc1Mrd2w1RjF2UnpoMEtjUFpsWG1Ed1VDTVdqUS82dVQ2YmZjMW82SWpvdFVlT1pESHIyOS9KbVQ1NmdEeUhhQkFlRktHR0JGbVp5a3YiLCJtYWMiOiIzMDgzYWZmN2JhNDc3MjkzMjc5NDJjMjgyMDNhZmIzZWNjNThlYmIxNDczOWViZTdiMDA3MTViNGI1ZmU0Y2FjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNrK1pic2paRHBPWlFnaWhzWHp1anc9PSIsInZhbHVlIjoiNWlQNUhZT05XN0VTTjA1UDFIVEEwa3l0Skg4NE1IY0NUcUtmdzc4Zm5tbk1kSXV6cVJ2aEx6c2NQTlA4dm53b090RUtrcTN0aGpXSmYvYzlBNmdtNkwrTkl3a1pRZUZnKzVHZ05ydkZCNStBSW1udERoZjVmMmdNK296enBoVkV6Ky9pZXRHbFd4Wit2ZDVCSzJ4WlRIWW40L3oxODQrWUZPdkZFd0p3bHc5U0pjNFBEeVFjV28rT3J1ck5uTWhLMmRJdTVZMVBqSWVIWDU3SnhOaXFITXRzbnZuTDB5TkJQTUFiS3F2UnNmcEVxejcwa1lGU3BjVUt5eTRpUEtsL29DZ2M5a1ZZQTJkL29aRUhlU2VMU1VkS0svVlZmTjBzdFozdHlkOEVLQ1ZHVlZZVkpiY2szOEYzbXFtaHVRQSsxTzQ4K2l1S1h0TElyamdOdjB2SjN3QkQvMHU3c0pSanNqNE9iWG9UZWUzdjNobW1uaU51QXZGUmVVakNzWi9GenFXNGtXMHR5VVVKYTZDVGhoWldWWDNEblJ6Ym1YeDZNUEEyMTJxdExrTWJjNUs5TGZFZFVuR3BXQysvb0twMnFwKzNBME5hZm93RlpTS3Q0UXJhMjNTemkzTUxKOEJUNTd0bExZUmdtcytGVmgrbFB1dm1JNTFZL3E4aUxQakUiLCJtYWMiOiI3MmI4NWVkMTZmMjRjYTk0ZDYwODM0MDRkMTQ2NDAwMTNkMjJmZTZkMWNmYmE1YWNmMjg5MDNiNDM1OWQwM2FlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRibmVkM09ab0VON0hQM0JJQ3JiSFE9PSIsInZhbHVlIjoiaDVCd0dNWTVHWFNhWlhRM3ROb2pvaEsyOHQwYkRueFZmaTNncy9HbVdMdmo5ajdrcklHNFloVld6WTdLcm0wdGxoLzlJUGpxUmsxVU1GWjdSTngrbzBnVGxkM1pxMlNtejJueVNmWmt1djlDYnBWL0VFZ0txa2VNbXV6ZjE1dkFwOXVLVksxUmFiNVRxVWxCeEN5NjBJMW9jeXRrQkhETFJUOXQyZUI1SEltVkZJMW1LUUNFMVhCaGRsR2M0UzRIY1lyYjV3dGJMVHh4eU52VEZkZENNMUdTaEVob2dSYTJMVWRJME9nWEt3bi9MRXBWa002UXhHSU5melpDaW9xa1RFOVZVcWV5cDNTUytjVW1Hd081U3RIeVFLaUtVMEw3NmdVUVdybkg2d1lkbENSa3BlMGtnMGE4MGpJSG5QTDEyVTloNDI4MnhvR0FUZVkvd1hyY0xua0lBUkc1NGpTekFMRGJsN2pNYndBRDRkV0NQcnpuVXZobnc3WXJBeWRlTnlqOXpIZnhUUlFsQ0VNdm1ZYW80OE5qNW1uL0twUnFoTnFKblJncEZjVUlXY09oNitXc1Mrd2w1RjF2UnpoMEtjUFpsWG1Ed1VDTVdqUS82dVQ2YmZjMW82SWpvdFVlT1pESHIyOS9KbVQ1NmdEeUhhQkFlRktHR0JGbVp5a3YiLCJtYWMiOiIzMDgzYWZmN2JhNDc3MjkzMjc5NDJjMjgyMDNhZmIzZWNjNThlYmIxNDczOWViZTdiMDA3MTViNGI1ZmU0Y2FjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1628767774\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1866820155 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InBrTlNWeXVVemg3QituQXNPY2tJVEE9PSIsInZhbHVlIjoiN053YlhQRSt4bTl2Ry9sMlFZMWVZQT09IiwibWFjIjoiMzU0ZTJlNWIzMmQ2ODhmOWIyMDYwNzZjNGRkYjRiMTVhYzIwYmQ3NWRlOTNmNDgzOTQ1ZGI0ZDc0NzBmOGYzMCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866820155\", {\"maxDepth\":0})</script>\n"}}