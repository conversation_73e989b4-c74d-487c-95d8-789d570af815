{"__meta": {"id": "Xf03b51b3147edcc7a186cab58dd15361", "datetime": "2025-06-28 16:01:24", "utime": **********.326315, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126483.814144, "end": **********.326329, "duration": 0.5121850967407227, "duration_str": "512ms", "measures": [{"label": "Booting", "start": 1751126483.814144, "relative_start": 0, "end": **********.244229, "relative_end": **********.244229, "duration": 0.4300851821899414, "duration_str": "430ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.244238, "relative_start": 0.43009400367736816, "end": **********.326331, "relative_end": 1.9073486328125e-06, "duration": 0.0820930004119873, "duration_str": "82.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45846696, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02663, "accumulated_duration_str": "26.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2803361, "duration": 0.02225, "duration_str": "22.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.552}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.311838, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.552, "width_percent": 2.103}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦 ا%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦 ا%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦 ا%", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦 ا%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.315409, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 85.655, "width_percent": 14.345}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1488844410 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1488844410\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1460072759 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1460072759\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1212969571 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"48 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;&#128230; &#1575;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212969571\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1898399054 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">101</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilg4WThGNDFsK0VodWlwMjBwMEpQakE9PSIsInZhbHVlIjoiNUdWekhURHZIV2V2RFNpaUtxK2VjVUZSSU5NVTd4WDh6ZGlpQjBMTnNUWDlFMUxZSEJ3YXE2djlHYU94OU8vejJkNkJLT0oxbVl4eHdlVXJEdjU0bXduMUVSNDB0czhmT0tkMUpDRDFvWFk5YzIvU0NWbGF4SkNCMHU4aGRJaytnRkR2bkRoYzdmRnBUenhJcWU2eFNUNW84Lzk1YloyNG01MWFJenBHZ2JYdUpEWjhUT3JYN2o3TXl1bzNJamJRMm1oRzJoSTNlSFYyYnpmNzc1NVRpQnp4WW5tSzZEUEJqVTN2aGl0Q3hNWklUVWNKS2FIc3hudVlZalBBR1VJOTlvQmgyZnN6akNlZS80YTZpanc0NUl1VXVSVmtxOWpLb0tNSDNFTzI3R1pvNXJaTE9hWVI4M1ZPclpDa0Z3UGZBc2lFNjkzTlZvNzhJSnhSSEowYXpvWSttdk85NEp5RWxmcWpiMm0vVDJjbGxPTktkNWs2QmVKWmlscUZ5dm5qdFp0ZlBQRExxOTJvRHg1ZmMxdG1pZGljK25jSzRxZi9LK1RSeXh5S3NRSUtpejJ2dmNOSk9iNXZpMHFJTGtFczg1T3d6K3RYRENZOEp5Vk81R3JZVFU4aW9nSmYwRkdwQ2x1YnpxOS96cGNDYURzL0ZmVEJnWHFHZ1Jjc1Z6QnciLCJtYWMiOiJmYmFiZGU3MTc2YTcwYmRlMzI3ZGZiNGIxNGVhOTQzMWJmMDJlZGJmODljZDMxNjkxN2E1ZTgwZjFmNzRlMjMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRwUnFnS05EQy92cXhINGE5V0piTXc9PSIsInZhbHVlIjoiRURXdURvWG9uOVNtUW50RTV6bVFZYk83QUMwUDhma1BzTkwzWmVWME45TVo3QWpsS0VobGRWL3p3azk2VmNmL3JlU2xSN0ZPWjhVMUFINXU1S2VpZmVXTDlmditoNFpSVTF2U0M2dmZLNDNieElFNTVmemwxZVBwaEVrN2ZtWGRQK1ZKdnF2NjNDWEhpVW1sMVRVUHNmOFQ1U3hjcFhvZFBmNHMyOURBTDRLaURhZG1aYTFLRGtvQkpVWk1CQjJDY0FvSFBwcUNKUVM5MnRsRkl4U0tMQ2EwRTRyK1hIZklyQmJHL2lkdDZxZ0l3c292OUZuOEtxaHEyODRsbkhITUM2clkyaStLUUw4ZmZhT21ITWtVN2xxWG12aTdvNENGempkTlF4d09jY0ZFb2RuQjZVMDhCTThGT205Njl0Ris2REZSWFE5TDFucm40Ni9lclJTMXplV0M4SmtXVWlqUUpFc1BCUzBYL1FqQ0xzeXFOd3hYcUQzVkhNc3RVcmZsSjZUMVdhODRacjVKV3VzeS9xcE90TzhkNCtSR1lIdno2Rmd6ZGQvZm9HcllCN1VQR0pBOWFoTkZQMG5rZ1lmY0RibVZoSHVDVFd0ajBiRlBJOTcrK2dyWEZOTlJLbmxjcGZxYzdRNTBOUndYUkc3MElKaGN2aSszZDB0RTZwUEoiLCJtYWMiOiJmMmUwODllZWRhNDYwM2RhNDZlMjM1ZjQ1ZGRiOGI3YTc3MzQxZjkzYTdmNmYxZmFmZTgyM2JmYzkxNzBjYjBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898399054\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-833939434 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833939434\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1477823501 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlEYVhVSXk1YzJsQmhtUG5abzJqUUE9PSIsInZhbHVlIjoiVDZnS0s0eVZYZkk3UTNiMyt2RUlIbk5FMzlQOEpPM2xlaVEvUldDSzUyYVJQUnBaMUJzUkFUbEg2K1FpZjZaVG5OZnRpZmdrd1ljRHdSdmE2Nm5rMkRoQ29VS2k2ZWZwOVNFZFBsRUtGMXQyQXh4WkhxVUFYWU1JUUk3NzcrMWowS0VrbTJ0SC8xNEhRQmJ4UEFMTXp2ZFY5Y3h1ZkFIOFU1NDltSzNzTWIrUlF4RUI1TklPb1ZLKzFsWXFBbU0wSjNnanc0L0ZxckpjVmFVbW1wc3FvcUZNSWRzQzNwWkloc3Bybk5pTnU5MUh5U0NoL2w5VzJyZnllVlNsSm02bzQ1UUljdE96a1FVWmdBcUpxQUE2cDY2Nk5zTXBJRXJkQUlBeWZmRlNSTlYxbThxZXdTeG9WQlJYa2Fhc1kyM3MwSGUyRlQ4U0V5YkJYb1IycHY0SEE3RTJOclhtRUlKUGZSeDFmZ1h3ZXVEbnlCSVd5Z3crWUgyUkhjcnFHWHJPMXQ0Vmd5bmYrTmora01FTHFwaGl2YlR1TUFMY3dTWC9jYTI1dlBjTUNwTVhvdHh0MzlrU2Y1T0JVR2g3MERqd0RCaVFOb2g3YkZSSXVKVTVKcHM0aFlrbDRCMlZzeVZxVUthSE00cnBla2Q2d01yNS9IWmpoNnY0R2F0aUcxbWIiLCJtYWMiOiIxNGMxNDY2MDA1OTkxOGQ5MTRkM2RjYTc0MWQxNDA4NDdiMzQ3NTEyNDcwYmJjYzQzYjcwNmY5MWI0YTE5NWMxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFZcm5Pdkh1c2JEZnBZK0xMWTBnNHc9PSIsInZhbHVlIjoiajFoTkJBR2NyNU9rY09ScHVwTUVqYkJIOGE1dHZ4QktKYU96bk1xRmRoLzlUQWpOditmM1ZWakg0ZCtXUUdSdjBvOFlZRTlubENOV3hJWlU2YVB1YjdlbG1QOFl1c0kzaXlPeEliM1c5SjV5MHNrL3MzQ1dZNXlHYnBPdHdic1V3bXJqRXFvVGZ3UFFJbTl6cXBBL0MxVU5US3QycWVIam5qa3ljL1pnSWJTSjVLTUZBRlUwSnhuQm9GMFRTQ2c3OVJGL01iM1h6Qm1nd3p2V1M3Q2JVdUliQ0F6YXFSWXVDRlNRK1Q4by9WTUY5bFd1S2w0NnkreXRKNm1jaXVyOVMwOExnZEp0azBoWm05VW5JNWtwTUVGdmd0Mmk3TlFYTG4zcEdkVnZnSG1UdDlHYUh5VHRJc0hFUHgweXJLYVp2QTFTWWFvSWJ6MEZCanNBdFdFM1BNQkRsT2pQTFg0dE5sYTRtOCsvOVEwN1QwMGI1OFhqeTdhdnU3eE5vTXNNa2dRckkyRnJVc2poZFZFWWE2Uk5qNFpwZ2UySE5XQXFoTm1Xb296V01kQ1Nic0VVQWp0UUs3MmlQcmFoM1lKeXV0ditZN1VSMDFubzJjWjEydDQzMDlsMmJCZkxieFpXS0lSM1lEVGcyaTkrWU9ua1dqUUVqTVRsQXpCdXpnV0YiLCJtYWMiOiI5YmIwNDNiNDZhNTczMzY1MjczMDFmZGUyYTczYjkzNDU0ZWQ2ODQ5YmUxZmY0NTkyN2ExMTIyZmVhZWM4Mzk4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlEYVhVSXk1YzJsQmhtUG5abzJqUUE9PSIsInZhbHVlIjoiVDZnS0s0eVZYZkk3UTNiMyt2RUlIbk5FMzlQOEpPM2xlaVEvUldDSzUyYVJQUnBaMUJzUkFUbEg2K1FpZjZaVG5OZnRpZmdrd1ljRHdSdmE2Nm5rMkRoQ29VS2k2ZWZwOVNFZFBsRUtGMXQyQXh4WkhxVUFYWU1JUUk3NzcrMWowS0VrbTJ0SC8xNEhRQmJ4UEFMTXp2ZFY5Y3h1ZkFIOFU1NDltSzNzTWIrUlF4RUI1TklPb1ZLKzFsWXFBbU0wSjNnanc0L0ZxckpjVmFVbW1wc3FvcUZNSWRzQzNwWkloc3Bybk5pTnU5MUh5U0NoL2w5VzJyZnllVlNsSm02bzQ1UUljdE96a1FVWmdBcUpxQUE2cDY2Nk5zTXBJRXJkQUlBeWZmRlNSTlYxbThxZXdTeG9WQlJYa2Fhc1kyM3MwSGUyRlQ4U0V5YkJYb1IycHY0SEE3RTJOclhtRUlKUGZSeDFmZ1h3ZXVEbnlCSVd5Z3crWUgyUkhjcnFHWHJPMXQ0Vmd5bmYrTmora01FTHFwaGl2YlR1TUFMY3dTWC9jYTI1dlBjTUNwTVhvdHh0MzlrU2Y1T0JVR2g3MERqd0RCaVFOb2g3YkZSSXVKVTVKcHM0aFlrbDRCMlZzeVZxVUthSE00cnBla2Q2d01yNS9IWmpoNnY0R2F0aUcxbWIiLCJtYWMiOiIxNGMxNDY2MDA1OTkxOGQ5MTRkM2RjYTc0MWQxNDA4NDdiMzQ3NTEyNDcwYmJjYzQzYjcwNmY5MWI0YTE5NWMxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFZcm5Pdkh1c2JEZnBZK0xMWTBnNHc9PSIsInZhbHVlIjoiajFoTkJBR2NyNU9rY09ScHVwTUVqYkJIOGE1dHZ4QktKYU96bk1xRmRoLzlUQWpOditmM1ZWakg0ZCtXUUdSdjBvOFlZRTlubENOV3hJWlU2YVB1YjdlbG1QOFl1c0kzaXlPeEliM1c5SjV5MHNrL3MzQ1dZNXlHYnBPdHdic1V3bXJqRXFvVGZ3UFFJbTl6cXBBL0MxVU5US3QycWVIam5qa3ljL1pnSWJTSjVLTUZBRlUwSnhuQm9GMFRTQ2c3OVJGL01iM1h6Qm1nd3p2V1M3Q2JVdUliQ0F6YXFSWXVDRlNRK1Q4by9WTUY5bFd1S2w0NnkreXRKNm1jaXVyOVMwOExnZEp0azBoWm05VW5JNWtwTUVGdmd0Mmk3TlFYTG4zcEdkVnZnSG1UdDlHYUh5VHRJc0hFUHgweXJLYVp2QTFTWWFvSWJ6MEZCanNBdFdFM1BNQkRsT2pQTFg0dE5sYTRtOCsvOVEwN1QwMGI1OFhqeTdhdnU3eE5vTXNNa2dRckkyRnJVc2poZFZFWWE2Uk5qNFpwZ2UySE5XQXFoTm1Xb296V01kQ1Nic0VVQWp0UUs3MmlQcmFoM1lKeXV0ditZN1VSMDFubzJjWjEydDQzMDlsMmJCZkxieFpXS0lSM1lEVGcyaTkrWU9ua1dqUUVqTVRsQXpCdXpnV0YiLCJtYWMiOiI5YmIwNDNiNDZhNTczMzY1MjczMDFmZGUyYTczYjkzNDU0ZWQ2ODQ5YmUxZmY0NTkyN2ExMTIyZmVhZWM4Mzk4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477823501\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1477062426 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477062426\", {\"maxDepth\":0})</script>\n"}}