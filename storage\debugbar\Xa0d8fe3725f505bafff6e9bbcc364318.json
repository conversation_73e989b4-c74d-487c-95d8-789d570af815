{"__meta": {"id": "Xa0d8fe3725f505bafff6e9bbcc364318", "datetime": "2025-06-28 11:25:12", "utime": **********.831259, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.363371, "end": **********.831274, "duration": 0.46790313720703125, "duration_str": "468ms", "measures": [{"label": "Booting", "start": **********.363371, "relative_start": 0, "end": **********.773869, "relative_end": **********.773869, "duration": 0.41049814224243164, "duration_str": "410ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.773878, "relative_start": 0.4105072021484375, "end": **********.831275, "relative_end": 9.5367431640625e-07, "duration": 0.057396888732910156, "duration_str": "57.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46266696, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00231, "accumulated_duration_str": "2.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.81438, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.416}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.82424, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 84.416, "width_percent": 15.584}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1454/thermal/print\"\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1968542835 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1968542835\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-987517959 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-987517959\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1435091776 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1435091776\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IlNBcFBDOUdCNm1hbnZ3QUE1U3JNNGc9PSIsInZhbHVlIjoieVhFTWlGNVpFWEFZTDQ3UE8wU3lZamdMTFhLZHNkRm1PV2NuQWh6Nks2RWNrdm8yVFVEUWV1WC9HbkNsV3FoTTg2b1A2SGh0QXAyZ0JvZ3FmVE9oQ2JJNGtleUxERmI3cjZlMjBZa2xhbEJodE9sSWR4TUhLeG1kRGU5UENBWVl4b2psZFFpNTd0eUttcVViZHpub3lJZzZSeFBjOS84aFd3aTN1dWtYYkttQXExcDNwdmVHTExFVE83THR0K1FQa1dmVUFPaTk2S3hnV2IrRkh3THZUcGlPVHpNT2tuaSt6QjZWWkE0a3ZpVW1qSWpJK202Qk5IMTZoVkxjU2RlUUJYRC9kaEI2a2UwZGdnWGpWMXdIeVIzZ200MldUYVBEZEk2Rnk0TGxjVUdVaXRFbHdXU3ZmRk5rWGhlNzR5elVIMHN5STdudFd6R3ZqNURPMWNrd09KRG1TWlpNbUZSRGozM1Z2V2FvUW9rcW9udFErQVpnbEwxSEZWaHJMeElHc1JDNm44MndsZW40YXk5NUk3NEQzdW5uOWpYNEt1aytER3lBL0tFbjZ4WUJ2aWVYdkRsMEhucHp0akJLTSs4VjRNajRVVURwNVRza2pNSm5UMjBKU1VmUmpkSC9CRmF2RDNFL3YwTUJwSnFzNmthZlR5SE9WK2ZGVkp1MnBJUnkiLCJtYWMiOiJmMjZjOTkwY2I5MGVmNjE0YzNhM2I0NjU3YTRkZGU1MTA0OTIyZWZmNzcxNWExNTdiOWQyNDk1MDdmYzcwOTMwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InIxU3NjZERvMmVmcHI5dzJxclpBUnc9PSIsInZhbHVlIjoibjYyR2liZmE4NkV0dGJEV0FERVMyV3lqMGNiTDdUeEJZSDhKS2QxZE00L2xrZDhQb3FmQU9jdG5QbjViVkxlOEJjYnE5L2p1S0xDSW0yWVVSRko0V2lDZm94bXNyOUJOakgzT1YvSGF5RW52dUNHZ01Fb09tRzZudGhvRGxGNFFlTFJtV1B1L3luWVRheDdLbG95cVloZHhONU9JUXpOdW0yblArcVNKaXN0ZmhnR1Rkc0U1OU1wYjhkZ1FqVHdqQlFnblVUWWlqK0c3TWt2NEwvRnVoWTZzRC9wb210YzN3WmpxVFJDU2N3TEU5SkgyQUxkdS9Eam8yWlVpMGx6K0REZGxleWxxR3NWaStrTFhCYkJsYzRNRVQvK1VubXZ0dDYxQ2NUZGpjQVFING53bVRoTUljK054NVdvUkZrcTNud2V1dmhMYm9WcGNUNUk2R3J0SkNuMXYzUFhmV3lUSmlvZHJwVW5ORktlaGhNVDZSRFlEQVdPM0JtSzNKMTRIK3F6b3FZQ3haMXlqVGgzWm1rSkt3THdzU0ZYUHR1YTQxZmpQOGVnTW01UDQzS05GM0xLOGhnMlZ4NkVPbkVOTVZIbk93WmMrUmxGM3lkTStDK002YTlEZG9lM00wMER2VXVrWFQ2d0I2YU5QMUVDRkcxQnE5TEt0bkdXQWkvTDkiLCJtYWMiOiJlNDE0ZDE0MDQ1NmZjNmEwZDg5ODY2NzI2NDhkMTYzNzY5MjU5Y2FlODNhMzc1NGNmOGRmNTgzOWE2ZmYwZmUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1680406799 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680406799\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1613207007 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:25:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBEWFkxWTR5Ni9qSmpTeG5BenVZUHc9PSIsInZhbHVlIjoid2FzTDJGVmRUaXEvdWlqYzlkUGxsd1dQeThLRnZLbUY2QWhWRGZGd2QzQUcxUlllVk5NM01GTWV2UFVwbDRvblo3djdKdmhpZzByOVJ0QVF3M0hEdEpKN0RsalQvQ2Q3KzJvU2FKbDRWc2w5ODBJMTFPNm5lT0RTdGYzOVI5TUhQSVFMTmdFTkNZc3Zrb3V5VSs1OVBZTUFkdENkTkk5WDc0UllOMUVWZUVJbzZWdlVhTWs3SFJ3RDZUSXBSSGZsZTFzMGtWQ0k3VmNhUWVibjR2YXJZZ0pubm1UY2tpSzkzUlJUMmxuNHJFMkQ4TDNsb3VTSmFJdDNXbGs0enBHMGFPOFZLZGQrcm1nd2NnV2ZhNllaUERiS2hIaHc0ZlBWODZZaGRDR3hCdzcrL2czbk84VkpSY0FqRmZmYkZsbUp6V1lOck43NHc1endyZGNtSGNVbm9sY3U1cFR1VktjM2ZhSmNKTVU4cXVhWUlHTFNZMDJXb2h6NlVpUmVvTWRPWEprMnE2R3A3aHE2bnNlK0tmN2ZSSDFYRlNFYzBJN0wzYjc4Tm1jTldyZmoxcmVtQ0Qwb3llVWhTV0U5RGdPQmhTVGdDdGI0dVFsZ01iMHMvcitRemFBd0RpVDQrcjRiQzVJZ0NrTSs1NGg5bGpuL3pzeFBiKysyVXhxWWlrbUIiLCJtYWMiOiJjMDQxYThhNmI5ZTU4YjUyNzQ1ZTM4M2YyZjcyYzFmNGI2M2Q0YTBmZDhhNTdmODk3NzAwYzMwYzk4MGNjNGYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:25:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZGZDY1V2lyeUp2R3ZTN1h2b0tOQkE9PSIsInZhbHVlIjoiNEtWRFNqeklBeFdXeG1ubXZlNHYrZGt5RDNvaUdhamQ4bExRMVliUUptWVpwTlB2c0lQc24rZXpmV3ArNmZZK3FLcWdJdFRrSXYzWlJCRkxLRm1RYy9LOXZ2Q1VncWRzOGxGOGRnN1EwNXFWa0FrMWFMYnV0aUdrRUdhZ1ZqWmlOZXd2WlpOcmtSbGtyNmo0L0NiT2tXZkgwaEh4Q2wxdzZaTjkrUG8yYk02VUdQWXgwTFRhN3FQZ1RwRG4yNnJqNjBWcE1GUk9MZVoxdDhpRjRxaFlyeDBVcjVNTkV5c2N0UjQwVFZyaHF4alRrRkl4WnV1eEI1UWlDbXplSXpzUVRiUHpybWUxdDh4MUhnNXlOb3B3TW02M0k1dUpiZUZZRGdGYzhrclIrdSszeGJ6V2hTY0ZRQ2pIQXdxeFhmN3pGUmZHa2p0K3ZmcTlMUG5EQXlyL0k0VTJvWUZ5NjlSVkdKWVVnU1RlMlQxaGwvT1BURVdTcUIxTG1BRktDUUlud0VSaEYyNlZ0OGR6d01ldmJCUjJpcERucVd2NDJrVFBWOFVzNk84Q1lUelJWQUs3ZHJlOWgxTzYxZ2dTcWsvUVRqWXEyNHgvYllNSGdLWXZWZFlUdzVMWkcxc3JzNlM4OWlwdDY2dEdIUEdnYzh6dGRzclJ6dEo2c2ROd0h4RVEiLCJtYWMiOiIxZDcwNGFmZTUzNTlhNTZjOTAyODZhNzcyYzEyOWQyYzhjYjhiMjE2ZTliYjcxZDY4NzEyZTk0YTAyMDM2OTAyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:25:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBEWFkxWTR5Ni9qSmpTeG5BenVZUHc9PSIsInZhbHVlIjoid2FzTDJGVmRUaXEvdWlqYzlkUGxsd1dQeThLRnZLbUY2QWhWRGZGd2QzQUcxUlllVk5NM01GTWV2UFVwbDRvblo3djdKdmhpZzByOVJ0QVF3M0hEdEpKN0RsalQvQ2Q3KzJvU2FKbDRWc2w5ODBJMTFPNm5lT0RTdGYzOVI5TUhQSVFMTmdFTkNZc3Zrb3V5VSs1OVBZTUFkdENkTkk5WDc0UllOMUVWZUVJbzZWdlVhTWs3SFJ3RDZUSXBSSGZsZTFzMGtWQ0k3VmNhUWVibjR2YXJZZ0pubm1UY2tpSzkzUlJUMmxuNHJFMkQ4TDNsb3VTSmFJdDNXbGs0enBHMGFPOFZLZGQrcm1nd2NnV2ZhNllaUERiS2hIaHc0ZlBWODZZaGRDR3hCdzcrL2czbk84VkpSY0FqRmZmYkZsbUp6V1lOck43NHc1endyZGNtSGNVbm9sY3U1cFR1VktjM2ZhSmNKTVU4cXVhWUlHTFNZMDJXb2h6NlVpUmVvTWRPWEprMnE2R3A3aHE2bnNlK0tmN2ZSSDFYRlNFYzBJN0wzYjc4Tm1jTldyZmoxcmVtQ0Qwb3llVWhTV0U5RGdPQmhTVGdDdGI0dVFsZ01iMHMvcitRemFBd0RpVDQrcjRiQzVJZ0NrTSs1NGg5bGpuL3pzeFBiKysyVXhxWWlrbUIiLCJtYWMiOiJjMDQxYThhNmI5ZTU4YjUyNzQ1ZTM4M2YyZjcyYzFmNGI2M2Q0YTBmZDhhNTdmODk3NzAwYzMwYzk4MGNjNGYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:25:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZGZDY1V2lyeUp2R3ZTN1h2b0tOQkE9PSIsInZhbHVlIjoiNEtWRFNqeklBeFdXeG1ubXZlNHYrZGt5RDNvaUdhamQ4bExRMVliUUptWVpwTlB2c0lQc24rZXpmV3ArNmZZK3FLcWdJdFRrSXYzWlJCRkxLRm1RYy9LOXZ2Q1VncWRzOGxGOGRnN1EwNXFWa0FrMWFMYnV0aUdrRUdhZ1ZqWmlOZXd2WlpOcmtSbGtyNmo0L0NiT2tXZkgwaEh4Q2wxdzZaTjkrUG8yYk02VUdQWXgwTFRhN3FQZ1RwRG4yNnJqNjBWcE1GUk9MZVoxdDhpRjRxaFlyeDBVcjVNTkV5c2N0UjQwVFZyaHF4alRrRkl4WnV1eEI1UWlDbXplSXpzUVRiUHpybWUxdDh4MUhnNXlOb3B3TW02M0k1dUpiZUZZRGdGYzhrclIrdSszeGJ6V2hTY0ZRQ2pIQXdxeFhmN3pGUmZHa2p0K3ZmcTlMUG5EQXlyL0k0VTJvWUZ5NjlSVkdKWVVnU1RlMlQxaGwvT1BURVdTcUIxTG1BRktDUUlud0VSaEYyNlZ0OGR6d01ldmJCUjJpcERucVd2NDJrVFBWOFVzNk84Q1lUelJWQUs3ZHJlOWgxTzYxZ2dTcWsvUVRqWXEyNHgvYllNSGdLWXZWZFlUdzVMWkcxc3JzNlM4OWlwdDY2dEdIUEdnYzh6dGRzclJ6dEo2c2ROd0h4RVEiLCJtYWMiOiIxZDcwNGFmZTUzNTlhNTZjOTAyODZhNzcyYzEyOWQyYzhjYjhiMjE2ZTliYjcxZDY4NzEyZTk0YTAyMDM2OTAyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:25:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613207007\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-708558924 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1454/thermal/print</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708558924\", {\"maxDepth\":0})</script>\n"}}