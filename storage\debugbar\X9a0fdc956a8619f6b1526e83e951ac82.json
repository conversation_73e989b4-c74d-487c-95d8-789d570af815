{"__meta": {"id": "X9a0fdc956a8619f6b1526e83e951ac82", "datetime": "2025-06-28 16:34:32", "utime": **********.382064, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128471.85141, "end": **********.382081, "duration": 0.5306711196899414, "duration_str": "531ms", "measures": [{"label": "Booting", "start": 1751128471.85141, "relative_start": 0, "end": **********.314534, "relative_end": **********.314534, "duration": 0.46312403678894043, "duration_str": "463ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.314542, "relative_start": 0.4631321430206299, "end": **********.382082, "relative_end": 9.5367431640625e-07, "duration": 0.06753993034362793, "duration_str": "67.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45728384, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026200000000000004, "accumulated_duration_str": "2.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.349611, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.069}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3625212, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.069, "width_percent": 17.939}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.370866, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.008, "width_percent": 20.992}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1649206643 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1649206643\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1764109197 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1764109197\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-450984855 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128238007%7C50%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllXeVRneTRSN2Z5d2VVV3Z1TGtTQVE9PSIsInZhbHVlIjoidEtvcFJWMlhpTHJmYmwxMTJJSWlNMHg3eGFkQ1RhOWNES0hibW9IMFdKZGVFSkFaRVV1dzQrSCsxS3lRTEx5RUw1TEJnZ2twWE94MmFYdXRPdERNSUEwMllGUWQxbFZsZVBsN28xaGFKaGgyNGlrUFU4YWcrMnVhYlVrbm5OUGJpZWxvU0lDa2M4eEpVZkFKTDZ5SXFCcFFaYm1oa1QvK28vMExuWjdiRGZlZUtPeUZta0ZlK3lwdWZ5a2krcFNudEgxSjJZeGE3T3plM05SY2JDbVZFQnF5Q3FVZklmandqeEhRc1p3RnRRM0t5dEY0OGZieFBMMHhCaFp4aTE3b0VxWlI3THRuRTVzVFFMWXJwNmh6eGY4MCszTVRmNk5ZOUU4czhlZlBhU1ZXUzBNQkhoSGVtdHF3Z2huU0x5V1VOSXdwWDlWc0psd1d6c0Y0emJDaVpvRUU5UnlxbzJrVFRLRytndHdheVZjalZqY2d1QmdEWHdiMVF3bU1hMCs3Nmhxcks4b3QwZWQ1Q3JHc0RyeUJZM2NnbDc4cnA0WjNHRm1QWC9YWEpNNXJEK2VSMldMa1UyRmhKQitmUy8yOUFXMnhvN085ZDBJTlExTTJuN0VvV3BnSFFPUnliSk1yUGtQSGpXKzZZSjVyREwwYUEvNWhqRlM0RTFwSHhUcVQiLCJtYWMiOiI0NWM2Y2M0NzExZTY4MGQ5NWJmNTg4NDFjNzczOWRlMmNjOTFjMjI3NTFhZWMxMTA2OTMxYTg5YTU5MTNkNGFlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjN6WXBORGtzN3FoeUMvaWIxalFVREE9PSIsInZhbHVlIjoiQVZ6a05hcFJMWEtqZE8zZFdwSFNDTWVKZGw0Yisrc1NOQjFhd3Z2NmhNSStKOCs4NnM4dHhLd1E2cGQ2VFJJbVVWQ3dYNEFEbjFVMmFIU0hJc01FV2NlZmV5SG5EM2ZBdHRucFVUOUFrM1JMbWNGcHQ0bWEyOHk1bm1wT3JEVFRySlhJNWJzTXJ1MksvMXRNZ2MwYXdRbWQ2dWVWcnhPUkJoekJ0NDZwNXRvSDBHK1ROR0RFbHExL21wajdhclVpam1GTEVEMVhycThZZXJRWFZIaTg4VVY5L2ZpMG9HeWFWMzV5M0RYWXdsNW5NT29ja0I3VTVtR1J4R2ZoM3dWWHYwR3ZHWTYxTThXbHhzMk4vQ05HanpZTW9Ea0xtcDJtYUJJbnlMZ3BRMkhsUUx1anpiejdFTWhJTGNuS3lJb0VGVWYvekdPbWdIcTVSWFlPUzFQb3h6WFE4TENPS0lybWJDUVFIQUxxaG81WVlYUUNkY0VRRlJva2dPMWZOZHV0NkZTYmY5RCtxR0EyWWYzQUl3eE5WTm5BRnlJQVFaclgyMEJScExUbmRYdDZaZ2NmVC9KTUZ0NVlONjdVYkJUdC9oemVRUkxFMzQrYTVjTlFTVTRGRlphbkNtcExRcENBTTh3cGdHS2l5Vm1JU2VyR1U0VkYrSDF4WnRaT3FXREsiLCJtYWMiOiJiYjJjZjA4MzA0ZTc5MzI4MjE1NTFjZWJmOTMwMmY4ZWVmMmQ1ZmNhNjFhZWZjNTY4MGNmYjg2YzVmYWUxOTdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-450984855\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-760655300 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760655300\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-358600320 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1GWjZqK29WUkVma1o2ZUdWUFBWbnc9PSIsInZhbHVlIjoiQm5PZEJDcUI2WGErQkRjRDVQZG1KUFlFTmF1MHhOR2xWaGlBNnRSeTJRd0pzRk1tWDFmNCt3Y0l1MzdZZ0EyN0tyR21YMG9iWnM4Sjh0SjA2aE9EcDZiOFJlRHVQZENyVGdzb1BxUFI2MW1IT2VxdGxvUklsQThOU3J6RTZVQnB6TWdnU3ViWlUxemJNNjlDakxrQ2pKY0VJaElWcjV1bXdNWUhKTHlmeGRPWWVZOWNwTVV5Wll0bVVZVXg3T3RuMHU0ZDdhZm50RXFPaTVmcGd3U29ZUEh2V0prNm5oMkVndXcvSmpGZHZGbnVsM1NFazNZT0tqbG1EdHJpem4wRk14N0pPUU1sc2NDQlpWUkorZTRLSkwrVzcrMURwL0xoUkZSWFlQMDJvRmozWnBxZVJxclBGbHpaR3FjNzNHMmxyTlJVRGp1Z1cyZEdUb3hRdUVPNzBwSVZTa2l4a3VweVFjZTJrL0l6c0V4UmlIT3VhUnpPYkpIQWJaVnVMMkdMam9rd3FEUlpGTkNkWFRWZ0dsa01pSUtINzhxUHh1OUVZNU1LM2pQY0JRTHVxMFNIV284cHlEdVRIMTk1Z003SVl6K3czVTRKQUZDdXhuSDF6SmVwemVxbXJtNEpLYkRuTk0vYzNNWGtLbTF5N2tESnVhWnFJU0psTFpoaFhqYUsiLCJtYWMiOiI0MzZkMGY1OTgzNGNmZDMxNjM1YjdkNzEwY2NlY2ExZjJhZWNhODBiZThlYzVhZjcyYTgzMzg0OTRkNGZjMzUyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJ4R01LUUFZUThIdSt0U3ZmSXdSb1E9PSIsInZhbHVlIjoicEhkMUVhb3I0Ulk2cFd0Snc2dXBoWFBDbzFMU0dWRmRqSE5xajl6V1lla3VBOFdaMzVGdXZLc29RdE1CTTRLOUh2NlVZSXpTNlMzV3ZWUyt2VEFKTy8vQmtDZFdzNk1TMTd4dktJQitLSUcwYTM1dVNnYjFVZFNJMHBESUpacWtWVFlqaDRrMFJPeWdyOEw0MWM1L1BsSnBQak5mNHRQVFU4ODRLZGJYNXFHTm5mK0FnNkVzTE03N2dJYTkvb0dIUlVsdjlTV3VZSWJrdjlMWCt0YWxsOHlBWFlpaStZVS93djBCMFlYNWNQRk5GTTdpK241aFF0UGs0cU5pSU9MNzhzOHFkcm1XZWUyZklQVkpWbnczRnZpeXNxTHJIeXBzS24vY3JFWE03RjREZjVacTM4YURQV2RjcmQ3a0xydERNMDhkcXhuS0Q1Y09kSXhHb1JkT3BuZjhROGNDUCs3b3hKRnErRnY1NkEwL2U3dVh2TDNscFN6SHpWZ295VktZZ0JrTXAyeWVEV1JlcnNqVlpMenBUUmdGeW1vMnBFQkZSU0RUSW5pZlFTb05oZDg5dkp6K25YYUR4VDBlNWpVYlE4bVVNbWg5c1Q1NXpBSXZTMS9nNm5Hd0wwSWpPODlFbVZzRWtWZ0hJTHIrSDBtRlY3MjZNb1lXeXJyUkZSSUIiLCJtYWMiOiJhMzM0OGY0ZGE1Mzg3ZWQ3MTQ1YmFkYThlZjk4ZWE0MGRkODUyZTBhZTc5OTZiMDkwYjVmZGZhM2M4MTNjNThkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1GWjZqK29WUkVma1o2ZUdWUFBWbnc9PSIsInZhbHVlIjoiQm5PZEJDcUI2WGErQkRjRDVQZG1KUFlFTmF1MHhOR2xWaGlBNnRSeTJRd0pzRk1tWDFmNCt3Y0l1MzdZZ0EyN0tyR21YMG9iWnM4Sjh0SjA2aE9EcDZiOFJlRHVQZENyVGdzb1BxUFI2MW1IT2VxdGxvUklsQThOU3J6RTZVQnB6TWdnU3ViWlUxemJNNjlDakxrQ2pKY0VJaElWcjV1bXdNWUhKTHlmeGRPWWVZOWNwTVV5Wll0bVVZVXg3T3RuMHU0ZDdhZm50RXFPaTVmcGd3U29ZUEh2V0prNm5oMkVndXcvSmpGZHZGbnVsM1NFazNZT0tqbG1EdHJpem4wRk14N0pPUU1sc2NDQlpWUkorZTRLSkwrVzcrMURwL0xoUkZSWFlQMDJvRmozWnBxZVJxclBGbHpaR3FjNzNHMmxyTlJVRGp1Z1cyZEdUb3hRdUVPNzBwSVZTa2l4a3VweVFjZTJrL0l6c0V4UmlIT3VhUnpPYkpIQWJaVnVMMkdMam9rd3FEUlpGTkNkWFRWZ0dsa01pSUtINzhxUHh1OUVZNU1LM2pQY0JRTHVxMFNIV284cHlEdVRIMTk1Z003SVl6K3czVTRKQUZDdXhuSDF6SmVwemVxbXJtNEpLYkRuTk0vYzNNWGtLbTF5N2tESnVhWnFJU0psTFpoaFhqYUsiLCJtYWMiOiI0MzZkMGY1OTgzNGNmZDMxNjM1YjdkNzEwY2NlY2ExZjJhZWNhODBiZThlYzVhZjcyYTgzMzg0OTRkNGZjMzUyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJ4R01LUUFZUThIdSt0U3ZmSXdSb1E9PSIsInZhbHVlIjoicEhkMUVhb3I0Ulk2cFd0Snc2dXBoWFBDbzFMU0dWRmRqSE5xajl6V1lla3VBOFdaMzVGdXZLc29RdE1CTTRLOUh2NlVZSXpTNlMzV3ZWUyt2VEFKTy8vQmtDZFdzNk1TMTd4dktJQitLSUcwYTM1dVNnYjFVZFNJMHBESUpacWtWVFlqaDRrMFJPeWdyOEw0MWM1L1BsSnBQak5mNHRQVFU4ODRLZGJYNXFHTm5mK0FnNkVzTE03N2dJYTkvb0dIUlVsdjlTV3VZSWJrdjlMWCt0YWxsOHlBWFlpaStZVS93djBCMFlYNWNQRk5GTTdpK241aFF0UGs0cU5pSU9MNzhzOHFkcm1XZWUyZklQVkpWbnczRnZpeXNxTHJIeXBzS24vY3JFWE03RjREZjVacTM4YURQV2RjcmQ3a0xydERNMDhkcXhuS0Q1Y09kSXhHb1JkT3BuZjhROGNDUCs3b3hKRnErRnY1NkEwL2U3dVh2TDNscFN6SHpWZ295VktZZ0JrTXAyeWVEV1JlcnNqVlpMenBUUmdGeW1vMnBFQkZSU0RUSW5pZlFTb05oZDg5dkp6K25YYUR4VDBlNWpVYlE4bVVNbWg5c1Q1NXpBSXZTMS9nNm5Hd0wwSWpPODlFbVZzRWtWZ0hJTHIrSDBtRlY3MjZNb1lXeXJyUkZSSUIiLCJtYWMiOiJhMzM0OGY0ZGE1Mzg3ZWQ3MTQ1YmFkYThlZjk4ZWE0MGRkODUyZTBhZTc5OTZiMDkwYjVmZGZhM2M4MTNjNThkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-358600320\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}