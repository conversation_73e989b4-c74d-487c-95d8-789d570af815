{"__meta": {"id": "Xfe49dc9cdffd80c0c3a4b9efbcdea65c", "datetime": "2025-06-28 15:19:36", "utime": **********.716537, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.240449, "end": **********.716551, "duration": 0.4761021137237549, "duration_str": "476ms", "measures": [{"label": "Booting", "start": **********.240449, "relative_start": 0, "end": **********.659909, "relative_end": **********.659909, "duration": 0.4194600582122803, "duration_str": "419ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.659919, "relative_start": 0.41947007179260254, "end": **********.716553, "relative_end": 1.9073486328125e-06, "duration": 0.056633949279785156, "duration_str": "56.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46286144, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2321\" onclick=\"\">app/Http/Controllers/PosController.php:2321-2355</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00276, "accumulated_duration_str": "2.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.698735, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.623}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.709673, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.623, "width_percent": 21.377}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1458/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-734400241 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-734400241\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1167883285 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1167883285\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2032532111 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2032532111\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-784368010 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123960877%7C11%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlV6bDlaMUorY3lHV1ZqRHAwcmNuRHc9PSIsInZhbHVlIjoieHV5YTEvQ1R3eFhSMWdOQmRLakRDTDJRajh2anVNVldzNU1RZklCK1hRTSs0dDQ2WC9UZ2wvdGhkbGRKWlRxYnd0R3kxU2k3RzNYeUVqNTVCaEJzckZqQUdLUmJvQXRhdHVuY20xVUI5aDI1QlFmc3lnS0MwM3ZXNzBSWWFkNm8zM2wwZGF6UG4zSTFtckdXVzEzZEJTN1YzM0dqUFkxN3U3N01USUY5RG94OWsrcXBBZUM5QmpkWk1jWVdzNXNubC9zTitsWStqa2hTU1prNWQwcFZxOW9IRE0rUDJhRWdxbnVxU2U0a2tldWpiWUtMZFVGRmtQcGpDckEyMlJaRVljK0V4ZlFGSU1rNzFsd3l2dTFkdXJRdTF6WU9qM05NTU9hQThZRHpFTHIxdWoxbCtUT3AzVnJEU0U0SllZUGpPUnJiYmhFZGJWbnBKY3paMTlUT09qMHNoUzZYTGFWZlFxM3pFN3Z0aHJFRTRVMGJkeityWVFmZFBkL1VESlhlODByM25XcE9mZ0pSZG1KU1VqNCtnb1dGZkNwV29WTGpUUS8rekJkd0FzUDBKUE1rNDRCWnFWVjNHTVNJTFoxUGhtL3M0RzBiYnlXL3IzZTdmNHRObmN0MzY3MFNRcmNzSk1VVy9OZjJPZWxndFZyVGZHd1pnTElYTmtYWmRnSTMiLCJtYWMiOiI1ZDk5NGRmMzA0YmY3MDU5M2Y4MWJmOTFlN2E4MzQwMTk3ZmQ0MGI5YzRhNzIwM2EyMjgxNjU4OWNlZjU2ZWUxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklFMC92QWxRdVBkVlc2Q1N2QUZsM2c9PSIsInZhbHVlIjoicnVQWDJmR0xwUmpBNzQ0SGNOS0xLRk5rSzQ1QlVzT2UxSEQzbTdpWDFGYVdPNEw3UzhzZFBJQUpXc1BRZVA1MTIwQWswbFdqZGc0cVd4aWgwRUI3VmczcEJ0WWt2RGdzcExXa1JiZnpXT25taFpUTHBjV3VNV1ZkTU4rV1lXTkhTb3d0TWJKaTJTRmZqUU9Ga0hkY2N0QXJOSVJyczdIelRLbGZ1eWkvTGtNZ1dhYnU2TjZWK2QzY1dnNXVPaEpkNzZGSTJyM3ZiMVlXOVBOVWtGcjVKRmJVYy9FdmZaMmlFdWtjdThmVFN5cU1Jdk85dThnR2dlOXc1ZWRqY2xFZzJLdXhua1Z3RjdsejRtaEFqQmE1dDI4ekt2dkRTMno0QXpBUUN4N2wwcTRPVVh2SUludTBVRGhMaDc1dHFORU1STDFrYTVXenU5WnRzcWhIclgvUFl4bjZsOHE3Sm5oZE1PRjAvT3BkNFoyVHp4WmZoM3ZGNnFSb1FvQTNmZHJ3QmxFaG9OYXcwTm4reFZURmlDbWgvWE5ubjVEeFZ2aUYvVmQ3UEJTb3N1L1c2UlkrZ3lwcWZTelA1SG1hWkJVWmozc01NMEdZejA5Vy9xbXhxaDRtS2g2NjJlRUlWMy9XWHBFbEJGWDl6SnJUaGIrRXkyb2lSVHlYVTdWYW1HZkUiLCJtYWMiOiJjOGQ3NTgyY2MyYjYzNDg2Y2VlZDEwMDczOTkxZTExNGVkNDQ3YTNlMzlmOTMxNDFmMDNlMDUzZDY1ODY5YjczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-784368010\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-725106678 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-725106678\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-729515602 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:19:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IisxdWFUUHNRM3Q0dVdpQy9pV01Ba1E9PSIsInZhbHVlIjoiZ04yZzRmYTc2Y0JYMFRaWkk5aDkweGNSai9iVXFFQ2JQTVdkRFVKdkQ3T0x5N0cwZFdMSXZYWi9YajlmOGlSTno1WUs0T2tmbkNCSTJIMUFtaE1nbklHVXJPL0VkVFp4NTJsQTJpZlIxNkcxWWc1MHM2cGFpaUVZTUk0OCtaTlEvT1RUK2FBZG41N1E4QnN4K2psL2xIU2tSdkNNSnQxbWNtNk1Cbk9QOFhLVHdQejNhRnRaY1F2U1FRV2VmNUdCa3BQTFBVNUwxOWpSYXRSbE1KQlZxdCt4alh4blNxS0tpaEZBSU52bkV2WXRzRjBxSGExaFV6ZUtId0pLMFdtNW44UmFNRjVpeFJjL2NGNVdrTFQ3cUduVDlmVHhJa0g4MUk4NU9RK0FXT3NxaEQ4eFpXblNUWkVSSVVsTCtKeFlBa1hjaFdsUzVEUnV1akpoYjlBblBqbkFuQUNYTjJsT01aeWM4ZXo2TVNvRnJPZ3hCRFgyOHorK29GdldTdVdDVGd3aUg0bWtkYVkvam5aVWVpOXlDRk5LUVMySWRnSmVqK01hMHgzT3gxRnpRMGgzcVVucmh4WG1UejA0UDA3ME43STRtaCtCV29oclJkODNpYi9OWkRRNUpUamxuUmpqRUt1WWVlQ2hDZlZITE9EbkFlRTRKeHlkUmQrQk9oak8iLCJtYWMiOiI3YjMwOGM3OTg5MDIwMThiN2UyNTM5YjNmYjZkZjcwZmI4ZTk3YzkzOWZkNmE1MTcyOGQ2NmYyMmEzMDhlYzY5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJKS2Z6ZkVTQVJ5Mko2U3VMQ3ZHeXc9PSIsInZhbHVlIjoiV3hIUGlHZHo4Q0VzSGM5ZFFEcEVBL0Ftd3AwRGxPdjNaL25pZ3NBdjNrZ2hqc3c0R1ZZYmJKR1BIajBxRTdPR2p0dHhtNDdENWNsT1hBM2FRb3IvWFU3K3V1WnRnT0xaRUtwTzE5N1B5Vk0vQ0dmakFNdm5qUGFCdzltVWR5TWl0cFpxZlBsYlVud2lWRVpLUDRRejIvTmtOQUlWcGYwTkJXTlIrVUxObXNYQnlxWnVmb28xZ0ROeFRtdXloMEYxVWs4ZnhvUVhhTFhjWUJ5ZmhmNHdDTTBRbTZYN1JOUzk3NUhZR2ZrdjlidGRVbEVGNFE2Smx2RUtSSkVmZGhUajBKQkJORkxFc1pUUHhocGd4M3hHSkROd0RaMmo2eFFUcWF1ZTROS2NvdkJxYUFjd2NGaFVrTTY3Wi9zcG5iN2RrenU2OFoyVHlGMzRRSSswR1BDcGZYM1g3QTFoSFBZNEhTMjM4Y2U0UWdZMSsvcVVmVkFwOFkyU3pBeml3bDAwU2dXZXBINE8rNkJZVGFrVG1ObWNKZjRBWm15OEJUdDJ3L3FIaFNyYVhML0dxS05KV3pUYjh4RFBvaVRubjdhS1diNWRDdURVYzhqM20xYTgzM28rVDFNNkZJd2NVTUlqMWlPWkdGcElHc05VVGF1SGtrUExqMHkwY2tFbTZsTzMiLCJtYWMiOiIxOTI2ZmU5ZjkxYzEwMDZiOGNkM2JmN2IyYTk4YWI0MWEyN2FhMjNiNzI1ZmFiNWM0NGJjZmZmYWJjZDEwODNlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IisxdWFUUHNRM3Q0dVdpQy9pV01Ba1E9PSIsInZhbHVlIjoiZ04yZzRmYTc2Y0JYMFRaWkk5aDkweGNSai9iVXFFQ2JQTVdkRFVKdkQ3T0x5N0cwZFdMSXZYWi9YajlmOGlSTno1WUs0T2tmbkNCSTJIMUFtaE1nbklHVXJPL0VkVFp4NTJsQTJpZlIxNkcxWWc1MHM2cGFpaUVZTUk0OCtaTlEvT1RUK2FBZG41N1E4QnN4K2psL2xIU2tSdkNNSnQxbWNtNk1Cbk9QOFhLVHdQejNhRnRaY1F2U1FRV2VmNUdCa3BQTFBVNUwxOWpSYXRSbE1KQlZxdCt4alh4blNxS0tpaEZBSU52bkV2WXRzRjBxSGExaFV6ZUtId0pLMFdtNW44UmFNRjVpeFJjL2NGNVdrTFQ3cUduVDlmVHhJa0g4MUk4NU9RK0FXT3NxaEQ4eFpXblNUWkVSSVVsTCtKeFlBa1hjaFdsUzVEUnV1akpoYjlBblBqbkFuQUNYTjJsT01aeWM4ZXo2TVNvRnJPZ3hCRFgyOHorK29GdldTdVdDVGd3aUg0bWtkYVkvam5aVWVpOXlDRk5LUVMySWRnSmVqK01hMHgzT3gxRnpRMGgzcVVucmh4WG1UejA0UDA3ME43STRtaCtCV29oclJkODNpYi9OWkRRNUpUamxuUmpqRUt1WWVlQ2hDZlZITE9EbkFlRTRKeHlkUmQrQk9oak8iLCJtYWMiOiI3YjMwOGM3OTg5MDIwMThiN2UyNTM5YjNmYjZkZjcwZmI4ZTk3YzkzOWZkNmE1MTcyOGQ2NmYyMmEzMDhlYzY5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJKS2Z6ZkVTQVJ5Mko2U3VMQ3ZHeXc9PSIsInZhbHVlIjoiV3hIUGlHZHo4Q0VzSGM5ZFFEcEVBL0Ftd3AwRGxPdjNaL25pZ3NBdjNrZ2hqc3c0R1ZZYmJKR1BIajBxRTdPR2p0dHhtNDdENWNsT1hBM2FRb3IvWFU3K3V1WnRnT0xaRUtwTzE5N1B5Vk0vQ0dmakFNdm5qUGFCdzltVWR5TWl0cFpxZlBsYlVud2lWRVpLUDRRejIvTmtOQUlWcGYwTkJXTlIrVUxObXNYQnlxWnVmb28xZ0ROeFRtdXloMEYxVWs4ZnhvUVhhTFhjWUJ5ZmhmNHdDTTBRbTZYN1JOUzk3NUhZR2ZrdjlidGRVbEVGNFE2Smx2RUtSSkVmZGhUajBKQkJORkxFc1pUUHhocGd4M3hHSkROd0RaMmo2eFFUcWF1ZTROS2NvdkJxYUFjd2NGaFVrTTY3Wi9zcG5iN2RrenU2OFoyVHlGMzRRSSswR1BDcGZYM1g3QTFoSFBZNEhTMjM4Y2U0UWdZMSsvcVVmVkFwOFkyU3pBeml3bDAwU2dXZXBINE8rNkJZVGFrVG1ObWNKZjRBWm15OEJUdDJ3L3FIaFNyYVhML0dxS05KV3pUYjh4RFBvaVRubjdhS1diNWRDdURVYzhqM20xYTgzM28rVDFNNkZJd2NVTUlqMWlPWkdGcElHc05VVGF1SGtrUExqMHkwY2tFbTZsTzMiLCJtYWMiOiIxOTI2ZmU5ZjkxYzEwMDZiOGNkM2JmN2IyYTk4YWI0MWEyN2FhMjNiNzI1ZmFiNWM0NGJjZmZmYWJjZDEwODNlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729515602\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-467872959 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1458/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467872959\", {\"maxDepth\":0})</script>\n"}}