{"__meta": {"id": "X5803c30fbac568552cc0dccacdeccae0", "datetime": "2025-06-28 11:23:59", "utime": **********.228374, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109838.743996, "end": **********.228387, "duration": 0.4843912124633789, "duration_str": "484ms", "measures": [{"label": "Booting", "start": 1751109838.743996, "relative_start": 0, "end": **********.159648, "relative_end": **********.159648, "duration": 0.4156520366668701, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.159656, "relative_start": 0.41566014289855957, "end": **********.228389, "relative_end": 1.9073486328125e-06, "duration": 0.06873297691345215, "duration_str": "68.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45689656, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1910\" onclick=\"\">app/Http/Controllers/PosController.php:1910-1968</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00502, "accumulated_duration_str": "5.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.203523, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 36.255}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.214672, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 36.255, "width_percent": 13.347}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%1235212%' or `sku` LIKE '%1235212%') limit 10", "type": "query", "params": [], "bindings": ["15", "%1235212%", "%1235212%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2187948, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 49.602, "width_percent": 50.398}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-344817626 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-344817626\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-385043880 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-385043880\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1933732083 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1235212</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933732083\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-979655269 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">28</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IktmZzNIUnBrdEdITS9oc3lNV0NUQVE9PSIsInZhbHVlIjoiZ1BzSjU5L0tFeTNFSFVjUS9UOXp6djVkbG9xUFVXR2tiMkVpLzNJZUtMRmY5MTB5UkhlM1V4dkovQUNxcVFBRlM0Qm02NnNBK2U3RTh1dHE3UGRQRkJZcHE2SEw4UjNCbURpRmdQVWVYMUFCQUE2REpCcXh3WEh0eFpmMnpIM3B2RWN2bHd6WWxpTHAyVUtQZGptQVkvWEJlK2piWFAwUmtkbjJ5UFU3NndIV2taNGRWb3FRZ2FkMVZmM0FMRjZNNTJwMzBDbkNqVVBrZVM5b2Y1QXdWWHI0Y3hwWUh5aHVVQUFCbVlZTkJDNllBUndjNCtWMmlmaFRXYTYvYlZtWTdibmwzbFM4L244UFlCS3hHYVoyd0kzS0pHNVlvMmRxdVp6RGpaQTNuVW9WRDl3YWdzSXQzdzY2bENseEhadVNmdkc4eHh6b0FlQXAyNEd5bzBsKzJtWlNhZkpuNzcvT2Q4OG5Hc1NSMGc1WDZmRXZaUUpkZUszZVZ4enBrRTZEYWxmTFVZRm8xUUtSNTJWUWVKazJ6aUpHRjlNamJ4VnJUTGgyU0ZnOExQRVpkcFpwY3g5MXpvcmdCN1pHYVR4a0R3TVZNdVByM1BsUVNONnFpQXN6Lzc3VmpSY0FxcHI3YlpWUEZVbWdIWEN4NFh3WDd3VzFpL1I4MWZzV2dsbDYiLCJtYWMiOiJlNTAyMzUwNzRhZjQ0ZGRhYWZjN2I5ODA3OGMzNTZkMzg3YzMyMTY2MjUzZjEwNzRhMTg3MzY2ZjBmOGE3NjJlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVteEVRci9BY0x1cjdsL0ZqSW40aFE9PSIsInZhbHVlIjoicUN5VytTaTIyMXRyUTNucGloYXU2elFpdUxrSGxRNDhhQWR4WERxbTBORnVTUzA1OEpNU01SZkNRaDRSbG1PbW0rVFh6Zmpubm1JR3FNU0RVY3BYMzNid04wTUhxZWZ5R0svRHU5ZC9rZmlyTjFhU29aNUZ3YjhWK25sbzIzdnJ4dVhNYzNTdUF3WTIxRis2UzlEWEtWSjRYd1Y4SkZ3UkR2L3B1c0tLNzBQSXpwYzJjNWMrZEFXMzFqMEwydVZGYTVkTUdpYU16ZitjL3lCMGprdkQxay9ObEFMVGE2S29Zbng0MHptZEdDcVg2N1ZXL3cydlJHSVRoYW1LQ0xLWUE5aEFaV0ErSkJ1aEhtZUZscUJCaDJlWm1rdDRRKzNtRkpFM0tHNE8vOGRKbHM1WVYxYnRObEkydWJWYmV0dU9IbUl3a25JT2F6TlUzcVB3MVVQSUtjWHJrTEdJL2NxUEEvWUcyaUxMVnkzUENMVThMUXdLYnc5Qy9CL09WZ00zWEJIY2wrU0tuV2tqL29jZVVNWFNmcEJNWWtDSWxyaHV1N0NHdXBmSUhQQlU3Z1ROOUJaOXNKa3dOaXM1OGJlNnV4L3RZWDRLbWpNMDBBRFc1L0t0ZWFOUHQxallkQjFkVmFhQ2s2a2ZSblg2V0FybDhibHpyTXBlZzdqUkdnMEgiLCJtYWMiOiIyZWRjNjBiM2E0NjVjNzBiZTgwOTZkYTBmYTA5Yjg5ZWZkMTcxMDUxNmIxMzZjNWYwZTc4M2FiNzNjMmNhNjA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-979655269\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik52VUxFYzRaaDgvSTdxNXhGTHhaaWc9PSIsInZhbHVlIjoiZ3NwKzE5UmhualpOUG9xMG94cVk3b20wQytQdjIySnJPNG11b3pnZU41a2F6RDd0Tzh4cTJQam9UVjRKcWUzajIzNGNRODYzcXJOZ2lNUHdBOGl5RjFteHpGS2xXMTJyN3dPMXJVQ2p0bWhkK0w0ampTS0dZbGNIdWt2Rmx4Qk1vNUVMRjNqbHJnK21QUHVRVFdPSVhBUEp3aUpkQUxGWU1Wa0EvOHltMWlib2FHM2ExRERiQ3BrNUM1RUl1MmtXOWpkOHcwVC9YTENFTHZHRDR5WEZJKzBsd2tZb0Z2S2FHeFZsNXJtTFExOUFrVEtVNHVia2ZudEtkQU9vaGxlbWg0UFkxYXp1TFE3bDAzUG5QV0dHQ0pCY2xodmMxT09tdjlNTk9LdnMrRDZkNXl1dk81aEpxQkdsdldpR1VIcXV0TVpnRHluOXhMb2hkanZsWW5TZVVQeTJHUjhYRFZ3dXRkRytYd1ZsNzhRbWpYZktTY0hPSFJnSEVWYmVkamVYQ3IxditHRks2d2lsUjlUZVFOUDlrbXJ0SXdRTTVaaVNjbm1TK1NISU5JMUNNMGZhK0ZCNkRxUnQrWkE2YWNXNjViNjZybGtpamV0L21rR21TS1JCb2xWaVNOeHl5Y3VhNGdPdTZIcUd5NmpmYXpOc3EzdGR1V1pKaFZrL3l1c3kiLCJtYWMiOiIwNTVmZjA1Y2YyNjA1NWJiMjBkNmZhYmM2NTRjMGY2ZGY1MWQ5NTQxNDJlNDZlOTI0ODFkOWMyMTFlOTQzOTRjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZoVjY4Q0hwT2FMaE1Yc0dnU2J5bmc9PSIsInZhbHVlIjoiZlBOOHZtamVyVmEveEdpRTREOFlaeno4bEo5V1VGdm1BeTJ0c1VqNS9sdkhPYUJrLzUrUUFFb1dyL0N1VXpPeFZZRGJMaXlSUVpDSzZaaWVyL0F5SkxIRnEvNTVmYXo0VTN1akgrbGw4YmwyNTVPNHBOc05RMkpib1VKUTlUVjJqbFJMMXJMcmIxWStlWk8wOGNIMHpHSWNzZlhCN2JTclEyanYzalY5MDArQmF5cnUwU1liNjVPZURXNVNtUlc3Vi9rbmJRc1EwZmQvSVFpbGsxeG13YWNpdlNIWXFHTHE0bTVxMmdrQjNhSmtIb0JMODg0b1NMWnRseUZCd3ZDY1hMbW8wTnR5LzJtV2JWeHltRFIxelZzanJiZkd5b2pMUndpWHhrTUc0L2JzdytRWi9PYUpaVHF4TWhxRWpCSW44WUpOOXkvMjRaTk9KSTMyTEI4c3hOM3lWM2pnWFFBUjk3aklQaTZpNWh0akpRcHozQ3o0U3N6anA3NXpuU0tWNU1Sd3A5bWZaS2JiUi82MW5IVVoxSUV2SnMxU1NEeHdoOFM1K29ESURMTmx3Q3J4QUJJTGVROTBSUkl0SHZFMStiTVg2K3pjNGFWR29vYzZoNEF0Tkxqa1AzSUl2aXhEcE1uQXcxUjJCc2k5TldCWVFJVDdtM0o0WllTUjJkbm0iLCJtYWMiOiI2NmNmNzc2YmI4NjAzZDQzMjYzODZkNmFhOTY5ZTE2NGNjZTg1ZDM4YWYxZjkxYjQ3NTFjMzg4OTU2NjdkNTU3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik52VUxFYzRaaDgvSTdxNXhGTHhaaWc9PSIsInZhbHVlIjoiZ3NwKzE5UmhualpOUG9xMG94cVk3b20wQytQdjIySnJPNG11b3pnZU41a2F6RDd0Tzh4cTJQam9UVjRKcWUzajIzNGNRODYzcXJOZ2lNUHdBOGl5RjFteHpGS2xXMTJyN3dPMXJVQ2p0bWhkK0w0ampTS0dZbGNIdWt2Rmx4Qk1vNUVMRjNqbHJnK21QUHVRVFdPSVhBUEp3aUpkQUxGWU1Wa0EvOHltMWlib2FHM2ExRERiQ3BrNUM1RUl1MmtXOWpkOHcwVC9YTENFTHZHRDR5WEZJKzBsd2tZb0Z2S2FHeFZsNXJtTFExOUFrVEtVNHVia2ZudEtkQU9vaGxlbWg0UFkxYXp1TFE3bDAzUG5QV0dHQ0pCY2xodmMxT09tdjlNTk9LdnMrRDZkNXl1dk81aEpxQkdsdldpR1VIcXV0TVpnRHluOXhMb2hkanZsWW5TZVVQeTJHUjhYRFZ3dXRkRytYd1ZsNzhRbWpYZktTY0hPSFJnSEVWYmVkamVYQ3IxditHRks2d2lsUjlUZVFOUDlrbXJ0SXdRTTVaaVNjbm1TK1NISU5JMUNNMGZhK0ZCNkRxUnQrWkE2YWNXNjViNjZybGtpamV0L21rR21TS1JCb2xWaVNOeHl5Y3VhNGdPdTZIcUd5NmpmYXpOc3EzdGR1V1pKaFZrL3l1c3kiLCJtYWMiOiIwNTVmZjA1Y2YyNjA1NWJiMjBkNmZhYmM2NTRjMGY2ZGY1MWQ5NTQxNDJlNDZlOTI0ODFkOWMyMTFlOTQzOTRjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZoVjY4Q0hwT2FMaE1Yc0dnU2J5bmc9PSIsInZhbHVlIjoiZlBOOHZtamVyVmEveEdpRTREOFlaeno4bEo5V1VGdm1BeTJ0c1VqNS9sdkhPYUJrLzUrUUFFb1dyL0N1VXpPeFZZRGJMaXlSUVpDSzZaaWVyL0F5SkxIRnEvNTVmYXo0VTN1akgrbGw4YmwyNTVPNHBOc05RMkpib1VKUTlUVjJqbFJMMXJMcmIxWStlWk8wOGNIMHpHSWNzZlhCN2JTclEyanYzalY5MDArQmF5cnUwU1liNjVPZURXNVNtUlc3Vi9rbmJRc1EwZmQvSVFpbGsxeG13YWNpdlNIWXFHTHE0bTVxMmdrQjNhSmtIb0JMODg0b1NMWnRseUZCd3ZDY1hMbW8wTnR5LzJtV2JWeHltRFIxelZzanJiZkd5b2pMUndpWHhrTUc0L2JzdytRWi9PYUpaVHF4TWhxRWpCSW44WUpOOXkvMjRaTk9KSTMyTEI4c3hOM3lWM2pnWFFBUjk3aklQaTZpNWh0akpRcHozQ3o0U3N6anA3NXpuU0tWNU1Sd3A5bWZaS2JiUi82MW5IVVoxSUV2SnMxU1NEeHdoOFM1K29ESURMTmx3Q3J4QUJJTGVROTBSUkl0SHZFMStiTVg2K3pjNGFWR29vYzZoNEF0Tkxqa1AzSUl2aXhEcE1uQXcxUjJCc2k5TldCWVFJVDdtM0o0WllTUjJkbm0iLCJtYWMiOiI2NmNmNzc2YmI4NjAzZDQzMjYzODZkNmFhOTY5ZTE2NGNjZTg1ZDM4YWYxZjkxYjQ3NTFjMzg4OTU2NjdkNTU3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}