{"__meta": {"id": "X7f3f917822f83d238a3cfe0dc2c53c51", "datetime": "2025-06-28 15:08:06", "utime": **********.543136, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.087959, "end": **********.543151, "duration": 0.45519185066223145, "duration_str": "455ms", "measures": [{"label": "Booting", "start": **********.087959, "relative_start": 0, "end": **********.489748, "relative_end": **********.489748, "duration": 0.40178894996643066, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.489758, "relative_start": 0.40179896354675293, "end": **********.543153, "relative_end": 2.1457672119140625e-06, "duration": 0.05339503288269043, "duration_str": "53.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46269304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00221, "accumulated_duration_str": "2.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.524879, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.995}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.534957, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.995, "width_percent": 19.005}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1706777928 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1706777928\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-490598977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-490598977\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-791698066 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-791698066\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1629054052 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123245178%7C6%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlYVGhJTlFMRG92bVF5dzRvdGlHQmc9PSIsInZhbHVlIjoiQUo4cmRxOHZlanhzemhTOU9USmhCVEJnbTQrYjBhVVJYSm1rbkx5ZXA2b0lkeGdiNzFpdGE4R3NmMUVFVXJEQVE1aVlzcnpQUTZmOWlWQ1pIaXh1RXI4UFNFc09ISCs4b2JCcitrSGRPRWt6alptVjJtZUVPUllSZHFBM1dCR3BQVDZhd2RSaHMxSG82T0RxaWs2cTFYTUhobzR5SzNPRHZEQk8wNFQ5NWQyRTRnYnZ4ZDZVMm5YUWIvQjFzMk5SM1hRYTZ0cFJjdTBiTnE4VjYyQ2NNaUtHeGFjYTdrcDROdFlFclhHRkxrS3o4Qk5Ed1hQVVJ6RXdlZlJLQUN2c2RqRGJrZ05XSTJhcEVBdGd5YXN1NDFBTDRlQ0Z2MGEzcGxWbFpZeldjSitmczZHTFRPL29sd3A2a00yOTdZTmM4WjMrTEozOVZSSVZSS1pHSjR0WUFvVk04R1lIWTRZYUI0aXhVNURVZ0lxM3I5Z3NtaGxQbWdHNFRyOVN0WE90TVBOMXlwYTdhQW1NRzRuUlpkRTFzanVicldTTkdRM3ZiZTh4Z1c4eFpKK3IrTXp0eEZ3Q0srTGNmWUtWa0h0elZyeTBhREFDaE9vZ1NkZFpwZ1Rrc0F6eUUwM0VnQkVZS25jdW5GbmZhVWpTSUdTejFjQ1J1NUVFTFpSZ3dqODEiLCJtYWMiOiI3ODczNmI0NWQ5ZGI2ZDJkMjNhMWI1YWE0NDJkMmU4OGE5YzcxYmM1YjMwZjgxMmEzNWU3NjNkZmQ5NWVjODRhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVReW1sRXZGM3BYckxBTTFHWUZaaVE9PSIsInZhbHVlIjoiUlpQU1BiMmxSNlZ6aG4zZ01rd1c1b0YrQnBOTy9pem1XYWNDWjFTc0lLeDU2ZnZ6Zm40dGFQSE00VStFTU9CR3V0Z3FLWVAyaTNnV3YrZFBJZVpQd05EeGtoNno3ZDFZK0t3T09oeFk1Qk1RUmU4TEpTWDFINll1MkF3SVVYdXJNdDVsUGg5RldCT3psanR2eUpQNDdHRmw1eHN0R043QkpwT3ZjUXN2czNOS2t1U01UWTNpUjVDL1JvN1BhVVJQS0tYQUpRRWk0bjk4b3ZCRE5sRGpJRHF5Q0t0WUJ2aDZLUHdYNVIveERIV3dFQkpReFA4cWZOVDlkOEJnTnV6QnY0UVlSaHFGRm4yUFVTZE1Ia3Q0MW9Oem83U3J4dnFoR2N6Wkkrc1gxYzZTWXk2V3BTcHY5MUt6WWprZCs3eWtSS2lTS25xOVdneWhHbzRWVWxoK1ZNeS9qdjZiRERsa2ZyNkdCdzlFYlNFYmxRcXZMN3NsUGk4U1pKY3ZnRUJ6ZzBhd3Q4Q1czVmliSm4rQURDdyt2azVvUno5VWJSS2xOeEhKNVNHMWFVbldKQnpHTnlWckExcGtiTlRvbEMvVGxMakpBWGh6MXliSzZDSXUwdzdsUEVhNnpOTXlONTR1dVk1Sy9XT09UTTBhSE9TNjBGTXkxblBoMmhFUXVDMzIiLCJtYWMiOiI2MGNjOTBkZThkYjI2NTg0NDEzMzM1OTc4OWViZTk3ZjFiMWVjZjhmYmNlNDQ2NjMzMmJkYTMxNzQ2MDc3ZWJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1629054052\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1553233376 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1553233376\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1192289432 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNwa2tTUzQ3czVIbk1mTm9mbnB5OHc9PSIsInZhbHVlIjoicTdGUUR4T0NSRHhKVml5TnBEeGxiRkZDalRMZ1ZNcjJ6a1ZPTDFWREFLNUJwcENtbnRoeS9ZYnhURmhmMWVsektDT2hBN2p6NGZiK3oxRkdVaEpURytXR0FTdUNOWU83VG5IazlINFlWVisyNE5NdmdIekNkWUNGMnpNK2kzVUI5WWcyMDJuQTBEVEpGRkVaU3RRVHhidnVudGcrN241UjV6aHVZNTRWYkU5SlpxWEdWZWQ0SWlGSTFxRVFRek4rV09iYzdGa2o4T0xQVHNZTXFmM29FRWhxMzJQbTlTTWZ6Y3c4RGNqQmtPUUNGOW94WUMvOFdBdjBEdkh2OXp5VWtNc1RndUN0SmVER0czd01NRGxHSldGR3hlSGRjVTY4NUVpNHpHVHRHelV2MTN3ZTFkV1k0VHdCdFhub294MjF6eGpQSlUzdTNMdmZZZ2dXVUhVMEc1eGJpYW1MaHZna0NUSmtJSURrK2VlZ0FvbVhZb2VKa3RncnYwVjFkSkw5bVVDL1hxcWYwbzJUSUR4NHNmUlY1V2lINVFQZWFCZm1XNDlySEcvQWFPdUcvTzZoTGZIbDRndE82TVNEMDZUNE1pNUxMOFI5dUNEUVA4cDVCUHJwREZwdm9PRjY0MkZBLzdUeUkzTXRmN1JZVW00MTRiZzY5dm9vREltZGJlT0QiLCJtYWMiOiIxYzgxZmY1ZTY5OTM4ZTY1OTI0ZmVhYTdmMmNhZDFkZDIyMjFlNWQ5MjEyOGVlOWE5Njk2M2MwMDU5YjQ4ODAyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InF0UmNRTU83R0ZHSjR0WG5vejR6Smc9PSIsInZhbHVlIjoiU3NhYkZER2grdzMrWVBDOXRTblZGMlM3eEFOZHlSWEFqUW5DWVBENlh3ZVBFeVlRdGRlV25tQ1dBODJ4OFpXRHpvdlZsdlBFNm9CVHVTVEx4andmL0pJMWV4akJqSFQzcTNFQUkzT2MxUGlNbTFCQlFnTXhFb2N2RnBiMlE5aXBETC83K3dqQ3hDcjRSS2Q1T3VJYlE3TUtnalRBbklLbFRWa0dHSGRSeFpPV2FGejluYTRQTVRjYmk1MU04WlgzaXdvNkFGcDk5VmpVY0JGTFpCS1l4RFVMd1Nka0FBeVZEdzM2ZTIvcDUvSVorbWlNdncvWS9QdlMwSDdCQmJDZEpKQVVLY1l6cDVNWW1NK3I2ZnVZOUVXWHRzektGemJ3WFJuQXhLS05kZzc5WjQwTzhPcG5ROWVpRTkrdWpzNzNQSmdhcmhUNlN6T0FrR3YvMW4vdkZ5NWVtQ1p3Uk5uVXRnT2ozNkUzL2x5S1lmY3ZSeDBaUTNEcWpXRlpvM1BFeC84RlppSUNtVk1UeUNvbDJaVnRETHF0YkVENEhjZXQrd3JVRGxLTWJtTGVYM2I1b2FhQVdoR0pOOThuelNYcVY5MHM0QXQ1THc1a2xHaEE3L2VIaW42SWlid21ENWcvaGwwOXF3dzBKNjNUakpZaFN1cHEzSmt0MElxWEE2NEIiLCJtYWMiOiIxNmY2ZTMyMzUzMTBjYjgzOGFjM2RiZjBjYWFlNDdjMzllZDQyMGFhNTAwYmU0N2Q3ODI5NjAxM2NlZDBkODdkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNwa2tTUzQ3czVIbk1mTm9mbnB5OHc9PSIsInZhbHVlIjoicTdGUUR4T0NSRHhKVml5TnBEeGxiRkZDalRMZ1ZNcjJ6a1ZPTDFWREFLNUJwcENtbnRoeS9ZYnhURmhmMWVsektDT2hBN2p6NGZiK3oxRkdVaEpURytXR0FTdUNOWU83VG5IazlINFlWVisyNE5NdmdIekNkWUNGMnpNK2kzVUI5WWcyMDJuQTBEVEpGRkVaU3RRVHhidnVudGcrN241UjV6aHVZNTRWYkU5SlpxWEdWZWQ0SWlGSTFxRVFRek4rV09iYzdGa2o4T0xQVHNZTXFmM29FRWhxMzJQbTlTTWZ6Y3c4RGNqQmtPUUNGOW94WUMvOFdBdjBEdkh2OXp5VWtNc1RndUN0SmVER0czd01NRGxHSldGR3hlSGRjVTY4NUVpNHpHVHRHelV2MTN3ZTFkV1k0VHdCdFhub294MjF6eGpQSlUzdTNMdmZZZ2dXVUhVMEc1eGJpYW1MaHZna0NUSmtJSURrK2VlZ0FvbVhZb2VKa3RncnYwVjFkSkw5bVVDL1hxcWYwbzJUSUR4NHNmUlY1V2lINVFQZWFCZm1XNDlySEcvQWFPdUcvTzZoTGZIbDRndE82TVNEMDZUNE1pNUxMOFI5dUNEUVA4cDVCUHJwREZwdm9PRjY0MkZBLzdUeUkzTXRmN1JZVW00MTRiZzY5dm9vREltZGJlT0QiLCJtYWMiOiIxYzgxZmY1ZTY5OTM4ZTY1OTI0ZmVhYTdmMmNhZDFkZDIyMjFlNWQ5MjEyOGVlOWE5Njk2M2MwMDU5YjQ4ODAyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InF0UmNRTU83R0ZHSjR0WG5vejR6Smc9PSIsInZhbHVlIjoiU3NhYkZER2grdzMrWVBDOXRTblZGMlM3eEFOZHlSWEFqUW5DWVBENlh3ZVBFeVlRdGRlV25tQ1dBODJ4OFpXRHpvdlZsdlBFNm9CVHVTVEx4andmL0pJMWV4akJqSFQzcTNFQUkzT2MxUGlNbTFCQlFnTXhFb2N2RnBiMlE5aXBETC83K3dqQ3hDcjRSS2Q1T3VJYlE3TUtnalRBbklLbFRWa0dHSGRSeFpPV2FGejluYTRQTVRjYmk1MU04WlgzaXdvNkFGcDk5VmpVY0JGTFpCS1l4RFVMd1Nka0FBeVZEdzM2ZTIvcDUvSVorbWlNdncvWS9QdlMwSDdCQmJDZEpKQVVLY1l6cDVNWW1NK3I2ZnVZOUVXWHRzektGemJ3WFJuQXhLS05kZzc5WjQwTzhPcG5ROWVpRTkrdWpzNzNQSmdhcmhUNlN6T0FrR3YvMW4vdkZ5NWVtQ1p3Uk5uVXRnT2ozNkUzL2x5S1lmY3ZSeDBaUTNEcWpXRlpvM1BFeC84RlppSUNtVk1UeUNvbDJaVnRETHF0YkVENEhjZXQrd3JVRGxLTWJtTGVYM2I1b2FhQVdoR0pOOThuelNYcVY5MHM0QXQ1THc1a2xHaEE3L2VIaW42SWlid21ENWcvaGwwOXF3dzBKNjNUakpZaFN1cHEzSmt0MElxWEE2NEIiLCJtYWMiOiIxNmY2ZTMyMzUzMTBjYjgzOGFjM2RiZjBjYWFlNDdjMzllZDQyMGFhNTAwYmU0N2Q3ODI5NjAxM2NlZDBkODdkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192289432\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1144345447 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144345447\", {\"maxDepth\":0})</script>\n"}}