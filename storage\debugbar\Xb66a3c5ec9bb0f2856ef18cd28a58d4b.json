{"__meta": {"id": "Xb66a3c5ec9bb0f2856ef18cd28a58d4b", "datetime": "2025-06-28 16:01:59", "utime": **********.308244, "method": "GET", "uri": "/pos/MTQ0OA==", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126518.877823, "end": **********.308264, "duration": 0.43044090270996094, "duration_str": "430ms", "measures": [{"label": "Booting", "start": 1751126518.877823, "relative_start": 0, "end": **********.231935, "relative_end": **********.231935, "duration": 0.354111909866333, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.231945, "relative_start": 0.3541219234466553, "end": **********.308265, "relative_end": 9.5367431640625e-07, "duration": 0.07631993293762207, "duration_str": "76.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48448216, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos/{po}", "middleware": "web, verified, auth, XSS, revalidate", "as": "pos.show", "controller": "App\\Http\\Controllers\\PosController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=496\" onclick=\"\">app/Http/Controllers/PosController.php:496-526</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0028799999999999997, "accumulated_duration_str": "2.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.266676, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 54.514}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.27706, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 54.514, "width_percent": 14.236}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2911332, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 68.75, "width_percent": 21.528}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.29304, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 90.278, "width_percent": 9.722}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => show pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1223506741 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223506741\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.296361, "xdebug_link": null}]}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/MTQ0OA==\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]", "error": "Pos Not Found.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos/MTQ0OA==", "status_code": "<pre class=sf-dump id=sf-dump-2137986688 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2137986688\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1147152060 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1147152060\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-34545271 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdXRDJTODNweEFsU0JWR1UwczVDRVE9PSIsInZhbHVlIjoieTlXNUM2SFBGTnExSytqbE9peWVDMXFGdlFZNkVYa29IdlRrb1RlT0hBbXFtZExmenFMTDl2Y0N2ZW1GbHQ1czU0OEkyMHdpa2pxc3FQdE1qbFNnWUFuQ1JnS2lLcmFCT0tDZ21nZVhtbVN0ejZhSnZJLy9DbFFKMzR4WnlvQnVxejRNU3krYW1Mb3dCMFNNK2R2aDRMM3g5L1llY3o2TXlFcjV4d05yUnlwa3FObzZ3NXUrMG1jU2dXcWRhN1VUS2hMVDZ1a3VzVGNLZEkvdnVCbE5reTY0Y0pZY1k3SzM5MGF5UitDOVNIR2JRR2MxVUFGZ2FtVmVhVXgrdXc0WG1VWVFTODh0eUptSVpPWFFBR29DdkUrbXRjM1B5SHdsTk1JUGZyaEN2MkNabVZoR1BCVFZMZVhHZTdGTzBOZjc2M3R4ZDVudXRkZk1SQUNFV09SZXlyMHoreWwwMVJzQklKNFNtK0tGbjMxM3ZnWDFjSThEdXFOR2tGdXlac3cwb0FYYitBSHFzelZUWVBSVHRFOGtDdkQrNytTTUVNeVd2bDl6aHNpMFB5S3JEYWs0SFdzNkJSdVRrT241WHF4MDAwYnhCQXBCN3NyM0I2U050ajllbElSTHFYczdvekdrMzQ2MmJhVVNORTBvV0VKZDMydVlKTlVnMlhjam5iV1IiLCJtYWMiOiI1NTIzYWRmYzQ3MjQyZDkyOTIzODMyNjlkNjIwMjM2N2VkNmQ2ZmQzNTRhOWNmMmY2MmM1YTU0YWU1YTZjZTZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNaRHhKVzUxa1V6dEUycGFlSHcrWmc9PSIsInZhbHVlIjoieEhueEl5bGpGeFY4eHNWQS9Lam9MZStKQVFST3JmOHpEYkhPdXJwYTAyNDA0UFRuS0llWnBrSWdkbWplbHcxbTltcG4yckhCbDFITEFaRE9sa28rYisrdzdxaGdqM3ExaE5OaTRobFBKcng0OGZycW5qK2NkVnM4c0ZhZ21zRGlpMGViNWpoQnNKV2p5UDY2UFQwb1hEU0lsakZLbnlxRzVBa0dKdG1OSnVSclRXNllCZmdVNEl1emJHditKTEpxdllpTU9IY1F4UXdNQ2RCdHpzemEyS2R4SG43cjRQVkhNWU56ekZvVktXRzhtVkRyYVhQa3VVMXdxTFNhekdpalRmbEVobERMcmFyVUlXc0ZuQXZIWmRFK0lldEJFOFk1MnhkYnVuOFpoU1MydDhpVzZPbktEM2svZ1BIbHhQaHpnOExDRzZkOVM2MlRjWWtmb3lub2NWQ0srWkhOMWFOOHQrcTh6ZkcwWXR6MlhCdlpqNVdnTUdBWTBWOXdBR1pjV0Z3cm5ERnhNdkFSQ25rNFR0S1RNV2FrNFNxQmZZRVNkWWZmOUg2QWwxVVFWSEFxWWQ3RFR3WW5VV2daWVNYT01BanFZejluREFoK3dVWEhGTHNmNHZEK0tEd3NtNUZhaitOa1NpdHRWWlFrR2pvcHBUWHRDK1Q2YVF1T0NJVmIiLCJtYWMiOiJkODFjYjRlMDNhNmUzZTY5ZDA1YWYyY2U0NjE4MzQ1NzNkY2FhNWZiZjFmZDRiN2Q4MmNmYjNkYmRiMzk4ZmZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34545271\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2043545027 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043545027\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1730681143 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImV3NnNGTFVqWFVkRjlvRUppc0xsdkE9PSIsInZhbHVlIjoiRVRlZzR5eWZSenFOV2dsMG8wbVh4bmNEZU9DcUloMTVkN3F2MGpVRWY4cXQ1dWJNSmpXdzgxSUkwaDNyQk5sWFY2UE1BVktJWTQwTzRaRnBWVjhNOEtsd0pWSzJreXdlVGxWNnFWbGZQdnZFS0ZkSVFDUXNlWUhuM2lweWNra01kWUtJRmRyWm1meExldjNqVWtueTArUVEwOEhXNTRZcTRSYVFuQURoTllxUERwV2RMNlFPRXRLelg0b0Zkck5FdmdzWStoMTliSkdSREIxU0hCS04zbGxhL3J4M3ZNd3FUTUxsK2pydlFpRStCQ3M5WHE1MDdrbVg5M0I4amUvN3kwbGtnSllUYmRsMXRySTQ1WEZPOVRiT0lRSS84TzlOaHoyWE9DeGliN2x5b3pzZ25MK2hvUFhWdHFnckpGVzZWblN2OUNSdEhCMmpJNkpVRnhwSkpCelJSM0JVTTNzV0x6c0l3UC8vZkhXMTdRWElqNHRZMjFKTnNacG5WZ1ZhSi9vOWRvaGZXbkxLNFNlbEVUd2o0Mjl1N1Vha2prWHRTNjN3YXdLYlp0YzJMWE9IbHZmRVZOWXpvUVFxRStxTGxycnAydzUxSmJLUGtLaVRtemVtdCtLckV4Rm1FNmxoUXJJNFdodlZnc1VwLzlKeG5mSlk0LzZiTlQrYlB3Q0giLCJtYWMiOiI2MGU5ZGM3MzI5ZTQ3ZTg0NWFjZGE0NzZmMWY2MmMzYzljZmIwMDdjMzI1MDhmNGQ2NTQ2NWQzYTEzZDZkMzNjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlHZTBEeDRiRlZiZGViNHEwbmlSSEE9PSIsInZhbHVlIjoiL0I0Q1R5TitLb0hMaVRqTmxvbTc5bEx1ekUwMGo0eWVrN0tyQkExbHd6eEMzNk1FN2h5WGwxaXRmZnVteENaVUJiYmprSHNYTko5SU05T0pCYXFFdkgyNFdXWDdneFNFWlZ0eWM0clZua2c4THlZWUJINWMzUWVYL1M5dFlwWFhUbHlIdEpNTENWQ1BTV0JScTB5WHhYSUlxN0Zaa0RodWNmTktQYnc4cFpQbFJpclNjT2JZY2RhZU9jVmREcTFWcENPTGVLT2kwVS9WY1pwdExCa2xxd1NvSnV5Tk5IM0lkM2pZQ042Ujlnbi9iVllYNmorUWlUamIxMTBmN0pUdGg2QXVENGVzT0xneXZ1aS9aY1J3ZVR2SkJCeGpxYnhRNFJoT1dMNHlORlllUE9lQ0RocEg4Vkk1aW5udkRCd0oydHJtY2R0T2JTdGJ0UXBydjNZcS9oQ0NpdUcwbmc1WW1uSlE1Yk13SWx4bG1pRGZETm9oMVZQZDFIYlVOK2IwQ3JIZ2RmTzBsMHdXUVFIY0ZRRDZFQzYrNWNlZDY0VGl1bjh6cUl4Y2lTVDdiRGdxY1pIUG1pUVpVUHYxNFNPZEdOelM4RTFYMXE2QWpweitleEprUk90VzM2cllNWHlqT1Z3MndkK01QYUk5YnJqbGpWdXpYRWw0RXdSMVFyeGIiLCJtYWMiOiIzMTNiOTg0ODhhMzMxNmYxMmQ4NzBiMmE0NWNiMGJkYzM2NGJlNzVmMmY0ZTJhMWViZjZkMDljOTllMmI4OTIxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImV3NnNGTFVqWFVkRjlvRUppc0xsdkE9PSIsInZhbHVlIjoiRVRlZzR5eWZSenFOV2dsMG8wbVh4bmNEZU9DcUloMTVkN3F2MGpVRWY4cXQ1dWJNSmpXdzgxSUkwaDNyQk5sWFY2UE1BVktJWTQwTzRaRnBWVjhNOEtsd0pWSzJreXdlVGxWNnFWbGZQdnZFS0ZkSVFDUXNlWUhuM2lweWNra01kWUtJRmRyWm1meExldjNqVWtueTArUVEwOEhXNTRZcTRSYVFuQURoTllxUERwV2RMNlFPRXRLelg0b0Zkck5FdmdzWStoMTliSkdSREIxU0hCS04zbGxhL3J4M3ZNd3FUTUxsK2pydlFpRStCQ3M5WHE1MDdrbVg5M0I4amUvN3kwbGtnSllUYmRsMXRySTQ1WEZPOVRiT0lRSS84TzlOaHoyWE9DeGliN2x5b3pzZ25MK2hvUFhWdHFnckpGVzZWblN2OUNSdEhCMmpJNkpVRnhwSkpCelJSM0JVTTNzV0x6c0l3UC8vZkhXMTdRWElqNHRZMjFKTnNacG5WZ1ZhSi9vOWRvaGZXbkxLNFNlbEVUd2o0Mjl1N1Vha2prWHRTNjN3YXdLYlp0YzJMWE9IbHZmRVZOWXpvUVFxRStxTGxycnAydzUxSmJLUGtLaVRtemVtdCtLckV4Rm1FNmxoUXJJNFdodlZnc1VwLzlKeG5mSlk0LzZiTlQrYlB3Q0giLCJtYWMiOiI2MGU5ZGM3MzI5ZTQ3ZTg0NWFjZGE0NzZmMWY2MmMzYzljZmIwMDdjMzI1MDhmNGQ2NTQ2NWQzYTEzZDZkMzNjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlHZTBEeDRiRlZiZGViNHEwbmlSSEE9PSIsInZhbHVlIjoiL0I0Q1R5TitLb0hMaVRqTmxvbTc5bEx1ekUwMGo0eWVrN0tyQkExbHd6eEMzNk1FN2h5WGwxaXRmZnVteENaVUJiYmprSHNYTko5SU05T0pCYXFFdkgyNFdXWDdneFNFWlZ0eWM0clZua2c4THlZWUJINWMzUWVYL1M5dFlwWFhUbHlIdEpNTENWQ1BTV0JScTB5WHhYSUlxN0Zaa0RodWNmTktQYnc4cFpQbFJpclNjT2JZY2RhZU9jVmREcTFWcENPTGVLT2kwVS9WY1pwdExCa2xxd1NvSnV5Tk5IM0lkM2pZQ042Ujlnbi9iVllYNmorUWlUamIxMTBmN0pUdGg2QXVENGVzT0xneXZ1aS9aY1J3ZVR2SkJCeGpxYnhRNFJoT1dMNHlORlllUE9lQ0RocEg4Vkk1aW5udkRCd0oydHJtY2R0T2JTdGJ0UXBydjNZcS9oQ0NpdUcwbmc1WW1uSlE1Yk13SWx4bG1pRGZETm9oMVZQZDFIYlVOK2IwQ3JIZ2RmTzBsMHdXUVFIY0ZRRDZFQzYrNWNlZDY0VGl1bjh6cUl4Y2lTVDdiRGdxY1pIUG1pUVpVUHYxNFNPZEdOelM4RTFYMXE2QWpweitleEprUk90VzM2cllNWHlqT1Z3MndkK01QYUk5YnJqbGpWdXpYRWw0RXdSMVFyeGIiLCJtYWMiOiIzMTNiOTg0ODhhMzMxNmYxMmQ4NzBiMmE0NWNiMGJkYzM2NGJlNzVmMmY0ZTJhMWViZjZkMDljOTllMmI4OTIxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730681143\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1499155764 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/pos/MTQ0OA==</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pos Not Found.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499155764\", {\"maxDepth\":0})</script>\n"}}