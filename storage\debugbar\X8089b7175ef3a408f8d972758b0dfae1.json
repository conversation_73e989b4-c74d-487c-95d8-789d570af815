{"__meta": {"id": "X8089b7175ef3a408f8d972758b0dfae1", "datetime": "2025-06-28 16:01:36", "utime": **********.640209, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.206788, "end": **********.640227, "duration": 0.4334390163421631, "duration_str": "433ms", "measures": [{"label": "Booting", "start": **********.206788, "relative_start": 0, "end": **********.576667, "relative_end": **********.576667, "duration": 0.36987900733947754, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.57668, "relative_start": 0.369891881942749, "end": **********.640229, "relative_end": 1.9073486328125e-06, "duration": 0.06354904174804688, "duration_str": "63.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46401800, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2396\" onclick=\"\">app/Http/Controllers/PosController.php:2396-2430</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00346, "accumulated_duration_str": "3.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.617227, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.636}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.630888, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.636, "width_percent": 19.364}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1762396830 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1762396830\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2006498260 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2006498260\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1910238316 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1910238316\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1908737100 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlB2akt6MDRacE9OMFFmVW5VVlgybmc9PSIsInZhbHVlIjoiZ0IxTE13eTRITHNUL28xQ2dodldneXFzMm5DZXZjUXVHWkxHbGt0T1VuT0tnaHFDL0t3dGxxTHRqRHNhTWc3Mk0zTHo4ckhoQldOTFUvaVhwQVd0ZGlSd2hYc0JQK3BxUGN1NHRsaFZyeW9PbC9YZXdVOWdqZVlOdHgzMC96a0JvdWswc1FoYkl6ZFQvTEdpbllBZ3JwWU9sMjBxb1h3VXRtTnhvemJIL2RvNW11Z2V6VHpJdXpTZGVSOXBrbWxRZDhSNVB4bEltNnY0bWxsUWZsc25kOEJyRFFBMG1tanRTTDI0SWpxMDVUOU55cXpwN3A3SDlsc3NyUmdINmptUkl1YnJmdmFYUEg5K2M1ZnRFR1JvR0J6TGZoRVErZ2ZkRmJJRkJHUGdkeWJSTW40cms1blZOOVpmRnRGYm9ndTFaZ01LUEhlZ1hrbjFUbnJFY3Nib093THNJcE9mcnluejB5cU9NYVplNmRKRDZNNUd1K1NwOGlVbTRiY3hab3cxTDN6YkcxSVBQQWZoMTE1aWRRU01Oa2Q2ZGJKaE0zcU1XcGZCbVZ3Y3duVVVwNUFhQmhuK3EvMWZYZUdPOGdhaC9oY0RFb1RkMjB3Z05MZkk5Si8zN3FKZnpkcm1nYTVJblNkOFhPbVZuSVFpeWM1Z2JGTFI1OHFMbTEwWVpMYWYiLCJtYWMiOiIzNGJiOWUzZDhhZTA0ZGVhOTA4ZDhjZmNiNzI2ODVkYmEyNDhlNWQxYWQyOGQwOGZhMmUxMWU0YTU4MWMxNmJjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBuV0dHRU9pbXQ2NnFTbUdRNTlmS3c9PSIsInZhbHVlIjoiUENBVVI5anBUZUtBWkNsdVJCWWI3R1djcGNsSzJmWW9MTXVrM3pIVnhMamluVjUvV1FFbEpXdEVpQWNTK0draFoxTHVva3llS1preDhxbkhHKzFzZFpqOGM5V1FWTEJkNG5pUXI0TUNKbEhqY0h6UnpBaGhzbC9nb1FBUXVRTWdHTVVRem9OVTJJQVFnVjZnUERPaXFvNG1tRmNlMlJ0ZXRaWThobTJmaWJKSlJuWmxoZzUrQTRkekt2Umk3SmpyRGlhNzRibWtpNkQvN1hKK29WNEh4anAzQjVnMUxLdzZlRkplaFlkZVVHZkJGM2ZOa1hURTVLQ2FEd3hSaHFIdXVUYm1GVEc5cFY4Y1NiTjFKc1M3MUovWVBJZDJYNUZjVmZnUjZxa1hhUEZITzhLQndBRzBCTTc5T3R0UWpzbk5VMEdZRC85TXVnellSWkJYUThsb3MwQjlyZ1JBT3d4blovYXpSaFp4Vkp6cjVhMGxZb0Z3Vmt5RDdIdEpBSVRDS0ZIVlRhQk9rZ0g2TkNBQ2ZuQVBIY29rK2tXK2gwc0FkVVdUY2FOR3NuUVlLVHI1N0l5bnRkT2o0Z2lQb3AzOTRyMkViNWhleGNPRmQ2M2dsUThEOWhjRCs4Y01jbWF5dWFKMTZDQ1RqemwrU0JDSm5ZUS9UQUhScjM2UGh3cysiLCJtYWMiOiI1OTQyZTNmNzkxYjU1OGVhYjRmODQ2NWM4ZmM0M2RlM2IzN2UxOTI1YzAxNTI1NWVlNDkyYTdmOTA4OTcyZmNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908737100\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1996849713 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996849713\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1154713163 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inc2ekx1cXA5R2JzT0lKQkRrUThqdFE9PSIsInZhbHVlIjoiejluYXFnanJrcXhhMHkySGIyUmhmTHAxYktpUmQybk9CZGorTXJnYWIyQVl5VHAydWFFa3dTVC9pMExPQmlsQlk5ZFVvRVZIUXFJY0N3NTU4MzBWMk5nOUVrZ3lPL3NiVGhKR1NJc0RJcXFBVjRxNFhhbHZFNHBvOWZna0lZVEJvMkw0V01NNGdlMTVucWZTWWFGRGtCMi8zNG5CQ0NlTFFzbmNoMCt5eURqZkRlKzdncDZEOFc1S0JIT1pyNDh6eDVkRnJuaXRjYXdFeko3eUFOc2JERjV5cHdSZ0JYUkpwa1N3bVhjVUtsbGlidkpMVFZDbDVOWjlrQlhqRXZGZUo0Wk94R1pWK2Jna3pxcUQrYkJONDd6R0Zja1pFeHBQaGtWdEVQL1BZSmJ3ajdqNndPMUpBNUJBME5tNjc2bTlUaEdib0hRbU9Fd2ppbE80SWRIcFdjb3huRjJLVlg0RHFDT0w5OHVxMUNITG9RU1FxenRvVmVkY3R4SnZienFNV09JcnVwZXhtUitrczdKeDZzRnZXWHpoSzVHRGl3TlBqWUtUeTZqTlB3TVpkcXhCcEdFMVFWUmg1aFE1OGlyMWtiK0xuSU1BdlpjMS9yWHZtOHlSNk1oTjdpdDFaOFFGNkRlZFVyQStacWVNNjd5UEhGTEhNY1lqTVQvQUI0K3UiLCJtYWMiOiJkZWQxNDZiOTc4YjNhNWJlODlmZDg3YWU4ZmI4ZDI5OTlmZWJlNGM4NmFjNzk5YzllMmY1MTk4YjM3NTg1MzY4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikd4cmE4VXlzV0svS1RtSWp2RzNUeWc9PSIsInZhbHVlIjoiY2VWZlA5SjVRQ2l5UDZ0QmRpQ0xtVnp1RElCb3o1OGNqQm0yM2V1T2xBREZUMFhYVVp5dXdGbWw5a1M5NzluTEFGT0ZJMHdINjlqMGpJTXFmVHBqcndMQnMwQ0c1dytvQjBvaE1xU3NMWjQ5cVIvK0lHYWJHZUw3UWV0TDQ2RWQreS81L3Nib2RST05NVWNpVUJqOFFlU2dlc3NIWjkrb2IyaGRLK2ZMTTVFNzl2VTFwMFZqMTliR2tETHIvdmdQd3ZGMU1uZWFIMjltSXRRV3pabUdNUXF1dmVjNUpmWmFua1Q3Vnpnd3RCeHhLaDk1MlBPc0gwVlczbnU1QUdLYTNpVWw1dnA5V1BrWUNQMmMvZUpLNzRJM3RGbmVnaENyTGQ0ZllQM1lTZFlkQkMva1gyNXJScjB3WGg5T3RBdXF6aExVcFhrQ05BTDlkVDhrOU5acWVmeUxlRStpRk1zZXdmVDhtcXlqRWZmbHhsZk5BTEMvd3lybFgvMThVRFpvY0I1SjZobm4wVkE3QXNHRGdrSnBEV3NBSUR1cHVSZmJ6SCswaUdmM0VoeDIvbCtMQldJVEUwdk94N0d4RFF1SXczQStMRTAra0JFcWV5QXkvM2k1M2ZBQWpVUXVPOXh6OTB5QkRYVW9ocHE1ZzN0UWVPVWJoSUk1N0JyYzc2SGgiLCJtYWMiOiIyYjA5Njk0Yjk3NzkxMDk3NWM0ZmFlNWFmNjA4NjQxYWU0YTBiNmI4ZjQ5Mjk3ZWU1MzBlNjhjNTM4ZGYzNDk0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inc2ekx1cXA5R2JzT0lKQkRrUThqdFE9PSIsInZhbHVlIjoiejluYXFnanJrcXhhMHkySGIyUmhmTHAxYktpUmQybk9CZGorTXJnYWIyQVl5VHAydWFFa3dTVC9pMExPQmlsQlk5ZFVvRVZIUXFJY0N3NTU4MzBWMk5nOUVrZ3lPL3NiVGhKR1NJc0RJcXFBVjRxNFhhbHZFNHBvOWZna0lZVEJvMkw0V01NNGdlMTVucWZTWWFGRGtCMi8zNG5CQ0NlTFFzbmNoMCt5eURqZkRlKzdncDZEOFc1S0JIT1pyNDh6eDVkRnJuaXRjYXdFeko3eUFOc2JERjV5cHdSZ0JYUkpwa1N3bVhjVUtsbGlidkpMVFZDbDVOWjlrQlhqRXZGZUo0Wk94R1pWK2Jna3pxcUQrYkJONDd6R0Zja1pFeHBQaGtWdEVQL1BZSmJ3ajdqNndPMUpBNUJBME5tNjc2bTlUaEdib0hRbU9Fd2ppbE80SWRIcFdjb3huRjJLVlg0RHFDT0w5OHVxMUNITG9RU1FxenRvVmVkY3R4SnZienFNV09JcnVwZXhtUitrczdKeDZzRnZXWHpoSzVHRGl3TlBqWUtUeTZqTlB3TVpkcXhCcEdFMVFWUmg1aFE1OGlyMWtiK0xuSU1BdlpjMS9yWHZtOHlSNk1oTjdpdDFaOFFGNkRlZFVyQStacWVNNjd5UEhGTEhNY1lqTVQvQUI0K3UiLCJtYWMiOiJkZWQxNDZiOTc4YjNhNWJlODlmZDg3YWU4ZmI4ZDI5OTlmZWJlNGM4NmFjNzk5YzllMmY1MTk4YjM3NTg1MzY4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikd4cmE4VXlzV0svS1RtSWp2RzNUeWc9PSIsInZhbHVlIjoiY2VWZlA5SjVRQ2l5UDZ0QmRpQ0xtVnp1RElCb3o1OGNqQm0yM2V1T2xBREZUMFhYVVp5dXdGbWw5a1M5NzluTEFGT0ZJMHdINjlqMGpJTXFmVHBqcndMQnMwQ0c1dytvQjBvaE1xU3NMWjQ5cVIvK0lHYWJHZUw3UWV0TDQ2RWQreS81L3Nib2RST05NVWNpVUJqOFFlU2dlc3NIWjkrb2IyaGRLK2ZMTTVFNzl2VTFwMFZqMTliR2tETHIvdmdQd3ZGMU1uZWFIMjltSXRRV3pabUdNUXF1dmVjNUpmWmFua1Q3Vnpnd3RCeHhLaDk1MlBPc0gwVlczbnU1QUdLYTNpVWw1dnA5V1BrWUNQMmMvZUpLNzRJM3RGbmVnaENyTGQ0ZllQM1lTZFlkQkMva1gyNXJScjB3WGg5T3RBdXF6aExVcFhrQ05BTDlkVDhrOU5acWVmeUxlRStpRk1zZXdmVDhtcXlqRWZmbHhsZk5BTEMvd3lybFgvMThVRFpvY0I1SjZobm4wVkE3QXNHRGdrSnBEV3NBSUR1cHVSZmJ6SCswaUdmM0VoeDIvbCtMQldJVEUwdk94N0d4RFF1SXczQStMRTAra0JFcWV5QXkvM2k1M2ZBQWpVUXVPOXh6OTB5QkRYVW9ocHE1ZzN0UWVPVWJoSUk1N0JyYzc2SGgiLCJtYWMiOiIyYjA5Njk0Yjk3NzkxMDk3NWM0ZmFlNWFmNjA4NjQxYWU0YTBiNmI4ZjQ5Mjk3ZWU1MzBlNjhjNTM4ZGYzNDk0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154713163\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1576443732 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1576443732\", {\"maxDepth\":0})</script>\n"}}