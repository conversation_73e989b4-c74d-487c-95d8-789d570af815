{"__meta": {"id": "X76a5d757402c77ad42ff8d80666c067b", "datetime": "2025-06-28 16:10:24", "utime": **********.193737, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127023.741775, "end": **********.193757, "duration": 0.4519820213317871, "duration_str": "452ms", "measures": [{"label": "Booting", "start": 1751127023.741775, "relative_start": 0, "end": **********.137687, "relative_end": **********.137687, "duration": 0.39591193199157715, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.137696, "relative_start": 0.395920991897583, "end": **********.193759, "relative_end": 1.9073486328125e-06, "duration": 0.056062936782836914, "duration_str": "56.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45700216, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00295, "accumulated_duration_str": "2.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1656399, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.373}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.176123, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.373, "width_percent": 17.627}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1829338, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80, "width_percent": 20}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2118011233 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2118011233\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1867603258 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1867603258\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-689420340 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-689420340\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-345272302 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127021650%7C31%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFsTHdUeXZwVjhxZjNKa1FuRC84Q1E9PSIsInZhbHVlIjoiaXVFUXVFbXZBbnNjWWxZQjdwUVZqcmpEUUFNTFR0bEJ2YzFXZW5GMnE1eWFsUzNkMDdiVkUxQm5Ya29XWDRYU1AzT01KZFFzZ0ozRW0yaXBNM3R5OFB2VXVuNUpWYVBMcVNRTWtrbkNMZnhYZTBrRS9FbXZreVpRS2VidXA3NlNuTmZxcUxQOFptdmpMU3ZNU2ExYXhHdEhlSjVPeE9NUUR1cUQvOW5QUXhJdE03MVBDbXYzVDFSSWNBaUlWdUZLcVoydVJWcnVhQ1NZVUNycUEvTDhFUXR4SnFPYjBxcnZHdWdtcFJkMWgvUklyOHJoMDFBT1VlLzBNUmlaclNGaUpzUEQyRy9wanljbnZtQnB3YUNSQ0tLLzVPUkhiRzNQVlZOblo2VlJVa09PV3ZNblBVTjZvUmxlRGtYY2VDNEJ6THRBL09jaTA4NThNdFdRb1dvdzl5RXYwWm1hK0tpZktOOExuOHBCd0NnZTlXMEo3YVk3UkVwTGRtMnRrdHRkMzBlbXRTQlFwSWpUM1J2RTVZOGJLTnBHRG5vN1Y3V25yNFJFVlBHUTBnd3M3RndwRkVNSFg3b2Z2ZGVuVzNOZU9CSStSQUhTV2s5VkFqWkJxeHhRNWxwTDh2OVk4WTVmZitMM1RIN2dyNjFiZ0M3dTBXcWdZMnVmREhVS2F1TkQiLCJtYWMiOiI2MDEzNTMwNTE4ZmMzOThhNTQ4N2M1Y2NkZTExODFhZjhlYzNiMWU1MWFlN2VjNDA3MGFkZTFhN2Y4YzIzNGM0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhsUUkra1cvY2FYWThqQm5ydXNsNGc9PSIsInZhbHVlIjoiTGJYWWlLNktLdkluYlJ2VHd4Rjd5RXZiemNFeWZEOEJCNzh1eTd2R21PUllPNzhvenUyanVjWnZ3enNEdmJ4NjQ4RmpZRncrd3VRT0hPYnpPa2NiekNyUW9iaEVVY2wzNFZ4cXBnUFRQRXRlc05oeFFMdG5jRloxU2pMWUhuMG40Z2RHN1dYb1piUDU0YWNwUGh6TkxsRCtJVmNBUDVjalZwRUhBbStXQnFiWFhHTzZ3UWdUdW1WaVFLeFVVY2JBK0J1eXBYeENrSzk1dHFmMmd0UEYvaUcyRWlSNHpvNjZjeHY2N3RJQUF0SEdyQ2FiSDJmUzdLY2ticE9ES2E2cUVzOU9wTGVNYUZFaHNSQ0FTRzlMa21xY3FBcHhueVhWZ1dpQXZibEpPbWdUQlVJZEFjYlhtWlIvV0FlMXhaWWhKKzQrZ1Z3YVcySlNRVlNoUFB0UE54VHp3VFIzWll6dzkwNkd2NGhRckhUQUllOS9WTjNEdU0vZkJXOU83OU9peHB6S1JkdDFFdVZRbjFISDkrb0RtK1RUTzVKSnNyRWZYR2M1N1dxK2NsWElBdVRtR3VZVkVSbUh3L25yRFlsRFo0a1JnbnBhZWpuNDZYNThGb2FTcEdSc2Rka25hbTFTcnZhcDZSOG8xNXJZYU5QZk9LQ21Fdm04NUlMTVRNaWUiLCJtYWMiOiI4OTIwMmRiZjFmNGQxOGYwYzdmZDYxODhjZjk5YTAwYTM2ZDYwN2Q1OWIwZTljNzgwNDU4ZTkwZGZlNzhlMTU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-345272302\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-600424031 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-600424031\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1677663070 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:10:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImYvdGJzVXlBWXJTQkNRQWpKRzgxWWc9PSIsInZhbHVlIjoiSFY5QVVTaWY5dCsvNnFoZlpVYytTVnlpeXAzejZyQy90VSt2bXJhRHNQb2xhS0lBS2JJM2xITmxOdU9JMGtJUzBKajFsUVFuVmR4NCtZTE15Z3ZSU0xTNlZjaS9Lc09DenBWbHFOdytQNzV0eEh0UjJPbjNueTA5SGFubG5aQkhrU3kzcHRIcEtRNk5aNmlyRHpYWWFkaTFyNHlIUDVNUnF0cUJUdnhNNzI1VmRhRWdtcGt5enpkclk2NHdkYkJpc3J6M0ltQXZCeDJZdHFHNFkwM2dBNVMwU1dsZERZbUY1Q1lISzNsQjFXNll0TW9SZzB6MlNZQit4REQwbnZmRjVlSE9BV0JiK0JzbmRwL0QvRzlhNUtGQWNLWTFMT2tCQmNSUDBEeDFRL0N0UEorZHZKM2JLbXhBTWoyQWpRdEVFM0V1aXN2Mm9wM2ZyQ20vYk9hejBQeENlOTM2dDk0TFJHVU9nRitMOGhmNWExOEJOVDFjQkYwaU43MWNyWUQ4NnhKVVJ4VzQrNVk0VDBUWmVIQ0Q3OHZxN0s2cGxaaEF6ZUtMTERPZWlGYzcwSVlhWjdLQjQ2TjVHeStHOU1PQzZBNGZkK3NNa2FYZUZDaGpia2o0VUFoakxpZkE3VUpWVjBtRTlYRHhDdWtUcmpmWXhLOXREeU1wMXJxUUc5cW0iLCJtYWMiOiJhZjhhODZjMWNhZTE3Nzc5MmJmMzI2ODFiYjllYWYzMTkwMzBiOGRjY2FhMzNkMDAwY2YyNjA1OWMyYmNmOGU4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9yMGNKUTNFL1NadU10c0QzaEZNenc9PSIsInZhbHVlIjoiU2hSckx1UWo5UTk4cEZwZmFGMDl2L1ArZ1FscE0xLzdrcG9FUW1yR2kzVUxUdW81ay9yYUlKWFVFTlJ4OG9mQ1NvMzgxVHFtc1RDTlFFN3phYVFuS2VpNC9ENGJxbWRYUk5zMGgyV2tuOWtGdnAzZGU2dndIOXJ0YXhVN2ZKK1BHOURUQUc3RE5WVm9TV0g1bzJnTWpDdzEwS1I1K3pXa01tYnA5RE5nMTliSVJVZUF2T21SSEYrTy8xTVp3Snd1d0doUU1zdzlNN3NPZ1J5WTRyODl6bFEzQ1pXcy9ZRVNPZE9IYmJIcmlRaDFTZ3FSUWtBaDFmbXdBenJpdXNRUWRTcU5XTS9pNW9qNFQrUE9EekYvOThHdFFlMjQwWDNiRkR1TkYyZ2RNNGQvWmhsTlF1SGFoM1pzQkxUa09qT3R2U013ODdFS3ZxT2s1cEkzMWdPc1NVV2pUbjFHbVRSNGhVUnFCTHJ2OENSa0kzQkxqam11ZXlyeEpmZ1J1bEF5M2F6QWxBNVV6NWlWYUF2b2xXSE9PQzlMUFI4NDc5SFIwRnhybk9oUWdpd0gxTUdJQUhVQUtjR1o4WGhFR3RCelplRCs5cExlTkgvUWhCNFdmb2FNdS9iejA1T0dJQlp4b2FlOU1BOFBoblRqNU1FVDVNcTZMV0RZYUNOV1JpTUciLCJtYWMiOiJkNzkyY2M5MzA2YmQ3YWM5NDViZDMwYWFlNjIyMTkyODM0MjQxYjAyZjJiMWI1ZjdjMTZjZTE0ZGVjNGUzY2E4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImYvdGJzVXlBWXJTQkNRQWpKRzgxWWc9PSIsInZhbHVlIjoiSFY5QVVTaWY5dCsvNnFoZlpVYytTVnlpeXAzejZyQy90VSt2bXJhRHNQb2xhS0lBS2JJM2xITmxOdU9JMGtJUzBKajFsUVFuVmR4NCtZTE15Z3ZSU0xTNlZjaS9Lc09DenBWbHFOdytQNzV0eEh0UjJPbjNueTA5SGFubG5aQkhrU3kzcHRIcEtRNk5aNmlyRHpYWWFkaTFyNHlIUDVNUnF0cUJUdnhNNzI1VmRhRWdtcGt5enpkclk2NHdkYkJpc3J6M0ltQXZCeDJZdHFHNFkwM2dBNVMwU1dsZERZbUY1Q1lISzNsQjFXNll0TW9SZzB6MlNZQit4REQwbnZmRjVlSE9BV0JiK0JzbmRwL0QvRzlhNUtGQWNLWTFMT2tCQmNSUDBEeDFRL0N0UEorZHZKM2JLbXhBTWoyQWpRdEVFM0V1aXN2Mm9wM2ZyQ20vYk9hejBQeENlOTM2dDk0TFJHVU9nRitMOGhmNWExOEJOVDFjQkYwaU43MWNyWUQ4NnhKVVJ4VzQrNVk0VDBUWmVIQ0Q3OHZxN0s2cGxaaEF6ZUtMTERPZWlGYzcwSVlhWjdLQjQ2TjVHeStHOU1PQzZBNGZkK3NNa2FYZUZDaGpia2o0VUFoakxpZkE3VUpWVjBtRTlYRHhDdWtUcmpmWXhLOXREeU1wMXJxUUc5cW0iLCJtYWMiOiJhZjhhODZjMWNhZTE3Nzc5MmJmMzI2ODFiYjllYWYzMTkwMzBiOGRjY2FhMzNkMDAwY2YyNjA1OWMyYmNmOGU4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9yMGNKUTNFL1NadU10c0QzaEZNenc9PSIsInZhbHVlIjoiU2hSckx1UWo5UTk4cEZwZmFGMDl2L1ArZ1FscE0xLzdrcG9FUW1yR2kzVUxUdW81ay9yYUlKWFVFTlJ4OG9mQ1NvMzgxVHFtc1RDTlFFN3phYVFuS2VpNC9ENGJxbWRYUk5zMGgyV2tuOWtGdnAzZGU2dndIOXJ0YXhVN2ZKK1BHOURUQUc3RE5WVm9TV0g1bzJnTWpDdzEwS1I1K3pXa01tYnA5RE5nMTliSVJVZUF2T21SSEYrTy8xTVp3Snd1d0doUU1zdzlNN3NPZ1J5WTRyODl6bFEzQ1pXcy9ZRVNPZE9IYmJIcmlRaDFTZ3FSUWtBaDFmbXdBenJpdXNRUWRTcU5XTS9pNW9qNFQrUE9EekYvOThHdFFlMjQwWDNiRkR1TkYyZ2RNNGQvWmhsTlF1SGFoM1pzQkxUa09qT3R2U013ODdFS3ZxT2s1cEkzMWdPc1NVV2pUbjFHbVRSNGhVUnFCTHJ2OENSa0kzQkxqam11ZXlyeEpmZ1J1bEF5M2F6QWxBNVV6NWlWYUF2b2xXSE9PQzlMUFI4NDc5SFIwRnhybk9oUWdpd0gxTUdJQUhVQUtjR1o4WGhFR3RCelplRCs5cExlTkgvUWhCNFdmb2FNdS9iejA1T0dJQlp4b2FlOU1BOFBoblRqNU1FVDVNcTZMV0RZYUNOV1JpTUciLCJtYWMiOiJkNzkyY2M5MzA2YmQ3YWM5NDViZDMwYWFlNjIyMTkyODM0MjQxYjAyZjJiMWI1ZjdjMTZjZTE0ZGVjNGUzY2E4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677663070\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1120224370 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1120224370\", {\"maxDepth\":0})</script>\n"}}