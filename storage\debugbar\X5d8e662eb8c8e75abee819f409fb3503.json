{"__meta": {"id": "X5d8e662eb8c8e75abee819f409fb3503", "datetime": "2025-06-28 15:03:16", "utime": **********.326578, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122995.869164, "end": **********.326592, "duration": 0.457427978515625, "duration_str": "457ms", "measures": [{"label": "Booting", "start": 1751122995.869164, "relative_start": 0, "end": **********.276562, "relative_end": **********.276562, "duration": 0.407397985458374, "duration_str": "407ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.276571, "relative_start": 0.4074070453643799, "end": **********.326594, "relative_end": 2.1457672119140625e-06, "duration": 0.05002307891845703, "duration_str": "50.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45068640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00266, "accumulated_duration_str": "2.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3029258, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.045}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.312833, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.045, "width_percent": 12.782}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.319091, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.827, "width_percent": 19.173}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1383881559 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1383881559\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-212972295 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-212972295\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-566140717 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-566140717\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122972644%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ino0TEF1LzBmMHlnbXZiQU52eVNEMmc9PSIsInZhbHVlIjoiT2YwdElaY0VWQ1hEVTVtWWhQVWx3SVNQd0N5UnM3azFIU2ZkRUNlZ1lMNlFNY0ozczFtaFNyOWRaUGNRT09yajJESGNhNXJwajVVcWVGNkZ2Mll6Y2FiQXlDSWpEYUs3NE5SQkJqelJqWUxObEgxRFo5eTB4eDF5NkNLTmh1RzVMc3B0K0wva08rd2hnT0tyTy9ZK1dudXZYcmxRamwrRmNLdW9Tb2doY2RnU0ltRm0wR3VSYWtObzR0RmgyZmlzaVVXekMybkhpYlFRV2VXSVR1dXdvL0crWTc0U1l1dDhiY2VSUVdndkYrcXRYZzJtTDJydXR5S3oyQWVaZHV0V2lrdmxhbWJSU1JiSEpCbnJFVFFNR20yUlRHdkhkQXpZaE1xUnpBU2FzTUFFU1NUekc4UnJ4VnZ1T0NrbFQ5eGp5ZDRwbWM2TnFQY2MvSHd1bE1TVytrSjFrNE1zYUFOZ3UxRDZRVDhlQURjbHFJT3Z5d3dmT2RTb0tnR0FOc0RINytTV05Pdk40OVkxS3JtUXYvVi9RR3B3M3IycmZqT2NkYmhsL0R3MlZ4TXVydXQ5WEd6N2lwcmV5LzRRWmlDdmt2Smo0TnNOa09JY2NTcmxvVmlOM1E0TnBjbzRFQzZibFNqSEZiZ0pjcWVQWmczVStPRXh2NjRVWU1Hd0UyMVkiLCJtYWMiOiIzZWU5MmMwMjU1MWI1Zjk1NzNmZjFmNzljZDdlM2IwY2RjOGFiMjhiOTMxN2FlY2RjYWExOTI3ZmIyN2M1ZGNjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRKMWtmaXNyT1I1Z2RMYXRIRm9HM3c9PSIsInZhbHVlIjoiQ1lWRktjcnZ1OGN2RE81MG1wSmo0U0dYeWE2ZG5yQXFtcm9FZmZrTUFibVc4bks0RWVJTUlZaTVDNnpyOU1WTEcvd2pFYno4K281Wng4MkJvODVhcTIrVGZZUWlMejgreFpRYWk3TWdwaTVPU29DREVEbzBiQzN0cnp6VmJidkZtaGJObFFLQmNIQmQ5SEV5ekxSZ0hJL3VYOS9ERmdBVXV2MGZGcStJM3d1WkluNVF5NElHT05DVFMrUnRMVlBWRDBzNGdkazYrZDVDdkRkQTFNcXlMbEZxdTZqRHVmWktjekZQcisxV2pZUHArR3RSTzZRaE1FbnBnMXpBdkdYSGUvOWs1QUdnbXhHT2MwZllPYlNEY1loRDZrWTFzbi82blpyK0VMQk1OVjBUYkw1NXhvTTVyV0R3WHppM0dweWVHMXJMQTR0YVVwdWNaOEtMVmY2NC9GdU1NeE1NWjkrRGllVERJWHFjWGZiK2NPcmFsUGlmNW5OZWlTVTdDcU80alZwRjhKWkl4cTNlV1NORFFyM2VPVmoySWJXLzNmdU83bXNIWDIrdnNuNEtoKy9KMFJUYWgrdWJ6c3lEMjh3MjNYdElRMGRZUi9DU0hRam5OS3JyRkxTWFBnbGVrTnZVdWNESXJRNmE0Z2ZOaE1LMHlEY1k3TFBaNkw0YmZyakkiLCJtYWMiOiIxODQxNGVlNmI0YzcxNzBlMDg3NjQ5ZDVhNzA5MDFkOGNiOTgyNjQyMzA5YjZjMTYyOGQ2Zjc3YjY5MThjZDViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1213936061 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:03:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRyMGFpb0dlYXU1Vjl0bTRNajBhU3c9PSIsInZhbHVlIjoiM2JsM2dETk13V084MUtNVnFoUXIxcWIzdHlDY05rZUFJanZMRnQ4VnU1S1hLUzJOZzV2OUdad3pKR0l1K1hnUCtIVmt3aHFJdnBSdUFjRzg3TWVtUmM4TnlpVDhMRW1MY2k4QXUvOW51TUxsVnFJK2YrbStuSDVwc3dNSTNPWTNsZFhzcWxwWTR0RUx2dGZCU0o4Ylc0K0c5MGlpcFZ1R3dVaFphTGZIMi9RNXdIUVhuSmllck50S1FxMjY5VjAvY0dBVXkzQ1Z2NUJ4R0xXZEJUR2NvMXhwTndIMWV1NnpBSis0bjZyc3h1UUFaZUlTaXhQZFh6RENNUUxweU1uSm5oOHgvb3RhSFAycWwrN3RvR3FVb3RZTTY0ZUQwSzM1d3BSNE1QdHNWbFRkdThobUZmUGFETU1PenVlRVZxUDk2WUQxSWxzWFJleDYzcFJxVmhESGdDVzdnMjk4bnVFL2JrdVppTEtJNnNPRDBEUlpEeEdEclZOUnIxaVRteUVwRTJMTnZEcUZ4RUZveGMwL0lnelZvb0RNTmV1OXc3SWUranFxYzFaejJzaFdsMmJQUjZGTXQwWkpHZTZxODBGZWYxY3VjOEVMN094VFlWbW14c0s3V1hZZVhnclJFM0ZjQ0E2MWNtd0dMUnV0NWpnc1pCNlBjZEY1SXU1ZlExNTQiLCJtYWMiOiJhZWIxNzIxMDhlNjhmNzA5MDQ1YWZkMGExZDlkNzkwNzY3OTM4YjEwZjc5MmQ0M2Y1N2FiZjA3ZjBmZWIwYTM2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IndobFBUMjN1dk40S2RJYytTMFJlZGc9PSIsInZhbHVlIjoiTWkrVjZqbXVZN1pUQUtjcUtXd2ZDbmxtTzFFQzNqWjJDRVVJNXFQK2FLVjF1K21vYmU5bitxa3hZU2tOcmFVNEoxN2xUcGZ6ZFBJbUNWMlRCSG5sc2dVWHM2Sy9WV3ZZODNSNnRxR1ZqYmQwUW51Zm1tM0NCS2Z6MVRCZW40S1lqaitKQ3FpT3UvUFlQTENVOUJkOXNTT0lrbXQ2UzhqVlBQUkFIcXpkVmIxWjl1QXFKVWlXbm1vemZYckh0OXlOV2FkdEpoOE1OY0ZCU3dFRUFGeW9peVZoVzBmY2U5ZGpyY3NERUJNSXZUcE1LMExubGdrV0xlSk1VcVR4V1YrUVJOZ0I4dTVsUWE3VEwxU0pJZkpYNzlHVE9FWE1PK09mN3lyZnZCSVdsRmZZc3VEa1EvQTBpYXlQWkdvaklsdyt2cVo2LzE3WXI2MUJJdFJ0MmRtblZ3Y09TQ0FadU1Na3dkMjR0MlY3NENQeTNwdWpBdXFRa1hmKyszSjlBMHhhVTlQZ0Vubkt4T3QxTHZycFNIWGs2ZnBIbjQvMEh4ckVzc254ZEdRWlFhMkh4WWVwQzBySlRYMzhaaWNkMGZqdVFuVjRONlRBTXZTUFVzelpHLzFVRFFLV25HSDU5RWJmbTE2VSsrVmxNNGdoSG1Ja2RoVC9sdmNZK1ovWmNnUlgiLCJtYWMiOiIxNGIxZmM2ZjAzZWM5NGVhODM0MGY1Yzg0MDkxZmJmNzlmYTEwYjAxZTc4ZjIzOTYzYmI4MDYxOWQwZGEwYzcxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRyMGFpb0dlYXU1Vjl0bTRNajBhU3c9PSIsInZhbHVlIjoiM2JsM2dETk13V084MUtNVnFoUXIxcWIzdHlDY05rZUFJanZMRnQ4VnU1S1hLUzJOZzV2OUdad3pKR0l1K1hnUCtIVmt3aHFJdnBSdUFjRzg3TWVtUmM4TnlpVDhMRW1MY2k4QXUvOW51TUxsVnFJK2YrbStuSDVwc3dNSTNPWTNsZFhzcWxwWTR0RUx2dGZCU0o4Ylc0K0c5MGlpcFZ1R3dVaFphTGZIMi9RNXdIUVhuSmllck50S1FxMjY5VjAvY0dBVXkzQ1Z2NUJ4R0xXZEJUR2NvMXhwTndIMWV1NnpBSis0bjZyc3h1UUFaZUlTaXhQZFh6RENNUUxweU1uSm5oOHgvb3RhSFAycWwrN3RvR3FVb3RZTTY0ZUQwSzM1d3BSNE1QdHNWbFRkdThobUZmUGFETU1PenVlRVZxUDk2WUQxSWxzWFJleDYzcFJxVmhESGdDVzdnMjk4bnVFL2JrdVppTEtJNnNPRDBEUlpEeEdEclZOUnIxaVRteUVwRTJMTnZEcUZ4RUZveGMwL0lnelZvb0RNTmV1OXc3SWUranFxYzFaejJzaFdsMmJQUjZGTXQwWkpHZTZxODBGZWYxY3VjOEVMN094VFlWbW14c0s3V1hZZVhnclJFM0ZjQ0E2MWNtd0dMUnV0NWpnc1pCNlBjZEY1SXU1ZlExNTQiLCJtYWMiOiJhZWIxNzIxMDhlNjhmNzA5MDQ1YWZkMGExZDlkNzkwNzY3OTM4YjEwZjc5MmQ0M2Y1N2FiZjA3ZjBmZWIwYTM2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IndobFBUMjN1dk40S2RJYytTMFJlZGc9PSIsInZhbHVlIjoiTWkrVjZqbXVZN1pUQUtjcUtXd2ZDbmxtTzFFQzNqWjJDRVVJNXFQK2FLVjF1K21vYmU5bitxa3hZU2tOcmFVNEoxN2xUcGZ6ZFBJbUNWMlRCSG5sc2dVWHM2Sy9WV3ZZODNSNnRxR1ZqYmQwUW51Zm1tM0NCS2Z6MVRCZW40S1lqaitKQ3FpT3UvUFlQTENVOUJkOXNTT0lrbXQ2UzhqVlBQUkFIcXpkVmIxWjl1QXFKVWlXbm1vemZYckh0OXlOV2FkdEpoOE1OY0ZCU3dFRUFGeW9peVZoVzBmY2U5ZGpyY3NERUJNSXZUcE1LMExubGdrV0xlSk1VcVR4V1YrUVJOZ0I4dTVsUWE3VEwxU0pJZkpYNzlHVE9FWE1PK09mN3lyZnZCSVdsRmZZc3VEa1EvQTBpYXlQWkdvaklsdyt2cVo2LzE3WXI2MUJJdFJ0MmRtblZ3Y09TQ0FadU1Na3dkMjR0MlY3NENQeTNwdWpBdXFRa1hmKyszSjlBMHhhVTlQZ0Vubkt4T3QxTHZycFNIWGs2ZnBIbjQvMEh4ckVzc254ZEdRWlFhMkh4WWVwQzBySlRYMzhaaWNkMGZqdVFuVjRONlRBTXZTUFVzelpHLzFVRFFLV25HSDU5RWJmbTE2VSsrVmxNNGdoSG1Ja2RoVC9sdmNZK1ovWmNnUlgiLCJtYWMiOiIxNGIxZmM2ZjAzZWM5NGVhODM0MGY1Yzg0MDkxZmJmNzlmYTEwYjAxZTc4ZjIzOTYzYmI4MDYxOWQwZGEwYzcxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213936061\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1953480978 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953480978\", {\"maxDepth\":0})</script>\n"}}