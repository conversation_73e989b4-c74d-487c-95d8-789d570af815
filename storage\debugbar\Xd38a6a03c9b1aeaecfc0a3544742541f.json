{"__meta": {"id": "Xd38a6a03c9b1aeaecfc0a3544742541f", "datetime": "2025-06-28 15:08:06", "utime": **********.577601, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.090956, "end": **********.577619, "duration": 0.4866631031036377, "duration_str": "487ms", "measures": [{"label": "Booting", "start": **********.090956, "relative_start": 0, "end": **********.51131, "relative_end": **********.51131, "duration": 0.42035412788391113, "duration_str": "420ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.511319, "relative_start": 0.4203629493713379, "end": **********.577621, "relative_end": 1.9073486328125e-06, "duration": 0.06630206108093262, "duration_str": "66.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45596880, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00358, "accumulated_duration_str": "3.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.544398, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.408}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.558458, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.408, "width_percent": 18.994}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5652401, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.402, "width_percent": 17.598}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-661298605 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-661298605\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1317024608 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1317024608\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-852432093 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852432093\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1786052906 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123245178%7C6%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlYVGhJTlFMRG92bVF5dzRvdGlHQmc9PSIsInZhbHVlIjoiQUo4cmRxOHZlanhzemhTOU9USmhCVEJnbTQrYjBhVVJYSm1rbkx5ZXA2b0lkeGdiNzFpdGE4R3NmMUVFVXJEQVE1aVlzcnpQUTZmOWlWQ1pIaXh1RXI4UFNFc09ISCs4b2JCcitrSGRPRWt6alptVjJtZUVPUllSZHFBM1dCR3BQVDZhd2RSaHMxSG82T0RxaWs2cTFYTUhobzR5SzNPRHZEQk8wNFQ5NWQyRTRnYnZ4ZDZVMm5YUWIvQjFzMk5SM1hRYTZ0cFJjdTBiTnE4VjYyQ2NNaUtHeGFjYTdrcDROdFlFclhHRkxrS3o4Qk5Ed1hQVVJ6RXdlZlJLQUN2c2RqRGJrZ05XSTJhcEVBdGd5YXN1NDFBTDRlQ0Z2MGEzcGxWbFpZeldjSitmczZHTFRPL29sd3A2a00yOTdZTmM4WjMrTEozOVZSSVZSS1pHSjR0WUFvVk04R1lIWTRZYUI0aXhVNURVZ0lxM3I5Z3NtaGxQbWdHNFRyOVN0WE90TVBOMXlwYTdhQW1NRzRuUlpkRTFzanVicldTTkdRM3ZiZTh4Z1c4eFpKK3IrTXp0eEZ3Q0srTGNmWUtWa0h0elZyeTBhREFDaE9vZ1NkZFpwZ1Rrc0F6eUUwM0VnQkVZS25jdW5GbmZhVWpTSUdTejFjQ1J1NUVFTFpSZ3dqODEiLCJtYWMiOiI3ODczNmI0NWQ5ZGI2ZDJkMjNhMWI1YWE0NDJkMmU4OGE5YzcxYmM1YjMwZjgxMmEzNWU3NjNkZmQ5NWVjODRhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVReW1sRXZGM3BYckxBTTFHWUZaaVE9PSIsInZhbHVlIjoiUlpQU1BiMmxSNlZ6aG4zZ01rd1c1b0YrQnBOTy9pem1XYWNDWjFTc0lLeDU2ZnZ6Zm40dGFQSE00VStFTU9CR3V0Z3FLWVAyaTNnV3YrZFBJZVpQd05EeGtoNno3ZDFZK0t3T09oeFk1Qk1RUmU4TEpTWDFINll1MkF3SVVYdXJNdDVsUGg5RldCT3psanR2eUpQNDdHRmw1eHN0R043QkpwT3ZjUXN2czNOS2t1U01UWTNpUjVDL1JvN1BhVVJQS0tYQUpRRWk0bjk4b3ZCRE5sRGpJRHF5Q0t0WUJ2aDZLUHdYNVIveERIV3dFQkpReFA4cWZOVDlkOEJnTnV6QnY0UVlSaHFGRm4yUFVTZE1Ia3Q0MW9Oem83U3J4dnFoR2N6Wkkrc1gxYzZTWXk2V3BTcHY5MUt6WWprZCs3eWtSS2lTS25xOVdneWhHbzRWVWxoK1ZNeS9qdjZiRERsa2ZyNkdCdzlFYlNFYmxRcXZMN3NsUGk4U1pKY3ZnRUJ6ZzBhd3Q4Q1czVmliSm4rQURDdyt2azVvUno5VWJSS2xOeEhKNVNHMWFVbldKQnpHTnlWckExcGtiTlRvbEMvVGxMakpBWGh6MXliSzZDSXUwdzdsUEVhNnpOTXlONTR1dVk1Sy9XT09UTTBhSE9TNjBGTXkxblBoMmhFUXVDMzIiLCJtYWMiOiI2MGNjOTBkZThkYjI2NTg0NDEzMzM1OTc4OWViZTk3ZjFiMWVjZjhmYmNlNDQ2NjMzMmJkYTMxNzQ2MDc3ZWJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1786052906\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1340189290 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340189290\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-51117877 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNnTGVzVzVHNnRUNGpQQzQ1TFpEcEE9PSIsInZhbHVlIjoiMEhzZE0wUDk1Z3k4NHcvYkVDSG80eUVGeURlWW5uR1lpdWNRNFQrYkEzRGdSY2RTWFhNdlcySThuRWZkaGhlazRlR0RGOVFsNmgrRnpCYkVnYnN1ZC9vbEdJQkc1WFFrNkpmN01qWGxIMmlGVGN5M08wa3N2VlJiTktjZFFWM3pQc2ZXMkk0LzRTaUVCM0c2RVEzVGswTGIvUjBCeWZTdmlIZUpkNm9sL016RFdRZDZwS2lhQk51OURVRzJ5dVdBK2xDRzRwcEREa3FHaU5MZWZkMVcwdEd1aCtsN3Z3aGVtQ0Fsd1Nya01leG9IcW90MWN1NUIvVFNJZk90TUh3dThwRHZVMTNSYmVzVFU0dW8yODNOd2ZlYTdLTERZSGhqQ3RSYmJHQk9uWi9BNTFyUEgwOXlMZzNScWRmb2FmWW9FOTUxV2NYSFlTczAwY1k4MW9vYnlPdXBBSXk4T2RJT20yZnRTN1pna21iNlBxdm02OXVKejYvaGJhNWgvVitweWpjZ3pNVFVFR2NidGFHY3REaU9UeEFRLzZhMmtPaEVPbkxGSmtwd1VpamlLaElGay9FT2tmOVpkOG01VEVUVnduV2Y1ZWtzbG0yUE9SeVFsZk8vRWZvM2VyeXZDWkN2WDg1SWkrbDZ3SVFyRmRsdWdhaDVyaDNLQnRCTG1DRW8iLCJtYWMiOiJiYjllZWIzYjU4MTQyNTE5YzUwZGM4OWM3NTkyODAwYWRmMzE1YTYxOWMxNjZlMjZkZTVjNTViYmMyZTMyMTU0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlNqbjlBU3VpQkVodlJ5NEJFdElQaHc9PSIsInZhbHVlIjoiZVZ1dSt2bU55eGNCTUI3QXBSc3dpTG95SXFrRzU1SDJ6cnV0VXVDcVNsUnJUcVBpR1RRUm9JbWR1Sm5sSUlJSjByOWZIVktUWFBuR0x2N3hpbHcvT25kaWdJK0F3bjZMSlFzUzE5ZWI0cFRTUG43T0l6WklQeFUrYXpwdE96VFBLRUNPa2c3bWE0YkwvQW9nbEZWWER2Q0JOYVFNU3V2SktUNTQ0K3dmU0ZUck1sWnM5aVA5Z00yVlJoQlNUU1ZWMHVqNTIrUFB5T0FTSkFITFQ2NytvMytacUFvUXQwWnNGa2lyNkhySDdpQk43aDdBWkpvTXRGaDVFOUVmUm11Ny9hU1c1dlNweXlwd25vOFNINUk0UzNZYXVtNGVlVStWa2ZYbVdqa0NxVGRhWElFWnRsN2FrVUtQVkZudk02R1J0cFZ3N2xXMXZzTG9jb082YXJkK1RNdzlBN1dSY3VmR2RaRnl3Y1R2UjlIck0rVVZOQmlCcnlnaGRmY1JDa3ZQMXIwOUVERjBRTmx4a21jUkVsWlZ4WkNiRUFrV0VJeXk1dFdYRUEyRjVnN284anFPN1FyVGgva0JiSmpZWG1Ed2FzUHNsS283SDRjZHFuVTE4eWJUeDNvQ0t5U0NvMXlBQTl4S2psV3I1TkRQcy9yZXdmclJkZkJpcldXOGJQWGwiLCJtYWMiOiI0MjJjMGVmYmQ4ODU0MjYxMDdlMDY4YzQyZDc5MDQ0NzM3YTkxYTIxM2Q0MGI1ZTEwZWI2Y2E1MjM0YmMwN2RhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNnTGVzVzVHNnRUNGpQQzQ1TFpEcEE9PSIsInZhbHVlIjoiMEhzZE0wUDk1Z3k4NHcvYkVDSG80eUVGeURlWW5uR1lpdWNRNFQrYkEzRGdSY2RTWFhNdlcySThuRWZkaGhlazRlR0RGOVFsNmgrRnpCYkVnYnN1ZC9vbEdJQkc1WFFrNkpmN01qWGxIMmlGVGN5M08wa3N2VlJiTktjZFFWM3pQc2ZXMkk0LzRTaUVCM0c2RVEzVGswTGIvUjBCeWZTdmlIZUpkNm9sL016RFdRZDZwS2lhQk51OURVRzJ5dVdBK2xDRzRwcEREa3FHaU5MZWZkMVcwdEd1aCtsN3Z3aGVtQ0Fsd1Nya01leG9IcW90MWN1NUIvVFNJZk90TUh3dThwRHZVMTNSYmVzVFU0dW8yODNOd2ZlYTdLTERZSGhqQ3RSYmJHQk9uWi9BNTFyUEgwOXlMZzNScWRmb2FmWW9FOTUxV2NYSFlTczAwY1k4MW9vYnlPdXBBSXk4T2RJT20yZnRTN1pna21iNlBxdm02OXVKejYvaGJhNWgvVitweWpjZ3pNVFVFR2NidGFHY3REaU9UeEFRLzZhMmtPaEVPbkxGSmtwd1VpamlLaElGay9FT2tmOVpkOG01VEVUVnduV2Y1ZWtzbG0yUE9SeVFsZk8vRWZvM2VyeXZDWkN2WDg1SWkrbDZ3SVFyRmRsdWdhaDVyaDNLQnRCTG1DRW8iLCJtYWMiOiJiYjllZWIzYjU4MTQyNTE5YzUwZGM4OWM3NTkyODAwYWRmMzE1YTYxOWMxNjZlMjZkZTVjNTViYmMyZTMyMTU0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlNqbjlBU3VpQkVodlJ5NEJFdElQaHc9PSIsInZhbHVlIjoiZVZ1dSt2bU55eGNCTUI3QXBSc3dpTG95SXFrRzU1SDJ6cnV0VXVDcVNsUnJUcVBpR1RRUm9JbWR1Sm5sSUlJSjByOWZIVktUWFBuR0x2N3hpbHcvT25kaWdJK0F3bjZMSlFzUzE5ZWI0cFRTUG43T0l6WklQeFUrYXpwdE96VFBLRUNPa2c3bWE0YkwvQW9nbEZWWER2Q0JOYVFNU3V2SktUNTQ0K3dmU0ZUck1sWnM5aVA5Z00yVlJoQlNUU1ZWMHVqNTIrUFB5T0FTSkFITFQ2NytvMytacUFvUXQwWnNGa2lyNkhySDdpQk43aDdBWkpvTXRGaDVFOUVmUm11Ny9hU1c1dlNweXlwd25vOFNINUk0UzNZYXVtNGVlVStWa2ZYbVdqa0NxVGRhWElFWnRsN2FrVUtQVkZudk02R1J0cFZ3N2xXMXZzTG9jb082YXJkK1RNdzlBN1dSY3VmR2RaRnl3Y1R2UjlIck0rVVZOQmlCcnlnaGRmY1JDa3ZQMXIwOUVERjBRTmx4a21jUkVsWlZ4WkNiRUFrV0VJeXk1dFdYRUEyRjVnN284anFPN1FyVGgva0JiSmpZWG1Ed2FzUHNsS283SDRjZHFuVTE4eWJUeDNvQ0t5U0NvMXlBQTl4S2psV3I1TkRQcy9yZXdmclJkZkJpcldXOGJQWGwiLCJtYWMiOiI0MjJjMGVmYmQ4ODU0MjYxMDdlMDY4YzQyZDc5MDQ0NzM3YTkxYTIxM2Q0MGI1ZTEwZWI2Y2E1MjM0YmMwN2RhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-51117877\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-620082915 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-620082915\", {\"maxDepth\":0})</script>\n"}}