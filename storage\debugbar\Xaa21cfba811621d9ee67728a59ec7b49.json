{"__meta": {"id": "Xaa21cfba811621d9ee67728a59ec7b49", "datetime": "2025-06-28 15:26:20", "utime": **********.449581, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751124379.962412, "end": **********.449597, "duration": 0.4871847629547119, "duration_str": "487ms", "measures": [{"label": "Booting", "start": 1751124379.962412, "relative_start": 0, "end": **********.375844, "relative_end": **********.375844, "duration": 0.41343188285827637, "duration_str": "413ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.375855, "relative_start": 0.41344285011291504, "end": **********.449598, "relative_end": 1.1920928955078125e-06, "duration": 0.07374310493469238, "duration_str": "73.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45819312, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2015\" onclick=\"\">app/Http/Controllers/PosController.php:2015-2068</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01514, "accumulated_duration_str": "15.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.415987, "duration": 0.01464, "duration_str": "14.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.697}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4392781, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.697, "width_percent": 3.303}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2138827796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2138827796\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1748158001 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124349103%7C15%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inc3VGhmQW01YWxTVlFaSUNvS1ZBMVE9PSIsInZhbHVlIjoiMmswVkRPMFRvaWpsand2VEU4SWozbncyU2p0TEgxbzd6a05UOUhJZzU3ZzMwWWthaEhRbGZ4Mm51eEtETzVoQzBmOFh6OWZsUXN6ZGREZWRyclhFUnUzVWh5WlBSSWZ6UElBLzRoSm95OTBoQjFtRGpqWC9GNnVIT1pYMEhnTWFlT3VaT1VwVDQ0M3ZlMzcwS0gyWkorSEo0VWJpTUliSlhqOGNlUmNmbGlxYTVMckZaRDM1Z20vUzJ0eXUxOUNjTHFPaG03Q2ZqSkpSOGpTUUR3cjN2ZkVhNjNDdno2cHNQcTdOMG92TmxmUkdmT3ZabzlwUHJJMnJqWUNPd1VaKzhhVnlSaDlQcU5hOVM2TjhQSTl5MjZkYkhzVzYxZ2RJekRLRkltVXhGZHlqRWhiZHBFc2dhQ0dqdXlJWlNTc3dNVFVMVmhvbjNzdmw3TEZVNXBydFJPb3RGRi9nQUFZdjlDU29CbVhucytRa2Y0WkY2RysxNmFLc01qT3pOTVIyb0ZQRUMwamp6MWd4TGY3QXpaeFJBK25LSGtGbWRuRHBOWmZNUjR2Qmw4OHZpQzFFdDJVK2hSeEdDUUd0TDdqTE1IN1NxTnprbm9iM2FMMEpXOE9vZHdEeUFxNkVmZk5UTHJSS0d5RU96WHVieE9BRHRlaUg5Vm9NNnBhSjdsbkwiLCJtYWMiOiJmN2U3MzI4YjIxZmI0ODM1MDEyMTJkMTVhYmNiMTU0YmY2OGE2NWIyYzA3Y2M5NTRkNTc1ODM1NjRiYWU5YTBmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZ0M0Vid2FLRTlNdTV0Qlo0VXNTeGc9PSIsInZhbHVlIjoiOTcrWHlvNmg4ZVFvb0RGbTM5bDBYTGU1ZndTQ04rNy9ESjY0V3pHbFJNZzRLLzNtSVJEdWI3VGhmNG5XdTA3Ym15bkV6TlllUDl6NUNVdUVpN05uOTlGbGlRUjk1aSs1RTE4MUtHTXVGSkZhTmZDd3l5c0R0WTA0a0lEV09kaWZxaVZwd1FFK0xuRk1CYy9yaW5SM1A0NkFEblB0ODNpaVdRWWR6RTNuM3p0MlZTZjhrVXpjUlEvRi9jMzZpT1EvWFVJMGlERml1T0xSZUh4K2xNbnkwdTVoQ04yTVRmQ1AwL09xU1pScXdBYkhUcDBTSG1HYkY5RmlwMkFoU2ZMc1h2NWUrU1VzOXEzRVpaa3F6VW8yKzNSamNWbDlOdnYxSzhrS2lTenZ6L2lHS2R2am1vM091U20xQlRLR3JOUHdkWGUxRU5HR2d2WGtrMmtVdElYc3N0Y1I3QTBrRW5CdWtES1B5RmIvYy94Z0p1OHF0a05waFV6Y3U3ZENNWktzNE96ekdaL1ltRllQUm5iNHRjY3JxVWNRUGRaejJGcFdRTDcyTWd1MitBUWNQcDJNaS9DUjd4TS9WSlBVR0VFNEkrUEtSbGlaYjZhSkN1V3N2eFIwazMyVDNDYkZrdXZpTnNzZVo4ODRkOXRkbGovQmNaQXBuMEQrcnBwTlY0SEMiLCJtYWMiOiI0NDVlNjIwMDU2ZjQxMzM2MjVhMGZjNGQ5ZDAzYWMwM2M5ZTc1ZTRjZjhkNDdkYzQyYzg1MzliN2E2MTNjZWI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748158001\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1953924675 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953924675\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-894885173 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:26:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InUwRHhKb1ZlUEgyd1MxaXVHZzJ2RXc9PSIsInZhbHVlIjoiS0pEcHpCZUl3Q3pZcXc3dnloOUFmYVVKcTlXa3BmRDBSeDZndnBwUm82cmdLS2RuOFdBQ2JDNUgyYzIzeGE3YzJSU1M3Y2xESnZnQmwyQWlzR3hjeXdNWVpPNmRUYzBBQjlGK1N3TE9oclM5RWU1eC8yYTdBS0JSVWhOL1lRWSs4SnBkYkw5b3BTSmtSYXhET1ZoM0N2QXB6M0pxdk5IUFlNYnJxUWxqUnZkQVUyQzdmcE1jQWVML1pyTjJreitzZzEza01uRXowVUxKdS92QStjRHJ5eTM0VVdqTHE3VmVMYTcyOU1ULzgyOE83eld0N1psUDc2Skl2cU1NUXhSOWtDUEJlTitJUzFpWFJ0UVNzQkNOSlVBUVlHblcwUjBCZXlCbVdxdEtVbERhQ3RNRTJ6NmNxeVMzZEVGbXFWbHhNZlI1VC9PaW9uYVZibVNxYi85ZHAwWU9Jdis4c2hjNHo1RnZ4OFhGUFZaNWxIRnN5TDM4WVBXVXI5WU5nQ2JZM3RSSjF6c1BTOFpwN0w4bTZ3d2FCLzRPVGJDWXFObWJIWlBVY2FZTkcvWEZjMkVoL0s1MklCVlVtVHFFa3ZjTXFsRnhzME5ZQ0NFTHBZb1dNQlZBTTc0NllNZWlFTlBHRzcxeTF4TW04ZHRCT1pnU3BjRHNKUm9VbUxFM2F3alYiLCJtYWMiOiI1YzAwMzQ4ZDM0YTNiYzc1NmJiMzU3NDQwMzk3NTMzOGYzNmQ0YzBkNGUwNDY5MmUwNzc2Yzg2MzA5OWM5ZjdjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImpOWVpmUXRMYVBXVmFyMHJoY3dPYWc9PSIsInZhbHVlIjoiS3Axc3JTK0tCeFZrVTdqV1FhbnVYak5KNnVvUWRZRmlIWXdnR05lOElYRVh4TWRPY0RNZ0RpWGd3T3FXdU1FUVpqVmVjSVJWR1puVm1jTzJhbldGVURVbVBOOE5HMGlwOFNuZmdMTGpqUDhYTVpMRW1UYithUDNmU2tRS251YWVtQXY2QkNBMlFpTXlBRDE2RFd0clN3VCtlNDI1SzhGRzlkb0dlS3o3MkRaem00MEVBekk5OTF5bGJLL1pOenhJZmU2bW1sQ1FRNXFoZ3hGQzJZUTJvUTB2QzlJSk92RzNOdmVLSUE1UkNDaXBWKzY3MXVPQXlZVGFqQ2V5MUVvQ0JpZFBTMTNrMktCMy9KbElBZXRweVNaQ3cxUVdnT1lYNjVaUjcyd0p2T3FRVTNwRGh2cGxRYmZPSXhwYmZWSVJQV0hyZ3dmREtQMWtYSlIra1hBY1FUTWoxdjZLNTJlTXA2UTI1R3dKV2p3UHJjM2QvQU9rNHl3ZTR6cmxqL29BVzdxU0l5L1dZbGszZDluN3NWRDM5WkVTR21DVjV0SWY2bEU4UHc0NndXclNvWEFZbzFDV2RzREdNWnkvNTJ0Z3Fzb3RZMlFObmMrOTBZUWVUMTl6Ym83aDV0RW1uQ2tTY2xpNjFqdFg3K1ZFVVdZNkJ4YVowdXEzNU1BSGZUeloiLCJtYWMiOiI4ODYwNTEwODI4YmQwMDcyOGQyMGYzZDFkN2Q3MmQ1MjU5Zjc2MDVkYjNkMGY3MGM0ZmRiZTliYTE3YTMyYzU3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InUwRHhKb1ZlUEgyd1MxaXVHZzJ2RXc9PSIsInZhbHVlIjoiS0pEcHpCZUl3Q3pZcXc3dnloOUFmYVVKcTlXa3BmRDBSeDZndnBwUm82cmdLS2RuOFdBQ2JDNUgyYzIzeGE3YzJSU1M3Y2xESnZnQmwyQWlzR3hjeXdNWVpPNmRUYzBBQjlGK1N3TE9oclM5RWU1eC8yYTdBS0JSVWhOL1lRWSs4SnBkYkw5b3BTSmtSYXhET1ZoM0N2QXB6M0pxdk5IUFlNYnJxUWxqUnZkQVUyQzdmcE1jQWVML1pyTjJreitzZzEza01uRXowVUxKdS92QStjRHJ5eTM0VVdqTHE3VmVMYTcyOU1ULzgyOE83eld0N1psUDc2Skl2cU1NUXhSOWtDUEJlTitJUzFpWFJ0UVNzQkNOSlVBUVlHblcwUjBCZXlCbVdxdEtVbERhQ3RNRTJ6NmNxeVMzZEVGbXFWbHhNZlI1VC9PaW9uYVZibVNxYi85ZHAwWU9Jdis4c2hjNHo1RnZ4OFhGUFZaNWxIRnN5TDM4WVBXVXI5WU5nQ2JZM3RSSjF6c1BTOFpwN0w4bTZ3d2FCLzRPVGJDWXFObWJIWlBVY2FZTkcvWEZjMkVoL0s1MklCVlVtVHFFa3ZjTXFsRnhzME5ZQ0NFTHBZb1dNQlZBTTc0NllNZWlFTlBHRzcxeTF4TW04ZHRCT1pnU3BjRHNKUm9VbUxFM2F3alYiLCJtYWMiOiI1YzAwMzQ4ZDM0YTNiYzc1NmJiMzU3NDQwMzk3NTMzOGYzNmQ0YzBkNGUwNDY5MmUwNzc2Yzg2MzA5OWM5ZjdjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImpOWVpmUXRMYVBXVmFyMHJoY3dPYWc9PSIsInZhbHVlIjoiS3Axc3JTK0tCeFZrVTdqV1FhbnVYak5KNnVvUWRZRmlIWXdnR05lOElYRVh4TWRPY0RNZ0RpWGd3T3FXdU1FUVpqVmVjSVJWR1puVm1jTzJhbldGVURVbVBOOE5HMGlwOFNuZmdMTGpqUDhYTVpMRW1UYithUDNmU2tRS251YWVtQXY2QkNBMlFpTXlBRDE2RFd0clN3VCtlNDI1SzhGRzlkb0dlS3o3MkRaem00MEVBekk5OTF5bGJLL1pOenhJZmU2bW1sQ1FRNXFoZ3hGQzJZUTJvUTB2QzlJSk92RzNOdmVLSUE1UkNDaXBWKzY3MXVPQXlZVGFqQ2V5MUVvQ0JpZFBTMTNrMktCMy9KbElBZXRweVNaQ3cxUVdnT1lYNjVaUjcyd0p2T3FRVTNwRGh2cGxRYmZPSXhwYmZWSVJQV0hyZ3dmREtQMWtYSlIra1hBY1FUTWoxdjZLNTJlTXA2UTI1R3dKV2p3UHJjM2QvQU9rNHl3ZTR6cmxqL29BVzdxU0l5L1dZbGszZDluN3NWRDM5WkVTR21DVjV0SWY2bEU4UHc0NndXclNvWEFZbzFDV2RzREdNWnkvNTJ0Z3Fzb3RZMlFObmMrOTBZUWVUMTl6Ym83aDV0RW1uQ2tTY2xpNjFqdFg3K1ZFVVdZNkJ4YVowdXEzNU1BSGZUeloiLCJtYWMiOiI4ODYwNTEwODI4YmQwMDcyOGQyMGYzZDFkN2Q3MmQ1MjU5Zjc2MDVkYjNkMGY3MGM0ZmRiZTliYTE3YTMyYzU3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-894885173\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}