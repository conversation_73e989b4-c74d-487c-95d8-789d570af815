{"__meta": {"id": "Xbaeafcaac74ed078451f3d7a01c487c1", "datetime": "2025-06-28 16:03:49", "utime": **********.903609, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.451154, "end": **********.903623, "duration": 0.4524691104888916, "duration_str": "452ms", "measures": [{"label": "Booting", "start": **********.451154, "relative_start": 0, "end": **********.831906, "relative_end": **********.831906, "duration": 0.3807520866394043, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.831915, "relative_start": 0.38076090812683105, "end": **********.903624, "relative_end": 9.5367431640625e-07, "duration": 0.07170915603637695, "duration_str": "71.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45698056, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02259, "accumulated_duration_str": "22.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.857539, "duration": 0.0213, "duration_str": "21.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.29}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.887595, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.29, "width_percent": 2.435}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8937619, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.724, "width_percent": 3.276}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1024707937 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1024707937\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1880441242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1880441242\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-168984691 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-168984691\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1818421739 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126627120%7C22%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImI1ejgzdmEvSHJOa2R6TkF3M0JnOGc9PSIsInZhbHVlIjoiWVpPMy9qSXU5dkt1VHBRK01paWVVYkFSUzJBdFlIVmI2RGp3Y0RMajErOVk0N0EybUFlVm5KQnhyejd4bUJ4SzJSeTNyYUxEMHZ5TkR5em1Xdm0xTFRobEJWbWsyQjZEU3ZMU211RzFCakNoZitlN0EvV1JITEprazJZRmxYZkwzNTRpeG44cUV2Q0txd1g5alNPdkg0OUZTN1dvWFk3NW1zeGZoZTNCTHh2UkdMWFpjMDNaQ2VrWW83bDRSQ0d5M082SXdKZVV0bEZhOE50R3IyVWVHaVgxV09nNURtczNrc1B1TVhodjdKTlJaQjVwTThuZHpUbjNLS2dGcW9XZWF4bHpkbWFKeDRrbC9RdjZGNkRZU3NVQWlTbEVJRzRxQlFrTXQvd2Y5aC9VR0ZHdWhTYURXbk9ocG5DWmVoazNzME5iSnZSYllsR2trM2czN2tPdjJKUzF4VlFsM08zNTgycmxUSEl1M1phZnRmYUF5Z0ZIb0w1Y2N5MERuQTdoYUh5bklDbnp4SWxFNDhyUXV2Wnd5YlNjd0FKNVJQd1pwRWJrQ09NUVZOYWZaMmFWMmJ1ME80ZG51Mm5YRTVwaW1jZjh4Ym0zRE9hdXdpQlRqb3BkaXp3dlVadThJUzdFRnhveFBuQ3hKc1l4MUZhUHBKUGczSXNHVjNkQk1WUFgiLCJtYWMiOiI2Njk4OWZiMTM1NzQ4YWEyZGY2YWRkZmQyODRiMjkxMGYxZTRlN2YzY2IxNzk1NTM2NTliMjdmNmQ0NTBiYzIzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImY5WjVFcmNNVWNOclNaQ1VMQ0ExV2c9PSIsInZhbHVlIjoieFFCVXlybXNmR0NpVXJ6UmhpditKQi9UcGs2dlhzUmFQMkNKTGdiTytQc1FMcDJNT1ZXenplMjhUUnZ5WTB5K1Uyd0JDN0lSQlQ1M0wxSkdLa3pWa0ZQcTdNLzNzeDZvblpjNGs0YWlDZ2VWR0dvQmNNUlhoUzdXK0htVVB6clBEc25ZdU9HeUtVcEE0YjlITm5MTjFTUEMvK3RBbFpMV2hHVzVVL1ZwN2RqU3F6V3RHVVlTcGpGWGp3NU9Xb2RpcHpER0NBWkFranNzUFlzUzhheTBENHNGeDJNc1VzTG4vb2MvUlRmSm8ydi9CalgxNUFHd3dsYWZBR1Rwangwc3gzQjYvTGpwdmowNlFMMnp6VzFFVXRla1JoWTBDL0hTcDViQjZKZ1RjM2w1WExOMWhBNnJLM3BXSjJ4SGErQ2RvcTdqNzhxdjVYUW0xSGxvOUtlL1FwWXJWOTU1a2pKTFJCN0FpK0NRdVdRaVlqVzltamVYMXdtR09HTmJiSnRiSzZXbEJtQ245VDU3Vi9ES2hWRndYUE13c0tlQlNVNnVtL0tudFVvcjZCRFEwM3I1TCs2aUxoWU9nUGtjaVk4OXFDcTZTUkR4TXR4c2VmcWU2WUhxQVpreWNTdXlEQXdVUlpxWWs1SExXMDZJWmtvNWNTUUVBR3hYVWR0WUVwUVciLCJtYWMiOiIwNWMyODYzYjYxMzA2MGFjOWQyNWExMjdmYTQ1MzE1ZTI4MDQ0M2NjMTU0MDc2ZjA5MDhmY2JkOWZhMWFjODEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818421739\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-767940504 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767940504\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1253108656 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJTcWVWYWpIV1UvUUhPVC9uSEV4Wnc9PSIsInZhbHVlIjoiTkNTQ0libzV2elppNFk1MnV1bEtzOGh5bzRHYVQrbXV5Zy92dzlhWDROc2RZSlAyVnhtOG9ldG1ZdkZOSlQ2K0JiZEI1ZlRXL0NjWnNtMm9WQ0RYQTg2Y05XajVnMU5rNlJOUXUxbmRzS29MQnJadmNRcHRQOWFSd21oN2hWZGhBY2Q0blpScVZPRmxLdFFDbUZ0djNoSkI0RkR1OEh5VUlCVEhaOHFvTFdxK3NTMWpkcXp1a3RueU9lZ0Z2aGR1WE95M0JOdWIyenhPTUZ5bU5xMnRBSzdjWDNzb095Mlh1U2plZ0VFUEpUSmdSbmhnOGVpV0VJbFA5dm9rWkQ2Nnl3dURqZjRKaDh1Ynp6VjFNUEtBRlpvRUw4VWJIaHZhRVN2QmxHdmxVTlU1TTNYTmtqOExWOEJVVlRkUVppZkZEM2kwOG1lM1pKcEovSFNBNWJVZ3JZMVFPcVFTMWdKMEtBdUY2eXJsTUZUU3BrcnRxSHYxS3NIb0JhenkvVXo3bEc3MWd0ckhIaHJIV1g4OVNGOHBCS1pob0hQMWVQWkVKMUZua1piNHI3TDFTWmtnWmpMM0MrTzJQRW85SHA0MjBHTkNPTDEzZkhoblEwZ1QzbVEwYmxGYXlqZFVUZDZSOEJuM3RCeWxQTnhqMlRDWnREY2RYNTdSNlhGZmluNWYiLCJtYWMiOiIzMWE4NmVmNzczODIxNjI2NjU0NzYyYzE4MWEwNDY5MTQwODhkYmJiMjllZTRkNDVlYTkyODk3YmI4YWVjMTdkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJPenh5eUhyam1TY3JyaW4rY1JLY3c9PSIsInZhbHVlIjoiOUxnTXBzVlkxcGJQMzUxakVTd1JWR0U5b2FvYmNMVUdFV2RNSytvbGlHdko0Rk9mV2t2ZHJNTSt3eFU1UjQra1BiaGsrSVUrZUZoakFmQTF2SXB0RmFGVVZoWCsrc1A4cE9DVTBqK1kyN0VXSWZ1Q01DRm1aRThPdGtzc1htbFRMVVhqMzRYVlArM0FkT3RzS0FHVFlrNVdMdGs2Yit5Uitua3c2RzRkYlNrY3c2THNObjBrdWNSUUx0a1B0T1RzYVdMM0lTYVJaQUl1TUpGU1V4eS9YWkVFWHJSOFlYdkJ5UlN3ZnlDdytBOTFSOTh6MXVUanlSR3BSSEJSdk9mOXh3RWc2Uk9zbHpQUC9hbzRuUVlRVThoeGZLTTdrcDlwZnQ3dW52L0ZEMHM0bUNQS3crZ2hkOGFLcHNOZEZtQ0lDaUtsTXhyL0Zmb3ZoaUUyTUZPK0RYUXBvbGpya0prU3JlMWd0N0tzTjN2Zy9NUHl5ZFpFOGpyUStUa0E5TktabmNBVE00Z1k5QThOVXV5Y0FzTE9aMmRtRDhhNjRTTCs5SVlVSlNNeUpTeEp3UUxzRUNOcWJPdVBxMUJ3RkRsRkZ3R2xYcUR1Qks3TjFVb01pZGdYQjhGN0tIWExSYjBVdmpZQmZwNWx4dWpwUnZXaTFZRURMWmtUNERmNml1cTQiLCJtYWMiOiIwMjlkY2VjOWNkNTVlODM2NjFjZDk3MTYwMjk2NmU3YjRiOTAxMDZkYWY1YjFjNDdiN2U1YmNkMDg5ZTRmODE1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJTcWVWYWpIV1UvUUhPVC9uSEV4Wnc9PSIsInZhbHVlIjoiTkNTQ0libzV2elppNFk1MnV1bEtzOGh5bzRHYVQrbXV5Zy92dzlhWDROc2RZSlAyVnhtOG9ldG1ZdkZOSlQ2K0JiZEI1ZlRXL0NjWnNtMm9WQ0RYQTg2Y05XajVnMU5rNlJOUXUxbmRzS29MQnJadmNRcHRQOWFSd21oN2hWZGhBY2Q0blpScVZPRmxLdFFDbUZ0djNoSkI0RkR1OEh5VUlCVEhaOHFvTFdxK3NTMWpkcXp1a3RueU9lZ0Z2aGR1WE95M0JOdWIyenhPTUZ5bU5xMnRBSzdjWDNzb095Mlh1U2plZ0VFUEpUSmdSbmhnOGVpV0VJbFA5dm9rWkQ2Nnl3dURqZjRKaDh1Ynp6VjFNUEtBRlpvRUw4VWJIaHZhRVN2QmxHdmxVTlU1TTNYTmtqOExWOEJVVlRkUVppZkZEM2kwOG1lM1pKcEovSFNBNWJVZ3JZMVFPcVFTMWdKMEtBdUY2eXJsTUZUU3BrcnRxSHYxS3NIb0JhenkvVXo3bEc3MWd0ckhIaHJIV1g4OVNGOHBCS1pob0hQMWVQWkVKMUZua1piNHI3TDFTWmtnWmpMM0MrTzJQRW85SHA0MjBHTkNPTDEzZkhoblEwZ1QzbVEwYmxGYXlqZFVUZDZSOEJuM3RCeWxQTnhqMlRDWnREY2RYNTdSNlhGZmluNWYiLCJtYWMiOiIzMWE4NmVmNzczODIxNjI2NjU0NzYyYzE4MWEwNDY5MTQwODhkYmJiMjllZTRkNDVlYTkyODk3YmI4YWVjMTdkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJPenh5eUhyam1TY3JyaW4rY1JLY3c9PSIsInZhbHVlIjoiOUxnTXBzVlkxcGJQMzUxakVTd1JWR0U5b2FvYmNMVUdFV2RNSytvbGlHdko0Rk9mV2t2ZHJNTSt3eFU1UjQra1BiaGsrSVUrZUZoakFmQTF2SXB0RmFGVVZoWCsrc1A4cE9DVTBqK1kyN0VXSWZ1Q01DRm1aRThPdGtzc1htbFRMVVhqMzRYVlArM0FkT3RzS0FHVFlrNVdMdGs2Yit5Uitua3c2RzRkYlNrY3c2THNObjBrdWNSUUx0a1B0T1RzYVdMM0lTYVJaQUl1TUpGU1V4eS9YWkVFWHJSOFlYdkJ5UlN3ZnlDdytBOTFSOTh6MXVUanlSR3BSSEJSdk9mOXh3RWc2Uk9zbHpQUC9hbzRuUVlRVThoeGZLTTdrcDlwZnQ3dW52L0ZEMHM0bUNQS3crZ2hkOGFLcHNOZEZtQ0lDaUtsTXhyL0Zmb3ZoaUUyTUZPK0RYUXBvbGpya0prU3JlMWd0N0tzTjN2Zy9NUHl5ZFpFOGpyUStUa0E5TktabmNBVE00Z1k5QThOVXV5Y0FzTE9aMmRtRDhhNjRTTCs5SVlVSlNNeUpTeEp3UUxzRUNOcWJPdVBxMUJ3RkRsRkZ3R2xYcUR1Qks3TjFVb01pZGdYQjhGN0tIWExSYjBVdmpZQmZwNWx4dWpwUnZXaTFZRURMWmtUNERmNml1cTQiLCJtYWMiOiIwMjlkY2VjOWNkNTVlODM2NjFjZDk3MTYwMjk2NmU3YjRiOTAxMDZkYWY1YjFjNDdiN2U1YmNkMDg5ZTRmODE1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1253108656\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1759473939 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1759473939\", {\"maxDepth\":0})</script>\n"}}