{"__meta": {"id": "Xd6334d55a5eeeab341b80d5a5b880d16", "datetime": "2025-06-28 15:05:52", "utime": **********.54328, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.080782, "end": **********.543296, "duration": 0.46251416206359863, "duration_str": "463ms", "measures": [{"label": "Booting", "start": **********.080782, "relative_start": 0, "end": **********.454475, "relative_end": **********.454475, "duration": 0.37369298934936523, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.454483, "relative_start": 0.3737010955810547, "end": **********.543299, "relative_end": 2.86102294921875e-06, "duration": 0.08881592750549316, "duration_str": "88.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46176328, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.026770000000000002, "accumulated_duration_str": "26.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.48901, "duration": 0.02106, "duration_str": "21.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.67}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.523535, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.67, "width_percent": 2.316}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5268571, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 80.986, "width_percent": 3.026}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5288699, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 84.012, "width_percent": 11.991}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5338628, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 96.003, "width_percent": 1.569}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.535538, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 97.572, "width_percent": 2.428}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-1322936999 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-1322936999\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1392714671 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392714671\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-731008809 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-731008809\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-878676264 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123070655%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjMwa3NUeW9DUEtHRm9GcGtwMGQ5dEE9PSIsInZhbHVlIjoiSmlNUHdWcEMxTS9ZaFlTVi9PZURxczkwSUtIczBiSWttRWMyVldJeENtMWpEQUJiRjlyOWFJZU1kU2FxVDZNbllUbm1xTGVRUEFKWUNCTjBRNWNNZ3QydWpyRkFGNFVNZmhjdmtRaldSNEVsWmkwUzdVU0ViVlNCWkpIVzBUdGduV2ZORk5uTDRMM0hpREVTUFppN2JhUm54Y2ViYmpuYzc0MlBGNjZsV0dHc3E4T3lINTE1aXFoTERxS1d6NkNKVkI4dlhGKytMZEg5NkttQ2RSRk1wMGt5a2Z2VUVZcnhzYythd3dQYTlTODgreWRvWTdKSDYzTXhFaGhGSUJKR3p6ZlpYaWI0WERDeEVxUmtZZUVkclY0R1ZyVng4ejk2MGh3RmNMVC9UQ1NJQUx4Ymx5QS83QlFqZnluTEJuVVpRMFF5Tm1USno1eVVrNjRyMlZGU1NqTTBia1R0US8wT3FmeFljVGdpS04zOE5nUlJ3dTFLajNQV3BzZGd0ODJQV2tDekxrMGFLc0hZWXNJWmQvYVgzOXg1Q29hOHFBLy9WVmgwVXJGYzFadVUrOE1LcitUd1Y2cU9iSzUyZDZmWFBVYXY3NDdRbzJkdHpSbXZrV3IyUi82RTI4eWYvaGI0aE8xdGhCNS9BUXR6ZTV6em1Zcmh4cEJGNUFTbCtRcFoiLCJtYWMiOiIzYTBhZTcyN2E2OTc4Y2U3Y2RjNjE4YjA3Y2ViN2M0MDE1YmU3ZjIyYWEzYzE0ZjQ1YTExZjk3Yzk5MDlkMDJlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZsZ3lKTEZnTzRMVUorVDYvcTI3UkE9PSIsInZhbHVlIjoiR0h0Qjh6bHowdm1WTU9aR1ozQml0ODRLWG04eEc1SklnTzJxZmgwcU84YWdOd2U0YWxDc29rTEVpbkt6ck5WazR1Vi8vS01VSWpDWnZ2RlgvUjk3b1AxaWZ6MWE5TmtCU0VmRGJNM1dDWHE3ZTJ1NzRhOStmZm5OMnFUa21TYzNzeGozUHErcjljSVhKMkdBdjNPYmg1aFZYNXZiQVJFNkoxTG1NVU8rOUlUVm5ka2pjdjVCaHZOL1pyVlB1Nm1yNmdyWGx5UlVSWVVHL1dsT0J1OWhxLzZ4U0hnbG5lazlPRWhvbEx3T0p0N0ZtUUJueEZhbWJsdkdLUDVSN05JMmFTMy9qQXFsbUhSQUdOc2FoM21ockhPdDVETDBib1JkVlpWZ0FTV0VHcjE4UWxGeHZaOXdJbTc2eFZzSExRTGVpV3dvNVFFS0NTd01zRXh4NTVzWE5meXZ3b3phRkZuNWVYVFFBd0pmTmhWY0JOZ3JKbzB1RytwRHhXaU9GcENMOVlIbWNSSy82eWVnbzVhMnlIS3EzSnF4Tk9PTTV0VnBTSElOdTg3bWdDVSs0aDNuZEVaNEVLR3hHUngrdVI1NUNDL0cwbVVyVTRDaS82TTZ5TnhtWUhVZGJOaVp1WmFNTWtxa0pzR3ZVUmdoK0ptT29zemFhbVZtaDJCeHFTZTMiLCJtYWMiOiI2ZjEwYzAzMzYwMmM3NDMzY2E1ZTMzOTg3YzMwMjI1MzJkOTQxYzJmNzdiYzI3YWM0MzdkNmZhNTcwYzRhZGQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878676264\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-728814431 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728814431\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:05:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtxU2d3QlVpTFhMQnhRUHdxeWMxQnc9PSIsInZhbHVlIjoiSEx0YjMwSTFPWnNSWGdRZXF6dVU2T2N6SllFZVR6OEIwWWtCVmh2elV1K1NvWURhbVFDWkY0clJUSmlkNHo1Q3dlbUJXbjFEOWJHUDZSM1pBT1lyNFVqTmIvTFF2aDBwNG5yeEFnZWtQRDVqRDRjODZycGxHL2dxcmhWMnkwb0lDOTFUc2owbWNwcll3b0NVUW1rV3VvdGYwVWFqUDdZVU9OZUNMV0N1L3I3bVZjckZINU9xRStPc25LdnhiL0xrYTh3aTlDUXI5a05hM3ZxSGVEMDhKVXE0NzFwNkpoVUNlR2g1K0tvUUgxcFcwY29IcEF4YUMxbnc4TlpPNnpaWUtPdWwzSjJkSERXc0h6L3hsRXZIU05XSmFuenpTVjRraXl1MzBici9JTWVHczNWYnVqYzMyR214MDlwS2I3cFhzMWN6cUd2UjhJRnR1Nll0Rm4zcUFOWVRiNGlraWNQdHpUTFRqMFBCZktpYjVzb3JDQTlDRXlmSzI3L0lENkEzdjVPUDRVTnZrOXJmUFk5L2tYSkdWa1lQM2xyb0VoRXo3VEtPSDE5eVdlOFNla3lqSnQ2VlQycFFNalNZWDlPcytpYUg1OTFaMGZSTTR3dmN1Q3l5dktFZUEyZUhiS0wxRGtYZDhPb3hJR05qYVFHWjRiVC9sbGZYZkFmdG5jTmYiLCJtYWMiOiIzZmY0ZWU1NDBkNTdjNDQyYzMyNzQ4NjYwMGJjMTM4ZjI0M2E1MGZmMmVkNGFmZmU1ZTc3YjU5MzFhZTM1YWM0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:05:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InMzWm5PakI4b1paUm8rNHp1TnNKdWc9PSIsInZhbHVlIjoieTNNODBrd0FIdzdLb2Z2OVVPQkNnbzBTcFpkak5tamdnM1pYMGd6OFh3aVE0MW0va1FvWU5pOU1icSt0QmVMeXhRTGV1QzlWaVZia0Q2S21MYjJVUjEvZi82cGFTS1JDMVVINWorUk5aeUcvOUI2cHhOK0R3TnY0OHpZM3IrTUFvNk0xMFAydC9FRlltWG9odHNsMjNyK0lnVFo0RzZEeVk5OVJnSWV6TmdlZURKeVF4U2NvdkNhWXJOSGdObFFBUlB2RVFFaEZSbVNTVmVuL0FDTjN3MnQvUllUaXVlRmNYejZrTkx0U0pSUHQyOXFRM1drOEN5ZWxnY1NGTVZlZDU1cDVEeHprZWlNREVzOUtBZTlYc1RVNE9xclRMZ3dsTnVhRnRXcVNpczZHSGFhUnhxV04yUThJV2RiY2xSaWJueE5IQVluQmVUclJVSU9GbEN2dit2dWNGNG1OV2N4aEJnMTNXVE4xaW1hZXJTSWU0WkVEQkpuTjBQMDJmTC8vcFh6ZG83TmFvUytmNGx1aENBekRmcEVBbHd0UnpibmZEOVZYS3AwNjVYdDNpekVnOWt4eUd6SWxUUUFobHViZEhhSXpHSmFVREowY0JMUEUvTDZpc3NveC9SMXlkM2wzWnNDWkgyWVpjSnp4aWpTb2FPdnc4dU9GTkR1Nng4L2siLCJtYWMiOiI3MjA0MWE0ZjMzOGVjY2M0ODgxNmRhNTUxZDk4NDc4MGY5OWIzNzk3YTk2NmVjYzI1NzFmMGFjOTgwOWE3ODcxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:05:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtxU2d3QlVpTFhMQnhRUHdxeWMxQnc9PSIsInZhbHVlIjoiSEx0YjMwSTFPWnNSWGdRZXF6dVU2T2N6SllFZVR6OEIwWWtCVmh2elV1K1NvWURhbVFDWkY0clJUSmlkNHo1Q3dlbUJXbjFEOWJHUDZSM1pBT1lyNFVqTmIvTFF2aDBwNG5yeEFnZWtQRDVqRDRjODZycGxHL2dxcmhWMnkwb0lDOTFUc2owbWNwcll3b0NVUW1rV3VvdGYwVWFqUDdZVU9OZUNMV0N1L3I3bVZjckZINU9xRStPc25LdnhiL0xrYTh3aTlDUXI5a05hM3ZxSGVEMDhKVXE0NzFwNkpoVUNlR2g1K0tvUUgxcFcwY29IcEF4YUMxbnc4TlpPNnpaWUtPdWwzSjJkSERXc0h6L3hsRXZIU05XSmFuenpTVjRraXl1MzBici9JTWVHczNWYnVqYzMyR214MDlwS2I3cFhzMWN6cUd2UjhJRnR1Nll0Rm4zcUFOWVRiNGlraWNQdHpUTFRqMFBCZktpYjVzb3JDQTlDRXlmSzI3L0lENkEzdjVPUDRVTnZrOXJmUFk5L2tYSkdWa1lQM2xyb0VoRXo3VEtPSDE5eVdlOFNla3lqSnQ2VlQycFFNalNZWDlPcytpYUg1OTFaMGZSTTR3dmN1Q3l5dktFZUEyZUhiS0wxRGtYZDhPb3hJR05qYVFHWjRiVC9sbGZYZkFmdG5jTmYiLCJtYWMiOiIzZmY0ZWU1NDBkNTdjNDQyYzMyNzQ4NjYwMGJjMTM4ZjI0M2E1MGZmMmVkNGFmZmU1ZTc3YjU5MzFhZTM1YWM0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:05:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InMzWm5PakI4b1paUm8rNHp1TnNKdWc9PSIsInZhbHVlIjoieTNNODBrd0FIdzdLb2Z2OVVPQkNnbzBTcFpkak5tamdnM1pYMGd6OFh3aVE0MW0va1FvWU5pOU1icSt0QmVMeXhRTGV1QzlWaVZia0Q2S21MYjJVUjEvZi82cGFTS1JDMVVINWorUk5aeUcvOUI2cHhOK0R3TnY0OHpZM3IrTUFvNk0xMFAydC9FRlltWG9odHNsMjNyK0lnVFo0RzZEeVk5OVJnSWV6TmdlZURKeVF4U2NvdkNhWXJOSGdObFFBUlB2RVFFaEZSbVNTVmVuL0FDTjN3MnQvUllUaXVlRmNYejZrTkx0U0pSUHQyOXFRM1drOEN5ZWxnY1NGTVZlZDU1cDVEeHprZWlNREVzOUtBZTlYc1RVNE9xclRMZ3dsTnVhRnRXcVNpczZHSGFhUnhxV04yUThJV2RiY2xSaWJueE5IQVluQmVUclJVSU9GbEN2dit2dWNGNG1OV2N4aEJnMTNXVE4xaW1hZXJTSWU0WkVEQkpuTjBQMDJmTC8vcFh6ZG83TmFvUytmNGx1aENBekRmcEVBbHd0UnpibmZEOVZYS3AwNjVYdDNpekVnOWt4eUd6SWxUUUFobHViZEhhSXpHSmFVREowY0JMUEUvTDZpc3NveC9SMXlkM2wzWnNDWkgyWVpjSnp4aWpTb2FPdnc4dU9GTkR1Nng4L2siLCJtYWMiOiI3MjA0MWE0ZjMzOGVjY2M0ODgxNmRhNTUxZDk4NDc4MGY5OWIzNzk3YTk2NmVjYzI1NzFmMGFjOTgwOWE3ODcxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:05:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}