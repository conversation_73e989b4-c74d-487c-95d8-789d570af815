{"__meta": {"id": "X9e2b1cf68c0d510053d409a5360e6d85", "datetime": "2025-06-28 15:04:52", "utime": **********.506596, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.085159, "end": **********.506609, "duration": 0.4214498996734619, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.085159, "relative_start": 0, "end": **********.447249, "relative_end": **********.447249, "duration": 0.3620898723602295, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.447258, "relative_start": 0.36209893226623535, "end": **********.506611, "relative_end": 2.1457672119140625e-06, "duration": 0.05935311317443848, "duration_str": "59.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46176328, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.006500000000000001, "accumulated_duration_str": "6.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.479604, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 24}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.488944, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 24, "width_percent": 5.231}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.491549, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 29.231, "width_percent": 8.615}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4933138, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 37.846, "width_percent": 47.077}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.498083, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 84.923, "width_percent": 5.231}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4995842, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 90.154, "width_percent": 9.846}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-1627212788 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-1627212788\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-679374525 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679374525\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-954119847 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-954119847\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-273870668 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123070655%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik4wS0NJQlUzT2h6MHpJWjRnaGtKcHc9PSIsInZhbHVlIjoiN1A0eDZRVEh0SmdlckRiRkQ5alJUKzJESkNFNnpiMmF6clNFamtlQStKa2d0ZkJyaTRveFhOY2xBYi9EU2JnZjBveUQydVI4Z3JxaXVvc2VYdVl0NkUzYXg3MjR0SzJrT1B5bzJoUTVUbUxpYTdqZnB6UjFEUDVUamE3cVJMdmdkWDRYenU4QlVnU2d6R203aU5xZVdkVElWdTU0RkxRRlRjckFXaFVqRnBTK0xkRmxZeFp3TXIrSFBqVEhzYVR4R2xPYjNYSUhaYTZqZWdzOEl2bjkzZ05VNngwZVpEd0R4VmNGa0JqcEZTQ2JjUEZBN3lNeWJyTkVXQStuaDhXVm1nYTc4cmpmK1hoVml5b09MWldZdjJPakFMaGZlVUdRckVhaXNYRDFwR2NtWm1jY3NNTVpEYVZCT045ajB1c2pqdHc4L25KYUZ3OVh6T3VKREthUjZyN0pJSUtiVytaMm96Q21weEhWamNtY1d1WXA5ZEpuSTU2Z0RDN1M2S2R1c1dhejlIdVRJUUdjb2RQRmxUTTFXSUhFZTV5d0xPb1I2a3k0Mlg2Yk9zcktSdy90K2xydmUzcm5TckUwWWJyMDV4djJQNmd3LzlnKythaUNUVFczN2pDRk9mS3F1cmIzYUdDalNXSDBNYlZac0RlUlNJTjZQeS8zend6OUhCT0IiLCJtYWMiOiJiNTE1OTViYjkyZDMzMGE3ZGQxNzZjMWIzMTY3ZWEzZmRlNzczMmRiOTQyMzRlNzY1MDY0NjIwYWE5ZTRkYTg5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpCSXgva3gwSTFLRUY3bmVQUXFpaFE9PSIsInZhbHVlIjoiZFlkZzlVU2lEaElXSWlBakRnQTlXOGZOai80VFQyVkRMeHdoZmhpQ1d3WnVFUFhUQUxCTkRpL00ya0ZrRWNwMWJXdFhSekNYY2NaYkdZbnJsSG05RUswYWlkZGNLZllQZml6Um9CL01aMmZoWllwN3R1RWN5N0k4eS83VVM4VVR5UFJnNWJmT2FLRi9sSStkYjhWMXVILzlzeWFIeTBTQkh6dFhSSVZmdXRrM3h3M290QllkVFB2TGRZZEw2UnRsVGlYbHd2WXQ4c2xERXVZOUYzZWhkN3pQS2h1TUFSSVVsaFNTSG1wNjJxTmZjUUtyMUpRQmxwYnkzMWhNYzlGWnZEa2xZVzh1TGE1bHpKRlNGTGVXV051b0tHZnJmN2ZkZkpPQ0o2Z3pJOHdDZktiT3lIdEV0VmhkclI5ZXBCeDFCL2tLZ0N6ZkFxcXpjYmg2VEg4Nm9QYm1OUHNucnVHbDR4MVRYbnBOUlY1WW9YTGxhNXI2WUMzU2dqWWZwVFM1VWczSzBVb0FCZXNhdDd5WXZkbkpBVjl5Q0NhUVREeUxRcGYrVmRmN3pDREQvalFiQmswdWR3NzliZy82S00zbVVncXJiRmpWaTdYSjNJNXpaeXNGMlQxWklEYmpqZG1Bd1JSNGJ2UlBPUlRxRDVHeW9FOUJGT1prc29MN3lxNXgiLCJtYWMiOiJmODM0OTEzYWZjMjE4YWQzMzdiMDNiOTM3MDQ2MmFjNjUyNjUxODgzN2ZjZmIyZTQ1Nzc0NWQ1NjgwZTZkZjM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273870668\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1711799725 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711799725\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1287527531 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:04:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjMwa3NUeW9DUEtHRm9GcGtwMGQ5dEE9PSIsInZhbHVlIjoiSmlNUHdWcEMxTS9ZaFlTVi9PZURxczkwSUtIczBiSWttRWMyVldJeENtMWpEQUJiRjlyOWFJZU1kU2FxVDZNbllUbm1xTGVRUEFKWUNCTjBRNWNNZ3QydWpyRkFGNFVNZmhjdmtRaldSNEVsWmkwUzdVU0ViVlNCWkpIVzBUdGduV2ZORk5uTDRMM0hpREVTUFppN2JhUm54Y2ViYmpuYzc0MlBGNjZsV0dHc3E4T3lINTE1aXFoTERxS1d6NkNKVkI4dlhGKytMZEg5NkttQ2RSRk1wMGt5a2Z2VUVZcnhzYythd3dQYTlTODgreWRvWTdKSDYzTXhFaGhGSUJKR3p6ZlpYaWI0WERDeEVxUmtZZUVkclY0R1ZyVng4ejk2MGh3RmNMVC9UQ1NJQUx4Ymx5QS83QlFqZnluTEJuVVpRMFF5Tm1USno1eVVrNjRyMlZGU1NqTTBia1R0US8wT3FmeFljVGdpS04zOE5nUlJ3dTFLajNQV3BzZGd0ODJQV2tDekxrMGFLc0hZWXNJWmQvYVgzOXg1Q29hOHFBLy9WVmgwVXJGYzFadVUrOE1LcitUd1Y2cU9iSzUyZDZmWFBVYXY3NDdRbzJkdHpSbXZrV3IyUi82RTI4eWYvaGI0aE8xdGhCNS9BUXR6ZTV6em1Zcmh4cEJGNUFTbCtRcFoiLCJtYWMiOiIzYTBhZTcyN2E2OTc4Y2U3Y2RjNjE4YjA3Y2ViN2M0MDE1YmU3ZjIyYWEzYzE0ZjQ1YTExZjk3Yzk5MDlkMDJlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZsZ3lKTEZnTzRMVUorVDYvcTI3UkE9PSIsInZhbHVlIjoiR0h0Qjh6bHowdm1WTU9aR1ozQml0ODRLWG04eEc1SklnTzJxZmgwcU84YWdOd2U0YWxDc29rTEVpbkt6ck5WazR1Vi8vS01VSWpDWnZ2RlgvUjk3b1AxaWZ6MWE5TmtCU0VmRGJNM1dDWHE3ZTJ1NzRhOStmZm5OMnFUa21TYzNzeGozUHErcjljSVhKMkdBdjNPYmg1aFZYNXZiQVJFNkoxTG1NVU8rOUlUVm5ka2pjdjVCaHZOL1pyVlB1Nm1yNmdyWGx5UlVSWVVHL1dsT0J1OWhxLzZ4U0hnbG5lazlPRWhvbEx3T0p0N0ZtUUJueEZhbWJsdkdLUDVSN05JMmFTMy9qQXFsbUhSQUdOc2FoM21ockhPdDVETDBib1JkVlpWZ0FTV0VHcjE4UWxGeHZaOXdJbTc2eFZzSExRTGVpV3dvNVFFS0NTd01zRXh4NTVzWE5meXZ3b3phRkZuNWVYVFFBd0pmTmhWY0JOZ3JKbzB1RytwRHhXaU9GcENMOVlIbWNSSy82eWVnbzVhMnlIS3EzSnF4Tk9PTTV0VnBTSElOdTg3bWdDVSs0aDNuZEVaNEVLR3hHUngrdVI1NUNDL0cwbVVyVTRDaS82TTZ5TnhtWUhVZGJOaVp1WmFNTWtxa0pzR3ZVUmdoK0ptT29zemFhbVZtaDJCeHFTZTMiLCJtYWMiOiI2ZjEwYzAzMzYwMmM3NDMzY2E1ZTMzOTg3YzMwMjI1MzJkOTQxYzJmNzdiYzI3YWM0MzdkNmZhNTcwYzRhZGQzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjMwa3NUeW9DUEtHRm9GcGtwMGQ5dEE9PSIsInZhbHVlIjoiSmlNUHdWcEMxTS9ZaFlTVi9PZURxczkwSUtIczBiSWttRWMyVldJeENtMWpEQUJiRjlyOWFJZU1kU2FxVDZNbllUbm1xTGVRUEFKWUNCTjBRNWNNZ3QydWpyRkFGNFVNZmhjdmtRaldSNEVsWmkwUzdVU0ViVlNCWkpIVzBUdGduV2ZORk5uTDRMM0hpREVTUFppN2JhUm54Y2ViYmpuYzc0MlBGNjZsV0dHc3E4T3lINTE1aXFoTERxS1d6NkNKVkI4dlhGKytMZEg5NkttQ2RSRk1wMGt5a2Z2VUVZcnhzYythd3dQYTlTODgreWRvWTdKSDYzTXhFaGhGSUJKR3p6ZlpYaWI0WERDeEVxUmtZZUVkclY0R1ZyVng4ejk2MGh3RmNMVC9UQ1NJQUx4Ymx5QS83QlFqZnluTEJuVVpRMFF5Tm1USno1eVVrNjRyMlZGU1NqTTBia1R0US8wT3FmeFljVGdpS04zOE5nUlJ3dTFLajNQV3BzZGd0ODJQV2tDekxrMGFLc0hZWXNJWmQvYVgzOXg1Q29hOHFBLy9WVmgwVXJGYzFadVUrOE1LcitUd1Y2cU9iSzUyZDZmWFBVYXY3NDdRbzJkdHpSbXZrV3IyUi82RTI4eWYvaGI0aE8xdGhCNS9BUXR6ZTV6em1Zcmh4cEJGNUFTbCtRcFoiLCJtYWMiOiIzYTBhZTcyN2E2OTc4Y2U3Y2RjNjE4YjA3Y2ViN2M0MDE1YmU3ZjIyYWEzYzE0ZjQ1YTExZjk3Yzk5MDlkMDJlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZsZ3lKTEZnTzRMVUorVDYvcTI3UkE9PSIsInZhbHVlIjoiR0h0Qjh6bHowdm1WTU9aR1ozQml0ODRLWG04eEc1SklnTzJxZmgwcU84YWdOd2U0YWxDc29rTEVpbkt6ck5WazR1Vi8vS01VSWpDWnZ2RlgvUjk3b1AxaWZ6MWE5TmtCU0VmRGJNM1dDWHE3ZTJ1NzRhOStmZm5OMnFUa21TYzNzeGozUHErcjljSVhKMkdBdjNPYmg1aFZYNXZiQVJFNkoxTG1NVU8rOUlUVm5ka2pjdjVCaHZOL1pyVlB1Nm1yNmdyWGx5UlVSWVVHL1dsT0J1OWhxLzZ4U0hnbG5lazlPRWhvbEx3T0p0N0ZtUUJueEZhbWJsdkdLUDVSN05JMmFTMy9qQXFsbUhSQUdOc2FoM21ockhPdDVETDBib1JkVlpWZ0FTV0VHcjE4UWxGeHZaOXdJbTc2eFZzSExRTGVpV3dvNVFFS0NTd01zRXh4NTVzWE5meXZ3b3phRkZuNWVYVFFBd0pmTmhWY0JOZ3JKbzB1RytwRHhXaU9GcENMOVlIbWNSSy82eWVnbzVhMnlIS3EzSnF4Tk9PTTV0VnBTSElOdTg3bWdDVSs0aDNuZEVaNEVLR3hHUngrdVI1NUNDL0cwbVVyVTRDaS82TTZ5TnhtWUhVZGJOaVp1WmFNTWtxa0pzR3ZVUmdoK0ptT29zemFhbVZtaDJCeHFTZTMiLCJtYWMiOiI2ZjEwYzAzMzYwMmM3NDMzY2E1ZTMzOTg3YzMwMjI1MzJkOTQxYzJmNzdiYzI3YWM0MzdkNmZhNTcwYzRhZGQzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287527531\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-377400848 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377400848\", {\"maxDepth\":0})</script>\n"}}