{"__meta": {"id": "Xcaee51eb67d99c91be34529c33d1d926", "datetime": "2025-06-28 11:22:34", "utime": **********.256878, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109753.848004, "end": **********.25689, "duration": 0.4088859558105469, "duration_str": "409ms", "measures": [{"label": "Booting", "start": 1751109753.848004, "relative_start": 0, "end": **********.192721, "relative_end": **********.192721, "duration": 0.34471678733825684, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.192728, "relative_start": 0.3447239398956299, "end": **********.256891, "relative_end": 9.5367431640625e-07, "duration": 0.0641629695892334, "duration_str": "64.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45193504, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0020099999999999996, "accumulated_duration_str": "2.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.237346, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.139}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.246946, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.139, "width_percent": 15.423}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2495718, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 87.562, "width_percent": 12.438}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-2014256301 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2014256301\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1214938067 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214938067\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-955468460 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-955468460\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-957345605 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6ImUrS0VGSzVVVGhNWkxwTjRkQW8zTFE9PSIsInZhbHVlIjoiTDRlbTZ4eTRQbktjK0RkYkR2bVdzMkY5Ti9OcEJ1bTJsSTNMZkJ6WmdVZ2wwYnNqODRBalVweHNORlJyQXBPWnJzZHhteURtcnRLeHBOd0xGdWR1MTFpY0lvRDR5bXNzbDloa3BZRGhxNUhadSs1SWkrK2swd1lUQnNnemZWUXF2M3dkcWxZVVQ0eXZKeEx6WWo4ZVZ0MkZKYlVmNDZZcGs3V2FaV3ljR1RvbkFMNHZES0ZhVVdWclozL2dIdWs4YnZnN200YUdrVEMxNWNpTExzRGx2WWxPV29RbkFQbFhBeGRrRiswaXA2b3BaUFZ6MGVzU1p0TnpxWjkzdVBNdFgzV3ZkZGEvclBRQjEwN1hJbUcwdmk1bm1sNnpWV2NWeGNGUFpTSWxlRWhvSitFaEZydGZrQVhja3g1SE5jNGh3TlMyTTk4U0wrS3VJWmpHZzZ6dlp6NkFpNDFTZkx1K2hldlNiRXJ6dlpGRTFocnhHcmVXV2U1WWQ2OURWRU5YYWxpM3BpbXROMVpHeXJ4YnB2dFJtd0Z5bXZickZoYnBXSXRYMEw4WnI5MTBsb01WZjQyS0FDZDhVcHAxcy9DckR2c20xWUk4a2xZYWpKeHM2TXZxTnFySDR5dHZkUURWSmQ1dzMxbUxFOXMvNG8vS0FNTXdsa3FoQjZKR095SUkiLCJtYWMiOiIxOWYzNzg3YzE3MjY5M2Q0OGZmNzBmNTc2NDYwYjlkMzcwZTUyYjAwOTdjNmFkNDcxYjlhNTc0NTdhZGJlZWI0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5zRGljU1duTStCWlB0L2JvYnBxckE9PSIsInZhbHVlIjoiZmErZ1VIeEtBNEhQcGNVL0R3ZUc0NHhOcW1JbzhGajVTTmJCcnp3ek5KVVp6Sm0yaFdaSmNadmpzMHZxV1dkV2VQT081aHRXeXhBWnI3Nzlnb292cTRVdlRzdWhHL04zdFhGM25VSXhRZzBVZW50N3RRUVBBSGd0djRzUUJkcmRtZWV4TmZKc1k1c3VoS1QwcDAvOGNLSnZ1T2lPTTVEaVZySk50MHRQNVMyaXpGb1NJaUJ3bXNLTlhOeHEzL0RWWDJDdGwxM3BVTTFrS0dWMXpWeVhqQ1hSZVZGdTJxTzJ5NE5ZeWdzZjJyZ1dLRkE1TEtkdU9DUlloelFkV2VZcmRpVWVxaldncGJNcUpyMHR2blFPUlVxSHZaYmYwb0dHdktpTHZ6R21WNnp5MVNpdDdUWkdrbGU1cjE4amZnRVJpNUtnc2FnTy9PQWlTcHhWVmtSRlBjK0RmYjB1cXZPVTYzc2RwWFU2bzFWZ1lPYThNZ1FzYjR3Nkl3RFNoUVlqY0o2MmQrNm1FWjRwNmk5aWllekVaVGFTT3FqNW5oT0hicWxiWWZzSG1KSCt0NndEVGk0bGRKSVVDSms2d3JNcS9XUU1yRmMzNTFLTnJkUFVoWDFrc1BDeFhwUmpIZTE1VGJUWERwNStOY2JySVlhN21vZkpSclJidEpNekJqV2QiLCJtYWMiOiIzMzUxZDY5MWRlNGQzZWZmYTM1NDUzOGQ1NGFlNDU0ZTc2OWIwMWQyOGIyYTZiNTI5ZDczYTQzMjNhNjI3OTM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-957345605\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-959404239 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959404239\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-219514815 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:22:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJZbXBuNDdScDFYV1R6bStNTXRHdUE9PSIsInZhbHVlIjoidnV3S08wWmlNZ2RJR2xGZzBJSWdnK1pNSWlIdHZPdGFFbFlWSTcvQ0gzb3hGNWNHL1BVWlNNKzNCMjltVlJSL0o0TFhaWVhXTHFRQUZDUDBFZ2FvME4vNmRIaVZvRUlxQWJ4MElEY0luQ0hZR1JZR1UrRFpqYTMxVGQvaHloanViZkRpMkg3T2lYRFU0cjd0bDczWjBqa2Z2QlV5ZHZoWXF1ZU1hKzNCTG9aZ0k2VWl1NndsdGcvT3NXOFduR3lPbWJWRnJMQ2twVWhjSEtNUTdSOGZEYyt0T2Q3TFhQQW9RZVQ2NXVURHFGaWI4RkI4Njl3QUl4MXJ0QndZeHR2K2p6ZlpQc0NldjVoYXFIZmMxZHNzTU1GZE1NWWZNZVAyTUNFQTkyWEZaMGhZOXBEczJ6SVJkd2wrVFRUMzRPb2N1bnlYNUw0d2VpVHFjZXlvWGk5c0ZvVXhrUUZMR2ptWFZyL0ZMQTVPMTc0WFNWREZHcVBpNmt1QmxHeG1jQXBuSUI3SERkZktQemlrUWNlSkN1M3JnbDJ5TTRZZUdWQmxZaHlJOEhHZEFrb21jdEM5T1NUNXhGY1VlR1RHSmdpdGs3UllUVCtWM2Z5cXlIZ1VTbWtHcEpLVG44VDExK3pTVFlxOTNUeSs4OWxKL2NlQUhMUnF5NStSU25MWGdTUTYiLCJtYWMiOiI5NTAyYzgyYWEzOTNjMDg1NWZkZjA3ZjU3MzQ5ODAwYTcxODQ0YmZjNWM3NzM2MDc5YTQ4YTZiZmIyNTZiYTc4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkNGZXNCV3NINXdCaU83Y3hnVWJSUkE9PSIsInZhbHVlIjoiRmo5L3B5ODY4cjkrWWdNeGo1UXlQMmh0SkVyc0pseEc3eWFhZ2hrYkhoWWt6RU9uWm1xdjZTelAxOXdac3dmTUNKUzhUSmdidDlXTkJuVjg4YksvRXA1dTBRaVVHVDhENlJNUDkxK25tNVFWZ09rYXdvL1UzNXdKd3o2aEdWTm50d2k2eG0wSGpTMWNtQXNQRS83OXJDTUxXUW1OTTJIdHVnaXo4S29UT3ZtTVBwTjRxYmJ6aTZxSlE0d0t5QmNNM3FjOENxUGpLbkVyanVEWjRxcHFHZzZLNmpBTDlZREUxYXpjRVpOYUk4TUFKMDFudHF0QmoyazdMWlNhOGpQV1hEYk5uT29GdFBiWmcyVXUxQ2VjNGtxZmdMSElYWUVjQUc1aGtWR0RSNWR5Wk90VWh4YnBMSHZjdlZxd1dISzczUmkzdGp4V0NacDc2K04rZ21XUjM0d0RwSjlMSktHSDR6a2xoYUJOSEVtS1Y3SEdxZW5pRUJnckZYb2ZZWS9XK2IwOG55SXhsVU1CRlZpN0IrM0txcWxLQWFCN2E2cHlQZU1QMmp0NXkzZUdsMTRJUXBROUo5UEl6OUc1N2p6eXNEWE03akNQSzhsbHdxN1pnaUEybUFkNU01VDZScXlWNGhKNG9HM2NUTDNFeDllUUVXNVM0WnJiY1ZFQzNYeFMiLCJtYWMiOiIxMTM3OTgyMmVlN2Y2YWM3MmY2N2JlMGIzYTRmMTBjZDZmYjJkMDA2NTZjNjgwNWZlMmFkMzRlNDMzOTI2YzI4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJZbXBuNDdScDFYV1R6bStNTXRHdUE9PSIsInZhbHVlIjoidnV3S08wWmlNZ2RJR2xGZzBJSWdnK1pNSWlIdHZPdGFFbFlWSTcvQ0gzb3hGNWNHL1BVWlNNKzNCMjltVlJSL0o0TFhaWVhXTHFRQUZDUDBFZ2FvME4vNmRIaVZvRUlxQWJ4MElEY0luQ0hZR1JZR1UrRFpqYTMxVGQvaHloanViZkRpMkg3T2lYRFU0cjd0bDczWjBqa2Z2QlV5ZHZoWXF1ZU1hKzNCTG9aZ0k2VWl1NndsdGcvT3NXOFduR3lPbWJWRnJMQ2twVWhjSEtNUTdSOGZEYyt0T2Q3TFhQQW9RZVQ2NXVURHFGaWI4RkI4Njl3QUl4MXJ0QndZeHR2K2p6ZlpQc0NldjVoYXFIZmMxZHNzTU1GZE1NWWZNZVAyTUNFQTkyWEZaMGhZOXBEczJ6SVJkd2wrVFRUMzRPb2N1bnlYNUw0d2VpVHFjZXlvWGk5c0ZvVXhrUUZMR2ptWFZyL0ZMQTVPMTc0WFNWREZHcVBpNmt1QmxHeG1jQXBuSUI3SERkZktQemlrUWNlSkN1M3JnbDJ5TTRZZUdWQmxZaHlJOEhHZEFrb21jdEM5T1NUNXhGY1VlR1RHSmdpdGs3UllUVCtWM2Z5cXlIZ1VTbWtHcEpLVG44VDExK3pTVFlxOTNUeSs4OWxKL2NlQUhMUnF5NStSU25MWGdTUTYiLCJtYWMiOiI5NTAyYzgyYWEzOTNjMDg1NWZkZjA3ZjU3MzQ5ODAwYTcxODQ0YmZjNWM3NzM2MDc5YTQ4YTZiZmIyNTZiYTc4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkNGZXNCV3NINXdCaU83Y3hnVWJSUkE9PSIsInZhbHVlIjoiRmo5L3B5ODY4cjkrWWdNeGo1UXlQMmh0SkVyc0pseEc3eWFhZ2hrYkhoWWt6RU9uWm1xdjZTelAxOXdac3dmTUNKUzhUSmdidDlXTkJuVjg4YksvRXA1dTBRaVVHVDhENlJNUDkxK25tNVFWZ09rYXdvL1UzNXdKd3o2aEdWTm50d2k2eG0wSGpTMWNtQXNQRS83OXJDTUxXUW1OTTJIdHVnaXo4S29UT3ZtTVBwTjRxYmJ6aTZxSlE0d0t5QmNNM3FjOENxUGpLbkVyanVEWjRxcHFHZzZLNmpBTDlZREUxYXpjRVpOYUk4TUFKMDFudHF0QmoyazdMWlNhOGpQV1hEYk5uT29GdFBiWmcyVXUxQ2VjNGtxZmdMSElYWUVjQUc1aGtWR0RSNWR5Wk90VWh4YnBMSHZjdlZxd1dISzczUmkzdGp4V0NacDc2K04rZ21XUjM0d0RwSjlMSktHSDR6a2xoYUJOSEVtS1Y3SEdxZW5pRUJnckZYb2ZZWS9XK2IwOG55SXhsVU1CRlZpN0IrM0txcWxLQWFCN2E2cHlQZU1QMmp0NXkzZUdsMTRJUXBROUo5UEl6OUc1N2p6eXNEWE03akNQSzhsbHdxN1pnaUEybUFkNU01VDZScXlWNGhKNG9HM2NUTDNFeDllUUVXNVM0WnJiY1ZFQzNYeFMiLCJtYWMiOiIxMTM3OTgyMmVlN2Y2YWM3MmY2N2JlMGIzYTRmMTBjZDZmYjJkMDA2NTZjNjgwNWZlMmFkMzRlNDMzOTI2YzI4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219514815\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1439546695 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439546695\", {\"maxDepth\":0})</script>\n"}}