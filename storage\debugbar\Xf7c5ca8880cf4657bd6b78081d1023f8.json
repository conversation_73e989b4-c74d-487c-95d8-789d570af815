{"__meta": {"id": "Xf7c5ca8880cf4657bd6b78081d1023f8", "datetime": "2025-06-28 16:04:12", "utime": **********.739746, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.321131, "end": **********.739761, "duration": 0.41863012313842773, "duration_str": "419ms", "measures": [{"label": "Booting", "start": **********.321131, "relative_start": 0, "end": **********.673577, "relative_end": **********.673577, "duration": 0.3524460792541504, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.673584, "relative_start": 0.35245299339294434, "end": **********.739763, "relative_end": 1.9073486328125e-06, "duration": 0.06617903709411621, "duration_str": "66.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45712304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01746, "accumulated_duration_str": "17.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6994581, "duration": 0.01592, "duration_str": "15.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.18}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.723835, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.18, "width_percent": 3.265}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.730376, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 94.444, "width_percent": 5.556}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1780774265 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1780774265\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-36970041 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-36970041\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1821856072 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821856072\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1485259468 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126629815%7C23%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxaL3V5Sk5HNnlObW1Vbzl1alZDbEE9PSIsInZhbHVlIjoicm5IaUVPK3JYVVB0L2svMkpxcUQxK2c3eCt4OFhZalZCYTlsM3RzNkdpem8yMm1wdkdraHRYcDgweEhOdXBJNE1mUmxJdGdaSjROT2ZVVkVyUDdobjUvb284SkZTcmdRY2QwNEFxS0R1SFI3YU1MbjFUWlc5V0xPSWhBb0RhVDlpdG5TSGgvdXdWV3lBWGZTWkxqSkZIaGkvaUgycVp0S3hjZDc2S1E2S01MODhSSXF4M0hJdCt1elBMRFFRYTFleDVPeld3emY3TmI2L2J1N0wrSm1ta3ZIWUluVDZzelJXTXQrZ01Tc2lYcExOTW12emtZS3FMVUVYTUoyVHZrdnovc1A5VWlFeFA3QWs3c2JBblFCTC9BMGh0cGd2ekRwcmJpdG1BcEM5SkhVdUg0dTJ5MmJWbnZSZS9MbVVFMm9hWGU3V0x1WUhDUFlBNU4rTk9YSkttaW5UTHRaYnpTQ1p4WC9ROU40UzFSZzM3bEpONlZkSU5IN1RwN0Y2OUVsZm9xUkNLbEhWYk1BSTEzQTV4S3JiOGN6MVd2ZFBzVzFFVm9zYVFmWmpEQjVRTDhCRzFhSGpWdFRKM0dBMkszWS9tN3hGeWo2S3FtY1JFdm9EZTRQZFlid0FSSk12bU82TktYRHJpMU1oa3lIWSt2MUdsaVFnNk1nN0ZicWRIeWkiLCJtYWMiOiIzMGQ3ZWRhNTI1ZjJlODQ5Nzk4NzIwYTNlMDMwN2JlNWFhZDUxOTBlZDdiYTZhYWZhYTNhMDcwZWY2YWM2MDE5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikg5VUQ1VU85L3RPc0pLZm9mN09lQnc9PSIsInZhbHVlIjoialFucjB4czljcjF6ZWd3U0QyMWtQdVFKMUI4MW9kRTNDanA5N0Q1Rlg0bERUVjV5RU9qNHRhbDN6aHVicG5kLzEzZ3pJVlVCYWYxc081SFdEK3BoVkk3RzZYUmtPRS94WWx3Mk10OUZJbkdOem1nYmdsQUhhS2pwSUdyMEhYYm9jWHhFTlNNVGlpT3VxZkJKQk1uNmNJZXZzd1FnY25HMHN0blBMbWppZ1FzbUVhMmZja0lxM0d3WkZEMU1NaFVFQ1FXNVd0YTYvdDFGN1VTQkM1dUhqSkVNSXdBOHNOMWc0bDY2d3M1bGMyNHprUUlRUW9aMEpELzBDR2RlRVIrOG5EejdEaTRnWE5zT1hFQjFMRitTSjhkRnBIZk5kelp2c2FTWmpCc1pVT1VJNlUwWDNHUmdYRDhRRUw5TW40aU0xYVVnKy9VV1k4R2NEQ0VuSzh4V1N0OFVnRVQ5QUM4YVdCU01UQUhvbW9iWGZMYy81anVVclhqZjdLWU9PVEtiLzRTOWRRcEVZZE81MnRWRWo4WGd5UzRzeGhKMjRaTDVpaGh5MlFoZjlSaGFrZEVCOTY4OURkcEpZWUZidXlaY215dXBtV0dWMWUvTzMrVC8xYU56amFDMFI0V21kaUhWcUUzMXRKVnRVRkJjcU9QRXhEazJrL3FyNGlCb3ZsUVoiLCJtYWMiOiI1NmEyOGE2MjVjMjFiZGY4YzJiMGFhM2QxODE3OTIxMDEwY2YyODU0NzA2ZWQ1NzA3N2YxOTAxNGJmYjliOGQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485259468\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-786281213 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:04:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlV1MDBtZTRoMmw3QjNpM0VBTFhwNHc9PSIsInZhbHVlIjoiSFcwTW9GMExsQ0hMZjFYRW91MVZvVFhua2FteGpNRDRrUTM5eVEyWStWUGpxMFVtTnkyRnVsY2RvUWYxM21ZaG5qSmtLR3ZVWHV0dlZVWW82UGlHVmVtM1BwMU9IcGZ2NmFQd0d6RlROem9EejZQV0ZNOGRsaXJvM1VPYXB3S0tOMXRuZEh3UWhqWFY1a2dsZmhwdGpZazFRYTVPaGN4OXBraFZ0TnNRbTdDRC9hUWtYSGFkdVFPV1BWRzJkbStCbTJVVERSZGJTeHdjM3ZhV2R6M0dGbGdudk9zTjVJVGtTT1lTWDlITWMwMlIrYnpYQnhhY0RGRnBILzFvRkhHK1dZUW5CelVudkVLZkJGbHQ0MC81RVlvOUV0aEdvOEthY2RmeGVCaXdkY0R5MVRkbS93TnpIdXAzeVBMSzJrQnV3QVAxRVlNemtzMGdDZFA4YmhsbHFYV0NaY0NSZnFqU1pmaHlWclpNUk1JcmVEbVR0WFVrTEhrN01HZFh4ekJJK1k2WkVlS3lmK2NRYTc5SG1qYmhCM1hUL1MzNFlLd3JSTm1kNlBINytXSHBqTVMyVVFWbTBucWdXYUZXbC83Wm83WkE5cjJwOFFVRk83QXlrU3RZTFFKWnBlSE5VaWhaenI2SjZLMHFQZVNWcjRVUnFuQ1ZFQ29CMnNCd2R2eEEiLCJtYWMiOiJiYTZlZDdjZjU1NmE3ODUwOTIxMDI5MTI4ZWM1NDU2Y2UzZDZlMDM3Y2QzMDY0YTMxMzE0NDFmZjI2YjUyODkzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjhFNFFQRkdodnc5TVlDVXFyb2tvcnc9PSIsInZhbHVlIjoicWNrYmtjeGtBV2JoalZiVWhjNlBiYWVoSEpKcFVOdEdBeXd0SjY0ZWpFanZ6SFIzVVYvS21MYTNJb21QVDdQN1l1NVpMZ2h4YmRJTkJiMWNPcytSSDRQWHcrNDg3WHJ1NzhNTWtTWWRrUXU1aFBjTWRlZjhGeWdHUUYxOGtlWkJaS21Nc0psV1hseVd3ZEFOWGxhTDFYM1FmWG0zZWs1dVlwMk96ZEhCZkg2RUEyOUhzb1BsdE82TGNiL1ozcjQ4MmZSQlR0R3hjaUtzeE9kZU1UbmpZVTJtMk9PWnp3cmtlK3RVWUhSUUhzYnhTWUkvYlRBV2NybzBTajZucmFJZFo0YjJ3OE5YemJBdE5uTS9uMVBIMzlLdnB6Qk1jRi9BK1BzM2xmNUpUL05mVkR0SGhKbzF5dVNyNjh6VWoraEFwRDFTaEVUWXYzc3RMeXp3YlBramhQRHh6YS95bjRTMjEwaE9nc05XSDZaekxKc24wNnM3WHI3Q1I0QTRqazRPZnJLMFcyTFRtTjdIUVozb3gzQkU5WjdZbEJsRDRJd0JSbDhmTkM5UEVsdkZPa2NMWGRLZ1VJY0owTENGRWl2N3I2QXppODNObnF0SWRGMGtGSDl2T2pWalh5bEtxU01PV0RlbDRya2xYSVBwVmVnMHVRd0RaNmt5RkVJVnJTQTUiLCJtYWMiOiIyZDNkNzBmOTRlZjA4ZDQyODgxMDZlZWViZTI3MTkwMjViNzk1ZDVlMGU3Njg2Zjg1NTU0NTk5NzYyOTU1MmYzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlV1MDBtZTRoMmw3QjNpM0VBTFhwNHc9PSIsInZhbHVlIjoiSFcwTW9GMExsQ0hMZjFYRW91MVZvVFhua2FteGpNRDRrUTM5eVEyWStWUGpxMFVtTnkyRnVsY2RvUWYxM21ZaG5qSmtLR3ZVWHV0dlZVWW82UGlHVmVtM1BwMU9IcGZ2NmFQd0d6RlROem9EejZQV0ZNOGRsaXJvM1VPYXB3S0tOMXRuZEh3UWhqWFY1a2dsZmhwdGpZazFRYTVPaGN4OXBraFZ0TnNRbTdDRC9hUWtYSGFkdVFPV1BWRzJkbStCbTJVVERSZGJTeHdjM3ZhV2R6M0dGbGdudk9zTjVJVGtTT1lTWDlITWMwMlIrYnpYQnhhY0RGRnBILzFvRkhHK1dZUW5CelVudkVLZkJGbHQ0MC81RVlvOUV0aEdvOEthY2RmeGVCaXdkY0R5MVRkbS93TnpIdXAzeVBMSzJrQnV3QVAxRVlNemtzMGdDZFA4YmhsbHFYV0NaY0NSZnFqU1pmaHlWclpNUk1JcmVEbVR0WFVrTEhrN01HZFh4ekJJK1k2WkVlS3lmK2NRYTc5SG1qYmhCM1hUL1MzNFlLd3JSTm1kNlBINytXSHBqTVMyVVFWbTBucWdXYUZXbC83Wm83WkE5cjJwOFFVRk83QXlrU3RZTFFKWnBlSE5VaWhaenI2SjZLMHFQZVNWcjRVUnFuQ1ZFQ29CMnNCd2R2eEEiLCJtYWMiOiJiYTZlZDdjZjU1NmE3ODUwOTIxMDI5MTI4ZWM1NDU2Y2UzZDZlMDM3Y2QzMDY0YTMxMzE0NDFmZjI2YjUyODkzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjhFNFFQRkdodnc5TVlDVXFyb2tvcnc9PSIsInZhbHVlIjoicWNrYmtjeGtBV2JoalZiVWhjNlBiYWVoSEpKcFVOdEdBeXd0SjY0ZWpFanZ6SFIzVVYvS21MYTNJb21QVDdQN1l1NVpMZ2h4YmRJTkJiMWNPcytSSDRQWHcrNDg3WHJ1NzhNTWtTWWRrUXU1aFBjTWRlZjhGeWdHUUYxOGtlWkJaS21Nc0psV1hseVd3ZEFOWGxhTDFYM1FmWG0zZWs1dVlwMk96ZEhCZkg2RUEyOUhzb1BsdE82TGNiL1ozcjQ4MmZSQlR0R3hjaUtzeE9kZU1UbmpZVTJtMk9PWnp3cmtlK3RVWUhSUUhzYnhTWUkvYlRBV2NybzBTajZucmFJZFo0YjJ3OE5YemJBdE5uTS9uMVBIMzlLdnB6Qk1jRi9BK1BzM2xmNUpUL05mVkR0SGhKbzF5dVNyNjh6VWoraEFwRDFTaEVUWXYzc3RMeXp3YlBramhQRHh6YS95bjRTMjEwaE9nc05XSDZaekxKc24wNnM3WHI3Q1I0QTRqazRPZnJLMFcyTFRtTjdIUVozb3gzQkU5WjdZbEJsRDRJd0JSbDhmTkM5UEVsdkZPa2NMWGRLZ1VJY0owTENGRWl2N3I2QXppODNObnF0SWRGMGtGSDl2T2pWalh5bEtxU01PV0RlbDRya2xYSVBwVmVnMHVRd0RaNmt5RkVJVnJTQTUiLCJtYWMiOiIyZDNkNzBmOTRlZjA4ZDQyODgxMDZlZWViZTI3MTkwMjViNzk1ZDVlMGU3Njg2Zjg1NTU0NTk5NzYyOTU1MmYzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-786281213\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-581623862 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581623862\", {\"maxDepth\":0})</script>\n"}}