{"__meta": {"id": "Xecdb88aa8910cd8b14e636bf0bcd8c8f", "datetime": "2025-06-28 16:01:05", "utime": **********.635206, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.105151, "end": **********.635227, "duration": 0.5300760269165039, "duration_str": "530ms", "measures": [{"label": "Booting", "start": **********.105151, "relative_start": 0, "end": **********.570956, "relative_end": **********.570956, "duration": 0.4658050537109375, "duration_str": "466ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.570967, "relative_start": 0.46581602096557617, "end": **********.63523, "relative_end": 3.0994415283203125e-06, "duration": 0.06426310539245605, "duration_str": "64.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45544048, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013069999999999998, "accumulated_duration_str": "13.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.603968, "duration": 0.012199999999999999, "duration_str": "12.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.344}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.625504, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.344, "width_percent": 3.06}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.62778, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 96.404, "width_percent": 3.596}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-470449488 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-470449488\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1550790768 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1550790768\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1911783676 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1911783676\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1423208286 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126454152%7C5%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjQ3VHNlYllvK2FBc25zdnJWajJuQ0E9PSIsInZhbHVlIjoiOXFDaWF5UzRuZFNYWFZ1T0xoZHpQdHkyYmxnTE94ME1xNlpIeXh3R1VJRUNGOHFPdTlpWUtSNmRuK1IrRnBEWW9MZGFRUGlMU29LOXlsU3o1TklhZVVWbENCZmVpNkhKdFBsakFuN3dkWjVicG1XZ2g4L1hucXhoM0hlY3Z1RmxpMXVLZ1oxUkdOZWRGRHVVekFXcTJPeDEwZ0QrRStBQ0tpUVZqZWkxV3I1OEJ1VmljZmthNGU0OGFneFBTQWFUV0xMaUljcnBMZ08wa28xNWxDRWJjaHdNWFErQndlOG8yamVKSU5pREo2OHU5Vm9TUDZKOGZsOFJITWhVQkF1VXYyWkNZZEV2am9DaXNaa2pHTXFmeldWK0xHRDBaemF6WnZoSTBDZzMya2sxbEU1NDNRb0pPb0MybndzSWNMWkJsWUduMGhlVUxQSGxDeXoxSDh4SVQzSndwYkZabU90SjZCWjJocGE4aEdtKzNBNjhCektsN0dhSTEvWUMzb3lIZ0pUOTFGZmdnaUs5RmdnMDBjYmlOZmZzTFN0Qnc1RTNJZTRQQjJzWTNaNVlzWHh5L1BVeURJdU80L2pNYkx5TytHbkp6OUcxY1htaWI1bC95bFVqVHJacnBCQjd2TGJwT05UMVBXQkU5ZElRY1hLRGdBQ3N5SVhDb0NsL0pMMXEiLCJtYWMiOiJkMWY0OTViM2IyMmI1OWEwM2QxZmJkNzY1NDAxYzQyOTdkYjA3MzQ1ODBlODQ1OTBmMWRmMzA2ODY4YjA1NzkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlibVgxc0ZUdGNqeUgrUklONjVJaUE9PSIsInZhbHVlIjoiSHNJSy9pUzB5M3FMWEFrSFU1Q3V3YnlPRmNSRDJPR0hqaFJVU2dkL2hrRTBKU0gvR0xVWmN1T3NYZHlZWjE2OWtqYU55S1EyR09YK29xd1k4NjAvdFdrWkMzWTN5LzZodEtBa0Riamoybm8vMjUwZFFXSUNERGxPeUF4VEdQTzVXc0drWXBZME1lOTlLd3RnZ0k4M205Wjc0UkswUWZkWXNpbHpZQzhINWZrZXE5cDFoVlNleVhVM001N0oxZ0gxRTZlQndnSmVCOTlWMjRMaHhnQlVDUnNLT1RNNzJpSWxBTVBDZDFObXdQWS90QUlTV3pEY0VnSW5HYUdOYnMwamZ1YklqaTNhMWpWbkU1VC9HNlMydjBmS0pSbTR6djdvYmdmUTg5amptVUlVYk9aOFdZY2dDOXcyOExxeGpVQy9mUzl4OGNwK2xmOTliOTBlNmhJekFCZkpJZUJFNVQyQ2hDZGc4TUZIVmRKK2oyYVFXMnBBbTFyY21pWU1BYjBtdDAvakVMUFViYWEwTzlHK3N4Rmp2M0J6ZVg5ekQ4MDZHRXhVbzdORnZSamJqd3ZxRXp2b2oyYWkwK0hZbEsxWmhUU1VUbUR1U253Qk45OFFwRVlvSlZZK1VrMTdRNmVFTUhCaGlGYkxaUjljR2NvejRqU2k1anNHYU1jc21BanQiLCJtYWMiOiJhNTY3ZDI5NWZhNDZkZDlhNGNmZjEzNmIxN2I0ZTU3YWNjYzg3Zjk1YjJiODI2NzI5MWYxZGJjNmRhZWQyMDk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423208286\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-634903036 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634903036\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1863053947 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJCalV5RXJvVm8zT01HU3BLMXA3c0E9PSIsInZhbHVlIjoiRzc0T25KN054dE40T1pPeng2bFhtekVVMXE2VFFQdlBDdFhqTHlVMFNTT2ZoWnltNFh0OTdvckhxam9UK0RRY1lLNnc4ZlNqYWV6eDY5VzYycXVVeGdtTkhFdnlSaTJQcTlVWjV6bFBLRFdxUWt1bkFCSVFseEF4Z25tSWtSdk9vSys3SG45US9iNHFKZmY0UThFOHhTbmllSEk3ZHZHVjBWcnNXN2s5T0xTVlkyaTcxd2pMSkFLMEo2L0I3Qm5CckxQaG02bm1UQU5IWkNqcjM3ZjBrSkRmMUxTS1I1TlhjMk12MEpiQWo1NXFWcWhRSGR2WlNXU2FOTjZQTDdvTm0yUXBxVTJOaFRqU3RPMVNjcUFnSFYySXNvZHBYZlJTNUp0M0kzUGgvVzMrN2g2ZXBTYUVQMk1nQmRadnV6R29DRUM0VU9KREZGUjR4NUNxSjZOYjd3U1VnY1JMbkVET3dqdjlhUStIbUtaelFNWE5jdy9OWjlQOTYxZUwyTGFta2R4UTJac2R6bHNZSEFWblpkYXArK3ozNkFHcmlqS3k3UHFnbTcvMlBFV2NiNWlYT3VPYk1PWWwyU2l2TWVkZVViZ3AxcG1CK21TNGdIeE04aiszcCtsckVydVJsY3VKQWtIUWwreEM3aU00d2E5ZGZDV0YwcnhkMUFCREZ4VWoiLCJtYWMiOiIyZDI5ZTcxZjI0ODMyN2E0MjExZTU4Yjc4MWYzZDI2ZWVjY2QwZWRiZGNmM2JjNjM0MzZiNGVkYTJkNzljNjBiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlF1cm5MaVlvOGZQbUVFSnQxK0FablE9PSIsInZhbHVlIjoiOGVxb0x3d29rKzhsd3ZjZHpSRTBzWWtGM01SRFVOOGUzdWVobkl1dC8wUVBlWktKREJZWG9lUXZBcnFnNlRFVnBmcHZMMDVUeUlYWE1QaW9LdFI1dFZ4NTB2OFdXOGg4Rzk4NExIeGNXQlF4aE1DRGQ2TFFLekxCQWxpcFk4Z3FoclhuR2llaTluZTFqMFlZclNrdDVFRHpwWWZRUTVLazcvZG9nQ2dPajlITDl1K0NGd1VlUTBVUEl3YXZYejgwTkd2L2dFbW1tanZzUDZnSmNOWUhycEpNcFVHVkpuWmdEaHFZVTJkeFRxYVJZOEdVQnZlNEFsVmhqeHQ2NFZvWE9QWXhaZVZRSzFCVEdyY2czOG1EVmVUSTRCTERWaVMrVmtDOHl6UjhGZDYrUUI5VS9hTlY2Q0xvTittOFFOUWJtWUtKbFZqWEhDUVB1SkJmdmI1Ump3eUFLK3d0aDQ3bm8xZzQvMHRwS2Q0WklqS0F1MHpZeFVjdmVEbHdkKzU3cVJMdkJXaS9pNWJoY21UNGRlVmM4bEI3SE5UczRON3pPRWdiK0J2cHRMbmVmMnZrVTBJUWlIMGdmUWlQZlpneGFNa1lMM3dNOW5jMGl6Z2hmcmp1c0FoUkpTSk9leEE4VFhoRU9qWXZuczhhZXlpNUpXTUZMbnlSUVVGc0NCMTIiLCJtYWMiOiJkMzQxY2QyMmNlOTFhMzE3ZDczNWM2MDk4YWRkMWIxOGI3NjYzZGE0YmQ2YWYzNjVkMjg1MGJkOTkzNGM4ZDZkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJCalV5RXJvVm8zT01HU3BLMXA3c0E9PSIsInZhbHVlIjoiRzc0T25KN054dE40T1pPeng2bFhtekVVMXE2VFFQdlBDdFhqTHlVMFNTT2ZoWnltNFh0OTdvckhxam9UK0RRY1lLNnc4ZlNqYWV6eDY5VzYycXVVeGdtTkhFdnlSaTJQcTlVWjV6bFBLRFdxUWt1bkFCSVFseEF4Z25tSWtSdk9vSys3SG45US9iNHFKZmY0UThFOHhTbmllSEk3ZHZHVjBWcnNXN2s5T0xTVlkyaTcxd2pMSkFLMEo2L0I3Qm5CckxQaG02bm1UQU5IWkNqcjM3ZjBrSkRmMUxTS1I1TlhjMk12MEpiQWo1NXFWcWhRSGR2WlNXU2FOTjZQTDdvTm0yUXBxVTJOaFRqU3RPMVNjcUFnSFYySXNvZHBYZlJTNUp0M0kzUGgvVzMrN2g2ZXBTYUVQMk1nQmRadnV6R29DRUM0VU9KREZGUjR4NUNxSjZOYjd3U1VnY1JMbkVET3dqdjlhUStIbUtaelFNWE5jdy9OWjlQOTYxZUwyTGFta2R4UTJac2R6bHNZSEFWblpkYXArK3ozNkFHcmlqS3k3UHFnbTcvMlBFV2NiNWlYT3VPYk1PWWwyU2l2TWVkZVViZ3AxcG1CK21TNGdIeE04aiszcCtsckVydVJsY3VKQWtIUWwreEM3aU00d2E5ZGZDV0YwcnhkMUFCREZ4VWoiLCJtYWMiOiIyZDI5ZTcxZjI0ODMyN2E0MjExZTU4Yjc4MWYzZDI2ZWVjY2QwZWRiZGNmM2JjNjM0MzZiNGVkYTJkNzljNjBiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlF1cm5MaVlvOGZQbUVFSnQxK0FablE9PSIsInZhbHVlIjoiOGVxb0x3d29rKzhsd3ZjZHpSRTBzWWtGM01SRFVOOGUzdWVobkl1dC8wUVBlWktKREJZWG9lUXZBcnFnNlRFVnBmcHZMMDVUeUlYWE1QaW9LdFI1dFZ4NTB2OFdXOGg4Rzk4NExIeGNXQlF4aE1DRGQ2TFFLekxCQWxpcFk4Z3FoclhuR2llaTluZTFqMFlZclNrdDVFRHpwWWZRUTVLazcvZG9nQ2dPajlITDl1K0NGd1VlUTBVUEl3YXZYejgwTkd2L2dFbW1tanZzUDZnSmNOWUhycEpNcFVHVkpuWmdEaHFZVTJkeFRxYVJZOEdVQnZlNEFsVmhqeHQ2NFZvWE9QWXhaZVZRSzFCVEdyY2czOG1EVmVUSTRCTERWaVMrVmtDOHl6UjhGZDYrUUI5VS9hTlY2Q0xvTittOFFOUWJtWUtKbFZqWEhDUVB1SkJmdmI1Ump3eUFLK3d0aDQ3bm8xZzQvMHRwS2Q0WklqS0F1MHpZeFVjdmVEbHdkKzU3cVJMdkJXaS9pNWJoY21UNGRlVmM4bEI3SE5UczRON3pPRWdiK0J2cHRMbmVmMnZrVTBJUWlIMGdmUWlQZlpneGFNa1lMM3dNOW5jMGl6Z2hmcmp1c0FoUkpTSk9leEE4VFhoRU9qWXZuczhhZXlpNUpXTUZMbnlSUVVGc0NCMTIiLCJtYWMiOiJkMzQxY2QyMmNlOTFhMzE3ZDczNWM2MDk4YWRkMWIxOGI3NjYzZGE0YmQ2YWYzNjVkMjg1MGJkOTkzNGM4ZDZkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863053947\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1374130742 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374130742\", {\"maxDepth\":0})</script>\n"}}