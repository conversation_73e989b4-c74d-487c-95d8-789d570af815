<!DOCTYPE html>
<html>
<head>
    <title>اختبار عملية البيع والمخزون</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; }
        .test-form { background: #f9f9f9; padding: 15px; margin: 10px 0; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .btn-success { background: green; color: white; border: none; }
        .btn-warning { background: orange; color: white; border: none; }
        .btn-info { background: blue; color: white; border: none; }
        input, select { padding: 5px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🧪 اختبار عملية البيع وتأثيرها على المخزون</h1>
    
    <div class="test-section">
        <h2>🔧 اختبار تحديث المخزون يدوياً</h2>
        <div class="test-form">
            <h4>اختبار وظيفة warehouse_quantity:</h4>
            <form id="testInventoryForm">
                <label>المنتج:</label>
                <select id="productSelect" required>
                    <option value="">اختر منتج...</option>
                    @php
                        $products = \App\Models\ProductService::where('created_by', Auth::user()->creatorId())
                            ->where('type', 'product')
                            ->take(20)
                            ->get();
                    @endphp
                    @foreach($products as $product)
                        <option value="{{ $product->id }}">{{ $product->name }} ({{ $product->sku }})</option>
                    @endforeach
                </select>
                <br>
                
                <label>المستودع:</label>
                <select id="warehouseSelect" required>
                    @php
                        $warehouses = \App\Models\warehouse::where('created_by', Auth::user()->creatorId())->get();
                    @endphp
                    @foreach($warehouses as $warehouse)
                        <option value="{{ $warehouse->id }}" {{ $warehouse->id == Auth::user()->warehouse_id ? 'selected' : '' }}>
                            {{ $warehouse->name }}
                        </option>
                    @endforeach
                </select>
                <br>
                
                <label>الكمية:</label>
                <input type="number" id="quantityInput" value="1" min="1" required>
                <br>
                
                <label>نوع العملية:</label>
                <select id="operationType" required>
                    <option value="minus">خصم (minus)</option>
                    <option value="plus">إضافة (plus)</option>
                </select>
                <br>
                
                <button type="button" class="btn-info" onclick="testInventoryUpdate()">اختبار التحديث</button>
                <button type="button" class="btn-warning" onclick="getCurrentStock()">عرض المخزون الحالي</button>
            </form>
            
            <div id="testResults" style="margin-top: 20px;"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 محاكاة عملية بيع</h2>
        <div class="test-form">
            <h4>محاكاة بيع منتج واحد:</h4>
            <form id="simulateSaleForm">
                <label>المنتج:</label>
                <select id="saleProductSelect" required>
                    <option value="">اختر منتج...</option>
                    @foreach($products as $product)
                        <option value="{{ $product->id }}" data-price="{{ $product->sale_price }}">
                            {{ $product->name }} - {{ number_format($product->sale_price, 2) }} ريال
                        </option>
                    @endforeach
                </select>
                <br>
                
                <label>الكمية المباعة:</label>
                <input type="number" id="saleQuantity" value="1" min="1" required>
                <br>
                
                <button type="button" class="btn-success" onclick="simulateSale()">محاكاة البيع</button>
            </form>
            
            <div id="saleResults" style="margin-top: 20px;"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📈 تقرير المخزون المفصل</h2>
        <button type="button" class="btn-info" onclick="loadDetailedInventory()">تحديث التقرير</button>
        <div id="inventoryReport"></div>
    </div>
    
    <script>
        // اختبار تحديث المخزون
        function testInventoryUpdate() {
            const productId = document.getElementById('productSelect').value;
            const warehouseId = document.getElementById('warehouseSelect').value;
            const quantity = document.getElementById('quantityInput').value;
            const type = document.getElementById('operationType').value;
            
            if (!productId) {
                alert('يرجى اختيار منتج');
                return;
            }
            
            fetch('{{ route("test.inventory.update") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    product_id: productId,
                    warehouse_id: warehouseId,
                    quantity: parseInt(quantity),
                    type: type
                })
            })
            .then(response => response.json())
            .then(data => {
                let html = '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0;">';
                
                if (data.success) {
                    html += '<h5 class="success">✅ تم تحديث المخزون بنجاح</h5>';
                    html += '<p><strong>الكمية قبل التحديث:</strong> ' + data.before_quantity + '</p>';
                    html += '<p><strong>الكمية بعد التحديث:</strong> ' + data.after_quantity + '</p>';
                    html += '<p><strong>التغيير:</strong> ' + data.change + '</p>';
                    html += '<p><strong>نوع العملية:</strong> ' + data.operation + '</p>';
                } else {
                    html += '<h5 class="error">❌ خطأ في التحديث</h5>';
                    html += '<p>' + data.error + '</p>';
                }
                
                html += '</div>';
                document.getElementById('testResults').innerHTML = html;
            })
            .catch(error => {
                document.getElementById('testResults').innerHTML = 
                    '<div class="error">خطأ في الاتصال: ' + error + '</div>';
            });
        }
        
        // عرض المخزون الحالي
        function getCurrentStock() {
            const productId = document.getElementById('productSelect').value;
            const warehouseId = document.getElementById('warehouseSelect').value;
            
            if (!productId) {
                alert('يرجى اختيار منتج');
                return;
            }
            
            // هنا يمكن إضافة استدعاء AJAX لجلب المخزون الحالي
            alert('سيتم تطوير هذه الوظيفة لاحقاً');
        }
        
        // محاكاة عملية بيع
        function simulateSale() {
            const productId = document.getElementById('saleProductSelect').value;
            const quantity = document.getElementById('saleQuantity').value;
            
            if (!productId) {
                alert('يرجى اختيار منتج');
                return;
            }
            
            let html = '<div style="background: #e8f5e8; padding: 10px; margin: 10px 0;">';
            html += '<h5>🔄 محاكاة عملية البيع</h5>';
            html += '<p>سيتم خصم ' + quantity + ' وحدة من المنتج المحدد</p>';
            html += '<p><strong>ملاحظة:</strong> هذه محاكاة فقط - لم يتم تنفيذ البيع فعلياً</p>';
            html += '<p>لتنفيذ بيع حقيقي، استخدم نظام POS</p>';
            html += '</div>';
            
            document.getElementById('saleResults').innerHTML = html;
        }
        
        // تحميل تقرير المخزون المفصل
        function loadDetailedInventory() {
            let html = '<div style="background: #f0f8ff; padding: 10px; margin: 10px 0;">';
            html += '<h5>📊 تقرير المخزون</h5>';
            html += '<p>جاري تحميل البيانات...</p>';
            html += '</div>';
            
            document.getElementById('inventoryReport').innerHTML = html;
            
            // هنا يمكن إضافة استدعاء AJAX لجلب تقرير مفصل
            setTimeout(() => {
                html = '<div style="background: #f0f8ff; padding: 10px; margin: 10px 0;">';
                html += '<h5>📊 تقرير المخزون المفصل</h5>';
                html += '<p>لعرض التقرير المفصل، انتقل إلى: <a href="{{ route("test.inventory.impact") }}">صفحة اختبار تأثير المخزون</a></p>';
                html += '</div>';
                
                document.getElementById('inventoryReport').innerHTML = html;
            }, 1000);
        }
    </script>
    
    <hr>
    <h3>🔗 روابط مفيدة:</h3>
    <p><a href="{{ route('test.inventory.impact') }}">تقرير تأثير المخزون المفصل</a></p>
    <p><a href="{{ route('pos.index') }}">نظام POS العادي</a></p>
    <p><a href="{{ route('pos.enhanced.index') }}">نظام POS المحسن</a></p>
    <p><a href="{{ route('dashboard') }}">لوحة التحكم</a></p>
</body>
</html>
