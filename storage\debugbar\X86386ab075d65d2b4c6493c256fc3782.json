{"__meta": {"id": "X86386ab075d65d2b4c6493c256fc3782", "datetime": "2025-06-28 16:30:38", "utime": **********.106253, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128237.647239, "end": **********.10627, "duration": 0.4590311050415039, "duration_str": "459ms", "measures": [{"label": "Booting", "start": 1751128237.647239, "relative_start": 0, "end": **********.055735, "relative_end": **********.055735, "duration": 0.4084961414337158, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.055744, "relative_start": 0.4085049629211426, "end": **********.106272, "relative_end": 1.9073486328125e-06, "duration": 0.05052804946899414, "duration_str": "50.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45721584, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00222, "accumulated_duration_str": "2.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.082325, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.36}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.091699, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.36, "width_percent": 16.216}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.09688, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.577, "width_percent": 23.423}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1513712217 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1513712217\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1093508252 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1093508252\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1859659859 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859659859\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1083964259 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128229272%7C49%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imd0dy90UEZ1TnhQbi9zUHBVd1o1aWc9PSIsInZhbHVlIjoicDNvamFlWld0RmFVUDFKWjlwTThXVmRBak0xQXpBVE53eTJQYno2a0pMYmFjQ0ZjbUF4NE1rb1JaMjh6aEdEbmJwQlpHRDI0OTBRTVlhRkVhWVdVRmVQTmpKcVZyQnpFZ0laTm1sSmF0MUNnL0JYUmNBdWN3bXNadE9Ec3Q4RE9RcHFObmhQd0dLL1ZiV0hTeXVHYTA1bmlGZlFVS3QyRXZoTzhnTWRSK1lQOWFnZHBrY3Z6USsxWXhxZjYxK01EOVFnaGlqS1NwNFVYbnd0WFZKUzB1RytaRmg1RnhCVk00QnAzNVRjRml1MUlrWjlhY08yTVcxSXM2RHRMeG9hbzNFK3psYmpSbFlQcHovYW1wemh1VjJEcXk0d2JYdDhWWVVZTWlDTVE3cnJxcEdWZCt6cHBlNnp0QUhLRnZpenBlOXNZT2hoUmI3NXFoUWVxU0ppaTFKbUczaHJhdnM0SDllNXI2TnhNVzBqZHorREwrV2U4QTlsT2JLd1RXL0J2MDBNRWRqWGwzNGE2ODhQZzNiQTBkd3NZOTlJYVlPN05Sa3p3QlNnall3ZjVHSmo5RXB6WHFBSGE5c2lTOWgwcWVaS3hOTHk0dzhoUStaNzMvVjJDSUJWQ2E5WHloK01kUTZUa3d0eDhkT3ZaSGFrV1IvWnYwU0Y4cUpsdFo0NmciLCJtYWMiOiIxMGRiNTdlNWM1OTdlY2U0MWJjY2ZmMjBkNmY1ZjgyMGE2ZTEzMDg4NDkxMmVhZDc3OTRkOGIzZmI1NThjNjg3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjYwcWVYNE02eG5VYWVST0NITTdhNkE9PSIsInZhbHVlIjoiaThBTHJPVWVkdVRBOG9TTzI1SFYyWlFESy9ZSy9SellYVEdGY1ZrQ3Z3QTM0dXNWazRkTnVRRU5PbEJTenJCS1cva2Fya1ByUE1iQ1ZrcWd0QVlUZVZFS0JFYndaUWNmVldoS0hzN1psakZ2OHpqTTNVbmtmdU5ZSlpseFE1RGhFWk01MTA2WE8yNTR5YWozdW9NNEF0dXpSemd4QW1qWk12UlZVS0NBWHZycFlRU3JSUWlMR3hWTXdUUFlyMEFzM0RCaTlDdC9hdGFpOElqdTlOQkQyc3ZmUnJMVURUMVBBVTU0UFNZTkkwdkFBWGtoR3JDT0dWUmVPeGlKUVJuSklMTkgyRzM4NDlRLzU2SW1QQnErUTBybWVZOHhnc3laSUc3ZFhSMFd0TmVBbDZoWEFCaXJiZnRSOUhha1o5Z1g4RWoybWxLQXJQeXBUMVdraG9ZMkFKaVZKbmtIWlcrOFRDakU0VE9qVFVmcjVKWFlvZGZRdDZIV0F3QzFiL3BRamtiVHdmbDA5WkIxTG9xaStsY09JclVncVNIMUJWbTdPRXYzTDlObkNyNzFlSGZuRlo5azUxMHVsQW5aREgwbEQxZHk0N0xHUllmV3pjSkkyYmFJaURXdHJlUnE4YmhlUXZ0cGV5eXlpcTBTQUhJU1FST3VYTVlKTS8zaVlDRkkiLCJtYWMiOiIwYjFiYzU0YzJjM2M2ZDRiMzFlNDQwMzllYzQ3NmIzNDY2ZTU4MmQ0NWU2MDEwMTY1NTRjNTYxYWJmNTc4YzgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083964259\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-793164485 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-793164485\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-969871014 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlQNHR1S0hlZmZTaUloekgxQzgwVVE9PSIsInZhbHVlIjoiQTJ6c0RWYVRzclZWKy9JRDZxdmR4TXoyUmtlN05NL1VIbnhFWGNPSnNpcy9CNVNSc1RtdDhmdWYrdEpQN0IrOWdSMmc4WjdyZW9lNFZGc3UrVDA5TzNJTzhwNTZUcXpES2h0dmpFTEQyNjBBdVc5Rjh0UlgvclB4REpFUmJPd1RteC8xV1BqUEpNRS9iWEVwWXhtb0ZXdU5pcGU2S3A0MjhTMVFiYzU5aTJmRlRaWFVJR0QyNkJFOVdZdXV3MjNRczVYblROVlk0TnhmZUVBKzBYYWJ2TDhIZXNEdy9iL2w2Zkw5Qk9FdlByOFk0YjUvQ1hLY2Z5ai80eURDdVpuZ2h0UDFHaEF5Zk52WGt1djU4OEk5Zlk4UjBXTmJRaDlEczhlNGZSMUhNNU5mZjRSQzNWNGRRTExFWDhUZC9jMHB2aTVvbldzNWdWVDEyRGR0bmpjT1pGK3VqUTQzZUd3SkRlVXhzVHZ0b1lsaVNGN1JoVGVlM3dKeXB4bWxtUWpNd3lBVG0vU2l5TXk4b2U2Qk0wR2pnVXJuVk5lTzF3bEVVZ3FicHVLQ1oyNzJZMHBwYm5TT2JFZEFoNlRSaSs3MmJGMkl3Vk5BeUZRUExXTW5FWnZ6THIvQlBtaHhaNUs3aDYxbHlSMkNjZGRnNFJXWlJMYURWa1c2ZHRVeTJxN2wiLCJtYWMiOiI4NjZkZTQ4MjlhZTk1Yzk5MzE5NzlkYmMxMTI3MWQ2NjQyODUxYTZmMGM5ZWUwMWY1MzM4ODMzYmJmNWM4MTliIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InVObG05Umh6OFoyb0pSSUtSZVEzRWc9PSIsInZhbHVlIjoiL284SkNYUFQ5YVVjNDBIUndIdmNZWHNGbEpERHRHZ2RiU1RZZWVoOWhwNFhxdzVPS21xU21GSG9lemxpeFNDdTNJOW9CT2JaNFhkdkRSZ0x5aGVQUlBjSVdjL2FmTWdTOGRpKzFySXRra2E2aFlwNjYwc2pjTXJtb3J3S29TMmZ3QitwR3ZYOHRHVE00Q3BBVDhkY0hpbEFZUGErckx6Z3oxQnNMMVJsemZ0RlNaSlR6MURNM3kvZ09KeDdDencrSEhpR0t3dHNLVEFvZFBHNFJiNkdrMVE1Y1g0NGpOK2dIMnZEMnh5bURqOVlXVVM0QmxGcHdyN3VwUTlKeTU4dTB2SS9GVFFsZTBxRit2WlY4RGlOOXZqTzNxaHJnOXdqMGw2ci9xcHhRVnBzM0duUnYzSHdiNzdsR1daL1BJcVJweGdnMGk1a1k1cGJmWENKNE04TTAyYWZWYXZycU1ucTI3Z0xSazZ5dmxDdFZRaGtxK01xaitRNDdLVUozNUpIYjVoL3JlUHUwNjNOcTBtZzJJakV4S0xqd0lhWmJla2JuRG4rWnhxM01yMEZocWFzYzd6RC9LTUJJVmdPbjlNQXN6WWdRRm5mRXRMa3JkMHBEWlZyV21qYW5lN1Jqd25iYXdpd25WUTlVTm5hTGFHRXQxQUVtSHk4MUFuTElrcXkiLCJtYWMiOiIxM2M5NTI2ZGJhYzczZWRiZWMxNGQ2ZmE5MWQ0YTVhZTRjZjkwODUyNTYwYzQ3YjRmYmIwNjIxNjU0M2FkZTAwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlQNHR1S0hlZmZTaUloekgxQzgwVVE9PSIsInZhbHVlIjoiQTJ6c0RWYVRzclZWKy9JRDZxdmR4TXoyUmtlN05NL1VIbnhFWGNPSnNpcy9CNVNSc1RtdDhmdWYrdEpQN0IrOWdSMmc4WjdyZW9lNFZGc3UrVDA5TzNJTzhwNTZUcXpES2h0dmpFTEQyNjBBdVc5Rjh0UlgvclB4REpFUmJPd1RteC8xV1BqUEpNRS9iWEVwWXhtb0ZXdU5pcGU2S3A0MjhTMVFiYzU5aTJmRlRaWFVJR0QyNkJFOVdZdXV3MjNRczVYblROVlk0TnhmZUVBKzBYYWJ2TDhIZXNEdy9iL2w2Zkw5Qk9FdlByOFk0YjUvQ1hLY2Z5ai80eURDdVpuZ2h0UDFHaEF5Zk52WGt1djU4OEk5Zlk4UjBXTmJRaDlEczhlNGZSMUhNNU5mZjRSQzNWNGRRTExFWDhUZC9jMHB2aTVvbldzNWdWVDEyRGR0bmpjT1pGK3VqUTQzZUd3SkRlVXhzVHZ0b1lsaVNGN1JoVGVlM3dKeXB4bWxtUWpNd3lBVG0vU2l5TXk4b2U2Qk0wR2pnVXJuVk5lTzF3bEVVZ3FicHVLQ1oyNzJZMHBwYm5TT2JFZEFoNlRSaSs3MmJGMkl3Vk5BeUZRUExXTW5FWnZ6THIvQlBtaHhaNUs3aDYxbHlSMkNjZGRnNFJXWlJMYURWa1c2ZHRVeTJxN2wiLCJtYWMiOiI4NjZkZTQ4MjlhZTk1Yzk5MzE5NzlkYmMxMTI3MWQ2NjQyODUxYTZmMGM5ZWUwMWY1MzM4ODMzYmJmNWM4MTliIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InVObG05Umh6OFoyb0pSSUtSZVEzRWc9PSIsInZhbHVlIjoiL284SkNYUFQ5YVVjNDBIUndIdmNZWHNGbEpERHRHZ2RiU1RZZWVoOWhwNFhxdzVPS21xU21GSG9lemxpeFNDdTNJOW9CT2JaNFhkdkRSZ0x5aGVQUlBjSVdjL2FmTWdTOGRpKzFySXRra2E2aFlwNjYwc2pjTXJtb3J3S29TMmZ3QitwR3ZYOHRHVE00Q3BBVDhkY0hpbEFZUGErckx6Z3oxQnNMMVJsemZ0RlNaSlR6MURNM3kvZ09KeDdDencrSEhpR0t3dHNLVEFvZFBHNFJiNkdrMVE1Y1g0NGpOK2dIMnZEMnh5bURqOVlXVVM0QmxGcHdyN3VwUTlKeTU4dTB2SS9GVFFsZTBxRit2WlY4RGlOOXZqTzNxaHJnOXdqMGw2ci9xcHhRVnBzM0duUnYzSHdiNzdsR1daL1BJcVJweGdnMGk1a1k1cGJmWENKNE04TTAyYWZWYXZycU1ucTI3Z0xSazZ5dmxDdFZRaGtxK01xaitRNDdLVUozNUpIYjVoL3JlUHUwNjNOcTBtZzJJakV4S0xqd0lhWmJla2JuRG4rWnhxM01yMEZocWFzYzd6RC9LTUJJVmdPbjlNQXN6WWdRRm5mRXRMa3JkMHBEWlZyV21qYW5lN1Jqd25iYXdpd25WUTlVTm5hTGFHRXQxQUVtSHk4MUFuTElrcXkiLCJtYWMiOiIxM2M5NTI2ZGJhYzczZWRiZWMxNGQ2ZmE5MWQ0YTVhZTRjZjkwODUyNTYwYzQ3YjRmYmIwNjIxNjU0M2FkZTAwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969871014\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1079097538 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1079097538\", {\"maxDepth\":0})</script>\n"}}