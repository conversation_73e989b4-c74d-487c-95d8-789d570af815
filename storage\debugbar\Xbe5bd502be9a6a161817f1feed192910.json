{"__meta": {"id": "Xbe5bd502be9a6a161817f1feed192910", "datetime": "2025-06-28 11:23:47", "utime": **********.665621, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.291238, "end": **********.665636, "duration": 0.37439799308776855, "duration_str": "374ms", "measures": [{"label": "Booting", "start": **********.291238, "relative_start": 0, "end": **********.619347, "relative_end": **********.619347, "duration": 0.3281090259552002, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.619356, "relative_start": 0.32811784744262695, "end": **********.665638, "relative_end": 1.9073486328125e-06, "duration": 0.046282052993774414, "duration_str": "46.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45439112, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00257, "accumulated_duration_str": "2.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6481571, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.984}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.65747, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.984, "width_percent": 16.342}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.65957, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 88.327, "width_percent": 11.673}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1235582815 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1235582815\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1853655117 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1853655117\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-449564202 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-449564202\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1810128171 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IlRoVTgzMGxGTVE4M0EwWWRFL29wemc9PSIsInZhbHVlIjoiRTVITmVydkxGZmcrN3Q3YWpwWnl3b01XY3pPc3VuZEZBSTNQOUZCU2Q2THMvZlpHQmJ0ZnMzcDZ3cVF0Y1I0ZU1Ja3p5bWJMQzRNbWdNUVpmL1NOa3IxbnhiQVdlUFRoSEtoZHo2amVZTXJpcUY0UUU3QjhKVGk1cjZLQ29KL1pSbUxhNmVLQTVuOVVFYXJZeXRpUGdmODVpakM4WWszZ1FWWHlPWklyaVBlNDVoNGpKdWl5M3QrdTZOTlBrMUZ1M3RKVUtyYk9JYWd5ZUg5Q2lsajRlSk5JdExRRWNEc2xrc3YzTEtibm9kQkpraFhpUVhNeE5zczBVVFU0WGNwblYxYjlza0lQODQ1MlY5a2dOZnV3dmJWOGhaVzN6bE9hMlFtTW1rd2dzMHJZTWxkc2RYeFFBL1pKQkJJaGFXK3E0MUR0dTRpbUREZDFBWHNrbUJFV28vbWhpTXdQKzhEMzFWaUZCWWdPcmdjMkh3bDM1QjBYcEcxM1BRbTh3ZkRLR2IvczB3QWhNdXJkamliM2g4VndmV2VVVUVMVW1LMXBSTG9mTk5VRC9TRWk1amZkMUFSSlBDT3ZUcGlzdTBlK29icGh2bmlhMkpKYkZDRENGTzlkaHdJbTdpVGRmZXFsQ3d2cC8rUXNZaE9YT0srTzVDeXRqbCt0ZXlDeXd2MEciLCJtYWMiOiIwMmE1ODEwYTkyNGQ4YWI1NjU2ZmRmMjEwODYwZDg1OTMzMTc1ZDNlNGRmNGM4M2UyMmI2Yzc3OTlkNjJlYzdjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlCUjlWRjhydWt6YXRBT05pQUJDRGc9PSIsInZhbHVlIjoiTGZqMDNoNW5sRlBIWmpuNlRnQ0Ura0xCbzB5T0tFdGFiS2VEQUVucUlTTDllS09xUlBqTTVtR2tkOU5FRmhFTm0yQ2FUaXRuTWJuSW1DSXdUZjYxdm51bTZub1BIbEYrbVpWQkpjdDNVME1HVzR0OTVYaUl2ZUJsTm1nLzVIdlpEWVBxVWFjaDdKazRnbXhCejA1bGVWUU5ZcHBSQnVmNTROL2I5cXJvN3BZenprMjdDMXpxREliZWNWNmtrc09Ud0EvVlRlenlHUG1ST3piT0V5cXl0RGhOaHlLR2Q1cVQ3dnpDTkJZQ3pXc3hYQWpmWTVYTzU3RU9zUVVraER2R2M1MGNCZTJNVkRjSXh3YW45SFJHZGFqd0lIZXRxVlFsMFdMejFzWld4Qmc1L0swK3lSMGJSVXdzaVhscDFvRUF1d3hMbVZKeWVMamZyd2NybWF5UitqRm01UjVuNmV5Y2xpMVZjWFkzMjlXQzV6RkoyenF6aDVzVExZRlRNN0lSbFlTdUhycDhYM09TLzBtcEV5dVg4N25xbEJVWjZxSlovM2VPZklKTXpUWEpBd2hyckczM01rb1lkVUVPSkwxY0o3b29NYkRoNmtMMWtBUmFqVnBXZzRENkhQTGNIcmEyeU5TRG9VVmlVNm91Ky80WWZEdGhiNHcvc2RYZkI2QjMiLCJtYWMiOiJjMTk0ZWE4ZmQyMzJmNzQ2OWI2MThiYjkxNjJlZTBkMGNlMWQyNGYzMTBmMjI3NTkyZjQ1ODZiMGEyY2ExMjk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810128171\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2132239639 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132239639\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1132270812 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVjcER3ZE94Y1loZ3UrNnJ6VDdndnc9PSIsInZhbHVlIjoiMTVHMElmTnZGZWYxY01qNDRoTWpha3BvZnFTSkQxN1Z1eE1abG1vcGI3SUFocllUWWVVeU1YVDBvMmhGdVV6TU03SW91c2l1ams3ODdpL3c5cTFiMTZkMlJtdi9hVXlWcEgzL24zQW5OckdXVi9sSE40V3Z2eG81a0dPb2lpUWxrTWEyaThvOHdXV2dKeU9OenZOT096U1p2TThJVFM1MVBVY2UzeUlJRGR0VnJjMzl0WjNUMG1DSTJVSCtHRHBDRlNjc2dxMjFsZis3ekVUZzhuOTVJM0dFallRZ2o0YzB2cGt6K1B1aHJ2Nk1VV2d0VlUrckR2RG81WXR5ZXBVMHAxMHYyS21uRlBwY1BzSFlBNjM0NjFXSytDNnh3QU5CRnFyYnpLUUlpMUdpMVpVS2tNSWxONkNnSXlaZ0hjRTRwOHl3WFZaNk5mMHhZQWlzQ0o0YkMvUkZjd1BGQ2VxMG0xUzFNd2tKcmNUSHUwTUlsaDEzalFqVW5xdS9vbE91bHJ3SHB3MExqQmlaRVd2N0lGMnlLMTZ6bEV0RzExeTh5SFJTNnFjRGlqK0xqc2hZN1l4bTVwblRQNlRiRUFmQ0ZKd3VuK3pBK2VmV3R1N3lyYThBZFNKdXJFcVZQS0UzSjRtTVpHY2tZOVB6d1RHb2NaTUFlMURycldLTUx2NEEiLCJtYWMiOiIwMDMwZmQwODcxYjFlMjFlZDRjNTRiMGI5NTk0Y2EyNDBhMzNiM2Q2YzY5ZGE5ZTYxODlhN2MzMTg1NzgyN2JjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9lNHVzWDcrd0F1SnJMZlZhcUlEbUE9PSIsInZhbHVlIjoiTFRMUFJ1TGdNTzZzcVN0K0VabUVNVEt2MThtVDlDUVpLbmNObEdzV0YwaDIzSXBzeUVKcS8wcWc1dGlUVllWa2ZkYVJ1NjBleG10WmtPc1ROKy85VTd0dmx6T3VqRUZ1UTNoYjR0bEdwUk1oeEI0NHYvczVDQWRnaUs5THc3cEJ0OXBsdStabVY1Vm40SFJCaDRCZk1DMUtqa1A3d0xxYlJQVHg2aVlNaGw1cng3Vm9HRzNEaWVmeGJCQ2hHNzhiRjdUTTQxRW1rZ0ZFN0trM2dsd25iZ0xZZE0wdnY5VXlYVVVNMHBpMXQrTzRabDBnUlpxMk1FT3c4eStwdFEvNmErNmNEZWtJNW16elJHOVpLYTF3VzZzWXlTa29xd3REK0RPc3VGZkxhWWVDUlQ4S2ZBNG5jWDZVazFKd1l4QzBiaUpNU1M2amV5WUpzaGJmckFCdUdhYTkvdGtxMDZzbElUTkVFL3VialMxVVg0RWpBejRHSUhZdWx6WWVlc1pwWnprcmx1N0JlSDNpb3cwSjU1ajY0d2FlSmpyR3FWZEtzMVh6YkVrdzhLejRTbysxR1cwM29IU2pNRHhPMEQrM2Z0SWpMSEY1MUdOanFLZkd4VDR3S0JxeVNJRlZyK0JrSHlGV0QzMG43Z000cGJjWnFtaU5iUnFTQk1zV1NsVzYiLCJtYWMiOiI2MjZiZjUwYWIxMGYyZTcwNjJiYWQyNDJiNGMyMjQxNzVjN2Q1MTZlYmJmNGQxZGEyNjBlMTAxOGQ0ZDFiZjI2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVjcER3ZE94Y1loZ3UrNnJ6VDdndnc9PSIsInZhbHVlIjoiMTVHMElmTnZGZWYxY01qNDRoTWpha3BvZnFTSkQxN1Z1eE1abG1vcGI3SUFocllUWWVVeU1YVDBvMmhGdVV6TU03SW91c2l1ams3ODdpL3c5cTFiMTZkMlJtdi9hVXlWcEgzL24zQW5OckdXVi9sSE40V3Z2eG81a0dPb2lpUWxrTWEyaThvOHdXV2dKeU9OenZOT096U1p2TThJVFM1MVBVY2UzeUlJRGR0VnJjMzl0WjNUMG1DSTJVSCtHRHBDRlNjc2dxMjFsZis3ekVUZzhuOTVJM0dFallRZ2o0YzB2cGt6K1B1aHJ2Nk1VV2d0VlUrckR2RG81WXR5ZXBVMHAxMHYyS21uRlBwY1BzSFlBNjM0NjFXSytDNnh3QU5CRnFyYnpLUUlpMUdpMVpVS2tNSWxONkNnSXlaZ0hjRTRwOHl3WFZaNk5mMHhZQWlzQ0o0YkMvUkZjd1BGQ2VxMG0xUzFNd2tKcmNUSHUwTUlsaDEzalFqVW5xdS9vbE91bHJ3SHB3MExqQmlaRVd2N0lGMnlLMTZ6bEV0RzExeTh5SFJTNnFjRGlqK0xqc2hZN1l4bTVwblRQNlRiRUFmQ0ZKd3VuK3pBK2VmV3R1N3lyYThBZFNKdXJFcVZQS0UzSjRtTVpHY2tZOVB6d1RHb2NaTUFlMURycldLTUx2NEEiLCJtYWMiOiIwMDMwZmQwODcxYjFlMjFlZDRjNTRiMGI5NTk0Y2EyNDBhMzNiM2Q2YzY5ZGE5ZTYxODlhN2MzMTg1NzgyN2JjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9lNHVzWDcrd0F1SnJMZlZhcUlEbUE9PSIsInZhbHVlIjoiTFRMUFJ1TGdNTzZzcVN0K0VabUVNVEt2MThtVDlDUVpLbmNObEdzV0YwaDIzSXBzeUVKcS8wcWc1dGlUVllWa2ZkYVJ1NjBleG10WmtPc1ROKy85VTd0dmx6T3VqRUZ1UTNoYjR0bEdwUk1oeEI0NHYvczVDQWRnaUs5THc3cEJ0OXBsdStabVY1Vm40SFJCaDRCZk1DMUtqa1A3d0xxYlJQVHg2aVlNaGw1cng3Vm9HRzNEaWVmeGJCQ2hHNzhiRjdUTTQxRW1rZ0ZFN0trM2dsd25iZ0xZZE0wdnY5VXlYVVVNMHBpMXQrTzRabDBnUlpxMk1FT3c4eStwdFEvNmErNmNEZWtJNW16elJHOVpLYTF3VzZzWXlTa29xd3REK0RPc3VGZkxhWWVDUlQ4S2ZBNG5jWDZVazFKd1l4QzBiaUpNU1M2amV5WUpzaGJmckFCdUdhYTkvdGtxMDZzbElUTkVFL3VialMxVVg0RWpBejRHSUhZdWx6WWVlc1pwWnprcmx1N0JlSDNpb3cwSjU1ajY0d2FlSmpyR3FWZEtzMVh6YkVrdzhLejRTbysxR1cwM29IU2pNRHhPMEQrM2Z0SWpMSEY1MUdOanFLZkd4VDR3S0JxeVNJRlZyK0JrSHlGV0QzMG43Z000cGJjWnFtaU5iUnFTQk1zV1NsVzYiLCJtYWMiOiI2MjZiZjUwYWIxMGYyZTcwNjJiYWQyNDJiNGMyMjQxNzVjN2Q1MTZlYmJmNGQxZGEyNjBlMTAxOGQ0ZDFiZjI2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132270812\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}