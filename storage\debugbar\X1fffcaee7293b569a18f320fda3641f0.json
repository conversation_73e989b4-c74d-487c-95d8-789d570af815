{"__meta": {"id": "X1fffcaee7293b569a18f320fda3641f0", "datetime": "2025-06-28 16:03:36", "utime": **********.090632, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126615.629445, "end": **********.090648, "duration": 0.46120285987854004, "duration_str": "461ms", "measures": [{"label": "Booting", "start": 1751126615.629445, "relative_start": 0, "end": **********.011154, "relative_end": **********.011154, "duration": 0.38170886039733887, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.011162, "relative_start": 0.3817169666290283, "end": **********.090649, "relative_end": 9.5367431640625e-07, "duration": 0.07948684692382812, "duration_str": "79.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45535720, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02707, "accumulated_duration_str": "27.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.044523, "duration": 0.02578, "duration_str": "25.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.235}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0807128, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.235, "width_percent": 2.253}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.08387, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.488, "width_percent": 2.512}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1562001475 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1562001475\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-880182292 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-880182292\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1210214879 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210214879\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126613193%7C18%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNBblRWN3RSalVBaEgrV0RWbHROU2c9PSIsInZhbHVlIjoiTzAzaTJreXZNNkZjVmdzL2k1Ykl0RVNjSmlNL2FSMGUreUhMY3NFRjh2SVBoNHFLVFFsVVhmaXpiM0R0UWxWODJQV1gvNE1YaG41aUVjMVlrd0Nvc2FrZHhVRzNnSStwbXNvYmhIQ0tGUUdESjZRQXg2M2J0Q05LN1RiWktmU0YveitEK2VkZWhBN0kwNzVtQzZlZGVlTlM3MEpOYXpua2xEUGVKWEFkaURoaVBva2xLRllNMWNXTnRtNWxSMjJTYUxJMzlpMTgzcW01NXRacjU3ODM4ajhtaGYzUGhKOUxhV3JTM1Voc2RoSzk4ZTBJRGlsMjM2SmpDZFhtVWtoUy9rYVBEQmhldkNzbnhzeEdMTjVuUWJKdDl5OU93Lzd2V1B2RDdLV3BtMUl0UVNyRVUxemViWmJWQ3NRRnNocmMzVGRSN0VRTUZRZXVEN0dKSlpTVUkveDVhSC83cXdOZU5RRlNFS1VWdDNCNU0zdVVyZlhiR29ZVjFGNkZJR3RhOGFpck4rTlkveE1WamN2VUhBVjdQdUMvWVJhRXJzVVIvVlVrYm1zRHRFMEV5bEwzc3pVUjByRm1jeDd0V2Rpb2lhKzN1ZVFveGtRbXc2ZmpVOFMxaXd0S1RxT1dFK1JuWVE4UjUyRTBPamhOaE4vM3lFVzduWThMOUF0eGM3TVIiLCJtYWMiOiJkOWNiMzA5NjhjYmQyZTliYTZlZTg5ZjZhZDFjZDE3MTE0YzMyNjc5ZmVhNjE2ZWI2ZGU1NzNmMjFlNzdmMjFmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpvNUlKSUhXdG5YdUJRZ2pYcGFzdnc9PSIsInZhbHVlIjoiN2ErYzdua3ZOWkpzSGlyd1lQKyt3RFZhbmZsTDRUVng4Z0tUeENuOWJzbUx5SlpvaEJTbm9xN1N6ZkFtT2pSMUFjNlp5VW0zdGIyVUNrZW55SnExNjl3N2dyU1EzOXZMcHZkb3RUZFRJbytkSUhROVg1bmRYQ1hpVjlLRjJVeDVRTkZPZDk5bzRCSExHRVVyWEVsbktWbDlvcnZYcm5uYzYzcVdDUVhPaXdsL3FkQkRpdXZFSU1UOHptT3lKemF0aVEyVEhtRW9YZmZoRm04bmN6dFpXak0zTmVramlZWjdMNUg4Rk4rZCtjcDcvUklBR0VlN2JkN2hTdkNNTHdJZDFBUWhySXJrY3F5Nk9JU2R1TnRsQTAybWVJV1BiVWhBbXdRRkltMVN4V2taSEVSWU5jM3ZranhSOTJ1NkdReUhvMDV5U21xQ05WUkQyaU83bkZ4WkZpRGI0QlRPbHhpRFpMaEs3VVVocXBhQ09tNXplYXVwSlpPU0Y3QTNmNytQbFpEUEh2aE5DTDdTbGZRWmlMQk9Ja3lwR3g2WXAzWndha0N2VnExVmFEU2t5a3dsdHA2YWZSSHRyVlc1eFcyV1hpdTZaMGVZV1diY0JtWnFvSERFNG5teW5FN1Q4M3RDank5alcxSEZFcUVtaW1YUHNKQkRXOUZzeGtUb1UzQkMiLCJtYWMiOiJhOGU3Yzk5YTBiZmQ3ODI3NDkzZGYxN2E3MzlkMjkzYzAyMzVkYTUzNGZjOWZhMzQxYzEyZDc5ZDJlODVjN2UzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-757310895 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757310895\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-37997458 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhFTzNPb0FtcjhFK2VOVjl3aDBweUE9PSIsInZhbHVlIjoiayt5cFBQYWtnVnlneHY1cUE0SVBhcTk4UVpGVy9TTkozNmVCMlVFSm1lcHRvWXIzMVVHZk1CellPQ1RoUXh1YWFGSkNERW1wcytsNFNGY1VqeWNHaG8rSXF0Qjh5Sjluc0lCM1UweGczR3dCS0g1MzAwd1ZRSitYMGFpTTNpWStSTVZicDN5cTAyT2J1TlFGVlRMaXlVOXNCOTZMT25qTzB2MEVSN2wxUlNOVWRBczFEbUt2UnlJMEIxRk5FenZHa2NpMFNBelJub24wMGpEN0ZCSmhIamprbW8xYll1dEFIeHIxeG1wVTdEMkcxdUwybHZpT1YvdEp4cmg5M1BCV2ZTN3VzZVk3cnh6QkV5WVRhSWhFZTZDNy9UNzF1NTZKK3U1Q2dBa1B1OFhPYmJsZ1lpakp4YkxROVRSeUpDZHZjSXJyUlJBMjJOaHo2Y09kTWtIZ0lYaG1jdTBhVUJzakRoK0p2VG92bThqZC9zMzluZlpZK3dlL0twdzd6NjUyZ0pmS0V4S2hLdnZkaUxBdEc3QS85SWE2Ti9uNXA5SWlkTWxVZ0Qxc2UwNVMwcFRNWjJBbmFrRko2RHVjcWVBcEV0NS9vdnJwa0VXOGNCT041Ny9YNDhwMkNjWWthMFJrTG85N0owYUdvaHp2d1VGMys1TXl4NVVGVDUyR1F6eGMiLCJtYWMiOiI2NTcxZTdiMTQ3ZmNiMGRjNDMyOGNmYWE3YTNmNDlmMTc2MDMzMGVkM2FjMGRjNzdmNzk2NzczNTA2Yzk5ZTI0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlQ2L3Y0cEdHbHNWV1lhejlKUHo5Rmc9PSIsInZhbHVlIjoidW43cWlrNjNxbTdrOWdXK0RMS2xkMEFBK2VoclZ1Z3V5SDduRXV1ZWE0Z2JsK0VWWk8wdTlZWmZaZFE0Mm9HakhpK3daNGtTVDZ0SDA0MlZTbTgyU0dYZ3ZuOWlWWVRnVC9tc1pwTk4yZ21oOHdIVVBrdFhuR3ZwS3BKOGVlUHZQckFtVUF3WFZVZUhSLzNjdlZRRXF3eFBSWUVlSC9iUVhVOHk4NzZuMmxBRFFUN1FSNElNOU5uVnZsWVo1Z0JtZnhFakxzdEt0cHBOVkV5RkI4TDMrZFpqSFZWajM3T0VGejhHUUlXUldZdmhWdmVxcTBBVlIwWkpsRDIvZ3U1aGFpUFFNd05tNk9qVU1OMmtjRXg4T09NQnVNbStiMGZaWGZHYzAxR3cvdTR6NE83Q0JleDYvRGE5N2QwaHhrcm9OSzhJVllQc1F2b2I3a1FkVWR3QS9OTEhOUE9UYWROWmFzNDBzaHlZMkQ2VGJ6Zmx2SGdHdi81bVN4dXFhREMrTGxOTzA0YS9XRXdnRzFTZnY2ei84UUZHR0xMd0s4R0lkMTRUbWxRelZZVEorWVg3bUpVNzZJdUxmdHBLYWViRHl5VUtxRVY0RmJNVEtTY3VqbWVJVXBZY01EdGNUcy94SWV3WnFUVmd6ZUg2OHNqd21kbmdrSDYxTjlsRmxpTXIiLCJtYWMiOiI4MzgwMTlhMzU0ODU1ODI0M2I4NjI3N2Q5MmI1MjVhM2FmMWY2ZDBiNzRlNGQ2MTA0ODI0MGE2M2VhZDliNjc2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhFTzNPb0FtcjhFK2VOVjl3aDBweUE9PSIsInZhbHVlIjoiayt5cFBQYWtnVnlneHY1cUE0SVBhcTk4UVpGVy9TTkozNmVCMlVFSm1lcHRvWXIzMVVHZk1CellPQ1RoUXh1YWFGSkNERW1wcytsNFNGY1VqeWNHaG8rSXF0Qjh5Sjluc0lCM1UweGczR3dCS0g1MzAwd1ZRSitYMGFpTTNpWStSTVZicDN5cTAyT2J1TlFGVlRMaXlVOXNCOTZMT25qTzB2MEVSN2wxUlNOVWRBczFEbUt2UnlJMEIxRk5FenZHa2NpMFNBelJub24wMGpEN0ZCSmhIamprbW8xYll1dEFIeHIxeG1wVTdEMkcxdUwybHZpT1YvdEp4cmg5M1BCV2ZTN3VzZVk3cnh6QkV5WVRhSWhFZTZDNy9UNzF1NTZKK3U1Q2dBa1B1OFhPYmJsZ1lpakp4YkxROVRSeUpDZHZjSXJyUlJBMjJOaHo2Y09kTWtIZ0lYaG1jdTBhVUJzakRoK0p2VG92bThqZC9zMzluZlpZK3dlL0twdzd6NjUyZ0pmS0V4S2hLdnZkaUxBdEc3QS85SWE2Ti9uNXA5SWlkTWxVZ0Qxc2UwNVMwcFRNWjJBbmFrRko2RHVjcWVBcEV0NS9vdnJwa0VXOGNCT041Ny9YNDhwMkNjWWthMFJrTG85N0owYUdvaHp2d1VGMys1TXl4NVVGVDUyR1F6eGMiLCJtYWMiOiI2NTcxZTdiMTQ3ZmNiMGRjNDMyOGNmYWE3YTNmNDlmMTc2MDMzMGVkM2FjMGRjNzdmNzk2NzczNTA2Yzk5ZTI0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlQ2L3Y0cEdHbHNWV1lhejlKUHo5Rmc9PSIsInZhbHVlIjoidW43cWlrNjNxbTdrOWdXK0RMS2xkMEFBK2VoclZ1Z3V5SDduRXV1ZWE0Z2JsK0VWWk8wdTlZWmZaZFE0Mm9HakhpK3daNGtTVDZ0SDA0MlZTbTgyU0dYZ3ZuOWlWWVRnVC9tc1pwTk4yZ21oOHdIVVBrdFhuR3ZwS3BKOGVlUHZQckFtVUF3WFZVZUhSLzNjdlZRRXF3eFBSWUVlSC9iUVhVOHk4NzZuMmxBRFFUN1FSNElNOU5uVnZsWVo1Z0JtZnhFakxzdEt0cHBOVkV5RkI4TDMrZFpqSFZWajM3T0VGejhHUUlXUldZdmhWdmVxcTBBVlIwWkpsRDIvZ3U1aGFpUFFNd05tNk9qVU1OMmtjRXg4T09NQnVNbStiMGZaWGZHYzAxR3cvdTR6NE83Q0JleDYvRGE5N2QwaHhrcm9OSzhJVllQc1F2b2I3a1FkVWR3QS9OTEhOUE9UYWROWmFzNDBzaHlZMkQ2VGJ6Zmx2SGdHdi81bVN4dXFhREMrTGxOTzA0YS9XRXdnRzFTZnY2ei84UUZHR0xMd0s4R0lkMTRUbWxRelZZVEorWVg3bUpVNzZJdUxmdHBLYWViRHl5VUtxRVY0RmJNVEtTY3VqbWVJVXBZY01EdGNUcy94SWV3WnFUVmd6ZUg2OHNqd21kbmdrSDYxTjlsRmxpTXIiLCJtYWMiOiI4MzgwMTlhMzU0ODU1ODI0M2I4NjI3N2Q5MmI1MjVhM2FmMWY2ZDBiNzRlNGQ2MTA0ODI0MGE2M2VhZDliNjc2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37997458\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1038059783 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038059783\", {\"maxDepth\":0})</script>\n"}}