{"__meta": {"id": "X809ec372a6eb56db85b64b0495966d55", "datetime": "2025-06-28 00:36:50", "utime": **********.407609, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751071009.986077, "end": **********.407626, "duration": 0.42154884338378906, "duration_str": "422ms", "measures": [{"label": "Booting", "start": 1751071009.986077, "relative_start": 0, "end": **********.330911, "relative_end": **********.330911, "duration": 0.3448338508605957, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.330923, "relative_start": 0.3448460102081299, "end": **********.40763, "relative_end": 4.0531158447265625e-06, "duration": 0.0767068862915039, "duration_str": "76.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45615688, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02281, "accumulated_duration_str": "22.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.358195, "duration": 0.02155, "duration_str": "21.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.476}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.390096, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.476, "width_percent": 2.543}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.395997, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.019, "width_percent": 2.981}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IkRXc05ObTdrZ0E1ZkpPL0RySjBOanc9PSIsInZhbHVlIjoibFM1c0RYci9Yc3lqQ0VINW12V2RLZz09IiwibWFjIjoiYjUyYjNlYzYwYmU5MjNkOWQ0Mzg5MzgxNmE0YmY2YzMwM2Q5Nzk0ZjQyZWQ5OTg3MmRkYmVkMjU2OWVmNzk3NSIsInRhZyI6IiJ9\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-902415199 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-902415199\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1625896025 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1625896025\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-505686038 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505686038\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-502952715 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkRXc05ObTdrZ0E1ZkpPL0RySjBOanc9PSIsInZhbHVlIjoibFM1c0RYci9Yc3lqQ0VINW12V2RLZz09IiwibWFjIjoiYjUyYjNlYzYwYmU5MjNkOWQ0Mzg5MzgxNmE0YmY2YzMwM2Q5Nzk0ZjQyZWQ5OTg3MmRkYmVkMjU2OWVmNzk3NSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2817 characters\">laravel_session=eyJpdiI6IjE5YmxhMUw3bXJQWWh3R0habDVwcFE9PSIsInZhbHVlIjoiTkZUZHViY3FpMFMvWUgzVm1OV0JwUHZoMVU1WjA5ck5UbEh4NjBRemY0ejlwWVNadXBvOWxTWVFjQzJMNGxNSTc5NzJwa1dxQ2hqL2VlQUZvMUs4YnV3Z2RhaExoU0p1RE5pdlhQQ1dzN0RpS0xnTWxrcmEvVFZUZERCSnFDQlhEdG03UkROakJlb2huQ24xSWlPcTA0QnpwdnNZaEl5T2JnWkpNMHNSVGpUeGZpWHJNcDBBWVFmaHE4Z0dkYmFlTm5VRWR4YWlaOHNZa2hPaDh2RGdndHB2MVJLZEFZMUU4Nmttek5WOEhIOExCS0p2S3h1Mm83MFRQc1U4NHMzN2lsY1J6ZnNLdVBhOXVCbktKdkNNdUQrcGJHZm9sUDkyRWhHVW90U0twWWw3Q3d1VzlyYzgxWjY2U2MxWWdnOXhaUEJkK0k5dFU1dWEvaDNXNmZtMkY2WUJKdVZSNnlFYVF0Y0JtL3Fac0tRZ3dyWDFwVXNSdmNhNzIyVjBiSHpQbDM1OXBiL1R6WndGT25IMnFYV3ZBNkdsNjB1SU54NXRIUWJBdTM1eDBiditjVzlWcW1DQlVTMkgzSmw3S0xYdDJmRVI4MXNEaENKc1dTbFRMR1pPWUVZc1crMkY4QnNxMDl2eEpvclhUb3hhMjZqYnFqaGVGTEdOWWxSZzBXNUUiLCJtYWMiOiJhMmVhNTI4OTM2NDBkYzJlNTU0MzRmNWUxYTg5YWQ0OGIxNGMyMmZhYzM5MmYzYzllZGEyNWMxZTVjYzdhNWUyIiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=zkpa4v%7C2%7Cfx5%7C0%7C2004; _clsk=9vvwq3%7C1751071005797%7C29%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5LM2E3ZldscTdNR2w2YW5lRFovd1E9PSIsInZhbHVlIjoiZTFsZ1hCWXhqZ1NTM011ZmcyY1d4a3lqS2x0UmtoVitaOUZFNkY3U3FtblgzOW1zS2NoYzNlKzBpSXhnOVY5MHhXOWc3cEM1RW4vQkFCSTgxZzBmNGpSSnJ3Vm9DQklvdGwyZ2pINUtvVlk5VXRNMVpEcm1sSjNscDFxZDM4YzVkWW83U29tdHpnci9YVG0yNHREdVl3ZmE4UEN6YXFLbFNHdDRVcnpzMml4SnZDWVpuWExUWDcwUmFoRExET3kzSVlvc2t0WFNUdFM2VGhFTndDN1VrbkV3OHBwVE9PRjRvWkM4UXhoVms1OCtPWDlHaDhMVXJUbzRNdmRkMFY3TkNtZDY5a0ZIMHJ0RnNSTXIvZ3FCbkZLaWk4MHdWd2tWeDlTYy9KcDFKSWlsWUJucHlHaWtRcW8yVXVxU3BwUGRENThNV2t5Y2hxL1FWUlh5b0VoNHFQb0ltd3B4TFNMUXZLRFF0VWt0cWdOY0ZJNUtKeDE3cFZjT2V2Wlk5OTNhUyt4bU5IL3lEWnNBTkp0N1JTMVUzc3I5QWI3TWtyMno5Tk41TFhTeW9nVGxOMjgvOFBkQklqNU9Da2ZTbHdRdVF0R2lpdFJNSVA3eXErdjdMNnhWZTZVamRueHgxY0tCTUJEdWxTc29lYy9vRjFic1FUWmM4WTlUbDFwcEhBWTgiLCJtYWMiOiJjYmQxNTU2MzI0Y2M1OGM2OWQ5OWVhMmE1YTJlYTI2ZjY5Y2Q3OTMyMzVkY2RlZTAzZTY5YWMwZDZkODgwZDJlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjA5RDhzcWJRZHZFNlR4ek13ODAxNFE9PSIsInZhbHVlIjoiV2Z5VGJYcytZMTB1eG9FTndlNHZ3U2dPVkNaOFZDRnVzWFcvRUl4MTNqTzJtOGJzclFvUWdKQ1NlV2QraVIrT0IzajYxVHVFZ1RyMHU3QkdSMUtoRFNXcHR3N0dIbnlyNUozRityWi9iMXBTWXdkK2MvcHg3aG5TR0Rlck5SUUdFTkRWaFV0S211V0QxeURQa1RKUzR4cURTQjdNVmdmOUdISW1UVXA0aFpsL3p5VzlweUdBcWdPUFBobmlLbU5DdUYybzJxSTd6a3hMSE9QZFRSTXdmUU56ekJYWEZjMXZZQ3dVdU5HT1BUYU0yUWwzYzRwUzZNVWNqTVNibXpBeWxhdVpQQml4QWFTZGRFL2lwaXZqcytIV0Q4QVVFOHdhazR6bWNiOUhKWWp5YXVaZE9GUU1uWVhoQ3IzMVQzM1B6OTIwY1pPUmhaS3VOZEdjUW1uRWR3elUzUDJ1WU5lV0FZSkh6NER0ZEtMb1p3WlVFL0o3MWR1cDl5Sk5Wa2RzRGNLelFXSFgrN2lHVmtka1dCdzJzcWFiTEVHeUF3bmI5S05USE4wMUcwdCtaZ29ibjJ4UWtNeVFaem1zN3RLbTR3L1F5R1JGRkdXMXMyNmhrQTk0LzExM2dydFBLcVAvUHRMS01la2xEREM2ZFFZS0NVTEI3K0hWSUN2Vm5pU3AiLCJtYWMiOiJkNmJkZTA4NDJjYzVmMTkzOTJlMGRmMjQ3Y2ExN2U2NGZhZTIzYjA2YzllMWM2ZTc5ZDMwMGIyMWQ1YzBjMjEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502952715\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1687024674 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">chEs2sQpmg2fYpUuqAOS1ZcIPLmVIQdx921QTw47</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9XNo0PIFsccsEXzvfQLAUsci7MaweQoaRcTvbnBl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687024674\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-855475143 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 00:36:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZzaG1KellESlYrVUp1RzFCNnA0YVE9PSIsInZhbHVlIjoiRE03NHl2K1JlVjROZFBYZmdvVDYybTJTVU4yamNZbzhhVG1xNnhibFhHL3d6aVFNZDdVeFVramExVUxBRW5xQ3dhYTlDRC9HcXZSSFRXRUtpM0tzaFpmNGMrUm9QTThmK1Y5ZUNKcFVobTlqZlRjb3ZOaGJ4OXJ2MDcwVlU4Z1JpbHdCU2FKSEtpWVVDVHRvVXI0Ym0vQXdSbTRSM2hxbW10am0walFjK0EzVzVnWDcxNUNvdGtoKytaVGEwYThNclczMDhEcjlERE0vamhWV0tDVkJYWkdpMldrZXZGeUx0Sk9Hc1hNMzlobE5GakpNMmxJTHY1dVhXczNiZXM3MEY1Tmh2a3ZMcm5yVjJuR0crOUJoenY3RTYyNmRTcjBhVFNOVkl3YzhVdzBsSkNNenA0bkhrbDhSbjNMQmU0MDhZOEMxcDdzNlVNb21QdUF6TE1kbGlwVzZQWXhkSlJ2K29HcURxcWlCQ1ZFVWVoNDRYVy9Tc211UWlFTCs1cDVMamFLMHdQUzl4WkxnNG9zKzN5b1hHbXhuVE9KdDVvc3gxMGVVRnhPL095ZUE0bkZ6QW5MTFpHL3Q5VXNWekZ6TkFKbUVLTlAzam91MGtLNzFBWlB0aE9DUG1hK0c2OUdxWkJSdHd6Y2YzSUdQaEwyY1RqK1N5Yjc4WjRYb2JicCsiLCJtYWMiOiJlMGRjNjI1NjU0YTg2MmI3OTllMTlhZTBmOTllOGFmNDFlMTEwYmUzNzg5MTEwNDY2NTVkNjhjNzhlZTY1MmI1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:36:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxwSTZGSjNXZ3A3RjFYeXMxVGRta2c9PSIsInZhbHVlIjoiY3U2U0NJQW9KUkVXeXpBN0V4SFlnelJxV3lnOHNkdTIzcENRSzhPMVhaMkV4UTRJOEtwMGJOL01Mc3RZa1Byd1YwbkZoQVMweVVvMGF3bGtDQWVNSGdYRElJakNuN1FaMEgvdEtoZ3RIWFA4c3lFQTJLQVlubXJobElBWUVRNU9DS3ZybkkrWG5HblYyeGxERTJuZThKK1FPYWdqcWVnU1dCbEI3T2RIRUZQZ3ZnbXUvYmpEcGNkajhhQWdXWDJuTjdTRk9QcnduZ3hkZ0kvSnp6bjgxV045d3pQQ2Q2M3I1azdGaGpmUjZjYVJtMHpNcHpucGU4QkxVMDQ3NWpoSlhQVVdMcTZPdHlvZGdRTlBqcVIydUVmT0NMZ1RtL2hDQVphTXRrMTJScEFKZXcraUdmd2FMSEc3aWlCSVVYci9TOEd3MXlzVDUwNUZKNFdEVzN4WjRvNFhURTlucDdKM3dOa1BBUmlyd0xFQnpxUnEybmF6cVlPSFd3Q3hXd1M4UW4rY1FsR09CR0FyVUxtK0F6MzJlQ3o0dC9kaWVrQ3RsajVCL1ErQzN3aFBJdjhYbXROdzZmRnE5ZmJQbVlmOU5HTExvamd6TWJ4K0NoU2VJVHpjMFR2cSt5eVBacDJJem50TVNNMlYzWmRVbmhMUU9ieXBaRGNoUUNvRkZ5WHoiLCJtYWMiOiI4NmVmMzcwZTc1YjI5MWJhYzQ2NTNlYmZhMTQ1NjJkOGYzZGM0YzY2YjM5OTc5ZTNiOWY2M2VlZWVhZDlkNTgxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:36:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZzaG1KellESlYrVUp1RzFCNnA0YVE9PSIsInZhbHVlIjoiRE03NHl2K1JlVjROZFBYZmdvVDYybTJTVU4yamNZbzhhVG1xNnhibFhHL3d6aVFNZDdVeFVramExVUxBRW5xQ3dhYTlDRC9HcXZSSFRXRUtpM0tzaFpmNGMrUm9QTThmK1Y5ZUNKcFVobTlqZlRjb3ZOaGJ4OXJ2MDcwVlU4Z1JpbHdCU2FKSEtpWVVDVHRvVXI0Ym0vQXdSbTRSM2hxbW10am0walFjK0EzVzVnWDcxNUNvdGtoKytaVGEwYThNclczMDhEcjlERE0vamhWV0tDVkJYWkdpMldrZXZGeUx0Sk9Hc1hNMzlobE5GakpNMmxJTHY1dVhXczNiZXM3MEY1Tmh2a3ZMcm5yVjJuR0crOUJoenY3RTYyNmRTcjBhVFNOVkl3YzhVdzBsSkNNenA0bkhrbDhSbjNMQmU0MDhZOEMxcDdzNlVNb21QdUF6TE1kbGlwVzZQWXhkSlJ2K29HcURxcWlCQ1ZFVWVoNDRYVy9Tc211UWlFTCs1cDVMamFLMHdQUzl4WkxnNG9zKzN5b1hHbXhuVE9KdDVvc3gxMGVVRnhPL095ZUE0bkZ6QW5MTFpHL3Q5VXNWekZ6TkFKbUVLTlAzam91MGtLNzFBWlB0aE9DUG1hK0c2OUdxWkJSdHd6Y2YzSUdQaEwyY1RqK1N5Yjc4WjRYb2JicCsiLCJtYWMiOiJlMGRjNjI1NjU0YTg2MmI3OTllMTlhZTBmOTllOGFmNDFlMTEwYmUzNzg5MTEwNDY2NTVkNjhjNzhlZTY1MmI1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:36:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxwSTZGSjNXZ3A3RjFYeXMxVGRta2c9PSIsInZhbHVlIjoiY3U2U0NJQW9KUkVXeXpBN0V4SFlnelJxV3lnOHNkdTIzcENRSzhPMVhaMkV4UTRJOEtwMGJOL01Mc3RZa1Byd1YwbkZoQVMweVVvMGF3bGtDQWVNSGdYRElJakNuN1FaMEgvdEtoZ3RIWFA4c3lFQTJLQVlubXJobElBWUVRNU9DS3ZybkkrWG5HblYyeGxERTJuZThKK1FPYWdqcWVnU1dCbEI3T2RIRUZQZ3ZnbXUvYmpEcGNkajhhQWdXWDJuTjdTRk9QcnduZ3hkZ0kvSnp6bjgxV045d3pQQ2Q2M3I1azdGaGpmUjZjYVJtMHpNcHpucGU4QkxVMDQ3NWpoSlhQVVdMcTZPdHlvZGdRTlBqcVIydUVmT0NMZ1RtL2hDQVphTXRrMTJScEFKZXcraUdmd2FMSEc3aWlCSVVYci9TOEd3MXlzVDUwNUZKNFdEVzN4WjRvNFhURTlucDdKM3dOa1BBUmlyd0xFQnpxUnEybmF6cVlPSFd3Q3hXd1M4UW4rY1FsR09CR0FyVUxtK0F6MzJlQ3o0dC9kaWVrQ3RsajVCL1ErQzN3aFBJdjhYbXROdzZmRnE5ZmJQbVlmOU5HTExvamd6TWJ4K0NoU2VJVHpjMFR2cSt5eVBacDJJem50TVNNMlYzWmRVbmhMUU9ieXBaRGNoUUNvRkZ5WHoiLCJtYWMiOiI4NmVmMzcwZTc1YjI5MWJhYzQ2NTNlYmZhMTQ1NjJkOGYzZGM0YzY2YjM5OTc5ZTNiOWY2M2VlZWVhZDlkNTgxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:36:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855475143\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-601490869 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkRXc05ObTdrZ0E1ZkpPL0RySjBOanc9PSIsInZhbHVlIjoibFM1c0RYci9Yc3lqQ0VINW12V2RLZz09IiwibWFjIjoiYjUyYjNlYzYwYmU5MjNkOWQ0Mzg5MzgxNmE0YmY2YzMwM2Q5Nzk0ZjQyZWQ5OTg3MmRkYmVkMjU2OWVmNzk3NSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-601490869\", {\"maxDepth\":0})</script>\n"}}