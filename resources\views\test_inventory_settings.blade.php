<!DOCTYPE html>
<html>
<head>
    <title>اختبار إعدادات المخزون الجديدة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; }
        .highlight { background-color: #ffffcc; }
        .negative { background-color: #ffeeee; }
        .positive { background-color: #eeffee; }
    </style>
</head>
<body>
    <h1>🧪 اختبار إعدادات المخزون الجديدة</h1>
    
    <div class="test-section">
        <h2>📋 ملخص التعديلات المطبقة</h2>
        <div style="background: #f0f8ff; padding: 15px;">
            <h4>✅ التعديلات المنجزة:</h4>
            <ul>
                <li><strong>السماح بالكميات السالبة:</strong> تم إزالة <code>max(0, quantity)</code> من وظيفة warehouse_quantity</li>
                <li><strong>منع خصم المخزون:</strong> تم تعطيل تحديث المخزون في جميع أنواع POS</li>
                <li><strong>الاحتفاظ بتقارير المخزون:</strong> لا تزال تقارير الحركة تعمل للتتبع</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 حالة المخزون الحالية (بما في ذلك الكميات السالبة)</h2>
        @php
            $warehouseProducts = \App\Models\WarehouseProduct::with(['product', 'warehouse'])
                ->where('warehouse_id', Auth::user()->warehouse_id ?? 1)
                ->get();
        @endphp
        
        @if($warehouseProducts->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>اسم المنتج</th>
                        <th>SKU</th>
                        <th>المستودع</th>
                        <th>الكمية</th>
                        <th>حالة المخزون</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($warehouseProducts as $wp)
                        <tr class="{{ $wp->quantity < 0 ? 'negative' : ($wp->quantity > 0 ? 'positive' : 'warning') }}">
                            <td>{{ $wp->product ? $wp->product->name : 'منتج محذوف' }}</td>
                            <td>{{ $wp->product ? $wp->product->sku : '-' }}</td>
                            <td>{{ $wp->warehouse ? $wp->warehouse->name : 'مستودع محذوف' }}</td>
                            <td>
                                <strong>{{ number_format($wp->quantity, 2) }}</strong>
                            </td>
                            <td>
                                @if($wp->quantity < 0)
                                    <span class="error">🔴 سالب</span>
                                @elseif($wp->quantity == 0)
                                    <span class="warning">⚪ صفر</span>
                                @else
                                    <span class="success">🟢 موجب</span>
                                @endif
                            </td>
                            <td>
                                @if($wp->quantity < 0)
                                    <span class="info">✅ مسموح بالكميات السالبة</span>
                                @elseif($wp->quantity == 0)
                                    <span class="warning">⚠️ نفد المخزون</span>
                                @else
                                    <span class="success">✅ متوفر</span>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="warning">
                <p>⚠️ لا توجد منتجات في المستودع الحالي</p>
            </div>
        @endif
    </div>
    
    <div class="test-section">
        <h2>🔍 اختبار عدم تأثير البيع على المخزون</h2>
        @php
            // جلب آخر 5 عمليات بيع
            $recentSales = \App\Models\Pos::with(['items.product', 'warehouse'])
                ->where('created_by', Auth::user()->creatorId())
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        @endphp
        
        <div style="background: #f9f9f9; padding: 15px; margin: 10px 0;">
            <h4>📝 توضيح الاختبار:</h4>
            <p>سنقارن كميات المخزون قبل وبعد آخر عمليات البيع للتأكد من عدم تأثر المخزون.</p>
        </div>
        
        @if($recentSales->count() > 0)
            @foreach($recentSales as $sale)
                <div style="margin: 20px 0; padding: 15px; border: 1px solid #ccc;">
                    <h4>فاتورة رقم: {{ $sale->pos_id }} - {{ $sale->pos_date }}</h4>
                    <p><strong>المستودع:</strong> {{ $sale->warehouse ? $sale->warehouse->name : 'غير محدد' }}</p>
                    
                    @if($sale->items->count() > 0)
                        <table style="width: 100%; margin-top: 10px;">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية المباعة</th>
                                    <th>الكمية الحالية في المستودع</th>
                                    <th>تأثير البيع على المخزون</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($sale->items as $item)
                                    @php
                                        $currentStock = null;
                                        if($item->product_id > 0) {
                                            $currentStock = \App\Models\WarehouseProduct::where('warehouse_id', $sale->warehouse_id)
                                                ->where('product_id', $item->product_id)
                                                ->first();
                                        }
                                    @endphp
                                    <tr>
                                        <td>
                                            @if($item->product_id > 0 && $item->product)
                                                {{ $item->product->name }}
                                            @elseif($item->description)
                                                <span class="info">{{ $item->description }} (يدوي)</span>
                                            @else
                                                <span class="error">منتج غير معروف</span>
                                            @endif
                                        </td>
                                        <td>{{ $item->quantity }}</td>
                                        <td>
                                            @if($currentStock)
                                                {{ number_format($currentStock->quantity, 2) }}
                                                @if($currentStock->quantity < 0)
                                                    <span class="error">(سالب)</span>
                                                @endif
                                            @elseif($item->product_id > 0)
                                                <span class="error">غير موجود في المستودع</span>
                                            @else
                                                <span class="info">منتج يدوي</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($item->product_id > 0)
                                                <span class="success">✅ لم يتم خصم المخزون</span>
                                                <br><small class="info">المخزون لم يتأثر بالبيع</small>
                                            @else
                                                <span class="info">ℹ️ منتج يدوي - لا يؤثر على المخزون</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <p class="warning">⚠️ لا توجد منتجات في هذه الفاتورة</p>
                    @endif
                </div>
            @endforeach
        @else
            <div class="info">
                <p>ℹ️ لا توجد عمليات بيع حديثة للاختبار</p>
            </div>
        @endif
    </div>
    
    <div class="test-section">
        <h2>📋 تقارير المخزون (للتتبع فقط)</h2>
        @php
            $stockReports = \App\Models\StockReport::with('product')
                ->where('created_by', Auth::user()->creatorId())
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get();
        @endphp
        
        <div style="background: #fff3cd; padding: 15px; margin: 10px 0;">
            <h4>📝 ملاحظة مهمة:</h4>
            <p>تقارير المخزون لا تزال تعمل للتتبع والمراجعة، لكنها لا تؤثر على الكميات الفعلية في المستودع.</p>
        </div>
        
        @if($stockReports->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>المنتج</th>
                        <th>النوع</th>
                        <th>الكمية</th>
                        <th>الوصف</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($stockReports as $report)
                        <tr>
                            <td>{{ $report->created_at->format('Y-m-d H:i') }}</td>
                            <td>{{ $report->product ? $report->product->name : 'منتج محذوف' }}</td>
                            <td>
                                <span class="{{ in_array($report->type, ['pos', 'enhanced_pos', 'sale']) ? 'error' : 'success' }}">
                                    {{ $report->type }}
                                </span>
                            </td>
                            <td>{{ $report->quantity }}</td>
                            <td>{{ $report->description ?? '-' }}</td>
                            <td>
                                <span class="info">📊 للتتبع فقط</span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="info">
                <p>ℹ️ لا توجد تقارير حركة مخزون</p>
            </div>
        @endif
    </div>
    
    <div class="test-section">
        <h2>🧪 اختبار الكميات السالبة</h2>
        <div style="background: #f0f8ff; padding: 15px;">
            <h4>اختبار وظيفة warehouse_quantity مع الكميات السالبة:</h4>
            <p><strong>قبل التعديل:</strong> <code>$product->quantity = max(0, $pro_quantity - $quantity);</code></p>
            <p><strong>بعد التعديل:</strong> <code>$product->quantity = $pro_quantity - $quantity;</code></p>
            
            <div class="success">
                <p>✅ <strong>النتيجة:</strong> الآن يمكن للمخزون أن يصبح سالباً</p>
            </div>
        </div>
    </div>
    
    <hr>
    <h3>🔗 روابط مفيدة:</h3>
    <p><a href="{{ route('test.sale.process') }}">اختبار عملية البيع</a></p>
    <p><a href="{{ route('test.inventory.impact') }}">تقرير تأثير المخزون</a></p>
    <p><a href="{{ route('pos.index') }}">نظام POS العادي</a></p>
    <p><a href="{{ route('pos.enhanced.index') }}">نظام POS المحسن</a></p>
    <p><a href="{{ route('dashboard') }}">لوحة التحكم</a></p>
</body>
</html>
