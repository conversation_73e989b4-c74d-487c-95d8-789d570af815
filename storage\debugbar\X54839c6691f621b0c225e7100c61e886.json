{"__meta": {"id": "X54839c6691f621b0c225e7100c61e886", "datetime": "2025-06-28 15:44:51", "utime": **********.316783, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125490.934129, "end": **********.316794, "duration": 0.38266491889953613, "duration_str": "383ms", "measures": [{"label": "Booting", "start": 1751125490.934129, "relative_start": 0, "end": **********.251508, "relative_end": **********.251508, "duration": 0.3173789978027344, "duration_str": "317ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.251517, "relative_start": 0.31738805770874023, "end": **********.316795, "relative_end": 1.1920928955078125e-06, "duration": 0.0652780532836914, "duration_str": "65.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45819624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01754, "accumulated_duration_str": "17.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.283186, "duration": 0.01465, "duration_str": "14.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.523}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.30584, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.523, "width_percent": 2.68}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%Manual products are not allowed in Enhanced%' or `sku` LIKE '%Manual products are not allowed in Enhanced%') limit 10", "type": "query", "params": [], "bindings": ["15", "%Manual products are not allowed in Enhanced%", "%Manual products are not allowed in Enhanced%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3083591, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 86.203, "width_percent": 13.797}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-633757400 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-633757400\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1356482887 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1356482887\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1337566193 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Manual products are not allowed in Enhanced</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337566193\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-412587424 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">64</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125486476%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBrTmNTU1VVM3lFbTJQSXdTNHF5MWc9PSIsInZhbHVlIjoiUC8wMEEvdjJpK0VlenVHVnRkZXBpN0QyaW8yalJmcDlMOTh0K1g1NEUrZkVBME9wakgxbWFya1BhU2krQXVTZzVhZjRmYUtqVCtOSEt4U3NyUm11SXdIc0NHbm5TTzdOUFNZMUVXNlJ3K3dWa01hdXFBR1FTVlViS2lOU1p0Ykt2MzJ1Sm1hWjkxNGNxR3ljK2ZhY290UnBUQ1NDYnZ6V1Q2WHJWdjBaMVhKN3dWYXFORG5lOUtMaUR5VUpMK0hhd3hzOU1FNFd3Zld3Z1dpcmxOZ1p0b3ZzZVpacjJVbGhteDk2aUVMenJ0MGZRWkxHTlZPM1VUQTJBSFJ2RHFqNkNsTHZSNTBrOFJYWjJnTE8zQytmZ2dWbkhRcTdhY1VUUGxpVTVKblNnNzF6ZzBYT3doTXVFSFhxWnlvcTMwSGN6Zjh3b0lzMXRteWVrWHhLZmgwT1d0VGFoVjZoUFJueHhKelNjVDAvQVVHMjUwWGRaVndYYnZmMmcwZEhDMDVFampva1hDWXI3NTFtNWpIbThrcVdBeXVid21mQ2RNNU14YWg3eWU4VWtKcDcvbElvNERBMW5sVW1nbXllNkxNbWNsdjF6SjE0a3NLRUFZK2NiWlY2V0lKb2FqSGNyNXczZkZCeHczbDJiSlZjM09LM0hudXVWdU41YStmd3k0T0siLCJtYWMiOiJmOTRkOTgwN2U0ZTI5NTY0NDRhMWJiYmQyNTg1MWJkYTI2ZTNmNTkwZTliYTk0Y2VmMmVhZDA2ZWQ1ZTc1YzBiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkFnbGkweURoUVNubGlDaTFkenZxOWc9PSIsInZhbHVlIjoiK0ZDYzJjdlRkRlZMblU3VkV1T2k0cFY1MElVQjdHVmhFd3RnVVpuUmt6aGRlbmlMdTBCZEs4NWRLRVEwN0FROTRySDFzOXA1cllhRUZveEM2eXZFd29FSTN3V2ZRdlFZUUZXN3VOa25tNGFpbFpBVHdMVTZ4a1AvR29ENkpZMy9NaHdvZzNQVXczdlgxTnhKZlJuQmlUQ1pSTTdGNmxrLzh3TDR4eWh3VFBrVVJpYWMwYXRTbEhXM25vem1kNmNZbWlYQ3pGUmNtRDk4WUlDZ2RkWFFiY1QvVUZyZDlKUzN6a2cxYTNmM1ZRaUVrc1VqNUY2azlMQkx0SUl5NlUyUjB2dkNidHBKTWJQdE9zT2tnd0dFMXN1ZnJwWjJEaUI2elZqZFFrQVRGWDNKekVqTkl6bkxaT3pYdG1CMVBSU1JBZzgwL05rMjBqMzErN29Da0VDb28vV1RTazZCeWdEcTlxZVRYVXNMZE9nanJRR2kyUTBZU2JkQTBDVkFEdC8vbEdKQ0lKZVBCTFJvR2g4WWlTUTY1ZmpWZ1dabmRPMXZIcHZ2R1JwZ1J1QnJPY2NlMlhwY1k2MWdTWXNYQVkrZXhhTnlPRnNid1lYTmpGV3JmbUsveG5yYjV3ejZpYXdyMXlPaitucktIbnIwNlR1M0RJc1cwRWVGUGRwamU2NkIiLCJtYWMiOiJhM2E0NTVmYTVkMGM4YWJmM2NiZTk5OWI3OTRkZjdiZTVhZDA0ZTliNThiNDc1ZTQ1MjY1MDcwODQwZWI3NDk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412587424\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1060609732 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1060609732\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-804675795 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:44:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRLbXVHU2tndFZnRzJTZmk1cWVvelE9PSIsInZhbHVlIjoiZmlkUiszQlRJVnFvZXpaN3pISjYrZ3F0Sm5GdnhhVnRZakx6aE5LZUsrUFZHVHluaDdaU1dlbFBsWjhPc1JHOXF5akxPRjEzWHdQZU1jczBIcTVkZmFJeFJLeHJJbEUvSUNraGFkMWc1c0hEWU9COXdldUoyTTMvTjduRUwxTWlsY0hYaVJiZ05kVGNtL0xFek42OFRXVlNlTjNtaGJPNk9pdzVXVGdEOGg0YmZKYlllRm1FZkhqeVoweUwzTzJnQm5Yb2MxSlMyWi9LdWx0R3RiaFNsQVlwaVE0MVcveGd2UHk3QUp3UWdWN0QrVGRwRGJaOThuS0toZE5TYWRucEplKzBSS1NqZnNHTXVEcjh5dEVYTmhxRWZVQ01NNmx1ckUzUFdSMnJ0SExORzNRQm50di91bS80aGl4K3psRmFqWUU4UTZ5Zy81cldtMlE0VzAzcVdiWStObzJNbFdOWUp2QmFWTU9QazhvdjZGbW5iN0dBM0Jka3pSNFJVRjRSd0l3SWJsb2R1WWw0UUgxcmxTQU9pVUgyWWNDdjVXVWlVaHkyL3ByVGsvTDB4ck5FSDRHemxUSFljRzR2NUp4ZUZYUGZleHhiWjI0YzNXbEMwc3dyQ1dOTWJrT2hWZ0VjS0VwMVFhcjJmWTlyeS91WnFlYVBidU1ocjMxUWNHR2oiLCJtYWMiOiIzMDdjYTg0MjU0N2NhMGE1ZmY1Mzk3NWY3YzVmZDJkN2E3NzlhNDdlZGM5YjhkN2I2ZjFlMGY1NjU4YzI1MTNlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:44:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZUTGdTckV4Znl3Z3M4WU1ab1Jmb2c9PSIsInZhbHVlIjoiTnlINURDbytSSktHemIrbkpzdnRORXU5bnV5THcveEl2TUdLMC9uREU5WWpTbUZzRFBqWmw0d1I2aTFSa0NJSUJpUDNqMzdiZ243dVpVczl2eVhVZnlGaGFhYndzd2taR1g2d3NrMVp0OFVDWDFvR1o2Uk5OQk1yK0FjL3RQdG9xb2JhRWxNYnlIeEpXcUpSeEV3VVMwWVpqUkd3b3FHVEtoNFZzZy9DNjlmUmhJSHlGTjdNYU95dnpGUUhXam9UVUMyYjZsV2xqVlFXdmlPaE1jUEZFREV6ZmlBak5zbmovY2t4V0phYWlPcEZvRndiV1F5cHBjSzVBNnZteDB3Wno5WlVSZEtkZTZzeEJSWmk3QkR6aEs2UDBJblRGSTlzejhyell4aUxUOU9BVDU5K3Z5bkRLRURYUmpLcVZqUlVYbGNpWm9SdUJpNjN2Y3RBcXorK09mdEJrUEZpa0dzUXhlMEQvcitQVGNHelJSOGxTUzlNZldwTTlKcHF2SFBKY011RFE4Vm1HWFlPN0ZnZUhFMklsYVRZdlFEcjZVM1p3dWRMWjBRbTFaRktkQlI2QzloVlk2NWcrVVBqM3FhdjBsWURGRmg1ODdHWXIzdkRWM3NjTHpuWmdmbkN1RHNialVDR012QktYTitUcDJWcDB0cjA2WDBadTY4b2ovV1kiLCJtYWMiOiI1MTU1MzAyMTdiZjI0MTBlNWQ2MzEzM2U4OTg5ZGEwOWM2YTdlYTU4NWFmOWVlNWZlY2Y4OWMwNTZkZDRmNTRmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:44:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRLbXVHU2tndFZnRzJTZmk1cWVvelE9PSIsInZhbHVlIjoiZmlkUiszQlRJVnFvZXpaN3pISjYrZ3F0Sm5GdnhhVnRZakx6aE5LZUsrUFZHVHluaDdaU1dlbFBsWjhPc1JHOXF5akxPRjEzWHdQZU1jczBIcTVkZmFJeFJLeHJJbEUvSUNraGFkMWc1c0hEWU9COXdldUoyTTMvTjduRUwxTWlsY0hYaVJiZ05kVGNtL0xFek42OFRXVlNlTjNtaGJPNk9pdzVXVGdEOGg0YmZKYlllRm1FZkhqeVoweUwzTzJnQm5Yb2MxSlMyWi9LdWx0R3RiaFNsQVlwaVE0MVcveGd2UHk3QUp3UWdWN0QrVGRwRGJaOThuS0toZE5TYWRucEplKzBSS1NqZnNHTXVEcjh5dEVYTmhxRWZVQ01NNmx1ckUzUFdSMnJ0SExORzNRQm50di91bS80aGl4K3psRmFqWUU4UTZ5Zy81cldtMlE0VzAzcVdiWStObzJNbFdOWUp2QmFWTU9QazhvdjZGbW5iN0dBM0Jka3pSNFJVRjRSd0l3SWJsb2R1WWw0UUgxcmxTQU9pVUgyWWNDdjVXVWlVaHkyL3ByVGsvTDB4ck5FSDRHemxUSFljRzR2NUp4ZUZYUGZleHhiWjI0YzNXbEMwc3dyQ1dOTWJrT2hWZ0VjS0VwMVFhcjJmWTlyeS91WnFlYVBidU1ocjMxUWNHR2oiLCJtYWMiOiIzMDdjYTg0MjU0N2NhMGE1ZmY1Mzk3NWY3YzVmZDJkN2E3NzlhNDdlZGM5YjhkN2I2ZjFlMGY1NjU4YzI1MTNlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:44:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZUTGdTckV4Znl3Z3M4WU1ab1Jmb2c9PSIsInZhbHVlIjoiTnlINURDbytSSktHemIrbkpzdnRORXU5bnV5THcveEl2TUdLMC9uREU5WWpTbUZzRFBqWmw0d1I2aTFSa0NJSUJpUDNqMzdiZ243dVpVczl2eVhVZnlGaGFhYndzd2taR1g2d3NrMVp0OFVDWDFvR1o2Uk5OQk1yK0FjL3RQdG9xb2JhRWxNYnlIeEpXcUpSeEV3VVMwWVpqUkd3b3FHVEtoNFZzZy9DNjlmUmhJSHlGTjdNYU95dnpGUUhXam9UVUMyYjZsV2xqVlFXdmlPaE1jUEZFREV6ZmlBak5zbmovY2t4V0phYWlPcEZvRndiV1F5cHBjSzVBNnZteDB3Wno5WlVSZEtkZTZzeEJSWmk3QkR6aEs2UDBJblRGSTlzejhyell4aUxUOU9BVDU5K3Z5bkRLRURYUmpLcVZqUlVYbGNpWm9SdUJpNjN2Y3RBcXorK09mdEJrUEZpa0dzUXhlMEQvcitQVGNHelJSOGxTUzlNZldwTTlKcHF2SFBKY011RFE4Vm1HWFlPN0ZnZUhFMklsYVRZdlFEcjZVM1p3dWRMWjBRbTFaRktkQlI2QzloVlk2NWcrVVBqM3FhdjBsWURGRmg1ODdHWXIzdkRWM3NjTHpuWmdmbkN1RHNialVDR012QktYTitUcDJWcDB0cjA2WDBadTY4b2ovV1kiLCJtYWMiOiI1MTU1MzAyMTdiZjI0MTBlNWQ2MzEzM2U4OTg5ZGEwOWM2YTdlYTU4NWFmOWVlNWZlY2Y4OWMwNTZkZDRmNTRmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:44:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804675795\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-571179352 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-571179352\", {\"maxDepth\":0})</script>\n"}}