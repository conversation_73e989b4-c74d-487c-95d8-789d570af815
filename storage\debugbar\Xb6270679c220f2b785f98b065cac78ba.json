{"__meta": {"id": "Xb6270679c220f2b785f98b065cac78ba", "datetime": "2025-06-28 15:28:02", "utime": **********.065901, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751124481.621645, "end": **********.065915, "duration": 0.44427013397216797, "duration_str": "444ms", "measures": [{"label": "Booting", "start": 1751124481.621645, "relative_start": 0, "end": **********.01227, "relative_end": **********.01227, "duration": 0.390625, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.012279, "relative_start": 0.39063405990600586, "end": **********.065917, "relative_end": 1.9073486328125e-06, "duration": 0.05363798141479492, "duration_str": "53.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45727768, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00491, "accumulated_duration_str": "4.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.043851, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 33.809}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.053847, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 33.809, "width_percent": 13.646}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%يخصم المخزون فقط للمنتجات الحقيقية%' or `sku` LIKE '%يخصم المخزون فقط للمنتجات الحقيقية%') limit 10", "type": "query", "params": [], "bindings": ["15", "%يخصم المخزون فقط للمنتجات الحقيقية%", "%يخصم المخزون فقط للمنتجات الحقيقية%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0567732, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 47.454, "width_percent": 52.546}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1158543279 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1158543279\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1337083125 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1337083125\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-639588234 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"34 characters\">&#1610;&#1582;&#1589;&#1605; &#1575;&#1604;&#1605;&#1582;&#1586;&#1608;&#1606; &#1601;&#1602;&#1591; &#1604;&#1604;&#1605;&#1606;&#1578;&#1580;&#1575;&#1578; &#1575;&#1604;&#1581;&#1602;&#1610;&#1602;&#1610;&#1577;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-639588234\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">205</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751124481231%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhBVE9mOFBKOEYxenVuOFcvVStUYmc9PSIsInZhbHVlIjoiMStIbW0zcFdoZ3ptaXhVK0l6M2dPWk9lZnpSSVVFbTIyVTZxaW9RSkpycU9KQVpQYW1JcGhhTFVsSWRvZ3FaLzNrWFkxSnR0RjNaUzN1Nm1tZGdLcHk4eEk3M0xSZVAvTjR6eS9zeTBoSC9pSzhTK1h1VE9BWlp2THNPMmVOcEk4ME8ydHVUWEpZT2VIMUgrYVptQ0xsRmIwYWFUWTViemVUQWpJemZ4bFJwNlNpdWFCc253MjYwK2VwL2pvOFVjdkhINENOOUpLL0F6RURMaWRMVDZvbSt1Yktsb2FMNHVWVEc3SkZySU5TU1hxK2tqdm91aXhTd3VQQjA5WVdLVU5hK3EzTE9jM0xiT2xKWWZlTkpiUUh2UFRDeEdVbUtmYldQVTV4K0ZZb1N0SGR3ZTlOZjZhSzdYMXhrT0Y0Y0RuRElhaUxvZEhUbGZRaGwrNmZ0K3BoSmJyQ0FRWEV3UzhSTUhoT0E3YXQzRGdOT05GMnJKUU1UVmhyRXhYeFBQdUV2MUFvdXErV2swRXQ0SU5QUlpleGRlZGV3ZlJ6dVZ3Q1JyelBPdnJSZWVIbFFiMElybVlxdis0RXRNMXoycDJyekVMVEpwcFh6OUx5SzJmbjZPVzFOMTZEcVNjSHpkSGU1cUlrVm8rQ0t2SmwwbTNBUU40d052SUhDY1FjZTMiLCJtYWMiOiI1ZDU4NWQxYmQ4Nzg4NGE1MjE1OTZiZjI1ZGI4ZDA2MWRjY2M4YzljYTk3YTg3MzA4YTcxNmU2ZmYxMTE2YmYwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ind2amc1Tmh2NnFxK0lsaVJ3dFo3eGc9PSIsInZhbHVlIjoiVjUvVklCQmdpSEpoK1haaTYyMTFjRXRrdFM2WERORlgrYjRCazUzdkZvczJNTzlaOCtSTjZkNzRpd3ZrR3k1V1d6akkrYW1LK0FRSmEzVy8zUzJma0x4aHRyazZwREdZQkhoa0pNakQxR2FtQW9aanNaWUtKOFZ5TTlIcnNwRFd1ODNKMTIrTUdLT2UwZi9PSkNWNm9uSUhZbGJNcnU2MWpoRS9QeEs2elQxYkNoN2E3cHptWk04WTRaMGU3YjZDdzZFYlY3TkdBM3FWOGptSloxV1lVNnRycHFSbVZRdlFyL0JZSUVvcW9ub1lkVzNBUGVlWW0zQ0hDVTlnRVlqQXBvNFFKTjZLWFB2L2lzaTNFQklqdmloaUo1UzhUR3M1ZGo3STkveHpFekk1QXhsMGtzK2hxS1FWc0lXL1VTNWtUVnJCQmQwTEovbkhTdVdnQTIwZGpHVFBkNHpZZjR2R2RyOTlneEwzdDl6S3RJQkQvRWE5bUhBaWV6UUhpVnNMSmwvdWMyNWF5bU9EMTBmQnBwOTNXbStDUlg1c0xXUHpjMU5uTy9UQmV0bVBuQTd4U05xWU8xc1BzK1FuVTNnQWQ1RHlsdXV3cGNVK055Uk5NNTlodmt3aXNvL3BGSTFhQlZwS1dNN29Oc3lVZkZMNjNla2RBdUxyMzI0R3V5WmgiLCJtYWMiOiIxZTA4ZDQ2NjE1OGYyNTY2YWUyMjFhNmY0ZGRjYTIyYjc2OTg5NjcwYTc0NjcyZGYwMGUwM2U3NjAwMDFhMDE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:28:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlsTU1sNllsd2ZkMXJxWUFKM2VRL3c9PSIsInZhbHVlIjoiWEF4elZtNmF5ZUlZMTZOM1lHR1dCaXl5eUFxOWJwTlhlZ1pQV3pHdXJrR3hrVnd2TG5qWUJSbmdPUk5XNGErS0k3SnlCZzFWVnM4Zm9HaHd6VnNBRDF5aW8xVlljMjZ3LzZkQXZOc2srTmlMSHo5U2w5TzRqM2U0cFJoWlBaZEVMWXQ3Z1BTZnBnbG1NUWRhTE5SWG1xSjUxVjVwRzdjMnJXa1h4RXNLVm5DL0ltS1plU1VaZUc3MXNHbGhmVitMY0NaNjVMSG9EVEMxSlBlSVp6eVorYy9LSzYrSTUrSjcxazFnQ1BJZXFFT1NVZncrbFZTUTRiYWt6ZHlMcGk2U1ZNZFlFSFc2cUVXZVg0Y2tOM0ZvVXdIaHJYSmFDazM4c283Y2NWb0M4dVZhcGQ0U1Rpb0hKRFZnT3hiVkc5SGdYV3VSbXk3THJsejNCaDdLNjVHcEpJdkRYRXhTN3MxTyszRm9qQmg0QUF2MGYwbEFEanVHUytpOUFEWG5TL29pR1B2RnNmY0F6U3UwL1NUSmZHaEZaQ3FwNFdrUVdHUHQrRDU2VGkrVUVnVFNPRzR6a3NlUmV4ZTE5ZGRmdmdRUTVOamFoYTF5V2l3VUUvSXQ2MWQ1T2pKUmpncmF1TFVQaGlUemtyRDc2cFNXRHRoZmszdUpiODA4WWlQMCsxcVgiLCJtYWMiOiJiM2FlYjFjN2RlOWIyODYzZTQzMWQyNTgxZWE3NDk4YTc2N2EwYjBmODc5ZjlhNWI0MjFiZmViN2YzMzAxNDU2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:28:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InA4OXFuelFraXlNbnZueDQxVnV5MWc9PSIsInZhbHVlIjoia2FVZ0FCR3R1dDFLbHBjN0FCWjFsZFNpSmJPbW96elFMbVNLMUsvQXlPN1FuSmRpQ2pyZ1dqVjBKSWpSSk5jQThJVmpCcWU3TmI5T0NqcGNIa2VRUTk5cm5FSFBpM3VmRjRkL2RUZHd4K25aVGFMcUd6akRRNHhYa1dHTnJpb2p5MkRqUnU1d3FjZnoyclhFTnB1a3FramtHeEY2RVV4WFdLM1ZBUFI3N3BYZGdUQ1dLLzAyVUVNclJVWE4wcE1jTW5lUEFWVS9Hbk8ycmZwbmkzNCtwNS9ZSkpoVE5Uc2kzYkhwSkMvd0NwRFBYTG43UlczeS8vMmtNelgvQXhabGNKYVoyU3A4RzZXcjA2dExKVENjVlZTWjZhWDR3YXlhOWcwaUwxWXN3UzR0UytCbk9ZclMzQ0hMK0trS29vS1FqS1Btck4vY3ZlR2JKOUdXQjRuYVh1dHRZUFlibDAyZktLc3Y1N3h0cWtTcmVlVGlEQUlEb1BEeUpWZkpDM3pQNnFnM0Z1U2lMNUhpMHZaWVhLMXNQTFJEclB5d05oUlcvU0h3YTAxRlhVV0g1cGlIcWMvdEliOXJma1BpZ2tQNVlDbGxiUnpjSmIrbHNDcjV1K0NtbndSd09OWmFSM0xrUXBDRGhWblhPd3BMcDdTRVpCQWN4VVFOOU9sWGU3bjciLCJtYWMiOiJmYjBkZTZjZWRmODQzN2MwNzAyYjU3OTgxNmMxMWRjNWRkMGY2OGI0ODMyZDc4OTUxNmNhMmM5YjY4ZjQ3NWE4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:28:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlsTU1sNllsd2ZkMXJxWUFKM2VRL3c9PSIsInZhbHVlIjoiWEF4elZtNmF5ZUlZMTZOM1lHR1dCaXl5eUFxOWJwTlhlZ1pQV3pHdXJrR3hrVnd2TG5qWUJSbmdPUk5XNGErS0k3SnlCZzFWVnM4Zm9HaHd6VnNBRDF5aW8xVlljMjZ3LzZkQXZOc2srTmlMSHo5U2w5TzRqM2U0cFJoWlBaZEVMWXQ3Z1BTZnBnbG1NUWRhTE5SWG1xSjUxVjVwRzdjMnJXa1h4RXNLVm5DL0ltS1plU1VaZUc3MXNHbGhmVitMY0NaNjVMSG9EVEMxSlBlSVp6eVorYy9LSzYrSTUrSjcxazFnQ1BJZXFFT1NVZncrbFZTUTRiYWt6ZHlMcGk2U1ZNZFlFSFc2cUVXZVg0Y2tOM0ZvVXdIaHJYSmFDazM4c283Y2NWb0M4dVZhcGQ0U1Rpb0hKRFZnT3hiVkc5SGdYV3VSbXk3THJsejNCaDdLNjVHcEpJdkRYRXhTN3MxTyszRm9qQmg0QUF2MGYwbEFEanVHUytpOUFEWG5TL29pR1B2RnNmY0F6U3UwL1NUSmZHaEZaQ3FwNFdrUVdHUHQrRDU2VGkrVUVnVFNPRzR6a3NlUmV4ZTE5ZGRmdmdRUTVOamFoYTF5V2l3VUUvSXQ2MWQ1T2pKUmpncmF1TFVQaGlUemtyRDc2cFNXRHRoZmszdUpiODA4WWlQMCsxcVgiLCJtYWMiOiJiM2FlYjFjN2RlOWIyODYzZTQzMWQyNTgxZWE3NDk4YTc2N2EwYjBmODc5ZjlhNWI0MjFiZmViN2YzMzAxNDU2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:28:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InA4OXFuelFraXlNbnZueDQxVnV5MWc9PSIsInZhbHVlIjoia2FVZ0FCR3R1dDFLbHBjN0FCWjFsZFNpSmJPbW96elFMbVNLMUsvQXlPN1FuSmRpQ2pyZ1dqVjBKSWpSSk5jQThJVmpCcWU3TmI5T0NqcGNIa2VRUTk5cm5FSFBpM3VmRjRkL2RUZHd4K25aVGFMcUd6akRRNHhYa1dHTnJpb2p5MkRqUnU1d3FjZnoyclhFTnB1a3FramtHeEY2RVV4WFdLM1ZBUFI3N3BYZGdUQ1dLLzAyVUVNclJVWE4wcE1jTW5lUEFWVS9Hbk8ycmZwbmkzNCtwNS9ZSkpoVE5Uc2kzYkhwSkMvd0NwRFBYTG43UlczeS8vMmtNelgvQXhabGNKYVoyU3A4RzZXcjA2dExKVENjVlZTWjZhWDR3YXlhOWcwaUwxWXN3UzR0UytCbk9ZclMzQ0hMK0trS29vS1FqS1Btck4vY3ZlR2JKOUdXQjRuYVh1dHRZUFlibDAyZktLc3Y1N3h0cWtTcmVlVGlEQUlEb1BEeUpWZkpDM3pQNnFnM0Z1U2lMNUhpMHZaWVhLMXNQTFJEclB5d05oUlcvU0h3YTAxRlhVV0g1cGlIcWMvdEliOXJma1BpZ2tQNVlDbGxiUnpjSmIrbHNDcjV1K0NtbndSd09OWmFSM0xrUXBDRGhWblhPd3BMcDdTRVpCQWN4VVFOOU9sWGU3bjciLCJtYWMiOiJmYjBkZTZjZWRmODQzN2MwNzAyYjU3OTgxNmMxMWRjNWRkMGY2OGI0ODMyZDc4OTUxNmNhMmM5YjY4ZjQ3NWE4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:28:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}