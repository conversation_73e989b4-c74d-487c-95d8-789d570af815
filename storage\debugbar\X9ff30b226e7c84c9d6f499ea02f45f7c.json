{"__meta": {"id": "X9ff30b226e7c84c9d6f499ea02f45f7c", "datetime": "2025-06-28 11:24:02", "utime": **********.356484, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109841.943156, "end": **********.356499, "duration": 0.4133429527282715, "duration_str": "413ms", "measures": [{"label": "Booting", "start": 1751109841.943156, "relative_start": 0, "end": **********.282396, "relative_end": **********.282396, "duration": 0.33924007415771484, "duration_str": "339ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.282405, "relative_start": 0.3392488956451416, "end": **********.3565, "relative_end": 9.5367431640625e-07, "duration": 0.07409501075744629, "duration_str": "74.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45689992, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1910\" onclick=\"\">app/Http/Controllers/PosController.php:1910-1968</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.020579999999999998, "accumulated_duration_str": "20.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.312252, "duration": 0.01526, "duration_str": "15.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 74.15}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.337779, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 74.15, "width_percent": 1.895}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%123521%' or `sku` LIKE '%123521%') limit 10", "type": "query", "params": [], "bindings": ["15", "%123521%", "%123521%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.340239, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 76.045, "width_percent": 12.585}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (893) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.345839, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 88.63, "width_percent": 9.524}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.350359, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 98.154, "width_percent": 1.846}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-612598895 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-612598895\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2125693993 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2125693993\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1559285589 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123521</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1559285589\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-118100616 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">27</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IjhzRkRqb0JQSlZDUjhhd21NRGdSVWc9PSIsInZhbHVlIjoiM2M5eXVZV0dZMkpMdmpNYWJNODN0M0VGTUpPRDViVGhwandYZytGNGtyRlRwQXErdGtWNXp0bytZaWxkSnJPbGd6OXVxeVRsYmhDbkQ0UkxXdm1JMWJzTlRJM014TnE0Mkd6Y0E4VG45UW1ZRndFOWRPUWFvaGdQV1Q5L2JrZDYwSXJTdjRSd2plUFo0cy9GelNrUHhNczJxeTFDVGNwRzEydlo0YW1CNlR6MFdWY3RjUHdBcGZrVm16NFR5RnVaRFZSMkRnSWxzTzR2ZGVNcFE2RTVLQ0E1SVhZdjlRWWszMDFzVTRPeEM0bHBZMEIrdDF6c0lkenNVVlF0dnBaeWtCSW9OR2s4N2wzcWVOdnhWZGlVbWFBNENXbFZuYmZ6ZjhuaWE3QlpDWWhpbDlnRXRNbm1LSjFUL085RkkzQ3hURzVtT0F5bEZVRXhGNnlPYm1DRXg1MmYrdGJPZkVTMUh2MFNRdzNpNEMwdUlsVjBMR1pCS3RMdmY0Z1dTaXZjaGpPaUlNRjUwZUFXekpZeDh5Ujg1VGd2ZlRINURHeXduaUltNnFHdUh2dGtKeGZlb011dHJvM0o4T2pBeE83UEhlYVZMcWVGTGgxYk5jR3V0b0haRWxmbTk2M2Z4OHJ3eVdpQzRXc3VaVG9mT0tDaVE2OVlpeGlKcGZUU2Z1TkMiLCJtYWMiOiIyYjQ4ODA5NTAyNDFiMzMxMWMzMjRjNmIwNjA5NDdlZWVmNWE1YjlhNjk4Y2EyYWU4NjU5MzcwNDk2NWU0ZDhjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZEM0YwanozcUpUd29pWHh0SzZOaHc9PSIsInZhbHVlIjoiaDZaWTZJTm9vKzBudlFJWThPcUh5dE1rVnZ1b05SY2h5Vk1DdlNtS0pCMXE1Z1RnWlpMUFh0aUlTZ3lWM0xVRWMvZTNkR2EwVVF3U2VJMUwydGM2R2o2NjMwcUMvSXBSZk9rMnFnU21KZnRtbVJMRHRDZDdLTmRRVlYxUWx3dGpzNXE3a3I2L081Z28vcS9LSU04UndEaDcxUGpaelVPZEFNYndQRTZJaUk1NE9JbXEzanFQamhPSVpWVzU3eDYrSVVIeDhmSGhxdEs5UGJWVG1ubS9ScWxjUkFYV1JhUi9BbEQ1am5VVUtHL2xDQUo5ZlcxMW9DV0hMdDQ3OXQ3MTZ1ZklEcGJmWUlzVWllSDdVZGFIUithaFExaDRkVEJqazROcDFkSDVPZjRmdU5QZW1uV0hGMHZJTHlTVk5KelFtbitBTURjUU9aaWRkTDVtUVNPeG5ndnFJTHkwL3lMVVVWcklidk9rYnRUK0Zwa09xNDV3U3pkdEcrTG96MDBNcE1JdjNTL3ozTXFmUHdXNVFxSUVpWXN4OGo1bVFWS0pBR01EM3M0SWFwbXpCaFk5RkdxVDdweDhQOTZnMnVQSjhEaFR0dmtGd0JVVjM1OGJlOXdJNkdGNUgrZ2pxK2VyWjcrZzFhckhEQkJWVEY3dXAyMktLVDZESWU4OXlWOFYiLCJtYWMiOiJiMzRiMThkM2UxODY2MGRiOWM2NWI5NTI3NjA2YTRmZjk2ODMyNjhlMmI3Njc0ODlmZTAyZTYyNTJjNmRhYWExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-118100616\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1114181336 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114181336\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-967298210 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:24:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdxOVlFaEpROGdwb3VRdm5zNFUvT3c9PSIsInZhbHVlIjoiMVFuVGJtOGw2NDRFcE1Cb3ZhblBkaWNNZ25iTEdpRGtHSHVoYzQ1VDYxMGJ4RVI3dTVhVm5HWks4aytlU0k1bWcrODZEbytGVFllTFVGU1hXL3pCMkFYbks1MWRzVTIyeERBYXRUK1hDQTNKSWp4TUhEdFNncDEwSld2ZVpVUWhkd3lWRmtqM1lTY2pBaGh6L3ZXVHRXM0VzV1Vqa0pHYTh0VFA5QmYvQ2d3aE0yS3BLRHQ0d2piZUV2akZiQVlPN3BxOHl3djQ3VlNEZHNmb2xOSHpyMGQ4SkpvSnBrMExSVFlEQXVxNWowajJ3TUVLQzRzYys5VWZ4NlVSYmxabWprUVF6YmxsNmV5c1N5SVZ5MFYvVnY1UkIyUXpmMXN0TUZZOUVtTGMyU29ZNm1SNTVtQW4yV0EvbWtkQU8zQit4NEVxRlg4K2I4ZVR0Y0VYYjQvTVk3YTdNcDhlNlRsZzhlNWtzcE9ReGFIV0wvOVFrUmxPYk1qVHlBVWM4RmlTMG5ieDQydWxFeUNhWnlXdm8wNGl5VzBrazl3RHExelNjcVZtTVlnWENnS0o2M3BsNnlDNFZVS3ZjekJkVUVld2Fob2FXRWQyWk5qdHBEbENmR2tuK2xuUnBETjl1MVVlUGEwZm9LbUtFc3E1YkROVGJFOEtFL2VUc0pTT0NoUHoiLCJtYWMiOiIwNzY3NTZmMzRiMjg3NGRkZDc5NTk2NTg4MzYxYTgxN2JkN2E2Y2Q3NGRiYzQxODFjOGNjYTc0YTRkN2U2NTkyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjhLQk5ZcXhDQ3REcStnVXJIcHlrSXc9PSIsInZhbHVlIjoidzJzdmloNHptWWNGZ1Z3UkpCd3JJa3FNQWpZVzc4OFdPaDVYZENORit4eGl4YXVjNncvUmtRMk9yakYwV3VCNk55MUV3WlBFSFhFTjVERXNtOERVVlBJMGZEcVlUdlVDZ1l1ZUg3UXUvb0lFSitjMFpmOHhXb2h0SWVJb1YyTkdOVWF1OGxmeklMNzBnL0l6bGZYU1lNdGJHdjlaalVhQTl5aHpDdVY5UnpJUE9QSGY1bk5KYlZhN3Y3K1c5bG9uZCtiVjR6NlJhQ2tLRGk5ZXZuSkdMNXBlTGZrT2o0R2dERi9RQmVBT1c3MjlzdjlPYlUzaFVXdkRXN1ltTFJ1MnRaMmJGazhaRVgzRk9kdUd1YVI1SWg4VktHMUhlOFIrc2JSSlFSVGVuWng2YUFkRTBGT0lSZE5DL1lXbzYvTjd5dUZNZ0xyWnQwcVBTODBhaUp2Rm1QS3FuQUtoeCt3QUJPci9xTlcyNzdMWmRLUG9DS0dTYW5MNTF0bGs0NXUwYStqTGhEWUpvUXdzTFZib3lWL1RqUHBpTkIvb0xEdmNrNEJ6bzZGOTZCVlhrM1h2UHYrcnZUOHJnS1NYamZ4eUJzSFN5N1RXZ2VWUXpXcFdwSFltaWVYUmplQWNKMTEyRmpTd053WWk0UjlNV0ZrK0IrZDUyYithb0FZNTE0akEiLCJtYWMiOiJhZTA0NmJhYzljZWY0MDliMGMwZGJkNDVkYmQ2YWE5YjlmMmM4MWZiZjY2NjI4MjlmMjIyOTJiMDQ1MmZiYjgwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdxOVlFaEpROGdwb3VRdm5zNFUvT3c9PSIsInZhbHVlIjoiMVFuVGJtOGw2NDRFcE1Cb3ZhblBkaWNNZ25iTEdpRGtHSHVoYzQ1VDYxMGJ4RVI3dTVhVm5HWks4aytlU0k1bWcrODZEbytGVFllTFVGU1hXL3pCMkFYbks1MWRzVTIyeERBYXRUK1hDQTNKSWp4TUhEdFNncDEwSld2ZVpVUWhkd3lWRmtqM1lTY2pBaGh6L3ZXVHRXM0VzV1Vqa0pHYTh0VFA5QmYvQ2d3aE0yS3BLRHQ0d2piZUV2akZiQVlPN3BxOHl3djQ3VlNEZHNmb2xOSHpyMGQ4SkpvSnBrMExSVFlEQXVxNWowajJ3TUVLQzRzYys5VWZ4NlVSYmxabWprUVF6YmxsNmV5c1N5SVZ5MFYvVnY1UkIyUXpmMXN0TUZZOUVtTGMyU29ZNm1SNTVtQW4yV0EvbWtkQU8zQit4NEVxRlg4K2I4ZVR0Y0VYYjQvTVk3YTdNcDhlNlRsZzhlNWtzcE9ReGFIV0wvOVFrUmxPYk1qVHlBVWM4RmlTMG5ieDQydWxFeUNhWnlXdm8wNGl5VzBrazl3RHExelNjcVZtTVlnWENnS0o2M3BsNnlDNFZVS3ZjekJkVUVld2Fob2FXRWQyWk5qdHBEbENmR2tuK2xuUnBETjl1MVVlUGEwZm9LbUtFc3E1YkROVGJFOEtFL2VUc0pTT0NoUHoiLCJtYWMiOiIwNzY3NTZmMzRiMjg3NGRkZDc5NTk2NTg4MzYxYTgxN2JkN2E2Y2Q3NGRiYzQxODFjOGNjYTc0YTRkN2U2NTkyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjhLQk5ZcXhDQ3REcStnVXJIcHlrSXc9PSIsInZhbHVlIjoidzJzdmloNHptWWNGZ1Z3UkpCd3JJa3FNQWpZVzc4OFdPaDVYZENORit4eGl4YXVjNncvUmtRMk9yakYwV3VCNk55MUV3WlBFSFhFTjVERXNtOERVVlBJMGZEcVlUdlVDZ1l1ZUg3UXUvb0lFSitjMFpmOHhXb2h0SWVJb1YyTkdOVWF1OGxmeklMNzBnL0l6bGZYU1lNdGJHdjlaalVhQTl5aHpDdVY5UnpJUE9QSGY1bk5KYlZhN3Y3K1c5bG9uZCtiVjR6NlJhQ2tLRGk5ZXZuSkdMNXBlTGZrT2o0R2dERi9RQmVBT1c3MjlzdjlPYlUzaFVXdkRXN1ltTFJ1MnRaMmJGazhaRVgzRk9kdUd1YVI1SWg4VktHMUhlOFIrc2JSSlFSVGVuWng2YUFkRTBGT0lSZE5DL1lXbzYvTjd5dUZNZ0xyWnQwcVBTODBhaUp2Rm1QS3FuQUtoeCt3QUJPci9xTlcyNzdMWmRLUG9DS0dTYW5MNTF0bGs0NXUwYStqTGhEWUpvUXdzTFZib3lWL1RqUHBpTkIvb0xEdmNrNEJ6bzZGOTZCVlhrM1h2UHYrcnZUOHJnS1NYamZ4eUJzSFN5N1RXZ2VWUXpXcFdwSFltaWVYUmplQWNKMTEyRmpTd053WWk0UjlNV0ZrK0IrZDUyYithb0FZNTE0akEiLCJtYWMiOiJhZTA0NmJhYzljZWY0MDliMGMwZGJkNDVkYmQ2YWE5YjlmMmM4MWZiZjY2NjI4MjlmMjIyOTJiMDQ1MmZiYjgwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967298210\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1883512336 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883512336\", {\"maxDepth\":0})</script>\n"}}