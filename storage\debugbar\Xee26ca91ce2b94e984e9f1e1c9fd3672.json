{"__meta": {"id": "Xee26ca91ce2b94e984e9f1e1c9fd3672", "datetime": "2025-06-28 16:02:05", "utime": **********.811848, "method": "GET", "uri": "/enhanced-pos/get-invoices?page=1&search=&date_from=&date_to=", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.385375, "end": **********.811861, "duration": 0.4264860153198242, "duration_str": "426ms", "measures": [{"label": "Booting", "start": **********.385375, "relative_start": 0, "end": **********.721036, "relative_end": **********.721036, "duration": 0.3356609344482422, "duration_str": "336ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.721046, "relative_start": 0.33567094802856445, "end": **********.811862, "relative_end": 9.5367431640625e-07, "duration": 0.09081602096557617, "duration_str": "90.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46605304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-invoices", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedInvoices", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_invoices", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2467\" onclick=\"\">app/Http/Controllers/PosController.php:2467-2533</a>"}, "queries": {"nb_statements": 17, "nb_failed_statements": 0, "accumulated_duration": 0.019219999999999994, "accumulated_duration_str": "19.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.755908, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 8.949}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.765734, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 8.949, "width_percent": 2.029}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2500}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.768103, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2500", "source": "app/Http/Controllers/PosController.php:2500", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2500", "ajax": false, "filename": "PosController.php", "line": "2500"}, "connection": "kdmkjkqknb", "start_percent": 10.978, "width_percent": 2.237}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2500}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7701888, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2500", "source": "app/Http/Controllers/PosController.php:2500", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2500", "ajax": false, "filename": "PosController.php", "line": "2500"}, "connection": "kdmkjkqknb", "start_percent": 13.215, "width_percent": 3.018}, {"sql": "select * from `customers` where `customers`.`id` in (10)", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2500}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.774893, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2500", "source": "app/Http/Controllers/PosController.php:2500", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2500", "ajax": false, "filename": "PosController.php", "line": "2500"}, "connection": "kdmkjkqknb", "start_percent": 16.233, "width_percent": 1.873}, {"sql": "select * from `pos_payments` where `pos_payments`.`pos_id` in (1448, 1452, 1453, 1454, 1455, 1456, 1457, 1458, 1459, 1460)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2500}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.776751, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2500", "source": "app/Http/Controllers/PosController.php:2500", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2500", "ajax": false, "filename": "PosController.php", "line": "2500"}, "connection": "kdmkjkqknb", "start_percent": 18.106, "width_percent": 1.769}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1460 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1460"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2509}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2502}], "start": **********.779233, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 19.875, "width_percent": 7.388}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 121}, {"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2509}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}], "start": **********.781813, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 27.263, "width_percent": 1.197}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1459 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1459"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2509}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2502}], "start": **********.7830338, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 28.46, "width_percent": 7.752}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1458 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1458"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2509}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2502}], "start": **********.785656, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 36.212, "width_percent": 10.718}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1457 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1457"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2509}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2502}], "start": **********.788816, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 46.93, "width_percent": 7.648}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1456 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1456"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2509}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2502}], "start": **********.791355, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 54.579, "width_percent": 7.596}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1455 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1455"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2509}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2502}], "start": **********.793773, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 62.175, "width_percent": 7.336}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1454 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1454"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2509}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2502}], "start": **********.79612, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 69.511, "width_percent": 7.336}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1453 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1453"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2509}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2502}], "start": **********.798444, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 76.847, "width_percent": 7.284}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1452 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1452"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2509}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2502}], "start": **********.800757, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 84.131, "width_percent": 7.752}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` = 1448 and `pos_products`.`pos_id` is not null", "type": "query", "params": [], "bindings": ["1448"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2509}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 786}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2502}], "start": **********.804178, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Pos.php:79", "source": "app/Models/Pos.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=79", "ajax": false, "filename": "Pos.php", "line": "79"}, "connection": "kdmkjkqknb", "start_percent": 91.883, "width_percent": 8.117}]}, "models": {"data": {"App\\Models\\PosProduct": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPosProduct.php&line=1", "ajax": false, "filename": "PosProduct.php", "line": "?"}}, "App\\Models\\Pos": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\PosPayment": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPosPayment.php&line=1", "ajax": false, "filename": "PosPayment.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 33, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-invoices", "status_code": "<pre class=sf-dump id=sf-dump-1696566328 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1696566328\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_to</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-854524179 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-854524179\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-358849522 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126520354%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikd0a2RhZm1IWUxPQVJyT0oxUTAvVGc9PSIsInZhbHVlIjoiSG9zYmkwVUI0YUZGeDV4dFVKS3RCWWFKeStnenNFbzlnSkUyZ05VV0xSNm9tR25DZzMrbmJvVXN6bGFXdzJYZmViQlJuUHpsYjdUazVXdHArRW81eGtkaUNkL2ovbG9nVWovcGNQT2xKSXhBR0UvTllRUVlTWTNwTldWd0VuNU0xMURCZW5zekhNYUxJekVya1BOSlJoK1hjV0wzRXVSZEViSENoZEtpcHUyWHUxYnRwU3d2cDlTY0dCdmM5VlJYWmowME5oeXc2N1Jwd2ZESFA2WVMveHdqV29wWDNDRGxBUWNkcWQ0VitBamcwNjk3emVDSm5BVXoyUElKdjIvTWllQTgrZmg5QWdjYzAwcEdKalNDQ2o4aTB3K2tyMEgwQVJQOWhadEdzRm1iL3hVQm5TN3dmclNDZVVOZ0ZXVGlBWWRwY21kNzZkbU53ZkdtSlNxaGlmckkzdE9DeTZHanp5S1M4VmJuL3BIMUl5RzlIU01XWDhDWWxxL3FzZVJHRWsrZTZxejkwQjdLRklDVHpJL3BWR0R5QlgwZDloTnMzN2s3MEhCbDRYbis3SDlEcm03L2tWTUc4c1pLb2VLa0x0UGlOdXV4VE9kc0RCRkhMekJnRGNtM1B2SzZjNDJIQ2FlZjBhMDVvdFI1M3JKT1NZUDZ5TFhwdDN6THczYy8iLCJtYWMiOiJkZmM0ZjJiNjQ3ZTdlZTg3OGNmMGU0ZDM3NzU2ZjIyNDE2MGQxOTU4YTNlNGM2M2JlOTA4OWQzYTM5YTZkM2Q1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9NM3BHRFlKb0RRZW5ONmM0NjNJcGc9PSIsInZhbHVlIjoiZmFIVnYyOER2WnRwMGx3b0Vhc3JaSHZSeGtndHhvNVBlUXFFZnhxODNJcytVZnpZNHExRTZoU1hzbGFhVWM3dkRWcmxURUxnNHhNZFp3Wm0zZVdqd2ZyR0ZpWHZXQkJ6bktXVTBRdzlTZGJKZWVoSlVEck12YVA4NXQwRUc5VDhtbHQ3VnBsY1I3OVVtb09xbUhMaDVRUUFVSkovdWJDdzRqREJ6eW8wYzNWZGRWK0ZpdmNNVHh0UkFId0tiTjZEdFJpdFp0TXB6Smg3MnFBb2VuTFFsNmlSOE11eHVVeEJ3MngrTy9pa25OMTRjMEhKQUY2dGFjSVFmL2k5RkxvTlg4ekVHaG54OFVqUEw4RjExV0tlM1JBR2RsTi9HOW9QSHhwelk5azdMWTJBSHZQNHp1ZWpqZmFRV0hKL0dNSEFsU2hvUUZrakFNRnBnRVlSWEpHL2xHakdVUi8xWTN4c0hZTW0vcFJ1NUY3aXp6SEZQTzhEUng0cDlWUGdvcXJENkZRUjRCTklVQXlVMUJSTnJXY0J0cUFCUFc5TVNodmY0NUhJcWJQRXpHRDhZaDUvTDNVT0o4ZE1GYmFqd1p3UVA2YzREWnVjeEFYcTNzUWpRSGphTk1ZNUFqVUZaSVVHZzR5L1FhdmpBamFxTWtzSXkwTEd3QUQyUnVaaG1WNVkiLCJtYWMiOiJjMDhjNGZmMTZjY2NjZmIyOWU5NDBlYmY2NzZmYmJjZWQwY2IzNzRhNWVmNTRmODU0YzU5Y2YxNGY3NmNjYjI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-358849522\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1751333947 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751333947\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-167181732 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZkcTRMU1JVaHZCWjNBVWgvSkdPMmc9PSIsInZhbHVlIjoiTjlKcmxpemFJUlFWaXUxREhLRTQ4RWRYcXdUWjQwZGtrT2FHbGdDZURGTGJzblVoTWpkQzVublYwdmowZWVlZEc2eW5pWEt6UWF4TzZJVUQvNFF1Yk16UTM3Z3JZS1FtSjR0QXZOWXRReHhsckxyTkNqRmI0K1A5M21OMXlFaEpSZXR0cFN2ems1OENsaExNS2xFdlZLRGJQNTVOL0Q0Z0lJQUUwQmRHUU5sdVFkT04wa0Fjbm5OalI5QjBhQmE0L1RCaEV2eDBLVC9WWHM3ajVKaDMySVJSbTQ0KzN1N0lmS0VDd1R1aXVLMlhVZVRRa0EzN3p1NU5XVEFoMWZxL2Q2Rk1EU0ZBbVp4TGM0alFLN1AwTHZudlV0a0I3Qms3UmxqSFRXbGpnTzZnbW10cEY1dFdnZi9CQVY4bnFxS1BsT2thYjZMNTAvbzgzdmxvTm9hbTkzQ0h6dzlPLzlMZWZiTWp3Z01VQU8waDNuRis3Yk55cjZwdnBWM0ZoZlg1VWRSQTJjL2pWa01rR3hBRHlBVFNTL2JpZG1jVkY0R0RmQU0yMDlnSTErbTIyTWVXbVV0eWk1cHdoUXorUHNxc1lZRHZmK0g1dldqUEYwZnhLdGZHTWR1VGdKNG5DM1poWStRWTF6QnpNOUdWaXQyZFVIOFNmazBsV1RBNlFhVDkiLCJtYWMiOiIwNDFmNDMzNzBjYjJlYzYzZDY3MTk4ZDdmNTRjNDRlOTA3MjUyM2EyNTM2MmJlZjU1OGE5MjYxNTVkYzQ2MTM1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJUSmMrU2tuZHZpK2FhN2k1NWs2Nmc9PSIsInZhbHVlIjoiWUUyeHE3ZlE5c3FLTnlIWWhQWjJVQjBTdXh6NXNhaWNJTDR2dEorMmxFZVhVR2k2YUl5VXJsYnorZGQ3SkxNV0hZSWVVZWNNWmZ4eGJPRUJETlNHeFVYNzRQSmV6ZG5yTTNVSDd2eCtJQ0xmV3FiL3RFaG13d1RJMnFvc0t5ZjdZSC9iaTNldHBaV1lBck5BeTZqNXpoWlAwMEZrNkpHUEZ3Vk14TUM0QlphMk5Udjh6VGpOODZ4M2hoK011bzdjRld2SzEyeGhzWWFmNThGYlI5Z05Yb1Q5cTNVUlpQZmJvWU9QRFFSZ3M1UUpsQlE3UTM4OEtHOUlvNVhmVldUamNJVWtwQkxqTjNxVG9YZTNZRnVER1hWWk41eDQzaTdIb0ZVb1JKNm1VUkRRMGlRbDVnYURXUjRPTFpvMFl3Tm5EU29iU1pINUYxRkIvU0dQb2lwOW4xY2swMUdxeWVkYjcxOGs2Mk1ZcklDNU9OeVJjODlTSk1GL0pkNlV5TnhIdVV6NEZHTTZOZEt1ckJNNWxSQnhZakl5aGtoNURzS0g4MklpMHlLcEdqbHZqZnprNk9QOU5GUUtIdSswZW9Cek50aWNRMkJzWEljT2tRMXNPNSs3Y2tqY3QzTUdXYXkvZVBIUjAya2Z5TmZxdVlHcW5OcGFpZDRIUzZRMjEvUlEiLCJtYWMiOiI3ZDM0Y2E2ODU5NzA3MzllYWI1M2Q1MmQwN2UwNWRhM2U3MGI4ZTY4Nzc2YThhMTljNzQ1ZjZiMGNlZjQzZjc2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZkcTRMU1JVaHZCWjNBVWgvSkdPMmc9PSIsInZhbHVlIjoiTjlKcmxpemFJUlFWaXUxREhLRTQ4RWRYcXdUWjQwZGtrT2FHbGdDZURGTGJzblVoTWpkQzVublYwdmowZWVlZEc2eW5pWEt6UWF4TzZJVUQvNFF1Yk16UTM3Z3JZS1FtSjR0QXZOWXRReHhsckxyTkNqRmI0K1A5M21OMXlFaEpSZXR0cFN2ems1OENsaExNS2xFdlZLRGJQNTVOL0Q0Z0lJQUUwQmRHUU5sdVFkT04wa0Fjbm5OalI5QjBhQmE0L1RCaEV2eDBLVC9WWHM3ajVKaDMySVJSbTQ0KzN1N0lmS0VDd1R1aXVLMlhVZVRRa0EzN3p1NU5XVEFoMWZxL2Q2Rk1EU0ZBbVp4TGM0alFLN1AwTHZudlV0a0I3Qms3UmxqSFRXbGpnTzZnbW10cEY1dFdnZi9CQVY4bnFxS1BsT2thYjZMNTAvbzgzdmxvTm9hbTkzQ0h6dzlPLzlMZWZiTWp3Z01VQU8waDNuRis3Yk55cjZwdnBWM0ZoZlg1VWRSQTJjL2pWa01rR3hBRHlBVFNTL2JpZG1jVkY0R0RmQU0yMDlnSTErbTIyTWVXbVV0eWk1cHdoUXorUHNxc1lZRHZmK0g1dldqUEYwZnhLdGZHTWR1VGdKNG5DM1poWStRWTF6QnpNOUdWaXQyZFVIOFNmazBsV1RBNlFhVDkiLCJtYWMiOiIwNDFmNDMzNzBjYjJlYzYzZDY3MTk4ZDdmNTRjNDRlOTA3MjUyM2EyNTM2MmJlZjU1OGE5MjYxNTVkYzQ2MTM1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJUSmMrU2tuZHZpK2FhN2k1NWs2Nmc9PSIsInZhbHVlIjoiWUUyeHE3ZlE5c3FLTnlIWWhQWjJVQjBTdXh6NXNhaWNJTDR2dEorMmxFZVhVR2k2YUl5VXJsYnorZGQ3SkxNV0hZSWVVZWNNWmZ4eGJPRUJETlNHeFVYNzRQSmV6ZG5yTTNVSDd2eCtJQ0xmV3FiL3RFaG13d1RJMnFvc0t5ZjdZSC9iaTNldHBaV1lBck5BeTZqNXpoWlAwMEZrNkpHUEZ3Vk14TUM0QlphMk5Udjh6VGpOODZ4M2hoK011bzdjRld2SzEyeGhzWWFmNThGYlI5Z05Yb1Q5cTNVUlpQZmJvWU9QRFFSZ3M1UUpsQlE3UTM4OEtHOUlvNVhmVldUamNJVWtwQkxqTjNxVG9YZTNZRnVER1hWWk41eDQzaTdIb0ZVb1JKNm1VUkRRMGlRbDVnYURXUjRPTFpvMFl3Tm5EU29iU1pINUYxRkIvU0dQb2lwOW4xY2swMUdxeWVkYjcxOGs2Mk1ZcklDNU9OeVJjODlTSk1GL0pkNlV5TnhIdVV6NEZHTTZOZEt1ckJNNWxSQnhZakl5aGtoNURzS0g4MklpMHlLcEdqbHZqZnprNk9QOU5GUUtIdSswZW9Cek50aWNRMkJzWEljT2tRMXNPNSs3Y2tqY3QzTUdXYXkvZVBIUjAya2Z5TmZxdVlHcW5OcGFpZDRIUzZRMjEvUlEiLCJtYWMiOiI3ZDM0Y2E2ODU5NzA3MzllYWI1M2Q1MmQwN2UwNWRhM2U3MGI4ZTY4Nzc2YThhMTljNzQ1ZjZiMGNlZjQzZjc2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167181732\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}