{"__meta": {"id": "X39b957a311a469ac58f21954dc05f1f0", "datetime": "2025-06-28 14:59:13", "utime": **********.072183, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122752.640008, "end": **********.0722, "duration": 0.4321920871734619, "duration_str": "432ms", "measures": [{"label": "Booting", "start": 1751122752.640008, "relative_start": 0, "end": 1751122752.975179, "relative_end": 1751122752.975179, "duration": 0.3351709842681885, "duration_str": "335ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751122752.975187, "relative_start": 0.33517909049987793, "end": **********.072201, "relative_end": 9.5367431640625e-07, "duration": 0.09701395034790039, "duration_str": "97.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43802096, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00169, "accumulated_duration_str": "1.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.001488, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 88.166}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.00567, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 88.166, "width_percent": 11.834}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1764244372 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1764244372\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2060488337 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2060488337\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1508157640 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122750616%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlIxQTBRL2NvdEdQQjV0dERuZHMzQXc9PSIsInZhbHVlIjoiMTZmM094cUpCZDMyU293TEFzUjU3dU9oODBSVVNnQjErZWQwb3g3cWt2Z2ZXMnpVUytjajYzV215ZktaN0kzNUMzSUJyR0k5dWpwRDFwZitLRmZObVBLY1pXQWtONnk3VTlzSHNwdTZBYWlEa3RPeTJtc1E4WmVwTmJ6VnlYR0FLR0RhT2wrK29vcDNNdFZydXY0QTdLakpUWFArZENzRUxvakYzbGVXSERiTW8wL2V5T2FzSE9EOHd4UXJJOHJUVlc0SXJrb1VCcVpyZUdicmRSaXNKekR3dE44ZTBmZ2szNXJEQTl2MGY3bEVmNlY1WFVDQjc2b1NKMmdhN245Y3gwZzhpTTUrZUorTGVMa1pCTXlkOTdBRmRBSU5qbFpoeGNCUWtDZlRLMU0zUzR1QVlqY29JV2VYSHUwRVFLTVVLclBjWEhXZWRBTE9CNXZ4eUltWWhIZ293WWlGUkxBNVgxUU9OMnpvUW9LdnBQdW5sZkRWdzRjOXN3S0tZUlBIdHlpdWNvV1ZaWFBpeDM2VE5UUWNwd2wyS09xQWRDZzVoaWhpSEpPaFE1RHVsZnlyaStJSkdkMzlmcmNIdVhKTXlyRUxBODJ2SWVGU0lrNXQ2MWp4K25hQnd2eUthWWY0dTVqZ1ozeWJqNzVIYUxON1hrakY4czl0Y1dqVndkWXoiLCJtYWMiOiI4ZDY5ZjQzMDI5NmM1ZDBkMzQ3YTlmY2FkODEyY2JmZGQ1ZjVmZWQ2MDZmMDBkNTM0NmQwZTFjM2JkYjQ3M2ZjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlXVmdJWWQrZStBblRXZjJ2bkpnOUE9PSIsInZhbHVlIjoidGcxT2N5b0pKVm9kWk1YYXI3SXFkL2J0eVY2UUVsVTN1QUp3Q0MvYURmdGpaOHNFVDBDMUkxUWt0Ylk4M0J4MjdyZllxSUd4TUkvVGFNREdsdlhlWGxiUjdydkxnVkd3S2VFVTcrRmlIZitsaXRFLzVxYTV3WW54VXZUWGtPck5Mck1kWlRLQURRRTRFZkdLVFczQmVuYjk5dWhHQmd4UGFRcHBrSmNqNkNHNHZxL2VKTlk0M1dIbVRmYXoySWtZa01pUlVNdkNqN3FnaEhIR2RKTWxpUzREVmU0aTdzMW1OMHlRTzVMekZESEI4ZVVTamYwTmYxV1hBZTNGR05UN293MnFLbFhiTXNmZzBoSGJYaEgvTjdBcDduekZOMDVJSll3ZVQ2V3JjL0R1UHV2ZWlDbk1KeEpkYmZQNWhzWXczdGJmd3NVT0RKUngyc0FGaGREaVFlWFNXZThvSVRMa2dteW9ZSE1ITDVXNjdpcU4vTkNOczE5U2xvdndSQmZEdjJJQk4wallESSsyRDZyYkJEMGhhc3oza2s2a0NkWFU4MUhzdjBFeVgyWEQxN2FYUHlLRWI1WU9OQWxrVmd3Zm0zWjBBdkJWU2hpWmNDaUF5Q1NqM0lqNU5UYzVIdGhNeFJlY09HbzRmWFIzYTF1RHNpYTF1N3dnL1V6MEh2azEiLCJtYWMiOiJlOTY0MGEyODkyZTc5MjczZTZhNzIxNjlmMTJiMTM3YjhiOTIzN2M4YjAwOTQ3OGJiZTM1MDNmMGJhNjAxODgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508157640\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-426110828 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6nyGbgT6n8t00JbJ5CSgj7vkpXnxnyISs5XS6j6J</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-426110828\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-797582006 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9VTmRIbVJJemlWOUNCc01rVkdoWFE9PSIsInZhbHVlIjoiWmFTQXkyRklaM3IyZ1lQVHQ2Zk1kTWNQeXczTmtTY0hvSlptU0pkQjI3MXdId3lIVVpKK3YrY3o5czJqUFZndHk1dmY2TU5EL1RrbWVaZnlqc2lzK0gyVXUxaHhLOXY1TUJjWERqRHBiUmthQmFIZHpxSWwzN2RmeVpDMDFQSTFkNUJ6RG9Oa1BGRUgzRFpxQ3JBeDBmWFBZWUIrMGh4NDA1MVBWVU1sTy9vYlFaTUhDNnBxN3I1dTkraFRYeW5nWGNFK2k4ekFEYWllN1BIRDB0WGtXZkJuTys2bTBIaVRyNHRzbEYwTGRYSEZRSGk2N3VjZmxtWTRCNEtYVWxjOFY0YW5LUkE3ekFHbTI1NDlqZENuUSswRmtBOE85ak43bGE0OVE2NlVkQU5aWUNQSlQyazlWVnJFOUU2TEMvNTFXTXJPMXVQcVRLTzRKcE90cHR5cnAreS96c2p0dVkxUWVqeHJZTWxzWGg4KzRDSjhCSVNFM2oyamFpK2lZVVFOUHFFOW1TVUlHRkhEWHZ3dmNtaHNGeCtDdFcrOHJUc0g5dUVtY3RFcVlxZStSWFhyZHVKUTNQbngwU2xqWDBqYi93dWpPb1J4a3hscnM2ZU11RFc3OGdONDBJM0grWUlKTDhKbzNGVmxGS1FiWXlwK3Z2Q1czMmNaZmZadmVWeFYiLCJtYWMiOiJkYTg4ODg3OWVlZjM3MTI4OGMyZjc2ZDNhMjA5MzBlNGYxYzU0OTgxNmViMzhkYTljMzA1ZjJlY2Q0NmFjZWMzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlSQmo4dWtlU0FYeWdKMFgzMm04bWc9PSIsInZhbHVlIjoiQ1hPMG0yYXc4UkNLcXpYQWlVdlltZVc4d1dRbHRsZkZkdXpQMFNncE51UlBOeGZHbTg4Y2RIZEZtU0wwRVExVU12eFhQVHUzRVVRd1NzT3lJOTJHUVE2OWk3WGlkU2NnTFZpWDNjbUhlM2JIWkJwaFNETzkxbzMzdmllK05YMDlKb01WN1ozTUZPQWc2dDJETTUxQWttanpJVlhHOUdZWEp4QlA3VVNlMTFweGF5YWRYU2lPanJ2TjQyUTR5Ni9sMDFmVlZqR3c0YTBubGw3UGFPTDIwZnRVTW9BL0EvTkdqWUlSK0pVL21pQzQxd3dPRndtalg2UmxMNzQ0YmpUdXpWbThBRENSckN2d3dMOCtuRDhWeFdDY0szRWtnY3F5cHZES2JiUXdYbHdvR2pjV1ZQUUg4S0xiMzBLQUZ2SlFJeWphN25US0ZkSktuQ2VsQ2p1L0ZFMjJPdmhCeGZUS3YzWHdsR2tPYnhsbGYxUFpzS1pMVGJZR0lvbHQwaVQ4ZnpMQUNIQkJZVXI2MzhUaGErd25QZDRwQXdXTk5MZ0J6UEJMaXZXNFVCbUxDbVQ5SDJOU2hCODdUTGdycUY4ZDVjUGxlb3gvSllxWHplOGFYRlAyUWtnWmozSzIyU1diWEhzL1pod0pVMk4xbzhDaitUVzNldkFYUlZ4a2FYSHkiLCJtYWMiOiI0Zjk5MTExMzg5NDIwYTQxMWVmZjFjODM3NDhiNGE1YjFjNzEyNjA1OGVhYzEwNGVkYWQ3MzQwNzdhY2ZjYTE2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9VTmRIbVJJemlWOUNCc01rVkdoWFE9PSIsInZhbHVlIjoiWmFTQXkyRklaM3IyZ1lQVHQ2Zk1kTWNQeXczTmtTY0hvSlptU0pkQjI3MXdId3lIVVpKK3YrY3o5czJqUFZndHk1dmY2TU5EL1RrbWVaZnlqc2lzK0gyVXUxaHhLOXY1TUJjWERqRHBiUmthQmFIZHpxSWwzN2RmeVpDMDFQSTFkNUJ6RG9Oa1BGRUgzRFpxQ3JBeDBmWFBZWUIrMGh4NDA1MVBWVU1sTy9vYlFaTUhDNnBxN3I1dTkraFRYeW5nWGNFK2k4ekFEYWllN1BIRDB0WGtXZkJuTys2bTBIaVRyNHRzbEYwTGRYSEZRSGk2N3VjZmxtWTRCNEtYVWxjOFY0YW5LUkE3ekFHbTI1NDlqZENuUSswRmtBOE85ak43bGE0OVE2NlVkQU5aWUNQSlQyazlWVnJFOUU2TEMvNTFXTXJPMXVQcVRLTzRKcE90cHR5cnAreS96c2p0dVkxUWVqeHJZTWxzWGg4KzRDSjhCSVNFM2oyamFpK2lZVVFOUHFFOW1TVUlHRkhEWHZ3dmNtaHNGeCtDdFcrOHJUc0g5dUVtY3RFcVlxZStSWFhyZHVKUTNQbngwU2xqWDBqYi93dWpPb1J4a3hscnM2ZU11RFc3OGdONDBJM0grWUlKTDhKbzNGVmxGS1FiWXlwK3Z2Q1czMmNaZmZadmVWeFYiLCJtYWMiOiJkYTg4ODg3OWVlZjM3MTI4OGMyZjc2ZDNhMjA5MzBlNGYxYzU0OTgxNmViMzhkYTljMzA1ZjJlY2Q0NmFjZWMzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlSQmo4dWtlU0FYeWdKMFgzMm04bWc9PSIsInZhbHVlIjoiQ1hPMG0yYXc4UkNLcXpYQWlVdlltZVc4d1dRbHRsZkZkdXpQMFNncE51UlBOeGZHbTg4Y2RIZEZtU0wwRVExVU12eFhQVHUzRVVRd1NzT3lJOTJHUVE2OWk3WGlkU2NnTFZpWDNjbUhlM2JIWkJwaFNETzkxbzMzdmllK05YMDlKb01WN1ozTUZPQWc2dDJETTUxQWttanpJVlhHOUdZWEp4QlA3VVNlMTFweGF5YWRYU2lPanJ2TjQyUTR5Ni9sMDFmVlZqR3c0YTBubGw3UGFPTDIwZnRVTW9BL0EvTkdqWUlSK0pVL21pQzQxd3dPRndtalg2UmxMNzQ0YmpUdXpWbThBRENSckN2d3dMOCtuRDhWeFdDY0szRWtnY3F5cHZES2JiUXdYbHdvR2pjV1ZQUUg4S0xiMzBLQUZ2SlFJeWphN25US0ZkSktuQ2VsQ2p1L0ZFMjJPdmhCeGZUS3YzWHdsR2tPYnhsbGYxUFpzS1pMVGJZR0lvbHQwaVQ4ZnpMQUNIQkJZVXI2MzhUaGErd25QZDRwQXdXTk5MZ0J6UEJMaXZXNFVCbUxDbVQ5SDJOU2hCODdUTGdycUY4ZDVjUGxlb3gvSllxWHplOGFYRlAyUWtnWmozSzIyU1diWEhzL1pod0pVMk4xbzhDaitUVzNldkFYUlZ4a2FYSHkiLCJtYWMiOiI0Zjk5MTExMzg5NDIwYTQxMWVmZjFjODM3NDhiNGE1YjFjNzEyNjA1OGVhYzEwNGVkYWQ3MzQwNzdhY2ZjYTE2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797582006\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}