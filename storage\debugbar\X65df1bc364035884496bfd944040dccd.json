{"__meta": {"id": "X65df1bc364035884496bfd944040dccd", "datetime": "2025-06-28 15:19:24", "utime": 1751123964.005949, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.589731, "end": 1751123964.005961, "duration": 0.4162299633026123, "duration_str": "416ms", "measures": [{"label": "Booting", "start": **********.589731, "relative_start": 0, "end": **********.940217, "relative_end": **********.940217, "duration": 0.35048604011535645, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.940225, "relative_start": 0.3504939079284668, "end": 1751123964.005963, "relative_end": 2.1457672119140625e-06, "duration": 0.06573820114135742, "duration_str": "65.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45798592, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2005\" onclick=\"\">app/Http/Controllers/PosController.php:2005-2058</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01686, "accumulated_duration_str": "16.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.972609, "duration": 0.01642, "duration_str": "16.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.39}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9978852, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.39, "width_percent": 2.61}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1848209081 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1848209081\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123960877%7C11%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inc0eDF6aE1acU96WVRLeDZEZ2p5TUE9PSIsInZhbHVlIjoiWVNxVDJBb0twMlM4VHUzeklkVUx2UHZZMFZjTlpyeXVzSDlDYjUrT1BEbnFvYXllNUV0THZzRUc2VzZzSlZFWkRiSlU4SDhJWk1oUnZmR2VWQngyS3FVRE56VEswT2srME5PY3pHQnZrTkdDUGplK29zcEpRekJwRitlWkhKTGlqdnJKOUVCZ2dnRGs3MFp4NlA2LzZxL0tlRmtMSDAzQzl3UllQY21sNXdVUnRCVHlsa0ZycWt5Q2dGR3pqVmJhOTg2UWlFL1BoS2FzMGhWNjNpZFlzTjIrcGxJRnlZQ3VyaVQ3UnFaVkxuUGRBMU93K3FCbkpISEhWY1A2bEJjUzlXdWtJR1lhaDNVUzA0amcvdnlCTi9vdjl0TEsrMEJySWJuTXM1bTluSlo1eGdCRUVNQXRtK3BSWHRuMkVuSzBwTWJxS2xaN2NDYUtVRjBuQ2hXaU04U1p2QklhVHRCRTdrbTM2UFpqQVQ0L1BFKzNneTVJcW1jU1NiUHA5MUlsd3hSNXkrZXNWUU1PK0hFaWt5ZjdhUmdzdVYzTXhVL1lFR1NFNTd3cmdnV0dDUFJHSFB4bnlTKzhNZnF2QmI5UVBiRXZ6bWI2VXcxSEpBSDFXN3pJSVNyZC82Y2pKSmZDTlpud1NqLzdxbG1KUVQzRys5Z3NJcEVtUjZnUzZwV2QiLCJtYWMiOiJjMDBlMzk5ZGU1MGIyNGZkYjcyOTNmYTA5YWNkYzk2NmU2MmZmZTA0NjcyNTUxZTMwOGM3MDZlNzkwMjI0NmM2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1ZcEZ4cGFBZ1BoNWZRcE95MTNXL3c9PSIsInZhbHVlIjoiZWZIVDNaUWtVQzcvVlNBR1BjTjlEZnJrTDYzUnlJSTlwaTV2NzhLTUpWSlExVG1ZaXAva3IzUDhoemRZM3FZR3RVcHowNFRZS3NyNDA1cjMzVDZFT0w5Z3JBaVNoeHErS1VSMU8yV2FJQ2psQTdtS0dNdW5weDRLSS8vZXZ2eExyT0hFWGNPSVl3c3hzVjQ1SmxOb0oyUXBteGhPVmpxdURUUElBZXA2OW53anhBL21BM0lNbHgwWGxaZjBvTnpRWHQvc0NHZXFlYk5kZUVhRmRBdkIzZzZPOHA5Q09SSGxobHVNelVtOGxlOE9YNkFjbURNRm1iSGJ0Ry9vb1g2NWluNXdqOUs0VEJ1am5GWXBoay9jUSt2RVJBWklqRTdsNTFzSXNEN3dIT1VMbWVwbENVVkhRdy9ZUmdGS1dYVlBwc3J2TmIwbVBCcFZTMTV5bFd2RDZTQ1E1YWN6empldzNYRTVtbFIwVEFiMVlCVEtOUUR2SlRNSUdEOG4vZ0ViY2NKcSt6M29wVWtueXRVVXE4cWtZYW91MnhKN1BTTFpoRkNOWkRyUnFMTkdTSCtkVHF1aHEzM0NOY0hFSS9HNncreVQyQVg0MzB2UUYxdks0Z0EwTWdncVJXa3JZa2p4Ymc3aEdaMDlPUVVEcXlLR2hsNmNVakh6eTFBVFdtZVoiLCJtYWMiOiI0Yjg0YzQ5ZjIxNTgzMGY2YWIyZmNlZjg0YzZhM2M2M2MyM2E5MjQ0OWZlM2MwOTdhZTdiM2Q3OGNmMmY5ODkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-704742734 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-704742734\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1827171903 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:19:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVxSWQ0cm4rSGlBSTdTUEZ2ZUNEbUE9PSIsInZhbHVlIjoiWmxKSk1YZFhZd0pheEEyY3c4clVIak9RcE5mRkZvT1l0R3JCRUVGZ0NLeUlYa29jNGpCM3FndHhMa0prZGQvVXYwbWlHMGVyMkUrMXBoOFEzcE5hQldRV0p0RlJWMTRHd1NaSjRvOEpiQjZTb1J1OW5ac2F3QTlMMytqeC9tU0tjMFd4TEk1ZURjbkNuV2pGY2FNU053eHZNbDV2TmtLL3huNW43WS9zOVV5MG1CaWNZZXNOWHF2Z1ppUWN6N0lvc0drdzB3elhSUjJrTTVOcm1JbUdldWd6MDB3Sm9xOWlwT3RoVi9wRVZwS0FmSFlBY3Jya1lMN3JtU3BXMW5pZ3g2L0ozcVdjd0ppWm9YUTFHSjBxQlNVUWc1cVAwY1NzVXFRVjRBaEt5SkNMSS94dDV3YWxDeGx3UVcwNnZJdS9iems5YTZEZTNUT1BZVGRQVjIvZ0lKT3pneXZHbHUxZUxFSEhTVnNQdnQ1MHZ4dEtSYUZ6MEp1V1BWVWtwOHVXakRDdlhyUjRObEZqeDN4T0lYL3ZITG5PZzBRZmRrS0Jia2NtTXQwTjQ0a0l1VGpHY2tyQitzY0VrbmI0MEN3WWVNM2o1YmdQZGd0L3AyNldndVo4ck9xN0VrdHZvUVBKb2FFSjg0ZzNFVUxibUg2bTlnVE82OEMwQ0t2dGp6eGEiLCJtYWMiOiJiOGMyOTA3NWFlOTA1M2NiMThiYmQ3OTQxNTE5M2YyNThjNWMzMDc0M2E4YTcyYjdmZDZlNzQ5NDE4MDE1YzgzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikh5SE11MUU0QyswZG1abWNXN0dObXc9PSIsInZhbHVlIjoibWY0REY4SHNCUGxjOEFqK2p0ZFdiSVFMR0lmZkVEZnZ5OTU1SUFmdmZHSHZoZjJkSnk1L3k4SWdENWczTHA3N1JPbG5UN3JMeGlFMkZpT3NvOGU0ZFpneUlmQUpJVUdpMWN3VmxxTWxHYlBVMU5oT3M0azJGYXlsMVIvQUlxQTNDOUhxZ095VDlJQ3A0MmQzYlFlTFpaeS9ySVoyQjNhd3NFWm1RbHhtME1rOFpvYVhpVldYNnBXam1wRUlpbGhwV3MrSExFM1VqM2xrdFh6aFR1Q3psWjdMZ2VzbHJsNUVjV2FIb0JzNFJqQkhwNTU2RnViV1pBTURWZXJNeWtLTStKS250RStreTl0ZlBRVnlvbEIwaGtkUmh4Q05rOXRvMFNaMHlEcW5yQlRnNWVHbW9SOW1qb0JtWW01emprczB1b2lzRXN6bXNoWjhpMXJoUEE4QzFhcjhFbU1Nbmpic3lxd3BUbis0eXkvVVNzUlM5NjkvUmFoY21WT3FJa1VCOWg1Q0YyaXNJZGNLQm5sYXFMSWxoKzZRNHk1MWJualJBaE1iZzNjcVY5VGczRnpRbWlOTUd4VHpuTzkwRExNNkIzaFdDUmlBMnhtS1RKc0Q3RWcyVCt2L2U3T2FoQ2VFaTJoalNYc0t3bDM4dzFtUnNkTXM2V1Jod0VoSnEwZkMiLCJtYWMiOiIxMDcwMzBhMGVlNzUwY2FlOGJiNjdkMWE2MTZhZDI1ZjI4NjM3YzJjNWViMWUyYjUwMDcyNGY4ZmFjMjIxMTg1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVxSWQ0cm4rSGlBSTdTUEZ2ZUNEbUE9PSIsInZhbHVlIjoiWmxKSk1YZFhZd0pheEEyY3c4clVIak9RcE5mRkZvT1l0R3JCRUVGZ0NLeUlYa29jNGpCM3FndHhMa0prZGQvVXYwbWlHMGVyMkUrMXBoOFEzcE5hQldRV0p0RlJWMTRHd1NaSjRvOEpiQjZTb1J1OW5ac2F3QTlMMytqeC9tU0tjMFd4TEk1ZURjbkNuV2pGY2FNU053eHZNbDV2TmtLL3huNW43WS9zOVV5MG1CaWNZZXNOWHF2Z1ppUWN6N0lvc0drdzB3elhSUjJrTTVOcm1JbUdldWd6MDB3Sm9xOWlwT3RoVi9wRVZwS0FmSFlBY3Jya1lMN3JtU3BXMW5pZ3g2L0ozcVdjd0ppWm9YUTFHSjBxQlNVUWc1cVAwY1NzVXFRVjRBaEt5SkNMSS94dDV3YWxDeGx3UVcwNnZJdS9iems5YTZEZTNUT1BZVGRQVjIvZ0lKT3pneXZHbHUxZUxFSEhTVnNQdnQ1MHZ4dEtSYUZ6MEp1V1BWVWtwOHVXakRDdlhyUjRObEZqeDN4T0lYL3ZITG5PZzBRZmRrS0Jia2NtTXQwTjQ0a0l1VGpHY2tyQitzY0VrbmI0MEN3WWVNM2o1YmdQZGd0L3AyNldndVo4ck9xN0VrdHZvUVBKb2FFSjg0ZzNFVUxibUg2bTlnVE82OEMwQ0t2dGp6eGEiLCJtYWMiOiJiOGMyOTA3NWFlOTA1M2NiMThiYmQ3OTQxNTE5M2YyNThjNWMzMDc0M2E4YTcyYjdmZDZlNzQ5NDE4MDE1YzgzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikh5SE11MUU0QyswZG1abWNXN0dObXc9PSIsInZhbHVlIjoibWY0REY4SHNCUGxjOEFqK2p0ZFdiSVFMR0lmZkVEZnZ5OTU1SUFmdmZHSHZoZjJkSnk1L3k4SWdENWczTHA3N1JPbG5UN3JMeGlFMkZpT3NvOGU0ZFpneUlmQUpJVUdpMWN3VmxxTWxHYlBVMU5oT3M0azJGYXlsMVIvQUlxQTNDOUhxZ095VDlJQ3A0MmQzYlFlTFpaeS9ySVoyQjNhd3NFWm1RbHhtME1rOFpvYVhpVldYNnBXam1wRUlpbGhwV3MrSExFM1VqM2xrdFh6aFR1Q3psWjdMZ2VzbHJsNUVjV2FIb0JzNFJqQkhwNTU2RnViV1pBTURWZXJNeWtLTStKS250RStreTl0ZlBRVnlvbEIwaGtkUmh4Q05rOXRvMFNaMHlEcW5yQlRnNWVHbW9SOW1qb0JtWW01emprczB1b2lzRXN6bXNoWjhpMXJoUEE4QzFhcjhFbU1Nbmpic3lxd3BUbis0eXkvVVNzUlM5NjkvUmFoY21WT3FJa1VCOWg1Q0YyaXNJZGNLQm5sYXFMSWxoKzZRNHk1MWJualJBaE1iZzNjcVY5VGczRnpRbWlOTUd4VHpuTzkwRExNNkIzaFdDUmlBMnhtS1RKc0Q3RWcyVCt2L2U3T2FoQ2VFaTJoalNYc0t3bDM4dzFtUnNkTXM2V1Jod0VoSnEwZkMiLCJtYWMiOiIxMDcwMzBhMGVlNzUwY2FlOGJiNjdkMWE2MTZhZDI1ZjI4NjM3YzJjNWViMWUyYjUwMDcyNGY4ZmFjMjIxMTg1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827171903\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}