{"__meta": {"id": "Xc814761b9daf4b0b6eb8ae05bf7df642", "datetime": "2025-06-28 16:34:50", "utime": **********.237166, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128489.781698, "end": **********.237189, "duration": 0.4554910659790039, "duration_str": "455ms", "measures": [{"label": "Booting", "start": 1751128489.781698, "relative_start": 0, "end": **********.1657, "relative_end": **********.1657, "duration": 0.3840019702911377, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.165712, "relative_start": 0.3840141296386719, "end": **********.237192, "relative_end": 2.86102294921875e-06, "duration": 0.07147979736328125, "duration_str": "71.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45714232, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02034, "accumulated_duration_str": "20.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.193869, "duration": 0.01923, "duration_str": "19.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.543}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2225568, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.543, "width_percent": 2.95}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.228604, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.493, "width_percent": 2.507}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1033078039 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1033078039\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-546340383 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128472755%7C51%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktqQkhYdlBtUDY1ZFFmR3VSYk9xdEE9PSIsInZhbHVlIjoiWXRSM2tRZkhtMEgvUkJpRmRDSGZpc2EwTVp0SUR4OE91SkIzU0JjSi9zc2laaWFveFVkbWhpSktMZkhUUVdpUktjME5GbUZBQmUwZGx5OEdCUEF1aEhlWEt4cGxvWHBNUmtFS1F3T1VMVG5QTEQrQnczeXJkQkQ1WHpPUm5qZml6VHVRTGZwZUthb0xBYmdhWmxEdm9lVTJCalZZMVhDQ3hFRUxXRDJ2NlltblhoTklFZUZiRE5NYTFUNHkyUjdBM01HZnpjcUdtTjZhOFIrZ2Z4QTR4NStvNTB4ZlhvNWRLTlI1aGp1Y2dHS01PSVdVWVpCTm5aelFJNFVDWHBWeG85WndLNXJVemlSUEtmd05GT05KMjhPanZZa3hpTjRITk4ydkRPWkVUc2taSlhJY09sMEsvcWFpb3ozZCtuVW5QWTd3MEh1RU51SGZ5YVNoOWJsbndMU1p3WmNka1A0ekxOdlFyTlFtSUp0M281cHAwYk1yWVFaT3lpY0w5Rm9XV3UxeHg5VlB6blFnbmVXb1JzaDNucWV2UGh2Uy9HTWQ3djVyZm9XL0FJU1g5TVYwenFrOGQrVXFDbFlWR3ZHaG9qYXZ4SGU2YTJTL0ZNMEIzYlFSVVNhQlIxWFd3RjJuNDFZNkRZK1oxSmpwN05HdmFuRWJ0U3lVWWc4eUh2blUiLCJtYWMiOiJkMGQ2ZmE2OWM0ZWIxNDVjM2YwMmFhMDI4NGRkNmZkYzBkMTMwOGY2NGZkNzA1MzRhOGNjNTk1ZWFiMTRhMjNhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlU4UkdocHRtaUlxbkhlUnpleWNIbEE9PSIsInZhbHVlIjoiT3dUMmhsanRFU3FWeml3ZU9mNE5hRVRwMVF2OGxZVEhaV0YxTlREWWIvaStjblJJNXVhS1FkSkk0aUhBcVBSQ21XeldvNzVZSFJDaGxUUklPY0d5anB3VmdEUUhRTXIzNjI2UlY3OGhYYmd5UHBOb25pMGdvZG1OZldUUWFDK1NnTElzQktaMUF2UWpCTUg3ZVhXUmRJY0dLNG5sQ0xtZGFPWHkvemd6WTJ0UHcydFBZdUNsSVM3UTBuUmlVUm5LbEx2NXY4NkhOeW1lclNHbUtzcVl1VU5GanpwTU1FOEszZE5IMUFHZ2p6akhBSHdvNTEzYmJoR1F3enR6VUQzRWt5TEF1aFZZNkJoeWJiOVlYU2pTU0VvcnZIY1ZMY0RlRjRpUzA0bVVEVWV2OVV4bGdkVUdBenRHTlU3UjE4STl0bGNpa2QzS2N3NWNmcWFHUzFyL3NMQVVUNEUzTHFSS1RSbklmMnBsK3VURzBZbHBlSCt3M0tGVmd2R3VUWXAyK1B3VzRBM0puS0FJRzFqRXN1YzNEVnFlUXg3KzczYThhTkx1SWFWWWVNSEdHUXdETWYxd2RsS3NhbGhWb1VwbkZiQkVFY3N0a0RTR0ptU01IZXQ1TGNGc29DcnM4YWhQZDZZaUs5dDdBc0pJWDBWeUkxNFFHa05RbWVzaUVZUlEiLCJtYWMiOiIwNzNlMjU2NGM0Yzg4Yjg1YzRiYzViYzg5ZmUzNGZhMGJlYjI2YTg0ODc5OWEzNjQ3ZDAxZjFkZmVmZDVhNDY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546340383\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1359143941 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359143941\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2103904972 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpBRmpDKytuWVhGTkZmZGR3RWVOdlE9PSIsInZhbHVlIjoiN0cyTTRja2taSmd5K0s3QUlZZWhOYXVXUmZiVjlwWmdyRDNVUXB2VkQ2U2pPUEFUK2RjL3paQXM3cE5uV0xFczJGaG5oRWMrdzluUkx6T3k3dVZvVmpzRks4ckN4UnpCUWNCOVdqeWY0ZWxUWmIyZHRncVZBQm5TenUxeXBCcVhGVDE1eExPS1FIYXl0Z01wY2p4M2JqRG44L0J5M3ppeVBOQWFUVW9KM2ZZWHY1QXFHeUtZRWVQRGhtaU45TzJvOHhXN3NwWFRSdzF0UXJERXJnK1BJbTNNWTFqOGE3MThrUWc1cFFLNkJTMnFLOHpVRW9MYVFibWdMUjhXTVlPd1E4TDI4WVNOWGlnRWVFVFdwS1JUMGRrUVVwMWlwcXppSXRRVnY5dzlpT2tOWHJIb0tnK3F3SWU0TmVjcHB5VU1BLzFWaW9YN0lrVXlKMzU2TUJRTmUyL1FVRjZGQzlWR21YeEg0K1R4QmprajlwaDdUZjVBSjk0TmsxYmc2V2FncWtwaUpNRDNOV0FoVXpYbnE1RnpPdmpWNkdzN0VXN3FMVHM5Y2ZiazErenU3aW9oNW9Ld2hXZVl2eTRXRVQ4b1hPbEdQTkdQUkR2aGtZUnRIQitYVU1vL1M0OEFZYVJlZzE2SDZlQU1taXdRL0RzdHpEa3E2REFjbGR1dXVJcTIiLCJtYWMiOiI3ZDVmOGUzY2FkMzkzNGQwZWVhYmMwN2NiZWIwMTJjODdjZmM4ZWQyOTRhYWZlNjM1MzZkYWI4MjJlNjNmYzY0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkhIbWNBdW8rT3VPUTR1OS95cElEWGc9PSIsInZhbHVlIjoiV2psWGxMdzlsdVJnRXhnVzBKWEZjd1lUUWNVckpERVZUUlZyNXMrdWN0RVZub1F3TmZBbkRGNEFKdG5leGhzUllyZEpNODVDcnMrUE1ORi9Bb3UxOGwwZWNBVHgrTWZ0VURkOEk4VmV4dmZaS0J6cDNOaUhuL05TMitTaXg2ZlEzUEIwRXVKd3g5a0k2VlZveXoway96LzNCNVNHYVZ0R3Z2SjBnaTZ0RnBPVFdBbzdGSHRiQmp6UVJIUVFMSnBEb1RwdGgwWjFQOUtlRE9lUWZWY2xzckVmSkEyUWI3V1l1c0N3QlR4QkVhWWI1S2UrMzJRMTVGSWtBT2wwbUpSOHVZRTFIRTF4UFUydFJjNi9PQUw3Zys4RTJPRHlkenNJOUJETk5iWXJlWjdFOW0zd3R5SGZlM0tBS1ZIZWtEcWYwbVBTNGhIeTlFUVZFYVlkL1BCUTRkWUhOeFY2c0w0NjhPK0FUOE9GUHBaTDNKL3BVT1M0YmNveFJlb1lUS2t5bFNheDFzaDB0SmdoQnhHd29GSDBNTFYrUVJvWk8zRGgvL2RzN2pNUUY0TDRsSXAwYUxaenBjMnlwRmRGVm5PZnFoVmxOTTBXRHlpVzk3MXNsZW5jd1cvRmRLNlRRRmQ0VndvR1QvdlFlU20wdmVYSUNodnR6UTBQT1QzUmRrcFEiLCJtYWMiOiI4ODUxMmU4NzFiNjhkYjg2YWQwZjZjY2EzZDM1ZjM3ZjEyMDE0NzZmNDAzOTAxZmMzY2I1YWJlNzVjMGM1YWNjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpBRmpDKytuWVhGTkZmZGR3RWVOdlE9PSIsInZhbHVlIjoiN0cyTTRja2taSmd5K0s3QUlZZWhOYXVXUmZiVjlwWmdyRDNVUXB2VkQ2U2pPUEFUK2RjL3paQXM3cE5uV0xFczJGaG5oRWMrdzluUkx6T3k3dVZvVmpzRks4ckN4UnpCUWNCOVdqeWY0ZWxUWmIyZHRncVZBQm5TenUxeXBCcVhGVDE1eExPS1FIYXl0Z01wY2p4M2JqRG44L0J5M3ppeVBOQWFUVW9KM2ZZWHY1QXFHeUtZRWVQRGhtaU45TzJvOHhXN3NwWFRSdzF0UXJERXJnK1BJbTNNWTFqOGE3MThrUWc1cFFLNkJTMnFLOHpVRW9MYVFibWdMUjhXTVlPd1E4TDI4WVNOWGlnRWVFVFdwS1JUMGRrUVVwMWlwcXppSXRRVnY5dzlpT2tOWHJIb0tnK3F3SWU0TmVjcHB5VU1BLzFWaW9YN0lrVXlKMzU2TUJRTmUyL1FVRjZGQzlWR21YeEg0K1R4QmprajlwaDdUZjVBSjk0TmsxYmc2V2FncWtwaUpNRDNOV0FoVXpYbnE1RnpPdmpWNkdzN0VXN3FMVHM5Y2ZiazErenU3aW9oNW9Ld2hXZVl2eTRXRVQ4b1hPbEdQTkdQUkR2aGtZUnRIQitYVU1vL1M0OEFZYVJlZzE2SDZlQU1taXdRL0RzdHpEa3E2REFjbGR1dXVJcTIiLCJtYWMiOiI3ZDVmOGUzY2FkMzkzNGQwZWVhYmMwN2NiZWIwMTJjODdjZmM4ZWQyOTRhYWZlNjM1MzZkYWI4MjJlNjNmYzY0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkhIbWNBdW8rT3VPUTR1OS95cElEWGc9PSIsInZhbHVlIjoiV2psWGxMdzlsdVJnRXhnVzBKWEZjd1lUUWNVckpERVZUUlZyNXMrdWN0RVZub1F3TmZBbkRGNEFKdG5leGhzUllyZEpNODVDcnMrUE1ORi9Bb3UxOGwwZWNBVHgrTWZ0VURkOEk4VmV4dmZaS0J6cDNOaUhuL05TMitTaXg2ZlEzUEIwRXVKd3g5a0k2VlZveXoway96LzNCNVNHYVZ0R3Z2SjBnaTZ0RnBPVFdBbzdGSHRiQmp6UVJIUVFMSnBEb1RwdGgwWjFQOUtlRE9lUWZWY2xzckVmSkEyUWI3V1l1c0N3QlR4QkVhWWI1S2UrMzJRMTVGSWtBT2wwbUpSOHVZRTFIRTF4UFUydFJjNi9PQUw3Zys4RTJPRHlkenNJOUJETk5iWXJlWjdFOW0zd3R5SGZlM0tBS1ZIZWtEcWYwbVBTNGhIeTlFUVZFYVlkL1BCUTRkWUhOeFY2c0w0NjhPK0FUOE9GUHBaTDNKL3BVT1M0YmNveFJlb1lUS2t5bFNheDFzaDB0SmdoQnhHd29GSDBNTFYrUVJvWk8zRGgvL2RzN2pNUUY0TDRsSXAwYUxaenBjMnlwRmRGVm5PZnFoVmxOTTBXRHlpVzk3MXNsZW5jd1cvRmRLNlRRRmQ0VndvR1QvdlFlU20wdmVYSUNodnR6UTBQT1QzUmRrcFEiLCJtYWMiOiI4ODUxMmU4NzFiNjhkYjg2YWQwZjZjY2EzZDM1ZjM3ZjEyMDE0NzZmNDAzOTAxZmMzY2I1YWJlNzVjMGM1YWNjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103904972\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1375539681 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375539681\", {\"maxDepth\":0})</script>\n"}}