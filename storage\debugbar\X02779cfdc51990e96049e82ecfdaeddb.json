{"__meta": {"id": "X02779cfdc51990e96049e82ecfdaeddb", "datetime": "2025-06-28 16:02:16", "utime": **********.524501, "method": "POST", "uri": "/enhanced-pos/remove-from-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.141341, "end": **********.524514, "duration": 0.38317298889160156, "duration_str": "383ms", "measures": [{"label": "Booting", "start": **********.141341, "relative_start": 0, "end": **********.474977, "relative_end": **********.474977, "duration": 0.3336360454559326, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.474987, "relative_start": 0.3336460590362549, "end": **********.524515, "relative_end": 9.5367431640625e-07, "duration": 0.049527883529663086, "duration_str": "49.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45927504, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/remove-from-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedRemoveFromCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.remove_from_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2125\" onclick=\"\">app/Http/Controllers/PosController.php:2125-2150</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00201, "accumulated_duration_str": "2.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.506398, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 82.09}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.516209, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 82.09, "width_percent": 17.91}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "[]"}, "request": {"path_info": "/enhanced-pos/remove-from-cart", "status_code": "<pre class=sf-dump id=sf-dump-1535372657 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1535372657\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1162745753 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1162745753\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-582899836 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582899836\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1158269877 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126520354%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inh2cmg0LzY0b3NJVmlSK1F3cnN1ZHc9PSIsInZhbHVlIjoiTCtuQXMvRS96SGhhQ2g1MnY5UmlqWlg3cDlUSUluMCt3YUljaytxYkJVaDdqNEdUeWhRVjU4QXZoWUpna1kxdFFlWkZZWThJZXpuQ1NHK3RvbEh3VkVrL0VWSDFxblZjSHZ4cWFTRUJ0eW1EUFl3K2lYS1VrRXQ1aGdZVm1LSUh4NnBYNzNaWmdRSGNlSXBJUERGd3JMSjJsTzNVcmNWb2luamhUV2JSbHU5TlBVQ3ZLM2N6WEdLR0ZpMG5iSGVWQ1puNWl0TCtqaGpUd0V5RERIZUl6LytHR3N6OEd4Q1RLdVNObjJtZVBrK0JvN1JGOThVV0Q1Qll6TVNtd2lRalZlanBmTVlYV3lQOTRyZkRLQkdremE1UlFhVHYzYzlTYmh5Mnd3TWJsOFFGY1pUMWh1Z0xpVUgxUmRHT2Z1RGpUNnZkQ1Mza1c3Q2FWRXNtY3lNQ1hDcmN2YzJVMElxVDBFWjlBd3poRWFWb0N5dXdGRmFjMDdxS04yTzNrVWdXSGJJcktoZG5HTi9ycXNIM09QVktMR2U3QVVuY040VE0yZmdHaGlyanpPM2ZWSlRPSnBRY2hLakhaNVgySnh1Y29mQWZRcGpEUUFYWkRSbVhXYURvQ0F4bE1POHlZWjRmd1hPek5JUStpNDBQYW13a3lXL05CZk43UnI5UitKQUYiLCJtYWMiOiJjNjM2YTI1MGYxODE0ZmNhMTJhMjQzZGQ0MzhlMjRkMTc4MmQxODc1ZTQxYmFlNjk2MjUyZmU3NWMwNWZiZjg4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlNKM0RqWUZRMkhCVEhPeG1qWHhhR3c9PSIsInZhbHVlIjoiQWF4MUdyalAzUEtSZHNjZ0dkWlMvcUdSd25sR2plQUNVTkRib2lwVGZJdy82dXJ5MGtmd2RsSnBQUUo2cGY0Ryt1U05pWmh6QS9pYTBKbVUwNTZFckJjanBXejBLSURnY2xZODJFN2NnNEpRUy9JazhZVHh6aVVaNkFMeHd0VU9mMFZqNjFxZS9TV0N2VDNNSjI5Tkp6cytpNXlWRFlFT1pjd25oT3VSRHJTTzJSTjdISEcwdTdiSTVtbHBTcnVDdklXNGVUZGxzR3RxRHRscGpqVzhUZzIwRU4vMFIxaDNWcHEzS2t1eDFpcXJBczhPQTBkelFkZ0hCeXdHV3FPUW5oRDFUaUlRSG1qb09vMVh4WEdmOEhMMlJoV1FoQ21mN1J0cW5Sd0htVDY1TFYrWDRNRnYwVUZwaTA4aFRMMko0clJlQUordWZqTzl3RzFjQi92Z0lOUlVqOWl1MlBvTXhqdnRTbVhZRkRMNHpHcnhrWFdYczJ3alYzaEM2V2pMbXgyMWFJdWRRbnRTNkVhYmpOdWRxYzl0b09qNWY3L2lUQmVaRWx1eFdsWFBvcjBXNEFPUW9YdUs0ZG9WT01mQTZSWTdDUGluK1Ixbk12Tm8wbllFa3N3RnZUaEpTYmwyc2lDZjVzRTVmWS9IdmdmQ0QvZWo1WGVCUlBROTdyL1IiLCJtYWMiOiI3OTRiYjgzZGZhZjg1YzBjMDY0NGYxNTBmNGVlMjNkM2U4NTU3M2Y5ZDMwZTdhMDRkNmRhN2NmNGE0ZmFkYTJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158269877\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1439024702 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439024702\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2012482384 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijc4K293YmtvaCswajEwRy9JemJzeVE9PSIsInZhbHVlIjoiMGlQeXEwQWkwZUlpM3krdTZJb3drSHVzS3NMaFlSYjRLeGRlRGVaZVZtU29MVkphVWxpbzdOUDlWdysydTdEU0xMQTdzRDN1TkNaMFN5djNTR1VlZHRzVVZ5czh0eWZDUmo5eWhjeXpHWDVsS0dQbFB1T3J4UXdGUHlJMm9mbDIwVXZacm43V2VBeE9XeU9LM01LU0VaVUFBNVVmV29MN2FCMEV5V25nbzlPazVjZ2hLZ0J4aFBaU0pWVHhLWGZFMm9VY0VIblFoUlY0S0pMd2lCMS9GbXZwblVZVGpOYmgrOENKWFM3cHdvMVNKK2lYR1ZaUzBxUEdOVk56RzhDWTA3M3hzZ2R4VTJaQzNLb1VSMEJuSElvZU1QZFczbUVzVmlmTHJrcm1XOXZNU0RuaW1VVUl0Wnh0ZHpFSHhLckpMSUY0WmFEcFVqVGxod2djUzE3RW5ZOHMxTkh0bjgvUVhlTkpjbHZRRnpaWFZ2VXhLL0drZEFmVERTSFJteGlxWjJpc2lydytEeUxTYmtmanlucmJ3QU5IZ0Juc3I0T0hlVG1OdW9Qc1VXVDRGeHBtVytQNHpJbWlFc3JXbFo5OE5ZR0ZZUHZyTFhPSVZEOUZtTnErRktyR0tQT3NpOG9DTGc0dlVpcmlGUFlyMGJMK0I0ZWM4TnJMM1kvVmswNzAiLCJtYWMiOiI2YjA2MWYyNWNkNDE5NzAwYmFlZWRkZjBhZmU0NGQ0ZmQwMWExNmZlODA3MDljYmM5YTBmMTJmOTVmM2I5YTBkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlU2Mk5vS3VWcm5pcG14YWxhVnFVRGc9PSIsInZhbHVlIjoiUFh6N1dudU1QNmUxQWtOY0NZMmpmMVNIVDJSSUh3bUI4bExFMkdEdmxYUnJQQU9xVW55RnZyald6TFZ6K1dKNCtLSk90M1JiQTBWTXErUXJEeWRkTExRbHR3MDZXOWt6K3RuR3pZRERia1B0cnloR3FtMDNXS2VWUjNSaWJKVXNwOGVVRTJpQWVQWTNUMW5ZL3RiZ2RrcG5QOVJlU1dsODRUam84ZTkyZklMQ0ppOGI5SHFBSUJVTEFjSEFZOXE2MXcrQmo0amh3ei9nZEw1aXlwS0RMUUhsbVkrVDFBVjA0WWVwaEwvck5ldWo0aWxMM25jVzMvL2lPak1sSWdLR3NtZmMrYTR6Q05kTitkbWo2VGh1eno3UkJLeXZtQ0NOTnlwbVNndUJCU0U3MWgwOHZUbUs3MDJXMjVWMEpRVW1FU1ZUWEFnY1Azemo0S1JPQzZ1dGVKWFJBcWFFWDBuOHZvZ1BXdlFMMkdkWW4zQnFDSUJjKzZsMFl0WTVQTk4zK0J0YnV4QURmUUpXVHROd3VZNEFsN1dQdVFhTzdpS2hvYjV5cVNOTVR4ZUdrM3Yzd1VtL2tXeWx4VmFOWDVYaGx6eFQvcjdTczNPOHBNNS9RdjErVGtOelRhMTFZbC9NakpHRVhVN1dJVTNGejFYTGIxTlYwbGZzVXhrc0JrSW0iLCJtYWMiOiJiNGY0MWZjZjc5Y2Y2OThjZWYxZDY4MjA4NzRiNmRjZDEwZGY3ZWE3OWYyNjAxNzk5MThhMDc2NzRjOWMzNmFkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijc4K293YmtvaCswajEwRy9JemJzeVE9PSIsInZhbHVlIjoiMGlQeXEwQWkwZUlpM3krdTZJb3drSHVzS3NMaFlSYjRLeGRlRGVaZVZtU29MVkphVWxpbzdOUDlWdysydTdEU0xMQTdzRDN1TkNaMFN5djNTR1VlZHRzVVZ5czh0eWZDUmo5eWhjeXpHWDVsS0dQbFB1T3J4UXdGUHlJMm9mbDIwVXZacm43V2VBeE9XeU9LM01LU0VaVUFBNVVmV29MN2FCMEV5V25nbzlPazVjZ2hLZ0J4aFBaU0pWVHhLWGZFMm9VY0VIblFoUlY0S0pMd2lCMS9GbXZwblVZVGpOYmgrOENKWFM3cHdvMVNKK2lYR1ZaUzBxUEdOVk56RzhDWTA3M3hzZ2R4VTJaQzNLb1VSMEJuSElvZU1QZFczbUVzVmlmTHJrcm1XOXZNU0RuaW1VVUl0Wnh0ZHpFSHhLckpMSUY0WmFEcFVqVGxod2djUzE3RW5ZOHMxTkh0bjgvUVhlTkpjbHZRRnpaWFZ2VXhLL0drZEFmVERTSFJteGlxWjJpc2lydytEeUxTYmtmanlucmJ3QU5IZ0Juc3I0T0hlVG1OdW9Qc1VXVDRGeHBtVytQNHpJbWlFc3JXbFo5OE5ZR0ZZUHZyTFhPSVZEOUZtTnErRktyR0tQT3NpOG9DTGc0dlVpcmlGUFlyMGJMK0I0ZWM4TnJMM1kvVmswNzAiLCJtYWMiOiI2YjA2MWYyNWNkNDE5NzAwYmFlZWRkZjBhZmU0NGQ0ZmQwMWExNmZlODA3MDljYmM5YTBmMTJmOTVmM2I5YTBkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlU2Mk5vS3VWcm5pcG14YWxhVnFVRGc9PSIsInZhbHVlIjoiUFh6N1dudU1QNmUxQWtOY0NZMmpmMVNIVDJSSUh3bUI4bExFMkdEdmxYUnJQQU9xVW55RnZyald6TFZ6K1dKNCtLSk90M1JiQTBWTXErUXJEeWRkTExRbHR3MDZXOWt6K3RuR3pZRERia1B0cnloR3FtMDNXS2VWUjNSaWJKVXNwOGVVRTJpQWVQWTNUMW5ZL3RiZ2RrcG5QOVJlU1dsODRUam84ZTkyZklMQ0ppOGI5SHFBSUJVTEFjSEFZOXE2MXcrQmo0amh3ei9nZEw1aXlwS0RMUUhsbVkrVDFBVjA0WWVwaEwvck5ldWo0aWxMM25jVzMvL2lPak1sSWdLR3NtZmMrYTR6Q05kTitkbWo2VGh1eno3UkJLeXZtQ0NOTnlwbVNndUJCU0U3MWgwOHZUbUs3MDJXMjVWMEpRVW1FU1ZUWEFnY1Azemo0S1JPQzZ1dGVKWFJBcWFFWDBuOHZvZ1BXdlFMMkdkWW4zQnFDSUJjKzZsMFl0WTVQTk4zK0J0YnV4QURmUUpXVHROd3VZNEFsN1dQdVFhTzdpS2hvYjV5cVNOTVR4ZUdrM3Yzd1VtL2tXeWx4VmFOWDVYaGx6eFQvcjdTczNPOHBNNS9RdjErVGtOelRhMTFZbC9NakpHRVhVN1dJVTNGejFYTGIxTlYwbGZzVXhrc0JrSW0iLCJtYWMiOiJiNGY0MWZjZjc5Y2Y2OThjZWYxZDY4MjA4NzRiNmRjZDEwZGY3ZWE3OWYyNjAxNzk5MThhMDc2NzRjOWMzNmFkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2012482384\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-929777764 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-929777764\", {\"maxDepth\":0})</script>\n"}}