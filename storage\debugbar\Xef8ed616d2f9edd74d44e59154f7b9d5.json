{"__meta": {"id": "Xef8ed616d2f9edd74d44e59154f7b9d5", "datetime": "2025-06-28 16:35:07", "utime": **********.495805, "method": "POST", "uri": "/enhanced-pos/process-payment", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[16:35:07] LOG.info: Saving POS Product: {\n    \"pos_id\": 1466,\n    \"product_id\": 0,\n    \"name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\",\n    \"quantity\": 1,\n    \"price\": 2.99,\n    \"total_calculated\": 2.99,\n    \"is_manual\": \"false\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.423198, "xdebug_link": null, "collector": "log"}, {"message": "[16:35:07] LOG.info: Enhanced POS - Checking inventory deduction: {\n    \"product_id\": \"2299\",\n    \"is_manual\": \"false\",\n    \"is_manual_type\": \"string\",\n    \"warehouse_id\": 8,\n    \"quantity\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.425185, "xdebug_link": null, "collector": "log"}, {"message": "[16:35:07] LOG.warning: Enhanced POS - Skipping inventory deduction: {\n    \"reason\": \"Manual product\",\n    \"product_id\": \"2299\",\n    \"is_manual\": \"false\"\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.425278, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.04065, "end": **********.495821, "duration": 0.4551711082458496, "duration_str": "455ms", "measures": [{"label": "Booting", "start": **********.04065, "relative_start": 0, "end": **********.369558, "relative_end": **********.369558, "duration": 0.32890820503234863, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.369569, "relative_start": 0.3289191722869873, "end": **********.495823, "relative_end": 1.9073486328125e-06, "duration": 0.12625384330749512, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46480360, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/process-payment", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedProcessPayment", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.process_payment", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2176\" onclick=\"\">app/Http/Controllers/PosController.php:2176-2440</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.00524, "accumulated_duration_str": "5.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4008899, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 30.725}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.410521, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 30.725, "width_percent": 7.252}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2179}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.413868, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PosController.php:2179", "source": "app/Http/Controllers/PosController.php:2179", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2179", "ajax": false, "filename": "PosController.php", "line": "2179"}, "connection": "kdmkjkqknb", "start_percent": 37.977, "width_percent": 0}, {"sql": "select * from `customers` where `id` = '10' and `created_by` = 15 and `is_active` = 1 and (`warehouse_id` = 8 or `warehouse_id` is null) limit 1", "type": "query", "params": [], "bindings": ["10", "15", "1", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2220}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.414596, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2220", "source": "app/Http/Controllers/PosController.php:2220", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2220", "ajax": false, "filename": "PosController.php", "line": "2220"}, "connection": "kdmkjkqknb", "start_percent": 37.977, "width_percent": 8.969}, {"sql": "select * from `shifts` where `is_closed` = 0 and `warehouse_id` = 8 and `shifts`.`deleted_at` is null order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["0", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2239}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.417167, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2239", "source": "app/Http/Controllers/PosController.php:2239", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2239", "ajax": false, "filename": "PosController.php", "line": "2239"}, "connection": "kdmkjkqknb", "start_percent": 46.947, "width_percent": 7.443}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2448}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2243}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4192212, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2448", "source": "app/Http/Controllers/PosController.php:2448", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2448", "ajax": false, "filename": "PosController.php", "line": "2448"}, "connection": "kdmkjkqknb", "start_percent": 54.389, "width_percent": 8.397}, {"sql": "insert into `pos` (`pos_id`, `customer_id`, `warehouse_id`, `pos_date`, `status`, `created_by`, `user_id`, `shift_id`, `updated_at`, `created_at`) values ('#POS00001', '10', 8, '2025-06-28', 1, 15, 22, 53, '2025-06-28 16:35:07', '2025-06-28 16:35:07')", "type": "query", "params": [], "bindings": ["#POS00001", "10", "8", "2025-06-28", "1", "15", "22", "53", "2025-06-28 16:35:07", "2025-06-28 16:35:07"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2251}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.421109, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2251", "source": "app/Http/Controllers/PosController.php:2251", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2251", "ajax": false, "filename": "PosController.php", "line": "2251"}, "connection": "kdmkjkqknb", "start_percent": 62.786, "width_percent": 7.824}, {"sql": "insert into `pos_products` (`pos_id`, `product_id`, `quantity`, `price`, `tax`, `discount`, `description`, `updated_at`, `created_at`) values (1466, 0, 1, 2.99, '0', 0, 'جالكسي كيك الكراميل 30جم', '2025-06-28 16:35:07', '2025-06-28 16:35:07')", "type": "query", "params": [], "bindings": ["1466", "0", "1", "2.99", "0", "0", "جالكسي كيك الكراميل 30جم", "2025-06-28 16:35:07", "2025-06-28 16:35:07"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2280}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.423423, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2280", "source": "app/Http/Controllers/PosController.php:2280", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2280", "ajax": false, "filename": "PosController.php", "line": "2280"}, "connection": "kdmkjkqknb", "start_percent": 70.611, "width_percent": 10.305}, {"sql": "insert into `pos_payments` (`pos_id`, `date`, `amount`, `discount`, `created_by`, `payment_type`, `cash_amount`, `network_amount`, `transaction_number`, `updated_at`, `created_at`) values (1466, '2025-06-28', 2.99, 0, 15, 'network', 0, 3.4385, '222', '2025-06-28 16:35:07', '2025-06-28 16:35:07')", "type": "query", "params": [], "bindings": ["1466", "2025-06-28", "2.99", "0", "15", "network", "0", "3.4385", "222", "2025-06-28 16:35:07", "2025-06-28 16:35:07"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2365}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.425726, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2365", "source": "app/Http/Controllers/PosController.php:2365", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2365", "ajax": false, "filename": "PosController.php", "line": "2365"}, "connection": "kdmkjkqknb", "start_percent": 80.916, "width_percent": 6.298}, {"sql": "select * from `financial_records` where `shift_id` = 53 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["53"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2369}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.427521, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2369", "source": "app/Http/Controllers/PosController.php:2369", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2369", "ajax": false, "filename": "PosController.php", "line": "2369"}, "connection": "kdmkjkqknb", "start_percent": 87.214, "width_percent": 6.87}, {"sql": "update `financial_records` set `overnetwork_cash` = 10.3185, `total_cash` = 118.7585, `financial_records`.`updated_at` = '2025-06-28 16:35:07' where `id` = 53", "type": "query", "params": [], "bindings": ["10.3185", "118.7585", "2025-06-28 16:35:07", "53"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2414}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.429182, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2414", "source": "app/Http/Controllers/PosController.php:2414", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2414", "ajax": false, "filename": "PosController.php", "line": "2414"}, "connection": "kdmkjkqknb", "start_percent": 94.084, "width_percent": 5.916}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2421}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.488864, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PosController.php:2421", "source": "app/Http/Controllers/PosController.php:2421", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2421", "ajax": false, "filename": "PosController.php", "line": "2421"}, "connection": "kdmkjkqknb", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/process-payment", "status_code": "<pre class=sf-dump id=sf-dump-1613119255 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1613119255\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>payment_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">network</span>\"\n  \"<span class=sf-dump-key>cash_amount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>network_amount</span>\" => \"<span class=sf-dump-str title=\"6 characters\">3.4385</span>\"\n  \"<span class=sf-dump-key>transaction_number</span>\" => \"<span class=sf-dump-str title=\"3 characters\">222</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-492544499 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">94</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128497517%7C54%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdxcGo3b1Z4aTFiS0ExYmsyQ1lERHc9PSIsInZhbHVlIjoiQW94T1Z4c3lRcVo1eUlFeDVJL3J0QTdhUVpvakgzRDNQS1BRWFRaOVVlWGtyNlZXTFNLSU9Nb3lKRmdJd0dZRytURG1RMmMzaVhkcVAwdXptQ0xIdXNuYXdpRVE0WUFQYnBmK092ZXc3TTBnczZsc3hiTTIxSHQwZDBjT2VwRXZ1ekFva3c3c2dTbG0xWmZVKzdsTlFudXpwSzNrdjNoVEpDVmdXMGNsYXVySlpad1NZc1hSaDRPWStnWWFSY0Jrb3dtMnhFcGhJMElnTnVoWEwyQXdlQzI2NlBjQ0txL2Y3RTQ2MDUva0VySlVnR0FQMzduSU1PUEVaMmxiakhKRy9scWFESTlWSktOVnpjb3NMaUhNeUlUbm1oK0JmaGJyWmdzVHVLV2owMGdwaVcrR1hIcTQ4L0VQQ1E1SitvQ2hZNlp5OHJWVFVCZ1JDV1NmaEIzdG9LYnZyeDV6WWVMazZGZm92ZkxVelptRWhVQkxEa3dTWnlqZlFMS0dhdkpZWm9jUFV5MWVaS1Noby8vR3RiWVZFZHhmd0FYV09DQ253aWkzVlU4eDFWbHFPbm9hdmZaaWtyQVV0VkRDSGtTUzdURUJNc085M0t5UmtFVHZhM1NTaTlvai84dFBuQ2hRMk00ZmllTlBWQUIzaEU4akhHWjloaUt6SnhqZnZhNWYiLCJtYWMiOiJhMzVjNDEzM2Q3YjdiYmNjMWVjN2FiMTY5NWRiYmVlZGQ1NjczYzIwZGZhOWQ1Zjc3OTNiMTgyYzA3Nzc1Njk1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlY2elpHSFNjcW1pYkhOZVJ5REw4cVE9PSIsInZhbHVlIjoiNXk5MmVpTFZES3l0R0p6Y2VIK1BDbHRtZStPeTQrOS95SkV4QSt1VytIT3FmcHhOME53NEcwWDNEb3hycm1wdUo0MXdodjdZMmRNd2hqdk5pZFVTK1kzbkd1aXg4Ni9sbFhOcnBhQzdmTjUwRGRvczErUGpQZmt2dldlQzJVdHBUNTJVK1E2ZG9Cb05xK21EeTMrOU0zTGt0MFdMYmorMGFCc3ZLS0xhQTNHTGNIQ21KL1JwYjZrdUlzVjJpQ3NpdGNsZkVhRDF0OCtreUY2WWZSZkhiWkU5RExZYVFFR2QrbVdCQ2Zjd1N4TE93K1lSRnlYVnJOcTRSMExaQzkyZFVOUjNQcm91dkEvZlF4ME11akdqUkprSERPUDdjcmpMWlB5bU52R0hyZjEwcWdCTmZwMnBJa0lVcXRxM1BsMWEzbUFnYlNqMExwVHJ2cDFyNWJDMXQzUm8xZmJiai8yK0VhMVRYV2ZLaTZiUXJ5b3daR3F2OWVleThVTSs5VGx6MUtRL0hwWENNS2Q5UjA3NUlLempkZ091Z3U2OGMwdlNmUnFIUHVuK2FvQ3hIdzBvSXF6VThkWStYNkRIVGRIQm5jcTFYcnV0bDRuN0dtSDZMYTMrR2ZtbnNsamh1bGtNVDlLTURrcEJTYzdDV3MwVGIvS3pNY0JucmlwQkdsRk8iLCJtYWMiOiJkYmVlYTVhMTE3M2Q1YWM4MWE0N2I3YzcwNDFjZjQxYThkY2I5NjA2ZjA1NmI5Y2RlN2FhZjNiZTc0NzZkZDczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492544499\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1959668147 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1959668147\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:35:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFWd0hDT0YySGp3VHBqRjRjUWFwMlE9PSIsInZhbHVlIjoieWJpSU1QdlJNa1NqUCtFUFIzdGtkY3VkNDNvckprRXVLOTA1dFJNbUdGQ3JHOUM1bGhYK1JtL3hWN3JYRENxRlFaVUVxbnVaQnZwU3M4WUFSckNyOG9Td25BaG9HWGl6dnN6QU56STBWME1kaldxdGtzZyt6ODVLS1dsZDJzK0NMSWE2RG9nTzdGemtnMnJKSkowRWN6d3VyQzhWRTFhYmxoR0JpczY5ZUNHaS9wWHV1NXJySkc2NHhXVkEwMGRSZTV5a2ovTmJnem5DeVJjelNSVEthODFQRzh4eDhuUUtReFBReFE2QzBmNEdhQjZHNkhqaXBBR1BkWUtDeWF5aW1Yc2J3YjFZL1ZwZWhvbDE4MkdKeXJjOUZuK2NzM2hBUlB5RDVwUkZBVXdMcDZtOVdJWkRSaWwvVU5ZNDRJSk4xQWNMdXFyckowS1M5R2V0bW1mZTI4ZGdFQ2pyNHNkZ2ttZE90MHNVSVh3WlVpVHNxb1BzTmZ4RFUvLzhnUnZaUEIvRkxqQ1d3V29lbFdVNE5mczVKamlyUFozeW1Xd0tnUjFpb1dBZEliVzhQazVDa1g4Wmc3T3JNVk5HRGdaK09ld1NDdGhZa3lXYUFUYTRoS3ZMd0xNbVh0ZG9IRDJXbUIrYkFvQmhhYitqQ0dva2o4Slh3enYvb1BscHRyRmkiLCJtYWMiOiJiMGFkNjAwYTI1NDg0MjgxMGVmYTFkMzFiMzhhYjVhOTdhMDVkN2QwYjUyM2IxNmU3YmE1N2FmYWUxNDYwYWYxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik82Q00xYThYbFRUS3huUXdzMGJwU2c9PSIsInZhbHVlIjoiK0NFK0lrUHBsdzF2MDVLdE03am95U3JKVFlDazdKZnJBNy9UMjlhZEFNRExEWVUveUhHalRuQWVDQ0NJR1lYMFZid2ZiWlBhVnNWVVFYZXNMQkhMMHAwWnhGWkl1RHRhMFlzRG9ocnhoUFFTM2g3VkVBb2hiRytkeUc3Qnl6dE8vZGVPc045M0M0NnpBcWl4aEZzZS9aVDA2MG9hU0t2MXd6Z2VJaU1iNVpnWVExYTVQSDY4NkR0NG5GSDVLdmFRWWJRVFhBVVgwc3JXemQ5aXBIazNCMWxmbmhUa0FTVzR4SlNqT01BN0Q5d0NINlFnY1lQWkxOS3ZZRWtHd1JBWjVWcFM5aUwwUFM5ZThIOFlScUhBUkVxUVJmTUN5UTRFR1pOT3dzZzZwVjBCTVNRTmZ5bnIxWUVPN0Y0dzZlVVhuK25wTzNDUC8rZGxTZkVtMmRVR0V6VDk0VFRjZWoxd05SSGtNd0pPcWt2aGNqakhpWGRCc2RZL1lBYkJFWGlHWFJPUk1IdWxVSE53aDJEWGJtVmJ2UUtNUW5vQ3FBQ05zbXhOT3djbm5TeTB3MytUU1pFWk5hMlRBSjhPMUR6OTUrZms4U0IvWjlLOHpCbDJRQzYva3RuN21BbjJtcUtmSmdLTFc4VWhtMUpySExyTTkydlRDYkkyaXBZM1d6T1UiLCJtYWMiOiIzYjYxY2MyNzdmZGVlZjEzY2IwYTBkNjY3YzhjMjhhODBkZjEyODY4MmQyNWY4YmY3Y2I0NDQxM2JmOTBiZjAxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFWd0hDT0YySGp3VHBqRjRjUWFwMlE9PSIsInZhbHVlIjoieWJpSU1QdlJNa1NqUCtFUFIzdGtkY3VkNDNvckprRXVLOTA1dFJNbUdGQ3JHOUM1bGhYK1JtL3hWN3JYRENxRlFaVUVxbnVaQnZwU3M4WUFSckNyOG9Td25BaG9HWGl6dnN6QU56STBWME1kaldxdGtzZyt6ODVLS1dsZDJzK0NMSWE2RG9nTzdGemtnMnJKSkowRWN6d3VyQzhWRTFhYmxoR0JpczY5ZUNHaS9wWHV1NXJySkc2NHhXVkEwMGRSZTV5a2ovTmJnem5DeVJjelNSVEthODFQRzh4eDhuUUtReFBReFE2QzBmNEdhQjZHNkhqaXBBR1BkWUtDeWF5aW1Yc2J3YjFZL1ZwZWhvbDE4MkdKeXJjOUZuK2NzM2hBUlB5RDVwUkZBVXdMcDZtOVdJWkRSaWwvVU5ZNDRJSk4xQWNMdXFyckowS1M5R2V0bW1mZTI4ZGdFQ2pyNHNkZ2ttZE90MHNVSVh3WlVpVHNxb1BzTmZ4RFUvLzhnUnZaUEIvRkxqQ1d3V29lbFdVNE5mczVKamlyUFozeW1Xd0tnUjFpb1dBZEliVzhQazVDa1g4Wmc3T3JNVk5HRGdaK09ld1NDdGhZa3lXYUFUYTRoS3ZMd0xNbVh0ZG9IRDJXbUIrYkFvQmhhYitqQ0dva2o4Slh3enYvb1BscHRyRmkiLCJtYWMiOiJiMGFkNjAwYTI1NDg0MjgxMGVmYTFkMzFiMzhhYjVhOTdhMDVkN2QwYjUyM2IxNmU3YmE1N2FmYWUxNDYwYWYxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik82Q00xYThYbFRUS3huUXdzMGJwU2c9PSIsInZhbHVlIjoiK0NFK0lrUHBsdzF2MDVLdE03am95U3JKVFlDazdKZnJBNy9UMjlhZEFNRExEWVUveUhHalRuQWVDQ0NJR1lYMFZid2ZiWlBhVnNWVVFYZXNMQkhMMHAwWnhGWkl1RHRhMFlzRG9ocnhoUFFTM2g3VkVBb2hiRytkeUc3Qnl6dE8vZGVPc045M0M0NnpBcWl4aEZzZS9aVDA2MG9hU0t2MXd6Z2VJaU1iNVpnWVExYTVQSDY4NkR0NG5GSDVLdmFRWWJRVFhBVVgwc3JXemQ5aXBIazNCMWxmbmhUa0FTVzR4SlNqT01BN0Q5d0NINlFnY1lQWkxOS3ZZRWtHd1JBWjVWcFM5aUwwUFM5ZThIOFlScUhBUkVxUVJmTUN5UTRFR1pOT3dzZzZwVjBCTVNRTmZ5bnIxWUVPN0Y0dzZlVVhuK25wTzNDUC8rZGxTZkVtMmRVR0V6VDk0VFRjZWoxd05SSGtNd0pPcWt2aGNqakhpWGRCc2RZL1lBYkJFWGlHWFJPUk1IdWxVSE53aDJEWGJtVmJ2UUtNUW5vQ3FBQ05zbXhOT3djbm5TeTB3MytUU1pFWk5hMlRBSjhPMUR6OTUrZms4U0IvWjlLOHpCbDJRQzYva3RuN21BbjJtcUtmSmdLTFc4VWhtMUpySExyTTkydlRDYkkyaXBZM1d6T1UiLCJtYWMiOiIzYjYxY2MyNzdmZGVlZjEzY2IwYTBkNjY3YzhjMjhhODBkZjEyODY4MmQyNWY4YmY3Y2I0NDQxM2JmOTBiZjAxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1360506956 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1360506956\", {\"maxDepth\":0})</script>\n"}}