{"__meta": {"id": "X3650495293ac6a6944eb83c4ea678e3b", "datetime": "2025-06-28 15:50:09", "utime": **********.595226, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[15:50:09] LOG.info: Enhanced POS Add to Cart Request: {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"price\": 2.99,\n    \"name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\",\n    \"is_manual\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.586244, "xdebug_link": null, "collector": "log"}, {"message": "[15:50:09] LOG.info: Manual product check passed {\n    \"is_manual_value\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.586422, "xdebug_link": null, "collector": "log"}, {"message": "[15:50:09] LOG.info: Product found successfully {\n    \"product_id\": \"2299\",\n    \"product_name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.588773, "xdebug_link": null, "collector": "log"}, {"message": "[15:50:09] LOG.info: Product added to Enhanced POS cart successfully {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"cart_count\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.588894, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.167283, "end": **********.595247, "duration": 0.4279639720916748, "duration_str": "428ms", "measures": [{"label": "Booting", "start": **********.167283, "relative_start": 0, "end": **********.540891, "relative_end": **********.540891, "duration": 0.373607873916626, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.540901, "relative_start": 0.37361788749694824, "end": **********.595249, "relative_end": 1.9073486328125e-06, "duration": 0.054347991943359375, "duration_str": "54.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45980320, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2015\" onclick=\"\">app/Http/Controllers/PosController.php:2015-2120</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00277, "accumulated_duration_str": "2.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.57332, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.538}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.583615, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.538, "width_percent": 20.217}, {"sql": "select * from `product_services` where `product_services`.`id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2053}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.587106, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2053", "source": "app/Http/Controllers/PosController.php:2053", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2053", "ajax": false, "filename": "PosController.php", "line": "2053"}, "connection": "kdmkjkqknb", "start_percent": 83.755, "width_percent": 16.245}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1860958354 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1860958354\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1467592849 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125486476%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhQVVV5OVdsd0JvKzI0RzhuV3VtRUE9PSIsInZhbHVlIjoiQjhlaGQ3TlAyTHRkQWlLQ2pBVEl2R1FsODhhR0JWZFJXUEVxZE0rSmtCdFF2eU1kRmVVUjl3QWtla2trN0UxejM1aFFuZm1TU3Y4OStjeTJPU3VTQjlmNHNENW03NWtiMnJsa25JcHltV2I0SHEvc3BweER3VmhmWlJCUzAwRWJmS0g1cXM2cnpKOUN2ZFR3QTMwcGRYT20xOTBWMUhVN05JTzgrM1pJakhycmpCdEFDQkZGS05iR25QNGRiQzNjbC9lOVlxdnJkenY4amFMc1c0K1g5cTVYMG1PdUdTaEtPWFRpYnozdDNpaFplQ05pSXlhOVRNOVVWKzYrMkNlbEozd09BWUJUcTVlRW9Fb3FkdlFZekE4Mkt4eDFRaTFaeG93YlplRVZMWXgzejNWaVE1T3BUdW1pdytmQm4rbCtrWS9TQ0dYV2ZPVnhYUmZaMDRyVXBULzhrb0R0Z3BsNzJSTWJDcURUM056WkhUWjM3R1FnYzd5S2REVThTRDdZM1pac25iSjF3WTVWeGNyMUlrdEEvSVRvQUgyM2hHcTBEanV3QXVpYWRyZ20yZWJxREsxMy9LR2pxQ0NRWllFVXpBZ1k4WDhvTmNybHdEdndVdmJscFA2RUh2b3pIRWJ1enlCdUw2S0hrWlF2ZEtDbzF3V1dacUc3NmJiajgzVWoiLCJtYWMiOiI4MGJiMTk4NWViNmU0ZGVjYTFmZjc2N2RjNjQ5YWU2MTgxYjAwNGFjNGEyYWJjZjA0YTcwM2VlNDllMDM4MzBmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZlOUwvK08zcVVkaEdlUHYvTzY3Qnc9PSIsInZhbHVlIjoiWHcrc21OQXhTOXJmbkNFQWFBYUZXalNGUjErZU9KQmk3bEJtVWpiRFVpVnFzZzh1RXh2WHkzK3pKeXlrdlZaY1c2Um8wcnUvL3hYMHJzQk9Wb0phUzJnUDhaS1l3ZlRIc2NGZCtZV1RranA5Z1BhazM0YlVuSjA3NyttVnVjbm1DVkJ0SVZuYWJsVzVua3lrZE5ZMFR6cEtoa01XMFF0ZWQ5V2pOYWlCbVhGSzBWNFh4Znk5WmxSV0lFc2J5cnRHdjBxTVNTbTRFdHp6WHZQM3AvcTR4Y21SNlNFQWZzdWV0QklGWXY3RG5YUTNDYUJCemhERDRzbmpOdXAvUWlXam9uWjlFblJNcFdGYjk1TTl3cXArQWtuOW5RbWFyMGMxdHBkam9FV2FSVlRlRmRpTmlXRVA5WHMvK3lZSnZXYXo1d3k1QzdnZDc3VmxMVVhqL2s3cW03RGhxdXBlcFNTUXpyajRsYlBTOFJKRUk5S013bzJKUGUyRWtJc1prM1JqL1MyUkVqMmZkczhCdDZZVEtMSE1CeGIydjFMaUVyR1JndFhEeWxuU3BsRmZ4R09vdVNNdmp4bm9NVjZtZmEzZUJMeXM3bjJTZ05SMFNFVGhZa2YrcTgzTytZVitsdlIxS2VwRFBLYkxwQUVrZC96akNCUzVpdkwyOU1pK2pMQVciLCJtYWMiOiJiNThiYTQ3OGNjZjI0ZWY5YjdhYWY0NzMxNjc2ZDY3MDc3ZTA0MTQyMzE4ODZkYjg5YmJlNTZlM2E4YjJlMDk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467592849\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-398816329 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-398816329\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-209218993 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:50:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjExOHNWamtQb0VjUXJ5ZGNmRTZOU0E9PSIsInZhbHVlIjoiS2U1REdPOW5MTmQ5RkJYY3J1ZHZ3SUpCUjJzK2NqK2JGcjVWNkpvdGgvaEs2T1BzS3QyMkhNN2xkUnNTOXhhUk9IN1BVWm9WMFg4RkhaRXJJU1dTU2tjYlE1R1ozNlkrRC92UndUNXdrOFZnWU8vV0JEcm9DVXVTSTYwU29QODNUa3lZQlZmUzgvU2NoYWRvWUxYdXYwTWdsajR4blJhMG9lSk9MNEVzTDc2a3JCeFlsN25pMDFjMnlyZjVSdTBQMHBid2lnTHFnZ0xRbDJvMDFhNUNqcHlIc1czNHNzZ01nWXZLT1dVYy9CVUtlZ25UUm5nYW9CZnliSjgrSyt2ZzNnclYyZUJCdXJKMjB0RDdOM1k3RWRNWXZPVll6dDZ2Q2FubGUvRXBjcUFTQ1R4MVZxQzJUNzYvMWs2a3paNlV3UDZWbFhBODRJV3ZYdStYU2FFOERzZnI5akNuRU0yaVV4RGFFdjFSQXp5Yll1Q1BJaVpuY1o5U1hZV25zNUpWUkYxNkkxc3UyZWJNcURPNERHSzJ3Wk51VTFramVXWWRJZHdWOVBkZVdvU2JNa0pta3RRS2xGTXQybTBsS25Iclo5R1M0a2Mvb1RoeWNNUzFUblpMTnltZlEwMVdnc3NsdUwvVEJBWEJIV0dVb0RBMkV1cTZDR2ZWQnhSZ3NkVXMiLCJtYWMiOiJhNTc0NzIwOTljZjhiMzZiZmE5M2I1ZWI2ZGE2ZWIwOTEzNWViM2JkOTVkYmJlY2RiMGNlYmUwYzNhOWIxMDIyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:50:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InVmSTFTZFNXRHk3SVJzU0VEcTJSekE9PSIsInZhbHVlIjoiaFY2T09NcjRSc0JtQmdDSk54U1JkSi9vbWc3bTZ5d05XcC9oQXBaeXc4QWMyNDFSanFRMFRHUURVMEJpSTlWNkgzNUY1ZmRzblA1cGQzenlnektFaTBDSnV2SjU4QjdWTTN3b2dXaGRxTnZKRDFISnFDNmVDbWg2a1g1WVJxZ2RlcUVSS1o0SnhHWkpCdDZTT2J1aExIK2lhT1p1SlBrZ0pNOHVqdmVlL09DeHZRWU5oSzlDR1dWNTdvaUJGRjF5QTdJTGlHNjZQcHY2TXhueksyelR1TW5Na1BXM0xjaHRBb0J1SXBXNlJkNDcrMU82SktqZ3I2YWozL0RMQ0U2V3V2RFZtRjY5QXVub0FxVElYbmxaQUI5U3Nmd0psQTFuUVJtRUJLNTVDUFdsS1ZzdVIwZU16MWdjTnZ5NnZwUXhtRkg4VkxHUWozVlpWSjJkd3NJTUhmTTBCMzVkd2ZRamhDK0lQTUhhTnZJTk8rYlhEblRwNmtqaVVNcGtrRFlERGIzbWIxTTFBaUZhZnJ6TjNCK2dHeVhnajhSUU1HWEVBRGNwZjZETVp2MHdFSi9yOWJ4dzBEdXIyeU91SnJEVk5xV1kvMTNKTzgwRUdKQVVTdWY2RTRoVy94b2FOUFpkQXVtazJWN0tQWEVVQTNjdllRZXN5NHFQYkJCbVY0azYiLCJtYWMiOiI5NTQ4ZDVmMWI0NjI1ZjI3NDZjZTdhNWRmNTM0OGJlMjAxMWJjYjA3Mzc5YThjZGM2ZmY0MTdkZDI2Y2QyNTA3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:50:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjExOHNWamtQb0VjUXJ5ZGNmRTZOU0E9PSIsInZhbHVlIjoiS2U1REdPOW5MTmQ5RkJYY3J1ZHZ3SUpCUjJzK2NqK2JGcjVWNkpvdGgvaEs2T1BzS3QyMkhNN2xkUnNTOXhhUk9IN1BVWm9WMFg4RkhaRXJJU1dTU2tjYlE1R1ozNlkrRC92UndUNXdrOFZnWU8vV0JEcm9DVXVTSTYwU29QODNUa3lZQlZmUzgvU2NoYWRvWUxYdXYwTWdsajR4blJhMG9lSk9MNEVzTDc2a3JCeFlsN25pMDFjMnlyZjVSdTBQMHBid2lnTHFnZ0xRbDJvMDFhNUNqcHlIc1czNHNzZ01nWXZLT1dVYy9CVUtlZ25UUm5nYW9CZnliSjgrSyt2ZzNnclYyZUJCdXJKMjB0RDdOM1k3RWRNWXZPVll6dDZ2Q2FubGUvRXBjcUFTQ1R4MVZxQzJUNzYvMWs2a3paNlV3UDZWbFhBODRJV3ZYdStYU2FFOERzZnI5akNuRU0yaVV4RGFFdjFSQXp5Yll1Q1BJaVpuY1o5U1hZV25zNUpWUkYxNkkxc3UyZWJNcURPNERHSzJ3Wk51VTFramVXWWRJZHdWOVBkZVdvU2JNa0pta3RRS2xGTXQybTBsS25Iclo5R1M0a2Mvb1RoeWNNUzFUblpMTnltZlEwMVdnc3NsdUwvVEJBWEJIV0dVb0RBMkV1cTZDR2ZWQnhSZ3NkVXMiLCJtYWMiOiJhNTc0NzIwOTljZjhiMzZiZmE5M2I1ZWI2ZGE2ZWIwOTEzNWViM2JkOTVkYmJlY2RiMGNlYmUwYzNhOWIxMDIyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:50:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InVmSTFTZFNXRHk3SVJzU0VEcTJSekE9PSIsInZhbHVlIjoiaFY2T09NcjRSc0JtQmdDSk54U1JkSi9vbWc3bTZ5d05XcC9oQXBaeXc4QWMyNDFSanFRMFRHUURVMEJpSTlWNkgzNUY1ZmRzblA1cGQzenlnektFaTBDSnV2SjU4QjdWTTN3b2dXaGRxTnZKRDFISnFDNmVDbWg2a1g1WVJxZ2RlcUVSS1o0SnhHWkpCdDZTT2J1aExIK2lhT1p1SlBrZ0pNOHVqdmVlL09DeHZRWU5oSzlDR1dWNTdvaUJGRjF5QTdJTGlHNjZQcHY2TXhueksyelR1TW5Na1BXM0xjaHRBb0J1SXBXNlJkNDcrMU82SktqZ3I2YWozL0RMQ0U2V3V2RFZtRjY5QXVub0FxVElYbmxaQUI5U3Nmd0psQTFuUVJtRUJLNTVDUFdsS1ZzdVIwZU16MWdjTnZ5NnZwUXhtRkg4VkxHUWozVlpWSjJkd3NJTUhmTTBCMzVkd2ZRamhDK0lQTUhhTnZJTk8rYlhEblRwNmtqaVVNcGtrRFlERGIzbWIxTTFBaUZhZnJ6TjNCK2dHeVhnajhSUU1HWEVBRGNwZjZETVp2MHdFSi9yOWJ4dzBEdXIyeU91SnJEVk5xV1kvMTNKTzgwRUdKQVVTdWY2RTRoVy94b2FOUFpkQXVtazJWN0tQWEVVQTNjdllRZXN5NHFQYkJCbVY0azYiLCJtYWMiOiI5NTQ4ZDVmMWI0NjI1ZjI3NDZjZTdhNWRmNTM0OGJlMjAxMWJjYjA3Mzc5YThjZGM2ZmY0MTdkZDI2Y2QyNTA3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:50:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209218993\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1204707651 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1204707651\", {\"maxDepth\":0})</script>\n"}}