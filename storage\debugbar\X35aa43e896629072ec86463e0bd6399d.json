{"__meta": {"id": "X35aa43e896629072ec86463e0bd6399d", "datetime": "2025-06-28 16:34:50", "utime": **********.215611, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128489.781698, "end": **********.215626, "duration": 0.4339280128479004, "duration_str": "434ms", "measures": [{"label": "Booting", "start": 1751128489.781698, "relative_start": 0, "end": **********.160248, "relative_end": **********.160248, "duration": 0.37855005264282227, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.160258, "relative_start": 0.37856006622314453, "end": **********.215628, "relative_end": 1.9073486328125e-06, "duration": 0.05536985397338867, "duration_str": "55.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45731408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029400000000000003, "accumulated_duration_str": "2.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1898031, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.388}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.200518, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.388, "width_percent": 13.265}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2065551, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.653, "width_percent": 17.347}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1039423364 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1039423364\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1914816356 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1914816356\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1375585868 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375585868\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1199702723 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128472755%7C51%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktqQkhYdlBtUDY1ZFFmR3VSYk9xdEE9PSIsInZhbHVlIjoiWXRSM2tRZkhtMEgvUkJpRmRDSGZpc2EwTVp0SUR4OE91SkIzU0JjSi9zc2laaWFveFVkbWhpSktMZkhUUVdpUktjME5GbUZBQmUwZGx5OEdCUEF1aEhlWEt4cGxvWHBNUmtFS1F3T1VMVG5QTEQrQnczeXJkQkQ1WHpPUm5qZml6VHVRTGZwZUthb0xBYmdhWmxEdm9lVTJCalZZMVhDQ3hFRUxXRDJ2NlltblhoTklFZUZiRE5NYTFUNHkyUjdBM01HZnpjcUdtTjZhOFIrZ2Z4QTR4NStvNTB4ZlhvNWRLTlI1aGp1Y2dHS01PSVdVWVpCTm5aelFJNFVDWHBWeG85WndLNXJVemlSUEtmd05GT05KMjhPanZZa3hpTjRITk4ydkRPWkVUc2taSlhJY09sMEsvcWFpb3ozZCtuVW5QWTd3MEh1RU51SGZ5YVNoOWJsbndMU1p3WmNka1A0ekxOdlFyTlFtSUp0M281cHAwYk1yWVFaT3lpY0w5Rm9XV3UxeHg5VlB6blFnbmVXb1JzaDNucWV2UGh2Uy9HTWQ3djVyZm9XL0FJU1g5TVYwenFrOGQrVXFDbFlWR3ZHaG9qYXZ4SGU2YTJTL0ZNMEIzYlFSVVNhQlIxWFd3RjJuNDFZNkRZK1oxSmpwN05HdmFuRWJ0U3lVWWc4eUh2blUiLCJtYWMiOiJkMGQ2ZmE2OWM0ZWIxNDVjM2YwMmFhMDI4NGRkNmZkYzBkMTMwOGY2NGZkNzA1MzRhOGNjNTk1ZWFiMTRhMjNhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlU4UkdocHRtaUlxbkhlUnpleWNIbEE9PSIsInZhbHVlIjoiT3dUMmhsanRFU3FWeml3ZU9mNE5hRVRwMVF2OGxZVEhaV0YxTlREWWIvaStjblJJNXVhS1FkSkk0aUhBcVBSQ21XeldvNzVZSFJDaGxUUklPY0d5anB3VmdEUUhRTXIzNjI2UlY3OGhYYmd5UHBOb25pMGdvZG1OZldUUWFDK1NnTElzQktaMUF2UWpCTUg3ZVhXUmRJY0dLNG5sQ0xtZGFPWHkvemd6WTJ0UHcydFBZdUNsSVM3UTBuUmlVUm5LbEx2NXY4NkhOeW1lclNHbUtzcVl1VU5GanpwTU1FOEszZE5IMUFHZ2p6akhBSHdvNTEzYmJoR1F3enR6VUQzRWt5TEF1aFZZNkJoeWJiOVlYU2pTU0VvcnZIY1ZMY0RlRjRpUzA0bVVEVWV2OVV4bGdkVUdBenRHTlU3UjE4STl0bGNpa2QzS2N3NWNmcWFHUzFyL3NMQVVUNEUzTHFSS1RSbklmMnBsK3VURzBZbHBlSCt3M0tGVmd2R3VUWXAyK1B3VzRBM0puS0FJRzFqRXN1YzNEVnFlUXg3KzczYThhTkx1SWFWWWVNSEdHUXdETWYxd2RsS3NhbGhWb1VwbkZiQkVFY3N0a0RTR0ptU01IZXQ1TGNGc29DcnM4YWhQZDZZaUs5dDdBc0pJWDBWeUkxNFFHa05RbWVzaUVZUlEiLCJtYWMiOiIwNzNlMjU2NGM0Yzg4Yjg1YzRiYzViYzg5ZmUzNGZhMGJlYjI2YTg0ODc5OWEzNjQ3ZDAxZjFkZmVmZDVhNDY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199702723\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2110771345 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110771345\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1538951418 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImgvMjl1cEQ1UFpPVVRKL3RKYUdLeGc9PSIsInZhbHVlIjoiNy82RFl1MnVibnlXK2huOGNYdmhHdmp0OXZXbjlDRjB2U0lGSFNIeU1pcnhZVVNWeDBvLzdkV0ZWTEI0VVJRbmpURWdwcjdQR3RNOEh0cnQ5SzFiNklORDRVQjBDV1BnOGllWXpBUU1ISDJ3WTRkTDJzZGl4MFdsTVpVb1ovZnZVeHZQU21uUURFMjFWZS9oNWNGbTVnRnpQYlRTaHdvTkFQV1FLOUZzU2RMNEZFdDVwT1NVNkdwdmR1ZVFOVERXSUIwR1hZRGlpWFN2Y3dOY2xIN3M1S09hNXNhWThuSXJ5Y2p1T0Vsb2NSREZDUC9ldjdSYWNXdFdtblljRjRTNnNISXhGRTJIaEd0aCsvQWhtZXRSZlRLYUtpL2cxY3B1RytGVDF5NEloRWl1K1hFZzd6Tnp2dTdsWkJyTDNLR1dCYUFhalc1amNwMWFmbVlKYytwVm1aTUdmWmJaeDN0SDJhTnBWRDY5c25WOWszb0pTTTVuZjFtSDRaeGdMaVh3bUZHakhPdWM5Nk10Sms0N21OTGxGK3VCdVpRMXY4M01ZdUdOb01NeWYzYzV4c3A1TWFKUTArdW5ISkI3R2wzdVg4VDRMa1N4UFZDaElvcGwxMkg3Nk4zeTZuc1YwdUU0anZzVUdQak0xOHFvM1lBSnV3bWZhdkhncmR1QytHY3giLCJtYWMiOiJjNDQyYWQ1Mjk4MmQ1MzUyNGI5Yzk4MGU3ZDIxOGRhYzFlOTExMjgxYzFlZjMzYzFhYWJjMmEzNWQ1ODg4ZjQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVNTGZsejE1RXZwazZwWGVNcEQ2SkE9PSIsInZhbHVlIjoiS0YydkM4ZHl0cHpQTGV1WmU0dkJYUkpVOGdPL09aUTJLSHZvVVhwRElOS0gwNTdkR2lBQ2hNT3krYnZiaWVieWwzbGhubjljSnQ4ZFBrdFlGd25Yalp1QXFPMXc1WmpsUC9uQm4weGk4Q2hNVksrdTN2VGFhcTBYdnVNNHBxOExCUHk0M0tnQ2t5NHhiNEU3ME0rcmVFRjNxUHJUaWJXWEErcHV4bWV6TDFaRzVtc0VvNFY2Umkwek5nSVVIZXhYQ0JXcEJjWXR1WXZLYU9Ma3ZNckQxMHNTUjNQek5kaEUzZzA4ZDJQa0U4am1UYTl4VTViOUVSeDlUTWVQb1QvSk0xczJucDhSbWFaQms3UE9KNE1hUzVKMEVHUUxIWldZNFdkYW5qTlQzSldjOEkvNWNFZmZJdUNYK20zQ2pJYUFuWEtPOFBtRUcwUVJLK2t6NFd1R1FSQjkzb3o5QWhYK3o2ckM0UTdEMjFwSjEwRHRrUHp2dElYdkNLNVAxeFdKYzVDYWQrYzFGWXcwVkw5dWEzMFBzQ2oydmkyRXMrQndIaHY5elR5a3RXdjJiWU9ucEVrN2xWMjR1bFlYQlZPR2V5WkZzVTNtZndOUEh5Q2d2UXdUbDZaemYzWmhsNGJJdUwxSUNIWjRraytRTlNuTWtDb0c0OXJZR08rS0M3SHQiLCJtYWMiOiIwYzgzZDY4Y2QzMTU2YmUxZmY3Mzg0MmFkY2MxOGUwMDE1ODUzM2UxMWM0MTIxOTdhYjY5MzJjOGEwM2UwOTU0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImgvMjl1cEQ1UFpPVVRKL3RKYUdLeGc9PSIsInZhbHVlIjoiNy82RFl1MnVibnlXK2huOGNYdmhHdmp0OXZXbjlDRjB2U0lGSFNIeU1pcnhZVVNWeDBvLzdkV0ZWTEI0VVJRbmpURWdwcjdQR3RNOEh0cnQ5SzFiNklORDRVQjBDV1BnOGllWXpBUU1ISDJ3WTRkTDJzZGl4MFdsTVpVb1ovZnZVeHZQU21uUURFMjFWZS9oNWNGbTVnRnpQYlRTaHdvTkFQV1FLOUZzU2RMNEZFdDVwT1NVNkdwdmR1ZVFOVERXSUIwR1hZRGlpWFN2Y3dOY2xIN3M1S09hNXNhWThuSXJ5Y2p1T0Vsb2NSREZDUC9ldjdSYWNXdFdtblljRjRTNnNISXhGRTJIaEd0aCsvQWhtZXRSZlRLYUtpL2cxY3B1RytGVDF5NEloRWl1K1hFZzd6Tnp2dTdsWkJyTDNLR1dCYUFhalc1amNwMWFmbVlKYytwVm1aTUdmWmJaeDN0SDJhTnBWRDY5c25WOWszb0pTTTVuZjFtSDRaeGdMaVh3bUZHakhPdWM5Nk10Sms0N21OTGxGK3VCdVpRMXY4M01ZdUdOb01NeWYzYzV4c3A1TWFKUTArdW5ISkI3R2wzdVg4VDRMa1N4UFZDaElvcGwxMkg3Nk4zeTZuc1YwdUU0anZzVUdQak0xOHFvM1lBSnV3bWZhdkhncmR1QytHY3giLCJtYWMiOiJjNDQyYWQ1Mjk4MmQ1MzUyNGI5Yzk4MGU3ZDIxOGRhYzFlOTExMjgxYzFlZjMzYzFhYWJjMmEzNWQ1ODg4ZjQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVNTGZsejE1RXZwazZwWGVNcEQ2SkE9PSIsInZhbHVlIjoiS0YydkM4ZHl0cHpQTGV1WmU0dkJYUkpVOGdPL09aUTJLSHZvVVhwRElOS0gwNTdkR2lBQ2hNT3krYnZiaWVieWwzbGhubjljSnQ4ZFBrdFlGd25Yalp1QXFPMXc1WmpsUC9uQm4weGk4Q2hNVksrdTN2VGFhcTBYdnVNNHBxOExCUHk0M0tnQ2t5NHhiNEU3ME0rcmVFRjNxUHJUaWJXWEErcHV4bWV6TDFaRzVtc0VvNFY2Umkwek5nSVVIZXhYQ0JXcEJjWXR1WXZLYU9Ma3ZNckQxMHNTUjNQek5kaEUzZzA4ZDJQa0U4am1UYTl4VTViOUVSeDlUTWVQb1QvSk0xczJucDhSbWFaQms3UE9KNE1hUzVKMEVHUUxIWldZNFdkYW5qTlQzSldjOEkvNWNFZmZJdUNYK20zQ2pJYUFuWEtPOFBtRUcwUVJLK2t6NFd1R1FSQjkzb3o5QWhYK3o2ckM0UTdEMjFwSjEwRHRrUHp2dElYdkNLNVAxeFdKYzVDYWQrYzFGWXcwVkw5dWEzMFBzQ2oydmkyRXMrQndIaHY5elR5a3RXdjJiWU9ucEVrN2xWMjR1bFlYQlZPR2V5WkZzVTNtZndOUEh5Q2d2UXdUbDZaemYzWmhsNGJJdUwxSUNIWjRraytRTlNuTWtDb0c0OXJZR08rS0M3SHQiLCJtYWMiOiIwYzgzZDY4Y2QzMTU2YmUxZmY3Mzg0MmFkY2MxOGUwMDE1ODUzM2UxMWM0MTIxOTdhYjY5MzJjOGEwM2UwOTU0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538951418\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}