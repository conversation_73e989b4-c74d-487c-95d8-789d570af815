{"__meta": {"id": "X9598d69eb648f4dafecd42a5598750ac", "datetime": "2025-06-28 11:25:38", "utime": **********.018784, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109937.648876, "end": **********.018796, "duration": 0.369920015335083, "duration_str": "370ms", "measures": [{"label": "Booting", "start": 1751109937.648876, "relative_start": 0, "end": 1751109937.969029, "relative_end": 1751109937.969029, "duration": 0.32015299797058105, "duration_str": "320ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751109937.969038, "relative_start": 0.3201620578765869, "end": **********.018798, "relative_end": 2.1457672119140625e-06, "duration": 0.04976010322570801, "duration_str": "49.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45782088, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1973\" onclick=\"\">app/Http/Controllers/PosController.php:1973-2026</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00191, "accumulated_duration_str": "1.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.000856, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 82.199}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.010551, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 82.199, "width_percent": 17.801}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"449\"\n    \"name\" => \"سكيتلز - حلوى فواكه، 10 جرام\"\n    \"price\" => 1.0\n    \"quantity\" => 8\n    \"total\" => 8.0\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1925215863 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">449</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">&#1587;&#1603;&#1610;&#1578;&#1604;&#1586; - &#1581;&#1604;&#1608;&#1609; &#1601;&#1608;&#1575;&#1603;&#1607;&#1548; 10 &#1580;&#1585;&#1575;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925215863\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">194</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IkpGdXFXNklPV0tBeTZiRGdQM1lPRGc9PSIsInZhbHVlIjoibjFIVFJ0VmtTT2owWmNkM0JZTjVMRGFRS0VWOHZRaE83OCs2Y1JRNkJqdW1PZVpjaE9GUlhMZjBFT1dLOTJrY3kxb0ptMkp3NllnNzdGa25YMTB6elBRRW82RlpGNkJqc25hNFp5QmRUbnBQK0NneHpYV2U3d2E3ZkJTUXJsaTBRalI3OFNiMnhOM3VsTHNBRGdsODl6YUdCQTdvMnNiMTZ1ZTNHRzdrUE1uMUJtUUhkVFMxQUdRZC9xNUtnQzdtMHB4R0hmSnFrR2lnNnNpeHRsNEhIU1J3dmFSc3YwNFVNQ2dXZ2RjdVljYXdpbHQrVkxJU3hPclVNNUVPcjZPTnI5VGphcVlLeHdTQlJQR2p2YS8zRUtWNStjU3QxNmo3Q0RZY05NZ2p0My82T3BqZFlGY3FsSkxjRWpKaS9Ia01BT1J5dUM4WDFXbU1FRjNFb0hsblVuSUhXMzhGSmJHcG5uN2NJMGFmMERpeTFhWmxkS0k3YjdMaFdyc1h1NWRWTU96Zjc0YWxOa25XV2dBTVl3eXRLMVVmQnZoZ0pUOTIyTFFWbVZ3MTRGQXczalBqWXBSbTMxUWxlVjRYVnp3TkViSTlDSDRhTnY3dUl1SlN3OWlVMzMwWWVZellqd1hoU3hER29JanA5Nk53aW94ZlVhVFF5RHJtNzNBejltbmYiLCJtYWMiOiI5MTM3NWJlZWQxNWQ2OGU2YTE3ZjgzZWNiNzcwZjVhYTIzYmZlYWVjNDFhZDhlZmE1YjlmZGRhZTJmZjIxZjZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9Idmx0Ti9wSFJraXhYNTBzTU1EcXc9PSIsInZhbHVlIjoiY3NQVVBCOEZwaHZUQTJSUk5UdEFQeUJsa3VCcjAxeGJRVzZxckQvdjNXNzI4ZDhlT1orc1psMnFkTjQzZDBFV2JDRnljNE9ROE96dTRjcjY4dzBaZzNIZGdhdmZud2dERG1EMnhQYldYdytKYXY4dzErakE1TXZtYVB6eFo0ZTUxNmo2RTlWWGdDSW9oRzZFWGtkU3BMcFdVMHJIK3B0Ni85czYrUDBYYVBPdUJETnNRWjlUaVJDY2pQZENVTDhBcnJRM0twbGRDN0NDRHR0Q0U1UFliMVprZnFpR3BTRVJTMW9XMFExUDZXcy9keXBGbXZwWTNUUlVwVnNFUlE3OE85L0VoVE1qMVI3UklRL1RJOVFMUnBJSkhudVZJdjlnVHRKSnpGOThBaHNrT1RUWjZPT01YK2RobWo3OE9kR3FFaDBVdHNEOFpCVnEwNzQ0d3RIMGttUkJmYlZsZ2NMNGJoNlBmZHZsTTNxMC9OQlR0eHBjQ1RQSU00RlU4WE5xZWdaUkR3QkRvWk5RbTB2SzFzWko4NzhTU1RvYmRwb1lqblJ6V1hoOXIrS1E3Smd2bzQ1cmxXQThqTm9hd0ltSXZzQnBRNFkzY054UDd1R3lYZU10R3E2dXYxTkI5S0VjWDJYclRUYkFIZzhqcjJBb3I0UGZtZFdBRWdTYlNNajMiLCJtYWMiOiJkZTE1MzNlNzRkOWU0YzM5MmI2ZjQ4ZDE1OGI0NDYyN2RmMTY3ZDk4ZTc2Mjc2NTA4ODE0NDEwYmZjZjIyOGM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1369250317 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369250317\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1761130807 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:25:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJPTkhsMVRoZzNWQ3hKd2x5SUdtc2c9PSIsInZhbHVlIjoiT0llOGowY1I4RWNzTW1NMitZc2dBcGFZZ1c5QTJOdjRLVS96REJjTlBXRUVLRGwvUXV3VUcyWEg3SlB2cjlLM3ZrT282d29jYzJQZjFGRnJ5aE9EbkdoRmg0TERlcy81MzkwK0FVYjFacnZTRG5FbFFBakZTWC9mSEVycENSQWlHWmU1c0tNcFdYbFIyaUZlTzZaL2Q5NlB3NEJ1dlNyeEoxSGZMTnA4MmFaWjl4aHVjMWlidDRyNU10WmlENGl0aEViRHNlMElkajdOcERLQlMwZVptTjQ2T1lvWkxsVmhSNlpZK0xWZ2Ixc0VsTmhSa0o5UVRON00wUFRtQjlFdHpXMFJEdTRpbm1pdERkV0RHcitLOWpLN2JlVGJFV043ZS9VWG5seWdHblQxSDZ4Uk1rbml3NVU5VGxUazhDZzVPVDdBTDcwTmU2bEF0THl6MUdWdEFOQ1AwMmV2M0NZOEVhNWVmbGxWYUxVOFZ4eFdjaW5MRVJXS3d3ZGxtc3g0SWVTSWtPRDhGd1RJaWZ4R09FeXNSYWl6M3VveHdvTzJzdTdIOEpqZnUydHVOQlZka2YrMDJFQ0wvQ21XQkRNTDcxZEc1NUVMU0hYTUlxenRQZWwxUTVhS1RXWlVaWmQwZGNFZkYzQjd1d3F2KzFDN2owV1o5cTRYNE1vbHFSaGQiLCJtYWMiOiIyNGM3NGY5MDk1YzQyMjU0ZjAyZjQwOTVhZWZhY2M1MGFmMGE4MTI4NTc4Zjk1YWJiODJkYzczMDYxZTE0ZjMxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:25:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkduQUF4bHk5OTVURERuck11bk9vSHc9PSIsInZhbHVlIjoiYmZtMlY3Z3RyN25GVnNHRlVuc2E0QU1OQXltbmR5bURoTEVLUHlSdHZRcGt5VzBsZ2RlemlWUFl2QjBoWmlzNFZSV3d6UkVHSU11RUFhRm1mbUpuOURGVE5LcWxBTVFaZkdocllrQ3l2eFEvalJGN2M1dkZZcFI1TWcxbHcrVUpDbDlBOFRySlhvbE94VDdFQ2JLRE1sZjNWaXpHbzdBcTV4cnFDMGl4bVNXWkhGbHlTN1pjSFFZNXk2Y1JRRzZseXRId1VDUFhiMXUzYlVDOWVQK0sxTUQzczJjZUc1VmprWUQ3Wjc5UlYxdjdvTnAwcXBFWVhYaDFEWGNUc2xhM3RjZVBWS1NYZ0JJY1BuREtqcEtHWHZhNUMrRXBMQ3BhS2lyUE9NUzBuZmNOSXM3dXcyV3Z1bFhlbU8xdGxFbmpraDdSQzR6a1M3VXF1V2NKcHdUanBFRml1MDNsUGNqWkU2T1pVa3FnekZyUGhMQUVCdmZJaDhlVisxRmlQMnB3R1pCVUdxd0t3dWxYSDdzRFErVC9CRFQ1OUNVKzlDcjJ0dys4YzlNbktoQzV1NDMvUnFSZ04rQVVBeTdVQnFQaTM5Zzg0c056eit0UU8vbXFEQTY2K2RsOGo5QXdlY1dPbGo1K2FoQmNmRmhkaUFZL3k0N0VYMnJubFkzU084NmsiLCJtYWMiOiIyYTBmYzgwM2RkYjQ1NTI0NzEyNDIxNTYwMTBhYWU0ZTdjZjc3NmE2ZmFlNTJmYzU0ZjZlYzI5M2Y2MTIwODFiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:25:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJPTkhsMVRoZzNWQ3hKd2x5SUdtc2c9PSIsInZhbHVlIjoiT0llOGowY1I4RWNzTW1NMitZc2dBcGFZZ1c5QTJOdjRLVS96REJjTlBXRUVLRGwvUXV3VUcyWEg3SlB2cjlLM3ZrT282d29jYzJQZjFGRnJ5aE9EbkdoRmg0TERlcy81MzkwK0FVYjFacnZTRG5FbFFBakZTWC9mSEVycENSQWlHWmU1c0tNcFdYbFIyaUZlTzZaL2Q5NlB3NEJ1dlNyeEoxSGZMTnA4MmFaWjl4aHVjMWlidDRyNU10WmlENGl0aEViRHNlMElkajdOcERLQlMwZVptTjQ2T1lvWkxsVmhSNlpZK0xWZ2Ixc0VsTmhSa0o5UVRON00wUFRtQjlFdHpXMFJEdTRpbm1pdERkV0RHcitLOWpLN2JlVGJFV043ZS9VWG5seWdHblQxSDZ4Uk1rbml3NVU5VGxUazhDZzVPVDdBTDcwTmU2bEF0THl6MUdWdEFOQ1AwMmV2M0NZOEVhNWVmbGxWYUxVOFZ4eFdjaW5MRVJXS3d3ZGxtc3g0SWVTSWtPRDhGd1RJaWZ4R09FeXNSYWl6M3VveHdvTzJzdTdIOEpqZnUydHVOQlZka2YrMDJFQ0wvQ21XQkRNTDcxZEc1NUVMU0hYTUlxenRQZWwxUTVhS1RXWlVaWmQwZGNFZkYzQjd1d3F2KzFDN2owV1o5cTRYNE1vbHFSaGQiLCJtYWMiOiIyNGM3NGY5MDk1YzQyMjU0ZjAyZjQwOTVhZWZhY2M1MGFmMGE4MTI4NTc4Zjk1YWJiODJkYzczMDYxZTE0ZjMxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:25:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkduQUF4bHk5OTVURERuck11bk9vSHc9PSIsInZhbHVlIjoiYmZtMlY3Z3RyN25GVnNHRlVuc2E0QU1OQXltbmR5bURoTEVLUHlSdHZRcGt5VzBsZ2RlemlWUFl2QjBoWmlzNFZSV3d6UkVHSU11RUFhRm1mbUpuOURGVE5LcWxBTVFaZkdocllrQ3l2eFEvalJGN2M1dkZZcFI1TWcxbHcrVUpDbDlBOFRySlhvbE94VDdFQ2JLRE1sZjNWaXpHbzdBcTV4cnFDMGl4bVNXWkhGbHlTN1pjSFFZNXk2Y1JRRzZseXRId1VDUFhiMXUzYlVDOWVQK0sxTUQzczJjZUc1VmprWUQ3Wjc5UlYxdjdvTnAwcXBFWVhYaDFEWGNUc2xhM3RjZVBWS1NYZ0JJY1BuREtqcEtHWHZhNUMrRXBMQ3BhS2lyUE9NUzBuZmNOSXM3dXcyV3Z1bFhlbU8xdGxFbmpraDdSQzR6a1M3VXF1V2NKcHdUanBFRml1MDNsUGNqWkU2T1pVa3FnekZyUGhMQUVCdmZJaDhlVisxRmlQMnB3R1pCVUdxd0t3dWxYSDdzRFErVC9CRFQ1OUNVKzlDcjJ0dys4YzlNbktoQzV1NDMvUnFSZ04rQVVBeTdVQnFQaTM5Zzg0c056eit0UU8vbXFEQTY2K2RsOGo5QXdlY1dPbGo1K2FoQmNmRmhkaUFZL3k0N0VYMnJubFkzU084NmsiLCJtYWMiOiIyYTBmYzgwM2RkYjQ1NTI0NzEyNDIxNTYwMTBhYWU0ZTdjZjc3NmE2ZmFlNTJmYzU0ZjZlYzI5M2Y2MTIwODFiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:25:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761130807\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">449</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">&#1587;&#1603;&#1610;&#1578;&#1604;&#1586; - &#1581;&#1604;&#1608;&#1609; &#1601;&#1608;&#1575;&#1603;&#1607;&#1548; 10 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>1.0</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>8</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>8.0</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}