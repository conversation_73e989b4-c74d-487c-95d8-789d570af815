{"__meta": {"id": "X31aa8bff47495c77ee6073a4753c3188", "datetime": "2025-06-28 11:24:39", "utime": 1751109879.018333, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.601614, "end": 1751109879.018349, "duration": 0.4167349338531494, "duration_str": "417ms", "measures": [{"label": "Booting", "start": **********.601614, "relative_start": 0, "end": **********.939118, "relative_end": **********.939118, "duration": 0.33750391006469727, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.939129, "relative_start": 0.33751511573791504, "end": 1751109879.018351, "relative_end": 2.1457672119140625e-06, "duration": 0.07922196388244629, "duration_str": "79.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46266824, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02448, "accumulated_duration_str": "24.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.977889, "duration": 0.024059999999999998, "duration_str": "24.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.284}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": 1751109879.0101109, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 98.284, "width_percent": 1.716}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1453/thermal/print\"\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1957488536 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1957488536\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1396910157 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1396910157\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-735737289 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-735737289\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1538745683 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6Ik9qWm1nK2tpMGRxU2hEVC8weGtaYkE9PSIsInZhbHVlIjoiS3BXMFhiTGpCcW9yelVacmh2ZzdXd2l2cWt2K2JXeFR6NDVxTUU4UXkveDgrcWVaZmNEamlONkUzenNibWRrYzJ5VjlDL2tUWGphd0ovR3NrUFVHb1VUcmpKcWV5VFFUeUdEYlhBZnBVMi95UGs4QW1oM1hCRnQ5S255NE9oZ2hWc0Q4anhBVlAwNDBVSzkxMkRoUnU5YlM5MnAycG5PUU9BM3h0NnhJT1lHQ1RuWm1lNklRdUJuVTBWTmZxcENqbGhQc203QytvdHJ6NXFWTy92SG1iTHl6bjJMYkV5eTNUZGIzbVNsVEJJYmNnS3dJLzN5YnFvYVNoTlJMQXdnSXBnZk0zcmNLNDN6YnMxRmtRWnBTZHpxQ2FYdnJXSzdKWGgxYjVsekdNNDBhRFZDSGN6SkEzQTZWbWNvMVJqQ3I0cjdYR1Awd1Rjd2trVGE1Zm5RU1JqTS9Kb01ONzVIWCtzYm1JNXlrTmZ5dm5CaW8rT3FRRVRWbXBOZ2lWOUlINDNvK1ErUURQWDU1UXpnSVdmMTlaSlZma1p6WktZb1ZkcFV3VTdHbkRmSXA0ZGVnMjZGcjFMUmhzUkgyNFFCT3d0RjlOSm42cGpMTWZnYU56aDlpRWhYOFBzcFRPRnFQMXJDRldkRHJWZGswVk0yUUtwdUwyN3ZvVGFwRklLajgiLCJtYWMiOiI3MDE5Zjg2MDA2M2EzNjg3ODM5N2RkYzM0N2U5MzJiNWIzMDhkMDYzZDY4MDUxN2VlZWVmOWZiOWFmYTViNmFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJRdmVqcmdRWWZXU3J0eFg2UkdLMGc9PSIsInZhbHVlIjoiblZFVWMzZEViV0NZZEljUHhURmZGZUVZMUlNdGVGUWhNMHhjU3c5czNDaTRCNGVSQ0tRWDdqTTJoR0xPQW41Q2hXcUV1RnBvb3pMajhNTTFRWllLQ0hwbUxmUXcya1dXSHFiV09tMFBDTW13aEJWblhGWG5EeEI4eHdrY1ZtMXNwNmdFY1RKM050ZFFiRW9rTEYxV0JvaFN1YStGbFRGbGZOWUJMdEJ5TjZlVVFMSVhCVEtRRUxKWlBKd2JhNFMvaGJmMHNVbk5sRzBZMHFKWXZrcHphTHMrM0NkeGZsbGtTdTZBbkVMUnBSZWtKbUhpZWFtQ1BFUmtHTitNVVlSblU0enkxRXFoUTJHTE1KZDhqc3h3eXdBckt1Q3BreWZnUjA1VUdQcUJ2My9nNE81bVkvUU1tUHR3bUtCNThCenNQMVdoNklGMWNBSEZ3alRONXo4MjNMaXM0a1BIUUhiYVhkdUpUNlN0ZGdLZ2tZU2pKRWduNnFNSlJnLzZZeU9CMVdEV1d6OWJ0bWJramtCbk00MGN0VituM21NZE5wcUhheVRqdkdkVHk2elJwS2J6Sng3ckJyQTh3R0dveGh4WUVhYlUraHIyZ0tKMG9ZOTlnUWpxMGF3VklQNUoyTS9xaGhBSXR3T0FEaTVzSW95RmpMOWV4STlKWElESW9DaE0iLCJtYWMiOiI4NTljMGNjNTBiNjhjYTM5ZmI0N2Q1MjdlMzA4MTA4YmU0ZjU3YWFjYWQ1NWVhNGVhMTNlMDgxZWQ3Y2E1ZWNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538745683\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1155654354 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155654354\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2032301111 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:24:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxQWVArZnZ3Q2cyZTQ0VXBoc1Z4SUE9PSIsInZhbHVlIjoidk13U1RjTk11MVJjNU5iR2JnQlBubnFMNjFiWFgwY0lEWjhOSEt2cytVVjczRmh2Q0REbzk1KytRNnR5RFh6eWhyM2RycldWcW1uNXZJaWpBZUdjTTBQTDFhZ1g4ODYrZnF1ZS9TWjY1T01YanNVQ3RDZ21ZRFNXTEVPK0xmbTJ5MU0rQkpkVUo2anZzRWt3di9YeVRPWWZuekllSGFlQWhVRTFWSExZZTVWMkVQYzBnMDUxK09xN3hyem8rVHlDL1BieEZYNksybDdUVGZ0S2NUSmJoUmRJbHhweENKVnBRUGxYWVpoeWFiVmVaYzFDSEpxWmtMQkQrWUtnNnQzaUVlKzdKOUpkNElDRjZLeDBNRnZuMHlycUNZZ1RwL0NUNG5tajN4aW5jL0duZm8zL0NWSlRYZ0sxYmUzZGc5Ym1jdnJESmRJSVA0UUlSdDVxZk52QWxDVWViUEc3Z1BqaFMwVU8wSmt3TlU2Y2U3Y3VPNDIrN0w0MndsV3FEQytQNGxXQlhVa2NhblFVUXh2ZVJHcVErQjNNRGZoVE5zRjc0V3lxNXBYOWZlSUlRKzhsNlkyMmw1Y0VrWS9IQTlCSFNuSnplNkpoNzZIZzltTkFWL0FvMlpjRlFXNU5ZdnA2MHU3R0VoU2h5MWowcWIxcEhtWVZSRlE3ZEYzUVdzeG8iLCJtYWMiOiI5OTM4ZmVlZjlmN2Y1MzQwYTZlODcwNGRjYjNlYWZiNGUyOTk2Y2QyOTY2MGZiN2YzNzEwNjdjYTQ4YzhiMzhlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVFaWRuSUdpZWVvbDlicmZRWlV3aEE9PSIsInZhbHVlIjoiRk42Q0hmYTUxbUVXZHUrNm9Kd1VHbWlRKy84cGlrZDV5Rkh4RVZrcERWeXZZQXMweXhtYm5lTUpQMG9kcVQvY0dyRXl5b013Rmh1bVZpKzRhamcyYUxLQ3ZyVXRKQ0VWWDk5ajZETi9heTJyYUthb3NLcVV2S3RnWXNOOUZYYkhrZ05XUjcyL1R6RWhMQWVxSGFCVk9HdU5naGpINkJUTnZVMUF6WlhydlJoNVNuWVlJUFR5V2diZzVqQ09XbllGdE9kd3dWNXAvTjdiTFhreFhaaVZYbElKRVlGbm14dXNoNEpFRTJjRjlRTFJqcXM1MTFEbllwbjhTbDQ4blNVQmpBVUt5QWN5TFlQTG9CdGJwZWpJalI1ZTkvOWF2Z0F2eXBYTXZGSEE1RmJyVHA3azErQTVMNlVyZlMrRnYyOGhpZS9ZemZGU2NOZytOdlNYWVdDVXZVVHhSTTMvZmI4dmF2ekxOeWNGdDZZLzgwaFpRVDFyRVFxbUo2S2N1a1dJZklNM2JQcGZ3VXpqVU56aERtd3Nyd3ZFVnBFclRJSm56OUVkaFJuQ3VNTm41cEwwWlQ4OWpRUytTTldxWnN6NU9ZQ3Vrd3o4NGh5Y21uY0F2c3phdkxybEQvdCtuOEpQc3Z1aWQ0WHAwL2wwUHNJR0xSRXRuczdCMkZpaTZIdFQiLCJtYWMiOiJlNTFhMGQ1ZDFmM2ZjNmEwNGYzOWRlZTBhNzVmMmQ0Yjk4MWJlMTA4N2NlZWEwMzhkYWZhZjY4MGQ0NTM2NDVjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxQWVArZnZ3Q2cyZTQ0VXBoc1Z4SUE9PSIsInZhbHVlIjoidk13U1RjTk11MVJjNU5iR2JnQlBubnFMNjFiWFgwY0lEWjhOSEt2cytVVjczRmh2Q0REbzk1KytRNnR5RFh6eWhyM2RycldWcW1uNXZJaWpBZUdjTTBQTDFhZ1g4ODYrZnF1ZS9TWjY1T01YanNVQ3RDZ21ZRFNXTEVPK0xmbTJ5MU0rQkpkVUo2anZzRWt3di9YeVRPWWZuekllSGFlQWhVRTFWSExZZTVWMkVQYzBnMDUxK09xN3hyem8rVHlDL1BieEZYNksybDdUVGZ0S2NUSmJoUmRJbHhweENKVnBRUGxYWVpoeWFiVmVaYzFDSEpxWmtMQkQrWUtnNnQzaUVlKzdKOUpkNElDRjZLeDBNRnZuMHlycUNZZ1RwL0NUNG5tajN4aW5jL0duZm8zL0NWSlRYZ0sxYmUzZGc5Ym1jdnJESmRJSVA0UUlSdDVxZk52QWxDVWViUEc3Z1BqaFMwVU8wSmt3TlU2Y2U3Y3VPNDIrN0w0MndsV3FEQytQNGxXQlhVa2NhblFVUXh2ZVJHcVErQjNNRGZoVE5zRjc0V3lxNXBYOWZlSUlRKzhsNlkyMmw1Y0VrWS9IQTlCSFNuSnplNkpoNzZIZzltTkFWL0FvMlpjRlFXNU5ZdnA2MHU3R0VoU2h5MWowcWIxcEhtWVZSRlE3ZEYzUVdzeG8iLCJtYWMiOiI5OTM4ZmVlZjlmN2Y1MzQwYTZlODcwNGRjYjNlYWZiNGUyOTk2Y2QyOTY2MGZiN2YzNzEwNjdjYTQ4YzhiMzhlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVFaWRuSUdpZWVvbDlicmZRWlV3aEE9PSIsInZhbHVlIjoiRk42Q0hmYTUxbUVXZHUrNm9Kd1VHbWlRKy84cGlrZDV5Rkh4RVZrcERWeXZZQXMweXhtYm5lTUpQMG9kcVQvY0dyRXl5b013Rmh1bVZpKzRhamcyYUxLQ3ZyVXRKQ0VWWDk5ajZETi9heTJyYUthb3NLcVV2S3RnWXNOOUZYYkhrZ05XUjcyL1R6RWhMQWVxSGFCVk9HdU5naGpINkJUTnZVMUF6WlhydlJoNVNuWVlJUFR5V2diZzVqQ09XbllGdE9kd3dWNXAvTjdiTFhreFhaaVZYbElKRVlGbm14dXNoNEpFRTJjRjlRTFJqcXM1MTFEbllwbjhTbDQ4blNVQmpBVUt5QWN5TFlQTG9CdGJwZWpJalI1ZTkvOWF2Z0F2eXBYTXZGSEE1RmJyVHA3azErQTVMNlVyZlMrRnYyOGhpZS9ZemZGU2NOZytOdlNYWVdDVXZVVHhSTTMvZmI4dmF2ekxOeWNGdDZZLzgwaFpRVDFyRVFxbUo2S2N1a1dJZklNM2JQcGZ3VXpqVU56aERtd3Nyd3ZFVnBFclRJSm56OUVkaFJuQ3VNTm41cEwwWlQ4OWpRUytTTldxWnN6NU9ZQ3Vrd3o4NGh5Y21uY0F2c3phdkxybEQvdCtuOEpQc3Z1aWQ0WHAwL2wwUHNJR0xSRXRuczdCMkZpaTZIdFQiLCJtYWMiOiJlNTFhMGQ1ZDFmM2ZjNmEwNGYzOWRlZTBhNzVmMmQ0Yjk4MWJlMTA4N2NlZWEwMzhkYWZhZjY4MGQ0NTM2NDVjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2032301111\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-371573661 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1453/thermal/print</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371573661\", {\"maxDepth\":0})</script>\n"}}