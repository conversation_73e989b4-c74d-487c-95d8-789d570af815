{"__meta": {"id": "Xbca4c205f9ac3e657e591f7e155707b5", "datetime": "2025-06-28 15:19:44", "utime": **********.489274, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.087906, "end": **********.489292, "duration": 0.401386022567749, "duration_str": "401ms", "measures": [{"label": "Booting", "start": **********.087906, "relative_start": 0, "end": **********.429434, "relative_end": **********.429434, "duration": 0.34152817726135254, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.429443, "relative_start": 0.3415369987487793, "end": **********.489294, "relative_end": 2.1457672119140625e-06, "duration": 0.05985116958618164, "duration_str": "59.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45599224, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00309, "accumulated_duration_str": "3.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.457221, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.696}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.46802, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.696, "width_percent": 17.799}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.473727, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.495, "width_percent": 16.505}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-477373735 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-477373735\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-394486986 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-394486986\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1532915668 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532915668\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123960877%7C11%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inh1dVdHcGc1WGNjc3ZsQXN2OVlkaEE9PSIsInZhbHVlIjoicG5zekk1NEI2L2Q2SThTZldmZU5MNUpXWG5TYU9SSzMwVDBuUmY5Wk1LYTdxWHp4VTByTmNwL1NDSnVjV3VzNjMxcU9ndTY4blVYYmx2OTBpTEFVanRrQU9kRWE2NHMwQytpT2l3VTNNUUxLZng2Zlhoa2JJdG5wVzlQa05xTkJ4MG5kWHp1b3F5dVRKZDVPcWdBeGdyRHhRVDh3V2lQc3BMdWxab0xYbmFpeXFoNUNYbmg2RnJmb3ovNWRuMjhUTGFlZFA1VW82THZ6dmRjN045RGE2My85QXBOVkJENlNHL1Z5aWlMRDQ0aVpLUmFGUVN2cEcwbm5pcUlnUVNDQWljQkdOZm5HOUFNTWFNWnFMZ3ZZVS9LLzZWSml0dlRSdC93NndWbXZESy9UYVlIT3NqVWhsR1BQWDQ3T2hIZ0Vram00VGVYcDNLNlhjWXBCaEVJTGZUQ3JEWUVYTXVJTEg1ZGZueGVrMnJHOUp3eDlKZ2xhZDZQWmJJZGhrVFNSdHJSR0tnL1E0TFM2RCt3YWg0Sllja202RjF4aGVKMGZ3dVV4YjF0WDVrb1BPWFpSK04rbmtxZFRnRU5wZzUyZzI4QWpZMDZOWlFmZ2RqVFRpdWlENzl2elN2NTRsMGtLOFRvTGVjUDJuU2VoSDg2OGdQVGZCV0J2YUJyRWIrZEYiLCJtYWMiOiJmYWY0ZWUzMDNkMjFiMTk0NmMzMmYwOGNlMDg1MWViZTE4ODJjNWY1NTFiZGEzNDMwYjAxNjgxNDA3NTc5M2E5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjUxV2ViRnRIYWhmTHIxcEtjam53Qnc9PSIsInZhbHVlIjoiME1MRmx1VEJvL0dUMzZzVnMyTXhvTkt5Z3lnOXhQelFtbmV5UUFER1l6NjhQcEhmQXNMbmZNY0Z1VjhmNnByM0E2dThsSVVnOGtGMnhJc3Y0YzBWb0lyRWZJZ0pLN2xydXNyY2RWN2V2VTJuNFkra0tqMU53RHhLeVg2TVFYT2hyWGZaeEY5VDI5cTBEdU5BUkowNXNkcEwvUUNTM3dpd3RDa05NazUrOVEwN052UVRKK2tTSkhuWjQ0dmNsUWtMc1N4alg5NC9KVmFmMUw2bUdQai9zMDZQQkUyNlMvaGMvaDU4MWJMQ2EyU2RCTUtRemI2dWp1dnFLV3ZLQ210YW5yVlZmWEcwWWZNeE90M0tPV1YyaG1oZGFCdVJ3azBqTXMzcUhxQlNaNXF3MkhOLzk1NVZnU2cxZVp0cGMyRlE3TVNsMUxLV2VGc2dVS0JnbWd2YVNhWWVXaitzYzVTUkdLdGhHbzhTNkFOR3NKUmxEeHNBaG91T043cVNiaEpIMHpVcmo5bW5OK2paL3ZSSWs5TEQ0N2tybmE2dEpzRGZhd3BRUEF4UlJZQkpoa0Q3bUFSMWJ3SmY1eE9FaHVPMDZ6KzVNUjBpZXpLRmhlK1E4NXQrOXFzdUdiSG9IQmMyS2xnS3BBOFdUWTFNZVBQUUduVVhhZnZzR0pQSi8wYWwiLCJtYWMiOiIwODRjNGY4NTcxMTMxMzg3ZThiY2JjZWViOGQ3ZDZiMDg4MWFjN2RiNGYzZDQ1ZWIwZjI0NGM0MzdkNmUzNGE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1593713506 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1593713506\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-37489297 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:19:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlKZnlKSWVRVk5vSHBSNFFVRmlianc9PSIsInZhbHVlIjoiVHRxeFo3NEFQc0w0QllyUitIRlF0ZzZZSlRUMnM3QnY2SWxzR3dMZXZxVjB3VGpTRmpycE1XRVBhV2dBciszQStQd2hNVFdhOS9teFZNMXZSelhsWTBvdVBBMGpOeFVRZjNtL3BGR09oNU1jclk2dHo3VDVnNzhmYmI0RndlYkNSTE9SdlEzMFcvc09Ob3ZWb3BHUE15N2o0N3hLYnFLK3p3TGJmdnFqTU5hR1MzK1V4bERqUkNFQzdGNldhMERpbUk2MmUvbUR6SUY5bHhwVHp2WTE0aWd0OTFXeWNoMVA0ZTdRUVRmT3RpZm1zTFZvQUlvL3dYV01kSjYvMENwK0w1LzV0OG8zTkNmSi9abFhhR0dsOGJPamcyM3k5OE1KbjBqeTRkY1ROY0VNdmRGN1p3SFNTMllRME9oeVQ5R0xxTTVJbW1sQ0J2eXJLbVlYUDRoYm1TOXVBZXg3bDk3SmRRd1M4N1U4MTRQcktSSFkreFFBVEo3MHE4TDMrSWpDaDZzaTZvL0VUZXJCSXNLMWdmV0hzanZITEZXM2NHNmVnL3o1Y0hXc3l2QVhPN3VnVDlyY2JhMzQ4Z0ovZ1EwY0EvT1BZSFFGZ3V2c1BlUUxTOGxYcjU3NXgxS1FkcW1TWGJQRXFvc2g4c25JaFFWdVBxbloraDBFQTVFdTJFVkEiLCJtYWMiOiJiZTg3YTg5ZmY4MmU3N2Q1YTkwN2M2OTlkYjdlY2JmODE1YjEwMWNmMTU3MjdlNTBiZTM0YzBlMzI3NTZlZmU2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjM3NjE5TUQ2bUFTTzF4bFRPeisyalE9PSIsInZhbHVlIjoicXhGQXBYRHVEQ200cW83LzdZd1BQS0k1MDB1MDVTNlZHNmg0SkRDeG1HQkJaRnV2MW1INGtVakthSlFoUVNQRGdmeEU2TjJVWlRwK1BCcm4rS1dPM0prUnNSUXZWVmNqbTVXZzQ5UWZWckFURzd6bGNJMlNIMUtkSy9HUHBZZHFaMzVhNVZMYXgvY01XcFdOc1JDM3oyM1grcDYwbEwrbVIvWVJLTGhhc0N6Y2pFL2tCb29ZTFhzejFYb0pxb0MxU3loUUpCMWtqNnBtOG1mL2pkaXlZSVBVbWY5U0NSb1dDOXFSZEQ3OUtWVFE3NnVzaURwUjRsallXMUNuTFRWbklUUjdPZEo4SXR3SVpZZ0ZMSGovaUlndlBKY2ZPemFJQlloc3hGNDY5Q0RNUWh2bThiMFNHbXgrWmlYTm1zSW9kSGE4Y2FZZmZnSEw4NlRtUXd3VW1Ua2cvZTBHaEpCa0xhQ3pOeXZPUlRqQXh6bGZTdjZrRXhVSlk0Vm5BUVdQL1BxSitqUWNOSWRvZGVJUVp1TlpXWDZ3S2R5MVdsa2tXcHo1REE4UEE3YXN6ZUVFS2ZlbGxBTE9jYXVzYi9DdWk5UXJGbm40S1hPbFIyUlFPYWg5aWxGZXd6VmlEQmZRaWFBRUdqN1R3V1VLczNNTVJiWGF3RUMrU3RlY2dZVHkiLCJtYWMiOiI0NmU4MmVhN2FhNTE3ZGExYjZhNTQyNzNjMWRhY2QzZjgyOGZhNmM5ODQ0YTdiZTVlNDE3YjUyMWY1MjQwYTNjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlKZnlKSWVRVk5vSHBSNFFVRmlianc9PSIsInZhbHVlIjoiVHRxeFo3NEFQc0w0QllyUitIRlF0ZzZZSlRUMnM3QnY2SWxzR3dMZXZxVjB3VGpTRmpycE1XRVBhV2dBciszQStQd2hNVFdhOS9teFZNMXZSelhsWTBvdVBBMGpOeFVRZjNtL3BGR09oNU1jclk2dHo3VDVnNzhmYmI0RndlYkNSTE9SdlEzMFcvc09Ob3ZWb3BHUE15N2o0N3hLYnFLK3p3TGJmdnFqTU5hR1MzK1V4bERqUkNFQzdGNldhMERpbUk2MmUvbUR6SUY5bHhwVHp2WTE0aWd0OTFXeWNoMVA0ZTdRUVRmT3RpZm1zTFZvQUlvL3dYV01kSjYvMENwK0w1LzV0OG8zTkNmSi9abFhhR0dsOGJPamcyM3k5OE1KbjBqeTRkY1ROY0VNdmRGN1p3SFNTMllRME9oeVQ5R0xxTTVJbW1sQ0J2eXJLbVlYUDRoYm1TOXVBZXg3bDk3SmRRd1M4N1U4MTRQcktSSFkreFFBVEo3MHE4TDMrSWpDaDZzaTZvL0VUZXJCSXNLMWdmV0hzanZITEZXM2NHNmVnL3o1Y0hXc3l2QVhPN3VnVDlyY2JhMzQ4Z0ovZ1EwY0EvT1BZSFFGZ3V2c1BlUUxTOGxYcjU3NXgxS1FkcW1TWGJQRXFvc2g4c25JaFFWdVBxbloraDBFQTVFdTJFVkEiLCJtYWMiOiJiZTg3YTg5ZmY4MmU3N2Q1YTkwN2M2OTlkYjdlY2JmODE1YjEwMWNmMTU3MjdlNTBiZTM0YzBlMzI3NTZlZmU2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjM3NjE5TUQ2bUFTTzF4bFRPeisyalE9PSIsInZhbHVlIjoicXhGQXBYRHVEQ200cW83LzdZd1BQS0k1MDB1MDVTNlZHNmg0SkRDeG1HQkJaRnV2MW1INGtVakthSlFoUVNQRGdmeEU2TjJVWlRwK1BCcm4rS1dPM0prUnNSUXZWVmNqbTVXZzQ5UWZWckFURzd6bGNJMlNIMUtkSy9HUHBZZHFaMzVhNVZMYXgvY01XcFdOc1JDM3oyM1grcDYwbEwrbVIvWVJLTGhhc0N6Y2pFL2tCb29ZTFhzejFYb0pxb0MxU3loUUpCMWtqNnBtOG1mL2pkaXlZSVBVbWY5U0NSb1dDOXFSZEQ3OUtWVFE3NnVzaURwUjRsallXMUNuTFRWbklUUjdPZEo4SXR3SVpZZ0ZMSGovaUlndlBKY2ZPemFJQlloc3hGNDY5Q0RNUWh2bThiMFNHbXgrWmlYTm1zSW9kSGE4Y2FZZmZnSEw4NlRtUXd3VW1Ua2cvZTBHaEpCa0xhQ3pOeXZPUlRqQXh6bGZTdjZrRXhVSlk0Vm5BUVdQL1BxSitqUWNOSWRvZGVJUVp1TlpXWDZ3S2R5MVdsa2tXcHo1REE4UEE3YXN6ZUVFS2ZlbGxBTE9jYXVzYi9DdWk5UXJGbm40S1hPbFIyUlFPYWg5aWxGZXd6VmlEQmZRaWFBRUdqN1R3V1VLczNNTVJiWGF3RUMrU3RlY2dZVHkiLCJtYWMiOiI0NmU4MmVhN2FhNTE3ZGExYjZhNTQyNzNjMWRhY2QzZjgyOGZhNmM5ODQ0YTdiZTVlNDE3YjUyMWY1MjQwYTNjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37489297\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-191748179 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-191748179\", {\"maxDepth\":0})</script>\n"}}