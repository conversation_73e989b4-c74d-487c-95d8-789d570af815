{"__meta": {"id": "X5079247e2af5279114dc640c28dc50e3", "datetime": "2025-06-28 16:01:27", "utime": **********.423607, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126486.92456, "end": **********.423625, "duration": 0.49906492233276367, "duration_str": "499ms", "measures": [{"label": "Booting", "start": 1751126486.92456, "relative_start": 0, "end": **********.331002, "relative_end": **********.331002, "duration": 0.40644192695617676, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.331015, "relative_start": 0.40645503997802734, "end": **********.423627, "relative_end": 1.9073486328125e-06, "duration": 0.09261178970336914, "duration_str": "92.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831872, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00742, "accumulated_duration_str": "7.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.392315, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 29.111}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.40681, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 29.111, "width_percent": 11.86}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-%' or `sku` LIKE '%<div class=\\\"product-%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-%", "%&lt;div class=&quot;product-%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.410619, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 40.97, "width_percent": 59.03}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-551984183 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-551984183\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1927253147 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1927253147\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1040734897 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"20 characters\">&lt;div class=&quot;product-</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1040734897\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1946294297 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjN2MGVPL1ZsUnZLVnc3aTcwRXJ2UUE9PSIsInZhbHVlIjoiSVdzZTZrZ1ZuTDJRdExqZWlkc2RKdEdiSHh6UWhXc0Y1UzdEdXl6eHg0YUpjMWt5UTJnaHMyZmV6MjY0K05kbkdmWERxZ2NsalFOZDlRVEw4ckUzYVpjMExScmQ2T2lSUHpLd0hub2FPdi9yb0U0aVdjMlZGQUMrdUpUQThZcDNCVVYvNE5mTlZFVUw1MnBNZFl2aUd2WkZMYWY0RGRLQTlLS2pWQjZVZGNvdHVueWM0cVZHbjVHWUtQR29CeHo4NWw1dE9MMGZBSVJ0bnkzblllMkxsd1hIMC84d1dFay9EeFhOM0p3czJPbmRScXo3UVhQcUYzTjVPZGNmWVRTUmRXWlJvbWxuem5JRklQWkx4NE90ZzJoeHk3YzRHUnJ3bnNNVGRpV0ViQWdTVU44M2tUMGVaSDgvRHNBQ3lBbFNQSWxQTkFsdnhtWjVCelhlaHJmbzh0MCtqL1FzTmdzMVZOZGwyM1Zyd0pZL29jVVArdHk2NnAvRFR4V3JWcUNqMURQMnk1ek5aRkJGcUtrTlZHY25YYzR4cE1HSG5zQXUxS2V5UXlxVGFWZWxRZlBKTlhSd003b2w3NkEwWnZEOHplRnQyWkUrQXRRY0pPQWlhTHJXcTl5UGRlY2ZKUVozR29qcXNoWnNCSVJJTUlkTVlJWXVkYnVLVEg0SWVHMlkiLCJtYWMiOiI0MjYwNmYwN2RkNTIwOGMwODYwNWE3MzI0YWQ4NDE5NmVhNTg0NThhNjhkYzM2MjJkOTA1YTZhODNmNzRmODMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImcyTFNpZTY1Vmg2Vk00REtLNlV2OUE9PSIsInZhbHVlIjoidlIyK0ZUUWRTWkJBRUtBTFFWMHRiVzRWQjNFbzRZUTdvZ2lFZkZFcVhYTGVLU09XOVIySnU5Y0tHMkliVVZuSTJFMFkrNk1mZm15VVBiRUNRMnk4cWU4TVpYRGQ4c1llQzM3ZHV2a2FBMHVSRmc2RTJWa3pVZzdRYWNUM3dpcmwwdkREaHZQcDd6ZlZtaXorNHdKSTlETVkwSnBESDV5WTBvUDcvMjF3ay8zTjI3ZnlyQlJWb2ZsTjhpa1YrVVNsdzJ5WUhhRVAzWVdtcnczdWkwcVBOeU5tKzRCSWVIUkxMSDJxMmxTRXpJSUtYRkxhZU1VOEM3NVlKWUFvZ0VxdHlxaE95T1EwdG43Nm9QWTcxaGpMT1VSUTRJQVlaNHdKR1hpcTA3a0lmT1lXWDI2bENnZXhzOFBTZ1NFWUtlRWlneUU0M3M3dUdDK253aWRKY0hOZDQzZVorVm5KdE5IZDJlcmkyN2tVMEk1ZVdMMzFDYjByVkM5VHEzdHdLVUljakdVNGhFQ3dVaFY5Z2s4Z1p2UWZiSFlvbS9mb01NMjRoY1VpOGJlOUtURWk1VWVMTTFKOWhQQno5TjRCNW9Vd3hveW1QNU9kRURXRkRaZEk3ZW41RStLYm9aNmRPdFFGY1R3N1BmS2ozMlM4UWRYaFRQY3JpaWlBN2FzRGluT0siLCJtYWMiOiI1MGFkZDY2NDVkMzkzNTlmNzI4NzE2NzQ4YTQ2N2MxNzFiMjRlY2NlOWU4NmQ4YWJjNTUwMjBjNDE5MjVlMjZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946294297\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1499343736 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499343736\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1825720569 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjQ3K0J4dnVQa3hhUG5SL3FmUVVYdkE9PSIsInZhbHVlIjoiUzdic1VJVGxiRnQ0YW5TTFZnaTZQZHY1eXhWdUE0YStBK05WUHNSUGUyWUZkcDJZdHJoSVd3NUlwVDRYZ1RMeHgvWTFJZi9KV0ZJTlBkbFMwQlZVb0Fpd3hCbjA3Q1ZVVjRmaTdvbVNpdW01YW5wSFIyQWpybFp0akNqUFV3OEl1UHNuNFVMMnl3YnBPN1l0Mk1XZ1lCV3hjeEtQQ1N1TzA1dlhVbXZ5Tzg1bU9Yb0laMi9uOEtVY1VyWExQMXpCUkZ2L25XUC9wcmI0UktRakQwN2tDZTZFUUVSMUh4akJ0TUxuOUZYTzk0cC9tY010MmtkY0tIc1N3OUFNK0xwWHc5UVdpVDRLRG8zdnNJMmRNODFxNUIyNTJBQUVia1pEaW5pZ3VtVDZUL1JvY1FhZEthRGtZeE1PSlBMWUxEcElwSG5tbU1uR0ZHd29jd3MyV1hraTJ5SklCcG5xT3ZxZWpTajE0ZnA2aVowV2NPNXNlOHY5YXNKMVluSWlwcUxZT0l1eEU4STRPY21OTVl4NUJ1QXI5YVBaVTVnbVNlNllEcE5zT01FUWh4RzhIdFBRU2wvS1BhMEJZWFRveExnbUNUNFlENGpLRnNYenZpeEJjYXVzNnBuZU00MjZQcTJVeitQSExtbWY4cStVZ0lSV1NVYk1hWkd4UXlLWG5JUnMiLCJtYWMiOiJhY2ZmZGM1MzljNGY2MzVmNzhkY2EyY2Q3YjAzMjdmMGVhM2QyZWVlNWJjYmM1OWUzMThjZjg1OTlmYTlmZTgwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlgyVEY3TnlRTW44YjBOOUpRVDEvM0E9PSIsInZhbHVlIjoia2dLQWpjSTZmV3FTellqcW5RWndVZnI0c3RFeUdQUnZKMXpTcVdvMXc3S2xDY01URWZ4VUlKZzJ4aGxUZklwenZGMUlnc2pWY3hOZjZpNWhUeEdOa0d5RHlqMUdTa2luYzJOaEtoVkdXNG1GbGV4UjI4dlFRYkRmMEJrNlJIZG9vRFFleVdFalVBQ0Nwc1oraTZzL1BjdnZrS0ZCR1dGUGtQYzJEL01aMC9pTWhtVFluWUR6ajZ1ZHk5NmtZS1IwOG5ycWI3NFFNTDJtUHZFVlJqZk5xWjBaTWJBellXZmwzUmN4Q3FVZXhiUS9LWlpZN2xRRmY5OTBmZTZFZjB0V2xDUjUwR1hicGZ1NGxvd1FTTDRuREJ1dnB1ZU82WjVSQ3dsdTBpc0w1YXFxM1oxcHlJTDRTYUcvUXZkYTZLRzBkdkdpZGN2Q3BIR1Y3VDJsazg0cXF4VkhDTmxSTEV1aDhkSDdJY1JqTDRqZXQwVUtra2Z5K0g0RUo5TFdUY3B6b3Blekc0STBsOUdUY3V4ZFpFUmNZSkk2MmtCZlNzc203dFBUR1VDYTVMZDFpTkl0bFJhdmwzbzg2RFBGU2I4YnU5UlVFNXFxTFFkSGVVRzBGZ2pmZlpBWnA3OHlRMDFFQlpOVUh2Ri9idy9ySjJLVlhWanhTNlRyYlVwVTd2bmciLCJtYWMiOiJlYTg2ZmZlMTY0MmFjODI2ZjBkNzZiM2EwYjVmMmM1N2YxZDFkN2YzMWUyYTUyM2U0NjM3YTFiNmM1MWYwZjUyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjQ3K0J4dnVQa3hhUG5SL3FmUVVYdkE9PSIsInZhbHVlIjoiUzdic1VJVGxiRnQ0YW5TTFZnaTZQZHY1eXhWdUE0YStBK05WUHNSUGUyWUZkcDJZdHJoSVd3NUlwVDRYZ1RMeHgvWTFJZi9KV0ZJTlBkbFMwQlZVb0Fpd3hCbjA3Q1ZVVjRmaTdvbVNpdW01YW5wSFIyQWpybFp0akNqUFV3OEl1UHNuNFVMMnl3YnBPN1l0Mk1XZ1lCV3hjeEtQQ1N1TzA1dlhVbXZ5Tzg1bU9Yb0laMi9uOEtVY1VyWExQMXpCUkZ2L25XUC9wcmI0UktRakQwN2tDZTZFUUVSMUh4akJ0TUxuOUZYTzk0cC9tY010MmtkY0tIc1N3OUFNK0xwWHc5UVdpVDRLRG8zdnNJMmRNODFxNUIyNTJBQUVia1pEaW5pZ3VtVDZUL1JvY1FhZEthRGtZeE1PSlBMWUxEcElwSG5tbU1uR0ZHd29jd3MyV1hraTJ5SklCcG5xT3ZxZWpTajE0ZnA2aVowV2NPNXNlOHY5YXNKMVluSWlwcUxZT0l1eEU4STRPY21OTVl4NUJ1QXI5YVBaVTVnbVNlNllEcE5zT01FUWh4RzhIdFBRU2wvS1BhMEJZWFRveExnbUNUNFlENGpLRnNYenZpeEJjYXVzNnBuZU00MjZQcTJVeitQSExtbWY4cStVZ0lSV1NVYk1hWkd4UXlLWG5JUnMiLCJtYWMiOiJhY2ZmZGM1MzljNGY2MzVmNzhkY2EyY2Q3YjAzMjdmMGVhM2QyZWVlNWJjYmM1OWUzMThjZjg1OTlmYTlmZTgwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlgyVEY3TnlRTW44YjBOOUpRVDEvM0E9PSIsInZhbHVlIjoia2dLQWpjSTZmV3FTellqcW5RWndVZnI0c3RFeUdQUnZKMXpTcVdvMXc3S2xDY01URWZ4VUlKZzJ4aGxUZklwenZGMUlnc2pWY3hOZjZpNWhUeEdOa0d5RHlqMUdTa2luYzJOaEtoVkdXNG1GbGV4UjI4dlFRYkRmMEJrNlJIZG9vRFFleVdFalVBQ0Nwc1oraTZzL1BjdnZrS0ZCR1dGUGtQYzJEL01aMC9pTWhtVFluWUR6ajZ1ZHk5NmtZS1IwOG5ycWI3NFFNTDJtUHZFVlJqZk5xWjBaTWJBellXZmwzUmN4Q3FVZXhiUS9LWlpZN2xRRmY5OTBmZTZFZjB0V2xDUjUwR1hicGZ1NGxvd1FTTDRuREJ1dnB1ZU82WjVSQ3dsdTBpc0w1YXFxM1oxcHlJTDRTYUcvUXZkYTZLRzBkdkdpZGN2Q3BIR1Y3VDJsazg0cXF4VkhDTmxSTEV1aDhkSDdJY1JqTDRqZXQwVUtra2Z5K0g0RUo5TFdUY3B6b3Blekc0STBsOUdUY3V4ZFpFUmNZSkk2MmtCZlNzc203dFBUR1VDYTVMZDFpTkl0bFJhdmwzbzg2RFBGU2I4YnU5UlVFNXFxTFFkSGVVRzBGZ2pmZlpBWnA3OHlRMDFFQlpOVUh2Ri9idy9ySjJLVlhWanhTNlRyYlVwVTd2bmciLCJtYWMiOiJlYTg2ZmZlMTY0MmFjODI2ZjBkNzZiM2EwYjVmMmM1N2YxZDFkN2YzMWUyYTUyM2U0NjM3YTFiNmM1MWYwZjUyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825720569\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-38532518 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-38532518\", {\"maxDepth\":0})</script>\n"}}