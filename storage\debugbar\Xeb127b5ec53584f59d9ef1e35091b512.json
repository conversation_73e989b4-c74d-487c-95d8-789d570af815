{"__meta": {"id": "Xeb127b5ec53584f59d9ef1e35091b512", "datetime": "2025-06-28 15:04:29", "utime": **********.299363, "method": "GET", "uri": "/financial-operations/product-analytics/top-selling?warehouse_id=10&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123068.770435, "end": **********.299377, "duration": 0.5289418697357178, "duration_str": "529ms", "measures": [{"label": "Booting", "start": 1751123068.770435, "relative_start": 0, "end": **********.197555, "relative_end": **********.197555, "duration": 0.4271199703216553, "duration_str": "427ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.197565, "relative_start": 0.42712998390197754, "end": **********.299379, "relative_end": 2.1457672119140625e-06, "duration": 0.10181403160095215, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46216152, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/top-selling", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getTopSellingProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.top-selling", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=206\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:206-364</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.04103999999999999, "accumulated_duration_str": "41.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.231324, "duration": 0.02607, "duration_str": "26.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.523}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.267857, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.523, "width_percent": 1.535}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' and `p`.`warehouse_id` = '10' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 235}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.271766, "duration": 0.00531, "duration_str": "5.31ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:235", "source": "app/Http/Controllers/ProductAnalyticsController.php:235", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=235", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "235"}, "connection": "kdmkjkqknb", "start_percent": 65.058, "width_percent": 12.939}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 240}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.278703, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:240", "source": "app/Http/Controllers/ProductAnalyticsController.php:240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=240", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "240"}, "connection": "kdmkjkqknb", "start_percent": 77.997, "width_percent": 10.039}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_v2_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos_v2` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' and `p`.`warehouse_id` = '10' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 260}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.28467, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:260", "source": "app/Http/Controllers/ProductAnalyticsController.php:260", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=260", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "260"}, "connection": "kdmkjkqknb", "start_percent": 88.036, "width_percent": 2.022}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, COALESCE(SUM(wp.quantity), 0) as stock_quantity from `product_services` as `ps` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` and `wp`.`warehouse_id` = '10' where `ps`.`id` in (1808, 299, 311, 310, 993, 639, 300, 1661, 1859, 476, 1745, 165, 1102, 1688, 298, 312, 484, 1767, 2028, 18, 164, 1101, 1702, 597, 1319, 2041, 2120, 157, 179, 309, 596, 758, 896, 1116, 1247, 1527, 2015, 156, 158, 159, 231, 1099, 1324, 1743, 1766, 2073, 142, 152, 153, 242, 250, 516, 522, 669, 681, 1105, 1108, 1115, 1126, 1313, 1374, 1510, 1716, 1746, 1806, 1897, 2007, 2016, 2, 3, 160, 490, 518, 521, 611, 638, 666, 924, 1103, 1106, 1110, 1117, 1118, 1265, 1372, 1763, 1802, 1956, 1980, 1982, 40, 41, 45, 88, 119, 151, 155, 162, 176, 202, 238, 243, 253, 314, 354, 367, 556, 568, 595, 632, 674, 753, 754, 1010, 1081, 1086, 1095, 1119, 1131, 1353, 1370, 1467, 1543, 1655, 1659, 1660, 1673, 1690, 1728, 1740, 1761, 1803, 1809, 1829, 1892, 1894, 1898, 1899, 1944, 1969, 2000, 2103, 2112, 2237, 2244, 6, 11, 19, 38, 80, 112, 114, 143, 144, 146, 148, 150, 154, 161, 168, 180, 181, 183, 219, 222, 237, 291, 313, 324, 326, 349, 350, 358, 361, 362, 385, 386, 400, 432, 478, 481, 485, 488, 495, 514, 515, 523, 531, 535, 543, 558, 565, 566, 574, 598, 606, 609, 610, 613, 714, 755, 904, 905, 1014, 1053, 1054, 1083, 1098, 1100, 1112, 1134, 1135, 1195, 1199, 1248, 1317, 1338, 1343, 1348, 1371, 1438, 1522, 1537, 1541, 1627, 1633, 1668, 1677, 1686, 1705, 1709, 1711, 1732, 1748, 1750, 1755, 1757, 1762, 1765, 1789, 1810, 1813, 1839, 1842, 1849, 1863, 1893, 1895, 1937, 1941, 1953, 1959, 1960, 1967, 1984, 1994, 1995, 1996, 2001, 2034, 2040, 2050, 2057, 2058, 2122) and `ps`.`created_by` = 15 group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name`, `psu`.`name`", "type": "query", "params": [], "bindings": ["10", "1808", "299", "311", "310", "993", "639", "300", "1661", "1859", "476", "1745", "165", "1102", "1688", "298", "312", "484", "1767", "2028", "18", "164", "1101", "1702", "597", "1319", "2041", "2120", "157", "179", "309", "596", "758", "896", "1116", "1247", "1527", "2015", "156", "158", "159", "231", "1099", "1324", "1743", "1766", "2073", "142", "152", "153", "242", "250", "516", "522", "669", "681", "1105", "1108", "1115", "1126", "1313", "1374", "1510", "1716", "1746", "1806", "1897", "2007", "2016", "2", "3", "160", "490", "518", "521", "611", "638", "666", "924", "1103", "1106", "1110", "1117", "1118", "1265", "1372", "1763", "1802", "1956", "1980", "1982", "40", "41", "45", "88", "119", "151", "155", "162", "176", "202", "238", "243", "253", "314", "354", "367", "556", "568", "595", "632", "674", "753", "754", "1010", "1081", "1086", "1095", "1119", "1131", "1353", "1370", "1467", "1543", "1655", "1659", "1660", "1673", "1690", "1728", "1740", "1761", "1803", "1809", "1829", "1892", "1894", "1898", "1899", "1944", "1969", "2000", "2103", "2112", "2237", "2244", "6", "11", "19", "38", "80", "112", "114", "143", "144", "146", "148", "150", "154", "161", "168", "180", "181", "183", "219", "222", "237", "291", "313", "324", "326", "349", "350", "358", "361", "362", "385", "386", "400", "432", "478", "481", "485", "488", "495", "514", "515", "523", "531", "535", "543", "558", "565", "566", "574", "598", "606", "609", "610", "613", "714", "755", "904", "905", "1014", "1053", "1054", "1083", "1098", "1100", "1112", "1134", "1135", "1195", "1199", "1248", "1317", "1338", "1343", "1348", "1371", "1438", "1522", "1537", "1541", "1627", "1633", "1668", "1677", "1686", "1705", "1709", "1711", "1732", "1748", "1750", "1755", "1757", "1762", "1765", "1789", "1810", "1813", "1839", "1842", "1849", "1863", "1893", "1895", "1937", "1941", "1953", "1959", "1960", "1967", "1984", "1994", "1995", "1996", "2001", "2034", "2040", "2050", "2057", "2058", "2122", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 319}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.287488, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:319", "source": "app/Http/Controllers/ProductAnalyticsController.php:319", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=319", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "319"}, "connection": "kdmkjkqknb", "start_percent": 90.058, "width_percent": 9.942}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/top-selling", "status_code": "<pre class=sf-dump id=sf-dump-1098923350 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1098923350\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-971467703 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-971467703\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2113550474 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2113550474\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1291718353 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=10&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122996219%7C8%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktFaG0vS1ZqdVZTc3o1S0hGdGx2MWc9PSIsInZhbHVlIjoidVhab21xbkJaVlRTdWY5T0tmV3RxOEZnRk1EQ2h5WjRyS2R6aDlWR2xJQ2hieWFGRGl6MzVJRTVhZTBudUFwc3FzTDI0eFdCckJueUhjTkFDUUptSlhLUWkwVTlqZmZZbytMbG1qbGY0aFdtcGlTeXlFWUY3WTVVZ1VRMnZDdnc2bGpNKzNvNVkxeUQxTVVVRWJEL09Uc3MwV1loK1Q3UnBEUlZiREtUU3hYbU1yQlMyY1pvelFaTGRhTlZkZmI3WDJNRU80NVJrWEVPVlllQzdQYlFHSDBiTzhuODF6eXN1elNoM2NNUnZoWElVTGc2QS82dDJRVEhjOUcrQzJOSFBlTjM3WEpMdUtoQ2hFcHNTKzV1OE50REZnaVl0T1JaZkhxNkFxb1o0dDQ5WnVCanZqdFJJcDVLdUhaRUExSTkyWE52OFgzRFNEUk5lbWNtQmprQ0xha0JlcXhBZlVObUhKbWRObGZQaTVJYlVKR0l0ODdVRjBQd0JYT05vVWpMQ1hERDlHdHdvNUlocWxFUUhqWnYxNG5BMEQ4SmE5UlJYc3F3M29iNXJvM09uYlVYd1pXRTFQTFdMNHo1c29zM3Rmc0oxaHMyUnJwbU15djd3a2pTRHNJYWZSUHZYS0MwaklCUlRKdWlXY1lJN2FlaStqNUluSStFZ1BKbU55ZDgiLCJtYWMiOiJlNWNkMjA0MmVlNWFlOWExZTRmZTY3OWFlYmYzNzBhMDdjOTM5ZDdkNDgyYTU4MDUzZjU4NTk0YzE0N2VlODZlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVQaDUwR3dSYzUzNjliYnA0aHdFL0E9PSIsInZhbHVlIjoicUJUUm42N29WeFpPd25abzZBdmgyQy96cFg2dE1XQ2d0L3djMVFXU3k2YXJVWXFyR2xHMVZ3SFArSkM4LytKNEw4ZGRJdlg0UGdaeG0wMUxlMGYvRjVOM2tMQ28xNVg3TG84WnVRREQxa29HOUNKWGVsd0hzNjh5MDFEcTNsaFB3anZOdklHdGJXQXJuWkIvcnlPR1lxUWZZcWQzRmxvU3hPUitDd1NLNWZqSjNQYTlmYVIvRmtoTXVWZGtyUTB3c2ZCWkYwTW5mMmEyelVucHVvaTkvRUNjeVdlTHk3Q09acEdCNEozQkJMT1hGTzhhcmFwbnV0bVcwQjN0TmV2MVBqai9EYzdkVnd2enhZeERZb0dYWS9kYzVSNGdoK0JqTHZZcXU4UGY1Q1N4ZUZzdnVuTkRuUjlzdTZDcUJFQnlyaDZhMWtzakZ4L0NNV0wvN0lKbW5iTEhPemNsMUk3bUFZOUtubkxFczRYTFFTSVFnbVpjWEZCV2dpWXdOaWVmQ0Z1ZWlQKzN6K25CYjNGYnU4QWp5d001bEhxRmlISFVMdFg5ME1EdFRiSVBpMjhxS2RESi9FVEltSHphYmlQRVg1b3JEMTYzSWJXTHhwTE1pSWhoOG9HUVZ5MkcxUHpldTdxT2FLcUs0OFduZENIN3dLOHBNQmlCdWVZSVhqNVUiLCJtYWMiOiJiOTBjNGI4MjQ2NzEwM2M4MTQxNmE4N2Q4OGFkMWIxZTcxY2U1ZGNiZjExZDUyNDM1ZWU5MjFmYzJiNTk2Y2I5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291718353\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-506220600 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-506220600\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-41932162 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:04:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRrL28wdElXOUlCSEpUN3FldmMycVE9PSIsInZhbHVlIjoiUUJXSmlBMkdtdjMvMnJ0ZEhhOWdmbEk2cDF2eWFjN2pTd0N1ZXdtNEZkRXFHZ3gzM1U0Qk9WdVUvdER0VWR2djlxOFdkc1hoUmpacGppRU10WGM1aVl4RFNhdHNnTmxGSXZNcHh3bC9yYXlHaXpqSGN2STlIQWtEV0FzK2JwdHc4bkRnZTVpOGg4S2w2V1l4a1l0NXpsZlM5U1p2NmlUOXkxOG5XdHNGMnJpaEtzREdVN09xS0xBdFNrb25CdUxtbFVFdlNQUnMvRWFFdzJxUWhCMWFDRXFmdGM3ejZwOW9uaHJMQXpFVFpTR2QzVERUY0V0eE9ZUGZCd2dCQlk4WWpsb1ZxbHR0UVY4UTQrWWE4V1hieFBUdXJNak5HSXp0c25iby9Qd0dpc29ET2NpSCtaRlFOSWpkTXVFeU00Zmk0cnFib3Brd2t0bWhsUFMrWDRxL1dzZjNIWDBoWnVyTTJrZEpCUmV3TUx1R1hlZ3V4N1NISUpscUFIZWhRQlJiYTlZQkEwbDdUUzdmbWVOQlF4ejFiQzFPanhZRkhXa3QvdlRyNXJ0a3RpeDdYWk83dHRRSC9hYXdDQ1BPQnlpemNVaysxdDErZ05ia1J5QTY2U3FQZ2kyMDFaL0ovempLdHQwaW5yc3pNaElTcmFZM3NQTkRWTFN6ZG0zcWo2VnYiLCJtYWMiOiIyMDhjNmNlNTAyY2ZhMmEyNzNkYjY2OWNhZDQ5YzZkOGQ0ZGQ1N2NiY2JhYzgzYzc0YzZjOGQ1OTA4MWU5NzJjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlByVXZQZ1hFNHhNQnpyNEZiRFBwdHc9PSIsInZhbHVlIjoiVEZ1ejVORVl1SGdxWmN0VUFyM2pablRBSXBzZURYSFVid2hWcGR6alFyOUtqTHl1UFBRaVZSNGFDYWMrSmhrSGZhM2pYREhma3Z2SXVsOHRZbExwRmJ6a21Xenl5Y1dhNEo3NjVTM2FESlhqY3ErRGlMSzQ4ajZybGlPQmNwcVhIc1ZNODFieDZVeldwdy80VTZrVmQ5S2RWdS8xdUlLNzNCVWRlbEJxbEZkNGxja2N2ZGZmRFdraVh2OW1IcEF5RGxEWFFSYWVFS0Z2U0tzMTlFUVduSFBFd2xsUEdhaExwQWhwaEpUM2xWRW1ZZEU5TjRzd0syeklYWGo4aVFLNFpMODV0bC9vaEI5Y3BDLysrZExuVzJGNnVDRUxCMDZST05HZW5ITzZFMWg4WW1qWnRhOCtMV1EvWFljcm1IeEV3b1JlODUzZWpMcnJ3OUJvdmRYc2xsRXVTa0hzTzlpVnJiK1JkZ2RWUDFFcnEySjBXeXp0ck9lQWRYTW8vSHFtL0w0TTJEM1VacVVjZzdxTEdFSU4rOHJHbUdIaU5reXl1czFNRm10czBCUm5YYnRqdndLdFV1a0crUzRScVhTN3JvOW9wWnFUQWErcTluM2c3WHd3djVFaDFjc2dodU5LZlVpNzJ3UHNwVlloNEJvTEQwY25DSTVmTHRCb1UwcVoiLCJtYWMiOiIxOWM4MjBlMTNiNmEwZTZmNDU4YWRlMjhlYmE3YWZlOGNjMDI1NDBiMTRkM2NjNzdkMmEzOTYyN2JjYmRkZTNmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRrL28wdElXOUlCSEpUN3FldmMycVE9PSIsInZhbHVlIjoiUUJXSmlBMkdtdjMvMnJ0ZEhhOWdmbEk2cDF2eWFjN2pTd0N1ZXdtNEZkRXFHZ3gzM1U0Qk9WdVUvdER0VWR2djlxOFdkc1hoUmpacGppRU10WGM1aVl4RFNhdHNnTmxGSXZNcHh3bC9yYXlHaXpqSGN2STlIQWtEV0FzK2JwdHc4bkRnZTVpOGg4S2w2V1l4a1l0NXpsZlM5U1p2NmlUOXkxOG5XdHNGMnJpaEtzREdVN09xS0xBdFNrb25CdUxtbFVFdlNQUnMvRWFFdzJxUWhCMWFDRXFmdGM3ejZwOW9uaHJMQXpFVFpTR2QzVERUY0V0eE9ZUGZCd2dCQlk4WWpsb1ZxbHR0UVY4UTQrWWE4V1hieFBUdXJNak5HSXp0c25iby9Qd0dpc29ET2NpSCtaRlFOSWpkTXVFeU00Zmk0cnFib3Brd2t0bWhsUFMrWDRxL1dzZjNIWDBoWnVyTTJrZEpCUmV3TUx1R1hlZ3V4N1NISUpscUFIZWhRQlJiYTlZQkEwbDdUUzdmbWVOQlF4ejFiQzFPanhZRkhXa3QvdlRyNXJ0a3RpeDdYWk83dHRRSC9hYXdDQ1BPQnlpemNVaysxdDErZ05ia1J5QTY2U3FQZ2kyMDFaL0ovempLdHQwaW5yc3pNaElTcmFZM3NQTkRWTFN6ZG0zcWo2VnYiLCJtYWMiOiIyMDhjNmNlNTAyY2ZhMmEyNzNkYjY2OWNhZDQ5YzZkOGQ0ZGQ1N2NiY2JhYzgzYzc0YzZjOGQ1OTA4MWU5NzJjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlByVXZQZ1hFNHhNQnpyNEZiRFBwdHc9PSIsInZhbHVlIjoiVEZ1ejVORVl1SGdxWmN0VUFyM2pablRBSXBzZURYSFVid2hWcGR6alFyOUtqTHl1UFBRaVZSNGFDYWMrSmhrSGZhM2pYREhma3Z2SXVsOHRZbExwRmJ6a21Xenl5Y1dhNEo3NjVTM2FESlhqY3ErRGlMSzQ4ajZybGlPQmNwcVhIc1ZNODFieDZVeldwdy80VTZrVmQ5S2RWdS8xdUlLNzNCVWRlbEJxbEZkNGxja2N2ZGZmRFdraVh2OW1IcEF5RGxEWFFSYWVFS0Z2U0tzMTlFUVduSFBFd2xsUEdhaExwQWhwaEpUM2xWRW1ZZEU5TjRzd0syeklYWGo4aVFLNFpMODV0bC9vaEI5Y3BDLysrZExuVzJGNnVDRUxCMDZST05HZW5ITzZFMWg4WW1qWnRhOCtMV1EvWFljcm1IeEV3b1JlODUzZWpMcnJ3OUJvdmRYc2xsRXVTa0hzTzlpVnJiK1JkZ2RWUDFFcnEySjBXeXp0ck9lQWRYTW8vSHFtL0w0TTJEM1VacVVjZzdxTEdFSU4rOHJHbUdIaU5reXl1czFNRm10czBCUm5YYnRqdndLdFV1a0crUzRScVhTN3JvOW9wWnFUQWErcTluM2c3WHd3djVFaDFjc2dodU5LZlVpNzJ3UHNwVlloNEJvTEQwY25DSTVmTHRCb1UwcVoiLCJtYWMiOiIxOWM4MjBlMTNiNmEwZTZmNDU4YWRlMjhlYmE3YWZlOGNjMDI1NDBiMTRkM2NjNzdkMmEzOTYyN2JjYmRkZTNmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-41932162\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1247711659 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247711659\", {\"maxDepth\":0})</script>\n"}}