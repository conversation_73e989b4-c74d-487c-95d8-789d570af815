{"__meta": {"id": "X13a83feb08b92a9dcbf6b160a6825b7e", "datetime": "2025-06-28 16:20:52", "utime": **********.876153, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.45538, "end": **********.876165, "duration": 0.42078495025634766, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.45538, "relative_start": 0, "end": **********.805696, "relative_end": **********.805696, "duration": 0.35031604766845703, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.805706, "relative_start": 0.3503260612487793, "end": **********.876166, "relative_end": 1.1920928955078125e-06, "duration": 0.07046008110046387, "duration_str": "70.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45702488, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020560000000000002, "accumulated_duration_str": "20.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8338928, "duration": 0.01955, "duration_str": "19.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.088}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.861774, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.088, "width_percent": 2.335}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.867483, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.422, "width_percent": 2.578}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IllIcE5GT2gzZUxNblQzWWtHQzRQQVE9PSIsInZhbHVlIjoiaG9UQVU0TDROZlhBQ2QzMDBqU1VvZz09IiwibWFjIjoiNDEzYThkZTIyYWViZDk3YmNjYjFhM2M0OGQ5NGEwZTI4OWExMDFiNjU1MjJmM2E3NDVkZjhkMWU5ODE3NWNjYyIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1106288832 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1106288832\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1956009796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1956009796\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1635997006 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635997006\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1419889557 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IllIcE5GT2gzZUxNblQzWWtHQzRQQVE9PSIsInZhbHVlIjoiaG9UQVU0TDROZlhBQ2QzMDBqU1VvZz09IiwibWFjIjoiNDEzYThkZTIyYWViZDk3YmNjYjFhM2M0OGQ5NGEwZTI4OWExMDFiNjU1MjJmM2E3NDVkZjhkMWU5ODE3NWNjYyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127647762%7C41%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFTb1lLNEkrWmtjNEQvWVdIdktXY2c9PSIsInZhbHVlIjoiRG02ZTg0Z0JDemJUSzZSOGk5c04wdmRkYldVcUhJREl0Z0FFUDdWa1VGdWltMVZWNW02SCtvWVZpa3J5c2hPOC9hbktGUjcvOUxMZk9aNTZnTDRHQjFuaFB5RFZpaFo4L2ZVb2FFQ3A3VlIwLzBDeDdiMEg1QlNnYUo3Z2QvajN2Y0FaOVZPd0RqM0dtWlpMSWRxa3QrbzFKSEFVelpKV3BTeDFGU3VhUy9KZFFMUElyeGFpMng4dThXNGNpdkltVFdlSUdBakl3NTcrbDJSNk0rU1dVQ3RqTWNXVHJId1JjSmxYcXZDZEJBWlpLbXMzRTdkVU50VXZQaEJtNnoyaW1ZOGRtVnBNdGVYZHVRNXB1VVZxV29vZGxFMEJmWnpMeEQxMitwcjVLa2dRY1c2Um5OMGlmQUJ6Y0dvS21kMFJlSHhkd2pFdmRsM3I4MExjcUZWUXB6MnlIYTJUU1czaU9FZjFaU3F4aU1uaGRHSkhuZ29VQ1NFNjhDVkJRUnhqdER5a0pKZ0hRRVlVWnJYSnp0ZWpYdmc3aTFWTS9kY002UGVCZklFemVZUk5IRVdVdkNOSFJwdHBueGxVTE84OU5pVHhpYUROZTZXWW8xSlZvY1dxRW01N25NdjRZZWFCbjM2NDZoaTFycjNaQzBmL1pMVWgxNm40RWR2MElYU2QiLCJtYWMiOiI4ZTM5ZGRmNDU5YWJmOTlmYzk4YTdiZDliYTJiYTViNWJlODg5MzJlNjMyOGFkMzZjNjQwN2E0NDIwMWYyZGJmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdIMEJ4WEpJZHB4NXIwUHREUkVGcXc9PSIsInZhbHVlIjoiOFFCK0FVTlFxUEhOQ3ZkdXltSjFmdWFFbk5JRWxjS0JXTytVZCtuWEU5VDUvRW1nVW9UTkNWalNjaUdNazNRSzk4N04yek9TWkxSMm1CY29Lc0dEQmZ4a0NxSVBaY2FIT2VEeW1JNjJkSHhieU5rSC9PMnM4NWt3cU12cVNYV2JSQWxuS0pxVW9HN21aREdENVFldHFFcGkwMFZKMWlFa0xXcHkwOFhzQVNIWG4yK0lIa3dWRzFKaVN6SVI3Q1VDdDVqZWd3cWY1amdmZDZsSW05SjlqK3pnRng4bU1NeHNESUlvN2hRaVBCK1dDYWVuVlVUdkNCMllLcFlid2wwZ3NzRFdlWk16QytFL1JzeXBxMjZRblZoMWF3SFhyWG5VVTVuUWVCM0YzRkVEWW9makhuNHhmbk9lRXdLdEVXMHczYmcxSTk4eFVGaWRxYzlOSW4wWGdqL3hvN3ExcXVWaC9TaFYvQjd3NEt6RktScVhrckRMYTB2OEo1UUNIMXRPelJxS2N6RGk3bks5OGxiWHppS2YxUVhDZklWb0h3SzRjYjA1bHdaclN0Q0Q1RkJmTE9rN0kvVVNPUXIvbkZ1QmdXZHVvcEFnTW5qUUdMNXVPU204cXRQT211U1RqeUFNYzdiMkFhWWVENFNvcWNyMEx2UWZPMVl1QmloN1dDUVciLCJtYWMiOiJiYjI2NGY4NDJmYmEyMTI3Yzk3NWQ3ZmRlNjRhNjFkNmFjODMzYmNjMTI2OWM4Yzg4NjhiYTlkZjA1MDZiNzM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419889557\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1416011594 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1416011594\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-429678667 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:20:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ildqb2t3ZkJxaWw1U2RWV2Y0K0RtQkE9PSIsInZhbHVlIjoiemFOUGlXU09tcjVUL1NwdW5LZHlyekhsWW5QQzhXV3oxTFR5a0x3cVZQUmwwc3lWcTM0b0JQNzB6RHAxcTdrVGQ3bnljQmRQeS9oYW82VU5RRHFqcFUwY1lrNXM0V1F5YXBGSnl1WTdnaWY1S29NcDVmK2pzQ2dJWU9HR3FDVE1kb1IrWmhYY1dLdVRYWktnYUY1Vm5icjFjQjRYTE5FcytBMEpFWHRPWGlya3h5UlhZVHlwcEN1RlZ2cnF0R040a3RaVm1sVVNoT2dzUWtsVVgrNjNhaG1ZU1N0L09KNTZnMGtET3N0LzRuWjVZMHY0eHkvVXhrejIvRWtuOVROaEg0N3NLTllPQXNRTmpIRVhLVkhYdkQwSVlEU2NUZmc3SXFUUHBPTzNLamcvbFh2ZkhoNnhLOEpRWmN6NUR5VVdjS1Q3Vmp1Y0d0MGtSL1hMMkhVdmZHMU5xV2ZuN1hEQjRZOVFIS0Jpd0dUK0tmbVFHRkVaMGIrSFZtR0w0b2hoOUxUK2Y5Kzc5ZjdiLzhOcCtUN0pqZnpNM0JLLzM0NjFtektIR0NSY3c4V3dJamUxOC9mRDBJVWkzREZqSnBPdTM3aGl0YVo3TWZuZWMzSm5FRHNITjRyTElWVlphTWJuU0VNTW5oR1BPWlZGdVNSOGtuZzdjenMzRzI2SE96ZzEiLCJtYWMiOiI5ZmVjYmY0MzkxYmFiOThiMzc1N2E0YTEyMTY2OGIxZmNiMGY2YTc2ZGUxMDE0MTM0YTZjMmJjZDFmNDYxYjEwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:20:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRxaDVLZnZCY09veTd4dFoyME1mSEE9PSIsInZhbHVlIjoiNENDR1h6aUZyNC8ybE5Od1B3eTU1Uk5JNDRQUHloc0hUcHlXZGdpMkc5a2h1S2JJbE9IdzRLclBkdXF6NFFNN2F3Y0k4SDRCelZydEw4WHREd1hrd3BkSlJ5M3ZGd3hYcitvYVAreWg0MVVmU1M0dHBmeXlEVkNhVlM2S2FEUjRoTXRNc2V6YmpESmhJWEZZT1JZMHZrUjRhWncyZTNBOVBZSGo4dmtuL2NtbXRsUkpDY0FXejNrMnl5UlFTZHJyRW4yZnpzNmVGNW9JRFJPVm1JMFF4TGd1OEIzajdFRmNzcGF0c1FCU2cxc1JlS043Qk5SU0FkdFJyb1RkTTVOclRrbzRwZVZjSElTdStUMmlvei9NZnhId2RnSkhTR3IyT3dZY1gvYjllMHNzQWR6N3UxMlp4bVRoNmh2T1VFZHBQWkJOS1V0WXl4MksyYmZmWXNPb3dWQTR1MGZzQytvR0Rhby9NMVZzTFdOczV5UXVCUVlhUTRUbGZoL2QxQTdQODJwdG93elFPYUp0aGVsMWdMZWVvSURMM2FXcEVyOTJxQmNaRW5LOEFSN0V6ZzJERHpsakFjQ3VJd0xPZXVnM3l2Y1JNYWJWb0ZrYndONmhZQ0RQM3FnRG5oQXZHWkJrdHRjbVZudUxqNExGQmhvK2VzUzUwN2Z4UWVFallkTFkiLCJtYWMiOiI0NDQ3OGQ0NWRlYmRmN2JhMWZhOTNlMDhlODEwYTcyNjRkNjMxMjQyZGQ4OTkwNzkwNjBlMGIzM2JlZGM3ZGIxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:20:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ildqb2t3ZkJxaWw1U2RWV2Y0K0RtQkE9PSIsInZhbHVlIjoiemFOUGlXU09tcjVUL1NwdW5LZHlyekhsWW5QQzhXV3oxTFR5a0x3cVZQUmwwc3lWcTM0b0JQNzB6RHAxcTdrVGQ3bnljQmRQeS9oYW82VU5RRHFqcFUwY1lrNXM0V1F5YXBGSnl1WTdnaWY1S29NcDVmK2pzQ2dJWU9HR3FDVE1kb1IrWmhYY1dLdVRYWktnYUY1Vm5icjFjQjRYTE5FcytBMEpFWHRPWGlya3h5UlhZVHlwcEN1RlZ2cnF0R040a3RaVm1sVVNoT2dzUWtsVVgrNjNhaG1ZU1N0L09KNTZnMGtET3N0LzRuWjVZMHY0eHkvVXhrejIvRWtuOVROaEg0N3NLTllPQXNRTmpIRVhLVkhYdkQwSVlEU2NUZmc3SXFUUHBPTzNLamcvbFh2ZkhoNnhLOEpRWmN6NUR5VVdjS1Q3Vmp1Y0d0MGtSL1hMMkhVdmZHMU5xV2ZuN1hEQjRZOVFIS0Jpd0dUK0tmbVFHRkVaMGIrSFZtR0w0b2hoOUxUK2Y5Kzc5ZjdiLzhOcCtUN0pqZnpNM0JLLzM0NjFtektIR0NSY3c4V3dJamUxOC9mRDBJVWkzREZqSnBPdTM3aGl0YVo3TWZuZWMzSm5FRHNITjRyTElWVlphTWJuU0VNTW5oR1BPWlZGdVNSOGtuZzdjenMzRzI2SE96ZzEiLCJtYWMiOiI5ZmVjYmY0MzkxYmFiOThiMzc1N2E0YTEyMTY2OGIxZmNiMGY2YTc2ZGUxMDE0MTM0YTZjMmJjZDFmNDYxYjEwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:20:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRxaDVLZnZCY09veTd4dFoyME1mSEE9PSIsInZhbHVlIjoiNENDR1h6aUZyNC8ybE5Od1B3eTU1Uk5JNDRQUHloc0hUcHlXZGdpMkc5a2h1S2JJbE9IdzRLclBkdXF6NFFNN2F3Y0k4SDRCelZydEw4WHREd1hrd3BkSlJ5M3ZGd3hYcitvYVAreWg0MVVmU1M0dHBmeXlEVkNhVlM2S2FEUjRoTXRNc2V6YmpESmhJWEZZT1JZMHZrUjRhWncyZTNBOVBZSGo4dmtuL2NtbXRsUkpDY0FXejNrMnl5UlFTZHJyRW4yZnpzNmVGNW9JRFJPVm1JMFF4TGd1OEIzajdFRmNzcGF0c1FCU2cxc1JlS043Qk5SU0FkdFJyb1RkTTVOclRrbzRwZVZjSElTdStUMmlvei9NZnhId2RnSkhTR3IyT3dZY1gvYjllMHNzQWR6N3UxMlp4bVRoNmh2T1VFZHBQWkJOS1V0WXl4MksyYmZmWXNPb3dWQTR1MGZzQytvR0Rhby9NMVZzTFdOczV5UXVCUVlhUTRUbGZoL2QxQTdQODJwdG93elFPYUp0aGVsMWdMZWVvSURMM2FXcEVyOTJxQmNaRW5LOEFSN0V6ZzJERHpsakFjQ3VJd0xPZXVnM3l2Y1JNYWJWb0ZrYndONmhZQ0RQM3FnRG5oQXZHWkJrdHRjbVZudUxqNExGQmhvK2VzUzUwN2Z4UWVFallkTFkiLCJtYWMiOiI0NDQ3OGQ0NWRlYmRmN2JhMWZhOTNlMDhlODEwYTcyNjRkNjMxMjQyZGQ4OTkwNzkwNjBlMGIzM2JlZGM3ZGIxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:20:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429678667\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2034351579 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IllIcE5GT2gzZUxNblQzWWtHQzRQQVE9PSIsInZhbHVlIjoiaG9UQVU0TDROZlhBQ2QzMDBqU1VvZz09IiwibWFjIjoiNDEzYThkZTIyYWViZDk3YmNjYjFhM2M0OGQ5NGEwZTI4OWExMDFiNjU1MjJmM2E3NDVkZjhkMWU5ODE3NWNjYyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034351579\", {\"maxDepth\":0})</script>\n"}}