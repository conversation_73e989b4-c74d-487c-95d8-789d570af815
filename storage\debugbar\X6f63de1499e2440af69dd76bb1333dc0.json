{"__meta": {"id": "X6f63de1499e2440af69dd76bb1333dc0", "datetime": "2025-06-28 16:21:45", "utime": **********.226827, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127704.723467, "end": **********.226848, "duration": 0.5033807754516602, "duration_str": "503ms", "measures": [{"label": "Booting", "start": 1751127704.723467, "relative_start": 0, "end": **********.154226, "relative_end": **********.154226, "duration": 0.4307589530944824, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.154235, "relative_start": 0.4307677745819092, "end": **********.22685, "relative_end": 2.1457672119140625e-06, "duration": 0.07261514663696289, "duration_str": "72.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45703360, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0033, "accumulated_duration_str": "3.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.191377, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 54.545}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.203422, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 54.545, "width_percent": 21.515}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.21278, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.061, "width_percent": 23.939}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-369954508 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127660348%7C43%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkV1ekdLYWM5MUlkb3VhOGlSc2pxK2c9PSIsInZhbHVlIjoiSGYxL3JVRUk3TUkzMGQvWjlHd0N0TlVPaXNaSlNOZXQ3RklTa01aaDRERzVCZ2R4MTR6QnZTd0ZRbmo1NXBKTkIyTFV2dTVITzl4RjY1TG1HNElLaUdrWG5MTkhVenhnaXl1QzhPQnNtRjhhWmJkdGRORDkza1F0a0MyV3NSUDBjMDJwTVlYdWN3ZXIxYlJMdng5WSt0WHB1R2hXZUpKQlExS2xpcitCa2FldjNqOTRTcFA3N1ZyY3JKbzErMDJFWlUxTHRKZ2pPNmtacFJ4NG8yQmdvRW1CRXB0VU5JTEpTdFd3Tkl0OGZldlBsbmpIdm9aa0RuTlR5eU1vQjZoNnFCTUtMSW9CZU1Jc2FHVXdDVWVTODJya1lLd3E1dVl6Tk15bG5NcFhRK3daTFZFRzRhR0ZmQjMrNnk0NzJ4ZVFZR1JVaDUzYjB6eCt4YzhtVXdaMUFPYm0rdEU3VkVvWW1zMHhTNS9zV1lwNjdaY0g3UUFQd3ArakhUMFdQQU1jN0NzNkoySWdVbTloQytPdGUzUnhDYjdMc0x2emxPSW1Ud0U4SSt4dmk0K0ZPKzdablRnU2N0bnAxWFAveWxKdW16UFFUTnQrejY2Z1UrVjZTdHFCV0Jtd2ZFNVorcU1kQUdoZnRUdk9lc3J4YVNEQmEyODdHQkQzYi9wSExwNUIiLCJtYWMiOiI0MDJkOWY5YmYzZWEzZDk4MTY5YjFmMzBiYjk4MDk0MDI0ZWRkOTRkN2JhNjVmY2Y0NzI0ZTdmMjA4OWNmMDExIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpMS2IzREZTYTJTT1Zza1o4RnpGZ0E9PSIsInZhbHVlIjoiMlJaR3ZDYXpyd1cwM3h2dU9WYi9OeU9aWjhjK1hkQUFBK011MS83RVNQbmE1ZjgzcUkvRW5ZZEpnQ1dEeGZ0bThGQ0RmbEdoL1d6ZEh2Z05xT3RMblZhYUlKVU5WbUh3MTJKMVplalN1bDh0NFR2VHJCSWZzeThjUzgrVlJNcE5zbGV1aWhncVp2MFc4Y1YrL0NyVGVuQ0RuSWJYS0ZZY29ReWdqNTJLaUQ2NktyWlVnbzNJUEltak42Y1o0M0p1ZDRIU1R1K0lsRU5KN2J5NkJXKy80WE12ZjErdTYyN1lqU2J5UTZERnFKMXc0bUdxd2M3WkVxbFZJQjNkL2FYcEtQUTdhVmM5L0pzbndpR2c1anpWeStVU0xzMk55QmFRcHlGaFFWQVZGZU1DSjRhN1Q2MUphSjhZeUNGcmZscityVEJJVTFYVW1FSUZaUnd6QnBtMnBtZUxsNnNOREpzeWNGTjUyanpRRWl0Y3JTL1hvbHlXMkk0ayt0M2JvZmFDNENVMTVRMEo0N3M4eWJReU5IdkFWR1kzZE1DaStLdzk1eklETUljeG5KSkFvZEMzOG9RZGJ1SzZRNC9aaW9ldHhoNGpxTzZ4d1pVWWJqdU9ZU21jbDBwK084bTFKQ3d3TkIwMnp4YVVUKzVjMXJpSDlFd1RQdWh1alE0OS93M3MiLCJtYWMiOiI5MDUyYTg5MGIwYjBkODE4ODRlMzg3ODllMTk1ZTg2NmQwZDQwYTBjNDkyN2FkNjE0ZTg3MTMwZTA3NTA1Y2NhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-369954508\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-855407668 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855407668\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1409942802 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:21:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZlTEUzZWRSanAyL2NGTTBEZWx2ZFE9PSIsInZhbHVlIjoiUVVFOHI0S2JTUlUzcDBnZjRucjY0bS9ZTk5UMjZjT3pDbWdGSnJKY3V0bkQ1UTlqclhmVUpzUFFBazltT3ZVVE5hSlBZVmhNa3ZaYzBUY2QzZG5la1VacnZ1c0VBMm1TS2hSa3B2R2VaRGxHek9PWVJtTDdlZXNJdmlzemtzWHBUODFkeUhCU3pSMHlJWi9YZjBuaGVUNlhIcDZ6QjdrSDM4emtlRGN0dUR5cllaSGNpRS9yTHkrQ3BjYlI2K21RMkdOR2RncEtVL3d6d3BSQVdkNGlUWjFQWUZyZXh2WlYrRjE5bHNLQUlXTytONWwwcmFLMUF5MkJHUWtWZldGNURGNGxCSjBpdHFuOGtiUGZZTUNjczNwZnJua25nMWFMSVhONzNGNkY4aDkrbWtDMXNFQW0zckI2VHB1UkpVNmV2WE5MWWRXd3JrNTkzU20rcXpnRVl5L09jSHAxUjA2Q085b3Bzbk95WUp0dXB2NEdEY2NkQURCNVpBOGVaRHZ5YkVlaHFPcFlWZ2QxMldubm5wNDZ1SXd4UStQQmVIZCtMTFEyUld3dDdrckp3enc0YU4zYmZVWTlyRE1jaWFuQWdObXlkNUZMQjNBTkRBZlg3RUJRajg5K2FJZW1NZFBNRklTS1RtY2lUM2JnRkxlMFpiQXg2REFSekhDRk9SMXQiLCJtYWMiOiI5OWRlOTU2Mjg3NmQzNTJkMzM4M2QyOTRiOWQ1ZWZlNzE1OGEyZTFkZTZlNjNlY2E2MGUzMDJmYjk5NjFlYzA1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjN4Q3N5dWVYSzdSTDRuUVg2UkY2aVE9PSIsInZhbHVlIjoiQnplSDBNWVh4NGgwdHlEQWNsd29uTFZhWFlEV2xXVTJBTjhMdUhxaUFUWkNqU1ZUU1U0eS9oRTUvY2E0ekJMdmlQQVkxQ0ZzTHNGdWxUZWVJcHZlTGJTMkdQazZiaFE0YVdlN0JHRUZLMmlTMWFIcTJMdWlTT09KZy8wMzdpNWR3dmc0QlBrMlFTQmR3bHZGVUIrOW85WEVuS2gwMitIWGwxdkthSmxhdWNhb0oveGhNL1NqSjk4eWlNVStsVGY1Z1Iydzlvc0J3c3hOWUt4Vm4vSDg2bk1jQXpIY1Q2U3FsMndTZlgxUDhNenBSS0lZaEI3NmhlUVloMHRRb0tCYlFGdzVONHVKYThGblk5UlNkYTQ2MUc0a1NlZU1HNG9tMlBNYTQ4UG5WMHV0TERPQWs2d2xuQ1dlVk9ETHJMd2lsc010dFB3NzFWbGJrblNIWUxnem1ST3JzZFpDTVNhNVdRTGd0bmE1TVljNXB6bjJYOGtPaDZ0MFdacmNHTTlYMDFDWlNic3RHMEdrZUltai9jNm01ZjBtb2FNQUpRQk9XWml5a1ZjWWFtVHFiNmRNZDhMc2xRV3dwNktzTkN3ME5ld2tsT1k2QlpyZXFWQXlRa0c5L29IdGZXbVM0STRsYThWTS9nVkVkOHRla1NaMHZHdVdTQVFmdmVTZXZtMXgiLCJtYWMiOiI1ZGI5YTE4ZjkyZDM3M2ZhZjhiNzgyZjFhYjBjZTU0Mzg0ZGI5NjQyOWEwNjcyNDNhYzczMmYxYzE1YmI2MDk0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZlTEUzZWRSanAyL2NGTTBEZWx2ZFE9PSIsInZhbHVlIjoiUVVFOHI0S2JTUlUzcDBnZjRucjY0bS9ZTk5UMjZjT3pDbWdGSnJKY3V0bkQ1UTlqclhmVUpzUFFBazltT3ZVVE5hSlBZVmhNa3ZaYzBUY2QzZG5la1VacnZ1c0VBMm1TS2hSa3B2R2VaRGxHek9PWVJtTDdlZXNJdmlzemtzWHBUODFkeUhCU3pSMHlJWi9YZjBuaGVUNlhIcDZ6QjdrSDM4emtlRGN0dUR5cllaSGNpRS9yTHkrQ3BjYlI2K21RMkdOR2RncEtVL3d6d3BSQVdkNGlUWjFQWUZyZXh2WlYrRjE5bHNLQUlXTytONWwwcmFLMUF5MkJHUWtWZldGNURGNGxCSjBpdHFuOGtiUGZZTUNjczNwZnJua25nMWFMSVhONzNGNkY4aDkrbWtDMXNFQW0zckI2VHB1UkpVNmV2WE5MWWRXd3JrNTkzU20rcXpnRVl5L09jSHAxUjA2Q085b3Bzbk95WUp0dXB2NEdEY2NkQURCNVpBOGVaRHZ5YkVlaHFPcFlWZ2QxMldubm5wNDZ1SXd4UStQQmVIZCtMTFEyUld3dDdrckp3enc0YU4zYmZVWTlyRE1jaWFuQWdObXlkNUZMQjNBTkRBZlg3RUJRajg5K2FJZW1NZFBNRklTS1RtY2lUM2JnRkxlMFpiQXg2REFSekhDRk9SMXQiLCJtYWMiOiI5OWRlOTU2Mjg3NmQzNTJkMzM4M2QyOTRiOWQ1ZWZlNzE1OGEyZTFkZTZlNjNlY2E2MGUzMDJmYjk5NjFlYzA1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjN4Q3N5dWVYSzdSTDRuUVg2UkY2aVE9PSIsInZhbHVlIjoiQnplSDBNWVh4NGgwdHlEQWNsd29uTFZhWFlEV2xXVTJBTjhMdUhxaUFUWkNqU1ZUU1U0eS9oRTUvY2E0ekJMdmlQQVkxQ0ZzTHNGdWxUZWVJcHZlTGJTMkdQazZiaFE0YVdlN0JHRUZLMmlTMWFIcTJMdWlTT09KZy8wMzdpNWR3dmc0QlBrMlFTQmR3bHZGVUIrOW85WEVuS2gwMitIWGwxdkthSmxhdWNhb0oveGhNL1NqSjk4eWlNVStsVGY1Z1Iydzlvc0J3c3hOWUt4Vm4vSDg2bk1jQXpIY1Q2U3FsMndTZlgxUDhNenBSS0lZaEI3NmhlUVloMHRRb0tCYlFGdzVONHVKYThGblk5UlNkYTQ2MUc0a1NlZU1HNG9tMlBNYTQ4UG5WMHV0TERPQWs2d2xuQ1dlVk9ETHJMd2lsc010dFB3NzFWbGJrblNIWUxnem1ST3JzZFpDTVNhNVdRTGd0bmE1TVljNXB6bjJYOGtPaDZ0MFdacmNHTTlYMDFDWlNic3RHMEdrZUltai9jNm01ZjBtb2FNQUpRQk9XWml5a1ZjWWFtVHFiNmRNZDhMc2xRV3dwNktzTkN3ME5ld2tsT1k2QlpyZXFWQXlRa0c5L29IdGZXbVM0STRsYThWTS9nVkVkOHRla1NaMHZHdVdTQVFmdmVTZXZtMXgiLCJtYWMiOiI1ZGI5YTE4ZjkyZDM3M2ZhZjhiNzgyZjFhYjBjZTU0Mzg0ZGI5NjQyOWEwNjcyNDNhYzczMmYxYzE1YmI2MDk0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409942802\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}