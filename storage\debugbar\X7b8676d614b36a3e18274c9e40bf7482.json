{"__meta": {"id": "X7b8676d614b36a3e18274c9e40bf7482", "datetime": "2025-06-28 16:03:41", "utime": **********.741812, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.251581, "end": **********.741826, "duration": 0.49024510383605957, "duration_str": "490ms", "measures": [{"label": "Booting", "start": **********.251581, "relative_start": 0, "end": **********.616599, "relative_end": **********.616599, "duration": 0.3650181293487549, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.61661, "relative_start": 0.36502909660339355, "end": **********.741828, "relative_end": 1.9073486328125e-06, "duration": 0.12521791458129883, "duration_str": "125ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50301576, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.06316999999999999, "accumulated_duration_str": "63.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6467052, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.96}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.657197, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 2.96, "width_percent": 1.013}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('100', 0, 22, 8, '2025-06-28 16:03:41', '2025-06-28 16:03:41')", "type": "query", "params": [], "bindings": ["100", "0", "22", "8", "2025-06-28 16:03:41", "2025-06-28 16:03:41"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.669636, "duration": 0.054119999999999994, "duration_str": "54.12ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 3.973, "width_percent": 85.674}, {"sql": "select * from `financial_records` where (`shift_id` = 53) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["53"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.725503, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 89.647, "width_percent": 1.219}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (53, '100', 22, '2025-06-28 16:03:41', '2025-06-28 16:03:41')", "type": "query", "params": [], "bindings": ["53", "100", "22", "2025-06-28 16:03:41", "2025-06-28 16:03:41"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.727447, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 90.866, "width_percent": 5.256}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-28 16:03:41' where `id` = 22", "type": "query", "params": [], "bindings": ["0", "2025-06-28 16:03:41", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.731851, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 96.122, "width_percent": 3.878}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "success": "Opening Balance has been set successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-1662127536 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1662127536\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1666344233 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1666344233\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1296446000 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296446000\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-506814700 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">67</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; XSRF-TOKEN=eyJpdiI6IlNLclB2R0NLS2x5RkZwUlpnWHZtQ1E9PSIsInZhbHVlIjoiM1dhZUxKdUloT05nVSszTXJabEhHVkt1cSttc2JyZzJJYVhib1dhL1dYbThhTVhoVVEzMU1mbWlhYjdFOTBLWS9oYWMxKzZHVzBROFA5NGhxTmRjL1NyNDZ6NW5CUEs2NlBva0JnQkdMUG9abEZqcjNKMXRnUk85cGZNckVTclN2YkxmSlFtbGpFMTdGditycGVCWGhydnFRTWxhK3poVCsyOStqa2tPcFpOSGNBR01CZmp1b25wK0c4Wm1EaGo2Nkk2ejVZaUVvM2xDSXdaRmNtWms3NnRneVRhMXdhVHh4K0xicFVuZjlKdnkvSzJ1Z3JnQ2JHVW5XczlXWEhzUHZXZUg1N29JeXJJOFlBWGpzYlRjSFhjQ3dmVUZKbzhYQkZqSXQ1WG5VSzZ6L0I1VHpWV0FpS1poOTdOUW00ZDlHZyt3bEx0TjAyR0VvUERFWnhQOTZ3LzQ1V1BVSFJFNHNlQ0x6S01ETDFYcDI4UVV4RWk2bWI3TXR6U0lSbW5iSWNWQk8vNlRXQ3pwb2diMnBWckNQbGJML0xGdHhRbkNScE9GTWtwU3RSN2dVTGlTTHdnUUlPUmpId0p2aFdhVWhteVlWSWxWNHNQK0JSaUNKSXV4VXlQVSt1anF5VWRoZkFRRW9KZkZ5MEYvN2NNR3k2RnJlN2x4d2NxL0JoT3YiLCJtYWMiOiI0MWY5MmVmMTI1MDk3Mjc4Yzg5OTAxOGE2MGJmOTVjZGY5MjZmNGFmNGZhYmQ4ODRkNTg4NDc1ZjNiZjUzZjA1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitMZFBOS2ZWZUFKbDN0NmxFMXhhQXc9PSIsInZhbHVlIjoiZnJ6aWpWUzRET0NVN0pXOEtoUk1qeEMrY1ZoV0FoQ2VZOVpoNjdwci92OXYyZ05Kby9aZlZDWVNvZ1hXSHF5WXlsd21qRnEwVXpwaXYwYnhKS2JOZ1BKeUNObUtHem1GSkNRbnVuWVBHUlhSUUt0VzRkSExUOTFEQTdGSXg5YmZXREQ5RmdFaDJQTjQ0SWZuMkVybFRWTlZmNHVIRFEvMVBtQUJCY2s4VTc1V3RmRUNPQ3dhRXo1OFU4dWphaFFwQW9SaFp1TGkvZ1hlUzhYRnF1cWsvNUVDVDEzRGM1dStpRmxVSG9UVG12U0wwSFNwc3NsczBKdXRnUEJDbUN1TXB6TXV4ZzlWTUY0UFArak1WWDZlUXNPNEZIRUI3YW1JbjlERmMrSldPbCtLRzFtUysxOWIyY0liY2FuQmdIMDloWGJNQTJhQ0F4ZG1Ld1RvbEdmeUZ5T3dQQUNLR3V6bEdlQVF5QzRFcXRNL3lJT3ZQdmpnTUFZdW00aENkY1JzSUlBVHM4R01HN1ArMVBVVnhnQ1dmc0lReDE5RnZIMWFXOFlkUHNlRHRBT1BuUjkvZG9sUFdaSGdrVGkyOUVpeWtXbFpRSHQ4QnhLL0gyVG9sbUNYNlJoN2lnSkJValdqdzZMTyttRlBTYytMUFQ1MWJnTkxOeGZtZDBTN2pJYWUiLCJtYWMiOiIxNTUzYWFjYWEzNjE0MzRkZTBhZDY3NzU5ZjA2ZmQ4MjA4NWMzY2JiNWQ1YTg3ODRiZmU0MTYzNDQ4NTJlOGYwIiwidGFnIjoiIn0%3D; _clsk=1mcm0n0%7C1751126618953%7C20%7C1%7Cz.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-506814700\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-103927437 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-103927437\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1205649198 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkMvNHV3MUhEMmxjWmFOZHBCNXdteEE9PSIsInZhbHVlIjoiUlFBQUxoK3V4L25kTjQ4Y014RFVsZHFZMTJhL05nZk9WZzVvcWtpbXhTb1JiZkZPSjQ5cVZQMEw1eHVUMXl2Z1ZES1IwRE9Ib2RKNmRrQnFNSEUzV1o2azJGNkxMeUdGRnVaZHI5K01OQXIwaWd3Lzh6SmxCMXErMTFrampuNjJEOXVrbld0ZUpZU3ZFUUE4ejg4ZTZoU3hHQVh3b256T2dFMEg3V0hQT2dNT0JHTG9Ib1RydTZIOGl1eWJYYnk3cnBPeGoyMHB5aGFHbWdSdEJJYWlBMks5LzIwVEc0QkRSTytUK25JWUVmSUlvODdmQjhmZDhMWFBpMDFrQVBueUlRdWIzTnNTNXFWY2R0M2hScmMxRzlHbW1qb0ZpcWVGS2NYNkN3K0RMYzRlNEhIcm9zRW1vSkZOSlpVSUN2aEFDdzhVdDZkeEQ4eW52Y0twQlc3ZjZ6Q3VTT1FlWFhQcUdPeCtUd2JZN2ZpVDJ1aENQalUva1VTK3V6clB0SmFYaHhQTi8yaG5PeWxEelRsWS9ubUpYZGozVWF6Tkt4RDdteTg2RnIyRVVSYXM5KzRUQjIrNW9RNmFoWWJ6K0gvOERuRlNuSTJzYmJtbnpXMW9tMEcwOW0wWVlBbVhIQ3dCbkdaM1kyeWJwNDBQWENtTCtFdmJVL25RTzRrL0xUbTMiLCJtYWMiOiI1MGZlOWM4OGIzNzE1MTNjYzE4MzQyZjJiNTc1NDYyYjMzOTYyYTQ3MmExNDk0YmQ2MjQwZjM3M2M3MjlhODhhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjhZZzR0dDJUTElOSDcrckRNSU5rNnc9PSIsInZhbHVlIjoiRUJrdUswOTQzazR3dHVEM3FpVjVCeWtNRlFrVEdkLytjMlBib2dkQTRtWDRTelJsekhSU1lXa0hyTGlQRDBCYWFaY21sTm5RRHRTK25oZWxsNG16S01XdWNtcHFicUpUa1dXbVc5VTBGeS9Rd1haQ2F1QjlONWJlY0lGTkZGV1dNaGI4YWpHbmdBcE9PTlY4eGdrczNVclZQS2RtUVpubGJuL3dqVVd4RFdxV09PVTRQc2c2NE9wM2pOYkpKamdORnNOY1hxd0dFelVvVWxBTFZuQ241OGQ0UGthb3BiR3VrU2QrbU03cTY0NFZXQVV5OTd5MXFIV1NyMHJHMWZ1VkJSZE94ZXphdkZVYzU2WHJPTEN5c1Y3MUNTWW5EMDZYbENFUkRvdGVBSFBFZGc4ejdqWE01YnFvcFZTR1hrNDdxNm04eFltRnlwUG1xc0dWVU1YNGVPWEp3Z0kvQVdVc2tVS0tLTUlQWHRudm5peGpGSEhCbkhuWEhnTk00TDNTZ0Evd09vVGp2MzVTcTJRaXU4ejJHdUp5RnNTVHFKblJBck15ZGFJalpRczVJdFB6eTE4QWRWcmYrK2JpYktTMm5mWFVicnZVZXpITkRDczd2Wnp2RXllalFnNEw0aW5XYWQzRDlUN0xmbHFBKzQxcSt1V0ZvYi9NeFFqUWFQRGsiLCJtYWMiOiI2MDMxZDFlYWVlMTljZDYwMzg5OTNhODMyNDk3N2VmOWI3NDVkNjMxNzk3NWE2M2NiOTExYmU2YmZlYTIxYzNlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkMvNHV3MUhEMmxjWmFOZHBCNXdteEE9PSIsInZhbHVlIjoiUlFBQUxoK3V4L25kTjQ4Y014RFVsZHFZMTJhL05nZk9WZzVvcWtpbXhTb1JiZkZPSjQ5cVZQMEw1eHVUMXl2Z1ZES1IwRE9Ib2RKNmRrQnFNSEUzV1o2azJGNkxMeUdGRnVaZHI5K01OQXIwaWd3Lzh6SmxCMXErMTFrampuNjJEOXVrbld0ZUpZU3ZFUUE4ejg4ZTZoU3hHQVh3b256T2dFMEg3V0hQT2dNT0JHTG9Ib1RydTZIOGl1eWJYYnk3cnBPeGoyMHB5aGFHbWdSdEJJYWlBMks5LzIwVEc0QkRSTytUK25JWUVmSUlvODdmQjhmZDhMWFBpMDFrQVBueUlRdWIzTnNTNXFWY2R0M2hScmMxRzlHbW1qb0ZpcWVGS2NYNkN3K0RMYzRlNEhIcm9zRW1vSkZOSlpVSUN2aEFDdzhVdDZkeEQ4eW52Y0twQlc3ZjZ6Q3VTT1FlWFhQcUdPeCtUd2JZN2ZpVDJ1aENQalUva1VTK3V6clB0SmFYaHhQTi8yaG5PeWxEelRsWS9ubUpYZGozVWF6Tkt4RDdteTg2RnIyRVVSYXM5KzRUQjIrNW9RNmFoWWJ6K0gvOERuRlNuSTJzYmJtbnpXMW9tMEcwOW0wWVlBbVhIQ3dCbkdaM1kyeWJwNDBQWENtTCtFdmJVL25RTzRrL0xUbTMiLCJtYWMiOiI1MGZlOWM4OGIzNzE1MTNjYzE4MzQyZjJiNTc1NDYyYjMzOTYyYTQ3MmExNDk0YmQ2MjQwZjM3M2M3MjlhODhhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjhZZzR0dDJUTElOSDcrckRNSU5rNnc9PSIsInZhbHVlIjoiRUJrdUswOTQzazR3dHVEM3FpVjVCeWtNRlFrVEdkLytjMlBib2dkQTRtWDRTelJsekhSU1lXa0hyTGlQRDBCYWFaY21sTm5RRHRTK25oZWxsNG16S01XdWNtcHFicUpUa1dXbVc5VTBGeS9Rd1haQ2F1QjlONWJlY0lGTkZGV1dNaGI4YWpHbmdBcE9PTlY4eGdrczNVclZQS2RtUVpubGJuL3dqVVd4RFdxV09PVTRQc2c2NE9wM2pOYkpKamdORnNOY1hxd0dFelVvVWxBTFZuQ241OGQ0UGthb3BiR3VrU2QrbU03cTY0NFZXQVV5OTd5MXFIV1NyMHJHMWZ1VkJSZE94ZXphdkZVYzU2WHJPTEN5c1Y3MUNTWW5EMDZYbENFUkRvdGVBSFBFZGc4ejdqWE01YnFvcFZTR1hrNDdxNm04eFltRnlwUG1xc0dWVU1YNGVPWEp3Z0kvQVdVc2tVS0tLTUlQWHRudm5peGpGSEhCbkhuWEhnTk00TDNTZ0Evd09vVGp2MzVTcTJRaXU4ejJHdUp5RnNTVHFKblJBck15ZGFJalpRczVJdFB6eTE4QWRWcmYrK2JpYktTMm5mWFVicnZVZXpITkRDczd2Wnp2RXllalFnNEw0aW5XYWQzRDlUN0xmbHFBKzQxcSt1V0ZvYi9NeFFqUWFQRGsiLCJtYWMiOiI2MDMxZDFlYWVlMTljZDYwMzg5OTNhODMyNDk3N2VmOWI3NDVkNjMxNzk3NWE2M2NiOTExYmU2YmZlYTIxYzNlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205649198\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1863038220 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Opening Balance has been set successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863038220\", {\"maxDepth\":0})</script>\n"}}