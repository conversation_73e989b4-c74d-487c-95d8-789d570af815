{"__meta": {"id": "X594c65c42631c14a6cb47e190703585b", "datetime": "2025-06-28 15:19:22", "utime": **********.463404, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.082685, "end": **********.463422, "duration": 0.3807370662689209, "duration_str": "381ms", "measures": [{"label": "Booting", "start": **********.082685, "relative_start": 0, "end": **********.404968, "relative_end": **********.404968, "duration": 0.3222830295562744, "duration_str": "322ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.404977, "relative_start": 0.3222920894622803, "end": **********.463424, "relative_end": 1.9073486328125e-06, "duration": 0.05844688415527344, "duration_str": "58.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45721456, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1942\" onclick=\"\">app/Http/Controllers/PosController.php:1942-2000</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00672, "accumulated_duration_str": "6.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.435102, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 23.214}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4446409, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 23.214, "width_percent": 8.185}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*************%' or `sku` LIKE '%*************%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*************%", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1972}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.447283, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1972", "source": "app/Http/Controllers/PosController.php:1972", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1972", "ajax": false, "filename": "PosController.php", "line": "1972"}, "connection": "kdmkjkqknb", "start_percent": 31.399, "width_percent": 34.375}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1972}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.452358, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1972", "source": "app/Http/Controllers/PosController.php:1972", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1972", "ajax": false, "filename": "PosController.php", "line": "1972"}, "connection": "kdmkjkqknb", "start_percent": 65.774, "width_percent": 31.25}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1985}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.456557, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1985", "source": "app/Http/Controllers/PosController.php:1985", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1985", "ajax": false, "filename": "PosController.php", "line": "1985"}, "connection": "kdmkjkqknb", "start_percent": 97.024, "width_percent": 2.976}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1962948985 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1962948985\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-237391387 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-237391387\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1167599087 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1167599087\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-988784330 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123960877%7C11%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRETkNWQ3FUZXN1dHFwOTIxR1R0amc9PSIsInZhbHVlIjoicmJmY1BKaWxGb0I0bkFnRFJwd0FxTkYyWi9KVC90Z3ZhamNZRTBrMWNKbHMrZmxzdWlWU0hsaG0wU1h5NWR1UCtBcmJiS2ZpazBvRDFtRnhqaTdJSnFuL1l3QlA1WFpKWHBzekpuTzBnaWoya2FEcSsxcXFIWGorNTA4ZXpRYVJaWXgyaVl5MVk3S2VaVzZSSEJMWkY0SzFOL3pEVERXYTBMRjI2T1MvRkU3VVZrWWV5ZWY3Y0kwdzY4TEJUam9vVWVGOTM4L3gvcjhaN2F2QXh5akhUREhtKy8xWTdrNnU5RmY1LytzckFzOVZENTJFYmQrb1FSRjhiTW00QzRNOVBWNTJVVUNBZS95NUg0dmNMcmk3TmlTd3lrTmc4Z0ZMQkhEMzVBalhDMWowd1VkMVZEb3hIdmw0WlpFZnFCVk9sTS8rTzZLYU0vR1lFcDllZUtwSGpackdXeXZnekFUaWIrclJ1R0ZxZ2RiSm5mSzl2amt1MlVGcE5sNkFJZ01rMUJha0lQL280bThrSE9lSHNCaTg4YkVuOFErWjUvWWxCbzR2ZG5HTkNKZU5PSTBITFdmS0E0cXEyaG9GR0ltdGxTRnBhMkxITUxxSlBXdjN5OVJuRHcyNklBVXY0eU0xZXpxRUJqVHVRT09oWm5yMTE0V3V3Zm1HTHZHV0tMdUgiLCJtYWMiOiJmZDRjMTQ0OTM2YzEzMTdkNjYyOTMxZWUwMWU2ODk4NDU5ZTdhNGNkYmNiNTJhNzRkNzVmY2FlZTA1ZDk0NmJjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjU2ajhkL0xaRmUySXBFVW5QTy9wWnc9PSIsInZhbHVlIjoiYThvTkVJeXhtMXdUR0xCTStaVmhiYmdOd0xTYnpGZndKYVNRVmxvaTE3NmI1cURjZXN4UHVwbGwxZzhFMVp0aWJOVlNycDRXdVd6dm43UGJBT0x4RmhoTnIxTWJDZzBpdkRMMHRFSDNyVkM5V1MxUHhnY09kc0tmK3NFUG84WWlHbWhaZndRM1JNUGJXWnZRdkd3OHdsTkVkajFQZ2JiYk9uZ2JadnIyc0FUWW5lSzFYRUJTOHhlY0RrRjdlcThKUk5pdkhwN3YyaVI3OUp6cWJ4b2dLZ3lSU2taV3d0YndmRzJDMTNEbTdBaXMwTndvaTB0Y1cwZHZhRlROQ1pzTXZXUW1tN1dKemhLYzNUVUxoSG9UbkZzbFI1c0NmUTBNMEp6blNjMmR0eGx6anpLRHBJL243SDNWc245UmxjcFBZY0RwdU10UTRrK2ljUnZlWDYycEd4VUxNcS9lYmZLNTNEWlRqT3huRUg1ZytMSUpCS29xNFZOTE04WVJ1MjM5MURyQnhpM2cxQk8yc29LclIyUlFoZDhPOC9HNXNqeDlGWXdLaVpkc3JNQjZjaXBzYThPMGdLU0ZIMlBwbm5Jb2lteGRXaDJOM2ExNUEwWXc3Rjd5L0ZZMlo1OU9kUWJORCtLZXE2QTJFcHFSSVN4Z29XamVtQ0dZdUR2cmNiV2QiLCJtYWMiOiI2ZTk1NzQyMGJhYmRiYTY5NTQxZDVjZDZkYmU3NGEwNDYyOTU5MmFmZjI2ODlhMTU1MmYxMmNjMTBlZjg1Y2MxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-988784330\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-520481516 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520481516\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1951648783 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:19:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inc0eDF6aE1acU96WVRLeDZEZ2p5TUE9PSIsInZhbHVlIjoiWVNxVDJBb0twMlM4VHUzeklkVUx2UHZZMFZjTlpyeXVzSDlDYjUrT1BEbnFvYXllNUV0THZzRUc2VzZzSlZFWkRiSlU4SDhJWk1oUnZmR2VWQngyS3FVRE56VEswT2srME5PY3pHQnZrTkdDUGplK29zcEpRekJwRitlWkhKTGlqdnJKOUVCZ2dnRGs3MFp4NlA2LzZxL0tlRmtMSDAzQzl3UllQY21sNXdVUnRCVHlsa0ZycWt5Q2dGR3pqVmJhOTg2UWlFL1BoS2FzMGhWNjNpZFlzTjIrcGxJRnlZQ3VyaVQ3UnFaVkxuUGRBMU93K3FCbkpISEhWY1A2bEJjUzlXdWtJR1lhaDNVUzA0amcvdnlCTi9vdjl0TEsrMEJySWJuTXM1bTluSlo1eGdCRUVNQXRtK3BSWHRuMkVuSzBwTWJxS2xaN2NDYUtVRjBuQ2hXaU04U1p2QklhVHRCRTdrbTM2UFpqQVQ0L1BFKzNneTVJcW1jU1NiUHA5MUlsd3hSNXkrZXNWUU1PK0hFaWt5ZjdhUmdzdVYzTXhVL1lFR1NFNTd3cmdnV0dDUFJHSFB4bnlTKzhNZnF2QmI5UVBiRXZ6bWI2VXcxSEpBSDFXN3pJSVNyZC82Y2pKSmZDTlpud1NqLzdxbG1KUVQzRys5Z3NJcEVtUjZnUzZwV2QiLCJtYWMiOiJjMDBlMzk5ZGU1MGIyNGZkYjcyOTNmYTA5YWNkYzk2NmU2MmZmZTA0NjcyNTUxZTMwOGM3MDZlNzkwMjI0NmM2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1ZcEZ4cGFBZ1BoNWZRcE95MTNXL3c9PSIsInZhbHVlIjoiZWZIVDNaUWtVQzcvVlNBR1BjTjlEZnJrTDYzUnlJSTlwaTV2NzhLTUpWSlExVG1ZaXAva3IzUDhoemRZM3FZR3RVcHowNFRZS3NyNDA1cjMzVDZFT0w5Z3JBaVNoeHErS1VSMU8yV2FJQ2psQTdtS0dNdW5weDRLSS8vZXZ2eExyT0hFWGNPSVl3c3hzVjQ1SmxOb0oyUXBteGhPVmpxdURUUElBZXA2OW53anhBL21BM0lNbHgwWGxaZjBvTnpRWHQvc0NHZXFlYk5kZUVhRmRBdkIzZzZPOHA5Q09SSGxobHVNelVtOGxlOE9YNkFjbURNRm1iSGJ0Ry9vb1g2NWluNXdqOUs0VEJ1am5GWXBoay9jUSt2RVJBWklqRTdsNTFzSXNEN3dIT1VMbWVwbENVVkhRdy9ZUmdGS1dYVlBwc3J2TmIwbVBCcFZTMTV5bFd2RDZTQ1E1YWN6empldzNYRTVtbFIwVEFiMVlCVEtOUUR2SlRNSUdEOG4vZ0ViY2NKcSt6M29wVWtueXRVVXE4cWtZYW91MnhKN1BTTFpoRkNOWkRyUnFMTkdTSCtkVHF1aHEzM0NOY0hFSS9HNncreVQyQVg0MzB2UUYxdks0Z0EwTWdncVJXa3JZa2p4Ymc3aEdaMDlPUVVEcXlLR2hsNmNVakh6eTFBVFdtZVoiLCJtYWMiOiI0Yjg0YzQ5ZjIxNTgzMGY2YWIyZmNlZjg0YzZhM2M2M2MyM2E5MjQ0OWZlM2MwOTdhZTdiM2Q3OGNmMmY5ODkwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inc0eDF6aE1acU96WVRLeDZEZ2p5TUE9PSIsInZhbHVlIjoiWVNxVDJBb0twMlM4VHUzeklkVUx2UHZZMFZjTlpyeXVzSDlDYjUrT1BEbnFvYXllNUV0THZzRUc2VzZzSlZFWkRiSlU4SDhJWk1oUnZmR2VWQngyS3FVRE56VEswT2srME5PY3pHQnZrTkdDUGplK29zcEpRekJwRitlWkhKTGlqdnJKOUVCZ2dnRGs3MFp4NlA2LzZxL0tlRmtMSDAzQzl3UllQY21sNXdVUnRCVHlsa0ZycWt5Q2dGR3pqVmJhOTg2UWlFL1BoS2FzMGhWNjNpZFlzTjIrcGxJRnlZQ3VyaVQ3UnFaVkxuUGRBMU93K3FCbkpISEhWY1A2bEJjUzlXdWtJR1lhaDNVUzA0amcvdnlCTi9vdjl0TEsrMEJySWJuTXM1bTluSlo1eGdCRUVNQXRtK3BSWHRuMkVuSzBwTWJxS2xaN2NDYUtVRjBuQ2hXaU04U1p2QklhVHRCRTdrbTM2UFpqQVQ0L1BFKzNneTVJcW1jU1NiUHA5MUlsd3hSNXkrZXNWUU1PK0hFaWt5ZjdhUmdzdVYzTXhVL1lFR1NFNTd3cmdnV0dDUFJHSFB4bnlTKzhNZnF2QmI5UVBiRXZ6bWI2VXcxSEpBSDFXN3pJSVNyZC82Y2pKSmZDTlpud1NqLzdxbG1KUVQzRys5Z3NJcEVtUjZnUzZwV2QiLCJtYWMiOiJjMDBlMzk5ZGU1MGIyNGZkYjcyOTNmYTA5YWNkYzk2NmU2MmZmZTA0NjcyNTUxZTMwOGM3MDZlNzkwMjI0NmM2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1ZcEZ4cGFBZ1BoNWZRcE95MTNXL3c9PSIsInZhbHVlIjoiZWZIVDNaUWtVQzcvVlNBR1BjTjlEZnJrTDYzUnlJSTlwaTV2NzhLTUpWSlExVG1ZaXAva3IzUDhoemRZM3FZR3RVcHowNFRZS3NyNDA1cjMzVDZFT0w5Z3JBaVNoeHErS1VSMU8yV2FJQ2psQTdtS0dNdW5weDRLSS8vZXZ2eExyT0hFWGNPSVl3c3hzVjQ1SmxOb0oyUXBteGhPVmpxdURUUElBZXA2OW53anhBL21BM0lNbHgwWGxaZjBvTnpRWHQvc0NHZXFlYk5kZUVhRmRBdkIzZzZPOHA5Q09SSGxobHVNelVtOGxlOE9YNkFjbURNRm1iSGJ0Ry9vb1g2NWluNXdqOUs0VEJ1am5GWXBoay9jUSt2RVJBWklqRTdsNTFzSXNEN3dIT1VMbWVwbENVVkhRdy9ZUmdGS1dYVlBwc3J2TmIwbVBCcFZTMTV5bFd2RDZTQ1E1YWN6empldzNYRTVtbFIwVEFiMVlCVEtOUUR2SlRNSUdEOG4vZ0ViY2NKcSt6M29wVWtueXRVVXE4cWtZYW91MnhKN1BTTFpoRkNOWkRyUnFMTkdTSCtkVHF1aHEzM0NOY0hFSS9HNncreVQyQVg0MzB2UUYxdks0Z0EwTWdncVJXa3JZa2p4Ymc3aEdaMDlPUVVEcXlLR2hsNmNVakh6eTFBVFdtZVoiLCJtYWMiOiI0Yjg0YzQ5ZjIxNTgzMGY2YWIyZmNlZjg0YzZhM2M2M2MyM2E5MjQ0OWZlM2MwOTdhZTdiM2Q3OGNmMmY5ODkwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951648783\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1942615766 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1942615766\", {\"maxDepth\":0})</script>\n"}}