{"__meta": {"id": "Xd2a3dcea2dfaa31fd894caf86cd0b5dc", "datetime": "2025-06-28 16:02:39", "utime": **********.488844, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126558.95133, "end": **********.488859, "duration": 0.5375289916992188, "duration_str": "538ms", "measures": [{"label": "Booting", "start": 1751126558.95133, "relative_start": 0, "end": **********.416865, "relative_end": **********.416865, "duration": 0.46553516387939453, "duration_str": "466ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.416882, "relative_start": 0.46555209159851074, "end": **********.48886, "relative_end": 9.5367431640625e-07, "duration": 0.07197785377502441, "duration_str": "71.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46420120, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2428\" onclick=\"\">app/Http/Controllers/PosController.php:2428-2462</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00253, "accumulated_duration_str": "2.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.470144, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.123}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.480829, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.123, "width_percent": 26.877}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1461/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-339661643 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-339661643\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2095716530 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2095716530\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1498782079 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1498782079\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1469272344 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126544321%7C12%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitxbCtuYjNKQ3ppbklhZnIyOGRCVnc9PSIsInZhbHVlIjoiU3luV0dpOW1kWE1BK0syRlJlT0RxYVlYUFZXaFN6cnllRUJMNEU5QTFCbWhCSmlkT0xwN3JSdXlTUndMM1RnZ2JOYmx5M0JJNzlXYm9ldkZnY3d3ZkRNMWtscjcvalB2YXIzTTN5c3RpRFdBRVBhMkJHQTN0MWljZVNZQWxyN0Z4ZGJyNEFvbUNDZXhwdk0xOVk5UlMrL3JWTTlLcDV0dTNYV05vYi9pOUR5bk5Ra0twSXpyYklhWFhGOFVVMU5pSktBY1MweE1sdENnY0ZrSjdTbHVrb01FZVlzcGx3eTUxWjJNaVRwL2RRRHV0QVp4cDhvNnZUTUh0QTAwT094YU9NeURyZXRaZ1JxQ0ppK2pReG5CUTJVeWVjMkZLYjNnRWtCTlFtVHVvSGxHd2JUZkoreGRPc001M09aUm8rUmhMTW4vaXhrZVJLYXoxOGF4d0xwZU1aVXBoZ1hYS1JCTFUzaHNNcW1tS0hvckhiMFJLZEpidk1iT29ZUmI5Zkt2WmdlVTZBRVRXRUNPOUhZTmQ0cmZLejdUajdheEJGOWVUOEVHRmlrUG1pRFNyeGNqVnpiZ2M0aC9hZzM0K1JpWHFkbEkvNWp5cUlyVHZHVXNYbU53SUxSMVlac0pXbk9rVm53bnNiNUNQM0NkVDhDYTduanYzU3NnSkp1MEdsSE8iLCJtYWMiOiIxZmMxNmMxN2U2MDI1NmU4NTY2NGZkMDQ4MTg5YjFiNjJiZGU1YzNjYjgxOWZhNzEzY2U2ZTI3YjA3MTdjMzZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJ2Z3ZHZWExMGEzd3VCaWJHQVN5M3c9PSIsInZhbHVlIjoiM09Xak5EcW8yT041LzRMZ21reE9kSWEyS2lMTDNidWJINnZ1TUc5T3BlV0tza0JlY0hoRUt6OStLUUtmQ1JiYmdMcmYwMndvbk5KeGthTEJ4UTRBK25PQm54V3ZScmZMVmt2b3VwU2FLYVlhNU1OMUIvajBFOWVUSnZ4QzBFNlFTWnJ5NXdXSzdHVW80dXRTMTNPME9kMHhvaUkxUFFSREh5WlZ4LzhlL1VmZjVRZmVZSk10MnBlOTRyN2UwUHhYTU5wNExaVHgrajdHbmJWUzBOcy81dXpQQVA2OHh2WndnVmpHQ05kMzZIc21rL1o1U1EwVjVWWklqWm5Pai96dHQ3eFpzU1Y5cEFkc25oQk15U2RFdUlHc3F2MkZjQTJCeDc5OHJZQWZhZWdoVzRQNTRuaENjZUtRYXpIZXBoaEJ5dDd5a3FJb0FaR0lqeXRFelVuek1CdXUzTldkTFlkWFVQUXplVVlPUVozSmYxZFN4OGZCNCs4a3B4aHo0eU5BaU1UNDB5eFZCWDdXK0lZRjk5a1A1V2RtWkhETmRHZGtGS0VORms0MlJjY2hlOVpyRWxHQVkyTmxJQ1FBd29UaWRlY1ZJQkJOQ3Y2OExKZU5iMEJkUVVkcnViczY2dnJJWkZsZmhxWmVoTnJaTFdCeEdlbHVoWHZtMXVzcXhLeFMiLCJtYWMiOiJhNWI5MDI2Mzg0OTk5Mjk0OTIxYzk0MjgxNDg2ZmIzYWQ2MWJhN2NhNWM3OTcxMGQ2YzZhMTNjZGJkMmM0M2ZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469272344\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-257648170 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257648170\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1736384403 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpURVA5Vm5BU2x5WU1qdUhLVXB6MlE9PSIsInZhbHVlIjoiWGhDZ3NnWis0MFBUQjlPM1hwZDdRZ2NNUmRSVnQxRWtJajErQ3h0QjBPWVNOVmcxdHdqWUF2QXFXSkRSZFNNWXIyLzc4a3JLdndTYnl0STh6bWpmTG02OXpnNmFEdTdhbndteTRzZEtkSmgzbHFtTVQxVzVycTVwZ1RBbFZQbWFxTlVKZkZVcnV3dnhHekNBQnQ2SHV6Qm5yMGordWczSjdXMktJNnprWjBEWXhMbk9NTkNEeDlON3kvejVSa3dUUjE3dkxkNWw2SmhKMlQ2N2JqVm9kdFdMVExnUmF6d1EzWEpUaGV6NndqWGdXOGJpc21pa3FWb0tNTysrODdxZDR1d3ZEM3JNTTZLcHV4aUVuNGp3dU9TT2RtSlYvUDAraTVvZk0vUnhpVnlzOFpMMEVzRnBzZFM5ajFFaDF2R3pTZ2Q5MDdwOXJTM2p3bFpZVEpITXlhUmw0d2xTM2ZDQzUvNFRUQkRWMUkyd0Jac2ljTHFNK0wrZ2t1eU9KYlFOOE04QjIySFhVa01hb3d4ejluZkdIR3pOOXNQeGdvNlhLc1RuazdiVVhqMC9SNngzdEcyb1hJTlRsclFtYXRYaGdzdlgzYUs0ME4ybThHRTZaZXFIS3ZsUkZPN1RkZ2xQWG0xUWVJYnF1MmpuZUFyV3RsSjMzK3pwV25RaFV0TGsiLCJtYWMiOiIyMjZiYzAyYjdlODY0OTNjOGU1YzczMzQ5NWU3MmQ2YjIzZjNhNGFjN2E3ZmJhN2JlNWRjNmUzOWU2YjA4NzQxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJQc3R4c1hNWWU5NVdZZStGVk1WYkE9PSIsInZhbHVlIjoiTzQwUkF2ejNyREhCSlRHT2h0N2dNMmhYVW9YV1Z3VC9ORFdlSUVJaDArb3Q4OU55V1NKaDY4ZnNwdXAvUUkyalEvdDNGbm9idWpSNWdXOFZsalZrK1pJTVJtSWJEQ0VPWko0RW8wc3dqK3NOUS9LbU1ueWJOWW4yZTdPdzFJMm5MRjh1S2xXY0JjekRjeDBMdzVWOXAyaDUreTJkN3pRU0tHSWg2YmlJUGFob1VHR1RjVm5ZYjhtN0Y0eFJCMHhSWGFrR2ttcFNEQkEzMmRXaGhIVmV3TVo3WE5obU0wMmRqSU16cFJaeXFoWjc1Y3JBclUvZDJMZlYyRnpzampXR1R3VWEzTDFIUS9yQXBGYzhXSWkxVTArcUx2Wld4b1FoYThPWFJ4ZnBjU3ZKK3BPQUpuK2ZYK2ovajZJK3dzTWEwbVQzSkxwRkJIUzNGNDNNLzZNSGxmVUVJNnV2WjBzN3RMYnd6RTY0Z2NiNzdsSURPVmh1bEJVTC9NUEJZektaOWJNRzhyK2RsdlhLc0hTNmpXb003TXVXaXZxUkJrRDlXaTNFUWtFR0lTdnk0RVQ5UURTb1JQdi9PQ1N0NG1XQll0eVFjMUNzSHZmT3ptdDQ3ZEJMTm8xNE5sMXVJZ0M1cUFQYml1NXZSYm0xdFloNVBOMVg4OFhmd2hobmpaY0wiLCJtYWMiOiJjODBlYzA2MDE4YTkyZmY1ZGY1NzFlZDY2YTk5ZGNkYWZmYjNjZjUyODMxNTU0YzA0YzU3Y2UyZDc1ZjM2NTM4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpURVA5Vm5BU2x5WU1qdUhLVXB6MlE9PSIsInZhbHVlIjoiWGhDZ3NnWis0MFBUQjlPM1hwZDdRZ2NNUmRSVnQxRWtJajErQ3h0QjBPWVNOVmcxdHdqWUF2QXFXSkRSZFNNWXIyLzc4a3JLdndTYnl0STh6bWpmTG02OXpnNmFEdTdhbndteTRzZEtkSmgzbHFtTVQxVzVycTVwZ1RBbFZQbWFxTlVKZkZVcnV3dnhHekNBQnQ2SHV6Qm5yMGordWczSjdXMktJNnprWjBEWXhMbk9NTkNEeDlON3kvejVSa3dUUjE3dkxkNWw2SmhKMlQ2N2JqVm9kdFdMVExnUmF6d1EzWEpUaGV6NndqWGdXOGJpc21pa3FWb0tNTysrODdxZDR1d3ZEM3JNTTZLcHV4aUVuNGp3dU9TT2RtSlYvUDAraTVvZk0vUnhpVnlzOFpMMEVzRnBzZFM5ajFFaDF2R3pTZ2Q5MDdwOXJTM2p3bFpZVEpITXlhUmw0d2xTM2ZDQzUvNFRUQkRWMUkyd0Jac2ljTHFNK0wrZ2t1eU9KYlFOOE04QjIySFhVa01hb3d4ejluZkdIR3pOOXNQeGdvNlhLc1RuazdiVVhqMC9SNngzdEcyb1hJTlRsclFtYXRYaGdzdlgzYUs0ME4ybThHRTZaZXFIS3ZsUkZPN1RkZ2xQWG0xUWVJYnF1MmpuZUFyV3RsSjMzK3pwV25RaFV0TGsiLCJtYWMiOiIyMjZiYzAyYjdlODY0OTNjOGU1YzczMzQ5NWU3MmQ2YjIzZjNhNGFjN2E3ZmJhN2JlNWRjNmUzOWU2YjA4NzQxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJQc3R4c1hNWWU5NVdZZStGVk1WYkE9PSIsInZhbHVlIjoiTzQwUkF2ejNyREhCSlRHT2h0N2dNMmhYVW9YV1Z3VC9ORFdlSUVJaDArb3Q4OU55V1NKaDY4ZnNwdXAvUUkyalEvdDNGbm9idWpSNWdXOFZsalZrK1pJTVJtSWJEQ0VPWko0RW8wc3dqK3NOUS9LbU1ueWJOWW4yZTdPdzFJMm5MRjh1S2xXY0JjekRjeDBMdzVWOXAyaDUreTJkN3pRU0tHSWg2YmlJUGFob1VHR1RjVm5ZYjhtN0Y0eFJCMHhSWGFrR2ttcFNEQkEzMmRXaGhIVmV3TVo3WE5obU0wMmRqSU16cFJaeXFoWjc1Y3JBclUvZDJMZlYyRnpzampXR1R3VWEzTDFIUS9yQXBGYzhXSWkxVTArcUx2Wld4b1FoYThPWFJ4ZnBjU3ZKK3BPQUpuK2ZYK2ovajZJK3dzTWEwbVQzSkxwRkJIUzNGNDNNLzZNSGxmVUVJNnV2WjBzN3RMYnd6RTY0Z2NiNzdsSURPVmh1bEJVTC9NUEJZektaOWJNRzhyK2RsdlhLc0hTNmpXb003TXVXaXZxUkJrRDlXaTNFUWtFR0lTdnk0RVQ5UURTb1JQdi9PQ1N0NG1XQll0eVFjMUNzSHZmT3ptdDQ3ZEJMTm8xNE5sMXVJZ0M1cUFQYml1NXZSYm0xdFloNVBOMVg4OFhmd2hobmpaY0wiLCJtYWMiOiJjODBlYzA2MDE4YTkyZmY1ZGY1NzFlZDY2YTk5ZGNkYWZmYjNjZjUyODMxNTU0YzA0YzU3Y2UyZDc1ZjM2NTM4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1736384403\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1730500276 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1461/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730500276\", {\"maxDepth\":0})</script>\n"}}