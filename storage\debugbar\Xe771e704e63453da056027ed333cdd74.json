{"__meta": {"id": "Xe771e704e63453da056027ed333cdd74", "datetime": "2025-06-28 16:20:47", "utime": **********.520258, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.076044, "end": **********.520285, "duration": 0.4442408084869385, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.076044, "relative_start": 0, "end": **********.438393, "relative_end": **********.438393, "duration": 0.3623490333557129, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.438403, "relative_start": 0.36235880851745605, "end": **********.520287, "relative_end": 2.1457672119140625e-06, "duration": 0.08188414573669434, "duration_str": "81.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45714440, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0269, "accumulated_duration_str": "26.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.470171, "duration": 0.02596, "duration_str": "25.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.506}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.504425, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.506, "width_percent": 1.673}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.511596, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.178, "width_percent": 1.822}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-630114729 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-630114729\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2141999904 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2141999904\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-598488153 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-598488153\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127581490%7C40%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImQ1V00wdlcrRXZ2ZWVhVU5UbWU4MlE9PSIsInZhbHVlIjoiZFBwRk9yYmQzYnlsWkdyRWRGTnFPS0hGaVUvTnYyaU5IT2NXaGc5K0pCYmFnV2JaUG81SmcwVnpNcHkrVzJtZjdrTUpwQmszTzFmYkFrS1NTekMyaVdoM0NuNk1uT3ZhYmJyd25rOWpOWXRlR3YvVm9XczFYYnZJMlUzVVg4cWVNMnQ5M0tPMmNFUGJlK2RoTTA2d1AvZzN4YlNFVDM5VkNocDVOYkRLcll0RUEvV3JPTmZQU01RTFcxY3ZLeDFWb3RMek5zNE9KZjNqblF6VVk4RzV5MUJ2azk5NXBTZ0VnMi83dHBFUEU3SkhrLzdkN2hBZmRzTHZFRkFCRndjM0FMejlGOFUzNXVjcEJVRS8zQXpFUmJJN3hDQnlJeTlMZmtRbVJXbVdYZ1A5WWNHaWJmL0hhUVV0cUI5Q2xMYmFyc2pXQkoxelRPck1Gcmxjd2tydWxXWktNMWZuUVBHSjBKaWlhTmJYWGlIYlhDVGZmYk50TVE2aG5ObUhaRGJoU0pWU1h5S0MrazJKWXc2b3RaMnBKc1IzUTA4SkFZK2NGN2FiWGxReUlrTnJ3ZEJFYWFvT2MzUStSaW9Zay9iUkRMb3BVVzZRN0dER3hnY2NtOFlHK1lOdjRxVm9wMnZ1ZWhYN3RKZkpMZGttcDlta0swcjNQRXBYSmo5Y3d6djYiLCJtYWMiOiJjOWU1ZmZmNTA0MzU2ZjRiYzNiY2M0OGQ1MDRiYWY1ZGFjYWE5NTBjOGNlMTlhZjEyNGFlZTQ2ZGJlOWYwZjg4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdxZnRLZGJnVjl6YjEyWFZ3Q091WlE9PSIsInZhbHVlIjoia3g4V1Y0RTUra3YyUG81anRENzE5QmlNWU5CRXpJVzRnSk91aVNuTkRJRElsMG5FZEZ2S05PSkFzTjY3WHcyZlVWN1k3TnZNSmRHKzhyS2xWajJjWEhGbVpZdGNldSt5WEUvM0tWVWY1ZnFEZmdnY0NpTXFhNTVkVlpNZU9idUgzbGdyaExNT3h5ckRjTkl3b3BQekF5MW9raERXQnJ3K3gwR1NqYkxyM2hpMUh5Tm0rZUJLK29rc1QwQUJyQmt5eC9yY09kR0Q0RlpZaXkwWDNlMm9UN05EbTBCWE1kU1dFaWNGcDg5TEtJMjIyWE9xbWJtQ1JyM2o1cFFqblhCaFF2TVNtN1pTbUhGWm1UQm1XTitPQllnYXowRnptQnVWcDZ3WjVucnR4cUlPNUtMbmh5blpVaEk1Vks5THJ1S0VCakFaVWdpNUdLbXY1MVpKYkw2TlNtQkxGcklKTWVHays3QzNRQ0Y3ZUZITHltdENBMFJNSW1HRExxMzFTMVliOW1VNWZyNVJxK3FsMzdSZms3dzNsUWwwbWdVTUZ3VEtITTIraEI4YkZVSXVFTUFGWjZadDNvWjB5VC9yc3hScitRZWFtREs0VmJzRWY1MlRxc1MwS2pVOVVJdEZOT242Y096QkNpTjUraUpGeGJBQkpHYnlpUjIrUVRtc3BjelYiLCJtYWMiOiI2MDk2ZTFkMTRmZWVlMzk3NmI2MjUxMWEzMTk2Yzk3NzdiZjcyYWYxNjIxZDExOWRkZDY3NzM3OTcwMjRlYjQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1616969389 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1616969389\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-370443464 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:20:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhyNktYT21YcmF5UElCb09UY1J0K3c9PSIsInZhbHVlIjoic3E2RitieDI4dzVnaVhTREo0NWg2WXNoOXNjVUsxYVFGamRYUHRYR09VUXR0M2Jjc1M4ODFWVUM2ZWNHd0s3dy9YVjVrWnl0YytwMEtSV0ZncWplSkpjc3hyck5mODNESnFJMjF4Zzk2MUwxTGx4bnJ4RThFMFl3VU0rT1ZQUDBFcXhaT2pYNUowL3R2ZE5oOWJsWFhsMTVuNis1VEVoa2NDTWRVY1dyNSttS0RrNGhFOWpOV0VnM0I3dFZIUmlGdmhkSWo3aURsV05NcDljMTFaaEp5VERPY0JWVEM5RGlrWDdSNFNtalJIem16MjhRell5YWd3QlZpY2trOGZTQ3R5c2hKVE1meW56SWo0TEJjUWtRRFJRRUNaOVFCR0k5V0NPZWJmdmVKTlVWcFFVM0Q0RFhhem9hNU9HckZKMERZRU9PS3dvZHNUaFUzQXlsWkpiUitKVEViZ2RpMTBUd2tlQVFjVDBpWWl1SEJXVjZybjdqUFJRei9uODdXNzkxVWgxc29xSHpJU3dDOVZ5cTRSc3h4YVVZK0JXblJvR0c0U3BtRitiU1JNMnpWR21mWGJPQXBDenpOcmhMVDFiQWlvaVE2RkMyRjFBQm1qWDRQWFMrWFpLaFFMTzNmY3BDd2haYTF0eE9iRm1PRjJuRXkyNFRiUWZMOVFkSW4zVEYiLCJtYWMiOiI5YjdjMGUyZDBjNDI3ZDNkZGZiYmJkY2U3MTMyOGE5NjQ0ZWViYTBjOTNhNjkxYjEyMmI3M2U1NjQwMjdkZGZlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:20:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlFxUHJvS1JIWGhBeXVjRnplRVZxVXc9PSIsInZhbHVlIjoidVQzM0Y3Um04V1lOQmE2UTFGQU5sbXZEcHZIa0dFeEIwRXQ2NHBwNTR4czArcEVrKzJUTG1yaUx2bjVjd1IwNjVwSDk3UkZoYVluUXJpcitlaDJZRUdhN0p4elBmcHpMd29wa09PSHVsdkllSWcvTGIvTXVSL0Noc0lmSDRiZDZwNE5mOXZVS09ubGJtek5IaUk4c3ozQW9nWHpOTllLdFZ4ckY2R2g0YkowdWhQdGNqeFdxaGR4YnZPc0JzOVNaSXlBS1VlY3hKYkZhOHV1WDZ1Qk53N3I2N01vRUYwU2tIZUtObjh0ZGpHVEFPbkw2V2c4YjczNlUvNE1XTkVmS3dkclZ2ZUl2dWdUTEI4TjJpT0xsQ1NnZVlWUkxhR1h5WjhiSmI1VGJlZ3VPSTIvYkpZKzZicU1YUzhnb3hoK1RvRytiU0QyK2lPMWN0ZFRST1Q3R1FYOGliK0RjSjcrckNLcDRWL1BvTE1GU3Awc2hMejZpeWkwQlZQbG9EMUpUQlFidnBCWks2TU92YXlaZGFqVE5KS2xPcnl2emF0OUorSDRYMzB1UHNsRGZEdUhoYlNrNm5CbUhFOXRQMUpUSjVuUW9VcG1UUzZoQitxWk1leHZFV2g5ZndtbmQ0ODZQaFNOZ0pTczRSN0xaMVF0aXBObG1IMmk2ZzRhU0piUGgiLCJtYWMiOiI0MTEzODJkNGZiMmYzNmMwM2I5ZTljMTgwNjMyYTgxZjIwZTk2NGUzYmFiZWU1MTAwNzE1OWUxMjdkMDcxNTg4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:20:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhyNktYT21YcmF5UElCb09UY1J0K3c9PSIsInZhbHVlIjoic3E2RitieDI4dzVnaVhTREo0NWg2WXNoOXNjVUsxYVFGamRYUHRYR09VUXR0M2Jjc1M4ODFWVUM2ZWNHd0s3dy9YVjVrWnl0YytwMEtSV0ZncWplSkpjc3hyck5mODNESnFJMjF4Zzk2MUwxTGx4bnJ4RThFMFl3VU0rT1ZQUDBFcXhaT2pYNUowL3R2ZE5oOWJsWFhsMTVuNis1VEVoa2NDTWRVY1dyNSttS0RrNGhFOWpOV0VnM0I3dFZIUmlGdmhkSWo3aURsV05NcDljMTFaaEp5VERPY0JWVEM5RGlrWDdSNFNtalJIem16MjhRell5YWd3QlZpY2trOGZTQ3R5c2hKVE1meW56SWo0TEJjUWtRRFJRRUNaOVFCR0k5V0NPZWJmdmVKTlVWcFFVM0Q0RFhhem9hNU9HckZKMERZRU9PS3dvZHNUaFUzQXlsWkpiUitKVEViZ2RpMTBUd2tlQVFjVDBpWWl1SEJXVjZybjdqUFJRei9uODdXNzkxVWgxc29xSHpJU3dDOVZ5cTRSc3h4YVVZK0JXblJvR0c0U3BtRitiU1JNMnpWR21mWGJPQXBDenpOcmhMVDFiQWlvaVE2RkMyRjFBQm1qWDRQWFMrWFpLaFFMTzNmY3BDd2haYTF0eE9iRm1PRjJuRXkyNFRiUWZMOVFkSW4zVEYiLCJtYWMiOiI5YjdjMGUyZDBjNDI3ZDNkZGZiYmJkY2U3MTMyOGE5NjQ0ZWViYTBjOTNhNjkxYjEyMmI3M2U1NjQwMjdkZGZlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:20:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlFxUHJvS1JIWGhBeXVjRnplRVZxVXc9PSIsInZhbHVlIjoidVQzM0Y3Um04V1lOQmE2UTFGQU5sbXZEcHZIa0dFeEIwRXQ2NHBwNTR4czArcEVrKzJUTG1yaUx2bjVjd1IwNjVwSDk3UkZoYVluUXJpcitlaDJZRUdhN0p4elBmcHpMd29wa09PSHVsdkllSWcvTGIvTXVSL0Noc0lmSDRiZDZwNE5mOXZVS09ubGJtek5IaUk4c3ozQW9nWHpOTllLdFZ4ckY2R2g0YkowdWhQdGNqeFdxaGR4YnZPc0JzOVNaSXlBS1VlY3hKYkZhOHV1WDZ1Qk53N3I2N01vRUYwU2tIZUtObjh0ZGpHVEFPbkw2V2c4YjczNlUvNE1XTkVmS3dkclZ2ZUl2dWdUTEI4TjJpT0xsQ1NnZVlWUkxhR1h5WjhiSmI1VGJlZ3VPSTIvYkpZKzZicU1YUzhnb3hoK1RvRytiU0QyK2lPMWN0ZFRST1Q3R1FYOGliK0RjSjcrckNLcDRWL1BvTE1GU3Awc2hMejZpeWkwQlZQbG9EMUpUQlFidnBCWks2TU92YXlaZGFqVE5KS2xPcnl2emF0OUorSDRYMzB1UHNsRGZEdUhoYlNrNm5CbUhFOXRQMUpUSjVuUW9VcG1UUzZoQitxWk1leHZFV2g5ZndtbmQ0ODZQaFNOZ0pTczRSN0xaMVF0aXBObG1IMmk2ZzRhU0piUGgiLCJtYWMiOiI0MTEzODJkNGZiMmYzNmMwM2I5ZTljMTgwNjMyYTgxZjIwZTk2NGUzYmFiZWU1MTAwNzE1OWUxMjdkMDcxNTg4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:20:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-370443464\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-938833539 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-938833539\", {\"maxDepth\":0})</script>\n"}}