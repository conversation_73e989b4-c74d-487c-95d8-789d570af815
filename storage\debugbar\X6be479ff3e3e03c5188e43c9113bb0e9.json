{"__meta": {"id": "X6be479ff3e3e03c5188e43c9113bb0e9", "datetime": "2025-06-28 15:00:25", "utime": **********.870385, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.451177, "end": **********.870397, "duration": 0.4192202091217041, "duration_str": "419ms", "measures": [{"label": "Booting", "start": **********.451177, "relative_start": 0, "end": **********.793639, "relative_end": **********.793639, "duration": 0.34246206283569336, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.793648, "relative_start": 0.3424711227416992, "end": **********.870398, "relative_end": 9.5367431640625e-07, "duration": 0.07675004005432129, "duration_str": "76.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45984976, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.839486, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.845208, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.862424, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.86491, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.00752, "accumulated_duration_str": "7.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.822083, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 31.782}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.826077, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 31.782, "width_percent": 35.638}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.830653, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 67.42, "width_percent": 2.394}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.840322, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 69.814, "width_percent": 7.447}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.845909, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 77.261, "width_percent": 4.521}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4090}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.854743, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4090", "source": "app/Models/Utility.php:4090", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4090", "ajax": false, "filename": "Utility.php", "line": "4090"}, "connection": "kdmkjkqknb", "start_percent": 81.782, "width_percent": 6.117}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4091}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.857861, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4091", "source": "app/Models/Utility.php:4091", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4091", "ajax": false, "filename": "Utility.php", "line": "4091"}, "connection": "kdmkjkqknb", "start_percent": 87.899, "width_percent": 4.787}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8595202, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 92.686, "width_percent": 3.59}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq24\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.8633082, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 96.277, "width_percent": 3.723}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8bGCesGqaoMPQwzhQFUSVF6PpqMDYWhqjXrguRvH", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://localhost/enhanced-pos\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1417882023 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1417882023\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1700101534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1700101534\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1037683994 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1037683994\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1306980489 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751122820411%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRqOWIzbTVQREZvMjdyU1VscGFYbFE9PSIsInZhbHVlIjoia2M3Nmx4aWhaMjBySHpOaGJJMEMrK0NWWGpVRU1Ca2JrU29rakt1b3lBczhxRld2NlZJRnJGZnFGV20xbXJ3ejZpNXcrUEtUbFJNbFQwQ2FuQWhKbnMwR2RDTmF5OXdGL1ZMVldsOS9SeCtpZ29EeE9LaDFuSDg2ZzVpbE5SK040NVd1dmIvUFoxZ2lYWHlWeGUydXpwdW0zMUNEUXpVcHd3bTlaRTg4WE9aZHA2SEszdzFRN3cxOStKTCszcFNrV0ZtZ2lsLysvanFxczRIRU5PV2xJZmVIY3VYTnVtOHZ4Q1J5cjQvbnloNmxCZFRLK1kxTWNGd3g3VW9HRjU2SUNXZCs3QkZpZ2hqYVJYK2QvZU91cEptSElobWlJeWdwQmVmRXhkcnh3cDF4UFBUcFdvSnhRYzl0bWtFZk4xNHFEdGdqOHV0NjlyU1hvemFhdXdCZEFOek9vWUZ6RCtQWFIzZmFYdXYySjNualZGbGFjZUpUSUFYdjZ5YkFCaXVhOTZoNFFyYk5jbmNyV2VWcStrRXBBZVBqRGVVREE0VG51L0RNeElVaGF1ejIvTUJUZmFqR2tBNitkdnpseVBYSHJjSzdRc2JQSlJMUk5iYWZERDlydVNmTGZldW4zTThDVks5c2RIb3RtcDYzNmNmdjR6eGNiZ3J4TGE2eEY0OUoiLCJtYWMiOiJiMmNlODYxZWI3MTI3MDk1ZDgwODE5NWY3ZWY4ZTEwNGQyMTU2NjU5MTc3YjVhMzMzYzhlNmY3ZDE0Yzg4MDhkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhOMEpQMzB5T2dHdVJHVzFtWDRnNHc9PSIsInZhbHVlIjoiRDNWbkVFNE9HR2c3dWkyc0x6clhjdmZHcE1sR09NTzJtbmxnKzY2Yi9mcDNUVnp3S21lWlBtSmVYdDZmVHNSd1FHZ1A4ZTk1SmlsUnVQSVJyQ2Q4dUl3NXYrNXpmc0xvdVFSQmdtWXl4cHpBbUErNnMxS05kZk0xV2xCTnpMOFJFWk1URWhyb2ZzSnNXTU1NVGdUcUZpcm43UERqeDhJZ0tMV1JvdDU1b0RxR2w5bkZTdVQrR1N1ZjY0a2pQM0lRRVdFTDNyUTVrY1ErRisvS0pUY3VLbVh3NW1CQTZQNEY1SzBUQ01uR3pyekh5YmxieEpnSGNsclcxZ3l3VXRrVHJKQzZNMjgxQ2FVb093ZnYyRVRndko4QnhDWGZ4c0FkR28rVVA0RURxWGE4ODRCL0x6OHYzRWU1SzJiUTFlWVd2d1gzbWxraWh4K0E0L1luRkhJTTJTbFRWVGdWaFV6Uk1sZnE3QkkzbDdYVzlmMTU0UlV1NTRPZnNKWC9UN05lUEc2SlE1ckVGdDFydGdWOWJGMGNGeXBKRW5WZkRnQjJoUEl3R0NIMlBhR09hazBRNkoyRXBsMTR1Q3RwSkw4andJZGxKOTdLcVhIWGQxbE1KNkl5Z093SlpVTkN0SVFobzdXaHB4eFFRa1dTK1o2V1BJb2JaOFQyQUs5MlM0RzEiLCJtYWMiOiI1YjM0ODExYTc4NjdmZGFiYjA4ZjY2ZmI2MTMwNmUxYjY4OWZkZDAwNjZjMmE4MTJiYWM3ZTg3M2JiYWVhNTYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306980489\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1837864104 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8bGCesGqaoMPQwzhQFUSVF6PpqMDYWhqjXrguRvH</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Yv3t2F3Cuf8tBFeQi1W4f7pI0PV2AdnhhHyXe6dW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1837864104\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1270094381 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:00:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9zQzUzTDJxN2xEV004eVZWSEJyNHc9PSIsInZhbHVlIjoiZnV5a1NBT0FKNTVXK2tRV1p5bWpiWTJkSDFHdWp2MlFmdm5xSUllUU9ZWExNVHpNR0dNY0tDYUhaNjBmbHBzSE5xL0F5RG51MEQ1TnllT3dnSld5TnV0ci9ENUd0NU9IaEFEd0VOdGM4d0JTbzRHcGlLWWVVVEI2VXJHaVpkQysyZVRHSjg2bGtuTmhScXBwTit3M0ZXdGdqeXQ0V0tJU0htK3NwaXhXakZsUEppZExJVFN6ZEpmR3kyQ3N6RHZNMmRad2hsOFJzTjlLdGQ1d1JPMWh4OHd5cHl4Mk5ZUGlWQmdiUHZMR2NSM3ZHaThvUS9SOVhEZWh2cll6a0RLc3pBcVJnMlRMbzVqOVhFRjF4SnZaa0Q0MmVEajlEQzZCT2VWNkg4WmNiWlMyOXRnYzRKWlhNdmphRldtT0lvWjJRK1Jycmg3RmpIV05iUDdQeEdiOC9iTkhmcXlTdnpFeGdLUU1UazVUVnUwdDVHd0pnZ0RrRTBmSXpsbUlXdGc3R2FHSk1HNkNVSkthVFZHZ1JldGhSRTNZWDNQeHRJWW1NR0ZaV2N6MzZDSm5YRmJqQ0s3dy9VZ04zb1ErdUU5Wlk2WjlIUjArR1NtbUk1ZjFMR013YW43ODVGL25odUh2N21keFQ0NnZiYTdMTzAwclE4VGpoVXdKb2JSVmJyNnYiLCJtYWMiOiI4YjZkNTYyNDg5NWFjMjQ2YjgzMGFlODkwZjZkODIwMjJiZGI4ODgwOTg2MjAxODUxNzExOWZjNTMwM2RlMTdiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkMyMWZyYkpiaThLeE5EeHZabGRITmc9PSIsInZhbHVlIjoiTCtxOEgzS2swZnR1ejdneDhBaU1CRDVJNU0rUkNPZXRZNUVNdHFhSUhtT2RFVTJuZ0txRTMzQ2V4NlA1NU5XUm50TVk0UXhSSDlsekNFcnJjWjF4RkFJMk0wRFNuQjI0ZDhNV0FaRmx2UGM1RWZKK3FKWGtzaWlqQzV1cVBRdVlQRFBWZEl6NkVqRWF2M1U3WU5XbFhKbWhBeWxCWW1DOGFHV3pHYWxmYWNoSUFoU21KYVNubXdsaW1nOVNlK1FxSTV0KzBVdm9wTVpTOFFVQUJ1NjFreFU0dzN2UnJxT1VicmY5TWZUcmNlY3dkQkJTN2Niam81K3JXTFAySWE4azcwTnR2Nmswd1I1b2hyV2dqSi9pbkVYOFFPeWpJd2NZOHlXWkQra0VkRExPbjhBcjQzVEozbFZkRG05U2NSQkZhdmdla21yejlYTW1CN3JCSTZUK2VVazBXeWJOVldKNk5kU21BSXNJMzcwRTBMN1ZKN2J2Qzh5Z09oZU1lajcraCtWUEJ4QkxWSUNjZDFOZGd0RDdISGV3Z01DZ095OGFLd2VWbXc2UE85cXkyQ1BpMXRYUmZ0MjJXZHpRUXRVY2NuR0Q3VmtaL0JOQXZWNGRFZVlaTFozWDE5K2xVdDRJM2ZraDRQUzRIcTdLVEVMdDNnc0ZyMGtwS25Hb2xVNXkiLCJtYWMiOiJiZjczNTc3Mzc2OTNlMDRjNDJiODQ1YjBjMWEyYzlkZjA3NzdiMTJiODM1NDkyNjNjMmMwMWRiMjQ4MjQ3MmUwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9zQzUzTDJxN2xEV004eVZWSEJyNHc9PSIsInZhbHVlIjoiZnV5a1NBT0FKNTVXK2tRV1p5bWpiWTJkSDFHdWp2MlFmdm5xSUllUU9ZWExNVHpNR0dNY0tDYUhaNjBmbHBzSE5xL0F5RG51MEQ1TnllT3dnSld5TnV0ci9ENUd0NU9IaEFEd0VOdGM4d0JTbzRHcGlLWWVVVEI2VXJHaVpkQysyZVRHSjg2bGtuTmhScXBwTit3M0ZXdGdqeXQ0V0tJU0htK3NwaXhXakZsUEppZExJVFN6ZEpmR3kyQ3N6RHZNMmRad2hsOFJzTjlLdGQ1d1JPMWh4OHd5cHl4Mk5ZUGlWQmdiUHZMR2NSM3ZHaThvUS9SOVhEZWh2cll6a0RLc3pBcVJnMlRMbzVqOVhFRjF4SnZaa0Q0MmVEajlEQzZCT2VWNkg4WmNiWlMyOXRnYzRKWlhNdmphRldtT0lvWjJRK1Jycmg3RmpIV05iUDdQeEdiOC9iTkhmcXlTdnpFeGdLUU1UazVUVnUwdDVHd0pnZ0RrRTBmSXpsbUlXdGc3R2FHSk1HNkNVSkthVFZHZ1JldGhSRTNZWDNQeHRJWW1NR0ZaV2N6MzZDSm5YRmJqQ0s3dy9VZ04zb1ErdUU5Wlk2WjlIUjArR1NtbUk1ZjFMR013YW43ODVGL25odUh2N21keFQ0NnZiYTdMTzAwclE4VGpoVXdKb2JSVmJyNnYiLCJtYWMiOiI4YjZkNTYyNDg5NWFjMjQ2YjgzMGFlODkwZjZkODIwMjJiZGI4ODgwOTg2MjAxODUxNzExOWZjNTMwM2RlMTdiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkMyMWZyYkpiaThLeE5EeHZabGRITmc9PSIsInZhbHVlIjoiTCtxOEgzS2swZnR1ejdneDhBaU1CRDVJNU0rUkNPZXRZNUVNdHFhSUhtT2RFVTJuZ0txRTMzQ2V4NlA1NU5XUm50TVk0UXhSSDlsekNFcnJjWjF4RkFJMk0wRFNuQjI0ZDhNV0FaRmx2UGM1RWZKK3FKWGtzaWlqQzV1cVBRdVlQRFBWZEl6NkVqRWF2M1U3WU5XbFhKbWhBeWxCWW1DOGFHV3pHYWxmYWNoSUFoU21KYVNubXdsaW1nOVNlK1FxSTV0KzBVdm9wTVpTOFFVQUJ1NjFreFU0dzN2UnJxT1VicmY5TWZUcmNlY3dkQkJTN2Niam81K3JXTFAySWE4azcwTnR2Nmswd1I1b2hyV2dqSi9pbkVYOFFPeWpJd2NZOHlXWkQra0VkRExPbjhBcjQzVEozbFZkRG05U2NSQkZhdmdla21yejlYTW1CN3JCSTZUK2VVazBXeWJOVldKNk5kU21BSXNJMzcwRTBMN1ZKN2J2Qzh5Z09oZU1lajcraCtWUEJ4QkxWSUNjZDFOZGd0RDdISGV3Z01DZ095OGFLd2VWbXc2UE85cXkyQ1BpMXRYUmZ0MjJXZHpRUXRVY2NuR0Q3VmtaL0JOQXZWNGRFZVlaTFozWDE5K2xVdDRJM2ZraDRQUzRIcTdLVEVMdDNnc0ZyMGtwS25Hb2xVNXkiLCJtYWMiOiJiZjczNTc3Mzc2OTNlMDRjNDJiODQ1YjBjMWEyYzlkZjA3NzdiMTJiODM1NDkyNjNjMmMwMWRiMjQ4MjQ3MmUwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1270094381\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2069265992 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8bGCesGqaoMPQwzhQFUSVF6PpqMDYWhqjXrguRvH</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069265992\", {\"maxDepth\":0})</script>\n"}}