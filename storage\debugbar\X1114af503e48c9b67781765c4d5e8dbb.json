{"__meta": {"id": "X1114af503e48c9b67781765c4d5e8dbb", "datetime": "2025-06-28 11:23:59", "utime": **********.60826, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.183405, "end": **********.608273, "duration": 0.424868106842041, "duration_str": "425ms", "measures": [{"label": "Booting", "start": **********.183405, "relative_start": 0, "end": **********.556266, "relative_end": **********.556266, "duration": 0.3728611469268799, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.556285, "relative_start": 0.3728799819946289, "end": **********.608274, "relative_end": 9.5367431640625e-07, "duration": 0.051989078521728516, "duration_str": "51.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45704944, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1910\" onclick=\"\">app/Http/Controllers/PosController.php:1910-1968</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00507, "accumulated_duration_str": "5.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5873609, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 31.953}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5969949, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 31.953, "width_percent": 7.298}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*********%' or `sku` LIKE '%*********%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*********%", "%*********%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5994961, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 39.25, "width_percent": 60.75}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1072665615 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1072665615\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-63311665 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-63311665\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1875939990 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875939990\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1902115972 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6ImUrWmRHT2hNNm5lbkV2emFDanlPVUE9PSIsInZhbHVlIjoiZWpUWGJtL1I4cXRWalYyVTBiUjFjM211dXg0MDJmZ0QrMWVaMXlQTzdMSnIwRE5USGYrWjhJVGxzK21yZ0tyV1gzcGRwVVFUS25tUHZDTTVCV1VWeU1oSUJQOWR5MHAyeXg0eXczeXNYVlJRNHVZM0NNSVNZZjd2ZnF4WkJjNjRkazI4SVNQQXU3V2NRc0pIUHhzMGhNQmx3SHI0Zk1ISUFjZWE1ZlpFV1NFaUp2QXJVTVlVcTIzZ2JrM2JqVFp3NlIyMCtqWHo0UGovandJQURDdm9VK0E2V3I2cC8zOUJVU01wMTFSYmhHdTZlZWVraUhzVUxSTnZySCtQNGJuMysvYzJ3N0JnSVRMUFdjK2hTQThPdExvbE14VXpzbjl1MC85TkZSeEF2eXNNMWpITDhOOUNrbzEvcHlqK3c1NXNjOUdOU3pXdE90L2RQQUx1RDR0MUFkaUMzc3VuQnd1RFltZmFEWE5vNW84VHVtMGVZTkcyTWtzS3dNUjNaYUxUZlAyS3d0VXJnWnc2TTBNUFF0SUJMdm1BTmdtVFFJVE8rcE5UdEVDaW1XOXFoNUhxK1VhZk1LVlc4TGN1MUt3T2pkcngvOEdJTHRTd2srS2VrdHlrcWd0NlljRXh2THl6SXNIZ1ZuOHZFRndBdHRGMTdLSTJGK1dmMmQxdEM0VkUiLCJtYWMiOiI4ZTQ2MTIwYTIzOTkwMjgzZTBmNDNiODY2NzVlMzI2MDZmNjM2MTA5ZWQxYjQyNjMzZGM4MmFiOTRlODA2YzQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlIxR0o3UHFGeEtPelBVODRtNFRJZlE9PSIsInZhbHVlIjoiWE9wSmQ5MGxvaDkxbkpUb2taemNnK2JqUUJOaHcxcC9vMFZCOERSaU5uc0xwbkllUlI3UytrY3RXYnhtUXE1VjFnWi9hbXpObVVWNnJuY3dCYStZQzZSOUR6ckRSbFZXamttdE0wYnVlaEpTVmZGd21HdU5NRE5FeXV4cDZjZjR0dUtBY0U1dFhkZUZVaG5jOEt2OVhJNzJTS0pmVHY4YXJVNWd5ZXdBTjdhOFg0YmVqSW5oejlGbDlKRFVUMUVrZm1qcVkwRWJ3azdpZXdnUnIwMGxNVTlTY0drcThyLzc4cnlXYVlCTlhVaEVRTGN2dXNUZC9qSEN5amt3M3NpcXdNc3pBd2lKWEVmb3dnZldTakt2OVc3RlpNamNRNzNTVGQxZzM2bU5XYWVCeVQvTWZESnZVV3VwQXc0Z1NYenNPeDZUSFZic3RjeHhDOGcvWHIvWUEyc3JEU0NjamdOcUc1UHhFZHFxTVBWWUg2UHF1b3JZK3l2NkN5RU5JaGJ1WFNpSEtEdlEyNmVabkhhMVR3REEyamk3ekFJYlNpQkt6SzF2Q2hzRkpHR1ZPNGlxcDFVNlpoRU4rYlhXbW5vay9tZU9kUFlBL011ZVFZZ3B6RmgxQ0UrOEhTWjhqbDVXbGd1MUlyWE1BQml1WUEwN0h5cmhDMGxIUGlaVnc3WFEiLCJtYWMiOiI4YTA0OWZjN2M3MWJiZjc5ZTBiZmE4Zjg3ZDM1NjY4Y2JkM2EzOTY1MTAzYzc0ZDBiZWQyODAyNWUzMmVhMTgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902115972\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1590231420 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1590231420\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1739656566 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJMd0JXRDRHRkpvMzNMTlNzdFFXRmc9PSIsInZhbHVlIjoiTnE1OEVHMVRhcCtCZUxmbHRRZndMQ0h5UWJTUndpSjVuTlFobnhUSm1yNys3OHRQV2Y2Rkd1NVM0RG1TSWhCWlpnRXBoT0pXTU1OU1N4RytuSEJyRHFUejVCZXNYT2tJeDBpTi9sV3ZjaXpkR1kzL2pqV3BBb2t3a2JPdWhvcGJPOEhsYkx3RlhtZDFTakZ3MExVYnVzRlljUU1yaFNZS3N2WjNkOTRkbFRuc2RSVElScjRRaWR2SnBaR2pWbUQxbXhJWDhLZ0c0RUdvcFRwUVhxZzVDN2tDZTdydzc4RS83WUsxVmFUQUpNNHNONERYcmFxTHA0R0hwNTFDQVlOWmxkRmNrMGN6RDMxMkVSeU5aQTdrdmVXZTBxcFhEQTRNRmNBdCtSMVlYaVRhbXM5bmhiVXhJeFlDdXBQUnB4cjkvWlFjdWthQ085MTNlTWtPd096R0htb1RWZFRyb1JzbDU4MDEzaTRiNzNHN29uU0dLOUFwZ29zMGxyRFY2V0xZeUQ3SnF6STR0REc0QVFYTGl0Z290RmtnZncrZEV1OXJkakJnbFRpaGFXNEZWNnVZa3RtTGNBV2VNK29pd05BZDBUMnJQbVhmNXYwUU53M2NoRExjRE5ZNWMwcmcrbGFTM1NQRytlclhuM3E3MHFlK3BVcmdlNUVRa1VxQlRwNW4iLCJtYWMiOiIxZmZhY2Q0YzYyYzE1MzMwODlmYWEyMjFkMzQyOTY4YTVlNWQ2ZDE0ZGI5ZDI4MGM4MDBkNTc0YjcyYTAyNDAwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlNjU2p3b29jczk0Q2tsRjNtSGVUY0E9PSIsInZhbHVlIjoiMUJvRnRzMEZNNkxWK3BSZ1d0d2JRRnpQWHhZd2o2WlF4YndKT2xrYXZCd05nT2hmeVcwQkp2aXRROGJQVDRmdFkwRmFVTzVyVDVuOWwwQktYelhOdE1BUEFuUTRZSVNYY3hvMWFJSFNqZU04dVRjSWw2UlFWZ2tFVUNvNE9zTlQwWDhOTHkxWHlHR3pkYmtYeDBNMGhHSnAzVjJER1h1cGRzZnVuY1NhM1hwVTdIendBWGlSM0luTmtHSkRLNVhWajY4VUhRNW5UNlBYYkZ2V1BMYzBWL1Q1TURBMHJ3U2QzWTNlRFovMzROaUY5aUExa3NiOWYzS1ZWamxtSFZrc0c4c2FSSVgyTm5WOFRJVzJ1NXB3OXVvUHF4aW5KSUZzZ0lqWVBLSkIweFhZT2tsRFRha3pVR0hjb1o1Uk1vTGpFRHAzSll5SHpURGJ4L0ZEa0l4VDhnZkJZT3M1dlhPSHBTZlppL3lBUXBhblh3MWh1YnE4MFV5d3N1NVlHZmZ3MzN0aFJKTm9ZRHpSbE1ERE05bHZZVUMrOW1WcmU1dUp3cFdtUGwxVFhUUCsraU1ETVdKWXFpRVBqMS9OOHYxYXRzMXZ2cnNLTVNKUXRlYXdLWSs3R29ZVkE5Qk1ZREtQNVRtcjRjbktHWmx6OUE0Y0pnSC9xMUNZK1o5N1VhNG8iLCJtYWMiOiJmODFjZTliNjhhNzZkYzQ3Yzg4ODdlM2M5ZDZmYTk3MWIwODAzZWQ1NWY4OWNmN2IwMWE4NjIzOGQ3ODQ0MGNmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJMd0JXRDRHRkpvMzNMTlNzdFFXRmc9PSIsInZhbHVlIjoiTnE1OEVHMVRhcCtCZUxmbHRRZndMQ0h5UWJTUndpSjVuTlFobnhUSm1yNys3OHRQV2Y2Rkd1NVM0RG1TSWhCWlpnRXBoT0pXTU1OU1N4RytuSEJyRHFUejVCZXNYT2tJeDBpTi9sV3ZjaXpkR1kzL2pqV3BBb2t3a2JPdWhvcGJPOEhsYkx3RlhtZDFTakZ3MExVYnVzRlljUU1yaFNZS3N2WjNkOTRkbFRuc2RSVElScjRRaWR2SnBaR2pWbUQxbXhJWDhLZ0c0RUdvcFRwUVhxZzVDN2tDZTdydzc4RS83WUsxVmFUQUpNNHNONERYcmFxTHA0R0hwNTFDQVlOWmxkRmNrMGN6RDMxMkVSeU5aQTdrdmVXZTBxcFhEQTRNRmNBdCtSMVlYaVRhbXM5bmhiVXhJeFlDdXBQUnB4cjkvWlFjdWthQ085MTNlTWtPd096R0htb1RWZFRyb1JzbDU4MDEzaTRiNzNHN29uU0dLOUFwZ29zMGxyRFY2V0xZeUQ3SnF6STR0REc0QVFYTGl0Z290RmtnZncrZEV1OXJkakJnbFRpaGFXNEZWNnVZa3RtTGNBV2VNK29pd05BZDBUMnJQbVhmNXYwUU53M2NoRExjRE5ZNWMwcmcrbGFTM1NQRytlclhuM3E3MHFlK3BVcmdlNUVRa1VxQlRwNW4iLCJtYWMiOiIxZmZhY2Q0YzYyYzE1MzMwODlmYWEyMjFkMzQyOTY4YTVlNWQ2ZDE0ZGI5ZDI4MGM4MDBkNTc0YjcyYTAyNDAwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlNjU2p3b29jczk0Q2tsRjNtSGVUY0E9PSIsInZhbHVlIjoiMUJvRnRzMEZNNkxWK3BSZ1d0d2JRRnpQWHhZd2o2WlF4YndKT2xrYXZCd05nT2hmeVcwQkp2aXRROGJQVDRmdFkwRmFVTzVyVDVuOWwwQktYelhOdE1BUEFuUTRZSVNYY3hvMWFJSFNqZU04dVRjSWw2UlFWZ2tFVUNvNE9zTlQwWDhOTHkxWHlHR3pkYmtYeDBNMGhHSnAzVjJER1h1cGRzZnVuY1NhM1hwVTdIendBWGlSM0luTmtHSkRLNVhWajY4VUhRNW5UNlBYYkZ2V1BMYzBWL1Q1TURBMHJ3U2QzWTNlRFovMzROaUY5aUExa3NiOWYzS1ZWamxtSFZrc0c4c2FSSVgyTm5WOFRJVzJ1NXB3OXVvUHF4aW5KSUZzZ0lqWVBLSkIweFhZT2tsRFRha3pVR0hjb1o1Uk1vTGpFRHAzSll5SHpURGJ4L0ZEa0l4VDhnZkJZT3M1dlhPSHBTZlppL3lBUXBhblh3MWh1YnE4MFV5d3N1NVlHZmZ3MzN0aFJKTm9ZRHpSbE1ERE05bHZZVUMrOW1WcmU1dUp3cFdtUGwxVFhUUCsraU1ETVdKWXFpRVBqMS9OOHYxYXRzMXZ2cnNLTVNKUXRlYXdLWSs3R29ZVkE5Qk1ZREtQNVRtcjRjbktHWmx6OUE0Y0pnSC9xMUNZK1o5N1VhNG8iLCJtYWMiOiJmODFjZTliNjhhNzZkYzQ3Yzg4ODdlM2M5ZDZmYTk3MWIwODAzZWQ1NWY4OWNmN2IwMWE4NjIzOGQ3ODQ0MGNmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739656566\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-371920659 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371920659\", {\"maxDepth\":0})</script>\n"}}