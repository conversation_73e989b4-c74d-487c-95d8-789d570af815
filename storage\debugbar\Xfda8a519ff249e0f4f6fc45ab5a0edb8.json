{"__meta": {"id": "Xfda8a519ff249e0f4f6fc45ab5a0edb8", "datetime": "2025-06-28 11:25:27", "utime": **********.390032, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109926.996557, "end": **********.390047, "duration": 0.3934900760650635, "duration_str": "393ms", "measures": [{"label": "Booting", "start": 1751109926.996557, "relative_start": 0, "end": **********.322458, "relative_end": **********.322458, "duration": 0.3259010314941406, "duration_str": "326ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.322467, "relative_start": 0.3259100914001465, "end": **********.390049, "relative_end": 1.9073486328125e-06, "duration": 0.0675818920135498, "duration_str": "67.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46266688, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.020850000000000004, "accumulated_duration_str": "20.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3546562, "duration": 0.020460000000000002, "duration_str": "20.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.129}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.383319, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 98.129, "width_percent": 1.871}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-314431611 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-314431611\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-28806824 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-28806824\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2144253368 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2144253368\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-971919640 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IjlOTklZME9SM01TQjVLSlZSUlJxNkE9PSIsInZhbHVlIjoiNUVXczhZLzBsQlh2S2NINkprbS96aVppQVdwd1gxUUVnT2ppMS9JcGhWYU1HcVFkc1NXQXljeHlObDZhMEw0Si92ODZMd1pJczlJYzFBVTlOWkhqQkk0eVUrWGxuVFQ4bmdNWUkwaU5Qc0pmVE50cEdHeTZHVCtZL1k5SFljZVdtNUYwSU5UazZEMnU5WlRpaXYrOEhZWUt4M0RCdHJBMnRqbWpsckJTL2p2NCtDRVBteGlVdWprMjhPQ3VJdWNzaTNJazdnY1RjZk5ZSWZyTExQcVB5RHFma0pyYW9CcHV4ZEtYVE90VUErQU84N092ZTBpTzhkRmJld2NzUjQvTmUwRXFINGl4dG1qczY1NWNZTFQvUzZuZ2N4MVBpaE9lMlR5S1hLTnV3QlZ3bzk2ZU8wclB1V1R4WmxuK3BZRFRBSmwvaUNQSGxMNWtIVXRxWHZvY0xsd25iQ0J5UTU4K0k5QWsrc3JLRExCQ2NLcVdSU2FZMU9Zc0NKNFV0R2pPZ2dnSHo1U0NDb1FkSXRTUGo0MmpnVGYrNzVyd3dWcXd2WGgwbnBxNlFIeGtaNkE2Uk95U29OT1k2WW1xMjQ3Z3hkSDB5MG9rL3lneDcvN0lFY3lsdUZjYjV1aURBTDdvUTRNSlV4U29leEpjUG5mdG91R0xUQXBmM29CMmhITXoiLCJtYWMiOiIxOTE4ODU2NWM0N2MxNmY0NDc2M2M3NjJlOThhMzg4OWFlNzQzY2YwYzM4Y2NkMDk3YTcwZWZkMzA0YTkyYTJlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilh4RUI4NmZxOXRFS0E1UmxZUHhEb3c9PSIsInZhbHVlIjoiekVLTGt2UGN6bmtudUszTHUwcVJqbGh3Q25kQmRJc2MxbW9sTXg4VEZCdHhpRHphMHRiNHhHQ3dBSGJPUWRsNjhpRUJqd1JVTVVMR1F2dmQ3K0VHYWlKU2paMk1VVjQwUGpnaDlwM25FcFJBT003WmJiZFpIWk9nb2hCKzZKQUdUWDdlOTA1c2RKV3pENjZsYllqaFpHZWorcldab09CS3JWUnZKN1BiaHhkZmdYRHpjQmUxNWJjZWRaemU3ajVjdW5sM3pyclJRR0x5N3ZUOFBtL2MwNTRRWUh1dlRPVkRPYlNmdXcxeUFqSWtjSWpVcTU2RS9laGdaWTJhYWQzdFJhYTRpbUFJQlJNNmpwS0tTNStwdkUwT0lrRDVDNU1RWUMvTTRFaWNZZ1FRbVZuN1Nkcm1OMTlHSEF1M2VINVBsbXFsVURUOE5JbHZNd1V2K1lHbTJiZFFiR3A4aSt0cmJmbldxWTVzZDdkNzkzSTR0cnNwSVZKeVEwMGJadUlsSEM3S2wzV3BNM0x2clpTZnVYV1F0eDZRWnVsSUJNak5Ddm9ibEdqZDZKQ043TWRKbm9TR0RlUFRvUHFXYTJtaGZWVFdNd2ZNeVgyaHd6TmpxdDNVcllzQTNwR1BQY0ZtV3FsdEJwN1dPTzBKVVBQQ1MvYWpuY0xSYitOTFUxZmgiLCJtYWMiOiI3ZWFkMjRjMDRhZGUzNGQ0MDE3MzNlZjNjNGRhYzdmYjkyMDkxNTY4NWVlNjYxNDdlYTk3M2FhZGVmMTNkYTkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-971919640\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1955273598 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955273598\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1372269311 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:25:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNIL1dvMjlyQ1kwZ3d1ZXFISTJ4QlE9PSIsInZhbHVlIjoiQndoY1RQbytobHpRZmVOOGlORVg5Y2JpUHE1OUlHR0pFU1MwN0U0a0Z5ckVrdnlPcTFIT3JxeU5ybUNzdDNrcnNtM3h0Z0cvMXZxRVBKZjBhc0ZDUmI3TDJLdTBKRGg2NmRaZ3V3NmVMZFhEbDAwOGFxeGlxZVFLbGo5NVBBVUNtbHRPd3NvTm93VGVMQmIvTERUMGQ0YVBodVVITXR3N3F0RWRId2paWjN1eDhVVmxxc0ZZQWVjWlFORHJ4TDVCOTdNOHZqZlowRUVMcFNiM1k4a0tSeHR5MmhIRW93RmRwNzFyQWtWMGJVUmZSUG9pQzh4Z3Z2Si9GN0lBTkFuWFRpRHltU0RLamZhY1VNYlJuMzJCQmJjWTNibmorNFVUM2xaeVFBYjJnUUF4aUlhZyt6Qjc5U3lQSXFtQXhpRWtjSVptWFNUUnU5ejNyL1luOFhaeUZTeTV5S2poNytBdUdiWUNieFdNakY1SElCTEJPeHg1TzZwZ2piZDk0eUt4OGE1eXNDMDdLdmJWQ24rajZaWlJjNEY3SlFkK2VSMTYvZkZ4Z1RYWmNPMkFhRGlFVStSdHZPdnc4Wjkwc3RhNzM3SWN3MEJIWEQ1KzlHTkVqdld2VW1RWXduRDNrVUxFaGR3RlE4VnZmY3hJRTdDTEo1ZzBrS0pmdCtBM3QvZFEiLCJtYWMiOiIyMjQxZGI4MGM1YzZmMTg0NGMyYzk1ODc0NTI5YzlmMDJmZjBmY2NkN2FhYWFjMWJiZDMwZWI3NzNhNzY2MTU0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:25:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFVOVRKWW5ZcXAxQ25xT2p6T2RaQkE9PSIsInZhbHVlIjoiYTRpQXJ1YWZpejVMYm01cWlROE8xb29FNkJEeDFNbnVmdzBsRUFSRjFTRVhBQ0lsK1pZQk1Mb3dzd0tOM0JRcUlHS2RqMXdGWG9lVlUzcVJMMGQ0UFMySWdrWDFTVVVjZGQrMG54b3RFZjk2cEhTYjNzUnp5VHpObnBiV3VuZEZONGpDdVBlUU9iTWJad21mWWhicjVjc2krdlNURWpURjQzbjhVNi9XYlZsQ3k5VXBvVUsyWUgvTnlKajI5bVI5dkhWT3BkeVFXNmhicHYycEtqSDEwSWFPTzJFRXJmMVVEaXNwUFoyL1NaWk5xN2Nkd1VjRFFPTC8vMno3ZXJOVWNIejUzNnpjOTVoWEFDRUxmdHVsaEd5NmMxOFBrdnBpdTF6Z0JGWVhNN1dUaDZLaWpiR0dEMGRNc2VsQnJVOU45R2FkcXFYVENvOGRaZFd2VGZSc2ZZWkZKVzUxbmdDU0FkTlFCa1o4YklUSXlWK3l0SE9oK2FUQnRCNGFZeTZpOFhQMWFsOFIyRGVlYUVOTWpCdkh0VFNBek1DU1M4SUxTZ1RNSFU4T0JJblRXM0FWSUg3Y2YyVk1vbHlVeFM4UHE1WUM4c0hDaTE0TTZLS0xhRG16WHN4aCt5V3J3Q3ZkeW43WmF2NWV0QTVRcDZYendBYTNTcGNrVjdvWjdJVXMiLCJtYWMiOiI5MjE4ZDU0ZTQwMjI2OTZkODUyNTE0OTU5Y2E5NTVkNTBjZTYzZDkwYzExYzExZTU5M2E1ZTBlNzA1ZjkyZDM1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:25:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNIL1dvMjlyQ1kwZ3d1ZXFISTJ4QlE9PSIsInZhbHVlIjoiQndoY1RQbytobHpRZmVOOGlORVg5Y2JpUHE1OUlHR0pFU1MwN0U0a0Z5ckVrdnlPcTFIT3JxeU5ybUNzdDNrcnNtM3h0Z0cvMXZxRVBKZjBhc0ZDUmI3TDJLdTBKRGg2NmRaZ3V3NmVMZFhEbDAwOGFxeGlxZVFLbGo5NVBBVUNtbHRPd3NvTm93VGVMQmIvTERUMGQ0YVBodVVITXR3N3F0RWRId2paWjN1eDhVVmxxc0ZZQWVjWlFORHJ4TDVCOTdNOHZqZlowRUVMcFNiM1k4a0tSeHR5MmhIRW93RmRwNzFyQWtWMGJVUmZSUG9pQzh4Z3Z2Si9GN0lBTkFuWFRpRHltU0RLamZhY1VNYlJuMzJCQmJjWTNibmorNFVUM2xaeVFBYjJnUUF4aUlhZyt6Qjc5U3lQSXFtQXhpRWtjSVptWFNUUnU5ejNyL1luOFhaeUZTeTV5S2poNytBdUdiWUNieFdNakY1SElCTEJPeHg1TzZwZ2piZDk0eUt4OGE1eXNDMDdLdmJWQ24rajZaWlJjNEY3SlFkK2VSMTYvZkZ4Z1RYWmNPMkFhRGlFVStSdHZPdnc4Wjkwc3RhNzM3SWN3MEJIWEQ1KzlHTkVqdld2VW1RWXduRDNrVUxFaGR3RlE4VnZmY3hJRTdDTEo1ZzBrS0pmdCtBM3QvZFEiLCJtYWMiOiIyMjQxZGI4MGM1YzZmMTg0NGMyYzk1ODc0NTI5YzlmMDJmZjBmY2NkN2FhYWFjMWJiZDMwZWI3NzNhNzY2MTU0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:25:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFVOVRKWW5ZcXAxQ25xT2p6T2RaQkE9PSIsInZhbHVlIjoiYTRpQXJ1YWZpejVMYm01cWlROE8xb29FNkJEeDFNbnVmdzBsRUFSRjFTRVhBQ0lsK1pZQk1Mb3dzd0tOM0JRcUlHS2RqMXdGWG9lVlUzcVJMMGQ0UFMySWdrWDFTVVVjZGQrMG54b3RFZjk2cEhTYjNzUnp5VHpObnBiV3VuZEZONGpDdVBlUU9iTWJad21mWWhicjVjc2krdlNURWpURjQzbjhVNi9XYlZsQ3k5VXBvVUsyWUgvTnlKajI5bVI5dkhWT3BkeVFXNmhicHYycEtqSDEwSWFPTzJFRXJmMVVEaXNwUFoyL1NaWk5xN2Nkd1VjRFFPTC8vMno3ZXJOVWNIejUzNnpjOTVoWEFDRUxmdHVsaEd5NmMxOFBrdnBpdTF6Z0JGWVhNN1dUaDZLaWpiR0dEMGRNc2VsQnJVOU45R2FkcXFYVENvOGRaZFd2VGZSc2ZZWkZKVzUxbmdDU0FkTlFCa1o4YklUSXlWK3l0SE9oK2FUQnRCNGFZeTZpOFhQMWFsOFIyRGVlYUVOTWpCdkh0VFNBek1DU1M4SUxTZ1RNSFU4T0JJblRXM0FWSUg3Y2YyVk1vbHlVeFM4UHE1WUM4c0hDaTE0TTZLS0xhRG16WHN4aCt5V3J3Q3ZkeW43WmF2NWV0QTVRcDZYendBYTNTcGNrVjdvWjdJVXMiLCJtYWMiOiI5MjE4ZDU0ZTQwMjI2OTZkODUyNTE0OTU5Y2E5NTVkNTBjZTYzZDkwYzExYzExZTU5M2E1ZTBlNzA1ZjkyZDM1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:25:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1372269311\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1217071709 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1217071709\", {\"maxDepth\":0})</script>\n"}}