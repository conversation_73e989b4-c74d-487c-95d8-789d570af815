{"__meta": {"id": "Xf31ec13d2ae1f2494fae6c3e1a18b45d", "datetime": "2025-06-28 11:25:01", "utime": **********.087014, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109900.703918, "end": **********.08703, "duration": 0.38311195373535156, "duration_str": "383ms", "measures": [{"label": "Booting", "start": 1751109900.703918, "relative_start": 0, "end": **********.036847, "relative_end": **********.036847, "duration": 0.3329291343688965, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.036857, "relative_start": 0.33293890953063965, "end": **********.087032, "relative_end": 2.1457672119140625e-06, "duration": 0.05017518997192383, "duration_str": "50.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45781720, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1973\" onclick=\"\">app/Http/Controllers/PosController.php:1973-2026</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00238, "accumulated_duration_str": "2.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0686789, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.034}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.078245, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 84.034, "width_percent": 15.966}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"1542\"\n    \"name\" => \"العاب دجاج اصفر\"\n    \"price\" => 9.0\n    \"quantity\" => 1\n    \"total\" => 9.0\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-1592439877 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1592439877\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1989474830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1989474830\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1747404009 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1542</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1583;&#1580;&#1575;&#1580; &#1575;&#1589;&#1601;&#1585;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str>9</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1747404009\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">147</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IkdSRWE1YU9VQjZXbDQ3WTU5ZXJiNmc9PSIsInZhbHVlIjoiUVZyV1VQaDlIeHRSSGt3UktOTWJIcHA2TEEyZ1JqTFFVck5YSVd3L0hxcTkwREd3cnRvcnl1dzZnMGVrSW91SGU0YzY4MjU4TG9IakhodmtoZzlaNit4cmErSkFvd1loWDQ5U0p3RXZoNGphUXIyc09QUllwK0RPNjlDaW11RFVzWW56UVN2QzJ0dGdxMVFnYVB1Zi9yeXA3TWJnaGVPemh4Z3ovZXMzWVh2a0hpU0VTVUptSWorYXo0QTBaLy9yd3FHR1FwR1l2bGF0MCtROXIvSXlicHRiaVhTaTJ4Yk1jOHA0dVBUbWl3ckoxWFJyNzYrWVpKSzVPcWlnMWFWMHFENGhqQWU4YnBSVFM4VnhsdTUyaG52b1gweGdxN2Jzb2ErcTNqOFV4VEhOQXJKOTlIREYxQkJZeGhlMUE0NGZuYTNRSW5CUytrUzFqRlZodVJWdzVnQVI4TGF3NjhyWW1kYWVRQWxxYlZzaHpzUXEwQ2JhUGZ0MzBFTUZLT092MFQrY2NvMmlJU1NvaFEzZ01GaXBNLzRPazh5YkFOTExPZjNJVXhmbkgybnVBa01UUDdNSE01bXdKMHp3aklVT1FVNFZiWmxDT1lvWmM3TXpGU0lzUFNhWDIvMTdIR2JOWWIxZFdybE50aUV3MGhTN1BNc2k5UWJETjF0ZXV6SWgiLCJtYWMiOiIwNmIwMmQ4ZDc0N2ZiNDcwZTY2YWFjOTYwOTM3OGFlNmI4YTE3OGJkNWI1NTVmZDhhMGIyOGJiMGRkNzMzOGFiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5QQWN5WnNyL25SRndmYk1SdEEwZXc9PSIsInZhbHVlIjoiZDZOSldRMjBxbndLQnNsQ21ZVUVkUlU2d0lGSjhzR01lSWFCeDJidVd1M2VEZFdpMEJ4MGJhNjZhL3B6UUZvdmFuRnozMmVhTHUwbFBCbGFmSVZ2SVlVdVRvRHRpUGZ5OTRvZUlZTFRrZVgxSkQyN2tuQ1dEMGRKbFhNNlo3VUVML1JTeHdCTmRHVjJEb1E4VWtlOFZjcmRsdzV2YTVuc3JFd0Z2ai9nbWdtbU5YVzBQQjgyWnpTOTdoaFhqN053eFd2aXVkYk9DZzVxSjQrb3oxUW5YNExXS01KbFM1TTlMenlLaWJJZlQ2TzhsVnRsam92VlA5S2JGSjRCcHlJMjVjblRiUGM5NVBOVTNMaHFKUk9PdmV5SllnOWRaMUxtakR1c3BtUUs3aWV3alVHVUVLMkFDYVNFSzAzdTdzUGFLaW9VUWJBWGZrdVNOQjEvY1VoemNjR1FBSFhXU1FzeUdJcUo4aktEVTZVeFhxSnpGMzFlTjZyK29rV1YxeG9ZNVArZis5V05oL1hLWUFDWlQ2S2ZSL08wb3R3dzI0US9ENW84MGU0bmFIMyszNi9VUXpaaEQvUDk5dlNIQ25WUWhRZGNYaXF6Tk1vMllVNFZRekx4R0hRU0pXdjQ2RGxaMk9Db2J6bUl2dzBTMVN2cUlsVFI4UzV5c3pyejAwakwiLCJtYWMiOiJjYWY0MmZkNDVlZDhmZmNmMmZkODM5YmZhNDg5NDhhMzg3Yjc5MjIwOTY0NGVmNWNjNGVlYTc2OWZjMzMyMjE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-739933345 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739933345\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:25:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjM3azdqcmZDZzZnV2h3ZW5ERkFVWGc9PSIsInZhbHVlIjoieEgyQ0FpSEgyZm8xTU5SZTQ3TXlpTnU1ZjF4NkgrcHNqMlFqWkJsUlhOYWZjcDhYcFNETmtLd29KcWFUaWJIdTRyT0VTQS83QmFYcFlxTXhMN0tCaWlYQzRPaFJuV2oyMEsraWpuOGF4OTZjYkhQSHdUTlFhaENlYnQrdXBYdTIraW81VUNMZFo4U00zdFJ5NVJud2VSUDV0UnEzVTBXeGRTNFIvZW1IWDBiR0daUTY0SnVPcXo2V0xCM0tZTnpCMXMrYkdBbGY2RndEUGxKdVZxZWN5aVhpNkIzYWZ4aHUvSy9NYVR3U1FYcExoc1d1N0dGSXZrdmVJcS9WcG9HNFNtOStNdGZidVMyVWcrQkhOVHlVMkhOczJjM2dvSm9rcEVoTXJQc0lsQThCbEc2MkxUOFdQMmlYZms3VmpoUVBHZWc3NmRlMTNyYVJncmUyNFcxbW1tK0NHZmpaT0hEYzF0dUJLeCt5bTR1djdRTnk2dHptRXlsdmF4YjkxTkhqRmo4aEphSmNvWGJ2cWFaR3dvczlNOGZyUk45R01OdVJYRTMyaEFGcG9Rb0FvRFNRNkxOSnBISTlOU0lua2VJSk9KQ2dhOWQxQzk0VTRCWFJFVHU5a3ZLOGNKSGJleEZsakpDYzAxdUFtMWxjWVBEanU4R0VYekZ2L1lCQWE3blgiLCJtYWMiOiJlOGQwYTQzNGVkNDZhNzg4NzM0ZjY1ZWRjOTlkNzg1YjNiOGZiMjNiMmYzYzEwYjZjZDIxZmZjNDhmMzU2ZjkxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:25:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1WTkJyeVhaaUxkTlZTSU13dTVoVHc9PSIsInZhbHVlIjoiMVNmQU42Y0dXSEpPSVViZjFFUUg4N0FqN2ZmWDhNRkJiT2VCbXRobEtyUTZydXpabVZCQTgzUXRYMnlHSGZIemsrVU9hMEtMYklCRFIzb0h1VnZHeko0b1RneXdkYVNkMWRvTE8wd1Y3eTJjWU5ZTFNYMHB0ZGtGSHo4ODdIUnZVUUdVVTYydUE0V1JYbUY4U0VGU25QS21VK1dFZXV4V05TRmFsTDkwbWFqWWRXcTdPOVlrSXBZSEtmbUwwVi9LMEl2eG1yZHRzemJwbmIvZ3N2bm50V0JjOU1PZ3R4OXJkakFEQ1hIaWhEQUd6WlhYaDVqcSt2UVVaekwxWUZsZUUxQko2bzRSK1J0ek5pc2ZzeDN4dExoRG9EQVJPMEhFdzRpL3pXeVFNcVQzbE5GOEFMSUJqVzV5VVBYTlZKLzZiOHhXYjZhc3BaYTFheGdhTkZjMjQ0akJhUC9ORVZGY0x1Qmo0eSs4OEtKNkROMGhBUFVTbFplc0RsZmcxRlpjMG5QQVJwYXFEakgrOEhHb3grOHpHM0JUalc5cXhvQlFhbHhhbE4zbE94UGpLSVh5dnJJTjliNWtyd05nTDFJaDI0Vzl6aWdoSStjVVRMSjJoOWJYQ0JxenFDc3BaVVNyaTFUVnRaQTRrUE5PVTNtU2YvanZSZmtJaERnRTB1aVEiLCJtYWMiOiIwYjM3MmQxY2EyYjI5YTEzZTYyNThjNTg2YjhjNGY0ZTk1ZGM1MmYwZjE2MTUzN2MwMzZhZDUzNzFhOGJkNTc4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:25:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjM3azdqcmZDZzZnV2h3ZW5ERkFVWGc9PSIsInZhbHVlIjoieEgyQ0FpSEgyZm8xTU5SZTQ3TXlpTnU1ZjF4NkgrcHNqMlFqWkJsUlhOYWZjcDhYcFNETmtLd29KcWFUaWJIdTRyT0VTQS83QmFYcFlxTXhMN0tCaWlYQzRPaFJuV2oyMEsraWpuOGF4OTZjYkhQSHdUTlFhaENlYnQrdXBYdTIraW81VUNMZFo4U00zdFJ5NVJud2VSUDV0UnEzVTBXeGRTNFIvZW1IWDBiR0daUTY0SnVPcXo2V0xCM0tZTnpCMXMrYkdBbGY2RndEUGxKdVZxZWN5aVhpNkIzYWZ4aHUvSy9NYVR3U1FYcExoc1d1N0dGSXZrdmVJcS9WcG9HNFNtOStNdGZidVMyVWcrQkhOVHlVMkhOczJjM2dvSm9rcEVoTXJQc0lsQThCbEc2MkxUOFdQMmlYZms3VmpoUVBHZWc3NmRlMTNyYVJncmUyNFcxbW1tK0NHZmpaT0hEYzF0dUJLeCt5bTR1djdRTnk2dHptRXlsdmF4YjkxTkhqRmo4aEphSmNvWGJ2cWFaR3dvczlNOGZyUk45R01OdVJYRTMyaEFGcG9Rb0FvRFNRNkxOSnBISTlOU0lua2VJSk9KQ2dhOWQxQzk0VTRCWFJFVHU5a3ZLOGNKSGJleEZsakpDYzAxdUFtMWxjWVBEanU4R0VYekZ2L1lCQWE3blgiLCJtYWMiOiJlOGQwYTQzNGVkNDZhNzg4NzM0ZjY1ZWRjOTlkNzg1YjNiOGZiMjNiMmYzYzEwYjZjZDIxZmZjNDhmMzU2ZjkxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:25:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1WTkJyeVhaaUxkTlZTSU13dTVoVHc9PSIsInZhbHVlIjoiMVNmQU42Y0dXSEpPSVViZjFFUUg4N0FqN2ZmWDhNRkJiT2VCbXRobEtyUTZydXpabVZCQTgzUXRYMnlHSGZIemsrVU9hMEtMYklCRFIzb0h1VnZHeko0b1RneXdkYVNkMWRvTE8wd1Y3eTJjWU5ZTFNYMHB0ZGtGSHo4ODdIUnZVUUdVVTYydUE0V1JYbUY4U0VGU25QS21VK1dFZXV4V05TRmFsTDkwbWFqWWRXcTdPOVlrSXBZSEtmbUwwVi9LMEl2eG1yZHRzemJwbmIvZ3N2bm50V0JjOU1PZ3R4OXJkakFEQ1hIaWhEQUd6WlhYaDVqcSt2UVVaekwxWUZsZUUxQko2bzRSK1J0ek5pc2ZzeDN4dExoRG9EQVJPMEhFdzRpL3pXeVFNcVQzbE5GOEFMSUJqVzV5VVBYTlZKLzZiOHhXYjZhc3BaYTFheGdhTkZjMjQ0akJhUC9ORVZGY0x1Qmo0eSs4OEtKNkROMGhBUFVTbFplc0RsZmcxRlpjMG5QQVJwYXFEakgrOEhHb3grOHpHM0JUalc5cXhvQlFhbHhhbE4zbE94UGpLSVh5dnJJTjliNWtyd05nTDFJaDI0Vzl6aWdoSStjVVRMSjJoOWJYQ0JxenFDc3BaVVNyaTFUVnRaQTRrUE5PVTNtU2YvanZSZmtJaERnRTB1aVEiLCJtYWMiOiIwYjM3MmQxY2EyYjI5YTEzZTYyNThjNTg2YjhjNGY0ZTk1ZGM1MmYwZjE2MTUzN2MwMzZhZDUzNzFhOGJkNTc4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:25:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1542</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1583;&#1580;&#1575;&#1580; &#1575;&#1589;&#1601;&#1585;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>9.0</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>9.0</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}