{"__meta": {"id": "X238d0c1f0ba67188e25864167d95e307", "datetime": "2025-06-28 15:19:20", "utime": **********.41344, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123959.943802, "end": **********.413455, "duration": 0.4696528911590576, "duration_str": "470ms", "measures": [{"label": "Booting", "start": 1751123959.943802, "relative_start": 0, "end": **********.352397, "relative_end": **********.352397, "duration": 0.40859484672546387, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.352406, "relative_start": 0.4086039066314697, "end": **********.413456, "relative_end": 9.5367431640625e-07, "duration": 0.0610499382019043, "duration_str": "61.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45601848, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00344, "accumulated_duration_str": "3.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.384224, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 57.558}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.396678, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 57.558, "width_percent": 22.384}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.403354, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.942, "width_percent": 20.058}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-852306659 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-852306659\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-963380417 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-963380417\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1876156880 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876156880\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1106287181 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123956711%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikt3VXpLVkZrRmQwR0lYNGwxZ2N3Znc9PSIsInZhbHVlIjoiODZRZERzUkRvU25zeDN1SWorbkhTQUI2dWl0Qm02MUJ6T0lTR0RnaFQzT0RDVzRSY3k0TWVxUnNmVmpSOEpSaWJPR29lRVMvMVpZR2dKUjNnTzduNEl2VEpxTUFvYlAvWEQ3QTdVMUFlZkRJV1BPMkpLbFhwUFozTStlbGN5TWIwdC9xOUZxRnFINGxjU2lMV1BmZExKZlpWV08rUktqdXQ3YXJKZ0VndmRXN05NdEdLejdXVEFPb2JXRGFFTDMwMnBDWmVPdW0yUVAwMFY5eDRmbHNLZTFEbHZrYzVUTHpPNnVRUXVWTmNUeEtHS2xScHFnMDQvVmhjblozVW5PSWhQMzdCa2oxS0kzNE1ZWEQyaE0xSVNJdVVycGZHV3EyMkpTcmhieXR0cWlxblF0Z2E5ekt4Rnk5Q08yclcvcUdaZ2F2UCt0TTNHQldwVnNkT095Q1pkbFhkdldNMkxMVjdTZHFtamlabmFwQUxGdFVpR2JXUTc4SFc3em0vVVV3NVZvWlpkZUtobGsrS0R2eXZzM2NLMUZYdDI0VXFXdzZ3RUJBTnIwUXBsWjJmUUcxVkN4MTI5UWhmdTlWN0NsYzd2SWdFMVdRZ0d6YVcxeFBIU2RmUW1MYkhhNnd4RTlNYWs0a1ZsMk5NdnphQkRQbGZOd01tbWF6ekV4dFZxR0EiLCJtYWMiOiJlNGQyM2QzNmRhODZkNDFlMzNjYTM2YWU5YWYyMGIzOTJmMTk5ZmUzOTdlNzA2MDEwOTljMDVkZDgzZjE2NGIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlPN0ZPMzd5RC8zQmpOaTVYSytoVEE9PSIsInZhbHVlIjoiWjgwa2JVOVp0Ky9qU1BIcU5PZGtzWUtjcityU3lqT3JDTlVjY0Q1dTROSkV1NWM0WTJGRFJRWHBndzFmM0RTdzRiNnFuSVBuWnFTK1I0MVBRNjJDZGpHemlkVzYrelNnd3RqWU0yZ3ZvWXh1dVdOVElFTzdQOEoxUm5tek9FeGpZeHlicFZ6N09JaFRrZzB2RHBIalBycHd3SzliaVdBeW5oVnhEUU1wcGFld3p5VHErVmM3azQ5OXNRRW5TZUJteWFJVzBQL0ozVjU2MFd5TW8yRXAraG5WTkdmUUVGYThTYlBQVTAzRWxLak1XSFl5b2djenhZbDQ3L3Bxdmx2TUM4L24wTjZUa3kvS1ZCeDFPV3lKZzhjeVh6VkE3U3BVM0FIVFZPS0ZMOUxmRXhGT2xaNGNCc05UWG1vanl3K09obFk0eUVsajdtdnlmYUlpMU9SYnJsOUp3UXh5ZlpPNGI2ZXBwc0JINVY5Z2cycWJDNWlLUGp1QXRZOEg2dXNlLzM5ekZtLzk2STViWWNCMlRLeG1nNy9oWTQ2c2ZjOWM4RUY2ZUZuNkxiTVVObFdoRFBmZzNqV0VITTRsd0NQd0F0d1dPcldQU2tYS01TTDJqYnZpd01pK1lFeDh5TXFaeXpON3VQZlJPbkRNbm5ONUpDQUlmRXFNUCsrM0gyUG8iLCJtYWMiOiJmMTgyM2U0ZjExYjRiMWIxZGU0NWFmMWJhNmJkMmFiZmUyOGRlZWNmMWYyN2QxY2Q5NzU1ZjM0OGE0M2YwMmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1106287181\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1710539678 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1710539678\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-460206396 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:19:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVmNDFBWlRTNlFuTCtOWll3WEdmekE9PSIsInZhbHVlIjoiMVdNOFI5bnZyOEpyZlFxWXpDcHZqRXRNeCs3STlvQXFWRXR1Qk5SUDhxVitFYjJxaFFDQ05xRk9MSTUrT3Jld0wya1FQelN5V3FpRmdVMGNFb3BvU1JTZTg5bWpuY0JaeElwNHNCcEs5ZG9hSVpISEtqb3NsR1ZzM2puR0dyKzNWVDBObDJEY0txM0xockR2czFzVFkvOHE1WmtnL3lwNkFzUVN0eU5sakFjQU1scTg0blRZZ2FNWnY1VkRRdUx6VThCUThPbzFjYlBYQ01pdjRxMmcrZnYyVGhxS3lXN0toYk0xbXI3bEoyeXMzOSsyLy82ZHNZSDZ1c3NrU2VXeEN4eThBZm9EQXFsM1MwejhVTFJVaU9XT1NaYTRFUDFkZnpWZi9YYWxoc3U0UzBPSitZVHBxU09WUklWd2tsZFNoSmdQZEtvRUZsVnVqTVVSL3ZDZTNOQmFLcHpQSWRaaUk1cjd2NnMyZW1DYmNnckdIeURmdVhUdjRJSkhPOVRUd0g3TlhuMUFCaTJNS2g4NXBvMk9OQW5wR25uNmYzM1NpL2lKTVVyOVhmZG9DT0NuN1NyRFVneEJMa3B6VCthdjhCQ0pVQWY5cVdWbjJhQ2cvTG5UWlY5WVZ6dWZ5OThYTFU4Q05LTHNvVkhQYndGZXJkQmpzcSttY3BQaXQ5aTMiLCJtYWMiOiIyNmM5ODgxZWY2M2U0OWVmYzdhNjc4MDM0OTBjZTVjZTViNTFjMzVlYjk1YTk5MDBhODdjYzUxMzA5MWJkMDExIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkR6aGJwQlNmdDZKWEpVSEVFMXB0dFE9PSIsInZhbHVlIjoiRm5sMG82TUxNdk1Vazd0TmZTdGxqcExCZ1A4aDNDVjhkVzFUclhZV2FvaHpZNTAwbEdabTdaZ2J5M0IzT3RicHFMUTI2L3VUSlYwVWl3ZXpIOEpCN0pGV3pWcHdYdFM0WkxtSFg3Q2NXSE5qbk5pMW5pd0dpcEM5OWpCZWVSbVhjYVlhLzRmYk5KejlLTGFQS0dWejZlaHBxN1VPaFFKV0Rxa0ZuZUlhelpnQkErMWdZdENxbXZ5ZnIyV29sR3JUTUliZHI1dWxIM21IVDdlMkRJWkN1djFHM1gwS2JjaUR4VmFwaitLZ0paZ1FGOG51RU1hc0NybS9Ya3E3c01tbHlaanFPajEveCtpaGg3amR6cnc3Wllxd3FsOHhjL3FFUkx2K3I2ejNyditUekJ3TGp0azNFNHR3V2ZRN0N2Vldhd3dyaWdpdkF6RmhoNXIvcHBENndHK0xxSXpDVDZCbVpqbkMxVEZrdXVtY1JmU1B0djdST0JneWg5aXVyRDh6NjVPYUZ4ODA4RlJuRlB5VGl5R20zcnFzUFBDNTN1MmFJeUFoZ3MxcUdiazUyWDJNQ2hxRzBCdEl1R1lEUHA4WVhrTzdzK2JhZFhhY3AwTXRUcm04Ymw0dmJhNU54T2lFWUxLR3puWGU3YzI2VW4yMXQwM2E1QnZtZW1SZy9kM3oiLCJtYWMiOiIxZTczMTJjMDBiNzM5NWI1OTJjYmViZDI5MjMwZjA4NThmOWRkZDM3MDYwZGM1NzYzZmMwNGI5ZWFkZDc2MzkwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVmNDFBWlRTNlFuTCtOWll3WEdmekE9PSIsInZhbHVlIjoiMVdNOFI5bnZyOEpyZlFxWXpDcHZqRXRNeCs3STlvQXFWRXR1Qk5SUDhxVitFYjJxaFFDQ05xRk9MSTUrT3Jld0wya1FQelN5V3FpRmdVMGNFb3BvU1JTZTg5bWpuY0JaeElwNHNCcEs5ZG9hSVpISEtqb3NsR1ZzM2puR0dyKzNWVDBObDJEY0txM0xockR2czFzVFkvOHE1WmtnL3lwNkFzUVN0eU5sakFjQU1scTg0blRZZ2FNWnY1VkRRdUx6VThCUThPbzFjYlBYQ01pdjRxMmcrZnYyVGhxS3lXN0toYk0xbXI3bEoyeXMzOSsyLy82ZHNZSDZ1c3NrU2VXeEN4eThBZm9EQXFsM1MwejhVTFJVaU9XT1NaYTRFUDFkZnpWZi9YYWxoc3U0UzBPSitZVHBxU09WUklWd2tsZFNoSmdQZEtvRUZsVnVqTVVSL3ZDZTNOQmFLcHpQSWRaaUk1cjd2NnMyZW1DYmNnckdIeURmdVhUdjRJSkhPOVRUd0g3TlhuMUFCaTJNS2g4NXBvMk9OQW5wR25uNmYzM1NpL2lKTVVyOVhmZG9DT0NuN1NyRFVneEJMa3B6VCthdjhCQ0pVQWY5cVdWbjJhQ2cvTG5UWlY5WVZ6dWZ5OThYTFU4Q05LTHNvVkhQYndGZXJkQmpzcSttY3BQaXQ5aTMiLCJtYWMiOiIyNmM5ODgxZWY2M2U0OWVmYzdhNjc4MDM0OTBjZTVjZTViNTFjMzVlYjk1YTk5MDBhODdjYzUxMzA5MWJkMDExIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkR6aGJwQlNmdDZKWEpVSEVFMXB0dFE9PSIsInZhbHVlIjoiRm5sMG82TUxNdk1Vazd0TmZTdGxqcExCZ1A4aDNDVjhkVzFUclhZV2FvaHpZNTAwbEdabTdaZ2J5M0IzT3RicHFMUTI2L3VUSlYwVWl3ZXpIOEpCN0pGV3pWcHdYdFM0WkxtSFg3Q2NXSE5qbk5pMW5pd0dpcEM5OWpCZWVSbVhjYVlhLzRmYk5KejlLTGFQS0dWejZlaHBxN1VPaFFKV0Rxa0ZuZUlhelpnQkErMWdZdENxbXZ5ZnIyV29sR3JUTUliZHI1dWxIM21IVDdlMkRJWkN1djFHM1gwS2JjaUR4VmFwaitLZ0paZ1FGOG51RU1hc0NybS9Ya3E3c01tbHlaanFPajEveCtpaGg3amR6cnc3Wllxd3FsOHhjL3FFUkx2K3I2ejNyditUekJ3TGp0azNFNHR3V2ZRN0N2Vldhd3dyaWdpdkF6RmhoNXIvcHBENndHK0xxSXpDVDZCbVpqbkMxVEZrdXVtY1JmU1B0djdST0JneWg5aXVyRDh6NjVPYUZ4ODA4RlJuRlB5VGl5R20zcnFzUFBDNTN1MmFJeUFoZ3MxcUdiazUyWDJNQ2hxRzBCdEl1R1lEUHA4WVhrTzdzK2JhZFhhY3AwTXRUcm04Ymw0dmJhNU54T2lFWUxLR3puWGU3YzI2VW4yMXQwM2E1QnZtZW1SZy9kM3oiLCJtYWMiOiIxZTczMTJjMDBiNzM5NWI1OTJjYmViZDI5MjMwZjA4NThmOWRkZDM3MDYwZGM1NzYzZmMwNGI5ZWFkZDc2MzkwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460206396\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-954134902 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-954134902\", {\"maxDepth\":0})</script>\n"}}