{"__meta": {"id": "X2473fdb61298bed39934f13c1b6993ef", "datetime": "2025-06-28 16:01:12", "utime": **********.982194, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.549164, "end": **********.982209, "duration": 0.4330449104309082, "duration_str": "433ms", "measures": [{"label": "Booting", "start": **********.549164, "relative_start": 0, "end": **********.931247, "relative_end": **********.931247, "duration": 0.3820829391479492, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.931257, "relative_start": 0.3820929527282715, "end": **********.982211, "relative_end": 2.1457672119140625e-06, "duration": 0.05095410346984863, "duration_str": "50.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45697520, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00245, "accumulated_duration_str": "2.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.958341, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.184}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.967857, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.184, "width_percent": 15.102}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9733622, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 74.286, "width_percent": 25.714}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-862351641 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-862351641\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1773923811 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1773923811\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-55292663 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55292663\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-423871541 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126468701%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJFREo5SXZzbFZiWk5tMFVEL0dTd3c9PSIsInZhbHVlIjoiajFSaS9qdDFsbWIrUkI2dHlQK0xJU2ZKaEVTdGdhWTVUN01VY0NDV3Mza3Nxa3N6Z3dJZXlnYWpOVTlwWVJCc0ZXWW9VTG9qNVhGNXE1MzJGUnRBM0Zmb25PM1FxemtKTlZaUUZQWWNPZEZZMGdaRCtkUDJHMVd0U2FlWncwNGRsemZpcVZ5QkFoeTVBUEo4d28vZWN6bG85L0VpRWhXVWFMb3pLRFRSd1VycnJrdTJxenRoaDk0SXNCZkRaNE1ybExRblIxdHNKckZWVzhMWjc5b3JmWnhZZTZYYzVEcVdlOVJidEMxZnpUcnlKUHJRYlZTUXFUdW03bGg1Ym81ZG4yRmt6RmVzV0N3ZXFoZ1NWeVJrMHlVYWE0QkVuaXZOT0ViT2Ixb2FMMXJqTlRtUFZRZXVIWDRtN05aZ1A4WnZXU0d3R1ByN3I2YU4wQmJtTEV0Wmg1RkI1cFJrczJwUDhjSnowMzRDQXVtSTBVaWlxRTlHZGlmOEF6V0JRTURjQis4TzNiRHRKZkN6T0gydkF4SENXRFY5WWpSVUNTVCs0cy9TZzk5VHJTcHZ1ako3WmxVVjliUkRaZVlrNUhoWkV4eUtzM3MycWE5eVNRbk04ekJKZTZvYklkK3hKOXFWRm9uVm5sdGZ1OFRtQlJNNlM3ckoxaFBUUUowL3pPSkEiLCJtYWMiOiIwNmI0NzZhNzBhYzZiODdiZDE1ZDY2NGU3NTk5MGNjZTQwNWFiZTMxYmNmMjZhYjIxMzhkMTdjMmMxYjA1MmVhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJVdUdDbFRmRDdLbjZaTW1ENjhMaFE9PSIsInZhbHVlIjoiSTNRVUtDVnhwSE1VWmRVZ1VFOFZKeTcxaWxacWVGSDhyMXFzQTRkckhGcFJhVkt2cktTQVRoazhoMG1abndPbXBwN2hpM2YzUlNyQndEdkZpWVN5SmZUcGF2QlhsT0UxcVkwaW5ielcvaCtwcUxTcXY2eGo5V1BKSnNqczNlV2ZxL3diV1Q3dExtUUpPS3p0YjdzRU9oRGhka00zUFhrdGN3Zkh6V0FvWWxaQWZJaXFsb2gyZ3lGU0hmbHlJQnFGVm1mWDhvMGphK0draEN6bmpGMGd6K3A4UkV0Vmx5Rko4QUIvMHlTYU9JS3NMNEJNWEhheTNTaHpxdEFxWTlQd2FrZEJFaVlORlNWNVFVdG82enZwaUtwelJjMVgrZC9RTThpL0xOamJUTmFieEdVeEsvWS94TlBVNVFCMXJScFJ5Z2xmaXBnMENsbTJlaWxqV3BOTTJpVzdXRDFrSzY3VFh5NjlvVkloS2R1SnZtdXRId0xXalpxbzRoa0ljUU03dUNySmhzWUlMYm1CWjhDd0k4WHV6TVNPRTlUZHJLQmNKY0FGRGp1a0xFNWtKMkYxZkV4RW5ybW5uM2kzWVkwSmJ0NXZldjcxN3NYY21NNFpoQmd0M2NJdXkzTTl2eFZ3eDJIRHNmNDExM0QwVjM3WFJhbG9zbnptWmxodFNKeXAiLCJtYWMiOiI0NDU5MDgzNDFlOGU2YjEwZGIwZGNkM2RlNTc4M2FkYmNjMjM2OGYzN2Y5NDA5MTg2YmNmNGM3NDgwMWI2ZDRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-423871541\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1518471292 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518471292\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-746700750 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVwTG8wSVovWFZRbnZhTllsbzJZNnc9PSIsInZhbHVlIjoianFIVHpzaEFEUmlnMjlWZ1BYK1JkUVFFbUkwMlVub0RWYkYxNG8wbnROTEc2YytKMldtOXlXcFJkUC8xTERBZmxXYlBoTmdLNkZTZXNvWi9NRmZ0dk9QSlAxSTJLamlwaHhsMXJ6dkQ2Rmw4T0gxRzRSeHVJUnJzZ0FRSFhUNHB1WjRReHdsNk5ZNDMwdVZyQ0JvcVQyelVYaHhlSC9sOWJuM0o0cW9JUm5yYW9MMnR6dGY4QTVwaTRJOVlyZngvcHJDWmJRUzBHTVZwV1IwbnhZNVZxaE5YYzh1R2xDY2t0ZkpXZ2h6N2M1Tkw4azJYdDJXdUNzTkU0ZWYvMk1DNitVc290UUV3M1h5cmNmdk9VeW16MWsyaWRZSytESHpRTnFpTTcxeUY4WUdSaUFhSDJSTkswSDl0V2tWRXhsRmtKdzBNblpKMVJCKy9mSkVhZVBiWm8zRXF3NDRzU2s0M1ZGWHFpMlBEc2xWb2dyd2NsREtwUXFZdDFnaXhlNSs1NTRwcGUwd25hb3VSaXJueWZ1SDIzOHZIcDROYkI4SGJISnlnMjdNNytqVzNXVGMxUC80UTF0S2FNUzVRU2hJV1lSb2FuNGh1Q2V1bDUxb25jaDlWWC9EeCtON2xVZDh4ZUhBNFhpVDQ2VUtERFZXZmdoQy91d042aXdLVE9kN2giLCJtYWMiOiJhZDVkYmUwNzA2MzA2MDMzOTMyOTRlYzRjYjEyM2ExZDI2ZjY0NTdmM2VmM2YxZDNiZGEwNjgyNTg1MTUxYWUzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik53QkJRbUpDa1ZHaEpFdUdrT21zcXc9PSIsInZhbHVlIjoiRFlTT1VKc1pQWUdIVkZQVVBpVmwxbzlkQVkwVWNMRUtaRjQ1WUlWRTBVMzNiaCtxOXVuT1dRUUZrK3dwdWNnSzloWkh3ekdnaFdaRFo5REp4VUQ1dWRKNWpOVmlOUk1XRUYxazU2L2ZBUEJ5RW1EN0dtUzJWYVA2YjRKRGpZRUZxNDlEeGYvRm9ad21jeVJKV0RzVmxYMnZ2MXNwZHNoaU5yRG95Mlk1TUJ4SC9OTjVrNFhKdFE4ZXhvR2gxRC9ZUHp2eWVHM2pWM2o5UTVYeEtJVHpaSXJrOUk3bnFCUDBiU01IU1U1SU5sU2RmcVI0V2lvOXU4NVdhTlVLcDJiUC8wS2pBWTJXY2FnL3F6aTR0YVpyUUJlWG13S2JndDFpS3NqTHE2c0RIUjczMlJCZDhpSVg4RW5jK2tHVlRYMzBMQklGRjBYNW1rcGZ5K3c3elVhU3c3WnhBNVpqQzI1UXh2dVVFWmZKV242OHdLY1Z0RVpTNXRvYVdIUzNoNmN4V2ZNbGVSS2tXU3FpTzVVYU1TUFU3Rm1EREMwY3NJcEtYN0FPR0JEb3ZyOVpVK21jMHJNVmhTWE03bVVQelFRTEYxVk5BKytTdFZ5ZU54RzdOVmJONDlVa1RPMkJ2YnBmSHRxZnpnR1FsK2p1MHZsbXVMbE5jU3RHdCtSQXA5WDAiLCJtYWMiOiIxNmEzNDA5ZDE2ZGJlYTgyY2Y0YzhjYjFkZTA4MjM0ZDUzZDc0ZjExMWM5ZjU5MjNlYzU1MzI4MWM2NDg5NDcwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVwTG8wSVovWFZRbnZhTllsbzJZNnc9PSIsInZhbHVlIjoianFIVHpzaEFEUmlnMjlWZ1BYK1JkUVFFbUkwMlVub0RWYkYxNG8wbnROTEc2YytKMldtOXlXcFJkUC8xTERBZmxXYlBoTmdLNkZTZXNvWi9NRmZ0dk9QSlAxSTJLamlwaHhsMXJ6dkQ2Rmw4T0gxRzRSeHVJUnJzZ0FRSFhUNHB1WjRReHdsNk5ZNDMwdVZyQ0JvcVQyelVYaHhlSC9sOWJuM0o0cW9JUm5yYW9MMnR6dGY4QTVwaTRJOVlyZngvcHJDWmJRUzBHTVZwV1IwbnhZNVZxaE5YYzh1R2xDY2t0ZkpXZ2h6N2M1Tkw4azJYdDJXdUNzTkU0ZWYvMk1DNitVc290UUV3M1h5cmNmdk9VeW16MWsyaWRZSytESHpRTnFpTTcxeUY4WUdSaUFhSDJSTkswSDl0V2tWRXhsRmtKdzBNblpKMVJCKy9mSkVhZVBiWm8zRXF3NDRzU2s0M1ZGWHFpMlBEc2xWb2dyd2NsREtwUXFZdDFnaXhlNSs1NTRwcGUwd25hb3VSaXJueWZ1SDIzOHZIcDROYkI4SGJISnlnMjdNNytqVzNXVGMxUC80UTF0S2FNUzVRU2hJV1lSb2FuNGh1Q2V1bDUxb25jaDlWWC9EeCtON2xVZDh4ZUhBNFhpVDQ2VUtERFZXZmdoQy91d042aXdLVE9kN2giLCJtYWMiOiJhZDVkYmUwNzA2MzA2MDMzOTMyOTRlYzRjYjEyM2ExZDI2ZjY0NTdmM2VmM2YxZDNiZGEwNjgyNTg1MTUxYWUzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik53QkJRbUpDa1ZHaEpFdUdrT21zcXc9PSIsInZhbHVlIjoiRFlTT1VKc1pQWUdIVkZQVVBpVmwxbzlkQVkwVWNMRUtaRjQ1WUlWRTBVMzNiaCtxOXVuT1dRUUZrK3dwdWNnSzloWkh3ekdnaFdaRFo5REp4VUQ1dWRKNWpOVmlOUk1XRUYxazU2L2ZBUEJ5RW1EN0dtUzJWYVA2YjRKRGpZRUZxNDlEeGYvRm9ad21jeVJKV0RzVmxYMnZ2MXNwZHNoaU5yRG95Mlk1TUJ4SC9OTjVrNFhKdFE4ZXhvR2gxRC9ZUHp2eWVHM2pWM2o5UTVYeEtJVHpaSXJrOUk3bnFCUDBiU01IU1U1SU5sU2RmcVI0V2lvOXU4NVdhTlVLcDJiUC8wS2pBWTJXY2FnL3F6aTR0YVpyUUJlWG13S2JndDFpS3NqTHE2c0RIUjczMlJCZDhpSVg4RW5jK2tHVlRYMzBMQklGRjBYNW1rcGZ5K3c3elVhU3c3WnhBNVpqQzI1UXh2dVVFWmZKV242OHdLY1Z0RVpTNXRvYVdIUzNoNmN4V2ZNbGVSS2tXU3FpTzVVYU1TUFU3Rm1EREMwY3NJcEtYN0FPR0JEb3ZyOVpVK21jMHJNVmhTWE03bVVQelFRTEYxVk5BKytTdFZ5ZU54RzdOVmJONDlVa1RPMkJ2YnBmSHRxZnpnR1FsK2p1MHZsbXVMbE5jU3RHdCtSQXA5WDAiLCJtYWMiOiIxNmEzNDA5ZDE2ZGJlYTgyY2Y0YzhjYjFkZTA4MjM0ZDUzZDc0ZjExMWM5ZjU5MjNlYzU1MzI4MWM2NDg5NDcwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-746700750\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1637066585 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637066585\", {\"maxDepth\":0})</script>\n"}}