{"__meta": {"id": "X04a44ebcc534fb743c87211461448050", "datetime": "2025-06-28 15:38:49", "utime": **********.678103, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.225756, "end": **********.678124, "duration": 0.45236802101135254, "duration_str": "452ms", "measures": [{"label": "Booting", "start": **********.225756, "relative_start": 0, "end": **********.623972, "relative_end": **********.623972, "duration": 0.39821600914001465, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.623982, "relative_start": 0.3982260227203369, "end": **********.678127, "relative_end": 3.0994415283203125e-06, "duration": 0.054145097732543945, "duration_str": "54.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45884424, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2015\" onclick=\"\">app/Http/Controllers/PosController.php:2015-2087</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0024, "accumulated_duration_str": "2.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.659342, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 81.25}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.669768, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 81.25, "width_percent": 18.75}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-1188145876 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1188145876\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-990782308 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-990782308\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2051988849 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051988849\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125105407%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilo0UVVKTkNucXV2YTJsVWpGK29YR0E9PSIsInZhbHVlIjoidGRFcXJzQXhrYmt0b2tqSGtDK0pkaktHVUJlOG9CbzhUb2g4Uis1R05OeWxWaXluaVdlRXpIWjE5YjZyV1IrbDArVGxpVlZnNStOUlczUVlGZG93MW5NQk5qNkRsdlh5WUtoT2ZBbVVVZVY2MG5kSmF4Um8vS1pyYzVkV1FadTNWNFdUMXpYZ1luNXVNNFpTMm9OMG1NTUlRRThiNGxuSzFFVkdyV2ZVWktKVTNZS25KU0RpeWdBcjRQa2o0WG5TSEpFNjR3TS9rQWFMeEFua3VjemNBdVZ6UDB3dFoxWE1tQzZRaWkxMkVCZlM5VTI1aVJQTW5odHR2NG0zUlVoVyswSi9naWczL3VXOHFTSzJXOUs3Y2F1ZktzeTZMbGlrWHR4YXFWd1NkbVFyL0JmS0FaZlZMeUNmdXlNZEp3T0d2NWRkTkpaTGlIZURBck41QWp0N1p1SGZMY0lxZXhIc3ZpU0FqSjMvNlowbWdFN21XS210QVErN1o5Q0NhTmQ2VnRGeTh2TkNXSklKR1laaWQ1d2VjUGhTbTlmLzQ2MlFDSzFqcGhnVGJycGJCZUdxOHQwdy92M25aa2RpNTNLbHluR2Q2UXh5bi91aWVHVXFvQjBrNG14dUxLcHBBOWM3NjdJQ1VmaTl2NE9JbXAxOTBEdDVzTTFLUnlZSDQrdGUiLCJtYWMiOiIyNzhlYWEzMjdhNThiNzYxMTFjZDY5YzZkMDY3OWE1MjJhMDhiOWE3NzNlNmJkZDhiZDAyMDEzN2ZmZDY5OTY2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdidG5HbnVPdGFBcDlRWkd5b2dJd0E9PSIsInZhbHVlIjoiWmgrc29xb3NrSENpR3lsQkJjbFJ1N0ZsZGdpWmhRNEttd2hLdEdtZ1JLUWN2NFZPanBWTXFrRlhNUUpaRUNDTndaMkRId2xzajZQa2UxOWFLSDVSd0FPbkVTT3YrUER5WXcrRmhRZzN0WnhGRDZ4UWVoSEJoS3NzOCtROXFrUG55Y2hvbjR2N2tGTGQ0anU3aFZwL0N5VFZGZS9lV3VJY2NmV0E1MmhldXFlMmNVdUtORkZTaVhSQ0pRYUVXQlZ1dkMzQ1F0NXFTcW4xTTBoUGhRWlIzSDV5ZzhDaGRwM3l0cHdpdjl0TmZ1SjlXa1BPUzIxTm1CdzRXbDhvOW9lVzFGcXp0Mjk5eHBndDBQalhhYUQyNlJ6WWxDalZqb3UwV2UvNUJjSDBzWGlSUVM3L0lYZ212eVdENU9QcWhSbGxDTnllQmFrR2lqTzA4OUVVUGNET0ZUVVNSaUVaODlvdmdnNkdmZmJPcnlZVTRUVFAxRlRqdzJTZnRtNE8zMVB6b2dXVGR4SU5hOHRYMkREUnUwSlNzVlZvREtEcU4yQnFsS2p4QTJMZGx6Sk5ZRmcvZHgrN2hvTDZRZkJ6VGZ6Q3NvY2toVUF4TEN2QzA3Skh6MnplT2NCNC9rMVdYRzBIeVl6TkRoSTNIa2hYam1Eb25FZXowQkVKY3hmazhJT1giLCJtYWMiOiIyMTUzZjU1YzNjMTlmYzU5OWM5M2QwNTQzZmNiYmVmMTYxY2NkZWM2YTk4NmE0NGJlMzZkYjM2MTkwNjE3ZjVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1696138219 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1696138219\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2072460097 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:38:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhHMTdMNll1T2hRT0x2eWJ0amJ5SVE9PSIsInZhbHVlIjoiNmpmcjNnK0ZtZ2RTVnNtWXdGaUtDNjNvSjRmNTdDSjdWRVhNOFJ4enN4NG5GYWhTVlM0UVFMYVNoSjlVaWg1Tis0dFhTZlhlaXozZHczTkhmQUwxZ2ZlTm9GRENSWWhYeXpBcjNadERENFRxYzZ5ZXEvSHZHTUZTTlZxM3dPMXErRkg2eDVuVnZUTEJOc0ZDTEhvL2RjZkVqeFJ5QmlyUDR3YzRKL1B0c3EvckJNUVNDL2RuR3FHR3JnaUdJU0wweTgrU1Y2cnJ5SExLai84bFdjT25aYzhDNXo3VGtsYWFVQXJYaXpWQmxESGdlWXBuaWx1WmJZd1dkdlAvS2dMWTRZaFNjMitVVlEyV2RRWE1TMi9JRHBYN2RzLzVtaHNhM0ZhVVQ5ZXZ2dWRReE8wb3dJMTVhdXBBdGdVdk5yVEYvcUhwNXhkRUNYSTErN2pUQVArTWg5VzhNejhncjVYWXpmb2RVZ2laR0FRRm1paVBOTTRMVGxyalYrYXRLVEVtblFBWVJodlpmV3VBN0Y4aEZSVVdIaCsrTkM2cHZBRHdqWWtBU0tudGFQZG1mL3dVMDg3Qjc5UTI3aWRjQmtLK0hkZFNQTGJIdjVNOTNoTWRWNmNrQmZwbnJqTXB6cmswQisxckJCc3I3MXZWTGoxZTdKQTVrOUlpTzgwM1VSbGYiLCJtYWMiOiJkMjZlNTdiMmJlMzdjOTdiMmI4Njk1MmI0OWFjYTY0YzI0YmFiNzczOWM4MDRkNmEyNzVhNzJiOWQ5ZjVkMjA0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpjYitPN2FTZDk0ZDJqR0xiUGVyNkE9PSIsInZhbHVlIjoiRjFNS3ZxVk5FYnBNYnl3T3NxYzNvbEtYalhGYnRxeUNTSDZQZFRFM0N0OTIvWUdMdUQrR3VOL2VlMXFtc3RmSjR5R3lWZ0huN28yMlZ1eW5rbE0yQ0owNnRDamttNE5jVTNEd3VvKzhndGFCdm11UUlORlBFMCtKelFtOWNLeGZ6cS9DSG1zTTU0cVgzNk9GY01WVWtSNzM0TnVjN0lIKytiOGVpNVlBbkppZG9UVHZIQUtTRkxrSWxOTzN0YUNjZmtEdWl4WXc3V2MwZEFwdjI2Wmc4dFFFNGxIVkFSdW9hVUdRMlVNM1JHZFBXTzQ0TXdjSzRjL1prSS9DTkU5WEZkVVZiNDcxWDVLa0lLK2lMbXNscHhPTXFHZCtQenRxNThDS1E4eHV2dmYvRVJ5dE9HWGxENjYzQ0doaXVLbmtsdWJWQnliOWpRTnBIdkdsQlNZZTQxRkZ5YWJScnYrRU5Od0l6eWIxOFkrYWpoUlFoMXJPSHIwV0FpVWlNU1lpY3QyUCtYS1dGdFdzQUZMK0FMUzRjVEFwbWJrZ3REaG1IVEtvVlZTTnAxcVVoSnNPdENXbkpxbktnckJFTVJyeXE1aXZ4c1RoMnZCQ1RXc0Q0YXQ3VHAvUzBROUtGWFJnb3BaaEtxVURRUjB3NGZ1UDNja0ZOU3hYdVhUMWx2aFgiLCJtYWMiOiI3Y2UyZmY5MjhlODlkNDAyNmFjNzg3OWE5NjI5NGRjZjVhNzM3ZTBhMDJhODBhZmVjYTBiZWVhNTllMjZmMGNhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhHMTdMNll1T2hRT0x2eWJ0amJ5SVE9PSIsInZhbHVlIjoiNmpmcjNnK0ZtZ2RTVnNtWXdGaUtDNjNvSjRmNTdDSjdWRVhNOFJ4enN4NG5GYWhTVlM0UVFMYVNoSjlVaWg1Tis0dFhTZlhlaXozZHczTkhmQUwxZ2ZlTm9GRENSWWhYeXpBcjNadERENFRxYzZ5ZXEvSHZHTUZTTlZxM3dPMXErRkg2eDVuVnZUTEJOc0ZDTEhvL2RjZkVqeFJ5QmlyUDR3YzRKL1B0c3EvckJNUVNDL2RuR3FHR3JnaUdJU0wweTgrU1Y2cnJ5SExLai84bFdjT25aYzhDNXo3VGtsYWFVQXJYaXpWQmxESGdlWXBuaWx1WmJZd1dkdlAvS2dMWTRZaFNjMitVVlEyV2RRWE1TMi9JRHBYN2RzLzVtaHNhM0ZhVVQ5ZXZ2dWRReE8wb3dJMTVhdXBBdGdVdk5yVEYvcUhwNXhkRUNYSTErN2pUQVArTWg5VzhNejhncjVYWXpmb2RVZ2laR0FRRm1paVBOTTRMVGxyalYrYXRLVEVtblFBWVJodlpmV3VBN0Y4aEZSVVdIaCsrTkM2cHZBRHdqWWtBU0tudGFQZG1mL3dVMDg3Qjc5UTI3aWRjQmtLK0hkZFNQTGJIdjVNOTNoTWRWNmNrQmZwbnJqTXB6cmswQisxckJCc3I3MXZWTGoxZTdKQTVrOUlpTzgwM1VSbGYiLCJtYWMiOiJkMjZlNTdiMmJlMzdjOTdiMmI4Njk1MmI0OWFjYTY0YzI0YmFiNzczOWM4MDRkNmEyNzVhNzJiOWQ5ZjVkMjA0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpjYitPN2FTZDk0ZDJqR0xiUGVyNkE9PSIsInZhbHVlIjoiRjFNS3ZxVk5FYnBNYnl3T3NxYzNvbEtYalhGYnRxeUNTSDZQZFRFM0N0OTIvWUdMdUQrR3VOL2VlMXFtc3RmSjR5R3lWZ0huN28yMlZ1eW5rbE0yQ0owNnRDamttNE5jVTNEd3VvKzhndGFCdm11UUlORlBFMCtKelFtOWNLeGZ6cS9DSG1zTTU0cVgzNk9GY01WVWtSNzM0TnVjN0lIKytiOGVpNVlBbkppZG9UVHZIQUtTRkxrSWxOTzN0YUNjZmtEdWl4WXc3V2MwZEFwdjI2Wmc4dFFFNGxIVkFSdW9hVUdRMlVNM1JHZFBXTzQ0TXdjSzRjL1prSS9DTkU5WEZkVVZiNDcxWDVLa0lLK2lMbXNscHhPTXFHZCtQenRxNThDS1E4eHV2dmYvRVJ5dE9HWGxENjYzQ0doaXVLbmtsdWJWQnliOWpRTnBIdkdsQlNZZTQxRkZ5YWJScnYrRU5Od0l6eWIxOFkrYWpoUlFoMXJPSHIwV0FpVWlNU1lpY3QyUCtYS1dGdFdzQUZMK0FMUzRjVEFwbWJrZ3REaG1IVEtvVlZTTnAxcVVoSnNPdENXbkpxbktnckJFTVJyeXE1aXZ4c1RoMnZCQ1RXc0Q0YXQ3VHAvUzBROUtGWFJnb3BaaEtxVURRUjB3NGZ1UDNja0ZOU3hYdVhUMWx2aFgiLCJtYWMiOiI3Y2UyZmY5MjhlODlkNDAyNmFjNzg3OWE5NjI5NGRjZjVhNzM3ZTBhMDJhODBhZmVjYTBiZWVhNTllMjZmMGNhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2072460097\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-269222883 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-269222883\", {\"maxDepth\":0})</script>\n"}}