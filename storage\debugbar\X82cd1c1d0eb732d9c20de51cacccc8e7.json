{"__meta": {"id": "X82cd1c1d0eb732d9c20de51cacccc8e7", "datetime": "2025-06-28 16:30:29", "utime": **********.369409, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128228.931027, "end": **********.369425, "duration": 0.4383981227874756, "duration_str": "438ms", "measures": [{"label": "Booting", "start": 1751128228.931027, "relative_start": 0, "end": **********.314259, "relative_end": **********.314259, "duration": 0.38323211669921875, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.314274, "relative_start": 0.38324713706970215, "end": **********.369426, "relative_end": 9.5367431640625e-07, "duration": 0.055151939392089844, "duration_str": "55.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45707424, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025199999999999997, "accumulated_duration_str": "2.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.342824, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 57.143}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.352613, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 57.143, "width_percent": 19.048}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3603418, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.19, "width_percent": 23.81}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImMwU2FScUI2OTZDQ29LYzdKMEllOWc9PSIsInZhbHVlIjoieW52bTQxTnpHZEttc3RxYVpzcU9EQT09IiwibWFjIjoiZDZjZmQ5OWY0OWRkMWI1YzE0YjgzMGU1ZmNkY2NjNTViMmUzOWJhZTMxNTQzZWYyNjQ0MWQ0MzM3MzRmM2UxMSIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1426484348 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426484348\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1058801737 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImMwU2FScUI2OTZDQ29LYzdKMEllOWc9PSIsInZhbHVlIjoieW52bTQxTnpHZEttc3RxYVpzcU9EQT09IiwibWFjIjoiZDZjZmQ5OWY0OWRkMWI1YzE0YjgzMGU1ZmNkY2NjNTViMmUzOWJhZTMxNTQzZWYyNjQ0MWQ0MzM3MzRmM2UxMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128226556%7C48%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjV6Z0FwaEQwKzJYOGs5VW5XZC9NZXc9PSIsInZhbHVlIjoiSTdyZXFYc1hSN1pWV3N2Y3owOVRWTVdFb2d2UWJjeGlwUklEM0JSS25FQXRHVHI5eGxQUmIwNC9Lbnl1c2xnZXNzYm96TGt0eEpFVHRETnV4bE9zYVVMelYvdkw5V0toVmdQaXNoWVFBOTZvOUdxRjFLd1ZHdllVQm1hOURFL2RneGFBbzFnc0dFWHdUU2RIcmFIVEFSOUhMUys3bTJaY3FSRSsycmw1UUtZSmtkdnpPMHNReUl3eG9lQjdqK0RwT1JkZ0c5bTJUSEJKQWpBN0k0NzR6Y1NUeUVZdlM4alNCT1A2Z2VoVEV6K011SVFDeDlSRU1VZXFYS1A5alViWGpHYzM0M1drbUo3QWlmc1kzQUNSRCtXK1hpV3FBNk9mNkt6ekVkcWdBRi80NzhCV0ptS2g3WW56c2hEOEhhRXRGOWcwa1p6NFNqZUZTOHByYkhqTndDVnVaUFlqbGxIR25CRldkMm05dEhIaExsazBPVkdrWlZ2eUFhSXZtVnhSNGlNMlJ5NXI1NFUvZkVYNmx1R2NOM2tJY2Z2ZDZxbWRLNzByWWxaWDhyTzhOSkpycXNiSHRmSXo2dGgyTnZ5elpVYzFuSVJ4cTZKSkkyQlh5bXM4K1RCRzhpOVFIVnNUV3J3YTF0ejRuQnIrMGVXU2JPZXdhM1lubU05eitrVnoiLCJtYWMiOiI2YTliYjRkYmFiZmFkOTlhOTI2M2JiZmRhMWExZWNhODM5ODJjYTUwYTg2Zjg3MDY2NzBmMTg5NzM0YjE0OWVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJDTSt2R0RhcGxTSHphbUtOUEZlQWc9PSIsInZhbHVlIjoiVmxGTWxqYXFpMTdCRVc2eWRxS1I0N1VvYWtidDB2QkhVYU9ud00wRWU3QTRMc1pOc2VMd2ZNUDJLL3V3SWxUNjZNRkdERzgrc0JFZSs0NG5EL2N0UHltNUhGMlJDTmZhdDE4cnBJdzJ2bmhCUk1GVmxKUVpMSU94emRuWWcwSHVheDc3TmIzMGc0dUpzL1ZaU0JkSmtRQStJL1Y0TXNRekdjeEhobDFqbCtUbmRLM2RCNU1ZUW1NVk5HSHJLNkJpWFRySE1Lb3E2a24vQUdGRSs4b1BNeGhtY2tHMmZ1UEtpd1ljbElyak1XeXlDckdqa2VIZVEyS2V4enh4TmtMTithQWtqTC9FbmJ4Zmp2UVR5MHQ3Mko0R2UrY0NXd2l0TllsdEQ2a2pYR0Jzdk0zME5KOVJiUkFKa2J2b0paZmFqYWduWjh0dmFpVWdFTTJ6TUtMQWs4eW1zT0ptbDNMb1A1S1VYM2dTY2lnM2hDK1EwNXQ3Ky9SS2psdGpFVVkzeDgyNTdwQjlWOXNpc1B2ZEVEVnVVamJDZTdMZVA1enRRZTdYLzRoMTdkS3haU2NSVEVUSFNtaHBMMVBoTDUwY2crRXJ5TXQxSDgxT1NSQkRORlYzZ01FWjk5VzhVQWJVcDBST3hUZ1dnYSs0ajdwY1BwNzVwdDZ6cE9PMElNN0siLCJtYWMiOiJkOWQwMjgwMTgxMTY2MGI4MjU5YTlkZTViNjBhMTNkOTI2MzlmYjYzNWUwZTZlOWJkNThiNDQxZDNmY2NhZjBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058801737\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-185391535 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185391535\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlI1c3NoSEFkQUxDamN0b3RtSVNZcVE9PSIsInZhbHVlIjoiS29XbElWYW9JSmFER3Y4b0F5bmtKclNkMUY4MjEyN2Nxa0VUV3FTWlRFS0g1RXEwcU50c1grQ0w4MUdONTRjRXhRSjBlYTRYWVM2cWhiWjY1NmxFTDFJYXVHVkZabFZoUUVTVUFsaVlIZ2ZaZE5XWGxBYUh5QzEzU2FGYmFyUnpMZWJDeVRjbU4rbytYOEwrMkRjdTNraVMyYlU3VHNLWFRLZkJ4Vkpvc2FHcHZVQ0xneXFDZ3FXRXBvS3NBR3BkTFh2WkFLbS9XNitwdVJiTXNWdFhhd1BHbXB3OHgxQjd6clRQeEZ2cEU4L2FkTkJuSzZrREhQM2ZWaUZZSGh1NW9RVWFpUDNIZThnYTRVUTJNWnZ5dWtOQmRodTFUYUpVaE5DWURYZDhQcngzR0I4U0JYZlhGR3YwUWxpeXpmNlkxczRVT3NuNm5SdUZtb1ViTXVFRVMySDFGTTRxVk5ST1BiWm4zZXpKMlpGWWtXSzFLMFRDaWwxbk9BZllTN0tTQzNKKzB3NnlLREtRRTM2ZGp3dFlwcCsvTEJRa24wbkJqdzMvQnluYk5tWGRJYkJLZVh6WmxNSUxjMUpXeVA2bWUrY0MwWHRQUmVnRHlpUFAwWmZEYVl2dzdMUFJ2ZFRDTjF4MlFzVFZWWG5CZTVoZWczbDlEVDNqSjVpM1pSdjIiLCJtYWMiOiIxYmY2NDVkYzNkOGYwNTEyZjJlNTVjN2JmNzY3ZWNmOWFkZTQ5YTQyMWMwMjdjM2YxMjYzZmNkNTU5MzMyMjQxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZTUEhyTWE3ejRSSElPVEp4eGc5L3c9PSIsInZhbHVlIjoia0ZwS1dDaGZkUEcwTTBlNEV5WUNGank3d1k4NUhUdi9pWTNmcG5HSC9rTFNicXVNd2Z4engvNmNTS3ZiVnJOaHVvK3IzMGwzdjJXSDl2MFQ3QWdTNHFRMmg5N1dXbEhjR1ZtTEI2amZma29SdUdUajlpVk9nOFNsUGRXWlZlOTJoZWdRN3QwNUkzNGFKSlA4UzVwNXZKay9pVDR0SEk2WDYvMnNjR3d0T3FKRGVDSEliaC9ubWpMV2hGUEl2eTRnWEJmLzhWMnlMbEhxczRzZzFzQzNiV0VHQTUrV0k4dWxyWnc5Y0ZPcU9VaUFEZEtvQ0NMVC81N2ZzMlI1WTZkdzVTZlJEcE51enlqcWMxUGxnRjgxTW5mZzBwYzhkdWVORmEzVXNFL3BFTitaRkljRFFEbWlqVDFhaEFxU3BRRGg2aGFWNDhsZEtzNjZzUHlmOGFYUlc2akg3ejZjbmswR2UrNndHb0hWa3pCKzZCQjdCZFlzNG9zMmZsOFFvM29HZ3Npck4yVERyZGQ2NnUwU1pqSXo0MzJhd3Y3NS9mcVlFTGprOC8vQUxxdWxIUXZPd0lBZWpnamdJRmRjaGJpT0ptOTdTVTRleU9HejFBL0Vld3ZzQVZSYnNhTjlMQkpKSGI0U0dQVGRCbFdRTlJ0N092bkVmbzRnSWpBcW4xeUEiLCJtYWMiOiIzMWZkMThhZmI5OTJkMzQxYzllZDIwN2UxMTdjZjI2ZGZlMTFlMDA5Nzk0YzRlMWQxYjQ3OWExMjMwOTgyNzkyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlI1c3NoSEFkQUxDamN0b3RtSVNZcVE9PSIsInZhbHVlIjoiS29XbElWYW9JSmFER3Y4b0F5bmtKclNkMUY4MjEyN2Nxa0VUV3FTWlRFS0g1RXEwcU50c1grQ0w4MUdONTRjRXhRSjBlYTRYWVM2cWhiWjY1NmxFTDFJYXVHVkZabFZoUUVTVUFsaVlIZ2ZaZE5XWGxBYUh5QzEzU2FGYmFyUnpMZWJDeVRjbU4rbytYOEwrMkRjdTNraVMyYlU3VHNLWFRLZkJ4Vkpvc2FHcHZVQ0xneXFDZ3FXRXBvS3NBR3BkTFh2WkFLbS9XNitwdVJiTXNWdFhhd1BHbXB3OHgxQjd6clRQeEZ2cEU4L2FkTkJuSzZrREhQM2ZWaUZZSGh1NW9RVWFpUDNIZThnYTRVUTJNWnZ5dWtOQmRodTFUYUpVaE5DWURYZDhQcngzR0I4U0JYZlhGR3YwUWxpeXpmNlkxczRVT3NuNm5SdUZtb1ViTXVFRVMySDFGTTRxVk5ST1BiWm4zZXpKMlpGWWtXSzFLMFRDaWwxbk9BZllTN0tTQzNKKzB3NnlLREtRRTM2ZGp3dFlwcCsvTEJRa24wbkJqdzMvQnluYk5tWGRJYkJLZVh6WmxNSUxjMUpXeVA2bWUrY0MwWHRQUmVnRHlpUFAwWmZEYVl2dzdMUFJ2ZFRDTjF4MlFzVFZWWG5CZTVoZWczbDlEVDNqSjVpM1pSdjIiLCJtYWMiOiIxYmY2NDVkYzNkOGYwNTEyZjJlNTVjN2JmNzY3ZWNmOWFkZTQ5YTQyMWMwMjdjM2YxMjYzZmNkNTU5MzMyMjQxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZTUEhyTWE3ejRSSElPVEp4eGc5L3c9PSIsInZhbHVlIjoia0ZwS1dDaGZkUEcwTTBlNEV5WUNGank3d1k4NUhUdi9pWTNmcG5HSC9rTFNicXVNd2Z4engvNmNTS3ZiVnJOaHVvK3IzMGwzdjJXSDl2MFQ3QWdTNHFRMmg5N1dXbEhjR1ZtTEI2amZma29SdUdUajlpVk9nOFNsUGRXWlZlOTJoZWdRN3QwNUkzNGFKSlA4UzVwNXZKay9pVDR0SEk2WDYvMnNjR3d0T3FKRGVDSEliaC9ubWpMV2hGUEl2eTRnWEJmLzhWMnlMbEhxczRzZzFzQzNiV0VHQTUrV0k4dWxyWnc5Y0ZPcU9VaUFEZEtvQ0NMVC81N2ZzMlI1WTZkdzVTZlJEcE51enlqcWMxUGxnRjgxTW5mZzBwYzhkdWVORmEzVXNFL3BFTitaRkljRFFEbWlqVDFhaEFxU3BRRGg2aGFWNDhsZEtzNjZzUHlmOGFYUlc2akg3ejZjbmswR2UrNndHb0hWa3pCKzZCQjdCZFlzNG9zMmZsOFFvM29HZ3Npck4yVERyZGQ2NnUwU1pqSXo0MzJhd3Y3NS9mcVlFTGprOC8vQUxxdWxIUXZPd0lBZWpnamdJRmRjaGJpT0ptOTdTVTRleU9HejFBL0Vld3ZzQVZSYnNhTjlMQkpKSGI0U0dQVGRCbFdRTlJ0N092bkVmbzRnSWpBcW4xeUEiLCJtYWMiOiIzMWZkMThhZmI5OTJkMzQxYzllZDIwN2UxMTdjZjI2ZGZlMTFlMDA5Nzk0YzRlMWQxYjQ3OWExMjMwOTgyNzkyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImMwU2FScUI2OTZDQ29LYzdKMEllOWc9PSIsInZhbHVlIjoieW52bTQxTnpHZEttc3RxYVpzcU9EQT09IiwibWFjIjoiZDZjZmQ5OWY0OWRkMWI1YzE0YjgzMGU1ZmNkY2NjNTViMmUzOWJhZTMxNTQzZWYyNjQ0MWQ0MzM3MzRmM2UxMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}