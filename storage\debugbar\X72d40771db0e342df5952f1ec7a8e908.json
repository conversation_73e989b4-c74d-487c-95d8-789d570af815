{"__meta": {"id": "X72d40771db0e342df5952f1ec7a8e908", "datetime": "2025-06-28 16:20:52", "utime": **********.860932, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.45538, "end": **********.86095, "duration": 0.40557003021240234, "duration_str": "406ms", "measures": [{"label": "Booting", "start": **********.45538, "relative_start": 0, "end": **********.805557, "relative_end": **********.805557, "duration": 0.3501770496368408, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.805569, "relative_start": 0.3501889705657959, "end": **********.860953, "relative_end": 3.0994415283203125e-06, "duration": 0.055384159088134766, "duration_str": "55.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45715040, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0022199999999999998, "accumulated_duration_str": "2.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.833601, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.27}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.842692, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.27, "width_percent": 17.117}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.847547, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.387, "width_percent": 12.613}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IllIcE5GT2gzZUxNblQzWWtHQzRQQVE9PSIsInZhbHVlIjoiaG9UQVU0TDROZlhBQ2QzMDBqU1VvZz09IiwibWFjIjoiNDEzYThkZTIyYWViZDk3YmNjYjFhM2M0OGQ5NGEwZTI4OWExMDFiNjU1MjJmM2E3NDVkZjhkMWU5ODE3NWNjYyIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-509279103 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-509279103\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1839455000 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1839455000\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-328854135 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328854135\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-467972975 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IllIcE5GT2gzZUxNblQzWWtHQzRQQVE9PSIsInZhbHVlIjoiaG9UQVU0TDROZlhBQ2QzMDBqU1VvZz09IiwibWFjIjoiNDEzYThkZTIyYWViZDk3YmNjYjFhM2M0OGQ5NGEwZTI4OWExMDFiNjU1MjJmM2E3NDVkZjhkMWU5ODE3NWNjYyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127647762%7C41%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFTb1lLNEkrWmtjNEQvWVdIdktXY2c9PSIsInZhbHVlIjoiRG02ZTg0Z0JDemJUSzZSOGk5c04wdmRkYldVcUhJREl0Z0FFUDdWa1VGdWltMVZWNW02SCtvWVZpa3J5c2hPOC9hbktGUjcvOUxMZk9aNTZnTDRHQjFuaFB5RFZpaFo4L2ZVb2FFQ3A3VlIwLzBDeDdiMEg1QlNnYUo3Z2QvajN2Y0FaOVZPd0RqM0dtWlpMSWRxa3QrbzFKSEFVelpKV3BTeDFGU3VhUy9KZFFMUElyeGFpMng4dThXNGNpdkltVFdlSUdBakl3NTcrbDJSNk0rU1dVQ3RqTWNXVHJId1JjSmxYcXZDZEJBWlpLbXMzRTdkVU50VXZQaEJtNnoyaW1ZOGRtVnBNdGVYZHVRNXB1VVZxV29vZGxFMEJmWnpMeEQxMitwcjVLa2dRY1c2Um5OMGlmQUJ6Y0dvS21kMFJlSHhkd2pFdmRsM3I4MExjcUZWUXB6MnlIYTJUU1czaU9FZjFaU3F4aU1uaGRHSkhuZ29VQ1NFNjhDVkJRUnhqdER5a0pKZ0hRRVlVWnJYSnp0ZWpYdmc3aTFWTS9kY002UGVCZklFemVZUk5IRVdVdkNOSFJwdHBueGxVTE84OU5pVHhpYUROZTZXWW8xSlZvY1dxRW01N25NdjRZZWFCbjM2NDZoaTFycjNaQzBmL1pMVWgxNm40RWR2MElYU2QiLCJtYWMiOiI4ZTM5ZGRmNDU5YWJmOTlmYzk4YTdiZDliYTJiYTViNWJlODg5MzJlNjMyOGFkMzZjNjQwN2E0NDIwMWYyZGJmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdIMEJ4WEpJZHB4NXIwUHREUkVGcXc9PSIsInZhbHVlIjoiOFFCK0FVTlFxUEhOQ3ZkdXltSjFmdWFFbk5JRWxjS0JXTytVZCtuWEU5VDUvRW1nVW9UTkNWalNjaUdNazNRSzk4N04yek9TWkxSMm1CY29Lc0dEQmZ4a0NxSVBaY2FIT2VEeW1JNjJkSHhieU5rSC9PMnM4NWt3cU12cVNYV2JSQWxuS0pxVW9HN21aREdENVFldHFFcGkwMFZKMWlFa0xXcHkwOFhzQVNIWG4yK0lIa3dWRzFKaVN6SVI3Q1VDdDVqZWd3cWY1amdmZDZsSW05SjlqK3pnRng4bU1NeHNESUlvN2hRaVBCK1dDYWVuVlVUdkNCMllLcFlid2wwZ3NzRFdlWk16QytFL1JzeXBxMjZRblZoMWF3SFhyWG5VVTVuUWVCM0YzRkVEWW9makhuNHhmbk9lRXdLdEVXMHczYmcxSTk4eFVGaWRxYzlOSW4wWGdqL3hvN3ExcXVWaC9TaFYvQjd3NEt6RktScVhrckRMYTB2OEo1UUNIMXRPelJxS2N6RGk3bks5OGxiWHppS2YxUVhDZklWb0h3SzRjYjA1bHdaclN0Q0Q1RkJmTE9rN0kvVVNPUXIvbkZ1QmdXZHVvcEFnTW5qUUdMNXVPU204cXRQT211U1RqeUFNYzdiMkFhWWVENFNvcWNyMEx2UWZPMVl1QmloN1dDUVciLCJtYWMiOiJiYjI2NGY4NDJmYmEyMTI3Yzk3NWQ3ZmRlNjRhNjFkNmFjODMzYmNjMTI2OWM4Yzg4NjhiYTlkZjA1MDZiNzM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467972975\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-981721741 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-981721741\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1133149499 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:20:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdLSWk5aHhpaXN2VDFyUTJES1AyRVE9PSIsInZhbHVlIjoiSlJKWE90bFN4VEtORnc4YUNsZnFvQ3BCYVZKcVo2QmFpU2t4cXhtWGxPdGVEN29zNFlTUnIxYmdqblBaQzZIdFp3dWplZUY5czVjVUlMb1JMS3A3djBZQ0tGVWpESTFNUHF5Z2tTVXFSU1cydExFakZVRTNyRlN0RTdmRktJRjNiUFV2L0JqaVZnNmthWFRTeC9mcHNvRlFPMzUrL2J0dlJKeFdvVy9ONThTUTBqMUlhdHNmRTVuMk9hbWFLdU9UYUN6dmE0V1hzV3NzUy92VHRXL3pyN2VzalgrL0dPR0NDQ3E5RjlQZVE3K29teWhobHFrL28vclJva0hYTnFHcHJQMmVibXhENmdTYVIyZ3gxblZjdjE1TWx4d2VMUXIrcEY0Y3lrei94eW5nV0xYQ1h1RzdMdURjR1FvWG1tSTdvSXN1WC8vSnZ3Ung5NDZqR2dXbC9yTVh5K1ErS0N0WVMralVIMHZrMzl6aXpOQzNWVzdDTFg1VFhTSWxnbFlOUW1ZU0FJakY0c2dtVTY2MzByalU3dU80UmloWTZMRzMrcnRraXFuK251a2l3bEdERUJnYVhiTElkdm85LzJseHZkcmtaMEpJQ2lBWUpCVjN6VmtJRG0zSlF2dWxxQWM3NVc2Zktjd1JnVjdERk5mV0hId0s1MU05ajZXbG9CVnciLCJtYWMiOiIyYjRiMjJhN2Y2NWVlNTczOTljZjc0ODM0NzFjMmMyYjE4Mjg4YWU0YjE5NzZhOGIzNjExMDk3MzEwOTJkNzMyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:20:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik9TTFc0Sk0vT0VpbWlMYVQ2TE1FSEE9PSIsInZhbHVlIjoiOWpNV083S3BFeldQdTBpaDJWWERFU2ZVK2pWdEhBaWhBZXFiVUhLelpzYnFuYXhTVWdSb1dxUXI2dEU0ZTYwOUxYVitkMnoxZEZBQWNWQlBSWmFncUhpbElkVklGYnRoNThwN0ZlOUJPaFBSby9LcjRJUkRZY1RZbFovcStxUDVhVGtqRWdnWmx1RnpoMitjK1d6NG91bVE2K3ZFTU1TS2xsWnorYXJVdTluOCtraUV3dUthS1lhcFh6Vm1YM3N3dXBMaGVURllOcytJNk9KQlR0SUVIM29kMkgyTU02T3FmVzZzbm1Wc2hZdW92ZnVRYlQ5QUZDNlVzcnRDcHpvMkEwSDRkS0ZzaHdsMlJuZlc3VWJ6Ym12TlpMM3U3eER1ZTh1TVJ1SHJDYU1TVlNLODJpekNSZUJZb3R4SmR1VEV0WWMyNXU2QkVSK3VTdUVIQlFkL3ZJVE0rclNtUVBpQW9uM0VxZUdMODVXSFFkMjNWa2liNS9naDJXR2hXdlRTYmFvL3FnaDNDU3NpWXB4WHlWTHhtMmVvRmdybHpueUVjMUp2K3luK01HakxQSUJ1K2FEbzFucTlMNWViNkVRaTFsZi9LYmN3VlhoT1QxeU90aitmek8wZSt1dUxlSXpCVjhQY2dDc1d2ZDFqa1pBTk9ScVlWVXNPYlUrczQzbVkiLCJtYWMiOiI3MmY3M2FkMjBhZTgyNTYxOWQzZDczODIwZmUyMmEwYmIwMWRkNTlhMTdjNWI5ZTdkZDI3YzM0MTAxOWNkNWUxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:20:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdLSWk5aHhpaXN2VDFyUTJES1AyRVE9PSIsInZhbHVlIjoiSlJKWE90bFN4VEtORnc4YUNsZnFvQ3BCYVZKcVo2QmFpU2t4cXhtWGxPdGVEN29zNFlTUnIxYmdqblBaQzZIdFp3dWplZUY5czVjVUlMb1JMS3A3djBZQ0tGVWpESTFNUHF5Z2tTVXFSU1cydExFakZVRTNyRlN0RTdmRktJRjNiUFV2L0JqaVZnNmthWFRTeC9mcHNvRlFPMzUrL2J0dlJKeFdvVy9ONThTUTBqMUlhdHNmRTVuMk9hbWFLdU9UYUN6dmE0V1hzV3NzUy92VHRXL3pyN2VzalgrL0dPR0NDQ3E5RjlQZVE3K29teWhobHFrL28vclJva0hYTnFHcHJQMmVibXhENmdTYVIyZ3gxblZjdjE1TWx4d2VMUXIrcEY0Y3lrei94eW5nV0xYQ1h1RzdMdURjR1FvWG1tSTdvSXN1WC8vSnZ3Ung5NDZqR2dXbC9yTVh5K1ErS0N0WVMralVIMHZrMzl6aXpOQzNWVzdDTFg1VFhTSWxnbFlOUW1ZU0FJakY0c2dtVTY2MzByalU3dU80UmloWTZMRzMrcnRraXFuK251a2l3bEdERUJnYVhiTElkdm85LzJseHZkcmtaMEpJQ2lBWUpCVjN6VmtJRG0zSlF2dWxxQWM3NVc2Zktjd1JnVjdERk5mV0hId0s1MU05ajZXbG9CVnciLCJtYWMiOiIyYjRiMjJhN2Y2NWVlNTczOTljZjc0ODM0NzFjMmMyYjE4Mjg4YWU0YjE5NzZhOGIzNjExMDk3MzEwOTJkNzMyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:20:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik9TTFc0Sk0vT0VpbWlMYVQ2TE1FSEE9PSIsInZhbHVlIjoiOWpNV083S3BFeldQdTBpaDJWWERFU2ZVK2pWdEhBaWhBZXFiVUhLelpzYnFuYXhTVWdSb1dxUXI2dEU0ZTYwOUxYVitkMnoxZEZBQWNWQlBSWmFncUhpbElkVklGYnRoNThwN0ZlOUJPaFBSby9LcjRJUkRZY1RZbFovcStxUDVhVGtqRWdnWmx1RnpoMitjK1d6NG91bVE2K3ZFTU1TS2xsWnorYXJVdTluOCtraUV3dUthS1lhcFh6Vm1YM3N3dXBMaGVURllOcytJNk9KQlR0SUVIM29kMkgyTU02T3FmVzZzbm1Wc2hZdW92ZnVRYlQ5QUZDNlVzcnRDcHpvMkEwSDRkS0ZzaHdsMlJuZlc3VWJ6Ym12TlpMM3U3eER1ZTh1TVJ1SHJDYU1TVlNLODJpekNSZUJZb3R4SmR1VEV0WWMyNXU2QkVSK3VTdUVIQlFkL3ZJVE0rclNtUVBpQW9uM0VxZUdMODVXSFFkMjNWa2liNS9naDJXR2hXdlRTYmFvL3FnaDNDU3NpWXB4WHlWTHhtMmVvRmdybHpueUVjMUp2K3luK01HakxQSUJ1K2FEbzFucTlMNWViNkVRaTFsZi9LYmN3VlhoT1QxeU90aitmek8wZSt1dUxlSXpCVjhQY2dDc1d2ZDFqa1pBTk9ScVlWVXNPYlUrczQzbVkiLCJtYWMiOiI3MmY3M2FkMjBhZTgyNTYxOWQzZDczODIwZmUyMmEwYmIwMWRkNTlhMTdjNWI5ZTdkZDI3YzM0MTAxOWNkNWUxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:20:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133149499\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IllIcE5GT2gzZUxNblQzWWtHQzRQQVE9PSIsInZhbHVlIjoiaG9UQVU0TDROZlhBQ2QzMDBqU1VvZz09IiwibWFjIjoiNDEzYThkZTIyYWViZDk3YmNjYjFhM2M0OGQ5NGEwZTI4OWExMDFiNjU1MjJmM2E3NDVkZjhkMWU5ODE3NWNjYyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}