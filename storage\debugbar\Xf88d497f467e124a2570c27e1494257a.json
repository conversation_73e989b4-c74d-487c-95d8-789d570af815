{"__meta": {"id": "Xf88d497f467e124a2570c27e1494257a", "datetime": "2025-06-28 15:08:22", "utime": **********.62951, "method": "GET", "uri": "/pos/1456/thermal/print", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 167, "messages": [{"message": "[15:08:22] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58108, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58133, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58141, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.581483, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.581548, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.581621, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.581717, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.581815, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.581884, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58195, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.582015, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58208, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.582141, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.582215, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.582308, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.582404, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.582557, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.582684, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.582804, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.582919, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.583036, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.583143, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.583248, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58334, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.583483, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.583637, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.583762, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.583871, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58398, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.584076, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.584177, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.584269, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.584369, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.584468, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.584589, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 80.60000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.584703, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 84.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.584811, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 84.20000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58491, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 87.60000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.585019, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 90.20000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.585122, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 92.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.585237, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 95.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.585346, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 98.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.585464, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 98.60000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.585576, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 100.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.585683, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 102.20000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.585779, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 105.60000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58589, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 107.00000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.585998, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 109.20000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.586103, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 110.60000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.586207, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 114.00000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.586636, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 115.40000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.586816, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 118.80000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.586945, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 119.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.587058, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 122.40000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58718, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 123.8000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.587289, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 127.2000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.587396, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 129.8000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.587591, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 132.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.587748, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 133.4000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.587869, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 135.60000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.587987, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 138.20000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.588096, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 140.40000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58821, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 143.00000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58831, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 145.20000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.588413, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 145.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58854, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 150.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.588723, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 151.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.588859, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 153.60000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.589022, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 153.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.589144, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 158.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58926, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58937, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 164.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.58949, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 169.2 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.589608, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 169.39999999999998 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.589721, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 171.59999999999997 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.589832, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 172.99999999999997 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.589932, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 173.99999999999997 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.590037, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 172.99999999999997 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.590138, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 173.99999999999997 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.590258, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 172.99999999999997 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.590495, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.600779, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.601019, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.601188, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.601313, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.601452, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.601583, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.601701, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.601811, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.601917, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.602029, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.602136, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.602249, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.602361, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.602482, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.602601, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.602718, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.602839, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.603052, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.603218, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.603354, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.603481, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.603602, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.603714, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.603828, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.603949, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.604075, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.604189, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.604324, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.604459, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.604591, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.604723, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.604846, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.604961, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.605079, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.6052, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.605331, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.605447, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.605559, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.605673, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.605801, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.605922, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.606052, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.606166, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.60628, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.606405, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.60653, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.606647, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.606766, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.606877, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.606996, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.60711, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.607247, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.607373, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.607497, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.607629, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.60774, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.607855, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.607968, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.608084, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.608203, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.608335, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.608471, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.608586, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.608708, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.608827, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.608946, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.609059, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.609172, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.609285, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.609402, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.60952, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.609635, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.609743, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.609853, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.609965, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.610071, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.610178, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.610291, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.610393, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.610503, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.610623, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.610732, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.610851, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.610982, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.611102, "xdebug_link": null, "collector": "log"}, {"message": "[15:08:22] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.611219, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.015346, "end": **********.629753, "duration": 0.6144070625305176, "duration_str": "614ms", "measures": [{"label": "Booting", "start": **********.015346, "relative_start": 0, "end": **********.455288, "relative_end": **********.455288, "duration": 0.4399418830871582, "duration_str": "440ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.455299, "relative_start": 0.4399528503417969, "end": **********.629758, "relative_end": 4.76837158203125e-06, "duration": 0.17445898056030273, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52309928, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.thermal_print_clean", "param_count": null, "params": [], "start": **********.57565, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/thermal_print_clean.blade.phppos.thermal_print_clean", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fthermal_print_clean.blade.php&line=1", "ajax": false, "filename": "thermal_print_clean.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.thermal_print_clean"}]}, "route": {"uri": "GET pos/{id}/thermal/print", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@thermalPrint", "namespace": null, "prefix": "", "where": [], "as": "pos.thermal.print", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1667\" onclick=\"\">app/Http/Controllers/PosController.php:1667-1726</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.01004, "accumulated_duration_str": "10.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5070288, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 21.713}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.521916, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 21.713, "width_percent": 7.371}, {"sql": "select * from `pos` where `pos`.`id` = '1456' limit 1", "type": "query", "params": [], "bindings": ["1456"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.525964, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "kdmkjkqknb", "start_percent": 29.084, "width_percent": 5.179}, {"sql": "select * from `customers` where `customers`.`id` in (10)", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.532768, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "kdmkjkqknb", "start_percent": 34.263, "width_percent": 8.068}, {"sql": "select * from `warehouses` where `warehouses`.`id` in (8)", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5359359, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "kdmkjkqknb", "start_percent": 42.331, "width_percent": 10.956}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` in (1456)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5400538, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "kdmkjkqknb", "start_percent": 53.287, "width_percent": 28.386}, {"sql": "select * from `product_services` where `product_services`.`id` in (0)", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.545516, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "kdmkjkqknb", "start_percent": 81.673, "width_percent": 5.279}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1695}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.563129, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 86.952, "width_percent": 6.773}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 121}, {"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 21, "namespace": "view", "name": "pos.thermal_print_clean", "file": "C:\\laragon\\www\\erpq24\\resources\\views/pos/thermal_print_clean.blade.php", "line": 279}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.591402, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 93.725, "width_percent": 6.275}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\PosProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPosProduct.php&line=1", "ajax": false, "filename": "PosProduct.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1456/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos/1456/thermal/print", "status_code": "<pre class=sf-dump id=sf-dump-1765175896 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1765175896\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-208405582 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-208405582\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1077858595 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1077858595\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123286871%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ill3cVlIQ3RlRHNUdTN5UCtBOUgzNVE9PSIsInZhbHVlIjoicGsxQlFYOCtRck5Dd3ZpRUFmQ3VGdGRBdm8wNUFQWlZaNHJyaFJsb3ppOGNoMEI4WTRia1JDSWFGZnZKNktsWkN3b1QwWmQvVlVlMU1qY0VaTnR1WWpGZjI1RUhiNzRLNVpJck1wamliTlJKWVY3RVB6SW91RTg5TC9RQWVnMXEveFlleVQyN1NDaDVzQ2hlR0xhUUE0TGNxOHhHTHNkUWdsNmxmeFFqdExOd0FJZitZZnNOSE9FZGtPTUNMNnBrVGNKYjhlUnJWY1JUdm8vWlltMU16dHdydmlVYXRxQTA5WVFRVlF6Q2FuVjh5ejNNRkVUY2g5dXN1d28xS3U1ZURJdlRFT3huVUFPZjlMNmU4M0NxMW96c0VmVG9OcG1abWRyd3I4a3dQamtFUjVHQ2lPRHQvRjl5aVFtTldGQWFmQ1dFdlVNYytNUEF0WFhNdUhOZXNHdDZGWEVvZDI0Y1RhZ29xU2dFK2lxVlgvZm85a1FyaGlabFdzNk9KeTFXMjU3U2JuRUdXOXF6TkVWU2F5QlhQWVRwalpScHZlQUpkOGllZldtRE5DTVBhbzhLWU43TmhPVUhZS29QVXFDNGNWcXdVczRHSUNjNDhZQXREdmVSaGh2bXhveC83UzlzSjZoTWdiWitJVkM4U3BvNnJuMElrMGJqTVpFaXNndEYiLCJtYWMiOiIyNDZlNmYzMDc5ODAwMTJhOWJhYTRiNjJmMTY2ZmY0NGMyZTRkNmI5MWU2NmQyYWE3MWQxMTQyOGQwNjdhMWUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtzU1VUeUp6SGZwNnhtVm5MeFYzOXc9PSIsInZhbHVlIjoicUd1M3Arc25MUzI2d2orTVpwNk5FUUhFSXRXdysxdVhBZVJzTk5mbjc3Q1lTQjVvU1ZIM0pQN2FSZDZFaUVMWWJld3J1NlpsbGsrV3Z6amthdHgyK2NnOXRVYi95Wk1hdW1iNCt0VlBnYmxSYUFpbnc3MVVLOEZuN1U1ZHYxUmsvOG5hWFRma2tOQUpxa3VxV1dYM0dNQjVtMUJxVUlDRXkrZG9QK1pjYU00Yk1YdmRJSFFBRnNMSGdwRTRkSEtySVN4bnZZU2dHTkZ3VmMxeXFWaVdVSFBCZkxzd1E2U0c4VFQwcUFNMGtzSDA1S1I2OTJ0YXhNcWNSaXdkMWJiRmpUZHBtM3JsVlVJNUZNV0I5QjMySkZ2VlViV1RrSFUraDF6bUh2ODhJOHArMmIrY2ZJbGMzTklBYjRzNnFNSzBHeVNjYU80WDY5Y2hxKzB1TUtxdFBiOFNsVk5pR2E4VjBpSnNTMHpLaGdLZFh3V0lZWlpKcGR3REs2Ky9Qb0d0dDR2eFlYVWNTRmZCYVpqbXpSUkJyMGg0VlVwMk9ZMXZONmRZSXdBVEFTRVpvQ01aQm1pazRaTUlzVk9yVVgrMndpUW1jOGg5QWdZdVBXK3FYNnNYWkpqTWh4ZE5VRjdpR1lBdW9ET3VwNGVaaFkyV0JXRFdNZ1k0ZmJCd1BWZjgiLCJtYWMiOiI5OTAxMDUwOGM4M2YyNjE4NTQ5NmI3NGJhNzBjYzMwNjJlNWVkM2NiNzY5ODc2NzljNTAzZmI2Zjk3Y2M1YWQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1598306225 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1598306225\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1967865494 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imd0SGpMbzhpZlQ3ZGNOdVNsS204Z3c9PSIsInZhbHVlIjoiN3VVZ3hZQjJFZzJya2lCR0RhbTBWM1lIakhsNjlPcHdURXJNYWIyd1RsdkRZSjlrVzJRSlhpZjdITlh4a090V0ZHME1ESmMvUWlhWlAxUGFjUmFJZyt0U3g2WElEUHZwazEvQ0NIOHZXbWhkbG9ZRzBhMUsrQUUrMnJNYWxmcFFTR3FhWGVmcGZTNlRmZUZQcXQ1Q1BxRGg4a21sZXJzeHdUSFA1ZDVLellBNDZVQ2ltTGNzTEg1ZkZtOFlnQXhpdUFCKzU5bWRwbStnRmYyMG5FSWR3Y1c4UFY5MXlrZjlkSG03c21zNlk0TWI3M0E4OE5qQURJaHBwSExUQWoxL0k0OUIrL1pHNXFRZlhHY3JpQkxsMHgvc0ZEWEprdld0UTI1VHFPSkpVdjhKT1Q5UytDU1VZY2d5UTlqTjhRSHIzOEFGRHpMbnMzc0pEZ216STNSbHNQZEdxSGgwSkhxOHV5MzFPQXVhWlpsQzNqbUY2ajcyN0hzLzZuTHRoZ0t0a0hZdzhtTElMRHAzdDNlYTFNTFFueWZJajByQXBVVkRYTS9kL0R4UVFITXUwNjB0WWIvMFJSdjVsbUpkcGtJRGlHUU9wY05PUmFmZVZzRGkrU3MwMVY1MHFzODI3ZkQzUGJOSmhVcUR5YUJTQlhURm5jcTNNaXdDZFlkQ092b0YiLCJtYWMiOiI4ZmQ2N2U4MjNkZTI3ZTllMWFjY2VjNTZmMjYxNmVhMWY2M2EwYTE4ZWJiMzgwZTAyMDA2YWYwYzQxYjI3Yzc3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjkrSTBiMng4b1I2UDZDSGJWSjhYaXc9PSIsInZhbHVlIjoiU3hoOXlzMXQ3QUFHeHY4d1NvL3NtQ3lUUnoyTVJiVThkMExFdlJEdmRzY3JSNzhiTFFnODRyUWVnNjluODZCclVzbVlRZ25xQ2NYRlRLbWNYOU9qeDNrY24zS1BRVnU2dGZhZk9pWllKSk9pbnRHdGxIODQzYklpbndPMXZXS041WXdURWJLdHNQZ0ZkTUVWYytDK3pKeW9XaUtEaElDK0ZmdCs2ekpQVE5MRzNERGgxTEs4QUMvc1k2UWxCalgvNGFSL1FvMSt2eUIyVFk2UmV0OWxrenBSNHg3bFJHaGQ4NjdmZU9KUzNuZnJyMHUwZzhzWTlUSzdIbGJpUFQ1T0tvaGQzMDY3bW12c3FOR200Q3IwVktDYXl6cXc1SXpFaGlyaDRKZ0Q4UjdCN1BxcVNTSHdmMVpwMThHdjBVelVOOVdMbzFFL2tJSHJCcjJFVTJvWW9YYnIzMzU5cFJCcTJxT0dXL3diWmFDVXhlWmhoMHVzZGtRbzh3enlLUTRzYTh5U3BJYnJPOTZVWSs1ZXNrSTVlTTJqOWRZcGY0QlM4aVI3Rm5XWDJXTncxZG1UU1BmMnpHcXZlMmNhMDhtWVAzYU5aa3I0MEFTNG15Wlp0Um5TT2RwZ2dkcThMQWgvUnpESDIxakxRYS9UeGhPVkFsNGRiS05INHpmdk41YUsiLCJtYWMiOiJjYTc1MWM5NDZkZDkzZmM2Mzk0NDc4MzVkMGEzOTU2ZGYwYWNiN2MyNTRjMzhjM2YwN2YwNzA1YWU0ZDdmNzEyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imd0SGpMbzhpZlQ3ZGNOdVNsS204Z3c9PSIsInZhbHVlIjoiN3VVZ3hZQjJFZzJya2lCR0RhbTBWM1lIakhsNjlPcHdURXJNYWIyd1RsdkRZSjlrVzJRSlhpZjdITlh4a090V0ZHME1ESmMvUWlhWlAxUGFjUmFJZyt0U3g2WElEUHZwazEvQ0NIOHZXbWhkbG9ZRzBhMUsrQUUrMnJNYWxmcFFTR3FhWGVmcGZTNlRmZUZQcXQ1Q1BxRGg4a21sZXJzeHdUSFA1ZDVLellBNDZVQ2ltTGNzTEg1ZkZtOFlnQXhpdUFCKzU5bWRwbStnRmYyMG5FSWR3Y1c4UFY5MXlrZjlkSG03c21zNlk0TWI3M0E4OE5qQURJaHBwSExUQWoxL0k0OUIrL1pHNXFRZlhHY3JpQkxsMHgvc0ZEWEprdld0UTI1VHFPSkpVdjhKT1Q5UytDU1VZY2d5UTlqTjhRSHIzOEFGRHpMbnMzc0pEZ216STNSbHNQZEdxSGgwSkhxOHV5MzFPQXVhWlpsQzNqbUY2ajcyN0hzLzZuTHRoZ0t0a0hZdzhtTElMRHAzdDNlYTFNTFFueWZJajByQXBVVkRYTS9kL0R4UVFITXUwNjB0WWIvMFJSdjVsbUpkcGtJRGlHUU9wY05PUmFmZVZzRGkrU3MwMVY1MHFzODI3ZkQzUGJOSmhVcUR5YUJTQlhURm5jcTNNaXdDZFlkQ092b0YiLCJtYWMiOiI4ZmQ2N2U4MjNkZTI3ZTllMWFjY2VjNTZmMjYxNmVhMWY2M2EwYTE4ZWJiMzgwZTAyMDA2YWYwYzQxYjI3Yzc3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjkrSTBiMng4b1I2UDZDSGJWSjhYaXc9PSIsInZhbHVlIjoiU3hoOXlzMXQ3QUFHeHY4d1NvL3NtQ3lUUnoyTVJiVThkMExFdlJEdmRzY3JSNzhiTFFnODRyUWVnNjluODZCclVzbVlRZ25xQ2NYRlRLbWNYOU9qeDNrY24zS1BRVnU2dGZhZk9pWllKSk9pbnRHdGxIODQzYklpbndPMXZXS041WXdURWJLdHNQZ0ZkTUVWYytDK3pKeW9XaUtEaElDK0ZmdCs2ekpQVE5MRzNERGgxTEs4QUMvc1k2UWxCalgvNGFSL1FvMSt2eUIyVFk2UmV0OWxrenBSNHg3bFJHaGQ4NjdmZU9KUzNuZnJyMHUwZzhzWTlUSzdIbGJpUFQ1T0tvaGQzMDY3bW12c3FOR200Q3IwVktDYXl6cXc1SXpFaGlyaDRKZ0Q4UjdCN1BxcVNTSHdmMVpwMThHdjBVelVOOVdMbzFFL2tJSHJCcjJFVTJvWW9YYnIzMzU5cFJCcTJxT0dXL3diWmFDVXhlWmhoMHVzZGtRbzh3enlLUTRzYTh5U3BJYnJPOTZVWSs1ZXNrSTVlTTJqOWRZcGY0QlM4aVI3Rm5XWDJXTncxZG1UU1BmMnpHcXZlMmNhMDhtWVAzYU5aa3I0MEFTNG15Wlp0Um5TT2RwZ2dkcThMQWgvUnpESDIxakxRYS9UeGhPVkFsNGRiS05INHpmdk41YUsiLCJtYWMiOiJjYTc1MWM5NDZkZDkzZmM2Mzk0NDc4MzVkMGEzOTU2ZGYwYWNiN2MyNTRjMzhjM2YwN2YwNzA1YWU0ZDdmNzEyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967865494\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-958453200 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1456/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958453200\", {\"maxDepth\":0})</script>\n"}}