{"__meta": {"id": "X7f07188a828bfe8cd8e0d2fae00a14dc", "datetime": "2025-06-28 11:24:55", "utime": **********.663983, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.269229, "end": **********.663996, "duration": 0.39476704597473145, "duration_str": "395ms", "measures": [{"label": "Booting", "start": **********.269229, "relative_start": 0, "end": **********.598286, "relative_end": **********.598286, "duration": 0.329056978225708, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.598294, "relative_start": 0.32906508445739746, "end": **********.663998, "relative_end": 1.9073486328125e-06, "duration": 0.0657038688659668, "duration_str": "65.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46266912, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01985, "accumulated_duration_str": "19.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.630897, "duration": 0.01952, "duration_str": "19.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.338}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.65785, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 98.338, "width_percent": 1.662}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-979556470 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-979556470\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1505587649 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1505587649\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1475394413 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1475394413\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6Ik9PVWZBUmlIT3BHOG9wVWhoaS9Nc0E9PSIsInZhbHVlIjoiaXBWTkpyR1ZGY3ZOVWU4K1owdmcySW1sQkpEZWpjL1N4MjU5MlZLcnZEVmtGZFdZYWZjd3dFQVBuazVqb3hyQVdpZWZzcU0vT29CYnhVYXVFNzRGdmlFb2lXd2FaQmNSa0pvWDNvemNMK1k1V2VrSHVjM2IwNmtOb25hTE00aS9sY0dPdEJvcDlSZVRtbDhoSHIyMDFXTHdWNEFvc3pxWXM3bXB1QW10ckowMGFuYTY0SEJaaC96ZEtMMVZOSEFQMG0xRExOeHc4MXNINzJGM3drQ01YSUNta05OYlgwa0xaUG9CdG8rVEZxSWlNbUxVeFlNcklJSEVQbC81SHN4MHRvU3RrSjArZnU4UStUSStvbmNhemZTbS82eHZpMGNXYmh6N3hPdWRzYjhaeXZLTW5NYjhNVzQvaitoNGVENngydmNFdTVpOFdoR0U1bnJnaENHSjdKd3h3QVNnM3hPemREclREZ2x5Z21tS0VDM2M1Y1R3SkY5T3dUNmwzUFM1aGNrQjJZTzJrdTlvMDdob3VLK1hlbXFEWGJ3N3g0aXlYUllwWDZmb2dKOWNjRGM4aWNsS2pzdDBKdXIrUGRILzhwbUJaaHM4WE9HUHZMcEVrODgweHZZbzROOVkvaXo0T1hWcXBIeWdqMlp1NUlSYzdibi9ZeHY3eUp3K3oyV1UiLCJtYWMiOiI1NzQ4Mjg2YzRkYjE2NzZiYzY2MTNhYmNkZGRmNmY5ZDUwZTExMGZjNTg3ZjY0N2M0NGYxNjk3ZjY1NWI2ZDNiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklJaC9WNjFDT2tzM00yZzU5aVZ2NkE9PSIsInZhbHVlIjoiRGdqZUNjMEVKNk5RUklzdE00cCtiWTUwbzAxMFdRMTJrVi9tZWJ5WE01bzZTbHVVcEdxenY5YUtZY0pTSFhtclhvSllCMkVhSnFqWm0zS243T29jcThkL1ZCK3UrNWNGazNxdHAycld2YXlTSkRkNFV2WXJsOE1rdHFEd1VTMXkyNWh5MWlaL3hFdlNnUW51cHppU2RlRDh2ZHl5YnJpVHZzWlZaL1RmdVlqTFEyV01NMHE5U3ZpSWpXOEpoRUNDQjJLNmNSRkNNTmNHVFV4TUtMeXFYbHEvcUlCZ2NSTUdraVdUeXdGZXJmZEtPNGo4dGw4S3hUSVlYTk0zRFhnd2VmdkNMVVZPTUNiSTlzSTVwRm1uci9xZVRMS2ZRZUQ3MEUzQnRtbWlnN3NQNEtJZEVkeW5vOEY0YW5yUnpiK1lCOW1wN0hWeitnSjJrVTJONndGTGlVQk5JMElqVThXNkhSU0l4dXJsUnBDYkVoZEJjNHBpNHo3Wi9za0ZkSDNla0s1TU9uZXVFY0kzd01RaTJNTXJ0eUJHWk1DQ2NBS3JDUmkyTlUyYzR0TDl6Yks4S1gybWxWL0R1c2FIMU1XeTY5MzNhQjRyV2JmM2RKdnh5RkNYMzViVHdDZHVFZnBGbHlQTnRKOVVqeGhBcmh0ZVl1UXVULzFTYXpHbGdzZHQiLCJtYWMiOiJjZGIwMzRiMGI1ODMyNjdkZjMxNTczMWFjOTJmNDVmMTA2MmQ4NjAzYjBhNmQ1NjM5YWMzODJkMzMzMDA5YjhhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-36631978 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-36631978\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1588339914 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:24:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJveEtKVTBCQXluV0wwMkoyeHVNSmc9PSIsInZhbHVlIjoiVkhycUFVTWtlU2VOeE9OUUxzVzBtZHZKbTZhWWFHWVlWYUV3ZmhQby9oRGwzTTh0OWhCZHRxcjJ1c0htN1BtZlVEa2Y1YXlSenFpS3lva0I4UHNjTHQvSm1BcjNSYkx0TnBVbHlkZlRxVHZONVdlK0I1U0tYaEZqVlVJd2JFN1ozZW5hdzVJQkxEK0FnSU5zRy83QmZRSC96SFdOMTN2Rm1MVllpSS9kcVFpclMyY0txTVo0UWxWUkJIUDJJNkkzdy9taGVqYWY4c1o4a0FGSDh4cWtNZmtDSjZIUWIvU3RyQmhLYW9qYWFiNS9iREVhang2ZE90T1B4d0VmZnBhMU1aeEs3ZEFCelIvNlVnZ1ptVzBORXhFN3paa2hpRHdCS0drdjh3QWhBTDlyWW5mUEJNU3hWZkR0QlJBQ2N5MXZoVEJWWWR6OHJNNHNPOEhuTW5JNkphcjVNUVBNSkFNNE1ETG5Zb0ZLWTBHVkIwOEptOG1HV3N2dDdXM2xQQW4zUDNQOWdoNC9sRHFLOTBpNTBwdTF5YzVvM2pTRjFxYWZHU3MrTWF3dnlvRTQ0alJvQXh2bC9TdWdJSVpObTZOYm1IZ2UvOStzZTYvZ0pTNnJjL05BbWRPR1lQNHlqU1M5ckJZU0g4VHZyY1VVb3Rwbys0dWF3TUV5SHBQWGwvcGQiLCJtYWMiOiI3MDU1MjhhYjA4NTU3NmY2NjAyYmFhMjMzMTY4NmRlYTZiYzkyZjEyYzAwYjU4NzIwYWJjNWZmNmZhYTE4Yzc2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IngxUVdpcnBnVTV6NXYzb2dEb0dGUUE9PSIsInZhbHVlIjoiSGF5SFI1c09zWGRqRWdRdnFGZS9GbUE5Q1NkdVhOTTIwLzZOOUpCajZEc3pkSnNJZ0ZZckc5ais5NVRJb3RXcEVpYmNiWUlvZC9VNGhrWHladzhHbVZ0eWxmeXlVOWhadU5GT1dWR0FMTEh1NVpPNWg1MHh4R2o3azExdTNycnozWC9vdW0rbGhmOU10L3FVZ2swZmV4RXpDaEQ5cnUwbFlzVXJ3ZkVTRGtCZGVIbjJGWmFONWxTdU5rZVZScmF5YVdsdzhsTURyL0Z2SStDV0RwZkp3NGUyUG9CbW1TVkQ5bUg5NEREWVl1RWZ3RS9XUTdUZk9ZNTNtNXhKdklNUE16QUU2b2JTcnI5ajU5MG45dUlsaWo3MWo5dDVJVTdCdlRUVFk3cUJwbG54QUpEWHZ3aDNQUVJxK3hiRUsxdnlvYW9sNHBRNGREVFN4N08zZlFLL0w2Y0grbE9nelVCZ21Gb3VpWmErdHVwTmFEa09sZzc5N2NWZTRwMFBPNWM1NFBiTVYzVTB4WE1wTXJ5S2wxRE10YXN4b21mU05pZ3lxY0J5UzZOK1VzSjR1YUxuN1QvRlByZDVLK3ZmcVdxeHEyZmdETE5CaDhUN3pkMW56SUpuM1R0WkpNckhNZzdxZTcvSWl0NDZMdUsvSVFQWjhBblRzYzNnaDFrVkl4cDIiLCJtYWMiOiJjNzU3MjUwM2FlNGU1Yzk5MmM3MTIxYzZlZmRlZjM5Yzk2YzFmYmM5NGY0NzQwOWFjNzI1ZTZmNGI3NDdiY2FhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJveEtKVTBCQXluV0wwMkoyeHVNSmc9PSIsInZhbHVlIjoiVkhycUFVTWtlU2VOeE9OUUxzVzBtZHZKbTZhWWFHWVlWYUV3ZmhQby9oRGwzTTh0OWhCZHRxcjJ1c0htN1BtZlVEa2Y1YXlSenFpS3lva0I4UHNjTHQvSm1BcjNSYkx0TnBVbHlkZlRxVHZONVdlK0I1U0tYaEZqVlVJd2JFN1ozZW5hdzVJQkxEK0FnSU5zRy83QmZRSC96SFdOMTN2Rm1MVllpSS9kcVFpclMyY0txTVo0UWxWUkJIUDJJNkkzdy9taGVqYWY4c1o4a0FGSDh4cWtNZmtDSjZIUWIvU3RyQmhLYW9qYWFiNS9iREVhang2ZE90T1B4d0VmZnBhMU1aeEs3ZEFCelIvNlVnZ1ptVzBORXhFN3paa2hpRHdCS0drdjh3QWhBTDlyWW5mUEJNU3hWZkR0QlJBQ2N5MXZoVEJWWWR6OHJNNHNPOEhuTW5JNkphcjVNUVBNSkFNNE1ETG5Zb0ZLWTBHVkIwOEptOG1HV3N2dDdXM2xQQW4zUDNQOWdoNC9sRHFLOTBpNTBwdTF5YzVvM2pTRjFxYWZHU3MrTWF3dnlvRTQ0alJvQXh2bC9TdWdJSVpObTZOYm1IZ2UvOStzZTYvZ0pTNnJjL05BbWRPR1lQNHlqU1M5ckJZU0g4VHZyY1VVb3Rwbys0dWF3TUV5SHBQWGwvcGQiLCJtYWMiOiI3MDU1MjhhYjA4NTU3NmY2NjAyYmFhMjMzMTY4NmRlYTZiYzkyZjEyYzAwYjU4NzIwYWJjNWZmNmZhYTE4Yzc2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IngxUVdpcnBnVTV6NXYzb2dEb0dGUUE9PSIsInZhbHVlIjoiSGF5SFI1c09zWGRqRWdRdnFGZS9GbUE5Q1NkdVhOTTIwLzZOOUpCajZEc3pkSnNJZ0ZZckc5ais5NVRJb3RXcEVpYmNiWUlvZC9VNGhrWHladzhHbVZ0eWxmeXlVOWhadU5GT1dWR0FMTEh1NVpPNWg1MHh4R2o3azExdTNycnozWC9vdW0rbGhmOU10L3FVZ2swZmV4RXpDaEQ5cnUwbFlzVXJ3ZkVTRGtCZGVIbjJGWmFONWxTdU5rZVZScmF5YVdsdzhsTURyL0Z2SStDV0RwZkp3NGUyUG9CbW1TVkQ5bUg5NEREWVl1RWZ3RS9XUTdUZk9ZNTNtNXhKdklNUE16QUU2b2JTcnI5ajU5MG45dUlsaWo3MWo5dDVJVTdCdlRUVFk3cUJwbG54QUpEWHZ3aDNQUVJxK3hiRUsxdnlvYW9sNHBRNGREVFN4N08zZlFLL0w2Y0grbE9nelVCZ21Gb3VpWmErdHVwTmFEa09sZzc5N2NWZTRwMFBPNWM1NFBiTVYzVTB4WE1wTXJ5S2wxRE10YXN4b21mU05pZ3lxY0J5UzZOK1VzSjR1YUxuN1QvRlByZDVLK3ZmcVdxeHEyZmdETE5CaDhUN3pkMW56SUpuM1R0WkpNckhNZzdxZTcvSWl0NDZMdUsvSVFQWjhBblRzYzNnaDFrVkl4cDIiLCJtYWMiOiJjNzU3MjUwM2FlNGU1Yzk5MmM3MTIxYzZlZmRlZjM5Yzk2YzFmYmM5NGY0NzQwOWFjNzI1ZTZmNGI3NDdiY2FhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1588339914\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-765651585 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765651585\", {\"maxDepth\":0})</script>\n"}}