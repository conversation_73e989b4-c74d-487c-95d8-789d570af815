{"__meta": {"id": "X45caf7d04ff95cd6edcb76f62942fb09", "datetime": "2025-06-28 16:34:57", "utime": **********.464231, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128496.977518, "end": **********.46425, "duration": 0.48673200607299805, "duration_str": "487ms", "measures": [{"label": "Booting", "start": 1751128496.977518, "relative_start": 0, "end": **********.388791, "relative_end": **********.388791, "duration": 0.4112730026245117, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.388802, "relative_start": 0.4112839698791504, "end": **********.464252, "relative_end": 1.9073486328125e-06, "duration": 0.07544994354248047, "duration_str": "75.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46462864, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2553\" onclick=\"\">app/Http/Controllers/PosController.php:2553-2587</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.021759999999999998, "accumulated_duration_str": "21.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.424943, "duration": 0.02122, "duration_str": "21.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.518}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.456076, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.518, "width_percent": 2.482}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1345850708 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1345850708\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1410432507 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1410432507\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1045492482 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1045492482\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1623900657 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128494675%7C53%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVHNVpUV3N2bHNiNFVNbk43d0hmY3c9PSIsInZhbHVlIjoieGpXZDN1dTMzcU9VK1pBU1U0N3pMeTRURTVoYkVQUU5IMnhRc0lWWGZYdG5lVWhqa0o4MzZHV2daSWZtODk0S0txMllEeHV0cEREN1E0SlJVUFYzczUrREVjTWVtbWpzSUZFclIxeGVaN0RFQjd0VE81MFUzOUcrUUUwUWEvWFVPSUNYRFpVNFpvcU14eGpQcnJYTTVjUVdnaGxuT25JS2RzY1FMWEVTUUVVQ0hiQ1NYUlI1S2V5WVNVZWk5WDd2dTR4RGpWWmFZVGh1M1pLZktXNUdLSWVTZ05PT0xSL3I5MnRLY2NyRXFKbzJEZXFKVFZOU25adzlaK0E5US81T0tqcVZGWVRya1ZPVk9IZGc0cUtqZmVJZjJQS1VHcisxS05oRy9NOTl6RWpMbjMzSUhZSGZjSmNaK1o1N1hwRWZFN1g5QitKT0Y0NHpPSGpCaGxDQi9nRnpsK0Q1cWd1M05KQU94NlU2Y2Fud1kraUI2Nkl0MHFwUTNqVEtpa3BHT1JZUFRQMlRKczdSL29mdDMxcGtlcHdmSnBjQ0lnMjNuRXVZakZaUDVRVVpsR0Nza2psL1IwUGNnUVZSOVNPU2NQRDdKRmMyQ0JnYU5RYmt4VCttUEkrSjBiNFRvaC9vQ3Vac0FJSGE0NjlPeFJkTFl2Q04wS0RQTW8xb1YyR0giLCJtYWMiOiIwYjk2NjA3YmZlZGQxNTZiNjYxMDUxYmQ5MDg4ZDJjNDI4MmVlODQyNDAwMzFkZWRmNzA3YTk3ZmVkNDgyNDFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFNNTRScFIwSktaQTlsTGp2YWtoNGc9PSIsInZhbHVlIjoid1Z5ZnlhOE8zdlV2cjBkOUFGR3Q5TFQ4bmY3LzVQa0x6YVRlbEQ1aURoR1NTbjJXR1ZRMnMzZ0VoZ05EU1MzK0tkZlhtY0JPeFBReUY5YmIyeENXa1JOblVIY0NwNk9BTWIyQktJcFdxazI5dDFBc25TZFRqbXVQQnA5anJOMnM4YlBhbEFMLzBVRHJPaitKVTVKMzhqUTFQQkhaZlRDc2o5c0poRHJra2s2RitRZHJ4ZDdKeDVOZDBmQi9PcnlhTWtYSVV0cWltd25tUlFwZTBTWTVKNHZhVklraE9nYmxQa2RiNHM1dFVVWDI3V0NwNFpsYjhkcjVOcGFScXp5NTBKdEJEbkNtNXU1N0RDb0RBaFdFWDhqTHFJanNmdUoyT0c1Tk1xeFBkYXhDSHYzQno0YkJhdHJFMU5zRytJWFJZM1pUam83UXhPOExIMjJadWFyeXQxamV1MXBlRlVhSmxCa0ZQOE8xZkpnM1dvSXpsNzVvUVFtVnZWWXlFYTNPb1d3bzVTZmMrcjBpMlRvb21GbEhVcVVlUS9xRHlEdlh5Sm1tWnVXL2Y0cS8rR3BpbyszVWRFd3k0dmZCY0Fnb2xPeXd3cmllZGhBOWdBdnRYdW5NZE9KRTIycU51N2xROWtMZUo3S2ljcmR0TDk0VVlmMTdaTGUzSnRrdDdKVlciLCJtYWMiOiJhMGE0NWFmMTcwNTBiZmM1OTZkYjMxZTUwZDJiYjdmZjAwYzZmZDg3MDAwYjRlNmEyMGExM2JlM2FkZjM1N2M1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623900657\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-979306980 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-979306980\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1296278177 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImErc2JFOU9MbGZEZGxVL0ZXaHN3THc9PSIsInZhbHVlIjoiZ013bjkyd0VRa0lZRmw4L1VJYkpBZGJZM1MxR0M2RG94aFBzZEhJTkdZQjBzYXpCNFJkdzh4a3hHb2dpYW9VeXdMQ3BNV1dNekcyNUVkZ2VFTHIzR21BTGtiSEVDeUpXOVBSaDRmSVBxdjQwZ0VpU0xKYjl6TExsV1VyUHh3ZWxLY3ZzNjZLL08xTDRMZDVHMUJobENPQlFhZ2s2aDh4ZUlmMnVVaERKQlFNMk9UNitJTC9lM2NNYW43STlvZTAxTjh4M0RGRWR2cFFrc2lqNkt6U0FUZzNQTVQxbklHMWNQNHZyMURXQnp2WE9vbW5aeEFHMFkrdkdPUGpNdHljS0NVM0YxVGh4TmdUWWg3cndoK0pYUlc3RFUvRDhDMmhJMDhORFlHdldQQWU5bnRpeGZnT2hudllsTmc3TGhLV0FQdTlCVk8vTE5xb3FNVHNCSWdFdHVodllBL0Ywb1IwbXBOOFZNekpmbDFvWDFlY0J1VjlGR1dLSzhudDIzb043eGo4eUxJc3FkOGlNcExMRmdETWJIS3R6aWFsY3VZNGloQTAycTJkamJjZndXOGVyV1cwTHZ6UHhGRTVHSjdPVjl6VXg3NHZWSkJ4SjRpVy8xVTRNUndFSFlaaU8xQjRVTmVNY3FTVU5rOXJVZUxlajIyUGIycUllc0ZzRWp4V0YiLCJtYWMiOiJhOWE3MDc1OWFlYWZjZjIyODJkNWNkNGEwOTZiNTUzYWNjZjNhMDI3MzMzNjdiZGEwMDI3NGZjYWU4MDliZjRhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNqdk9kdENGbWRUK1NjZCtQTjdJK2c9PSIsInZhbHVlIjoiWDJQWUNBNnA3TUhPQVpLem5JN3FkRWQwVW43WWNJRTVVRFp2WnpKK1ZRUEFSbTB0ZFZWcFc3b1ZIRFYyZENwT2JXYlBaUitiMkNYUEg2NWpISHJSNmc4YWY4TzB0eTFUcDRJMUp2S1ZjdFNnd3Q2a1ZnbkZGYkZnNHBwWEVILzhCWlBNL2p2ZGVFc2VJOEIzQUxQZjQ1UVVIVDRjVDNMOEVONnI5NDFWTEFVcnhUUG9yWXBHMjVOa2pBQlZieVphckFlQ1ZvKy9UajVnS080LzRWZk9qNmQxZld3TnJ1ODlWeDl4bFVNSU1iVW9pM09YRGVJa05ZZW53SFlHZUNiek9ta2U1VVpzdk0xSldMYmcxNGNmTHBJaW82aUg1R24xbXIvK1FqSzBtZDFUbCtYMVExS1NnVXlHT2lOb2kyRUNSaVVnSVBWREZxYVMrQzNUSUdKM2NVNk1FTDZiTkZwMVV2bkp5YURCSld4czk5Y01LSmtwQmh3U0VTQ2UySHFLeFdpb0l1cUxaWTRsNkhSOFhXOXhWU2Y0OGIvWDdCUWs5NTlsOWMyZFpqQmpjZGRWMTg1VzdMZ2t4U2NhRGtGUE5FdjJKU00vWWNwQTVzcWZ5Y1lqcEgrUEcxL1JoMXVKNUJXRWtVUTg2aW5wUmh1RzFYWU9qNENwajk4VUg1M2ciLCJtYWMiOiJiYzczOTMxZjA0Mzg0MDZhNTI0N2NmNjI1NzBmMzk4YjliMTdjZmQ5MGVjMmY2OGNhNzVhMzQzZGI2MzY2N2ZmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImErc2JFOU9MbGZEZGxVL0ZXaHN3THc9PSIsInZhbHVlIjoiZ013bjkyd0VRa0lZRmw4L1VJYkpBZGJZM1MxR0M2RG94aFBzZEhJTkdZQjBzYXpCNFJkdzh4a3hHb2dpYW9VeXdMQ3BNV1dNekcyNUVkZ2VFTHIzR21BTGtiSEVDeUpXOVBSaDRmSVBxdjQwZ0VpU0xKYjl6TExsV1VyUHh3ZWxLY3ZzNjZLL08xTDRMZDVHMUJobENPQlFhZ2s2aDh4ZUlmMnVVaERKQlFNMk9UNitJTC9lM2NNYW43STlvZTAxTjh4M0RGRWR2cFFrc2lqNkt6U0FUZzNQTVQxbklHMWNQNHZyMURXQnp2WE9vbW5aeEFHMFkrdkdPUGpNdHljS0NVM0YxVGh4TmdUWWg3cndoK0pYUlc3RFUvRDhDMmhJMDhORFlHdldQQWU5bnRpeGZnT2hudllsTmc3TGhLV0FQdTlCVk8vTE5xb3FNVHNCSWdFdHVodllBL0Ywb1IwbXBOOFZNekpmbDFvWDFlY0J1VjlGR1dLSzhudDIzb043eGo4eUxJc3FkOGlNcExMRmdETWJIS3R6aWFsY3VZNGloQTAycTJkamJjZndXOGVyV1cwTHZ6UHhGRTVHSjdPVjl6VXg3NHZWSkJ4SjRpVy8xVTRNUndFSFlaaU8xQjRVTmVNY3FTVU5rOXJVZUxlajIyUGIycUllc0ZzRWp4V0YiLCJtYWMiOiJhOWE3MDc1OWFlYWZjZjIyODJkNWNkNGEwOTZiNTUzYWNjZjNhMDI3MzMzNjdiZGEwMDI3NGZjYWU4MDliZjRhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNqdk9kdENGbWRUK1NjZCtQTjdJK2c9PSIsInZhbHVlIjoiWDJQWUNBNnA3TUhPQVpLem5JN3FkRWQwVW43WWNJRTVVRFp2WnpKK1ZRUEFSbTB0ZFZWcFc3b1ZIRFYyZENwT2JXYlBaUitiMkNYUEg2NWpISHJSNmc4YWY4TzB0eTFUcDRJMUp2S1ZjdFNnd3Q2a1ZnbkZGYkZnNHBwWEVILzhCWlBNL2p2ZGVFc2VJOEIzQUxQZjQ1UVVIVDRjVDNMOEVONnI5NDFWTEFVcnhUUG9yWXBHMjVOa2pBQlZieVphckFlQ1ZvKy9UajVnS080LzRWZk9qNmQxZld3TnJ1ODlWeDl4bFVNSU1iVW9pM09YRGVJa05ZZW53SFlHZUNiek9ta2U1VVpzdk0xSldMYmcxNGNmTHBJaW82aUg1R24xbXIvK1FqSzBtZDFUbCtYMVExS1NnVXlHT2lOb2kyRUNSaVVnSVBWREZxYVMrQzNUSUdKM2NVNk1FTDZiTkZwMVV2bkp5YURCSld4czk5Y01LSmtwQmh3U0VTQ2UySHFLeFdpb0l1cUxaWTRsNkhSOFhXOXhWU2Y0OGIvWDdCUWs5NTlsOWMyZFpqQmpjZGRWMTg1VzdMZ2t4U2NhRGtGUE5FdjJKU00vWWNwQTVzcWZ5Y1lqcEgrUEcxL1JoMXVKNUJXRWtVUTg2aW5wUmh1RzFYWU9qNENwajk4VUg1M2ciLCJtYWMiOiJiYzczOTMxZjA0Mzg0MDZhNTI0N2NmNjI1NzBmMzk4YjliMTdjZmQ5MGVjMmY2OGNhNzVhMzQzZGI2MzY2N2ZmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296278177\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}