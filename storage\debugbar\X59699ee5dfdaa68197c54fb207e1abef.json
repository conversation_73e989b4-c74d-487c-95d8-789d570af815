{"__meta": {"id": "X59699ee5dfdaa68197c54fb207e1abef", "datetime": "2025-06-28 16:10:20", "utime": **********.859148, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.39729, "end": **********.859164, "duration": 0.46187400817871094, "duration_str": "462ms", "measures": [{"label": "Booting", "start": **********.39729, "relative_start": 0, "end": **********.782117, "relative_end": **********.782117, "duration": 0.3848268985748291, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.782128, "relative_start": 0.3848381042480469, "end": **********.859166, "relative_end": 1.9073486328125e-06, "duration": 0.07703781127929688, "duration_str": "77.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45546304, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022470000000000004, "accumulated_duration_str": "22.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.81858, "duration": 0.02143, "duration_str": "21.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.372}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.84911, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.372, "width_percent": 2.492}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.851913, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.864, "width_percent": 2.136}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1081896898 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126882941%7C30%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFmN2VXUm5jZUVIRGd3cUhmM0JpVGc9PSIsInZhbHVlIjoiQUxaSWR6cGQrenB5ZEhkc2ZWU2JROEVXbVdTZmJHakErNUJJSUgydmMrYUNrRko4VVVwQVpkQ0s2bXpvTkk1NmFQV3U1aFFiclI3QkluQjZ0VnJHWkJaaHZGeG5GdTRvcktVeDdwM3dHZ3JsbEUyM1pOVG5SWWlwS1RzQ1Z6eTVla3pCd1FrRXpQUVJiU0NQVnk1YVVNS0daRXY5M0p0RW5zRHM1UnluWjgwNzVvenc0U1dqZk5CRkZ1NnpBcWw3SXd4M0lhM0NrNTNGbWw0TjM2SHI1bjU2WEYzdXhMWGlXTTB5K3BuWXdMVWpYQTJ4Umc5N2ttcDFRN3VSdVJUYmlaNmN3RTZ5a1F6RkVqMjY5RHJ2TkF2OFNHMkdjSUt3T25vVTd4RzF5cXIzSlpVUHVTQUlERDlWbW5xb1Q3Ky9QZUxra0hzR3hzSXhSZVQyeGJGYVM4VHd5dTdoWDVyR09IVnpXWkdRdEQ3QW1TV2p6ZTdMZ2ZiMmZFdFVjSTBxd0FESGdKc3NvaWZZdnBnQktnUXkwelM2ZFBWY1d3OVNTVDVuZDdKbFJSUGpNQjdDaERZL2ExSU5iaFBXd2pXeFM2Tko5MUhQM3FKNHZVWnNveisrVllzME0yc29iNHY5cGRNMlBOcVcxdVk4NGtiaE9MS0pxQlJiL0JzUHlqSU8iLCJtYWMiOiI2NTgwNmE2ZDg3MjI5MTRjNzY1OWY2NWFiNGY5Zjg4ZDUzNTMwZDhiYWU3ZDVmYjJiZWU5MWMyNGM2NmY0NjhmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJlNEl6ZEUvaDcwVThnUkJqQUY2UHc9PSIsInZhbHVlIjoiNmxXT1cxWXlmN1ZkQ0V6QTdpSWppb0g1OVkyQUtuQ0NFZ0ZRa1pxSVh6VU1WNDVNcGZHRE0wWnRPcTIwTDFUUHhwN0xHMjdCSnFYNHhCMko3Y3ZzMjdYNVZFdGJDRk4vMERYdDRvVFkrV2dzMThzTGI1MnYvT0VTRjRkaTFCOThjQ1hlUGRIcWxaZGZ0VVhscDVSNjNjV1NjdG1FMnk5SzlDbEFKS3RWbDVzOFM5MitMMWhnNFBmMzQxdHQ3MjBYVEtEZ1l3cC9ObHpqS211UmZrc3E5TFlURzhWRnd5djdLYkdPTVhiWE02aGRBQ0ZKdXUxVkUzMHJjeXlSdnVGSVU0QjNlSGg0Nlo0QkV0WnN6ZWZmaHhwVlZWc1cwVWVLNldoMVFQVnkxN1gzRjREQTYwZjBMNjVvT0Mya3B4VHZqdkJxQnJQeXdiL3B4RFBIMFlwYkdKaTNGWWI5YWZKWUgyMlc5QU1NaHBubVA4OW11cE0vWDBValoxZmtuS2FPQ3Q5QjBiTjVYRit2eTNxM2dzY2xQT2d5cDZMamtweDc5bXpNNVBzbVlGblBncVlxTHRJZkxtdjUzNHFBMXJ6VVpTZWUrYmVyUGl3em1TNjZtY1dyWW5GekNwZjdTSkhGa01KM2JCNVFmcFVMdXdPOWJ6eGhVSjI0Y0NBNEpjSm4iLCJtYWMiOiI4ZjcwZDAyZTc5YzgyY2RjYTk2ZGJlZGE4NzIxMjMwYTc2MTdhMzAyN2YxNmNhNjc3OGVjZWM1M2UyZGMwNzQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081896898\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1138366319 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138366319\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2047147240 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:10:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFZTDhMT2lRTTY1ZFE4QmFvMUcrbkE9PSIsInZhbHVlIjoibS8xU09MbnFCVEIwUVBMLzB6NWZVZEl1RGhRcG00cFB4b3RQRnUyQVhSekNBY2VUSWpTWnNhSzNQcUdUQlhOc1ZXUy9BZnIxTHdJS1hZQWlkQzBIdUZ3Y1BBSEhvVXNYSHh3c0I4dXBQT0RRMW5hQVhOT3BlbG9lUVNFdDh6NVhST042bHRjV25FbytTcWIxMXFQcWtIaGJaQ0FmdldDaitKYkw1WDVCcHFzY0JCbWIyT1dVdExlRzQzcnhJdTJhbE1Ob0ZtSHJuRGxOZlVLQVhJVHVlenAyZ29SQTNFNURvTXUxcXFlTFFkUlBRTC9PVnB3NlVEWU00SXFBckZQRmhJL1BDVlNWcEd5QnV1ZWFaaG00TWs2Y3ptTm5ZOUNvWkZmenNJNHJpUGxBc3VTaEpZa3hIa2ZGSm9QazRyRUYvNmdkb1lld3ljRVVmNHZzWE5DdFJaNXk2V3owNEpmcXJwR3dIUXo3Z1J1N0pKSTVCckJ6dm9sVU5lZEJVZkpHN0V5T2VWWUhXQzZQMzV6VE82WGJkb2dOYjNIT0NRR2lSWG92bXM4NE9vd0dHVlMwZENBWHZQcjU0UXNZbWQwL2gwbFkraVVpUFNFWkNNcU1ZRXBTNUxLUTRvSDkwZUJjN3RHQzZadEl2Ujl0ZDVOZFZLdkY0NUEzdk53Z1JSVHYiLCJtYWMiOiIzYzEzZDRlYmIzYWUzOTRhNWYwZjI2MDQ1NDIxMWQwZDIyYzE3MDU0ZGI2MTMyNzExN2RhYmQwMDdkNzY3NTQxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InovMHBteE9RRlh4aW1zNTE2c2RMUFE9PSIsInZhbHVlIjoiZ3hHUi9ETnFRcXF6T3dhWkdCaEQxdGNnbSs4WDJCLzFmZTFwclJBMkhiUTQ3NThMUzNBZWVJSzlXVGd6QWl2SXQ0ckNVYis4YWsrOWtxTHBmbWpMaDBLcGlSWVBJbXFjQ2oxdyticTNQSyt2L2g1THRCNU53UDBWampyVU5GQis5YVB6akY3TWsxUS9aMk9lMVBkWm0xa0dRZWJZemY2UEZiRmFEcGR6cUJPYVhZaWZHVGlrMzhlWXp6dXVSWEdoQ3NkR1I3WVJmSkw1cXJ6SFhMUFdLUWNMUU5palE5a2RKc0JyK2M3ZjRYNnN0L3JTOEhuekZmaCtoTlNtV0lSdmdKRTVpQjliS01Ud21ZeXRURHRObmNQdmJ1YXVBV0drUEhHWlZFaFl4YjJoWmdwZmRubk1MY3FXcXlWa3VBS0k3THNUTVYxb2RRVTNLS0F5My82bWhPcXNmY01raXlFLzl5aFR4M3VTTk1HemNvaVhuQldkSDNodEVpMHRmVEF0UmFaY1JGeTNYWU5pNG41Qmo1UnF0SXpJYUJrZlQzSUh1N1ZTV3dWaWhERHlicDJHQzFEME1mcjkzNWlCdG1HUDNYRE5jajFBQTlpVGtMZ3RkMUFrNGlVWVBRK1hSc2ZGNjhTZy9iQUtjQ3I2V3FmNFFzclozQ244aHV2d09ZWk8iLCJtYWMiOiIzYTY4NGQ1Yzk3YjRjYjk2NTkzODU4NWVmNTRkN2RhMzcyODI2ZDVkMDhiOTYzMzEzZWEzMzhmMzcwMzQyMWVlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFZTDhMT2lRTTY1ZFE4QmFvMUcrbkE9PSIsInZhbHVlIjoibS8xU09MbnFCVEIwUVBMLzB6NWZVZEl1RGhRcG00cFB4b3RQRnUyQVhSekNBY2VUSWpTWnNhSzNQcUdUQlhOc1ZXUy9BZnIxTHdJS1hZQWlkQzBIdUZ3Y1BBSEhvVXNYSHh3c0I4dXBQT0RRMW5hQVhOT3BlbG9lUVNFdDh6NVhST042bHRjV25FbytTcWIxMXFQcWtIaGJaQ0FmdldDaitKYkw1WDVCcHFzY0JCbWIyT1dVdExlRzQzcnhJdTJhbE1Ob0ZtSHJuRGxOZlVLQVhJVHVlenAyZ29SQTNFNURvTXUxcXFlTFFkUlBRTC9PVnB3NlVEWU00SXFBckZQRmhJL1BDVlNWcEd5QnV1ZWFaaG00TWs2Y3ptTm5ZOUNvWkZmenNJNHJpUGxBc3VTaEpZa3hIa2ZGSm9QazRyRUYvNmdkb1lld3ljRVVmNHZzWE5DdFJaNXk2V3owNEpmcXJwR3dIUXo3Z1J1N0pKSTVCckJ6dm9sVU5lZEJVZkpHN0V5T2VWWUhXQzZQMzV6VE82WGJkb2dOYjNIT0NRR2lSWG92bXM4NE9vd0dHVlMwZENBWHZQcjU0UXNZbWQwL2gwbFkraVVpUFNFWkNNcU1ZRXBTNUxLUTRvSDkwZUJjN3RHQzZadEl2Ujl0ZDVOZFZLdkY0NUEzdk53Z1JSVHYiLCJtYWMiOiIzYzEzZDRlYmIzYWUzOTRhNWYwZjI2MDQ1NDIxMWQwZDIyYzE3MDU0ZGI2MTMyNzExN2RhYmQwMDdkNzY3NTQxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InovMHBteE9RRlh4aW1zNTE2c2RMUFE9PSIsInZhbHVlIjoiZ3hHUi9ETnFRcXF6T3dhWkdCaEQxdGNnbSs4WDJCLzFmZTFwclJBMkhiUTQ3NThMUzNBZWVJSzlXVGd6QWl2SXQ0ckNVYis4YWsrOWtxTHBmbWpMaDBLcGlSWVBJbXFjQ2oxdyticTNQSyt2L2g1THRCNU53UDBWampyVU5GQis5YVB6akY3TWsxUS9aMk9lMVBkWm0xa0dRZWJZemY2UEZiRmFEcGR6cUJPYVhZaWZHVGlrMzhlWXp6dXVSWEdoQ3NkR1I3WVJmSkw1cXJ6SFhMUFdLUWNMUU5palE5a2RKc0JyK2M3ZjRYNnN0L3JTOEhuekZmaCtoTlNtV0lSdmdKRTVpQjliS01Ud21ZeXRURHRObmNQdmJ1YXVBV0drUEhHWlZFaFl4YjJoWmdwZmRubk1MY3FXcXlWa3VBS0k3THNUTVYxb2RRVTNLS0F5My82bWhPcXNmY01raXlFLzl5aFR4M3VTTk1HemNvaVhuQldkSDNodEVpMHRmVEF0UmFaY1JGeTNYWU5pNG41Qmo1UnF0SXpJYUJrZlQzSUh1N1ZTV3dWaWhERHlicDJHQzFEME1mcjkzNWlCdG1HUDNYRE5jajFBQTlpVGtMZ3RkMUFrNGlVWVBRK1hSc2ZGNjhTZy9iQUtjQ3I2V3FmNFFzclozQ244aHV2d09ZWk8iLCJtYWMiOiIzYTY4NGQ1Yzk3YjRjYjk2NTkzODU4NWVmNTRkN2RhMzcyODI2ZDVkMDhiOTYzMzEzZWEzMzhmMzcwMzQyMWVlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047147240\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}