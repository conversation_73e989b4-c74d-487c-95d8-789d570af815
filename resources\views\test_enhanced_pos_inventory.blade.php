<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار خصم المخزون - النظام المحسن</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .result-box {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            min-height: 100px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        .log-entry {
            font-family: monospace;
            font-size: 12px;
            margin: 2px 0;
            padding: 2px 5px;
            border-left: 3px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🧪 اختبار خصم المخزون - النظام المحسن</h1>
        
        <div class="alert alert-info">
            <h5>📋 خطوات الاختبار:</h5>
            <ol>
                <li>تحقق من المخزون الحالي</li>
                <li>أضف منتج إلى السلة</li>
                <li>أتمم عملية الدفع</li>
                <li>تحقق من خصم المخزون</li>
                <li>راجع السجلات (Logs)</li>
            </ol>
        </div>

        <!-- عرض المخزون الحالي -->
        <div class="test-section">
            <h3>📊 المخزون الحالي</h3>
            <p>عرض الكميات المتاحة في مستودعك</p>
            
            <button class="btn btn-info" onclick="checkCurrentStock()">
                📋 عرض المخزون الحالي
            </button>
            
            <div class="result-box" id="currentStockResult">
                <em>انقر على الزر لعرض المخزون...</em>
            </div>
        </div>

        <!-- اختبار إضافة منتج للسلة -->
        <div class="test-section">
            <h3>🛒 اختبار إضافة منتج للسلة</h3>
            <p>محاكاة إضافة منتج إلى سلة النظام المحسن</p>
            
            <div class="row">
                <div class="col-md-4">
                    <label>معرف المنتج:</label>
                    <input type="number" id="testProductId" class="form-control" value="1" min="1">
                </div>
                <div class="col-md-4">
                    <label>الكمية:</label>
                    <input type="number" id="testQuantity" class="form-control" value="1" min="1">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button class="btn btn-primary w-100" onclick="addToCart()">
                        🛒 إضافة للسلة
                    </button>
                </div>
            </div>
            
            <div class="result-box" id="addToCartResult">
                <em>انقر على الزر لإضافة منتج للسلة...</em>
            </div>
        </div>

        <!-- عرض السلة الحالية -->
        <div class="test-section">
            <h3>🛍️ السلة الحالية</h3>
            <p>عرض محتويات السلة</p>
            
            <button class="btn btn-secondary" onclick="viewCart()">
                👁️ عرض السلة
            </button>
            <button class="btn btn-warning" onclick="clearCart()">
                🗑️ مسح السلة
            </button>
            
            <div class="result-box" id="cartResult">
                <em>انقر على الزر لعرض السلة...</em>
            </div>
        </div>

        <!-- محاكاة عملية الدفع -->
        <div class="test-section">
            <h3>💳 محاكاة عملية الدفع</h3>
            <p>اختبار عملية الدفع وخصم المخزون</p>
            
            <div class="alert alert-warning">
                <strong>تحذير:</strong> هذا سيقوم بإنشاء فاتورة حقيقية وخصم المخزون!
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <label>معرف العميل:</label>
                    <input type="number" id="customerId" class="form-control" value="1" min="1">
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button class="btn btn-success w-100" onclick="processPayment()">
                        💳 إتمام الدفع
                    </button>
                </div>
            </div>
            
            <div class="result-box" id="paymentResult">
                <em>انقر على الزر لإتمام الدفع...</em>
            </div>
        </div>

        <!-- عرض السجلات -->
        <div class="test-section">
            <h3>📝 السجلات (Logs)</h3>
            <p>عرض سجلات النظام لتتبع عملية خصم المخزون</p>
            
            <button class="btn btn-info" onclick="viewLogs()">
                📄 عرض السجلات
            </button>
            
            <div class="result-box" id="logsResult">
                <em>انقر على الزر لعرض السجلات...</em>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إعداد CSRF token
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function showResult(elementId, message, type = 'info') {
            const element = $('#' + elementId);
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colorClass = type === 'success' ? 'success' : 
                              type === 'error' ? 'error' : 
                              type === 'warning' ? 'warning' : 'info';
            
            element.append(`<div class="${colorClass} log-entry">[${timestamp}] ${message}</div>`);
            element.scrollTop(element[0].scrollHeight);
        }

        function clearResult(elementId) {
            $('#' + elementId).html('<em>جاري التحميل...</em>');
        }

        function checkCurrentStock() {
            clearResult('currentStockResult');
            showResult('currentStockResult', '🔍 جاري جلب المخزون...', 'info');
            
            $.ajax({
                url: '/get-warehouse-stock',
                method: 'GET',
                data: {
                    warehouse_id: {{ Auth::user()->warehouse_id ?? 1 }}
                },
                success: function(response) {
                    if (response.success) {
                        showResult('currentStockResult', '✅ تم جلب المخزون بنجاح!', 'success');
                        
                        if (response.products && response.products.length > 0) {
                            response.products.forEach(function(product) {
                                showResult('currentStockResult', 
                                    `📦 ${product.name} (ID: ${product.id}): ${product.quantity} قطعة`, 'info');
                            });
                        } else {
                            showResult('currentStockResult', '📭 لا توجد منتجات في هذا المستودع', 'warning');
                        }
                    } else {
                        showResult('currentStockResult', '❌ فشل في جلب المخزون: ' + response.message, 'error');
                    }
                },
                error: function(xhr) {
                    showResult('currentStockResult', '❌ خطأ في الاتصال: ' + xhr.responseText, 'error');
                }
            });
        }

        function addToCart() {
            clearResult('addToCartResult');
            
            const productId = $('#testProductId').val();
            const quantity = $('#testQuantity').val();
            
            showResult('addToCartResult', `🔄 إضافة المنتج ${productId} بكمية ${quantity} للسلة...`, 'info');
            
            $.ajax({
                url: '{{ route("pos.enhanced.add_to_cart") }}',
                method: 'POST',
                data: {
                    product_id: productId,
                    quantity: quantity,
                    price: 10.00, // سعر تجريبي
                    name: 'منتج تجريبي',
                    is_manual: false,
                    tax_rate: 0
                },
                success: function(response) {
                    if (response.success) {
                        showResult('addToCartResult', '✅ تم إضافة المنتج للسلة بنجاح!', 'success');
                        showResult('addToCartResult', `📊 عدد المنتجات في السلة: ${response.cart_count}`, 'info');
                    } else {
                        showResult('addToCartResult', '❌ فشل في إضافة المنتج: ' + response.message, 'error');
                    }
                },
                error: function(xhr) {
                    showResult('addToCartResult', '❌ خطأ في الاتصال: ' + xhr.responseText, 'error');
                }
            });
        }

        function viewCart() {
            clearResult('cartResult');
            showResult('cartResult', '🔍 جاري جلب محتويات السلة...', 'info');
            
            $.ajax({
                url: '{{ route("pos.enhanced.get_cart") }}',
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        showResult('cartResult', '✅ تم جلب السلة بنجاح!', 'success');
                        
                        if (response.cart && response.cart.length > 0) {
                            response.cart.forEach(function(item, index) {
                                showResult('cartResult', 
                                    `🛒 ${index + 1}. ${item.name} - الكمية: ${item.quantity} - السعر: ${item.price} ريال`, 'info');
                            });
                            showResult('cartResult', `💰 الإجمالي: ${response.summary.total.toFixed(2)} ريال`, 'success');
                        } else {
                            showResult('cartResult', '🛒 السلة فارغة', 'warning');
                        }
                    } else {
                        showResult('cartResult', '❌ فشل في جلب السلة: ' + response.message, 'error');
                    }
                },
                error: function(xhr) {
                    showResult('cartResult', '❌ خطأ في الاتصال: ' + xhr.responseText, 'error');
                }
            });
        }

        function clearCart() {
            clearResult('cartResult');
            showResult('cartResult', '🗑️ جاري مسح السلة...', 'info');
            
            $.ajax({
                url: '{{ route("pos.enhanced.clear_cart") }}',
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        showResult('cartResult', '✅ تم مسح السلة بنجاح!', 'success');
                    } else {
                        showResult('cartResult', '❌ فشل في مسح السلة: ' + response.message, 'error');
                    }
                },
                error: function(xhr) {
                    showResult('cartResult', '❌ خطأ في الاتصال: ' + xhr.responseText, 'error');
                }
            });
        }

        function processPayment() {
            clearResult('paymentResult');
            
            const customerId = $('#customerId').val();
            
            showResult('paymentResult', '💳 جاري إتمام عملية الدفع...', 'info');
            showResult('paymentResult', '⚠️ هذا سيقوم بإنشاء فاتورة حقيقية!', 'warning');
            
            $.ajax({
                url: '{{ route("pos.enhanced.process_payment") }}',
                method: 'POST',
                data: {
                    customer_id: customerId,
                    payment_type: 'cash',
                    cash_amount: 100,
                    network_amount: 0
                },
                success: function(response) {
                    if (response.success) {
                        showResult('paymentResult', '✅ تم إتمام الدفع بنجاح!', 'success');
                        showResult('paymentResult', `📄 رقم الفاتورة: ${response.invoice_number}`, 'info');
                        showResult('paymentResult', `👤 العميل: ${response.customer_name}`, 'info');
                        showResult('paymentResult', `💰 المبلغ: ${response.total} ريال`, 'info');
                        showResult('paymentResult', '🔄 تحقق من المخزون الآن لرؤية التغيير', 'warning');
                    } else {
                        showResult('paymentResult', '❌ فشل في إتمام الدفع: ' + response.message, 'error');
                    }
                },
                error: function(xhr) {
                    showResult('paymentResult', '❌ خطأ في الاتصال: ' + xhr.responseText, 'error');
                }
            });
        }

        function viewLogs() {
            clearResult('logsResult');
            showResult('logsResult', '📄 السجلات متاحة في ملف Laravel logs', 'info');
            showResult('logsResult', '📁 المسار: storage/logs/laravel.log', 'info');
            showResult('logsResult', '🔍 ابحث عن: "Enhanced POS" أو "warehouse_quantity"', 'info');
            showResult('logsResult', '💡 يمكنك استخدام: tail -f storage/logs/laravel.log | grep "Enhanced POS"', 'info');
        }

        // تحديث المخزون كل 10 ثوان
        setInterval(function() {
            if ($('#currentStockResult').text().includes('تم جلب المخزون بنجاح')) {
                checkCurrentStock();
            }
        }, 10000);
    </script>
</body>
</html>
