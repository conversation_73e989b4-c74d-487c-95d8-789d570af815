{"__meta": {"id": "X159340e7fc1162b354579efe4c3322e8", "datetime": "2025-06-28 15:49:35", "utime": **********.303306, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125774.840925, "end": **********.303321, "duration": 0.46239590644836426, "duration_str": "462ms", "measures": [{"label": "Booting", "start": 1751125774.840925, "relative_start": 0, "end": **********.230903, "relative_end": **********.230903, "duration": 0.38997793197631836, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.230913, "relative_start": 0.3899879455566406, "end": **********.303323, "relative_end": 2.1457672119140625e-06, "duration": 0.07241010665893555, "duration_str": "72.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45163840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015109999999999998, "accumulated_duration_str": "15.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.266555, "duration": 0.014039999999999999, "duration_str": "14.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.919}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.289011, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.919, "width_percent": 2.912}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.29458, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.831, "width_percent": 4.169}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-986690628 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-986690628\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1498992465 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1498992465\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-760060132 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760060132\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2042681176 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125771282%7C26%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRTYkcxNXVBMHErZ2IzZmJNbnFiRnc9PSIsInZhbHVlIjoialFFQjNHeUQzV043RWJ0V0kzVHJVU21QUkRjcktYMWVVV0NPR0NuZkZnUHVEUGRrOTBSTUJ0KytlMmtDRGo3eHVlVk5NZlBtaGtZQ2U4OHp4YTRkT256TURNZUQvK1Zqcm1kVVNWLzZtcERidEZMakk3b0RkTXNTVktMbi9aZFRNRU1COForS250djBselpsaDB2enJQTW9DbFFtUjcwRTZBcTdZY0sxaytiSzduQ01ra3l6YUZmOHFpcUd4Q0xPbXRlSmw4aE1IY1I0SXNiTFByb2ZNcndndS9TU1YyQno4a2V6YVhoaHlncGpzNVJuVW9mM0dBSnFCc1BLc2tTODg2T3ZNUUp3TGdWWnMvdnA0WDNRZCtHZXJMKzdwMGsrWTl6ekhFcVBPZk0rYnVXRnNvekl4eVlMNUI5MkFMWkd3RC9jNHlCTWxFcEpuL0c3L3o5NWtib1NldzlCS1ZPcmEzRXR6QytLUjF4M0pENnJPUTJkMmpwVzlpclpmbVBtVUw5bUg4WmpJZ2EzWEpydWVsQUVKei9Rb0hoekRseXltWDVMNWoyVmF3UnR0MGdLb0lhL0VXZmFPd1FtZVFXdkpaVXd3ZHI3NWUwTGxnWTl5VEFKdmtYU1lrK243STV3MlRRY1hHdDA3d3QyR0lmUzlpVnJqRjNzR3RiMW5zcEIiLCJtYWMiOiI4NTRhZTM2YWM5NmNkNWFiMWUwMmI2NWZmZmYyMDIwM2MyZDcwMzhhMzFjODczY2EwZjQzNjMxNGRmNGI1OTI3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklDTU90ZThkcmNSVlNjamFXRWdzYmc9PSIsInZhbHVlIjoieUgyZ2llNm1hMW5oUUxJMDdHcEp2UHlxSUtZZHJUQXR5bVpGWU5wcEcyZXpxbGJ1SWZPWUc2K1JtZ1FWazVKWDZSaFN5bkNhWW9HOEFLNDUvYmNPREp4MVpaZU5nU0lnWGs2cHR6SjVYdHFhc2lwdjdOUlN6S1ZLbm9HSzcrQit5b041eXJxMzd0OWx4ZW93UWlBZW1hOE03MFRGeWFqdjdVMDhyQkZOQ1dmQ0U0aUJodjl4TnowbGdMTTFWZHlzaUNYRm1ZTVNsdW91S2J1ZHBnZk9QNmFCd3VHU254eFlJNHZYU0l1cXI0MTBuWlZDem9rYmRydkZxcmR0V1FlWlZIZS9MQ1VMT0VGZ243UWZ3bFYwY3lRRWM4b1prbE5xN3orUkVXcFZUcGRBTnVOeXNSaDJEcWpsRFhWNmFrQW1UbzI3SitKNmF6eElSQ3Q2RGgwU053ZTlLcWRDM3dlQnJjU01pcURQbEtjSGthd3VIQ3ppM1JtTUFndWdoaVNKZlA3OTVJVXlJam1ITXBhVVBrR0RtUjNBTmNJczRLN3lzNjlud0NUcTQraWI2WENoT2NnQzRaQW9ldkhYM0tBanUzQUJwNDZ6TkxFSGFRVUQxOHI3YVR4RERwY3BUWnhZK1owblpqZkFZRjBhVHdJUGJJZXhiNExHTWxjbTZuUUoiLCJtYWMiOiI0NmRmYjZiODVjOGY5MzgxMmM3NGE3ZGFhYTQzNjUyYTFhNzY4ZjE4OTFkZGI0ZDgyMDdiNGFiOGU3M2ZlNjNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2042681176\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-745008492 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745008492\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1812308221 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjllU3d3dlNVWlBZVEdpbWlRTlJ1cmc9PSIsInZhbHVlIjoiOUhna3hEMzE0cWVoZzdzVkt4TDZrTnZmbndCMEdmcGZ1eEp1TlFiUUUvRUZYd3NMNEJpa2VlUG40WTZ5dGNBQ0tOT3N5U2dDMzVNSm9oamZUQnMvd3I3OVVhNTNoWFFvV1B1eHN4Qmp4TXN1WGU2TW9qSCtwWjJiMHpMbkxpQk9EcUQ3WWVQLzAxdHhIS2UvQXMwanVNWDdiSE4zdGo1cmM3dXBtRmVMNmZwZVN0WitRUkMzelBCaGE1YVZsRWlxcDErRGNFcU5MaDVCYUhVcnY4M2xpa0ZZZHZHTy9LaG81dnNKd1VIUGJmNjlaS3U1eWN6VkMwcko0VjIyS21UUWkyN25pQkNQUnVGdW5ETnY4bEhoSnZFSTJZbUtNTTNtS2s5Ty9NL1JPZVFPWlNkVGNBTFRvQVl2cEMyeEY3a29iYmlXa1lWUjVkMnIxSWhHalAyR3FCRXRkdGxnVEZRWlZ3djJaeVd1dE5mWm9Tc0FrMGJLMEtrWWV2d2JGbUlhNkI5bmkwUGtwcnJKWUtJSmtiUThiWTV5NVZPYVBiRjk4ZVdvZVJwUUlyVDlnTFdNbXo1cERRQVdtbGVmak1zM1VyMVVCQ3NQZzhkbmNFYkVyNmJZNWVpYlBaZCt4eXhrQU5SY1Jaai9XWnQ1ZldkeG5qSnFIQjcvTGlDeVo0em0iLCJtYWMiOiI4OWJkNmQ4NWQ2MTFkMjg5YjE3Y2Q1MzRhMzVlNjlhNDZiMTg1ODU1YzYxMWQ5NjE2NGVkYmRmMjk2MDRkYzJmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxEYTVwWVVYYXVSV1htS1ljeWN1Snc9PSIsInZhbHVlIjoidU4zR2J5dTczVWJYWUVzeVRBaExkZDR5ck5BK3FPRTFYZ0RhQkgwUWQzb0tkR0VXK2tEVXdQdDRhQzZuVE1oeWh5bFJXT0RsY2l1SW92V0RpaVQ5K1hBQ3dwNG9Nak1XNDlvQTVPSGJxZDk0Zjl1K0Zvc1Q3L2dxUCszMUVVVEpZSVlNV3N3THlMZVBxd1ZtRFE3WVhTVUtYV2d2NGVaUEQ0UzRGR2JoVi9XQUZoUmdvMlVEVnNQNnVDWVBqWWg5YXRHcndkN092ZVZ4elBMNFlpQWxKR0hESHE5K3Y3M3FQbG14VjR2Rm9XekNJWGhMeEdDV2RicUxIaDFiS01ZaHpNVXpib3BvZEdNUVhDYnZmZzFvMnlvZTZWeGFRVkFrSWVsT0FIZnAvREYwMC8zaEZndmd6cGtLWFVxMnZaYmdIVVkrTFN1S2FoN3F1eTJKV0NRRkIvZ0tiRVEvQlJ2ZkxkKzF3K3RWYkVmRStGMWRkaW50cGhMd29FRTdNNTFvNSs1UXE1dkhIQkFyb2lOa1Y5bWFpZVRyQ1c1eTdEVlJqNzhtb2pWMjVXN1ZlQ29uZFhkMzQ5Q2JYdnVOb1JpdjVHdmJqZzdLZnlpYjZpNlRPSmVQUWphZlFIb3VFakNIV2Rxd2tmT1dSM2RaZmRUK1FJcTVoNTZYblVNYnA4bFIiLCJtYWMiOiI4Y2I3MDU0OTgzMzliYTlkNWY1NmU1ZGNhYTg3ZmIyOTliYWFiMGUwMzRlMzE2NGZjMWFmNjc5Njk2M2VjZTdkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjllU3d3dlNVWlBZVEdpbWlRTlJ1cmc9PSIsInZhbHVlIjoiOUhna3hEMzE0cWVoZzdzVkt4TDZrTnZmbndCMEdmcGZ1eEp1TlFiUUUvRUZYd3NMNEJpa2VlUG40WTZ5dGNBQ0tOT3N5U2dDMzVNSm9oamZUQnMvd3I3OVVhNTNoWFFvV1B1eHN4Qmp4TXN1WGU2TW9qSCtwWjJiMHpMbkxpQk9EcUQ3WWVQLzAxdHhIS2UvQXMwanVNWDdiSE4zdGo1cmM3dXBtRmVMNmZwZVN0WitRUkMzelBCaGE1YVZsRWlxcDErRGNFcU5MaDVCYUhVcnY4M2xpa0ZZZHZHTy9LaG81dnNKd1VIUGJmNjlaS3U1eWN6VkMwcko0VjIyS21UUWkyN25pQkNQUnVGdW5ETnY4bEhoSnZFSTJZbUtNTTNtS2s5Ty9NL1JPZVFPWlNkVGNBTFRvQVl2cEMyeEY3a29iYmlXa1lWUjVkMnIxSWhHalAyR3FCRXRkdGxnVEZRWlZ3djJaeVd1dE5mWm9Tc0FrMGJLMEtrWWV2d2JGbUlhNkI5bmkwUGtwcnJKWUtJSmtiUThiWTV5NVZPYVBiRjk4ZVdvZVJwUUlyVDlnTFdNbXo1cERRQVdtbGVmak1zM1VyMVVCQ3NQZzhkbmNFYkVyNmJZNWVpYlBaZCt4eXhrQU5SY1Jaai9XWnQ1ZldkeG5qSnFIQjcvTGlDeVo0em0iLCJtYWMiOiI4OWJkNmQ4NWQ2MTFkMjg5YjE3Y2Q1MzRhMzVlNjlhNDZiMTg1ODU1YzYxMWQ5NjE2NGVkYmRmMjk2MDRkYzJmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxEYTVwWVVYYXVSV1htS1ljeWN1Snc9PSIsInZhbHVlIjoidU4zR2J5dTczVWJYWUVzeVRBaExkZDR5ck5BK3FPRTFYZ0RhQkgwUWQzb0tkR0VXK2tEVXdQdDRhQzZuVE1oeWh5bFJXT0RsY2l1SW92V0RpaVQ5K1hBQ3dwNG9Nak1XNDlvQTVPSGJxZDk0Zjl1K0Zvc1Q3L2dxUCszMUVVVEpZSVlNV3N3THlMZVBxd1ZtRFE3WVhTVUtYV2d2NGVaUEQ0UzRGR2JoVi9XQUZoUmdvMlVEVnNQNnVDWVBqWWg5YXRHcndkN092ZVZ4elBMNFlpQWxKR0hESHE5K3Y3M3FQbG14VjR2Rm9XekNJWGhMeEdDV2RicUxIaDFiS01ZaHpNVXpib3BvZEdNUVhDYnZmZzFvMnlvZTZWeGFRVkFrSWVsT0FIZnAvREYwMC8zaEZndmd6cGtLWFVxMnZaYmdIVVkrTFN1S2FoN3F1eTJKV0NRRkIvZ0tiRVEvQlJ2ZkxkKzF3K3RWYkVmRStGMWRkaW50cGhMd29FRTdNNTFvNSs1UXE1dkhIQkFyb2lOa1Y5bWFpZVRyQ1c1eTdEVlJqNzhtb2pWMjVXN1ZlQ29uZFhkMzQ5Q2JYdnVOb1JpdjVHdmJqZzdLZnlpYjZpNlRPSmVQUWphZlFIb3VFakNIV2Rxd2tmT1dSM2RaZmRUK1FJcTVoNTZYblVNYnA4bFIiLCJtYWMiOiI4Y2I3MDU0OTgzMzliYTlkNWY1NmU1ZGNhYTg3ZmIyOTliYWFiMGUwMzRlMzE2NGZjMWFmNjc5Njk2M2VjZTdkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812308221\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}