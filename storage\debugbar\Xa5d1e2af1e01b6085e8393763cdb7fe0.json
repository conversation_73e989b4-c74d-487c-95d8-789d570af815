{"__meta": {"id": "Xa5d1e2af1e01b6085e8393763cdb7fe0", "datetime": "2025-06-28 16:30:18", "utime": **********.486277, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128217.95714, "end": **********.486292, "duration": 0.5291519165039062, "duration_str": "529ms", "measures": [{"label": "Booting", "start": 1751128217.95714, "relative_start": 0, "end": **********.403029, "relative_end": **********.403029, "duration": 0.4458889961242676, "duration_str": "446ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.403037, "relative_start": 0.44589710235595703, "end": **********.486294, "relative_end": 2.1457672119140625e-06, "duration": 0.08325695991516113, "duration_str": "83.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45709448, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017980000000000003, "accumulated_duration_str": "17.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.441742, "duration": 0.016890000000000002, "duration_str": "16.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.938}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.469213, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.938, "width_percent": 3.671}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.475919, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.608, "width_percent": 2.392}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1807916325 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1807916325\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1823520135 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1823520135\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1177657487 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128216412%7C46%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdRL0xlSGN6Z095aDJlejBQYVhCQ2c9PSIsInZhbHVlIjoiKzVidllxaGdxdTdBMUdmbDlFa09kVUxuRkgvSVQzcGhUQ2Vtb3VWSjZseFJweVhhYVR1NENLR3paWHBSUzBxRGwxR3pHWXZQYjJvQXhhOC83SG5WSUFleDk0bkFmVnBmL09ORGdBVGhUSW54WjYrTEFFUzlWcDAvYWdadUpucHRKQmZFVlZRNjloVm1obkkva2dFcHpSdUNkeXhFWkdXZzQyVGdSMlFRRXZwbkNBMEpzMHFEOXVVOGJrbndnQXU2SjRhM0Z0Y1hNU3Fxcm1ITVNEOXRmWnRPYUYrWHduM3ZzSGdyck12K1FWb05sTHVxYjVYMDBrbFAwdE9jOFpnT2VqQzNrb0tHNlVQZ3M2UHlqQklmdldRM3kxWnJwbmU2YWJ5alNkQjYraURpOW1QRVVveFZQc0NRTXQzNzhmbXNpUUE1bW5Zd01pUmtldVl1SHphNnNLSG5Jdmthemk3R3lGbzRGNE5KTUo0WGp6aEw4amgyaHFvZUYrOHVQeXUxZThBREdmZWRtYmpiSDRqMFpOTFEyTEdvSjFUam5KQ2JlVlpBWW95eE9NdUFaN25vY091elFTZi9saFlHTmZLa0ZOYUd3Q2NhM3J0d1c2K2N0Rms2NHE1RVNYR0VObzk0Qm85SWw4S3NpTnRiUDVKN3FiZld4cEprOVFEbUpVcCsiLCJtYWMiOiI0MDgxOGMwNGZkMDhmYzc2MjRmODQ0YWY2OGZhNTc3ZTdiOGRhYmU2YzJjNjYxMzViNzhmODVkZWUyYjNlODA1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFkZzAyNmdlbTEvcUxBSWVtNmM0UWc9PSIsInZhbHVlIjoiWUdzbERzcGlxMDUrQ2ZqczNzaENqT3E5L29NTE52bEpqVjhMVEdXa0VDOHd0aS90ZC9FWkJqZmlFejBTTXUzZjRpdUN4Z0Y5K01IUUpVaXhJV0p0WURFUVhhY1FLQlFoRUJES1ZvR3VubURVOWhUMDcwZ1BHL1pYVGUzUjNvbGJUSVlZMWN3c09LRjJ1ck1TcUEwdUxQcy9kR2VkNkgxbXRpWkFSNkdDVGJucHJCRnNJNmdSdUMwNXZ3QlhMQUt5TEFvbkQ0Nmx5YTFZM1p6TzE2YTNCU3J2Y0V2U3NJVjhLcndLbnliQkQxeDNyaFFVakc1KzV5dDRUSVJlWkNIRUdXVitiL1VOZVpYSU55YWlqc0NOdkNMVGtGQkNZVkY3WElQalpyekJwNUZaaXlxc2FWNVR6SjhKVkd2disreXI0Ui9YQnRFRHNzOTAveHlQRWhkcEVMaVArSlQyeXNmT0I2MkJidHRWRExyWGZ4cDcvUTFxT3d0SGJmRExOdWFYRFliWEdWMDFPSThFVWoxUkVZd3RqUG9Dc1JLU3IxWStHKzNYejZEUTljeXF1TUJhUDFRQStCQTFpdVZYMmNBRVppSWQ4TmVFTGlXYWFDajFjQTlzVHJoSnpZYVlJaXVVSGVkVTZSUVo5MVlKOUlKaStWSzd1bTF6QzByODJVNmQiLCJtYWMiOiIyZWFiY2I1ODc0NzZhY2IwZmNhNjY3NmJhYWI3NDRmYmExNDI4MjNhODFiNjU1YTBmNTE2YTYzMzg0ODNiZDBmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177657487\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1957825285 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1957825285\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1389648956 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkI1ODhtQ2Y4bzIreUJmR1Z1VFVQZ3c9PSIsInZhbHVlIjoiakIwVzdUYklqdkRFSlBHQitqOHMyTHE2eDVUTUFxR0VuN2NrdGREMG9mN2UvWjZjdTg4c244VExIM0VSRlV2RzVCYjU4blFFaTNoU3RiSFQ2NXhzWFJKUThVU2pjQTFpaGdZbTEzUlhvSGtiWGVVdGVuVFZMUUN4ZjM0SEl2MXRqQktkZjdBeXhNYUQ0eXFaSlFwb3FRSXpMVFFIMDdHcWl6SHZ0RkFtZzlZUGRlRTdvZzZaYnlUcTFhbUtuVzVhb3Fwem0zd05BbmZQYzRWTTBJVVZPUE5aREd0Z2hGcDNBbXQrWGFVTk5DYkdNczUxa3NsQkVhRVRQcmRJRU1aS0pRRjBiYVZpSXFqR0VPcXFlTlZHOWVIYS9iRWpGVXZMUjRpdHd6QmQ5VE1WZkptbHNZU2MrcWk2d0ZvTE5qbTR6M0JIUzdZeGFOQklVY2hucDVwbm9pRW9scnQya3FhdnpDVThPZGFlSnNRaHozS2tDYUJUbFRIa1VGR3BWdi9Xbi9YMDdFTGdKeUMyRk5SOGtFaThiU01NaXE0Tlg0T3VJWGdUajlBM0NBTjFlTUx6VFNPc1d1cDNoNi9QRzBZODZUc1hrZjlFdkRpUkNUUHlWK0NlV2VjQWZGWFVvazdEMUIxa0Zmd2J3WUpXaysvVWpNQ2dTZXNrWDIzK1FwNUIiLCJtYWMiOiJmMWE1YjFhMDZlYzM4YjE2YjBiOTUzMDVmOGM5MDYyZTZkOGNkNThkNzRkNDYwMWFjNWQ3MTcwYTAwYTBkYWE2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJlNVN5eUJ2SXhMUnR2QzBhdzNIV1E9PSIsInZhbHVlIjoia2Jyd0diRVJGY08rSmVCY0p1emhRQkdZcVdyS25MS1RVRk1WTVEyS0ZTcDlWVTVvSU11LzU1aTVpai9nbmdHVi9rMmlxSnhQOWFhNjVRT0FON3gwUy82ZS83WThKeDkzY1JiNXcxd0xXSHc5bGE5bzNvYjQyWGRpVXV0RzFNR0J0ZXBZY005a2dwakcvaFRUNkFyNUtOZDE4QnFDQVp6NUkxVFVZeE5td0RCcFFzT1RoU2xpNFc1dUt1N3hJczY5aFAzMmFmLzNMUUxIdEVQVWhUYnRvWUJnRCtWYUgyRyt4NkxjcXM2ODNDejFtbk1lQ3ozNC9ITFYvQTJNUExNOGMzc3hXQlpZdm5ocThNYnNFb0cvemF3RkJtN0dldnZnNWhjYm1iZHV0bEw4QVlXU250MGZUbGQxQ1hEeGxYNHFsclRLQzhRWmJFeXFaRzd4VnM5bVIvdnZaempnQ1d4NUNnK0VoWTQvSFBhZXR0TjlnQ2dFYk9PSW1tcW9TY3ZLa2xOWjJvR2tqc3VzWmg1bFV4MndDWm5FUGFZNGdxNE9CQmFpc1JPcWFhNmdZdVhNRXZMR2dQcERqVnN1L3dscWdtWXZkMEJqVUp3U1hmN3pXbnRQZkxlWXMwRytMODB2TG5sKzAwM0orN1V0eTk4UlorY2g2VGtMeXF0cmtTMFciLCJtYWMiOiIyMGU5YmUwMGU0MjJjOWIwMzU3OGVhYTBlMjhmNjk4MzY0ZTU5YThmNjVkNzA5M2QwYWRiYzhkNmNhMmQ4ZGIwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkI1ODhtQ2Y4bzIreUJmR1Z1VFVQZ3c9PSIsInZhbHVlIjoiakIwVzdUYklqdkRFSlBHQitqOHMyTHE2eDVUTUFxR0VuN2NrdGREMG9mN2UvWjZjdTg4c244VExIM0VSRlV2RzVCYjU4blFFaTNoU3RiSFQ2NXhzWFJKUThVU2pjQTFpaGdZbTEzUlhvSGtiWGVVdGVuVFZMUUN4ZjM0SEl2MXRqQktkZjdBeXhNYUQ0eXFaSlFwb3FRSXpMVFFIMDdHcWl6SHZ0RkFtZzlZUGRlRTdvZzZaYnlUcTFhbUtuVzVhb3Fwem0zd05BbmZQYzRWTTBJVVZPUE5aREd0Z2hGcDNBbXQrWGFVTk5DYkdNczUxa3NsQkVhRVRQcmRJRU1aS0pRRjBiYVZpSXFqR0VPcXFlTlZHOWVIYS9iRWpGVXZMUjRpdHd6QmQ5VE1WZkptbHNZU2MrcWk2d0ZvTE5qbTR6M0JIUzdZeGFOQklVY2hucDVwbm9pRW9scnQya3FhdnpDVThPZGFlSnNRaHozS2tDYUJUbFRIa1VGR3BWdi9Xbi9YMDdFTGdKeUMyRk5SOGtFaThiU01NaXE0Tlg0T3VJWGdUajlBM0NBTjFlTUx6VFNPc1d1cDNoNi9QRzBZODZUc1hrZjlFdkRpUkNUUHlWK0NlV2VjQWZGWFVvazdEMUIxa0Zmd2J3WUpXaysvVWpNQ2dTZXNrWDIzK1FwNUIiLCJtYWMiOiJmMWE1YjFhMDZlYzM4YjE2YjBiOTUzMDVmOGM5MDYyZTZkOGNkNThkNzRkNDYwMWFjNWQ3MTcwYTAwYTBkYWE2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJlNVN5eUJ2SXhMUnR2QzBhdzNIV1E9PSIsInZhbHVlIjoia2Jyd0diRVJGY08rSmVCY0p1emhRQkdZcVdyS25MS1RVRk1WTVEyS0ZTcDlWVTVvSU11LzU1aTVpai9nbmdHVi9rMmlxSnhQOWFhNjVRT0FON3gwUy82ZS83WThKeDkzY1JiNXcxd0xXSHc5bGE5bzNvYjQyWGRpVXV0RzFNR0J0ZXBZY005a2dwakcvaFRUNkFyNUtOZDE4QnFDQVp6NUkxVFVZeE5td0RCcFFzT1RoU2xpNFc1dUt1N3hJczY5aFAzMmFmLzNMUUxIdEVQVWhUYnRvWUJnRCtWYUgyRyt4NkxjcXM2ODNDejFtbk1lQ3ozNC9ITFYvQTJNUExNOGMzc3hXQlpZdm5ocThNYnNFb0cvemF3RkJtN0dldnZnNWhjYm1iZHV0bEw4QVlXU250MGZUbGQxQ1hEeGxYNHFsclRLQzhRWmJFeXFaRzd4VnM5bVIvdnZaempnQ1d4NUNnK0VoWTQvSFBhZXR0TjlnQ2dFYk9PSW1tcW9TY3ZLa2xOWjJvR2tqc3VzWmg1bFV4MndDWm5FUGFZNGdxNE9CQmFpc1JPcWFhNmdZdVhNRXZMR2dQcERqVnN1L3dscWdtWXZkMEJqVUp3U1hmN3pXbnRQZkxlWXMwRytMODB2TG5sKzAwM0orN1V0eTk4UlorY2g2VGtMeXF0cmtTMFciLCJtYWMiOiIyMGU5YmUwMGU0MjJjOWIwMzU3OGVhYTBlMjhmNjk4MzY0ZTU5YThmNjVkNzA5M2QwYWRiYzhkNmNhMmQ4ZGIwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389648956\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2054003196 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054003196\", {\"maxDepth\":0})</script>\n"}}