{"__meta": {"id": "X717d8913d7e15e47fd8a23fccce1373b", "datetime": "2025-06-28 14:59:15", "utime": **********.290913, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122754.848854, "end": **********.290926, "duration": 0.44207191467285156, "duration_str": "442ms", "measures": [{"label": "Booting", "start": 1751122754.848854, "relative_start": 0, "end": **********.237004, "relative_end": **********.237004, "duration": 0.3881499767303467, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.237015, "relative_start": 0.38816094398498535, "end": **********.290928, "relative_end": 1.9073486328125e-06, "duration": 0.05391287803649902, "duration_str": "53.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45081144, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00356, "accumulated_duration_str": "3.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.266884, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.326}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.278188, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.326, "width_percent": 20.225}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.284201, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.551, "width_percent": 15.449}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-534661735 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122750616%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlduWkJMYjQ1Qzg5UVc2dWFXOXFKTVE9PSIsInZhbHVlIjoiY0dieWtCL0dablRRcXFYNS8wMkZBdGdIakVUc1IxNm1SeUQ3RlRLVTV5bGtZQU9uTFgrYXUyQXY0alI5bFlFRzRHWjBqTHpwb3VCQ081TGZjeWtxNlNISnRhTkY4NmtzczEwTlVqMFBWcUdTNlJaQm9XRmNlakpNTElJaEFHRjF6SnRUVlFVNjBmaWRVSjNjU2pJNzJON252THlTVHY0d2tNVEdrN0prK1BTSHh4TXZ2R015QVgzcStJSHpMd3BSYlYvK09MT1F3dmpkSm80WndxUTI3MG0vcm80UENlTTlNK2tiNjBGK1hYUXZGZnVkbU13WWRnOGtTVzNIRHc4ZkVQNjVncFNKdGI5QnlZK0F5SGlPS1c1T1NiL01zTnVNQmdpclZJY0dWTm9iNlpiMkIzeklLbDMxK0NFMkdDa3piVTFYOXcxVWFwY2owOFlEYjBqbTd3R2FETTkrZ3RlSG1meVk0US83My8xRVZnL2dNbFZieXJ0RTBZbXVxd0VZQ2V1NXlrT2xhTkppVGFkQnN2SkkwWGdtcVRYQVN2NWdFZ0w2V2l0bUJwMWtqM1o1b0xzNWg5ZmI0d1d1bzRlUGhlcGJHVE0zaGlGZW9JWUhpc2FueDVDTjVYdmkzY05uRjRGNGlaelVMbjRicmJHdlR3RHkxbUl3S0paN1p0WjAiLCJtYWMiOiIyNjg1ZDE1MTY3NWI1MGQ4MmNhOTFmYmM3MzgyNjMzMTgzY2VjODA0MzRlMGVlYzcyM2RhMjc2MWQwYTg2NTA3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik4wRy9abndOVWFWK3FTMklhTDk4ZWc9PSIsInZhbHVlIjoiS2ltVW5xbjgwdlRwTi9QekpqREZxU2hBUGk2Rnlkd2FXQk80dkRSRFdqaXZwS3RRTkV2Z1pKWFpwYmNHV2NyOWtDWUc3SVk5MjdBMm96YXpjMjBTcFlpOGJ4VXdRbjRZQmg5SUwvYnhqZ1R5Tm1SamxydGs5dVhpNmJlczNWenBIb0s4V1d3UVo3UFMrVVlzS3Bmd25nRk9rNEh4dlRrcUNtMXpXME1STU5nUnViM1lnRFVYRkgzN2Z3V2ZUS0RNMjdXR2dMSDNUZUxpTi9KUlZOMERsTitOdmc1OWRDTlBoeElIMHJtbi8zTUZNMkZ1ZUtRbjhVSGF3Q05oamdiVEdGREduYlpYSlJrMWZNMFpoTUIxRDlsUlJuUzVyYXBMSHVhMHd3WExjUWFDaHNiYVphU3pmVTlyZkpPMzdENmNOZCtyeWFrS1Y0WnltanNUbUN0eFZaTHdOb2tpNlhtVWlXRndTZTlYMHhuOEtnbkEwL3hwMDFRczV3cit5NkRZQTFkWk9rRnR4ZlVyL1UvY2toaVp1ckI2aUd0ajVQY25ocEg5SVZHWEZ1cENWWE1ramtsQ3BxdjJrcEhKSE5XTXc0MWpiajJDVGJFMEMrYnN4QWVLTEJRTEJMYzBFek1uMEZ4TVVsMUVGTDEwL3RtRnQ1Z21HOU9LNCtTTEk4c0MiLCJtYWMiOiIwZTlhNDc5MzBiYjhhNzRlNzZmMWNkZDUwMzQyODcxMDY0YzFmMzc3ZGI4NGQ5YzRiNzc3Yzc5OWY0YTVhODVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-534661735\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-167777966 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167777966\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpZR2lLdmd3Z0NrN1ZEbGl0Ym9LM0E9PSIsInZhbHVlIjoibWdMZzV5SklZRFU3Q2QyQWJzU2Vtcng1R3phNkpkL3pDeWtFVTdWa0NZVUg5eDJUWXIxcmd1TTA3aGJlWTE0QSswWUlGL2cwdmF6aTRXcTlDVmtKM1diaXNPaDlEK0ZEOWd0V3JSdXZLcXppRncvdDRuR0p5bkhGNUZLcnFaTGozanY4aGF3UzBFejhwN0YvamlxeHJIVTJveW9RNUd3ZGFzaVU3U2xISm0rZGxTcmNWWlBDcFNWQ2xtV1J4aUZ4Y2dMK2ZyN0JuSFJQQXk5dEw2SXB2aVlwNExEcFJGVFRNdEhqc01NaGllcHBxNXhpZ0RXTjcvZUZHZTl4SCsvSTJyZUdGUS9VWjV5ajJEc1dhb3V4REtPTkwrSEl1SXExWDY0RmFFY2w0UDE4TGdvOS9aMVd3ekJtWDFmVDBueFVFYjFBNlE2SUI5MmZCL2lITlRnc210b212VStFNjJyUkNiSHg1NVJyYU8wcExWM2EzSGc1S2RkV1h2NldvWkxNblI3TnBwNy93MUo3M2ExQlVBVmx5Y2IyOW1EMkNUSjdYdU1JRnUvUEtSVmFOUUkxV1lHcWRsQTJTQTk1bXI5cFExR0RiTUZWZUxOWjVNdDFaamJBeXRBVW1hRUFjdzZOeVRpYU4zK3gxdk1BV25VOFErTjV3VXh1T2RLeWxndjciLCJtYWMiOiJkMDVhZTc3M2Q3MGI0MjRkM2VmYjg0OThhZTMzOThlMmM3MjQ2NzFkOWZjNWI1YzNlMmMyMzI5ZDIxMDdlM2NjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkhLSDZXNWdiZVFwUDJ4ZXBQb3djL2c9PSIsInZhbHVlIjoicFN4bldQMmFLQm9pV0hzWlEvVDRiVnhZYnFTMlhqS0JNUnk4MHhsRGVYRjhRTzhVSEIycXpobWZ1WWpDN1IrSHZBTnhlVGY0NTNsbDRDTEEwb1prNDFHRVUvbGEzVWl0TWdMN2czM05QSVgvNW9CMnRiWlcxeGoxRWVtQVZQOG1sblNWcERmLytMUzN5anM2V3I4UG1uUWhWWUt3UEFWeklVdUF3NEwxb21BQzZaTW1rNFl4K0M5VWtLclN0RStvRElDdVVGQnYvcy9PdUdhaXJkSFg1VDNvRjA1dFM4NHhLU2tweE1FVENBdzhMaUIzVGxIeThkU3M2NzIyWGVjeHpoZzF1ZEg4ckJZOUE4Rjd2SzB0OGlQRHgrL3hEdHJuRlg1Y3NFb3RyRWR2a01YTDJCSUZzbDZwVnpJckFsMWlVMEcvV3RmOHVaOGtSS0lNT2tEN3RsNy9lcnV4Um1CTHJ0S29rekw0UUUxeE9UeElmSXdrdllDanFWWEZKZWlkdmpXalpUOThaUVlKYjJrT1pjNitnNnYvdDJydisxbjBreWQrYmhNV3JQcHlHKzZwQ3cwY2Q5TzlNNW5DMEZXb3VzZHFIdXRRMnp1bWdmZ0tVNW9Oc1VvSWNoaGdGNGcyTzZvV0FrallFWlVhYkdyZ2NJUlFsVHdaRHdjK3pCN0giLCJtYWMiOiI5NDYyNWZiOWYxYjE3YTc4NDk1MmYwZWNkNmExMDU0MWYwOWUxMWJlYjgzNzg2ZWQ5MTA4ZGVlOTExNTc0MjRkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpZR2lLdmd3Z0NrN1ZEbGl0Ym9LM0E9PSIsInZhbHVlIjoibWdMZzV5SklZRFU3Q2QyQWJzU2Vtcng1R3phNkpkL3pDeWtFVTdWa0NZVUg5eDJUWXIxcmd1TTA3aGJlWTE0QSswWUlGL2cwdmF6aTRXcTlDVmtKM1diaXNPaDlEK0ZEOWd0V3JSdXZLcXppRncvdDRuR0p5bkhGNUZLcnFaTGozanY4aGF3UzBFejhwN0YvamlxeHJIVTJveW9RNUd3ZGFzaVU3U2xISm0rZGxTcmNWWlBDcFNWQ2xtV1J4aUZ4Y2dMK2ZyN0JuSFJQQXk5dEw2SXB2aVlwNExEcFJGVFRNdEhqc01NaGllcHBxNXhpZ0RXTjcvZUZHZTl4SCsvSTJyZUdGUS9VWjV5ajJEc1dhb3V4REtPTkwrSEl1SXExWDY0RmFFY2w0UDE4TGdvOS9aMVd3ekJtWDFmVDBueFVFYjFBNlE2SUI5MmZCL2lITlRnc210b212VStFNjJyUkNiSHg1NVJyYU8wcExWM2EzSGc1S2RkV1h2NldvWkxNblI3TnBwNy93MUo3M2ExQlVBVmx5Y2IyOW1EMkNUSjdYdU1JRnUvUEtSVmFOUUkxV1lHcWRsQTJTQTk1bXI5cFExR0RiTUZWZUxOWjVNdDFaamJBeXRBVW1hRUFjdzZOeVRpYU4zK3gxdk1BV25VOFErTjV3VXh1T2RLeWxndjciLCJtYWMiOiJkMDVhZTc3M2Q3MGI0MjRkM2VmYjg0OThhZTMzOThlMmM3MjQ2NzFkOWZjNWI1YzNlMmMyMzI5ZDIxMDdlM2NjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkhLSDZXNWdiZVFwUDJ4ZXBQb3djL2c9PSIsInZhbHVlIjoicFN4bldQMmFLQm9pV0hzWlEvVDRiVnhZYnFTMlhqS0JNUnk4MHhsRGVYRjhRTzhVSEIycXpobWZ1WWpDN1IrSHZBTnhlVGY0NTNsbDRDTEEwb1prNDFHRVUvbGEzVWl0TWdMN2czM05QSVgvNW9CMnRiWlcxeGoxRWVtQVZQOG1sblNWcERmLytMUzN5anM2V3I4UG1uUWhWWUt3UEFWeklVdUF3NEwxb21BQzZaTW1rNFl4K0M5VWtLclN0RStvRElDdVVGQnYvcy9PdUdhaXJkSFg1VDNvRjA1dFM4NHhLU2tweE1FVENBdzhMaUIzVGxIeThkU3M2NzIyWGVjeHpoZzF1ZEg4ckJZOUE4Rjd2SzB0OGlQRHgrL3hEdHJuRlg1Y3NFb3RyRWR2a01YTDJCSUZzbDZwVnpJckFsMWlVMEcvV3RmOHVaOGtSS0lNT2tEN3RsNy9lcnV4Um1CTHJ0S29rekw0UUUxeE9UeElmSXdrdllDanFWWEZKZWlkdmpXalpUOThaUVlKYjJrT1pjNitnNnYvdDJydisxbjBreWQrYmhNV3JQcHlHKzZwQ3cwY2Q5TzlNNW5DMEZXb3VzZHFIdXRRMnp1bWdmZ0tVNW9Oc1VvSWNoaGdGNGcyTzZvV0FrallFWlVhYkdyZ2NJUlFsVHdaRHdjK3pCN0giLCJtYWMiOiI5NDYyNWZiOWYxYjE3YTc4NDk1MmYwZWNkNmExMDU0MWYwOWUxMWJlYjgzNzg2ZWQ5MTA4ZGVlOTExNTc0MjRkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}