{"__meta": {"id": "X21750f5182c3497d1255310b2699744e", "datetime": "2025-06-28 16:21:49", "utime": **********.443227, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127708.995039, "end": **********.443247, "duration": 0.4482080936431885, "duration_str": "448ms", "measures": [{"label": "Booting", "start": 1751127708.995039, "relative_start": 0, "end": **********.385529, "relative_end": **********.385529, "duration": 0.3904900550842285, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.38554, "relative_start": 0.3905010223388672, "end": **********.443249, "relative_end": 1.9073486328125e-06, "duration": 0.0577089786529541, "duration_str": "57.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45718512, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00266, "accumulated_duration_str": "2.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.412154, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.038}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.422538, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.038, "width_percent": 16.917}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.428036, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.955, "width_percent": 18.045}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1889401326 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1889401326\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1223649921 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223649921\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127705801%7C44%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdTMDFNc0dOZ0lQTDBRZndQVEpDS2c9PSIsInZhbHVlIjoiWFgyR3RUQm1JMElmNVlIVmlPbVZzL29ELzRjb3BacHhGREx4NlFVL3o2UmpyOW42T0ZtZ2NPQ2hHL2dURzk4MHpZbUlmUVNvaXRjSHFzZ2hxUStCQi9SK2NxRWxaeHhEVzM1cUljTGtqMHhMNytmY0hNaFNUekJnRkErRWRqSUVtNEMrVnBUUHpCak90SS9WT3cwKzVnSjk3ZlJVMDZGeTdnYVh2K2x1eG1kYlZraDMvd1FaRzhSTkdmSEFlTkpTeE5LKzdsTm1IKzU1eTZ6QXhuUnZvQTlZdXhVcENyeks4TXpyb2JsQk5UaWF0NTFFcG9mbXRzeWZWdjZISWlwOVljejFTbkNVM3ZITktNSUFnQ1lOSklVdVo5T2orcWNnYWFqQWpPUy9aNEtXMXc0NzRORXJpY1JTSElIZW9yRFdvM1RZSDhTRFQ5dUdOSHV2eTJYeUhaU0U5V2J1NzVXRGtGKy9IVVRvZTRQN2Yyak14MXgwL3VqKy9CK3N2RzUrQnBqZENMd0JKeC9CUnpRcEdXY2luYWx5ZUFxa1RaSHBPL1hoMFkzNFhyVGZPRWhjN2tNamFPbUxKTGN2NURDenRTMmUyVXpPb0g3cDNEZVdFU0dJdlcvaFoyOSt3RWt4VTR2ZkZUTGxmUWVhZFhIeWJmSWdTbStjUFNUQzlyeS8iLCJtYWMiOiI5MTAzOGE5OGVlMThlY2U0ZDI0NTc4ZWI2NzJhOWQ1MGYwMzhkY2U0YzkwMjQwZTY5NTQ2ODhiMTNkMmVmMzBhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldNZUZsdVJOSVB1VzgzNEh1M0U2Smc9PSIsInZhbHVlIjoiTUV0K01Sd2Q3MXpNbTNFcE9PM1psby9kcHloaFg2NldFcHFlSm1nT21QTTM5aEJBdFJ0bkJXc1BwQTFZTDFhaE43c1BILytpMWhBWXB6bGRzUlNQY0I4bFBPWkwzOVcxSGFnK3E3NGpKU3RSeUVjOHJTZ1RZNU5GZXIxdjlvaXd5RmNkS1Zxb3VqOEorYUcyRnhTTDNNODhIMmdOR0ZDRXRMSHJFRDk3azlIMDlML05HMWwreE1ON0FPUjB1bzRaVlF1bTRjWjVqTVBqU202MWtxMGRFa2ZTend0bis0TDVSNzJCdUNLbTR0QWtUaHVMcVhDQ2VncmhyazQ3b1FoZWkxNDhYUnFQODl2S29VTjhOUkdjOG1qOTZCcE1yaFh5dUhubkppcjlRUmdzMnpnQ3paejVLTVdGbHZJUmwwSWhBWHB4ZlJSK3g4L0x2MDZvUWlyNnZGYjVuanhSRU04WUlpblNzb2RSdlY3QjlnZ2J5aTRreUNEOG96WTgvZ1FwRGRlS3R2YVdKZG1YcHN4NUp2OEtjVEVtMCt1eld1QVdZV2NrNE5aWmZjalV5ako4Mk9yUmlrRXRWa2VMTHJyNG9nS0VFT3B4bUlkaE5MZWxqRGhFeStlcm8xWC9NeU10UHA3MEw1Rks5Z2VpazZ6MC9WNUtEWGdwY3d4SGdMZWMiLCJtYWMiOiJkMDQ4OTBmNjNjYzc2N2Y5ZjcxOTc3ZDIzMGZkYTZhMWE4MjQ0MDE5OTU2ODRjNmRjODI2MDlmZmZmNjQxNjJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1572334996 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:21:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRxNldTbXJjQndKVE5NRE9jRWZyRGc9PSIsInZhbHVlIjoiQTVwdVAvaFFWWHc4cGdlaHZyQzZ1RkRJaWtCMGhFbUk0UXVzUG5xRUZaY3BKanRmRmllSjVqbWdOWWFRUmFiQXBDLzRkbXF3Q3p2S0JHbmdsTktNZ2UwK3oyanpMSnk4RjRIUEZVSTVTb2dDOFc5WDR4Z1UxOXlYZEFxY1E1U1E4UkZsS0xNaFp0cTBSUkVIVVd3djZ5SHJnZHJXR1V2QzYxL2J3OUUraUIwZXI5QzFoa0dKUWJOUG5VUmYxQmlkMm5VSzlnWHlmUzZ5NWlmYTlxWk5jQ2htS2c5WjJjRVN4WFVGMjgvem5FKy8rK25uNWJQbW0vOHJhR3dUMUt3VTNlSlVrYXlOZGcwMXZlenFoWGNxcVdLRTdpbk9YaVFBL21xWjlpaXpwSG14dDZ1Mkh1Z1NaY01UYTRGM1VjZVJkWm82VVc0NjhFOE9vcTh2MFRrRiszQlF5OWdQdkZPbFh2VHhheElzMW94cENxaTJLbFkrWmN6SmVFU0hoaDNOQnVrQTE0UGxEUW9OOGlzQTVoQ2tiVHFEaTY5a2diUkRseFEvOTR0bVY4b3grcS8vaTgrblNUbGw4VjVVeUgzYnR1TXFaVWppalNPTmtDZGg2aGFhZGdXdTVQcVUvRlFaUWJ3eng0ZUFVb2FNUWJuRDRjYXhUNm10ODhBU1JVZTciLCJtYWMiOiIyMmI5YmIyZGU5M2FlMTM3NWY2YWEyMGU0MGFiYWViODhkZDZiODc1ODE1MDYxYTE3ZmFhMWQ2YzkyYzBhN2NkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBSZXYwVVF5OS9aSzk5V3FrWHRDUlE9PSIsInZhbHVlIjoiVklWQ3dZZVBlMmpVTVlLOHAvbzJQYk41MTE5ZFZtaVFHSmFXTEVXS1JzWld3UE5ldmcrWDFMMTdnN3RBeW90V0w2ZE52R1ovSVAraGJrNW5ENVhvOG04Nk85cm1FemthTWZlRHVYbkVsbVIxRzZVNkF1MDk1M3QvUC9oMGxVNWFKS1J0Ri9QU3lEQ09WVjVQV1dmNTU3SWx0RmM4N3dCaWE4WVZkVEhwSnJkMlhNd0xQdXBFa2dFUW9ubGU5QmlNRjBBcDhmOU9RZnNQQUxaZCtBTkhYUUxLQ2gvQkhwcGtEeGRUUGczRnB6RHhZcmtKYnZmVktnemlKTlhsaXlXRTRyQnd5S01HRHBabTBsTVJ4bW1UaXVyVjU2eXRUaW41bHlmM2JzYk44L25McVNYeXo0SFR1SjNka0RSK1pielJKWFZydlNSWjcyOWJqYStZM3E3R0ZhdWFsSmxzUzNiWDA2bDN4bnpTMERPY3VqQXFRWlZIc3hGemJLcjJnRFYybFhaN2NkVXVpald2b216b0UveVJoQ3JKQTZTQ1pNZEhFelVVMUg3NFF1MEREdG9TYnJVM1lzRFdpRnd6MTArMFYxdURJTm1TTTVVUVVEa2VpS3RjSG40SUk4NkJNSlJCZS9lZE1JRlo1NUI2T3drZnZPY1M1dFB5amYxTzc5TnciLCJtYWMiOiIzNzI1MDYyYjAxNDM5NGE3YmJhY2Y3MzU5OGQxNTk5NzIzMGVmMTQyMjBmNDc4MzJhNWU5NTc4OWQ3NzU2OTRhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRxNldTbXJjQndKVE5NRE9jRWZyRGc9PSIsInZhbHVlIjoiQTVwdVAvaFFWWHc4cGdlaHZyQzZ1RkRJaWtCMGhFbUk0UXVzUG5xRUZaY3BKanRmRmllSjVqbWdOWWFRUmFiQXBDLzRkbXF3Q3p2S0JHbmdsTktNZ2UwK3oyanpMSnk4RjRIUEZVSTVTb2dDOFc5WDR4Z1UxOXlYZEFxY1E1U1E4UkZsS0xNaFp0cTBSUkVIVVd3djZ5SHJnZHJXR1V2QzYxL2J3OUUraUIwZXI5QzFoa0dKUWJOUG5VUmYxQmlkMm5VSzlnWHlmUzZ5NWlmYTlxWk5jQ2htS2c5WjJjRVN4WFVGMjgvem5FKy8rK25uNWJQbW0vOHJhR3dUMUt3VTNlSlVrYXlOZGcwMXZlenFoWGNxcVdLRTdpbk9YaVFBL21xWjlpaXpwSG14dDZ1Mkh1Z1NaY01UYTRGM1VjZVJkWm82VVc0NjhFOE9vcTh2MFRrRiszQlF5OWdQdkZPbFh2VHhheElzMW94cENxaTJLbFkrWmN6SmVFU0hoaDNOQnVrQTE0UGxEUW9OOGlzQTVoQ2tiVHFEaTY5a2diUkRseFEvOTR0bVY4b3grcS8vaTgrblNUbGw4VjVVeUgzYnR1TXFaVWppalNPTmtDZGg2aGFhZGdXdTVQcVUvRlFaUWJ3eng0ZUFVb2FNUWJuRDRjYXhUNm10ODhBU1JVZTciLCJtYWMiOiIyMmI5YmIyZGU5M2FlMTM3NWY2YWEyMGU0MGFiYWViODhkZDZiODc1ODE1MDYxYTE3ZmFhMWQ2YzkyYzBhN2NkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBSZXYwVVF5OS9aSzk5V3FrWHRDUlE9PSIsInZhbHVlIjoiVklWQ3dZZVBlMmpVTVlLOHAvbzJQYk41MTE5ZFZtaVFHSmFXTEVXS1JzWld3UE5ldmcrWDFMMTdnN3RBeW90V0w2ZE52R1ovSVAraGJrNW5ENVhvOG04Nk85cm1FemthTWZlRHVYbkVsbVIxRzZVNkF1MDk1M3QvUC9oMGxVNWFKS1J0Ri9QU3lEQ09WVjVQV1dmNTU3SWx0RmM4N3dCaWE4WVZkVEhwSnJkMlhNd0xQdXBFa2dFUW9ubGU5QmlNRjBBcDhmOU9RZnNQQUxaZCtBTkhYUUxLQ2gvQkhwcGtEeGRUUGczRnB6RHhZcmtKYnZmVktnemlKTlhsaXlXRTRyQnd5S01HRHBabTBsTVJ4bW1UaXVyVjU2eXRUaW41bHlmM2JzYk44L25McVNYeXo0SFR1SjNka0RSK1pielJKWFZydlNSWjcyOWJqYStZM3E3R0ZhdWFsSmxzUzNiWDA2bDN4bnpTMERPY3VqQXFRWlZIc3hGemJLcjJnRFYybFhaN2NkVXVpald2b216b0UveVJoQ3JKQTZTQ1pNZEhFelVVMUg3NFF1MEREdG9TYnJVM1lzRFdpRnd6MTArMFYxdURJTm1TTTVVUVVEa2VpS3RjSG40SUk4NkJNSlJCZS9lZE1JRlo1NUI2T3drZnZPY1M1dFB5amYxTzc5TnciLCJtYWMiOiIzNzI1MDYyYjAxNDM5NGE3YmJhY2Y3MzU5OGQxNTk5NzIzMGVmMTQyMjBmNDc4MzJhNWU5NTc4OWQ3NzU2OTRhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1572334996\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1736390141 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1736390141\", {\"maxDepth\":0})</script>\n"}}