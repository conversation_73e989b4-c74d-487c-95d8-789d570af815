{"__meta": {"id": "Xa74666677ccdaa54f8cbc0324dea440c", "datetime": "2025-06-28 16:03:35", "utime": **********.023595, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.492965, "end": **********.023609, "duration": 0.5306439399719238, "duration_str": "531ms", "measures": [{"label": "Booting", "start": **********.492965, "relative_start": 0, "end": **********.883573, "relative_end": **********.883573, "duration": 0.3906080722808838, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.883583, "relative_start": 0.39061808586120605, "end": **********.02361, "relative_end": 1.1920928955078125e-06, "duration": 0.14002704620361328, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50388488, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.07217, "accumulated_duration_str": "72.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.91981, "duration": 0.01401, "duration_str": "14.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 19.412}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.945431, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 19.412, "width_percent": 0.693}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-28 16:03:34', `closed_by` = 22, `shifts`.`updated_at` = '2025-06-28 16:03:34' where `id` = '52' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-28 16:03:34", "22", "2025-06-28 16:03:34", "52"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9565501, "duration": 0.054729999999999994, "duration_str": "54.73ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "kdmkjkqknb", "start_percent": 20.105, "width_percent": 75.835}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-28 16:03:35' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-28 16:03:35", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0129771, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 95.94, "width_percent": 4.06}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "success": "Shift Closed Successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-700809954 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-700809954\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-800601451 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-800601451\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-346458594 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">52</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-346458594\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1099741644 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126613193%7C18%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9wTXdMK0toSkNhZWF5bXM2alJPNlE9PSIsInZhbHVlIjoiNlZtc0NuMTFYaGhFWFRiRGx3amhjRGQ1Zlk2cUppSG8wdllWU2VoV3NEaWtkK3ZxR2ZmbndhZlpzVFVUTnpWZHRrQVdxZW00dzV5cWx4V09aOGlrN281NzI0dTNIdjMzMml5dDJxZkFFT2lhK2gvR2Q0TVBqZFFsNjlYVk4zSkNUUUJpTjdSM1k4cXZBUy9HbGRpeDk1MDB2TmwwNVpJMDV5QVh5NGJsKytRRXpQdkloeHpxQi9GaHlQWjF1cUpzNEpFSGVhQmpMQkpLckt5NCtqQXppR0NJVkF2MytucG9pMTdRTWZiSFh2T20wc0hnaTc0YVEvVS95dmZUeGo2elU1Y2ZsWDNnTGhFTXNCY0RyWjcwZy8za1dQeFpZQU5Yd281YU5ZT2Izd1M3dGxGMkkydyswZXoraDl0Ri93SzB4c0JoVEk1Z0tJRW5uSmQvRkFEV0VLLzNvODI0OWhsUitPZnRlTWJFNjUwTXBFdjVFbkFka0lJZDhiVWQ2ZlNDOHRwUXZFdDBZSTdBTmJmL0FqcEhuSFFlY3JyVmNtSkxvc2wraUlqUWdySklKcUk3d1dLUW40OFJEaWMwTk1lby9jbUFUelFJM3pOb1l5eThCZlJFVkFaOTI0a0JXWVlpWGt4S2ZkTENid0RJcDdSRkNlZFN3S0dCWXVxZkNPNmkiLCJtYWMiOiIxYjU5NTc5ZjFmNGExNGUyZTRhOGFlOWQxMTk3NjJmNDE4NDQ0NDczZDQ1ZTU2Mzc1ZGJkN2YxZjMzODRmOTcwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1SRm1JaWRJWVdnRWQ1Ulp1VFhCUWc9PSIsInZhbHVlIjoicHpmcjgzQ1ZWaWVUaU5lajBQLzVBN0xkZ1ZFWGk0MU5SQ29yVWp2S3ZSb20rNW9acVpFWHgzcUY4MW0xNzRwSnFET2FiY2tKZUJSdnZ6MmM0bmFLSEhGMlp5SEhmdVNEVVJGbVRPSk5zbFF0aWVGRjE4VlhJekNoSk1Xd2NPU081MnU0c1dnR09Cem13bzV1bHdUOUc2ZlZQcnEwdWhKSklUSEtNUk56b1d2TFhTbWp1NTltNVJkU1BlQzZyVGdEQnZKc1hyM0Fsc0FQZHpTblloZ05NbFU0OXFjUENGZGVhNVIxMWxVOFFuZTRUSjNHTnRrTDU5Z1Z5MVFuaXAyWkQ2Yytqd3JkdjdmVGYxQ3dIbC9HQlk2NmVlcGNFcEFvL2lkUlFMbEdPRlhBT2orOW9WWXpKME1QLzc3RVphbWpjUThyYUlWWk5MZHNUOGFpU0ZSMTVDTjk0SU9SN2RoVHFJOWZYVDEreENIT0N6RlUwZS9VOHF3NzFRTFhKb1JaL0RqY0I2T0lZd1orckV4UmJkdFJubDNzQ3JwcE9Va3RvMW5zSWlLZ3BsVW5xby9aeXFCanp1MmhiK09PcmdFYmpjZzcraGpqWnpVSkIxemE4Vm9XVHMwSitMb0JWSWQwanNaeEVCNGVidVlYWk42RGpjRlJ3QlJpd0J6azRwd3UiLCJtYWMiOiJlNjE3MTJhMDU4YTVhZWNhOTJkNGJmNGZjNjJmNGExMzY2ZDgwZjdkMmI3NjIyOWRiM2ZlNWZmYTM1NmViOTA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099741644\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1647551626 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647551626\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-467704371 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhTTERtelprdWNMQmNUdjVSVUpLdWc9PSIsInZhbHVlIjoiSHNROGVVdTdubHdSN3F0SGxyTkV0S0REMkFud2RMNTlIbnVNdUZ3KzZyVFlqVUk5ZG91YnBXQ2t2Y1dlVmQvaVloN3YrZkVUd0R6eVBPa1U5TDlaOWV6S2c1L3lERy9UQlQ5eFpHTnV5dHhsc0t5cGJzN1FFTnB1Rmp4Z2RDZDRGbWs5RU1sV3pIdUl0ZldWS20rRWdNbFdXNWxsQW40LzRtNkJKY2xMQWpwK3JYUHZXNm5wcWNqeG5BNHVMQVpNM3YrbmFrWk1OWWhUN0JRMXNtbUM3ZXlWdVhCc1JHdFFXLzZrbWZjVVFXMGt3L01XbjZOVjNSZXVHY0RXV0daNGMwcDdEdVlRbnBYOHRyWmZCclVXbnpZL0crVW5saCs0eHIzY2oyM1VmT0E1NVl5MlRJM3JQaHJMa1VXYm9mRmdUYXJqNVJpd2c3NDhmM01wbXhGZENUWnVob2kzOG1xQk9WREE5a012UzVZSmJDRGV4OTZJZ1lKRHNtcWs0Z2t3MThKRDMyVlVKZmtqOEFoblBWNUIxaDhnUldlSmlUbmUxSU5OeHBrMGtEZkxJYXZXenRLcVE0WnhDUXpEVytWdlBhNjZzaGt2bnRIaTRJUnpqTGoySWxkTThNZDhKOFh3c3dHZWpRUklkZ09kaGRLQjBCTjFDVVlHVGRaM0xJWHkiLCJtYWMiOiJkMTg0Mjg5YTdhNGIzZjE5ZDk2MDVlZWJlOGM5ZTc1ODJhNDI1MjQzODFlMGUwYmJkYTU3Mzc0NTZmMmRiOWI3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitMNWhWa3g0YjJOZWVqcnFlUk5SbFE9PSIsInZhbHVlIjoieGhoVzdRbng0WGN5TEN2ZnlCbHJ5ZVNxZEVaRjlicHpDQ01Wd1Fuazc5UGplWmJ5Z1l5dmdjUGEwTWlOUUQyb1VPbFZwdmkveURHY29yb2xTUTZJN3BjMnZLQW5VVjZxWU5tbDNJTmN6WFpvVFNEcndlWUJNSitUMENWOVROWmxyQ21jR2F1RDdkS2o1QXU3Vk1UVFdocHc3YTFRa0RrWWsvUysxbGR4QWVydUw4Y25pd2ErS1B0R3VjZTJDRFNJMVlSMm5ySVpIQlhycFltUlhOaytZejJjMGpVSzdONVMzSFZiM3o0TGxTNEwwcXhLbVExajdMMzNDM1ZFLzZSbjJWWjZMeTY1cjlDYUxmOHJIeGhGUVI1RzZtSWY5V3JXRFZnekhyYkVLVkFNZ090OGJWSEZ6M1NucStwOW90QU5CM1lwUzBwYkdlZkNaMFViTzRwc0hpK3J3aVhsUTArTTh6Nms4dW83OTE5NTNzb0plS05MaXZqMzJ1OFZlenkwMDFQY1lMZ3JPbUp2TjBUWjJBOHpuUFpUMDVyWi84YnVyWGVUb1FyVFBoQlM2RkVRRWVJdnVKWmlBTUFxOG9ob25OZk5USXFPMnpXR2NjS1hvUnI0ckt4UlJWc3l0T05BWllxdUNJYVpWcm9qN3pFVG9wTjM1U2grZUR6YU1uSVciLCJtYWMiOiI4YWY1NDcxNjIyNWZjZWFiYTkxNzEyYWM0Y2RhYjYxODM0MTNmMzcyZGEzMDQ2MWMwYTY1Mjg0YWZiYjZkZjIzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhTTERtelprdWNMQmNUdjVSVUpLdWc9PSIsInZhbHVlIjoiSHNROGVVdTdubHdSN3F0SGxyTkV0S0REMkFud2RMNTlIbnVNdUZ3KzZyVFlqVUk5ZG91YnBXQ2t2Y1dlVmQvaVloN3YrZkVUd0R6eVBPa1U5TDlaOWV6S2c1L3lERy9UQlQ5eFpHTnV5dHhsc0t5cGJzN1FFTnB1Rmp4Z2RDZDRGbWs5RU1sV3pIdUl0ZldWS20rRWdNbFdXNWxsQW40LzRtNkJKY2xMQWpwK3JYUHZXNm5wcWNqeG5BNHVMQVpNM3YrbmFrWk1OWWhUN0JRMXNtbUM3ZXlWdVhCc1JHdFFXLzZrbWZjVVFXMGt3L01XbjZOVjNSZXVHY0RXV0daNGMwcDdEdVlRbnBYOHRyWmZCclVXbnpZL0crVW5saCs0eHIzY2oyM1VmT0E1NVl5MlRJM3JQaHJMa1VXYm9mRmdUYXJqNVJpd2c3NDhmM01wbXhGZENUWnVob2kzOG1xQk9WREE5a012UzVZSmJDRGV4OTZJZ1lKRHNtcWs0Z2t3MThKRDMyVlVKZmtqOEFoblBWNUIxaDhnUldlSmlUbmUxSU5OeHBrMGtEZkxJYXZXenRLcVE0WnhDUXpEVytWdlBhNjZzaGt2bnRIaTRJUnpqTGoySWxkTThNZDhKOFh3c3dHZWpRUklkZ09kaGRLQjBCTjFDVVlHVGRaM0xJWHkiLCJtYWMiOiJkMTg0Mjg5YTdhNGIzZjE5ZDk2MDVlZWJlOGM5ZTc1ODJhNDI1MjQzODFlMGUwYmJkYTU3Mzc0NTZmMmRiOWI3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitMNWhWa3g0YjJOZWVqcnFlUk5SbFE9PSIsInZhbHVlIjoieGhoVzdRbng0WGN5TEN2ZnlCbHJ5ZVNxZEVaRjlicHpDQ01Wd1Fuazc5UGplWmJ5Z1l5dmdjUGEwTWlOUUQyb1VPbFZwdmkveURHY29yb2xTUTZJN3BjMnZLQW5VVjZxWU5tbDNJTmN6WFpvVFNEcndlWUJNSitUMENWOVROWmxyQ21jR2F1RDdkS2o1QXU3Vk1UVFdocHc3YTFRa0RrWWsvUysxbGR4QWVydUw4Y25pd2ErS1B0R3VjZTJDRFNJMVlSMm5ySVpIQlhycFltUlhOaytZejJjMGpVSzdONVMzSFZiM3o0TGxTNEwwcXhLbVExajdMMzNDM1ZFLzZSbjJWWjZMeTY1cjlDYUxmOHJIeGhGUVI1RzZtSWY5V3JXRFZnekhyYkVLVkFNZ090OGJWSEZ6M1NucStwOW90QU5CM1lwUzBwYkdlZkNaMFViTzRwc0hpK3J3aVhsUTArTTh6Nms4dW83OTE5NTNzb0plS05MaXZqMzJ1OFZlenkwMDFQY1lMZ3JPbUp2TjBUWjJBOHpuUFpUMDVyWi84YnVyWGVUb1FyVFBoQlM2RkVRRWVJdnVKWmlBTUFxOG9ob25OZk5USXFPMnpXR2NjS1hvUnI0ckt4UlJWc3l0T05BWllxdUNJYVpWcm9qN3pFVG9wTjM1U2grZUR6YU1uSVciLCJtYWMiOiI4YWY1NDcxNjIyNWZjZWFiYTkxNzEyYWM0Y2RhYjYxODM0MTNmMzcyZGEzMDQ2MWMwYTY1Mjg0YWZiYjZkZjIzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467704371\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1549613082 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Shift Closed Successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1549613082\", {\"maxDepth\":0})</script>\n"}}