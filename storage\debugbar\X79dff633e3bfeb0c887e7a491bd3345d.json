{"__meta": {"id": "X79dff633e3bfeb0c887e7a491bd3345d", "datetime": "2025-06-28 14:59:02", "utime": **********.353957, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.921733, "end": **********.353973, "duration": 0.*****************, "duration_str": "432ms", "measures": [{"label": "Booting", "start": **********.921733, "relative_start": 0, "end": **********.256675, "relative_end": **********.256675, "duration": 0.***************, "duration_str": "335ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.256684, "relative_start": 0.*****************, "end": **********.353975, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "97.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0031000000000000003, "accumulated_duration_str": "3.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3219872, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.332322, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60, "width_percent": 17.097}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.341515, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 77.097, "width_percent": 22.903}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122734939%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhvaTMwSkJTczFRbEJpMW1EdzlNWEE9PSIsInZhbHVlIjoiSlVOZEt5Z094czNtWDRmeFZqd3ZreU9NWHlKUmRBcHE0RmRNb0huY2IvSldtQitYSnpTeUwyRnF0Mm5qakEySXVQSEhuVExsRXlCTVUrZGN3T3oxV0RJVVoyai94SmViYTFqT0RwM1FyeGhjUVR5TnVmWTJSK0hGUXpiN3J4STFCY0cvTWRSaytRdE9RUHo0dWQyaVM4SSszeklmZ1lQL0tGall5cXhhSS9qWDRJNnhaTjdweVd1WGMxZW55ZC9SK2xHYUl1bkMvVnpIVkZYR2NqSGFOaDh1cFJJR21DSEF6aWx6VmJSRVlXc1hhVE9LaVNWRzBuL2JNNFNVMElCV2lRa3F6SkdEUHgzSDZQWVgrNmlqdEZaS3BjUmd0ZmI2Y2hTc29UYXNXanduZGxSbVVlQzgyNzFYbDkyU3Y5MkZtOVZ0N0M1ZjdtQW5SRGdnZE1zQzAzUGVSbWg4ekl2NVM0enlMSTNnOHNlb1dtQ2VvZHRpSU1uN3BVc0k3c0hCT04rN0RrS0k3MnZNWGxoTlYzcCtSQjFHUGxvNHlwUVE1ZVBiNlYwVGVJckFEWDZ5M1p3bTJEUExQWG5uaDBhSGh5WGs0bERlallZcjNVOTdidHUraTJpYm5sZHFBOW9YQTFOSzlzcXJ5SFplOVovNkdkc2Jvb2NiU09Eb00ybGQiLCJtYWMiOiJlOTNmZmM3Y2JhZWI2NDM5MzM1MWU1YjFjOTM4Y2JiMDI4N2JjZTA5MjZiYjUyYjZhMWJiY2I1MWFlNzI4ZDdkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJMVTU2ZG82dHBGYjFnR3FJWU84Umc9PSIsInZhbHVlIjoiTWkyWlJDblZ4NzlycU82Q05DTThpdUVJWWNWZ0cwdmpwVk5aZkRvNlZybzJZOHVKUmhtZHhSQllTV3BZUjhXUzB6UmxubC82MmhvaEorLzhjK3ByZjNlNUxmNlhUWWZ3OWh1YVZ3OSt0WFpVY2FHUUtPbmcyNWxhVTN2L1pINlJzSlhBMHpBaDVTVnpVNnpKVFdTTW80N2lreHYxWWhvRWZoNCszQjVPVFk0b3lzM21uYXExWDF4Y2FjYzBSd2IzRzhtZXVzVkQrajROK3BNMnZRUjZLUjFLZDFlTDhIRStMTVd0L0loR1c5VDAxQ01vakFWTE52T0dmLy9rMDVnaG83Y2JNcklQcGhvUndJbFhiU0k3QnFuWmtoNzJzcWZaR1U1MGxXYkJOMGMvckZQTVZCVTJnVExaVENpbzYwQWxHMGpjWllKK1NaaGFXYkU3UUZNTEZLRHlJWnJDTjFhZDVHRFlNRXpPbDljZ2tRYkhGalZQME45VlZWSEpjcmZmbGJ6RHUrNjZvbzY3b1FCUldDQ2srQ1V2MWNibWxSMENrRXlOcHBaTVVrdyszd04wZUs0ZzVkdUwwNXJRU2prVlJqQmhPakRRc0xsNUVXaWtNOWF4eHlrV2VyS0FMdi9LaVhWaG9FZXdWdVlFdFVhV001TVY0dXhKOG5iNnVBdUEiLCJtYWMiOiIyMmQxOWZkMWM0MjYxYzliZGY2MTM5ODgzNGUyMWNmNTVhYjdlZmRkNDcwZWFlM2FkNjgyMTNjNWIyZmQyZThlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1871370547 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6nyGbgT6n8t00JbJ5CSgj7vkpXnxnyISs5XS6j6J</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871370547\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1638591149 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxKWGg1SDBHcDMwbU5nTUNmV1VaRUE9PSIsInZhbHVlIjoiOU5WNUFGeS90K1FHeUtIMFVSRnIvMjJFOHNwRXoxSlFXSUU2NHo2c3JnSC9kVjFVNWdORG9EUW16bmJJNzFrRi9ucktNeDh4NWI1Qm9Jd3JkSExUeGZoa0lMNFpCbHJhd1dRWEFsbU4vdThpVEFmem4xQnl0OHZwYVJnTDJHRVdxbmJTbDFyeElYT2EvTTBYekhQTW1xa1c4M0ZyUFVaUFBsSVBGczhIT1J1Lys3RndaeGw1a05RUkE2Sm9CRVBpbFFDeFFoWk5sZWh0TFZyaXJqaFFnTUFONVlLS2tGbnl6ZnBzdERUajNCL1pQeENrNFU0YTBlVURjVlltTWUyaVNWOW5IcUplajdvKzMvS09NMkdTMEd6WE5lTkYyek14M05LZ016TXdOQW1La0hvWnpyOXRSQTVycnNKSEFnclB3UzFkVExzNDd2MUtBV1FiNWxHRzdqYTlCS3V6TnkwYllkN3o4VkhKR21CcTY4MW5Bd0x3SDIvKzl3ZURpTWRSZGwxUmxmWUUvRDZPaUh2UmQrc3RyeFNWNC9GbHBjRzRwZ2xHZFRoQ2RpR0x1aEV1MzlaS0ZlWTc4N0RKamxhazZOQTFoVHRDMUtEeWxJUWZjaWFuYnc2VHVXK21qdVNLNWN3MlZ3U2FNV0VOSnFIeTNUMXdybEVPQVBRVmxqQUQiLCJtYWMiOiJkZjhhNzMyNzc4MTMzYWFkMjIwNDI4M2NjY2MzNGZiNTA4OTFjMzkyNDA3Y2IxMTBlMTQ3NjEyMzM1MDYyNjllIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNvQ0gxTEV5OVJ5bmZWR0NuT3p0Umc9PSIsInZhbHVlIjoiNDZ3UnNWL2xnakV6cVZXbk9XWktKeW9BNzhnbW13VWJ0L3pyM3d2MEViMXVxTzg4MWI4T0FJbE5CVkRJRm03K1EwcmFRZG9mSzdNNkhXMENoNkQxWFlwTWNtcUh3QTR1b3lIOHFNV2RLaHMxa09jVUdJV29JWlFrTjcybXpoK1FLQmdsaDk4ZWFFV1B2QnFBNEdKcGJFSnQzbzRGa2lGWVZ2YnpnR0p0MkdvT0RWNDEwK2ROanVTWnpSN09oajBDVWExU2grODkya3cwVnFuaWN4YUZzUTc2V2NHaHpKeUdPTVR3N05ra0lYVDFGL3IyNndrTTh4TW03ekd2YndYRDdHMHdqTUtYSkc0SHJtOGQzQUhOVCtpdmZoZSs5S01DaEJpNXN1SlJUZzhOUGJXWStUNmQ3Q0g0a0gvalpIREgwaHhCZysvcWgvWGFzK3RabGRncE03V25VOUZwS3RJUHRsWnNrOTZyNFgwekNxK09JbkN5Y3UzU0YzdS9FMUhwazNnVGt1RFExWktkZjJyaDhwRFZwMW5PbWdsYlhBK0I1YXZLc3QvcGF6bzRjWjJlWGVoQ29YZ0NqeEpPdXNlNis1NG5GTldHeU0xVForWHd4L0E1OXc2SlR6NVdQYi9UOHVxM0NEWVhkUjExdXlCcDQ4MXIwOXlpSldhVUM3MnoiLCJtYWMiOiJhNDRlODEyNTNhOWUxZTZiMWQ4N2E2MTJjMWMzNmI4Y2ZmN2VhY2IyN2U4MGJjYzRmYWQyNmRmYmZkZGZlMTE2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxKWGg1SDBHcDMwbU5nTUNmV1VaRUE9PSIsInZhbHVlIjoiOU5WNUFGeS90K1FHeUtIMFVSRnIvMjJFOHNwRXoxSlFXSUU2NHo2c3JnSC9kVjFVNWdORG9EUW16bmJJNzFrRi9ucktNeDh4NWI1Qm9Jd3JkSExUeGZoa0lMNFpCbHJhd1dRWEFsbU4vdThpVEFmem4xQnl0OHZwYVJnTDJHRVdxbmJTbDFyeElYT2EvTTBYekhQTW1xa1c4M0ZyUFVaUFBsSVBGczhIT1J1Lys3RndaeGw1a05RUkE2Sm9CRVBpbFFDeFFoWk5sZWh0TFZyaXJqaFFnTUFONVlLS2tGbnl6ZnBzdERUajNCL1pQeENrNFU0YTBlVURjVlltTWUyaVNWOW5IcUplajdvKzMvS09NMkdTMEd6WE5lTkYyek14M05LZ016TXdOQW1La0hvWnpyOXRSQTVycnNKSEFnclB3UzFkVExzNDd2MUtBV1FiNWxHRzdqYTlCS3V6TnkwYllkN3o4VkhKR21CcTY4MW5Bd0x3SDIvKzl3ZURpTWRSZGwxUmxmWUUvRDZPaUh2UmQrc3RyeFNWNC9GbHBjRzRwZ2xHZFRoQ2RpR0x1aEV1MzlaS0ZlWTc4N0RKamxhazZOQTFoVHRDMUtEeWxJUWZjaWFuYnc2VHVXK21qdVNLNWN3MlZ3U2FNV0VOSnFIeTNUMXdybEVPQVBRVmxqQUQiLCJtYWMiOiJkZjhhNzMyNzc4MTMzYWFkMjIwNDI4M2NjY2MzNGZiNTA4OTFjMzkyNDA3Y2IxMTBlMTQ3NjEyMzM1MDYyNjllIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNvQ0gxTEV5OVJ5bmZWR0NuT3p0Umc9PSIsInZhbHVlIjoiNDZ3UnNWL2xnakV6cVZXbk9XWktKeW9BNzhnbW13VWJ0L3pyM3d2MEViMXVxTzg4MWI4T0FJbE5CVkRJRm03K1EwcmFRZG9mSzdNNkhXMENoNkQxWFlwTWNtcUh3QTR1b3lIOHFNV2RLaHMxa09jVUdJV29JWlFrTjcybXpoK1FLQmdsaDk4ZWFFV1B2QnFBNEdKcGJFSnQzbzRGa2lGWVZ2YnpnR0p0MkdvT0RWNDEwK2ROanVTWnpSN09oajBDVWExU2grODkya3cwVnFuaWN4YUZzUTc2V2NHaHpKeUdPTVR3N05ra0lYVDFGL3IyNndrTTh4TW03ekd2YndYRDdHMHdqTUtYSkc0SHJtOGQzQUhOVCtpdmZoZSs5S01DaEJpNXN1SlJUZzhOUGJXWStUNmQ3Q0g0a0gvalpIREgwaHhCZysvcWgvWGFzK3RabGRncE03V25VOUZwS3RJUHRsWnNrOTZyNFgwekNxK09JbkN5Y3UzU0YzdS9FMUhwazNnVGt1RFExWktkZjJyaDhwRFZwMW5PbWdsYlhBK0I1YXZLc3QvcGF6bzRjWjJlWGVoQ29YZ0NqeEpPdXNlNis1NG5GTldHeU0xVForWHd4L0E1OXc2SlR6NVdQYi9UOHVxM0NEWVhkUjExdXlCcDQ4MXIwOXlpSldhVUM3MnoiLCJtYWMiOiJhNDRlODEyNTNhOWUxZTZiMWQ4N2E2MTJjMWMzNmI4Y2ZmN2VhY2IyN2U4MGJjYzRmYWQyNmRmYmZkZGZlMTE2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1638591149\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}