{"__meta": {"id": "X340654ac4071bd9b71b0599a1ee3f074", "datetime": "2025-06-28 15:43:31", "utime": **********.934609, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=9&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.416367, "end": **********.934627, "duration": 0.5182600021362305, "duration_str": "518ms", "measures": [{"label": "Booting", "start": **********.416367, "relative_start": 0, "end": **********.858065, "relative_end": **********.858065, "duration": 0.4416978359222412, "duration_str": "442ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.858074, "relative_start": 0.44170689582824707, "end": **********.934629, "relative_end": 1.9073486328125e-06, "duration": 0.07655501365661621, "duration_str": "76.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46292144, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01689, "accumulated_duration_str": "16.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.895373, "duration": 0.012119999999999999, "duration_str": "12.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.758}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9156091, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.758, "width_percent": 3.789}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and `warehouse_id` = '9'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.91873, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 75.548, "width_percent": 3.256}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.920435, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 78.804, "width_percent": 17.111}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and `warehouse_id` = '9'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.925034, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 95.915, "width_percent": 2.072}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15' and `warehouse_id` = '9'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.92661, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 97.987, "width_percent": 2.013}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics?date=2025-06-01&warehouse_id=9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-1209341070 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-1209341070\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-635261096 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635261096\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-805651375 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-805651375\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-226857749 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"84 characters\">http://localhost/financial-operations/sales-analytics?warehouse_id=9&amp;date=2025-06-01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125396906%7C17%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFacVo2Mk9jRmNFZ3N5MkM3K3JvTkE9PSIsInZhbHVlIjoiRGFZTHhRZWdJdnlVSU1jN1VJZzMzVDlJTHVJWGdidDdXV3ZQRnMzdWthL1RmM0tva1l0a3ROQ3o5bFMreHBLNWRIR3ZrRUtsd3ZkMCtCRFhHbm92T25HeE1nQTg4b0YxN2hXd3ZSdzlOMW9uQVE2Y3ErUmJFbWIwS1ZTbDIzalBWalpJZkVLYnBkdmhmVzdjQlloZ3VFaXRJQWRYSDhVODliNkQzL0duakh5TXhhUk9jeEd5bDRyb2NzR0liM000OFprM29JUUxIR3ZuRjBUQ3pOTE9UYmNpenNRYUhSSWdQZXR0dkowLzdVYUU2K25ReUdRKzlLaGR6ZWhWT1N2eHlvRWN1aERyM24wdEp3eUZiNXlURVNrWmRWbTZobjNKdmNzVnh1NXZ0alNUbXJ6cjkvaHZNWkNWc01FNC9iNXgrUVFtYUM4aDdlSmovYlltUkh2SWMxTFBpajdEUEg4RmVxRSthK3hub0FCMjJpcG1rZ0tuVG1WRzgrK1hIT0d4UStJaW9wbE5IMHIyU3hEazVYWTdwMGJVcnJQTUNhRzd4R3ZJcUpIenYxdW5lNmlNb01BdTRNam1Dd0FqQXI4Y0hwUDI3dGFzR0FySGRlUjUvajRyK1A0TThKV2FTQnM2UU5HWVJhd1psMmVaaEVuZzFGME5YL1F6V1dCaGNBaUMiLCJtYWMiOiIyNzY5MGVhYTQ1N2JhNGIyNDM1NDlmZDliOGZhNjZmNWYyMDFiMjM5MTE5NTdlNzUyYmNmZjI2NTE2NjcxMjUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZyblA2a2ZxVkttK1FZeVF6bmVvc3c9PSIsInZhbHVlIjoiWTRLRW8yaWE4TEgvZXpEcjcyVUg2eklUQllQM1h2RWxoRnNDMWFBZ2pLcUFxT0tLZ0JaWER3b2M4djBReDFSVW4zaWhtQlRhZEpvSkZVS1llOENKbGp2Z0NWS2tQcC9TNmtkbzQxajhLWmgzaFVFR3Qyb000T3c2dFBUZmlYT0QzSnJSVkwxcTMvbXRoOTd4MnM2SGIzNDJGVGY5MGUxRFV2bjdTUVNWT2tUQ3M2YXJ1REt4YTRlcElCZDdXWWd3WGtzNmpCRXhPWGQ0UnpKZk92c2MveUd2eXVjY00velpSU2ZoekMwaGRtek5tYjRrV281SHVIaEVzQjR0SHJKN2VoRFhyTGp1M3FiSjJmNXYxeDBkQ29hWEZvcEE4YVpkT0tjTUVERi9uMGNNY2JVM0dTZU5PcWdJUHF3OW56TWw4enptcUtZWVoyWEpuNzd2anBKWTlTeVFpdXZKSXZxeVd4ZmVlRjg1OGtvaDNneTBZVnF3N0N0YkJOMUZuTWphNkhTaHoxeTVMT0J0QTBXeHpONmJjUS9FNkdWZ2VLNnNsL3p4S1ZSY3dVUEtqMzFLMTdBZEtENC93SHU3QldydnlITTYzU1o3ejdKTmNNTHFNMUlHV1ZmeWJsUksrQ3JsZURyZmYzSzBXeEU1cFpLS1daQitwM3o3ZGUwYzJyWEYiLCJtYWMiOiJmNDVkOGM1ODVlN2JmNGIzM2Y4YjRmMWU4MDA0YjEyY2ZiNzUzMGQyNWRmNTIzYTUxZjU5YzI2ZmRjMTVhNGUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226857749\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1421650787 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421650787\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2043263586 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:43:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkI2VGoyUXBZSlBiZW9YK2JSOVU3N1E9PSIsInZhbHVlIjoiZTBDNDEwL3drdHhEYjNvbWFWdk9Pd1ZYb1poZmJXbHpzbFFYMHo4eVVpSDlrNHFnQTJJZUljOENOUS84cG1LQ2JtODd5ZXFKUFVBc0FlMmNzTEhabWFLV2UzbnJlbkRuTHY5SGlDKzF2elNSL21DZkM1dHovV0JnUG1GQVQva2ZOZVhzUHVpek1Vd1lQNXhXMVhLWFVLWit0M1Q0TEh4NFk1c3E3VVNSdDVxZ3ZJekNnYWkvb2o4Tm52ZVlQZjJyb3ZiV1VxQTNPS0RwN3g0dVlqWEd0VE4xQ29DNmhLTXM0WDUxNzlvVjhJZGFOWVlHa3kySWpWTXhxUWIya2dXRm5tVUxLOHZxbU5hL1Y1TjlRek1obG9Ec0syTG82Y05pRE8wMm1Gb25oYlJOVUNTekVYWXlEZWNkd1o0WWE1VTdGOUJBSmU0VCtzVVp5Ukp2VUQ3eVFnTXZDOW5FdXNWUzFPSWs0SHNneTBjSkNXajVlL3V1MkZmY0RpOWNLVkF1ckpHMStCQ0hzK3VvS1p4VUs2V2YvaGZWV3FIZjRrNjFOU3pHSUROSjJxSGpBbjEyTC9HRFpWalc2aFNmb1VXdGNFRHU4dyt6ampPUmpaR1BvaWtTaGVra2tzd1BKT2Z0bnFLeFBFTG83WnJVRHIyRlRZczdEekxoU2hPS2RuMUUiLCJtYWMiOiIwYmExNzlhYzA4OWY0NWNjMjg2MTNkNGJjYjJkZjUxMTg0NjU4N2FkMTQ5YmQ2NzhmYmUwNGJhMzViMjMzYmYzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilg1YzNMT2hpTVM1Wmc4TCtzQ2c2R1E9PSIsInZhbHVlIjoiUzdJT2Z1SWNYM2NBZjVTQlVIUmw3a2dwSjVKY1pJcG4zb000YzVSZFYwRnJvbXlsdGlFT3o5N1Y0RjZSTTNYcXh1SG1EQ2M5WTNXZ1l4d2dFTXFmVjlFOHY0YUJxQUpITktNM0dKU1BwQ210Zk5UWWZTL2hFWEJQNE9UcGNrNDhXZmRjUTFTSEJUZ3RwckxXei9td0dlbXBjVFZ0YmNaVXd3RC8xeGg2YitSdjZKcWpWdnNjbkZLdWg3b3llRkVxb0ttdzVaZDhZNk5najNuOEVXMVhxZEhVSWk2YXpqVUhRMnIyZnNTNnpZMzZkaHZVZkN3clpqREIzYlpNUFI3UFRBcThqcWhaUnF6bzRNc2ltWldudUtEMTEzTXZSdVdCeWRoT2ZuVUZOL0UwUW1yaEs2SDIvWDdoL1FBSStQN3VkTjRXYWFYc21PeUJ0UzkzcHpMN3ladTJ6RlAybWpUMThNU1JCdUNqTStRK2tFckRTNTEyMEl2aHdGZGhPdm5CajRERFJLMHJRc1JSTUZJMHAwRjdYVGVGMjRzcjFhTGczbFplL0ZPbHZDMVR3akFUZTNmZUhvek5hc0N2QWdQZi9UNUZkaUVJV2xmQU00emR4L3ZyOHN0dVBUc2lJTzhraEhPejhrbWVIWjIrMExoUkRTQSthZWJQNjFGTHlpSFciLCJtYWMiOiI0NmMwYjE4NmYzN2JhNzA0YzQ1NGE0YmI2MDljYTUwMzgyZjM2ODdlNzBlNWVhMTI2YzU3YTliMTc0NjY2NDk3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkI2VGoyUXBZSlBiZW9YK2JSOVU3N1E9PSIsInZhbHVlIjoiZTBDNDEwL3drdHhEYjNvbWFWdk9Pd1ZYb1poZmJXbHpzbFFYMHo4eVVpSDlrNHFnQTJJZUljOENOUS84cG1LQ2JtODd5ZXFKUFVBc0FlMmNzTEhabWFLV2UzbnJlbkRuTHY5SGlDKzF2elNSL21DZkM1dHovV0JnUG1GQVQva2ZOZVhzUHVpek1Vd1lQNXhXMVhLWFVLWit0M1Q0TEh4NFk1c3E3VVNSdDVxZ3ZJekNnYWkvb2o4Tm52ZVlQZjJyb3ZiV1VxQTNPS0RwN3g0dVlqWEd0VE4xQ29DNmhLTXM0WDUxNzlvVjhJZGFOWVlHa3kySWpWTXhxUWIya2dXRm5tVUxLOHZxbU5hL1Y1TjlRek1obG9Ec0syTG82Y05pRE8wMm1Gb25oYlJOVUNTekVYWXlEZWNkd1o0WWE1VTdGOUJBSmU0VCtzVVp5Ukp2VUQ3eVFnTXZDOW5FdXNWUzFPSWs0SHNneTBjSkNXajVlL3V1MkZmY0RpOWNLVkF1ckpHMStCQ0hzK3VvS1p4VUs2V2YvaGZWV3FIZjRrNjFOU3pHSUROSjJxSGpBbjEyTC9HRFpWalc2aFNmb1VXdGNFRHU4dyt6ampPUmpaR1BvaWtTaGVra2tzd1BKT2Z0bnFLeFBFTG83WnJVRHIyRlRZczdEekxoU2hPS2RuMUUiLCJtYWMiOiIwYmExNzlhYzA4OWY0NWNjMjg2MTNkNGJjYjJkZjUxMTg0NjU4N2FkMTQ5YmQ2NzhmYmUwNGJhMzViMjMzYmYzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilg1YzNMT2hpTVM1Wmc4TCtzQ2c2R1E9PSIsInZhbHVlIjoiUzdJT2Z1SWNYM2NBZjVTQlVIUmw3a2dwSjVKY1pJcG4zb000YzVSZFYwRnJvbXlsdGlFT3o5N1Y0RjZSTTNYcXh1SG1EQ2M5WTNXZ1l4d2dFTXFmVjlFOHY0YUJxQUpITktNM0dKU1BwQ210Zk5UWWZTL2hFWEJQNE9UcGNrNDhXZmRjUTFTSEJUZ3RwckxXei9td0dlbXBjVFZ0YmNaVXd3RC8xeGg2YitSdjZKcWpWdnNjbkZLdWg3b3llRkVxb0ttdzVaZDhZNk5najNuOEVXMVhxZEhVSWk2YXpqVUhRMnIyZnNTNnpZMzZkaHZVZkN3clpqREIzYlpNUFI3UFRBcThqcWhaUnF6bzRNc2ltWldudUtEMTEzTXZSdVdCeWRoT2ZuVUZOL0UwUW1yaEs2SDIvWDdoL1FBSStQN3VkTjRXYWFYc21PeUJ0UzkzcHpMN3ladTJ6RlAybWpUMThNU1JCdUNqTStRK2tFckRTNTEyMEl2aHdGZGhPdm5CajRERFJLMHJRc1JSTUZJMHAwRjdYVGVGMjRzcjFhTGczbFplL0ZPbHZDMVR3akFUZTNmZUhvek5hc0N2QWdQZi9UNUZkaUVJV2xmQU00emR4L3ZyOHN0dVBUc2lJTzhraEhPejhrbWVIWjIrMExoUkRTQSthZWJQNjFGTHlpSFciLCJtYWMiOiI0NmMwYjE4NmYzN2JhNzA0YzQ1NGE0YmI2MDljYTUwMzgyZjM2ODdlNzBlNWVhMTI2YzU3YTliMTc0NjY2NDk3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043263586\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1328750055 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"84 characters\">http://localhost/financial-operations/sales-analytics?date=2025-06-01&amp;warehouse_id=9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328750055\", {\"maxDepth\":0})</script>\n"}}