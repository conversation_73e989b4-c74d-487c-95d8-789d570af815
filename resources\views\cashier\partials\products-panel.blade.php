<!-- Products Panel -->
<div class="products-panel">
    <div class="card h-100">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title mb-0">
                        <i class="ti ti-package"></i>
                        {{ __('Products') }}
                    </h5>
                </div>
                <div class="col-auto">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary active" id="gridViewBtn">
                            <i class="ti ti-grid-dots"></i>
                        </button>
                        <button class="btn btn-outline-primary" id="listViewBtn">
                            <i class="ti ti-list"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card-body p-0">
            <!-- Search & Filters -->
            <div class="search-section p-3 border-bottom">
                <div class="row g-2">
                    <div class="col-md-6">
                        <div class="search-box">
                            <input type="text" class="form-control" id="productSearch" 
                                   placeholder="{{ __('Search products...') }}">
                            <i class="ti ti-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="categoryFilter">
                            <option value="">{{ __('All Categories') }}</option>
                            @if(isset($categories))
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary w-100" id="manualEntryBtn">
                            <i class="ti ti-edit"></i>
                            {{ __('Manual Entry') }}
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Categories Tabs -->
            <div class="categories-tabs">
                <ul class="nav nav-tabs border-0" id="categoriesTabs">
                    <li class="nav-item">
                        <a class="nav-link active" data-category="" href="#">
                            {{ __('All') }}
                        </a>
                    </li>
                    @if(isset($categories))
                        @foreach($categories->take(6) as $category)
                            <li class="nav-item">
                                <a class="nav-link" data-category="{{ $category->id }}" href="#">
                                    {{ $category->name }}
                                </a>
                            </li>
                        @endforeach
                    @endif
                </ul>
            </div>
            
            <!-- Products Grid -->
            <div class="products-container" id="productsContainer">
                <div class="products-grid" id="productsGrid">
                    <!-- Products will be loaded here -->
                </div>
                
                <!-- Loading Spinner -->
                <div class="loading-spinner d-none" id="productsLoading">
                    <div class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">{{ __('Loading...') }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- No Products Message -->
                <div class="no-products d-none" id="noProducts">
                    <div class="text-center p-4">
                        <i class="ti ti-package-off text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted mt-2">{{ __('No products found') }}</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
