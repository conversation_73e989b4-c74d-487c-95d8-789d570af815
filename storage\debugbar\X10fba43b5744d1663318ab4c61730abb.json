{"__meta": {"id": "X10fba43b5744d1663318ab4c61730abb", "datetime": "2025-06-28 11:23:40", "utime": **********.430663, "method": "GET", "uri": "/pos-financial-record/opening-balance", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[11:23:40] LOG.info: Opening Balance Request Started {\n    \"user_id\": 22,\n    \"warehouse_id\": 8,\n    \"is_sale_session_new\": 1,\n    \"has_manage_pos_permission\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.418857, "xdebug_link": null, "collector": "log"}, {"message": "[11:23:40] LOG.info: Returning opening balance view", "message_html": null, "is_string": false, "label": "info", "time": **********.419947, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.033505, "end": **********.430679, "duration": 0.3971741199493408, "duration_str": "397ms", "measures": [{"label": "Booting", "start": **********.033505, "relative_start": 0, "end": **********.35605, "relative_end": **********.35605, "duration": 0.32254505157470703, "duration_str": "323ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.356058, "relative_start": 0.3225529193878174, "end": **********.43068, "relative_end": 9.5367431640625e-07, "duration": 0.07462215423583984, "duration_str": "74.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52067104, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.42594, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record/opening-balance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@opinningBalace", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=262\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:262-323</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0034199999999999994, "accumulated_duration_str": "3.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.387993, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.357}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3976262, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.357, "width_percent": 9.064}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4120111, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 68.421, "width_percent": 18.421}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4139462, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.842, "width_percent": 13.158}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1776257782 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1776257782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418171, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1824086034 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824086034\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.41982, "xdebug_link": null}]}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/pos-financial-record/opening-balance", "status_code": "<pre class=sf-dump id=sf-dump-666975506 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-666975506\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1375502088 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1375502088\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-827345490 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-827345490\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-26511879 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IjlDOWduYkF5ejZRVXJPWGpVbG0zOUE9PSIsInZhbHVlIjoiYm9VR0FnRE1ralp6dEFFM21udThFKy9YZG5MNXlTV05EeEpTVVU1T0t5Vkt5OGQ3dlpsTnExdEo2TVVFRzY2R1p1akgzZnJ4dG5mN3Q1WTlITGJ5TWtiYjg5aHQxVHMxVUtDZlNQY1BlVFhXWHJuZGdMTmk0cURyWkcrSzQvUEhDQmtLc2RmaGdYckdjMWxvTEJZQ0xmNFlXTTdBaEE2Sm1teFJFWGNXb1hVWlhUUHFNd0kyU21oS3dMQU9wRTYxcXpSRy9qNStTMEw3KytxYzFjQnl1b2JwWTZZSThEajVJVUh6N3lKY0x1blpjRWZLZXBlY0xXaHZ1SFFBWk5UeFo5VzIzc2VoOHArV2tEYmhmWWxnYjdSdXI1SWk4UllWY0N2NjlEZFUyanhlSXFwUmQ5MWVwSG5pUVFvRHVvYU5SYkwwN1orelRWR2Q1V2RHMmxnYzNBM0lYNWhjeGhtRUtkVGdCQ3F4YmhzaTd1WXEwRlNJdURKVHJISGRpbkRnVmFYcHpxckRFdlRsNUNaQllLSytLOWhHcWxkaERxWkxUaEU1RWdGa2J6TkU5QVd2d1REZUJ0MERiNm9SVG03NkR1VkNnZm9lMGpycUxWVURFcWNSZlZRa2RyZzExTXltU1FZemwxZVk0ZHdUN3VIcHN6aXNxUzNFT25wUEFDRzEiLCJtYWMiOiJmNDhjYzRhMjdjOGZiZjI0ODEzOGE0MTA5ZjYxYmI2NTc0NzUwN2Y0MzUyZWIwZjYwNTdkYTQzYWM2ZTY2ODU1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlF4alhkM09ObngzcnY4cmxtTUFPVkE9PSIsInZhbHVlIjoiQVVRSFFsVVNzblNva0JIRGl2bXp1dkljQzJIWWtrR0pIaGlsS2RJTkd5alhWbHpUZjRMSFRaR2NTK2NXOUIvejB3b3JOQWlxa2g3OTBjaUNzelZDallEKzZZbE1hSUdWVUd3U0FWRDB2SmNCWGJycmp3dm4vYUNxdjA0SERESzlOM3BhZ25NaGVITXVmT0NNNkNWSzcvdm1xdFl2NTExK3JlU2ZndStZSU1CdVlMak1KRnpPdmR4cmtOK2VzVENITTU0Q2ZxR3pKd2pwcFk4NG5YekRmNHEzTlhObWhxWW5Ka3p3ZTVHVk1idVVmbGc4TGVRMWE4OTg0ZloraTFQL0h6TFg5Wm40QWVWU2pPWko1MTlIdlhTdlN5K1NSNW94ejYvZTdIZ01Kb3JGbE9nWVNzNFpTTTIzN2t4RzZiZGpoRW5TZzhKK1dHMkQrOGFucG5oTXVoVEdXNldqZ2QwVTJvQWJQUGFlSVhoMndrbkhTeUxtenQ3UnFRM2NRdGJWOTkxUTRaYmh6eHo3MEJCcFliQ0hoN21vRWNvZlVEM0NWWkNqUlVIc2FYZnlMelp1Wnd3c1UvVWFUcEtnVjVKaFFncTZMSHF1VUY4YTF2aE8zV0FEQ2tyM29lNFhuZFdOdnhJVWtGdW16RFlxeTZyZVZyd1oweEVreXQ2VmlQK1oiLCJtYWMiOiI0MGFiZTZhNTBiMTI4M2ViYTdjNmE3ZmRmZGU5YmJlZGRhNzhmNjc0ODRjOWExY2E0MjJhYzI1NmUzMzU3YTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26511879\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-619507565 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619507565\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRXanVROUlNT2FpQkNNb1JZVkRqOHc9PSIsInZhbHVlIjoiWTQwU2RKbmVCdDNvdTc5aUY4bU9TVC9oTUxCc0tHMURFbjliSndrRk9VSy95akE1Z2JjYldpbjJ3Uk1NdmNJT013VTB2aFhrWlJSMjR3VWwzaEtET0pTS0ZHSmVmTC9ZTDFZelRHdVh6M2Z3VGVBSGdaK2xCMEswTFhacStjUUt0eFh6RUJkTnBPcUZrdE42RkNXVytMYlpUQytWU2QvLy9hb3NwQkQrQVRrZnJFM3dydXlUb0JaZElvb2xsMzRHNnBTRDg5NXNReEN5VmJsNDA1bjBwa1BwN0QzSGhnUmhSVkp2Qit0b2Q1MjJSdENtLzhrTHNUKzlISHptUFBmbVNJV0JuKzJuRWl3d3BIT2R4T3BoNG9kUi9ZZFl3M1R4OWFUeVRMOHlFZnhvaExOeS9MeDNvQVZkSmVVRUN0dmU1emZsUHU1Q1pJMU5CSmcvKzZxc09Yd1VPRDBtTTFRKzltWXBMTU9aalYxQjJ4OS81UHRJVm82UEFXMkJwNzkwbkdkYXJqeGV0UVFIWm9kbjI1MEZpVXMrdVdlZkdaQng3T1oxYU5RUDRZNDREaDFVUXpqODBkRkNhb0ppcXgrN1ZjYUZNYUV4WEcvOFVrTFhCZllDUkcvQnNYekFndjVqQkF4SFFyODVyclVFVGpZU2ljM1R1Ump1Z3JpdDVEZkwiLCJtYWMiOiI4MjJjNDQxZTBiYWJlZmExYmMxYTg2OTBlOTczYjBiYjQ1NmVlMmE2MWE3OWI4YjI3MjFkMTQzYWIzNTYwMDRmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBpNUZtWHlGU1ZQalVOK1RsYldVd0E9PSIsInZhbHVlIjoiMHZwditrMFR6Si9nWk1mV2k1dmJ1VUhheDJQWktzVnFBM2wvR3loVnJ3TTh2anNrakxwNXZ1TmJSMzJwWlplWi9RMlk5eDRFZnpNYmRHZ3MzVnFhelNDbXE3K2c3MERmTU5KTFN5OE5YM1VTNW1MWU05T1VLbkE0bCtWRU8wcjlFazlhM3NIWHh3WGw5b3IwY3gwRjRZRG9zcGNXWGdFSmtkN0FkVDZrdjFiaHJqWTEyV00wTnljRjdtR0ZwSTA5ODQxSUo4OEhSSTA4eXNWT2xCSUxvVjBhbFlHQzAydFU5aFpHZ3dtSkVXem5XZXg1clZsYTA0V0RLckV1QXhHek5Sb0U5RmN6N21DY0pPS3pmVm4vRThBLzdNdnVtN2p5dW01NzRFQnVpR3pPK2NlYWFoUTRobVBESGRWNTlUNXJkck0vWkIwMG1lb1lIRi94b1VmenlLTU5OdG9oeEZrSGQxU25NcVhaTWduWUNKWlRkRWZmMmkwMFZQbXcvZmRROHlUK3l4L2JIbkZEV1ZvTmhxTVQwM3ZHWEI3bXhyY1dzK1YzMGVrRjVzQWV4NXNVeWp1Nzd5T3hhTmVhalFRRFdNQndxM20yeFA0MC9mSlAxMUt3Q1d0ZjlxR1ZaNk1uanlEMkkrd0taVml2TGhLd3kxTkE3cGQ3N05DZ2U3eDYiLCJtYWMiOiIxMjMzOGVkNjc1YzNkMWZjMzAyZjI1ZmQ1NzY3NzNlYzNkOTMwMzNmZjQ0NzUzODgwZWMwNTk5MWQwZGM3M2E2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRXanVROUlNT2FpQkNNb1JZVkRqOHc9PSIsInZhbHVlIjoiWTQwU2RKbmVCdDNvdTc5aUY4bU9TVC9oTUxCc0tHMURFbjliSndrRk9VSy95akE1Z2JjYldpbjJ3Uk1NdmNJT013VTB2aFhrWlJSMjR3VWwzaEtET0pTS0ZHSmVmTC9ZTDFZelRHdVh6M2Z3VGVBSGdaK2xCMEswTFhacStjUUt0eFh6RUJkTnBPcUZrdE42RkNXVytMYlpUQytWU2QvLy9hb3NwQkQrQVRrZnJFM3dydXlUb0JaZElvb2xsMzRHNnBTRDg5NXNReEN5VmJsNDA1bjBwa1BwN0QzSGhnUmhSVkp2Qit0b2Q1MjJSdENtLzhrTHNUKzlISHptUFBmbVNJV0JuKzJuRWl3d3BIT2R4T3BoNG9kUi9ZZFl3M1R4OWFUeVRMOHlFZnhvaExOeS9MeDNvQVZkSmVVRUN0dmU1emZsUHU1Q1pJMU5CSmcvKzZxc09Yd1VPRDBtTTFRKzltWXBMTU9aalYxQjJ4OS81UHRJVm82UEFXMkJwNzkwbkdkYXJqeGV0UVFIWm9kbjI1MEZpVXMrdVdlZkdaQng3T1oxYU5RUDRZNDREaDFVUXpqODBkRkNhb0ppcXgrN1ZjYUZNYUV4WEcvOFVrTFhCZllDUkcvQnNYekFndjVqQkF4SFFyODVyclVFVGpZU2ljM1R1Ump1Z3JpdDVEZkwiLCJtYWMiOiI4MjJjNDQxZTBiYWJlZmExYmMxYTg2OTBlOTczYjBiYjQ1NmVlMmE2MWE3OWI4YjI3MjFkMTQzYWIzNTYwMDRmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBpNUZtWHlGU1ZQalVOK1RsYldVd0E9PSIsInZhbHVlIjoiMHZwditrMFR6Si9nWk1mV2k1dmJ1VUhheDJQWktzVnFBM2wvR3loVnJ3TTh2anNrakxwNXZ1TmJSMzJwWlplWi9RMlk5eDRFZnpNYmRHZ3MzVnFhelNDbXE3K2c3MERmTU5KTFN5OE5YM1VTNW1MWU05T1VLbkE0bCtWRU8wcjlFazlhM3NIWHh3WGw5b3IwY3gwRjRZRG9zcGNXWGdFSmtkN0FkVDZrdjFiaHJqWTEyV00wTnljRjdtR0ZwSTA5ODQxSUo4OEhSSTA4eXNWT2xCSUxvVjBhbFlHQzAydFU5aFpHZ3dtSkVXem5XZXg1clZsYTA0V0RLckV1QXhHek5Sb0U5RmN6N21DY0pPS3pmVm4vRThBLzdNdnVtN2p5dW01NzRFQnVpR3pPK2NlYWFoUTRobVBESGRWNTlUNXJkck0vWkIwMG1lb1lIRi94b1VmenlLTU5OdG9oeEZrSGQxU25NcVhaTWduWUNKWlRkRWZmMmkwMFZQbXcvZmRROHlUK3l4L2JIbkZEV1ZvTmhxTVQwM3ZHWEI3bXhyY1dzK1YzMGVrRjVzQWV4NXNVeWp1Nzd5T3hhTmVhalFRRFdNQndxM20yeFA0MC9mSlAxMUt3Q1d0ZjlxR1ZaNk1uanlEMkkrd0taVml2TGhLd3kxTkE3cGQ3N05DZ2U3eDYiLCJtYWMiOiIxMjMzOGVkNjc1YzNkMWZjMzAyZjI1ZmQ1NzY3NzNlYzNkOTMwMzNmZjQ0NzUzODgwZWMwNTk5MWQwZGM3M2E2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1829488402 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829488402\", {\"maxDepth\":0})</script>\n"}}