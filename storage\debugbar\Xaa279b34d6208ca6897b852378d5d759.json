{"__meta": {"id": "Xaa279b34d6208ca6897b852378d5d759", "datetime": "2025-06-28 16:30:38", "utime": **********.897741, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.470745, "end": **********.897756, "duration": 0.42701101303100586, "duration_str": "427ms", "measures": [{"label": "Booting", "start": **********.470745, "relative_start": 0, "end": **********.829746, "relative_end": **********.829746, "duration": 0.35900092124938965, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.829755, "relative_start": 0.3590099811553955, "end": **********.897758, "relative_end": 1.9073486328125e-06, "duration": 0.06800293922424316, "duration_str": "68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45874608, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.009099999999999999, "accumulated_duration_str": "9.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8617048, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 18.242}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.876101, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 18.242, "width_percent": 5.604}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%جالكسي كيك الكراميل 30جم%' or `sku` LIKE '%جالكسي كيك الكراميل 30جم%') limit 10", "type": "query", "params": [], "bindings": ["15", "%جالكسي كيك الكراميل 30جم%", "%جالكسي كيك الكراميل 30جم%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.879013, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 23.846, "width_percent": 44.176}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.886233, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 68.022, "width_percent": 26.374}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1995}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.891295, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1995", "source": "app/Http/Controllers/PosController.php:1995", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1995", "ajax": false, "filename": "PosController.php", "line": "1995"}, "connection": "kdmkjkqknb", "start_percent": 94.396, "width_percent": 5.604}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1518934860 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1518934860\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1113815804 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113815804\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1100658302 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">140</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C**********007%7C50%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjAxZzhNV2VYcUhHQWpKbmkvQlNxRFE9PSIsInZhbHVlIjoiTTJwclViZXNwREVqYWd0eVNwdE1mSHduMjRIRXdLOU9BdjduNjZNM1V4WTJRM2srK3J0SzBOd1RjT25XV0Q0NVVYLzloRmM2S1NGRUl1UnI5dFhIY29walhJMGJGL0dGZitBRmRWZGdxM05WeHk0UGVMeVVBNXBIRHd3d0l1dWp5TVZIK2JNMmpmMml2blRJekNZZ0RRZUpRQXN0d0Z5bkRJWVhua2Zac1NKYnJFZWM4UXFpTDEvTXNnc1RHZ0EvdFVJaDBXOW9WTVFzU3I5bjBJemVrWGJrdnVIelI3RjhwUTYwdzROY2Jqb1ZINzRTeDFpbUFVL09aV2VzTkdMY2pRSk0zZXlmZDNpU1lnVEs2c0tEYmIrL00xa1pHcHA0UXVqM1J0VVM0RG9tdFNXR0dHL0M2cFlQa0pLZ29lUWdxR01YV0VBUW1FdThjalBMZ0haNVlTL0ppNFRDMWJUY0VrWWUxckgwekVJZy81UWM4dmJ1RzhBQVVObURyQUZLaE43TW9RK08xaUpDdUE0RzQzRmNHWk1UeDhDRENBbm0vZlg4Q0FocHo1SlZEZlBTV3U2aHRId0JKVlJKMUNlUHZyMVZaclgxTnpyeHluMDhBWitVdnNQV3RPdzBHd2YxWVFya21rWkxBd3gxcHQvOG5YcUZMb0tUQ0llekN4VE8iLCJtYWMiOiJhOTQxNTI1MGM0NDA2M2Y3MTc3MGUyYWE5ODYzNTdmM2M1NjBiZDQ1MTlhZWI2MjVkNDc3ZGYyMWRmOThhNWZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVMcENGSXRLdEcyQ1liaEZMZ2hYU3c9PSIsInZhbHVlIjoiaFR6cnE0OVRHWFVjek5BRGs4Y1grS0x5QkxsRXIzNkZEMlVsa1B3MzgyOE9QVVY3TktaUFNFbHN2aTBkUWN6RmlSbU1IdXkwZEhtRkhvU1NSOXIxYVlTblB4RUIvWHdCUis0cjdUMGlSMTZ5a3JRTmNqYlRmTEJTRUxQNzA4Qm93OUpzaVFBNFB4dU1mNnlCYUJCYVNlN1ZLbDRGZ1VNR1B0NGlmZUhYUUtJTlViT0lmZnNPWThRUUtiWmNOenlKZE9kbjhTa1grclFiRVVWTnFEZ3ZSK01BMkVYTktiUXdiS1RlZkpTczNnZ1ByRzd1TmpFQk5abzhOekwvZnFoS3N1WGhXdUVmbWZZSWNKUUtaYUNvRURtWjI4QVhIS05HZDFYSXhZakxYTnlZcUVFMlNkUWc0YS95T2l1MGNWMlRsNUY2VVJ6eUVsWmgvVWNabUpYOWdRckM3RS95Q1M5L3NxbXRZWUkxb3g1NGd0MDA5VzErYk1MSzlobm9Yc1d1SkoyK1JWd3hUSFF4NjZUOGQzVFBhKzlQSk1RMVFnaGhzc2xvelVMUzRUU2xKOGplRSsyTlFOWlNXZkdSOEJGMW5iYWVrcXErSGdDdnlpZkR4YTJJN2FoVFpuMmlBL3Y0cnR4SVFTMFMwQTQ2Wm9HQzgxeFhrNjVjc3poT1RqT1UiLCJtYWMiOiIxYTVkZDNiNWM4ODUzNDRmOGY3ZGYyYzg2NDhmOWI2MDU5MTc2MDQyYTg1NjI4YjE1YTdlZmVjZTY0ZWQ0YWE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1100658302\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1350641750 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1350641750\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1900654647 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxScldwTHZ1ME8xSHczVUpuK3VBR0E9PSIsInZhbHVlIjoiRmlBbkF0QXM0MytNaXNhVVIzekFUM2xlZnE0N0dKQm8zUmZOcWh5L3Y5K1BDNHpPdE02aW1qa3pTNW9FR3hWaFJDMXpEWUZ1Zk05ajlHTnljSXE5aWhMS253V1I5RHRGcTAvWFo3cVo0WEtmOEhoSVhsSlU0L2ZlaHlINDl0MWlrMUhLZzBRN0xtTDZ5amtkWE1pQmFCTEhONDJmT0d3cFQzc3ZURmp4NmtTazRNMklJMStTS0ptWnJEVFlpT2dHTjQ1dExMQ1pTVjhCLzFUY0lpaXA5MWtjS3p2YXRTRStTK1A2UndFN2U1TnNqZTZYa1BOeUlqODFXVGhWdFgyaCswV1BCV0tsZWhLOGx6dlhhUWRhVDlKRXBlQ0p5TnNybGZYbVJ6RUZ3clVWOE03RWxCN2N0YUZGQTdqZzQ5amlGa2pHV205V09RMFdqZnBGRjlreWlmY05oQUNxcmlqSlNnZGRhcTFBRUVZenZwNENHVGxtdTVEWU5vTXVPOURkRVhwWVdWWkJ6Qk5uWGdhQzdLN2g3MGtNZ2U1OUVPNk9xaHVFa05Xb3ZLYnVvVkdiWU43QWVwNnpkeHh1TUdiZUJNUmRnR25CZ1JrZFd3MWVmc2RGQUlJUDlPVWdSNkNuOU9qdmdpVGcrYU5sVDJmeEdXanFkOWx3d3F2VklxRS8iLCJtYWMiOiI4OTE1MzJjMmE0ZWYzNmJlMmE1ZDRlZDUzZDAyMTEwZGM0ODBmZGNkNDI4NDE4NzJjYzIzYzk4ZDA1NmIxYWIwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5xaG41YmtERmNVbm9TRm5CaWwzTmc9PSIsInZhbHVlIjoiTS9zamVYVkZETEN3WFpmZXRldW1jSnVFaHNxNGhLRW8vZFJ4QjFSR2wzcGtkcTVwb1lKNjVsS1ZLYktzd0dpNEMxZXdMTkF5Y0Mrb0pTd044UG1pVzZaRFRMaGpQZ1EzNDdpa3BDZ2R3eEFwWE1raEowVG1oQ2I4RDVEOEpIc3lzbStndUNBaDZzeENPZGdXL1lPMHdnZ0x3RUZCbTNLNmRrZytaRUZUbHgzNjliZHNxN0lGRzJORUpJQlJmRkhaZ2xzRnZzZHkyM0VmOE5xNUhzU2hTcVM1d2Jwc1hnUHdmbHlZSlE0R01kdU9vWkM2UVIvUnN6UVJZTXp1azQ3eUVVNFIwVUpmd3hjS2x1V2lWMzFRWDFDQ2hnR1VYTzArdHRrME55c2xLZWhRWnE0NzRFWmdoeVpzejNybHdBeU9xV0RKc1UvazZZY2tOdUd6Zm5tQW9MYzE2cW4xV2Z6WWt5WDVreUREUjJiUGg4UkxXSUZtaHBOcTJFWGNWdm1PbGgvd0pUc0R4Z3c0ZXhTVkpxdlZsalRCUEdKb0MvZ3NWQkI2MWVRZUJsUDBJMk5MelYwS0lobERDQW94QTIydGtSSWZVUW9pc1hRMXR5aEEvUmdvbXZIeFIyc0V1YlFESWh5L0ppM2k0VURHNEFkTjZCZFVQaWJieTNUaTFkcG4iLCJtYWMiOiIxYjhiNDNmYjY3MmMyMjNmMzcwZjVjZGZmMTg5ZDIxOTYxMGY4NzdiZDYxZmE2MDVjYmMxNTQ0NGI1ZTNkOGJkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxScldwTHZ1ME8xSHczVUpuK3VBR0E9PSIsInZhbHVlIjoiRmlBbkF0QXM0MytNaXNhVVIzekFUM2xlZnE0N0dKQm8zUmZOcWh5L3Y5K1BDNHpPdE02aW1qa3pTNW9FR3hWaFJDMXpEWUZ1Zk05ajlHTnljSXE5aWhMS253V1I5RHRGcTAvWFo3cVo0WEtmOEhoSVhsSlU0L2ZlaHlINDl0MWlrMUhLZzBRN0xtTDZ5amtkWE1pQmFCTEhONDJmT0d3cFQzc3ZURmp4NmtTazRNMklJMStTS0ptWnJEVFlpT2dHTjQ1dExMQ1pTVjhCLzFUY0lpaXA5MWtjS3p2YXRTRStTK1A2UndFN2U1TnNqZTZYa1BOeUlqODFXVGhWdFgyaCswV1BCV0tsZWhLOGx6dlhhUWRhVDlKRXBlQ0p5TnNybGZYbVJ6RUZ3clVWOE03RWxCN2N0YUZGQTdqZzQ5amlGa2pHV205V09RMFdqZnBGRjlreWlmY05oQUNxcmlqSlNnZGRhcTFBRUVZenZwNENHVGxtdTVEWU5vTXVPOURkRVhwWVdWWkJ6Qk5uWGdhQzdLN2g3MGtNZ2U1OUVPNk9xaHVFa05Xb3ZLYnVvVkdiWU43QWVwNnpkeHh1TUdiZUJNUmRnR25CZ1JrZFd3MWVmc2RGQUlJUDlPVWdSNkNuOU9qdmdpVGcrYU5sVDJmeEdXanFkOWx3d3F2VklxRS8iLCJtYWMiOiI4OTE1MzJjMmE0ZWYzNmJlMmE1ZDRlZDUzZDAyMTEwZGM0ODBmZGNkNDI4NDE4NzJjYzIzYzk4ZDA1NmIxYWIwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5xaG41YmtERmNVbm9TRm5CaWwzTmc9PSIsInZhbHVlIjoiTS9zamVYVkZETEN3WFpmZXRldW1jSnVFaHNxNGhLRW8vZFJ4QjFSR2wzcGtkcTVwb1lKNjVsS1ZLYktzd0dpNEMxZXdMTkF5Y0Mrb0pTd044UG1pVzZaRFRMaGpQZ1EzNDdpa3BDZ2R3eEFwWE1raEowVG1oQ2I4RDVEOEpIc3lzbStndUNBaDZzeENPZGdXL1lPMHdnZ0x3RUZCbTNLNmRrZytaRUZUbHgzNjliZHNxN0lGRzJORUpJQlJmRkhaZ2xzRnZzZHkyM0VmOE5xNUhzU2hTcVM1d2Jwc1hnUHdmbHlZSlE0R01kdU9vWkM2UVIvUnN6UVJZTXp1azQ3eUVVNFIwVUpmd3hjS2x1V2lWMzFRWDFDQ2hnR1VYTzArdHRrME55c2xLZWhRWnE0NzRFWmdoeVpzejNybHdBeU9xV0RKc1UvazZZY2tOdUd6Zm5tQW9MYzE2cW4xV2Z6WWt5WDVreUREUjJiUGg4UkxXSUZtaHBOcTJFWGNWdm1PbGgvd0pUc0R4Z3c0ZXhTVkpxdlZsalRCUEdKb0MvZ3NWQkI2MWVRZUJsUDBJMk5MelYwS0lobERDQW94QTIydGtSSWZVUW9pc1hRMXR5aEEvUmdvbXZIeFIyc0V1YlFESWh5L0ppM2k0VURHNEFkTjZCZFVQaWJieTNUaTFkcG4iLCJtYWMiOiIxYjhiNDNmYjY3MmMyMjNmMzcwZjVjZGZmMTg5ZDIxOTYxMGY4NzdiZDYxZmE2MDVjYmMxNTQ0NGI1ZTNkOGJkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900654647\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1838240562 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1838240562\", {\"maxDepth\":0})</script>\n"}}