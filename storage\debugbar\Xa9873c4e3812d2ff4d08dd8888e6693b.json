{"__meta": {"id": "Xa9873c4e3812d2ff4d08dd8888e6693b", "datetime": "2025-06-28 16:04:03", "utime": **********.858298, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.429453, "end": **********.858312, "duration": 0.42885899543762207, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.429453, "relative_start": 0, "end": **********.79248, "relative_end": **********.79248, "duration": 0.36302709579467773, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.792493, "relative_start": 0.3630402088165283, "end": **********.858313, "relative_end": 1.1920928955078125e-06, "duration": 0.06581997871398926, "duration_str": "65.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46423080, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2428\" onclick=\"\">app/Http/Controllers/PosController.php:2428-2462</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01291, "accumulated_duration_str": "12.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.829236, "duration": 0.0124, "duration_str": "12.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.05}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.850885, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.05, "width_percent": 3.95}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1462/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-704519289 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-704519289\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-229243270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-229243270\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-14212122 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-14212122\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1397360656 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126629815%7C23%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZIaFV0WGdFR3g5WkdORW9BUkJyNEE9PSIsInZhbHVlIjoiSG54cDFDQWtjSmQ1WUsyTG91VTMwVjg4TVRyNTlqQmVBenlPdVpEZnJsbFVLNWM2STFjRGZxSW5iOW13ZlFjcUxHcHdla2xCMG1ISDFOZzA2NlJicXB1bis0ekxLbm5zNVprMUNCZzZ1c1J0M2F5MmQ5YjdHYS85VXJ2ZEw2MzJNbGVlSi9qWTFSaENoZk15OC9NYW1YbHVjVitkdGhKNTNiVTF0U28ydHIvSm5nRVRGcEJkOU5rVExkUWM2ZGNaRXY4SGx1U3FMT2UyMFJ6Q2F4Y3FsdzJxdnNMbjJNckdTSVJtWlUxUkdtbVJCdWdvNTVkdzJzK3FEbGphM2tkZXdVUkhwQ0kwMWtqNlhOSWUwQ2JTaFp4RmJoT2MxZUN3eXZMUXNtdzBYOW1zMzFiTWdFSnMwVTFIWktERmUrOTQyK1pSNTlsY0RFZGM0NFFvQk0xUmNQZHRCY1BIcXkzVkdQS2l4NlVmZG0xQ3hpbmExSHdCbTZ4OTBQME9oY0x0azhlRDFUU3JhZG94UCtvS01RUDAvNFRaN2pVMi9UUlZNNUxqV1pQeGdxTkQ0Mi9sVkE4SkZHUHNVcC9rbzRCdzhFUFBxOXFiKys2M1VyaUFqVlhBRTRRUmIwdUpHYUtsYk96TG5ST3FqNFRaaW16UEhvTFllK3p1R2ErMlRERVEiLCJtYWMiOiJiMTFmNmIyMjZkMTQwNjQ0ZDRhMzU5ZjI2MTExN2YwMmMzMTdlODI3ZTExNzZhYWM1ZGFjODdkOTAwODVhYWFlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZRbHNyNExDWlZMMHVRTVlNeC94eHc9PSIsInZhbHVlIjoiekYyTHlheDlCNjlDMWR4T3JVSFdQSzhLUlNHbXlTeGJkeVF2emtYY3VMUWtNeVQyV2VsYlM3Tk1ZOXdvTUVSNVJMdWd5WVFlQTdab2NBMTh6bWlZUy9GUnNlb3luRTMyRWhZNTZ6N3J6UEhaMXdRUkFKb0lqeG1FdDZ2aGRTSnQ5WWIwU0hjQU0zNTBmNk8vRFl1eXJGeUhuZVIydEZzU3hPNzRBTk9GOHd4OFplemJZc1BLdEVvaUtDL0ltT2VkQnlBV3dpV0tvaEdrNEUxZHN3R0FoNC9TdnMrYWRPUGpsRmY4RnBDYzJmM2VicFJiOURhYXBFL0hKVU82eVI3bVAvb3A5WjloaGNSWEcwVEt2ZmJQN1pWZWRRbjdCcU5LYmdFWi9Od0ZYTE1JQzVwNWFoRytMb0Nzd25ud282TXJOR3IzaGlTbzN4SjRUR0M0bUJ3Y0RIczFNeDBlZGJtUWppa0Z1cTkvVzIzYmtkK2JQZi9lQXRDMHVRUHZyQitxeEdsdFNDa3pZSW1wdFVpdWYvN2VWa0ZqSFppM28wd2tWbUMyd0ZoY2JIVWJlMUsxZkE5S3hJeFJmRUsydDBzTkdaTGdGVnVWemFaZXJQLzNKemZGTzdISVV1YlEyYUV6cTlrTnIzZTRlRlI2Wk40Q3h3bEVFbGJRWmJEeDgxSWEiLCJtYWMiOiIwYzg3MmI4NjJiYjY5MGM4ODRjZjZjODI4NjZiNDJiZjU3ZWI5MDlkNmQzYzk4YmEzNWRhYjZlNWZmNzc4YjdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1397360656\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1007345771 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007345771\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1582984621 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:04:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlB0YmRMbEIxNG1QYWU2YUEyVDRQcUE9PSIsInZhbHVlIjoicDFvMDl5dHd6UEZRTjlIVXBKcjdkdm9ZTG1KMUhNN1R6eU0xZTZ5dWRvUTFrd0JzcWRqc2hSMERrV3M0ZTV6TTdianh1TTRTUkM1NGk1aDFSaHJkNkQxZlRkalFDcFlQM3dFRUVnUVhnYXF0dWxjMjNkaGVSSm9uWEFhaGF3dzZmOFhxcmV5ZzM2bTRjZmZKVE9CTitHM09UU1cvTU5UcHRnTHpjMVdZZGpvaS9kZE5MTkJnUjJkbVlMTG1XRE9FKyszbkpPNlFUZnBxWEZSWk9TN3NhQ2RoUlFGRkRIbEU1Z2UyR202TncycXJQcmlhdzZnb3ZrZS9iWjJMYlg4enNXQUxMVU4vTWMwOWJnUUREeXIzekFtNjZ3VjBQd0RIZW04MDZZYnpzQkxYTlFmYXFYZ2ZCTm9LczZnWXgxQ1NUVVFacys5UXJDTWhLN1hNdlZXN1dHUUJzclJtcm1wa3NYOUozV00rczNZZFZBMjV6R2xkR0ZjSmNmdHprVjcybDJoa3NEU2Q4dUVHNllkSzkxYlQwUllGR2k1V2d5NUpXWlBjTEUyMVZER05jWTJVajZTWmN0dXNNVnJIc2FJelVwSmJ5UlMyaHEwWHZraXl4YXR2cGw4V2ZKZXpGTW5aYlVkdFBIQmhHVHhXN1dXM3hnQjVjOFEvY296cXNBZkIiLCJtYWMiOiIyYzllODQ0OTE0M2MyNjAwZjA2MmJlZjUxYmRmYjc5NDhmMTRkY2RmYTk5YTkzZjZiOGVhNjIwMjAwMmI2YWVjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJianJRMzA3Q09xeEtIQ2pNbzJOcXc9PSIsInZhbHVlIjoibkRLL3lEMW4wTVRzSGhJb2N4WXFRSkJnTjQrejdZM3RMK21mVm5ZMVJWOVlvWTNFMEdCUG5vUy9BWGVBM1YybU5IZ212RllibGt5SkltQWtlTEVvcW1vMGRPMXZaVTlhaWRIOG5VMjNuOEhSRXNCOFRnek9QeTY1eURTWk9KbUhHUEgvY1gwUDVhMGlHNUlsY0FMeHF3azkrTWN6YUYwK2lFY3UyOHl3S2RYRGRBd0drQXduQ2pOYjFEVEZDMDVHUHRTeVJPR1hmTi92TW11TGdUczlzSGFIeU5SZHVVWmRUeElOQjBGVEZKVnoxbHo4bjNFOC9FTjVGRUwxRmZuOUxqS3U3Qmc1ajE5NEhlaDhSdk1zTG55bmF2YnprcUREV2JLUWl5RjdzQTQ5WWZUVzJtTGIvZkFwRTM1WDZkUTFuOXZRSjNtcTFZVFUzQ0ZwSXRUWUMwN3dxVW9qMGk3Y3huWkF3K0FhOGsvK3p5RWs2YW5HQUxWSkZ5YlFuTGlWci8wN0lCUUpZc0hDTVlsUlNBdmRaa2hxOGU0OHFJZGtrVGd4YVZDSlNpR2x3TFhpdnRDMzZGcnNnbmtHKzdPVGp6eE5FR1AxQkxwNjJTSS9rdnArZkhZeithMW5Gdyt6RG0vYlQvUVFqQUdyOG5ycFhyWnBIVGVHL2NaVWdWMnkiLCJtYWMiOiJkZWRiNjFjNmM4MTE1NDhiNzc0OTg0ZDQ0OWM3ZWZlOTdmNGZiMjQwMmEyOTJmZDg0ZjRkZDBlYjEzMTE2ODdlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlB0YmRMbEIxNG1QYWU2YUEyVDRQcUE9PSIsInZhbHVlIjoicDFvMDl5dHd6UEZRTjlIVXBKcjdkdm9ZTG1KMUhNN1R6eU0xZTZ5dWRvUTFrd0JzcWRqc2hSMERrV3M0ZTV6TTdianh1TTRTUkM1NGk1aDFSaHJkNkQxZlRkalFDcFlQM3dFRUVnUVhnYXF0dWxjMjNkaGVSSm9uWEFhaGF3dzZmOFhxcmV5ZzM2bTRjZmZKVE9CTitHM09UU1cvTU5UcHRnTHpjMVdZZGpvaS9kZE5MTkJnUjJkbVlMTG1XRE9FKyszbkpPNlFUZnBxWEZSWk9TN3NhQ2RoUlFGRkRIbEU1Z2UyR202TncycXJQcmlhdzZnb3ZrZS9iWjJMYlg4enNXQUxMVU4vTWMwOWJnUUREeXIzekFtNjZ3VjBQd0RIZW04MDZZYnpzQkxYTlFmYXFYZ2ZCTm9LczZnWXgxQ1NUVVFacys5UXJDTWhLN1hNdlZXN1dHUUJzclJtcm1wa3NYOUozV00rczNZZFZBMjV6R2xkR0ZjSmNmdHprVjcybDJoa3NEU2Q4dUVHNllkSzkxYlQwUllGR2k1V2d5NUpXWlBjTEUyMVZER05jWTJVajZTWmN0dXNNVnJIc2FJelVwSmJ5UlMyaHEwWHZraXl4YXR2cGw4V2ZKZXpGTW5aYlVkdFBIQmhHVHhXN1dXM3hnQjVjOFEvY296cXNBZkIiLCJtYWMiOiIyYzllODQ0OTE0M2MyNjAwZjA2MmJlZjUxYmRmYjc5NDhmMTRkY2RmYTk5YTkzZjZiOGVhNjIwMjAwMmI2YWVjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJianJRMzA3Q09xeEtIQ2pNbzJOcXc9PSIsInZhbHVlIjoibkRLL3lEMW4wTVRzSGhJb2N4WXFRSkJnTjQrejdZM3RMK21mVm5ZMVJWOVlvWTNFMEdCUG5vUy9BWGVBM1YybU5IZ212RllibGt5SkltQWtlTEVvcW1vMGRPMXZaVTlhaWRIOG5VMjNuOEhSRXNCOFRnek9QeTY1eURTWk9KbUhHUEgvY1gwUDVhMGlHNUlsY0FMeHF3azkrTWN6YUYwK2lFY3UyOHl3S2RYRGRBd0drQXduQ2pOYjFEVEZDMDVHUHRTeVJPR1hmTi92TW11TGdUczlzSGFIeU5SZHVVWmRUeElOQjBGVEZKVnoxbHo4bjNFOC9FTjVGRUwxRmZuOUxqS3U3Qmc1ajE5NEhlaDhSdk1zTG55bmF2YnprcUREV2JLUWl5RjdzQTQ5WWZUVzJtTGIvZkFwRTM1WDZkUTFuOXZRSjNtcTFZVFUzQ0ZwSXRUWUMwN3dxVW9qMGk3Y3huWkF3K0FhOGsvK3p5RWs2YW5HQUxWSkZ5YlFuTGlWci8wN0lCUUpZc0hDTVlsUlNBdmRaa2hxOGU0OHFJZGtrVGd4YVZDSlNpR2x3TFhpdnRDMzZGcnNnbmtHKzdPVGp6eE5FR1AxQkxwNjJTSS9rdnArZkhZeithMW5Gdyt6RG0vYlQvUVFqQUdyOG5ycFhyWnBIVGVHL2NaVWdWMnkiLCJtYWMiOiJkZWRiNjFjNmM4MTE1NDhiNzc0OTg0ZDQ0OWM3ZWZlOTdmNGZiMjQwMmEyOTJmZDg0ZjRkZDBlYjEzMTE2ODdlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582984621\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-136809406 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1462/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136809406\", {\"maxDepth\":0})</script>\n"}}