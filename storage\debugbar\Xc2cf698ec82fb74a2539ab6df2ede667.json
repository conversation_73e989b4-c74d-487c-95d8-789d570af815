{"__meta": {"id": "Xc2cf698ec82fb74a2539ab6df2ede667", "datetime": "2025-06-28 15:49:39", "utime": **********.492452, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.043151, "end": **********.492466, "duration": 0.44931507110595703, "duration_str": "449ms", "measures": [{"label": "Booting", "start": **********.043151, "relative_start": 0, "end": **********.381772, "relative_end": **********.381772, "duration": 0.3386211395263672, "duration_str": "339ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.38178, "relative_start": 0.33862900733947754, "end": **********.492468, "relative_end": 2.1457672119140625e-06, "duration": 0.1106882095336914, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 62667152, "peak_usage_str": "60MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.04876, "accumulated_duration_str": "48.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.414658, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 3.158}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4245749, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 3.158, "width_percent": 0.882}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4272091, "duration": 0.04679, "duration_str": "46.79ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 4.04, "width_percent": 95.96}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-1054187312 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1054187312\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-131545929 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131545929\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-893650431 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-893650431\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1301025655 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125775754%7C27%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZYb1V0TnYySlBEcUNQRFBrWVdVTmc9PSIsInZhbHVlIjoicHJuaWRTOERvMm93Skw5YXQxTDh5Sng1YUw1NWV2ZzVTbm5qajhNUlFHTU1kZ2htK0RhR3NOazc4czl1dnVXaEU3UnVodU4xcmZGOU5IbDllZ1E2UE5ZbzJVQTNaOW5sU2VqYmoxN3JmamVpWisxOUdXTUZscjhlTlEzTkcreldvZEMvMmJqRjJNSWg1RjNGUUpjQjBHM09sNWVrczBuMzlFSGJuRmRobkhQWGdWMEpmbUJjZm13V2JSdWZiR2p6L2JNWjdVVzFjN2RobjNTNWFLdjVkbWlWdTFtZmJzT25TbjN4dmZKWG5EZ3FlTUdhdUZFSnpwbmVSWFdLVlpEbkpNaGFEMlU3RGk4cWF5UWxEalBqZ1duWmtITzM1Zy9XSWRhL2MxS2szWitVZE1pMEh4eGttYXhsbk02SXBlNFNJVDV5dUZUbFREK0tZcTZoUk5ZUHdRczZUcm83U3RUamZuNUxpSzlBQ2pXcDFCQmJkTkxnWll6U05TVDJpZS8rTTZ4empRZC9xK3dROW9FZmRITFJCV25HaDF6YUZxcUdhTzZjTktZSkRuOStyY2pDeWNpQjBWZzhLUkpYOVdIVmF3NmMySHQ5OHd5WHFocGRqL0xlQnNnUEVra202dnBpU0hHTmx6MDdYbGdMdHlpSEdSSjlSdU05STdWbUtvY0IiLCJtYWMiOiI1MGVlZGFjZjg4MTg0YjI1MzJmZDYwZjkyM2I1MjIzMTNiYjZjMGUyNDE0MmRkODA3ZTNiZDFmYzdlZmFmOWQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5Wck4vekJqOURtOUQvN284SVBTY0E9PSIsInZhbHVlIjoiQWJKZmlKMmw4dkJVckxqaGFVcCsyVHNJMmhLMkZZSTdRNmtwSGo1elJkUTI1S3NQSnJuWlJQeC9ZdzVudmJpWG1hU2pqRzFkakEwZmhQUGhRZWNhbG42MWFmODJKMUdNMTdtQnptWEZhRWxrRjlidGF5dThIZEdFckh3OHkvWFdFKzZiN0JieHdhQlFxd3VEUnp3bnllNXJWYUhOOUQwZGxVWGNINkROa2g4ZERlWFN3dXhOa3AzOC9NVldLK0tkS0p1NEt4amYzL3loMzc4WGM2dmZUcUMwU04zb2lwWHRvK0tWcVhrN1QzajlwMHlxSEw1TU1RK0U0eGNDd0JTSTFsODMydkIvUWdKd3BGbVRuYzJRTEJTc3RzVTVEeStXUWgzWVZNcy9zM2t3RjJhK3Y0QTQ2TCtPUXc0N2ZBV2ovczRmM0tEdE8waTNha3pFOEMzQjJSTm55dnRscStnSzNPWnVUajdwcnl1R0RnaGdudVBGR1h4VjJwMEN0WHlvMmlHMGRnMjVRLzJEQmRrdHBJVmxVeEZ6K0VlaitnM2xPekdROVExVmVHeEZsMi85Z1hvWE1DK1pOUXBIOWNyLzRKL2p3dk8rV0hsU04yUzVxcnprSGlma0hHaXBGOVkvT2ZJZzgxNXZBK1pLclRUWDMxUDdmTm9xZUU2aVFhS1UiLCJtYWMiOiJkNjFlZDUyYWFmMTQ4MDkyMmU3MGU0NTI4YzNjYjVmMzgyZTM3ZTk4NWEwMjc1M2UxYjM4Nzc3ODAzZmUwOGE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301025655\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-402529509 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402529509\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjMvVHZzMTlzVG1mWkpOUyt0amNPL1E9PSIsInZhbHVlIjoibWN5VFZvSHZDN053SEJUQWpVR0ZNdkRKRldNQlBDSFcvY2JKTnZEWC8wM0JuZWpsajVTM2hnenFqMHo2dGk2RlBOK1I1VHRuamxIalMxNWlUOWs2dGxWaVVTM3NQWFR1SjFUcHMvR3NxSk05em53OFNCWkx1T0kzNmcrbTdmVXJtM1pzNFNtakI0WFZUckJFUGZoRVk2b1JBSndxbFRJU2dOQVE3YzNhZnUyR09ZKzRiQjBuSTI0V0R0dlMzQlYwRUJFTUVCUUdIeVUyV1MzcE45WGxLZ2hMbDE5WkUwZkxlMnROZllHcU5GbGFjQTFHWGlWWnEvY0tTdkl6ZkFBR1VKVTJ1UnNKTDc4aGlKTVdtM1hZRGhnVkhqUXhzUThVQnhIeWJQUkdSb3RUaFpsd0ltcEFsQkdqVkRGUm1vNG0rRERJWVhkNHJaeVkybU1qZkdVM3ZjaWMwckttRGwwYmZVNXZReEs1a0NQVTNid2gwaDcyeDdrWHNsSVN2T2o4b2UzMXlOMjR3ZE9taTc5RkFWN3F2TGM4cjA5dGN0WTdhdERpM1FOc05iVnNybys3d0NnNHpBR0FRb0hWaVFQWTlUWGltc2JidkNMV2U0MHNoMlE1WENEdGxJVzdrNTU2dUtKUS9iQjlRWm8zRDVrSVQ2VENzTnBOTFVoT1E3YnYiLCJtYWMiOiIxMDk4NTUzNjFjODdkY2JlYmU2OTc3ODg1NTdkOWJiZDk2MDczMWNmZWM2NzY2ZjZmNDRkZGE3MDljOTk3NDE0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5DZzkzdlhHeCtGMVZiNnpzbUR0VUE9PSIsInZhbHVlIjoiRW1BNDF1ZWtQTlhhc1dGWHg4VzRtNG1tTXdFbHFOY05IMWhUaDRlRTZaNVBCcDJtVHpUcStUbWJqMW51Uk5FMFVxOTBiYnFkK0ZqTkNQSFczMDN3VEdrUDdDdEh6TnpBdWc4KzNqcTNGTU9IOUxWdGNSOG5UNDl3VTZLckloK0FtY2ZtNjlMRzlkeWhrRWxkTDBrS1dmME9UTC95WkNqR2F0dFZHak14bTF2YkZ6NXZ6NWtHWVU1aDFabkRFZndMbEpRSDFxNHV3SGR6eHRGV0EzNVg4YzNUeFV5VURMSFI1RFhYVnh5TkZJVFVOWDU0dURMVUNrMXQ4c01UMC92UFRES2wxL1hmQ3lXdjVLSDE5VkxXMUVLM2M1MUZtbFF2b2ZUSFArUksvNWEwMDM4eGREMklHOWEzNnJzcDM4Y3lFci9pNEhBd2JxcUsrUUc2WmxtV3pLYnYzQ09tZTFtQm02OHlubnNTUXd3emZWSGNJbjdQTHRlRE5HQUVDYzFUSndBTEdCek1TYUxyMmt1Y0ZlZjFaR294ejR6WGRnTy9zTkZXamc1akNjUkVNUXlseVYwcEYrR1JXUWtGYk1Qck9nVnFtakNqZk9yUzR4bVZWejZUbHF0RVNXd3VUSk9RYnBhdHJXODZkL2pXMitLaUowZGY1TUhERTllaDdKamgiLCJtYWMiOiIwMjhhYWRlYWNjOTEwMDRkNGJkNjYwNTM0NjY2MmUwMzU5NTUyYWFiMjZlMjA1NmZhYTAxYTQ0ZTg2ZTE5ZThkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjMvVHZzMTlzVG1mWkpOUyt0amNPL1E9PSIsInZhbHVlIjoibWN5VFZvSHZDN053SEJUQWpVR0ZNdkRKRldNQlBDSFcvY2JKTnZEWC8wM0JuZWpsajVTM2hnenFqMHo2dGk2RlBOK1I1VHRuamxIalMxNWlUOWs2dGxWaVVTM3NQWFR1SjFUcHMvR3NxSk05em53OFNCWkx1T0kzNmcrbTdmVXJtM1pzNFNtakI0WFZUckJFUGZoRVk2b1JBSndxbFRJU2dOQVE3YzNhZnUyR09ZKzRiQjBuSTI0V0R0dlMzQlYwRUJFTUVCUUdIeVUyV1MzcE45WGxLZ2hMbDE5WkUwZkxlMnROZllHcU5GbGFjQTFHWGlWWnEvY0tTdkl6ZkFBR1VKVTJ1UnNKTDc4aGlKTVdtM1hZRGhnVkhqUXhzUThVQnhIeWJQUkdSb3RUaFpsd0ltcEFsQkdqVkRGUm1vNG0rRERJWVhkNHJaeVkybU1qZkdVM3ZjaWMwckttRGwwYmZVNXZReEs1a0NQVTNid2gwaDcyeDdrWHNsSVN2T2o4b2UzMXlOMjR3ZE9taTc5RkFWN3F2TGM4cjA5dGN0WTdhdERpM1FOc05iVnNybys3d0NnNHpBR0FRb0hWaVFQWTlUWGltc2JidkNMV2U0MHNoMlE1WENEdGxJVzdrNTU2dUtKUS9iQjlRWm8zRDVrSVQ2VENzTnBOTFVoT1E3YnYiLCJtYWMiOiIxMDk4NTUzNjFjODdkY2JlYmU2OTc3ODg1NTdkOWJiZDk2MDczMWNmZWM2NzY2ZjZmNDRkZGE3MDljOTk3NDE0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5DZzkzdlhHeCtGMVZiNnpzbUR0VUE9PSIsInZhbHVlIjoiRW1BNDF1ZWtQTlhhc1dGWHg4VzRtNG1tTXdFbHFOY05IMWhUaDRlRTZaNVBCcDJtVHpUcStUbWJqMW51Uk5FMFVxOTBiYnFkK0ZqTkNQSFczMDN3VEdrUDdDdEh6TnpBdWc4KzNqcTNGTU9IOUxWdGNSOG5UNDl3VTZLckloK0FtY2ZtNjlMRzlkeWhrRWxkTDBrS1dmME9UTC95WkNqR2F0dFZHak14bTF2YkZ6NXZ6NWtHWVU1aDFabkRFZndMbEpRSDFxNHV3SGR6eHRGV0EzNVg4YzNUeFV5VURMSFI1RFhYVnh5TkZJVFVOWDU0dURMVUNrMXQ4c01UMC92UFRES2wxL1hmQ3lXdjVLSDE5VkxXMUVLM2M1MUZtbFF2b2ZUSFArUksvNWEwMDM4eGREMklHOWEzNnJzcDM4Y3lFci9pNEhBd2JxcUsrUUc2WmxtV3pLYnYzQ09tZTFtQm02OHlubnNTUXd3emZWSGNJbjdQTHRlRE5HQUVDYzFUSndBTEdCek1TYUxyMmt1Y0ZlZjFaR294ejR6WGRnTy9zTkZXamc1akNjUkVNUXlseVYwcEYrR1JXUWtGYk1Qck9nVnFtakNqZk9yUzR4bVZWejZUbHF0RVNXd3VUSk9RYnBhdHJXODZkL2pXMitLaUowZGY1TUhERTllaDdKamgiLCJtYWMiOiIwMjhhYWRlYWNjOTEwMDRkNGJkNjYwNTM0NjY2MmUwMzU5NTUyYWFiMjZlMjA1NmZhYTAxYTQ0ZTg2ZTE5ZThkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1553130928 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1553130928\", {\"maxDepth\":0})</script>\n"}}