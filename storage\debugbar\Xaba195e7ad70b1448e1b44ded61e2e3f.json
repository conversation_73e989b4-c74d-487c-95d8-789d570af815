{"__meta": {"id": "Xaba195e7ad70b1448e1b44ded61e2e3f", "datetime": "2025-06-28 15:08:10", "utime": **********.631059, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.202993, "end": **********.631073, "duration": 0.42808008193969727, "duration_str": "428ms", "measures": [{"label": "Booting", "start": **********.202993, "relative_start": 0, "end": **********.579341, "relative_end": **********.579341, "duration": 0.37634801864624023, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.57935, "relative_start": 0.3763570785522461, "end": **********.631074, "relative_end": 9.5367431640625e-07, "duration": 0.05172395706176758, "duration_str": "51.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46270200, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0022600000000000003, "accumulated_duration_str": "2.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.613556, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.204}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.624232, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.204, "width_percent": 20.796}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2092663473 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2092663473\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2081617111 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123286871%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjMxRWt4L2MwaS9YVzB1eDY1S1pJU2c9PSIsInZhbHVlIjoiK2JhVEFNWEdGV0VOUWE2aVE0UnMxbkNZc2JDemFDNndkcnJQTXY5VUNpUjFQZTZzUU4yOTljU1YwS0pwdjZJN3NyRFR0bFhaRGlHazF1NkEvQThnb1VWUWZXcWpYZllPSTFIYUhUZGpxbUNTdmhEM1poM0s4VldOQXk3VFFnSVU3WFExZDJkekh2YjRac3BSYjgzK0RwbXhBeTd6R3NpSDBjazc5c0twSThtblhKMnBoU2M2Ry9ycHJGZ0ppeUVJci92OGlnVTR2eldlTUdKVVZjNGR5ckRYMWc1M1NFZk1LZ0ZDRXZrMThJRE85amdialNHck5kdGJBZUFCZVJJZnBUSDZKVDdBQ0NIQ3B1K3hLcGo3ZkRISTU2cStUT0lPTWdoVkRYOWZPMW03Wm5DNUw0Z2dhU3dqSTY1ZndsMm1VazZSTjdNUWNQaWg2WFA1MGRtTFE1RVJjRUhaa1ljNHA1NkNOdExVMnZBdGdza3ZNaG00N0pPcHF6clhjNWltNFdnZGVuaGx1SnBMSjJJbG11L1YvUEJObjAwQVh5WlovM0RCWGhid1RWM2FRUDFtUmd0TmEwNTliZUZDbEhxdnUzK08rTVN4RDl1MEdPRStLVDBTaEtpYUU0R2tYZXZjTHVBYkpub1ZBNDB0clZtSGpTeFh6d0N5MlpJcEQrMUEiLCJtYWMiOiIyYzMwYjkwMmJhYzM5ZWJmNWYxNTI5NTNkNmI3NTAwZTc5YTdhYmE5MTVkMzA0ZDM1Y2ExZTI4NWQ3Yjk3NzU1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZhemRUc1IyQ0VLQlF3K0VxNlhQY2c9PSIsInZhbHVlIjoidGpYcjBmL0IrWTJIQkxoRHBiR0E0WTZoOWNTVER3a2NOR1VzQ2c4UU5yRi8rMWdyNHVpdUN6R2c3ekhzTnM5czhYUStTM21yWXVLRmNIMVdxMW1mSUJaVXdJbTJHMTZ2Zi9kSDFaNGJiSURjMGg4a2FrSzBDY2hSeGFNTk5lcW04TU52S1VnRmlyU241TTZmR0NyalF3Z1BkN0JEOWlPUDVWZVVoanhrcy9zMkJVZ2NVNGk4bndGU3lmVERBVllkSGFWeElKNXpzb01XRXJhN2VXOThueFB5WUpERnV4M2VQYWg5aXN6VHdQdzhaSlVMZVdYTWtMUWIxRGNRMkNOY2NHci9OZE0xOVBSbHBDVG5NdlVrMUpCWW5zOGlaMWZUdkRaU1J4MUlnSU1ZSjlqOXdlRlB4S3VjTmhVMmk4ZE1WN0NuT05XeURBaHcwcGZqTXF6OVZ2Sm5lc3hoMmRDTFN5ckR6eW5mUUV6Qi9oVXdZa3dROVpZZWZENXpZTUZOSDlCN1JKVDdBU2N3WExKWlJOT2tQQUFjcC9QK2pBcnNIOFp0WmNQL0t0OXZ1YlJqbFNXbk96K2JCSFpXS2dwS05QK3VGNlZoVC9lYTIralZCamRGMWZ4aUZ2T0srOHBiOGpEaU1qMXhKU2d4bUZvUC8vaGJxOCtUcmpGN2l2VFEiLCJtYWMiOiJhY2M2YTNiZDlhYmEyMWRmM2JiZjgzMzUyNGYxNmIwMzc3YTI0YzVmZDNiNmNmZWE2YjhjN2YzMTQwMTg2M2I1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081617111\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJrR2hKODFoVm85V0UwUkFHaXRzVmc9PSIsInZhbHVlIjoiTlRENXg4QmZwMDAvNE4vazNGUFFWNHhTa1BlNGY3N3ZodDBGRWkreGd4RWtBT1JsaVB3Z0dIQmdpeFM0RW9BVWx6eXVNOG5EYk1aQUN1R2k5U2ZEZE1ENm5qbUpKQTAzejFHQ2c4U1M3czNPUHZiZXNTWmc4eG1CdGFFbU5WN21rcFczNTlIWGNFMkhyb1B5WTd4OEFuNG5tN2RDYWpsMTNXb1pBdk9yVEFwNHE4UFpSUWgvb2hiZngydk4zUFJNNjJTMzVyNTI0K21UdDJzTE0xY3JWTW5QTTljOFNzd2xmK2ozQTB5Q2Q4NSt2d3QwSUludWgzZXh6N29jWThZMC9PM1FDNWlwbkUzU2JPV05FbVVBTzRMZW5RT04rZWZWYlI2dlRVaXl6VTk1V3p5RXlCdmlpUEoxNlZNRHgrYUJLMUhGZUp5RzBwdW9XUmRBcjdwWnhYSVVGM2FrUjFCeHFwUlUwa0JQQnNrcXFNYVVwcXJRSWVQcTFjTW94OWp3YmwrMDZDZkhWZm5QZ1dLZm1wOG9pWTBKZk1ibnZ6NEVuaDF0MllUaGpNVk5sZ00yR0QwZWpTaHJDaTB1aUhsbVpjZldqZjVMVjQxUGVjWDNTb251S2VmL3d5TzhwZ2RDakdrNVo0MHVMWGd4TUtpTzlrTkF0ZE5IR05BU1BNMUgiLCJtYWMiOiJmNTBmNjNkYjA3YTY3ZTQyOGJjY2FkZWI5M2UzZTk1MmVhZGYxMWM2YzJmNTYzYmU5MmM0YmQyOGU1NzUzNGUxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFRSm9BUWx4Y081TUYxYjBsWVFlcnc9PSIsInZhbHVlIjoidVpHYWU2TWNKRXgvb0p3cXM5aTU0M0kzTDhGb3liM09UMjlXM2tUeEt6RTBBcVcxWFlJK0dMME9QbllMOGVxd3JFZ21NNVZORnVOcGxSTHpYUHNZZms4TDlVUm5VbEtOVCs3MHN4RWxwVUlRM1l4VEk0cmYxR0lWRHdpb2x0bTRSNDUya0xLOEEzdXFJNkwvV2pJRXhJWmZxK2hvR21oL3FDelBON2NobmdnRmxLV2VGeXZUVnJtWlBCc1ZOOW1tZVcxSUxqUHJUZjUxTjQyN1BpVm1HOVdpcXJTaFpsMHlLNWJXb2RsRmxsQWlsQUJCM0sxcnYwUHZRblVrSklDRG9WRURQZWlwYTRKQXFxWnZSSTdCZkFYTjVKRnBTNHBlNEpHNlN6UFV6R0NEcGlQb2hrSThuWG0yeXJOdXc0MXV1MHV1azRZenNCUS9jcWpyV05NSHpaT1BBRUpJT1lLUG5ZaUVQd2xieGtnTEtpSzdYY3ZWMlF2eVQwcStQRVNzSW85cnRIVDFPYzloc0V6LzdwWkJKMXNkdXMwWjdWWTA5djFFV25Ga29Eb0lmbVNhS2h1anlZMmtEaDhCR0pHV0dmd3djMWNnRm10SmppaUZTdVdUZGlIbDlNZTRRUkNlTy8zL2UvNU5la1pqTW9WdzU0QXpDZGRyb1c5aTk1NEkiLCJtYWMiOiI3MWY1YzZlYTdmMjFkNWU3ZWQ4ZjZkMDI5Nzk4Mzc0MzI4ZDgyM2ZiOTQ3ZmJkZGI0MmJjZjMyMTY3YTMxZjZhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJrR2hKODFoVm85V0UwUkFHaXRzVmc9PSIsInZhbHVlIjoiTlRENXg4QmZwMDAvNE4vazNGUFFWNHhTa1BlNGY3N3ZodDBGRWkreGd4RWtBT1JsaVB3Z0dIQmdpeFM0RW9BVWx6eXVNOG5EYk1aQUN1R2k5U2ZEZE1ENm5qbUpKQTAzejFHQ2c4U1M3czNPUHZiZXNTWmc4eG1CdGFFbU5WN21rcFczNTlIWGNFMkhyb1B5WTd4OEFuNG5tN2RDYWpsMTNXb1pBdk9yVEFwNHE4UFpSUWgvb2hiZngydk4zUFJNNjJTMzVyNTI0K21UdDJzTE0xY3JWTW5QTTljOFNzd2xmK2ozQTB5Q2Q4NSt2d3QwSUludWgzZXh6N29jWThZMC9PM1FDNWlwbkUzU2JPV05FbVVBTzRMZW5RT04rZWZWYlI2dlRVaXl6VTk1V3p5RXlCdmlpUEoxNlZNRHgrYUJLMUhGZUp5RzBwdW9XUmRBcjdwWnhYSVVGM2FrUjFCeHFwUlUwa0JQQnNrcXFNYVVwcXJRSWVQcTFjTW94OWp3YmwrMDZDZkhWZm5QZ1dLZm1wOG9pWTBKZk1ibnZ6NEVuaDF0MllUaGpNVk5sZ00yR0QwZWpTaHJDaTB1aUhsbVpjZldqZjVMVjQxUGVjWDNTb251S2VmL3d5TzhwZ2RDakdrNVo0MHVMWGd4TUtpTzlrTkF0ZE5IR05BU1BNMUgiLCJtYWMiOiJmNTBmNjNkYjA3YTY3ZTQyOGJjY2FkZWI5M2UzZTk1MmVhZGYxMWM2YzJmNTYzYmU5MmM0YmQyOGU1NzUzNGUxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFRSm9BUWx4Y081TUYxYjBsWVFlcnc9PSIsInZhbHVlIjoidVpHYWU2TWNKRXgvb0p3cXM5aTU0M0kzTDhGb3liM09UMjlXM2tUeEt6RTBBcVcxWFlJK0dMME9QbllMOGVxd3JFZ21NNVZORnVOcGxSTHpYUHNZZms4TDlVUm5VbEtOVCs3MHN4RWxwVUlRM1l4VEk0cmYxR0lWRHdpb2x0bTRSNDUya0xLOEEzdXFJNkwvV2pJRXhJWmZxK2hvR21oL3FDelBON2NobmdnRmxLV2VGeXZUVnJtWlBCc1ZOOW1tZVcxSUxqUHJUZjUxTjQyN1BpVm1HOVdpcXJTaFpsMHlLNWJXb2RsRmxsQWlsQUJCM0sxcnYwUHZRblVrSklDRG9WRURQZWlwYTRKQXFxWnZSSTdCZkFYTjVKRnBTNHBlNEpHNlN6UFV6R0NEcGlQb2hrSThuWG0yeXJOdXc0MXV1MHV1azRZenNCUS9jcWpyV05NSHpaT1BBRUpJT1lLUG5ZaUVQd2xieGtnTEtpSzdYY3ZWMlF2eVQwcStQRVNzSW85cnRIVDFPYzloc0V6LzdwWkJKMXNkdXMwWjdWWTA5djFFV25Ga29Eb0lmbVNhS2h1anlZMmtEaDhCR0pHV0dmd3djMWNnRm10SmppaUZTdVdUZGlIbDlNZTRRUkNlTy8zL2UvNU5la1pqTW9WdzU0QXpDZGRyb1c5aTk1NEkiLCJtYWMiOiI3MWY1YzZlYTdmMjFkNWU3ZWQ4ZjZkMDI5Nzk4Mzc0MzI4ZDgyM2ZiOTQ3ZmJkZGI0MmJjZjMyMTY3YTMxZjZhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}