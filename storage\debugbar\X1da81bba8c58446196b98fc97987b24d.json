{"__meta": {"id": "X1da81bba8c58446196b98fc97987b24d", "datetime": "2025-06-28 15:09:16", "utime": **********.326038, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123355.896271, "end": **********.326051, "duration": 0.4297800064086914, "duration_str": "430ms", "measures": [{"label": "Booting", "start": 1751123355.896271, "relative_start": 0, "end": **********.25336, "relative_end": **********.25336, "duration": 0.3570890426635742, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.25337, "relative_start": 0.3570990562438965, "end": **********.326053, "relative_end": 1.9073486328125e-06, "duration": 0.07268285751342773, "duration_str": "72.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49496056, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01862, "accumulated_duration_str": "18.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2871099, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 9.613}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.297111, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 9.613, "width_percent": 2.148}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nAND p.warehouse_id = 8\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`warehouse_id` = '8' order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2998881, "duration": 0.01643, "duration_str": "16.43ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 11.762, "width_percent": 88.238}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-460417529 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-460417529\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2132969254 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132969254\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-130771333 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-130771333\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1865914463 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123070655%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imd4MytiRDBvZDhwVmVzOEp0b3RoUEE9PSIsInZhbHVlIjoid08zLzVhV2Q3cW1OeU05YzI5ZGx3eWlJeEJhNHEwdksvTERkeUIvck4vYy9FYTZUKytORExFSk43MXN1UTV5Zm0vdE9lNXN4VWk0ZStXWG4zZktGMkY0dHlxQ3NzWTNlQmljUEtMamRPdmRBdEIxVnR2eTdkdHdhL2xFTGNranM1QXA5RzNvMTNIcFBFb3RjOXpNV1lyWVB1RFFWdjJhRmVtWENyRHBKVXZQZUFFVWlhNFRkZjFWSkgrRDNMTGNmR0ppL2Y3RUFxWEVTR2F3RVF5WjB2enNMSkRnYzVmOGVWTjdQcWl0c0R5TEw5RXptM1JQM0I5WnVGMUJCaHdZRGdsSkhsVEIvNWFXUG5iUmJsczJYUjRSVjVFT29rUDlwRWtVMGRrZ0JSeWJzR1lqY0x3SDg1VFJlc0plOWt1VHJTZ2xnZjNrZ3ozVUVPWW01MWV6Mm5EclVMRGZCN1NIOEpQYlRDZmgzQm5OR2VOYlM3K0lNYVp6dVZweHQzNktXNVI0ZFlacXBMTldDZFJCRk5PdmFjZTNqSzNXM1pja05EM1ZOQm9sam9uYkZ1NUdQbmgrZVVja3BZbG5OS3ZiQ2VpWlQvM3Z4djlNZlpybVZpSUx2MjczSDl3bTRTektRTzRnR1FyaEVTdEFtTEFrc2JWUVkwYlZmL2tLTTFPajEiLCJtYWMiOiIyNDk5ZWM2ODE2NjI4MWIxMTU0MmNkNDFhMGI4NTA4Mzc3OTFmNTcxMjY0Nzg4ZDhjYjJjZGRlYWIzNzBkMDBjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdGai9TRW5pWS9tck5WM1hEUWRXZmc9PSIsInZhbHVlIjoiSmdYL3F0VWgvckplQVpib1ZYSGtpdXU2UkViMG5TZTUvdlRLd1Q5WmFwelZreksrL0Q5aFVWdEZTdXVYUlZWVmo3WnVveWp6c09UVUNacnVFcGNBeEg4YVY1OHhpaTNpa3Fxc2xTajhLL1pzckhLSzhjMksrU1h5RTB2THgxV21tSkpMYWZtNlI4Z3QycUtJYXJJZjFXMExOdktLY0ZxVEpiNFQzcnVEakRQOUJPWVo5Nnc1Q3NNQjJGa3BVWDkvNVZ4azR3TFZZV3VNanM4MEdieHVKNUdScHU5ZTVVSWJ2eGRBcG4zUnBtWFRmMnV1aDJXRTMwQ1N4d1hXencxV3FPZCtYMGZNWldmZExYeVZhOUhrMnVZdHU5S2EzNjREL2ZRTXFUNk5mdkpNYkJhMy9laVFoei9NSHgwTm10dFNwTGZtNFdYelJPRlcxdUJzMlg5SXhqMGgzSlFoa3I1K0xqQ3JCRG5HNnlOWElvaWRBRnl2Y3FEclpyaWJ3TVlDOVBzM3lDUm5nYlBsQnZEWXFFTHlWdTVhemZQZkNKZnJtUm1GZEhvY1FKOC9CZnJVRk51SXN1b0xUNGd2ZWQ5bXR6K2pMci9jVmxuUzFVUEZsUjJJTjduRXQ4ZEd0QW15UGlkQUZyZGVvWDBpODZpZ3BPWVZPVG5QNmY0UHR5MUIiLCJtYWMiOiI2YmNmZWJkMjdhZmNlOTBiYTIxMzNmYmJiZGI0ZTE0NzZjMmE4MTM1MDQ4NjhhMWYwOWZiZjE4ZGZhZTg1YjFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1865914463\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:09:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJFTzZ1cE9PV1BLY3ErMHRSOW9RYnc9PSIsInZhbHVlIjoiZ05QTWRCN05mTU1pUExIZGFyd0tkVnZaUXZqQkNCL2JQRUtpWExBUzVNTWFMdlh6Q1BmejVMSHN5RTU1OGRRRTJycVdiMmt3ODZyN3htY3lTU0hPSHJyYVQvN0liT3p0bWVnYkFTay9WRDdsWGRORFo3K3phYTBHYkZZdDVuRjZCZUxlNnMxSFJJZFNzL1U2WUplVEVUaUcyazdTNE94WTRIeEt1RmV2SmVZakRQNi9BaFZ3OFlzdmJEZEdHdHNZMHFXU2x2YmpNL0N1REJUdnhtdC8xZ1JYSVNhbzIwRTZUdWNBZEx4dm45YVdKYS9NNXFUMW9EbytQY3lOWlpPY0Z4bVFXbTBWcVpGMzlaOUttMmd4OWdQK3pmQnZpN0pHMVY2Tk1VNkhaeXc1TURGRktuWStlelgrS0F1MGYvTVo1RkJhR0FNUHZRRWpHLy9VekV6dG44ZmZJS2FBMkJnUG95UVNtMGNnWWdEZ3dYWWhHUXk0NjFMWXVZMDYrMjR6SVQxZFdua2x3eHpIb2thRHpwZGdyNXc2eFIvUFdnSlNicjNKbVBzZkVtY2EzTzM4YjZIOUFTelhMeHBaSVZHSVAvcUtlempVQVovVktaeXNXSjlMQ2JkbWY0OVBZY0hTZVpjb3pZS0E5Qm53RUFyVUdIMTZBU0RGN25LeWxUc28iLCJtYWMiOiI4MWJkNWQ4NTM4MTllMDg1Y2E0NmE0YjIwOWE4NzE4YTM0OTJiYjIyNmU4NTAzMWNiNTZkMmM4ZDExNWRlODkwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:09:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpGSzJtOHlpZUkzWHpnTGozSTJyZmc9PSIsInZhbHVlIjoiTWpJSTF1YTNwMnFIRHpINVZjVEpuMDlEY1pjV2hmMjRKWW5QSTcxTVZDSHJNMHJkYisycWthcTYyemhXMGU1clNtNmFaWFFsdk16Zk1Fc2FIL2RZUjducHpIcFk5THdIQWFDTjZWWVhvbUc5Z0U2WGN2WWNRZ01sZjIrZEthbmVnTHhOaUFGb0FyWGROazgrMGF5VnNVZ2VYcGJxdUJWc2tSODBXRTVWbXAzZzluQURvbUQ1TklSVHYzL0tybkhKWG5Yb3JISzZ3dkJMdldXek1xRnBUVzhDeWNHK2NyL21YTFRMcGxSZ1BKZWJGSVFTcnR1WkR0MEgzNnJZSUNsbjhxWlZlQTBsVWFXbFJxUnJlUkJXbHdSM3BqOENOZ2x2VDVHNXZ1QWJyS1NRN2JBVzB6T0RWVGY5R1dTdEZOOTBBYlZ2cURSOFQ3K2hsc1VnbGRxbDhXQWxZOHFkWGo5a1l6ZTZoYWdqT0l3YTlWMVpRRjg3ZXRRci9XbVpjQzU5YkhHSkJ1aytWTDEyN05JenNnSGViYVBYbCtWN0U5L1FDTG9aRGE3b2J3TnAreG9DL2EveG12citPSWx1TytsUk1mSzlPQUhoVTZpUkFKMk0xMUlLYjJiaU8zR0Nhdnc4eXhtMDU2OVhmS053Zmphc1hoMjhlTXcrTkJVM2picU8iLCJtYWMiOiI5MmMxNGMzYzRhMTU4NzI5MzAzZTFjYmZhZDQ3YTY0OTRlMjY3YjJkYThlNGY4OTlkOGFmMDAyOTUyM2FiYzBiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:09:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJFTzZ1cE9PV1BLY3ErMHRSOW9RYnc9PSIsInZhbHVlIjoiZ05QTWRCN05mTU1pUExIZGFyd0tkVnZaUXZqQkNCL2JQRUtpWExBUzVNTWFMdlh6Q1BmejVMSHN5RTU1OGRRRTJycVdiMmt3ODZyN3htY3lTU0hPSHJyYVQvN0liT3p0bWVnYkFTay9WRDdsWGRORFo3K3phYTBHYkZZdDVuRjZCZUxlNnMxSFJJZFNzL1U2WUplVEVUaUcyazdTNE94WTRIeEt1RmV2SmVZakRQNi9BaFZ3OFlzdmJEZEdHdHNZMHFXU2x2YmpNL0N1REJUdnhtdC8xZ1JYSVNhbzIwRTZUdWNBZEx4dm45YVdKYS9NNXFUMW9EbytQY3lOWlpPY0Z4bVFXbTBWcVpGMzlaOUttMmd4OWdQK3pmQnZpN0pHMVY2Tk1VNkhaeXc1TURGRktuWStlelgrS0F1MGYvTVo1RkJhR0FNUHZRRWpHLy9VekV6dG44ZmZJS2FBMkJnUG95UVNtMGNnWWdEZ3dYWWhHUXk0NjFMWXVZMDYrMjR6SVQxZFdua2x3eHpIb2thRHpwZGdyNXc2eFIvUFdnSlNicjNKbVBzZkVtY2EzTzM4YjZIOUFTelhMeHBaSVZHSVAvcUtlempVQVovVktaeXNXSjlMQ2JkbWY0OVBZY0hTZVpjb3pZS0E5Qm53RUFyVUdIMTZBU0RGN25LeWxUc28iLCJtYWMiOiI4MWJkNWQ4NTM4MTllMDg1Y2E0NmE0YjIwOWE4NzE4YTM0OTJiYjIyNmU4NTAzMWNiNTZkMmM4ZDExNWRlODkwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:09:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpGSzJtOHlpZUkzWHpnTGozSTJyZmc9PSIsInZhbHVlIjoiTWpJSTF1YTNwMnFIRHpINVZjVEpuMDlEY1pjV2hmMjRKWW5QSTcxTVZDSHJNMHJkYisycWthcTYyemhXMGU1clNtNmFaWFFsdk16Zk1Fc2FIL2RZUjducHpIcFk5THdIQWFDTjZWWVhvbUc5Z0U2WGN2WWNRZ01sZjIrZEthbmVnTHhOaUFGb0FyWGROazgrMGF5VnNVZ2VYcGJxdUJWc2tSODBXRTVWbXAzZzluQURvbUQ1TklSVHYzL0tybkhKWG5Yb3JISzZ3dkJMdldXek1xRnBUVzhDeWNHK2NyL21YTFRMcGxSZ1BKZWJGSVFTcnR1WkR0MEgzNnJZSUNsbjhxWlZlQTBsVWFXbFJxUnJlUkJXbHdSM3BqOENOZ2x2VDVHNXZ1QWJyS1NRN2JBVzB6T0RWVGY5R1dTdEZOOTBBYlZ2cURSOFQ3K2hsc1VnbGRxbDhXQWxZOHFkWGo5a1l6ZTZoYWdqT0l3YTlWMVpRRjg3ZXRRci9XbVpjQzU5YkhHSkJ1aytWTDEyN05JenNnSGViYVBYbCtWN0U5L1FDTG9aRGE3b2J3TnAreG9DL2EveG12citPSWx1TytsUk1mSzlPQUhoVTZpUkFKMk0xMUlLYjJiaU8zR0Nhdnc4eXhtMDU2OVhmS053Zmphc1hoMjhlTXcrTkJVM2picU8iLCJtYWMiOiI5MmMxNGMzYzRhMTU4NzI5MzAzZTFjYmZhZDQ3YTY0OTRlMjY3YjJkYThlNGY4OTlkOGFmMDAyOTUyM2FiYzBiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:09:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}