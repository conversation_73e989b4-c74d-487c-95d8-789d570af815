{"__meta": {"id": "X546ba8ce06ccb1d6745f7543822ff999", "datetime": "2025-06-28 16:02:46", "utime": **********.799364, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.333909, "end": **********.799381, "duration": 0.4654719829559326, "duration_str": "465ms", "measures": [{"label": "Booting", "start": **********.333909, "relative_start": 0, "end": **********.719053, "relative_end": **********.719053, "duration": 0.3851439952850342, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.719064, "relative_start": 0.38515496253967285, "end": **********.799383, "relative_end": 1.9073486328125e-06, "duration": 0.08031892776489258, "duration_str": "80.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45712256, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02653, "accumulated_duration_str": "26.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.746696, "duration": 0.02518, "duration_str": "25.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.911}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.784566, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.911, "width_percent": 2.94}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.790729, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.851, "width_percent": 2.149}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1163639562 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1163639562\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-183706722 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-183706722\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1842929966 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842929966\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-812775545 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126563401%7C13%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVHbExyc0pnejRsRlBpN2VoUzJBZVE9PSIsInZhbHVlIjoiTGpGbVlXQ2FuNHhNeEdYeTRDYVkzODkzcTFEeDVvU1cvQjJmemVLdkcxcXdmTzJxNys4TlRMSHdoaE1VSW5LRHBGNDZXSlpxcno3Qmh3a1YwSUI3SFI5YzZ6OHBLS1JkWEgzWUdoWkRVR3Q4K0R3RUd3NnNMVVY1cTBQV3V5WXJ2d0VSMDdONnVlUWg4ZWEvV3FiV016OXJWMDJ1SEthU3F5bGZYeDBKVi9nK21pQmNiM1p4N1Mxeklmd2NiT1RjN1RnRlFRRkNhYk56alFKREI1ak1GNDEvdGZ4K05mQ09EL1hQbUIxaXpON0crSGNZbDIyMFhhNzYvQkNYam40N3M3UWNXeUw4L3QwUEJkRWRCRWZ0anRwWDVQb1A5UEY0bmhiUG9saU1mMmVrMzFxQ2NkdnJpT3N4dGpuK0R2SDkwczZFSTZveWVsZ3RmK0w5ZEtNUUZWVEVBRTJYYjhuWWZSbjRlZ1FhRWdueml3Tzg3ZUxVOGV2aVF5eENtZE9ualc3WGM5SnVWK0FWQ3ZEQUo0emxsbFNoT0JCWW02Y1h4RE5KTVZQY2NNSGZWTkppUWFlZnViUndJKzFCUVlmRU1RNGxBeHkycUZ0NTRIcFBHcXhkZHkwR0hzOCs2S1VTa1d0MXk4VnBET3R5OXVvNFZRSk9qcU5tZGc3Yk82SFciLCJtYWMiOiIzNWFhYTMzNDY0NmUxNTE3NGI3NjFjYTA2YmVhOGZkYjQ3ZjhjMDM1ZjE1OTI0MmE1MDIzMGYyYzA0MjAyMTA2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5wZnN1SHJianJDSTR5NkpqL3lGNHc9PSIsInZhbHVlIjoiWjhqaG12blBqemRoZ1dNQlJiTHNPV3ZwdU82UVo3N0c4aUZRU1JzOHRKaXJzZ044cmlqNkFpbjc3WVloYm93UU1JNU1zRnFSaW5pTnh3Wi9IVmxFN3NjU0JKSVZSbnZ0NVdmTHgyODU1ZGs5WVhjUVB1Y2FseHJjbEcvbEN6bVY2a1pOWG95aE9iMi94dHpoU3FDVHNaTTZuRjZHcGdCOVFLdHE3RzduMVhabjhzRlgwSWVBZEg3UmllNTVNUG4vMXl5WVpHRWdmRTU5V2c2SGlheHF5UlNEM0ttWkN6TFRRTThsOFBhZDUyZ2tIVGtwa0VvcGRiMWdWZmMwK3ZrQzJmc2NYcnZJQXh6OGI3ZDcyY2pCVytMVUc2N3RrSWhHZWU5ampReXZnZU1JY3dVOUV6WjBtbFU0V0pmclNxWmpIN010cDlMcll1MEtaeXVCeW9LNVU1RjFUVmNJZWVtcE00VURNYzMrT2tFcU5yZERwUmhPbkFSMjF2VlJjL3N6TFgxMVZYYlFkb1pmWXZoZzhVZmFYK2tkSjYzVmpSQ2VOa3R1dDIwQnFQMnJvY092VjNSbWxTU3NMNFUyTlZwSm4xU3NnaGxSUWk3c3lBclJxYzUxT2NpZE1jYUZmTXpuQ09lVXJBbE1rR0tpTW1qV01RVlorWmt0Q2thRE1hRHkiLCJtYWMiOiJmYWEyNDQ3YjJlMjc2NmJmZDczYzIxOWJmNTBhOWJmOTY0YzY4NzFlODQ2YWQzYzhiNDg0OTliNGU1MWQ4MmUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-812775545\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-811827770 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-811827770\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1771169732 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktGSnNPTFJQNkh3c25XWldWQWZLK0E9PSIsInZhbHVlIjoiOTV4REpSTitrWFI3NmFrcjc0aTQxbmgwaVgvU1l2Wmc5UGRNOFEvTjJ3aEtsa2cvTTNrd3dHcFFTZHdqc3RzQ2llSjgxYXlMWDVnNGxrY2lWOTdxbjlFa3BBM2ttam01bjBpOFNlYkJPbFdVY3ozZWVxQWFpWEpoV2QyelZPQkNoQzdwc0lQZlVQZFFTNEFwRTI4VzYwTUR5YTU4YzljWm5Fa3lZb3l1NGpENGVHemd5MndUR0V3aCszM1hPdnA2ampEWS9RS05QUERXTWxDKytHNFhkNGdlcjdZMjZtb3Axd3ByRUtDUHRkVXdXcmorMHVVWUV5d0J5RkxYVThzVEpUVHRFZVJqOEx5KzFLSlB2cWxyWC9yUWo1OSsxTnZIT2RNa1Vod25UTk8wVmhVV3pBZHpEOVVIMmQ2TjZ4cXdxSnV3TUVMRVEzS3BoWktjWE1mblYzYXZjdENkREZ0Z29IQXhHc3pOWm5ZZHBwVzZDL1Z4ZjJydzUvV0hBa2d2MTg1aXFWeEd5NEdrczRwTVo0cWtPM1ZTN0lETTBCWmJEYUxqKzdYNTVaYkoxL0lTVFlvNFRSZ0dvR1lPWGtFeUl1L3ViZFhDenNEVlJnNm5ycVVhd3htdFdwSEZGelM2TEJyM0FaQmdSQmlhUS9jMDNQN3ZOWmU0NjNqOVFaRzgiLCJtYWMiOiJhYmE3MTRhNmFjZmJhMWMzNjJjNzFmMjVkZTQ2ZjBjNTYzNTY3MmM4NmRjMmJlYzdiYjlkMmViZWJhMzUwMTZmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ing1bVE3bXJOMnNtK0Z0b3o3NFN1T1E9PSIsInZhbHVlIjoiL0MrWmhXUnJuamNmZnFnMXVNN0xzOUpzejFzOCtqRDFMYnExSU0wZ1l5MlJNRHdGTlJnRDZVZkQ2MWoxWncrNUU1K0dmaXNsOE5RWi9UN2k1L3hsOWhLTmIwKzk4OWJSOS9rdU9BTnJncFJaL09SZHhTR1cvTnBvcTFLL01yYXpadUtYd2hhV04yVU9LZ3FUOGUrNi9UaUpkRVlBbnZuc1FESWRJN1pSY2t0aW1TN0NTWEZURnFGcXNIRTBMbXN1RUhIcUt6clg4MzZ1MGJRYkNzTVI3MS9SSUhVM2VjTW12SFhYN1R6ZDBrOGxYOTZQTEIzaGh0WXhEcGdYb0s2WDJJSnc1VXN2ZEYvdmxOR2dQRWNYMWhBVy9pUll3cThOZTk5TEZvQnJKVnBROTVXRDdkTS8rWmZsTTJDdWhCRnVoOHQwUmg4dnlUZmJyNU05TG5LVE5FWnJpZXdqK0o3cTZvVHN4d2dXMmZTTWdJS2s3Y2RJUmp6dGl6aThEa2JvZ1ZKWHFPUjg1eDJMR0IzY0Q2MjRlNVZaMWlKZGxKWjFyTjB2RVA0ZzRPSmVFWXc5RC9ld203VkNsZTRqOS9GM2pUMC9JTG1oakVkN2R0eE9vK2dTTDJnL0FVZjBpSkVodC93dHJRR3NlaFJYM0RjYkc1RXN0RVlNRzM4MDN0Z3AiLCJtYWMiOiIzNjQ4MDlkY2RhODc3ZmJkYjA2ZGExNjdkYTY1YjA0OTQwMzkzNTVlYmFjYmMxZTY5ZWM1YjNlMzFiMTM1NjU4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktGSnNPTFJQNkh3c25XWldWQWZLK0E9PSIsInZhbHVlIjoiOTV4REpSTitrWFI3NmFrcjc0aTQxbmgwaVgvU1l2Wmc5UGRNOFEvTjJ3aEtsa2cvTTNrd3dHcFFTZHdqc3RzQ2llSjgxYXlMWDVnNGxrY2lWOTdxbjlFa3BBM2ttam01bjBpOFNlYkJPbFdVY3ozZWVxQWFpWEpoV2QyelZPQkNoQzdwc0lQZlVQZFFTNEFwRTI4VzYwTUR5YTU4YzljWm5Fa3lZb3l1NGpENGVHemd5MndUR0V3aCszM1hPdnA2ampEWS9RS05QUERXTWxDKytHNFhkNGdlcjdZMjZtb3Axd3ByRUtDUHRkVXdXcmorMHVVWUV5d0J5RkxYVThzVEpUVHRFZVJqOEx5KzFLSlB2cWxyWC9yUWo1OSsxTnZIT2RNa1Vod25UTk8wVmhVV3pBZHpEOVVIMmQ2TjZ4cXdxSnV3TUVMRVEzS3BoWktjWE1mblYzYXZjdENkREZ0Z29IQXhHc3pOWm5ZZHBwVzZDL1Z4ZjJydzUvV0hBa2d2MTg1aXFWeEd5NEdrczRwTVo0cWtPM1ZTN0lETTBCWmJEYUxqKzdYNTVaYkoxL0lTVFlvNFRSZ0dvR1lPWGtFeUl1L3ViZFhDenNEVlJnNm5ycVVhd3htdFdwSEZGelM2TEJyM0FaQmdSQmlhUS9jMDNQN3ZOWmU0NjNqOVFaRzgiLCJtYWMiOiJhYmE3MTRhNmFjZmJhMWMzNjJjNzFmMjVkZTQ2ZjBjNTYzNTY3MmM4NmRjMmJlYzdiYjlkMmViZWJhMzUwMTZmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ing1bVE3bXJOMnNtK0Z0b3o3NFN1T1E9PSIsInZhbHVlIjoiL0MrWmhXUnJuamNmZnFnMXVNN0xzOUpzejFzOCtqRDFMYnExSU0wZ1l5MlJNRHdGTlJnRDZVZkQ2MWoxWncrNUU1K0dmaXNsOE5RWi9UN2k1L3hsOWhLTmIwKzk4OWJSOS9rdU9BTnJncFJaL09SZHhTR1cvTnBvcTFLL01yYXpadUtYd2hhV04yVU9LZ3FUOGUrNi9UaUpkRVlBbnZuc1FESWRJN1pSY2t0aW1TN0NTWEZURnFGcXNIRTBMbXN1RUhIcUt6clg4MzZ1MGJRYkNzTVI3MS9SSUhVM2VjTW12SFhYN1R6ZDBrOGxYOTZQTEIzaGh0WXhEcGdYb0s2WDJJSnc1VXN2ZEYvdmxOR2dQRWNYMWhBVy9pUll3cThOZTk5TEZvQnJKVnBROTVXRDdkTS8rWmZsTTJDdWhCRnVoOHQwUmg4dnlUZmJyNU05TG5LVE5FWnJpZXdqK0o3cTZvVHN4d2dXMmZTTWdJS2s3Y2RJUmp6dGl6aThEa2JvZ1ZKWHFPUjg1eDJMR0IzY0Q2MjRlNVZaMWlKZGxKWjFyTjB2RVA0ZzRPSmVFWXc5RC9ld203VkNsZTRqOS9GM2pUMC9JTG1oakVkN2R0eE9vK2dTTDJnL0FVZjBpSkVodC93dHJRR3NlaFJYM0RjYkc1RXN0RVlNRzM4MDN0Z3AiLCJtYWMiOiIzNjQ4MDlkY2RhODc3ZmJkYjA2ZGExNjdkYTY1YjA0OTQwMzkzNTVlYmFjYmMxZTY5ZWM1YjNlMzFiMTM1NjU4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771169732\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1485596004 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485596004\", {\"maxDepth\":0})</script>\n"}}