{"__meta": {"id": "X96c22a222f62ea4fa4909e97ac431947", "datetime": "2025-06-28 16:30:48", "utime": 1751128248.057989, "method": "GET", "uri": "/pos/1465/thermal/print", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 163, "messages": [{"message": "[16:30:48] LOG.warning: Implicit conversion from float 160.79999999999998 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 186", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.015189, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.015507, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.015641, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.015756, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.015869, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.015957, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.016056, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.016161, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.016267, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.016371, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.016479, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.016584, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.016704, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.016806, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.016909, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.017018, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.017147, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.017333, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.017456, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.017641, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.017786, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.017974, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.018124, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.01824, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.01834, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.018441, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.018544, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.018701, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.018863, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.018988, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.019109, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.019285, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.019466, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.019591, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.019693, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.019797, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 80.60000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.019896, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 82.80000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.020001, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 84.20000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.020096, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 87.60000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.020197, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 89.00000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.0203, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 92.40000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.020409, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 93.80000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.020526, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 96.00000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.020636, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 97.40000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.020734, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 100.80000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.020832, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 102.20000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.020927, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 105.60000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.021016, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 105.80000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.021101, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 108.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.021247, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 111.80000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.021463, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 114.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.021648, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 116.60000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.021734, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 118.80000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.021835, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 119.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.021919, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 122.40000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.022, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 125.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.022084, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 127.20000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.022176, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 128.60000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.022272, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 132.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.022363, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 133.4000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.022441, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 135.60000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.022541, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 137.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.022764, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 139.20000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.022943, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 140.60000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.023143, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 145.20000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.02329, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 146.60000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.02341, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 151.20000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.023516, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 153.80000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.023615, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 156.00000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.023717, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 156.20000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.023811, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 158.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.023916, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.024038, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 160.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.024264, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.024496, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 160.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.024754, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.024899, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.034272, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.034416, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.034528, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.034606, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.034681, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.034759, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.034844, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.034916, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035001, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035082, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035159, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035234, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035316, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.03539, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035465, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035536, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035606, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035676, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035744, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035814, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035885, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.035954, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036031, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036108, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036183, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036261, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036333, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036411, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036484, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036564, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036662, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036734, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036803, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036893, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.036976, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.037047, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.037131, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.037212, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.037291, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.037373, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.03747, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.037552, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.037649, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.037738, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.037836, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.03792, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.038023, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.03811, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.038203, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.038287, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.038358, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.03844, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.038536, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.038626, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.038707, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.038807, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.038909, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.039001, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.039081, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.039163, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.03925, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.039346, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.039441, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.039533, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.0397, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.039866, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.039994, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.04012, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.040236, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.040341, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.040442, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.040542, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.040651, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.040751, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.040857, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.040957, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.041062, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.041189, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.041321, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.041439, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.041558, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.041661, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.04176, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.041868, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.041969, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:48] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": 1751128248.042073, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.470509, "end": 1751128248.058212, "duration": 0.5877029895782471, "duration_str": "588ms", "measures": [{"label": "Booting", "start": **********.470509, "relative_start": 0, "end": **********.89925, "relative_end": **********.89925, "duration": 0.4287409782409668, "duration_str": "429ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.899261, "relative_start": 0.42875194549560547, "end": 1751128248.058215, "relative_end": 2.86102294921875e-06, "duration": 0.15895390510559082, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52469872, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.thermal_print_clean", "param_count": null, "params": [], "start": 1751128248.008905, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/thermal_print_clean.blade.phppos.thermal_print_clean", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fthermal_print_clean.blade.php&line=1", "ajax": false, "filename": "thermal_print_clean.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.thermal_print_clean"}]}, "route": {"uri": "GET pos/{id}/thermal/print", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@thermalPrint", "namespace": null, "prefix": "", "where": [], "as": "pos.thermal.print", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1709\" onclick=\"\">app/Http/Controllers/PosController.php:1709-1768</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.0092, "accumulated_duration_str": "9.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9435408, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 19.674}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.956162, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 19.674, "width_percent": 5.87}, {"sql": "select * from `pos` where `pos`.`id` = '1465' limit 1", "type": "query", "params": [], "bindings": ["1465"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9590352, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 25.543, "width_percent": 6.848}, {"sql": "select * from `customers` where `customers`.`id` in (10)", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9656668, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 32.391, "width_percent": 8.587}, {"sql": "select * from `warehouses` where `warehouses`.`id` in (8)", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9689739, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 40.978, "width_percent": 7.935}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` in (1465)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.972545, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 48.913, "width_percent": 28.478}, {"sql": "select * from `product_services` where `product_services`.`id` in (0)", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1711}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.977743, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1711", "source": "app/Http/Controllers/PosController.php:1711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1711", "ajax": false, "filename": "PosController.php", "line": "1711"}, "connection": "kdmkjkqknb", "start_percent": 77.391, "width_percent": 8.261}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1737}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.996489, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 85.652, "width_percent": 7.935}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 121}, {"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 21, "namespace": "view", "name": "pos.thermal_print_clean", "file": "C:\\laragon\\www\\erpq24\\resources\\views/pos/thermal_print_clean.blade.php", "line": 279}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1751128248.025645, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 93.587, "width_percent": 6.413}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\PosProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPosProduct.php&line=1", "ajax": false, "filename": "PosProduct.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1465/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos/1465/thermal/print", "status_code": "<pre class=sf-dump id=sf-dump-1629063474 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1629063474\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1041307784 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1041307784\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1325883585 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1325883585\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1766739589 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128238007%7C50%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZXemh6ZmVCaUdWZ2ZCMERzTzEyRXc9PSIsInZhbHVlIjoicjBTbU5UQWZVYkhsUUNJLzRQRlluUUxpUnNTUk9ub3R3MEtFRGpRcThWWWVWUXVDeTd1OFZWbGFwa3BzTEdHY1VLZGdtZnMwTWRSM2xObFdoY01YSC9aL1IrbkU5bmw4N2NSVmZpaFV1OXJPNlk4VWlGUnVpcFppVGZ2aWszZFVWVUVuaUlCQWd3S21Ib1RNYXA3aUR3bHZsY2RaVCtIZlJhQW1haEJuZVlYRFVHT01vTE00aktXL1p0bkQzaDFJRW1IbmxybUxRZkFGeTFVS3hLUjBSWXI2ZGdScWJrMnBTajBrMjJ5cmpQTlJqUEREUi9JMEF3TVFHM25CMFd6K2NzZHROS0tDS3RnTjhKcjlsR3E5ZWdZYXNZaEVEYXBVNDEvVEdnSzZGdU1DY1pzekZiQVlwQllBajJQcG5qZS92aXZ3eG1vOCs0Y3NnYllXU0czNlJ6SUcvQmFoTmRQSDc4Rk5BVjNMaWhibExGd2ttdG9jSm5QalVFNkZmeUtFb2VvRm9IS0VGYmdGbUdRQWpxS0VJTlJ3ZlVETHJBQUIxSnNiT08xTzZKb1lkRmxqSEhpL2V6Q2YxZDZwQ3NkcW9EM05QZVZvbXpZUnVjaFFjK2hmejhzcXQ4eXlDbVdTRDBmSTBJZThTVHdIZVFxWnorUVdYS0hZb2xtWi9KbVciLCJtYWMiOiI5ODQ5Y2VmMzFjZWU3OTIyNjBiY2FiMTdmYjdjZDE5MDhkYTI1NGQwMDdkNThhMDRiNGQwZjg1NDNlZGEwMWJjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVrUnIzbUNlQkRFZC9nUGdTYjVXOFE9PSIsInZhbHVlIjoiZTk4SnptQ3MyNGEySElMQisvKzlkd0ZsMzYvOENqN3A3Y1ArZXI3TThSeEJtSGdkVEZCc2F4QldDbDJYWkNmOWhUR0dlaE1NTm5BMnZ1eHFsS0dpT25XdFlvNHJ3TFhCK3FmY3ZUVm43WEJMalhoZFByTzRlZVZkUVArMHJaeU1WVzZIdWRHQ2JQbXFNUVJ1T1UwbWp3VEIxMzVONW9JZXB4OG9iU1pqME5mMkhwYWhEaFpDWW5sYmJ4ajJOUERnZ0tnR3d3UnhEc2laRmNlaE1maTBXZThUZktiRHZLNFpnemw2YWo4L1dQQ09aNGxPZnBZMjFZK3YvelZkUXBYb1Y0ZGRmbDJISGh5K3lKaUo5YnJLcWlVSW1PWVJXNlpYd0RycCszQWV6REtlZkU1NXhzamVJOXVlNUdoZmlYODhCZHBlQURGV3hsL3gzSktqWjdaODVVa0dyWmZtUGFidTNpQlFwMnNwZzR6aXoxNWt5MS82bGZWQmtlc21nUzQ5R25BRWc5RmVXWGgyOExLUGdRRXRlMnA4MFYwRFk3aGxyTW5tbEVYVTUzenhDZnlrenBXbFBHTEVldTdDSVV6dlE0UjFpSVRrdDdOUXg1RjRMd01XNlRJeHhLN05MaVN2blM3cnBqZnQycG1LY0NWVFJ6c1cra3cwUHp6dnRxQU4iLCJtYWMiOiI2MGNlODE2YTEwMjkxOGZjZGY1YTZlMjlhNDMyZGIzNWQxY2M4ZmVjYTI0MmZlOTM1MWM5MzQ4ZWM2NzYyZmIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766739589\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-568306177 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-568306177\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1902724900 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpDMjBseXE1eWZGL3dMdTRVMkUrbHc9PSIsInZhbHVlIjoib3BEbk1lbXpCM1FKYjluQTE4ZzBBZVlBdm1xYWd0eExLSHlWb1JsOGc4ZEx0OEt2UEE0a3hBVFkwSU41T0dVQWZjNmdLZFE0ZkxHL2wraWcreFp3dW10a2o3N01ycU0yNnllMEVCSjFCN2hpS3N0djg4Z3FXSTUvR012ajBrbFNRb3hzSi9idU43SjM3Snp4dEZPeTU2Wk8rTFRRQUJYMUZTYWZyNXcvZXVoUHRWSTJ0WW9BNUo2eThOVXdmdjh2VUs2SFM1L1oyM0s0N21Hb2dFdFdFWkVjOVRyTlo2aG42QXlaS3ppeG9QemZaUlg2MWE2OExQZU0zck16VEkvNlpMZTB5alVIRzgzZVl0RDFJY2FIaE9KcGk0WkoxNEtUb3JtY3VqYk1GbCsxN2hKaHJXWVl2VGxDaHpOaVZPaUtZSTFHdDl5NGo2TUZETzRxb2xkVmtJc0N2dU1Ydmp6QVBpMDN2N01ORkVLMmMxMmxyZXJMeHd0UE1yOEs0aUVKYU1kelhwSS95QkpzTE1UdmZiWVhlWU1mV1V3ckdHenF1SlBlTzZpbGR2bUVvK3ZKY095d09KaHowTDlNUUZER3Zmd3BHTVB1SXN5UGM1SjExbTRHU2VvSzZCaDZ6U3VEdjVwNmkzaXllaW5vUlo0K3QzcUpveWRwcnBVZDlJMGQiLCJtYWMiOiI4Yjk2ZWYxMTg3MDI1ODIzNjZiYmEwMWY0OGMwMGU1ZjlhZDRjMjAzNzI2MDI5ZDVlMDRiNGMzOTQwYzBhMDYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZiejlKODUxS1BkRjA1RW1KbEgrSUE9PSIsInZhbHVlIjoicm1zVVo4dVhqaFBra3h5TXZzV0Z1MW5CdmRmeHJmcldscGpmZUZ2ZllheC90M0N3OTl3L2MzNVQyKzR1TkplSXYrTDV5bnptZDc3aVVQazFzSG92eEx2RTkxTVdIaTdERzYrT3RuQWRQN1hTblZXb05MaklMbUEyeS91Q1Z6V1huQ3FCOWNIVlQ2TG5ycThpRnZpNmhWMzNKNVQramJBeDFNSEtPODNYejVSdnh6SG1WNHJnZ2piVm9HdjNSRHFmeWgrQmRacCs3VTNRRTV5UW53TmNRQkt2MGRHQm5SVVlRTy9CYXJSVktaUEQ3UGliSjdMcUJZbUVkeTZlZUd3Z2hpOW5FTmlVV05tRVlqODg4RkQvTnR1OCswN2dlQUMwVXl6c055L0l5NEJWcXFBK2pNaVY0M0lGVVhFaHJ5bitJdGlWMG5LQXZXMnZCaWtCbWpLeHl2Vi9FYkF6QmpDeWZDRzJRYTZuNEdXdUhBRUp0d1F0dkhORDVTZGNaUm52UllMeFVyWUZZd1ZLS05zWEhrUU0vY2lZenB0WnArZ3hvNnVIbm1XMy8yZXpNUU5kMitwVFhRU1JkQ3VCUjRqUUVENng2RllZRC9JZkp5dkxFeFBxanhzc1lEd3RKUXdzK1p4OUczUFZ4a1pSSWlBMEg0aU5pSFdEdk05QVZlcUwiLCJtYWMiOiIwZTMwN2NkOWMzYTlmOThjODgwMjBiODAwNTE3YTc2YThkMGVkM2IwOTdjNzg3Y2RkZDlhMzMzMTRhZDRlZWVlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpDMjBseXE1eWZGL3dMdTRVMkUrbHc9PSIsInZhbHVlIjoib3BEbk1lbXpCM1FKYjluQTE4ZzBBZVlBdm1xYWd0eExLSHlWb1JsOGc4ZEx0OEt2UEE0a3hBVFkwSU41T0dVQWZjNmdLZFE0ZkxHL2wraWcreFp3dW10a2o3N01ycU0yNnllMEVCSjFCN2hpS3N0djg4Z3FXSTUvR012ajBrbFNRb3hzSi9idU43SjM3Snp4dEZPeTU2Wk8rTFRRQUJYMUZTYWZyNXcvZXVoUHRWSTJ0WW9BNUo2eThOVXdmdjh2VUs2SFM1L1oyM0s0N21Hb2dFdFdFWkVjOVRyTlo2aG42QXlaS3ppeG9QemZaUlg2MWE2OExQZU0zck16VEkvNlpMZTB5alVIRzgzZVl0RDFJY2FIaE9KcGk0WkoxNEtUb3JtY3VqYk1GbCsxN2hKaHJXWVl2VGxDaHpOaVZPaUtZSTFHdDl5NGo2TUZETzRxb2xkVmtJc0N2dU1Ydmp6QVBpMDN2N01ORkVLMmMxMmxyZXJMeHd0UE1yOEs0aUVKYU1kelhwSS95QkpzTE1UdmZiWVhlWU1mV1V3ckdHenF1SlBlTzZpbGR2bUVvK3ZKY095d09KaHowTDlNUUZER3Zmd3BHTVB1SXN5UGM1SjExbTRHU2VvSzZCaDZ6U3VEdjVwNmkzaXllaW5vUlo0K3QzcUpveWRwcnBVZDlJMGQiLCJtYWMiOiI4Yjk2ZWYxMTg3MDI1ODIzNjZiYmEwMWY0OGMwMGU1ZjlhZDRjMjAzNzI2MDI5ZDVlMDRiNGMzOTQwYzBhMDYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZiejlKODUxS1BkRjA1RW1KbEgrSUE9PSIsInZhbHVlIjoicm1zVVo4dVhqaFBra3h5TXZzV0Z1MW5CdmRmeHJmcldscGpmZUZ2ZllheC90M0N3OTl3L2MzNVQyKzR1TkplSXYrTDV5bnptZDc3aVVQazFzSG92eEx2RTkxTVdIaTdERzYrT3RuQWRQN1hTblZXb05MaklMbUEyeS91Q1Z6V1huQ3FCOWNIVlQ2TG5ycThpRnZpNmhWMzNKNVQramJBeDFNSEtPODNYejVSdnh6SG1WNHJnZ2piVm9HdjNSRHFmeWgrQmRacCs3VTNRRTV5UW53TmNRQkt2MGRHQm5SVVlRTy9CYXJSVktaUEQ3UGliSjdMcUJZbUVkeTZlZUd3Z2hpOW5FTmlVV05tRVlqODg4RkQvTnR1OCswN2dlQUMwVXl6c055L0l5NEJWcXFBK2pNaVY0M0lGVVhFaHJ5bitJdGlWMG5LQXZXMnZCaWtCbWpLeHl2Vi9FYkF6QmpDeWZDRzJRYTZuNEdXdUhBRUp0d1F0dkhORDVTZGNaUm52UllMeFVyWUZZd1ZLS05zWEhrUU0vY2lZenB0WnArZ3hvNnVIbm1XMy8yZXpNUU5kMitwVFhRU1JkQ3VCUjRqUUVENng2RllZRC9JZkp5dkxFeFBxanhzc1lEd3RKUXdzK1p4OUczUFZ4a1pSSWlBMEg0aU5pSFdEdk05QVZlcUwiLCJtYWMiOiIwZTMwN2NkOWMzYTlmOThjODgwMjBiODAwNTE3YTc2YThkMGVkM2IwOTdjNzg3Y2RkZDlhMzMzMTRhZDRlZWVlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902724900\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1183615818 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1465/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183615818\", {\"maxDepth\":0})</script>\n"}}