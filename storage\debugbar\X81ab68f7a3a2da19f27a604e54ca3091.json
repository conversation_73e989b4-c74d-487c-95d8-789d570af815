{"__meta": {"id": "X81ab68f7a3a2da19f27a604e54ca3091", "datetime": "2025-06-28 16:21:00", "utime": **********.393291, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127659.979333, "end": **********.393303, "duration": 0.4139699935913086, "duration_str": "414ms", "measures": [{"label": "Booting", "start": 1751127659.979333, "relative_start": 0, "end": **********.339323, "relative_end": **********.339323, "duration": 0.35999011993408203, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.339332, "relative_start": 0.3599991798400879, "end": **********.393304, "relative_end": 1.1920928955078125e-06, "duration": 0.05397200584411621, "duration_str": "53.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46425152, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2441\" onclick=\"\">app/Http/Controllers/PosController.php:2441-2475</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00216, "accumulated_duration_str": "2.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.375049, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.556}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.38651, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.556, "width_percent": 19.444}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-861875749 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-861875749\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1810311763 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1810311763\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1966437585 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1966437585\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1187156801 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127652854%7C42%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJ0TnNFa1VOdTN1T3VKRFdXUnFhd3c9PSIsInZhbHVlIjoiR1pmVkh3ZC9OZ1BpcitJZjZEc09FS25CbnN4LzNqWi95Zkh0b2w3cGtUZlh3dUpTRDM4NGJuTVpsVE9IZGs5STZ1RG5EaE5ONFUvK0xRbko4Ry9GSyt0a1N6cDRUakFzRjlydlNFZ1NzUmZLdkEycjZlUCtXREszNzNYSmRCR0F4dFdpQU10V3c1OFVDQnk0TmNpVC9sY3JudDhnSUFwRWRoNVhRV3BxTW56dWhaVmx5TGFVTG5UVGh1b2FWczBIRzBwK1h5VkJKUE9LUXdRSzdZSkd4c3J6Wk1WVHhEVW80TG9GbmVZQmh1Lzlka2VqU0NNOTcrSnUrZWdCUlRQbUkreTRodC8zSjd2YU5RVFRGb0ZjUm1Tb1pVNGcxQllQaEF3bFlZN3hHVXBZYitxYVRMVmZJK09IemhBVjJsS1EyTTJSaG5Ea0R2QlJuL1huYXVTNFh2NzN2TDNZbHp1QzVOTEVRYzNyb1pRbHF5ZFRjbGNFQnBPTHJzd3NTVjd0TVpHS255YjRHQVN4WHVuNHZNblZONzhUU0gvUTB4VmdEUWpaUnZBL3hPUUVzY2FvekF2c3Q0eGlmVUFUSmZabEZISW55K2NXV3JpSzVReUoyS0RBSHZLaDNoUXNjcStBS2hMSUEvMmx1bXp2bFN3OGxaN1BkTDBNVE1jajd1MEsiLCJtYWMiOiJlYzJiMWFkODRhMzA3MmE0YzAwZjY5ZGNiNGFhOGI3Mzg5MmIxYWExN2RlNjBmMmQ1NTE1MmI2NjE2MzEzMzhiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklPc2VBUzJRNVFvSWQ2MEZ2blo3dXc9PSIsInZhbHVlIjoiZjA3amRuN2dPMlZQU2V0eFRURGp0ZzFsTEN2Y0NacVNjRW04cjdmNld4a2QvOWZ3RHBtVnJzOThJazhrR3BOS3JUZXpmUXpKVEM3c2xFQ0cvRkZ6aW1jd2kwNGdKclp2NHFnOExvMk1oaithT1F1U281dVFSZDU3bGlibXZtbjRUS2hWQlJibGk1bjFhNHlIdU1JY0RPb0R1aFhiRmZjT1FMRk5WZWhQS3VsTW1YM0lOTmVuaUhwcW1UU1Fzc1ZldmZqbGpPc09mV1N0ZHhXRk9tcHA4alQrQ2dXK1RLKzFrRWRCRngrL2twcGxkcTlKQU9vOGZ2c1lxemg2Y1Z0TTlqYm1BZ1VGVWhmWmpieGkwbm5oS2YxUEdTaW5JeVRrZEo4NU05VDBuVVVKMkVYb0JPR3NVVkd0Wm1SZEtqcU50ZHNYZi91TWhQWHF2TXoxSFNyZHFOUnlRWFRNOXpCUWFjK0hGbGk2MGl5Q09VL0lmc0Rhc0ZyVjM0SGtYRnFFNDBpZlkveHcrVW9UbTV5MGIzNTdMTmNRQW5iaVJFV1lQd05POTU1Q05pd3dHczNpWGxFNkM2YWJpYnBwYnR3cmZTelpZRWFJUVE5dGtIRUVabHFxUkZLVXZGR1dVT1dCWU5nS29jYUV1blU4OVh0QnhvNVBlZGtxb01xQ0I4TVciLCJtYWMiOiIyMDliNjRlMDAxMTIwMzAzNzMyMjg4MmI2MDVlZjJlMjc2NjBjNzhmZmQxYzdmNzM2ODc5NTY2MmUwNWMwZjI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1187156801\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2082891388 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082891388\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1523528323 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:21:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVlWGN6RG4wbGNzR3h3V2tWNjIxM1E9PSIsInZhbHVlIjoiVzZ1NzdnZmVuMVhRQUg2MVBlaUdRMnIvZDBXZFI5b1lZTzVSVEs5NnBBWWRwRnVlWTFOcGVSSW1sT3RnM2RIRjlWbklMc3dpSi9kUStaZzZQRWZqNzlaTFVHR01mcHhOOTNlR2U0NEFhbEpoK081MGptSXRTblA4TkR5aDBxNy9pbW1RcTBROEhHM1h4UlNJTS9xSzV1SmR2M0ltajZOVUc4ME9vRTZSdVRnL2tVMEJPUUFBbWErQ2xCRVVTR0tTekFPUkRKdytiQkUzbUdCeGV1NmpQOG9rdUhjQ2E5d0Nkb2dRdVFvb1l0elRIYnB0bUpEbzQxdklsckt2eXMyaXpjZmlKTU1PQjhmN2t3UVZ0TWJRWUxIMlNnTlZxQU1HUmh4czNkUEZMOXJ6Vzc1YXpYQVo4MEFYUUNHWWdqdXAzMEJiazdnZlRPQlcvR21oNUV4TUNpb2ZTMFl6SXgzV2RCNUpVd2srOUdhdVpMNWtPY3NyMnJKTWpMenJBYUMyVHp6bnBNRUlKNnFrVjlMZi9tVE9aYmhZNTBmM3hxOWI3dEtwcVJhRHB3ZUNDTDh1S1ZxRXVOTTQybmVmMkFudzVBejJkcEl3SU5OWWI2ZXIrUy95NzVza3VLTG1uRW5NNUQxNDhMaEg3MkJiVU53Y3p3NE56QU9yOU1vWnpoYXMiLCJtYWMiOiIxN2M0MTE0YmMyNzRhOWI4M2MwYWZjOTVmMmFkYjYyMWQ3OGJlYWQ0NTc3N2NhOTRmZGYxMDJkMjhjNTMzYWRhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFXSm8rWCtJdGpVZzNmWUhYU0lIeFE9PSIsInZhbHVlIjoiK3JnVE5VcTFuOG1rUVNrVGxKVmJGL2phU2MwQTJIczdkTVc4blVNYXExZ3dsN3ZvaWRTRDZoUTE5NlFSZ1dRRnljM3VLUkh3d09SR2lxSnJCUDNORGt1cHd0eU5yY2Q3bE5XODJ2U0JST3NhZmpGcUlzbWVkSWdTbHdMQUJlM0JDSWpRRFNibkR1SEFEVkFVMFNhWlVaYlEvWE5wajc1aGpCWi91TzhoMFhFemFodHV2dFJiWnkzVXdIcTk3bE9EKzFuQ2xIWTU4TVRtNE9WSVhpV0srYXYxaVRVUWtpQmNVUnBUTi9KN0ZaZ21KREpMc1FhRjFma2xUVlFDaytUczdIdWc0SE50YkdsOTVGK2pIdUd5THp0NDYvenFJTUgycyt2N2dHWkNBMzRsTno4R0RzZXU4aGlRUStEKzZUSzlKWmNTclNrYjlZYkxnZmNkQWROdWxzb1J1WDhSNzhiSm50OU9RTVBjSE1zRzViK3YxYU12NGcxbnVXcE1FalZSbUNOZk8rQlFtZ3A1aXJsSm54K3UzeS9nQnFPT1VMWnFJdzhyNnNpSVQ4NWlRUDNOMUJNRXlaanRZNDYrWmtyOTE3ZWo3WjlZTXpEeEY4TEtJdXRPM1haZ0ZUYXRTRGprMWkzeXhiaVFLaCtHbnAvdjRtdU5jLytSZEpoMDc3cFIiLCJtYWMiOiJmOTIyNjk1ODMwMzVmMWFlODE0YTZmMjQ1Y2Y1ZjJjMWVmNGM5ZWM2ODg5YmE2ZjdmMzdhMzAyNWZmMDg3MzAzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVlWGN6RG4wbGNzR3h3V2tWNjIxM1E9PSIsInZhbHVlIjoiVzZ1NzdnZmVuMVhRQUg2MVBlaUdRMnIvZDBXZFI5b1lZTzVSVEs5NnBBWWRwRnVlWTFOcGVSSW1sT3RnM2RIRjlWbklMc3dpSi9kUStaZzZQRWZqNzlaTFVHR01mcHhOOTNlR2U0NEFhbEpoK081MGptSXRTblA4TkR5aDBxNy9pbW1RcTBROEhHM1h4UlNJTS9xSzV1SmR2M0ltajZOVUc4ME9vRTZSdVRnL2tVMEJPUUFBbWErQ2xCRVVTR0tTekFPUkRKdytiQkUzbUdCeGV1NmpQOG9rdUhjQ2E5d0Nkb2dRdVFvb1l0elRIYnB0bUpEbzQxdklsckt2eXMyaXpjZmlKTU1PQjhmN2t3UVZ0TWJRWUxIMlNnTlZxQU1HUmh4czNkUEZMOXJ6Vzc1YXpYQVo4MEFYUUNHWWdqdXAzMEJiazdnZlRPQlcvR21oNUV4TUNpb2ZTMFl6SXgzV2RCNUpVd2srOUdhdVpMNWtPY3NyMnJKTWpMenJBYUMyVHp6bnBNRUlKNnFrVjlMZi9tVE9aYmhZNTBmM3hxOWI3dEtwcVJhRHB3ZUNDTDh1S1ZxRXVOTTQybmVmMkFudzVBejJkcEl3SU5OWWI2ZXIrUy95NzVza3VLTG1uRW5NNUQxNDhMaEg3MkJiVU53Y3p3NE56QU9yOU1vWnpoYXMiLCJtYWMiOiIxN2M0MTE0YmMyNzRhOWI4M2MwYWZjOTVmMmFkYjYyMWQ3OGJlYWQ0NTc3N2NhOTRmZGYxMDJkMjhjNTMzYWRhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFXSm8rWCtJdGpVZzNmWUhYU0lIeFE9PSIsInZhbHVlIjoiK3JnVE5VcTFuOG1rUVNrVGxKVmJGL2phU2MwQTJIczdkTVc4blVNYXExZ3dsN3ZvaWRTRDZoUTE5NlFSZ1dRRnljM3VLUkh3d09SR2lxSnJCUDNORGt1cHd0eU5yY2Q3bE5XODJ2U0JST3NhZmpGcUlzbWVkSWdTbHdMQUJlM0JDSWpRRFNibkR1SEFEVkFVMFNhWlVaYlEvWE5wajc1aGpCWi91TzhoMFhFemFodHV2dFJiWnkzVXdIcTk3bE9EKzFuQ2xIWTU4TVRtNE9WSVhpV0srYXYxaVRVUWtpQmNVUnBUTi9KN0ZaZ21KREpMc1FhRjFma2xUVlFDaytUczdIdWc0SE50YkdsOTVGK2pIdUd5THp0NDYvenFJTUgycyt2N2dHWkNBMzRsTno4R0RzZXU4aGlRUStEKzZUSzlKWmNTclNrYjlZYkxnZmNkQWROdWxzb1J1WDhSNzhiSm50OU9RTVBjSE1zRzViK3YxYU12NGcxbnVXcE1FalZSbUNOZk8rQlFtZ3A1aXJsSm54K3UzeS9nQnFPT1VMWnFJdzhyNnNpSVQ4NWlRUDNOMUJNRXlaanRZNDYrWmtyOTE3ZWo3WjlZTXpEeEY4TEtJdXRPM1haZ0ZUYXRTRGprMWkzeXhiaVFLaCtHbnAvdjRtdU5jLytSZEpoMDc3cFIiLCJtYWMiOiJmOTIyNjk1ODMwMzVmMWFlODE0YTZmMjQ1Y2Y1ZjJjMWVmNGM5ZWM2ODg5YmE2ZjdmMzdhMzAyNWZmMDg3MzAzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523528323\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-407251824 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-407251824\", {\"maxDepth\":0})</script>\n"}}