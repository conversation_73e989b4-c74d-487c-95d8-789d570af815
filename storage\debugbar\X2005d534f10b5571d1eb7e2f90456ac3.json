{"__meta": {"id": "X2005d534f10b5571d1eb7e2f90456ac3", "datetime": "2025-06-28 15:49:35", "utime": **********.352336, "method": "GET", "uri": "/financial-operations/product-analytics/top-selling?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125774.838922, "end": **********.352356, "duration": 0.5134339332580566, "duration_str": "513ms", "measures": [{"label": "Booting", "start": 1751125774.838922, "relative_start": 0, "end": **********.242006, "relative_end": **********.242006, "duration": 0.40308403968811035, "duration_str": "403ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.242016, "relative_start": 0.4030940532684326, "end": **********.352358, "relative_end": 2.1457672119140625e-06, "duration": 0.11034202575683594, "duration_str": "110ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47299888, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/top-selling", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getTopSellingProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.top-selling", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=206\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:206-364</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.04081, "accumulated_duration_str": "40.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2800438, "duration": 0.015710000000000002, "duration_str": "15.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 38.495}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3067079, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 38.495, "width_percent": 1.936}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 235}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.311095, "duration": 0.00982, "duration_str": "9.82ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:235", "source": "app/Http/Controllers/ProductAnalyticsController.php:235", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=235", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "235"}, "connection": "kdmkjkqknb", "start_percent": 40.431, "width_percent": 24.063}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 240}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.322646, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:240", "source": "app/Http/Controllers/ProductAnalyticsController.php:240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=240", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "240"}, "connection": "kdmkjkqknb", "start_percent": 64.494, "width_percent": 6.984}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_v2_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos_v2` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 260}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.326943, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:260", "source": "app/Http/Controllers/ProductAnalyticsController.php:260", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=260", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "260"}, "connection": "kdmkjkqknb", "start_percent": 71.478, "width_percent": 1.25}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, COALESCE(SUM(wp.quantity), 0) as stock_quantity from `product_services` as `ps` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` where `ps`.`id` in (1325, 298, 299, 1808, 165, 1458, 1247, 310, 311, 639, 164, 1297, 1083, 1697, 1102, 1195, 993, 1829, 162, 1287, 2121, 155, 309, 2028, 1668, 1701, 143, 457, 1090, 159, 160, 476, 674, 1745, 312, 163, 175, 1743, 2007, 18, 300, 484, 1096, 1101, 1415, 681, 1106, 1197, 1467, 1661, 1688, 1859, 1981, 896, 1112, 1118, 1744, 2165, 157, 231, 597, 1081, 1563, 2170, 924, 1088, 1105, 1110, 1111, 1323, 156, 237, 976, 1117, 1147, 1294, 1803, 1806, 1894, 2240, 166, 179, 433, 1092, 1126, 1343, 1681, 1702, 1989, 2041, 2074, 2299, 158, 242, 385, 516, 753, 972, 1181, 1338, 1461, 1767, 1846, 1899, 1979, 1983, 1995, 2256, 46, 170, 176, 456, 568, 758, 1009, 1099, 1103, 1116, 1312, 1403, 1473, 1527, 1667, 1693, 1705, 1716, 1727, 1967, 2018, 3, 142, 232, 596, 611, 751, 1084, 1104, 1319, 1353, 1363, 1405, 1663, 1807, 1838, 1960, 2077, 2116, 2120, 2172, 2286, 145, 251, 448, 470, 595, 606, 607, 608, 1040, 1086, 1108, 1114, 1115, 1214, 1638, 1650, 1655, 1813, 2015, 2186, 2187, 2297, 215, 313, 383, 386, 423, 429, 449, 474, 496, 598, 754, 1119, 1139, 1207, 1265, 1282, 1296, 1324, 1354, 1452, 1455, 1541, 1543, 1660, 1686, 1699, 1723, 1726, 1766, 1809, 1820, 1844, 1897, 1962, 1970, 1973, 1975, 1984, 2029, 2073, 2119, 2146, 2193, 2208, 2266, 2, 7, 147, 148, 150, 152, 153, 171, 250, 254, 337, 445, 462, 464, 515, 522, 668, 669, 1053, 1094, 1253, 1313, 1315, 1322, 1374, 1376, 1388, 1510, 1651, 1653, 1683, 1718, 1746, 1817, 1830, 1843, 1880, 1944, 1964, 1980, 1985, 2014, 2016, 2042, 2072, 2118, 2164, 2225, 2226, 2233, 2239, 2300, 144, 146, 180, 183, 218, 269, 350, 356, 359, 362, 384, 427, 440, 465, 490, 495, 497, 500, 512, 518, 521, 603, 638, 666, 757, 760, 952, 1025, 1100, 1156, 1191, 1270, 1298, 1306, 1372, 1385, 1407, 1475, 1515, 1643, 1645, 1647, 1659, 1671, 1685, 1687, 1704, 1706, 1717, 1733, 1763, 1802, 1825, 1839, 1842, 1845, 1851, 1895, 1956, 1965, 1968, 1982, 1994, 2017, 2030, 2114, 2145, 2149, 2155, 2162, 2167, 2171, 2174, 2177, 2183, 2224, 2232, 2236, 2237, 2254, 2255, 40, 41, 45, 72, 88, 119, 151, 181, 202, 219, 236, 238, 243, 246, 252, 253, 255, 314, 324, 326, 330, 353, 354, 358, 367, 393, 432, 446, 450, 460, 461, 468, 478, 479, 548, 556, 562, 566, 591, 605, 632, 641, 836, 900, 942, 977, 1010, 1034, 1044, 1095, 1107, 1131, 1140, 1144, 1198, 1275, 1286, 1302, 1348, 1370, 1408, 1428, 1436, 1440, 1443, 1517, 1519, 1537, 1550, 1646, 1673, 1690, 1695, 1696, 1703, 1720, 1724, 1725, 1728, 1740, 1761, 1779, 1810, 1816, 1822, 1823, 1892, 1893, 1898, 1955, 1969, 1971, 1978, 1987, 2000, 2025, 2026, 2027, 2076, 2086, 2103, 2112, 2117, 2138, 2156, 2158, 2173, 2188, 2192, 2197, 2201, 2204, 2213, 2220, 2228, 2231, 2244, 2295, 2296, 2301, 6, 11, 13, 19, 31, 33, 38, 43, 80, 93, 109, 112, 114, 154, 161, 168, 173, 185, 207, 216, 222, 228, 239, 247, 261, 291, 302, 339, 346, 348, 349, 351, 352, 361, 400, 406, 434, 435, 436, 439, 441, 451, 452, 455, 469, 471, 481, 485, 488, 498, 514, 523, 525, 531, 535, 538, 542, 543, 558, 565, 574, 609, 610, 613, 618, 637, 648, 649, 664, 667, 714, 747, 755, 803, 842, 846, 857, 904, 905, 910, 918, 928, 941, 1001, 1014, 1023, 1038, 1049, 1054, 1091, 1098, 1134, 1135, 1149, 1157, 1177, 1184, 1199, 1204, 1248, 1263, 1266, 1273, 1277, 1280, 1283, 1285, 1300, 1305, 1308, 1316, 1317, 1358, 1371, 1406, 1438, 1457, 1459, 1470, 1474, 1478, 1495, 1502, 1511, 1512, 1522, 1530, 1536, 1545, 1553, 1627, 1633, 1634, 1649, 1677, 1694, 1698, 1707, 1708, 1709, 1711, 1712, 1713, 1715, 1719, 1721, 1722, 1732, 1748, 1750, 1755, 1757, 1762, 1765, 1789, 1814, 1819, 1821, 1824, 1826, 1828, 1840, 1841, 1847, 1849, 1863, 1881, 1896, 1906, 1910, 1914, 1915, 1920, 1937, 1941, 1953, 1959, 1961, 1966, 1986, 1991, 1996, 2001, 2010, 2034, 2037, 2040, 2049, 2050, 2057, 2058, 2068, 2070, 2081, 2087, 2098, 2099, 2100, 2113, 2115, 2122, 2124, 2131, 2141, 2151, 2152, 2153, 2157, 2159, 2181, 2189, 2190, 2194, 2199, 2202, 2206, 2211, 2214, 2217, 2227, 2230, 2241, 2249, 2252, 2262, 2268, 2285, 2292, 2303, 2305, 2306, 2307) and `ps`.`created_by` = 15 group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name`, `psu`.`name`", "type": "query", "params": [], "bindings": ["1325", "298", "299", "1808", "165", "1458", "1247", "310", "311", "639", "164", "1297", "1083", "1697", "1102", "1195", "993", "1829", "162", "1287", "2121", "155", "309", "2028", "1668", "1701", "143", "457", "1090", "159", "160", "476", "674", "1745", "312", "163", "175", "1743", "2007", "18", "300", "484", "1096", "1101", "1415", "681", "1106", "1197", "1467", "1661", "1688", "1859", "1981", "896", "1112", "1118", "1744", "2165", "157", "231", "597", "1081", "1563", "2170", "924", "1088", "1105", "1110", "1111", "1323", "156", "237", "976", "1117", "1147", "1294", "1803", "1806", "1894", "2240", "166", "179", "433", "1092", "1126", "1343", "1681", "1702", "1989", "2041", "2074", "2299", "158", "242", "385", "516", "753", "972", "1181", "1338", "1461", "1767", "1846", "1899", "1979", "1983", "1995", "2256", "46", "170", "176", "456", "568", "758", "1009", "1099", "1103", "1116", "1312", "1403", "1473", "1527", "1667", "1693", "1705", "1716", "1727", "1967", "2018", "3", "142", "232", "596", "611", "751", "1084", "1104", "1319", "1353", "1363", "1405", "1663", "1807", "1838", "1960", "2077", "2116", "2120", "2172", "2286", "145", "251", "448", "470", "595", "606", "607", "608", "1040", "1086", "1108", "1114", "1115", "1214", "1638", "1650", "1655", "1813", "2015", "2186", "2187", "2297", "215", "313", "383", "386", "423", "429", "449", "474", "496", "598", "754", "1119", "1139", "1207", "1265", "1282", "1296", "1324", "1354", "1452", "1455", "1541", "1543", "1660", "1686", "1699", "1723", "1726", "1766", "1809", "1820", "1844", "1897", "1962", "1970", "1973", "1975", "1984", "2029", "2073", "2119", "2146", "2193", "2208", "2266", "2", "7", "147", "148", "150", "152", "153", "171", "250", "254", "337", "445", "462", "464", "515", "522", "668", "669", "1053", "1094", "1253", "1313", "1315", "1322", "1374", "1376", "1388", "1510", "1651", "1653", "1683", "1718", "1746", "1817", "1830", "1843", "1880", "1944", "1964", "1980", "1985", "2014", "2016", "2042", "2072", "2118", "2164", "2225", "2226", "2233", "2239", "2300", "144", "146", "180", "183", "218", "269", "350", "356", "359", "362", "384", "427", "440", "465", "490", "495", "497", "500", "512", "518", "521", "603", "638", "666", "757", "760", "952", "1025", "1100", "1156", "1191", "1270", "1298", "1306", "1372", "1385", "1407", "1475", "1515", "1643", "1645", "1647", "1659", "1671", "1685", "1687", "1704", "1706", "1717", "1733", "1763", "1802", "1825", "1839", "1842", "1845", "1851", "1895", "1956", "1965", "1968", "1982", "1994", "2017", "2030", "2114", "2145", "2149", "2155", "2162", "2167", "2171", "2174", "2177", "2183", "2224", "2232", "2236", "2237", "2254", "2255", "40", "41", "45", "72", "88", "119", "151", "181", "202", "219", "236", "238", "243", "246", "252", "253", "255", "314", "324", "326", "330", "353", "354", "358", "367", "393", "432", "446", "450", "460", "461", "468", "478", "479", "548", "556", "562", "566", "591", "605", "632", "641", "836", "900", "942", "977", "1010", "1034", "1044", "1095", "1107", "1131", "1140", "1144", "1198", "1275", "1286", "1302", "1348", "1370", "1408", "1428", "1436", "1440", "1443", "1517", "1519", "1537", "1550", "1646", "1673", "1690", "1695", "1696", "1703", "1720", "1724", "1725", "1728", "1740", "1761", "1779", "1810", "1816", "1822", "1823", "1892", "1893", "1898", "1955", "1969", "1971", "1978", "1987", "2000", "2025", "2026", "2027", "2076", "2086", "2103", "2112", "2117", "2138", "2156", "2158", "2173", "2188", "2192", "2197", "2201", "2204", "2213", "2220", "2228", "2231", "2244", "2295", "2296", "2301", "6", "11", "13", "19", "31", "33", "38", "43", "80", "93", "109", "112", "114", "154", "161", "168", "173", "185", "207", "216", "222", "228", "239", "247", "261", "291", "302", "339", "346", "348", "349", "351", "352", "361", "400", "406", "434", "435", "436", "439", "441", "451", "452", "455", "469", "471", "481", "485", "488", "498", "514", "523", "525", "531", "535", "538", "542", "543", "558", "565", "574", "609", "610", "613", "618", "637", "648", "649", "664", "667", "714", "747", "755", "803", "842", "846", "857", "904", "905", "910", "918", "928", "941", "1001", "1014", "1023", "1038", "1049", "1054", "1091", "1098", "1134", "1135", "1149", "1157", "1177", "1184", "1199", "1204", "1248", "1263", "1266", "1273", "1277", "1280", "1283", "1285", "1300", "1305", "1308", "1316", "1317", "1358", "1371", "1406", "1438", "1457", "1459", "1470", "1474", "1478", "1495", "1502", "1511", "1512", "1522", "1530", "1536", "1545", "1553", "1627", "1633", "1634", "1649", "1677", "1694", "1698", "1707", "1708", "1709", "1711", "1712", "1713", "1715", "1719", "1721", "1722", "1732", "1748", "1750", "1755", "1757", "1762", "1765", "1789", "1814", "1819", "1821", "1824", "1826", "1828", "1840", "1841", "1847", "1849", "1863", "1881", "1896", "1906", "1910", "1914", "1915", "1920", "1937", "1941", "1953", "1959", "1961", "1966", "1986", "1991", "1996", "2001", "2010", "2034", "2037", "2040", "2049", "2050", "2057", "2058", "2068", "2070", "2081", "2087", "2098", "2099", "2100", "2113", "2115", "2122", "2124", "2131", "2141", "2151", "2152", "2153", "2157", "2159", "2181", "2189", "2190", "2194", "2199", "2202", "2206", "2211", "2214", "2217", "2227", "2230", "2241", "2249", "2252", "2262", "2268", "2285", "2292", "2303", "2305", "2306", "2307", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 319}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.32965, "duration": 0.011130000000000001, "duration_str": "11.13ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:319", "source": "app/Http/Controllers/ProductAnalyticsController.php:319", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=319", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "319"}, "connection": "kdmkjkqknb", "start_percent": 72.727, "width_percent": 27.273}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/top-selling", "status_code": "<pre class=sf-dump id=sf-dump-1276647852 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1276647852\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1473776614 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473776614\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-610154007 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-610154007\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1820021839 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125771282%7C26%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRTYkcxNXVBMHErZ2IzZmJNbnFiRnc9PSIsInZhbHVlIjoialFFQjNHeUQzV043RWJ0V0kzVHJVU21QUkRjcktYMWVVV0NPR0NuZkZnUHVEUGRrOTBSTUJ0KytlMmtDRGo3eHVlVk5NZlBtaGtZQ2U4OHp4YTRkT256TURNZUQvK1Zqcm1kVVNWLzZtcERidEZMakk3b0RkTXNTVktMbi9aZFRNRU1COForS250djBselpsaDB2enJQTW9DbFFtUjcwRTZBcTdZY0sxaytiSzduQ01ra3l6YUZmOHFpcUd4Q0xPbXRlSmw4aE1IY1I0SXNiTFByb2ZNcndndS9TU1YyQno4a2V6YVhoaHlncGpzNVJuVW9mM0dBSnFCc1BLc2tTODg2T3ZNUUp3TGdWWnMvdnA0WDNRZCtHZXJMKzdwMGsrWTl6ekhFcVBPZk0rYnVXRnNvekl4eVlMNUI5MkFMWkd3RC9jNHlCTWxFcEpuL0c3L3o5NWtib1NldzlCS1ZPcmEzRXR6QytLUjF4M0pENnJPUTJkMmpwVzlpclpmbVBtVUw5bUg4WmpJZ2EzWEpydWVsQUVKei9Rb0hoekRseXltWDVMNWoyVmF3UnR0MGdLb0lhL0VXZmFPd1FtZVFXdkpaVXd3ZHI3NWUwTGxnWTl5VEFKdmtYU1lrK243STV3MlRRY1hHdDA3d3QyR0lmUzlpVnJqRjNzR3RiMW5zcEIiLCJtYWMiOiI4NTRhZTM2YWM5NmNkNWFiMWUwMmI2NWZmZmYyMDIwM2MyZDcwMzhhMzFjODczY2EwZjQzNjMxNGRmNGI1OTI3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklDTU90ZThkcmNSVlNjamFXRWdzYmc9PSIsInZhbHVlIjoieUgyZ2llNm1hMW5oUUxJMDdHcEp2UHlxSUtZZHJUQXR5bVpGWU5wcEcyZXpxbGJ1SWZPWUc2K1JtZ1FWazVKWDZSaFN5bkNhWW9HOEFLNDUvYmNPREp4MVpaZU5nU0lnWGs2cHR6SjVYdHFhc2lwdjdOUlN6S1ZLbm9HSzcrQit5b041eXJxMzd0OWx4ZW93UWlBZW1hOE03MFRGeWFqdjdVMDhyQkZOQ1dmQ0U0aUJodjl4TnowbGdMTTFWZHlzaUNYRm1ZTVNsdW91S2J1ZHBnZk9QNmFCd3VHU254eFlJNHZYU0l1cXI0MTBuWlZDem9rYmRydkZxcmR0V1FlWlZIZS9MQ1VMT0VGZ243UWZ3bFYwY3lRRWM4b1prbE5xN3orUkVXcFZUcGRBTnVOeXNSaDJEcWpsRFhWNmFrQW1UbzI3SitKNmF6eElSQ3Q2RGgwU053ZTlLcWRDM3dlQnJjU01pcURQbEtjSGthd3VIQ3ppM1JtTUFndWdoaVNKZlA3OTVJVXlJam1ITXBhVVBrR0RtUjNBTmNJczRLN3lzNjlud0NUcTQraWI2WENoT2NnQzRaQW9ldkhYM0tBanUzQUJwNDZ6TkxFSGFRVUQxOHI3YVR4RERwY3BUWnhZK1owblpqZkFZRjBhVHdJUGJJZXhiNExHTWxjbTZuUUoiLCJtYWMiOiI0NmRmYjZiODVjOGY5MzgxMmM3NGE3ZGFhYTQzNjUyYTFhNzY4ZjE4OTFkZGI0ZDgyMDdiNGFiOGU3M2ZlNjNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1820021839\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1931495780 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931495780\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-319148437 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRCUlNrcTJtQ09tSGdJcDMrb1VmakE9PSIsInZhbHVlIjoiYzNJMW13SDdvZXBWMll1Q244M1QveEo5R1VhaXVlTzNka1A4U25FdGs4eUx6VGFtUGpod0Y0ckV0M25ubUhoRzI1cHMrelFHaW1BVWZFcnpZVWdRb1FUNVc1NnZpT3gzWURSRm1SZG5XNWliQTVMVjZUZFdjM3RhaG5QOXRPOHlhTDdtenpkU01BS3I3UGRjMlhNQmNmeHg2bi9iS3NFQ3BYTGVIdk9sOHVZK2xXdDZtY2xVOHpEaHlVSlF4a1ZGdEpvSE9odFlsdWVuOFBnaFVCSFExa0JDR3duMDRsdEE3bGUreUdXUy9yT3NOek1hZGxva0o5NGxjWnRIL2VKSUNBV2NTR2JxYUtDNkFjd2c5NCt5UGxOa1M2ZS9CZG1ZRFJqS3kvRkdZcHdKT1VSbTdxYkdkTFRjVU85VHBSNFRZRFBOSmFOMEw5Wm1xcnUvTzg4ZHFjOUltbGdXQVFDZldYOHdSZ2sxaEVtQmxaMVNEdjRTalUxU1ZkU0ZxR2w5L084bzkyanZXUjRjbUtkQlQwQ20yTkduUnNFOCsySWVzOU9uaEd2aUw4dWViTzAwcEROdnJoMHJtWG9sMmQ1TEpQN3A4MnFPdEtzL1ZiWXR2MEJ1S2puMVJZNVYwQ0tFY0VIVFpsdWE2YmtEeGUyVjFLa20zaFV3dDl1K2ZnK3IiLCJtYWMiOiJjMjQ3ZDY4ZWJiMTFjYjc1MDFiMjlkYzlmZjQ3OTUzODY4NTE4MzYzMTdmMDQ1ZGZlMzA1NmY4NTZhZTJkYjBkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRmVnF0eFQvMDdHS1FxNGIvRmsrbWc9PSIsInZhbHVlIjoicGJoVWVuZ1o1UXRRWFlHVFdGc3dOYWZRSjJxeW52NlVUZWN3dHJhSDljbUFRZ1p6UkJPcERTQW5sc3krdExROWdoTXZiWjNmQm9rRzlOVm1LZ1ZtWmpQT09sUnZlbURmczhUaXhXUEQxOEtyazh5Zyt0N0p1YWhTdDI1QkF3L2lPVFdTRlpCUG9oZm56dW02ZEdkZHIwUERjUGxZT29RT0xvWUR4VHJPRkg2Zm5heHRMbUNQN3BLUzJLUTVzSHFjeFZJS01PMTk5UXROL1dpQzhjdHhvYnltaFo3Y0FMT05xT0czSnNnSUh0UXk4MTFITGc2cVhjRFNGckFOd1hzUGpuc015RWRKVDFnbDE5SzdsODJ2eXBoTzFNdlRSWUxLd3hVa25aZEFBVTFxOVZEcng5c2x4RlRQMG5nMnV0WXlpVDNCYWk2ZGZ4YmVuT0lmWllrT1RJUzd0RUczSUdsRjF5RmMvcmo3SXhJUEdmMWpqNlNLMDBnSWRSb2EzaHlnSFMxMkZDRDNvSWg5U1FQTXpSMTV1Y0J4NjR0OTZBNWlzL3FZenpNbFg5NW05T1duNWl4MjlSSmVpL3FRZkUvZkNvcnl4ZjR4WTB5NmV3TFFoV21DdW1qOFVUdmN0bWlldWxLbm42UllDem96anR0eURzajQwQ2pOd3B1NDdGRk8iLCJtYWMiOiJmMGMzMjJhNmE4MzdiNDljNjc3ZDk0OTMzMjdiOWU3OTI0NGExYjdlMmMxYTQ0NjYyNjkxODM3YjUxOGE5MzllIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRCUlNrcTJtQ09tSGdJcDMrb1VmakE9PSIsInZhbHVlIjoiYzNJMW13SDdvZXBWMll1Q244M1QveEo5R1VhaXVlTzNka1A4U25FdGs4eUx6VGFtUGpod0Y0ckV0M25ubUhoRzI1cHMrelFHaW1BVWZFcnpZVWdRb1FUNVc1NnZpT3gzWURSRm1SZG5XNWliQTVMVjZUZFdjM3RhaG5QOXRPOHlhTDdtenpkU01BS3I3UGRjMlhNQmNmeHg2bi9iS3NFQ3BYTGVIdk9sOHVZK2xXdDZtY2xVOHpEaHlVSlF4a1ZGdEpvSE9odFlsdWVuOFBnaFVCSFExa0JDR3duMDRsdEE3bGUreUdXUy9yT3NOek1hZGxva0o5NGxjWnRIL2VKSUNBV2NTR2JxYUtDNkFjd2c5NCt5UGxOa1M2ZS9CZG1ZRFJqS3kvRkdZcHdKT1VSbTdxYkdkTFRjVU85VHBSNFRZRFBOSmFOMEw5Wm1xcnUvTzg4ZHFjOUltbGdXQVFDZldYOHdSZ2sxaEVtQmxaMVNEdjRTalUxU1ZkU0ZxR2w5L084bzkyanZXUjRjbUtkQlQwQ20yTkduUnNFOCsySWVzOU9uaEd2aUw4dWViTzAwcEROdnJoMHJtWG9sMmQ1TEpQN3A4MnFPdEtzL1ZiWXR2MEJ1S2puMVJZNVYwQ0tFY0VIVFpsdWE2YmtEeGUyVjFLa20zaFV3dDl1K2ZnK3IiLCJtYWMiOiJjMjQ3ZDY4ZWJiMTFjYjc1MDFiMjlkYzlmZjQ3OTUzODY4NTE4MzYzMTdmMDQ1ZGZlMzA1NmY4NTZhZTJkYjBkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRmVnF0eFQvMDdHS1FxNGIvRmsrbWc9PSIsInZhbHVlIjoicGJoVWVuZ1o1UXRRWFlHVFdGc3dOYWZRSjJxeW52NlVUZWN3dHJhSDljbUFRZ1p6UkJPcERTQW5sc3krdExROWdoTXZiWjNmQm9rRzlOVm1LZ1ZtWmpQT09sUnZlbURmczhUaXhXUEQxOEtyazh5Zyt0N0p1YWhTdDI1QkF3L2lPVFdTRlpCUG9oZm56dW02ZEdkZHIwUERjUGxZT29RT0xvWUR4VHJPRkg2Zm5heHRMbUNQN3BLUzJLUTVzSHFjeFZJS01PMTk5UXROL1dpQzhjdHhvYnltaFo3Y0FMT05xT0czSnNnSUh0UXk4MTFITGc2cVhjRFNGckFOd1hzUGpuc015RWRKVDFnbDE5SzdsODJ2eXBoTzFNdlRSWUxLd3hVa25aZEFBVTFxOVZEcng5c2x4RlRQMG5nMnV0WXlpVDNCYWk2ZGZ4YmVuT0lmWllrT1RJUzd0RUczSUdsRjF5RmMvcmo3SXhJUEdmMWpqNlNLMDBnSWRSb2EzaHlnSFMxMkZDRDNvSWg5U1FQTXpSMTV1Y0J4NjR0OTZBNWlzL3FZenpNbFg5NW05T1duNWl4MjlSSmVpL3FRZkUvZkNvcnl4ZjR4WTB5NmV3TFFoV21DdW1qOFVUdmN0bWlldWxLbm42UllDem96anR0eURzajQwQ2pOd3B1NDdGRk8iLCJtYWMiOiJmMGMzMjJhNmE4MzdiNDljNjc3ZDk0OTMzMjdiOWU3OTI0NGExYjdlMmMxYTQ0NjYyNjkxODM3YjUxOGE5MzllIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319148437\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1704153711 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704153711\", {\"maxDepth\":0})</script>\n"}}