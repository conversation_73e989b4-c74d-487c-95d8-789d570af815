{"__meta": {"id": "X0085569455fadb810c34602d1706c787", "datetime": "2025-06-28 16:03:49", "utime": **********.883357, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.450153, "end": **********.883373, "duration": 0.43321990966796875, "duration_str": "433ms", "measures": [{"label": "Booting", "start": **********.450153, "relative_start": 0, "end": **********.83334, "relative_end": **********.83334, "duration": 0.38318681716918945, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.833349, "relative_start": 0.3831958770751953, "end": **********.883374, "relative_end": 9.5367431640625e-07, "duration": 0.050024986267089844, "duration_str": "50.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46408360, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2428\" onclick=\"\">app/Http/Controllers/PosController.php:2428-2462</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0017099999999999997, "accumulated_duration_str": "1.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.867232, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.041}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.876171, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.041, "width_percent": 16.959}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-348289908 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-348289908\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1948830007 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1948830007\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-185275515 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-185275515\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-975058192 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126627120%7C22%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImI1ejgzdmEvSHJOa2R6TkF3M0JnOGc9PSIsInZhbHVlIjoiWVpPMy9qSXU5dkt1VHBRK01paWVVYkFSUzJBdFlIVmI2RGp3Y0RMajErOVk0N0EybUFlVm5KQnhyejd4bUJ4SzJSeTNyYUxEMHZ5TkR5em1Xdm0xTFRobEJWbWsyQjZEU3ZMU211RzFCakNoZitlN0EvV1JITEprazJZRmxYZkwzNTRpeG44cUV2Q0txd1g5alNPdkg0OUZTN1dvWFk3NW1zeGZoZTNCTHh2UkdMWFpjMDNaQ2VrWW83bDRSQ0d5M082SXdKZVV0bEZhOE50R3IyVWVHaVgxV09nNURtczNrc1B1TVhodjdKTlJaQjVwTThuZHpUbjNLS2dGcW9XZWF4bHpkbWFKeDRrbC9RdjZGNkRZU3NVQWlTbEVJRzRxQlFrTXQvd2Y5aC9VR0ZHdWhTYURXbk9ocG5DWmVoazNzME5iSnZSYllsR2trM2czN2tPdjJKUzF4VlFsM08zNTgycmxUSEl1M1phZnRmYUF5Z0ZIb0w1Y2N5MERuQTdoYUh5bklDbnp4SWxFNDhyUXV2Wnd5YlNjd0FKNVJQd1pwRWJrQ09NUVZOYWZaMmFWMmJ1ME80ZG51Mm5YRTVwaW1jZjh4Ym0zRE9hdXdpQlRqb3BkaXp3dlVadThJUzdFRnhveFBuQ3hKc1l4MUZhUHBKUGczSXNHVjNkQk1WUFgiLCJtYWMiOiI2Njk4OWZiMTM1NzQ4YWEyZGY2YWRkZmQyODRiMjkxMGYxZTRlN2YzY2IxNzk1NTM2NTliMjdmNmQ0NTBiYzIzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImY5WjVFcmNNVWNOclNaQ1VMQ0ExV2c9PSIsInZhbHVlIjoieFFCVXlybXNmR0NpVXJ6UmhpditKQi9UcGs2dlhzUmFQMkNKTGdiTytQc1FMcDJNT1ZXenplMjhUUnZ5WTB5K1Uyd0JDN0lSQlQ1M0wxSkdLa3pWa0ZQcTdNLzNzeDZvblpjNGs0YWlDZ2VWR0dvQmNNUlhoUzdXK0htVVB6clBEc25ZdU9HeUtVcEE0YjlITm5MTjFTUEMvK3RBbFpMV2hHVzVVL1ZwN2RqU3F6V3RHVVlTcGpGWGp3NU9Xb2RpcHpER0NBWkFranNzUFlzUzhheTBENHNGeDJNc1VzTG4vb2MvUlRmSm8ydi9CalgxNUFHd3dsYWZBR1Rwangwc3gzQjYvTGpwdmowNlFMMnp6VzFFVXRla1JoWTBDL0hTcDViQjZKZ1RjM2w1WExOMWhBNnJLM3BXSjJ4SGErQ2RvcTdqNzhxdjVYUW0xSGxvOUtlL1FwWXJWOTU1a2pKTFJCN0FpK0NRdVdRaVlqVzltamVYMXdtR09HTmJiSnRiSzZXbEJtQ245VDU3Vi9ES2hWRndYUE13c0tlQlNVNnVtL0tudFVvcjZCRFEwM3I1TCs2aUxoWU9nUGtjaVk4OXFDcTZTUkR4TXR4c2VmcWU2WUhxQVpreWNTdXlEQXdVUlpxWWs1SExXMDZJWmtvNWNTUUVBR3hYVWR0WUVwUVciLCJtYWMiOiIwNWMyODYzYjYxMzA2MGFjOWQyNWExMjdmYTQ1MzE1ZTI4MDQ0M2NjMTU0MDc2ZjA5MDhmY2JkOWZhMWFjODEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-975058192\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-512117957 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-512117957\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1055888761 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRSaU5NUVAxTmdmMlZmejhzd2IyekE9PSIsInZhbHVlIjoiTXBDb0hxd0xLK2kvNzFsa3ZldUNmR0sxbW40Znc1dzJ3M09MM1dZMjJKWm9lcGF1QmpGM2pVWFVveTkwVkxKMW5EWWtJWVpFVk5lckhOTjJXRUhhdHhrZTByVjlHZVBQMzArdFNyYyt2SU5obk1od2kxTGNDSDJYUU1SVlhWUmx6WTJpNlBMKzZMSTFPN2NOa2FQdlhIblJlQlU4NWRCTlhCcTFISEVVbVZVRjEzQTg3Vmk5QThZaDNXRmRvT2FIa3BtZTgxbVlITmsvajZTajAxMWZEVHp6WTUvcmorRm1pZ0pFZnFnRmpuL1YwSDFWZCtSLzhIaitSUzFSeDY0UXMyemtnQkZwTjBPM2hFOElucFc3QnFHOUhmVUNoSXdiNnlPc21kdVZCTzBlQ3E3aHdKV0VtTE9DVGYzTFFlOXFiR1BpRFJJMTg5YTY2d3NRR2VCanlzQ2lrYXRNdURPRURrUGNTeE5NVjhLOTFmM1o5dUhraS9lZEhKcHE0NmoyUzhZUDFwK0ZoOGxRY2FSbEVwcEZXalE5Y0xRNzkrZ3BMOGRDNXVHMjlaam5RSzBzOWFWbkdZRlpiSkdxZisvdTEyUDRZakV6WkJvZFA0T3lmZHF4RmFQUGhtNXpVd0IzTm5XQUE1bVg2NmpqQXR4a2hvaUZOUFZ2dmdPUm90VEIiLCJtYWMiOiI2NzU0OTAxZTg3MmFjYTI1MjIyODNlNGJjMWZiZTkzNWEwYWNiYWM0NGJmYjg5OTFiYzVhYzYxMTNmY2UxZDI5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZQZ1dJR2JLck15bTEwSEpkUHVNRmc9PSIsInZhbHVlIjoiS2syeEFwZUNZV0pWSU9lbDVIaGszU2VnUXJKazBmcUZRWTRnZU42enhTVWFwRW9rZmprWSttdXFMeTdNbENPZU5ld21tTExzVGF4M2d4WXpFWjd1eHk0ZU5qMExteTdWc0UzR0hwYVdmR2g3blJnL2w2Z2FCVVhFaXlVSXBkNHhjNzV4UHgxSzlDZWtZK2hDQ2pzZjNxcm9WbytLNWtTazZ2WDhTTFNpa0o4NTFPd29YRXdEUmFiNC9Cb2NkSU5RNmNHMzFteXU0RzNWbFhzOEhkRy9scHRwaTQvbkw0NVRIV2F0S1lKZnFscXRXdTkvU2lCVEtyQjZqV2JsOFRIS2ZrU0djd2lydTVsWnp2MnpROExCWllWU3BJTkpRRkh4TGxsTlhydzNCZTJqV05Ia1c3MUN6NTM1aUFwT0JucTV1MkRQTDBqMkNJTWpqYXFCRlZ6ZUVUTmJxQ3o4RVd5akNyK3liRDVaK2VGNkNaTTk2TTBQalF3R2VpVVZ5N3RRMlA1S2prZHRyajlxbHhBNkl6WHh6Q1VCVXJJUU1CT21XMC80TXJyemxacE1ZK0Z3UU9ETDJqd2xncTU1cWtOMTVZelBsYjh1bUV6WEhPVFNmYW1Ba1dpQnBPeXQ4UHBlZE8rRmV4VkdSTFh0Vmd4OEdOdFZkaVVTbGdJNVNuNXAiLCJtYWMiOiI5ZTZjZTY1ZmRmZmJkYTBhYjM0NDllZjA5MzQ1Y2VjY2MzMzA5NmYxY2I1MjE4NjExZWFiZDkwNWI0YThlNGE4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRSaU5NUVAxTmdmMlZmejhzd2IyekE9PSIsInZhbHVlIjoiTXBDb0hxd0xLK2kvNzFsa3ZldUNmR0sxbW40Znc1dzJ3M09MM1dZMjJKWm9lcGF1QmpGM2pVWFVveTkwVkxKMW5EWWtJWVpFVk5lckhOTjJXRUhhdHhrZTByVjlHZVBQMzArdFNyYyt2SU5obk1od2kxTGNDSDJYUU1SVlhWUmx6WTJpNlBMKzZMSTFPN2NOa2FQdlhIblJlQlU4NWRCTlhCcTFISEVVbVZVRjEzQTg3Vmk5QThZaDNXRmRvT2FIa3BtZTgxbVlITmsvajZTajAxMWZEVHp6WTUvcmorRm1pZ0pFZnFnRmpuL1YwSDFWZCtSLzhIaitSUzFSeDY0UXMyemtnQkZwTjBPM2hFOElucFc3QnFHOUhmVUNoSXdiNnlPc21kdVZCTzBlQ3E3aHdKV0VtTE9DVGYzTFFlOXFiR1BpRFJJMTg5YTY2d3NRR2VCanlzQ2lrYXRNdURPRURrUGNTeE5NVjhLOTFmM1o5dUhraS9lZEhKcHE0NmoyUzhZUDFwK0ZoOGxRY2FSbEVwcEZXalE5Y0xRNzkrZ3BMOGRDNXVHMjlaam5RSzBzOWFWbkdZRlpiSkdxZisvdTEyUDRZakV6WkJvZFA0T3lmZHF4RmFQUGhtNXpVd0IzTm5XQUE1bVg2NmpqQXR4a2hvaUZOUFZ2dmdPUm90VEIiLCJtYWMiOiI2NzU0OTAxZTg3MmFjYTI1MjIyODNlNGJjMWZiZTkzNWEwYWNiYWM0NGJmYjg5OTFiYzVhYzYxMTNmY2UxZDI5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZQZ1dJR2JLck15bTEwSEpkUHVNRmc9PSIsInZhbHVlIjoiS2syeEFwZUNZV0pWSU9lbDVIaGszU2VnUXJKazBmcUZRWTRnZU42enhTVWFwRW9rZmprWSttdXFMeTdNbENPZU5ld21tTExzVGF4M2d4WXpFWjd1eHk0ZU5qMExteTdWc0UzR0hwYVdmR2g3blJnL2w2Z2FCVVhFaXlVSXBkNHhjNzV4UHgxSzlDZWtZK2hDQ2pzZjNxcm9WbytLNWtTazZ2WDhTTFNpa0o4NTFPd29YRXdEUmFiNC9Cb2NkSU5RNmNHMzFteXU0RzNWbFhzOEhkRy9scHRwaTQvbkw0NVRIV2F0S1lKZnFscXRXdTkvU2lCVEtyQjZqV2JsOFRIS2ZrU0djd2lydTVsWnp2MnpROExCWllWU3BJTkpRRkh4TGxsTlhydzNCZTJqV05Ia1c3MUN6NTM1aUFwT0JucTV1MkRQTDBqMkNJTWpqYXFCRlZ6ZUVUTmJxQ3o4RVd5akNyK3liRDVaK2VGNkNaTTk2TTBQalF3R2VpVVZ5N3RRMlA1S2prZHRyajlxbHhBNkl6WHh6Q1VCVXJJUU1CT21XMC80TXJyemxacE1ZK0Z3UU9ETDJqd2xncTU1cWtOMTVZelBsYjh1bUV6WEhPVFNmYW1Ba1dpQnBPeXQ4UHBlZE8rRmV4VkdSTFh0Vmd4OEdOdFZkaVVTbGdJNVNuNXAiLCJtYWMiOiI5ZTZjZTY1ZmRmZmJkYTBhYjM0NDllZjA5MzQ1Y2VjY2MzMzA5NmYxY2I1MjE4NjExZWFiZDkwNWI0YThlNGE4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055888761\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}