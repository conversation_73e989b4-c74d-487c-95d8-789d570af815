{"__meta": {"id": "Xf9d66ac2bc49b9f25ce50de2969e6f2d", "datetime": "2025-06-28 16:01:25", "utime": **********.395261, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126484.895202, "end": **********.395276, "duration": 0.5000741481781006, "duration_str": "500ms", "measures": [{"label": "Booting", "start": 1751126484.895202, "relative_start": 0, "end": **********.321883, "relative_end": **********.321883, "duration": 0.4266810417175293, "duration_str": "427ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.321896, "relative_start": 0.4266941547393799, "end": **********.395277, "relative_end": 9.5367431640625e-07, "duration": 0.07338094711303711, "duration_str": "73.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831920, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007260000000000001, "accumulated_duration_str": "7.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.364501, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 32.645}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.379198, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 32.645, "width_percent": 8.815}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"produc%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"produc%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;produc%", "%&lt;div class=&quot;product-stock&quot; id=&quot;produc%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.383056, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 41.46, "width_percent": 58.54}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-2026799983 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2026799983\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1486145030 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1486145030\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1304922349 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"37 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;produc</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1304922349\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1809593634 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhYV3V6aGdjcXVaeXlRdXlsV1hGWWc9PSIsInZhbHVlIjoib01IczdpekFOVEZBSU9OQVJtUnFoRU5UZFViZFdkdVdIdHlwR0ZlUmxDbmgwQnVyejl5OXZxRkdHeDdZS3c2QnFGM3JoeW9Hd1U0MzJyQnAybHhYbThqRDVSNGJBUUw2ZTJWUkp3S1p0Z1VFVGxBaEM0aCtpcFNCcGUydUcvZzBHYkFjaFhFcU1MbXIvVzhZTVhXVHE1QUp0TzhBTkxydFJBOVVKTFIvbU5JUVRxemlUdHRGd0MrMjlVYjhqQlc4dDNQRERWT01wd3g4WUY3Y29ZcU1pSEtDZjdCTE1wL2xZRVRzUElmTTVTa0o3V1JodFd5U1FFYnJha0pDeXRPcDF3TVFuUDhuYU1hMUVTUTh0WGJwbHlacjlsZlllSVI1SU1YTEI1dFVQUmFyVTF2aWVLQnJjYWtDVDFUemlwbkZzV3AxcVhGRERVSkRZc2lNMTBsQTJIU3hqcU1UazZRZ0FJcTlwQ3NmSlBlMzVhZDdpYnN5S2xReEkybWFvdFpnT0Q3UkpETFBZNnNQT2ZjTml0MTFQRUdxcDZOVkVyT084VWlNQ1NIVWpDMXBOWUJBaHRYRTQ0ZXljN251SFp2MTRjRXVneGFKOG1peFh2YThjOVVrR2wzVHdoSG1DRndHcWFDK1ozcHZoYm1uVTJKRVFReDVLMjF2RnUyUkcrdVkiLCJtYWMiOiI5MTVlNzRkYWUxYzA0NzI4ZjdkYjY2ZmUyMTY2Mjk3MDgxNmY2NjEwZTVhZWRjYzZhMTIwOWYxZmU5ZjQzYjQ0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVLUytnbURyRklFZkx3TmZSdVRpUlE9PSIsInZhbHVlIjoiQzdQNVYxTER5d0k0a1lWQnVkTlhzR2F1alZVVG8xbHQ5V2hLa0F1cGI0aThxSE5LcjNERDl2QnBkRzdzUUlucCtFN08zS1NOeWJ1ZHZrcWNSTy9KRXFnNkM1OHRUbTJtR1BhK2JtVUtqM24wemFoUlZqTVNZekZZa1p0SG1BUk9TMXJmM2lvYW81VnU4UjdhdURtRHBMdWY0U2RZcHlZaHczZmNJOTRBMmpxOERTUVl3OXRURERpUm1Wc0xGMEMycVpyMVczQjZJb251aTBZUzRFZUZwYzRIWEp3UlRoN21CSk5NUWZoQzZLRXhqUlZ1STQvUXU2M2tzZkowUG0yK201cWxBNGV3VlYxaGZkelFDM21TMDhSaWVsZDZEOXgwbjJDQmxuVWFmSWVtaDEzNmM3TVBIa0FLaGRrK3BxTTNMKzFweVY3WEdaSXZCYlRySFdGSlJUeERmYmRLYi9SUnphczV1ZVlJa2wxakRGNTZPdTBZRzZpRHZvTUlobXRuQXJPVTl2RzMrMWtvL1ZMcHk3N2xGNXRXOHpBeC9RUlFudUZpWXVYUUwwSmtFeDRqQUV5M3NKRmZYK1MzcnN1YmJ4VU8wMCtJbDZVNi9FSGUwVHVSdm13SlN3eGtvVU9QblFPajNGaC82Z1ZVV284Uk5vQ0ZiSE1wbDNpYVdnY1UiLCJtYWMiOiIxMjIwYzFlMTEyN2YzODY4NjE2NzFhZDNlMGFlOWMwMzM1NTk0ZjU5MzNkNDBhZGQxZDgyZmQ4ZDIyOGNiYzFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1809593634\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2024308132 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024308132\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1657307052 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFnWXJtaUFIYTNINmt2WE85aGg1Zmc9PSIsInZhbHVlIjoiWmZlR3V2YW9ZM3RmemJDakJuam96cUJLc2JISmtqZUFmNG1ITk5WT1JLbllTVTNLODRwS1hHNWNxSjJEZjNDRFdtWU1JUFQ1cUo2elA2OW9tQmN1ckh1UzZhODVxSmNrMnJCY2hJaytudmhIZUYrbzBtbXNsUjFFRnpyc05XQjBjb1VGNXcyczNtUC81RWF3ZXVXRmIvYy9HaXBvRjZQSzlpNXJsK1NSejM0Sit4dEJvSzZsUWwwdEkrZFNkS3cza2p5blhicTB3V0VWZi9WWW5wM2kva1dTZnlvemN1akZEYTV4NFRVMWF5a1lwUmxFbGdIMEt0ZzBuSjJUcGtPckc0Sk5xNzFBaWZaclM2NVFJNXRDQW1vTWxIdVJabHh0ZU9xQmhhR0xUbFVOQVkrZmV6T1JQZWUybm90OCtQN1hTWGZrMXdzc2duN0EwWWM5OGY5MFVlKzNjcVlWUUs1L1hlNUphamp5eEVyRE0vSFFDTDRpVUdlZkZreHhvMENGL3BxNml5bFN4RnFVamE5bU1rYSsweDlpUldhWnNxeHBSWUhHZFE2bE4rNVJUMzIrUS9ZMzZPd2RKejAwZTdkY0gxOThpbU8rakpBck9VLzRYYytUYzBvWUY3dnhoVnhKb0dqelhBMklmSTUwMFJaMlRjS2YrbmZOY3hvOXdYK0IiLCJtYWMiOiJhZDNiZWI4ZTVjMTVkZTViNmViMDMwY2MxYmRiZDU3OWEwNmJmYmM2NjE4ODQ0NWQ1MDViNGJmNmVhM2I5MmVkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRKMUVQSUF1VkZGM1Q4dm9ONjN6Smc9PSIsInZhbHVlIjoiR1dGRVUvRnlaclNXcmhsK3hZd3Q1bUFXYnhHdWhpMmZ3K094VWJzUFk0bTVDVVVsRmVHalVEQ3M5WjI0emRUSGl4ckRibmdFcWlzREc4ZzA5US9DeCs1NlM0bTgyNklUU0NYUnlkZXp5a1Q2V1RpSElyVFFnbXlHQjBtL1lMV3VNZml1S3JNVGtDbzVWNTdoOHJXVXVUVmZDKzI3RWk4RmdkM1lrdk9weG51STJPNVE5V0dPMkNJSWRlWWk2cVBqVkxPd0tvRGZjZmpLbHdOa1V5OWJrK0VMTlNiVUJvN0xybEdLMnY4bWtvSW4zcEJvN0NoTHJ2SFRWNk1uL3RNVVBLSkgxV2RpK0NZRzdrL2JxSkVpMHVWMGtCQUt4bHAyYzZKTjlzdXJFQStrMEhhVmEyR3pHbkxSZXNEM3BEZlBHWVZvaW1YalpVZFhkWXh0dWVtY2dtMkhTMGFOcHF1VFhLWlhTaUx4a05RTUVYSC9HcTNWRllBZ3NLbW5vb1ptT0ZzeC9Zd0dJR01GMFhNTDhmbXdkZ0h3Nit6djVNZFRsK0hDVk9OME5oS0ZuMERuT2xtOFNvTHpSWXdlRU1uM3R6K1h4bmZJSVk0QnVSL21uSElsNEMxeDdzS0RmY1V2M3lCRkprcmx4cDd2YisxRUdFdjZyU1pETWFJV2VITEMiLCJtYWMiOiJiZTAwYmE3ODQ1YmQyOGI3ZmZkNmQxMDJlM2VkN2JjMDRkMGY3NDQ3OWM4ZjY1NzNiZDQ1YTk5ZWEyOTFiNzhjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFnWXJtaUFIYTNINmt2WE85aGg1Zmc9PSIsInZhbHVlIjoiWmZlR3V2YW9ZM3RmemJDakJuam96cUJLc2JISmtqZUFmNG1ITk5WT1JLbllTVTNLODRwS1hHNWNxSjJEZjNDRFdtWU1JUFQ1cUo2elA2OW9tQmN1ckh1UzZhODVxSmNrMnJCY2hJaytudmhIZUYrbzBtbXNsUjFFRnpyc05XQjBjb1VGNXcyczNtUC81RWF3ZXVXRmIvYy9HaXBvRjZQSzlpNXJsK1NSejM0Sit4dEJvSzZsUWwwdEkrZFNkS3cza2p5blhicTB3V0VWZi9WWW5wM2kva1dTZnlvemN1akZEYTV4NFRVMWF5a1lwUmxFbGdIMEt0ZzBuSjJUcGtPckc0Sk5xNzFBaWZaclM2NVFJNXRDQW1vTWxIdVJabHh0ZU9xQmhhR0xUbFVOQVkrZmV6T1JQZWUybm90OCtQN1hTWGZrMXdzc2duN0EwWWM5OGY5MFVlKzNjcVlWUUs1L1hlNUphamp5eEVyRE0vSFFDTDRpVUdlZkZreHhvMENGL3BxNml5bFN4RnFVamE5bU1rYSsweDlpUldhWnNxeHBSWUhHZFE2bE4rNVJUMzIrUS9ZMzZPd2RKejAwZTdkY0gxOThpbU8rakpBck9VLzRYYytUYzBvWUY3dnhoVnhKb0dqelhBMklmSTUwMFJaMlRjS2YrbmZOY3hvOXdYK0IiLCJtYWMiOiJhZDNiZWI4ZTVjMTVkZTViNmViMDMwY2MxYmRiZDU3OWEwNmJmYmM2NjE4ODQ0NWQ1MDViNGJmNmVhM2I5MmVkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRKMUVQSUF1VkZGM1Q4dm9ONjN6Smc9PSIsInZhbHVlIjoiR1dGRVUvRnlaclNXcmhsK3hZd3Q1bUFXYnhHdWhpMmZ3K094VWJzUFk0bTVDVVVsRmVHalVEQ3M5WjI0emRUSGl4ckRibmdFcWlzREc4ZzA5US9DeCs1NlM0bTgyNklUU0NYUnlkZXp5a1Q2V1RpSElyVFFnbXlHQjBtL1lMV3VNZml1S3JNVGtDbzVWNTdoOHJXVXVUVmZDKzI3RWk4RmdkM1lrdk9weG51STJPNVE5V0dPMkNJSWRlWWk2cVBqVkxPd0tvRGZjZmpLbHdOa1V5OWJrK0VMTlNiVUJvN0xybEdLMnY4bWtvSW4zcEJvN0NoTHJ2SFRWNk1uL3RNVVBLSkgxV2RpK0NZRzdrL2JxSkVpMHVWMGtCQUt4bHAyYzZKTjlzdXJFQStrMEhhVmEyR3pHbkxSZXNEM3BEZlBHWVZvaW1YalpVZFhkWXh0dWVtY2dtMkhTMGFOcHF1VFhLWlhTaUx4a05RTUVYSC9HcTNWRllBZ3NLbW5vb1ptT0ZzeC9Zd0dJR01GMFhNTDhmbXdkZ0h3Nit6djVNZFRsK0hDVk9OME5oS0ZuMERuT2xtOFNvTHpSWXdlRU1uM3R6K1h4bmZJSVk0QnVSL21uSElsNEMxeDdzS0RmY1V2M3lCRkprcmx4cDd2YisxRUdFdjZyU1pETWFJV2VITEMiLCJtYWMiOiJiZTAwYmE3ODQ1YmQyOGI3ZmZkNmQxMDJlM2VkN2JjMDRkMGY3NDQ3OWM4ZjY1NzNiZDQ1YTk5ZWEyOTFiNzhjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657307052\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1416871247 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1416871247\", {\"maxDepth\":0})</script>\n"}}