{"__meta": {"id": "X08a94a47ca6849b34667c0e978e783e5", "datetime": "2025-06-28 15:26:40", "utime": **********.151942, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751124399.696679, "end": **********.151957, "duration": 0.4552779197692871, "duration_str": "455ms", "measures": [{"label": "Booting", "start": 1751124399.696679, "relative_start": 0, "end": **********.084472, "relative_end": **********.084472, "duration": 0.38779282569885254, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.08448, "relative_start": 0.387800931930542, "end": **********.151959, "relative_end": 1.9073486328125e-06, "duration": 0.06747889518737793, "duration_str": "67.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46292872, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2335\" onclick=\"\">app/Http/Controllers/PosController.php:2335-2369</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.015860000000000003, "accumulated_duration_str": "15.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.119148, "duration": 0.015460000000000002, "duration_str": "15.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.478}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.144407, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.478, "width_percent": 2.522}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1402748322 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1402748322\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-386407587 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-386407587\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-586230601 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-586230601\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-143079079 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124397135%7C17%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjgrV2VDanZSb0tkSkQyRUw4UXlhZ2c9PSIsInZhbHVlIjoiYmpmUExPRG9XU0xaQURJMGZKWUV6Sy9wZFppYklRV01kZS8vNElGZ3ZuNjloTDhMcXdQT2krNU5lejZnSVRxSlBYSS9iWUpoYW1KdElwTEt4SVIvclF0SElnbWp3NFNpVXdHT2NrUFI5VnZTWVJNUUdxRmxQc29FMDJnQS9tcjEzWnJFRktMK2w2MDFvZVYvckN6L0xCZnlZdmFpMVMvRFhOSzFqRHlFVGEwTitZVjFpNVFaSyt2LzA3eml0T0QxTzk5b3hvWWVwSzJSaHNxb1BCanE1YUFWWDQyNVMvRFhkSUg4R0Y3YzlZZllpRWhhL2ZneTdBenlnelRKTlZpVXlIYnhGQTFIVFpuRU84WXFFNlFjSTZuaGJ4VnRYRXRPaGt3UzdOb0FwZkdxQTVPL2xrOTgyb1BPMDFvTU45L2thQ0NOVlo5RlV0ZzIyRncwblJzblJJWTV2dmp0c1hvY0ZRdzRCeVAvSnpFSzM1NTRTT3MxQ1hYZVUxbXcySXNuRlp0MG14Tmh3WUdpTnMrQmZpNVlVMkRRQTRaM010b01KYU00UUlXV0puOHprSzRtMVlCRGFwRmtyUlhQT25TbStYUXVkNVZQSzZHUFkwVFYwNlZ1aGdyNWJKeTZMVlBzcHd5MmU5VElySXdVWjNnWSthTndRbHFvckszamkvTm8iLCJtYWMiOiJjMGVkMmEwOTMzYzZlYzZlMWM5NGM1OTk4NDg0Njk3MTI0NGJhZTc5OWI3MmM5NmE1YTNiNmE4ZGFjY2M2MmI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJJV0Qyb1hDTklIRjZiWlFSVm40V0E9PSIsInZhbHVlIjoiOEZrNHdBSmViVnVXcTI3cE5hVHFkZUwxNS9FbVd3UlU0QXdIRndJd0ZJSFNDOHRBWTdqYytUYTZMUW5oNkw5UVVFK1RtdVZjTU01U21rUU5YbFdIZVVON0NzamNYYThvb0VRbDUwSHNNM1hZVitDb0U3NTlRY1NROVlScHRuMWlqUEVQY3BuSzUyTnZQbFUzQnkrQVlqeHRPaXlWMFhtZDNqZStsd243b3d6YUswdmo0VmFPdVRpWVBFSVlUMU9hczY0OXE1eC8zNlB0bEFzeVZxajUxaHdYS1ZCVjc0MWNTeEZwNW5IQjNOWmxBYjR0L1Y1Q0l2UzNQcEhUQ3pKZ0llT1U5bFNPQi9oQm1BdFMrYWNSZjNyNzRPMU13VzFXUUViYk5ZaGQ5Z1F1SXRjWmNxb2E1bXdMemd2cGlyVWp2bjl0cU1HNkh0eWJ5R1B2T3Zjbk1rSWRTYXl0cjBNeTRyc1AzaTZPTm12SGlwWFFMZDlkdEpNL3dDZmVTN2NCZE1UOWRqK1JXWk1teFN4QmJhaE1QRUFJRTNQNC9xeUE5dmVuRXBPT0NxV2NPNFNJVDBOMDRIZ1pBQTFhbnJ1T0ROa1dBNkxtK3k4Y24yTEJSUXdvbzhFVlFnN0hPdzB5SGhCeCttWmFkVWE5emFYeHZhczdLUU40T0MyaThNUy8iLCJtYWMiOiIyNTc4Y2EyZDFlNDMyNzUxY2U1N2JkYjVhODRlMWRlYTNjYWI2NGMwNThhNDIzNjYxYzEyNjdkMzdhY2RlZDM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143079079\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-776701056 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776701056\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1544521857 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:26:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikw0eGFtSlNWbHhBYUlWRWRJZ2RXYWc9PSIsInZhbHVlIjoic2dPc3lYMUFjdU1ORzIrUXJBT1VmdGdWR25YMmN4clIyMG90UWxkR1VBN2hRYXZ6OTZDZ3dPU0JSYUtZNXhIb3pFdWdHeTl5cTlKQUIralFiRFUvMjJXSUt1cFlnS1cwbTY2Wm5MdGcxZ3JuZllXVEQvTWpUV3FwUWV5WTIrSjl0c2FTSmYzWEV1ZjIxOTZ6N3hwbjk5M05JSHQ2ZEFrYng0TTUwZEc4Z0YxRThRNXNBY1pvM0hBU1RkbWt4a1lxM25ySi8vQ0RtbjdDVmVyZzVmZTd3dXA2ckMzaEZoMXNjemNmeEZFaEpLQXczU3NOTEgvUmpsRHhMbUhBMzQzSkVYck5OM3psa0hPZGZuTFpTTUgxWjhnd0l2V25lYmFwVU11VjZBL3RycWpHVHQybXVDaHhrUUZTREkwSzN3OUl4S2ZYVzZiWXFTZ05seWVjbTgwU3JGL1B2WWJaS3NVL0VuTFAzdHNyYVIreWMvQ3FwMUtJK09ydjhteVZPV2UwK2lGZDlHMUdqMjdJSyttbWRUcWpNQ2hMc3dSQlo4NjVIS01WWXFhUHFaNkJ1Ty9XZEVMOVFxM2hlZlJPYUQ4VkZYQS9lOTZUYkpSODYrZkpWdCs3T1dGcGRpNGdzSFpQUjZHWjBOVGRZMWtDMWxMbzJIMEwwQmVVRUwyckN2dk0iLCJtYWMiOiI5ZDhhY2E4MWZlZDE4ZjBiNjkzOTY5ZjkxZTM5NDdkYmU0YzcxZmVmOGNjZGEyZWI2ZTRjNTg5N2QzZjNkZmY4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJQaTBnTU9tWHpCRGtqYUM3Z0RWMGc9PSIsInZhbHVlIjoickwvNVlpZEY5ei82Smlyb1M1UmFMNXlOejdvQW5aMCtONEs0emtIWG00RHl4RFdmS1ozOVRDVWNaZk5JeTRiSmRha1pJMTFQUERtbmVSelBGUVRsVG4wMjhoUTlrdTllZmhkNVB6cXdycUFGRks0bzdXREE3YVZ3U3dROVpPVUg4aGpXOWRaSEthT1ZCRzhNcXpwMk02ZlJpWlRodURpaUFrR0NhaTczRHNGcmtiWmVkL25ZUEdKUWV4VVVlZXJMRTU3L3NhWG42USt5L2VvZWhabWJPK3ZyT2ZnSW1pckNhMlBCUlI1MjJzTENVSGMzNnMwSFFSSlJxZDJ2UCtFazhQNUlMY29xSHQwZm44T29oaU85bVllNGhUUmJCNTBUQmtXU2IzM1UwdC9lZnBwNjRPZW9laUgxdTdhSjNiTnplUi9BVXM2aWRUcEswRzdBWm4wUmJZS0wxaFNWUVAwa0FaUEJiYkdKb2s3UlZxelN4eXdGSlp2NnpaOHpnRmo5MGlvVzZBMlJjTkNxbjM2bkd5Rm9OUzdPRTRJOGlPMUMzOE5iN3FiSmkwcDkyUURHUmtVbXBnRVNWRC9PaG5DWUYvalpCQ1VTWmxsODNJWDJxeFZLSG0weFk5VWI3Y2FuUlZLQTN2bVlDQUhPQnR0UnZzT1U4VVY0TGlTNnFhOHciLCJtYWMiOiI2ZWRjZTZjMGYwMzIyYzViYzBhNTJkNjIxNzcwMTU2YjRhZjBkZDgzMDEyMjRjZDJjOTdkZDFkODVhOGZkMTA0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikw0eGFtSlNWbHhBYUlWRWRJZ2RXYWc9PSIsInZhbHVlIjoic2dPc3lYMUFjdU1ORzIrUXJBT1VmdGdWR25YMmN4clIyMG90UWxkR1VBN2hRYXZ6OTZDZ3dPU0JSYUtZNXhIb3pFdWdHeTl5cTlKQUIralFiRFUvMjJXSUt1cFlnS1cwbTY2Wm5MdGcxZ3JuZllXVEQvTWpUV3FwUWV5WTIrSjl0c2FTSmYzWEV1ZjIxOTZ6N3hwbjk5M05JSHQ2ZEFrYng0TTUwZEc4Z0YxRThRNXNBY1pvM0hBU1RkbWt4a1lxM25ySi8vQ0RtbjdDVmVyZzVmZTd3dXA2ckMzaEZoMXNjemNmeEZFaEpLQXczU3NOTEgvUmpsRHhMbUhBMzQzSkVYck5OM3psa0hPZGZuTFpTTUgxWjhnd0l2V25lYmFwVU11VjZBL3RycWpHVHQybXVDaHhrUUZTREkwSzN3OUl4S2ZYVzZiWXFTZ05seWVjbTgwU3JGL1B2WWJaS3NVL0VuTFAzdHNyYVIreWMvQ3FwMUtJK09ydjhteVZPV2UwK2lGZDlHMUdqMjdJSyttbWRUcWpNQ2hMc3dSQlo4NjVIS01WWXFhUHFaNkJ1Ty9XZEVMOVFxM2hlZlJPYUQ4VkZYQS9lOTZUYkpSODYrZkpWdCs3T1dGcGRpNGdzSFpQUjZHWjBOVGRZMWtDMWxMbzJIMEwwQmVVRUwyckN2dk0iLCJtYWMiOiI5ZDhhY2E4MWZlZDE4ZjBiNjkzOTY5ZjkxZTM5NDdkYmU0YzcxZmVmOGNjZGEyZWI2ZTRjNTg5N2QzZjNkZmY4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJQaTBnTU9tWHpCRGtqYUM3Z0RWMGc9PSIsInZhbHVlIjoickwvNVlpZEY5ei82Smlyb1M1UmFMNXlOejdvQW5aMCtONEs0emtIWG00RHl4RFdmS1ozOVRDVWNaZk5JeTRiSmRha1pJMTFQUERtbmVSelBGUVRsVG4wMjhoUTlrdTllZmhkNVB6cXdycUFGRks0bzdXREE3YVZ3U3dROVpPVUg4aGpXOWRaSEthT1ZCRzhNcXpwMk02ZlJpWlRodURpaUFrR0NhaTczRHNGcmtiWmVkL25ZUEdKUWV4VVVlZXJMRTU3L3NhWG42USt5L2VvZWhabWJPK3ZyT2ZnSW1pckNhMlBCUlI1MjJzTENVSGMzNnMwSFFSSlJxZDJ2UCtFazhQNUlMY29xSHQwZm44T29oaU85bVllNGhUUmJCNTBUQmtXU2IzM1UwdC9lZnBwNjRPZW9laUgxdTdhSjNiTnplUi9BVXM2aWRUcEswRzdBWm4wUmJZS0wxaFNWUVAwa0FaUEJiYkdKb2s3UlZxelN4eXdGSlp2NnpaOHpnRmo5MGlvVzZBMlJjTkNxbjM2bkd5Rm9OUzdPRTRJOGlPMUMzOE5iN3FiSmkwcDkyUURHUmtVbXBnRVNWRC9PaG5DWUYvalpCQ1VTWmxsODNJWDJxeFZLSG0weFk5VWI3Y2FuUlZLQTN2bVlDQUhPQnR0UnZzT1U4VVY0TGlTNnFhOHciLCJtYWMiOiI2ZWRjZTZjMGYwMzIyYzViYzBhNTJkNjIxNzcwMTU2YjRhZjBkZDgzMDEyMjRjZDJjOTdkZDFkODVhOGZkMTA0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1544521857\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1702190080 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702190080\", {\"maxDepth\":0})</script>\n"}}