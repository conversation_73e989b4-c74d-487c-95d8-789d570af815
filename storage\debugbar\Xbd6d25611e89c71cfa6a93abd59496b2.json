{"__meta": {"id": "Xbd6d25611e89c71cfa6a93abd59496b2", "datetime": "2025-06-28 14:59:10", "utime": **********.64355, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.199305, "end": **********.643569, "duration": 0.44426393508911133, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.199305, "relative_start": 0, "end": **********.572997, "relative_end": **********.572997, "duration": 0.37369203567504883, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.573006, "relative_start": 0.3737008571624756, "end": **********.643572, "relative_end": 3.0994415283203125e-06, "duration": 0.07056617736816406, "duration_str": "70.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45100000, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0037700000000000003, "accumulated_duration_str": "3.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6020582, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 44.828}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.615019, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 44.828, "width_percent": 13.263}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.62774, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 58.09, "width_percent": 29.178}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6343591, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.268, "width_percent": 12.732}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1889218819 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1889218819\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-392455942 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-392455942\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1049619027 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049619027\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1255424908 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122745018%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhrZFJDenRDQlpVWWdFWUk5VS9VNGc9PSIsInZhbHVlIjoiWHBPSmxhVjdDdHdkZkd3cW94V0xaWm53T0Y1TFQxOS9BUjY4NnZRNDNOL0h0anFWWE11MGR3MWl2cjFORngrUzRJOGI3REJWeXV4c1J4K3I4UTJpalhPT2pzTmpBRVpvKzdoaVhOU1hwNkM1OVNabWJIbTVYS1NvY2l0aVVMb0crRGhHUm4xcTA2a1BMUE1LQUpIZ1NiemUrYnUvaEtmb29lb0UyZTVOQnRrc1VXU3Y4N0g3RnoxYm1jcVZ1TnRvOW5YdElDeWV3VW1GZTQ4TTVwZjNSNTZyenJoWW42bWdpeDdMNHpKUUh1ZHJvSWxMTVI5YzFxWjNPa01Qd2huZjcvRVd3cjVoZklrbThZb1NhWVNWTlo3dmhCUWp6R1k0WC9mK1doZVJZSE10MVlmRmtoMVJxU2RlL0N4RExRWWpkOUZnYXFMemJWWGFOWno5KzVjZjZyVHN3YVY4ajljOHIvWEZwYmVqQzRPaEZmWTd4K1htV2x0MlZpMy9RVlRYNnRzWllzMVVDUEJUb3R1YzF6YTdqTkhOMlpSZGxwdWxrVVdMYmQzZ3hsbEx3eDNEcGdWWDlieGljTnczUGZXOTB6MWliZ2RjQkgyajBUNzZsMTkvd3JxTmV0YkdhcEgwMFJIOW83dnc5RDRZYk1QUWVCNC9DaS9sRENKNWwvL3ciLCJtYWMiOiJhNzAwOTE2MGZhNDMzN2Q5MWY5MGU1NGViNDIyZjJlZTIyYmY0ZjZkNjA0ODM1MjgyZjZiN2MxZDZjZTNjNmY4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJmQklyOHNnVXh2VkcrNlpiNDlXYUE9PSIsInZhbHVlIjoibDd5bnNnc3kzdkc1V3lveFg2aWlNaGpad0pvRm1EN0ZRNThLTjhDcFd5L2JISk5kdzFSckNhRzN1SGRWQmkxeHM4QjVuMk1NY3BLTVZQM1BRWHM4bE51dzFUZVJmUk90K2lKWWtvVHEzZUpPVm9iUFV1TysvcnBMNFc3ZzlTekFjKytSTG9KWDNWS0xrTlM1a3JSNmI4TVV6RzRIVjlGWkdQMDcwVXFJZlBkcmdvY29XQktjN3FzaXAzcDBIZFFyUEx3M2M3V3ZFdm44RFZKaDJsUE5ZbEFoOTVwcllSTHhpRkZJTWNhVjIvdEtzRTQyd3BIMjhUR0pCckM0QVFsajVHa3pBRHJpb3dBMUlxNmpxdURhNVU4OUFZSHVhUGw1UzQrVDIxYUFJRjJYMkxSbXhWY1laVEl5dXJKKzBiSWUzNFBlbm8vcGxIUTBQeVlQOFNwTElBU3hBcExETFhaSGFhNWNWQ2pudnN2RHBvNlVXNktFSTd1QVRQNGQzQ1Z6R1I0MW5Cc2c0bGE2bG9Oa3RZVFJxTys5Y2FHRUhzZ2NsbUU2Y3UxdCt3MDZyS2M0Y29rZUE0ZFBHV1ZYaTBSOWcwTHZ0bkRqeDNFMHlkZHRaMTRPWnV0UGFaVjB4aFJrQXNkS0l3alNvT0lFNWRUSW85RWYxMG1xQ1N5WmtOcmoiLCJtYWMiOiIyNGY2MWI1N2I1M2RmYTZmZGRlNzY4YmNiNGJjYTAxMzMwZDhhMWJkYmVjZTE1OWJiOGU5YTQxYmNiN2FiZGI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255424908\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6nyGbgT6n8t00JbJ5CSgj7vkpXnxnyISs5XS6j6J</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1001743843 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxLYSsvaExEWlRZcE13dkxBV0g5c1E9PSIsInZhbHVlIjoiVStUTUphOWQ3eE5WcEpQRTVrUEZxOVR4QjRrdnc1UktQU2dEWU5yTWNDK0huZUlseUFQeGZTUUNTM2hhVWpaQXU4ODQ2dFh4cVh6Z2g1aFJCeEFYbEE4bDcwcld0clNndTZjK3ZlS3AvUFVXSC93enRiWUJHYnVFTXlnNksxUnN6Q1lYcGczai9kV2d4NWZRM2dvcEdDYUd3amg2ZlZlV0N0Q3Y1bDVoRzEydXFQc3hEaExoZm8vdUZCaFhHOHU0SEx6K3FtY3V6bVVjd3UyUzZYZU5SY2RXSVU5WklmMjNicHVpNmFhRzNXOWppcnlIbkphUjJNVVN3d1dRSUxVK1g1OHRwditycG5MUnVVeTFRRmllVDBVZjZaTlJGejBwTHRSSE1MMDJBVllpdWdOcXpkdDBTMDZUZlZYSlE4RTdwQnJ2eU90VTlNZjNoNHVFajBKNC8vYXVhOFFjNXBBdEpjUGJXNjE0eEZRcFFYUW4yT2ZSNmp6TkxiZGpzRTJ1Zk5icU4rUkIvbGorSkx3L0U0Rm5lby9XVzZ2SnhOcUdRdk9VUytHWXV4Qm5WR2NGaEF6a2orUSs1M0h6YkpKT2ZRMllRSWE1dWlrcCtvUDB1elRIMXVXWVdCUHNEejlDek1YaHJxV1A4bHVUazBDMCtHSnV3N1ZNWDRkNk9YS3YiLCJtYWMiOiJhOTUxNjczMzEzY2FiMDMyZmU0MzhjNTgwNjcxMTZlOTU0NDM4ODY5NzlhY2Q2Y2Y5OTY5YjFlMTc1OTM2N2I0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBrenBaaXRTMWo4R2hBRS93ZS9qN2c9PSIsInZhbHVlIjoiY2M3TEtpaEhnUTM4enFnc3F1eGpSeEpxeUVRMjFucEhMRFNoNlQrcytqUFBRb0IrZ3E3T3AyaG9rWnczdHUxQlBNVXZUaHE4S1FFUzFGK1lsdkhVT3pFd0tndWRaYmN1VUxaYThKTXJoUWpTMUxabjlxZnpCbDIrdnFKL09FSnBYd2dKVEtRa1BMWWptUjZobjRkWUxseEt5Uk9TcndYVDhhUmxqdldDKy9XbytXNVpvdzBLSnl2R3pEN0FtaWFUUkFFQ1V5b1E0UGdHMHZLTGVqcnlyZW0rMUp3UGsrN09VcDIwVWpwTjAzTkZXNjRaSG9YTFlZUEJiZ2hjbmRjTVhjSElFSENpeXFrMW9QZVpOL000N2VDUE1WdkxZSnZkMHY4djFYT3hTVzlteHhKL3VHOG51WjY3MVZ5Q0lIa0pjNEVpbGdlcCt4dlAxVWdEMG1TQUhaQmtjeXhLYS9ScUl1ZUx3dGZhUmZ3ZTZsWnRRN1piRmEyVkRKVlFSU1AzL2hDQUxtR2pTZXVQTDFJRXY4TGVYTDJzRm4xb2F2RC9CU0p2azRLTVJNencxakhubHZKNkozYVB5MUJlb3QwS2dad1JEd0kwRDlhLytWREpOV0FCWnZvTGFmSEhwL1B5Y1FXcndiV3ZYdTR2SFlVei9RajFCY0w1TzNHYlpXUXciLCJtYWMiOiI2NTJlNzczM2UxNDQ1OTlhN2EwMzk4MjY1NTM4MmMzNDdhOTUxYzRjM2Y1NGMyOWM3YTE2NjdlMTBkODNiNDI5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxLYSsvaExEWlRZcE13dkxBV0g5c1E9PSIsInZhbHVlIjoiVStUTUphOWQ3eE5WcEpQRTVrUEZxOVR4QjRrdnc1UktQU2dEWU5yTWNDK0huZUlseUFQeGZTUUNTM2hhVWpaQXU4ODQ2dFh4cVh6Z2g1aFJCeEFYbEE4bDcwcld0clNndTZjK3ZlS3AvUFVXSC93enRiWUJHYnVFTXlnNksxUnN6Q1lYcGczai9kV2d4NWZRM2dvcEdDYUd3amg2ZlZlV0N0Q3Y1bDVoRzEydXFQc3hEaExoZm8vdUZCaFhHOHU0SEx6K3FtY3V6bVVjd3UyUzZYZU5SY2RXSVU5WklmMjNicHVpNmFhRzNXOWppcnlIbkphUjJNVVN3d1dRSUxVK1g1OHRwditycG5MUnVVeTFRRmllVDBVZjZaTlJGejBwTHRSSE1MMDJBVllpdWdOcXpkdDBTMDZUZlZYSlE4RTdwQnJ2eU90VTlNZjNoNHVFajBKNC8vYXVhOFFjNXBBdEpjUGJXNjE0eEZRcFFYUW4yT2ZSNmp6TkxiZGpzRTJ1Zk5icU4rUkIvbGorSkx3L0U0Rm5lby9XVzZ2SnhOcUdRdk9VUytHWXV4Qm5WR2NGaEF6a2orUSs1M0h6YkpKT2ZRMllRSWE1dWlrcCtvUDB1elRIMXVXWVdCUHNEejlDek1YaHJxV1A4bHVUazBDMCtHSnV3N1ZNWDRkNk9YS3YiLCJtYWMiOiJhOTUxNjczMzEzY2FiMDMyZmU0MzhjNTgwNjcxMTZlOTU0NDM4ODY5NzlhY2Q2Y2Y5OTY5YjFlMTc1OTM2N2I0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBrenBaaXRTMWo4R2hBRS93ZS9qN2c9PSIsInZhbHVlIjoiY2M3TEtpaEhnUTM4enFnc3F1eGpSeEpxeUVRMjFucEhMRFNoNlQrcytqUFBRb0IrZ3E3T3AyaG9rWnczdHUxQlBNVXZUaHE4S1FFUzFGK1lsdkhVT3pFd0tndWRaYmN1VUxaYThKTXJoUWpTMUxabjlxZnpCbDIrdnFKL09FSnBYd2dKVEtRa1BMWWptUjZobjRkWUxseEt5Uk9TcndYVDhhUmxqdldDKy9XbytXNVpvdzBLSnl2R3pEN0FtaWFUUkFFQ1V5b1E0UGdHMHZLTGVqcnlyZW0rMUp3UGsrN09VcDIwVWpwTjAzTkZXNjRaSG9YTFlZUEJiZ2hjbmRjTVhjSElFSENpeXFrMW9QZVpOL000N2VDUE1WdkxZSnZkMHY4djFYT3hTVzlteHhKL3VHOG51WjY3MVZ5Q0lIa0pjNEVpbGdlcCt4dlAxVWdEMG1TQUhaQmtjeXhLYS9ScUl1ZUx3dGZhUmZ3ZTZsWnRRN1piRmEyVkRKVlFSU1AzL2hDQUxtR2pTZXVQTDFJRXY4TGVYTDJzRm4xb2F2RC9CU0p2azRLTVJNencxakhubHZKNkozYVB5MUJlb3QwS2dad1JEd0kwRDlhLytWREpOV0FCWnZvTGFmSEhwL1B5Y1FXcndiV3ZYdTR2SFlVei9RajFCY0w1TzNHYlpXUXciLCJtYWMiOiI2NTJlNzczM2UxNDQ1OTlhN2EwMzk4MjY1NTM4MmMzNDdhOTUxYzRjM2Y1NGMyOWM3YTE2NjdlMTBkODNiNDI5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1001743843\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-319273118 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319273118\", {\"maxDepth\":0})</script>\n"}}