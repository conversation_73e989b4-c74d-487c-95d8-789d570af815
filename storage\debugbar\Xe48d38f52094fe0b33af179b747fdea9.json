{"__meta": {"id": "Xe48d38f52094fe0b33af179b747fdea9", "datetime": "2025-06-28 15:08:23", "utime": **********.710107, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.018446, "end": **********.710121, "duration": 0.6916749477386475, "duration_str": "692ms", "measures": [{"label": "Booting", "start": **********.018446, "relative_start": 0, "end": **********.65084, "relative_end": **********.65084, "duration": 0.6323940753936768, "duration_str": "632ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.650851, "relative_start": 0.6324050426483154, "end": **********.710123, "relative_end": 2.1457672119140625e-06, "duration": 0.059272050857543945, "duration_str": "59.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46269312, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00209, "accumulated_duration_str": "2.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.691138, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.99}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7014182, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.99, "width_percent": 22.01}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1456/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-2121002431 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2121002431\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-784288497 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-784288497\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-800548582 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-800548582\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1057859020 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123286871%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imd0SGpMbzhpZlQ3ZGNOdVNsS204Z3c9PSIsInZhbHVlIjoiN3VVZ3hZQjJFZzJya2lCR0RhbTBWM1lIakhsNjlPcHdURXJNYWIyd1RsdkRZSjlrVzJRSlhpZjdITlh4a090V0ZHME1ESmMvUWlhWlAxUGFjUmFJZyt0U3g2WElEUHZwazEvQ0NIOHZXbWhkbG9ZRzBhMUsrQUUrMnJNYWxmcFFTR3FhWGVmcGZTNlRmZUZQcXQ1Q1BxRGg4a21sZXJzeHdUSFA1ZDVLellBNDZVQ2ltTGNzTEg1ZkZtOFlnQXhpdUFCKzU5bWRwbStnRmYyMG5FSWR3Y1c4UFY5MXlrZjlkSG03c21zNlk0TWI3M0E4OE5qQURJaHBwSExUQWoxL0k0OUIrL1pHNXFRZlhHY3JpQkxsMHgvc0ZEWEprdld0UTI1VHFPSkpVdjhKT1Q5UytDU1VZY2d5UTlqTjhRSHIzOEFGRHpMbnMzc0pEZ216STNSbHNQZEdxSGgwSkhxOHV5MzFPQXVhWlpsQzNqbUY2ajcyN0hzLzZuTHRoZ0t0a0hZdzhtTElMRHAzdDNlYTFNTFFueWZJajByQXBVVkRYTS9kL0R4UVFITXUwNjB0WWIvMFJSdjVsbUpkcGtJRGlHUU9wY05PUmFmZVZzRGkrU3MwMVY1MHFzODI3ZkQzUGJOSmhVcUR5YUJTQlhURm5jcTNNaXdDZFlkQ092b0YiLCJtYWMiOiI4ZmQ2N2U4MjNkZTI3ZTllMWFjY2VjNTZmMjYxNmVhMWY2M2EwYTE4ZWJiMzgwZTAyMDA2YWYwYzQxYjI3Yzc3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjkrSTBiMng4b1I2UDZDSGJWSjhYaXc9PSIsInZhbHVlIjoiU3hoOXlzMXQ3QUFHeHY4d1NvL3NtQ3lUUnoyTVJiVThkMExFdlJEdmRzY3JSNzhiTFFnODRyUWVnNjluODZCclVzbVlRZ25xQ2NYRlRLbWNYOU9qeDNrY24zS1BRVnU2dGZhZk9pWllKSk9pbnRHdGxIODQzYklpbndPMXZXS041WXdURWJLdHNQZ0ZkTUVWYytDK3pKeW9XaUtEaElDK0ZmdCs2ekpQVE5MRzNERGgxTEs4QUMvc1k2UWxCalgvNGFSL1FvMSt2eUIyVFk2UmV0OWxrenBSNHg3bFJHaGQ4NjdmZU9KUzNuZnJyMHUwZzhzWTlUSzdIbGJpUFQ1T0tvaGQzMDY3bW12c3FOR200Q3IwVktDYXl6cXc1SXpFaGlyaDRKZ0Q4UjdCN1BxcVNTSHdmMVpwMThHdjBVelVOOVdMbzFFL2tJSHJCcjJFVTJvWW9YYnIzMzU5cFJCcTJxT0dXL3diWmFDVXhlWmhoMHVzZGtRbzh3enlLUTRzYTh5U3BJYnJPOTZVWSs1ZXNrSTVlTTJqOWRZcGY0QlM4aVI3Rm5XWDJXTncxZG1UU1BmMnpHcXZlMmNhMDhtWVAzYU5aa3I0MEFTNG15Wlp0Um5TT2RwZ2dkcThMQWgvUnpESDIxakxRYS9UeGhPVkFsNGRiS05INHpmdk41YUsiLCJtYWMiOiJjYTc1MWM5NDZkZDkzZmM2Mzk0NDc4MzVkMGEzOTU2ZGYwYWNiN2MyNTRjMzhjM2YwN2YwNzA1YWU0ZDdmNzEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1057859020\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-570027292 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFjU3hrczdLVUFiV0tMVjJJSm1qdWc9PSIsInZhbHVlIjoiZ0xUdmwzemhySkQvVGhnWitSNDZtYTgzb1djNVl1RHVjOGxIcVZYTGovUEZKZ1loMG51Z21jN1lmMk9RQXVFTHhRWkJRZ3FCUzBqMVZPb25FWkNYNkEwajNWeTJ3Rmp1dmpBRUl2ZlIzTFEvRVZmYTVtUEV3TGpHaFpSVk9ieTZMNVY0Qjc0VnJpdy92YUFvVlYwSy9hbG5JZFNicW1tbS9ERFlJVnF0SkhCTVhyZHJoc2NCQnR2ZGh2WTEra2drSm4xQkZJZ2Y1eTZoM0tNUzdzVW01N056RDUwS0IwcUZsTDBJZzBRR1U3TDkzR2NOelFFK2hVTlRVL3RIelJ1T2ZOMUY0SUVuNGNJSkM2aWhMenprWXlaV21tTVA4Y0lBeWtrS3E0azlsMmJXbE5Ta3pEbm0rMG44VVBuTEdLZ0U0NU9YL0ErOXN0bWZ6bDdFRExPQ2l0bEltek43Mm9BRFRhZXdzelh2b0kxV1RCU1ZMY2pVNGNCVm02aTBiWkFHeEhPSk13N2dKVVgwSzZpbERZUk5MQUcrMmlaaUtLY0pKRXc2SE9rVzdDKzhPbkowdS91d21OcUZHbDFEMStKR21hNUVDQVJ4WERqd2tteXpHZG5Sc3ZvTnpiKzJ5S3o2SkEzdjJoY1ljd2dvWjV4d3B4dG00M2xSZnd5NEFMUy8iLCJtYWMiOiI3MjAwZDE4ZjczOWNkNDlkZjQ0ODhkNDU0NzZkMWE5MTBlOWYyYjhkOWExMDI4NjdkYzE5YWU1YjA5OTNiMWIzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBhV1JPcm5tTkM1ZStoZTBFWEgzN2c9PSIsInZhbHVlIjoiNWhWbDg1RE0wdjZ5QVdZZXQ2NnM4OUhRTlBjd0F6WHJHRE1IUFFObmdGWlAvY3A0Nk9IS04rZVZOT0ZYK2RvcVFHc1U5YS9mcXVCeHpWUmN5WDc4bDM1MU1NcVMvNXlDT1FNRGNIRWNzSHN1VEJQRGtsL04zbGRycE0zUGFSSFJCMEhxRmViMjgveGZHYWFtdGdzV1pNeGMvdm9XTzY2MU5odWlKVy9Gd0tOODR1VzF6YVU4RUF6elZ3SkNnNk90eTZJdUZvUWN5NEtLdGZPd3c1amJxY1F6WitaMWFqaVJ3SWV0ZklaMlVPRTFqbVZSVUtVRFRkTWlyd3RqaXlaNTVEMGsreGxraEFjazRHNjB5dVBXSWtnVWVMckdvcXk5VDUvcmE2SkQrNVd4ZG9nbHYxMzFmVXlRTS82VS80T0hpL1FvcmtKNlEybitRL1h0K2VoY0JuTnJLOVJ4UXhic2tsQ1BMblV2SEpWTlRmZnVUM1FvUVptS3ZWK0Y1NHlrMHFYenh1dkNSTko2bStINlVINlFIUFdFMG9LNkVBTWhUZzc1c3NpaTdYNDIvVWw5OS9lTE1jLzlPNnpkcFVvVmZqY0lYM0pVU3BDR2RseHFneG9HV015KzZsOVhsa05oZGEvcjE4ZXpVVmdTZDR0bEY2NjhRR1VWa3dOdTllV28iLCJtYWMiOiI3OWFkZmM2ZWFiODc3YzYxNzQ3YWQ1NDEwNWNmMzU0ZDc2MmJiMWI2Nzc0YmU1MzU0YjYyYWUyYWM0ZDc3ZTI0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFjU3hrczdLVUFiV0tMVjJJSm1qdWc9PSIsInZhbHVlIjoiZ0xUdmwzemhySkQvVGhnWitSNDZtYTgzb1djNVl1RHVjOGxIcVZYTGovUEZKZ1loMG51Z21jN1lmMk9RQXVFTHhRWkJRZ3FCUzBqMVZPb25FWkNYNkEwajNWeTJ3Rmp1dmpBRUl2ZlIzTFEvRVZmYTVtUEV3TGpHaFpSVk9ieTZMNVY0Qjc0VnJpdy92YUFvVlYwSy9hbG5JZFNicW1tbS9ERFlJVnF0SkhCTVhyZHJoc2NCQnR2ZGh2WTEra2drSm4xQkZJZ2Y1eTZoM0tNUzdzVW01N056RDUwS0IwcUZsTDBJZzBRR1U3TDkzR2NOelFFK2hVTlRVL3RIelJ1T2ZOMUY0SUVuNGNJSkM2aWhMenprWXlaV21tTVA4Y0lBeWtrS3E0azlsMmJXbE5Ta3pEbm0rMG44VVBuTEdLZ0U0NU9YL0ErOXN0bWZ6bDdFRExPQ2l0bEltek43Mm9BRFRhZXdzelh2b0kxV1RCU1ZMY2pVNGNCVm02aTBiWkFHeEhPSk13N2dKVVgwSzZpbERZUk5MQUcrMmlaaUtLY0pKRXc2SE9rVzdDKzhPbkowdS91d21OcUZHbDFEMStKR21hNUVDQVJ4WERqd2tteXpHZG5Sc3ZvTnpiKzJ5S3o2SkEzdjJoY1ljd2dvWjV4d3B4dG00M2xSZnd5NEFMUy8iLCJtYWMiOiI3MjAwZDE4ZjczOWNkNDlkZjQ0ODhkNDU0NzZkMWE5MTBlOWYyYjhkOWExMDI4NjdkYzE5YWU1YjA5OTNiMWIzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBhV1JPcm5tTkM1ZStoZTBFWEgzN2c9PSIsInZhbHVlIjoiNWhWbDg1RE0wdjZ5QVdZZXQ2NnM4OUhRTlBjd0F6WHJHRE1IUFFObmdGWlAvY3A0Nk9IS04rZVZOT0ZYK2RvcVFHc1U5YS9mcXVCeHpWUmN5WDc4bDM1MU1NcVMvNXlDT1FNRGNIRWNzSHN1VEJQRGtsL04zbGRycE0zUGFSSFJCMEhxRmViMjgveGZHYWFtdGdzV1pNeGMvdm9XTzY2MU5odWlKVy9Gd0tOODR1VzF6YVU4RUF6elZ3SkNnNk90eTZJdUZvUWN5NEtLdGZPd3c1amJxY1F6WitaMWFqaVJ3SWV0ZklaMlVPRTFqbVZSVUtVRFRkTWlyd3RqaXlaNTVEMGsreGxraEFjazRHNjB5dVBXSWtnVWVMckdvcXk5VDUvcmE2SkQrNVd4ZG9nbHYxMzFmVXlRTS82VS80T0hpL1FvcmtKNlEybitRL1h0K2VoY0JuTnJLOVJ4UXhic2tsQ1BMblV2SEpWTlRmZnVUM1FvUVptS3ZWK0Y1NHlrMHFYenh1dkNSTko2bStINlVINlFIUFdFMG9LNkVBTWhUZzc1c3NpaTdYNDIvVWw5OS9lTE1jLzlPNnpkcFVvVmZqY0lYM0pVU3BDR2RseHFneG9HV015KzZsOVhsa05oZGEvcjE4ZXpVVmdTZDR0bEY2NjhRR1VWa3dOdTllV28iLCJtYWMiOiI3OWFkZmM2ZWFiODc3YzYxNzQ3YWQ1NDEwNWNmMzU0ZDc2MmJiMWI2Nzc0YmU1MzU0YjYyYWUyYWM0ZDc3ZTI0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570027292\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1454970560 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1456/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1454970560\", {\"maxDepth\":0})</script>\n"}}