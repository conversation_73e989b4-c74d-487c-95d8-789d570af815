{"__meta": {"id": "X6dad9d3e6acc1c111e8d47747e5597b7", "datetime": "2025-06-28 15:08:52", "utime": **********.513177, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.078589, "end": **********.513193, "duration": 0.4346039295196533, "duration_str": "435ms", "measures": [{"label": "Booting", "start": **********.078589, "relative_start": 0, "end": **********.457975, "relative_end": **********.457975, "duration": 0.37938594818115234, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.457983, "relative_start": 0.3793940544128418, "end": **********.513195, "relative_end": 2.1457672119140625e-06, "duration": 0.05521202087402344, "duration_str": "55.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45611592, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027799999999999995, "accumulated_duration_str": "2.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4855971, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.741}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.495539, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.741, "width_percent": 14.748}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.500514, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.489, "width_percent": 11.511}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-951012933 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-951012933\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-791224927 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-791224927\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-230118627 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-230118627\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-722735704 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123308213%7C8%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImttSU5QYTV1cGw4UlM0WWV4L2dabWc9PSIsInZhbHVlIjoid3NtTGJ2RFZ1ckFabFQ0cFRKbTFSTVZWTWJIRUtuUjdXbHBiQUJRRVNNaDE3QnA5aVdUTm0xV09NMms2ejhEek9rR0VWNk9JZFlYeWg5ZTN1Mk02Vnh3RG0wWXpvNStwdFEzRDNpNnhPUUxmR25IajRpUHBIQXlyUnlQTW1aTzJISERqRnVKQnVHNzZiSDJRL1dQNTVsRGtNYllxNkdrKzhoZGRDUStNcEZMWTFjdytueGZibnlZS1ErNkNOMnNPOCtPL1ZCUGhWd2U5aEhXNHIvd3ZsWEViVDZ6Vmp3VGVGTTd2REhJQ1VORXJFTWlDWFByNnBEUkFVb2VsZmw3OGZRTXo1ajFiM0s5MTZGWFk3UnBMSU1WcERkQmdhYUFaaGVDUzFobXlNQnJWcFFzVGhaKzJQeDJQZnUyZmdZSytSMnNoV2h2U0F6VzRUeWlxZmNqRyt2MHphNnVDZG9RcUt1UEYwOHIwZGljd2V4ciszeHo2OHFEZXR3bTFjWmorSlo3d01EN2llOEYrMWNjTXFYVXd4RkNLQVFCWTFxaSthNXlJM1doQjdmOGVXS2N4WG1aWkYxZ1U3dUo1SEtQT25LSXFHOWU3MlJack5ickpwWUVBYm9uMXpSRU5CQXhqTXY4WERjYWlFVVN0V0ZDVjVxdnd5OXU0eEkzUTJBQjciLCJtYWMiOiJkMmQwMDFkYTY0YWExOWUxMDc0NjcyMTVhMGU4MzRiMzFmODJmZjczZjIxZjgxNzMwOGJjZjg1M2QyMjFmZmM2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBnS0NEVzNUaUhPYlZidnQ1a2k5NVE9PSIsInZhbHVlIjoiSm9LcHlERXlQcUZ5dDhmTU1WOExnZHlLdFNrUVV4aDI0d2Ivcy83VUxSV2FNbWdZQnNaRGJYYURIZXhsOEVtdnB3QXQ5UldkSGl1V1hFMkNVQk00WnN5QTRENlFtSVJYQnBvckZ1S21CeDJYWGVJL2lYNS9nZzNuNnd6MDJKSkRmZGVZQVErNmk4ZGRQa1lzL2I2eTh4SGcwYzRhbk9VWnd0cTRqZy9jTWFXdWFPM3NRRElKcDVsY2VRNGkzZlhERHlPS2lZcWdOK1pBaWo1YkN4QjBVVVA5aWFMVk9GN2YvL3RBYWpnZFNWK0xkNWRUcW96VEFQMXRveEh1ZFlOZEJGNjJKbzJJVzRESE9yZVc5YmgvK2lBdEk5aVNDNyttVmgrODNMSGlsb3JMQmRqd3ZXYnpFWG1IZVVJSjFqRGh5TXBQSEJHcFE3ejlqVXdHWUlUcnlFS1BFYXZqaDk4a2ZLRmxjY2x3ZEtrYnlYd1dYRWZqeHRIOUx5bjhlR29MUzZCRWRiMHY0WitEdU1hTG43YzdCZldtSG0rMGdHbWE5aVMwYXNRTjR3cTZPT29GbDFQVWd3d2h6aitYbCtxTWFjdFhCdUh6VnVpd3ovaVNnTWI5czNLQUE0ZzFVMlQvSjFzcERxT1R4VEN5dGE4QVptVWM2d05hMGM2dnI2ZlYiLCJtYWMiOiI0NmVhMjIzNWYzOWNlN2EwNDI4MDg4MzRlMGQ0ZDBjNDYwMzA1MTI5ODlmN2YzZWVlYzM0OTY2NDUzN2QyOWE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722735704\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-890518258 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890518258\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1640089455 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imk2cTRMcjJZRDJFQXl5OEFScW1iSUE9PSIsInZhbHVlIjoiR0EzdjhxTVVzV21MdW9WbjdKMkZULzVOMFFLT2lHTmJrT0Z0aDNmNXFyeDBQZ1I0T0hyMklZbDNGY1A0TElPRzd0QzFLeDN0MGU0d1orbkpQY3BqVlJpRTQwSDJtNTZEdk5WcGlLelpDYnRTeVZveHNITGZ2TEwvcGJ1dmxUTkllcWUxNkpkYmF6Sk84bzRESkM3Q1ZnMTlaZTNNanpJSG5tWDhSR052TlFUTmNXSHF0MWNRaWRDMmY3MHg2SkdaRGQxL25lQWRsZDRsVGRSS2xQWllWUlliOTFMeHhzdllEekhqVmZEU1gxQll5dWpXTVhWYmE2UFh4a2hnYzFIWW5IK2x3ZFBZeVVZQUduaVF3Q0tjZFNVNmw4TlFoNzd3WGZqYVBIVU1RcE9LcnJ2WWpNVG81MjVId2NqY2ZtMG9GWUhUSVpTcHBJOGsyVVNBMUY0WWZzNktiNlREbTlBakR5K1cwaFpRVDB5QWEzV1pONUNuUHp6VWZxbkNUUk9CajJCSU1QcWkyQ0VYaVlOR1QxMmhHeDhlZmpKanVQR0pEN3FrUk14S2xuTU9taHU5NEhlM0pZMUpNemR4RWpsbjhuS2hkc3hRT2tqdmV1SXJsb2VRNjFjeW9zRUdJY0g3V1F0TTIwdVZuemVNcmpXN1ZDRFJyOFl5cDlrQzBRN0QiLCJtYWMiOiI2ZGJjNGE0MGYzODVlOTk4NzZjMDBkY2FkYWQzMWJmOTViZDQyMzdjMjQzZDZiNzI1ZjYxMzVlNGFjYmM5MTgxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFwK3NiZW5MTEM3VCtudU54OElFakE9PSIsInZhbHVlIjoiVzdyY0g1VGZLNHJrcUM3dUVDOHBVWU4zbkh3MzFQZXFMaDk4dkdDRkpCZk91MkgwTk5hQlJtVnlndVlHZ2FtK0wxaURndXRJMTB2VXcyMzEyS0I1TjJTdnEzZGhYZHpKUE9LZVZTeDR1OG9vMGRUU3JIcGNXdUJyMVFoWUVOMDJkQ1ppWXRNUHpsRDVlQ01ZL2Rod0lFTGFmYnJqN3lVQ2JhRldhU0E5d2wwbG1pVUlPWVAzbndISVY0RGd5VWFGNjh4bmZpNlJHUlpqSXFuZGJyUkVsQ05kYy9xOG9wV0tXQ09lQVlHUHRjd2xFMlQ0UTNmSjlkYVJGd0JLOHVGc2l1K3R1VnUydjFUTE5YMCsxTDBIcnRURkdISkp1T0ZLY0x3ck9xaXlnbTI3bEJ1Nng3RXRlZ0RBRVlHNndCS3lJaExpV2ZXL3V2Y0Z1ei9yelFnUUEySVV5V1lsb3lkSTlIZndwNUxFU1NWU2JSY1MwaTdjYnJrbVRqTUhqSGo3VUtDMGc2LzlBbno1RnhvTll0dFlabGlYSVVCNVlValFXcWZnODVYNkdVK0RmN0FlOC9TdkRYcWVyY1U0ZURNMFVzVXdObmJMRVRBZm1vbTRlNWFCZ3RhRUpxalNCKzQ3L3k5ZVg0cVZyUzlrNHZVdUptYkJpRXhPTUtKM3Z4K2YiLCJtYWMiOiI0OTA4YTIwYzU4Y2VlNGEzZGRkOTVhYTQ4ZTE3YjllMDE0YzMzNGZmNTc5ZmVkMGU5OTJjMWY5YjA5MjYyOTE4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imk2cTRMcjJZRDJFQXl5OEFScW1iSUE9PSIsInZhbHVlIjoiR0EzdjhxTVVzV21MdW9WbjdKMkZULzVOMFFLT2lHTmJrT0Z0aDNmNXFyeDBQZ1I0T0hyMklZbDNGY1A0TElPRzd0QzFLeDN0MGU0d1orbkpQY3BqVlJpRTQwSDJtNTZEdk5WcGlLelpDYnRTeVZveHNITGZ2TEwvcGJ1dmxUTkllcWUxNkpkYmF6Sk84bzRESkM3Q1ZnMTlaZTNNanpJSG5tWDhSR052TlFUTmNXSHF0MWNRaWRDMmY3MHg2SkdaRGQxL25lQWRsZDRsVGRSS2xQWllWUlliOTFMeHhzdllEekhqVmZEU1gxQll5dWpXTVhWYmE2UFh4a2hnYzFIWW5IK2x3ZFBZeVVZQUduaVF3Q0tjZFNVNmw4TlFoNzd3WGZqYVBIVU1RcE9LcnJ2WWpNVG81MjVId2NqY2ZtMG9GWUhUSVpTcHBJOGsyVVNBMUY0WWZzNktiNlREbTlBakR5K1cwaFpRVDB5QWEzV1pONUNuUHp6VWZxbkNUUk9CajJCSU1QcWkyQ0VYaVlOR1QxMmhHeDhlZmpKanVQR0pEN3FrUk14S2xuTU9taHU5NEhlM0pZMUpNemR4RWpsbjhuS2hkc3hRT2tqdmV1SXJsb2VRNjFjeW9zRUdJY0g3V1F0TTIwdVZuemVNcmpXN1ZDRFJyOFl5cDlrQzBRN0QiLCJtYWMiOiI2ZGJjNGE0MGYzODVlOTk4NzZjMDBkY2FkYWQzMWJmOTViZDQyMzdjMjQzZDZiNzI1ZjYxMzVlNGFjYmM5MTgxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFwK3NiZW5MTEM3VCtudU54OElFakE9PSIsInZhbHVlIjoiVzdyY0g1VGZLNHJrcUM3dUVDOHBVWU4zbkh3MzFQZXFMaDk4dkdDRkpCZk91MkgwTk5hQlJtVnlndVlHZ2FtK0wxaURndXRJMTB2VXcyMzEyS0I1TjJTdnEzZGhYZHpKUE9LZVZTeDR1OG9vMGRUU3JIcGNXdUJyMVFoWUVOMDJkQ1ppWXRNUHpsRDVlQ01ZL2Rod0lFTGFmYnJqN3lVQ2JhRldhU0E5d2wwbG1pVUlPWVAzbndISVY0RGd5VWFGNjh4bmZpNlJHUlpqSXFuZGJyUkVsQ05kYy9xOG9wV0tXQ09lQVlHUHRjd2xFMlQ0UTNmSjlkYVJGd0JLOHVGc2l1K3R1VnUydjFUTE5YMCsxTDBIcnRURkdISkp1T0ZLY0x3ck9xaXlnbTI3bEJ1Nng3RXRlZ0RBRVlHNndCS3lJaExpV2ZXL3V2Y0Z1ei9yelFnUUEySVV5V1lsb3lkSTlIZndwNUxFU1NWU2JSY1MwaTdjYnJrbVRqTUhqSGo3VUtDMGc2LzlBbno1RnhvTll0dFlabGlYSVVCNVlValFXcWZnODVYNkdVK0RmN0FlOC9TdkRYcWVyY1U0ZURNMFVzVXdObmJMRVRBZm1vbTRlNWFCZ3RhRUpxalNCKzQ3L3k5ZVg0cVZyUzlrNHZVdUptYkJpRXhPTUtKM3Z4K2YiLCJtYWMiOiI0OTA4YTIwYzU4Y2VlNGEzZGRkOTVhYTQ4ZTE3YjllMDE0YzMzNGZmNTc5ZmVkMGU5OTJjMWY5YjA5MjYyOTE4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640089455\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2094973398 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2094973398\", {\"maxDepth\":0})</script>\n"}}