{"__meta": {"id": "Xf043e80445b44814eaa954b55f2a741d", "datetime": "2025-06-28 16:01:16", "utime": **********.181545, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126475.678744, "end": **********.181559, "duration": 0.5028150081634521, "duration_str": "503ms", "measures": [{"label": "Booting", "start": 1751126475.678744, "relative_start": 0, "end": **********.09298, "relative_end": **********.09298, "duration": 0.41423583030700684, "duration_str": "414ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.092993, "relative_start": 0.4142489433288574, "end": **********.181562, "relative_end": 2.86102294921875e-06, "duration": 0.08856892585754395, "duration_str": "88.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46415616, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2396\" onclick=\"\">app/Http/Controllers/PosController.php:2396-2430</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02715, "accumulated_duration_str": "27.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1359632, "duration": 0.026600000000000002, "duration_str": "26.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.974}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.174449, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.974, "width_percent": 2.026}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1915031932 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1915031932\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-917077644 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-917077644\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-914013277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-914013277\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126473347%7C8%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFwZzZVVzVqQlVrdmVOdDBJSnROYnc9PSIsInZhbHVlIjoiTXRWdXBBQU84ZkZYYmcvMTV1aWd3WmdCUWJFWlFUZ05JZk5rMjRWK00xYXYycDNBdTc1UnJVM2R5SExISU1lWVVlODhSOWphaVpqeENFdzhsdFpoNnl6eko2T3B5NGltL1FjeTdGRDhlYzZFZTR0MXUzWnBybDdlb29uaGhmSlppSVRPMFNHTFRzQTlOWkxmSXVPOUNIMUliUHhFcGM2a29CZXhkci9SdzQ3bWVmcWJXM21YM0VhTjR6eVBOWnpOQ28rbmdoYjIzMlgrYU5QY1BmUkRwNENnaUJTbEM3QkJvVGtpN25MWnNOa1hSYkdHSHZ5SXkyWVZmMHFPUmUwaGZaRGlIT3dzVURkTzVUdzNtTGJqdUlGRTRnOWd4elpnRGMwQkZ3eW80TUNUbDhvNzF3aSswWXExWWsrckFubGdsNitSSW0xcld1eHNYSzdMeWNCUHN1TkN3RzBaT0E4bHRZVmNWcDFXZjI2V2QveklTeEhoM0pNSm9OdHFIVFN2V1dxWmlsUDZXWmV3UWI3V1V6S2ltZ0pUcklwakJOZUVOQXFJSDZQWUc4RnBjeGxTaG50ZWlvTjdrOWlSNDdvWWxjdU1sTVpydHhmWFNRbHRJdmFrK3Ewb080eXJjUmhlTE1aYTMvZitLQlppbVhhQm0xR1AyME9WRWNKZjdPam4iLCJtYWMiOiJjNjQ1YzEzODdhOGFjYTUxMjZiZThlMjg1YzRhYTAyNTUwNGIxZGEwMDUxNjk3MThkNjIzYzZjZjM4OGViOGU2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5TemV4bWFnZ0ZmM0ZzRk1TT1VoQ3c9PSIsInZhbHVlIjoiL3hud2pEMi9EMWtSR2U4NXJlSDZNMVI1bzI3Mmk2YTZRL0FacXNxeUd5eXhVUjkxUHVTUUlIT1M4R1paKzRqSFdjZXZuaHlkOWVMelU1K3BaQmRQbHdBRlI0M1FUSFhLeVhyVldqSXpKakZtWDBzTUJuOFNwVEpnZEd2cVFkdkNXNmQxZEtNNFp0UDVJVmorbjVPTldBdVlwOUJBUnduR2puM244c29LUlFwSGloUUFPNkY0V0t3R3VlQXprSi9uVnFmRU14aEpnZmljLzNLdWZ4UWxRRGIvMHRCdHJ2bHVnYmpJOUlkSUVZSEt4c3FQaHlxZWtFR2tEVkZsRE1ISkJoNG9ja1p2RXkwWktBNUtzYnRhWE9OdUVwR1Zrbk1JNjc2eThVNHFuSFk0UVdYTjk5VDRkYmN1cEhnNExsTEM4V2ZmVlNwSXBiMEZPNGJsSXJaU0R3NmJxNzFTc0JMQ0hWekNyRks1UFhxcHYrUjhzNVZac2dHczcyWnR3MEZia1RWbVlvT3lFWFFBRFIyekRpZ0tMOXNUVllDYlFvMHp1TWphTkpoK3M3Nk1Gdmp4aFo2V2Zqc2E0ZmYxaWxuQ1ljZkJWdE5NYjl5QVUxWXplejQ2MFZIOThEQVJ0M2VvMCtkcTB1SVRhNkJyR0JVeTNoc0d5MFp1ckhYRXpoS0kiLCJtYWMiOiJjMzFlOWU5YjA2YTVjOTA2YjU2NWM3MzVmMWU1OGQ1NDE4ZWYxZDI0ODlhMzc4NzI2NTY0ZmE3ZDc1NTEyYmE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1772362691 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InkydVY4b3IwSXVlSnEyT21yMWRYanc9PSIsInZhbHVlIjoiVXl6RHR0K1B4aFg0N25nRjg4cDlxY0s3QjNnNytBY1ZRK1FFTXBLdVQ0NVR5R2tqSjY5ZFFhdUlYaWRqZUhRSnM5OC9jamV1c0x4UThYVkF6WFg1Si9SbmgxOEdkNS81bk9mWXdXUTBpbkNac1lsMVF5dHVMSHlnVEJHOWNlRkZNMUpNcFJyQnhjdzM0WkRwZGlqWTVtb3V1ZEM0dXdJdTdjMXl5KzNreUg0cEM0ZncvLzJNbTVwZ1ZCbjN3U3hWdDR2SElOZFpwcjNyNWRJUHNWcTlPOS96VW9CWjczTjB6cW1NZHpodzJNemhyc2VLcUlIYXhzR3hiSEc1NlEzY2FVZmcxRjFWZm1aVUdDS3Z3aHQyRG9sUXEwRDhDUHNnSDNWVG16SW83TGVSdW1qbzJhZVlyam15RjZzUlE3VGVjaUpkTXRFZjFjZ21weDZJaTlNRk9CVFMwOVg2T2RNNWkzbU04ZDk1eDVESFV3VE5UbmZkaUlwQ0QrSTJQTG5vRjAwamxoUUdlZFZzN0Z3YzlVdWZUVTdodGhLOXJhSFl5Z2F4THZhZ2JBQ1F4U1JJWHp5Q3VSUzdoNG93a3d1YW1rUXBxK2VoSG9Uc1F2S0ZVSFoxemM1SDFIcTRVSHJBZGMrS283cmdoeW81eU5ETjlVRmVpN0drMkVvc3IwMnEiLCJtYWMiOiI3OTRmODhlZWY0ZTE2N2E2NGJlOTExYjM4ZDRiNzhmZmE2MjZlOTAwNmI4ZDYwMTJiYzk4ZTM2M2JlYWE1YWFjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJmOFJrbGROVFNwM0xPSnVmaXJQR2c9PSIsInZhbHVlIjoiMkJ2T2hlNjZkbTRZR3FyV0xmOXNTa0dvSjVTcUEra2g4aVp0OWkzZldsakhabUFPWTBOVENxVkdqOTRJRXcwNTZDMHo4M3ozUVhTSDNOYTdOUEVHQXJqYUNSbjUwaWVwcTRGZWx3bERNZ091bFY1anpVVkRkY1pFZVJmMXc3Y1I1ZENLdTZQMGNXR3Jtdm0xMm9GNllBRldrdEdEbk9GcExmMXZqY1EwK0JLTWczblc4ekZYV3V3eEdsaXpWOTk5Z1B0MS9DU1p1OGJDRGZzUnNobG1xNGZzZWcrMzJvUDY1bzhBaVUwaXZNTWlDZVNuU25DU29Gb0VnNGZ6dkd2emRUeDdVYTRpQk9JVDl4QUc3Z3YvOFBqVGZrM0pkRTZreTJpM1R2SkluUXUzOWJYT211N3p0a3RTVFp6Nm9qR0c5M3RoWm9KNTNvQlhBVnFxZFA2WWFndExpcjB4Z2xIMGFTdk5nWEpGbVZXSG9EQmtpU2RpRHozVlVSdWpwTDBPb0laOGlsVVlaTnZ1MVV2ZUpQYmU2MDU4ME15eGJBZzQvbURpbjV1T0RXWnNPVm5jSjgzZ0FEOHVYNlNoRGV1SHdJVFE2UzRIbWpLNzNtMnJ5KzNiUmhKM2lhbDd5MzgzNWtaclNvZnYzbHdsaHFoREs2dVVnKzlQZWRCTTB5UUQiLCJtYWMiOiI3Njc5NDc0ODNhMzUzMTQ0YTE3NGM0NjM5M2JkMjdlOGRjNjlmZjlmZDI1OWEzYTA5MmYyY2VmYzRkYmI2ZTY5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InkydVY4b3IwSXVlSnEyT21yMWRYanc9PSIsInZhbHVlIjoiVXl6RHR0K1B4aFg0N25nRjg4cDlxY0s3QjNnNytBY1ZRK1FFTXBLdVQ0NVR5R2tqSjY5ZFFhdUlYaWRqZUhRSnM5OC9jamV1c0x4UThYVkF6WFg1Si9SbmgxOEdkNS81bk9mWXdXUTBpbkNac1lsMVF5dHVMSHlnVEJHOWNlRkZNMUpNcFJyQnhjdzM0WkRwZGlqWTVtb3V1ZEM0dXdJdTdjMXl5KzNreUg0cEM0ZncvLzJNbTVwZ1ZCbjN3U3hWdDR2SElOZFpwcjNyNWRJUHNWcTlPOS96VW9CWjczTjB6cW1NZHpodzJNemhyc2VLcUlIYXhzR3hiSEc1NlEzY2FVZmcxRjFWZm1aVUdDS3Z3aHQyRG9sUXEwRDhDUHNnSDNWVG16SW83TGVSdW1qbzJhZVlyam15RjZzUlE3VGVjaUpkTXRFZjFjZ21weDZJaTlNRk9CVFMwOVg2T2RNNWkzbU04ZDk1eDVESFV3VE5UbmZkaUlwQ0QrSTJQTG5vRjAwamxoUUdlZFZzN0Z3YzlVdWZUVTdodGhLOXJhSFl5Z2F4THZhZ2JBQ1F4U1JJWHp5Q3VSUzdoNG93a3d1YW1rUXBxK2VoSG9Uc1F2S0ZVSFoxemM1SDFIcTRVSHJBZGMrS283cmdoeW81eU5ETjlVRmVpN0drMkVvc3IwMnEiLCJtYWMiOiI3OTRmODhlZWY0ZTE2N2E2NGJlOTExYjM4ZDRiNzhmZmE2MjZlOTAwNmI4ZDYwMTJiYzk4ZTM2M2JlYWE1YWFjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJmOFJrbGROVFNwM0xPSnVmaXJQR2c9PSIsInZhbHVlIjoiMkJ2T2hlNjZkbTRZR3FyV0xmOXNTa0dvSjVTcUEra2g4aVp0OWkzZldsakhabUFPWTBOVENxVkdqOTRJRXcwNTZDMHo4M3ozUVhTSDNOYTdOUEVHQXJqYUNSbjUwaWVwcTRGZWx3bERNZ091bFY1anpVVkRkY1pFZVJmMXc3Y1I1ZENLdTZQMGNXR3Jtdm0xMm9GNllBRldrdEdEbk9GcExmMXZqY1EwK0JLTWczblc4ekZYV3V3eEdsaXpWOTk5Z1B0MS9DU1p1OGJDRGZzUnNobG1xNGZzZWcrMzJvUDY1bzhBaVUwaXZNTWlDZVNuU25DU29Gb0VnNGZ6dkd2emRUeDdVYTRpQk9JVDl4QUc3Z3YvOFBqVGZrM0pkRTZreTJpM1R2SkluUXUzOWJYT211N3p0a3RTVFp6Nm9qR0c5M3RoWm9KNTNvQlhBVnFxZFA2WWFndExpcjB4Z2xIMGFTdk5nWEpGbVZXSG9EQmtpU2RpRHozVlVSdWpwTDBPb0laOGlsVVlaTnZ1MVV2ZUpQYmU2MDU4ME15eGJBZzQvbURpbjV1T0RXWnNPVm5jSjgzZ0FEOHVYNlNoRGV1SHdJVFE2UzRIbWpLNzNtMnJ5KzNiUmhKM2lhbDd5MzgzNWtaclNvZnYzbHdsaHFoREs2dVVnKzlQZWRCTTB5UUQiLCJtYWMiOiI3Njc5NDc0ODNhMzUzMTQ0YTE3NGM0NjM5M2JkMjdlOGRjNjlmZjlmZDI1OWEzYTA5MmYyY2VmYzRkYmI2ZTY5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772362691\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-877803330 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877803330\", {\"maxDepth\":0})</script>\n"}}