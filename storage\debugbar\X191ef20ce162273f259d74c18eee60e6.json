{"__meta": {"id": "X191ef20ce162273f259d74c18eee60e6", "datetime": "2025-06-28 00:37:01", "utime": **********.672802, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.257924, "end": **********.672824, "duration": 0.4148998260498047, "duration_str": "415ms", "measures": [{"label": "Booting", "start": **********.257924, "relative_start": 0, "end": **********.617817, "relative_end": **********.617817, "duration": 0.3598928451538086, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.617832, "relative_start": 0.359907865524292, "end": **********.672827, "relative_end": 3.0994415283203125e-06, "duration": 0.054995059967041016, "duration_str": "55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45612952, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026699999999999996, "accumulated_duration_str": "2.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.643811, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.801}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.653653, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.801, "width_percent": 19.476}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6590319, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.277, "width_percent": 21.723}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-12919500 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2817 characters\">laravel_session=eyJpdiI6IjE5YmxhMUw3bXJQWWh3R0habDVwcFE9PSIsInZhbHVlIjoiTkZUZHViY3FpMFMvWUgzVm1OV0JwUHZoMVU1WjA5ck5UbEh4NjBRemY0ejlwWVNadXBvOWxTWVFjQzJMNGxNSTc5NzJwa1dxQ2hqL2VlQUZvMUs4YnV3Z2RhaExoU0p1RE5pdlhQQ1dzN0RpS0xnTWxrcmEvVFZUZERCSnFDQlhEdG03UkROakJlb2huQ24xSWlPcTA0QnpwdnNZaEl5T2JnWkpNMHNSVGpUeGZpWHJNcDBBWVFmaHE4Z0dkYmFlTm5VRWR4YWlaOHNZa2hPaDh2RGdndHB2MVJLZEFZMUU4Nmttek5WOEhIOExCS0p2S3h1Mm83MFRQc1U4NHMzN2lsY1J6ZnNLdVBhOXVCbktKdkNNdUQrcGJHZm9sUDkyRWhHVW90U0twWWw3Q3d1VzlyYzgxWjY2U2MxWWdnOXhaUEJkK0k5dFU1dWEvaDNXNmZtMkY2WUJKdVZSNnlFYVF0Y0JtL3Fac0tRZ3dyWDFwVXNSdmNhNzIyVjBiSHpQbDM1OXBiL1R6WndGT25IMnFYV3ZBNkdsNjB1SU54NXRIUWJBdTM1eDBiditjVzlWcW1DQlVTMkgzSmw3S0xYdDJmRVI4MXNEaENKc1dTbFRMR1pPWUVZc1crMkY4QnNxMDl2eEpvclhUb3hhMjZqYnFqaGVGTEdOWWxSZzBXNUUiLCJtYWMiOiJhMmVhNTI4OTM2NDBkYzJlNTU0MzRmNWUxYTg5YWQ0OGIxNGMyMmZhYzM5MmYzYzllZGEyNWMxZTVjYzdhNWUyIiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=zkpa4v%7C2%7Cfx5%7C0%7C2004; _clsk=9vvwq3%7C1751071010550%7C30%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im53cTM3WjN0ZGtFV3JTak0wUEhWdHc9PSIsInZhbHVlIjoiYVduM3NlV1FYa2NhODlEK281WVd0bkJqR3BqM1NEenJKdUNFdVcrYnB4bmVzY3VSRHRxRUhzWmVUeXA4VFp0cnhuWXpPVmgxNVhJNTduYkN0b0s5R2NzUDFFdjgrRlFiU0ZzT2hKOXNUT0EzUSt3U2I4WEtadUJOOHlxRGZmc0NPcTE4Q053a3dvZ050d1I1d2ltdUUwdVJ6THhYMWVJeDg3YXFFL1dVUjhjWS9jRndqaW16TUQ1bWUyWGFobWFQa3lGTGd1VjBTb1V1YndkNjJpeDFsTHRnSkwvMHE3L0FTYjI3WWZTU1pqZlJrWHFwWXBwUTQvWDIzeHl3VmFvQ2xBaUNPSjVGMnNPSGUvR2VITWRMRjI0S0hYTVFGN3g0MXdwL1EwY0Vmb0ZETkdvTDBZMTZkSlRmUm1abi9wcFVoUXM5NWs3WGxOL0FxK056Z09jTmJYU3F6WmdKZ2x3QjJRTzRzZ2Q0QitXQ1owT3hBWFdRRTE2U1FHWEN1NzE5WXRXcFV4NVp5TkV3cVFwR3VJd1BXOEdIdXM5L2lUdXUvcXRxWCs2d2ZoczhROXBJN2cxN1gyUXdJbnAvNjNnL2NnSnB1Q3BaTGdjM0tnVmVXcVRxamZZTEhoNjkxOFMrRzhPSk9zWkJuTFlXbkVVd0NtUWF1RTgyMjl1M0JSUVkiLCJtYWMiOiI0ODIwNjhkNGUwZTJiMTZlNWZkYTUzOTIzNTllNTk2YTBkYzczYmMzODYyNDkxNTUwMjlkNjdkYjk1MWI4ZDY5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imk2TVIxMUUvd1E3ZkdoQlV6R3BMSEE9PSIsInZhbHVlIjoiczBJc05Hb3dWV3Myc0F5YlZSSjVkQ3ZkNlZDemxRNkRKVTdVait1bFdTTmdENDZPQ2F4Qng5ZGJTMFBac2xmWTE5T3RJem0xUzd2VVF2b0EyUEh6SmhjL1lucktXMmM2ZE1KeWJick9FVTRaVktLcXpaREJhTkVQVHIwUmliUytmRnpWbzVDbHhJZ1l1cXlvVEdpeVNKZ3hzQ2RPd0FHTXNtWWkzbkNLNlhBZjVGNndQTWVHTTdJMzNsSmdaUjhZQTkzc3pmck10S2dnNkxISHBPWWh6QWNUdHFTZkRiam9rYXdmejMxRGZGRFFyVmcvdXRYd1djTU0yK2VjbEVIdzlhUjFmOFlMRHo4M3ByRkZmbGdiR3MvaktFSWVVVGU1ZkFlOXVaNzhwMmpPTG1yRFRlR1dIL3VMcFV4dWtMTTRDVFBSajhlcDVwZkFPV1dzb2JzVFBwLzJ4WTJZMDdTU2FoUmhGVEhhUWlZK2xUenB3RVVTRG45WXc4T21BdWV4QlFidC9NL0RiNEhCYVorSXp5TGRMTkhZRHBaMEpoQnRoRTIzNWNUSHliamNZemZSTDBOREs1M05pZmFMMk9ldlZUR2lKd2EvSDZ5UU5ZRE9qZjdzT3hKNjU5VUN6OCtST3dINFFKK3h0Nk80b2NOTHVZVE1ObC9XVUErY002TEwiLCJtYWMiOiJmNTJmMDJmYmRjZmYzZmMyMTAxMzhhNmI0M2UwNmQ5NDgzM2UwMjYwMWQ5ZGZkNDNjYmM5MWRiYTg0YmZiNDkyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12919500\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1528156103 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">chEs2sQpmg2fYpUuqAOS1ZcIPLmVIQdx921QTw47</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9XNo0PIFsccsEXzvfQLAUsci7MaweQoaRcTvbnBl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1528156103\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-442662355 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 00:37:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNIdGIvK3IrNUMwaVdTbVdYNmNOZEE9PSIsInZhbHVlIjoiQjBKOU4wbU5hNmRoQzNvU095UEF5aU5QTlZJTnFRbnlickZpVzFvMUxIaE4zb1duUzlVTDhob0UzditLdVJZUDBxQlBWdmJvWmtpSjVrdTd4U2M5ZjRxUXBjdlNRRUhMaThaQnQ1UGJONmVpTnlHTEdqT1JaVlBSZzgyYUpyWE9pQXdvSWlhRnpsOHg3SCtPWitQNE1MamNvWnFBTG9xQis5Y0g0b3ZDN1VpSG9uWUwrVGFYVm4vQUwxWEYwczhTaXdNbVprU28rZWlJNk5RSDBaaTNIZU9PWXJxNFcxUHVQV29BeTMxVkw4bTJlVTVnNlZVWHo0QlMyTU42WTBaOEJuQ0tsRmU2eWx3eFRjTGQ5YjlhQ2hYOTNLaDViaFJ0R2plY2VuVUk4MCtlZkY0RVpMVFVKL2I4T3d1NkM0RnlPNE14UWtrWjZQU2J2MVYwNWhqSzRESGdxTTRnNmZrUGtXUDZMMlU5S1ltU25YNlExalRpSWdqeHhGajZndVlPVDZDWUFNTk5ZQW9qTnBLb05GWklZTmVLSVhlTFFoVjZ3dFBrVHFGMnFnNmlCRE1XdklqeXdHK09pUlhhSytPS1pPZnQ2MTRuaU0rQ3laemZBaDhUckV1enZwNUprSHRmWGpHcG5vQTZYbDFCZWo2N2lIa2dSRDBmMCtQclFrWXQiLCJtYWMiOiI2MTFlZTg4NzM0NWIyYzdmMWEwMzQ3NjdlOTFjMGVkZmE2MzgxZDI1MzBlY2QxMjRkMjQ0NzAyNTIyZjQ3MTI0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:37:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZpRFhwUGpqYU1EbnlLaUxUNkxaZWc9PSIsInZhbHVlIjoiMU1wL0JuOGZGYjZNTTNZU1VuaHNWQjBYZE1hRHhRYnI4aW9EaitsdnZLcExOUTk5V3RUSEtzMUZ2eHV5bmg0ZWV3SUdFZDlGcmxTd25SYTlLRCt4eDdXSXF4Q3RKMkErZ1BNb1ZQK0xVcWR4TW1XNlFRdkF1R01XSkgzZ3o2UVJ4bHN1cG9INGkxVm1aZ0RhQ0RRV3lyNzNHRGo2dksybmlHc0ExS1Ivb05WSldVSW9DMms5NE9oZGVoY01TNy9mbFhyakdnVHU3NG14YWtZYVBOeCtTMVk3dlRHUitnYzlIZDJPWHFlVTltN05idnJRNFI4dUpFWmNlMzhJUi9SbVFUNEpZRms3Yjd4VGZRRE80UTh2dkc2Qkh3MUNla0hJaTliVWsvaEhldGJ1Zm5vMnVGTU1oTC9MRFRKNTdZTkhWQnVKNERzTVRmOHA3eW9IL3BDNnIwclNYM3FYV2pvcDNTOEpHVmlFVGN0NmtNL1VPQlhpQWh0R1hPcmdJeWN5bCs0UXdNSWlCTnJsT1czYno2VkFqS2VLRzN0WDd5aUZkYWN4SmhYM2NvZzJqUmFXa2VZUk8vWUxPSGhhWjF0NC9rYXlrZ0lxQ05kbTY1WnFJc2tmRDFUdDdjVVpyMW9ld1F1MFl2dzZVOWptdXFpOVNESFQzeWkwRDMvTWxSL04iLCJtYWMiOiI5ZTg1M2RhZjNkZDA5MmVjZDcwODAwODRmMGU5NGQwOGI5ZmI1NzE1MDM5MWUzNTMwMTk2ZmZlN2Y4MDhjZDRiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:37:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNIdGIvK3IrNUMwaVdTbVdYNmNOZEE9PSIsInZhbHVlIjoiQjBKOU4wbU5hNmRoQzNvU095UEF5aU5QTlZJTnFRbnlickZpVzFvMUxIaE4zb1duUzlVTDhob0UzditLdVJZUDBxQlBWdmJvWmtpSjVrdTd4U2M5ZjRxUXBjdlNRRUhMaThaQnQ1UGJONmVpTnlHTEdqT1JaVlBSZzgyYUpyWE9pQXdvSWlhRnpsOHg3SCtPWitQNE1MamNvWnFBTG9xQis5Y0g0b3ZDN1VpSG9uWUwrVGFYVm4vQUwxWEYwczhTaXdNbVprU28rZWlJNk5RSDBaaTNIZU9PWXJxNFcxUHVQV29BeTMxVkw4bTJlVTVnNlZVWHo0QlMyTU42WTBaOEJuQ0tsRmU2eWx3eFRjTGQ5YjlhQ2hYOTNLaDViaFJ0R2plY2VuVUk4MCtlZkY0RVpMVFVKL2I4T3d1NkM0RnlPNE14UWtrWjZQU2J2MVYwNWhqSzRESGdxTTRnNmZrUGtXUDZMMlU5S1ltU25YNlExalRpSWdqeHhGajZndVlPVDZDWUFNTk5ZQW9qTnBLb05GWklZTmVLSVhlTFFoVjZ3dFBrVHFGMnFnNmlCRE1XdklqeXdHK09pUlhhSytPS1pPZnQ2MTRuaU0rQ3laemZBaDhUckV1enZwNUprSHRmWGpHcG5vQTZYbDFCZWo2N2lIa2dSRDBmMCtQclFrWXQiLCJtYWMiOiI2MTFlZTg4NzM0NWIyYzdmMWEwMzQ3NjdlOTFjMGVkZmE2MzgxZDI1MzBlY2QxMjRkMjQ0NzAyNTIyZjQ3MTI0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:37:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZpRFhwUGpqYU1EbnlLaUxUNkxaZWc9PSIsInZhbHVlIjoiMU1wL0JuOGZGYjZNTTNZU1VuaHNWQjBYZE1hRHhRYnI4aW9EaitsdnZLcExOUTk5V3RUSEtzMUZ2eHV5bmg0ZWV3SUdFZDlGcmxTd25SYTlLRCt4eDdXSXF4Q3RKMkErZ1BNb1ZQK0xVcWR4TW1XNlFRdkF1R01XSkgzZ3o2UVJ4bHN1cG9INGkxVm1aZ0RhQ0RRV3lyNzNHRGo2dksybmlHc0ExS1Ivb05WSldVSW9DMms5NE9oZGVoY01TNy9mbFhyakdnVHU3NG14YWtZYVBOeCtTMVk3dlRHUitnYzlIZDJPWHFlVTltN05idnJRNFI4dUpFWmNlMzhJUi9SbVFUNEpZRms3Yjd4VGZRRE80UTh2dkc2Qkh3MUNla0hJaTliVWsvaEhldGJ1Zm5vMnVGTU1oTC9MRFRKNTdZTkhWQnVKNERzTVRmOHA3eW9IL3BDNnIwclNYM3FYV2pvcDNTOEpHVmlFVGN0NmtNL1VPQlhpQWh0R1hPcmdJeWN5bCs0UXdNSWlCTnJsT1czYno2VkFqS2VLRzN0WDd5aUZkYWN4SmhYM2NvZzJqUmFXa2VZUk8vWUxPSGhhWjF0NC9rYXlrZ0lxQ05kbTY1WnFJc2tmRDFUdDdjVVpyMW9ld1F1MFl2dzZVOWptdXFpOVNESFQzeWkwRDMvTWxSL04iLCJtYWMiOiI5ZTg1M2RhZjNkZDA5MmVjZDcwODAwODRmMGU5NGQwOGI5ZmI1NzE1MDM5MWUzNTMwMTk2ZmZlN2Y4MDhjZDRiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:37:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-442662355\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}