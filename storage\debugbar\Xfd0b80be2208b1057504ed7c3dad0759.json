{"__meta": {"id": "Xfd0b80be2208b1057504ed7c3dad0759", "datetime": "2025-06-28 15:02:51", "utime": **********.801459, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.325766, "end": **********.80148, "duration": 0.47571396827697754, "duration_str": "476ms", "measures": [{"label": "Booting", "start": **********.325766, "relative_start": 0, "end": **********.724923, "relative_end": **********.724923, "duration": 0.3991568088531494, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.724935, "relative_start": 0.3991689682006836, "end": **********.801482, "relative_end": 1.9073486328125e-06, "duration": 0.07654690742492676, "duration_str": "76.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46176232, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.008289999999999999, "accumulated_duration_str": "8.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.767932, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.919}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7781458, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.919, "width_percent": 5.187}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.781361, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 28.106, "width_percent": 9.65}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.783763, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 37.756, "width_percent": 48.975}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.790155, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 86.731, "width_percent": 4.946}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.791914, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 91.677, "width_percent": 8.323}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-1813910977 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-1813910977\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1894519575 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894519575\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1166771291 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1166771291\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1942473368 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122962998%7C6%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxEQjNvai9FRlNMcUgxbDJ6RjdNZmc9PSIsInZhbHVlIjoic1RRcS9rMVZhTzNCMTI4cEdXOFRHeW91dVoweG9FaTkyVlBXUUxqczFqMktLd2VKeWNSdlQxSzNTT2gzOFVwbGFBd0djWHRWVEdoRjdyTmh5ZnMzV2ZIK3FNRi9YVW5PdkF4L0IrS0NnWUtNdy82OFVIYVJpZGU2TUt6OHF4VmRwRERQdDdKc2lrbDZKZEZlQnl1WjYrekNpTFV2Y3RqUmN2akxMR0FvUkZ5cnE0TXY3RHUyZXI2QjVyNXBSeDRaMVFsS3FQQVNwTmpRYjJ5Y3JVT0REZExMZDNmMDJnMXJYV01FcjBsMi9qcWRFeW5MQXA4TXRCZytTcUs0cmNCYUU2bVoyTWpvS0lLdnFQUHg3T00vZnZVTGJma2lKSVFIREI0bE5mSXdhQ1F3RElXMEIvMlhhV1lMNjAwOXJya0R5djF4Rnc4VjFRTUFrUnZvV0U0VTcxUmZUTWNsSnkyV3RXZ2tQTldGUUFnVG9CbGNReFNpbXdtaGIwbkhrK3hsVmtieGJXTFhBOVFtT2ZtVk5UdTU5UmgvV2paSjlpZ2dJb2Jqa0JCNXBOSHRZY0ttaGs4OUVYbnZGeGhWMy9Db3dWZEtMS2tKWmFaOFRCMHRHd1RvR2hhWmtFM0xoeHJQa2dBVXgvOVR2REJXUzU4bTBGWnRkZTZESjdVNTBqT1IiLCJtYWMiOiJkNjA0MjhjNWYxYWY2MjRiOGRkNTVjOGY2MzIwZGE2ZTgxOWU0N2M0ZDFlZjBmMGU4MzMxNjQ5MzViYzRmMDk4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Inh0RzNXcjRIZHprRTRPZ0JvMWdjaHc9PSIsInZhbHVlIjoiY3FvUSs1Y0RaU1dTeGRVTDRXbTRNNWZOdWh2WUcvME4yWDdKU252VUIybXBZZUFjQVBsTzFUWVQyZk8wd2txNVFSQkFxM3BiTElSNFpMcnRBNzI1TDFHQm4yYms2VDRtOXNyV3g0UEEwSFpjbU8wbFBYN29DQTBubVhXaVhWWkxGYSt5cVdSUHFRTVhzS3BQNzFUZUU4QUFwb1Z2VUd2UER3NndVc2NpRDZ4cFgzdGlBaXRjRGxQeThpYXJ3aDNYOXVxWEZIY0ZyZVUrWUlQVUw1bUxHbS9vWHFIUk83TWlrN2ExTDNMVXBDMzhndStwOXJwcVBDZmRmcGhYSXBwMTl0ZmdXbXF5U2Rib3I5UjVjMVAxSDJGUUpWU0FXcDJXNmpxYjRuOEdwOUpBV1hIMk5aVVIxZUU5TktJRFFzbXhFMGF1SlFVb3gwV0FBZStXY3BSTGZEREk5Y3JRNmY5c0lpcUpLOXdmcW16Z0hyejE1a1YwSVllVjR4UkNYT0EwQys2NVIwWERHZmlKSHl1eU5uaGZLWUorY2pQV2pNWEFKODI2cmdlejQybUw0Y3BMbmtaVlZNS0VGUU82Z1huZC9hTUl2NHBNaUFzK2VpY2d2Zno0dmovSlo0eXZhRGl6aHdHUVhWcFM4TjdrVjZ2ZGpZRDVtMGF6NTV3Mk84TmQiLCJtYWMiOiJhZTg3YzNkOGI3ODc1Y2YzY2Y1M2FkMGE1NmNjOWQwNTc1ZGZmN2U5MzlmODdjMTliM2U0NTI3OTcyY2ZjMjVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1942473368\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-28850543 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28850543\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-859029283 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:02:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZUdFkrZ29MTm55NS9Cem95ZjFMWkE9PSIsInZhbHVlIjoiQWd1dU0yK3hqZVYvdjVRcnkxTmhFZno2Y1orNU9YTkpmRldTaGtzYzBvOUZZVitWM3UvZld3R2pGMlRVeGx3Mnk4a29MeXk4MG1wNEpUa2l6UEpYQXlacEovNUpUREdGL2VES3ZSclo0TTJsK1V3N1VlOHpFMWoxL080Q1JjRUljcEN0Nm84bytGU05OcXNzYk9HY3hsM05QVm4zUzZTdkd3S1FRK1VHcmlpYVh6VUlMQzkxTTAyWnluVzM5K3NKdXZZZVVYeWdmc01aekxJOWd1Ym90Y0F3TGhYeTAzMEFHMk1zWTdab0V5RUxWbklVUytOd0R2aDY0ZWYzbjE4cUZ4LyszTHVOdHRwazc1Y2xxaHFreWo3WmkxTjNHMlNjVlE2TmZVenc2Z1MwS2lwOElEblNnUWtLM3pycTJrSDY0MEdsclNMV25SbWpxS1NwWUVqTjB0L3BmTTM1K0J6VHQvRENhU2Mvb0tXWWxjekZNekFTemx2Mi9kb3F4M3BDWDRXR0lnTGRGSURxek1RS0ovTWk5QjQxTk1DNWk4c0tOS1NxWTUwY0ZGL3pQWlR4azFrcjlHNE5iSC96cDNJTkdCU21JS2ttKzZ2OGxkdE9mc2xFblVWejFyTU1PZzdKellhUGNWV1h5cGwrb3IxQ0pzYm5GZ21oRG5IRDY5UGMiLCJtYWMiOiJhOTMyYTRkOWEyMzgzZTdjZmM3YzRlNzhlMDZiNWUyYzE2OTNkNmRhMTZkMzQ0NTEzMjNjYTc3M2EyMTE4ZDQyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:02:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpVRTBsb3RwVzIrYU42WVk2MFFnVkE9PSIsInZhbHVlIjoiVDQxMTZFRWZyUUNiQ1NDZ0Z2L3FsdDQwS0E0ZlJlalBGZlJXOGo1c0UySGRPeHhuZ1pKVnpTOURGSFVaK3J3QzNtOEM0RjZNWG9WVXpsM0w2MTVmK0RFczZkOHVtRStBMzRRUG02Q3F0MEJFeStKQ1J3eDVRTmRZV1cyWEZrbTNpQURZSmdBdDRQRGQrTWxGNllaV1lOa2tzWVBPRW9MQTNwOVJ6Y0s3Y3d5dHl1bGQ0b0VyK2dnandOTWRDOHhEYWlYdUV5ZkJxTHdzYzRpQWtSREpSZlQyckF0NUg4dTJXOGRiNzNSVkRzSVFMVlQ4YTFKWnc0aWVDd3dGUVNIQldrM3BDWHFWTkZaNXNLM2FyaGE3d3VzdEF2Nk4rMDV0bGhXVEJCN1RSZ0NjV0FIQ1diTlVyQndtblplU3kraGtXV3ZRZnE4bnpMYTV0dUtjOXBKVCsvajdob0o3c3JuM0hZTEdqN0lOUGVma2RQY2FCUG1tZFJaUkErR1BITDdoYWp6RzdqdFpsbWhzZVZUVjZXZmdCRS9nSGx3djhuOTNYZHVpUk9iRVEwSHd3MUx1bDMwYlRSWlNRZnVETDRXUUNodVJRWTVEVEJFaEZFM0M2VjBxZXdIZGtLeW5XTG8wd3B5amxwWHJzdDd4UUFrR3VEb2U3S1VKdVVteGRzbWwiLCJtYWMiOiI0ZWY0NTZkMDRkZjg0NjllMmY1NDM2ZjdjMjMzNDdiYWMyMWE3MDdjZDBmZGQwODY2NjBlN2UzN2RhMWRlYWI1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:02:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZUdFkrZ29MTm55NS9Cem95ZjFMWkE9PSIsInZhbHVlIjoiQWd1dU0yK3hqZVYvdjVRcnkxTmhFZno2Y1orNU9YTkpmRldTaGtzYzBvOUZZVitWM3UvZld3R2pGMlRVeGx3Mnk4a29MeXk4MG1wNEpUa2l6UEpYQXlacEovNUpUREdGL2VES3ZSclo0TTJsK1V3N1VlOHpFMWoxL080Q1JjRUljcEN0Nm84bytGU05OcXNzYk9HY3hsM05QVm4zUzZTdkd3S1FRK1VHcmlpYVh6VUlMQzkxTTAyWnluVzM5K3NKdXZZZVVYeWdmc01aekxJOWd1Ym90Y0F3TGhYeTAzMEFHMk1zWTdab0V5RUxWbklVUytOd0R2aDY0ZWYzbjE4cUZ4LyszTHVOdHRwazc1Y2xxaHFreWo3WmkxTjNHMlNjVlE2TmZVenc2Z1MwS2lwOElEblNnUWtLM3pycTJrSDY0MEdsclNMV25SbWpxS1NwWUVqTjB0L3BmTTM1K0J6VHQvRENhU2Mvb0tXWWxjekZNekFTemx2Mi9kb3F4M3BDWDRXR0lnTGRGSURxek1RS0ovTWk5QjQxTk1DNWk4c0tOS1NxWTUwY0ZGL3pQWlR4azFrcjlHNE5iSC96cDNJTkdCU21JS2ttKzZ2OGxkdE9mc2xFblVWejFyTU1PZzdKellhUGNWV1h5cGwrb3IxQ0pzYm5GZ21oRG5IRDY5UGMiLCJtYWMiOiJhOTMyYTRkOWEyMzgzZTdjZmM3YzRlNzhlMDZiNWUyYzE2OTNkNmRhMTZkMzQ0NTEzMjNjYTc3M2EyMTE4ZDQyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:02:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpVRTBsb3RwVzIrYU42WVk2MFFnVkE9PSIsInZhbHVlIjoiVDQxMTZFRWZyUUNiQ1NDZ0Z2L3FsdDQwS0E0ZlJlalBGZlJXOGo1c0UySGRPeHhuZ1pKVnpTOURGSFVaK3J3QzNtOEM0RjZNWG9WVXpsM0w2MTVmK0RFczZkOHVtRStBMzRRUG02Q3F0MEJFeStKQ1J3eDVRTmRZV1cyWEZrbTNpQURZSmdBdDRQRGQrTWxGNllaV1lOa2tzWVBPRW9MQTNwOVJ6Y0s3Y3d5dHl1bGQ0b0VyK2dnandOTWRDOHhEYWlYdUV5ZkJxTHdzYzRpQWtSREpSZlQyckF0NUg4dTJXOGRiNzNSVkRzSVFMVlQ4YTFKWnc0aWVDd3dGUVNIQldrM3BDWHFWTkZaNXNLM2FyaGE3d3VzdEF2Nk4rMDV0bGhXVEJCN1RSZ0NjV0FIQ1diTlVyQndtblplU3kraGtXV3ZRZnE4bnpMYTV0dUtjOXBKVCsvajdob0o3c3JuM0hZTEdqN0lOUGVma2RQY2FCUG1tZFJaUkErR1BITDdoYWp6RzdqdFpsbWhzZVZUVjZXZmdCRS9nSGx3djhuOTNYZHVpUk9iRVEwSHd3MUx1bDMwYlRSWlNRZnVETDRXUUNodVJRWTVEVEJFaEZFM0M2VjBxZXdIZGtLeW5XTG8wd3B5amxwWHJzdDd4UUFrR3VEb2U3S1VKdVVteGRzbWwiLCJtYWMiOiI0ZWY0NTZkMDRkZjg0NjllMmY1NDM2ZjdjMjMzNDdiYWMyMWE3MDdjZDBmZGQwODY2NjBlN2UzN2RhMWRlYWI1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:02:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859029283\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1866301818 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866301818\", {\"maxDepth\":0})</script>\n"}}