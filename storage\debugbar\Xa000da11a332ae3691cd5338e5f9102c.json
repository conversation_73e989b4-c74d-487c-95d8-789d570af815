{"__meta": {"id": "Xa000da11a332ae3691cd5338e5f9102c", "datetime": "2025-06-28 15:08:28", "utime": **********.237623, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123307.825309, "end": **********.237638, "duration": 0.41232895851135254, "duration_str": "412ms", "measures": [{"label": "Booting", "start": 1751123307.825309, "relative_start": 0, "end": **********.185864, "relative_end": **********.185864, "duration": 0.36055493354797363, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.185872, "relative_start": 0.3605630397796631, "end": **********.23764, "relative_end": 1.9073486328125e-06, "duration": 0.051767826080322266, "duration_str": "51.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45594256, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00236, "accumulated_duration_str": "2.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.213368, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.915}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2235188, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.915, "width_percent": 17.373}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.229031, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.288, "width_percent": 12.712}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1757262712 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1757262712\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-251084529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-251084529\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1173962395 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173962395\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123286871%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNqZ1ZTcUVPUXdDVHhMdCtLb0U0T0E9PSIsInZhbHVlIjoiVFM1cTFxVVlCZ1JKVjRzYVZVaEF2WDMyRG9qYlRyQ1daUjVDN0VncjJTV3djTGNESkNnV3A2S1I5R09mUTRIRHNTMDlWMFJiMkI4VW95MHF0Z2l0OGt3ZTk5SXl3WGRUck5SRWVEZWVUdnY0c1QrOVVTcDRXNTJpTm5mbVp1SEEwVXhIOGV0dzNQSWx6NU5WWkNtNkhDMCtNT2g0SmkrOS9nVXpXcVAxZkxJdVVuM2lHVDV1UUVUOU4xbDhpTk5INHFSYWRxZjIwd3FCT1dJbEI4TG40bXRVK2hJVXF2TTNXOGdJTFhCOVYwb1dTNzhqY1BMbFRYYzZNbVpsZmZ1ZkNLbzV1T2FKZjhzTE9rZjlpYUdlNXY2alVwUTE4TjNSa3hhMlZNZElkMUgvaXU3VVpYMEEzdzhUK25Jd0drajh6YS9wVytQd0NXNERNeVJhNCszbXlJS2NIT1F4d0hRQkRaNW9FR1pBeXFOM3NNRHVCRW9JR25WTnk4MHB4OVVsdXFrdWs1bENLaHl3UGduUDVvdTVJLzNPNm5WRVFvaCtwMVBnSnIyVG1EdkNFM2pCV1JmQUVxQjVsamc3UTZHZTdQMlhzSEszT0dCRGJxclNtSkxtTnBiK1paaXMyVUFPVzk5NFZGNEFoNzhYelVuTVZiMmJuYnlQT1k2OEN1RWEiLCJtYWMiOiIxMzI5MDlkYWRmZDcyZjQ0Yjg5MzI5MWI4OTdhN2Y4M2Q4ZGUxMmY3M2ExNGFhMDU4NmZhMWVjMjFkNmViMzIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImRucFJWREFBWHpxOTcxcnAwZG41Rmc9PSIsInZhbHVlIjoickZ6WVZRRXp5MnlNZlVQaytLWkdTTVVXRE1IWDVTemVvdktFa0lsTHRjekhqenNBa0FPSGg3aWd1dHJaNE9QOE1kZ2JHUkk4ejV5T1FjK0F3TzM0TjhVSnM0eGJJd29NSk9SRW43RTg4ZVRlellZM0xtNWhOVThRbHlOWXVubGcyZHNwckpwWWVtdzZhaXV5M01JTzVKWXY2UXd6MVhEbkQzY0dxZU1jd3VMRHdNeGVId25VOEV6aTU0c2dpTmhkWE5NSlU0TVVzcGdEd29RVExJWkRiUTJNWGl4MnhNWWp2MjJxQi9mOXRsR0R4ZG1zRlFIejh4cjZTSFFyNTlPUHJYUHdNRE1pREIwQUJFcXlQU05IQTRxRmlKYWNhS1hFb2ZhcjV0bEdtbmVoNnJQbXFuZWxMZmhqZjJrUjhQb3ZLcUxEbENLdlduTS9CZkFtS3Fmd2NYSHo0bUpRcVdKSDNTVTRkc1QraUcrNWtqVlEzaVJ3M1Q1MEdvd1lJRmhKaWdacS82K3F0bFlSQjEzMlJMWWlSMmx4SkVydzdBSC9wek00amM3Y0ZLb0RpbStpaWFkZmNxdzNpM1pvWGhtaUNCLzNzSVliS01laVBLdnQ2VE1GSW03RVFWNGlXaTNZTFM0RnJJVTA0azFyZDBXRkE4ZThzS1Viam5IaW5mRzkiLCJtYWMiOiI5MDRiY2YzMzVlZDc3ZjZlZDVjNWI1YTc4MjM3MGI4YmY0YjEyNzY0MTk5Y2E3NDBlM2Q0OTZlODM1ODM1M2IxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRMMVE5N1lhSjI1blVndTA1UHJQNEE9PSIsInZhbHVlIjoiU05YUFVRaGl4ZnpURzQ4ZnhZaDdLSmlkZmpaMjhyVnE0aTdYTkZHSURxbURaUGFsQTE2RUZ2R2ptejVIYWVRZDJlNDlMMitjRzhuNk54ZkpHMVQxSW1uaVZSTlNOTUFiL0lPQ0V0bkdpaEg5N3NIdHFkTUJOODFJTEdGQUhhcE1FdWxiYlJCcHhhcTE0U2huaE14aHoyakhjK1NJT1FGTkhxa016VStUVVdFTlN1cmd1WHVCeTZPQ3BUcVdBWm1XWklMQytBQ0liK0RQTjhPZkNVVWZRaU1TT2xoL3dzY0FQNS9ETTB3Nm1vVVcxQXY1eXVsd2NzUW8xR05JVGx2cEdwMmZjMk1xeStpeWltbFdLd1RKT1FDMjVLYTR3MEViYVFKK1oxVWFsTUwxVTRSUHB2bTNGV2pKNG50WmlKNlVEUU1qd0w3d1JLRlB2YkMxUG05aGovMjk5bzYraEkxOE5EZ2JFUmNRSW9VYjRqcEJKVEZaUXF4ZklzUSs1NXZ0R0Q2ckJpRnl4bVJGWUl5QU8rUW5ZN2JVaEN0UmNRNTFOVVpwM3Z5NkMwWENUd3F6ZUdpbnBjM1huWXArN01YYjVHSXZWMDNjTy9kUXhXTTRuTnFpdHc2Sm41SEFCQXhvWlpndkM3VGtpaEhqYjJQanFxYnRaMDN0dnhxUWJSQ2oiLCJtYWMiOiIxYzVkMjkzNWNkZWQ5YTU0Y2RlMjNjMWIyNWFhMWI1M2FiMWJkYTY2MWEzOGNlZWFmM2ZlNDRiNGU5YTNkODAwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlB1MTdDZDVBRzgzdU9TTkg1S1hueXc9PSIsInZhbHVlIjoic1NHSU0yZ2FRRHI0cTNwN1hPSTF0Qm9YcExNelpJQytWV1owZFU0R2NXMit5RG9nUG9vd2lxdFB2WllDV1pYQVRKeWorcFhmL0pNazAxK0pmRWxNZTJmcmFEOTYvVjhLY20zUDlNRjA5U3FwNGF4c2IwMmMycGh5c0UvQnQzUzl0dHlQN3hlQVFWQ09BTk5VN1Nma1hpZFNMck9DZU1sRU41a0k2dTNNNFpURnp0Q0MyMGc4VDBITzhVWHQrSmx1RVJucnpxcnBQTzNtU0RXSmxSMVgyZ25ISzA0cHpRK0pLN01sUHRkRjV3NTRzNDlKRkd6M0VteGRKWnVtdTF1MnhGMlVwbXNXZGdqaFhDUEVJWDl0MTRMU3FJRjg4c295Y1dJSDR0TTlSN2dmSENXU09hUVBSakhvWEwweWFSZHRaUExIZDlkdUk1V2ppdHdVd3VlbVRiL3BOK2NPcnkwSy8vQk94VUpoZzIrNGR4dFpLRDFEU2UxYTdkcFlZcVJKMmNTbXJYb2dDMlM1dVFlc29iRHY0TnpDeHJYcGYxNTFjbS90bzcwSytQRDR0UFh6YUcyK0RVNWNJYlppVmhGV3J2V1FQNlZzYnBGeVZKTTZTajBRZTN2TlEwTFRIc04wVXhTZWNZVGJJOGIrV29hMkFPck1GOThWS2FBU3B0Q2wiLCJtYWMiOiJiZDhhYmY4Zjk1YmUxNWJjZmNkOGMwYzcwYzZmMjFlZGU1ZjdkN2YzYmJhMzIyOGM4MDRhZjlhN2EyMTBlNDEwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRMMVE5N1lhSjI1blVndTA1UHJQNEE9PSIsInZhbHVlIjoiU05YUFVRaGl4ZnpURzQ4ZnhZaDdLSmlkZmpaMjhyVnE0aTdYTkZHSURxbURaUGFsQTE2RUZ2R2ptejVIYWVRZDJlNDlMMitjRzhuNk54ZkpHMVQxSW1uaVZSTlNOTUFiL0lPQ0V0bkdpaEg5N3NIdHFkTUJOODFJTEdGQUhhcE1FdWxiYlJCcHhhcTE0U2huaE14aHoyakhjK1NJT1FGTkhxa016VStUVVdFTlN1cmd1WHVCeTZPQ3BUcVdBWm1XWklMQytBQ0liK0RQTjhPZkNVVWZRaU1TT2xoL3dzY0FQNS9ETTB3Nm1vVVcxQXY1eXVsd2NzUW8xR05JVGx2cEdwMmZjMk1xeStpeWltbFdLd1RKT1FDMjVLYTR3MEViYVFKK1oxVWFsTUwxVTRSUHB2bTNGV2pKNG50WmlKNlVEUU1qd0w3d1JLRlB2YkMxUG05aGovMjk5bzYraEkxOE5EZ2JFUmNRSW9VYjRqcEJKVEZaUXF4ZklzUSs1NXZ0R0Q2ckJpRnl4bVJGWUl5QU8rUW5ZN2JVaEN0UmNRNTFOVVpwM3Z5NkMwWENUd3F6ZUdpbnBjM1huWXArN01YYjVHSXZWMDNjTy9kUXhXTTRuTnFpdHc2Sm41SEFCQXhvWlpndkM3VGtpaEhqYjJQanFxYnRaMDN0dnhxUWJSQ2oiLCJtYWMiOiIxYzVkMjkzNWNkZWQ5YTU0Y2RlMjNjMWIyNWFhMWI1M2FiMWJkYTY2MWEzOGNlZWFmM2ZlNDRiNGU5YTNkODAwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlB1MTdDZDVBRzgzdU9TTkg1S1hueXc9PSIsInZhbHVlIjoic1NHSU0yZ2FRRHI0cTNwN1hPSTF0Qm9YcExNelpJQytWV1owZFU0R2NXMit5RG9nUG9vd2lxdFB2WllDV1pYQVRKeWorcFhmL0pNazAxK0pmRWxNZTJmcmFEOTYvVjhLY20zUDlNRjA5U3FwNGF4c2IwMmMycGh5c0UvQnQzUzl0dHlQN3hlQVFWQ09BTk5VN1Nma1hpZFNMck9DZU1sRU41a0k2dTNNNFpURnp0Q0MyMGc4VDBITzhVWHQrSmx1RVJucnpxcnBQTzNtU0RXSmxSMVgyZ25ISzA0cHpRK0pLN01sUHRkRjV3NTRzNDlKRkd6M0VteGRKWnVtdTF1MnhGMlVwbXNXZGdqaFhDUEVJWDl0MTRMU3FJRjg4c295Y1dJSDR0TTlSN2dmSENXU09hUVBSakhvWEwweWFSZHRaUExIZDlkdUk1V2ppdHdVd3VlbVRiL3BOK2NPcnkwSy8vQk94VUpoZzIrNGR4dFpLRDFEU2UxYTdkcFlZcVJKMmNTbXJYb2dDMlM1dVFlc29iRHY0TnpDeHJYcGYxNTFjbS90bzcwSytQRDR0UFh6YUcyK0RVNWNJYlppVmhGV3J2V1FQNlZzYnBGeVZKTTZTajBRZTN2TlEwTFRIc04wVXhTZWNZVGJJOGIrV29hMkFPck1GOThWS2FBU3B0Q2wiLCJtYWMiOiJiZDhhYmY4Zjk1YmUxNWJjZmNkOGMwYzcwYzZmMjFlZGU1ZjdkN2YzYmJhMzIyOGM4MDRhZjlhN2EyMTBlNDEwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-650132859 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-650132859\", {\"maxDepth\":0})</script>\n"}}