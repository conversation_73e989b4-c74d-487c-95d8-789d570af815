{"__meta": {"id": "Xe61eceabf5fb288689b2e093a2acb9d0", "datetime": "2025-06-28 16:01:05", "utime": **********.625262, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.114149, "end": **********.625281, "duration": 0.511132001876831, "duration_str": "511ms", "measures": [{"label": "Booting", "start": **********.114149, "relative_start": 0, "end": **********.561481, "relative_end": **********.561481, "duration": 0.44733190536499023, "duration_str": "447ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.561491, "relative_start": 0.4473419189453125, "end": **********.625283, "relative_end": 1.9073486328125e-06, "duration": 0.06379199028015137, "duration_str": "63.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45697520, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00313, "accumulated_duration_str": "3.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5932002, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.425}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.604298, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.425, "width_percent": 21.086}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.610272, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.511, "width_percent": 19.489}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-665269155 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-665269155\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1117447352 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1117447352\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1759644522 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1759644522\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1996447153 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126454152%7C5%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjQ3VHNlYllvK2FBc25zdnJWajJuQ0E9PSIsInZhbHVlIjoiOXFDaWF5UzRuZFNYWFZ1T0xoZHpQdHkyYmxnTE94ME1xNlpIeXh3R1VJRUNGOHFPdTlpWUtSNmRuK1IrRnBEWW9MZGFRUGlMU29LOXlsU3o1TklhZVVWbENCZmVpNkhKdFBsakFuN3dkWjVicG1XZ2g4L1hucXhoM0hlY3Z1RmxpMXVLZ1oxUkdOZWRGRHVVekFXcTJPeDEwZ0QrRStBQ0tpUVZqZWkxV3I1OEJ1VmljZmthNGU0OGFneFBTQWFUV0xMaUljcnBMZ08wa28xNWxDRWJjaHdNWFErQndlOG8yamVKSU5pREo2OHU5Vm9TUDZKOGZsOFJITWhVQkF1VXYyWkNZZEV2am9DaXNaa2pHTXFmeldWK0xHRDBaemF6WnZoSTBDZzMya2sxbEU1NDNRb0pPb0MybndzSWNMWkJsWUduMGhlVUxQSGxDeXoxSDh4SVQzSndwYkZabU90SjZCWjJocGE4aEdtKzNBNjhCektsN0dhSTEvWUMzb3lIZ0pUOTFGZmdnaUs5RmdnMDBjYmlOZmZzTFN0Qnc1RTNJZTRQQjJzWTNaNVlzWHh5L1BVeURJdU80L2pNYkx5TytHbkp6OUcxY1htaWI1bC95bFVqVHJacnBCQjd2TGJwT05UMVBXQkU5ZElRY1hLRGdBQ3N5SVhDb0NsL0pMMXEiLCJtYWMiOiJkMWY0OTViM2IyMmI1OWEwM2QxZmJkNzY1NDAxYzQyOTdkYjA3MzQ1ODBlODQ1OTBmMWRmMzA2ODY4YjA1NzkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlibVgxc0ZUdGNqeUgrUklONjVJaUE9PSIsInZhbHVlIjoiSHNJSy9pUzB5M3FMWEFrSFU1Q3V3YnlPRmNSRDJPR0hqaFJVU2dkL2hrRTBKU0gvR0xVWmN1T3NYZHlZWjE2OWtqYU55S1EyR09YK29xd1k4NjAvdFdrWkMzWTN5LzZodEtBa0Riamoybm8vMjUwZFFXSUNERGxPeUF4VEdQTzVXc0drWXBZME1lOTlLd3RnZ0k4M205Wjc0UkswUWZkWXNpbHpZQzhINWZrZXE5cDFoVlNleVhVM001N0oxZ0gxRTZlQndnSmVCOTlWMjRMaHhnQlVDUnNLT1RNNzJpSWxBTVBDZDFObXdQWS90QUlTV3pEY0VnSW5HYUdOYnMwamZ1YklqaTNhMWpWbkU1VC9HNlMydjBmS0pSbTR6djdvYmdmUTg5amptVUlVYk9aOFdZY2dDOXcyOExxeGpVQy9mUzl4OGNwK2xmOTliOTBlNmhJekFCZkpJZUJFNVQyQ2hDZGc4TUZIVmRKK2oyYVFXMnBBbTFyY21pWU1BYjBtdDAvakVMUFViYWEwTzlHK3N4Rmp2M0J6ZVg5ekQ4MDZHRXhVbzdORnZSamJqd3ZxRXp2b2oyYWkwK0hZbEsxWmhUU1VUbUR1U253Qk45OFFwRVlvSlZZK1VrMTdRNmVFTUhCaGlGYkxaUjljR2NvejRqU2k1anNHYU1jc21BanQiLCJtYWMiOiJhNTY3ZDI5NWZhNDZkZDlhNGNmZjEzNmIxN2I0ZTU3YWNjYzg3Zjk1YjJiODI2NzI5MWYxZGJjNmRhZWQyMDk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996447153\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1703808771 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703808771\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1014571163 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZCSk5jbWxZOVdITUxZeDFPU2lSQlE9PSIsInZhbHVlIjoiYkZSd2FXcURrVHdNaVFaRXd6MXJYR0hxMzRWY1N1akd3eW5EUzhFUytoTnFjQ0hVU2l0RGRVQjJuUXpjSHI4N1ZYM2pNTkcvVW1OL0NCL1diRWJkQXAvSlg4blVCNVdJRlU5dnpPTUwra3N0QXUzQUNLdSt1SGVTUWF4dWxyNnZNdTg5Q2c1TWtJRzE5blVhaXNpNGYzeDR6SW1RK2lDMTljVGNaVE5HTDN0eFVlNzM1OW1ZNG1STkxONFBRTWcxRi9WRk9ybWwya0tZU3F3a0xnT1pNYlpBd3hYajlTZDllclZDQTMxbnRtUVdUMTlPQ3VLVWZaUE56eThWWUc2SW5aZHAxTkFuYmhmczFFcW92VmVsZmo2aGVaVllLUFFjWW9tZlB1WENJa3dhVFhoRE9kQlZKaWRodUU1enBpTGQ0K2dHS0hpaktMQ21xSFlrNUhIOFhIdmhVOUtYTndLbjl3Sk51M2h0Z2F5SEZWdkxjN0dIOXZlc1NTNkd5dHRaRTJZM1ZlbXlwZlVnYUN0YS9vQkc0Ym1FNFRmTXdHYjZKRkd6eFAySk12c1FqeDFQeXhEbDRoWHNQWW8rU280Mjk5dStVSldkSzhZZEV2VXpBVmVWVUxKVUhER3BQeWQwT1FIbkhNNUxSb29iN2dHeHVaNkRQSFhQTXB2d1J4REEiLCJtYWMiOiI3MjhhYzlkNWRkOTg4ZDk5OTkyNDBlNjQ5NDA3OGQ0NjU5NWJmNmZhOTMxZjk5ZGFmZDZjMGExM2ZlNGRjNzVlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InlMM1drQXBWQXVndDBuSkcxRjFYK0E9PSIsInZhbHVlIjoidmtjSzNWamZ2a3VBeVI4R1ljNTVWREJxTlpEbWNYRFZRN00xNXNDOVAvbzdrbkZLNG9GSmFTTmo4eW5mODNBUWIvQis1RkRmdDZPMWhMK3Npd1NBUDA2ZjdQOWdvSmhHMU1iNUdJbllSZFJpMUdnVnF5RVQ3MlAwRUYxb0JpU2t4MStDbzlqS1Yrbkp4a2ZNWG52ZUZxays4MkJ5VW5lcG5UQ3h3YzB5UUhHKzRLZndzZkowR28xNmdLbWhOWVZKalBYckdxSThCSXhCeHlMM25sZWxtV2hzUVBZMU9tcFpkdXltbGk3bFpza2xmTnZjQW9ycU1kc0lpZUJXMVA2VFhyUnV0N1BHZGgyUXBwMW1rSVlXdjJ6a2RQcEFEcm9YVitUQ2gxa2xhYTZvTGxtSnl5SndlN0tpTEFPS2NkcmlPRnhEMUNCcFU1SHYvb1crMFUzdnZsSEFKeURFNEtMU1dzWVlEeUI4TldnR2V6WEQyMjVnVjA0Zi9hZ3p1ZVdHUmhLZFVxUjJkaG4zNUtZOENyWkRCTnAwR3BudzJwU1VMWnZiUFBjQlJxbk5QSzhmck5RdzY2RENTTkVpY2F4WHBVSEc0a0FaaStpaFhBMzZqdkpkeW9PbG1JWGhJNXVIM0JvT3lCRHdLTk5QMTlYbFk0MktYNXRHY3ZxNTNydHQiLCJtYWMiOiIxMTFhMGJkZjlkODNjMTA0YmUxZGFjODQ3ZjM3M2QyMGNmMTg4Yjg0MDA3Y2Y2MzVmNGVjMDJiYjFhYTRkNjI3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZCSk5jbWxZOVdITUxZeDFPU2lSQlE9PSIsInZhbHVlIjoiYkZSd2FXcURrVHdNaVFaRXd6MXJYR0hxMzRWY1N1akd3eW5EUzhFUytoTnFjQ0hVU2l0RGRVQjJuUXpjSHI4N1ZYM2pNTkcvVW1OL0NCL1diRWJkQXAvSlg4blVCNVdJRlU5dnpPTUwra3N0QXUzQUNLdSt1SGVTUWF4dWxyNnZNdTg5Q2c1TWtJRzE5blVhaXNpNGYzeDR6SW1RK2lDMTljVGNaVE5HTDN0eFVlNzM1OW1ZNG1STkxONFBRTWcxRi9WRk9ybWwya0tZU3F3a0xnT1pNYlpBd3hYajlTZDllclZDQTMxbnRtUVdUMTlPQ3VLVWZaUE56eThWWUc2SW5aZHAxTkFuYmhmczFFcW92VmVsZmo2aGVaVllLUFFjWW9tZlB1WENJa3dhVFhoRE9kQlZKaWRodUU1enBpTGQ0K2dHS0hpaktMQ21xSFlrNUhIOFhIdmhVOUtYTndLbjl3Sk51M2h0Z2F5SEZWdkxjN0dIOXZlc1NTNkd5dHRaRTJZM1ZlbXlwZlVnYUN0YS9vQkc0Ym1FNFRmTXdHYjZKRkd6eFAySk12c1FqeDFQeXhEbDRoWHNQWW8rU280Mjk5dStVSldkSzhZZEV2VXpBVmVWVUxKVUhER3BQeWQwT1FIbkhNNUxSb29iN2dHeHVaNkRQSFhQTXB2d1J4REEiLCJtYWMiOiI3MjhhYzlkNWRkOTg4ZDk5OTkyNDBlNjQ5NDA3OGQ0NjU5NWJmNmZhOTMxZjk5ZGFmZDZjMGExM2ZlNGRjNzVlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InlMM1drQXBWQXVndDBuSkcxRjFYK0E9PSIsInZhbHVlIjoidmtjSzNWamZ2a3VBeVI4R1ljNTVWREJxTlpEbWNYRFZRN00xNXNDOVAvbzdrbkZLNG9GSmFTTmo4eW5mODNBUWIvQis1RkRmdDZPMWhMK3Npd1NBUDA2ZjdQOWdvSmhHMU1iNUdJbllSZFJpMUdnVnF5RVQ3MlAwRUYxb0JpU2t4MStDbzlqS1Yrbkp4a2ZNWG52ZUZxays4MkJ5VW5lcG5UQ3h3YzB5UUhHKzRLZndzZkowR28xNmdLbWhOWVZKalBYckdxSThCSXhCeHlMM25sZWxtV2hzUVBZMU9tcFpkdXltbGk3bFpza2xmTnZjQW9ycU1kc0lpZUJXMVA2VFhyUnV0N1BHZGgyUXBwMW1rSVlXdjJ6a2RQcEFEcm9YVitUQ2gxa2xhYTZvTGxtSnl5SndlN0tpTEFPS2NkcmlPRnhEMUNCcFU1SHYvb1crMFUzdnZsSEFKeURFNEtMU1dzWVlEeUI4TldnR2V6WEQyMjVnVjA0Zi9hZ3p1ZVdHUmhLZFVxUjJkaG4zNUtZOENyWkRCTnAwR3BudzJwU1VMWnZiUFBjQlJxbk5QSzhmck5RdzY2RENTTkVpY2F4WHBVSEc0a0FaaStpaFhBMzZqdkpkeW9PbG1JWGhJNXVIM0JvT3lCRHdLTk5QMTlYbFk0MktYNXRHY3ZxNTNydHQiLCJtYWMiOiIxMTFhMGJkZjlkODNjMTA0YmUxZGFjODQ3ZjM3M2QyMGNmMTg4Yjg0MDA3Y2Y2MzVmNGVjMDJiYjFhYTRkNjI3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014571163\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-74787312 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74787312\", {\"maxDepth\":0})</script>\n"}}