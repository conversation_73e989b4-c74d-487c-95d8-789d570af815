{"__meta": {"id": "X85d08565ed46fa5c31a9fbdac8e7f87a", "datetime": "2025-06-28 16:21:49", "utime": **********.433705, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127708.99404, "end": **********.433719, "duration": 0.4396789073944092, "duration_str": "440ms", "measures": [{"label": "Booting", "start": 1751127708.99404, "relative_start": 0, "end": **********.372956, "relative_end": **********.372956, "duration": 0.37891602516174316, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.372967, "relative_start": 0.37892699241638184, "end": **********.433721, "relative_end": 2.1457672119140625e-06, "duration": 0.06075406074523926, "duration_str": "60.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46412080, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2441\" onclick=\"\">app/Http/Controllers/PosController.php:2441-2475</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00196, "accumulated_duration_str": "1.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.416069, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 74.49}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.42627, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 74.49, "width_percent": 25.51}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1079524714 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1079524714\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1042241982 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1042241982\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127705801%7C44%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdTMDFNc0dOZ0lQTDBRZndQVEpDS2c9PSIsInZhbHVlIjoiWFgyR3RUQm1JMElmNVlIVmlPbVZzL29ELzRjb3BacHhGREx4NlFVL3o2UmpyOW42T0ZtZ2NPQ2hHL2dURzk4MHpZbUlmUVNvaXRjSHFzZ2hxUStCQi9SK2NxRWxaeHhEVzM1cUljTGtqMHhMNytmY0hNaFNUekJnRkErRWRqSUVtNEMrVnBUUHpCak90SS9WT3cwKzVnSjk3ZlJVMDZGeTdnYVh2K2x1eG1kYlZraDMvd1FaRzhSTkdmSEFlTkpTeE5LKzdsTm1IKzU1eTZ6QXhuUnZvQTlZdXhVcENyeks4TXpyb2JsQk5UaWF0NTFFcG9mbXRzeWZWdjZISWlwOVljejFTbkNVM3ZITktNSUFnQ1lOSklVdVo5T2orcWNnYWFqQWpPUy9aNEtXMXc0NzRORXJpY1JTSElIZW9yRFdvM1RZSDhTRFQ5dUdOSHV2eTJYeUhaU0U5V2J1NzVXRGtGKy9IVVRvZTRQN2Yyak14MXgwL3VqKy9CK3N2RzUrQnBqZENMd0JKeC9CUnpRcEdXY2luYWx5ZUFxa1RaSHBPL1hoMFkzNFhyVGZPRWhjN2tNamFPbUxKTGN2NURDenRTMmUyVXpPb0g3cDNEZVdFU0dJdlcvaFoyOSt3RWt4VTR2ZkZUTGxmUWVhZFhIeWJmSWdTbStjUFNUQzlyeS8iLCJtYWMiOiI5MTAzOGE5OGVlMThlY2U0ZDI0NTc4ZWI2NzJhOWQ1MGYwMzhkY2U0YzkwMjQwZTY5NTQ2ODhiMTNkMmVmMzBhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldNZUZsdVJOSVB1VzgzNEh1M0U2Smc9PSIsInZhbHVlIjoiTUV0K01Sd2Q3MXpNbTNFcE9PM1psby9kcHloaFg2NldFcHFlSm1nT21QTTM5aEJBdFJ0bkJXc1BwQTFZTDFhaE43c1BILytpMWhBWXB6bGRzUlNQY0I4bFBPWkwzOVcxSGFnK3E3NGpKU3RSeUVjOHJTZ1RZNU5GZXIxdjlvaXd5RmNkS1Zxb3VqOEorYUcyRnhTTDNNODhIMmdOR0ZDRXRMSHJFRDk3azlIMDlML05HMWwreE1ON0FPUjB1bzRaVlF1bTRjWjVqTVBqU202MWtxMGRFa2ZTend0bis0TDVSNzJCdUNLbTR0QWtUaHVMcVhDQ2VncmhyazQ3b1FoZWkxNDhYUnFQODl2S29VTjhOUkdjOG1qOTZCcE1yaFh5dUhubkppcjlRUmdzMnpnQ3paejVLTVdGbHZJUmwwSWhBWHB4ZlJSK3g4L0x2MDZvUWlyNnZGYjVuanhSRU04WUlpblNzb2RSdlY3QjlnZ2J5aTRreUNEOG96WTgvZ1FwRGRlS3R2YVdKZG1YcHN4NUp2OEtjVEVtMCt1eld1QVdZV2NrNE5aWmZjalV5ako4Mk9yUmlrRXRWa2VMTHJyNG9nS0VFT3B4bUlkaE5MZWxqRGhFeStlcm8xWC9NeU10UHA3MEw1Rks5Z2VpazZ6MC9WNUtEWGdwY3d4SGdMZWMiLCJtYWMiOiJkMDQ4OTBmNjNjYzc2N2Y5ZjcxOTc3ZDIzMGZkYTZhMWE4MjQ0MDE5OTU2ODRjNmRjODI2MDlmZmZmNjQxNjJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-414948005 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-414948005\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-496217117 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:21:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRUR1FOVU9SaGovaWdRTHFac2pVS1E9PSIsInZhbHVlIjoidXd6b2oxRjh4OTgzSGdUL0hOcGtIQjY3cFBZZ0JBUTlUd1pBcDhVbHN4M0FBdHRUeC9ZbFJFL0tRbkFWZlVUWmVNTTVPQXgrWmRKMU1BYU1yNmhYQUdGY1daKzB5ejllYjJIczNMVExwUnV2Uk5yY0ZDVG9UMXRUeWVGeVArOUhxQXRLNU9ZRGhUN3o4QTl1ejU1YVhzazJpSENLY1FhZjhmcHZGWmVsY3NidVoxTGdQdmoranN3WmtaYVd4dE1RSzRyd3F4YXVkOTJTNHJhTnN0Yk41eVZ3bWlhMzNjcDVNTFhRVkJUenpDUldWZmsxOGZ6ZWNjcGZxeVNxTjdUdHpUZkM4NEJpcFhLU1djbjlESnFaaG9BZFNjSitOeVJDeUx1ZHNETlA5TWF4SWxlU1NBaXJkZUo4czJJb1JIY3UyMEVZNDJ6bTgrSG81NVhpR2NDUmMwOEhrK2dzZDVmcEd6Z1RvY2w3VGg3bDBKZVFWS0JJanQ5WVB2dkZBei9jakgwalVjbS9WbVA5M1VMYVdXNHF0NmtxREFvWUplQk1XQWV2L1lRVEtaTmNuLzkwWlMycUZXOG1uTmQyWkl2ZVh3Yk1CRkx0ZkZFMGkzelZTRjdmUjh4S3lpZzZjaGFPaGtJY0MwcU8zakJDT0wyM3ZDZnprbTNNaEVXQnIyMHgiLCJtYWMiOiIwNTExNzQ3M2JmNDU4OGNmZTM3ZTkyNGFiMjJhNTQ2NTI4MDZmZTEwZDZmNjVkMWI1NjU5MTZiOWVmNmNlNjE3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBkRHhGUGVNdE8zTjdMWVNvS0tvRnc9PSIsInZhbHVlIjoiRzJrVEt2L1JrcWJrMU11MWFQSjlZeGZibUtwM3pjY3IyMXJyRUxnWGlGVVhST3gwQmdLMTQ3dVp4T3RLWCt0TlBha1pMT3VqVE1RQ3BrRjU5dHdLR1hqTzM0NTVTRFhncEwrSHVXN1A2YmNIam43TVJVTVN4T2QveFd5dVBwR21NemJtcVE5MHMvN2FwbytGdTR3dHM0a3ZJVEMxc0JLc0RrUE9jb3l5RmNrek1Ea01BTWdzM003RjhreTFIQUg5YUNLSEwyTlJlY1NROE1qTlpINHFjSFBSaWJYZ2k0RjVsRmJyVldxNDE3YnpkZ3N3V3pDSHc0aUNHSHd3UzExWHhNSXpYMlptQXErNS9PVmtkYnMrMUVSenNheVdTYnhsTzNPRnlJQUl0RmhLbkJTZ09la2N1UWxDclFVRkdwbUdPaEUzNVh0MmRtVjVrWnNOT2xMcnhPbks4UkVvdU9pK0hPOFpyeHBHWUgrWk12ayt1UVY5YlpPSDJralBjNGVqZG84WEFPU214K3piZE1saW8reHhiSm9seU55b2phM0RORWNKVHNabDNRVjJrZWpsTUo1STdlMm9wU2h0eGxDTUROUXBMeGpEKys2RzJUZlZpcmJLTVU0dCtmMGtkQU5UaThkWXUzbHRRZGJzMHJRVnRCTnJUOWdKdG55ZnpaeVciLCJtYWMiOiJjOTQ2M2NjYzQ0YjRjMzI0YTY5YjRjNDJlODk5MGRkZWZjOWM4NWJhNmMwMzdlY2Q3OTEwZjRlMzFiMGY0YzJkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRUR1FOVU9SaGovaWdRTHFac2pVS1E9PSIsInZhbHVlIjoidXd6b2oxRjh4OTgzSGdUL0hOcGtIQjY3cFBZZ0JBUTlUd1pBcDhVbHN4M0FBdHRUeC9ZbFJFL0tRbkFWZlVUWmVNTTVPQXgrWmRKMU1BYU1yNmhYQUdGY1daKzB5ejllYjJIczNMVExwUnV2Uk5yY0ZDVG9UMXRUeWVGeVArOUhxQXRLNU9ZRGhUN3o4QTl1ejU1YVhzazJpSENLY1FhZjhmcHZGWmVsY3NidVoxTGdQdmoranN3WmtaYVd4dE1RSzRyd3F4YXVkOTJTNHJhTnN0Yk41eVZ3bWlhMzNjcDVNTFhRVkJUenpDUldWZmsxOGZ6ZWNjcGZxeVNxTjdUdHpUZkM4NEJpcFhLU1djbjlESnFaaG9BZFNjSitOeVJDeUx1ZHNETlA5TWF4SWxlU1NBaXJkZUo4czJJb1JIY3UyMEVZNDJ6bTgrSG81NVhpR2NDUmMwOEhrK2dzZDVmcEd6Z1RvY2w3VGg3bDBKZVFWS0JJanQ5WVB2dkZBei9jakgwalVjbS9WbVA5M1VMYVdXNHF0NmtxREFvWUplQk1XQWV2L1lRVEtaTmNuLzkwWlMycUZXOG1uTmQyWkl2ZVh3Yk1CRkx0ZkZFMGkzelZTRjdmUjh4S3lpZzZjaGFPaGtJY0MwcU8zakJDT0wyM3ZDZnprbTNNaEVXQnIyMHgiLCJtYWMiOiIwNTExNzQ3M2JmNDU4OGNmZTM3ZTkyNGFiMjJhNTQ2NTI4MDZmZTEwZDZmNjVkMWI1NjU5MTZiOWVmNmNlNjE3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBkRHhGUGVNdE8zTjdMWVNvS0tvRnc9PSIsInZhbHVlIjoiRzJrVEt2L1JrcWJrMU11MWFQSjlZeGZibUtwM3pjY3IyMXJyRUxnWGlGVVhST3gwQmdLMTQ3dVp4T3RLWCt0TlBha1pMT3VqVE1RQ3BrRjU5dHdLR1hqTzM0NTVTRFhncEwrSHVXN1A2YmNIam43TVJVTVN4T2QveFd5dVBwR21NemJtcVE5MHMvN2FwbytGdTR3dHM0a3ZJVEMxc0JLc0RrUE9jb3l5RmNrek1Ea01BTWdzM003RjhreTFIQUg5YUNLSEwyTlJlY1NROE1qTlpINHFjSFBSaWJYZ2k0RjVsRmJyVldxNDE3YnpkZ3N3V3pDSHc0aUNHSHd3UzExWHhNSXpYMlptQXErNS9PVmtkYnMrMUVSenNheVdTYnhsTzNPRnlJQUl0RmhLbkJTZ09la2N1UWxDclFVRkdwbUdPaEUzNVh0MmRtVjVrWnNOT2xMcnhPbks4UkVvdU9pK0hPOFpyeHBHWUgrWk12ayt1UVY5YlpPSDJralBjNGVqZG84WEFPU214K3piZE1saW8reHhiSm9seU55b2phM0RORWNKVHNabDNRVjJrZWpsTUo1STdlMm9wU2h0eGxDTUROUXBMeGpEKys2RzJUZlZpcmJLTVU0dCtmMGtkQU5UaThkWXUzbHRRZGJzMHJRVnRCTnJUOWdKdG55ZnpaeVciLCJtYWMiOiJjOTQ2M2NjYzQ0YjRjMzI0YTY5YjRjNDJlODk5MGRkZWZjOWM4NWJhNmMwMzdlY2Q3OTEwZjRlMzFiMGY0YzJkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496217117\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}