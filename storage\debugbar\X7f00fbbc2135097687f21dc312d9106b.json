{"__meta": {"id": "X7f00fbbc2135097687f21dc312d9106b", "datetime": "2025-06-28 16:03:42", "utime": **********.80999, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.358984, "end": **********.810007, "duration": 0.4510231018066406, "duration_str": "451ms", "measures": [{"label": "Booting", "start": **********.358984, "relative_start": 0, "end": **********.729338, "relative_end": **********.729338, "duration": 0.37035393714904785, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.729346, "relative_start": 0.3703620433807373, "end": **********.810009, "relative_end": 1.9073486328125e-06, "duration": 0.08066296577453613, "duration_str": "80.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45714464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02771, "accumulated_duration_str": "27.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.75746, "duration": 0.02664, "duration_str": "26.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.139}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.792877, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.139, "width_percent": 1.913}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8004699, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.051, "width_percent": 1.949}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1368650349 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1368650349\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-35449405 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-35449405\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1533645340 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533645340\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1684420320 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126618953%7C20%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitoQXIrZUF5OGJKSisrQmRYY2hVVkE9PSIsInZhbHVlIjoiT1hIdlNrVERpenZDOWxxdDVZZmdjUngxNmhpMG5DZFdrVzB6dDZPRG1scDdENS9rQmc0OVZOTGxYOEZZRm9YaTg3R1g1N2tIODFSc2E3UlNtd2Z2ZDNjTEhJZXNHd3hIMHNMMktGa3hKK0N2NVczRU9UOHdtbUtsY211NGw2bDN1NlNlWlJXeHBsR2FQUWZTRU9aRmlOamU4VHpKQk1QS0hZZ2g1aHNLVDBpdWtZbThRWkdxcnhaNklPZ3QvQThoV2RwNFVtb2dUWU85d1hQMDEvSGxPSnFBV2d3QnhQcTJlMkJwRlFNUWpXK3pPdFFNZ2NNYjBrbldPVjhrSUxxdnhpWEUrQWYySUNPUDJnMkZ3YzVtdkhGTWU5eWd5YjYxNXNoYW5kZnJUaldQMGpJbStuMmFiR3h5Wmt6dTNTY3BtVHF5RFRUMFVwb0lQaHBLMk9FRWFUYmhkWCtRU1UxTVlkeHE0Tmk1VXdpVXBnNUZIdEFZeEFRQVowNXFhUXFiL3RNNWJLRmVKVDBxWkFERDBkeEU5RmFrVG52NWJIcSs4N3dGVEhTek1aVVRoY0k4cDBoY3Iyd3hRa2p6YmYzdWFmRVBSVEl6aW9kNGwzOE04NndtWjdTdDdObzZ5MXlNb21RMk1BcEpRcVBQK3RDYUZIUmtQWjdyNlpyWExGUmIiLCJtYWMiOiI5MzAwZjI4MDc5NTYwZTg5MDU1Y2Q2OTMwZGE4ZTQwNzRiM2FmNzgxMGVjY2IzMDBjOTdhZTdhYTY3YWEyNGU4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVXS29pUnd2bkVxcDRKRWgwanlwVkE9PSIsInZhbHVlIjoiZERPNkFWWmNlMEQwQ2tldEY2WmhJc0xPVzNCQW0wTWE1ZDU5eHpaMUpkU0owS3Z0UW84Wi80dysrdWpMQlhWZ3hiaWF3SDlDUHZVNnRVQkg3OWVWdjNjWTZuZ3dFSVZYTTQvL0ExWVd6ekt1TkhLZ3VXL3pXN1ZQSkUxNk9XaFNsZTVQQURWWlh6UTRqd0J1MGttUG4wMjJTWmN1b3FsazRkMEZEeXZJeDZ6N3VTWUMyUW1tUXRDbWk2WjRwdjlRaEpuZXByaUJ1dCtrbjBBbW93QUIyZzkwNURDQXpUM0Roa3NjNkJnZEFZS2o2QTJ4d0NEUFRQcDZuRXN6cHN1WkhwUkZ5czFUY3VyMGhxRlFxODhZREhqZVYrVENNRW5iRWJPSWVTK1pHR1JHZG1EUXBycEhlQXhGeWNwMG5OQzRxaGlWUjNOdGI2WUE0REZOakt5UkEyRFgyMFQweFdSc1FiKzBWb3RBUm1pSnIxUUdIMDFjTGNnNTBMLzdua3JTSm5sajIza0Rla0JpL3grdk9LN0ZjN1ZFUDhrcHhobHdMcDhscGs0VStubW1MYUlycDlqMS9JUzVZLytTV0xkamlHZDZHdzViNzZsS2JHbmpjUFc2eFU1U2tmR2R3K0k5SVNreGsrSGkyT1JLVlBoY1pZUnl0QStoQ2pqL2VIZWEiLCJtYWMiOiJlYjI4YjFkNTRhMDQ4YzMwMDkwNDVhMzFmOGVjNWFiZWVmOTdiMzA5OGRhYzAxZDQ5NWRjZThhZDAzYzU2NzkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684420320\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-757102913 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757102913\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1476609690 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilc3UnNUaXdFcGU2SUJjVXVVZDl2L0E9PSIsInZhbHVlIjoidmlvMURpL0VYSFZRZ1VuTFFNeFdZQUM3UVFFU3k3RHc3TlMvT3p2Z0VhWXNNdm5vS1M0WGdtb0t1L2dsK3h4VHRHZjlSdFo4K0E1MEZuY0kwWWdPTjZhdzFYajljVm96NWZxSFBxbUN3S2pMMjJlMzNyZmM0NTQ2ZENUVDRDL2ZvWWd0TU9PRWRGQVgyRTZlNEIreWllUEJCcSswQ2lSYkJydCtIOCt5c0hpMUVaNHNZNXpYVDRTNHhTazVyS3FMQTVTbEZyRkRNY0lSNXhFUmsvdnpYck9WTnh4a1RTN3ZDcGczU1VqSEN2MDJjTVdwS0dsTzI2Mk9nWTNBcXFIR1VCU2ZmdlMraXRHSHFzZ0JIN1NiVnBFNVVwMnl1MGVVaEJtcGxDL0FuNm9RR0NqSW1RREdZdnpWTUJhdTk0M0lMQngxWERBVUhzdFhoKzIrSnpUR1BLZUhOdThJcVRwaDJYb29PM2Z0NnpLTHdXUk1iWjJzcmlCbCt3aEJkaUtxUFg0MTYzWVdJeHZmNmNKMGlIbHcvSllQTjRlbXdQYXVVTG1QejEzTFFhUlFvTCtwOENaY1I2dmxaRnpLNHFHd2o2aXMyNG9NWWtFSWpLOFpVOCtuWm10TW9MaHRyL0V5ZGlkYXRPdkFyUW9MRDgzOTM1ZS9oOHBKc2k0LzZEd0QiLCJtYWMiOiI2NmMyZjhjNzBlNzJlNTMyMzA4Mzc2MTRiMzE3MzNhMWZhODc2NDFjMDc5ZmY3OGRjYzhjMTM0YmVkNTIzOTMyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1BNmw4QmNBcmM2azl3Vld0eFJNV1E9PSIsInZhbHVlIjoiQU5zclh0azNrczJCSXlaVEVYYlp0Z0RhNmJtTm9RczJaSkI4QzZEUXl0ZGJuQWVNOGE4SzY0NEswaEFRNzd4c3REbUJJTGdnaTYwRHZYY0xXUGcrc3hlZThWVlRTWmsvdis3SndueXFnbGxVWXN3WFBUYjE5T2pRVmhNcFhnQi9lVTMwMTNnQm45ZUlQN0ljNCsxVll2VnU0M2VpMmYxWEJweGRmUk1zcDhnOWJ0U0VYSVhFQ2lUV2twKzlacVdzQ3BOL3JrT0hSeEN1azl5TTBKeHNnTUVmWHlhci9XQW01TTBMQ2tOSmh1UHhtZGFmR3RUZzNweDVFcENONXRTNUZGWmJFWGRrNGdqQzRoMzJ2K3d1a0NaN2xlKzJuMHViTURRcmpYbE1LQzIzWlU0S1dZWGx3M2IxSDEwbk95M1FObzlJdm53bVpXVVBPU0hid1U5S3lKQzZBbFBnZFh5YUkyNWxSYW12TzNRNlE4bkdydS9iN1l5U2F6cldKY2hQbUJoMEJ1K0NuNVZ6V1RwQmFTZkRpZzhIUGlsc3UybjVKMW90R0YxdG8yZ0hNbi95VHg5ZVJiM0FJVUZPTmV0d1JUQ3laTjM1TDNicXpVbDdSUmdIaG0ydFQ5cTd6cTM3LzBzWmYrUkVZZlJJRjdyQzMxcEZOTDZ3OW8vYWtHcHEiLCJtYWMiOiJmMTczOWM1MTZmNTY2YTliYzUzZjMzM2I3MzM3ZjU5OGRjYTg2ZjhjNzIxMzE3ZmFmZDJlY2Y3NjdkMTM2YmI5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilc3UnNUaXdFcGU2SUJjVXVVZDl2L0E9PSIsInZhbHVlIjoidmlvMURpL0VYSFZRZ1VuTFFNeFdZQUM3UVFFU3k3RHc3TlMvT3p2Z0VhWXNNdm5vS1M0WGdtb0t1L2dsK3h4VHRHZjlSdFo4K0E1MEZuY0kwWWdPTjZhdzFYajljVm96NWZxSFBxbUN3S2pMMjJlMzNyZmM0NTQ2ZENUVDRDL2ZvWWd0TU9PRWRGQVgyRTZlNEIreWllUEJCcSswQ2lSYkJydCtIOCt5c0hpMUVaNHNZNXpYVDRTNHhTazVyS3FMQTVTbEZyRkRNY0lSNXhFUmsvdnpYck9WTnh4a1RTN3ZDcGczU1VqSEN2MDJjTVdwS0dsTzI2Mk9nWTNBcXFIR1VCU2ZmdlMraXRHSHFzZ0JIN1NiVnBFNVVwMnl1MGVVaEJtcGxDL0FuNm9RR0NqSW1RREdZdnpWTUJhdTk0M0lMQngxWERBVUhzdFhoKzIrSnpUR1BLZUhOdThJcVRwaDJYb29PM2Z0NnpLTHdXUk1iWjJzcmlCbCt3aEJkaUtxUFg0MTYzWVdJeHZmNmNKMGlIbHcvSllQTjRlbXdQYXVVTG1QejEzTFFhUlFvTCtwOENaY1I2dmxaRnpLNHFHd2o2aXMyNG9NWWtFSWpLOFpVOCtuWm10TW9MaHRyL0V5ZGlkYXRPdkFyUW9MRDgzOTM1ZS9oOHBKc2k0LzZEd0QiLCJtYWMiOiI2NmMyZjhjNzBlNzJlNTMyMzA4Mzc2MTRiMzE3MzNhMWZhODc2NDFjMDc5ZmY3OGRjYzhjMTM0YmVkNTIzOTMyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1BNmw4QmNBcmM2azl3Vld0eFJNV1E9PSIsInZhbHVlIjoiQU5zclh0azNrczJCSXlaVEVYYlp0Z0RhNmJtTm9RczJaSkI4QzZEUXl0ZGJuQWVNOGE4SzY0NEswaEFRNzd4c3REbUJJTGdnaTYwRHZYY0xXUGcrc3hlZThWVlRTWmsvdis3SndueXFnbGxVWXN3WFBUYjE5T2pRVmhNcFhnQi9lVTMwMTNnQm45ZUlQN0ljNCsxVll2VnU0M2VpMmYxWEJweGRmUk1zcDhnOWJ0U0VYSVhFQ2lUV2twKzlacVdzQ3BOL3JrT0hSeEN1azl5TTBKeHNnTUVmWHlhci9XQW01TTBMQ2tOSmh1UHhtZGFmR3RUZzNweDVFcENONXRTNUZGWmJFWGRrNGdqQzRoMzJ2K3d1a0NaN2xlKzJuMHViTURRcmpYbE1LQzIzWlU0S1dZWGx3M2IxSDEwbk95M1FObzlJdm53bVpXVVBPU0hid1U5S3lKQzZBbFBnZFh5YUkyNWxSYW12TzNRNlE4bkdydS9iN1l5U2F6cldKY2hQbUJoMEJ1K0NuNVZ6V1RwQmFTZkRpZzhIUGlsc3UybjVKMW90R0YxdG8yZ0hNbi95VHg5ZVJiM0FJVUZPTmV0d1JUQ3laTjM1TDNicXpVbDdSUmdIaG0ydFQ5cTd6cTM3LzBzWmYrUkVZZlJJRjdyQzMxcEZOTDZ3OW8vYWtHcHEiLCJtYWMiOiJmMTczOWM1MTZmNTY2YTliYzUzZjMzM2I3MzM3ZjU5OGRjYTg2ZjhjNzIxMzE3ZmFmZDJlY2Y3NjdkMTM2YmI5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1476609690\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1153737929 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1153737929\", {\"maxDepth\":0})</script>\n"}}