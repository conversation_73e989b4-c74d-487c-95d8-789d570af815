{"__meta": {"id": "X2e9fce7b4b9be97df56b90d191c832ea", "datetime": "2025-06-28 16:01:26", "utime": **********.90048, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.39961, "end": **********.900494, "duration": 0.5008840560913086, "duration_str": "501ms", "measures": [{"label": "Booting", "start": **********.39961, "relative_start": 0, "end": **********.839555, "relative_end": **********.839555, "duration": 0.4399449825286865, "duration_str": "440ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.839569, "relative_start": 0.4399590492248535, "end": **********.900496, "relative_end": 1.9073486328125e-06, "duration": 0.06092691421508789, "duration_str": "60.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831872, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00435, "accumulated_duration_str": "4.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.878891, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 36.552}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.88934, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 36.552, "width_percent": 9.195}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-sto%' or `sku` LIKE '%<div class=\\\"product-sto%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-sto%", "%&lt;div class=&quot;product-sto%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.89198, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 45.747, "width_percent": 54.253}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1971123005 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1971123005\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-380399886 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-380399886\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-88655643 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&lt;div class=&quot;product-sto</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88655643\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1807677883 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">50</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRCWE9OTGNwalI0dFluMjJocUR5T1E9PSIsInZhbHVlIjoiUllSZ09pMTgzaHFNNis5MGRNNkRjOExLM25CTWdNQjZITExFa1lwYjgxRTRYa3dPQnByU2I2LzV0bUlXYTVpOGF2blgxaUlrQnFFeUdabW5BeERRd3hKMUJRQWt1ZXROUFlWVDNwWFB4UEZiT3lIcWN2b1hRWThKQnAvb1JLTWIwRFZoN0lEaU95NDJ0MG9UbXlGcGlvZytSVHJTb0dlaHVGUE5sVGZyREZWSnlpVWtaVWp6eEh6VVFsbG80RGJVRFVGdUE2QzhyM0hVY3EzY1NueGk4bWwrUHNGc2xvTGJHREZENVdpZjMwNis2cU8zNzZUNnZ2SjA2UU5Fb1BaVEV0Rkpzam4zb1Y1UWRueTdidGVOdEtEbzZKUzZxRVZuZmd1YnQ0WjNhMFBqaUh2RjJvU21pYklubC9KUmxOM05mVzZWMUdzSmU4Umk2OWhYSTNJbGZOK3MrTjd4WWFzd0JlRUgvUEJudzRsRmZaUEJFd3J1d1lnbnBPNWVkU2JMVXI5eTF6RzYvV2QwV0hGd3VBQWJWanROeU1OY1htMmlWQzFvczB3RUxmY3FMbDUxbElwOU9jZTRzOWxYdytjWFJMcE9paVEzNFBFSndVZEVDZEdWRFpRTm9UaEZNVVg1ZlhTeEFUSWxoc3J0VlovTVFCdnhDcnJrNjRnTDJGeXgiLCJtYWMiOiIwM2MyMTI3ODJjY2Q4MzY4ODQyZmU5YTc5ZjRmZTQwMjlkODUyYTI3YmM5OWIwMTdkZGFlZWUwZWE3NzI4YjUyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtkVzBabStGMFdxaFRoK2EzNnN3UFE9PSIsInZhbHVlIjoiVW5QejYwOGN4eTMrRWJjbGd2YzJCcU1FZnBFQUJEMUp2eGMzSnBQbFJlMkhodmlhNUs0UHdhTWlraHI1bXJWVkV3ZTZJWUhwZmpOa2J4MmcyMUUxcmtqWWtlZTFPaDh4MkNvM3BiRFhTY1dQVGp0OHc0aGNicGRFalRjWTl6aHkvbmsrKzZiRndKbTkrTkVYbmN1SC9hMlVOVUJsYXpoN2ViSXRiVnJ4MWQ1TUtmcjNvRFVRTUZIdzZhOG0zSWs1Z0x0WTZXeUdiSHk3c0htSXlyT29BeGNKQjJWdURBeHRTRmw1Snh3M1poTXZCUEQyd0R3QzA4Z293REZWUmxGdU9iVHhCZDFUR3lHdVBJMFlZY0d0bnpTcVpFaWQwWnJVcGpJaGR3aWdTdHNwR3RscUNwejlJRnpBcWI4cjh4cGdnRVd4TDltZnhQRmpJQjRoT0pRU2JCOER5ck9acFRJYVhhMzlIYTQxS0dpalBRTmhZWk8wd1M3YW1pd2xBSVpMUzBZOXhxU3Rlam9pd1pqOGFIdkVtbjg3bTcvclpFUUpVYTZna2c3aE0yQ3JwU0xRTndaYlA3WmRnSGhxemlyVHk3UWVFVW9zazVIcHp1Vk53S09zYlB0dEZqZlpBQWlsVnRkSGswMGZFMFVPV1Q3a1BEdmFZVGMxR1h0RTV3NkoiLCJtYWMiOiJjMjY5NzE4ZjY5ODk4MmU5MjcxYmZmZGEwZTE4NmZiNzZkNzIxYjUxMzM3NjYyN2ZhZjQ4ZDVjMWFmZGQ1OWRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1807677883\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-798837550 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-798837550\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1876570793 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdvY3dQMGVVRkYwbFMrbEo0b2lLR3c9PSIsInZhbHVlIjoiYW85TlpRM1grZFZRbkZVUHkzZTFma3FTRW96RFEyZXNEcCs2anVpSGxtTFdROHdzUzRPVzdJeUVFYW44ZVRPckZwcmdNeCtIVnlNaDdoZytjK1VvZkM4VFcyN1lUOWtqRklUTEFVb1lLNkpmSHJNeVJLbStpRUlIR0xOZ3hlRjJ3MXlSNnZjM3hFeUY0cWhEVnNjeWtSanJialNuVVk1TjFnZ3ZBY0d5YXdGV0JxUjNITDdwZlNNanQ3MU9BWGkyMFo5NGVHUWpDMGI3Uis0bHJqcSt1TGxlOVc3b3lma3RXZUxKb3JsVXZQQjlKbW9ObC9lZkc5YTQyOUNtTVR4VzlDbkJjdFlPeTZPbTlNdHVLc0oxU1RxYUFEaUhBU2VCNkFodUNzTFlSczJUc3lkeXRrYWc5K21YUG5jYXdNcDB2NG4wb3l2ZXFzWWNFeE1CejVwaEtubnZpeVljbEp2WVVlazRybVplK1E1LzNqcnR0TTBZNDRXa2RnM3haU0VObTJkYmIxNkFsUXZoUmZRTUNEWnJSem1Od0NiRTBOeVlKMjNIY0tTTFpZcUt1YnNwRVh1M21Ea2s3YzBKTDk4dkJJNDAyUHlabzBKMzl3eXA4bXRwMFJzOHNZTXVOOEg1RjdEcWJ6QktqYmxlQXRwMURZMmVWVFdpNFlzU2ZnVVEiLCJtYWMiOiJiNjIwNjAzMmFjN2YxNTk1ZTFkNDYwOGZiZGRkYTUxYzM2MzY0ODg4Yzg2MjBjYWFhZTgwNTExN2MzOGU5MTY5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpEc0hSUnNDNWpqcExmVU94b1REVlE9PSIsInZhbHVlIjoieS96Y0lTRlh3K09zdnhHMTljcVRmbGpKR2FickhrcENNN3VEQ0crejU2K0xic3VZZ3hzY3FrL2xMRndwNEdLeUdhTjF0SFQrM0pHcEh5YjRkdUllVzBxMEMyVEMwSzJwM292dXF4NlZ6SzhHNU1LY244d2poMVdGa3o5SmlleVIybDgzc09SaE1Wa0xTMTViYUlpaTFvK01pbXJaNnIrSkxVTW1LUEtlb1VlY20xNEZ4aC9DeTBLeldhRWFZcGFQZGhiQkgrRFV4eERPRWZ6VFlicGNNakVJdk1nWExDYnNSeXZkVVpKVjVGeE1aWWwybElSVmxkdkNiRERjbTY3VVFFRWErenJ5Y2YyQ2JzSzhmVHJrM3MwTjduVHhUbVpaWm96QW9pRXFJb1BCV3ByU1h4S0czaUpnMFl1bC9Fa2F4SE5iMmp5NHVsRnRuYk10bTRwR2F3MzJaMUNVSTdua1BLNFhYSUdySWtqSk92anphTkorc3pGQXNPeXk4MWFRT0Q1TEhsSmVCN3ZFVTMvVXZ1dUZ2elFSL2EyY1JkaVVtSk9SaWdiRnBUMjZpeGlqN0lzQUFVQzc0TmkzZ01yNGZncFE1K3RSUk1Tc1NWODI3U0FaNFVTd1d4eVBEdzl4cFdOK3BjSTIwTTVILzkycU5XUE56ODVyb3NUMFRLanEiLCJtYWMiOiJlZTk4YzU2NjFkYmYzMWVmOWJlODQ2MWY3MzNjNWVlNDJiNzBmMjAyMTZkZDkyMGI0YTVkMmM5NmNjNWEzMzY4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdvY3dQMGVVRkYwbFMrbEo0b2lLR3c9PSIsInZhbHVlIjoiYW85TlpRM1grZFZRbkZVUHkzZTFma3FTRW96RFEyZXNEcCs2anVpSGxtTFdROHdzUzRPVzdJeUVFYW44ZVRPckZwcmdNeCtIVnlNaDdoZytjK1VvZkM4VFcyN1lUOWtqRklUTEFVb1lLNkpmSHJNeVJLbStpRUlIR0xOZ3hlRjJ3MXlSNnZjM3hFeUY0cWhEVnNjeWtSanJialNuVVk1TjFnZ3ZBY0d5YXdGV0JxUjNITDdwZlNNanQ3MU9BWGkyMFo5NGVHUWpDMGI3Uis0bHJqcSt1TGxlOVc3b3lma3RXZUxKb3JsVXZQQjlKbW9ObC9lZkc5YTQyOUNtTVR4VzlDbkJjdFlPeTZPbTlNdHVLc0oxU1RxYUFEaUhBU2VCNkFodUNzTFlSczJUc3lkeXRrYWc5K21YUG5jYXdNcDB2NG4wb3l2ZXFzWWNFeE1CejVwaEtubnZpeVljbEp2WVVlazRybVplK1E1LzNqcnR0TTBZNDRXa2RnM3haU0VObTJkYmIxNkFsUXZoUmZRTUNEWnJSem1Od0NiRTBOeVlKMjNIY0tTTFpZcUt1YnNwRVh1M21Ea2s3YzBKTDk4dkJJNDAyUHlabzBKMzl3eXA4bXRwMFJzOHNZTXVOOEg1RjdEcWJ6QktqYmxlQXRwMURZMmVWVFdpNFlzU2ZnVVEiLCJtYWMiOiJiNjIwNjAzMmFjN2YxNTk1ZTFkNDYwOGZiZGRkYTUxYzM2MzY0ODg4Yzg2MjBjYWFhZTgwNTExN2MzOGU5MTY5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpEc0hSUnNDNWpqcExmVU94b1REVlE9PSIsInZhbHVlIjoieS96Y0lTRlh3K09zdnhHMTljcVRmbGpKR2FickhrcENNN3VEQ0crejU2K0xic3VZZ3hzY3FrL2xMRndwNEdLeUdhTjF0SFQrM0pHcEh5YjRkdUllVzBxMEMyVEMwSzJwM292dXF4NlZ6SzhHNU1LY244d2poMVdGa3o5SmlleVIybDgzc09SaE1Wa0xTMTViYUlpaTFvK01pbXJaNnIrSkxVTW1LUEtlb1VlY20xNEZ4aC9DeTBLeldhRWFZcGFQZGhiQkgrRFV4eERPRWZ6VFlicGNNakVJdk1nWExDYnNSeXZkVVpKVjVGeE1aWWwybElSVmxkdkNiRERjbTY3VVFFRWErenJ5Y2YyQ2JzSzhmVHJrM3MwTjduVHhUbVpaWm96QW9pRXFJb1BCV3ByU1h4S0czaUpnMFl1bC9Fa2F4SE5iMmp5NHVsRnRuYk10bTRwR2F3MzJaMUNVSTdua1BLNFhYSUdySWtqSk92anphTkorc3pGQXNPeXk4MWFRT0Q1TEhsSmVCN3ZFVTMvVXZ1dUZ2elFSL2EyY1JkaVVtSk9SaWdiRnBUMjZpeGlqN0lzQUFVQzc0TmkzZ01yNGZncFE1K3RSUk1Tc1NWODI3U0FaNFVTd1d4eVBEdzl4cFdOK3BjSTIwTTVILzkycU5XUE56ODVyb3NUMFRLanEiLCJtYWMiOiJlZTk4YzU2NjFkYmYzMWVmOWJlODQ2MWY3MzNjNWVlNDJiNzBmMjAyMTZkZDkyMGI0YTVkMmM5NmNjNWEzMzY4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876570793\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1462454169 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1462454169\", {\"maxDepth\":0})</script>\n"}}