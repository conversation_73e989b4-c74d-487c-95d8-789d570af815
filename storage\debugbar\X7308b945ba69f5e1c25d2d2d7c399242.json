{"__meta": {"id": "X7308b945ba69f5e1c25d2d2d7c399242", "datetime": "2025-06-28 11:21:15", "utime": **********.773919, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.324209, "end": **********.773937, "duration": 0.*****************, "duration_str": "450ms", "measures": [{"label": "Booting", "start": **********.324209, "relative_start": 0, "end": **********.657676, "relative_end": **********.657676, "duration": 0.****************, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.657685, "relative_start": 0.*****************, "end": **********.773939, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.05767, "accumulated_duration_str": "57.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6850212, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.982}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.695537, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 2.982, "width_percent": 1.092}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.704451, "duration": 0.05532, "duration_str": "55.32ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 4.075, "width_percent": 95.925}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "y929FO0OR2qYd721zZmtMrJIBFsWgMiMXXHdl0OR", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=lntfvz%7C1751069310598%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldJekF5MFd6bVk2QnpaWXlOSlJ4UVE9PSIsInZhbHVlIjoiM0hQc3VZeTZpYWJKQnJ3d21VZjN2eU82dlVlbzd0dkRKVU8rQUNSWXprcVBRaCt0NmRXdjNvTjRvRzF0N0ZwTElLZjVBRm9valdxblJ1c2ZnMFVFTkw0dVBHOHlWT1NUVE5YazQxY1MxbUVUei9SZFVJVmRydGh2TUJuZ0dWZXowVWFhN25WbmpMT214RzFMa0dvQUJLMEtkT1J1VTFtVWowUU1pbS9KVklOb1gyRkhaa3hhQkhERDJoVnI3MmI5N2ZkNHJSZkRPSUc5TXJ3MXM3eHQwbWdDTWlzd3E0Z0kwSTBmL0tFRXRMU1RYSWhDV09kNFQxMS9RKzNjUm96cmlWbmZyVGJBWUJTZlJnMG04dnFUSktWVG9lcy92WlpiK1JMMVZJOEZJdlFDcE5SNUlLV2M5Q1p3RGJrbXpXUWp4ejlKR3oyQndNa29ONm1pN2tIeW00b3hrN25ucURlbGo5UVQ2VE5iRW5uMFIvcGV2RW4yVDBLSDN5MDRiTVhTektaeWJadkNkckl0V0hpa2hpS2V3dDMrUjE1SFJOSk1heWNSWFExTGFTMlZSOVpaUjVLZzhNNmpSOFA0Rm9teWx6bE5XU3kzNFN4bGpKcWtWMXpobHN0TkZ6ZkVIRTJwd1JSVEsrNFdnKzNRSjlsaWpad3BjaCtmRmNMeE9XRTAiLCJtYWMiOiI5OWY4YTZhNWU4NTgyYzVkYzkyZjY3YTQ4ZGI1NmNmODRmOGJkYzA3OTllOTUzNzRhNTUwYTU3OGFiNmU4OWNlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNDVmFydnFrT1NQdXhLbFRZK2R0bHc9PSIsInZhbHVlIjoib3g4bDVDK0l6R0c0STZWWHp2SzhjOEw1TWNXWUFGbk04Q3pJMWlTZlAvTStnalZKTXF2Zk9aaFo1cFR4TkZtNUttM295RlQvUjRBY1dQbEdlUXdQUlgxWmdQY3ErMEFpdmh2TnlzVTIyekRRK3cwK09SQVRvbkRSUURqYW5LSXhYb3kxQjA4YXA3dldCZkdGU0NUa1N3R1JVdkdLTGw5bDVTRFZIM1hOeXp1ZXhXQ2hHNElKMmNreUcraFBHc1NZamZkVDQ3WGU3Um1iOVVKL0wwWDFhL1kvZVI0SVI3UXo3YjdTRW5WT1pXSnVFcTMyYStTcUxjbEFTL29Hb1UxZU0zRzIvdUMydlUzL1k3cWhDbWRYM1FMUm1MV1JIQ2xoTjZLaUlLNWxJTDhNTHFSMmJLcjgyODVpbmtLNU11NG5Wc3VSTUhJSHVsT282bSt3OG5iQk11NGd1Um1VMzVUTjNlWC9iVWlVa0RXTG1xWHlLOUZXOFBqZ1Q3dWlzWlgwZThlVUdmKzlTakhYa0Y5a28zbTJHa2pSem51M0pBM2R1RWQzdVhObXh1YnZNNHBMMVRwY2o1UWxNRUdRTFlReFpSYzJwdlJLRlJQdUtMNUtTOU9xRjNudzQrdi9XUWdKNEk4bHFkWmFNUytOYnR3a0U4ekVUWVNVUmxyRlhxd3QiLCJtYWMiOiI4YWJhYTQ3YWEzNTQxMTRjYTQ4YWFlMjE3ZjMyNzM2OWRjMGIyNDY1Zjc2ZTUxNTRlZTYzYjg0NmVkYjk1ZWQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1127742167 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">y929FO0OR2qYd721zZmtMrJIBFsWgMiMXXHdl0OR</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2hP8eHBMtk5urSHEGR7nQ11X4spn4BWThBS2vTaS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1127742167\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1593208934 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:21:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlMxMWVTS3RHaUdGZlpSVnd3c0R6ZFE9PSIsInZhbHVlIjoicGZxSjdUekhpT0tqdU9oQTZSaC9JcVB3NjM2ZlBBRi82NTBXVDZrM0x2UUhXUWo4VHNIM1VLOEpSSXJRVDJ1WkJFbklud1dCNVhKSGZwWG1oWWVOM2lJYWc4S2duVUd0TVU5VzByb2ZPUnpsbm5YMWI1d0J0QzNNdTEyV1YzSkt6M2FtZnVOR2pWWm1IcFN6NFl0aWMrdzBHdzNhVjYybFhTOVhCV0xHWlJIMG1EYmtVeXhHQ0VXSGZ6NXJRYlcyRDdXZ3RQMWF3NGhsVVozM3EyMlRMUGoxc0ZqdXROY0pCMkxJaWNOWVZEQnJxd1dXQVRRUmYzZHl1RVhTMnd6bEVJZG9ZVklXVVlQMW1UVUora0RQZ0s3SFdrbzRmTU16UjQ2bnBFM1JYNTl0bjhaRVl4QjV3eTZOWXpkK0VYa05NNDcyOFF3L1YrbUtpUDkxQjRKSk43a2hnRXdTUUEyTkZBa2xPbHNab056dmtJOWt0UDRMVXR1bnJhUDl3NnlPcUU4d0FncFVRL01NU1RneFpSOEoyUUxMa01jaEl6cWNkc0JkaXFGRzB3N2wvRmJSQVZKTmt1MENXZXQ0NzlFaDlQK2d0UTAvVUZNRDc5VEFuVFlBRVNkclN6ZmIwK2w5aGNtMkxFQlhOc2FkR0RqMElkQ1FjTnlLR051dnVtOGMiLCJtYWMiOiJmMGY2MjFlM2Y2OTRmYzYxNDkyZDIwYTYxZWZmMWU0ZmYyNjM4YjU2MjZkNGJkZmYxNjFkM2YxMmFmZWQ3ZjJmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:21:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkNTZzByWlI1QUpPWEdSakxYUnNpRkE9PSIsInZhbHVlIjoiN2psYS96RDVVaEF4c2U5WjZIYWxZUFVESkkwdXl2R2l6L2p1NWx5YVNDcHNIemRmbTdRUmF1cWhSTldxVjBpSFBkMjBhdTFWbHZCRERKbWZjb3NtUmRVR3JSM0xycm85M3ZGQjdUTk1oMmwvZUxzVmRXWFdzNnVheVVkWm4vU1lJemJFMVpYbkNGSmQyUU1NbGZFeGJYNFJNRlZ1TTM0Y1NSK0lzTmpXcW02WkZIWGZtMHNwMFFXUm5GZzBCYnVFU0luV3ZoRDNxRExmcE52WDh3bEdxTFo0ZTRkNDF2RHNDSHUzTFNMUFlYazNud2g5TXp5RVk3emttbGFJWHBPMjRrWW9HclNoekgwTVdCelE1S29wKzYwaDhyc25najJOZVJuTDZHckNkU2pPNmhlSFEyOGZtUVdYNVl6SW9VRkZyY1dVa2JpT0xweHFoSFJGYmhRVEdHM0cva0Rza293dXJ2NXA2bGRqZ21ua3ZpYmc0VStvVndWTEtOdjhRQW1hQSs5Nm1FMVBNZE13OGMxYlFRdWF1NWkvemZua1o0VDhjby9ONktxdStuZ0J0VnYwc3RmSEIrR29mTllBYnZRbW8xbHB2TTgvMHM5T0JIQ1FFZ2xlUHorZnJwM1E2amZRVlgzQUpRUUFMbFQxYmFPU1FnNGg1OG95K2UvWU1OZGwiLCJtYWMiOiIyZGVmOTAxOWI3YjJhZTU2YWQwZWRlZDAyNDI3M2JlMGE3YTQ1M2Y0NTVmMDRiY2FjN2FhNjQyNzk2ZGFhOTVlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:21:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlMxMWVTS3RHaUdGZlpSVnd3c0R6ZFE9PSIsInZhbHVlIjoicGZxSjdUekhpT0tqdU9oQTZSaC9JcVB3NjM2ZlBBRi82NTBXVDZrM0x2UUhXUWo4VHNIM1VLOEpSSXJRVDJ1WkJFbklud1dCNVhKSGZwWG1oWWVOM2lJYWc4S2duVUd0TVU5VzByb2ZPUnpsbm5YMWI1d0J0QzNNdTEyV1YzSkt6M2FtZnVOR2pWWm1IcFN6NFl0aWMrdzBHdzNhVjYybFhTOVhCV0xHWlJIMG1EYmtVeXhHQ0VXSGZ6NXJRYlcyRDdXZ3RQMWF3NGhsVVozM3EyMlRMUGoxc0ZqdXROY0pCMkxJaWNOWVZEQnJxd1dXQVRRUmYzZHl1RVhTMnd6bEVJZG9ZVklXVVlQMW1UVUora0RQZ0s3SFdrbzRmTU16UjQ2bnBFM1JYNTl0bjhaRVl4QjV3eTZOWXpkK0VYa05NNDcyOFF3L1YrbUtpUDkxQjRKSk43a2hnRXdTUUEyTkZBa2xPbHNab056dmtJOWt0UDRMVXR1bnJhUDl3NnlPcUU4d0FncFVRL01NU1RneFpSOEoyUUxMa01jaEl6cWNkc0JkaXFGRzB3N2wvRmJSQVZKTmt1MENXZXQ0NzlFaDlQK2d0UTAvVUZNRDc5VEFuVFlBRVNkclN6ZmIwK2w5aGNtMkxFQlhOc2FkR0RqMElkQ1FjTnlLR051dnVtOGMiLCJtYWMiOiJmMGY2MjFlM2Y2OTRmYzYxNDkyZDIwYTYxZWZmMWU0ZmYyNjM4YjU2MjZkNGJkZmYxNjFkM2YxMmFmZWQ3ZjJmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:21:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkNTZzByWlI1QUpPWEdSakxYUnNpRkE9PSIsInZhbHVlIjoiN2psYS96RDVVaEF4c2U5WjZIYWxZUFVESkkwdXl2R2l6L2p1NWx5YVNDcHNIemRmbTdRUmF1cWhSTldxVjBpSFBkMjBhdTFWbHZCRERKbWZjb3NtUmRVR3JSM0xycm85M3ZGQjdUTk1oMmwvZUxzVmRXWFdzNnVheVVkWm4vU1lJemJFMVpYbkNGSmQyUU1NbGZFeGJYNFJNRlZ1TTM0Y1NSK0lzTmpXcW02WkZIWGZtMHNwMFFXUm5GZzBCYnVFU0luV3ZoRDNxRExmcE52WDh3bEdxTFo0ZTRkNDF2RHNDSHUzTFNMUFlYazNud2g5TXp5RVk3emttbGFJWHBPMjRrWW9HclNoekgwTVdCelE1S29wKzYwaDhyc25najJOZVJuTDZHckNkU2pPNmhlSFEyOGZtUVdYNVl6SW9VRkZyY1dVa2JpT0xweHFoSFJGYmhRVEdHM0cva0Rza293dXJ2NXA2bGRqZ21ua3ZpYmc0VStvVndWTEtOdjhRQW1hQSs5Nm1FMVBNZE13OGMxYlFRdWF1NWkvemZua1o0VDhjby9ONktxdStuZ0J0VnYwc3RmSEIrR29mTllBYnZRbW8xbHB2TTgvMHM5T0JIQ1FFZ2xlUHorZnJwM1E2amZRVlgzQUpRUUFMbFQxYmFPU1FnNGg1OG95K2UvWU1OZGwiLCJtYWMiOiIyZGVmOTAxOWI3YjJhZTU2YWQwZWRlZDAyNDI3M2JlMGE3YTQ1M2Y0NTVmMDRiY2FjN2FhNjQyNzk2ZGFhOTVlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:21:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1593208934\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">y929FO0OR2qYd721zZmtMrJIBFsWgMiMXXHdl0OR</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}