{"__meta": {"id": "X3062039b9d119cdd2d2ab48984e58c3a", "datetime": "2025-06-28 15:44:45", "utime": **********.938128, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.462289, "end": **********.938145, "duration": 0.47585582733154297, "duration_str": "476ms", "measures": [{"label": "Booting", "start": **********.462289, "relative_start": 0, "end": **********.861721, "relative_end": **********.861721, "duration": 0.3994319438934326, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.86173, "relative_start": 0.3994410037994385, "end": **********.938147, "relative_end": 2.1457672119140625e-06, "duration": 0.0764169692993164, "duration_str": "76.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45694240, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02522, "accumulated_duration_str": "25.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.889822, "duration": 0.02444, "duration_str": "24.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.907}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.922435, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.907, "width_percent": 1.784}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.927584, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.692, "width_percent": 1.308}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-835304019 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-835304019\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1872255194 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1872255194\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1927309237 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927309237\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1319233452 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125105407%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlYwZ2lueUUxSXFHaVdBbWZIckprU2c9PSIsInZhbHVlIjoicEt1Q3hveXBPclFEVUExcHprYWNDelRwVzNhM2g1ZlBNczVHaW11bVhKMzh4UUV0R1hLcjB1TTgyMUF2dnZYZUJjanBTL0NQek44SlJOZTJjS21MdDdqbDZxaFE0dVRmM3Arb0kvTGtFZzRncWVOeXFFemQzaG9KenE5NU16enhNRkZDYWp1ajZGZFpoWUcyaDQvekVOYjIzUjBvam1nc0dUREF5RDJiaUtzcXAwV1hteDFMcjBKN1M1TVVIU0RZSWo2NjlMN1IwWjVJTUFQLzJyRnFwOVBQRlVRVG02eFJwaGgrN01jVE9CZlpwK2R1ZkhXZkkxOGZQQlFOSkhXd1hRdjVQSTYvWldIaDhhYkVCbGw2V0FxUkJqM05mbHR5SVFCM01zd3dxZTVMSmVUR3dudm5zd0hPMExNN2NMRjFvNzE2THZrblNjRGdEQmJDTTVTTUFydkFCK29tZ24wZUo4TWxyMXBMOWxpZnBRck9Ib24rczVEUUJRdGJ0WVVQUHBROWwwSDR6eVVncElYNTVZalNuNWZuSjh4NVQvaGdPL3pIeU9VU1N5YXdFSS9tbkVoY01BVklXU1BCa201ekRlNDBKYTR6ajRrMC9VS0lOTG1ycThxY3ZPNHNOUm53Znd4Z3VrZWFTMTdZUHI1V2NJdWU0bERwbGhjOVJDeksiLCJtYWMiOiJjZDAxZDA3MTA2N2VmNGVjOWViN2RjN2Y0ZGY2YjMxYThjNDNmMDIxN2E4ZTVmMWNkODE3N2U4NDU4ZGI5YzVhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZMZ2lSL0dxbUExbXR4WC8xTEJGa0E9PSIsInZhbHVlIjoiYlNXYWY4SHZTUnE2NzhMOC9nTnZlRC9RK2RKdEVwelQwMlBzeDRESU1KL3p4TlJaOEQ3U3h0ckFIRWRGNFNxUFMyUXAvTVoxSzlPQURoY3h5KzZhbDVCeXR6aHZYVTZ6eXRwVVAxdXE1SmloWDgxUUtpeVVzdFg5VEg5WHczVy94Q2RhZWhNU3dzcVlydmJaZFkvZlNSUHkybmtBenAvQStpaDRwMlkzUUZHZnFaeWNlb2xIR2hNckhISUZPZWNCd0tIenI2OHViK0FRNU1zRlBRbFlqUDRxU1cvT1VUS2Y4TkozM2tuS1NySXpsS2dGRmhmSU5iN0h0QlNtdlJxY3VIZnA4YUlqZFdkOHpHeG1YYi9ZbmJId2ZHVWNLbjZ2Q2dFb09KcEpQN1d0V295bnJYNEdHaUtkcFVud0hBemxPZHdTd2dBVTNKcXlSNlhYZ2dkWmVsTnBxMkxGV2JwdzNLTEI2WFBGT2hDczhkbnJPZjJwSWJKOGVjTFZQYjZGRkM4dU15NFNpWFVnMmd3TWVtY0NVWFkzZFdaTlY3aFNUN0xzSEFGUjY5dFZJMHA1SmdlVjJyQXNDcG5iNUM3eURKbE1RWjc0WkpOV0U0K2JIbDlCQXp4M0pZU00xVTRqTUMzU21KSWoyWnNjTHdwaUl5TzVZcDc5T1BEM1ZDM1MiLCJtYWMiOiIzY2FkZWIyMDMzYjY5ZmRkZTlkZWM3ZDIyYmUxNDljMjBlOThjOGM2ODIxMGJkMTU0ODVjYjUzOGVhZDRmYTMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319233452\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1122746060 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1122746060\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1839135579 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:44:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVuMERUOHNSMjdDZzY0YlF3cStQcUE9PSIsInZhbHVlIjoiL3E1RmtLa3FYK3NmU2VQVTZIRFdBUEVYK01hNmdzRUZqTVhkL2pZRWVnNWErc0wvZUlubERXdVNyYTcxVGZWN0RVczFrUTVQS1ZlR21aaHNtQmJJdnFXeWJJdVFIZ1YxUHMxbytHNGxvL2NhTm85SVExMlp2TlBoUEpXekhZZ1ovMVlpMmVZRzhDeS9SNlRtQWZFcU5hWmhxSWJQTnkyNzYzNmpFWUFYbWh1K05jeGZtYnpaMFc0c2xQYUV6cE5jaHcvYzVLNm1LZWxCcVdFRlV3UkljSHFlK1d1bFRzYVZ6dEJtOFk5REdpLzZLc2wxNUJWN1lTYWtzSEgxa2Q3Z21xczNkQnR4Qy9rZlBrSUc1NVFpYkdGcGFrQWplUDBUK0E3UkpWWlNPRkppSHpYYlZudzdyUUN2UFN5TXlpUzcwdE8vb2xwODY1b0I4eHRKUDBrYU9PbTRFL09tZlFDa1BXQlBrNlVONjA5YXlDSzk0T1QvV2VndVdVWHdJVG9HRTJQUzNSd2tCcmp5bHM3YUR1aHlER2pwZGY2VTd3OWZRcko1ZkhyN2dnY2xHVnFUYjU2SlZ3VktaOERiZzlKS1R5enRiWlhyeDg1Slg2Wm9aSXd0OUZ0RlhFYjVzcmo1dUgyMDhKd0I3a2t2VGpWR3U1M05qamVlNTJNV3VZZG8iLCJtYWMiOiJkZjFmNDI3ZTVmYjRiMjBmNGY2Njk3YjU0Y2M0ODZiMTU3Y2Q1Yjk5MWZjNTcxZDhiMzI1ZWM4OTM2MjIyYWE1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:44:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlltN0Z3Rkt1azE4eTFaWnZkZmppaUE9PSIsInZhbHVlIjoid0NYR2o3UHYzVmRHUVd4anFDWGtvemdFeUVHZFgwaWZBR240SWhGRW9LWndVYTlVZEYyMSszZzRNSUxVL0djSjRnR0krRGp0ck45NHc0UHEzRi9DU0g2d1luR01BWWZTWW0xZWNwZGRXWEp5a2tsOXd3QXUxUmlZVHVVd2FMWVMwSlp6ckRta1FEQWJZaHp5QWtkMXVHODVCOVNZOURDN1V5Nis4cUxhOG1oS3VRNWJ1VHhCZFAwaHNEZitTRFVLR29iVXY4RGJueFdiRW5XY0pGanAybGswV0t3YnMyaFc3ekkyRUNCbmxZeHNtUFJSWFdXYWpJNVhadlhmRGtuSXlMME9RcHJmN1BScmZVZEdqUkE4NDF1KzVERWhyZW9GdmNmdVkwdWlPTVg4bmJBUGhWTW1rOG83N2t2Z2RETUhEMll5K2xRR1BoNEhnRkp0dHpVdFR3TCtObTdjcXdDWUNBL2lYTm1CRXNoQU0zdzZmV2pKL0dxbk5ONlNYZGlyNm5DWmNhUzNHU0pIUHozV3UvbWhaTHFLTStYR2k4eWxLZ28waStUWW1NSGdiY2FjbWp5RUR3ZzJlcG9FOURGYVNEMURFKzR1cnFlajBsLzZiZHhhZUZqZndWZU9XZFJGRGVDNzJuVm5xT0NzZEhqQmVQaHlOV1d0ZXFkRGhXVnIiLCJtYWMiOiI0YWI5N2ZiN2U1NjdlNzZmNDc5MzE4MTNlNWY5NWEwODBkZjI3ZDZhNDdmYzJiMDZlZmMzYzc0MzI3ZGQ3Njg2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:44:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVuMERUOHNSMjdDZzY0YlF3cStQcUE9PSIsInZhbHVlIjoiL3E1RmtLa3FYK3NmU2VQVTZIRFdBUEVYK01hNmdzRUZqTVhkL2pZRWVnNWErc0wvZUlubERXdVNyYTcxVGZWN0RVczFrUTVQS1ZlR21aaHNtQmJJdnFXeWJJdVFIZ1YxUHMxbytHNGxvL2NhTm85SVExMlp2TlBoUEpXekhZZ1ovMVlpMmVZRzhDeS9SNlRtQWZFcU5hWmhxSWJQTnkyNzYzNmpFWUFYbWh1K05jeGZtYnpaMFc0c2xQYUV6cE5jaHcvYzVLNm1LZWxCcVdFRlV3UkljSHFlK1d1bFRzYVZ6dEJtOFk5REdpLzZLc2wxNUJWN1lTYWtzSEgxa2Q3Z21xczNkQnR4Qy9rZlBrSUc1NVFpYkdGcGFrQWplUDBUK0E3UkpWWlNPRkppSHpYYlZudzdyUUN2UFN5TXlpUzcwdE8vb2xwODY1b0I4eHRKUDBrYU9PbTRFL09tZlFDa1BXQlBrNlVONjA5YXlDSzk0T1QvV2VndVdVWHdJVG9HRTJQUzNSd2tCcmp5bHM3YUR1aHlER2pwZGY2VTd3OWZRcko1ZkhyN2dnY2xHVnFUYjU2SlZ3VktaOERiZzlKS1R5enRiWlhyeDg1Slg2Wm9aSXd0OUZ0RlhFYjVzcmo1dUgyMDhKd0I3a2t2VGpWR3U1M05qamVlNTJNV3VZZG8iLCJtYWMiOiJkZjFmNDI3ZTVmYjRiMjBmNGY2Njk3YjU0Y2M0ODZiMTU3Y2Q1Yjk5MWZjNTcxZDhiMzI1ZWM4OTM2MjIyYWE1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:44:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlltN0Z3Rkt1azE4eTFaWnZkZmppaUE9PSIsInZhbHVlIjoid0NYR2o3UHYzVmRHUVd4anFDWGtvemdFeUVHZFgwaWZBR240SWhGRW9LWndVYTlVZEYyMSszZzRNSUxVL0djSjRnR0krRGp0ck45NHc0UHEzRi9DU0g2d1luR01BWWZTWW0xZWNwZGRXWEp5a2tsOXd3QXUxUmlZVHVVd2FMWVMwSlp6ckRta1FEQWJZaHp5QWtkMXVHODVCOVNZOURDN1V5Nis4cUxhOG1oS3VRNWJ1VHhCZFAwaHNEZitTRFVLR29iVXY4RGJueFdiRW5XY0pGanAybGswV0t3YnMyaFc3ekkyRUNCbmxZeHNtUFJSWFdXYWpJNVhadlhmRGtuSXlMME9RcHJmN1BScmZVZEdqUkE4NDF1KzVERWhyZW9GdmNmdVkwdWlPTVg4bmJBUGhWTW1rOG83N2t2Z2RETUhEMll5K2xRR1BoNEhnRkp0dHpVdFR3TCtObTdjcXdDWUNBL2lYTm1CRXNoQU0zdzZmV2pKL0dxbk5ONlNYZGlyNm5DWmNhUzNHU0pIUHozV3UvbWhaTHFLTStYR2k4eWxLZ28waStUWW1NSGdiY2FjbWp5RUR3ZzJlcG9FOURGYVNEMURFKzR1cnFlajBsLzZiZHhhZUZqZndWZU9XZFJGRGVDNzJuVm5xT0NzZEhqQmVQaHlOV1d0ZXFkRGhXVnIiLCJtYWMiOiI0YWI5N2ZiN2U1NjdlNzZmNDc5MzE4MTNlNWY5NWEwODBkZjI3ZDZhNDdmYzJiMDZlZmMzYzc0MzI3ZGQ3Njg2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:44:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839135579\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1014441499 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014441499\", {\"maxDepth\":0})</script>\n"}}