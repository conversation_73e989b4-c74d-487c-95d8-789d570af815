{"__meta": {"id": "X0e18e8c6858c2f2a8b620bc2fc2ba7b7", "datetime": "2025-06-28 16:02:28", "utime": **********.375983, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[16:02:28] LOG.info: Enhanced POS Add to Cart Request: {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"price\": 2.99,\n    \"name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\",\n    \"is_manual\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.366802, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:28] LOG.info: Manual product check passed {\n    \"is_manual_value\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.366974, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:28] LOG.info: Product found successfully {\n    \"product_id\": \"2299\",\n    \"product_name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.369718, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:28] LOG.info: Product added to Enhanced POS cart successfully {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"cart_count\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.369854, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751126547.98199, "end": **********.376, "duration": 0.3940098285675049, "duration_str": "394ms", "measures": [{"label": "Booting", "start": 1751126547.98199, "relative_start": 0, "end": **********.323007, "relative_end": **********.323007, "duration": 0.3410170078277588, "duration_str": "341ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.323016, "relative_start": 0.34102582931518555, "end": **********.376003, "relative_end": 3.0994415283203125e-06, "duration": 0.052987098693847656, "duration_str": "52.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46011856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2015\" onclick=\"\">app/Http/Controllers/PosController.php:2015-2120</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00253, "accumulated_duration_str": "2.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.355181, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.636}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.364729, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.636, "width_percent": 13.439}, {"sql": "select * from `product_services` where `product_services`.`id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2053}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.367589, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2053", "source": "app/Http/Controllers/PosController.php:2053", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2053", "ajax": false, "filename": "PosController.php", "line": "2053"}, "connection": "kdmkjkqknb", "start_percent": 77.075, "width_percent": 22.925}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1439389414 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1439389414\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1920734002 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1920734002\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1839537686 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126544321%7C12%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlTM0JlZUwySWZETzRjRXpWek9tQXc9PSIsInZhbHVlIjoiYUtXa05OeE8xbTFaMmFyTkorQytDc09NOEVNWnRnR285UFAwd2lyYlZLVmVpTnU1K1RPOGovRC9mbjUzekZ2ZCs3VzYvMlRhTm9xSGpQOHFXcWI4UlhCeXBJcnhLMFlsK3dONEJrWHlxQTJZcTNLWnprZkFNREJNaHY1bG41TzJWRWJ1ekh6WlVZMERUSE5QL0k0NWZkc1M3Y1g2VWY1cUM5TDJOZFdsYzNNZTBHZjM5elhqRFNIMXp2M2xncWpMTUViQjFIWVFYMTlQSzFMaDJtMWNNT3hoMkt0emV1ZlhTQ05FZ1VZQ3VkVDVJcGd2Zk5VYXR0UU9MYjE5M1llY2JHNkJ2ajVkZGo4ZVRsLzBWUWQ0bkg3eVd0TytJck8xeFRxWll1d2JQYVdNbDFKWXIramlqMzA1dGRPamlGRlpGQUg3L0JpK2Q5UUpWK1FQRzdZL0NISm9NOUM3YUtFMXEwZm5XTGdWeFdsY1ZIK20vc29OWWZnbW5MRXlFeWV6aFdrQVMvNk1pdCtWc2xrN0gvN0owSWExdk15UXBSVDc1cHNDbjYyempNbSs2dG9xMXh3VlRHaXFIcjB1OTNtbmNyRTVVY1NrQUZnZlZ1amZZQUNKbUJXT1FtbzdJNDJlcTlXb0s2UGZYdnhJKy9QcGJ0SnJrN2ZRaTg5aTI4Y0wiLCJtYWMiOiIyN2I0YzI2NTczZDZiYmY1NmUxZjc1MGU1YWEyYmY0NzU2ZDA5MDQxNzE2ODlhNjU4NDA4YzM5OTNhYmMzMTRmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJ2MWNJZU5ZUjBxcmpaY1hRanVrUHc9PSIsInZhbHVlIjoiTHYwN3RpTnRyOWs1SitGL2lNd000TGZnOTIrcjZncnBUajdUdXhQTHQxR3FjOG45NVdRNms4QlMxejNtZFNNY3pqVUJlK2phcVpXb0E4S0xWVDZnYTVIUEd5aitxQkJUVjQvREVobnUvRWUvYkhXbHJmWlpvTVpzanNaMklKYy8yc2hCVU1kL3BQVHBGMC9SK2pwL0dLdFgxcWwxdWpIY3hDVEcvTzNVSis4L1NLZ0pKaFdCazZSWmNuckFrZkVuSGNVeEtVMDlENmJqcHZNR09PMzd6NzZvNXc5eTduU295TFVHUlh3VHlEQ21CVllYODlQeEp6Rm9Lbll5UGRqbGk2andUS1hmZEJZVzY5Y1R0OXFsQ1JGWkwwU1p2M0dtMlk2SWhlZlFWUVNDSTVXMlhFZDRtTm5GbFZ2NWxCZUdBTld5cUUwckhZMUNQSkxYN2ZPSjV2ckxYODQ1RUFWbmJkcng0cFMyVHV0R05XOW5oV0xiZXVFKzNNU0dkcnZtZm82TlR6bXo4ZHRwV3dZRURQaFc3Y2dhK0tyN1dJTGJWQUtOWXRsZUYrTHk0TkhRanRCOWZ1cHhUTjd3aUNzdSsxYjc0aWF2WTJsRDArWHp5VkNCOFN4cC9hbTRGUzFyMkNuVkZyVlVTQjdjdGMzTVNwVk5qY0Vaa0Z2VEFaaCsiLCJtYWMiOiI4MTVkZjc5YWE5ODY1NmZjZTg1ZTM5ODQ3YjhlYWZmMjVjMzA5MDZhNGYxYmIzYmRlYTI3YmE5YzY1NmQ0Y2ZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839537686\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-680049005 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-680049005\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1673768601 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJHYkxEd2RqSmwxcnR3ZzJUTCtnR1E9PSIsInZhbHVlIjoiUURxNWRDWmtlZlNJdjNmK0JVeFdrdkJGSGZjRWtleFBlTmRwUzFVa0Q5TUtaa3dheG4xYmVQWUxGZGZvRkIvenJvUk1WdFRMWGZvRzRXZlplTm1NclJkMHc3NkNjdEhtTkgrRkhKWnNZZFRVRkd2QUNZV0xJVWtjbWhEZnRDbTlwNXBHQUJnamtjTlBDUGo4TEJVemR6WENrdVljYTlLYzhacGhzVFh3bENxeEFEdWVqeWk4V3VqU2hENGJjM2tScDJDeUx6QWRwNWVRL05YVm1nclpDMG1qMnRsQ2ZITC9mYkJ1VEdpMWdzNzkvSUV6dE9nUDUwTVNDb0pmaGJ2V1AzemlqcFJybmRmY2NBcE5pN0JYY2dra1NZc3diNDdqbkNRZWczaFkzTmdTWi82OVZrRWJsTU5XWG4vdDU4cWJoNDFTNlZMamhuTUlUMk00RUlqcmN1dmNVRHc5cHlaclFLR09HRU5Zd2t5M0xralp3SVg0K0o1K3p6d0pjbmdYM0d3V2lHYUN3dWMxaTdlMUtXOWtqMnkzSjd3ZWJyN0Z3ZDFUT3I0WHpKbWRmS1JWOTRFMFN4NmVoYUNoTXY4b25Wd0h3TU1CdXhWV3N4RUxoUjVySXY0TlZlcDRTVkZiY1REYW1nWTlBSzl5UGFwTzdrQUNMYS9tZk9KY3VqYzYiLCJtYWMiOiI2OTFjNjM2NGUzODYxYmZhYzgyNDVkMDczZmRlNmViZTQ4OTI5OGExNDY2NzJlYjNiMGY3OGIwZGVkYzc4YjFmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZXM2RkblNVOXhiQzlEQjM4bmRONmc9PSIsInZhbHVlIjoibDAzWExFcUtuNmRPUzFmT1RPUlU4ZVhkYXk3cjhoaDl0bXk4cEJNZWdXalpDZ3YxYkl1RFQ5djZxaVhsS3l4dWpFV0NWTEt6WFV3MWRwVlpJTjAreDQzVjdlYjNDd3RJc044VUlGTkpPa2ljNzUwYzRuazRsYjUrbE5KZjZUUXYwTGF2Y1FYUzBoa1RpK29sVWxIUGs5cTY3NGdFbVJSekpQSHFCQjhDb0c3WStLYWg1M1ZXamZVOFVTcFNhT3NFRXZTVkZpOHpPSEZuNUdpd1BuVDhISzdXSmpFQU9PaUFSek10bnZxazFoaGVlRmoyQktlMEE2RjFKUnpGODZ1TjZneFhmL3QzcVRSRE56L2JTR0h6c2xpSmtPTFdFRnhxWk5kK2VOWFcxS0xSeGdHWUo4aml2d2gzNE9KYnhoT2kwSjUwRGRWNWQ4VEhvTTNSQ01GWGZObSt2bEFlZkVwZDhWUW5mQVpvMUJ5dFhDM3ZENEQ0UEVRUGxFL3dMdlN6UTNHLzltMTdlUWY2OEV4ZStwMjhYODQ1am4xVi9ReVA0N2ViR1c1Z1d6S1RCODlVTE5KdGZiSldFTmtCQyticWVkL0dNelpqKzlZdWVobVFLelgzcVNMYUwzV2xkZXNRT1oyZWYrVFRtc3F0SFkwejJrbnNLUS9RN1h2L1diakkiLCJtYWMiOiI1YzBiM2E0NjMxOTlkY2UzYWFhYzVmNmY5MjcwYWJhOGY0OWFmM2NiZmMxZjg4ODQ5ZDVkMTljYWYyZWU5OTFjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJHYkxEd2RqSmwxcnR3ZzJUTCtnR1E9PSIsInZhbHVlIjoiUURxNWRDWmtlZlNJdjNmK0JVeFdrdkJGSGZjRWtleFBlTmRwUzFVa0Q5TUtaa3dheG4xYmVQWUxGZGZvRkIvenJvUk1WdFRMWGZvRzRXZlplTm1NclJkMHc3NkNjdEhtTkgrRkhKWnNZZFRVRkd2QUNZV0xJVWtjbWhEZnRDbTlwNXBHQUJnamtjTlBDUGo4TEJVemR6WENrdVljYTlLYzhacGhzVFh3bENxeEFEdWVqeWk4V3VqU2hENGJjM2tScDJDeUx6QWRwNWVRL05YVm1nclpDMG1qMnRsQ2ZITC9mYkJ1VEdpMWdzNzkvSUV6dE9nUDUwTVNDb0pmaGJ2V1AzemlqcFJybmRmY2NBcE5pN0JYY2dra1NZc3diNDdqbkNRZWczaFkzTmdTWi82OVZrRWJsTU5XWG4vdDU4cWJoNDFTNlZMamhuTUlUMk00RUlqcmN1dmNVRHc5cHlaclFLR09HRU5Zd2t5M0xralp3SVg0K0o1K3p6d0pjbmdYM0d3V2lHYUN3dWMxaTdlMUtXOWtqMnkzSjd3ZWJyN0Z3ZDFUT3I0WHpKbWRmS1JWOTRFMFN4NmVoYUNoTXY4b25Wd0h3TU1CdXhWV3N4RUxoUjVySXY0TlZlcDRTVkZiY1REYW1nWTlBSzl5UGFwTzdrQUNMYS9tZk9KY3VqYzYiLCJtYWMiOiI2OTFjNjM2NGUzODYxYmZhYzgyNDVkMDczZmRlNmViZTQ4OTI5OGExNDY2NzJlYjNiMGY3OGIwZGVkYzc4YjFmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZXM2RkblNVOXhiQzlEQjM4bmRONmc9PSIsInZhbHVlIjoibDAzWExFcUtuNmRPUzFmT1RPUlU4ZVhkYXk3cjhoaDl0bXk4cEJNZWdXalpDZ3YxYkl1RFQ5djZxaVhsS3l4dWpFV0NWTEt6WFV3MWRwVlpJTjAreDQzVjdlYjNDd3RJc044VUlGTkpPa2ljNzUwYzRuazRsYjUrbE5KZjZUUXYwTGF2Y1FYUzBoa1RpK29sVWxIUGs5cTY3NGdFbVJSekpQSHFCQjhDb0c3WStLYWg1M1ZXamZVOFVTcFNhT3NFRXZTVkZpOHpPSEZuNUdpd1BuVDhISzdXSmpFQU9PaUFSek10bnZxazFoaGVlRmoyQktlMEE2RjFKUnpGODZ1TjZneFhmL3QzcVRSRE56L2JTR0h6c2xpSmtPTFdFRnhxWk5kK2VOWFcxS0xSeGdHWUo4aml2d2gzNE9KYnhoT2kwSjUwRGRWNWQ4VEhvTTNSQ01GWGZObSt2bEFlZkVwZDhWUW5mQVpvMUJ5dFhDM3ZENEQ0UEVRUGxFL3dMdlN6UTNHLzltMTdlUWY2OEV4ZStwMjhYODQ1am4xVi9ReVA0N2ViR1c1Z1d6S1RCODlVTE5KdGZiSldFTmtCQyticWVkL0dNelpqKzlZdWVobVFLelgzcVNMYUwzV2xkZXNRT1oyZWYrVFRtc3F0SFkwejJrbnNLUS9RN1h2L1diakkiLCJtYWMiOiI1YzBiM2E0NjMxOTlkY2UzYWFhYzVmNmY5MjcwYWJhOGY0OWFmM2NiZmMxZjg4ODQ5ZDVkMTljYWYyZWU5OTFjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673768601\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1692790464 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692790464\", {\"maxDepth\":0})</script>\n"}}