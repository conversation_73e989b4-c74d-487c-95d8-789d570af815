{"__meta": {"id": "X9c5824ea7001f1a067f30f79a27c33f7", "datetime": "2025-06-28 15:43:24", "utime": **********.968194, "method": "GET", "uri": "/financial-operations/sales-analytics/customer-analytics?warehouse_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.457592, "end": **********.968208, "duration": 0.5106160640716553, "duration_str": "511ms", "measures": [{"label": "Booting", "start": **********.457592, "relative_start": 0, "end": **********.858845, "relative_end": **********.858845, "duration": 0.40125298500061035, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.858855, "relative_start": 0.4012629985809326, "end": **********.96821, "relative_end": 1.9073486328125e-06, "duration": 0.10935497283935547, "duration_str": "109ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46328720, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/customer-analytics", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getCustomerAnalytics", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.customers", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=359\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:359-488</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.04305, "accumulated_duration_str": "43.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.892683, "duration": 0.02, "duration_str": "20ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 46.458}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9212751, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 46.458, "width_percent": 1.022}, {"sql": "select count(*) as aggregate from `customers` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 368}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.924093, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:368", "source": "app/Http/Controllers/SalesAnalyticsController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=368", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 47.48, "width_percent": 0.813}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 378}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.927377, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:378", "source": "app/Http/Controllers/SalesAnalyticsController.php:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=378", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "378"}, "connection": "kdmkjkqknb", "start_percent": 48.293, "width_percent": 2.369}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 387}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.930579, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:387", "source": "app/Http/Controllers/SalesAnalyticsController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=387", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 50.662, "width_percent": 0.976}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30') and not exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` < '2025-06-01')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 406}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.932893, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:406", "source": "app/Http/Controllers/SalesAnalyticsController.php:406", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=406", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "406"}, "connection": "kdmkjkqknb", "start_percent": 51.638, "width_percent": 3.298}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30') and not exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` < '2025-06-01')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 421}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.935787, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:421", "source": "app/Http/Controllers/SalesAnalyticsController.php:421", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=421", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "421"}, "connection": "kdmkjkqknb", "start_percent": 54.936, "width_percent": 0.929}, {"sql": "select `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`, COUNT(DISTINCT pos.id) as total_orders, COALESCE(SUM(pos_payments.amount), 0) as total_spent, COALESCE(AVG(pos_payments.amount), 0) as avg_order_value, MAX(pos.pos_date) as last_purchase_date, MIN(pos.pos_date) as first_purchase_date from `pos` inner join `customers` on `pos`.`customer_id` = `customers`.`id` left join `pos_payments` on `pos`.`id` = `pos_payments`.`pos_id` where `pos`.`created_by` = 15 and `pos`.`pos_date` between '2025-06-01' and '2025-06-30' group by `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 766}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 427}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.937978, "duration": 0.01213, "duration_str": "12.13ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:766", "source": "app/Http/Controllers/SalesAnalyticsController.php:766", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=766", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "766"}, "connection": "kdmkjkqknb", "start_percent": 55.865, "width_percent": 28.177}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 776}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.951854, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:776", "source": "app/Http/Controllers/SalesAnalyticsController.php:776", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=776", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "776"}, "connection": "kdmkjkqknb", "start_percent": 84.042, "width_percent": 7.526}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 776}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.956364, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:776", "source": "app/Http/Controllers/SalesAnalyticsController.php:776", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=776", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "776"}, "connection": "kdmkjkqknb", "start_percent": 91.568, "width_percent": 6.667}, {"sql": "select `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`, COUNT(DISTINCT pos_v2.id) as total_orders, COALESCE(SUM(pos_v2_payments.amount), 0) as total_spent, COALESCE(AVG(pos_v2_payments.amount), 0) as avg_order_value, MAX(pos_v2.pos_date) as last_purchase_date, MIN(pos_v2.pos_date) as first_purchase_date from `pos_v2` inner join `customers` on `pos_v2`.`customer_id` = `customers`.`id` left join `pos_v2_payments` on `pos_v2`.`id` = `pos_v2_payments`.`pos_id` where `pos_v2`.`created_by` = 15 and `pos_v2`.`pos_date` between '2025-06-01' and '2025-06-30' group by `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 802}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.960639, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:802", "source": "app/Http/Controllers/SalesAnalyticsController.php:802", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=802", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "802"}, "connection": "kdmkjkqknb", "start_percent": 98.235, "width_percent": 1.765}]}, "models": {"data": {"App\\Models\\Customer": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 11, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/customer-analytics", "status_code": "<pre class=sf-dump id=sf-dump-959900661 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-959900661\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-182478215 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-182478215\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1953577900 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1953577900\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-484291016 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125396906%7C17%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlMydjF0RnZGTXdiZXhhYS9CNkgwTFE9PSIsInZhbHVlIjoiVW9Yb3RXWFMybk1RMXdMZ1FzeVIwanBBMU9zWEo2RzJ0bGhDbUx0Q2xPVGVJVjl0b05NZEljNTdTb0hTMDdtd0FCSnp5aUZPYjlJQmk5MkxGSTRQenJMSElHMDJ5ZUNTa014NmN4dUhmbnFOSkpUMjlLYTlxTVpLMk9vZDQ1K0FrT1JjMDFqN05aeUhraSsvSVFjb3RmeWF5bzJ5ZjgzTzBZS2NLSDMrUGVybVRyNFVwbHRyZzc4SUowazZ6dGhrdXFKR2tXMDJBTWh6YzFoVTN5cTdxM1lnTHliVEVCcmgzYnZMWklJZjN2VUxtc1JzTHVTU2hMcVpTT3VWOWMveFpPRWdmNk4rOHo3L3d6OW5NeFNIeWpBWkgxSGJTMG5Xd0lJbUI0ZXYrRjVBVm9GUnl1blZkWllDekUwK2RGQ3hDUTBXK3EvQVV2OFAyRFpxRzJYaVQyckdmUGdLK3M1RFd1dDZpL2lTU05iNnZJQTVjY1c5cW4zMUhMcWlCQnFOMjBkTW5tcEx3UUVrYnhKYnpWVUU4QktTSnR0bmNKTmx1dG9XbkVlRGVNYnJiTGpCWTgvbHIxWGQrRnFCam1Fc09tdDlhM1lBb2FzUHVHSlAzTzNpZGd1ZlNBWjdnUXMrRlhvalhxT0hvRjdlZENSV25CSUpkUzBuMXplOUFvRk4iLCJtYWMiOiJlOGNhMGJkNDcxYmYwNGNhNTlkYTQyYWQ0NmE2MWFkYmRhODMwNmFiNzQxZmEzYWY2NjY3ZDM1YjYxMDZmMzlmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjRhVEtUa2dJYWcxa1J0aC90VDcycUE9PSIsInZhbHVlIjoici83bUx5MnAxQnowS0JOU0xUNzBzZXN5OTY0VW4ycDNnTzgvTjdUd0VBOGhiYWZLMGJ0bzljWVFTNW1rdHhmK3hKV0pRdk5rN2hOcCs2L2twWlhSWDNiN3V0ZXNaRDA0OEZhZ1pHWkY2OHhzWE1tZDZOSXE1bDZqeHlFWFh5RjBFMFFBN3VJUDRxMm9YaFJTVU9SQVpzNUk5SkVKQUFWZ01RZ2VpL2w5ZDU0THluTzZqUnNacXdUS29qQmt6d2VWNFlLZGJlYVY2clovTHY5aHc5a3Q0Nk11ZHdVUVh1R2pMSi9pL0tpQ092RmtQelNOaGhaR1BhSlByemhsbklHT0tCbGlET0VwZGlQK2F3amdSbXpRUyszT2Z5MW44VExuU1lXZnJQa2RLeUswKzBQRVZCTkpvdzErNzBrSndLdFQySGdZVjJwYnlyZCtkYmhOTGh0VkVKZURZQ004cXNIZFl3VDZuTzNhZjRKNWQ3QmsvNXJqUTA0M0hPY0loMjhMSVFXT29IblFsaG5lSXdwWjBrMXRJb1NzblZid3BXRWFMN0VJVWdoL2hvNFlPZzdEOEttWkUzUG91cjVGSzA3TytldWNYTFQzVnV4TC8zTTJQTHlGUWFTdFoxUy8vYkF2aWFTQjN3VjgvVUNNNkp5OFdabUNjL2c0VXVLVjFrbkgiLCJtYWMiOiIwMDg4Zjk3OTkzYjU2ZGRkYjY2OGM1NzNlODE2ZjYxNTdlYTNkMjlhZWM5ZTAyNTUwOGQxYWNjN2RiOWE1ZjIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484291016\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-991798738 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991798738\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1101890798 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:43:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNlR1BwNmdQd1YxWUxJMmM2blVxbHc9PSIsInZhbHVlIjoiYm1reEpyRU5JMjFBMHBKSzYyTUJiUVF5VTBpSkZoMU9Gb1QrWlRDSEx4UnN5V1NwQ250cFFTcnJFRkRCQTJHSEpEVkJId0dnNW51YXlqdnZOQlcwYSsvc2h6TkVJQTBvVHBmWnRvdm1qbW83ZTduZHhqQ0ZUaEpZQkVrZDNzdjBCeWxGUHBQNmRCUVRHVUpXSjAzd25hYy9jYU9ORlhMRTZuRzc2RitObW9xZ1pqUWcyRGRuc09TYXpqajdvemYxQ0xWdCticko5U2RGQ1Z3TGRSTWhHdnR4bVdRU2RRL0JENjBzVzFLMkNKY3g4blJzcUk3WjNramNmYmY3NXJYc3E1Wm9vaVZlOXJDNzUrWmZJNnNmeGZvdFE0aTFiMWU5dXZRMVpYM2YyVVh4U2VHYmhOKzlOeVRhN2QrL04wNCt3YzNqMVkrVHdzemF6alpTajI1dXlDYTFpY05zamw3bTJpdnVZSlAzR2p3ZG05N1dLYkdORkFvVHRER3RkZndOYWxXOHBKYVJNTFZHOUhZZDduZDRrdy9ZQitpckRSbDVJSWhKUGp1eEFNVmNCVzZ0ajlVWi9JMUNPRXR3NlNQTU1oMVF5T3ZqQmpnS1kxZmRKQU9sZlhITzc2ZnAzMGhiWkMvZ3FKUXhJTmh2L2ZranVrcDdGZjNkQ0JobE1ySWciLCJtYWMiOiI1MGJjMDQ2OGZhNDJiNmUzZTIzYmQ1ZWUzZmMwMTIwNjczNjY1N2ZjYTVkMDU4MjhkMzhhODFiMTk5ZmVkZjk3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImtzLzlPbGVRVUgyaUZZcE9LNnBEMmc9PSIsInZhbHVlIjoiSVgxejQxV3M0czloSVhmcXloV2Z6cTZoUC8xT285eHp1VHZaSzl0cmREdnloKzlhQ2ViYVFSOHdkbG4rb2FNTERmbmlnVGhPaGdidERMTnpEcnhDRG5PSVQvbGZFeFIraG1DV3dEZjlaTFU3L2JSQkd0VEh6MUpFazVDcnc4Z1VHY2NZUkcwUzV2M1JLZURJV0tjTEdocU12REtlUStIZi93N0JtUUJxb0x0aldaQldFT0xpRFJQaUtLR3U2d1V2UmQwNDdqdFpFTUpNbFI3bjJFOTBXNjVqNHlxdENXaFluZUZYcU9jT2QxNFJ6UEwvRzFzeW8vNDMvM2llVlU0ZkM5WU9MZGUvdzN6dEwzd1pnbWpGS0pGNk9oV1QxUnkxZXUvVzAwaGxEa0pHYUFNcTdYZ01KY01FRU5HQmZoTFVxb3NpY2RSN1JTVHV5VkY0cVk3dmkzRmhnU3dMNTFLVjd0Ykx4THhzQ0ZxT3gvci9GRm12M0V6bzJUVU9YWCtJcWIyT2ZUZThmaGZic0F3dmZJcVFveWViUXhjeTJvbXpMekxJM1lSdUdUK3E5d0lWcFdSc0xvM1BvZHI3VlcrTVVkZDlQK0l0TTZhWTM3MnNVek10MU9IeVRhWUlGcnFQRlRYWS9TWEZtS2RhLzJiWFl6RVdIMFlWU05HZHA0anYiLCJtYWMiOiJhMjNlMjZhMjY0ODQ2YjAwMDE5YWU5OTdlMjVjOTU4N2I0OTc1NGE5NDkxYjA4NzJkOTE2ZGEzZDViMTA1YmJmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNlR1BwNmdQd1YxWUxJMmM2blVxbHc9PSIsInZhbHVlIjoiYm1reEpyRU5JMjFBMHBKSzYyTUJiUVF5VTBpSkZoMU9Gb1QrWlRDSEx4UnN5V1NwQ250cFFTcnJFRkRCQTJHSEpEVkJId0dnNW51YXlqdnZOQlcwYSsvc2h6TkVJQTBvVHBmWnRvdm1qbW83ZTduZHhqQ0ZUaEpZQkVrZDNzdjBCeWxGUHBQNmRCUVRHVUpXSjAzd25hYy9jYU9ORlhMRTZuRzc2RitObW9xZ1pqUWcyRGRuc09TYXpqajdvemYxQ0xWdCticko5U2RGQ1Z3TGRSTWhHdnR4bVdRU2RRL0JENjBzVzFLMkNKY3g4blJzcUk3WjNramNmYmY3NXJYc3E1Wm9vaVZlOXJDNzUrWmZJNnNmeGZvdFE0aTFiMWU5dXZRMVpYM2YyVVh4U2VHYmhOKzlOeVRhN2QrL04wNCt3YzNqMVkrVHdzemF6alpTajI1dXlDYTFpY05zamw3bTJpdnVZSlAzR2p3ZG05N1dLYkdORkFvVHRER3RkZndOYWxXOHBKYVJNTFZHOUhZZDduZDRrdy9ZQitpckRSbDVJSWhKUGp1eEFNVmNCVzZ0ajlVWi9JMUNPRXR3NlNQTU1oMVF5T3ZqQmpnS1kxZmRKQU9sZlhITzc2ZnAzMGhiWkMvZ3FKUXhJTmh2L2ZranVrcDdGZjNkQ0JobE1ySWciLCJtYWMiOiI1MGJjMDQ2OGZhNDJiNmUzZTIzYmQ1ZWUzZmMwMTIwNjczNjY1N2ZjYTVkMDU4MjhkMzhhODFiMTk5ZmVkZjk3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImtzLzlPbGVRVUgyaUZZcE9LNnBEMmc9PSIsInZhbHVlIjoiSVgxejQxV3M0czloSVhmcXloV2Z6cTZoUC8xT285eHp1VHZaSzl0cmREdnloKzlhQ2ViYVFSOHdkbG4rb2FNTERmbmlnVGhPaGdidERMTnpEcnhDRG5PSVQvbGZFeFIraG1DV3dEZjlaTFU3L2JSQkd0VEh6MUpFazVDcnc4Z1VHY2NZUkcwUzV2M1JLZURJV0tjTEdocU12REtlUStIZi93N0JtUUJxb0x0aldaQldFT0xpRFJQaUtLR3U2d1V2UmQwNDdqdFpFTUpNbFI3bjJFOTBXNjVqNHlxdENXaFluZUZYcU9jT2QxNFJ6UEwvRzFzeW8vNDMvM2llVlU0ZkM5WU9MZGUvdzN6dEwzd1pnbWpGS0pGNk9oV1QxUnkxZXUvVzAwaGxEa0pHYUFNcTdYZ01KY01FRU5HQmZoTFVxb3NpY2RSN1JTVHV5VkY0cVk3dmkzRmhnU3dMNTFLVjd0Ykx4THhzQ0ZxT3gvci9GRm12M0V6bzJUVU9YWCtJcWIyT2ZUZThmaGZic0F3dmZJcVFveWViUXhjeTJvbXpMekxJM1lSdUdUK3E5d0lWcFdSc0xvM1BvZHI3VlcrTVVkZDlQK0l0TTZhWTM3MnNVek10MU9IeVRhWUlGcnFQRlRYWS9TWEZtS2RhLzJiWFl6RVdIMFlWU05HZHA0anYiLCJtYWMiOiJhMjNlMjZhMjY0ODQ2YjAwMDE5YWU5OTdlMjVjOTU4N2I0OTc1NGE5NDkxYjA4NzJkOTE2ZGEzZDViMTA1YmJmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101890798\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-267663034 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-267663034\", {\"maxDepth\":0})</script>\n"}}