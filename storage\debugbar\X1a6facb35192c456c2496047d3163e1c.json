{"__meta": {"id": "X1a6facb35192c456c2496047d3163e1c", "datetime": "2025-06-28 15:46:27", "utime": **********.84408, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.328569, "end": **********.844099, "duration": 0.5155301094055176, "duration_str": "516ms", "measures": [{"label": "Booting", "start": **********.328569, "relative_start": 0, "end": **********.772648, "relative_end": **********.772648, "duration": 0.4440791606903076, "duration_str": "444ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.772656, "relative_start": 0.44408702850341797, "end": **********.844101, "relative_end": 1.9073486328125e-06, "duration": 0.07144498825073242, "duration_str": "71.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45165952, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01317, "accumulated_duration_str": "13.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.806062, "duration": 0.01244, "duration_str": "12.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.457}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.829436, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.457, "width_percent": 2.885}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.835458, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.342, "width_percent": 2.658}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1854234616 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1854234616\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2097187334 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2097187334\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2027170285 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027170285\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1902058513 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125411738%7C18%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVKa2xjS0tpMXBZSjBLcDNjKzd0MUE9PSIsInZhbHVlIjoieklaSDJyRFZ0RkV4eGxSejhVRTBmZ05XdzcxNnIrVHh6SFpjczNpWWYvTTBQOG9OaVpQM0lQc29oZndaK3d6MzYwOGNGRkViQ24yR1g3V0hyZVN1UlRuZ0ZOTFNEdVVpcG5iSXpZbkdjQWNoYmJVSERUQ2J6SlF5bmZvSWR3NlMxMis3ZnpQcDlUUUQxbk9LVWNhd1ZJOTE4NjJ4eldqendPVkNQSE5FUlFjVEpROFZhMkNTTXV0djRpRUxtdTYrZ2M2U25yUEkrVGVCTXVaeGRLY0hHME1lMlJOVVlCUGxsdkptOStsODlVUlI0Skh6WXU2QVpJT0V4K3k1S01WUm9Ud2FVKzJpbnEvUHd3aGlId3VFblJIeGxtUmtZRWhYQ0ZlcHh0eE5UY2d1NW5GRzVvUnNYcUlieXowL01EVGZ1ZkJSVy9aaFcxd1Evbi9oT2YrZnJuMFJsREZvWGhGUHZva0cyME5wZGhESWZLa0pDTTRTeXBPRzRqeHpITkVHQllPRWh0TzBnbUJhbWc0M1JwL0Viek1pSytSOUNjMnVUSGZMUCtYSTFvV0VPMmJyaVMvc2FEaHEva2xEN2p0N0g4YnYxczE3OTM5ZnlQWW5mY0MxVlJtbHNIN2N1RzJYV3F5NEJyWHppTU04aGVIUkp4U0puZ2ZWRDk4NzF1L1YiLCJtYWMiOiJlZGI1OTkzNjlhNDNhZWFiZTg0ZTUwOTkxN2QwYjJiMDA1MTczMTgxYjk4ODA3NTNlZjVjYjRhNjI2OWIzYmE1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldjTUtXV1IreVJzNTVzd2dENlYzeVE9PSIsInZhbHVlIjoiNEFPZnpXcUkwTG1ZSkFoL3djOGJHeGtSYVFTZVVTUG82VFhDRC9RN3hobllHbGpnR0NlcmtDc0VLVlB3Qlg3Z0pkL0VZL0crYWF4MkVBNkhEa1E5WDRrNjhzd3JuQ3FPQytwRUM1SEk3ZGpCMVByMTkzNUpUTDBQNU5GYm0vUWptaFJXeU96a3p4Lzc4T2FOKzlvUENacm4vVFp1WmR1eHI5TXRBbTRqbjdYWmVFQU9LS0hSSkN5YXlwRmg1amRjQ2o3elZPRjBKZXhDemVndFpsOGhMUzQzWG5JTm9uQ0pQckNGUWV4bVFyMTAvZUZwbGtiSmFQaVByMFVsUTNnQTRwUU4waWQvWnVpeitZTUFWek9lSFh5UFBORTNMekdOTjZzVVo4U2ZPOVBlMjBZQSsxSTV0R1d3dWZNWG5jZGxoTnQ0TCs4a3J3YVVjYTRjc0xwUmNsa29aNHdEMndrTWFxdGVMbUZYTVozYVBBS3c2NE1GN2FGUUt3Zm5KNHlSbHV5NUtjRUZmbU9QVFBMcUlncnFGZ3JMN3ZLc0R4SXpyTmJhRnRnLyt4cnFtODRyeFpVc00ydHd2MEdnejF6ckZhZFQ3VnZuRFRjNUEzVURUdXFocU9mUnZ6WTlkREpXREIzSVFYWU9sZjJmVkFhMU80ejVvVlIwN0s3NEdRNjEiLCJtYWMiOiIwZjkyNjIzODMxOWQ3ODg4YTBjMTVhNzg0MGI5ODY4YmQyNDBiYWM3M2U1YTE3OTQzOWU5NDE3MjExY2E0YTdjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902058513\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1101014697 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101014697\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-951740805 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:46:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9lcGQ0alNMMmw1YkZ4VUNITFlEMEE9PSIsInZhbHVlIjoiMWFEaWQyTkdGUlRxbDljOGJEaGJWUmFvN0hzVU1NMXdISzA5djZSTEp1NnJuYUVyOXFsSXZ5UG4ybHowUlU2WGUxL3hvalpmUTlHZWl2VktmN0lZU0VtaHVOYmhaYTc2cldJMXBSWjFOQVJFVzJMNGNwNVZGSFVQM0JaMDdYcU5KaTNIOUVubHdFM0JqNWFBZ2gvSHFnenRjOHVXVkhiRjdpRDVrcS9jSDNqdWI5RGFVME1FY2pmUXlyVjZsYXNjakFZRWxCelJMMHVhOFIyTzNWeVlwaXJJcHRaZVhvTUxmQ3UvWXFhQkk4aFpZQlQyTnBaK3FFV2hPLzBXNW9UWFJNNFBMWm1yNndZanVOeTBTUUtPblBYclptdHo5MTdjWUtGZ25zdEp1K01UUnNTNGVXRWlQQ2dqbEhKVTk2L1grU3hqWWxMQTVOb3FsL1M1R3hrY25HSlVXWC9MZXYrQnFMQUpDeG14RXI1Z3RUUkNSNHlCdWJWb3ZTRzMyTTQzanpFbW1zVFJ3TlY0cHF1N0t5RmpIdC8yVW5vVms1c1hNcVhXRm1DWWpMK3NaaVpXc0VUUFd1d244eGVwUzkrdE0xckE2WGRQak5DcXNTRG5tdU1VZ09jakwvbUJOZDcwZy9FUExLRkdNL1J5TS9mZjRweDlIU0kvUmFkbDh1TEciLCJtYWMiOiJkMTNiMzg0MzQ3NjA4OTlhNmI0OGE2NTg2NTI5MmE4MDMxNDFiMWQ1YWJjNDJlMWI5YTU1MGVjZDViNTMzY2Q5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNxc2ZXTlpQbUZaUUZDVmY1TmlsTXc9PSIsInZhbHVlIjoiT1RHTStCV3RoT2RHbFNBTzBmaXNoUXdqblhraFQ4R01jcUtoYVlpclQ5RDVhMkR0WWt3T0EyS2t2TFkrVGtrK0VmTHNrTUNKWGlIODBBeWtpbFZUUXVXNjJXOGJvbGFCK21jTldLR1pjTXAxMEVxelJvMUtnNERUamkzMWNqVEc0VDBOeURwVFkvL081bGkrVVFnODhWc3FxczhHYkgyYXFlTXdQaVpzZmhyZTNQN3kzbjFzV1BidTBUY3JCVWJPVFZSVEdWVm1FT2VJS2JKaTF3YVhtZXk1V1NTaDhNd25ZS2dGc2xpVkdpcUdiODNlZ3czdTRvNmpzKy9EVWZSYTNNWUZtSkxnM1FHbzgrNXNUZ0NobzB5b0ZmbW45b25ZaWN5cFJ0WlJtczZLMnFrVTJXNzJ2RG9zVXJvUkk0TEJQL0tGTVdiTVdqR1krZlAyKzlIM0lGMUZrMzdxelhncFJ6cWhGUHpzUnVFeERpUmdldEFsZUtleUZZYXN5QUJzeWwyZU5aNU1GVHMwZ1hIclhTWU5rVU5rcXRTZFVxdml6K3ZJZGt0bTNCUmM3c1htQ3ZER1VXUk9sYjJOZklnTkpNdW1yU1FUbXhkTkZHRy9Xay93V3BqVmp4eDVmY3JLUjdSWDJXV3pxRVhSc0hMemhHT1ZNNHdrNVErZGJjUnYiLCJtYWMiOiIzMzRhYjMxMTQxMGJlYzAxYWJkMTAyMDVlZDc5ZTA3OWZkOTc3YzMzYzIyODAyMTg2YzIxOWVmZjQxYmFmZjdhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9lcGQ0alNMMmw1YkZ4VUNITFlEMEE9PSIsInZhbHVlIjoiMWFEaWQyTkdGUlRxbDljOGJEaGJWUmFvN0hzVU1NMXdISzA5djZSTEp1NnJuYUVyOXFsSXZ5UG4ybHowUlU2WGUxL3hvalpmUTlHZWl2VktmN0lZU0VtaHVOYmhaYTc2cldJMXBSWjFOQVJFVzJMNGNwNVZGSFVQM0JaMDdYcU5KaTNIOUVubHdFM0JqNWFBZ2gvSHFnenRjOHVXVkhiRjdpRDVrcS9jSDNqdWI5RGFVME1FY2pmUXlyVjZsYXNjakFZRWxCelJMMHVhOFIyTzNWeVlwaXJJcHRaZVhvTUxmQ3UvWXFhQkk4aFpZQlQyTnBaK3FFV2hPLzBXNW9UWFJNNFBMWm1yNndZanVOeTBTUUtPblBYclptdHo5MTdjWUtGZ25zdEp1K01UUnNTNGVXRWlQQ2dqbEhKVTk2L1grU3hqWWxMQTVOb3FsL1M1R3hrY25HSlVXWC9MZXYrQnFMQUpDeG14RXI1Z3RUUkNSNHlCdWJWb3ZTRzMyTTQzanpFbW1zVFJ3TlY0cHF1N0t5RmpIdC8yVW5vVms1c1hNcVhXRm1DWWpMK3NaaVpXc0VUUFd1d244eGVwUzkrdE0xckE2WGRQak5DcXNTRG5tdU1VZ09jakwvbUJOZDcwZy9FUExLRkdNL1J5TS9mZjRweDlIU0kvUmFkbDh1TEciLCJtYWMiOiJkMTNiMzg0MzQ3NjA4OTlhNmI0OGE2NTg2NTI5MmE4MDMxNDFiMWQ1YWJjNDJlMWI5YTU1MGVjZDViNTMzY2Q5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNxc2ZXTlpQbUZaUUZDVmY1TmlsTXc9PSIsInZhbHVlIjoiT1RHTStCV3RoT2RHbFNBTzBmaXNoUXdqblhraFQ4R01jcUtoYVlpclQ5RDVhMkR0WWt3T0EyS2t2TFkrVGtrK0VmTHNrTUNKWGlIODBBeWtpbFZUUXVXNjJXOGJvbGFCK21jTldLR1pjTXAxMEVxelJvMUtnNERUamkzMWNqVEc0VDBOeURwVFkvL081bGkrVVFnODhWc3FxczhHYkgyYXFlTXdQaVpzZmhyZTNQN3kzbjFzV1BidTBUY3JCVWJPVFZSVEdWVm1FT2VJS2JKaTF3YVhtZXk1V1NTaDhNd25ZS2dGc2xpVkdpcUdiODNlZ3czdTRvNmpzKy9EVWZSYTNNWUZtSkxnM1FHbzgrNXNUZ0NobzB5b0ZmbW45b25ZaWN5cFJ0WlJtczZLMnFrVTJXNzJ2RG9zVXJvUkk0TEJQL0tGTVdiTVdqR1krZlAyKzlIM0lGMUZrMzdxelhncFJ6cWhGUHpzUnVFeERpUmdldEFsZUtleUZZYXN5QUJzeWwyZU5aNU1GVHMwZ1hIclhTWU5rVU5rcXRTZFVxdml6K3ZJZGt0bTNCUmM3c1htQ3ZER1VXUk9sYjJOZklnTkpNdW1yU1FUbXhkTkZHRy9Xay93V3BqVmp4eDVmY3JLUjdSWDJXV3pxRVhSc0hMemhHT1ZNNHdrNVErZGJjUnYiLCJtYWMiOiIzMzRhYjMxMTQxMGJlYzAxYWJkMTAyMDVlZDc5ZTA3OWZkOTc3YzMzYzIyODAyMTg2YzIxOWVmZjQxYmFmZjdhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-951740805\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1962682950 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1962682950\", {\"maxDepth\":0})</script>\n"}}