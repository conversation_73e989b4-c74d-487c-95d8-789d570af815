{"__meta": {"id": "Xfd9fc2a305df80fa3468c306b2c6d389", "datetime": "2025-06-28 16:34:57", "utime": **********.475075, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128496.978516, "end": **********.475095, "duration": 0.4965789318084717, "duration_str": "497ms", "measures": [{"label": "Booting", "start": 1751128496.978516, "relative_start": 0, "end": **********.403624, "relative_end": **********.403624, "duration": 0.4251079559326172, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.403635, "relative_start": 0.42511892318725586, "end": **********.475098, "relative_end": 2.86102294921875e-06, "duration": 0.07146286964416504, "duration_str": "71.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45716856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014879999999999999, "accumulated_duration_str": "14.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4322941, "duration": 0.013859999999999999, "duration_str": "13.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.145}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.455701, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.145, "width_percent": 3.36}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.463829, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.505, "width_percent": 3.495}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1324236602 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1324236602\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1271593622 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271593622\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1327355274 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128494675%7C53%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVHNVpUV3N2bHNiNFVNbk43d0hmY3c9PSIsInZhbHVlIjoieGpXZDN1dTMzcU9VK1pBU1U0N3pMeTRURTVoYkVQUU5IMnhRc0lWWGZYdG5lVWhqa0o4MzZHV2daSWZtODk0S0txMllEeHV0cEREN1E0SlJVUFYzczUrREVjTWVtbWpzSUZFclIxeGVaN0RFQjd0VE81MFUzOUcrUUUwUWEvWFVPSUNYRFpVNFpvcU14eGpQcnJYTTVjUVdnaGxuT25JS2RzY1FMWEVTUUVVQ0hiQ1NYUlI1S2V5WVNVZWk5WDd2dTR4RGpWWmFZVGh1M1pLZktXNUdLSWVTZ05PT0xSL3I5MnRLY2NyRXFKbzJEZXFKVFZOU25adzlaK0E5US81T0tqcVZGWVRya1ZPVk9IZGc0cUtqZmVJZjJQS1VHcisxS05oRy9NOTl6RWpMbjMzSUhZSGZjSmNaK1o1N1hwRWZFN1g5QitKT0Y0NHpPSGpCaGxDQi9nRnpsK0Q1cWd1M05KQU94NlU2Y2Fud1kraUI2Nkl0MHFwUTNqVEtpa3BHT1JZUFRQMlRKczdSL29mdDMxcGtlcHdmSnBjQ0lnMjNuRXVZakZaUDVRVVpsR0Nza2psL1IwUGNnUVZSOVNPU2NQRDdKRmMyQ0JnYU5RYmt4VCttUEkrSjBiNFRvaC9vQ3Vac0FJSGE0NjlPeFJkTFl2Q04wS0RQTW8xb1YyR0giLCJtYWMiOiIwYjk2NjA3YmZlZGQxNTZiNjYxMDUxYmQ5MDg4ZDJjNDI4MmVlODQyNDAwMzFkZWRmNzA3YTk3ZmVkNDgyNDFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFNNTRScFIwSktaQTlsTGp2YWtoNGc9PSIsInZhbHVlIjoid1Z5ZnlhOE8zdlV2cjBkOUFGR3Q5TFQ4bmY3LzVQa0x6YVRlbEQ1aURoR1NTbjJXR1ZRMnMzZ0VoZ05EU1MzK0tkZlhtY0JPeFBReUY5YmIyeENXa1JOblVIY0NwNk9BTWIyQktJcFdxazI5dDFBc25TZFRqbXVQQnA5anJOMnM4YlBhbEFMLzBVRHJPaitKVTVKMzhqUTFQQkhaZlRDc2o5c0poRHJra2s2RitRZHJ4ZDdKeDVOZDBmQi9PcnlhTWtYSVV0cWltd25tUlFwZTBTWTVKNHZhVklraE9nYmxQa2RiNHM1dFVVWDI3V0NwNFpsYjhkcjVOcGFScXp5NTBKdEJEbkNtNXU1N0RDb0RBaFdFWDhqTHFJanNmdUoyT0c1Tk1xeFBkYXhDSHYzQno0YkJhdHJFMU5zRytJWFJZM1pUam83UXhPOExIMjJadWFyeXQxamV1MXBlRlVhSmxCa0ZQOE8xZkpnM1dvSXpsNzVvUVFtVnZWWXlFYTNPb1d3bzVTZmMrcjBpMlRvb21GbEhVcVVlUS9xRHlEdlh5Sm1tWnVXL2Y0cS8rR3BpbyszVWRFd3k0dmZCY0Fnb2xPeXd3cmllZGhBOWdBdnRYdW5NZE9KRTIycU51N2xROWtMZUo3S2ljcmR0TDk0VVlmMTdaTGUzSnRrdDdKVlciLCJtYWMiOiJhMGE0NWFmMTcwNTBiZmM1OTZkYjMxZTUwZDJiYjdmZjAwYzZmZDg3MDAwYjRlNmEyMGExM2JlM2FkZjM1N2M1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1327355274\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1730875601 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730875601\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1063684798 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImN2d1FBTDdpWVNYMUdzRndxbC9LclE9PSIsInZhbHVlIjoiQmU2V0tVOFpIUGF2MnN2TG9ZYm9UMDJMdlZML2YvMk80NHZvMmRoalNPaTFTOTYxQ1Y5N0lodE80K20vL2FZY2dicGhqWDMwOGdxVEdqd2JrNEt3Y2JwUjFISWpWZGRRVHRFV3dIZUhIRFF1WW9iM2ZLYlFyS1JzNjNPRHFybnhObjRwZVp6cWpZbjF3UUFMOGtmc0gwQi9jRTVhZjc5dXdMenIraHJhaHhieFJkRXRMdEpwZWg1dGROMk9TTXY2QmU1YjdyaGgxd2dTYVhJNDhsMktjMlE3c0R5OVN0R050U3dPOWJaeG1LSkRNQzBQVWVYbWRHdGlQc3JjQTZGeW0xa1Y3bUhQL1dQY0VQaUFpSFc3SW9UVlJMU1A0NlJOMHFnVjFHeENPcFNSNkFjVXFMK0FudjJnSUloQzdLdUt1N3loeEc2VE13eG05Y2JNOG16QXVSSFk4enJoVzgxdDA5OUFwNTMxU0p2bWtSc25pVnZJcXd2NTNoREs1Znc0VWpQSzQxR045UzNjaVVHOUl1VzA3enlTV1kyTnJDY292ZHBJK0dpbnVlYm5aVmhVSDREcVZnSTNMcXNOT0Y5NXM0bzVXL0crUWxNWFNnOERwVUtocFZEQkJWVXpEWkRhc09sSUgyakcvbndoM0psZ0Q5MVdOaDNqbXZsRDNGYWkiLCJtYWMiOiJmZTY2ZWUwYWMxNDVjYzdhY2Q1MGJjYTIxYjUyOTVhMmM4YjUzY2MyNTc2ZDZiYzM1YjY3MzY5YzY5OGQ1YTZmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IllCLzNaaERNZkpvOEludmQ1WG5qUFE9PSIsInZhbHVlIjoiQXNMUmplU0VzWDAyRXJTTWNpWDRVVzdFcmZRQkpSTWFoV3VPYTUzTE1TMG0zVWpVK1VkRHhBYmFMUlgyaE5QekM3My9SQmF4bG5pSm1xNSt6YjZwb0gxazdZZmhKSlFSVDg4L2FhTXdOWEhFL0hPbXFMQjFyaDRNdnNQUDQrWDlwMk9Db1pabTlvY3FSQ096TTdyNWZ2Lys5QlBpSFZ4QTJZTFUxSEh3OVM4am5yR2tnWDUyYkN1bk52cVFIeVZ1a25YdlZZOU5FbFhkT1V1cVV1MlhvTlBtOEdFNW1WaGpGcUFVMDdhOVF5a24xME5HZit3dE5xUWFGRHhqUDJBQ1F5dm1hZyt6Z001dEc5V3RpUlY4VFJJTkUxQ0wyNHlDdEd5cDNXZ25KRDB0eUdKemd1blNTM3hxclI4bFZZVWZodk1KcmZIRkpWMmN0cWRZbWhQOTNCY21IQlp5dVloQkNhcVpPVzFRZEoyZEdtUjVtYW4rWW1ZbVBMQVdRWGRiKzh1aWk4Yys1ZnB3c0RDQ1RtcVFqaWFWdk5vNFVzY0xsQzdqK0trS3hVanFWVDB5MkdvNUxFQVczUmhaZFVoNkRLWTRob21KcTAxVmJnZGtHSFNuR0tSbUpRWUN0RENqRG9xbENTdjBFTzYxTFFJM0kra3RXa0ROdlhzcW1OZ3kiLCJtYWMiOiJiOThkNTljZTcwYWMzZWJiMmVlMzY0NDRkZjFjYWYwOGJiMzU2Y2E3ZmM5YzI5OTE3ZjM1NzExMjUzMDYyNjNkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImN2d1FBTDdpWVNYMUdzRndxbC9LclE9PSIsInZhbHVlIjoiQmU2V0tVOFpIUGF2MnN2TG9ZYm9UMDJMdlZML2YvMk80NHZvMmRoalNPaTFTOTYxQ1Y5N0lodE80K20vL2FZY2dicGhqWDMwOGdxVEdqd2JrNEt3Y2JwUjFISWpWZGRRVHRFV3dIZUhIRFF1WW9iM2ZLYlFyS1JzNjNPRHFybnhObjRwZVp6cWpZbjF3UUFMOGtmc0gwQi9jRTVhZjc5dXdMenIraHJhaHhieFJkRXRMdEpwZWg1dGROMk9TTXY2QmU1YjdyaGgxd2dTYVhJNDhsMktjMlE3c0R5OVN0R050U3dPOWJaeG1LSkRNQzBQVWVYbWRHdGlQc3JjQTZGeW0xa1Y3bUhQL1dQY0VQaUFpSFc3SW9UVlJMU1A0NlJOMHFnVjFHeENPcFNSNkFjVXFMK0FudjJnSUloQzdLdUt1N3loeEc2VE13eG05Y2JNOG16QXVSSFk4enJoVzgxdDA5OUFwNTMxU0p2bWtSc25pVnZJcXd2NTNoREs1Znc0VWpQSzQxR045UzNjaVVHOUl1VzA3enlTV1kyTnJDY292ZHBJK0dpbnVlYm5aVmhVSDREcVZnSTNMcXNOT0Y5NXM0bzVXL0crUWxNWFNnOERwVUtocFZEQkJWVXpEWkRhc09sSUgyakcvbndoM0psZ0Q5MVdOaDNqbXZsRDNGYWkiLCJtYWMiOiJmZTY2ZWUwYWMxNDVjYzdhY2Q1MGJjYTIxYjUyOTVhMmM4YjUzY2MyNTc2ZDZiYzM1YjY3MzY5YzY5OGQ1YTZmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IllCLzNaaERNZkpvOEludmQ1WG5qUFE9PSIsInZhbHVlIjoiQXNMUmplU0VzWDAyRXJTTWNpWDRVVzdFcmZRQkpSTWFoV3VPYTUzTE1TMG0zVWpVK1VkRHhBYmFMUlgyaE5QekM3My9SQmF4bG5pSm1xNSt6YjZwb0gxazdZZmhKSlFSVDg4L2FhTXdOWEhFL0hPbXFMQjFyaDRNdnNQUDQrWDlwMk9Db1pabTlvY3FSQ096TTdyNWZ2Lys5QlBpSFZ4QTJZTFUxSEh3OVM4am5yR2tnWDUyYkN1bk52cVFIeVZ1a25YdlZZOU5FbFhkT1V1cVV1MlhvTlBtOEdFNW1WaGpGcUFVMDdhOVF5a24xME5HZit3dE5xUWFGRHhqUDJBQ1F5dm1hZyt6Z001dEc5V3RpUlY4VFJJTkUxQ0wyNHlDdEd5cDNXZ25KRDB0eUdKemd1blNTM3hxclI4bFZZVWZodk1KcmZIRkpWMmN0cWRZbWhQOTNCY21IQlp5dVloQkNhcVpPVzFRZEoyZEdtUjVtYW4rWW1ZbVBMQVdRWGRiKzh1aWk4Yys1ZnB3c0RDQ1RtcVFqaWFWdk5vNFVzY0xsQzdqK0trS3hVanFWVDB5MkdvNUxFQVczUmhaZFVoNkRLWTRob21KcTAxVmJnZGtHSFNuR0tSbUpRWUN0RENqRG9xbENTdjBFTzYxTFFJM0kra3RXa0ROdlhzcW1OZ3kiLCJtYWMiOiJiOThkNTljZTcwYWMzZWJiMmVlMzY0NDRkZjFjYWYwOGJiMzU2Y2E3ZmM5YzI5OTE3ZjM1NzExMjUzMDYyNjNkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063684798\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}