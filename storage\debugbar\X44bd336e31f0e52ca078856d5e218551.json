{"__meta": {"id": "X44bd336e31f0e52ca078856d5e218551", "datetime": "2025-06-28 16:01:28", "utime": **********.028584, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126487.516218, "end": **********.028599, "duration": 0.5123810768127441, "duration_str": "512ms", "measures": [{"label": "Booting", "start": 1751126487.516218, "relative_start": 0, "end": 1751126487.961749, "relative_end": 1751126487.961749, "duration": 0.44553112983703613, "duration_str": "446ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751126487.96176, "relative_start": 0.4455420970916748, "end": **********.028601, "relative_end": 1.9073486328125e-06, "duration": 0.06684088706970215, "duration_str": "66.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0053, "accumulated_duration_str": "5.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.001026, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 33.396}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.013893, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 33.396, "width_percent": 11.509}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"pro%' or `sku` LIKE '%<div class=\\\"pro%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;pro%", "%&lt;div class=&quot;pro%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.017187, "duration": 0.00292, "duration_str": "2.92ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 44.906, "width_percent": 55.094}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-595241613 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-595241613\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-715479325 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-715479325\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1991744029 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&lt;div class=&quot;pro</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1991744029\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-133929085 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZjU3Q4TFZzVldmQllGRlkwK3hSL1E9PSIsInZhbHVlIjoiRzB4d0RaeWcrOS9ZaktTdzhySXJLZkhOWHIyZnpBYUIwT0tJa0VCUHQ1V20vLzFoMDE5ZFovdjl2ZWtCOXRVNmRGRlVwb25hS1hvb0sxeDd2aDhKUi9vMC8wRHk2dWtId2RKdnZKaG1RSGRjai9HV3l5N2VoNXRWc0h5MDljSGpZVEh5SFBlTFhQOE5aV1N2d0tQK0w5Q3c0dW1GeldtOE9YMVp4Nmt4QURNVkRtQTgyTGRoNUtjcU91c2tjSEowSmNXT2t5bzVVNGRtU1QzN0l6WWh5UWZFQmJGRkxmR0JpaHYvcDlZZjRhYitmbzNBS0hmUXhRZFl6Zkp1VkdURHhjYVZ5elc3aUtDcHJ6VXJqNFlVaDZQRWVDVzFsQVRIUURrcFdvTEhxTnp0TWdxZzBWWEZMSndJLzREdUJjNlhDYUk3NlNOaXJnYzFzMzNlZE02ZE9jeVVqcHpQdlE3cE1kY2lmOVVnYnN0UC9vUFJ3VUhBK0tYeE5DSTMwb3pMbERTSnJXbjhCVDdScWZuMWh4eW9TOGo3cjFYdkxNYnI3RVRBVFRsT3RGMVBUcWlRWnNoQTlZMm9FWXZXWXVKT05mMlVmenc2MkJsN0YxYnUxaUVmSWtiYXN1djVrUXpwSUIyYmNiY0tCZWR2eGRrNlYwUFI4bVovTjdJOTYwVVAiLCJtYWMiOiJjN2YzOGY2NDBmMzYzZTM2ZDMwYTM4NTkzMGQ2MzlhODdlNmQ5ZDk3ZTkyOGY4YTEwZTA0ZjM4MDRmMmIzYjdmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtyK2hBR05mUVVuOHhMWVpvVmlyOUE9PSIsInZhbHVlIjoiNjdwcVpIenB1VU54cHowN1huWForMEtCMjJJdVRnV21JbFZKcjllUFh0MXZORHNOTHE5WU13SzF6cTZxSHhqL2hrcXRMV1NFTnlNTmpydkYvWVdZUmkwQkFaUWdrZTVYc25aZWpIVU8wSlhQbCtKK0ZlaCtyb3BFZ1B0SlJHY2RjSzV5WkwwZXdTb3F6UjhNSHRLT2hZVVRRT2tkWUhqbmR2Tm0vYUh4SnlzM1lSS29oTkZzTmxqTlZCaFJGMXkzMjRVaFZVQjNLSlJvTEp2K1Fla0VhbFlTZ25Jbkh0WHRSUkEyblBib2dNQmg2NUN3YWJiVlVIZ21qaFVlY09lZ24xN0JoRkJEOTRsM1Q0QWtiRzBMQU9QeWdYRjNEOEU3eGx3eUhhMFpsUlVEZ2d5VjVFczE1S3I5clc1MUYzOTd5cmRRSGZ4Um5GUTRmY0VQT0pvYWhYbzRJQU0xbldQeHVFRVpRWlBaNTBEVzVHWjJVaEVzcXZFZ2RQVklnR0hNRmNGVDdpWVJsTUNObXdGNktLL0VSODBVdFBhVE5OZjYwREdrY0xweWhabVU5NkVvQ09Ldk9idUVITG91RCtybzhGVW9oSVFCZkRlb2tqaEtxOHhjMzZSdklXTWlaMTJrTmF4Q2hXVnlzMTRkZEF6NjlmYnZtR1ZBL0pPWVMzZUUiLCJtYWMiOiIwMjU2ZGFjYmI2NzJlNmJjNWNmNTllYTg2MzEzODVlNzRjYTQwYmUxODkyODUxYmQ3OTFjOTY2MWFjN2JlNjZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133929085\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1410326327 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1410326327\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-731818840 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdrZk5MSzFDL0RIQWtuQWZUcG4rVlE9PSIsInZhbHVlIjoiYUEvUTh2S3RNYXFpcFNpS3hCYmQyR3FsOWhXZEZYa1JHdGE5b1JBREdBREZWWVc0b1UwZW5WelBwSUI0MG1OWHpRdHdORmVpY0VYd2hWNmtERjhRTDNtWmQvVDVMUkZwWHhHazFzaXJIZGx0QTIwbDg1dTlBeEhmUTg4Z3ZGZnVES09EQmNKMmVLeDVSSmVmbjRhcmlnUjZtN2xLRFo0aCt3WUkrRUdpSFNhbnNLNkdxVysrS3c1VFMwUDhnYjBWSnhxcE5GK0xrZmh1UUQybnNzai8zeG5FazByaUFSY2V2anIxc0lCZXorZTdWdEJLOFZuRTd6enB1T0QyM1ZzNWZGZG1zQzI0aWZERE9pazk5TDZzNXZBMjBobEhnQzJQMEVjYmFPSXMwOGhLUS9qZDMrdUUyZmNKSGtiTE5XOW1uWGpEY2U5cVFTY0RaeVRnU3kyRlEvQVdGNVA5Yzd3dDdzd0JHekpTcXNSVTV3QjJWSkJweHVYZm82cUhhRjNzOFdQUlFOaTZDL0JMUzNWanhhT0oyTEtDUUYvZE5sMjBhemRFNi9lTU5jdUtFeW1GL0FERWNBZzdTaHVKWnR5UGlEZVFPaVhWWlNWaUk2R1ZxZktNWEhjVU8xMTFNSjhwVER2ZjVyb0YvSzIza2JGV1hRbUlkN0FqU25iWnBnZVIiLCJtYWMiOiI3ZGRlOGViMjlmN2U2NmU4NGVhMWVmZDVkYWE5NjQ3MzEzNDZmYmNmY2E2NTg0NzZkZGE2MjkyY2VjZGFmYmI3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVaWGNMdVVzSEw4c1lvdnhWUTVVZlE9PSIsInZhbHVlIjoid243a3JPckQrL3lOUEFiejFIUmNqZzhpeDJ0ekVXYnBVWlYrczhydWFBbnRiVlFDcW02M1FvTmJobXhRalVHdUE2YU1pMjg5STdHdEpsOExlNFdTOUZxU0YxVk1OeXNvQ3FoVzJmVEM3SjUrZUxmOW10MFF1dVRSaGJMSFMrNmJBdHdEaWRPZFVYRUFyL1l0L0pNSG9KN3FNYkpJQ0NNVHprVzNTbVordzJhN3dhWng5RnlHU0tMTTNkUm9PL1RmRWJBamIwbVN3WmN3NzVJWS9CRzhydXlHMHR6YlF6YlVVaEFhR09BaWtLR3ZCUkJxeFVibWZMYlkvQ1BuOEZ5UmZ6Vi8wTXJSd2JORDBBRCtBbWdFa3NyeUxvZGs1SytNZE8zdTEzZjBaRU1kekFRTjd5NVVpMVdwdGRQbEluNnhaTnoxK1N1ak4rTE9WNW1WR3lON0p1YW5NUThIdTd1bUgyMFdUZUx0RDQzc2xORFBkV0lsS1hObTVqUWRVd1l2eDBwRTNyQk1sekxUZkhHRW5yWGgyR0xhYXJIelVtcVpZVHVPUnBJSkc4K0hzdkdvWEEyajRYZDJiZUhsMEpLcmhSOW9ZMnFMWFBQUDNZV3ViOXlBMFpJczJ3bEFSNk5wRVN5czVrRjMzS2Rzc21aZTl4K3QwcFNUanNXVXNjMUwiLCJtYWMiOiI3MGEzOTA0M2U0NjkyMTk3OTVmODI3YzgzZGJmMWVkNjcwZTM0MmU2NGZhZWY4OTFiODk0ZjRiZGI2ODNhMjgzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdrZk5MSzFDL0RIQWtuQWZUcG4rVlE9PSIsInZhbHVlIjoiYUEvUTh2S3RNYXFpcFNpS3hCYmQyR3FsOWhXZEZYa1JHdGE5b1JBREdBREZWWVc0b1UwZW5WelBwSUI0MG1OWHpRdHdORmVpY0VYd2hWNmtERjhRTDNtWmQvVDVMUkZwWHhHazFzaXJIZGx0QTIwbDg1dTlBeEhmUTg4Z3ZGZnVES09EQmNKMmVLeDVSSmVmbjRhcmlnUjZtN2xLRFo0aCt3WUkrRUdpSFNhbnNLNkdxVysrS3c1VFMwUDhnYjBWSnhxcE5GK0xrZmh1UUQybnNzai8zeG5FazByaUFSY2V2anIxc0lCZXorZTdWdEJLOFZuRTd6enB1T0QyM1ZzNWZGZG1zQzI0aWZERE9pazk5TDZzNXZBMjBobEhnQzJQMEVjYmFPSXMwOGhLUS9qZDMrdUUyZmNKSGtiTE5XOW1uWGpEY2U5cVFTY0RaeVRnU3kyRlEvQVdGNVA5Yzd3dDdzd0JHekpTcXNSVTV3QjJWSkJweHVYZm82cUhhRjNzOFdQUlFOaTZDL0JMUzNWanhhT0oyTEtDUUYvZE5sMjBhemRFNi9lTU5jdUtFeW1GL0FERWNBZzdTaHVKWnR5UGlEZVFPaVhWWlNWaUk2R1ZxZktNWEhjVU8xMTFNSjhwVER2ZjVyb0YvSzIza2JGV1hRbUlkN0FqU25iWnBnZVIiLCJtYWMiOiI3ZGRlOGViMjlmN2U2NmU4NGVhMWVmZDVkYWE5NjQ3MzEzNDZmYmNmY2E2NTg0NzZkZGE2MjkyY2VjZGFmYmI3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVaWGNMdVVzSEw4c1lvdnhWUTVVZlE9PSIsInZhbHVlIjoid243a3JPckQrL3lOUEFiejFIUmNqZzhpeDJ0ekVXYnBVWlYrczhydWFBbnRiVlFDcW02M1FvTmJobXhRalVHdUE2YU1pMjg5STdHdEpsOExlNFdTOUZxU0YxVk1OeXNvQ3FoVzJmVEM3SjUrZUxmOW10MFF1dVRSaGJMSFMrNmJBdHdEaWRPZFVYRUFyL1l0L0pNSG9KN3FNYkpJQ0NNVHprVzNTbVordzJhN3dhWng5RnlHU0tMTTNkUm9PL1RmRWJBamIwbVN3WmN3NzVJWS9CRzhydXlHMHR6YlF6YlVVaEFhR09BaWtLR3ZCUkJxeFVibWZMYlkvQ1BuOEZ5UmZ6Vi8wTXJSd2JORDBBRCtBbWdFa3NyeUxvZGs1SytNZE8zdTEzZjBaRU1kekFRTjd5NVVpMVdwdGRQbEluNnhaTnoxK1N1ak4rTE9WNW1WR3lON0p1YW5NUThIdTd1bUgyMFdUZUx0RDQzc2xORFBkV0lsS1hObTVqUWRVd1l2eDBwRTNyQk1sekxUZkhHRW5yWGgyR0xhYXJIelVtcVpZVHVPUnBJSkc4K0hzdkdvWEEyajRYZDJiZUhsMEpLcmhSOW9ZMnFMWFBQUDNZV3ViOXlBMFpJczJ3bEFSNk5wRVN5czVrRjMzS2Rzc21aZTl4K3QwcFNUanNXVXNjMUwiLCJtYWMiOiI3MGEzOTA0M2U0NjkyMTk3OTVmODI3YzgzZGJmMWVkNjcwZTM0MmU2NGZhZWY4OTFiODk0ZjRiZGI2ODNhMjgzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-731818840\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1013498072 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013498072\", {\"maxDepth\":0})</script>\n"}}