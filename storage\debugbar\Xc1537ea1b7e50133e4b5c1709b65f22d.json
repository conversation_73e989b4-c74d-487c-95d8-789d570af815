{"__meta": {"id": "Xc1537ea1b7e50133e4b5c1709b65f22d", "datetime": "2025-06-28 00:37:01", "utime": **********.700193, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.256924, "end": **********.70021, "duration": 0.4432861804962158, "duration_str": "443ms", "measures": [{"label": "Booting", "start": **********.256924, "relative_start": 0, "end": **********.618956, "relative_end": **********.618956, "duration": 0.3620321750640869, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.618963, "relative_start": 0.36203908920288086, "end": **********.700212, "relative_end": 1.9073486328125e-06, "duration": 0.08124899864196777, "duration_str": "81.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45430768, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00247, "accumulated_duration_str": "2.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.664187, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.206}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.67395, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.206, "width_percent": 14.575}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6762712, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 81.781, "width_percent": 18.219}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2817 characters\">laravel_session=eyJpdiI6IjE5YmxhMUw3bXJQWWh3R0habDVwcFE9PSIsInZhbHVlIjoiTkZUZHViY3FpMFMvWUgzVm1OV0JwUHZoMVU1WjA5ck5UbEh4NjBRemY0ejlwWVNadXBvOWxTWVFjQzJMNGxNSTc5NzJwa1dxQ2hqL2VlQUZvMUs4YnV3Z2RhaExoU0p1RE5pdlhQQ1dzN0RpS0xnTWxrcmEvVFZUZERCSnFDQlhEdG03UkROakJlb2huQ24xSWlPcTA0QnpwdnNZaEl5T2JnWkpNMHNSVGpUeGZpWHJNcDBBWVFmaHE4Z0dkYmFlTm5VRWR4YWlaOHNZa2hPaDh2RGdndHB2MVJLZEFZMUU4Nmttek5WOEhIOExCS0p2S3h1Mm83MFRQc1U4NHMzN2lsY1J6ZnNLdVBhOXVCbktKdkNNdUQrcGJHZm9sUDkyRWhHVW90U0twWWw3Q3d1VzlyYzgxWjY2U2MxWWdnOXhaUEJkK0k5dFU1dWEvaDNXNmZtMkY2WUJKdVZSNnlFYVF0Y0JtL3Fac0tRZ3dyWDFwVXNSdmNhNzIyVjBiSHpQbDM1OXBiL1R6WndGT25IMnFYV3ZBNkdsNjB1SU54NXRIUWJBdTM1eDBiditjVzlWcW1DQlVTMkgzSmw3S0xYdDJmRVI4MXNEaENKc1dTbFRMR1pPWUVZc1crMkY4QnNxMDl2eEpvclhUb3hhMjZqYnFqaGVGTEdOWWxSZzBXNUUiLCJtYWMiOiJhMmVhNTI4OTM2NDBkYzJlNTU0MzRmNWUxYTg5YWQ0OGIxNGMyMmZhYzM5MmYzYzllZGEyNWMxZTVjYzdhNWUyIiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=zkpa4v%7C2%7Cfx5%7C0%7C2004; _clsk=9vvwq3%7C1751071010550%7C30%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im53cTM3WjN0ZGtFV3JTak0wUEhWdHc9PSIsInZhbHVlIjoiYVduM3NlV1FYa2NhODlEK281WVd0bkJqR3BqM1NEenJKdUNFdVcrYnB4bmVzY3VSRHRxRUhzWmVUeXA4VFp0cnhuWXpPVmgxNVhJNTduYkN0b0s5R2NzUDFFdjgrRlFiU0ZzT2hKOXNUT0EzUSt3U2I4WEtadUJOOHlxRGZmc0NPcTE4Q053a3dvZ050d1I1d2ltdUUwdVJ6THhYMWVJeDg3YXFFL1dVUjhjWS9jRndqaW16TUQ1bWUyWGFobWFQa3lGTGd1VjBTb1V1YndkNjJpeDFsTHRnSkwvMHE3L0FTYjI3WWZTU1pqZlJrWHFwWXBwUTQvWDIzeHl3VmFvQ2xBaUNPSjVGMnNPSGUvR2VITWRMRjI0S0hYTVFGN3g0MXdwL1EwY0Vmb0ZETkdvTDBZMTZkSlRmUm1abi9wcFVoUXM5NWs3WGxOL0FxK056Z09jTmJYU3F6WmdKZ2x3QjJRTzRzZ2Q0QitXQ1owT3hBWFdRRTE2U1FHWEN1NzE5WXRXcFV4NVp5TkV3cVFwR3VJd1BXOEdIdXM5L2lUdXUvcXRxWCs2d2ZoczhROXBJN2cxN1gyUXdJbnAvNjNnL2NnSnB1Q3BaTGdjM0tnVmVXcVRxamZZTEhoNjkxOFMrRzhPSk9zWkJuTFlXbkVVd0NtUWF1RTgyMjl1M0JSUVkiLCJtYWMiOiI0ODIwNjhkNGUwZTJiMTZlNWZkYTUzOTIzNTllNTk2YTBkYzczYmMzODYyNDkxNTUwMjlkNjdkYjk1MWI4ZDY5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imk2TVIxMUUvd1E3ZkdoQlV6R3BMSEE9PSIsInZhbHVlIjoiczBJc05Hb3dWV3Myc0F5YlZSSjVkQ3ZkNlZDemxRNkRKVTdVait1bFdTTmdENDZPQ2F4Qng5ZGJTMFBac2xmWTE5T3RJem0xUzd2VVF2b0EyUEh6SmhjL1lucktXMmM2ZE1KeWJick9FVTRaVktLcXpaREJhTkVQVHIwUmliUytmRnpWbzVDbHhJZ1l1cXlvVEdpeVNKZ3hzQ2RPd0FHTXNtWWkzbkNLNlhBZjVGNndQTWVHTTdJMzNsSmdaUjhZQTkzc3pmck10S2dnNkxISHBPWWh6QWNUdHFTZkRiam9rYXdmejMxRGZGRFFyVmcvdXRYd1djTU0yK2VjbEVIdzlhUjFmOFlMRHo4M3ByRkZmbGdiR3MvaktFSWVVVGU1ZkFlOXVaNzhwMmpPTG1yRFRlR1dIL3VMcFV4dWtMTTRDVFBSajhlcDVwZkFPV1dzb2JzVFBwLzJ4WTJZMDdTU2FoUmhGVEhhUWlZK2xUenB3RVVTRG45WXc4T21BdWV4QlFidC9NL0RiNEhCYVorSXp5TGRMTkhZRHBaMEpoQnRoRTIzNWNUSHliamNZemZSTDBOREs1M05pZmFMMk9ldlZUR2lKd2EvSDZ5UU5ZRE9qZjdzT3hKNjU5VUN6OCtST3dINFFKK3h0Nk80b2NOTHVZVE1ObC9XVUErY002TEwiLCJtYWMiOiJmNTJmMDJmYmRjZmYzZmMyMTAxMzhhNmI0M2UwNmQ5NDgzM2UwMjYwMWQ5ZGZkNDNjYmM5MWRiYTg0YmZiNDkyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">chEs2sQpmg2fYpUuqAOS1ZcIPLmVIQdx921QTw47</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9XNo0PIFsccsEXzvfQLAUsci7MaweQoaRcTvbnBl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1894347768 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 00:37:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlQVWxDTitLZ3NySUx2RlBwb0lMVWc9PSIsInZhbHVlIjoiT3NjWDBPY3hxb2hoQmRyejJPWTJnRlhhWUNpNG8vV2JlK0R5SzA0aXZIaXBPZEdWV3ljWGRMRk50SHZBczcxM2ZMMEFPNGdEQ3Q3ZTVlM056amUwVlJZZEJGUXRkTE1mMjNBdUNEME4zbnM1QTlrb0dtM00vbEEwbklKUWhLS3JJVm9tQlV1V1FmKy9CdzZib3lzTEFBN2p3ekZyY21xZ292YmZsYnNEbkZHTldjWmw1THdlbDA4SDVtblcvenBZZ1V6ZlY1TjJOV2ROQ1BpVXp4d3Z2Y2w5TU43R3B5dkk0Tk5rSEE3ajFjcGxJalJ6VUhQOHlqLzZhZ1gyRUJXblhKMHY4YUp2bUdQTG5JK0U4aWtTUGRsSjh6empnWmdCa2R4TzVkSjNqN2NuZVNWN0RHWGE1TVFWT3FDTzFiS3RtSytucjZDeWxrY3ZaN1JrNm1WRCtBeDFha3pteEIyeThTOGJ5VWY4VktXR3pRS2FhMkVBNElpcmxXS0NzNzY3NEM3aVdlb0VNRlM4VG1mV1RhQmV2eUZGaXZMcTN2aUxTek04dnZjR2dUSTUxamNkUW9CcmFRVkgrZEdwMWZqeC83cmxLK0Z1aGpzbUErRUlTNUg3dXh2VUF2cmV6R2R4Nk9RampBZytFL2dIK3hUZ0hwMmZKUlN5djJuK2hmTG4iLCJtYWMiOiI1NjY1MTAwMWNhODE2YTM1M2QzOGI5ZjExNjU1YTg3ODhmZTUxYjNmM2Q3MmUxNGMxYzFkYzRmZTM0OTM1ZmFiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:37:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZRWUVndVA5ZGVncUt0MGU3Q0kyRlE9PSIsInZhbHVlIjoiZUVmb0JLSlRvbzBFSUJNdTZyTWk0dWJuT3dXYlNFTGRZck93V2NMWk1VQmdPZzIzQ3haWlpYZlhwNUhlT1h0QVRDN2pzSW5lTnVkbVZrVVpJZlZKSGhWSVdUVTVTVmdTeFJDdVQ1TWNOYTU2a0ZKWHp5Qk8raU5NLzA2ODd3Z2RjK1dkd0pJanZhalR4VmRxNHB6alRNU1BFc0JkSEhXOTV5bmZIL3poWkxpTjhzRzZReHZETVZOK2Zudk5Wclp0MVd0NW9tdUdGZUFFSkZLbmJva0xyVW5sVlluZFB4eGkzQWVTNzBJSkp3SUFkQTA5SXEzc0FRLytaZ3ArbVVXU3NDZEQ1eFdFWVdYTGFnY0E4bWwxMXhLWE1qajhuVUpJTmFqL2x2cTVhTHNFNGtIMlBEWWNGN2toSVl4Q0dWVlZLL0ZKcCtyV1ZCUEFrQjlRcWRNYXhSVjlQbi96WXZpaTNzdG4vaFhLRkF4TTY0cWdBUVprN2R1dnRuL2NMVGdtdURBQlAyWldadkFlc2I4Q2Q0cjhTL3V6djlQOFlOaEJ1UjFIOEI3cUd4c1ZhOWthUlRkdHBBd1VXaUZWSllRdnAzclN3UlJvMHFzQTNKdGZxNVhvWkFZR25jbVQwOGlFWkVqckt2K2p6ekR4OWRiNzczSkNFaUppZFB3VGVRNEsiLCJtYWMiOiI1ZTlmYmIyZDE4ZjY1NDI0ZTM1NWIxNjE1OTFhZTFlZGI1OTkxN2I2Y2M0MTdhZjRkYTE4MWQ0NTBiMDIyNjc0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:37:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlQVWxDTitLZ3NySUx2RlBwb0lMVWc9PSIsInZhbHVlIjoiT3NjWDBPY3hxb2hoQmRyejJPWTJnRlhhWUNpNG8vV2JlK0R5SzA0aXZIaXBPZEdWV3ljWGRMRk50SHZBczcxM2ZMMEFPNGdEQ3Q3ZTVlM056amUwVlJZZEJGUXRkTE1mMjNBdUNEME4zbnM1QTlrb0dtM00vbEEwbklKUWhLS3JJVm9tQlV1V1FmKy9CdzZib3lzTEFBN2p3ekZyY21xZ292YmZsYnNEbkZHTldjWmw1THdlbDA4SDVtblcvenBZZ1V6ZlY1TjJOV2ROQ1BpVXp4d3Z2Y2w5TU43R3B5dkk0Tk5rSEE3ajFjcGxJalJ6VUhQOHlqLzZhZ1gyRUJXblhKMHY4YUp2bUdQTG5JK0U4aWtTUGRsSjh6empnWmdCa2R4TzVkSjNqN2NuZVNWN0RHWGE1TVFWT3FDTzFiS3RtSytucjZDeWxrY3ZaN1JrNm1WRCtBeDFha3pteEIyeThTOGJ5VWY4VktXR3pRS2FhMkVBNElpcmxXS0NzNzY3NEM3aVdlb0VNRlM4VG1mV1RhQmV2eUZGaXZMcTN2aUxTek04dnZjR2dUSTUxamNkUW9CcmFRVkgrZEdwMWZqeC83cmxLK0Z1aGpzbUErRUlTNUg3dXh2VUF2cmV6R2R4Nk9RampBZytFL2dIK3hUZ0hwMmZKUlN5djJuK2hmTG4iLCJtYWMiOiI1NjY1MTAwMWNhODE2YTM1M2QzOGI5ZjExNjU1YTg3ODhmZTUxYjNmM2Q3MmUxNGMxYzFkYzRmZTM0OTM1ZmFiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:37:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZRWUVndVA5ZGVncUt0MGU3Q0kyRlE9PSIsInZhbHVlIjoiZUVmb0JLSlRvbzBFSUJNdTZyTWk0dWJuT3dXYlNFTGRZck93V2NMWk1VQmdPZzIzQ3haWlpYZlhwNUhlT1h0QVRDN2pzSW5lTnVkbVZrVVpJZlZKSGhWSVdUVTVTVmdTeFJDdVQ1TWNOYTU2a0ZKWHp5Qk8raU5NLzA2ODd3Z2RjK1dkd0pJanZhalR4VmRxNHB6alRNU1BFc0JkSEhXOTV5bmZIL3poWkxpTjhzRzZReHZETVZOK2Zudk5Wclp0MVd0NW9tdUdGZUFFSkZLbmJva0xyVW5sVlluZFB4eGkzQWVTNzBJSkp3SUFkQTA5SXEzc0FRLytaZ3ArbVVXU3NDZEQ1eFdFWVdYTGFnY0E4bWwxMXhLWE1qajhuVUpJTmFqL2x2cTVhTHNFNGtIMlBEWWNGN2toSVl4Q0dWVlZLL0ZKcCtyV1ZCUEFrQjlRcWRNYXhSVjlQbi96WXZpaTNzdG4vaFhLRkF4TTY0cWdBUVprN2R1dnRuL2NMVGdtdURBQlAyWldadkFlc2I4Q2Q0cjhTL3V6djlQOFlOaEJ1UjFIOEI3cUd4c1ZhOWthUlRkdHBBd1VXaUZWSllRdnAzclN3UlJvMHFzQTNKdGZxNVhvWkFZR25jbVQwOGlFWkVqckt2K2p6ekR4OWRiNzczSkNFaUppZFB3VGVRNEsiLCJtYWMiOiI1ZTlmYmIyZDE4ZjY1NDI0ZTM1NWIxNjE1OTFhZTFlZGI1OTkxN2I2Y2M0MTdhZjRkYTE4MWQ0NTBiMDIyNjc0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:37:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894347768\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}