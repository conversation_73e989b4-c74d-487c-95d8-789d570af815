{"__meta": {"id": "X2b713b4cc2f759c86d4bf6247f7cc43d", "datetime": "2025-06-28 15:48:00", "utime": **********.161759, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125679.695466, "end": **********.161773, "duration": 0.4663069248199463, "duration_str": "466ms", "measures": [{"label": "Booting", "start": 1751125679.695466, "relative_start": 0, "end": **********.074894, "relative_end": **********.074894, "duration": 0.3794279098510742, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.074905, "relative_start": 0.3794388771057129, "end": **********.161775, "relative_end": 2.1457672119140625e-06, "duration": 0.08687019348144531, "duration_str": "86.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45180712, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02631, "accumulated_duration_str": "26.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.109342, "duration": 0.02489, "duration_str": "24.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.603}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.147059, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.603, "width_percent": 2.851}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.153229, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.453, "width_percent": 2.547}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-498876824 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-498876824\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-337243763 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-337243763\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-270879866 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-270879866\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; XSRF-TOKEN=eyJpdiI6IkRBMG81ZHNUUEJaL3pHeHBXanZMOWc9PSIsInZhbHVlIjoiNVB2dFVFckpaUnFYRlNDNGRHOGptR3lRV1Vvdmhvb2t1OWt4OVJkVFFHWk9FRzhCdkNkK1dyVkpLeXlOUU9aeldKSHhsL1Z5QUNEQUo0MEN3Mmo1ZEZCMzgwakh6Q0ZHUmErWW1WNk80cnRnaWVWWC9TUVZ1ZEVkK1ozUHhvRkw4bW51YkdZckRya3NKZU1rWDJPNi9KQmRrcjhLcDRtMkdIWEZXMHc0Z0FLbFJtbjVHYURtekJnajV3T043WG9mNXhWcS93cUVYMkdwMnlJbzFwQjIxbjhCWlptUDNaa3RJQzAwKzFKelF1Rm9CbUNmdW9OV2pWekQ1TnFUM3RNNUlkRTh5V0JWaUhrSWR1K1NOejlKT0JtdklFTTZwczdqTU1MaldhWStxUFJKVjgxek1lMHRtcVNFNFBuUUtJcWZzWHhEZWhkdk9TNGllbHExZUJtN3FQRzBiaGhRTXpSNVM0U3FjczY0TW5PU2xVK3dnMzFEMmw3bWJ4Z1JMVW9jL1VYSUFvdU1rcWN1N3kvSDhpNmxIVEkyUDdRTVZBRFRGTUhOZVp6YnlndnM1WDNjSXgxSVVNdXIwMGFDMjVPMkNNZ3lmVWVucktseVdSamlpUGdYdTlJeTVwTlU0QXdiWEVyRGZ6eWZCK3piNXNyWDV4SFFxVTUwNkE4d2Q5YkIiLCJtYWMiOiI5YmJhZGFjMjVmNTI2ZWQ3YWJhMWQ5YmRmMjk5YWRkNDhmZTVjNzdiNWJlMjBjYWQzODIwNDY5MmRjMjAzYzk4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9UbzhxNEZQL21qQXBjclM5eXV4enc9PSIsInZhbHVlIjoidXlxWU5tdzh5cG91TGlKTGw2OUQyN01WTkg5RVRkNTN0amdDQ01wUlhKK3d2QzNKMDNZVGRsd2FTdkozSHJDMU1yQ2ZSbjVQVHduM1U1eE04TnV3ODF4S2tZNXRQTXlYUEtNSkVPTHdFTDFDcExnQ1o3dmJtdDc1eXNtamhYYjlGMHdRUWQrdWdiOFkwRjgzUlN5dDViOEp0dUJRblVSa21tWTI4aklZLzNad1FtUGJET0VjZXFydnZ6THhSQjMvYUxaV05wYnB3Y2M5Y0hMYTBmRWdWMGxIU0E1eVZqUEpram04d1p5UmNrdy9ZWlNpbnhSMXNmVXlyVVlwK0ExaXh5S24raC9kOTQ2UXpNWDFVaVFtUUYrdEU2YzRkSVA1M05xbjNmbU9pSEdUaHkycmZaM2dyVHFnMytWNzRhQXdjTzFqTEg1NDREZUtsb3BiOTM2dmMrWkRKOXVBSlE2RUZVZFExL2t4ek5LQy9jNnMrMjdWU1NKWU9LVHQyZEJTbkh6S3dTUmxxVjIvb21WV2x2NmFQTm82VjdiTklwMXd1L05qUjJMdE8yN1BkWGRoLzVoTHlvcXJYRnE4UWVPLzZzTW85VE9DNGNxMnhDZ2VDS1lKM1NBYnVxWitRRG5mcXU0RVJuV0hnSWFqRC92bFNrYkNwa2JHN0krUS9lMlgiLCJtYWMiOiI5ZmI2MmQ2YzNhMGJkMDdkZDY4ZTA2N2I3NWViZmNmYTE0N2Y0NzlhM2FhODhkOTNlYjg1OGVkNzE3ZWU0ZjNiIiwidGFnIjoiIn0%3D; _clsk=mwmaeg%7C1751125654314%7C22%7C1%7Cz.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-56774308 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:48:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlF6SWFXcmg5bDJuKzcxelE2c2pGR1E9PSIsInZhbHVlIjoiWmFoQnlETTRNU1BQMWNkMEk1WEVFaXU3ZXpFOUR5THZhajBWUVAvbUt0emliUjNOTFZWdVJDaHF1TTR4VE5kQ2MraHlWVEJMVTZuSDN6a0VJUGFGZzN4c1UwVmdYa3g3V3U1Zk54OGFEMjlrZmdiaWVxMUxycE13SXBQRGNjdUJGdDlFdVUrQ0tCd1R6VWJzSnRNMXFJTDVxUTVFNmY5NWQrRjJHM3hZMHl5WHhlQ09IVEdBMzdWSy9tN2dPYTBPV2ljUGtYY2JVWXBWNWxIdS9rSFVzRVpWQmwxUnJFMmpOVkYwYVI3TzBDMW1Qa2o3d2Q4R0d4eWlITUhOQzBaOFo3TFdiRlVLRTgzY0E3NVJOaW1CMFBpWWNNbnJtZ1JlVHBrODBleU9paitDYVlzUElRcGJmUHovTGZSY1Noa1BBT0VWL1ovOEQzWW1wRU04TldQOTFJZUJ5bVUvYnVjSit2amFzYkw5NXNDd1lJSWgrN1R5VXpNa3NINlpEUTF3WXhYNFFRMmMwaFpaM0g5MGRNaGwvUE1XeGVRMk9EWEg5b0k3UEJLcHBjakQ0d2x4ajUzRDVJUDJnYVRtNzNWTXIwamVSdTkyRXRnL0lqN0VZZEVCMGphK3NlK25OV1lTQkpobnlqUXFYVHI0MVJxQUo3U3lteENtaWdGbG9JeFAiLCJtYWMiOiIxNjJiMzNhM2U3Zjg0MThjOTg4ZGJiZWYzNTExMjVkM2FmNmM5ZDQzYTY1ZDBmNWNkZjQyZTBiMjQxMWVlZjVlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:48:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVLM212TUlONWI3SEx3VU9vSTFsS2c9PSIsInZhbHVlIjoiYUQxaEU2VVVuK3BFR2FZRm5uVzV4U0V6cDJUVWxDcmF4d3Rsb29ZU0QvRVpuazlKcjRUNWlMN3ZKd0FLYWswaUtwOWNlNEdlcSt6bnVBSnhSYkJOVDVYV2J0YTduRUhEUy9ER0pRbFA5R2FkMHdIVzdidksvVVFMMkM1VU1TbW1pQ0M2QWE1TUtYMDBpS1djdFNQUHhYVnZEYUNpSGVJM3hNeVVWbVVWQ0h0OWxJTDFEVHhRYW9iK0huMEl5T05Fcjc4eFViZWFUMThXOVNHWCtrWURGS3ZwYnVyRWtkZlc5M3NJTklhSU13VXZ0Q0c1L2QyTXlCQ2dHeGxWTGoxcUs5bzl4TXBzMndhUGtLcFltdzBna3AzOEc5UUh3T0NmSXViUEJHaVQyV2FlN2NFWEMwRzFHczJlMlNid1NWR2hZWDlhVUsxWk9LV2V5ajg3dW5LbGM5a0k1TEhPQkk3SDJodHY2UjZPRmlGM1NWUGpoS28xdkNPVmJoczV0R3BWenU1MzgvbWpZU2RZc3RHaHlPTVpIaWpkcFBmUVdJSml3L0E3c2VXdU5BcnMrYng2amcrcDcwSVk0ZGdwN1ovSGwzQlhIZFlHRlhUY1ZDcmxJUXdIUVJtcU45WVk2UTZTS3QwYUpwbmx2NTB3a0dzWFlzbXZhVm42ZUxPeVRUMmEiLCJtYWMiOiJkYjMxODAyNmNjYWY5YWIwNzk4MTFhZTE1NDdjNTJjNjhkMDY0YjY1NjIzOTJjYjJhZTAwZGNiM2E4ZWE4ZmRiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:48:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlF6SWFXcmg5bDJuKzcxelE2c2pGR1E9PSIsInZhbHVlIjoiWmFoQnlETTRNU1BQMWNkMEk1WEVFaXU3ZXpFOUR5THZhajBWUVAvbUt0emliUjNOTFZWdVJDaHF1TTR4VE5kQ2MraHlWVEJMVTZuSDN6a0VJUGFGZzN4c1UwVmdYa3g3V3U1Zk54OGFEMjlrZmdiaWVxMUxycE13SXBQRGNjdUJGdDlFdVUrQ0tCd1R6VWJzSnRNMXFJTDVxUTVFNmY5NWQrRjJHM3hZMHl5WHhlQ09IVEdBMzdWSy9tN2dPYTBPV2ljUGtYY2JVWXBWNWxIdS9rSFVzRVpWQmwxUnJFMmpOVkYwYVI3TzBDMW1Qa2o3d2Q4R0d4eWlITUhOQzBaOFo3TFdiRlVLRTgzY0E3NVJOaW1CMFBpWWNNbnJtZ1JlVHBrODBleU9paitDYVlzUElRcGJmUHovTGZSY1Noa1BBT0VWL1ovOEQzWW1wRU04TldQOTFJZUJ5bVUvYnVjSit2amFzYkw5NXNDd1lJSWgrN1R5VXpNa3NINlpEUTF3WXhYNFFRMmMwaFpaM0g5MGRNaGwvUE1XeGVRMk9EWEg5b0k3UEJLcHBjakQ0d2x4ajUzRDVJUDJnYVRtNzNWTXIwamVSdTkyRXRnL0lqN0VZZEVCMGphK3NlK25OV1lTQkpobnlqUXFYVHI0MVJxQUo3U3lteENtaWdGbG9JeFAiLCJtYWMiOiIxNjJiMzNhM2U3Zjg0MThjOTg4ZGJiZWYzNTExMjVkM2FmNmM5ZDQzYTY1ZDBmNWNkZjQyZTBiMjQxMWVlZjVlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:48:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVLM212TUlONWI3SEx3VU9vSTFsS2c9PSIsInZhbHVlIjoiYUQxaEU2VVVuK3BFR2FZRm5uVzV4U0V6cDJUVWxDcmF4d3Rsb29ZU0QvRVpuazlKcjRUNWlMN3ZKd0FLYWswaUtwOWNlNEdlcSt6bnVBSnhSYkJOVDVYV2J0YTduRUhEUy9ER0pRbFA5R2FkMHdIVzdidksvVVFMMkM1VU1TbW1pQ0M2QWE1TUtYMDBpS1djdFNQUHhYVnZEYUNpSGVJM3hNeVVWbVVWQ0h0OWxJTDFEVHhRYW9iK0huMEl5T05Fcjc4eFViZWFUMThXOVNHWCtrWURGS3ZwYnVyRWtkZlc5M3NJTklhSU13VXZ0Q0c1L2QyTXlCQ2dHeGxWTGoxcUs5bzl4TXBzMndhUGtLcFltdzBna3AzOEc5UUh3T0NmSXViUEJHaVQyV2FlN2NFWEMwRzFHczJlMlNid1NWR2hZWDlhVUsxWk9LV2V5ajg3dW5LbGM5a0k1TEhPQkk3SDJodHY2UjZPRmlGM1NWUGpoS28xdkNPVmJoczV0R3BWenU1MzgvbWpZU2RZc3RHaHlPTVpIaWpkcFBmUVdJSml3L0E3c2VXdU5BcnMrYng2amcrcDcwSVk0ZGdwN1ovSGwzQlhIZFlHRlhUY1ZDcmxJUXdIUVJtcU45WVk2UTZTS3QwYUpwbmx2NTB3a0dzWFlzbXZhVm42ZUxPeVRUMmEiLCJtYWMiOiJkYjMxODAyNmNjYWY5YWIwNzk4MTFhZTE1NDdjNTJjNjhkMDY0YjY1NjIzOTJjYjJhZTAwZGNiM2E4ZWE4ZmRiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:48:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-56774308\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}