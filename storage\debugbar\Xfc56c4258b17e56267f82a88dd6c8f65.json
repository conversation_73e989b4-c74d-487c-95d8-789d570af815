{"__meta": {"id": "Xfc56c4258b17e56267f82a88dd6c8f65", "datetime": "2025-06-28 11:23:05", "utime": **********.589979, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.185617, "end": **********.589992, "duration": 0.4043750762939453, "duration_str": "404ms", "measures": [{"label": "Booting", "start": **********.185617, "relative_start": 0, "end": **********.539256, "relative_end": **********.539256, "duration": 0.3536391258239746, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.539265, "relative_start": 0.35364794731140137, "end": **********.589993, "relative_end": 9.5367431640625e-07, "duration": 0.05072808265686035, "duration_str": "50.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45438880, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00254, "accumulated_duration_str": "2.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5716228, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.472}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.581094, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.472, "width_percent": 12.205}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.583178, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 82.677, "width_percent": 17.323}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-612585488 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-612585488\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1589662977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1589662977\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-117271533 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117271533\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1098702641 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IkhCSkkxU2VoWXJCcUpCaHh5dmlrc0E9PSIsInZhbHVlIjoiSkhKT3FLZGlUL2RqZkt1eDV3ODUvRnBzMGdjdG55WTRFZDQycEZydFA5Tmd5bGJYemJ0bmhVTU0zWXVHd2MwcCtyRWVnQ0kvM01oNitxdTlOQ3hPTXpiOGVMZTlHSkNyR1RyUTFCQlNnQjJkMGNMeVpURjFrekZ0d2hGdytVVm9ZMHBjdnJJUkZTRHZRSXozdjJ0UkVYbjAxSm5MdXZDSXZUTUZoSFNDa3ZPbjNwUkFNVktyS3MzMWYrYzFHYlZILzlWL3lnK3djZExvMndrWjd3TXR5eUlNblJLWVl1encyTE1YQkhzdmQvVjlWOFFzbzlEcDlIbnRhV1JtY2ZUcHQ3S25tMW1uWFh1RjNMT3ltbnh0ZUVJYVliVGFGSTJ3eUt6eWptMHg0S25MODkyR0pRUk5VYjRubG1Wd2dBS2J2WmlBMWpUM0dxMVY5VTFnZWNvcE1vS1Q4emgxWEd0R3c1YllUNUhUdkxvUW1CQlR5ZWFOL1ZXVVIvTW5VbDA1ak41ckNYWVQxejlmSllmUmxHMWo1cUFEbUcxL05jMzh6ZGxlYWRLdllRNFdmbVNxRUVLT0RSUmxkUEJzK2tuK3BTeXVPYmt5dDhVVFR2dmVBaU0ydlJ6R1c0MFdtbzM3bUc2UkdNajk5ekR0SFNHUFF3cFVaQitGUWc3OUN6NDEiLCJtYWMiOiJhODVkZjRlNjU3ZTZkNzEzNWY4ZTc4ZGJkZGQxZTZlZmU3MjUwMTBmNGVlMjE5Mzg2MjA5MjU4MWMzMmRmZDI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVyQmdLRlJTbXdGT1FHRG9FSk8zdVE9PSIsInZhbHVlIjoiTG1CeGp3a0lBdm1qYktQb0dBWnltQVRtYmRaVzgveHpXUElrTTdnQjl4cWpwWE0wYU9OenJFb280d21zc3B5U1Q0Mll3SUxUZUxsRnh6c2J4MzQxR1pRWHAzc0tJKytMRVFyK1VpaWdJSlNsQWZFblY2ZThDeFJZYnErNTVJcGdMcnpjNGZzMzYyYVpWTnZ2RUFjVFFlVWtwNEUvRHlzOStwRjF6RDN0VTZxbzg0V25OQkM5VTFqay9IZWxnQnJVQWdrckZ3L3M2d1NOTitrM2NBWTVzTVk2TnVIZnlLajJWSGpicGRuYkRHTWgzNEEzSDFFZ0kxY1NoRm9MYUxSQzJRbDl0ZGJrSEQxa2xXQWg1dmRlaE11L1lvaEo5Nis0MTlYTEUvTXJnWmhWQ3BHOHo4a2FyWDNHY2xZQVh3YWFiOTBZU21MT0Nva1BvVEt1RGRtdC9mekRkNkRsRE5YZ0dzNTVlYXQxVTB3MHJUQlM5WnRvVzB4UFRkQ0J5ZmhON3p4dm5MZGNxdDhyejR5NFIrNkNyWnBIeWJkd0JQRVhkeFh3WGxDcWNoejNtSzBOeTVONjdaNkhNRGtmWTN4ckNRbkpIUktBNzZsNko1THowZzdWTTBTa1JObnQ2cTNYUEwrWWh6c2Q2b0tmSytBNkVHaXJGS1ZFSTVwRmV4SzIiLCJtYWMiOiIwYjIzMGM4ZGYyZDA0YWI1MWE3ODkxMDE5NDI5M2RiNTRjOWI4YjUyZTM1YjE2NjMxYWRhMDkxMTA0M2JiOTAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1098702641\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-574115085 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574115085\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1183259124 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inkzb0d3cTZEelc2UWIyNjlLOGJBTEE9PSIsInZhbHVlIjoibnE2SEEvVWxRRWlicE12Tm1rcGtsYXRDbWhXcm5nVHU5N2VnWUdMTDU0RFJvRW9tRVhzMDBvZmZvZGthSTdYTDZZaVJkeDRONjVqWVJDMHZIcGZrZDRpUWVhSjlQMEx0M2wwemFaakhNU0p3UVVoNW92VmowcFBIR2NnUkEzcFpNWHRDc3Zad1FSeVJTVU4zUXlHb2xtcUt6enQyS2k5Y2pRRnR1VUJQVmtkQ21RMTMyODBsNmhNR3dxT1drSUVjVUpnQW1kVEpMeUNZMEt4cDBUZ2dVRzNOdlRyQ1pDUVBLbDdWQVkvR05EYjFrOUJuZm1CN25pYnlHZG02cDFReHZNU1ZLT2xpYVF2QXUybDZKT3J4UXUrRU9NdHorTXRXSnVQVUZjclRhcEI3UzRSTk5odGxOMlRUUnBhNW9BMGFWcVRISDFuVUpPZmFMcVZNNUNGOVdIaDF5Q1V1MzBYWFlGaytaZnQrYVN3WllvWHNPb0hUOHdUWkkvMnJmWTJ1VUlIMWVNNjVIcFBLYzVOTitqMHE2UHB0clVpZlZKUlIyWkdEd3YvYzk2TDlLSmdkc1I0UWk4OEQ3Q2V4R0gyZ3l0OHU0ZXY2dGpLc0NIZEcxTGdaa2RDTVFiQlZQcGpLN1YzbHNOYjJLQUdPZG5oaEdoWmFiSy96UHRodmZEbEMiLCJtYWMiOiIxNmUwN2NlYjlkZjI1ZDc0ZDFmMmMxYzQ1MWMwMmRhMTBhZGYwMDQzMDM5M2E2YTNiNGQzZGQ4NGI2ZmIzODE2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJnVTlybU8rSE9YS2hUNGljNGRDYlE9PSIsInZhbHVlIjoiaEFXanBXWDdJaEtjdWxua2VoNFMzOUZQRk9XOXRiQzc1YkgyT1RzWUhpMWQ5Uk8vRDFVd0tUekFsdHFGYXMxRnFwR3IxV0JFTFlpQzFUVHhud21GUG0xVmRTVHMyaGZhTWFaWE1LM3M1aWcrQlhXbUVoUU5mV0wzTWJDVTRKcUt4dGpMWGc1dmVsQUlhdVRNOEwzK081RFJvOStyek1POXNLUVpMc0tSY2xGNDE5d3ZweWdycFkwTlRIMlRmb1NyeVZ3b1JrVTFqeXY2MWpMY1NWZGttaFBKMTVBS1ozTDMvcEZ3ZzgvV2RyK2Rva1F6UVZGTGpPQmlDN0JVYkI3R3dRU21jWENNbWdxQzBSMDEyMThjNXdMdm9qTEJQTVV0ampCNFhvNFZyZ0t5VEpBV3Joa09hYm5xc20yeFIyZVI0cDhHOXAyM2Y4M0NPcUUvVUo1WkRtVDNTbGFNTjhrZ08rQ3ZhRG1RdTZ1U3NoWDFCSVFBYVBIY1RNYUFFY3krcW55MjhDWnFXa1VMVitPcFBvZG9xZlM2SDhLNmVMLzUxNWNNd0M3Z2R4RUJ4MmE3OG5lRHFhckc0RVNQK00reEE4ZVRqdE5wdWNnR3VoVURSY3p0TGNEM0hnNE1UZ2FPTXJpM0l0TVdZSGxPMXZjWmRpUmY5bjQ1b0FFYXBYRFoiLCJtYWMiOiI4YTc4NzMxYmQxNzY0MjQ5MjQxNzljODBmNmQ3YmU4ZTZlYzgyOTEzZmYxMDQ5NjY3ZGYzOTUxNjlhMjQ4OTZmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inkzb0d3cTZEelc2UWIyNjlLOGJBTEE9PSIsInZhbHVlIjoibnE2SEEvVWxRRWlicE12Tm1rcGtsYXRDbWhXcm5nVHU5N2VnWUdMTDU0RFJvRW9tRVhzMDBvZmZvZGthSTdYTDZZaVJkeDRONjVqWVJDMHZIcGZrZDRpUWVhSjlQMEx0M2wwemFaakhNU0p3UVVoNW92VmowcFBIR2NnUkEzcFpNWHRDc3Zad1FSeVJTVU4zUXlHb2xtcUt6enQyS2k5Y2pRRnR1VUJQVmtkQ21RMTMyODBsNmhNR3dxT1drSUVjVUpnQW1kVEpMeUNZMEt4cDBUZ2dVRzNOdlRyQ1pDUVBLbDdWQVkvR05EYjFrOUJuZm1CN25pYnlHZG02cDFReHZNU1ZLT2xpYVF2QXUybDZKT3J4UXUrRU9NdHorTXRXSnVQVUZjclRhcEI3UzRSTk5odGxOMlRUUnBhNW9BMGFWcVRISDFuVUpPZmFMcVZNNUNGOVdIaDF5Q1V1MzBYWFlGaytaZnQrYVN3WllvWHNPb0hUOHdUWkkvMnJmWTJ1VUlIMWVNNjVIcFBLYzVOTitqMHE2UHB0clVpZlZKUlIyWkdEd3YvYzk2TDlLSmdkc1I0UWk4OEQ3Q2V4R0gyZ3l0OHU0ZXY2dGpLc0NIZEcxTGdaa2RDTVFiQlZQcGpLN1YzbHNOYjJLQUdPZG5oaEdoWmFiSy96UHRodmZEbEMiLCJtYWMiOiIxNmUwN2NlYjlkZjI1ZDc0ZDFmMmMxYzQ1MWMwMmRhMTBhZGYwMDQzMDM5M2E2YTNiNGQzZGQ4NGI2ZmIzODE2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJnVTlybU8rSE9YS2hUNGljNGRDYlE9PSIsInZhbHVlIjoiaEFXanBXWDdJaEtjdWxua2VoNFMzOUZQRk9XOXRiQzc1YkgyT1RzWUhpMWQ5Uk8vRDFVd0tUekFsdHFGYXMxRnFwR3IxV0JFTFlpQzFUVHhud21GUG0xVmRTVHMyaGZhTWFaWE1LM3M1aWcrQlhXbUVoUU5mV0wzTWJDVTRKcUt4dGpMWGc1dmVsQUlhdVRNOEwzK081RFJvOStyek1POXNLUVpMc0tSY2xGNDE5d3ZweWdycFkwTlRIMlRmb1NyeVZ3b1JrVTFqeXY2MWpMY1NWZGttaFBKMTVBS1ozTDMvcEZ3ZzgvV2RyK2Rva1F6UVZGTGpPQmlDN0JVYkI3R3dRU21jWENNbWdxQzBSMDEyMThjNXdMdm9qTEJQTVV0ampCNFhvNFZyZ0t5VEpBV3Joa09hYm5xc20yeFIyZVI0cDhHOXAyM2Y4M0NPcUUvVUo1WkRtVDNTbGFNTjhrZ08rQ3ZhRG1RdTZ1U3NoWDFCSVFBYVBIY1RNYUFFY3krcW55MjhDWnFXa1VMVitPcFBvZG9xZlM2SDhLNmVMLzUxNWNNd0M3Z2R4RUJ4MmE3OG5lRHFhckc0RVNQK00reEE4ZVRqdE5wdWNnR3VoVURSY3p0TGNEM0hnNE1UZ2FPTXJpM0l0TVdZSGxPMXZjWmRpUmY5bjQ1b0FFYXBYRFoiLCJtYWMiOiI4YTc4NzMxYmQxNzY0MjQ5MjQxNzljODBmNmQ3YmU4ZTZlYzgyOTEzZmYxMDQ5NjY3ZGYzOTUxNjlhMjQ4OTZmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183259124\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-707212560 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-707212560\", {\"maxDepth\":0})</script>\n"}}