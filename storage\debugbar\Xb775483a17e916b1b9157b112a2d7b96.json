{"__meta": {"id": "Xb775483a17e916b1b9157b112a2d7b96", "datetime": "2025-06-28 15:38:27", "utime": **********.949916, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.561897, "end": **********.949929, "duration": 0.3880319595336914, "duration_str": "388ms", "measures": [{"label": "Booting", "start": **********.561897, "relative_start": 0, "end": **********.900129, "relative_end": **********.900129, "duration": 0.33823204040527344, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.900139, "relative_start": 0.3382420539855957, "end": **********.94993, "relative_end": 9.5367431640625e-07, "duration": 0.04979085922241211, "duration_str": "49.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45884424, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2015\" onclick=\"\">app/Http/Controllers/PosController.php:2015-2087</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00199, "accumulated_duration_str": "1.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9315178, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 81.91}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.941274, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 81.91, "width_percent": 18.09}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-1109884425 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1109884425\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1690578512 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1690578512\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1933079496 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125105407%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImMxTW5kNlZkSWs4ZUpsMXlyTlNGK2c9PSIsInZhbHVlIjoiU012S0h6OTg5dHQwMXdRUWowWjlZcldWcisvUklyM3M0UFB4Ry81eElQWUw4bUNscHRwekFxaExUaThmZDhtTEQ5VCs1MHpFa0FKWlBLUTJiZ2ttZ3kvNFFMNlMvZWh3ZHI1Z0ZISlk1Rk1IcXdnSHh3WFBWU1Q4aFVlRDJpT2psMUs1VUF1cjA2cU52RzBuT0I2Q2MwOXNxL1piSzdXckh0d0N2OWJvU2JIbDBHdHBJNEt3Sk1COVdnaEoyMzA3Q20vU1M5YUY5ZkEwR1pQNi9PbWxZMkVqYk5uSXMwUExHUXFOZ2VjUDV0S2x5WEtxTjhWdm10RUpjZzJkUytpcWd0Znl4WGFCRXM3dW1tQTFVaWVuNVl4ZXpUWVdrWDRpMkd1ZkJoS291RVdwSzRFQjM4SnFBbDlyWWN2TEpUMjE2Q1ZSeFdKZTJhLzFSU0hnS1hLYjZ3Y296Y1c0bEF5T1lZRzVFdUNkOEpDbHBIcW5PY2s4NDRBWXIyTUl4OUxyTVVBWG5maUJwNnVnaG5OZjBOWEM1YWZxUC8xdXN2QTJWeVpwZ21Pb0kwUE9kMGNIQmZNR0hKMmZKbmJabWI1eGI5bWxBY1Z4d2NscnVyLy9Yd21TVG1WSDNNVWFDenMvVTFDWkJlZDFDNE9OdjlTbVNXSy9GcnR2YVFxNHFpclgiLCJtYWMiOiJmZTgyMjE4OGNiM2RhNDYwZWI0YWUzODQ1ZGZhOTMyYzY5YWM0N2Y5NDA5MDVhM2MxZTVmNTlkYTNkNzMxYTc5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImFOL3M2cEpjLzliaGhXR2JlaHpEOUE9PSIsInZhbHVlIjoiOU5SVVd2MFNwOENGT0M2VEV2K0NnQTJkajV3Q3QrQXB3Uy9teG9JajRVcFR4QTJtQ0ZZS3N6Z0cvZmNxYjdqSkJMenNyZ2xBTVhMZ2lXR1Zqek5DMDRuVUlMcDRZOFc5UFBodWZJNklOUTVXUkRtZlBYK0ZZTG5wenRhb2JXWTgrTGx6K21aRUh5aUtUeTNqcmI5cmx4QWwyZjZ3QWlwTTZLa1MyL1d0L0hIc29zMGdYY1ZSNnBmR1F1WVQzRUxxZTVIN0hkU2pqUExBdmkyNktRb1E3VGFSczRkUW94THB4LzJWYnFGejhsQ2lvY1p5SG93Y3VDZFVUMWJKRW5LYnRiMms1Z3BQRkovWGhka3czZlFXMFA3Wk5STXBnbzFTOG5ta0hCRHA1UGlENFhQME90UkRLVjc4TGI2Yjhsc0ljZkhQL0hoOXNQQnJYajdoNmJsZDVuUWlrOHBMUWRFcUhITlcwQWtHYXZJTW5uWjhMaWE2eFhaTVBhbFdaYVRmYVYrK01SNzBCRkxWRklRaWZDdlJaN3F6d0RTUlhMekpmeUYxQzZsckJLMXI4eC9rVDRiVUxlSTB4U1pmT3FUTEdBSG8zVkw1eTJialZLSmxGemcxR0RlYUs2UzFVUnlJSHUxOHVZT0VsOWFpTFd6SzMyb2tRU3RpNkVFZHA0RU0iLCJtYWMiOiIyMjYzMGVkYzgwZWYxNjZhYThiZWEwNTE3NzJjYTdiODg4YmZjZWM1NDM1YWY0NWM3NDJiNzhhNTg2MGU1OGVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933079496\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-261217002 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261217002\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-564649400 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:38:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFtV2IxNXpOeHJtcTZqZXp2TGNrVGc9PSIsInZhbHVlIjoiUkNZcjJnTlcwRk1wUEV3OUNoVHhBN2FnUmFFZEgxbzZDUE9Sb0I5eFpZcSsvbG9lZ1VKUXkwSmdxZUJoUXFxQ1JKN09iTEZ2cWNpS0puOEpGR0dJWGR2bTRlaE0xeSsxMDh3SDg3TVp0MWlpc0JoczIwdmQ0OENBVmFwdXFreDVzaDhwMGk2aUVXYVhCZmN1VEZ4U2R2Q3pGbkdnTDBoRXVPNE5UY0p1VkE1bjRIbnY2ZUo0ek1GbzdYNXp4MXVxZmNHYjdXOVFDdHV1Y2lPbVlva3NUT2VsUU4vdGtnTyt0VVBIenY4SjVxOTVJRWo1bjRMWS8xU2lIb1VMR1g0TjJ2Ti9vZXUvT04zQi9nU1k3SjFHSlNETHdtZmkxT0R4ZE5DTlEzS3BtVWRxT3RwOFNVa24zczY3N3NaaFl4WmI3bGFMdnVuLys0VlFFWlhIZkNtWW9NT2xPT0x2SEgwQSs3UXBjYklidU5JT0V5TnVXRjhpYURiZnp3aXF5akc5MGhoTElQWDZuV3dJT3kvdVhOU0hMQWxzY0d5Qmx5ZXlQSmN4MnM5MUsvWHhtcW5IQmw2cTF3S1hUc2phKzRGSjg2Ui9yVDNGdXk4S1Jtay9PUjgrZGxWMWV2WGc5SVY0ZDBxUDlRWVhBU24zdUZ1Mkg1aVFNMnpHU2ZUU0pSYlEiLCJtYWMiOiI4NGE4MTY4NWUwZTBkZTZiNDUwMzhhOTYxMTc3YzEyMmZmM2UwMDk0YTQ5YTU4Mzc3YzRiMDAzZmM2ZDQ5MTNhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJsYno2bVY5NDAvQ0FBbHl6S0FONVE9PSIsInZhbHVlIjoiR3pHSU5MaHJXaytObGR4Wk9vTzkrWndTc1NsSitwTXkxUmFYRXJvT1hQeE1qT1huWVQ4RndUaDl4ckROWWdROXE5amlFV2JCUy9RTnUwWG9qa1VmZFh4V0gyZnhUalZJWjE2Ym51QitTeW5tLzQ1aXlwNXBubUNHalBXVmtMTUc3MHBZSWpNV1AyczlhdnJjTnk4NFpJall6L0IvRTYxUDVXbjJjNDlvM29OZWRjM25WbGoyNnlIVWV3V2dkL3dvVk9BMFNBczEvS2ZrOGt2WFl0dklsYnlVZmp3elU3dGY2Z3JKY1RzNGFHSEpQSFh1Smd3dk1GLzZqTEd1VGZkWXR1Vm1ybG1KdUt2bDhyRWRDLzQxWVRnRmdpUG9KamZsVUN2dVNrWExGbHg1ZHN3SlVGbWdHMlNnMUdaTmVXUHM1c2JyRmtmOHBXeGR4bmxnOXJlNGdtUXpWQzJXSXVMQWFPODVMcC9zRGN3QWtzNWhGU29tVm5wMzJtZWV4cmJ5ZWsyZEs1ekJMb3RuQTMxeWRrMGVvRG1HaXdIV2hWb0JnZlJMaEJITHN4SUxWNGtsLzJIWXRDUG9PQzFQUGdTMk5wcGNPeUt1SzIwK0dGOVVuc294Q0pZQ29xUDZwdWFVQTBDS3R2U1VDNG9vSGpoVmJOb0l2N0VVWW5hM3JUR3YiLCJtYWMiOiIzMWM1Y2IyYWE5ZDQ5MDhhYjFlNDUwYjgxZWYwODdmZGE1ZmVmMjgxNjlkZDc2OGU3NzRkOTM2YjQwNDM5ODg2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFtV2IxNXpOeHJtcTZqZXp2TGNrVGc9PSIsInZhbHVlIjoiUkNZcjJnTlcwRk1wUEV3OUNoVHhBN2FnUmFFZEgxbzZDUE9Sb0I5eFpZcSsvbG9lZ1VKUXkwSmdxZUJoUXFxQ1JKN09iTEZ2cWNpS0puOEpGR0dJWGR2bTRlaE0xeSsxMDh3SDg3TVp0MWlpc0JoczIwdmQ0OENBVmFwdXFreDVzaDhwMGk2aUVXYVhCZmN1VEZ4U2R2Q3pGbkdnTDBoRXVPNE5UY0p1VkE1bjRIbnY2ZUo0ek1GbzdYNXp4MXVxZmNHYjdXOVFDdHV1Y2lPbVlva3NUT2VsUU4vdGtnTyt0VVBIenY4SjVxOTVJRWo1bjRMWS8xU2lIb1VMR1g0TjJ2Ti9vZXUvT04zQi9nU1k3SjFHSlNETHdtZmkxT0R4ZE5DTlEzS3BtVWRxT3RwOFNVa24zczY3N3NaaFl4WmI3bGFMdnVuLys0VlFFWlhIZkNtWW9NT2xPT0x2SEgwQSs3UXBjYklidU5JT0V5TnVXRjhpYURiZnp3aXF5akc5MGhoTElQWDZuV3dJT3kvdVhOU0hMQWxzY0d5Qmx5ZXlQSmN4MnM5MUsvWHhtcW5IQmw2cTF3S1hUc2phKzRGSjg2Ui9yVDNGdXk4S1Jtay9PUjgrZGxWMWV2WGc5SVY0ZDBxUDlRWVhBU24zdUZ1Mkg1aVFNMnpHU2ZUU0pSYlEiLCJtYWMiOiI4NGE4MTY4NWUwZTBkZTZiNDUwMzhhOTYxMTc3YzEyMmZmM2UwMDk0YTQ5YTU4Mzc3YzRiMDAzZmM2ZDQ5MTNhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJsYno2bVY5NDAvQ0FBbHl6S0FONVE9PSIsInZhbHVlIjoiR3pHSU5MaHJXaytObGR4Wk9vTzkrWndTc1NsSitwTXkxUmFYRXJvT1hQeE1qT1huWVQ4RndUaDl4ckROWWdROXE5amlFV2JCUy9RTnUwWG9qa1VmZFh4V0gyZnhUalZJWjE2Ym51QitTeW5tLzQ1aXlwNXBubUNHalBXVmtMTUc3MHBZSWpNV1AyczlhdnJjTnk4NFpJall6L0IvRTYxUDVXbjJjNDlvM29OZWRjM25WbGoyNnlIVWV3V2dkL3dvVk9BMFNBczEvS2ZrOGt2WFl0dklsYnlVZmp3elU3dGY2Z3JKY1RzNGFHSEpQSFh1Smd3dk1GLzZqTEd1VGZkWXR1Vm1ybG1KdUt2bDhyRWRDLzQxWVRnRmdpUG9KamZsVUN2dVNrWExGbHg1ZHN3SlVGbWdHMlNnMUdaTmVXUHM1c2JyRmtmOHBXeGR4bmxnOXJlNGdtUXpWQzJXSXVMQWFPODVMcC9zRGN3QWtzNWhGU29tVm5wMzJtZWV4cmJ5ZWsyZEs1ekJMb3RuQTMxeWRrMGVvRG1HaXdIV2hWb0JnZlJMaEJITHN4SUxWNGtsLzJIWXRDUG9PQzFQUGdTMk5wcGNPeUt1SzIwK0dGOVVuc294Q0pZQ29xUDZwdWFVQTBDS3R2U1VDNG9vSGpoVmJOb0l2N0VVWW5hM3JUR3YiLCJtYWMiOiIzMWM1Y2IyYWE5ZDQ5MDhhYjFlNDUwYjgxZWYwODdmZGE1ZmVmMjgxNjlkZDc2OGU3NzRkOTM2YjQwNDM5ODg2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-564649400\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-29978152 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-29978152\", {\"maxDepth\":0})</script>\n"}}