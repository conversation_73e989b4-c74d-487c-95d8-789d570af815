{"__meta": {"id": "X8ee58dcf8cbe52d779801245d48a2c08", "datetime": "2025-06-28 16:35:00", "utime": **********.898115, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[16:35:00] LOG.info: Enhanced POS Add to Cart Request: {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"price\": 2.99,\n    \"name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\",\n    \"is_manual\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.889848, "xdebug_link": null, "collector": "log"}, {"message": "[16:35:00] LOG.info: Manual product check passed {\n    \"is_manual_value\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.890009, "xdebug_link": null, "collector": "log"}, {"message": "[16:35:00] LOG.info: Product found successfully {\n    \"product_id\": \"2299\",\n    \"product_name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.892042, "xdebug_link": null, "collector": "log"}, {"message": "[16:35:00] LOG.info: Product added to Enhanced POS cart successfully {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"cart_count\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.89216, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.518192, "end": **********.898132, "duration": 0.3799400329589844, "duration_str": "380ms", "measures": [{"label": "Booting", "start": **********.518192, "relative_start": 0, "end": **********.845055, "relative_end": **********.845055, "duration": 0.32686305046081543, "duration_str": "327ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.845064, "relative_start": 0.3268718719482422, "end": **********.898134, "relative_end": 1.9073486328125e-06, "duration": 0.053070068359375, "duration_str": "53.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46047368, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2015\" onclick=\"\">app/Http/Controllers/PosController.php:2015-2120</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00247, "accumulated_duration_str": "2.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.877318, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.587}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8873649, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.587, "width_percent": 24.291}, {"sql": "select * from `product_services` where `product_services`.`id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2053}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.890653, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2053", "source": "app/Http/Controllers/PosController.php:2053", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2053", "ajax": false, "filename": "PosController.php", "line": "2053"}, "connection": "kdmkjkqknb", "start_percent": 89.879, "width_percent": 10.121}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1533131305 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1533131305\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1044518224 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044518224\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128497517%7C54%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdEVUdLZU9hRU10WFhnRjBhdjBpWnc9PSIsInZhbHVlIjoiSjFqR2p5OUtVdTdYVjAvRndOd1RMNVNFenNIdmxnQXd3ZjNsVklmWjBEbFpTWnMwY2VocVRzc3IyVmlEQkRXRUdhSHBnNHNXbHZBNlYrVk9xWWpCdzhYdHJQY08zMk8xSDFQWU5mZXpkb0dnWmdPM2MxdnhrSFpjR01pd1NveWJmc09xbEg0TFlscTZKc2VjOXhvRjdFZlFDM0JlSit3Y0VtRUk0YjhROURzbEJrWnpsN25iVUw3R1E0anNUNUdRQlpZVXN3c292RTlUQlVXWnlKc3dubkZ2TTBQU2t4R0REWUpId0hpMnFSUUdDZ05JR0VlQ0J6MGZQbU1hbEE0dHlMaUIybWFSYjlRWDQvaGZxMlRMaFpTSHdjY0VUU1UxTFJ5VXNMSkttaG56ZVlDK2QxekpaNEE0RGIxZXIySm1TUHRZb1pqNXBTZGpuZ2Rmb3JVdm5uNHBUMUUxTnVFZ2psMmZwQnpYSWpwTDM2ZHl1Y1hsaDlNOU4xeUFSSGFXNjNSN3lBWnRsSUFnL0NHNExHc00wVTlCZ0xtRGliNU9nSXRuOVhwcThYTk1rbFNOekJSamNZcGo4ZjBjRlovM3RnNUFIa3hkY3V4NEZYV3QvYVNaaUNnMGlTNFM2UUxLdHpacWhJTzZyZGJBS3pWU29YNENBd2pueStsVjFzNmQiLCJtYWMiOiIzNTkzZjlkZjFjMWNiOTYzMWE4MDYwZDI2NTg1ZTMyZDJhYTU0NWFjOWNiNjBkOWY2NWQxZmI2OTI3YTI1MmQ5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhPOExRb1BvWHBhUUZ1RkJsN0Uxenc9PSIsInZhbHVlIjoiSlI2amwvbFd3NXo3TXVZa2tvQ0FyY2pkRGtnUTMxb0pVSEE0UWpWalYrckhSNkl2YUJWUEI1UTd3RTUwM2xZcW42bjJZQXRrYWliZVdaQ1MxUlVtNjNRMGJZaDR0ZHdkeVg4MGxLelhFQVJTOEZhYWdKZW9SZjlRUk0zTTFTWUxzRXFNbzNZc3ZFWFdPTlZUU0VVYzh5Zjd0MHZ2eXZXSS9JY1I1SjZML1pHRTFEUkZwbWU2RXZhcEF6UU5EQW1FQ2pGNFFGa2ZXVHhaWjd0MWhaQmpMNTB4ZytNblIrdWJpbmNEcitMUG9UNVhKNzJDbG1Qc25udGNLU2R6bEZvTGF1R1FqL2R2N3lNUU9UVDJlRVd6bU1ybTl1elhyTWY4T2ZyWmh2cnJyNWhJY3NiU29Cb29LeWtSbkpYUElHdWZlaVBtL2E1NGFyQngyYUI1VENodVhBMTkzbTdtTHkzd0VOWWdtUnBRYTA2S2w2THcwMmI5QWVvRk14cDZVSDl3OEVwcThVM1prVC9vbFk4LzZjbG1yTktLbkQ4SVFoUHhFL0h3YVJGMExCMXBUN3Azb0ZLTGFFWGVidklWNWJJdXptSjJTc1JUVzducmVsc2xjTU1Ja2Rvd3k2TTBwcXJ6TEowdTRXUld0UWZyK0NMUEVGcElHM1YxUlBnNU9KZTMiLCJtYWMiOiJhNDc5NmY0MTUzY2M0OWE4OWJlNDNlYmQxYmU4ZGVhNzgzODJhNjFiZmQ1OTUwNjI0YzBkZGJlOWI1YzBkMjk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-577175130 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-577175130\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2066196728 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:35:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtOSXpBcVBubGRDTXBDMVlaWkhmckE9PSIsInZhbHVlIjoiNzB0TUJ5K2VOenMvT3JYNGF5N2xvMCtoNEkrK054MU5HZDgrcFFXdE5tOEYwMC9kSXhibGM0SWFsMlhUN09HZ3pXMnNxQnRrelNsS21ZZ3VZeThIa0FpTnQxMlI2OERBYTJnRTNValJGZklxcERSUDcrWFU0V285RWxvQjUyWjZCZUFSZE9VU2djV2hqUmJOMXBTVElrcXVzVmVqeHJBSktDRjJSMjdwMUJrK1Q4bDloZSt4VjlOcWZVMFB2ZW1HZTJBNmdjTUhncWhaV1BxM05WcFRBbWdIeTk0WlZmWkhIck0yM3ljcGFDME01TXhBODNnb2hXRE84UVpSUjlkeXJQSEhwYTQ0bTljQlJnR3JhZ2krdm5ZYkw2SURJc1A2eVdiR2hFQ1VLSW5QZTJQYmt1eWZabURIb0d4bEJWMWt0RHhtMWh4QVZ6bzEwTE4rRlhxV0FSMW5iMi9mNXNCQnpuYVNEelM4cmI5bitxMHhpTUFTaEFxWjNNdTdTZ0pEczVaR0lDRmVhQ2wvOFp6N2pyWWJYdFdXM25iR0Z3ZWNHRGNKemhTWEx3WVlxZWtSVi9TR2VCT0o3T1M2ZHlPRGhsUU5VemVTVExBVDdBWmZFSloxOUZGYnF2MDhvZDFqaHB2YWNWci9qSnkzQW4xenBjL1NFd1pHd0kvbTdnV3giLCJtYWMiOiJmZjRjOTYwYjExMjliZGMwNzU3NDk5ZjhjZjdmYTczMjg5N2RjMGQzNTJjYWYzZTAzZGE0Mzc4NGVjNWNkZmI3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijh6aG5rNkYxN3I4RFRGY2JKMmJEYUE9PSIsInZhbHVlIjoicnZnVVlYYnNwWWx5WHp5bCttRjlNeEdnWVhNOVUvWjFPS2NlWEtYZnJha2lmUlBaaHFUWjJkZS9JNDgxbTNveHY5bTFuWEFXYmdBdWphTzBlYVFCZEpDM3RMUis3b2ExWkIraGROMUp4TmgxQm9IUGs5Rk16SktpSTJsSVMyRnBLUWlBOGppVjIxeElmTVk4ai9DQnIwQmFMbzlmUkNiQWpUZk92UXR3WG1CSFYvUFhOT2I2ZFhlS1VGMVpGdnVHVVpMSDFDaWlzN3puRGMxNkxiN0RwQTdrVjZvY0tSeGdrdFJJWURKd2pJYTkwakdzc3hxYlRjVmNMdU5RREJjaTZra3lTZEp1Y2hjSDQrMGt2cmoxWXdqekVBRHFYSnpoY012bGcyQjN3Unk1WGw1N05zNW91blFRQS9POTNDWEN4blA5d1NLcjlMUzA3eHRvVVowbU9XbXJHSUpCSWJsdEpwaXJYelpFZkpDUTZMK05YODhLVzQ2ckkxdXNNZHpvNFRaQUswQUtEVWRaUHdKYlp0cG5OSlRVWE05WklBd01WR2NFcTFwbnk1SzBFK1RXbFl3TGowczkrK3NMcHB6UnF0aStTYlFIVlRXMzV3S05GL0xET1YvUWZLYXU1bDgyeng0dk1SaFJIUGttcW1iNElOZFV3M1lsZDlhQzFYQmUiLCJtYWMiOiIyZDgxMGMwNGY0M2U1YTBmNWY1NGY3ODlhZmE2YjBmMzk2NjE3N2NhMjViNjEyOGM3ZmU3ZWFkOTcxOGJlYmQzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtOSXpBcVBubGRDTXBDMVlaWkhmckE9PSIsInZhbHVlIjoiNzB0TUJ5K2VOenMvT3JYNGF5N2xvMCtoNEkrK054MU5HZDgrcFFXdE5tOEYwMC9kSXhibGM0SWFsMlhUN09HZ3pXMnNxQnRrelNsS21ZZ3VZeThIa0FpTnQxMlI2OERBYTJnRTNValJGZklxcERSUDcrWFU0V285RWxvQjUyWjZCZUFSZE9VU2djV2hqUmJOMXBTVElrcXVzVmVqeHJBSktDRjJSMjdwMUJrK1Q4bDloZSt4VjlOcWZVMFB2ZW1HZTJBNmdjTUhncWhaV1BxM05WcFRBbWdIeTk0WlZmWkhIck0yM3ljcGFDME01TXhBODNnb2hXRE84UVpSUjlkeXJQSEhwYTQ0bTljQlJnR3JhZ2krdm5ZYkw2SURJc1A2eVdiR2hFQ1VLSW5QZTJQYmt1eWZabURIb0d4bEJWMWt0RHhtMWh4QVZ6bzEwTE4rRlhxV0FSMW5iMi9mNXNCQnpuYVNEelM4cmI5bitxMHhpTUFTaEFxWjNNdTdTZ0pEczVaR0lDRmVhQ2wvOFp6N2pyWWJYdFdXM25iR0Z3ZWNHRGNKemhTWEx3WVlxZWtSVi9TR2VCT0o3T1M2ZHlPRGhsUU5VemVTVExBVDdBWmZFSloxOUZGYnF2MDhvZDFqaHB2YWNWci9qSnkzQW4xenBjL1NFd1pHd0kvbTdnV3giLCJtYWMiOiJmZjRjOTYwYjExMjliZGMwNzU3NDk5ZjhjZjdmYTczMjg5N2RjMGQzNTJjYWYzZTAzZGE0Mzc4NGVjNWNkZmI3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijh6aG5rNkYxN3I4RFRGY2JKMmJEYUE9PSIsInZhbHVlIjoicnZnVVlYYnNwWWx5WHp5bCttRjlNeEdnWVhNOVUvWjFPS2NlWEtYZnJha2lmUlBaaHFUWjJkZS9JNDgxbTNveHY5bTFuWEFXYmdBdWphTzBlYVFCZEpDM3RMUis3b2ExWkIraGROMUp4TmgxQm9IUGs5Rk16SktpSTJsSVMyRnBLUWlBOGppVjIxeElmTVk4ai9DQnIwQmFMbzlmUkNiQWpUZk92UXR3WG1CSFYvUFhOT2I2ZFhlS1VGMVpGdnVHVVpMSDFDaWlzN3puRGMxNkxiN0RwQTdrVjZvY0tSeGdrdFJJWURKd2pJYTkwakdzc3hxYlRjVmNMdU5RREJjaTZra3lTZEp1Y2hjSDQrMGt2cmoxWXdqekVBRHFYSnpoY012bGcyQjN3Unk1WGw1N05zNW91blFRQS9POTNDWEN4blA5d1NLcjlMUzA3eHRvVVowbU9XbXJHSUpCSWJsdEpwaXJYelpFZkpDUTZMK05YODhLVzQ2ckkxdXNNZHpvNFRaQUswQUtEVWRaUHdKYlp0cG5OSlRVWE05WklBd01WR2NFcTFwbnk1SzBFK1RXbFl3TGowczkrK3NMcHB6UnF0aStTYlFIVlRXMzV3S05GL0xET1YvUWZLYXU1bDgyeng0dk1SaFJIUGttcW1iNElOZFV3M1lsZDlhQzFYQmUiLCJtYWMiOiIyZDgxMGMwNGY0M2U1YTBmNWY1NGY3ODlhZmE2YjBmMzk2NjE3N2NhMjViNjEyOGM3ZmU3ZWFkOTcxOGJlYmQzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066196728\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2133051175 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133051175\", {\"maxDepth\":0})</script>\n"}}