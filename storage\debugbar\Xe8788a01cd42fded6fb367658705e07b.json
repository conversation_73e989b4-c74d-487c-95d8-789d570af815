{"__meta": {"id": "Xe8788a01cd42fded6fb367658705e07b", "datetime": "2025-06-28 16:08:02", "utime": **********.783073, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.369636, "end": **********.783086, "duration": 0.4134500026702881, "duration_str": "413ms", "measures": [{"label": "Booting", "start": **********.369636, "relative_start": 0, "end": **********.729557, "relative_end": **********.729557, "duration": 0.3599209785461426, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.729567, "relative_start": 0.35993099212646484, "end": **********.783087, "relative_end": 9.5367431640625e-07, "duration": 0.05351996421813965, "duration_str": "53.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45712304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00239, "accumulated_duration_str": "2.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7591271, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.435}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.768996, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.435, "width_percent": 19.665}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.774404, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.1, "width_percent": 15.9}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1950302094 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1950302094\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-915164348 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-915164348\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-423773641 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-423773641\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2101035063 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126675710%7C29%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVpaG14V1NncGRZT3l4NmZvcWxFWlE9PSIsInZhbHVlIjoiRGlwWmthSXhjNFllQVl3Ukt3ZXJsTW5yZnNQRk9kSHpvOWI3Q3pvY3Y2SVdrd3RXUGFzYkczaWhxUVVhWXZ2TFAxM3NUdlBZd0NuazFIM25GaVRCRzNvZ3gvNXR1Uk8rSjB3MlpOci9yenZwdENaUm5EZFJCTXNRMDhuT0ZUSktDOWd4SzFRV2V1RVlacURhTXM2SUpPaCs2Vk1OaW1Wc3Yzc1BvUDZxWVV4eGlKcENwVUZ2c2F1ZXhrQ2svRWRxUElZWWtFVFhZcm5saFZzL1NBbTF1S0NXaGF6WCtFWFhnVU1FdGE2dlEyc0pxQlFFZWJ6MGdiMkRFTGhZZ2NIR214a2lsTGR6MlliM3F4a1l4TFJDTEh4ZHB4by9iRk9zSVM0VmthSkRzL2c2Mk1tZ3V4MUZrbHRRWVpuUHk0cEdqU21yQXlYeWJqM2d1TnNmRVhTay80NmNCeVZKRmpyZnpxQWhKWER2WFFzV21KZVRWejZySW92N0g0MGdCam5ZeWYzRytod083alNPdmVpOFpwdHphNTFibXp0dzBncHBKaFIranFxQlNDRnUxcFI3KzRpMXRabFMwc0QrUjE2MWpmazlFV3FnRjM5eTFIM2RtSHZQaGFvSnZqeVc5SXZidXhpSEtWUjEwZ1RGUDY5NkpHdEZSdzYvR1dlb3BXVUIiLCJtYWMiOiIwZjY3MTZkZTQwZTg5YzBkZDhhNDhhNzFkMTdlZjY3NWM1MmE2MTRjNzA2NzJjYWQ0MmZmYTgwYTIwMWI3NTBlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImUvU2NPcnhuWXFqOWErb3Z2eEpMWEE9PSIsInZhbHVlIjoibHlMVFNQdlU1OVZ2SGx0WW9YcDhuOG1CMnlML1BNRTlnN0lIQndnU2pkYitWWjhlN1pDTGkzRXAwK2VOdjlZS2VWNUlOMlRySVUwR1BXUmFCUmFpSFZKdThlQ3lPNm9McGxqSEduaC85dDZNd3g4dVRQUklsWHovblBwclhESG1rN2hMc3FMN2k3dVMwVGRuOHZrTmFTeGc3Nm04dkxtcENXcVdic1NCMUtabUl1MmNxd0tnUmhxK21WL1dNRHkvRTBGSzgxcVp1QlpDYTZseW1YWW5wbEVTUlptd3ZFcGxJWXl6M0Nub0RSOThPcG9LcnBOTDRjWDVoNVJzVk92NDVwVWhtVlhzUTVCcHNpcUlzb3RiZzUreWc4WWxKREZId1Z5SGdCWURMMHRnTlB0RnNPaEM5SlNhbXU4aGZGc0VIRGNCNWlJSHJsSHZGcVRuRjZadEc5WWJZdThJZDFZejl5V1ZQTDZBOGw2ZzJkd1loaVh3b1huSm1YMVB4V2xTaTc5ZEo4a0hhck9GSWJXQ1J6aENJMWJ3ZDFBeXF5U0xwL204K2R1cjQ2cDQ3TU5nYWIzMk1JYVJTdUMwOFBrY01lemF5Ujk5QWZSMlA2NW1BK01tL1ZwcDJENDdpaUhPNmNVN0gvYUhrbEw4ck9zZ3BuVTRMUHFOUVBDR2ZLR0IiLCJtYWMiOiI2MWE2MmM1Nzg0NDc5NWQ1ZTRjNGIxZmJlNWM3ZGY4OTUyYTRmNzIyZDU2YWJmNTQ2ZjZlMDg3MTMxYmY1ZmZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101035063\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1320588777 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320588777\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1029733443 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:08:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhIeUUyR2ZtakIwdm56RzZYK2lac1E9PSIsInZhbHVlIjoiYnlaVCt6SEZWL3UyeTVhTDBUeUpMWGNTRW93SVlKSGdHUml1blU1RFo5K0d5T1U4dlYyaGF3SlU1SlUxQ0hvOUVVb1lNYlBsNDVTR3NXZE11TG9HeUMrSThRemVMNHVySEUrR0haMGcvT3BvODJZSk5yMlhSNUdOc0h5Qi9keTJxQ29RWWVLbWlWVVNWdE1zbjdCR0czemtoVDVyWHcwc0NqTjhqNzV1cWdybVBTWWptZ3V6VXFVbDNhQ3Y3MysxWWFLaDA0NSt2aDZSV0hZV2NKaEFKSld2cDZWUUNsWEZMRTE5NVBKeG5EdjVmcVlSck4rekhsamJ0QUxSVzk5YnFMZ0RJL0E5TVo0cG4rVUovMGRPaEphR0UzRFpFMkpheGpjeWZSQmJDZlhYNk5VVXRacCtGOTZJRE9BcGJZZzFXb2Q5WWVRamFUN1UxTjlHT0JiL1dzWVFpWW5waW5OOVIzZCtFUGJKcCtHTSt4a0JpUUNpa1JKZ3ZoS0M5dC9qSkFxWloycnJxUFY1ZFluTXVEWFNXR01ITDk1SVdsV0JBMkRHWndKT0plbWN0WUpTREJ2QVhQa2Q4bjFwSVNCUWtvZnFJdDNZR3ZHbU5jQS9nTmpneHVsY0xyeStucFhrcVRxK0wzRjRQaUxEc01CL2lFVW9rZTBXZC9xTjIrZmwiLCJtYWMiOiIwNDM4ZjFhMjM0ZjM5YjdiNzNkYjRlMGYyZTU0YzY4OTliNzRhZDlmNDk0Mjc1ODNjMWZhYTZmNGEyMzBjMGM4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:08:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNLQ1R0Ump2OEt3S0Y2TjZ0Yk1ReUE9PSIsInZhbHVlIjoielBGYkFqcC9tWjc5VllQcDgxTW96SS9aUWN5c0NTQ3d4bjA5Zzd4cm83REROcGVaUkNKL0x1dkFXY3dHSnpBN2NwMnhmSkNxaWt3OFZaZzFUN0h1QzNhRyt5czNVclMwT3pUOHVPOFFDV1JNaThlUXh2RlQzSVdQSWthTFlDN25Ya0l5RE5qZ0YxdDlhSS9HRDRSenNYcFZGbEQvSlZoRUszakNQdGhvWUZRc3YxT1I1WFpzWktzWEM0ZGhnVHhIQlc3SVFLVlRlZW9yWjRnYStaQjFkVHNkZnY0UC95TWdNajgzZml1TTJUYlNObi9rcHVnaXQvUStlY0k4NW50cHEvUUNqUy9sUG9rYnVsNjhteE5yZ3VDVkRJQ3JmYnJQTUFwM2FrajFySElMVzlwSlgvSUhoN0ViNVduaE93b3hZdmxCRzhzWllTeGJSQWZlYWdxdEREekhnaUxsaFloTFBQRkx3dC9pRkRnMEtaZTZiZzZpV2loM1l0WkkrY2xFUlJrcC85TWNzekFWMkRmajFjeWIrTDJlYzlZN3ErRGI5QUo2cHUxbFdWTWZsWENGZHZhN0YrRFNGeDIzcU5JNVlpQzNqYVZ0aWJ4RVBHRHlCVHAxQVFxd1RzNk1GT08raGE0TEJOY0QxT2h1dndacmM1TVJ1S2VkSWxDWVVpek8iLCJtYWMiOiJiMGQzYTVlOWFjZmIyZjA4MTk5YzcwNWZkODJlMDBjMzQ5ZTg0ZGQ2NWQzYjlhZDU1ZDFmNzMyMzhhMmJjNGRlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:08:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhIeUUyR2ZtakIwdm56RzZYK2lac1E9PSIsInZhbHVlIjoiYnlaVCt6SEZWL3UyeTVhTDBUeUpMWGNTRW93SVlKSGdHUml1blU1RFo5K0d5T1U4dlYyaGF3SlU1SlUxQ0hvOUVVb1lNYlBsNDVTR3NXZE11TG9HeUMrSThRemVMNHVySEUrR0haMGcvT3BvODJZSk5yMlhSNUdOc0h5Qi9keTJxQ29RWWVLbWlWVVNWdE1zbjdCR0czemtoVDVyWHcwc0NqTjhqNzV1cWdybVBTWWptZ3V6VXFVbDNhQ3Y3MysxWWFLaDA0NSt2aDZSV0hZV2NKaEFKSld2cDZWUUNsWEZMRTE5NVBKeG5EdjVmcVlSck4rekhsamJ0QUxSVzk5YnFMZ0RJL0E5TVo0cG4rVUovMGRPaEphR0UzRFpFMkpheGpjeWZSQmJDZlhYNk5VVXRacCtGOTZJRE9BcGJZZzFXb2Q5WWVRamFUN1UxTjlHT0JiL1dzWVFpWW5waW5OOVIzZCtFUGJKcCtHTSt4a0JpUUNpa1JKZ3ZoS0M5dC9qSkFxWloycnJxUFY1ZFluTXVEWFNXR01ITDk1SVdsV0JBMkRHWndKT0plbWN0WUpTREJ2QVhQa2Q4bjFwSVNCUWtvZnFJdDNZR3ZHbU5jQS9nTmpneHVsY0xyeStucFhrcVRxK0wzRjRQaUxEc01CL2lFVW9rZTBXZC9xTjIrZmwiLCJtYWMiOiIwNDM4ZjFhMjM0ZjM5YjdiNzNkYjRlMGYyZTU0YzY4OTliNzRhZDlmNDk0Mjc1ODNjMWZhYTZmNGEyMzBjMGM4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:08:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNLQ1R0Ump2OEt3S0Y2TjZ0Yk1ReUE9PSIsInZhbHVlIjoielBGYkFqcC9tWjc5VllQcDgxTW96SS9aUWN5c0NTQ3d4bjA5Zzd4cm83REROcGVaUkNKL0x1dkFXY3dHSnpBN2NwMnhmSkNxaWt3OFZaZzFUN0h1QzNhRyt5czNVclMwT3pUOHVPOFFDV1JNaThlUXh2RlQzSVdQSWthTFlDN25Ya0l5RE5qZ0YxdDlhSS9HRDRSenNYcFZGbEQvSlZoRUszakNQdGhvWUZRc3YxT1I1WFpzWktzWEM0ZGhnVHhIQlc3SVFLVlRlZW9yWjRnYStaQjFkVHNkZnY0UC95TWdNajgzZml1TTJUYlNObi9rcHVnaXQvUStlY0k4NW50cHEvUUNqUy9sUG9rYnVsNjhteE5yZ3VDVkRJQ3JmYnJQTUFwM2FrajFySElMVzlwSlgvSUhoN0ViNVduaE93b3hZdmxCRzhzWllTeGJSQWZlYWdxdEREekhnaUxsaFloTFBQRkx3dC9pRkRnMEtaZTZiZzZpV2loM1l0WkkrY2xFUlJrcC85TWNzekFWMkRmajFjeWIrTDJlYzlZN3ErRGI5QUo2cHUxbFdWTWZsWENGZHZhN0YrRFNGeDIzcU5JNVlpQzNqYVZ0aWJ4RVBHRHlCVHAxQVFxd1RzNk1GT08raGE0TEJOY0QxT2h1dndacmM1TVJ1S2VkSWxDWVVpek8iLCJtYWMiOiJiMGQzYTVlOWFjZmIyZjA4MTk5YzcwNWZkODJlMDBjMzQ5ZTg0ZGQ2NWQzYjlhZDU1ZDFmNzMyMzhhMmJjNGRlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:08:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029733443\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-682300350 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682300350\", {\"maxDepth\":0})</script>\n"}}