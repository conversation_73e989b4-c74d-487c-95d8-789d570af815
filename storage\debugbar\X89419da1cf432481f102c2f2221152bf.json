{"__meta": {"id": "X89419da1cf432481f102c2f2221152bf", "datetime": "2025-06-28 16:34:59", "utime": **********.041206, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128498.591735, "end": **********.04122, "duration": 0.44948506355285645, "duration_str": "449ms", "measures": [{"label": "Booting", "start": 1751128498.591735, "relative_start": 0, "end": 1751128498.978759, "relative_end": 1751128498.978759, "duration": 0.3870241641998291, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751128498.978768, "relative_start": 0.38703322410583496, "end": **********.041222, "relative_end": 2.1457672119140625e-06, "duration": 0.0624539852142334, "duration_str": "62.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45878568, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0071600000000000006, "accumulated_duration_str": "7.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.011404, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 21.369}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.021357, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 21.369, "width_percent": 9.358}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%جالكسي كيك الكراميل 30جم%' or `sku` LIKE '%جالكسي كيك الكراميل 30جم%') limit 10", "type": "query", "params": [], "bindings": ["15", "%جالكسي كيك الكراميل 30جم%", "%جالكسي كيك الكراميل 30جم%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.024348, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 30.726, "width_percent": 35.615}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.029702, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 66.341, "width_percent": 29.888}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1995}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0339742, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1995", "source": "app/Http/Controllers/PosController.php:1995", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1995", "ajax": false, "filename": "PosController.php", "line": "1995"}, "connection": "kdmkjkqknb", "start_percent": 96.229, "width_percent": 3.771}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1978422980 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1978422980\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2029452604 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2029452604\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-92969618 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92969618\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">140</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128497517%7C54%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhQQUU1RlNrUmJNTk9mR0lNNThzUVE9PSIsInZhbHVlIjoiU1UvUVc3SmRiZXMyaDM4SnlrOHlNNDF0bmdOdkJJZWk5RjJ2ZFZHaFE1VG56Q2Y5ckRWcXJNckczaUR3SkM2MTY1ZmlyclpWeTlxZDkxdVZRZ3YwSmhJazI2V3RiZjR4TTVjanBJNVJtNUFjMlNkVHNpME5LU0pSanRtSjFla0h1dnhQdTFhUnpBQi91R3ZXQVBEQysvcjRyVUJkMDJuUkVOc0VMRDN1MU1qZlIva0VYUkhacCtNNVo2RVlTMldWOFJKNW5aTVp2OHBuN2pSa1k2Y21CWXFpSUs2cDNubEd2bmpxbmhvanAzM3JiazRJaFZxSTVTZmxJV3BsekxQWVhkK2Z3ZExpTnQvTi80UXFwejIxTlRNVy9kOHp6eEp0bGtUZm03Rld3bVU5a3B6U0Z1NUw5NFZZUjNMTkpHaGlvS3QxVXg4cndDbzN3WDlvamNEdWVvcDhJNS8yZkpQNmlOY050RXB0Z3NiSElmamp4YVVpQWhSUWhJcmZwMkNrYnJRMjNGS2poRVhCTkhmOFBhYlhvTGF1YUdhZjJPU2tRb2ZnT0VMMjlpOUQxaUxsSHRiZlNmQ1hjcmJyY3JUbUlWQ2dkMVVHNXZuald1YktTWWtPM0E0TmFmVFNwUFUrbkp1aTRmUTludUhhdWRUTHFVc3l1Vi9PenY4azA4N3IiLCJtYWMiOiI3OGMzNGM2YzY3NGVhYThmYzcyYTc1NDI5MDE4YTgxNWYxZWU5MWJiY2M3ODI0NzBiZGY0MDJjZTUwNTgwMTY0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZzNXJwdERBaGxNWTdSN3h5cFY1T3c9PSIsInZhbHVlIjoiUWdjZFcwekV3KzZYUWtOTTlEQmloSW1OcG9FbmM4b3pGb3ZVZTRKRUpSYy9HOHFpUnBQdUFFTWZwZGM1bytrbkZKQVAvMjdxUnBaRS9OSktuZDIrNllqNHhzQ2FpWjJndzZUd1NBQUQwOURhNUlTZysvMVozY3VkdmU2ekNwMXZTbjRRcFJKMjV4MlNCbEplTjBiU1UvMmRuRGI3UDNLMXdhbzVBRXpxdU04dnNqbUJzak1KZVMwWUVxMGIyRVpUN1owVG0wQUFKbExlblhFOXIyWmdWbGxIVVEyazRzaGRNSW53VHNUQzdYRmthUzhXQXY2WkdMbHNLY1JVQWlEaXdlbGlLSlFGK2VVYUxqRTRMYzhBa2hmOEJOajJXa085Y2JiOEtpWnhSWVJDeFRVRXhHazJQQzJuckt4YjBESE9MWkZjMndKOW4vYitYbTNoMjh6KzdwT0pPbTIwTDYzTmpDQk1lV2ZoTmNGd21HUVdDUGh2Wk5TWUNnMUdLcEdVRFo2c3lUNXBVeTI5czVtQjNJdGgyZlNxV2ZZNE1wV1JoZjF1Qk5obmd0Q2xUYndmaEtnNk5IZWtNa2NraFJCYzBORWpDbTdOT1VmcnBtajB3RU1aTTBMN1NyMTlNdFRBRGxKQUNKNXFnaVNQQnNoN2xvMGR3bFBRSVRReTNNcVEiLCJtYWMiOiI2OGE0ZGFjMzk5MjVjYmU4OGE2MTUwOTI0NjdlMDg0MDg2NWIxOTQ1N2RkMTMxYjA2MTE2NWYyYWQwNTAyMDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2070785059 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdEVUdLZU9hRU10WFhnRjBhdjBpWnc9PSIsInZhbHVlIjoiSjFqR2p5OUtVdTdYVjAvRndOd1RMNVNFenNIdmxnQXd3ZjNsVklmWjBEbFpTWnMwY2VocVRzc3IyVmlEQkRXRUdhSHBnNHNXbHZBNlYrVk9xWWpCdzhYdHJQY08zMk8xSDFQWU5mZXpkb0dnWmdPM2MxdnhrSFpjR01pd1NveWJmc09xbEg0TFlscTZKc2VjOXhvRjdFZlFDM0JlSit3Y0VtRUk0YjhROURzbEJrWnpsN25iVUw3R1E0anNUNUdRQlpZVXN3c292RTlUQlVXWnlKc3dubkZ2TTBQU2t4R0REWUpId0hpMnFSUUdDZ05JR0VlQ0J6MGZQbU1hbEE0dHlMaUIybWFSYjlRWDQvaGZxMlRMaFpTSHdjY0VUU1UxTFJ5VXNMSkttaG56ZVlDK2QxekpaNEE0RGIxZXIySm1TUHRZb1pqNXBTZGpuZ2Rmb3JVdm5uNHBUMUUxTnVFZ2psMmZwQnpYSWpwTDM2ZHl1Y1hsaDlNOU4xeUFSSGFXNjNSN3lBWnRsSUFnL0NHNExHc00wVTlCZ0xtRGliNU9nSXRuOVhwcThYTk1rbFNOekJSamNZcGo4ZjBjRlovM3RnNUFIa3hkY3V4NEZYV3QvYVNaaUNnMGlTNFM2UUxLdHpacWhJTzZyZGJBS3pWU29YNENBd2pueStsVjFzNmQiLCJtYWMiOiIzNTkzZjlkZjFjMWNiOTYzMWE4MDYwZDI2NTg1ZTMyZDJhYTU0NWFjOWNiNjBkOWY2NWQxZmI2OTI3YTI1MmQ5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjhPOExRb1BvWHBhUUZ1RkJsN0Uxenc9PSIsInZhbHVlIjoiSlI2amwvbFd3NXo3TXVZa2tvQ0FyY2pkRGtnUTMxb0pVSEE0UWpWalYrckhSNkl2YUJWUEI1UTd3RTUwM2xZcW42bjJZQXRrYWliZVdaQ1MxUlVtNjNRMGJZaDR0ZHdkeVg4MGxLelhFQVJTOEZhYWdKZW9SZjlRUk0zTTFTWUxzRXFNbzNZc3ZFWFdPTlZUU0VVYzh5Zjd0MHZ2eXZXSS9JY1I1SjZML1pHRTFEUkZwbWU2RXZhcEF6UU5EQW1FQ2pGNFFGa2ZXVHhaWjd0MWhaQmpMNTB4ZytNblIrdWJpbmNEcitMUG9UNVhKNzJDbG1Qc25udGNLU2R6bEZvTGF1R1FqL2R2N3lNUU9UVDJlRVd6bU1ybTl1elhyTWY4T2ZyWmh2cnJyNWhJY3NiU29Cb29LeWtSbkpYUElHdWZlaVBtL2E1NGFyQngyYUI1VENodVhBMTkzbTdtTHkzd0VOWWdtUnBRYTA2S2w2THcwMmI5QWVvRk14cDZVSDl3OEVwcThVM1prVC9vbFk4LzZjbG1yTktLbkQ4SVFoUHhFL0h3YVJGMExCMXBUN3Azb0ZLTGFFWGVidklWNWJJdXptSjJTc1JUVzducmVsc2xjTU1Ja2Rvd3k2TTBwcXJ6TEowdTRXUld0UWZyK0NMUEVGcElHM1YxUlBnNU9KZTMiLCJtYWMiOiJhNDc5NmY0MTUzY2M0OWE4OWJlNDNlYmQxYmU4ZGVhNzgzODJhNjFiZmQ1OTUwNjI0YzBkZGJlOWI1YzBkMjk4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdEVUdLZU9hRU10WFhnRjBhdjBpWnc9PSIsInZhbHVlIjoiSjFqR2p5OUtVdTdYVjAvRndOd1RMNVNFenNIdmxnQXd3ZjNsVklmWjBEbFpTWnMwY2VocVRzc3IyVmlEQkRXRUdhSHBnNHNXbHZBNlYrVk9xWWpCdzhYdHJQY08zMk8xSDFQWU5mZXpkb0dnWmdPM2MxdnhrSFpjR01pd1NveWJmc09xbEg0TFlscTZKc2VjOXhvRjdFZlFDM0JlSit3Y0VtRUk0YjhROURzbEJrWnpsN25iVUw3R1E0anNUNUdRQlpZVXN3c292RTlUQlVXWnlKc3dubkZ2TTBQU2t4R0REWUpId0hpMnFSUUdDZ05JR0VlQ0J6MGZQbU1hbEE0dHlMaUIybWFSYjlRWDQvaGZxMlRMaFpTSHdjY0VUU1UxTFJ5VXNMSkttaG56ZVlDK2QxekpaNEE0RGIxZXIySm1TUHRZb1pqNXBTZGpuZ2Rmb3JVdm5uNHBUMUUxTnVFZ2psMmZwQnpYSWpwTDM2ZHl1Y1hsaDlNOU4xeUFSSGFXNjNSN3lBWnRsSUFnL0NHNExHc00wVTlCZ0xtRGliNU9nSXRuOVhwcThYTk1rbFNOekJSamNZcGo4ZjBjRlovM3RnNUFIa3hkY3V4NEZYV3QvYVNaaUNnMGlTNFM2UUxLdHpacWhJTzZyZGJBS3pWU29YNENBd2pueStsVjFzNmQiLCJtYWMiOiIzNTkzZjlkZjFjMWNiOTYzMWE4MDYwZDI2NTg1ZTMyZDJhYTU0NWFjOWNiNjBkOWY2NWQxZmI2OTI3YTI1MmQ5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjhPOExRb1BvWHBhUUZ1RkJsN0Uxenc9PSIsInZhbHVlIjoiSlI2amwvbFd3NXo3TXVZa2tvQ0FyY2pkRGtnUTMxb0pVSEE0UWpWalYrckhSNkl2YUJWUEI1UTd3RTUwM2xZcW42bjJZQXRrYWliZVdaQ1MxUlVtNjNRMGJZaDR0ZHdkeVg4MGxLelhFQVJTOEZhYWdKZW9SZjlRUk0zTTFTWUxzRXFNbzNZc3ZFWFdPTlZUU0VVYzh5Zjd0MHZ2eXZXSS9JY1I1SjZML1pHRTFEUkZwbWU2RXZhcEF6UU5EQW1FQ2pGNFFGa2ZXVHhaWjd0MWhaQmpMNTB4ZytNblIrdWJpbmNEcitMUG9UNVhKNzJDbG1Qc25udGNLU2R6bEZvTGF1R1FqL2R2N3lNUU9UVDJlRVd6bU1ybTl1elhyTWY4T2ZyWmh2cnJyNWhJY3NiU29Cb29LeWtSbkpYUElHdWZlaVBtL2E1NGFyQngyYUI1VENodVhBMTkzbTdtTHkzd0VOWWdtUnBRYTA2S2w2THcwMmI5QWVvRk14cDZVSDl3OEVwcThVM1prVC9vbFk4LzZjbG1yTktLbkQ4SVFoUHhFL0h3YVJGMExCMXBUN3Azb0ZLTGFFWGVidklWNWJJdXptSjJTc1JUVzducmVsc2xjTU1Ja2Rvd3k2TTBwcXJ6TEowdTRXUld0UWZyK0NMUEVGcElHM1YxUlBnNU9KZTMiLCJtYWMiOiJhNDc5NmY0MTUzY2M0OWE4OWJlNDNlYmQxYmU4ZGVhNzgzODJhNjFiZmQ1OTUwNjI0YzBkZGJlOWI1YzBkMjk4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070785059\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2003973364 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003973364\", {\"maxDepth\":0})</script>\n"}}