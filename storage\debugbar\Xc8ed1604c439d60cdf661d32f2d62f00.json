{"__meta": {"id": "Xc8ed1604c439d60cdf661d32f2d62f00", "datetime": "2025-06-28 15:50:23", "utime": **********.283499, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125822.804764, "end": **********.283524, "duration": 0.4787600040435791, "duration_str": "479ms", "measures": [{"label": "Booting", "start": 1751125822.804764, "relative_start": 0, "end": **********.222826, "relative_end": **********.222826, "duration": 0.4180619716644287, "duration_str": "418ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.222837, "relative_start": 0.4180729389190674, "end": **********.283526, "relative_end": 1.9073486328125e-06, "duration": 0.06068897247314453, "duration_str": "60.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46402272, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2388\" onclick=\"\">app/Http/Controllers/PosController.php:2388-2422</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00289, "accumulated_duration_str": "2.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2648509, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.931}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.276028, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.931, "width_percent": 20.069}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1460/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1318407312 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1318407312\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1822527757 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1822527757\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1293978524 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1293978524\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1862735321 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125486476%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpNMUVpdmpkT1VVUXRLMDlaL0ZIY3c9PSIsInZhbHVlIjoiU3N0bkgwREFtbGVaZVEybHZ0U0tZeUt2bGtOV0NnL283L05sR29ZY3pQZExtUEhHN2RSdG5qclhCN3Q0d1pFL0dqWTg5UVNXWWVvaUxrY28yUEpyMVI2TzVZallVUzVNWkIvdDJaWTNZc1ZKYXhqaU54MEZwYXgxSmpmeEpXOUp5U3lhcTBnSmkremY5YzIyME1oeS9XSGdjbm0zdHExTDJhV1B0a29RbWVMM2t6V2hwY1Jvb0ttMmZKbWZWWWl6U24xS3orTzRjOFFobHVhWlpBRFA4bjBDSGZZTzkyN3JqK3Y4K01zeTVTRjZ0TGZ4cTU5MU1OOFJ3ekNKS0xwK05OT1RSOHNhb0Vwd3hBUGFoYnVlenNCb2dkUDJLMWkvWFE1enhsd2xFUGNMTm0vOGJjRGVWcWdBTWF5dkpzTjRhMTZLVmVpWHlPSVIzNTJTT1JOTWpUUlVsOUFSdWQzNFhDbE5ObmIwMW5qbFhubzZab1Axdnk0TDRKMGVmdkM3SjBSc09YZmNEdExhLzI5S05ZV2NuS0xuTXJKb1ZIa0N0bG1DOGdCTzVJK3RYYkpscUlLbjlTZ0xKQUZWYU9QQWZpOUNWeUdvazhBYTBLZ3k3Wm9QaVhxNlEyNWhuMjhaU1N5ZmFLSXNLbENkb3E2eGpmUTRkMWxxWlI1eE5IN1QiLCJtYWMiOiI5MjMzYjk3MjBmZmI5ZmJiYTExYjdhOTNlYWJiMWFlMWY3ZDA0NTE1OTRjYmY4M2NmZjNjMWI3Nzk1NjRlYzAwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhHMmljT3dFUmtVRWMxbi84VTcxWHc9PSIsInZhbHVlIjoiMlpxTjhsajBlbkJQbWtZTmJTVFZLRDZZVlpzazRwWUgrV1hGek5sSkx1RjNSWWZjclFCWVZ4R0UycE1ZelVFN1pUem1IMU9lWjF6VnFkZ2xjc3RocjJ1RFJCTjFzRllaU2dDbGw4Z2owWlRUWTNjTDQyNElMc2JMZ3ZuVVFIdzlPNTQ5UnYwZVRVd2RMVnRIWkdHOThieVE2MXd4dmtOTGpnY2Y0V2ltQk4rTjJaSHhtNnMwMTA1Mng0YTFncytrd1orck5PcVhNZTh2c2o4WFRtaFJXaVNLYkc5TDlMaFpvYWpTNFdZYnNtV3NBQkEvbGFZdndmd1BQb1RpZFRjdUQzRDhyVjhDbDYvbFk2RnBCM0E3aURWRzhXdGI5TXhmekxkaUhxU01PQUNHandNTlNYVlU5Sko5MWhhSGFRdFpxdWthOFY0RGJGYndjNHdmdUM4Q3lYZ0NZeEIrRGRVTERvd2dYeTFFUjJYYmhtc3NLRUJRQ2ltNXdMWXdqaGhoQVVRZ1ZIU2xwdXRoeStsYVQxZ2tBQ1BQaW9rbXEvSkJrYmJablJ4UW9DL0FGU0hJQ3JxckJGeElsMGlEd0J1SE1uL3hLbFVNRHFDODA3aVJ4QStVN0M5SkM3L3BYWmNiWXFLTjU3ZGJjL0VaeUVienNKS21xU24raDB1K2RqUDAiLCJtYWMiOiJmMDRlNGMwNzY2MWFmZTg1Y2RlMTQ3OGU5NTMxNDNmYjAyMzc3ZmNhZDJmYmFlMDcxYmYwZWVkOGZkNjNkZDQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1862735321\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1933296645 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933296645\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-546805505 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:50:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik84UHhKdFhXS0dqdjlTY2xiaEVIb0E9PSIsInZhbHVlIjoiREtKa0k2TUNRWDlJc29DakphdkdOd20yejg4Z2VGT2p3YlBJeXRzcXJvVTZld0EraXhoSnVCTFF2K1ZXL3Vneks3aEcwTkdWMHpyY3BlRXFQY3ZyVVEvZFJwcS9qZjEzVGE5TURITC9RUklWOXF3WEFjWTBzcDdCOEtmYkpLRFBjN2s5TDNhaFJUaEFTQ2NyZmFjNHM3ckNWVy9Bd0NiZmN5UTRGN0diZFFNTVY4NGFad3padjg1ZllaVEJtc241djIvZUhmRzhRSjdscnJlZVpIa3lZZDZxaTQvVlIwKy8xMTU5VUJHbzMrb1NBeURQbXpWV0FjVGU5b1EyNjhCWlh4RytIY21hMUhyMVJVaXp3Zlp4U3NTV1VMd1JUZ2hGcnd4eC9CaWpQYWVyVnNoRjdacDNHbmhkZ0hLRTlXM1dYVXk1UEljTmZWS1BTdjhoY0tBWjF4OXhFbk0zYmVIM2JtdHFWTnYzR3pDTjdJVVRRYnNUM0U4cVlmZFJkUXdKd1l2SlBnWDl4RUpqYWlic0RHTURYYWYyU2hPa0RGcGxiV0ZRaHpudWFOZWZVRHJGcDc3UU80WGZDd3lHdTFkNjZ4emVZTUVtaUhSY1dTdnplVkY3bk5qc2tXVzNUTER4Q0J5d3MrTFlUS0JtbHVWUE1HKzdMWll5VmVGbEpHdTYiLCJtYWMiOiI4ODNiNjY3MzVlOTVjNzcxODcyMDk4NGQ4NWUxMGNjZTUxMDgyNjkxMzViYzk5ZDFhNDJmMGIxNGQ3YjY3YThmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:50:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJ3QU1HM0VEc21waHgzWTV0d01ML2c9PSIsInZhbHVlIjoiZHF0MWJ1L1NxZ08vNXJiUGs1azZPOXJtQ1IrSmU2Q09TVS82VTQvKzlXcnlTa3FLKzhHMGd0WUM3cWZNNTNQYkFiaUh6eFJ6RDFaZklaMlk0dnQ2cklUajJ6VzhSVVlXQks4MTZ6VkZhaGROc2xGZGNoQ0taK2dkblFEL3dMSXFUQjZHMjJFVVZkb1dCMHNjdlVJSXRqbEJhVW1CeE8vRC9TOHlrUlVqNGo4anlHYUUzRXVoU1VSWDdqZ3lKM3Z6OUZWWkl0OWdsMk9ScFJNb1VuUDJHeE9pUWo3OVgyLzltd3RHU25hWXRKc0ZPaWswc093bVl5cm5IYUhNMHk4S0UvZEp0Mkc1UE4wbjJNdHNZdkFud0U5OWRMSUJwWHBXM3diY3lNSzd5M3BkT1djK0Frd0tiMmV4Q1NYTFAzVVdyd2pUTWU2aHZFellXNENhRnVEZkhIbmdaZnZ2azY0eHJoeVZ6OXQ2VzU1QjFmZm1mT3ovVmJZRXIwR0hQVnZQZ2J1WVhFeXhpWGExbFhVR25oN1hEeVpIQXBQZG9qb1EzZjlYbEswRnNLTXBQalI2ZHVSbUlrcUZVTG4rSkJuSUdwcU9ob204NHhZOGJNZERhTHNhQVA4M0ttV0RldXZPWENGYi9OaUdhVElFdFkycHgxVDNuMm90VHZ3VCtiblYiLCJtYWMiOiJhOWMwMDExY2FlYWJkNzU1MzllYWNlNjNlMmNhMTJlM2EwNDdiMzA2NGZhNGIxNmNhN2EyM2MzZjY4NTc3YTJmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:50:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik84UHhKdFhXS0dqdjlTY2xiaEVIb0E9PSIsInZhbHVlIjoiREtKa0k2TUNRWDlJc29DakphdkdOd20yejg4Z2VGT2p3YlBJeXRzcXJvVTZld0EraXhoSnVCTFF2K1ZXL3Vneks3aEcwTkdWMHpyY3BlRXFQY3ZyVVEvZFJwcS9qZjEzVGE5TURITC9RUklWOXF3WEFjWTBzcDdCOEtmYkpLRFBjN2s5TDNhaFJUaEFTQ2NyZmFjNHM3ckNWVy9Bd0NiZmN5UTRGN0diZFFNTVY4NGFad3padjg1ZllaVEJtc241djIvZUhmRzhRSjdscnJlZVpIa3lZZDZxaTQvVlIwKy8xMTU5VUJHbzMrb1NBeURQbXpWV0FjVGU5b1EyNjhCWlh4RytIY21hMUhyMVJVaXp3Zlp4U3NTV1VMd1JUZ2hGcnd4eC9CaWpQYWVyVnNoRjdacDNHbmhkZ0hLRTlXM1dYVXk1UEljTmZWS1BTdjhoY0tBWjF4OXhFbk0zYmVIM2JtdHFWTnYzR3pDTjdJVVRRYnNUM0U4cVlmZFJkUXdKd1l2SlBnWDl4RUpqYWlic0RHTURYYWYyU2hPa0RGcGxiV0ZRaHpudWFOZWZVRHJGcDc3UU80WGZDd3lHdTFkNjZ4emVZTUVtaUhSY1dTdnplVkY3bk5qc2tXVzNUTER4Q0J5d3MrTFlUS0JtbHVWUE1HKzdMWll5VmVGbEpHdTYiLCJtYWMiOiI4ODNiNjY3MzVlOTVjNzcxODcyMDk4NGQ4NWUxMGNjZTUxMDgyNjkxMzViYzk5ZDFhNDJmMGIxNGQ3YjY3YThmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:50:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJ3QU1HM0VEc21waHgzWTV0d01ML2c9PSIsInZhbHVlIjoiZHF0MWJ1L1NxZ08vNXJiUGs1azZPOXJtQ1IrSmU2Q09TVS82VTQvKzlXcnlTa3FLKzhHMGd0WUM3cWZNNTNQYkFiaUh6eFJ6RDFaZklaMlk0dnQ2cklUajJ6VzhSVVlXQks4MTZ6VkZhaGROc2xGZGNoQ0taK2dkblFEL3dMSXFUQjZHMjJFVVZkb1dCMHNjdlVJSXRqbEJhVW1CeE8vRC9TOHlrUlVqNGo4anlHYUUzRXVoU1VSWDdqZ3lKM3Z6OUZWWkl0OWdsMk9ScFJNb1VuUDJHeE9pUWo3OVgyLzltd3RHU25hWXRKc0ZPaWswc093bVl5cm5IYUhNMHk4S0UvZEp0Mkc1UE4wbjJNdHNZdkFud0U5OWRMSUJwWHBXM3diY3lNSzd5M3BkT1djK0Frd0tiMmV4Q1NYTFAzVVdyd2pUTWU2aHZFellXNENhRnVEZkhIbmdaZnZ2azY0eHJoeVZ6OXQ2VzU1QjFmZm1mT3ovVmJZRXIwR0hQVnZQZ2J1WVhFeXhpWGExbFhVR25oN1hEeVpIQXBQZG9qb1EzZjlYbEswRnNLTXBQalI2ZHVSbUlrcUZVTG4rSkJuSUdwcU9ob204NHhZOGJNZERhTHNhQVA4M0ttV0RldXZPWENGYi9OaUdhVElFdFkycHgxVDNuMm90VHZ3VCtiblYiLCJtYWMiOiJhOWMwMDExY2FlYWJkNzU1MzllYWNlNjNlMmNhMTJlM2EwNDdiMzA2NGZhNGIxNmNhN2EyM2MzZjY4NTc3YTJmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:50:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546805505\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-806932879 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1460/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806932879\", {\"maxDepth\":0})</script>\n"}}