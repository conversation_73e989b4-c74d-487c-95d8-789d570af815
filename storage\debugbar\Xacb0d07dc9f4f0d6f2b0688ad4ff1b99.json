{"__meta": {"id": "Xacb0d07dc9f4f0d6f2b0688ad4ff1b99", "datetime": "2025-06-28 16:01:27", "utime": **********.822932, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.236728, "end": **********.822949, "duration": 0.5862209796905518, "duration_str": "586ms", "measures": [{"label": "Booting", "start": **********.236728, "relative_start": 0, "end": **********.75368, "relative_end": **********.75368, "duration": 0.5169520378112793, "duration_str": "517ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.753707, "relative_start": 0.5169789791107178, "end": **********.82295, "relative_end": 9.5367431640625e-07, "duration": 0.06924295425415039, "duration_str": "69.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45846584, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00476, "accumulated_duration_str": "4.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.796702, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 37.815}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.808956, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 37.815, "width_percent": 11.134}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"produ%' or `sku` LIKE '%<div class=\\\"produ%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;produ%", "%&lt;div class=&quot;produ%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8119311, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 48.95, "width_percent": 51.05}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1859322160 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1859322160\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-714355473 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-714355473\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-740809010 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&lt;div class=&quot;produ</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740809010\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-367882354 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">44</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZDUDBsMlRXYUkzelJxT2JnaWxtb3c9PSIsInZhbHVlIjoiNnlnVGU2VjNwMWEyVWdkWW4vRFhRUUxTZ0FhbFVTamJBamJ5dHQzSFUzOU1IUFU4bE55QVNxWkcycHZqcnZwd29PY0hzb21FcVAybXZaK1k1ZnNFa21DU0hJUmV2UEF5L0JhWlVwMmZTWHVTQ3oyTU8zd1pqZ01YclJwSWpjMDZYNHgvMkVvOG45ZkpuNmhMRjlhTjRhV0V1UDVCVTJJYnlESktSYkdKZ0d5Y0FWNHZsVk1BRldCZDZCOE1GUkpQWE9wbGxMaXArdG8zUGY2MytIWGY3bm1MQmFCT2FoR1dGRUdTbWxiZ0VweWh6VUhtZHhma29YOHNFRFhxcFJYK3VSTStETGdYTGFYRE14T09uZ1c0RUpJdDA3MHU1c1VxTHVtcStoSlVtaGM3YU8vSC9Lb3ZleVcyYXB3NVRiblRIK3J0NGdHNnV3eldxcHRka013ekZNOEhsOGFuUVphd3NQZTNrbC8zY2VISmNlS21NY3Y4QlBDaGtOQWs1T0ZXYTJwTDVmM2NCZ2ZZc2w0dzg0aW9NYmlUK3RCQjFaOW1vWjVtSk44VGZmWFI4MzVKZEdrRzRPOERCa3FsR2YrQVRiNTNvaTJPNUh2V1M3K2pLVGZHdVl5YldpTDIvWDZsdU4zZWN2Z09YUDlMM2tVT0Q3aklJTDc2U1dudU95WEwiLCJtYWMiOiIxODk0ZTM0N2Q3NjI5ZDk2NWQ4OTdhMmNiOWRjYjkwZDM2MGU0ZWEzNzk2NDBkMDc1MDcwMTFiZDBkNGQ5MGQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpGY2thL2E4Znp4QnIwSG9ZVmM3ZUE9PSIsInZhbHVlIjoiaFRXb0VhQjhjakFiYWkzTjI2RUo3b3VrUGhkdDlVQlZEQkpERlpQK3ZuNTJSckc2dGRKYkxiRE9zM3pYcHFEZkg4V1c5VnZoWUEzQ1JodU84VmVsRFNSNWtDTXh5aFh4TXBpZGRvS3ZzN0lzNkxnZmhxNzI3cHNUVmxoVkNyV0RIMTdKdU95VmR1K1B5a0g3UndXR2RpaHVhdG9mNlF6NmNlT1l4ZXpOaVRTWWtBaUVxVXord1FWQlNxSEtMSkpKcUVQWTROWEZTSlFOM2E2YjgyNHJmcS9iVFBRRWJRSXA5UGRpek13cW1wMEhEc3F1UW5JUTBJeTYzK0N6TEtxRUZ6SjhrK1ZqMUd1YXViZXVyWWo2YTBHZVNtdC9CdUR2UFF3RlprV0d1ZkFnOU5BTUttckErR0kxUzlWbkZWMmhyeCtxQXk1UFdzWDA0UkpFTjhoUFY5NlpTSXlSczMxbVFvRVpZWitqZjdrWFNIWDhUcHJkUTJ6bVVKSmErQ0hFb1N1dHVpN1QvendZc2xQbytGM0xtamh3WFhtYmt2R2w3SmVNL05iR1BFM1Vtdzh2UEdCMmtPdUFXT0VzNmZoaWk5bFZFYThVK3dRUVgrbzhZOFRTV2ZOQ051MzVuWmpZclhER1Q2Y3k2Z0NjTTRheEk2b1NEdXZqZWh3eTRpY1oiLCJtYWMiOiIxZGY1ZDFkYTM0MmMwMjk2OGU0MjBjN2RlYWYyNDFiZGYzNDA4Y2EyMmUxMzQwZTEzNmRjZmM3NzExZTVhZjFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367882354\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1445894414 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1445894414\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1930984221 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1uZFc0Y0lOYUpLTmZBNWxUOC9XeEE9PSIsInZhbHVlIjoiUVd3d2g3UlJFOUhBaHlmVTZMNmRtLyt3SHplQkkzWmdUKzJsYnJHcm5ESFEyOERoL1VTUzE5WTlOUVl5ekNzVU8vZmU3YlNGeTgxaHROY1FPbnRhc1ZJZWYreS9VaVdScjdheUJSMHp4aTAva2FCUHR1K2dnQ3NkZ3pQcVpEaHNLWVFiVDBkeVp6cllrMUYzb0RHbXM4VXdhNmE5WEY0Nm0xMmhVNVc5WDU5V3V6UEIzSnFYdk45aEVUbGFZUXEyM3ZKcmhabnFuQ0VIeFJIWFB5THVBb09hcWIzM2ZlUkxHeXBCUlhtWnArVXo4aXgrdHQzK0p5WVoybkVudkhjUHR1VEZBUW5adjY2SmRNeXFodHRZU0hycmlVUEMzVjRXL05MWGRwVFljU05uNUcxKzZFNzdqRTNrRjFWSWlEbkJjT3lRTzc1cFEyZGJ1YTZkVlVXbm5NZXFpeXRCbjlTaTlyRGNKVXg4T2Zqd0tIN0ZmMXdUU1kyQzJZRFp1bzEzNUNaWjdjWDRJM01ENldMUUY4L1lMVVhsSjJ0VHZ4VjZwc1ZmZWQvYURXSXRVc3FRazR3MDlTTC9UZTBmcnFtL0hQLys3N3JleEF6dUdOdjFZSUZPYVdGUU9XeTVtMHI1T1RQT0xTZWJLNVAwVE8yaWY1R3hYM2Rxc0FPMzZacHciLCJtYWMiOiI2NzAwZjkyYWE0NzUzNWVkMjdkOWYxOTUxYjg3NmFlNGQ5ZmFmNGNmODY0ZjI1NzZjZWNkYzFiOWFiMzg4YTU2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlR4QThVV2pBUzMzY3lBS0huMDlHM0E9PSIsInZhbHVlIjoiNDhkOFNJT0FNU1dWMENzOHVoUUIzNGtpYWFKRXgvU3NJNTU5cWI2VmhLK2I0Uys3V3FZWHN4M2pYZisyWEpDN3N1R3BkS3p3ai9saEp1aGRRYnluekxlK2g2Wm13Y3RyUDRjNldma1duL2ZUMkc4cjVxVkNEdjVvNlRlOVVvMFFlaUdZUUx4dTl3eVl1aWdzYkJWRXZZZHVtZUgzUnp5b3JYTllvTVF4bXVaVXlkVTBYcnc3c2UzRVpvV2FUT2R2TXRQeXpxS2w2N21PODJuZHdVQ1VLWU44VTVXK00zMFJuSmZTZzFXZHdRelBQYmtKYjY0ZWZ6UDB5SlQ4YXhlbkU1bE50VHFHY0Z4TVcrdEY4Z3BiYnNaSk5ZRmtOU3BiODNHZ0RrTXFuSDdQbC9BTTcxeStYemdvbDVUajR0cUFFdGlmWEVRK1VtOWt0TUNPYWIzMmxLdHgvK3YzUlZVL1I2dm9wdFJySnBhNDdWNFBDL0dndDJ1cVZNWE9CcFQxN21KR0FPNGsvTHY0NVRuME15OVZ2a3dlUk13Nmt0aFpEdG5CUmtqWnFKN0JnbEl4MUVkKzM2Z0dxeE1HenVQTllKN0RBUTEyVWpuTzNTNng2RlMvUzNmaUt4OC9OMlN0Tm80ZjNjUzFxWnRUUHliTTI3b2JvYjdXRFRzNHlvU0QiLCJtYWMiOiJkZWNjMmY4N2RhYzI4MTRhODFlOTJlZjkzM2Q1YmJhNDk3ZDA4MTUxZWZhMjcxZTFmZGE1MmQ4YWI5ZTUzYzk4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1uZFc0Y0lOYUpLTmZBNWxUOC9XeEE9PSIsInZhbHVlIjoiUVd3d2g3UlJFOUhBaHlmVTZMNmRtLyt3SHplQkkzWmdUKzJsYnJHcm5ESFEyOERoL1VTUzE5WTlOUVl5ekNzVU8vZmU3YlNGeTgxaHROY1FPbnRhc1ZJZWYreS9VaVdScjdheUJSMHp4aTAva2FCUHR1K2dnQ3NkZ3pQcVpEaHNLWVFiVDBkeVp6cllrMUYzb0RHbXM4VXdhNmE5WEY0Nm0xMmhVNVc5WDU5V3V6UEIzSnFYdk45aEVUbGFZUXEyM3ZKcmhabnFuQ0VIeFJIWFB5THVBb09hcWIzM2ZlUkxHeXBCUlhtWnArVXo4aXgrdHQzK0p5WVoybkVudkhjUHR1VEZBUW5adjY2SmRNeXFodHRZU0hycmlVUEMzVjRXL05MWGRwVFljU05uNUcxKzZFNzdqRTNrRjFWSWlEbkJjT3lRTzc1cFEyZGJ1YTZkVlVXbm5NZXFpeXRCbjlTaTlyRGNKVXg4T2Zqd0tIN0ZmMXdUU1kyQzJZRFp1bzEzNUNaWjdjWDRJM01ENldMUUY4L1lMVVhsSjJ0VHZ4VjZwc1ZmZWQvYURXSXRVc3FRazR3MDlTTC9UZTBmcnFtL0hQLys3N3JleEF6dUdOdjFZSUZPYVdGUU9XeTVtMHI1T1RQT0xTZWJLNVAwVE8yaWY1R3hYM2Rxc0FPMzZacHciLCJtYWMiOiI2NzAwZjkyYWE0NzUzNWVkMjdkOWYxOTUxYjg3NmFlNGQ5ZmFmNGNmODY0ZjI1NzZjZWNkYzFiOWFiMzg4YTU2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlR4QThVV2pBUzMzY3lBS0huMDlHM0E9PSIsInZhbHVlIjoiNDhkOFNJT0FNU1dWMENzOHVoUUIzNGtpYWFKRXgvU3NJNTU5cWI2VmhLK2I0Uys3V3FZWHN4M2pYZisyWEpDN3N1R3BkS3p3ai9saEp1aGRRYnluekxlK2g2Wm13Y3RyUDRjNldma1duL2ZUMkc4cjVxVkNEdjVvNlRlOVVvMFFlaUdZUUx4dTl3eVl1aWdzYkJWRXZZZHVtZUgzUnp5b3JYTllvTVF4bXVaVXlkVTBYcnc3c2UzRVpvV2FUT2R2TXRQeXpxS2w2N21PODJuZHdVQ1VLWU44VTVXK00zMFJuSmZTZzFXZHdRelBQYmtKYjY0ZWZ6UDB5SlQ4YXhlbkU1bE50VHFHY0Z4TVcrdEY4Z3BiYnNaSk5ZRmtOU3BiODNHZ0RrTXFuSDdQbC9BTTcxeStYemdvbDVUajR0cUFFdGlmWEVRK1VtOWt0TUNPYWIzMmxLdHgvK3YzUlZVL1I2dm9wdFJySnBhNDdWNFBDL0dndDJ1cVZNWE9CcFQxN21KR0FPNGsvTHY0NVRuME15OVZ2a3dlUk13Nmt0aFpEdG5CUmtqWnFKN0JnbEl4MUVkKzM2Z0dxeE1HenVQTllKN0RBUTEyVWpuTzNTNng2RlMvUzNmaUt4OC9OMlN0Tm80ZjNjUzFxWnRUUHliTTI3b2JvYjdXRFRzNHlvU0QiLCJtYWMiOiJkZWNjMmY4N2RhYzI4MTRhODFlOTJlZjkzM2Q1YmJhNDk3ZDA4MTUxZWZhMjcxZTFmZGE1MmQ4YWI5ZTUzYzk4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930984221\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-763044268 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-763044268\", {\"maxDepth\":0})</script>\n"}}