{"__meta": {"id": "Xf5711f19cc0ffe06b077804cae29c396", "datetime": "2025-06-28 11:25:11", "utime": **********.83851, "method": "GET", "uri": "/pos/1454/thermal/print", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 163, "messages": [{"message": "[11:25:11] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.809154, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.809395, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.809464, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.809529, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.80959, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.809649, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.809708, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.809768, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.809833, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.809898, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.809956, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810016, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810073, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810133, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.81019, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810249, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810308, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810366, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810426, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810484, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810545, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810604, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810663, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810721, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810781, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810845, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810907, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.810966, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811026, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811084, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811144, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811202, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811262, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.81132, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811381, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 80.60000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811438, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 84.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811498, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 84.20000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811557, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 87.60000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811616, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 90.20000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811674, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 92.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811734, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 95.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.811922, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 98.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.812116, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 98.60000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.812237, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 100.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.812345, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 102.20000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.812436, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 105.60000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.812517, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 107.00000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.812578, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 109.20000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.81264, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 110.60000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.812697, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 114.00000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.812755, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 115.40000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.812811, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 118.80000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.81288, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 119.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.812938, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 122.40000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813016, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 123.8000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813078, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 127.2000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813141, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 129.8000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.8132, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 132.00000000000009 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813263, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 133.4000000000001 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813322, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 135.60000000000008 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813384, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 138.20000000000007 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813447, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 140.40000000000006 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813509, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 140.60000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813568, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 145.20000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813629, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 145.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813688, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 148.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813748, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 151.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813807, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 154.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813867, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 156.20000000000005 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813925, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 158.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.813985, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.814043, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 164.40000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.814103, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 167.00000000000003 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.814161, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 169.20000000000002 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.814221, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 169.4 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.814278, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 171.6 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.814338, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.820413, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.820518, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.820589, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.820657, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.820728, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.820794, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.82086, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.820924, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.820994, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821061, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821126, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821196, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821261, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821327, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821394, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.82146, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821524, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821589, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821653, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.82172, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821786, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821851, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821916, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.821981, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822046, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822111, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822176, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.82224, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822304, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822369, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822433, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822497, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822562, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822627, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822692, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822757, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822821, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.822919, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823007, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823096, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823163, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823228, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823301, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823382, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823463, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823543, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823613, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823678, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823743, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823809, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823875, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.823941, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824007, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824072, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824136, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.8242, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824265, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824329, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824395, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.82446, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824525, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.82459, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824667, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824735, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824801, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824867, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824932, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.824997, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825063, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825128, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825194, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825258, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825322, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825386, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825453, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825518, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825583, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.82565, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825716, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.82578, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825849, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825914, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.825979, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.826049, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.826115, "xdebug_link": null, "collector": "log"}, {"message": "[11:25:11] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.82618, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.371249, "end": **********.838651, "duration": 0.46740198135375977, "duration_str": "467ms", "measures": [{"label": "Booting", "start": **********.371249, "relative_start": 0, "end": **********.709709, "relative_end": **********.709709, "duration": 0.33845996856689453, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.709717, "relative_start": 0.338468074798584, "end": **********.838653, "relative_end": 2.1457672119140625e-06, "duration": 0.1289360523223877, "duration_str": "129ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52318344, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.thermal_print_clean", "param_count": null, "params": [], "start": **********.805359, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/thermal_print_clean.blade.phppos.thermal_print_clean", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fthermal_print_clean.blade.php&line=1", "ajax": false, "filename": "thermal_print_clean.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.thermal_print_clean"}]}, "route": {"uri": "GET pos/{id}/thermal/print", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@thermalPrint", "namespace": null, "prefix": "", "where": [], "as": "pos.thermal.print", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1667\" onclick=\"\">app/Http/Controllers/PosController.php:1667-1726</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.005749999999999999, "accumulated_duration_str": "5.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.759464, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 29.217}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.770483, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 29.217, "width_percent": 7.13}, {"sql": "select * from `pos` where `pos`.`id` = '1454' limit 1", "type": "query", "params": [], "bindings": ["1454"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.773006, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "kdmkjkqknb", "start_percent": 36.348, "width_percent": 5.739}, {"sql": "select * from `customers` where `customers`.`id` in (10)", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.777541, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "kdmkjkqknb", "start_percent": 42.087, "width_percent": 6.261}, {"sql": "select * from `warehouses` where `warehouses`.`id` in (8)", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7793741, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "kdmkjkqknb", "start_percent": 48.348, "width_percent": 7.652}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` in (1454)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.781412, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "kdmkjkqknb", "start_percent": 56, "width_percent": 27.478}, {"sql": "select * from `product_services` where `product_services`.`id` in (0)", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1669}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.784771, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1669", "source": "app/Http/Controllers/PosController.php:1669", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1669", "ajax": false, "filename": "PosController.php", "line": "1669"}, "connection": "kdmkjkqknb", "start_percent": 83.478, "width_percent": 4.522}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1695}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.795723, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 88, "width_percent": 7.13}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 121}, {"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Pos.php", "line": 144}, {"index": 21, "namespace": "view", "name": "pos.thermal_print_clean", "file": "C:\\laragon\\www\\erpq24\\resources\\views/pos/thermal_print_clean.blade.php", "line": 279}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.81484, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 95.13, "width_percent": 4.87}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\PosProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPosProduct.php&line=1", "ajax": false, "filename": "PosProduct.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1454/thermal/print\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos/1454/thermal/print", "status_code": "<pre class=sf-dump id=sf-dump-1623440766 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1623440766\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1078731474 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1078731474\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1972486119 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1972486119\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-317456155 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IlJzTzhoeTRMaWtjWFIyelVRMlJsWHc9PSIsInZhbHVlIjoibFZEWGpYdnN1Z21ER1k0UUJWYnpsNVVmdE5RZVJlKzdmMFdVaXNmT25idW1UWWMwZDhPemNWZEpTYm0wQllJUGIxMmV0RC9QU1cwb3MycVplY0VOTllqNnBwejk5OFFDOWJCbG54dTR4Z01vOE8vbjlUQTJlZGI2SmUvRFhubFU1U2k1MGJKdUN1bk0zbWlpamI0QXdvcVhOQ2RFd0pJWHZtZHpuZjlrWTlpV3Nzem1yMHQ5RkFLOXhZTmZVc3BEYlk4ZUkrOGRFMUZCK3E1SCtlRzRZVTZSbHVhckZvaWlEREd1Q0RCZkRtTUVuT1JaTlBWK1QxdjZBb0ZDZWt5WWJRZWJrcUVsMUJGYktvYkVmWEVKemZodllrY3A0bkRoNkkzZzhQSHMvWm5oS2hIRC9Od2JFVnBWYjFqUE1ha3RFdEN6aU5aZkxyOW9GdTVCYVVjUjdNQytyaDRGVzRyaElxTlFjajhDV1M1UmVESFJIUXppb2pkUVdpNzlqNXlEQXQzNXlubmRsdGVWaEhiSXdxUm0wemdZY1hmV0RPNWExR2FtelpVNjdLNnFsbXZEZzVmd2FUdzJLamdmT2dwYXNnM2d6SHUzRVNqUUh3eGtvOEdNS3Eva3RIcUdoU1JjOEk2NXJEWkFwZWdQQjQ1dmc1YzdyVSs1TkZOdUVxSmciLCJtYWMiOiJkNTY5ZTU4MzBhMjBmODEwZGNlYjZhNzhlMjQ4OWE2YjQ4NzY4MmJmZDZkNzgzNjMyODU1MDYwN2Y5MjI3NWU5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkltSWZiWlBOMXczSWgycTZZOFJ4aUE9PSIsInZhbHVlIjoiUllGVlg4RUdxdHJXYlVxUWJTVE1mNzd4NUhpRHJyU1ROU3hnWUxHQ3dwUlFoMGZHSHRhcGJBRDVpL01kQURPalBYMmh5c0ZTTFo5VStyN3ljaFhQRjNUUUlNbmttMmRraFlaZi9va3JhZHNzWUJTKzNqbXJhbWFDVDhJSENrYU9hd0dPUytNRkJWM2lTb3g2WHdSb0MzZkhkNjAwWS9JbVhWbC9iSExUbXV6T2NobTcyZEo5akxOSHBKVDBiZ3g3eWlvbzVUQ0pVTlNxN1ZkWWFGYW90dkxJbFp5ckJTT3lhMWhjVnNBWkZzbUhEVERDN040Wk4zM0ZNcitGV1RnVzZVUFBUNzVtSGpITnBQL1krVDg1RStVMVBURFBRMGI0MmVzYnozS2ttRUxJTktHM3dJdXAzMkFrWU5HVWJkVklNM2lqRmQ0dDM1djRrR1ZVQVcvcnYvR3dlY0xlb3FNeUszQitMNG1aaVJLU2ZYM2ZIRzhFNnhpb29zdEVDbGR3R0VGMG5zdnJSS0hFZFFrL2VvblVuYmxUMXd6Z2J4dTAyaHRHMWUrY2dvOWJXNEI5NDJvMFhLaG81ZThXbFlBbnlpVTNoQzZabGpCWkU4eTNhbnhRYmlMY3ozcG5WcDFGODZ0U3lGOGQ3MjU0bVZZeUIvS2szL3paSkJsSzVBMXQiLCJtYWMiOiJlMWY4Y2QyNWM5MzIzMWRmYzk4Yzk1YTIwNjJhMmYyY2M1MTZlNDNkNDIyZDk4MjQ1YjBmYWU4YzUyMGE4N2Y0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317456155\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-775302415 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-775302415\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:25:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNBcFBDOUdCNm1hbnZ3QUE1U3JNNGc9PSIsInZhbHVlIjoieVhFTWlGNVpFWEFZTDQ3UE8wU3lZamdMTFhLZHNkRm1PV2NuQWh6Nks2RWNrdm8yVFVEUWV1WC9HbkNsV3FoTTg2b1A2SGh0QXAyZ0JvZ3FmVE9oQ2JJNGtleUxERmI3cjZlMjBZa2xhbEJodE9sSWR4TUhLeG1kRGU5UENBWVl4b2psZFFpNTd0eUttcVViZHpub3lJZzZSeFBjOS84aFd3aTN1dWtYYkttQXExcDNwdmVHTExFVE83THR0K1FQa1dmVUFPaTk2S3hnV2IrRkh3THZUcGlPVHpNT2tuaSt6QjZWWkE0a3ZpVW1qSWpJK202Qk5IMTZoVkxjU2RlUUJYRC9kaEI2a2UwZGdnWGpWMXdIeVIzZ200MldUYVBEZEk2Rnk0TGxjVUdVaXRFbHdXU3ZmRk5rWGhlNzR5elVIMHN5STdudFd6R3ZqNURPMWNrd09KRG1TWlpNbUZSRGozM1Z2V2FvUW9rcW9udFErQVpnbEwxSEZWaHJMeElHc1JDNm44MndsZW40YXk5NUk3NEQzdW5uOWpYNEt1aytER3lBL0tFbjZ4WUJ2aWVYdkRsMEhucHp0akJLTSs4VjRNajRVVURwNVRza2pNSm5UMjBKU1VmUmpkSC9CRmF2RDNFL3YwTUJwSnFzNmthZlR5SE9WK2ZGVkp1MnBJUnkiLCJtYWMiOiJmMjZjOTkwY2I5MGVmNjE0YzNhM2I0NjU3YTRkZGU1MTA0OTIyZWZmNzcxNWExNTdiOWQyNDk1MDdmYzcwOTMwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:25:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InIxU3NjZERvMmVmcHI5dzJxclpBUnc9PSIsInZhbHVlIjoibjYyR2liZmE4NkV0dGJEV0FERVMyV3lqMGNiTDdUeEJZSDhKS2QxZE00L2xrZDhQb3FmQU9jdG5QbjViVkxlOEJjYnE5L2p1S0xDSW0yWVVSRko0V2lDZm94bXNyOUJOakgzT1YvSGF5RW52dUNHZ01Fb09tRzZudGhvRGxGNFFlTFJtV1B1L3luWVRheDdLbG95cVloZHhONU9JUXpOdW0yblArcVNKaXN0ZmhnR1Rkc0U1OU1wYjhkZ1FqVHdqQlFnblVUWWlqK0c3TWt2NEwvRnVoWTZzRC9wb210YzN3WmpxVFJDU2N3TEU5SkgyQUxkdS9Eam8yWlVpMGx6K0REZGxleWxxR3NWaStrTFhCYkJsYzRNRVQvK1VubXZ0dDYxQ2NUZGpjQVFING53bVRoTUljK054NVdvUkZrcTNud2V1dmhMYm9WcGNUNUk2R3J0SkNuMXYzUFhmV3lUSmlvZHJwVW5ORktlaGhNVDZSRFlEQVdPM0JtSzNKMTRIK3F6b3FZQ3haMXlqVGgzWm1rSkt3THdzU0ZYUHR1YTQxZmpQOGVnTW01UDQzS05GM0xLOGhnMlZ4NkVPbkVOTVZIbk93WmMrUmxGM3lkTStDK002YTlEZG9lM00wMER2VXVrWFQ2d0I2YU5QMUVDRkcxQnE5TEt0bkdXQWkvTDkiLCJtYWMiOiJlNDE0ZDE0MDQ1NmZjNmEwZDg5ODY2NzI2NDhkMTYzNzY5MjU5Y2FlODNhMzc1NGNmOGRmNTgzOWE2ZmYwZmUyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:25:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNBcFBDOUdCNm1hbnZ3QUE1U3JNNGc9PSIsInZhbHVlIjoieVhFTWlGNVpFWEFZTDQ3UE8wU3lZamdMTFhLZHNkRm1PV2NuQWh6Nks2RWNrdm8yVFVEUWV1WC9HbkNsV3FoTTg2b1A2SGh0QXAyZ0JvZ3FmVE9oQ2JJNGtleUxERmI3cjZlMjBZa2xhbEJodE9sSWR4TUhLeG1kRGU5UENBWVl4b2psZFFpNTd0eUttcVViZHpub3lJZzZSeFBjOS84aFd3aTN1dWtYYkttQXExcDNwdmVHTExFVE83THR0K1FQa1dmVUFPaTk2S3hnV2IrRkh3THZUcGlPVHpNT2tuaSt6QjZWWkE0a3ZpVW1qSWpJK202Qk5IMTZoVkxjU2RlUUJYRC9kaEI2a2UwZGdnWGpWMXdIeVIzZ200MldUYVBEZEk2Rnk0TGxjVUdVaXRFbHdXU3ZmRk5rWGhlNzR5elVIMHN5STdudFd6R3ZqNURPMWNrd09KRG1TWlpNbUZSRGozM1Z2V2FvUW9rcW9udFErQVpnbEwxSEZWaHJMeElHc1JDNm44MndsZW40YXk5NUk3NEQzdW5uOWpYNEt1aytER3lBL0tFbjZ4WUJ2aWVYdkRsMEhucHp0akJLTSs4VjRNajRVVURwNVRza2pNSm5UMjBKU1VmUmpkSC9CRmF2RDNFL3YwTUJwSnFzNmthZlR5SE9WK2ZGVkp1MnBJUnkiLCJtYWMiOiJmMjZjOTkwY2I5MGVmNjE0YzNhM2I0NjU3YTRkZGU1MTA0OTIyZWZmNzcxNWExNTdiOWQyNDk1MDdmYzcwOTMwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:25:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InIxU3NjZERvMmVmcHI5dzJxclpBUnc9PSIsInZhbHVlIjoibjYyR2liZmE4NkV0dGJEV0FERVMyV3lqMGNiTDdUeEJZSDhKS2QxZE00L2xrZDhQb3FmQU9jdG5QbjViVkxlOEJjYnE5L2p1S0xDSW0yWVVSRko0V2lDZm94bXNyOUJOakgzT1YvSGF5RW52dUNHZ01Fb09tRzZudGhvRGxGNFFlTFJtV1B1L3luWVRheDdLbG95cVloZHhONU9JUXpOdW0yblArcVNKaXN0ZmhnR1Rkc0U1OU1wYjhkZ1FqVHdqQlFnblVUWWlqK0c3TWt2NEwvRnVoWTZzRC9wb210YzN3WmpxVFJDU2N3TEU5SkgyQUxkdS9Eam8yWlVpMGx6K0REZGxleWxxR3NWaStrTFhCYkJsYzRNRVQvK1VubXZ0dDYxQ2NUZGpjQVFING53bVRoTUljK054NVdvUkZrcTNud2V1dmhMYm9WcGNUNUk2R3J0SkNuMXYzUFhmV3lUSmlvZHJwVW5ORktlaGhNVDZSRFlEQVdPM0JtSzNKMTRIK3F6b3FZQ3haMXlqVGgzWm1rSkt3THdzU0ZYUHR1YTQxZmpQOGVnTW01UDQzS05GM0xLOGhnMlZ4NkVPbkVOTVZIbk93WmMrUmxGM3lkTStDK002YTlEZG9lM00wMER2VXVrWFQ2d0I2YU5QMUVDRkcxQnE5TEt0bkdXQWkvTDkiLCJtYWMiOiJlNDE0ZDE0MDQ1NmZjNmEwZDg5ODY2NzI2NDhkMTYzNzY5MjU5Y2FlODNhMzc1NGNmOGRmNTgzOWE2ZmYwZmUyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:25:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1454/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}