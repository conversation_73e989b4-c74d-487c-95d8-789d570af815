{"__meta": {"id": "Xeaa579f0b5d9bd35d9eee05fd3550ff3", "datetime": "2025-06-28 15:06:52", "utime": **********.511961, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.085377, "end": **********.511973, "duration": 0.42659592628479004, "duration_str": "427ms", "measures": [{"label": "Booting", "start": **********.085377, "relative_start": 0, "end": **********.44408, "relative_end": **********.44408, "duration": 0.3587031364440918, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.444089, "relative_start": 0.35871195793151855, "end": **********.511975, "relative_end": 2.1457672119140625e-06, "duration": 0.0678861141204834, "duration_str": "67.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46176328, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00716, "accumulated_duration_str": "7.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.481041, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 30.587}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.492292, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 30.587, "width_percent": 7.263}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4954371, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 37.849, "width_percent": 9.218}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.49733, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 47.067, "width_percent": 37.43}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.501733, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 84.497, "width_percent": 7.263}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.50345, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 91.76, "width_percent": 8.24}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-999744619 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-999744619\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1863794598 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863794598\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1244091448 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1244091448\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123070655%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtxU2d3QlVpTFhMQnhRUHdxeWMxQnc9PSIsInZhbHVlIjoiSEx0YjMwSTFPWnNSWGdRZXF6dVU2T2N6SllFZVR6OEIwWWtCVmh2elV1K1NvWURhbVFDWkY0clJUSmlkNHo1Q3dlbUJXbjFEOWJHUDZSM1pBT1lyNFVqTmIvTFF2aDBwNG5yeEFnZWtQRDVqRDRjODZycGxHL2dxcmhWMnkwb0lDOTFUc2owbWNwcll3b0NVUW1rV3VvdGYwVWFqUDdZVU9OZUNMV0N1L3I3bVZjckZINU9xRStPc25LdnhiL0xrYTh3aTlDUXI5a05hM3ZxSGVEMDhKVXE0NzFwNkpoVUNlR2g1K0tvUUgxcFcwY29IcEF4YUMxbnc4TlpPNnpaWUtPdWwzSjJkSERXc0h6L3hsRXZIU05XSmFuenpTVjRraXl1MzBici9JTWVHczNWYnVqYzMyR214MDlwS2I3cFhzMWN6cUd2UjhJRnR1Nll0Rm4zcUFOWVRiNGlraWNQdHpUTFRqMFBCZktpYjVzb3JDQTlDRXlmSzI3L0lENkEzdjVPUDRVTnZrOXJmUFk5L2tYSkdWa1lQM2xyb0VoRXo3VEtPSDE5eVdlOFNla3lqSnQ2VlQycFFNalNZWDlPcytpYUg1OTFaMGZSTTR3dmN1Q3l5dktFZUEyZUhiS0wxRGtYZDhPb3hJR05qYVFHWjRiVC9sbGZYZkFmdG5jTmYiLCJtYWMiOiIzZmY0ZWU1NDBkNTdjNDQyYzMyNzQ4NjYwMGJjMTM4ZjI0M2E1MGZmMmVkNGFmZmU1ZTc3YjU5MzFhZTM1YWM0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InMzWm5PakI4b1paUm8rNHp1TnNKdWc9PSIsInZhbHVlIjoieTNNODBrd0FIdzdLb2Z2OVVPQkNnbzBTcFpkak5tamdnM1pYMGd6OFh3aVE0MW0va1FvWU5pOU1icSt0QmVMeXhRTGV1QzlWaVZia0Q2S21MYjJVUjEvZi82cGFTS1JDMVVINWorUk5aeUcvOUI2cHhOK0R3TnY0OHpZM3IrTUFvNk0xMFAydC9FRlltWG9odHNsMjNyK0lnVFo0RzZEeVk5OVJnSWV6TmdlZURKeVF4U2NvdkNhWXJOSGdObFFBUlB2RVFFaEZSbVNTVmVuL0FDTjN3MnQvUllUaXVlRmNYejZrTkx0U0pSUHQyOXFRM1drOEN5ZWxnY1NGTVZlZDU1cDVEeHprZWlNREVzOUtBZTlYc1RVNE9xclRMZ3dsTnVhRnRXcVNpczZHSGFhUnhxV04yUThJV2RiY2xSaWJueE5IQVluQmVUclJVSU9GbEN2dit2dWNGNG1OV2N4aEJnMTNXVE4xaW1hZXJTSWU0WkVEQkpuTjBQMDJmTC8vcFh6ZG83TmFvUytmNGx1aENBekRmcEVBbHd0UnpibmZEOVZYS3AwNjVYdDNpekVnOWt4eUd6SWxUUUFobHViZEhhSXpHSmFVREowY0JMUEUvTDZpc3NveC9SMXlkM2wzWnNDWkgyWVpjSnp4aWpTb2FPdnc4dU9GTkR1Nng4L2siLCJtYWMiOiI3MjA0MWE0ZjMzOGVjY2M0ODgxNmRhNTUxZDk4NDc4MGY5OWIzNzk3YTk2NmVjYzI1NzFmMGFjOTgwOWE3ODcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1004956009 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:06:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVBUlVkaEpreXRBZEtvS0Y1a25KWGc9PSIsInZhbHVlIjoicnZqOUJqYksxU3BMUGYvdVZHSnNCZ0VEVTBHemtMVjUwOFFrRFZLZHI4VERLWnFGQ2JrVHN5cHl0V2IyRUlJdUNzeWtLQ3RabmRQWUs4M012RzdXSkMzT1pYR2pzZkZLamY4ZWV1V3Myb0dmNGQ3MktVcVlFVlpMTk9NenBVM1hlUTlrOGxZNzVEY3lidVI5MmxTWVhheFpPN0x4dC84bE5INTVjbTB6SDNTZFM3cmJJSkRmRVRJYjBWR3RxQmJwMHFkNlkrYk9uZTJjakV0aHFOZWVkWFo5Qm5GVzhzaFh2eFpXSXFENXJMc3JPNlkwYjRrVnkxRzExeklXQXE0SThpcmh6R3hMY2tyUWpNN25vMmFGcVFRVy94SXM1dnNlMSt0VGZJZEpIUjNtZzAxRlB6Tkg0TTR1VDdVMzEyampiQ0s0aGhoQTRuSlg2aU94dmhNcisrdmRycUtpT2VlbEw3VUZSbTFtZFJNVXVLZ0VCS2VCNHA2cE9iRmdNSmNsMllxL3RiUXBkcEZWOTRZYzZ3ODk1eUFkMnZIR0loclNKRlROeENENHloRTIrZDEyd3ZjcElwK0hTYWpxdEovWHlqbW9lSm8wZG9IdStwRmVWNkZrd1FNemdROUp5YzJ1RWlka1RBMUdjeTBuUHo0bFFqOHd6TE9OSUlKaFgvTXkiLCJtYWMiOiIwMzZjZTZkMjA4Mjc3YzY5N2M3MTM1ZmRlNDZkOTQxNWJkMzg5N2RhMDM1YzVmOGJjOGY0YjI5OTZmNjlhNmU4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:06:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkI2Tk9KWFBVcndhWHJacU5YM2thdXc9PSIsInZhbHVlIjoiZFhZMTdWRTRpaDJ1TzIrVjZ6OURmOUptVHRBWFFQdytkSXllTjNCY1A0cWk5UE9rTSs4UHhYUGJDTXpyWDJXSTR2Zkl0Q0xnZXVtRm5nMzRML09sbE1uVkZ6NjRTNXpLYktHR0kzTG1mb0JBeklUVTBSZ3c0S29LbXpZQzFSM0M5bFJoU3htWW41R2pybDRuYVBlbkhNbUJreVVyUlB2TGJHcTgyMXQ2ZE1xNFV1eHByYkppU01XWTJVUG8ybExJVGYvUEpaOWhydG9Ec05ySUJHaVMyNlhXaittbC8wVjlNR1dEK0pGV2xYT2QrZzFINFkrOHZZV0paZW5PRk9UYWFIQ2twWmNDNzhFNzZqeFM1YTJtS2Eva21QZWNCalNnLzFuc0hiRFRVUGFYUnVmVHAwY3ZLd1FPd01jTXkzOFFsNUJBbi9SK3l6SExTVUZjN3lyZUNrOWJZejZnZUdyNVd0WFVWYUxKNS9sUkdDUUNwMTVyMkUrR0RJaXlqbGxheUZpN0ZucFJRL1ZGUUZnR1pNUXVMb2J0L1VJbFQwZ1pSdEFjaWw1Y2lsVDM1cFJKVURQd0cvWlVWaXRIZCt5WFZBM29lYm1rbzNxc3FSVkFPUFB4bjR5SGJLNjNVblpRV1hEd1hyTUdoTXhPcE1sUkw0djN6NHk3Ui9BQXpXV0QiLCJtYWMiOiI2MTkxMTA5NWY2Zjk3ODlmZDEzNDBhYmI0ODU3NzM3ZDI2MzFmMDc0NGZlZWZmOTA3NzY5MWUyZTQxMDcyNTAyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:06:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVBUlVkaEpreXRBZEtvS0Y1a25KWGc9PSIsInZhbHVlIjoicnZqOUJqYksxU3BMUGYvdVZHSnNCZ0VEVTBHemtMVjUwOFFrRFZLZHI4VERLWnFGQ2JrVHN5cHl0V2IyRUlJdUNzeWtLQ3RabmRQWUs4M012RzdXSkMzT1pYR2pzZkZLamY4ZWV1V3Myb0dmNGQ3MktVcVlFVlpMTk9NenBVM1hlUTlrOGxZNzVEY3lidVI5MmxTWVhheFpPN0x4dC84bE5INTVjbTB6SDNTZFM3cmJJSkRmRVRJYjBWR3RxQmJwMHFkNlkrYk9uZTJjakV0aHFOZWVkWFo5Qm5GVzhzaFh2eFpXSXFENXJMc3JPNlkwYjRrVnkxRzExeklXQXE0SThpcmh6R3hMY2tyUWpNN25vMmFGcVFRVy94SXM1dnNlMSt0VGZJZEpIUjNtZzAxRlB6Tkg0TTR1VDdVMzEyampiQ0s0aGhoQTRuSlg2aU94dmhNcisrdmRycUtpT2VlbEw3VUZSbTFtZFJNVXVLZ0VCS2VCNHA2cE9iRmdNSmNsMllxL3RiUXBkcEZWOTRZYzZ3ODk1eUFkMnZIR0loclNKRlROeENENHloRTIrZDEyd3ZjcElwK0hTYWpxdEovWHlqbW9lSm8wZG9IdStwRmVWNkZrd1FNemdROUp5YzJ1RWlka1RBMUdjeTBuUHo0bFFqOHd6TE9OSUlKaFgvTXkiLCJtYWMiOiIwMzZjZTZkMjA4Mjc3YzY5N2M3MTM1ZmRlNDZkOTQxNWJkMzg5N2RhMDM1YzVmOGJjOGY0YjI5OTZmNjlhNmU4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:06:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkI2Tk9KWFBVcndhWHJacU5YM2thdXc9PSIsInZhbHVlIjoiZFhZMTdWRTRpaDJ1TzIrVjZ6OURmOUptVHRBWFFQdytkSXllTjNCY1A0cWk5UE9rTSs4UHhYUGJDTXpyWDJXSTR2Zkl0Q0xnZXVtRm5nMzRML09sbE1uVkZ6NjRTNXpLYktHR0kzTG1mb0JBeklUVTBSZ3c0S29LbXpZQzFSM0M5bFJoU3htWW41R2pybDRuYVBlbkhNbUJreVVyUlB2TGJHcTgyMXQ2ZE1xNFV1eHByYkppU01XWTJVUG8ybExJVGYvUEpaOWhydG9Ec05ySUJHaVMyNlhXaittbC8wVjlNR1dEK0pGV2xYT2QrZzFINFkrOHZZV0paZW5PRk9UYWFIQ2twWmNDNzhFNzZqeFM1YTJtS2Eva21QZWNCalNnLzFuc0hiRFRVUGFYUnVmVHAwY3ZLd1FPd01jTXkzOFFsNUJBbi9SK3l6SExTVUZjN3lyZUNrOWJZejZnZUdyNVd0WFVWYUxKNS9sUkdDUUNwMTVyMkUrR0RJaXlqbGxheUZpN0ZucFJRL1ZGUUZnR1pNUXVMb2J0L1VJbFQwZ1pSdEFjaWw1Y2lsVDM1cFJKVURQd0cvWlVWaXRIZCt5WFZBM29lYm1rbzNxc3FSVkFPUFB4bjR5SGJLNjNVblpRV1hEd1hyTUdoTXhPcE1sUkw0djN6NHk3Ui9BQXpXV0QiLCJtYWMiOiI2MTkxMTA5NWY2Zjk3ODlmZDEzNDBhYmI0ODU3NzM3ZDI2MzFmMDc0NGZlZWZmOTA3NzY5MWUyZTQxMDcyNTAyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:06:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1004956009\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}