{"__meta": {"id": "X79f5a373413cf5631a19ad898b57ec63", "datetime": "2025-06-28 14:59:05", "utime": **********.452359, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122744.906896, "end": **********.452373, "duration": 0.5454769134521484, "duration_str": "545ms", "measures": [{"label": "Booting", "start": 1751122744.906896, "relative_start": 0, "end": **********.29263, "relative_end": **********.29263, "duration": 0.38573384284973145, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.292646, "relative_start": 0.38574981689453125, "end": **********.452375, "relative_end": 1.9073486328125e-06, "duration": 0.15972900390625, "duration_str": "160ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45087472, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0035900000000000003, "accumulated_duration_str": "3.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.414623, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 43.454}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4243782, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 43.454, "width_percent": 7.799}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.437581, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 51.253, "width_percent": 32.591}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4449868, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.844, "width_percent": 16.156}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-295732013 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-295732013\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-574819755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-574819755\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-523107146 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523107146\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1712494957 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122734939%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdKU2kvRjhpUXRZejByRUNSY0pLbkE9PSIsInZhbHVlIjoiNlBQZlR4UUFHWlZLRUkvZndPT2Q4STl6SWUwNEUwbElneDZjRG8rL2JMSlJHTDBtd0tFQVp5aGY1WW5GbHlpQkI0QTRLbEtFWWZmV3VFZERna0dhSVlGdzVIcE1obVZCbHhrbFdYcEp0QkJjSUdENjhlVkI5amtHZEtqQU05bnROdXcyU055OWRkakc5QjhaQTF4eldOSEkyamtzY0ExNGhQUWk5WkFXZDBrWm5FamcyNG5nU21jVEhmZ2FqQzQyQVVudlBLMDhYSVNtVkg4S3B2MExaMlVVd3lncmNLVC91UjRORzZiUVFnMHQzd2VDWnV4ajNxL0tuYmpyN1VuTUFQUmM3cE9ZK0Z6Z1pXVGkzUE9yKys5d2RQRHUvek1yQ2dreWpkcDJFR3YwQWZ2cVQ0bmpEaGVyYWIzbkJZSVFLQ3pzUGRIcGo2anpOdkJnanAvTDIrN1hkWDlSVW9nZFF5djZ6aGxXQmRhRWZnVVBYbkcvRGo4eHdVdmNBU0ZkeVpBYUJsS21pdnBIbGRQbVhuVHJPT0FZQmpRMmRWdm02WmZwNUlOWEkxUlZVY1htL25LbFhpLzViZTNPRTJ0T0VuOGI5OWxNeTdxaWV1MGVOKzZwVHBKRHcxNUozNzBybGNqZ0I3TXRqSksvK2JzcXdhSWM5YktqSGhtTnpHOUYiLCJtYWMiOiJhNzYxZWUxZWFjNDBiZWFiZmRiNjUyMjMxOWU0NTc1MzI0YmFmNGY1NzQyODQxOTQyMTNhZmMxNWI4OTY3NmNiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImMwMlJaUUw2cktkUzRkaitBMHhsTGc9PSIsInZhbHVlIjoiNmU4NU91Z2l3NEQyNDRXeTE4U3hoTFR1WktNYmE3OXEveEtCd0ZmQ01kcm9lRkR6RngxeXgxOVRIUWtuaWVQNUwrWmVrMExYZnpXYkRad083VTZjWEFHSGViUktjVEF6WGw4QjJydXhsNUJjV0Qwcm5DUE5vMWFxRkczaTVZSno0NGVBQzNxaWdqQXVwZDVIMmJCZG8rU29zRWEyQXRBN2oyekFtU25zN3M3OHBHZ29aWUlEWEM2d1hhUStaRWczSGlSRURMSmdqdnhvTzVBOHBxUjB1SmVqYk0yYjk3djg0b3RSM0l4UkNWWDBFMXYzOGI4UHVWMHlId01RWm5JVzRsRFRnNzJzUis2U0tyQWZ4c2NrVERCSFNMQjY2a0YyV25zYUliWmVSNDFoSnlRdEI3R3Fha1pBdURPWW5hY0tXblJPV01Ta3VxQkRLR2I4Y0o2aFBkVjB6Q0RVRTVNdVhjWFNHc3NyYXZZRU9xQjBMMVBXam5RUUlNOEh0cXNTL2MrSzV5UkdNbU1UaTJIUThXQ3hubHRweEx5NDllajlBelFtRVFrNEEzY2owVlptdVc5NkJacUNCa3pXMXU3VEt0WWxURVExWUptNkNpRWpnaVJIREJWd2prMitlQjR4WDROb2psNUlhajNPUWtXQ0pvV2FBTmRjZFdranRsVEgiLCJtYWMiOiJkYjczMjgyMGUyZTMyMTIwYmIwYTE2YThmY2E1YjE4NjJiZjdiYmZlY2U1YmE3YjViNDFkZTc3OWVhMGM5MTBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712494957\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1111483249 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6nyGbgT6n8t00JbJ5CSgj7vkpXnxnyISs5XS6j6J</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1111483249\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1202540237 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhQQzhZaW1ia1RoVHhFK3lvZ3NLVUE9PSIsInZhbHVlIjoiQmlqSWJTaWduOW1TY3RzdFd5dmhZay9VaWxaR0FwblZFNnE0YWhObHFJMnFFSnZIZUFYdjMxWnd0NU9ZbUJkVXBVZE9BNHUza01ycUM5aGtJVVRtSlB6R1NoUjZoWHVCcnltZVdDaFAvUVhSbE4rL3V2djZ2M2hxdS85WTRuMEpMb3JOK1VMMlVJaWhNZkx2YnRnK0xESHBjb1ZLTjcwQVA1Uys2TE9Qbi96V2J1bllrOEp4UlNOQzRlT2lJdEM1UXJzK1FhY1hKdlpvbTd2N0hsaFYzZ3hiNjZzaXRKSFRReGkwL1lJL1N5OUxJRHdabkp0eTFGVU9ma3V3NmIxYW0vOGxjcGYzdTQvbWltMm5MRzJLZEwvNng0K0x2b3VySFZDQlVvbC9JNTB1MWE5em9TOTZTTnBpV0UwTTlOSWFJOW0yVlRYbWtMenhXdzhHMnVlTXRmdzdiY0V4eHJJamQyQysxbEpkNzBPUFhoak1XY09WT241QVdkeDVNeDZybmc4VFNDR3ByUTFXK2psTmFmd0pxdm1HQXpZT2xodXhNMjFpRmRjVDI1NzhPNVRFTUQyNEx0N0RBKyswQnVZVWMybE9iZzVjODk5SE5RdmZkK0JNazRtOURsTktrTUtIWWNoQU1ubnlEMGUrOVppQ0NyeW5iaEp0LzVMNmMzb3EiLCJtYWMiOiI0ZmNhYWVjMWViNTIxNWQwNjY3NjRjZTI3MjQ3N2IxMTQ2N2IzMzlkODhkZjgxOWFlODcyMjJhMjAxNGQ3NDliIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktVU095cjZGN0JEY1hQS2grWW81ZUE9PSIsInZhbHVlIjoibFNiL2I3MGtlT0tvdTBiVW9PWTd6RFpabDlJR3NLSExleEtJV1BWS3pYdTloVjVyUDFkWnhWK3UxL2laaUkxTHhxRWUzYStrS0xqeEJFek4xYnBuREZJNW5xUGRiRTBRcWVjSUhFbUxibHpJa0dLL0M2UFBsWU44Zm5PdUtoc21ackVMVmNkK1lsTnhrZXlZRG5ZdWNteW55RGlTRkNNbEhjbmphaFNUUU80SzVJMXUyT29pSHFNMklPRjB6dnJGTis0ZWpnODFLYUpTVmhybUkyTWZ0TlA0cE5oTjFiYzQxZ1c2eE1UTjNrN2sxL2hDU05LZnFXbGxzdG5KYUEraisxd0lmTi83eWRFVFBkdWpqbHk5aUFlWnhMUTIxbmN3bys4dlMxOUlnM0dncngvK2hSRnozS3JBWlk4MkZ5K1NWbmEzQVZzNXgzdHYxY1NsbTRZTFg0YllCYUxBbDBMVHorOUt4OU1WellvclM2eDdJM05hVHMrRHFTcXo2YVlEbEg0MDlFZUdYSzIvN1cyZktDYXJCVHlwdzVWak8zSHlOWGZTWFFkY1p1R0pZajIyZ1pYT3BNSTdlNHgzTWl5emVlNlBCVXZxS2szTExSa2FmaGw5UHJpNzl5SExqT3oyWUQ4UlNYMG4xYlF5TlF0dHliNmZULzlHSzRORE5NL0EiLCJtYWMiOiJjZjJiYzFhMzBhOWNhZGRmMTM3ZmM0NGMyNjNmYzcxMTBjYTcyMjUzZWI2YWE3NTMyMWE1ZTBlZGIxYWY2NDdmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhQQzhZaW1ia1RoVHhFK3lvZ3NLVUE9PSIsInZhbHVlIjoiQmlqSWJTaWduOW1TY3RzdFd5dmhZay9VaWxaR0FwblZFNnE0YWhObHFJMnFFSnZIZUFYdjMxWnd0NU9ZbUJkVXBVZE9BNHUza01ycUM5aGtJVVRtSlB6R1NoUjZoWHVCcnltZVdDaFAvUVhSbE4rL3V2djZ2M2hxdS85WTRuMEpMb3JOK1VMMlVJaWhNZkx2YnRnK0xESHBjb1ZLTjcwQVA1Uys2TE9Qbi96V2J1bllrOEp4UlNOQzRlT2lJdEM1UXJzK1FhY1hKdlpvbTd2N0hsaFYzZ3hiNjZzaXRKSFRReGkwL1lJL1N5OUxJRHdabkp0eTFGVU9ma3V3NmIxYW0vOGxjcGYzdTQvbWltMm5MRzJLZEwvNng0K0x2b3VySFZDQlVvbC9JNTB1MWE5em9TOTZTTnBpV0UwTTlOSWFJOW0yVlRYbWtMenhXdzhHMnVlTXRmdzdiY0V4eHJJamQyQysxbEpkNzBPUFhoak1XY09WT241QVdkeDVNeDZybmc4VFNDR3ByUTFXK2psTmFmd0pxdm1HQXpZT2xodXhNMjFpRmRjVDI1NzhPNVRFTUQyNEx0N0RBKyswQnVZVWMybE9iZzVjODk5SE5RdmZkK0JNazRtOURsTktrTUtIWWNoQU1ubnlEMGUrOVppQ0NyeW5iaEp0LzVMNmMzb3EiLCJtYWMiOiI0ZmNhYWVjMWViNTIxNWQwNjY3NjRjZTI3MjQ3N2IxMTQ2N2IzMzlkODhkZjgxOWFlODcyMjJhMjAxNGQ3NDliIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktVU095cjZGN0JEY1hQS2grWW81ZUE9PSIsInZhbHVlIjoibFNiL2I3MGtlT0tvdTBiVW9PWTd6RFpabDlJR3NLSExleEtJV1BWS3pYdTloVjVyUDFkWnhWK3UxL2laaUkxTHhxRWUzYStrS0xqeEJFek4xYnBuREZJNW5xUGRiRTBRcWVjSUhFbUxibHpJa0dLL0M2UFBsWU44Zm5PdUtoc21ackVMVmNkK1lsTnhrZXlZRG5ZdWNteW55RGlTRkNNbEhjbmphaFNUUU80SzVJMXUyT29pSHFNMklPRjB6dnJGTis0ZWpnODFLYUpTVmhybUkyTWZ0TlA0cE5oTjFiYzQxZ1c2eE1UTjNrN2sxL2hDU05LZnFXbGxzdG5KYUEraisxd0lmTi83eWRFVFBkdWpqbHk5aUFlWnhMUTIxbmN3bys4dlMxOUlnM0dncngvK2hSRnozS3JBWlk4MkZ5K1NWbmEzQVZzNXgzdHYxY1NsbTRZTFg0YllCYUxBbDBMVHorOUt4OU1WellvclM2eDdJM05hVHMrRHFTcXo2YVlEbEg0MDlFZUdYSzIvN1cyZktDYXJCVHlwdzVWak8zSHlOWGZTWFFkY1p1R0pZajIyZ1pYT3BNSTdlNHgzTWl5emVlNlBCVXZxS2szTExSa2FmaGw5UHJpNzl5SExqT3oyWUQ4UlNYMG4xYlF5TlF0dHliNmZULzlHSzRORE5NL0EiLCJtYWMiOiJjZjJiYzFhMzBhOWNhZGRmMTM3ZmM0NGMyNjNmYzcxMTBjYTcyMjUzZWI2YWE3NTMyMWE1ZTBlZGIxYWY2NDdmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1202540237\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-994025971 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994025971\", {\"maxDepth\":0})</script>\n"}}