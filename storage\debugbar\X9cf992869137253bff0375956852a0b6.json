{"__meta": {"id": "X9cf992869137253bff0375956852a0b6", "datetime": "2025-06-28 15:08:56", "utime": **********.528362, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.08895, "end": **********.528375, "duration": 0.439424991607666, "duration_str": "439ms", "measures": [{"label": "Booting", "start": **********.08895, "relative_start": 0, "end": **********.462096, "relative_end": **********.462096, "duration": 0.37314605712890625, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.462105, "relative_start": 0.3731551170349121, "end": **********.528377, "relative_end": 2.1457672119140625e-06, "duration": 0.06627202033996582, "duration_str": "66.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46161616, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00638, "accumulated_duration_str": "6.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.499467, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 27.743}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.510508, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 27.743, "width_percent": 6.897}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.513279, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 34.639, "width_percent": 9.248}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.515082, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 43.887, "width_percent": 39.812}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5196152, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 83.699, "width_percent": 7.21}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.521366, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 90.909, "width_percent": 9.091}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-1641213268 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-1641213268\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1547445074 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1547445074\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1934399509 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1934399509\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1374243033 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123070655%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBRemxwNFlPaUx2cUxOcEs1VzJpUFE9PSIsInZhbHVlIjoiSjdwU1F4ZmtTL1hodUEyalNvRUE2QjVHUGVZWnFnY3hKMVhDNjZ4Y2txaDBSV1FlM3dHL0RvU0ZEd056b1FDRTZyWTNoNnQzb0MwbThBQUk3ZGZqKy9sSytJdHpmNS9TYzJsRlJuZlpuc3ozYmhWOHZIRFdOUmFtVnJFWWRDOHFzc3kydkJCQ2VtS3grRjd6VmxXcUV1SFpaRmNCRVFPRzhiclE5STZnWC8yTFVIT3Z4YWtlU2JpRG5XaU5UbFY1OGhMSWliUlNQWU1OcTlRS2IveUo0THZUVWFQa1VqWWxOS0tHTndNak5XM0dzSXUxd0wzS1N4by85bWx2SnRuSm5TRFRXN0hrcEUvZVRpTnRBcnM5Y1A4bzRzMTBIOGlJVGN4akpIbWRHODBndkY2SzFvQUk2OXV5a0JKbUswbGk4NmMrME4vZDNDdm9wM2x1d2VZNzBDNjUvZ2pLRzVyT2dJbVJDdDF5eDdLQUw5djVlV1pTZlRSS2V4MENLL2JhYUpVUWNTUmlvWm02dTIyWGNMTFAzc2dxS3hUeEM0MUplSC9xVzNxUDFMallWdlRTdkRmSmliQzUzSDc1WHMvQjdHcjFVOHRmRnlnY2kyS0hQZ2JXS1o4LzgrbnIyUStCeEZYOStsQ0ZQdVBEL3lhb0xEWTJpR2FKNHlsZEkxdHIiLCJtYWMiOiJkOGIxOWYyZmZkMWViMTM0NzczMmI1ZmU5Nzc5NGRjNGUxNTgzNDJiMmEwMGMwNjQ1NTA1NmNkMjNmZGVjOWU1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlR4N0hEWmF0ZndYR1dpQ1JkcmpLTmc9PSIsInZhbHVlIjoiQTlWRmlvMG41bkVxbHJ0QmNVV0VTVUZLelkzQ0g0YmdXMG1PZHNtNDJ6LzRlWUI0bjBIMFh6THhZdTlBOGwrai9hVjNiOXJlWTBTNEZVK3cvM1VRN2NZYkhpUnhXRXdZNjhLd2NTa3JwaGVmaEFmWjI1bzhqNHhZYUxkNWxVVjdMb1ZWaUdkdTQyZEM0b2hncVFVNXBxQ0FUZlRuL2RSREdqT1VyS2Z5TVRZUWg0VFFKT3RVU1J4eUhiVll0enJDVUhPa0ZtQkU0clZtN0pOMGl3NUJUeDBQMzF3UzJYZWdYYzRwaDd4eWxYcGk5alVVYVBWSUxhdEswVkJkc3N5ZHZSbDVBODk1VWEzRFJ3R2hmaUpSMy8xSStwQzRYMGx2ekp6bGwxYitxSkpQbzhUUm1JRjFwVlM0aUlTYjlMTmNrYS9ISU4vb3A2TGp0Q0VGL3hNczdMclNaZC9PaXBrQU5wdDBJRURRYXFGeVljOFRxcDE2bUZ5SThqY1o2NVhGNERwL0R3cW1sME40STh6WE4xVWtIUnN5b3MyOGQyZnNSUXZVSDBEVVdrN3plcHlNNlIwQ2pjZEhaWFFDVjZGbkplOWtHOE9XemtjZDVqTHBueGJld0hCWVdZajkyRFdzaVd0aG9EN1hlR1ZFaVBGSUJnY203enMrK05EVTZaaUYiLCJtYWMiOiJkMmFmN2I1Y2I0Y2NmNGFmNWQwYzBlODVkYjEwMzZhOWRjMTcxZWVjNDI1ZmVjNWM5MWY3M2Q4NjBmZGNiZjdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374243033\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1498742476 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498742476\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1192931350 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imd4MytiRDBvZDhwVmVzOEp0b3RoUEE9PSIsInZhbHVlIjoid08zLzVhV2Q3cW1OeU05YzI5ZGx3eWlJeEJhNHEwdksvTERkeUIvck4vYy9FYTZUKytORExFSk43MXN1UTV5Zm0vdE9lNXN4VWk0ZStXWG4zZktGMkY0dHlxQ3NzWTNlQmljUEtMamRPdmRBdEIxVnR2eTdkdHdhL2xFTGNranM1QXA5RzNvMTNIcFBFb3RjOXpNV1lyWVB1RFFWdjJhRmVtWENyRHBKVXZQZUFFVWlhNFRkZjFWSkgrRDNMTGNmR0ppL2Y3RUFxWEVTR2F3RVF5WjB2enNMSkRnYzVmOGVWTjdQcWl0c0R5TEw5RXptM1JQM0I5WnVGMUJCaHdZRGdsSkhsVEIvNWFXUG5iUmJsczJYUjRSVjVFT29rUDlwRWtVMGRrZ0JSeWJzR1lqY0x3SDg1VFJlc0plOWt1VHJTZ2xnZjNrZ3ozVUVPWW01MWV6Mm5EclVMRGZCN1NIOEpQYlRDZmgzQm5OR2VOYlM3K0lNYVp6dVZweHQzNktXNVI0ZFlacXBMTldDZFJCRk5PdmFjZTNqSzNXM1pja05EM1ZOQm9sam9uYkZ1NUdQbmgrZVVja3BZbG5OS3ZiQ2VpWlQvM3Z4djlNZlpybVZpSUx2MjczSDl3bTRTektRTzRnR1FyaEVTdEFtTEFrc2JWUVkwYlZmL2tLTTFPajEiLCJtYWMiOiIyNDk5ZWM2ODE2NjI4MWIxMTU0MmNkNDFhMGI4NTA4Mzc3OTFmNTcxMjY0Nzg4ZDhjYjJjZGRlYWIzNzBkMDBjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdGai9TRW5pWS9tck5WM1hEUWRXZmc9PSIsInZhbHVlIjoiSmdYL3F0VWgvckplQVpib1ZYSGtpdXU2UkViMG5TZTUvdlRLd1Q5WmFwelZreksrL0Q5aFVWdEZTdXVYUlZWVmo3WnVveWp6c09UVUNacnVFcGNBeEg4YVY1OHhpaTNpa3Fxc2xTajhLL1pzckhLSzhjMksrU1h5RTB2THgxV21tSkpMYWZtNlI4Z3QycUtJYXJJZjFXMExOdktLY0ZxVEpiNFQzcnVEakRQOUJPWVo5Nnc1Q3NNQjJGa3BVWDkvNVZ4azR3TFZZV3VNanM4MEdieHVKNUdScHU5ZTVVSWJ2eGRBcG4zUnBtWFRmMnV1aDJXRTMwQ1N4d1hXencxV3FPZCtYMGZNWldmZExYeVZhOUhrMnVZdHU5S2EzNjREL2ZRTXFUNk5mdkpNYkJhMy9laVFoei9NSHgwTm10dFNwTGZtNFdYelJPRlcxdUJzMlg5SXhqMGgzSlFoa3I1K0xqQ3JCRG5HNnlOWElvaWRBRnl2Y3FEclpyaWJ3TVlDOVBzM3lDUm5nYlBsQnZEWXFFTHlWdTVhemZQZkNKZnJtUm1GZEhvY1FKOC9CZnJVRk51SXN1b0xUNGd2ZWQ5bXR6K2pMci9jVmxuUzFVUEZsUjJJTjduRXQ4ZEd0QW15UGlkQUZyZGVvWDBpODZpZ3BPWVZPVG5QNmY0UHR5MUIiLCJtYWMiOiI2YmNmZWJkMjdhZmNlOTBiYTIxMzNmYmJiZGI0ZTE0NzZjMmE4MTM1MDQ4NjhhMWYwOWZiZjE4ZGZhZTg1YjFiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imd4MytiRDBvZDhwVmVzOEp0b3RoUEE9PSIsInZhbHVlIjoid08zLzVhV2Q3cW1OeU05YzI5ZGx3eWlJeEJhNHEwdksvTERkeUIvck4vYy9FYTZUKytORExFSk43MXN1UTV5Zm0vdE9lNXN4VWk0ZStXWG4zZktGMkY0dHlxQ3NzWTNlQmljUEtMamRPdmRBdEIxVnR2eTdkdHdhL2xFTGNranM1QXA5RzNvMTNIcFBFb3RjOXpNV1lyWVB1RFFWdjJhRmVtWENyRHBKVXZQZUFFVWlhNFRkZjFWSkgrRDNMTGNmR0ppL2Y3RUFxWEVTR2F3RVF5WjB2enNMSkRnYzVmOGVWTjdQcWl0c0R5TEw5RXptM1JQM0I5WnVGMUJCaHdZRGdsSkhsVEIvNWFXUG5iUmJsczJYUjRSVjVFT29rUDlwRWtVMGRrZ0JSeWJzR1lqY0x3SDg1VFJlc0plOWt1VHJTZ2xnZjNrZ3ozVUVPWW01MWV6Mm5EclVMRGZCN1NIOEpQYlRDZmgzQm5OR2VOYlM3K0lNYVp6dVZweHQzNktXNVI0ZFlacXBMTldDZFJCRk5PdmFjZTNqSzNXM1pja05EM1ZOQm9sam9uYkZ1NUdQbmgrZVVja3BZbG5OS3ZiQ2VpWlQvM3Z4djlNZlpybVZpSUx2MjczSDl3bTRTektRTzRnR1FyaEVTdEFtTEFrc2JWUVkwYlZmL2tLTTFPajEiLCJtYWMiOiIyNDk5ZWM2ODE2NjI4MWIxMTU0MmNkNDFhMGI4NTA4Mzc3OTFmNTcxMjY0Nzg4ZDhjYjJjZGRlYWIzNzBkMDBjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdGai9TRW5pWS9tck5WM1hEUWRXZmc9PSIsInZhbHVlIjoiSmdYL3F0VWgvckplQVpib1ZYSGtpdXU2UkViMG5TZTUvdlRLd1Q5WmFwelZreksrL0Q5aFVWdEZTdXVYUlZWVmo3WnVveWp6c09UVUNacnVFcGNBeEg4YVY1OHhpaTNpa3Fxc2xTajhLL1pzckhLSzhjMksrU1h5RTB2THgxV21tSkpMYWZtNlI4Z3QycUtJYXJJZjFXMExOdktLY0ZxVEpiNFQzcnVEakRQOUJPWVo5Nnc1Q3NNQjJGa3BVWDkvNVZ4azR3TFZZV3VNanM4MEdieHVKNUdScHU5ZTVVSWJ2eGRBcG4zUnBtWFRmMnV1aDJXRTMwQ1N4d1hXencxV3FPZCtYMGZNWldmZExYeVZhOUhrMnVZdHU5S2EzNjREL2ZRTXFUNk5mdkpNYkJhMy9laVFoei9NSHgwTm10dFNwTGZtNFdYelJPRlcxdUJzMlg5SXhqMGgzSlFoa3I1K0xqQ3JCRG5HNnlOWElvaWRBRnl2Y3FEclpyaWJ3TVlDOVBzM3lDUm5nYlBsQnZEWXFFTHlWdTVhemZQZkNKZnJtUm1GZEhvY1FKOC9CZnJVRk51SXN1b0xUNGd2ZWQ5bXR6K2pMci9jVmxuUzFVUEZsUjJJTjduRXQ4ZEd0QW15UGlkQUZyZGVvWDBpODZpZ3BPWVZPVG5QNmY0UHR5MUIiLCJtYWMiOiI2YmNmZWJkMjdhZmNlOTBiYTIxMzNmYmJiZGI0ZTE0NzZjMmE4MTM1MDQ4NjhhMWYwOWZiZjE4ZGZhZTg1YjFiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192931350\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1006408118 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1006408118\", {\"maxDepth\":0})</script>\n"}}