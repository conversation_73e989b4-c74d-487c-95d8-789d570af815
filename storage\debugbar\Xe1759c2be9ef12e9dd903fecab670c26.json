{"__meta": {"id": "Xe1759c2be9ef12e9dd903fecab670c26", "datetime": "2025-06-28 16:09:59", "utime": **********.671059, "method": "GET", "uri": "/add-to-cart/2306/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.259333, "end": **********.671072, "duration": 0.4117391109466553, "duration_str": "412ms", "measures": [{"label": "Booting", "start": **********.259333, "relative_start": 0, "end": **********.583512, "relative_end": **********.583512, "duration": 0.32417917251586914, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.58352, "relative_start": 0.3241870403289795, "end": **********.671073, "relative_end": 9.5367431640625e-07, "duration": 0.08755302429199219, "duration_str": "87.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48748160, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.020380000000000002, "accumulated_duration_str": "20.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.615144, "duration": 0.016030000000000003, "duration_str": "16.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.656}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6387892, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.656, "width_percent": 2.257}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.6507502, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 80.913, "width_percent": 3.042}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.6525629, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.955, "width_percent": 2.012}, {"sql": "select * from `product_services` where `product_services`.`id` = '2306' limit 1", "type": "query", "params": [], "bindings": ["2306"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.656613, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 85.967, "width_percent": 1.57}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2306 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2306", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6598449, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 87.537, "width_percent": 11.286}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.663276, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 98.822, "width_percent": 1.178}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-696053767 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696053767\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.655851, "xdebug_link": null}]}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2306 => array:9 [\n    \"name\" => \"ذرة بريتز\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"2306\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 51\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2306/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1266217017 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126882941%7C30%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImIrSGR6dG1FeE03cjh1UDRTNUxscXc9PSIsInZhbHVlIjoiSzltUHViMVRvRXJSNWFBSDA5c3lrZ3hvYzZKNlhSL25nTEZoemJFRHpNV1VKYVlPdWVVcHFRQ2hOaHcvZ0Z3L2VHYUdaeDIxK0Z6M1ZvREpYMG0zd2FWSEpEWEhROGQyMllseDZnTHk3NUtBVmN0aXVFeURsVTh3TTRrYy9vSlhMU0VscG9jVkc0VnMrN0V4OUpHelNhclc0eEMzUDhzM1AwN3RydE9uWVZKUk5ta3NDYzlkYzVUc0tjaUFjaTB4SnVpRyt0NFYrMEpsYUdwV1VTeDJuK0VpeUNzb2I3ME40WjNiQUNxelI0eENUZGMvZjlBUTFweFk1MXduR3pBUmg4U0p1N2JIWmRZbU4vbGlEQkdNQzI5NVdFTnlGZUhTUDBRU01JRmljMEw0L3VGWHptNkhFRXBpYjlieE54Qi9YczhFSTN3YzNxb3AzVDJnejBJRkkzdFRQM1h0V05iZGFENmpyK2xxSDZUUUM1bGp4V3NmZVNXWHBjVytSRDF1ckEwQWFkR0hiN2Z1L1RTMDRvQ3o3TGRFWEZXaXdabTFOeVJZSEczbXBLWFQycjh1M2NRci9TeGxxTE1QMmQwWlF2NllmK1dFZGxYdytESUpHajFzL3kwc0xMOEYxQ1lObzF3REdlVFlkNUVEK3FYV1VXRiswUnVsektzZFFUWEsiLCJtYWMiOiIwNzBlYTAwOGVmNDcyM2RlMGM3NTk4ZDJiNGQ3YzkwYzY5NDlkNDhiNjQ1MjExYzQ4MTdhMmZjMjAzOWYyZGQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhjNE5qdDFUUzlOdStUM0lmV0pKenc9PSIsInZhbHVlIjoib1ZjN29kYXFkaGF1bWhINit1Y1NKWmZzZmpwL0I4YUZ2bXByRU1yREtRb3ZpQ094UGJBc2pITDJMUU5QS1g0QndKVWtTM1RqQ0NMdzd4Nnh0ODM5VE9xV3A5V1VIZ2ViWk8rM1dyRDlFaUdxNTE5aEJDejE4SmJxcStvcEZwYXhCRHE3aytDa2lGWHQ2UGdTNGp4QkNkRHBib2pWeFY3KzlzTzN0cXhsWHBlODR0N1ZVSTJoTXlXeUw2Mld1aDNCWGdwRVQ1QUlCRVRndEQzSlJLaCt0dlVpdC9TUFV0bnpwK0h3OFBuZVhNanNZZndnUFdacTlqVkdFN2YxNzE4YVkyeCtyQUxsZzg3V0xFY1JsZTkwd1IzMVB4Z2JjUGdSdzRQaXJ0aVNiZ2xUNElUenhtVUQyYUVqWUVRaWRUVTMwQ05nampjdHp0Wi8rZnd1Mm1IcGRZb2JJU1JiY1REL2lFc252cHIwd3hnYzJlU2tUK2I2bHVkejdiQ3ZXM25iL0dXYjFoSmVBL1V0dUVmeTdzNHJjY21oamtvSUNxZzVpaE85Snl4MUhpQmJTV25OTUErcitNakdVQ3RobkkwQ0FpK3RSM3hsU0Z1WGZYNnR5RzA4SXVjazg5OUI5NDZqYWs1TnJHY2NCNis4bDVSOXYzbitnd21md1VVWkhZbnQiLCJtYWMiOiJmOWMzM2Q3ZGQxZDNiNjgzOTAwYTg0YzBhNDk0NTY5ZDE5NmExODI4ODdhMGQxNzI0MjY1Mzc5NGJiNGI1MDJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1266217017\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1356775852 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356775852\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1353842998 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:09:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlE1MFhGbTF5d0VzeHFodGtwNjBraEE9PSIsInZhbHVlIjoidE0rOVFZMDVFZTFjMFNDenFTTlN6eGNaN2ZsaGNmL0RRSmxNZmorNHJ4WU42alRPVG90RGtZNFdTa2dGUTlheHJpbWM3bytWUkJzY2haR1JHd0wyNnFHd2N4RUNzOTBtMWVpczN4c2MvTERKQUNsNjk4VWxFSEtPSmpWd2k1QlBXRzY5d3lPYWR4TGZwK3dtd21Lbzl2eDZ4NEhJK0gwamtCUExuOEpScXoxMGRZbGI3L0o5UlFYcjNyY1RJVGdJdFlBTW5ybm95cmRzZWZmMzdVa2FTUTlpdW50Z1ZTWnBDQnV0OU8zMjd1ZXJqSXRzNGhVU2JxTnVTa09qckhPZ1hUUzVUQ0R0emlKMG0zekdjTXVRWVhvUnRwYnowNzlLenRlcThoWE13Q2E4RlJnamhQbEt6RkNEc0o0Nkhpd0dlTXN1NU44VXBZY3N6MjVMS0dGeDMvc1V6UGM4d0F3WklSeVpPNkpOQkNBaHR6eUtTd3lmMGNPQ3greU9QQmNrdDk2ZmJ4RFJuU2k0dGRWY3hBbzBpQngvUEFLVU1NSXBITCtCQ3UrVlNwYk5CNDhndUtmSWhudnVHRGdIQmZac0xDVHVRZWllUm9LTkJSNHVkRWo0WGxqK1l6QUp3QkU2NXd1alM4Uk55enQ0Z0NOcUlmbDlBditnUUJ3TytMd2UiLCJtYWMiOiJhMmI3YmZiNmFhOWNjYTdjNTU4YWJhZjllYThmMjZmNjdmMjlmZmQwM2I1ZmRiZmQ4ZTVkM2ExMTBkYTg0YjRiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:09:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVBV3VYWnczYVNDTnE0S3d1YUxhYXc9PSIsInZhbHVlIjoiVmVyYVhnYnMxa04rTEVOZ2tZanJJcVJSR1JnTmtSRFQ0WkFYNThEZHFFc203bm0yNk1BcUhrRDA5bURXUFk5OCtick5pTXIzVVZUcFVMa2RTT1JhbXRGbzlSOFFZczhhV2d1Y3h1MnZxZHBXTGgrR2FBcG5Ma01GbllpNFdsMjUxZXkvaFkxWjQ3MGdmQjBZT1FkR082TXlnK3RReGlDSzFFeHhtN1FOSHF6TGMxZTZBdVF0ek54SHFlOHBLNlFoeUdpL2xnME1OQkk3VSt4V1NncnV6dDdYaXpxNDRGZVBvU2h3cVFrdGhhNFJ3NlpRMTlOTUdJYzdnVG5ybDMvaXRIQitQcFF0MXZxNnN1NWNFdjd1VmE1V3dCbWxuM3hKU2VkRGV5VzFmc0dabWlSajBzZm83YnUvK3o3UVhXTDZtb1pYSit4YmtlVzZldk1NeVlxRTkrQlM4RXRqRVVtMFd2NVdVOWhvdFM3ZldlTmd5ZnMrMFhrR25Sd1BsQnB5UE9DRDFudnpDUjN2OXRjRzE3UUZ6a0JXS1pkOS9FazdqYzc5STI3R3EzWEVRL3c2VUQyN25FdWVUQnRoVVF0R2x4Rms4N2dVemdsUW5meUt6RzY3RWp4cytFeUpXWmN2NzJaQUhCRzQ3M0JwelZ4aklJM05GcHlXRjBUeFlqWDgiLCJtYWMiOiJjNzk2MmVhYjEzNGJjOGU3YWUxMGVkN2M1YWUyNmUwYzY3MmM5NWZhMTE0YjQ3N2VjYWQ0MWM2Nzk4MWVmOGUwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:09:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlE1MFhGbTF5d0VzeHFodGtwNjBraEE9PSIsInZhbHVlIjoidE0rOVFZMDVFZTFjMFNDenFTTlN6eGNaN2ZsaGNmL0RRSmxNZmorNHJ4WU42alRPVG90RGtZNFdTa2dGUTlheHJpbWM3bytWUkJzY2haR1JHd0wyNnFHd2N4RUNzOTBtMWVpczN4c2MvTERKQUNsNjk4VWxFSEtPSmpWd2k1QlBXRzY5d3lPYWR4TGZwK3dtd21Lbzl2eDZ4NEhJK0gwamtCUExuOEpScXoxMGRZbGI3L0o5UlFYcjNyY1RJVGdJdFlBTW5ybm95cmRzZWZmMzdVa2FTUTlpdW50Z1ZTWnBDQnV0OU8zMjd1ZXJqSXRzNGhVU2JxTnVTa09qckhPZ1hUUzVUQ0R0emlKMG0zekdjTXVRWVhvUnRwYnowNzlLenRlcThoWE13Q2E4RlJnamhQbEt6RkNEc0o0Nkhpd0dlTXN1NU44VXBZY3N6MjVMS0dGeDMvc1V6UGM4d0F3WklSeVpPNkpOQkNBaHR6eUtTd3lmMGNPQ3greU9QQmNrdDk2ZmJ4RFJuU2k0dGRWY3hBbzBpQngvUEFLVU1NSXBITCtCQ3UrVlNwYk5CNDhndUtmSWhudnVHRGdIQmZac0xDVHVRZWllUm9LTkJSNHVkRWo0WGxqK1l6QUp3QkU2NXd1alM4Uk55enQ0Z0NOcUlmbDlBditnUUJ3TytMd2UiLCJtYWMiOiJhMmI3YmZiNmFhOWNjYTdjNTU4YWJhZjllYThmMjZmNjdmMjlmZmQwM2I1ZmRiZmQ4ZTVkM2ExMTBkYTg0YjRiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:09:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVBV3VYWnczYVNDTnE0S3d1YUxhYXc9PSIsInZhbHVlIjoiVmVyYVhnYnMxa04rTEVOZ2tZanJJcVJSR1JnTmtSRFQ0WkFYNThEZHFFc203bm0yNk1BcUhrRDA5bURXUFk5OCtick5pTXIzVVZUcFVMa2RTT1JhbXRGbzlSOFFZczhhV2d1Y3h1MnZxZHBXTGgrR2FBcG5Ma01GbllpNFdsMjUxZXkvaFkxWjQ3MGdmQjBZT1FkR082TXlnK3RReGlDSzFFeHhtN1FOSHF6TGMxZTZBdVF0ek54SHFlOHBLNlFoeUdpL2xnME1OQkk3VSt4V1NncnV6dDdYaXpxNDRGZVBvU2h3cVFrdGhhNFJ3NlpRMTlOTUdJYzdnVG5ybDMvaXRIQitQcFF0MXZxNnN1NWNFdjd1VmE1V3dCbWxuM3hKU2VkRGV5VzFmc0dabWlSajBzZm83YnUvK3o3UVhXTDZtb1pYSit4YmtlVzZldk1NeVlxRTkrQlM4RXRqRVVtMFd2NVdVOWhvdFM3ZldlTmd5ZnMrMFhrR25Sd1BsQnB5UE9DRDFudnpDUjN2OXRjRzE3UUZ6a0JXS1pkOS9FazdqYzc5STI3R3EzWEVRL3c2VUQyN25FdWVUQnRoVVF0R2x4Rms4N2dVemdsUW5meUt6RzY3RWp4cytFeUpXWmN2NzJaQUhCRzQ3M0JwelZ4aklJM05GcHlXRjBUeFlqWDgiLCJtYWMiOiJjNzk2MmVhYjEzNGJjOGU3YWUxMGVkN2M1YWUyNmUwYzY3MmM5NWZhMTE0YjQ3N2VjYWQ0MWM2Nzk4MWVmOGUwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:09:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353842998\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1055081332 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2306</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1584;&#1585;&#1577; &#1576;&#1585;&#1610;&#1578;&#1586;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2306</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>51</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055081332\", {\"maxDepth\":0})</script>\n"}}