{"__meta": {"id": "Xbe6611de4ddee0606c4e5952121ef2d1", "datetime": "2025-06-28 15:00:34", "utime": **********.249474, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122833.793102, "end": **********.249486, "duration": 0.45638394355773926, "duration_str": "456ms", "measures": [{"label": "Booting", "start": 1751122833.793102, "relative_start": 0, "end": **********.161148, "relative_end": **********.161148, "duration": 0.3680460453033447, "duration_str": "368ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.161156, "relative_start": 0.3680539131164551, "end": **********.249488, "relative_end": 2.1457672119140625e-06, "duration": 0.0883321762084961, "duration_str": "88.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46254592, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00197, "accumulated_duration_str": "1.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.195708, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.68}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2053368, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.68, "width_percent": 21.32}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-6697116 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-6697116\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2064822534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2064822534\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-183287171 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-183287171\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-880573492 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751122826313%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlnODRaQm0vL1BtRVlHTWxYcWovcVE9PSIsInZhbHVlIjoiVUEwTUhmWjJxYmJqeTdZTGdYUWJPeWdydjIvWVpqMXlHU1dxUHZjSWt5ellVWHcvUis4cVdFOEEyRXdPS2pSendBYitXTnV1Tm9NbnY0SFp6eVU1cThLWFZIVnB0Vmxha0tBbEMvVThKeXpZMU9TWUhrYzdPbiswNm85bGJaa3lDdHlUMjVNekVXV1huWGQwNWZxZlNTbGZLY0Myc3VXeEZENE5iTnRyazduTWJNbHNTUjMyZmE2b3V2L3pyc2Z0Q0JoSi9BSml2VWsxM2dkMGw5cmZhWUdxRmhkV0tOSTIzOHdiZ2lmUGNSdmp2SDYvWkMwMmd5V1pBVTAzcHpaejNvQ0lCSmhwSGNUQ0Q4QzdyUUNqSlFUUFdnVEx0YU5jUXppVmpDbUpOVmpNdzJTL2Q2UzFXdS90Z1NZazZzZm1LUkJ4S2xPUVd6SlhHUVVGYXNyTzc3NndQWW5vYmcrR2hrRUVZU2ZmdTdlRm84bm4rTkNYRFZpN21sR1BmN25FSHNLT1FLbGtXVGxPeXA3R0pKOEZlNGc3U2NCamxQNm9KNUxsL1FoMjVIa2pHcXUzZTZTMkNrWHY2Ym43R1hlbFVPWDdjNWZrNHpSMHNtRkpDV3pEcEV3OXBrbXIxbzFmdTZrMzloc2Y2QzJwTzVaNUhRdDVQcjV6bXlJeStoN3MiLCJtYWMiOiI1MDQwNDc3NTcxM2UyYjQxNGI3NzVhMzExYjk3YzczNzVkMGUzY2VmNzhiM2Q0YzMyMmU1N2E3ODRkOTZkYzA2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZaampmS0EzejJCTEs1TVNZMHNEVFE9PSIsInZhbHVlIjoiNHV3K1JGZEs1UDFlMHlFUVNqa3NLWk92eldxTWdjZGQ4MXFpdXBvY1h4NTg5SlNoeGx6NlFLOElqcGU0WHBxMHlKT0IwRlptQmI0VVlGZkpkQVMyRnlMZXBlWDhJSGVScDZHVmpGQktPVTA1YWFyL1RFcUxyekpOMEhvMjJpYUtvOVorM1NCaExqcEM3N3FVb1VTZlRkYjVZMEZGenhGRGVuelo4MWMvcE5LazRWYnJBSWI1dUJQR1lyMmsvYkYwYVR5SGlYN29RNE5Qa0xyWUdSbFlzUVhwZmlMbk10aVZMRHk4WVdKMGRZODhQb0tiWjhxemlqMUZsUFdPeFdkWDRiZjRyQTlVSEpVN1pCc0J6OGVaSWk2RithNlpnbkFvc1NiYjdzUlA2QnZmMmdlaTVTSnRJeFBWOHc1TThoZCtLZEZnMkxGOERmcmsyd2lWSnBNRFdnYXJ1b1o3bVRiRlZOZldMM1Vyd3A4eU5xWU5NdFJUTDhwMXd2ZGpHZHFiYllmSkNRSXR0NXllWStuRWUvRzhIdE04cUpEYlQrTHRLckVYMGROaGJDcWFWQlhmTWt3UnBKKy9SdzFIcVhQWXkzTWE2cFRPS3dtVVFUb2o2aWZTc1BwSUR6bDdYeUduVFFnM0JDSnBkRm1SV0JSSjFxOEtIQm5Eb2gwTW16aXAiLCJtYWMiOiJhNjQzN2U5M2FmYWFmNGI2MjFiNTdjZWYzYzRjNTRlOWU4YzA4NzE0NjZkNzBmZDBkNDY4MzM1ZGFhYjM1ODQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-880573492\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-119505837 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-119505837\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1823227768 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:00:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdMazk3Z1ZVVHdMVURqRFZYeHdPRnc9PSIsInZhbHVlIjoiQk5VMm1RU1Y4TFpIZ3YrRE5BVlJxUFlqWVFQYVBTVzMzNFpQNzVTRVVIWnB4bllzNmM4RHVtaEhzdVVaWGlxYUo4MFIwTjBPaktlNTdidTQ1MnJ2YWVWdld2NkJaVjlLcDJRL214b3FBRjc4NGVuMGk5cDVnOTk2akJuU1JaQWpGenpSVkFUWk5BajdYejk3d0ZoMjRIUnJtSXd2TVlDWCtFT3ZTcDNWT1NZSi9oeFdzRlNEYTRyYjd5ZHlNWUg4VlgwdE5kNXBrN0pjZGdXWkt3dVBsQTZRamczU2cxZUdKOGdCY1BmWjNpcG9ybnpBczV5Vm1zTDY2OWE0RUVaQVVOTkVrcTlKZ2xNN29WNVp2a0k4ZzRuR05wTVVkVW9mdGh3ZHNrRmdFcXd1a0ZBRk96YWdKeElIM3plcTJsNkNUc0xITHAwTHJiMHJ0ZDlhZ2g0RFI5ZWViV01QdHJZVDdSRTBzVWFqblpCMjc1Mjk2L2t2bms1bTdWb0srVmo4ZDk2RXRlQ0FMN1BYZzJXVWRWTFFWSlAzL21NcVZNRk5iVXlLczZWeWVQSkJiR0FhM2xmYWJLaG1LTzBQRUY2WHprSW1IakRQdWxuMnltYXFaQUUzNi9FbkY1RzEvTjVLMHlVWFlKMWZRUnc5NHRrT0xXbzdFVDR0VDI5ZU84ZzgiLCJtYWMiOiI3YmZjNjVkOWFmMGIxOGUzMTAyNjU5MDA5MzZiYzFjNGQ2MWM0ODQ2ZDE3MWQ5Nzc0OTVkZDNlNTQzYjE3M2UyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNlMGxCSUZMWUdMVC9XSTZTdWpNYnc9PSIsInZhbHVlIjoiS0g5aTAzQW9jMGJnb0Q4MjNCdEVERkNEZ0c5bWsyZU5pNGU0dzYxc0UyTzFpMFp3T2EvUXZJMGllNFFMbFhPRm9mMEJvR21tTW41OXRTODBrMmJHZWlmTVNvOXU1YmVMYnNYNnVjd2xIQ0UxNk03eFQ4eUVSaDN6U2lGZGt3QXlEYnpZUFFGTkh4OVd6WC9lYkJoS0lmUW4wNFB5SXJCZlRWY2VyY2tzTXYwTFJlOU5GQ2J0dkVqZkVQcjlrMHRmM0RGMmRvaEthNGVnVUdua0tCL24rT21GUy85aFJRblcyQWU0aW1hV3htT2F0eUF6YTFZTVFxZS8xU2s0ME1CcDE5cWdoRC9rMGF5cUVCM0U1dktIenhzYVVJTEYzYjJBOE5nZysrZmMvWndPWHhlbHF4N1B0WDV4MElvbVdsRmluQmNSSzVMaEhYSkNkWkx6Q3ZSVStOWEZlcExtVFVOcFpodWJ1USs5RXgwdjU0OVo0QlgrLzdDbUQxMGZoUXpORnNseFJ6eEExNWJPdmJ0bVdGNEdxOTRqbHV6aWdyUTBOMWd3TUpiNytJWGg5OElGV2dLbnJKbTU2MnRhMWVhdWNKaGNYMUVudDBIenFxR0RPRDdodnlnZTJuQ21ocDNFWE1VMFdvTVBwbTdRV3JGait2UjBzK1ZNK3NFb2J2T1EiLCJtYWMiOiI1NzI4ZDI5N2ZlYmUwMjI3MDdlMzM4OWQ5NjcyM2ExZGM5ZjM0M2I3ODkwNWY3ZTk5OWVlMzU5Y2RhNDRmMzk3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdMazk3Z1ZVVHdMVURqRFZYeHdPRnc9PSIsInZhbHVlIjoiQk5VMm1RU1Y4TFpIZ3YrRE5BVlJxUFlqWVFQYVBTVzMzNFpQNzVTRVVIWnB4bllzNmM4RHVtaEhzdVVaWGlxYUo4MFIwTjBPaktlNTdidTQ1MnJ2YWVWdld2NkJaVjlLcDJRL214b3FBRjc4NGVuMGk5cDVnOTk2akJuU1JaQWpGenpSVkFUWk5BajdYejk3d0ZoMjRIUnJtSXd2TVlDWCtFT3ZTcDNWT1NZSi9oeFdzRlNEYTRyYjd5ZHlNWUg4VlgwdE5kNXBrN0pjZGdXWkt3dVBsQTZRamczU2cxZUdKOGdCY1BmWjNpcG9ybnpBczV5Vm1zTDY2OWE0RUVaQVVOTkVrcTlKZ2xNN29WNVp2a0k4ZzRuR05wTVVkVW9mdGh3ZHNrRmdFcXd1a0ZBRk96YWdKeElIM3plcTJsNkNUc0xITHAwTHJiMHJ0ZDlhZ2g0RFI5ZWViV01QdHJZVDdSRTBzVWFqblpCMjc1Mjk2L2t2bms1bTdWb0srVmo4ZDk2RXRlQ0FMN1BYZzJXVWRWTFFWSlAzL21NcVZNRk5iVXlLczZWeWVQSkJiR0FhM2xmYWJLaG1LTzBQRUY2WHprSW1IakRQdWxuMnltYXFaQUUzNi9FbkY1RzEvTjVLMHlVWFlKMWZRUnc5NHRrT0xXbzdFVDR0VDI5ZU84ZzgiLCJtYWMiOiI3YmZjNjVkOWFmMGIxOGUzMTAyNjU5MDA5MzZiYzFjNGQ2MWM0ODQ2ZDE3MWQ5Nzc0OTVkZDNlNTQzYjE3M2UyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNlMGxCSUZMWUdMVC9XSTZTdWpNYnc9PSIsInZhbHVlIjoiS0g5aTAzQW9jMGJnb0Q4MjNCdEVERkNEZ0c5bWsyZU5pNGU0dzYxc0UyTzFpMFp3T2EvUXZJMGllNFFMbFhPRm9mMEJvR21tTW41OXRTODBrMmJHZWlmTVNvOXU1YmVMYnNYNnVjd2xIQ0UxNk03eFQ4eUVSaDN6U2lGZGt3QXlEYnpZUFFGTkh4OVd6WC9lYkJoS0lmUW4wNFB5SXJCZlRWY2VyY2tzTXYwTFJlOU5GQ2J0dkVqZkVQcjlrMHRmM0RGMmRvaEthNGVnVUdua0tCL24rT21GUy85aFJRblcyQWU0aW1hV3htT2F0eUF6YTFZTVFxZS8xU2s0ME1CcDE5cWdoRC9rMGF5cUVCM0U1dktIenhzYVVJTEYzYjJBOE5nZysrZmMvWndPWHhlbHF4N1B0WDV4MElvbVdsRmluQmNSSzVMaEhYSkNkWkx6Q3ZSVStOWEZlcExtVFVOcFpodWJ1USs5RXgwdjU0OVo0QlgrLzdDbUQxMGZoUXpORnNseFJ6eEExNWJPdmJ0bVdGNEdxOTRqbHV6aWdyUTBOMWd3TUpiNytJWGg5OElGV2dLbnJKbTU2MnRhMWVhdWNKaGNYMUVudDBIenFxR0RPRDdodnlnZTJuQ21ocDNFWE1VMFdvTVBwbTdRV3JGait2UjBzK1ZNK3NFb2J2T1EiLCJtYWMiOiI1NzI4ZDI5N2ZlYmUwMjI3MDdlMzM4OWQ5NjcyM2ExZGM5ZjM0M2I3ODkwNWY3ZTk5OWVlMzU5Y2RhNDRmMzk3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1823227768\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2005300503 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005300503\", {\"maxDepth\":0})</script>\n"}}