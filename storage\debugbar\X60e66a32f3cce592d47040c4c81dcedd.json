{"__meta": {"id": "X60e66a32f3cce592d47040c4c81dcedd", "datetime": "2025-06-28 15:25:49", "utime": **********.126968, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751124348.608552, "end": **********.126985, "duration": 0.5184330940246582, "duration_str": "518ms", "measures": [{"label": "Booting", "start": 1751124348.608552, "relative_start": 0, "end": **********.058636, "relative_end": **********.058636, "duration": 0.45008397102355957, "duration_str": "450ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.058645, "relative_start": 0.45009303092956543, "end": **********.126987, "relative_end": 1.9073486328125e-06, "duration": 0.06834197044372559, "duration_str": "68.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45617664, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00326, "accumulated_duration_str": "3.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0958989, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.951}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.10857, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.951, "width_percent": 16.258}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.117101, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.209, "width_percent": 17.791}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1464287951 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1464287951\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-115284329 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-115284329\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-432437490 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432437490\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-613139594 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124345871%7C14%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBzWVNmSEZRU2JlZSs0d1YzVUZUZXc9PSIsInZhbHVlIjoiWmdqNXJPNnJHSkVJWXpWL3R3KzZhblQxck82cW9sbEJjZkh4UlViN0krYUdmNlN4ZUI5SjdKUWdJbmw0VmVxd3hqVmRPYkRhYXVIOHh6ZXBJM01CZzVHZjBKWjJ2OUtLMDh0TnlhcWRjZDlldzlKU3p4SHh1WGNoMnJ6L2piWHp4QXhPU1FMNm9GektONGFzK2xraERHWnR3VktPY2ViRW9jR1dOYUtpVTRjRXNRMmlBQXZFclROcXF0R2tEbU51eFZSN3JTM0ptSmhEb3FteXVFcEloSFh0Nmd4U3JCMmYyTEZkYkpHdjkyZkJwb3hQQVJMQlFWR1NJSTZHQVdKQUYxR0VFN0lwVlRzRmRjalp2enFvV2lmTGxNcDdGc05wNC8xRDdvNXVtc1pVT0FqbmJwZmZWeUs0QVlJZDdRZldBQ1AzYzNoWnNOaWhhdy9HUTE0UkhjOW1rZ0wxcWZyRkxxWEIydllYWDBnRWdReHJrY3c1NHhzQVJpTTlnSjdRWmwxT01PT3U1QVRraHdIQ3dPUnVlaWx6SXBDYk91cVY1cTZNbW9SZkRkNFFaQVc5U05QTklWVkFFTHhWdS95dkxwaGU3SVFGYXgxMHZEclcvUTd6ek1rZlYvQmxLeTE4M0F1VisvMS9Ka3Z5R2tJZjhMWUUyNVpJYndBUDdmMWoiLCJtYWMiOiIyZjg3MmQ1YzE0YjllNGRkNDE4MjljMGJlN2I4Yjg1N2ZkZDIxOTBjZmI0NTZlMDM4MGM5NzMwNWNlYzkzOTQwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlBodDV5ZUJHWnR1MjgrakZWaUtMTFE9PSIsInZhbHVlIjoiYzhIekZGVGFHWUVRcTIwdHdDamZZK3BHSk0wa1JQeFpIekN3MFpISDhVOU9sZDBGVjVmc0RISjRnTjhIM3NseThENmJUUHV5S3o1TVk3ZlNzTWtDdGlmWEV1ZGRrSDhjR1NXTVlUQ2lDRzQ4aEtSWVdDa1paNlBxdXluUlREMU9NanEvV0JKSHl1RytUNTBuSE9aakdXTGxXa0twUkdHZEV6eVptUEFEUEYvT3FCYTVtM2dQUTJXeU9jbFFXbmRSMmhwaFM0bXlSSXNsRDF4Mm1jZ2d0ZFBMbXU0ZjB5WFVVUmRITHdBUGt6elFjZjNJaFp3VkJiZVpSOW5IaW1VWGZQdDd3M3VFeEdNU3JLNitETzk1VGhxVnhQQ3JadkNZMjlrRW5IVjJkdzIvY0dtMTYrTTI3Qm0yWnc1Ty9ndFBYTDF1clRQSzN0YVdFb21xbnpUWTk3VVBTVGhNU2lwQ3dTbHQ0Q3FhWnZEV0pHMk41ajg5MFBoS0dPV3o1NWlyRng2czJlZ3ZDUDNnZDdjK1VKRHRKYUUyQVFTRkVmaWJSQ0VDb2t4S3F4T0FlckgrK21kbGtCWndaM1VlWVQ5VGZWbWFpYmFXVU01d1FNT2lGK0QzTGxkWTFkZ0hIRzJBYVAvUVY5ZTR5WVI3Slp2NnhpZmd5NzRMMHlMNXBuaW0iLCJtYWMiOiJjN2ZkMTUxNTU3YjRlZTMwNTg4MDZiNTE0YzI0YjVjZDIxYmVhMmYzZWZhNmZkYTI0NzVjYzg3YjA5NTExNGRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613139594\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1927854333 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927854333\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2142399343 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:25:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJMMnl1NkhQRit0OTRneUhqcGYwc2c9PSIsInZhbHVlIjoiUnZIbEdpN0NrcTF0S25NT0FoYVBsdG5NUG1FdlRBZnB4blFoNVFTOTJ3ZE1tMlU0NHZSeHltdURHaDV3YU1KSG9PckNuVkplcTIwUnAxMitlN043bkZ2SFliVnliR2owaGgwZU1EbENJc2QvT1dUN2wzWktFMUNEK3hMb2crOWFzaTZwMC95Q3dNT3diTFdBZWZSUHB3L0VvbzJyVEF3UVZVK0ZhNTdZN3pnWTZ6WFVVbGVRUFlVT2NGSVR6bHdDb3cwWlY1TDVOV1dLNVZud0JvdlNLQWMyY3hyT3hyOG94ZUZXVnU1TlQyT1RZU0ptOWdBNDUxNlA0UVEycXI0ZUVmSXRIZnVnL1pqUFMxNGY3R3lzR1d5VmJMSHR0OEw4RWExMGJpUzBNdTdVYmNSbkNTRzVxa09GVmNpZHFCY1hxeUVqVFFBNEUvZm82WHAzZHdWVEkxdVRmTk9HTEZPQVllcDVTOFJEeUZscHU2a1JJY0RELzdPcHNzbFgxamZrNkpteFp6a0hBRVpuQlBoTkVVT3AyeXZLcnRvejJXanBXM1NXb1hkOHBhdjVzK2hTOWJHTVMydzN3MklBSHVJRHBWbit3aWQ4TjN4djUxc2pFTHBmRWQ3TnRNVXkxMkxrTlVtb0I4ZzV1NVpWZ3pidERpNjJXVVdoVEduMHczZlYiLCJtYWMiOiJjYTU0ZmIzMTdiYTk3N2RlMTY5MzM0OTE0NzRlNjg2MDk0MmUwY2VlZmFlZWEyY2ZjMTY2NTcxMjM4MzlkMmEyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:25:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdyOXNKU24rZHZQUEVlbVIzd3VLelE9PSIsInZhbHVlIjoiN0JVUk1MM243MElwTlJoNVhDa2VNOEI1Y1VVR0tnY1VXL1VCeVV4NnE0bnFPa202NUtUTXVScXJjblhWUXVvNGI2Nm5VOE1MdEorang4NGh2VmtVTy84UmpNOWhLamRIUEs2eUEvbjdHQlBtVGN3TndWd2VTREltcUdPNk1TYmFraTZ1N2lyVWVkTmZ4SG5YTHdYMEYvMGdHWlVuQm9EMlNSMWFobkNjc2h0bHhOU2RxWlpVN3VsdmNFeDhVOW5GN2FyTXJTdGplcG8rek1hMW1zOVBaN1EwVnpZVzc2OTU5OGZITHhXa3lDSDU5cVBQemRqZjMxSXJXbFllTW0vVnRPM1pjWHBDV29CVyszV0dseFhnS0NHMHltU3hUQWYvMlpIY3VWL05sd1VYTzM5YUV6Z3M1NlZNQ3JLdXZ4d2Urd2U4Vk5kNVFtcGhKcXIvbDlEcjBjeEZ4SGVobk9zNGdnZ0E5dmU2UnIycVZGK1ZiaXB4ay9RdVpUMUlqUnV0eUJjekJYNmxyK05HS3hGMit3QkREMFJDTmFscm1CNllzeVFQQlh1cmVOK2p0VWI4VFU2ditTWmJQeHJjUERIcEFYcndQaHRwMkozYk5ieEV5Y3lGUkxnakFmREROYVdmcmFWZSthZ0V3Z0VPTGw2MjUvQUhrbyswd1RaUUVUb1QiLCJtYWMiOiIyZDFmM2NkZGIxYTY0Y2FhZWFhYzQ5ODQ5OWY5ZDMzMDE1MGZlOGQ1Zjk4MjNiYTExMmExODc5MjZjNzAwNTUzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:25:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJMMnl1NkhQRit0OTRneUhqcGYwc2c9PSIsInZhbHVlIjoiUnZIbEdpN0NrcTF0S25NT0FoYVBsdG5NUG1FdlRBZnB4blFoNVFTOTJ3ZE1tMlU0NHZSeHltdURHaDV3YU1KSG9PckNuVkplcTIwUnAxMitlN043bkZ2SFliVnliR2owaGgwZU1EbENJc2QvT1dUN2wzWktFMUNEK3hMb2crOWFzaTZwMC95Q3dNT3diTFdBZWZSUHB3L0VvbzJyVEF3UVZVK0ZhNTdZN3pnWTZ6WFVVbGVRUFlVT2NGSVR6bHdDb3cwWlY1TDVOV1dLNVZud0JvdlNLQWMyY3hyT3hyOG94ZUZXVnU1TlQyT1RZU0ptOWdBNDUxNlA0UVEycXI0ZUVmSXRIZnVnL1pqUFMxNGY3R3lzR1d5VmJMSHR0OEw4RWExMGJpUzBNdTdVYmNSbkNTRzVxa09GVmNpZHFCY1hxeUVqVFFBNEUvZm82WHAzZHdWVEkxdVRmTk9HTEZPQVllcDVTOFJEeUZscHU2a1JJY0RELzdPcHNzbFgxamZrNkpteFp6a0hBRVpuQlBoTkVVT3AyeXZLcnRvejJXanBXM1NXb1hkOHBhdjVzK2hTOWJHTVMydzN3MklBSHVJRHBWbit3aWQ4TjN4djUxc2pFTHBmRWQ3TnRNVXkxMkxrTlVtb0I4ZzV1NVpWZ3pidERpNjJXVVdoVEduMHczZlYiLCJtYWMiOiJjYTU0ZmIzMTdiYTk3N2RlMTY5MzM0OTE0NzRlNjg2MDk0MmUwY2VlZmFlZWEyY2ZjMTY2NTcxMjM4MzlkMmEyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:25:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdyOXNKU24rZHZQUEVlbVIzd3VLelE9PSIsInZhbHVlIjoiN0JVUk1MM243MElwTlJoNVhDa2VNOEI1Y1VVR0tnY1VXL1VCeVV4NnE0bnFPa202NUtUTXVScXJjblhWUXVvNGI2Nm5VOE1MdEorang4NGh2VmtVTy84UmpNOWhLamRIUEs2eUEvbjdHQlBtVGN3TndWd2VTREltcUdPNk1TYmFraTZ1N2lyVWVkTmZ4SG5YTHdYMEYvMGdHWlVuQm9EMlNSMWFobkNjc2h0bHhOU2RxWlpVN3VsdmNFeDhVOW5GN2FyTXJTdGplcG8rek1hMW1zOVBaN1EwVnpZVzc2OTU5OGZITHhXa3lDSDU5cVBQemRqZjMxSXJXbFllTW0vVnRPM1pjWHBDV29CVyszV0dseFhnS0NHMHltU3hUQWYvMlpIY3VWL05sd1VYTzM5YUV6Z3M1NlZNQ3JLdXZ4d2Urd2U4Vk5kNVFtcGhKcXIvbDlEcjBjeEZ4SGVobk9zNGdnZ0E5dmU2UnIycVZGK1ZiaXB4ay9RdVpUMUlqUnV0eUJjekJYNmxyK05HS3hGMit3QkREMFJDTmFscm1CNllzeVFQQlh1cmVOK2p0VWI4VFU2ditTWmJQeHJjUERIcEFYcndQaHRwMkozYk5ieEV5Y3lGUkxnakFmREROYVdmcmFWZSthZ0V3Z0VPTGw2MjUvQUhrbyswd1RaUUVUb1QiLCJtYWMiOiIyZDFmM2NkZGIxYTY0Y2FhZWFhYzQ5ODQ5OWY5ZDMzMDE1MGZlOGQ1Zjk4MjNiYTExMmExODc5MjZjNzAwNTUzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:25:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2142399343\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2103643133 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103643133\", {\"maxDepth\":0})</script>\n"}}