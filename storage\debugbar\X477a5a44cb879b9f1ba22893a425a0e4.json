{"__meta": {"id": "X477a5a44cb879b9f1ba22893a425a0e4", "datetime": "2025-06-28 16:19:27", "utime": **********.880954, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.393487, "end": **********.880968, "duration": 0.48748111724853516, "duration_str": "487ms", "measures": [{"label": "Booting", "start": **********.393487, "relative_start": 0, "end": **********.819969, "relative_end": **********.819969, "duration": 0.4264819622039795, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.81998, "relative_start": 0.42649292945861816, "end": **********.880971, "relative_end": 2.86102294921875e-06, "duration": 0.06099104881286621, "duration_str": "60.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46439184, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2441\" onclick=\"\">app/Http/Controllers/PosController.php:2441-2475</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00243, "accumulated_duration_str": "2.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.862485, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.601}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.873238, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.601, "width_percent": 21.399}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1464/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-670137672 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-670137672\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1948750614 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1948750614\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-325702559 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-325702559\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2105993695 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127556646%7C37%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlIvZkVqMTBacHlOaFJ5TVlHYy9FS3c9PSIsInZhbHVlIjoiemdtbkVuNEM2dzE1ZWRPU3hDUmU5UzNKbzVEZEhvNXBpT2xtMUV6YWVGL1ZqNUZORmlwWDM1TERieThDT2tDSXlWUkxxZ3RxenFOUFE3cG1Pdm5xNmpGajBYL3NyVUNqbWtiZytnKzFGUUd0ZFRNa2sycVJKQ1FpYUVIMVZ3alRkWWptbngwU09vUmJwOFNyMkJ2dzgwUUFVTnU3cGpST21oMmh3RGZxb0ZaaEEreStHRmhtTkZGU2NycEg5dnZEak91ZkRWTDFXdDNIVmszR1JlZFhMb0dTTEUrOHpJdGR5elZFVUsvSEFuWHNUVUtydGFMMDNUUDMrdjRMOEhXV3laY2hqVFY3TlVKcGJiSjc4K1U0c3JPTlB1bEg5Rk5pQXhtREpQUDRCTlBaWDlVRFV3SUIzZXpmS1ljd2dNWDJPT2crRFhla3pDV3Z2ZEJaQWtTTWZtV2F0eElsVGo5R2NOREZlaFc2QnFhbTVLaGw5aW5WTGR5TlNleHgwa29SM2lvekJVelhNMEl4Zks4ZncvU2lpUWJBVVlmN2RTN2FJNWcxWllNeDZWblFqWGUvZkJaQk1IeFFxRHltSzVPcTZoekVJT3ZObVhlMWt0c29YbkdWMFQ3bGFVaHltZW5OcFFSMkVWVUs3akoraXRaMzhCU3NmckEvWnc3QmpVU2UiLCJtYWMiOiI0ZmQ4YWI2NjNiOTZhZDc4NTc5MWFmNDk0ZGQ1NGY3YTE3NGI5YmQ2OTU5YjNhYmU1N2Y0YmZmMWJlNDdmY2M1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlB1REgvVklHTXo1cHNOMVByQUpMbEE9PSIsInZhbHVlIjoiSFBSa0I2citnVy9HbFpYc3lZbDBBblJIcjAyMGdtN0hMcjU3Q0o1R2piVnpPM0FzNkRvYjBuaTQ4ZWV0MnBWZzdaMUQyQlRwVm83cFpMZWlvT2hGZ04xRldGbnByNlNnVGJxUXdlK0p5aFIvWWdMMnZoK2dTaHRWZUFWalFUd2UrdGtxL1FQUklydUNxWDJOU1FRazByM2R6OGpRQkkrWU9iK2wxNExBYUxRNDBDc1M4TWtPQ0NOK1NZZWtvS2RqUDc3ZG9nT2NpZ3pSZ3o1MUlDUGlZRkF6TXc3MWcwS3dBemF6MlNCT3RlSGxFL3VJUndXQ2poeXV2UklkUjliOC83Y0NMOGs4dFVwOGEyUktuNFE3amRZdjYwUVQrZ0wyanJRWVlEUERjekpqNGpsRnZkWnlSNU9PZE1DTDA3RVpDZ2hoVzlteGRPOTNXTzdYVW1lWTMxUnZYL0pTRnBZTXQveGZVSCtYMS82YzhJQVRQVmtxZ2pJeWQvK2dFQzh0ZnM5THhLcmpFM1Jna2RtdTM2RFViaE5vNk0veUwwOHBCVndhQ0NBYnh1LzJBaVZOMlRSeG9idVRQV3RVcCtJT2hUVWQ4aXhGUDRoY1VGSWxaSWoxK2c4dEhrNXJJTlhHK21lNUY0NXhpczNZNFlBSjBmdC9oUXIzTmVXTS8raEgiLCJtYWMiOiI5MjNkYWFhNDNkOTlkNDY3NGQ4NDQ1YjIwYTk0ZjE4NjQyYzM2MWFmNDdmODk1YjIxYTk5YTlhOGI1YzJhMTk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105993695\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1781251079 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781251079\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-646792464 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik44OUVZeHRCVVJnTlk5OU81Uzl5Q1E9PSIsInZhbHVlIjoiZkxOYjZ2NXdoM3VuaFJSMWpOdTI2MW1yZjNQMmVnWDBpVFVXbVRjTitaek1CMjJhWWI4a2JEWUdiWE5xRndYS0ZnT0Fub1RkT0xLUWFqbCtIQjJlMmQ2YU92eFVEZHd0SmVOTXdESTZrQXBoZ1FDWTdYdC9lRjRXdi9rRVRzclBjQVNHd212MWlPMkxNL3VLZlV1emtSVllWVjhTWmNodFl0c1V1QktWMmZSQ1hVdHNKYlRVWGxkaTA1SnY3Ym1ibEdsdTY2NjArc213U1FyZkVPc0JoQWdWSnZxVzhoYXd4aWRHd2FqZlE0ZFFwUEVhMU9VNnk4S1luRjU0dGtaK0xlSlNYNmJKdlpMY2ZvUG84ZzdNdEZOY0lFOHRpMUg5WjFocENlT3R5U1pyUzJCWWdSTGZIeFhNVzh2YlZrclhoMVZ6YXlGR2srdkczZWREWHFFMGhaeENqcEQyWC9jdVNVS2paR0pCSlpGMTJXZjhHbmV5eWUwN3g5ajBtMkRici9oVHVZNVo1N1YybjdiNURFMFFKM0dadGtqVEMycXBKdUQ0dnpjOFUxQ0k4dHkvN2RGc0dDaHp5WTRDQUE4cDY5SlhXR2o4V2RwcW1WcGJ1cVpvRkFGWlpIbTVIVjVaRC9IV3VMdDBpVnZMUHU2eEZKV2J6NDQwZTNTSkxONU4iLCJtYWMiOiI2ZGQ2YzM3NWVmZTU0MGE1MTc4ZjExM2YwNDYxNDY3NmI2ZDM1NGUwN2IzMTVkMmJjNDliOGYxM2EwZTZlM2RkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkptTHpQcFNDR3JJQ29oSk9zWnFMYkE9PSIsInZhbHVlIjoiN09iem1hVDRFaFRrVGd5RUlEN1EwamdheXYzZmVvMXZMSW45aURYWEdUWDJxeXhLdUNrR2dEaTJPZTJSaklOS0V4NkpHU3BXRTA4SnovVjliNzM3ZS9pdU1qOFZxQ05kYjZMLzFjbjNUUnFLK2x5YmtoZDZSQ1ZKK0UrcE9QMVQxQU8xRHkvRHlmVVNIczcxSWNiK04wSjBjcHJwZlZOSG5ZYzhLbVNOODVpU1lkREoxY3BObWZPVFJEZnVNZWx1aXZjekl1RURaNm05bFVEVWhlY3RGdWRMcjV3V1ZtLzN1ZDVoZWVrYWp3SkE4MDFxQUZ6a0R1QnRxbUVjVmE5MDNlZk84Y0Z3YWRsNzduZzZub1BCL2tsZklBQlhrV2JQYUYvd1loRjRXeUxQNUFNN2hPTklaOUF0aDRNbGMwaGxoaWdRazBvTXFOWjVhRHMzM0hTRkltWE1VS1o5amlLNkNJTTFsYXcydncyd3FjRVM2RXQ3UWZQYiszUExVWnYxdEtnYlo3UzVRdEp0TlluTlY1a3NxMzZMQTdIeTFkSG9scXJxbmMxVDVIZmNWSmk3WGhQOFd3a0pFTzR2clZnak5FYy9yR0Z0ZjBob0M4UVdjNXcxL0FvN081eFZzYWw3eG5NZVlYYzdDenY5UG8xNmcvZjVKNkhGamgraGg4cmsiLCJtYWMiOiIyNmFkODdmNDViM2JkODNjNTY3ZmMxM2JkZTdkNzkzYWZmZWU5MmRlMGQ0MWVlNDRkY2VkMTBhNjU1NDI0MjY2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik44OUVZeHRCVVJnTlk5OU81Uzl5Q1E9PSIsInZhbHVlIjoiZkxOYjZ2NXdoM3VuaFJSMWpOdTI2MW1yZjNQMmVnWDBpVFVXbVRjTitaek1CMjJhWWI4a2JEWUdiWE5xRndYS0ZnT0Fub1RkT0xLUWFqbCtIQjJlMmQ2YU92eFVEZHd0SmVOTXdESTZrQXBoZ1FDWTdYdC9lRjRXdi9rRVRzclBjQVNHd212MWlPMkxNL3VLZlV1emtSVllWVjhTWmNodFl0c1V1QktWMmZSQ1hVdHNKYlRVWGxkaTA1SnY3Ym1ibEdsdTY2NjArc213U1FyZkVPc0JoQWdWSnZxVzhoYXd4aWRHd2FqZlE0ZFFwUEVhMU9VNnk4S1luRjU0dGtaK0xlSlNYNmJKdlpMY2ZvUG84ZzdNdEZOY0lFOHRpMUg5WjFocENlT3R5U1pyUzJCWWdSTGZIeFhNVzh2YlZrclhoMVZ6YXlGR2srdkczZWREWHFFMGhaeENqcEQyWC9jdVNVS2paR0pCSlpGMTJXZjhHbmV5eWUwN3g5ajBtMkRici9oVHVZNVo1N1YybjdiNURFMFFKM0dadGtqVEMycXBKdUQ0dnpjOFUxQ0k4dHkvN2RGc0dDaHp5WTRDQUE4cDY5SlhXR2o4V2RwcW1WcGJ1cVpvRkFGWlpIbTVIVjVaRC9IV3VMdDBpVnZMUHU2eEZKV2J6NDQwZTNTSkxONU4iLCJtYWMiOiI2ZGQ2YzM3NWVmZTU0MGE1MTc4ZjExM2YwNDYxNDY3NmI2ZDM1NGUwN2IzMTVkMmJjNDliOGYxM2EwZTZlM2RkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkptTHpQcFNDR3JJQ29oSk9zWnFMYkE9PSIsInZhbHVlIjoiN09iem1hVDRFaFRrVGd5RUlEN1EwamdheXYzZmVvMXZMSW45aURYWEdUWDJxeXhLdUNrR2dEaTJPZTJSaklOS0V4NkpHU3BXRTA4SnovVjliNzM3ZS9pdU1qOFZxQ05kYjZMLzFjbjNUUnFLK2x5YmtoZDZSQ1ZKK0UrcE9QMVQxQU8xRHkvRHlmVVNIczcxSWNiK04wSjBjcHJwZlZOSG5ZYzhLbVNOODVpU1lkREoxY3BObWZPVFJEZnVNZWx1aXZjekl1RURaNm05bFVEVWhlY3RGdWRMcjV3V1ZtLzN1ZDVoZWVrYWp3SkE4MDFxQUZ6a0R1QnRxbUVjVmE5MDNlZk84Y0Z3YWRsNzduZzZub1BCL2tsZklBQlhrV2JQYUYvd1loRjRXeUxQNUFNN2hPTklaOUF0aDRNbGMwaGxoaWdRazBvTXFOWjVhRHMzM0hTRkltWE1VS1o5amlLNkNJTTFsYXcydncyd3FjRVM2RXQ3UWZQYiszUExVWnYxdEtnYlo3UzVRdEp0TlluTlY1a3NxMzZMQTdIeTFkSG9scXJxbmMxVDVIZmNWSmk3WGhQOFd3a0pFTzR2clZnak5FYy9yR0Z0ZjBob0M4UVdjNXcxL0FvN081eFZzYWw3eG5NZVlYYzdDenY5UG8xNmcvZjVKNkhGamgraGg4cmsiLCJtYWMiOiIyNmFkODdmNDViM2JkODNjNTY3ZmMxM2JkZTdkNzkzYWZmZWU5MmRlMGQ0MWVlNDRkY2VkMTBhNjU1NDI0MjY2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646792464\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-536455510 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1464/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536455510\", {\"maxDepth\":0})</script>\n"}}