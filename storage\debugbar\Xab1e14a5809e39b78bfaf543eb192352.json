{"__meta": {"id": "Xab1e14a5809e39b78bfaf543eb192352", "datetime": "2025-06-28 16:35:17", "utime": **********.952688, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.483664, "end": **********.952708, "duration": 0.4690439701080322, "duration_str": "469ms", "measures": [{"label": "Booting", "start": **********.483664, "relative_start": 0, "end": **********.870132, "relative_end": **********.870132, "duration": 0.38646793365478516, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.870141, "relative_start": 0.386476993560791, "end": **********.952711, "relative_end": 3.0994415283203125e-06, "duration": 0.08257007598876953, "duration_str": "82.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46448152, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2553\" onclick=\"\">app/Http/Controllers/PosController.php:2553-2587</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.019299999999999998, "accumulated_duration_str": "19.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.911073, "duration": 0.01877, "duration_str": "18.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.254}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.943603, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.254, "width_percent": 2.746}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1378870218 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1378870218\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128513642%7C55%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFSMkNRT2tqMklNOHZDNDhsZmRQOFE9PSIsInZhbHVlIjoiaXM0cUhLeUd4T0taOG9rQ0Rxd0IxRFdGZ0dvQlNXQUdsbmVmV1ZTUGN3a3UvaTlMMEZuOTR1NDc2SWtuWUowZ2ovejZMbUlKa0p1WVZzajl3OE1qMzNZMnZINVhJcFAvdHNkdDJ1Y1U2M0dhbFpFdXUrSXhNb3kxNytXVHpKa1dwd0hWaER0RFN3SjljZjdWT0J6M2ZabnBMdDNUQVF6YlI4VEN4aG0wQVJyTklIcHdCOFQyd2xFaFM4QllSNmtuemQzcGtnTDBKMUh3bExLWnpqTHo2SEFFb1hlcjFpY0JoTDdxTXp1YlFNRm5KYmV2ZjExZytOTEpPUzR5MmtDK3RCZHpaMWRUWThWNkVUWGRFTUdUZGlyYm9hbHNrajlibjRwR2NBYnBpb2d3ZUVzTTcySUw5MEtXRGxWdkJtZFBlaTlaSXorRFRjcTI3aE00THJ0ai9JQ09JYVZDZ3FEMnEzU0hpRjNtMkVINGtJY0JBNG85NktHQ1VJZkxTUldsMytSZzBUTGZWOFJlb3NqTVpha1NHTGlMeWhNazhxNE9SSW1sZTRzSHdsS2pTdXp4SnI5enRHTmIyTUh2Y05qZ1ljamEwSHVRZUdjMHhTSUlOcDBaUnByd1BhWFlmN1pBdjNiKysyTmFIelV6cGNrZjQvRnFtVFZzb2VRVHJOVi8iLCJtYWMiOiI5NzhhZTA0Nzk0Zjc4YTMwZjhhMDAzM2YwMGZhM2E1NzVkZGIyYjI1YmQ3ODk0ZDYwNzczZTk4OTY1ZTgyNDkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklncUxUOGszRnV5MTdaN3VHNEtPS0E9PSIsInZhbHVlIjoiTGdPcmNTVTRyMmVRN25ZQ00weW5PTkRUdk9hbFZweGhEdUt5SVRHWjRDS0d1aG0wL09ieklHL1pXMTczeUFHK2tJcURFK0tsLzFpTko0OUVEWGkxaUx6bUI3WU00ZkZiL0c5eU53ZDV3Q2VWdWMxbjBXd21odC9jNXlPTUVPMFdmcXZRQURlcFlpaEhDWXEvb2hnL1g3a2V3ejQ2dERNTHA4ODBpUThVRXVtQldsckgydGltYjJ6VEIrL0lodkxkRkp2K2ZqK3cvWE9DamhVZFIvcDJDUldaaE45cUV5bENvMkdUVGRjVTVqSDE3N2hCbDZQMExyU1I2WWVlcHBTaVdTQVBZa0tOQ21vNUU1SFdXb3BHQ2U4bkRLZGJicGp2cVA2Zm1XN2RQcHJWYzRZMXdmNXVKbzQxMFFmenVWWUovWVRNVmhWK2p1cXNodGlrQVpURUJ4bWpBdXdiVytWdlNYU3dtQWJXS215aXBESHFUQ0kvYjVBak1SS3VlcWIzY3JJZkw2UHJnMjlyTUFhRkJYbmlXVGZmSnliNCthbnZTVlJZcXRHc3A3ZTkxYk1mRnhOKzIwTW1QSXhKR3hvcXc2c0tKdUMxZE8yVHlITmZqaGxQKzJLT3Aya3l1WmZMVFM4cDY0TWNvY21seEo2UnNyZ1ZjTGx4U3pXdDUvKysiLCJtYWMiOiI2MmE0MzRkN2I1ODQ4ODhhZjk2YTZjZDY1ZTgwOWM2NTcyM2Q0MGM4YjQ3ZWFiNWNmOTNjNDBkNDgyODYwZTViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-687598683 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-687598683\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1300858343 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:35:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1uZGk4UTdRWm85eFV2TWZNN2RQUlE9PSIsInZhbHVlIjoiQVk5UjVJTy9rbnpFTjMwVG9GS244SEg5MDlJOGNoQ1R2OVRmVFlTeWplNlB4K0QvbUJhVEE1R1JUMUYwdUJuektSQ1VzdzdQK2lodlN5TTRwTXFBazMveFRQeE9RMFp2bXJrNWMwVHpYTVJYckhlQlF4MTNkaFd3MTFkVzdrWlcxTzZiNUc3T2hCWmZkQXI2cnU5emxLTUVNOWJiSjJ2aTZMOGJlbVIwNENsN2FZRDFaa0hnOUo5ZGFlYS95dzVIUDFIdXJQUG5iaU0vd3Fudk1tVXhZcldWQk1NVllLdVo0TmllVFVVQUNMYUV2a0UreDhoekMyTE5qUjVCZGVOTHFpNUlzMytGeTFiQmNsdzhBRGZOaThlY2pzczZlZzJhTXdZRmtZSGFyQmVDU2MrS1daSXdYK2RpSS9obFdKekZMbGJIYVIramx6UTlyQVpVdC9NYzdZbTZ0SDZBQ1ZCTHZ4Zk1mTHVmTVhlTm9nUjFGOWlRVzdWT3lQQVRnbDJKZU1YT0RXK1JiUHVudjJBN1Vtb2dTZmVma2wzSVhQbXJpSHJnVXA4RTFrbnpCVVUyTmUrOGwrWS8xaXBIRHdwZndQTG5XRnpkOFlRNGF3WXpIUHM1SzZkaS9GMVJwU0NEb2dOM29nYTIyVFVxQmJrWmxMbVI3R2J6dEp4MmZvQkYiLCJtYWMiOiJmMTQ2ODYyZTA2YTFjNzk0ZDhlZjAwZDUyYTI5NjVlNmJkZDYwNzJhZDhiZWFlZWRhZDY3Y2RjY2VjZjE3NThjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZuNWJINkNOSmVLekdRZGwwMFdieUE9PSIsInZhbHVlIjoiNnUzTXVnNytZVHBjVWJvamxQMTlvbzl5b2poZ1gweUM1VkVSa0M5enIrQitjbGF4U3ljdG5NZHlGU1NXNEFLVDJwLzNQOWRQTzRDcVJqdzVPdW1NM3pKOCs4MHlhNSttamVnUTYzNmxZVnVTZmp1eE9OTjNPcDZVVDFrRHdCQTdlZ212cEhkQ2NMQ3ZidXRlVm1sY0xnRUNYV2ZObnNRRDJtSGtyc2RRK0R2aWtUdHBXTGFybUtsVE9menFIV0xDeFFHbHM5OTlXdVlnNEdhTnFjVVpIWEJadUN6dGtrRHNxT0VXeVNJbjZXSTNOUTVhcS93RGFOQ2FGRjVmRHdLcGZ2MTN2ak9ubjRVbHhSbDdzdzJIRkorcGJKNzlsWEZqR2lvQTRkdTl3MEMzMDJvL1JmM0V0N3poMGtxVkw0eXZReXBLWmxETGtNVzhSYnVTTmpwdUlKYzg4aHVJd1ZTNmM0NW9OR3RPdWNtcUFCVnVZVk1WWlBRVkd2eWI0UVlXSldVajNZRkZHRWZYaFRDdlJoY0YxMVUzMjFZNWtlUnAwbjVzQmJWMHFBR2Nsam5BdXc3NnF6YWZ1RU5VRmlZRk9EUlI0a3RjNUNnUWkwUVM4SzJqakZoSjdPOUdjNjJJSHR5NDEvdHhjb0hORkR4WWNVOG5sNUZEVDM0aFVVOUEiLCJtYWMiOiJlNmRlOTlhMjc1ZTk3Y2UxNzE4NDY4NDNlN2ZhMjFhZGY0MjE0ZDEzNmJjMmNlMTRhZGM5MWZmNzdjNmE5MmRlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1uZGk4UTdRWm85eFV2TWZNN2RQUlE9PSIsInZhbHVlIjoiQVk5UjVJTy9rbnpFTjMwVG9GS244SEg5MDlJOGNoQ1R2OVRmVFlTeWplNlB4K0QvbUJhVEE1R1JUMUYwdUJuektSQ1VzdzdQK2lodlN5TTRwTXFBazMveFRQeE9RMFp2bXJrNWMwVHpYTVJYckhlQlF4MTNkaFd3MTFkVzdrWlcxTzZiNUc3T2hCWmZkQXI2cnU5emxLTUVNOWJiSjJ2aTZMOGJlbVIwNENsN2FZRDFaa0hnOUo5ZGFlYS95dzVIUDFIdXJQUG5iaU0vd3Fudk1tVXhZcldWQk1NVllLdVo0TmllVFVVQUNMYUV2a0UreDhoekMyTE5qUjVCZGVOTHFpNUlzMytGeTFiQmNsdzhBRGZOaThlY2pzczZlZzJhTXdZRmtZSGFyQmVDU2MrS1daSXdYK2RpSS9obFdKekZMbGJIYVIramx6UTlyQVpVdC9NYzdZbTZ0SDZBQ1ZCTHZ4Zk1mTHVmTVhlTm9nUjFGOWlRVzdWT3lQQVRnbDJKZU1YT0RXK1JiUHVudjJBN1Vtb2dTZmVma2wzSVhQbXJpSHJnVXA4RTFrbnpCVVUyTmUrOGwrWS8xaXBIRHdwZndQTG5XRnpkOFlRNGF3WXpIUHM1SzZkaS9GMVJwU0NEb2dOM29nYTIyVFVxQmJrWmxMbVI3R2J6dEp4MmZvQkYiLCJtYWMiOiJmMTQ2ODYyZTA2YTFjNzk0ZDhlZjAwZDUyYTI5NjVlNmJkZDYwNzJhZDhiZWFlZWRhZDY3Y2RjY2VjZjE3NThjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZuNWJINkNOSmVLekdRZGwwMFdieUE9PSIsInZhbHVlIjoiNnUzTXVnNytZVHBjVWJvamxQMTlvbzl5b2poZ1gweUM1VkVSa0M5enIrQitjbGF4U3ljdG5NZHlGU1NXNEFLVDJwLzNQOWRQTzRDcVJqdzVPdW1NM3pKOCs4MHlhNSttamVnUTYzNmxZVnVTZmp1eE9OTjNPcDZVVDFrRHdCQTdlZ212cEhkQ2NMQ3ZidXRlVm1sY0xnRUNYV2ZObnNRRDJtSGtyc2RRK0R2aWtUdHBXTGFybUtsVE9menFIV0xDeFFHbHM5OTlXdVlnNEdhTnFjVVpIWEJadUN6dGtrRHNxT0VXeVNJbjZXSTNOUTVhcS93RGFOQ2FGRjVmRHdLcGZ2MTN2ak9ubjRVbHhSbDdzdzJIRkorcGJKNzlsWEZqR2lvQTRkdTl3MEMzMDJvL1JmM0V0N3poMGtxVkw0eXZReXBLWmxETGtNVzhSYnVTTmpwdUlKYzg4aHVJd1ZTNmM0NW9OR3RPdWNtcUFCVnVZVk1WWlBRVkd2eWI0UVlXSldVajNZRkZHRWZYaFRDdlJoY0YxMVUzMjFZNWtlUnAwbjVzQmJWMHFBR2Nsam5BdXc3NnF6YWZ1RU5VRmlZRk9EUlI0a3RjNUNnUWkwUVM4SzJqakZoSjdPOUdjNjJJSHR5NDEvdHhjb0hORkR4WWNVOG5sNUZEVDM0aFVVOUEiLCJtYWMiOiJlNmRlOTlhMjc1ZTk3Y2UxNzE4NDY4NDNlN2ZhMjFhZGY0MjE0ZDEzNmJjMmNlMTRhZGM5MWZmNzdjNmE5MmRlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300858343\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}