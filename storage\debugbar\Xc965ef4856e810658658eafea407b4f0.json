{"__meta": {"id": "Xc965ef4856e810658658eafea407b4f0", "datetime": "2025-06-28 16:01:26", "utime": **********.229934, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126485.687081, "end": **********.229948, "duration": 0.5428669452667236, "duration_str": "543ms", "measures": [{"label": "Booting", "start": 1751126485.687081, "relative_start": 0, "end": **********.161351, "relative_end": **********.161351, "duration": 0.4742698669433594, "duration_str": "474ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.161362, "relative_start": 0.47428083419799805, "end": **********.22995, "relative_end": 1.9073486328125e-06, "duration": 0.0685880184173584, "duration_str": "68.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45846616, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0056, "accumulated_duration_str": "5.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.204796, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 36.964}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.215689, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 36.964, "width_percent": 10.179}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;%", "%&lt;div class=&quot;product-stock&quot; id=&quot;%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.218595, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 47.143, "width_percent": 52.857}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-598367897 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-598367897\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-81340903 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-81340903\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-417952510 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"31 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-417952510\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1938153488 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">64</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZnd1V0UEo4R2RQU09kVkFEMWJManc9PSIsInZhbHVlIjoiN3d6dUlWMzJPeEk1TldkR0dWMit5ZTRabVVZOG53bGdOdTBxUW9YZE1oazIyZTU1Y3JFd1oveUNEUkJkanJjY0tvNDlYM1JkVjl2WlRvWjFCZnlTRWxaSWw4NTRETUJaTXR1UGR6c0hENXZuVDdsMGYrbFFBS2ZvY2RuNTVEcTRQKzlRU1F5S0pKc0krM1F0STZxOGxUUCtvOGJFREpPUkFUeitnMjRmTytTRUYxYWVqNHR3RXRRMEExT0hTSWc0Lytuajl2eHhUVFkwSUdUakU4WiszMW5NZ2dlRUhhamNmYytpaGZvcG9mdnVnUTM3Sk56NWpjU1NZQWdsaGFJSEgzeGNqdFlFYzJkQWN0cWpmbVUyZmU0anNQZHBtaVhPTzFmdlErOE16VitWUHhqWC82cEttc3BNc1llbFZXcFNVK2ZkLzQ5cm1SV2tqTmM2bTNWTmYyaHFnVHNUdXhqV3RsWDczMjBlUDBsMnRtYVArS0Q2MHVLZ21PekRvWXZWczdTMjBiWDRNTzRPdVhrd0YvR3lhNlQ1M0VBMHU0MzN4dCs1YW9pWFhvakJjUTRVK2xGRVBiVkVNU0FBQ2VUSHBGNVNmQlYva3RZM2hCS3BxMy9peDRGbExOcHUrNlh0ODAxMXREaXEvUWdlUjRiN1J4N3FmVlBEanhEWjZVbnMiLCJtYWMiOiJhNjIyMmZlYjEyY2RhOTcwZjJiNWNmMjBjNDJkNzQ2ZjhkZWQ5Nzg5NzcwYWM5ODdmMTliNGJlZmZkMjQzMWJjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im92VWxNVFJlUVc0ZFdPQ0J1UHg2bEE9PSIsInZhbHVlIjoiQmdENlFHaml3ZWlPckFmZ2hraWlNdlRwTVdIanJueDFLQllaMVB0bUVyOGQrVWdFdXcyN1MwUm9HeFZNd3Z5a1FTdXZwWnh5Q25MSVFxUHc2TWl6NmtxV2JWQlRuWGpRRmNpejZERW1tU0Z4UVp1NUVyMHFDaTIrdi9namZHK3YwN3YwU2U3NTA0aCtXYzNxMnJBSFdFbmdzQjA3Ti9vdXVUV0RvQ0JxRS9RaGxaTnNzZGo2ZmhXSTZpYjdKc2dxdUl0MWJYRmVxTFVWSzE5Vkd3ZVZ6UWN4aFVUNDNGRGdQY3FUK0R2c0pCcDhmOW43d3hLUVlwK1UxbGZxc2VBVEZDQnlmNkM3RXdOSUg0S0NHdUdpMEhJMkt0TUxESmNuOGhKYVZ4bVVOT0k1dEZ5UmNwTVBtTVdPbnYrTTZ0REU1MVVNK3R6WVhQV2ZHMGNKdCthemhTTHJ1b1REbUVZcnkweWRsUDRlWkd5WE8xVHNxdTVpY1FxczdrSWZBM081ZUVIRWl6Y0JaNWU1RExjcEdmeGtHeHAwY2R3bGYxVG5EMTh3ZER1cHBRQ0k0TXQ5VmRuelZuQTFxam52bG5kZVc4MVk5QW54eTBqOWV3MXVNeklWOFBlQ01QOW9Way9rdFY5MnJ1Yk9RTGZhOWM1b1QrQjEzREZQVGZOWUlROCsiLCJtYWMiOiJhZjAwNGY5ZmZjNThhNDMzNmIzZjRmYTFlNmYzNjgwZjA5ZDRkOWM5ZGQ1YTdlNTg3MmI1NjZiMGQzNjlhNzNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938153488\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1550377707 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1550377707\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1494788795 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdOT1hNVVF5NzBoV2FFZ2xKUFJPVkE9PSIsInZhbHVlIjoiT05ST042N0dQWFpBbGkrNmV1U2R0L3RpSGNMODhFYVNTMHZQL0t5dEtXMFNsM0NNcEJIMWNMOC9COUxWZkUyY1Z4UzJnUk82TGEyZm5sTURVVE1WZVYyeENlc1B5emhYRks4T2p3NFh0RUtWV09RdGYwVDRvZDBvTzRGcS82akp2R3BTTGpTNmMraEZ5eW1LVXdDWTcxK2c0di9oRkt6YmtnWnRhQ3R0REkvUjg2cndYL1FvcFhSNWFueHVEYzMxbHRkRFhFQ3pKYWFZanh3Z21lNVBFVEpBQjg1YnpaQVdtU1FnVU82OGZSTStiQldBUUNwNm8wNCsxS2w3TkxDQW1ZM0RRRU85VmFyTHE3dWtNcGFsSUR3SjR5Zjd0MVVBcnhuT0YwcUxpZGFTVi8wYzcvek5YY2xsY1VVU2pjcmcxK0NvanEzeEtTc2RCRWo4NnVOUXN0Zis2RC9oSHpoUFArejR4QWlsZ2x1Z0pEOG9HSXZlbHFtT3o4MCttSURxdnFUZzV4VzdwbkhOVnkwVE0vdjlFY2dQSTVpN2p3N1FUOE95RUlpQTR4QXoydVZ1SVJEY01xdUpSTm1CRFZkcTdhM1ZGZUdHQUJvVWRYZHRrc2dUT1kxWStWbmR5dWJPRGkvbzcyVkljakhtNmJDcTV6Z21DcXhLK1N6c05MemQiLCJtYWMiOiI2YzM1Yjc5OTAzZTFkMmU2NTI2MmUxM2Y1N2JkNTkwMGQ5ZTA3YjNhZmE2NDA3Y2NkMzViOTQ2MDYwNjM4MGEzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilp2a2dFSmV5dHNhcmxpVm8waXJQUUE9PSIsInZhbHVlIjoidWNmRjBSVSt3SCtQTzBkVE1UTFRYSlJPcHk2bG9DUXo4RHRQaEkyVlRTdGhCVjdKK1Q1MFh4b1cvYS9QaWZESnArZTBBU0lCNy9TLy8yQXdnV05hRzY5STlmSCtsR2hqVTYrcEFUSmxndjZwLzg3RkYyODYyMWF3TnJkdVhscXRsMjRGc3cwMnpRMUVJTElQbVVscC9XN3NWQkk5cU1qTVJxdURQNDk0S3Awb1VCajFveFJUeGt1WFlRUkRzdGp6T0FzUzg5QThLSE9MSnd1bk05eSs0MDhTdGdVUVN3Tk5Ma0R0QUpJQjFBS000TDlhWm84cGJSR2FVR0MrZXl1eFRRK092N0hQL25UV0tHV1FZc3B3TnJOaG9QWnlHTzJjQXFkZFE5d2s1NzI5THUxN1VTTnFCUzc0RWpvMjB3ekZnN0tzYXhWUjQ4KzIrbnc2U25RRHpTbUx3bVk0NTRHa21KaEVQTzlTYlBwVTkrbGxqeTRVQS90ZXF2eVAzbVBFMVY4cm1rT2E0YXgyVTFsMDkydkVoQWpKU0E3a0hIZGxZMDAwenNTblpKQnpPNCs0QzRrWGFLQXJFN3Ywd2F5N1ExMC9MdThUMUJCc0tZWHBwREdrVjlhUUlabnBXL1JyeVUxMjFDSHI0K3JvQW1EUGdyWlAxNVNuMVpTYTV0blkiLCJtYWMiOiI5OGFkMzlhNTQ2YjMzMWYwNjU1ZGJjZThiMDUwN2E5ZjJlMGE5YmVmOWM0NDkyZGRhYmZlOWFlYTdhNzhkZTViIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdOT1hNVVF5NzBoV2FFZ2xKUFJPVkE9PSIsInZhbHVlIjoiT05ST042N0dQWFpBbGkrNmV1U2R0L3RpSGNMODhFYVNTMHZQL0t5dEtXMFNsM0NNcEJIMWNMOC9COUxWZkUyY1Z4UzJnUk82TGEyZm5sTURVVE1WZVYyeENlc1B5emhYRks4T2p3NFh0RUtWV09RdGYwVDRvZDBvTzRGcS82akp2R3BTTGpTNmMraEZ5eW1LVXdDWTcxK2c0di9oRkt6YmtnWnRhQ3R0REkvUjg2cndYL1FvcFhSNWFueHVEYzMxbHRkRFhFQ3pKYWFZanh3Z21lNVBFVEpBQjg1YnpaQVdtU1FnVU82OGZSTStiQldBUUNwNm8wNCsxS2w3TkxDQW1ZM0RRRU85VmFyTHE3dWtNcGFsSUR3SjR5Zjd0MVVBcnhuT0YwcUxpZGFTVi8wYzcvek5YY2xsY1VVU2pjcmcxK0NvanEzeEtTc2RCRWo4NnVOUXN0Zis2RC9oSHpoUFArejR4QWlsZ2x1Z0pEOG9HSXZlbHFtT3o4MCttSURxdnFUZzV4VzdwbkhOVnkwVE0vdjlFY2dQSTVpN2p3N1FUOE95RUlpQTR4QXoydVZ1SVJEY01xdUpSTm1CRFZkcTdhM1ZGZUdHQUJvVWRYZHRrc2dUT1kxWStWbmR5dWJPRGkvbzcyVkljakhtNmJDcTV6Z21DcXhLK1N6c05MemQiLCJtYWMiOiI2YzM1Yjc5OTAzZTFkMmU2NTI2MmUxM2Y1N2JkNTkwMGQ5ZTA3YjNhZmE2NDA3Y2NkMzViOTQ2MDYwNjM4MGEzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilp2a2dFSmV5dHNhcmxpVm8waXJQUUE9PSIsInZhbHVlIjoidWNmRjBSVSt3SCtQTzBkVE1UTFRYSlJPcHk2bG9DUXo4RHRQaEkyVlRTdGhCVjdKK1Q1MFh4b1cvYS9QaWZESnArZTBBU0lCNy9TLy8yQXdnV05hRzY5STlmSCtsR2hqVTYrcEFUSmxndjZwLzg3RkYyODYyMWF3TnJkdVhscXRsMjRGc3cwMnpRMUVJTElQbVVscC9XN3NWQkk5cU1qTVJxdURQNDk0S3Awb1VCajFveFJUeGt1WFlRUkRzdGp6T0FzUzg5QThLSE9MSnd1bk05eSs0MDhTdGdVUVN3Tk5Ma0R0QUpJQjFBS000TDlhWm84cGJSR2FVR0MrZXl1eFRRK092N0hQL25UV0tHV1FZc3B3TnJOaG9QWnlHTzJjQXFkZFE5d2s1NzI5THUxN1VTTnFCUzc0RWpvMjB3ekZnN0tzYXhWUjQ4KzIrbnc2U25RRHpTbUx3bVk0NTRHa21KaEVQTzlTYlBwVTkrbGxqeTRVQS90ZXF2eVAzbVBFMVY4cm1rT2E0YXgyVTFsMDkydkVoQWpKU0E3a0hIZGxZMDAwenNTblpKQnpPNCs0QzRrWGFLQXJFN3Ywd2F5N1ExMC9MdThUMUJCc0tZWHBwREdrVjlhUUlabnBXL1JyeVUxMjFDSHI0K3JvQW1EUGdyWlAxNVNuMVpTYTV0blkiLCJtYWMiOiI5OGFkMzlhNTQ2YjMzMWYwNjU1ZGJjZThiMDUwN2E5ZjJlMGE5YmVmOWM0NDkyZGRhYmZlOWFlYTdhNzhkZTViIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1494788795\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1066989102 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066989102\", {\"maxDepth\":0})</script>\n"}}