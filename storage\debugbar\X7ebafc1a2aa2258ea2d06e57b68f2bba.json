{"__meta": {"id": "X7ebafc1a2aa2258ea2d06e57b68f2bba", "datetime": "2025-06-28 16:35:08", "utime": **********.989775, "method": "GET", "uri": "/enhanced-pos/get-next-invoice-number", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.545897, "end": **********.989792, "duration": 0.4438951015472412, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.545897, "relative_start": 0, "end": **********.931679, "relative_end": **********.931679, "duration": 0.38578200340270996, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.931691, "relative_start": 0.38579392433166504, "end": **********.989793, "relative_end": 9.5367431640625e-07, "duration": 0.05810213088989258, "duration_str": "58.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46464448, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-next-invoice-number", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getNextInvoiceNumber", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_next_invoice_number", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2458\" onclick=\"\">app/Http/Controllers/PosController.php:2458-2473</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025, "accumulated_duration_str": "2.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.971017, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.8}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.980877, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.8, "width_percent": 13.2}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2448}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2461}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.983161, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2448", "source": "app/Http/Controllers/PosController.php:2448", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2448", "ajax": false, "filename": "PosController.php", "line": "2448"}, "connection": "kdmkjkqknb", "start_percent": 86, "width_percent": 14}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1466/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-next-invoice-number", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1149787060 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1149787060\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128497517%7C54%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik4wbVhtbGVTM0RRSWZZa05scUtndmc9PSIsInZhbHVlIjoiSWpVUEROTTZDSko2QVJMaVJoVlVHNExZR2U5MFR2eXE0WWFPOW0vdE5lREZMNkxiQ1M1clRXQ1dnaU0vUXJXNHhPbDAzMmtHMTFCTDUwNHY2TEp4M2dPSVl3Ym8wNlVIemZGbDZ5dnNiUFU3cDJxT3c1MS9JbnVCNnhqWi9qSCtPVXZFS3JLelhvSHRoTTY2K1pZTnB3MDdBMUNNWVF4Mk9lYjBCQTlkMHBJSzlZbDNtTCtCeUFqc3NNdmJvSEF0aERJQmxRTHZwL2JRaEdGNTFsZFpZRXNoZXRMRGhVZlNUQlRsUGF3NVU0Mmo3UXhNMjhSQ3VOMWNCNWsyN2k5RnFOSXNSTWNMMkd4dDJFR0h0N0k1VXRSWDE5ekJrZDV4dHVrVGFTMlFscHBmb1Bqa2M4RHlIdHQ0RktZN0s0K0NLQk5pRm5iL09GNk8zMnI3LzJBMGJobmlGRkVkZWd0U3I2RXdjK1EramNuWjBHMDBnM0lpUW5menpPUlUzdGNUSU9Pek5VRVM0R3NRaVFYdWRWekVkUWpzdndETTN2N3FuS1NKZi9ON0R0NDlld2V2bWVqdjVTYXFmcTR0dUZSSGFZM0FkcmJ4T1VTTktPNGVuUDBodFBidFN0Sld5KzZ0VkxacmNocUZoa1BXM01aV3BuU0tsSm5RZlkrSEtUdkoiLCJtYWMiOiI5NmQ3OWE1OTQ5ZjVlZjRhMTUwOWM1ZGM3ODNhZDY5MTM5NmMxOTY2NTBmNDgwNzg4ZmI4MWU0YzliM2QxOTlhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlF5N25ZT0tmTWcycm5JcGJKNllpc3c9PSIsInZhbHVlIjoiaHFlOTBVK01hU0d4aDFaVjJmaVZNbG55U0psZXRYTGFJSUJkaU9GN2xqUXRuOHZrVHNybVpFWVFHY0tMaTQxU3pxeWhnTk0rWDdqSDhPNEZ6STZhaWhJbHRiN0FMcy9XVW5NTVZWRmJmUFdQQm1GMnpJK2x5YXo1RGNGRUNlN0p1Q0R5cWhCajdpbHRrVXZhR2E4MnU4RFBZeDM4TWYvNFRwZEE2Z0UvZG1rZjdScms5bk8vZ0pHSkxGcjFoZXMrdXIzMkNqZkU5bHhQb2xqcHUvbnNlbTVXYytFWVI3QmtNY0NFVHdxL3pNNUJKZGdGTHZlRHBIdDA4L09vcXVHV04xUXdaaDVjN2FKcWY1d3Y3d0tzN1JqM0lSREM0TE1rTXY1bW0xdkRnZlVFYkNkTDFadWlqOFhra3dYbkx5Z3dYQVVIc3hSR3RTaytVZXNYRkVXdWxjcFFmZWVGMG05SmcwVDQwRjdmL0NxUlBhZEhJYnl3Z1QrekZOQUNLRUQwME13MEdDUFV5aTN6cGozWHdsM1RiSjNDaGQrcUdXNGZPNnNYemhmcXBpdDMrNEdwMmE2dHF4MjFyUmVGUzh0QjhEeGoyTHlkd2N5YUdVUjdBNkxGV0NFU0t1aG16emQ4b3VwMHVMYnFCdEdJUzlDY2dBZ0tWdnpjeHBYUUhnYlQiLCJtYWMiOiI0MjIxYmNhM2Q1NjE4ODVkNzdkN2UxNTgyMGYzN2U3ZmIyYzRhNzRhNDk4ZDQ0N2EyMWQ5ZTUzMDhlYzQzYjYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1020126872 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020126872\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1321356090 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:35:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imo1Nmt6aG9iVDJFd3Y1N0FUNnVmQmc9PSIsInZhbHVlIjoidm9nOWNMMENFWTh1RVF5ZjVWUDYyWFkvMHNUd2JjcFlweXdFZk5CNUl1SFlka1NZeVNUcFpzRmhWWklhYzQxb1l6dE1jRVFhQlF4Ym5KV1pSbExSalRwRllhRmpGNU0vK0NqT1lVMVhCSC9uWW1scUxvT2t2OGp2aHVQUjBGMU1PM3NKV21ZMGMxSHNuTFlieDdodTRLM0pwMDdwejVaRERxcm5VSW9jeXlxbXU2VU0xZmlGcU9uUUJhYnhpMEY4MllnNWN5c2kvbEYrOW1uRyszeWdIZTBYanJvVFA5cldmL1lyaS9jRUg0aVRJSUdPQjFmZ2lrMUFNSjROWXRUWFhvTGZsQzNRMUhkc1ZOUFZGcWZ5NG9aQVlwWm9xZEdlM04vbVl3OWNpeXlHdW9VQ0c3Smx4UTV0dktRQStIV1hTYUVKMVRkQ2E1dW5NbXNUbjFaSmNvNk5Td2tHUlR5YlNuRVdrYjR0RXdNWTRKbmVLSy81NFRpZzlRUERvbTdLdkhzL0tBNm1ZYzM1Z01zeklQRFlXTWg1OGNHK2RvMUdtZm95aTJIUFJCc2tITWVmWmV2YmpoVlZVajZ6ZjVxUk9WUnkyN0lRMFFTZzdQZk9tZVQ0ZUExRUJQUlhHbnlQUGJ1YXZBdktSV1grb2JNWHlCV0t6NWdkZ2hrVDZIRVMiLCJtYWMiOiIzY2NlYjg1YThhNjk4MzliOGU1MmE5YTkzODcxNTVjMmY0NWVjYWFjZmZlYWFmMzg0Y2M0YWUyODJjMTUyMjYwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InVaRDl0VHcrU2N1N0Nhd1dsZkVMRUE9PSIsInZhbHVlIjoiQXRjQll5MkxxWEtnazJHL25ubGN3TDk5cTBDSDlMSkVqbEw0K1BmYWtGRU1VWGt5TmRlWmtCZG1ReUY2N05XOW1kRlZUL3VQeTVqdXVvRVVqV1FHWTlRRWhuRlYyeFFhRnpucDJwbEJ0UGU1ZWtLTzBvVmNLeG5Ob0ZBNkdlR3dMRXQrOWptcmhwd2ZxNDhJbnRUQ2gycEdaOHdHQlVXMUFBYWo1SGllYmxvbk9seVhRMnQwdWw1akU5a1duRE5EbWdrcDJGL3BTWWFSVVVpUmxSL3pZRHh3QjNMckhmem9SamN4SGNiZEovVnBtZ2NROStObWtSbC9JWHpMaW1VL1owbXdBL2NtSzlpazI4Uy9aQmJOckxDYllXRW5USk1QMDdXOWVBVk00ZDREbVVuMDhlRTdGMHlmMUhtUFdsbm1xL1J1V1MrbTFDWjh0K1kyUzV3WDRXNzl4aUhSaVhLaW52UHFwSXdlNmx4bHZmVXY0djJIbEhWL2gxQWJwLzVJWkRDMXM5NHNqazRQVSs3QnZlUEZrcHZEdU9aaG1UcWdhbXdocTBpWmVrY1R3MWNkY2t3TFBtZzRMN1dKbllmNUtyaUx5ajVLTTdjaCtFY2VxdWRrUDY0N0s0RytiME5kT2xZSU94aWNjSU1iYkpuT2dOS244SG1kK1dyekxPaTYiLCJtYWMiOiI2ODk0ZmU1ZjM2NDg4MTg4MWMwMzFiYTM4NDM3OWNiZTExMWJjYjRjNTc3ZmE4YTNlMjE4YTNlZjBjZmI5MTRlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imo1Nmt6aG9iVDJFd3Y1N0FUNnVmQmc9PSIsInZhbHVlIjoidm9nOWNMMENFWTh1RVF5ZjVWUDYyWFkvMHNUd2JjcFlweXdFZk5CNUl1SFlka1NZeVNUcFpzRmhWWklhYzQxb1l6dE1jRVFhQlF4Ym5KV1pSbExSalRwRllhRmpGNU0vK0NqT1lVMVhCSC9uWW1scUxvT2t2OGp2aHVQUjBGMU1PM3NKV21ZMGMxSHNuTFlieDdodTRLM0pwMDdwejVaRERxcm5VSW9jeXlxbXU2VU0xZmlGcU9uUUJhYnhpMEY4MllnNWN5c2kvbEYrOW1uRyszeWdIZTBYanJvVFA5cldmL1lyaS9jRUg0aVRJSUdPQjFmZ2lrMUFNSjROWXRUWFhvTGZsQzNRMUhkc1ZOUFZGcWZ5NG9aQVlwWm9xZEdlM04vbVl3OWNpeXlHdW9VQ0c3Smx4UTV0dktRQStIV1hTYUVKMVRkQ2E1dW5NbXNUbjFaSmNvNk5Td2tHUlR5YlNuRVdrYjR0RXdNWTRKbmVLSy81NFRpZzlRUERvbTdLdkhzL0tBNm1ZYzM1Z01zeklQRFlXTWg1OGNHK2RvMUdtZm95aTJIUFJCc2tITWVmWmV2YmpoVlZVajZ6ZjVxUk9WUnkyN0lRMFFTZzdQZk9tZVQ0ZUExRUJQUlhHbnlQUGJ1YXZBdktSV1grb2JNWHlCV0t6NWdkZ2hrVDZIRVMiLCJtYWMiOiIzY2NlYjg1YThhNjk4MzliOGU1MmE5YTkzODcxNTVjMmY0NWVjYWFjZmZlYWFmMzg0Y2M0YWUyODJjMTUyMjYwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InVaRDl0VHcrU2N1N0Nhd1dsZkVMRUE9PSIsInZhbHVlIjoiQXRjQll5MkxxWEtnazJHL25ubGN3TDk5cTBDSDlMSkVqbEw0K1BmYWtGRU1VWGt5TmRlWmtCZG1ReUY2N05XOW1kRlZUL3VQeTVqdXVvRVVqV1FHWTlRRWhuRlYyeFFhRnpucDJwbEJ0UGU1ZWtLTzBvVmNLeG5Ob0ZBNkdlR3dMRXQrOWptcmhwd2ZxNDhJbnRUQ2gycEdaOHdHQlVXMUFBYWo1SGllYmxvbk9seVhRMnQwdWw1akU5a1duRE5EbWdrcDJGL3BTWWFSVVVpUmxSL3pZRHh3QjNMckhmem9SamN4SGNiZEovVnBtZ2NROStObWtSbC9JWHpMaW1VL1owbXdBL2NtSzlpazI4Uy9aQmJOckxDYllXRW5USk1QMDdXOWVBVk00ZDREbVVuMDhlRTdGMHlmMUhtUFdsbm1xL1J1V1MrbTFDWjh0K1kyUzV3WDRXNzl4aUhSaVhLaW52UHFwSXdlNmx4bHZmVXY0djJIbEhWL2gxQWJwLzVJWkRDMXM5NHNqazRQVSs3QnZlUEZrcHZEdU9aaG1UcWdhbXdocTBpWmVrY1R3MWNkY2t3TFBtZzRMN1dKbllmNUtyaUx5ajVLTTdjaCtFY2VxdWRrUDY0N0s0RytiME5kT2xZSU94aWNjSU1iYkpuT2dOS244SG1kK1dyekxPaTYiLCJtYWMiOiI2ODk0ZmU1ZjM2NDg4MTg4MWMwMzFiYTM4NDM3OWNiZTExMWJjYjRjNTc3ZmE4YTNlMjE4YTNlZjBjZmI5MTRlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1321356090\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1852527639 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1466/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852527639\", {\"maxDepth\":0})</script>\n"}}