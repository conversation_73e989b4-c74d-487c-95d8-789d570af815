{"__meta": {"id": "X6a03dd70f0a7e51d045e306a192f4165", "datetime": "2025-06-28 16:10:09", "utime": **********.170517, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127008.766168, "end": **********.17053, "duration": 0.4043619632720947, "duration_str": "404ms", "measures": [{"label": "Booting", "start": 1751127008.766168, "relative_start": 0, "end": **********.11935, "relative_end": **********.11935, "duration": 0.3531818389892578, "duration_str": "353ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.119364, "relative_start": 0.3531959056854248, "end": **********.170532, "relative_end": 1.9073486328125e-06, "duration": 0.051167964935302734, "duration_str": "51.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45315856, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026899999999999997, "accumulated_duration_str": "2.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.150246, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 74.721}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.160846, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 74.721, "width_percent": 12.639}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.163318, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 87.361, "width_percent": 12.639}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2306 => array:9 [\n    \"name\" => \"ذرة بريتز\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"2306\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 51\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1450783579 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1450783579\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-773962257 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126882941%7C30%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlE1MFhGbTF5d0VzeHFodGtwNjBraEE9PSIsInZhbHVlIjoidE0rOVFZMDVFZTFjMFNDenFTTlN6eGNaN2ZsaGNmL0RRSmxNZmorNHJ4WU42alRPVG90RGtZNFdTa2dGUTlheHJpbWM3bytWUkJzY2haR1JHd0wyNnFHd2N4RUNzOTBtMWVpczN4c2MvTERKQUNsNjk4VWxFSEtPSmpWd2k1QlBXRzY5d3lPYWR4TGZwK3dtd21Lbzl2eDZ4NEhJK0gwamtCUExuOEpScXoxMGRZbGI3L0o5UlFYcjNyY1RJVGdJdFlBTW5ybm95cmRzZWZmMzdVa2FTUTlpdW50Z1ZTWnBDQnV0OU8zMjd1ZXJqSXRzNGhVU2JxTnVTa09qckhPZ1hUUzVUQ0R0emlKMG0zekdjTXVRWVhvUnRwYnowNzlLenRlcThoWE13Q2E4RlJnamhQbEt6RkNEc0o0Nkhpd0dlTXN1NU44VXBZY3N6MjVMS0dGeDMvc1V6UGM4d0F3WklSeVpPNkpOQkNBaHR6eUtTd3lmMGNPQ3greU9QQmNrdDk2ZmJ4RFJuU2k0dGRWY3hBbzBpQngvUEFLVU1NSXBITCtCQ3UrVlNwYk5CNDhndUtmSWhudnVHRGdIQmZac0xDVHVRZWllUm9LTkJSNHVkRWo0WGxqK1l6QUp3QkU2NXd1alM4Uk55enQ0Z0NOcUlmbDlBditnUUJ3TytMd2UiLCJtYWMiOiJhMmI3YmZiNmFhOWNjYTdjNTU4YWJhZjllYThmMjZmNjdmMjlmZmQwM2I1ZmRiZmQ4ZTVkM2ExMTBkYTg0YjRiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVBV3VYWnczYVNDTnE0S3d1YUxhYXc9PSIsInZhbHVlIjoiVmVyYVhnYnMxa04rTEVOZ2tZanJJcVJSR1JnTmtSRFQ0WkFYNThEZHFFc203bm0yNk1BcUhrRDA5bURXUFk5OCtick5pTXIzVVZUcFVMa2RTT1JhbXRGbzlSOFFZczhhV2d1Y3h1MnZxZHBXTGgrR2FBcG5Ma01GbllpNFdsMjUxZXkvaFkxWjQ3MGdmQjBZT1FkR082TXlnK3RReGlDSzFFeHhtN1FOSHF6TGMxZTZBdVF0ek54SHFlOHBLNlFoeUdpL2xnME1OQkk3VSt4V1NncnV6dDdYaXpxNDRGZVBvU2h3cVFrdGhhNFJ3NlpRMTlOTUdJYzdnVG5ybDMvaXRIQitQcFF0MXZxNnN1NWNFdjd1VmE1V3dCbWxuM3hKU2VkRGV5VzFmc0dabWlSajBzZm83YnUvK3o3UVhXTDZtb1pYSit4YmtlVzZldk1NeVlxRTkrQlM4RXRqRVVtMFd2NVdVOWhvdFM3ZldlTmd5ZnMrMFhrR25Sd1BsQnB5UE9DRDFudnpDUjN2OXRjRzE3UUZ6a0JXS1pkOS9FazdqYzc5STI3R3EzWEVRL3c2VUQyN25FdWVUQnRoVVF0R2x4Rms4N2dVemdsUW5meUt6RzY3RWp4cytFeUpXWmN2NzJaQUhCRzQ3M0JwelZ4aklJM05GcHlXRjBUeFlqWDgiLCJtYWMiOiJjNzk2MmVhYjEzNGJjOGU3YWUxMGVkN2M1YWUyNmUwYzY3MmM5NWZhMTE0YjQ3N2VjYWQ0MWM2Nzk4MWVmOGUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-773962257\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2136713771 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2136713771\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2120590085 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:10:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imh3Tkt4bmFrNzR1WWFBdXhuV0lzdlE9PSIsInZhbHVlIjoiNWx0L3p4QnQ3R3luMnRZSU9SWXZhOXByYThpRjhEZ29rYjU2ZlBaaDdrWWVreGJsS1llaTFKZG1hcEJqQXZUUFRQZEFHdXNCNzZHenZ5dnRJYzFaeU9CNzhQTXZVS1JydnBtaUsrajFSVTl1cDhnRlhZYWt3d0N1dndRYlpVdFJYZUladk5ncVk5MmN1aHR2TmU4OE5yK0s1SUlPMFk2NzROdkdqV2VrODBkSXNmMENPdVFRU0VBWEVyNTN6SWdYQXRxS0VtTTZ5MmNSTzR0cnhyeXdBKzRWd2JTbUhpZzkxbjVJb2o0ZnNITHRDNkZyTTl4WDZXS1hHQ1dHdWIrQXdJZmZHUVBvVGpvaXRNTnBGaHhVSlVnd3FMUEhmeDZISHBobllIZTU1QU5TUHY0MjBBSlE1aUlCTlVDcjNYM1NTYmZwNDBhcTIzZi9uUjVTajlaZENsRFVUSFlVcHE2RzhTaENWNTRsY01sT3Q0ZXRHek0yN0h3TW0zUEdqWnlESTVoMWt0eGdubjBBZysrNlVkN1ZzTjN1bFJhV2lxYlBmbkNuQ05KZmZhdFhJVndHYkFySnByTEZUSVptR0ljT3diYWVSZk53WHNyMWVsUHlJci9EZTgrMWNSSDhTcXQrUENwQ3dwOTh0MUZFQ3FuQWlVenBHR1kxU0dUSFZsQ0YiLCJtYWMiOiJlZGVkMmY0MmY5OTlhNWNhMTUzYjc2Y2ExNmZmYzVlOGJmMDJhMzU3ODE1NWRlN2M1Y2U1MDY5YjBhMjExOTYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjhVUFRGcHlTOVg4RCsxSWE4c2l0WHc9PSIsInZhbHVlIjoicXFPTDJVUm55UE9sN1ZlMkp2Rm55KzZiYzhuUHlzTUo0TmFtK0pjajB0UTdmb2tTRTZQZGRmZmFjM1V3eW4zVmlTQXd0R0FONDNrNlg3c00xTlEwWHpXOCtZT056R1pFZWQvajZFZys1U0hRdXBVRllWYjZDZ1NSWlNEbFBDK3llcERTUXppSmFrdTBQK1dXejJaOTdoQld6VVhOZXNML3h0R3VUeEo3TDNkcGVFaVFpSE9qMUZQSlRMQzVzeWxSb3doaTg2cFZpRDlzZW9HY3p2UWVHVzNsUU9GKzlxc2FOVjIzYjJva3dLS2ZsS2hLSDJWWFJCWWY5WnJad1YrOE52VmhMZ1pSMGFNOHhXTS9VQktBb0EwRzc1WWhKeThXVkp2dUVGU0V4SnNUNVpsNklmQXRJYjdiS2I2WUU5MXVHOThuQnBhTkpnbXJrTndmcXRkY3dYZ0s1c0w1ZXZoVjFUa1BWc3RkbUhaRVhUMXBqbmFac2p0MHNwb1lHQ0lzc1Y0SERCUFFtazNHZGRueXY3eWM1amdQSlRoNUhqcW9XNVJkMm1naWc2VzNNcDdqV3hRSVU1RlBFSjhDaDJ5Q1pIeDlZNjB4NTdhNmZhcjBaUUtXcXg3ekRlanUxa1RERTg4QW9NbHBxK2VweExvRFptRFAzaDJhVDRzWC94MTAiLCJtYWMiOiI4ZTM4YWZhOTdlNDhjNzBmNzgwMDJkYzg5NTZjMmQ0MDVjZmEwZmMzOGQxNWI2Zjc2ODgzYjQxOWM1ZWU0ZGYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imh3Tkt4bmFrNzR1WWFBdXhuV0lzdlE9PSIsInZhbHVlIjoiNWx0L3p4QnQ3R3luMnRZSU9SWXZhOXByYThpRjhEZ29rYjU2ZlBaaDdrWWVreGJsS1llaTFKZG1hcEJqQXZUUFRQZEFHdXNCNzZHenZ5dnRJYzFaeU9CNzhQTXZVS1JydnBtaUsrajFSVTl1cDhnRlhZYWt3d0N1dndRYlpVdFJYZUladk5ncVk5MmN1aHR2TmU4OE5yK0s1SUlPMFk2NzROdkdqV2VrODBkSXNmMENPdVFRU0VBWEVyNTN6SWdYQXRxS0VtTTZ5MmNSTzR0cnhyeXdBKzRWd2JTbUhpZzkxbjVJb2o0ZnNITHRDNkZyTTl4WDZXS1hHQ1dHdWIrQXdJZmZHUVBvVGpvaXRNTnBGaHhVSlVnd3FMUEhmeDZISHBobllIZTU1QU5TUHY0MjBBSlE1aUlCTlVDcjNYM1NTYmZwNDBhcTIzZi9uUjVTajlaZENsRFVUSFlVcHE2RzhTaENWNTRsY01sT3Q0ZXRHek0yN0h3TW0zUEdqWnlESTVoMWt0eGdubjBBZysrNlVkN1ZzTjN1bFJhV2lxYlBmbkNuQ05KZmZhdFhJVndHYkFySnByTEZUSVptR0ljT3diYWVSZk53WHNyMWVsUHlJci9EZTgrMWNSSDhTcXQrUENwQ3dwOTh0MUZFQ3FuQWlVenBHR1kxU0dUSFZsQ0YiLCJtYWMiOiJlZGVkMmY0MmY5OTlhNWNhMTUzYjc2Y2ExNmZmYzVlOGJmMDJhMzU3ODE1NWRlN2M1Y2U1MDY5YjBhMjExOTYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjhVUFRGcHlTOVg4RCsxSWE4c2l0WHc9PSIsInZhbHVlIjoicXFPTDJVUm55UE9sN1ZlMkp2Rm55KzZiYzhuUHlzTUo0TmFtK0pjajB0UTdmb2tTRTZQZGRmZmFjM1V3eW4zVmlTQXd0R0FONDNrNlg3c00xTlEwWHpXOCtZT056R1pFZWQvajZFZys1U0hRdXBVRllWYjZDZ1NSWlNEbFBDK3llcERTUXppSmFrdTBQK1dXejJaOTdoQld6VVhOZXNML3h0R3VUeEo3TDNkcGVFaVFpSE9qMUZQSlRMQzVzeWxSb3doaTg2cFZpRDlzZW9HY3p2UWVHVzNsUU9GKzlxc2FOVjIzYjJva3dLS2ZsS2hLSDJWWFJCWWY5WnJad1YrOE52VmhMZ1pSMGFNOHhXTS9VQktBb0EwRzc1WWhKeThXVkp2dUVGU0V4SnNUNVpsNklmQXRJYjdiS2I2WUU5MXVHOThuQnBhTkpnbXJrTndmcXRkY3dYZ0s1c0w1ZXZoVjFUa1BWc3RkbUhaRVhUMXBqbmFac2p0MHNwb1lHQ0lzc1Y0SERCUFFtazNHZGRueXY3eWM1amdQSlRoNUhqcW9XNVJkMm1naWc2VzNNcDdqV3hRSVU1RlBFSjhDaDJ5Q1pIeDlZNjB4NTdhNmZhcjBaUUtXcXg3ekRlanUxa1RERTg4QW9NbHBxK2VweExvRFptRFAzaDJhVDRzWC94MTAiLCJtYWMiOiI4ZTM4YWZhOTdlNDhjNzBmNzgwMDJkYzg5NTZjMmQ0MDVjZmEwZmMzOGQxNWI2Zjc2ODgzYjQxOWM1ZWU0ZGYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120590085\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2306</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1584;&#1585;&#1577; &#1576;&#1585;&#1610;&#1578;&#1586;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2306</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>51</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}