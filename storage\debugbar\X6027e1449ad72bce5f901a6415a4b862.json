{"__meta": {"id": "X6027e1449ad72bce5f901a6415a4b862", "datetime": "2025-06-28 16:21:04", "utime": **********.506975, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.101349, "end": **********.506988, "duration": 0.4056389331817627, "duration_str": "406ms", "measures": [{"label": "Booting", "start": **********.101349, "relative_start": 0, "end": **********.437488, "relative_end": **********.437488, "duration": 0.3361389636993408, "duration_str": "336ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.437497, "relative_start": 0.3361477851867676, "end": **********.50699, "relative_end": 1.9073486328125e-06, "duration": 0.06949305534362793, "duration_str": "69.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48253536, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0071, "accumulated_duration_str": "7.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.467018, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 20.282}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.476274, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 20.282, "width_percent": 5.352}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.488607, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 25.634, "width_percent": 8.169}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.490315, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 33.803, "width_percent": 5.07}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.494255, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 38.873, "width_percent": 42.535}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.499339, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 81.408, "width_percent": 18.592}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-706854802 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706854802\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.493357, "xdebug_link": null}]}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1079825492 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1079825492\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-565022786 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-565022786\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1902057206 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1902057206\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1053107062 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127660348%7C43%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpXckhpaStkSVhrL0FhS1pkQlpmNGc9PSIsInZhbHVlIjoiY0JMakc1aTB1Mk9qYVpkcUNxc08yUGNoM092N0pJay9GRGN1UmZwc2ZyVVZiSGVoaUZwRWJiQmtjaU40SlIxeHZ0ZkhDZURsT056ZEJiZ3V4L25sWk5LNFpjbnFlTGtvVU1mZEVtdThUek1lMmdGbGhHb1hoYllmYmRxQVVjS0k4bUlCUFNRYjZMamlkUkxCT3pvb3hHaitJaEN5RzVwOUNPN0lkMGl6bUhYT1E4QWY2MFRKaXh5ZXJvakJIL1YvdGZwZ3djcWVRL29KN3dOQlQ4Ykdzd0lMZ3hKZFZISHNoVkU0QzZ5Rzhpdnp0Z2hZVTViWFdCTzZaZkFjampWd2FQWk94UkhDcGJOd0UyOGVxek44NWcxVVRGUzJYOWxiWnpGQ1hmeWQvS2p3aEtpTHYyTmdkdkQxYWVyOVVQZUhHRUU2TDZWdVkyZFhkTzVlbHYyVHpUcGtMV0ZxN2F3d0svZ2l3WnpDemFiTVFTeWNLemludUhRbkJGelNNQnE3REhtZDQ3TGhPU1Q1UFZNbFFDUkc4ZXB4dDRDU1ZITU04MURFR3RJWmRGVDErZE13aXVvcEVKN1lQdFBBR3lyRGNqaXVTZzViNTkvcGc3cjRub1JudmswaHNCdFVFMjNiVVFwZFFGQUFxdUxYeFVZbkN2Vk1mSURDSWFBS2FuNDIiLCJtYWMiOiIzMGQ5YTBlZDA0ZTBkZDBkOWZjOTM0MDhmOTcyMzE0MWVjMzc3NzEyNjY5ODAxNDc5YTIwNjY3OWMwMTIwNzI3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imh0bHNha3JIVkxFMlpDWGRnZ1YwUXc9PSIsInZhbHVlIjoiTVRSVERzNVhzNjhPdVNoWVZQVXlGTmYxbWpNSVRIdmt2cE1zSkg3RUtlSjZvU25FOEs3Q1ZZUVFmWFBZRWY3TlpVdG55dEhzOEx5bFE1MERDYkFTbXoxSzExRTJ1YXRIc1Q3a2xqQU5KZXNPdTJ5Ymt6WFR4TEtIN0JnQVR5SjYwUkdVWTV5Z1JTSGNVcWxZVE0wU3luMVg1bDVDTGhnTVhKVkw0NDJwb1JHbk9qNnZlMDhUUkdVZWlnTWo4cll5SWE3YmxldnpCWWpEcVk4djZrSEIydUNldUdpZlBGaWJVWloyeW01YkEzRW1YYUZpSlZobExTUnkyeXEyU0tkSzZncjAvMGVuU3ZrUUJGZ0xlNWxhUzROVnNrM0xMQzBFZ0xUc3Z3b0x2RDFFRGV2Q3l6b2ZPaGFxcXBKS1BjZjZnZFFkTGtvMEkxcFlIbHNLdkdnWHVNZ3dmT1psa1BDQU5ZMVdQcHNFNm4yOG5BTlNxNyt1V3BPMlBVWWdUWGdmd2RoSi81NFp1dHZUbkZlWnZ0SU1QWHFVVEpLa3JNbnphT2dPQmx3SlAvY09BRVlZQzU5aEZIRklxZUdnaEdGQTFEcU4vZDU2YlVrSTlsMHZxWmRZOWM5WVpqTitGc0x6UzRFNDZ4RDhnOEJQTUQ0cUVDSVQyYnhzazRRaXRSbWwiLCJtYWMiOiIxZmYzYjgzZjY0MWY1OTRlNTcxZjFjMThhYWIxZDhkYzQxZDY4YzY4MThhNGY0ZmRkZmJhM2NhY2RhOTBmZDM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053107062\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-131262474 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131262474\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1840922561 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:21:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZvMjg4Qy9zS3RGd1R2ZFRGclhwR3c9PSIsInZhbHVlIjoiTm5Mb2lkM1pLVVZDUmpacGtyTjFhRHdVMnlIUXoyRXEzZkgvZkZXb0dtZVlidkY3ajFPRWZlZFMrVW41RmJzOEZESzBOUDhYcXBXWFlSZ2VwM3JxWXJwcnkrOTM4djk3MkZlalhKMlREb0RsL0NaaUpiNkt2dk1EaGs0WlU3YU5FMmFzZzhPNFY1ZVVGNzJLcnJjNEZlZlkrQ2E4OGZTd012T1FqOXZON1BNaFEzMjNHMm9UM2JxSEpzdkNWNm40WTUyWDhhTS9xUXJtNjduelJSYUR5U294bUxVLzRFQWUyak42V0NwcXdnQVpHNVpLeVVYTzY4UDlFb3dtaGFIT1hDY0FFcFFlUFF4UXd6cUxreGpReXJ3RE0ySmp3YzZaanhqaTRqaTg0eWxPWkMraXV1TEVMM25McGc1TjVPWXBDSm94RHA5bEZESThNMlN1VGI1ODYvOUhVN2I0MjlqUDh0ZlcyNHJQYzlMdmVMUElMeWl2SzVmNmxzWiszRGs5WDJ4aWgrbVdlb2NKbkdxT2ExOUVJR3FCS3kzMjdLcDZqSFhpSVlPbFIwRkJyVUtDL09LQTJ4aFZQMEhSYmhmSVk3aTg0TWxnN1I1MjJXZzhEa1NqMTMvTC91ZGcwN010V1FpWENSTisyUjRCWmVHMFIzbWI5MCt3dFZGR1dzSFoiLCJtYWMiOiI3ZGZhYTVlYmYyYmRhNDQ2ZjdkNDk3MGYwYjBiM2Q3ZTg3MTljMzU5NzcwZjc2M2IxM2Q2MThmMTg3YzU1MmEyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ims1UHBNUGxWdWZIQUtmQlgrZ3JRRWc9PSIsInZhbHVlIjoiK1ljQ25KWC8xdmp3VGh5clpwZll1UTdzN2ZKa21Bam1OSFYvUWt6MlArN0ZDc1hudmdTUkJ4aUdMeEw0MzAzZks3ZnhOYytqeWZqc0FDTW5qeUlCczM5WWg3SmxoWkhJbllOZDlyV25lS3FPL3VsOExLLy84Q09zOFNTdkcvUnV2SXZKNktBN3hmOEVpZFgzUUZISHBFREk4ZzBSbTZGNFBTYnNTcysraFBrdlFoWTB3ak5walFCaUh4WGY5Q1JkNWtRd1ZvZjlwd3UvNDZhdG5mQmMyL2lsSGNPbUt5WG1Gek0yTnE2Ym5jMjk3OG9pOUd5MkxLK1pQcHRRWEV6UlZ2M2RxYkNyb25DR1BpTXpIZjlZWVN4eWd6aGljNWloNWFVV09XM3ljRVF1ajRjUzFYZFdXUWNHb1BLSkVuajJ3Y0RKaUhJNUdtWWNmQzZzWXM5TTZBK0RFUUc4aGR2VGVYd3pSTmwyNVFBbHdPSG1VK3EzQy92QWtOak9uSVFUaFJ5Wm1CTzBCSGJMOEcrZ1ozRHg1RWZKVGx1Y0haU0FmdUlKMklYVndOYmRucjd3MXdMdmRBSmFBQmY0TGc1VHBpSnhnRkRUSEFXZHlWTVFxQlFFL1h6SklSOVR6aEE4UC9ZbEN1MTI3UkxmaEQ2ZEtTR2FLaG5CM0FybVR2T0ciLCJtYWMiOiJmODllMjQxOTAxNDcxZjljM2FlOTc5MzYzZWIyOTk1Mzg1ZGQxODU0NGYzMWFmMTVlM2QyNzRhMmUwZTE4ZGE1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZvMjg4Qy9zS3RGd1R2ZFRGclhwR3c9PSIsInZhbHVlIjoiTm5Mb2lkM1pLVVZDUmpacGtyTjFhRHdVMnlIUXoyRXEzZkgvZkZXb0dtZVlidkY3ajFPRWZlZFMrVW41RmJzOEZESzBOUDhYcXBXWFlSZ2VwM3JxWXJwcnkrOTM4djk3MkZlalhKMlREb0RsL0NaaUpiNkt2dk1EaGs0WlU3YU5FMmFzZzhPNFY1ZVVGNzJLcnJjNEZlZlkrQ2E4OGZTd012T1FqOXZON1BNaFEzMjNHMm9UM2JxSEpzdkNWNm40WTUyWDhhTS9xUXJtNjduelJSYUR5U294bUxVLzRFQWUyak42V0NwcXdnQVpHNVpLeVVYTzY4UDlFb3dtaGFIT1hDY0FFcFFlUFF4UXd6cUxreGpReXJ3RE0ySmp3YzZaanhqaTRqaTg0eWxPWkMraXV1TEVMM25McGc1TjVPWXBDSm94RHA5bEZESThNMlN1VGI1ODYvOUhVN2I0MjlqUDh0ZlcyNHJQYzlMdmVMUElMeWl2SzVmNmxzWiszRGs5WDJ4aWgrbVdlb2NKbkdxT2ExOUVJR3FCS3kzMjdLcDZqSFhpSVlPbFIwRkJyVUtDL09LQTJ4aFZQMEhSYmhmSVk3aTg0TWxnN1I1MjJXZzhEa1NqMTMvTC91ZGcwN010V1FpWENSTisyUjRCWmVHMFIzbWI5MCt3dFZGR1dzSFoiLCJtYWMiOiI3ZGZhYTVlYmYyYmRhNDQ2ZjdkNDk3MGYwYjBiM2Q3ZTg3MTljMzU5NzcwZjc2M2IxM2Q2MThmMTg3YzU1MmEyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ims1UHBNUGxWdWZIQUtmQlgrZ3JRRWc9PSIsInZhbHVlIjoiK1ljQ25KWC8xdmp3VGh5clpwZll1UTdzN2ZKa21Bam1OSFYvUWt6MlArN0ZDc1hudmdTUkJ4aUdMeEw0MzAzZks3ZnhOYytqeWZqc0FDTW5qeUlCczM5WWg3SmxoWkhJbllOZDlyV25lS3FPL3VsOExLLy84Q09zOFNTdkcvUnV2SXZKNktBN3hmOEVpZFgzUUZISHBFREk4ZzBSbTZGNFBTYnNTcysraFBrdlFoWTB3ak5walFCaUh4WGY5Q1JkNWtRd1ZvZjlwd3UvNDZhdG5mQmMyL2lsSGNPbUt5WG1Gek0yTnE2Ym5jMjk3OG9pOUd5MkxLK1pQcHRRWEV6UlZ2M2RxYkNyb25DR1BpTXpIZjlZWVN4eWd6aGljNWloNWFVV09XM3ljRVF1ajRjUzFYZFdXUWNHb1BLSkVuajJ3Y0RKaUhJNUdtWWNmQzZzWXM5TTZBK0RFUUc4aGR2VGVYd3pSTmwyNVFBbHdPSG1VK3EzQy92QWtOak9uSVFUaFJ5Wm1CTzBCSGJMOEcrZ1ozRHg1RWZKVGx1Y0haU0FmdUlKMklYVndOYmRucjd3MXdMdmRBSmFBQmY0TGc1VHBpSnhnRkRUSEFXZHlWTVFxQlFFL1h6SklSOVR6aEE4UC9ZbEN1MTI3UkxmaEQ2ZEtTR2FLaG5CM0FybVR2T0ciLCJtYWMiOiJmODllMjQxOTAxNDcxZjljM2FlOTc5MzYzZWIyOTk1Mzg1ZGQxODU0NGYzMWFmMTVlM2QyNzRhMmUwZTE4ZGE1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840922561\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1701768271 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701768271\", {\"maxDepth\":0})</script>\n"}}