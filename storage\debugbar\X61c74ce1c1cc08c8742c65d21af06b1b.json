{"__meta": {"id": "X61c74ce1c1cc08c8742c65d21af06b1b", "datetime": "2025-06-28 15:38:24", "utime": **********.639195, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.189117, "end": **********.639211, "duration": 0.45009398460388184, "duration_str": "450ms", "measures": [{"label": "Booting", "start": **********.189117, "relative_start": 0, "end": **********.578982, "relative_end": **********.578982, "duration": 0.3898651599884033, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.578995, "relative_start": 0.3898780345916748, "end": **********.639212, "relative_end": 9.5367431640625e-07, "duration": 0.06021690368652344, "duration_str": "60.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46375000, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2355\" onclick=\"\">app/Http/Controllers/PosController.php:2355-2389</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00241, "accumulated_duration_str": "2.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.620937, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.083}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.631823, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.083, "width_percent": 19.917}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1552817719 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1552817719\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-793597866 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-793597866\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1654413991 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1654413991\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-921792543 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751124481231%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRWOEZKR2gweXlPOGh5K2RWaTJjTmc9PSIsInZhbHVlIjoiS2JZMDFjVXVxQzNMMW9vaml3V1ZRb2JCTVI4MnFvckVaT2RrNGw4QmFkVWtjWkJ4YWRpV0Z4bmt3QThzWVVzMUNWMUZFa08rSnR2amh3MmZUVnAvYVhPdmxiVEJDWDVpTnVFUDdzY1A3OFF1WXB1WkFBam1qYklFQXRoaENnTHZTWWp0V0x4L3ZSaE9GM2szM0dxVzM2NVFUSmgxY096YXgyY1kzYWczR21aai8remJIajh6Ync2Y2xPUk1qR0x3TW8xb1p3emx4Rk51djJwTFpubkoreXlLcjVoaFVtenV6ZFVSNWxiQlhxdkhRVlhxSHFCaVNBcGtpV0tmWUUwU29CcVg2QnBPeXExaUsvY0I1ZDY4b1FZek4yTHpLZVZrbHFpSHQ0UVArdEpHNzYzd2JQeWJwZDZMeEVmaDdtRHl1TjBUM1EwUnN6N05pd0RPdGV0dkJLUGltRWF0cDVPa01ZbUxNeDFmL000YjVjejBKWFBpdmdERjdqS09Td010YTNhRlhKWU05R01BWldKWlpmWVl3Z0p0S3BCRkoxcUtMR3ZJeUdZdXphTEJZN2I1RGhhR1grajl0UTYyKzRrYUhoaEVDMUprQXhvQVljQzQ0UXU3c0ZqaFlidHVMKzdDUy8yM2ExVVlYYmxYa05oOXRlRzVlbWpzK0s4RVh2dUIiLCJtYWMiOiIxZTRkNGJkZjQzNTFjM2Y0YjFiMGE0Yjk5OTlhYWY3YmJmZDIxZDJiMzc2ZTZkNGIxODM4YzFmODk2ZTBjMDYxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdOZDRxTnE2dWJQMzU1WjlVNExmTHc9PSIsInZhbHVlIjoiYzhNVzg2N2FZNjdFYWhEZi84dzJtWFZ2Y20vdEo4M2RHWndVZjFyYUZJTFk1Rk5iVjdkWHdEdGFiQkFlN3JEVktMdUNtWFBYMHpvZCtuMW1IVmVQRTVzQ2Q2c1dsTnlqV2lrcTQ2R0htZHNVMTRGR3lET1JFckY1eUM3NXNRb2VNOFU1c3o5b0ZRNXg1ZXlkWUVtQkRUZ2Q2QzNwbzUwYm1jYisyMWUzVGNQTS91Q1dkWXFvTEIvTElzVkxCSGpmZHo2dGppRU9HWk9VdDluL2xkOGh4dW8rZzZWN1RQUkF4dlR4ajhTQnkwc1lhN1pURmpHYkVrVlkzaDBuTXhmRXkrYkg2SkcrdDVhdGkvOVpaaEg5UStaaEZCamY4QTMrUU5UQTBibFoyMHo1OWV3amhNRlVvN2dWb1YrUFllTGFPMFVPVGVsOFpaSG0rM3g4bWNBbUVUT2tsVzJnZ0x4MEU1YjVrNTJXTkU0MVFXZ1BWMGh6SFUrcjBoS1YzWk4xNDQ5bDhCNWozL2FmVXJBVTMxVC9HUlJHTmJTMmhlOGVvelh1TnQyc0dqeDMvRTRrRllmdHZLcTY2Z1M4TTdkZXFrZGxzR0pYMVdyUDRIeVBiQlNVRWNiT0s4bm1GMGNYMEh6WExrQmErUk93Sm5tQXhOQ2lPSTJoVlFqOFp0L0kiLCJtYWMiOiI2Y2VjYTZmOWUyNDMyYWIxNGY0OTJmZjEwYjViODRhZWQwMTM3Yjk3ODg2ZDY2MWIzZTViM2E0YWQzMTVkMWQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921792543\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1825325017 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825325017\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1408402190 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:38:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdSRW5hcENibUZIOXZxcSt0b2ltdHc9PSIsInZhbHVlIjoiMVRaTitGQWNESHpmbEg0SSsyaGF2NEdzbXkzRVNKRGFZa1FEcTgwU3U1L2lMQXVDR1ZyWWFTcVpBVmdhcWlKSVU4RHQ3bkxYUWkxNnlpRVR3UjJEZVNKeS9rT0VKc2JTUU5GVnpsL3NwYm5ZbkhrVEpWTEhUaG9wOHlVTjZYTGFDeXJqQWUvdUZkcVcvenB4Y3lxS21oWWt2WHo2OEpMNU1vVjVKRmhsYk8yekFHVU12cHcvbGgrcmx0a3dPdThtdXJRS2VZb1FHcE9samVsWThZdkJ2SVRlS3ltaldhS25mWXRzdGxuejB2NStHbUlpWDFKS0ZqbFlUZUxpZzN2UDlPZVgvWndKMHJKMGNGUDdtbG5Gc2dLcUtKOTd2RkU5L1IwNlAydzUvRVNxcXR2NDBBQTE0YklSdUZSNDVCQUpLcy93TFVtUHl2OVlxb2hSS2g2cHo1aHpUYkI0N0RKVHlleFVjcW0zRXpobVdZSnVROWRnOTc4R1lGOG5RSFoxK3Jad0IxMnNzQWc1cnRzWGo0QWUya0hvelMzRzFrTVM3YVErTkZvNkVFbXRnY0VOMlJEL1JxZXdRR21YWTJseHZMSWZLTTc4SjRIUGlSR042Z0dmRE9qdUZsMzJSMjl0RWhkdkltQy8xdURvelVaWnFBMUY1Q1VxUUo4QTZtaGYiLCJtYWMiOiI5M2YwMmZkOWE1N2VjMmRiZjU5NWI3NjUyMjYxZDcyN2MyOTZmNjAzNWE4NTBkYzJlYzgzOGM3ZWFkMTE2NmI2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRxcklPV3VSdzZVOFdUV1ZuQmY3TFE9PSIsInZhbHVlIjoiL2poWnpWQ09CcFJPR0NoWi9ndDdaT0w2Q2NjUk9NRUo3NWdVSDVjZDl2T25rUG0rWFhlSSs3UEFqM0VCVWJDVUNycXJWcVJIQjF4cmlOYmZjZFhxMGZMQTJZUHdDWTFpV2VKSnRDd21zK3BmNTRxZXRXMmFiTXhlTTJuYkZYN0tLd0psZUNaODlhZ1JYQ0U5SHZZemhOMTZib2d2UTR2Q3NLK2NmVnVJN3ZkZVV0YkNYSVVneFAxcGRLNUFTeXdUUzBGNml2VUo4ZFZKalVTWElBQzhLUEFEaFR1ajl0K3hYT2FPK3VoaHkya3ZZSm9OZjg5ZXJORVVBQXhwb3IyMURYOC9Wb2xFZHhtSG9lbDd2WDNJaXRPTjJBZmIzeWpOSUZyTWFJaVM3ckhZZGVPVzNnL3VNeksyWEdOMlBoZ0JxLzVhZ0NQTGJyRlBpYlVkNFhKN3NvbjA4SnBHeUNLUG04bFZuZXJ6UGxJdkttNXUrMlN2TnQ5OVJjQmMyVmxiMnhtRWhkWDNuSHRXckpic1dSRDdINGpXUjRaZjY1bmIvQVJxcTVHNDJsL2VFSGk2dUVQOTR1OGxTMlNnS2J4VlRQNW96ZGZFN1pqZFJiV1ZSZXVmOG16YjdJcFRmZlRIOWZCZWQyQ3o3OGNZR2pZR08yL1lYbHEzRVRsb3hhQ2wiLCJtYWMiOiI3YzY5ODA5YjM1ZDE2ODFlYjU0NjI2YTg5MGI2MjMwY2RlYzUyNzMzMmE4OGVkNmY0NjMwYTFmODk5N2I3ZDI4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdSRW5hcENibUZIOXZxcSt0b2ltdHc9PSIsInZhbHVlIjoiMVRaTitGQWNESHpmbEg0SSsyaGF2NEdzbXkzRVNKRGFZa1FEcTgwU3U1L2lMQXVDR1ZyWWFTcVpBVmdhcWlKSVU4RHQ3bkxYUWkxNnlpRVR3UjJEZVNKeS9rT0VKc2JTUU5GVnpsL3NwYm5ZbkhrVEpWTEhUaG9wOHlVTjZYTGFDeXJqQWUvdUZkcVcvenB4Y3lxS21oWWt2WHo2OEpMNU1vVjVKRmhsYk8yekFHVU12cHcvbGgrcmx0a3dPdThtdXJRS2VZb1FHcE9samVsWThZdkJ2SVRlS3ltaldhS25mWXRzdGxuejB2NStHbUlpWDFKS0ZqbFlUZUxpZzN2UDlPZVgvWndKMHJKMGNGUDdtbG5Gc2dLcUtKOTd2RkU5L1IwNlAydzUvRVNxcXR2NDBBQTE0YklSdUZSNDVCQUpLcy93TFVtUHl2OVlxb2hSS2g2cHo1aHpUYkI0N0RKVHlleFVjcW0zRXpobVdZSnVROWRnOTc4R1lGOG5RSFoxK3Jad0IxMnNzQWc1cnRzWGo0QWUya0hvelMzRzFrTVM3YVErTkZvNkVFbXRnY0VOMlJEL1JxZXdRR21YWTJseHZMSWZLTTc4SjRIUGlSR042Z0dmRE9qdUZsMzJSMjl0RWhkdkltQy8xdURvelVaWnFBMUY1Q1VxUUo4QTZtaGYiLCJtYWMiOiI5M2YwMmZkOWE1N2VjMmRiZjU5NWI3NjUyMjYxZDcyN2MyOTZmNjAzNWE4NTBkYzJlYzgzOGM3ZWFkMTE2NmI2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRxcklPV3VSdzZVOFdUV1ZuQmY3TFE9PSIsInZhbHVlIjoiL2poWnpWQ09CcFJPR0NoWi9ndDdaT0w2Q2NjUk9NRUo3NWdVSDVjZDl2T25rUG0rWFhlSSs3UEFqM0VCVWJDVUNycXJWcVJIQjF4cmlOYmZjZFhxMGZMQTJZUHdDWTFpV2VKSnRDd21zK3BmNTRxZXRXMmFiTXhlTTJuYkZYN0tLd0psZUNaODlhZ1JYQ0U5SHZZemhOMTZib2d2UTR2Q3NLK2NmVnVJN3ZkZVV0YkNYSVVneFAxcGRLNUFTeXdUUzBGNml2VUo4ZFZKalVTWElBQzhLUEFEaFR1ajl0K3hYT2FPK3VoaHkya3ZZSm9OZjg5ZXJORVVBQXhwb3IyMURYOC9Wb2xFZHhtSG9lbDd2WDNJaXRPTjJBZmIzeWpOSUZyTWFJaVM3ckhZZGVPVzNnL3VNeksyWEdOMlBoZ0JxLzVhZ0NQTGJyRlBpYlVkNFhKN3NvbjA4SnBHeUNLUG04bFZuZXJ6UGxJdkttNXUrMlN2TnQ5OVJjQmMyVmxiMnhtRWhkWDNuSHRXckpic1dSRDdINGpXUjRaZjY1bmIvQVJxcTVHNDJsL2VFSGk2dUVQOTR1OGxTMlNnS2J4VlRQNW96ZGZFN1pqZFJiV1ZSZXVmOG16YjdJcFRmZlRIOWZCZWQyQ3o3OGNZR2pZR08yL1lYbHEzRVRsb3hhQ2wiLCJtYWMiOiI3YzY5ODA5YjM1ZDE2ODFlYjU0NjI2YTg5MGI2MjMwY2RlYzUyNzMzMmE4OGVkNmY0NjMwYTFmODk5N2I3ZDI4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408402190\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1515213252 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515213252\", {\"maxDepth\":0})</script>\n"}}