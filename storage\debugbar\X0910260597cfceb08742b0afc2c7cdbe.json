{"__meta": {"id": "X0910260597cfceb08742b0afc2c7cdbe", "datetime": "2025-06-28 15:38:24", "utime": **********.673247, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.190118, "end": **********.673263, "duration": 0.48314499855041504, "duration_str": "483ms", "measures": [{"label": "Booting", "start": **********.190118, "relative_start": 0, "end": **********.608429, "relative_end": **********.608429, "duration": 0.41831088066101074, "duration_str": "418ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.608437, "relative_start": 0.4183189868927002, "end": **********.673265, "relative_end": 1.9073486328125e-06, "duration": 0.06482791900634766, "duration_str": "64.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45685432, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00283, "accumulated_duration_str": "2.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.640754, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.311}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.656554, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.311, "width_percent": 17.314}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.663355, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.625, "width_percent": 18.375}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1328797355 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1328797355\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1103080838 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1103080838\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-153026748 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-153026748\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1131200986 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751124481231%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRWOEZKR2gweXlPOGh5K2RWaTJjTmc9PSIsInZhbHVlIjoiS2JZMDFjVXVxQzNMMW9vaml3V1ZRb2JCTVI4MnFvckVaT2RrNGw4QmFkVWtjWkJ4YWRpV0Z4bmt3QThzWVVzMUNWMUZFa08rSnR2amh3MmZUVnAvYVhPdmxiVEJDWDVpTnVFUDdzY1A3OFF1WXB1WkFBam1qYklFQXRoaENnTHZTWWp0V0x4L3ZSaE9GM2szM0dxVzM2NVFUSmgxY096YXgyY1kzYWczR21aai8remJIajh6Ync2Y2xPUk1qR0x3TW8xb1p3emx4Rk51djJwTFpubkoreXlLcjVoaFVtenV6ZFVSNWxiQlhxdkhRVlhxSHFCaVNBcGtpV0tmWUUwU29CcVg2QnBPeXExaUsvY0I1ZDY4b1FZek4yTHpLZVZrbHFpSHQ0UVArdEpHNzYzd2JQeWJwZDZMeEVmaDdtRHl1TjBUM1EwUnN6N05pd0RPdGV0dkJLUGltRWF0cDVPa01ZbUxNeDFmL000YjVjejBKWFBpdmdERjdqS09Td010YTNhRlhKWU05R01BWldKWlpmWVl3Z0p0S3BCRkoxcUtMR3ZJeUdZdXphTEJZN2I1RGhhR1grajl0UTYyKzRrYUhoaEVDMUprQXhvQVljQzQ0UXU3c0ZqaFlidHVMKzdDUy8yM2ExVVlYYmxYa05oOXRlRzVlbWpzK0s4RVh2dUIiLCJtYWMiOiIxZTRkNGJkZjQzNTFjM2Y0YjFiMGE0Yjk5OTlhYWY3YmJmZDIxZDJiMzc2ZTZkNGIxODM4YzFmODk2ZTBjMDYxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdOZDRxTnE2dWJQMzU1WjlVNExmTHc9PSIsInZhbHVlIjoiYzhNVzg2N2FZNjdFYWhEZi84dzJtWFZ2Y20vdEo4M2RHWndVZjFyYUZJTFk1Rk5iVjdkWHdEdGFiQkFlN3JEVktMdUNtWFBYMHpvZCtuMW1IVmVQRTVzQ2Q2c1dsTnlqV2lrcTQ2R0htZHNVMTRGR3lET1JFckY1eUM3NXNRb2VNOFU1c3o5b0ZRNXg1ZXlkWUVtQkRUZ2Q2QzNwbzUwYm1jYisyMWUzVGNQTS91Q1dkWXFvTEIvTElzVkxCSGpmZHo2dGppRU9HWk9VdDluL2xkOGh4dW8rZzZWN1RQUkF4dlR4ajhTQnkwc1lhN1pURmpHYkVrVlkzaDBuTXhmRXkrYkg2SkcrdDVhdGkvOVpaaEg5UStaaEZCamY4QTMrUU5UQTBibFoyMHo1OWV3amhNRlVvN2dWb1YrUFllTGFPMFVPVGVsOFpaSG0rM3g4bWNBbUVUT2tsVzJnZ0x4MEU1YjVrNTJXTkU0MVFXZ1BWMGh6SFUrcjBoS1YzWk4xNDQ5bDhCNWozL2FmVXJBVTMxVC9HUlJHTmJTMmhlOGVvelh1TnQyc0dqeDMvRTRrRllmdHZLcTY2Z1M4TTdkZXFrZGxzR0pYMVdyUDRIeVBiQlNVRWNiT0s4bm1GMGNYMEh6WExrQmErUk93Sm5tQXhOQ2lPSTJoVlFqOFp0L0kiLCJtYWMiOiI2Y2VjYTZmOWUyNDMyYWIxNGY0OTJmZjEwYjViODRhZWQwMTM3Yjk3ODg2ZDY2MWIzZTViM2E0YWQzMTVkMWQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1131200986\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1051876297 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1051876297\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1674421558 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:38:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdiOUJSTW9zTDFkQzlmMFlpU1dwSEE9PSIsInZhbHVlIjoibkJmSXdWb2hpWnpwOVUxWEZxYTNMMmhnRllRQXlONzNXYVMrVFZlMys0eUJOYU5aWW1LQzNBSE1jaDRXRXU1VnBHT3lQVkY3cUFVS2VtTWFsdk44ZUliMTkxb3pkZVdVbFNyeHlWS1hEdTUrRTdvQzgzQzlMc2IvM3IzS1R1SGZOVmlKZXVycTlHR2hTV2tPc1FETVBqdi91ejB0RmQ5b3B1YXJDTnNQY29hMXJKR3IyUXdBRXhMc3ZXRWFzWFI1UEgvbzBVd3NTSGJJR0lSeGxMbWd2MzIwMFdkaXgzUVhjRUhXNXZzckliS2JYNUlFbkVoaEkwS1pid0EyUnhOT0hYektkcHBzTUZyc1o1WTBmdDcxdHl3ZGtSUGgzY1lwZTVkYUdFTFYwNzVwdHJYRVowQTc4S29sbnlmRk41QS9MS3dBVGVrTWNXWVRvQUtETWR3SENRdTh0ak8wSkNybThDeDVJTDByZWZJMjNMWTEvVUQzalArWGNCS0pPUmtnWms3amw0NzFpMXYvM21Cb0dLcDQ3amphQ0p0SlNNM2ZPUTdheFhaWmJWUzdzQ0tsQ1A2cVhkeHQ3SU9pZ3NoZEhYVVBHZ2ppK3VFWDBnb0g5WENyMFBNTFZVTWtFRjZCYlEvUU13dUZueEc1SHNFMmUzVlIyWWd6OFVJZytFZUMiLCJtYWMiOiJhZjNlMTZiMzY2ZDkwMDYzYTg1MzhkMjg5M2Y4NzFlOWIxNDNmOTJlMjliMzNmYWY3MWFmYjMwNDZlNzllZTZlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJDc05Na3hSMmNoRkFZZW5oNE9vWHc9PSIsInZhbHVlIjoickVKNTN4ZGE1SUx4OWNSalNrajRqUlFNNXdUKzhvN04xK0VhejltenNaQjROKzNXeXpUcmxIeEdtaFFnOWV1emlyck1JdEVpYkpHUWkvZVlvVFBhQ1JNQ3AvVU5wNUFFcWJ2VTBDYjFoUUJWS3J4ZjZwNVRTVEQ3alIwbjIvVC9ZTkU3dXZoVERLeHpOdDZ3Q081ZDFhOFNnbTVlUWhsdllsZi83Y2h1cVI0UUNZZjdidGRUZE92aTk5Y3k0bmVsNkQ2cjFqdnQ5dHk0RmQzNzlhM3N4bElKZXJoNk1ac3grM0dFR3UyQXlWRGIxSm1Ra2lDK3Faa09NcWYyZ0RuOXh2c20vNWEwZVh3VEJQZmY1ZzlvQzBydHRNb2pqTUQ1TnlxNHNNTnV1a3RGOUhVZndpMWNoM2tMeUlFbVRDY0xmeFZFeGNOQmFlZHVETUxwRmVEZXlLeXI2bUw5Uk1Zc28wLzhObjdRZXd3ZHY4aE4rNDJrZ2M3dS9aSG94LzZ4TkpILzVFRC8wTUdmaCtxd1Eramp3RXZCYVVsSVNpQ1NwZk0zUXllV2RCK0Nyc3BaMktpSTZFQjZBSk56cDRJblEyTnhXOGZCSkUxa24wTFNaL0ZLRnUrZkcya1d3TVpuTkcwZjNrUGsxcTd4TEQ4dUtSWmhPdmpkS2FwMmtHUUciLCJtYWMiOiIzZjU0ZWNmMWQwMDg4ZjdiZjEwZmVkYzNjNGYzNzM1YjVhMzlmYjdlNmYxMDk0NzI0M2ZhZDE2ZjdlNjZlMmFkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdiOUJSTW9zTDFkQzlmMFlpU1dwSEE9PSIsInZhbHVlIjoibkJmSXdWb2hpWnpwOVUxWEZxYTNMMmhnRllRQXlONzNXYVMrVFZlMys0eUJOYU5aWW1LQzNBSE1jaDRXRXU1VnBHT3lQVkY3cUFVS2VtTWFsdk44ZUliMTkxb3pkZVdVbFNyeHlWS1hEdTUrRTdvQzgzQzlMc2IvM3IzS1R1SGZOVmlKZXVycTlHR2hTV2tPc1FETVBqdi91ejB0RmQ5b3B1YXJDTnNQY29hMXJKR3IyUXdBRXhMc3ZXRWFzWFI1UEgvbzBVd3NTSGJJR0lSeGxMbWd2MzIwMFdkaXgzUVhjRUhXNXZzckliS2JYNUlFbkVoaEkwS1pid0EyUnhOT0hYektkcHBzTUZyc1o1WTBmdDcxdHl3ZGtSUGgzY1lwZTVkYUdFTFYwNzVwdHJYRVowQTc4S29sbnlmRk41QS9MS3dBVGVrTWNXWVRvQUtETWR3SENRdTh0ak8wSkNybThDeDVJTDByZWZJMjNMWTEvVUQzalArWGNCS0pPUmtnWms3amw0NzFpMXYvM21Cb0dLcDQ3amphQ0p0SlNNM2ZPUTdheFhaWmJWUzdzQ0tsQ1A2cVhkeHQ3SU9pZ3NoZEhYVVBHZ2ppK3VFWDBnb0g5WENyMFBNTFZVTWtFRjZCYlEvUU13dUZueEc1SHNFMmUzVlIyWWd6OFVJZytFZUMiLCJtYWMiOiJhZjNlMTZiMzY2ZDkwMDYzYTg1MzhkMjg5M2Y4NzFlOWIxNDNmOTJlMjliMzNmYWY3MWFmYjMwNDZlNzllZTZlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJDc05Na3hSMmNoRkFZZW5oNE9vWHc9PSIsInZhbHVlIjoickVKNTN4ZGE1SUx4OWNSalNrajRqUlFNNXdUKzhvN04xK0VhejltenNaQjROKzNXeXpUcmxIeEdtaFFnOWV1emlyck1JdEVpYkpHUWkvZVlvVFBhQ1JNQ3AvVU5wNUFFcWJ2VTBDYjFoUUJWS3J4ZjZwNVRTVEQ3alIwbjIvVC9ZTkU3dXZoVERLeHpOdDZ3Q081ZDFhOFNnbTVlUWhsdllsZi83Y2h1cVI0UUNZZjdidGRUZE92aTk5Y3k0bmVsNkQ2cjFqdnQ5dHk0RmQzNzlhM3N4bElKZXJoNk1ac3grM0dFR3UyQXlWRGIxSm1Ra2lDK3Faa09NcWYyZ0RuOXh2c20vNWEwZVh3VEJQZmY1ZzlvQzBydHRNb2pqTUQ1TnlxNHNNTnV1a3RGOUhVZndpMWNoM2tMeUlFbVRDY0xmeFZFeGNOQmFlZHVETUxwRmVEZXlLeXI2bUw5Uk1Zc28wLzhObjdRZXd3ZHY4aE4rNDJrZ2M3dS9aSG94LzZ4TkpILzVFRC8wTUdmaCtxd1Eramp3RXZCYVVsSVNpQ1NwZk0zUXllV2RCK0Nyc3BaMktpSTZFQjZBSk56cDRJblEyTnhXOGZCSkUxa24wTFNaL0ZLRnUrZkcya1d3TVpuTkcwZjNrUGsxcTd4TEQ4dUtSWmhPdmpkS2FwMmtHUUciLCJtYWMiOiIzZjU0ZWNmMWQwMDg4ZjdiZjEwZmVkYzNjNGYzNzM1YjVhMzlmYjdlNmYxMDk0NzI0M2ZhZDE2ZjdlNjZlMmFkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674421558\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}