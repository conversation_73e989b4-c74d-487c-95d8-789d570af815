{"__meta": {"id": "X4a693f1253433e108b93826a8fc9e1d4", "datetime": "2025-06-28 16:19:34", "utime": **********.59996, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.153259, "end": **********.599975, "duration": 0.4467160701751709, "duration_str": "447ms", "measures": [{"label": "Booting", "start": **********.153259, "relative_start": 0, "end": **********.539263, "relative_end": **********.539263, "duration": 0.3860039710998535, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.539273, "relative_start": 0.3860139846801758, "end": **********.599976, "relative_end": 9.5367431640625e-07, "duration": 0.06070303916931152, "duration_str": "60.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45701584, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00313, "accumulated_duration_str": "3.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5734, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.412}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.584738, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.412, "width_percent": 16.613}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.590518, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.026, "width_percent": 15.974}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1309985758 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1309985758\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-389585553 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-389585553\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-635776954 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635776954\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2137714104 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127556646%7C37%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhTN2RWblBXaE1WLzg3V0tQQWhvQmc9PSIsInZhbHVlIjoiWS9GQ0NuTXBaUWNvdGd3VXo2SUJYWkNQVFpOYWpMbUpGOGdBZkhoQk1wMVdvV0ZaRVpKbEd0Zk5ZZHRsSGpyS3oyZFFzVUhCRkhFSGlKS0FLSThCSWVESzIyUjN3Q09nQWJrVnU0VlRYSzJsaWh1TnhML1FBbVFyNVBkMGJSRnZjU0E4aXVaSm1OZ3JGL0F1NkhXc1BJTDExTFBiUFR5N2VXSGlaYjl1a2FNSWVLcUxMYVF4Q2MyeHlibExjMndzTDlyMEVoSVBLakIxVXhpK3ZHVmh2Y01FelVlVEtzR2VxWkFkb016S0NSOWFJWjVUNUNHVE9manVpRGMwcXBYRkR3em50N2piMERhZExiN281bDB1STZldVRnRlM3aHNYUlY1bCsrSldoQ1l0SFhvem1iZ3RKZ05yUmNaUzdLZnlrQlM5b2lVY3dkRVZBRHNGdlAzbUNKZzVab0QrZlcvVDR1UkJUQmNObWpzejN1ZlJkY3YweXYzc0xkcWNhQkJUVDJVL2x5ck5NRVpxSkh4N29YZStkVFgyRklCVGd2ZklZcFFRdTZDcGk5VSs1Z2pDYXJtOWd1dHRBUzV6WHp3aWkxRkxHaW50dzRtaWExZkd0alZIUGNzVHZzVjFmVlNhb3p5eENxcm9uZDBQWjlXbDdSQ3A1T0hWbkRVVGd2NTIiLCJtYWMiOiI5Mjg0ZjcyODE1MTI0M2I5MDNiZThjYTQwZjQ0ZmI4YzliZTMyMzAyNmFiNTNlMGNmMTQ2MjRhMTc4MmRlOTA5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdtbjVDWnEvUjh2Sm9QL3lqRTZ2T1E9PSIsInZhbHVlIjoiZlhhRkJKdGwvZkxCSVQ1UHQ5S2JUczloU3laV0NlMXpHOHViMjh0WC9QbjBoR2YrVGxyMS9ybUpTandiSkJ4T0I1dEM5dUYrTzFxVWNCMWtld28xZEtpMy9uRGdvblozbitYa2xyRUMzaG16azEyWmIvRHpiYkFrSzlIdmtOTDIrbkdOeUtRUEcxQWZiVUVkTVFOYWFucG9jVkJyQ3ZXV2pVbHRUV0pwZ0xrZk03aThoa29xQ241dWNnWWsrSi9VaWxQcTg4bFIrSVgyUE5lT2FtdVRxMis4c0pnR3dVcU1LT2lwMC83WUdTWHlLcFZRS0V5V3ViOHJLMm4rZk9DL2g0bmZwZW5MQzFtN3k0aFBDT1pQZmVQY2hTdjVTczc0SlZDc0F1ZDBNMlY5eHFpRmtVMTFpdDl2aU9rVGsrV0ptWlpXdFVFSlEyUTllWG5TOVBReGs1aFRHWUtoL3lwaEdkOExmSDhGUTNGOEluMVIxU1dzK08xNkZ4ZjZFMU1hekxWSWFMUDFsZWpiUTRpaGJDcDB5U0RmZyt4dmJsdEJpT3dZZzB4cXluUVNRTWhscE9jTzBvdHMrYUIvK2RLVmNlYVlwNGJxblYvMmdSZnkzc2hwTUN2YmRocTlFMGNLdGEzSmg5RnZNUVArUyt3bkxxUTJ0L3dZZG05TjRQODkiLCJtYWMiOiJiZjc4MGIxNjFmMzdlNzgyYjkwMWUzYjY4YjBlNmRkOGE4MjNmMjRhYmZhMjQ0NDNmMThlYjIxYzBlOWRkNWI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137714104\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-395286752 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-395286752\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkN3aHlKOGFncHhnMHI3MUJRQ2tDcEE9PSIsInZhbHVlIjoiZ3VIY3lZL0NpRVdaanJ4VUNuQlh4WlBhdGxmbSt3QUtrKzhqOFVTaCtOVUIxZFNrSGJGblB1dnNDVGFneEo3WDNPRGNVa0tmU0xpNy9iTGZkY2xmVFBVMFNLTUVZcXYzZE96a2tYcktZSDB1NUVGMnhWT2J1WDV4R3VTd1N2N01lNjc4RWZIZFh2THBtS2I1ODVvYmc3bWhWYkdYejhubXNvMWVxamZtbWQ1S0tXVkFSVmcwNEtQdHhQK09BVUliR0FLUzB0WG0wYlJZRWtJZmxxN3FhZ0hsSnhOK21OY09TTElBS1FtNmQwZUhYS3gxZklwZ1VTYzhzdDNiZkdKclk1YjFUQ0NlTFpxMmhSZmRhUHE1MWlBUFdyOEVEbVZNMUl2bmZFZEFEaW95MDlsMlZvcFZSNWZJUjk5Kzl1SFF0aUY5TERMYzIzWCtDQWlYOEpuZjJLOVd1a2IxSEd4UWdJTmV2cjk1a0JBUVJLWVdHUUV1TjBmbkZUcm9pbXIwL3hJd3RqcTFTQ3VDQWE2bnhPZDh6c0EzRWYzNkZPYWxWN3hZakY0K2RGTjY1Q0RFS2lvNVJWZlN6dDFLYUU3VmxDSDVjbjZ4WE9vOHBwZ3p6VzNBYi96V1JGZ0Niemk1QXczQmczN28wQXFuekFiam1CUUNkTGxGNkpSdXJ1WEciLCJtYWMiOiI4ZGJiMGVlOTBiYjkxZmM5YWRhM2Y2ZDhlOGRlODFjZjRiNDdiMzIyY2ExMjUyNGViZjk2ZDRkYmRlMzE0MDEyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlYvZWhLTVJzMWFlbTBBY3B1am1vaWc9PSIsInZhbHVlIjoicCtldld0R0pLNTNVQ09nTUF4am9kYjBTU2w2cWsyZXFDQUhpT0VObjdlcjIwaEVSQVk2T0FNSzNNRkRJWHFCd3NWRllHcll5TE11OUZRa3pvMEZMeDFSNW5oRUJaUXFLbG9aOENwTmxEMHBYYUVhZHh1ejhXWldWc1ZKVFIrVmV2THVJN08wM2NjOXhEWVB0bUt6NFJILzY5TkpzK1ArSkJDREtsMWpJK2hGNUZZWmVjdVlRWXJzMU0vd2tKb3FaeWlhVEY5Wld6RXJiYUE4RzJ2aFdlQ1B2Q1dFVU1HMWhWMVMxRHhzY1VoZUtnUDdTbWVrNFExS3RFUjJQSEt5d09QbFB1VmUwT3liZENYS3V4NlAwYW1yMnQ0UGdIVHkwMi8zWFVJb2gyZHF2SjU1anhETDR0S0tnOXh1d0g1YkVyd3B0dUxiN3UrbThHNGdYR0l5ekl5dDlJaXBiamdsbkZXLzdSK3dWdG5QaSs5Tk5VaXNYOUJTR1UxVlZwb2htdGRmWVBHZDRDSzEzY25MRXVtUG1qMTZ4MmhaZEJ6T2gvbG1taVNpODA0YWZScFJwakNqZFBXanVVUy9CNnE5Mk9QeUpHOFhjNVNEL1BIWW5wcFV6VWQ0bnE3VUt0R2kwbk5mSWE2L0Zmc3FYcE5xUmFKdzErdWZtVzRxNmVXN3YiLCJtYWMiOiI2NWZlZDdlZGQ1NTAwODBhM2Y0NmM4NjFmZjhmYWU4ZWExMGQ3ZGE1MTc4OTBjMTFhMjViOTllMjIxYmFmZjg0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkN3aHlKOGFncHhnMHI3MUJRQ2tDcEE9PSIsInZhbHVlIjoiZ3VIY3lZL0NpRVdaanJ4VUNuQlh4WlBhdGxmbSt3QUtrKzhqOFVTaCtOVUIxZFNrSGJGblB1dnNDVGFneEo3WDNPRGNVa0tmU0xpNy9iTGZkY2xmVFBVMFNLTUVZcXYzZE96a2tYcktZSDB1NUVGMnhWT2J1WDV4R3VTd1N2N01lNjc4RWZIZFh2THBtS2I1ODVvYmc3bWhWYkdYejhubXNvMWVxamZtbWQ1S0tXVkFSVmcwNEtQdHhQK09BVUliR0FLUzB0WG0wYlJZRWtJZmxxN3FhZ0hsSnhOK21OY09TTElBS1FtNmQwZUhYS3gxZklwZ1VTYzhzdDNiZkdKclk1YjFUQ0NlTFpxMmhSZmRhUHE1MWlBUFdyOEVEbVZNMUl2bmZFZEFEaW95MDlsMlZvcFZSNWZJUjk5Kzl1SFF0aUY5TERMYzIzWCtDQWlYOEpuZjJLOVd1a2IxSEd4UWdJTmV2cjk1a0JBUVJLWVdHUUV1TjBmbkZUcm9pbXIwL3hJd3RqcTFTQ3VDQWE2bnhPZDh6c0EzRWYzNkZPYWxWN3hZakY0K2RGTjY1Q0RFS2lvNVJWZlN6dDFLYUU3VmxDSDVjbjZ4WE9vOHBwZ3p6VzNBYi96V1JGZ0Niemk1QXczQmczN28wQXFuekFiam1CUUNkTGxGNkpSdXJ1WEciLCJtYWMiOiI4ZGJiMGVlOTBiYjkxZmM5YWRhM2Y2ZDhlOGRlODFjZjRiNDdiMzIyY2ExMjUyNGViZjk2ZDRkYmRlMzE0MDEyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlYvZWhLTVJzMWFlbTBBY3B1am1vaWc9PSIsInZhbHVlIjoicCtldld0R0pLNTNVQ09nTUF4am9kYjBTU2w2cWsyZXFDQUhpT0VObjdlcjIwaEVSQVk2T0FNSzNNRkRJWHFCd3NWRllHcll5TE11OUZRa3pvMEZMeDFSNW5oRUJaUXFLbG9aOENwTmxEMHBYYUVhZHh1ejhXWldWc1ZKVFIrVmV2THVJN08wM2NjOXhEWVB0bUt6NFJILzY5TkpzK1ArSkJDREtsMWpJK2hGNUZZWmVjdVlRWXJzMU0vd2tKb3FaeWlhVEY5Wld6RXJiYUE4RzJ2aFdlQ1B2Q1dFVU1HMWhWMVMxRHhzY1VoZUtnUDdTbWVrNFExS3RFUjJQSEt5d09QbFB1VmUwT3liZENYS3V4NlAwYW1yMnQ0UGdIVHkwMi8zWFVJb2gyZHF2SjU1anhETDR0S0tnOXh1d0g1YkVyd3B0dUxiN3UrbThHNGdYR0l5ekl5dDlJaXBiamdsbkZXLzdSK3dWdG5QaSs5Tk5VaXNYOUJTR1UxVlZwb2htdGRmWVBHZDRDSzEzY25MRXVtUG1qMTZ4MmhaZEJ6T2gvbG1taVNpODA0YWZScFJwakNqZFBXanVVUy9CNnE5Mk9QeUpHOFhjNVNEL1BIWW5wcFV6VWQ0bnE3VUt0R2kwbk5mSWE2L0Zmc3FYcE5xUmFKdzErdWZtVzRxNmVXN3YiLCJtYWMiOiI2NWZlZDdlZGQ1NTAwODBhM2Y0NmM4NjFmZjhmYWU4ZWExMGQ3ZGE1MTc4OTBjMTFhMjViOTllMjIxYmFmZjg0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}