{"__meta": {"id": "X53d87031f4c50e35367f4ea0fd7763de", "datetime": "2025-06-28 11:22:26", "utime": **********.137922, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109745.687626, "end": **********.137933, "duration": 0.45030713081359863, "duration_str": "450ms", "measures": [{"label": "Booting", "start": 1751109745.687626, "relative_start": 0, "end": **********.055501, "relative_end": **********.055501, "duration": 0.3678750991821289, "duration_str": "368ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.055509, "relative_start": 0.36788320541381836, "end": **********.137936, "relative_end": 3.0994415283203125e-06, "duration": 0.0824270248413086, "duration_str": "82.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45438552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02725, "accumulated_duration_str": "27.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.093144, "duration": 0.02632, "duration_str": "26.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.587}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1292381, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.587, "width_percent": 1.798}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1315532, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 98.385, "width_percent": 1.615}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-369540339 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-369540339\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1207731180 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1207731180\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-284138338 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284138338\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-95110201 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IlVNR2I3SjhXRWQvb0d6MlhuS1FrTFE9PSIsInZhbHVlIjoiQkx1bWM2OW5RU0FUOUwwY3lXcTFOK2RXZWVpOVhsYTRmS2VWVlBPV3BrRTBRaytSdUczUStTdmpaK1VZNlh4NE8rUW1VOVM5V2dPYjFneS9naTl1SDNIdThyOUlwM1owK0NUOVc0c0pSTTFzbGx5NWlPZ3NyRG51OW9KSWtUTGxKb2ZvZ3p5dEY4SU1aS052M1JLeFNRcUcyR0lmOVNZdHBrTzBUS0xLNjBaYVRaVHBveTRYMzhtVDdtN2xjaWFtT2NRVXp2YWppR3FvdmU4ZklNVk1oRTZZOGtWRG9PUEQ1L1N3OWhSWkU0U2J2L1YyYnBrRy9zNHA5VHF0b2tVUWlVVDlFT2pweXk4NUpmZUFEaHQwbHkxYUs3STVDcUR2MlczUFVPRkpTUU1FNUI1S3VVUFNMeU5heVlNL0pkck8xR3YvSlhVeDdqNmluQU1iL1BNUlFhNWZLNlZ5UDBiUzVMYjZnekVVcm5qNHBXWm9Ua0xMM2JoNWJhK3k4aXk2Rm1LZVk3NFJ6YTBvQTdoZUJkSmMrS3NRUUlYZHdrakxxWDVTMWU3SC9tSnZORjFUaS9adUFSY0g0TkZ3anl3b0VHcEpMNkpRdkxqNTNEeTViYmIwQ1dRT1FxNlBKNFBHUjRMdzJoaS82SmFhOXdsM2tzdGxVZ1FFUmI5V0tCamIiLCJtYWMiOiIyNTc3MmNiY2I3YjRmMGY1NTVjYTBkM2Q3OTQ5MjJlMTZhZDBmNTg3MDA2ZTRhZDg2NDEzNzQxMzE1ZjlhMzdiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InV0MnBTZGVDa3FLbDBQRHV3NjZGNEE9PSIsInZhbHVlIjoicUxEamtnLy92Ry9KeHJpZS83Z2VSMDdFT0c5cHJTWmtNbnBiYVZaQWF1SGJoM1hZZEpER0NXblVlcnJXdlFRaHlTaC9xa1ZTWlYwUDV1SWFXNUhzajdzcnpFMXZSRVJOWENKNXpZdkU0cTNhL2J4ZWRLY252VUw1d1FzcEZVc0xDS1ZMMlZUSDlCbFM2ZGJMWlllcWhwbnZzbzJ0dkFHa3JWcWtGTmVzTzFpYnB2Z3J2TWFzYm81ZzdVY25RZXU2YVZhNDZMV0h1cDJuV3FHbmxVRHRYMjYzTVMxTjhIOU9UK0tqcktUbVdmL1dWb3dvSGdURlBiSEVlTlc3eVVnU2RFMVU1NTJaSGk0OEszczZxT0lYcWg4WGF3dVNka3MwQVExcXl3YUFhdUg1UUhlaGhQNitsbkxJV3ZPTnZsVEFJQXdoSEs4aVdvZ2kxNWhBMGRLbWhPUEpTS3BGSTMzUTNkbmR2KzZtbjl2Smx1L3oveWZ2ZDZqMzhHdzFtcHZMVk1PSEZDQWpmYWYzR1hyTW93R0xEYXlnUnNEZC8rK3ZKS3FKOUpYaCtBeGk4OFZnbGttNXo5T1I3bFZCY0Z5NDdwY2JUKzJVYmlFNXpEblcveDI4clh2c1lOUDhwWnlVU2xPS2FBY1UwS0IvTk1aMktGQkUvdDJxR1dmQXZlVk8iLCJtYWMiOiI0MWM3MzBmMjExODRlMTI0ZDVmY2Q1NDk1OWQ3MDIwZDNiNTNhY2ZjODEyNzVhYjdhZWYzNWMzNjY2NGZhYTdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95110201\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-296171437 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-296171437\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:22:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5PTWxubXg3N1RmcnY4OVdEK096cGc9PSIsInZhbHVlIjoiTkhUOVhsKzE1R0RtR01rcURxSGd6Ymk2RWNKeGpTbGkwa1czR3ZmYUxyT1BWdU1LWVVjQ0IyZDJzbmM3ZVlLMDhoYjlqcTJGQmlPWUIvT0FYaElYSXRYdDR5a1N1TWp5Znp2ejZDaXFmNVJabjNLbEd6bWgrWk05QzV6ZThYWCtscXRUYVlwVXNHdk9kdTNzZHhkckRodEJ1YXlCQldqbHVkOGFaaVV6c05aempOSHNtcTlMUXhMeklYdkZzZ0hkN1o5Wkt3YXZsVEtqRVZWSTlNM2l6QzViVXY3SVBnMGNvNXJGQS9HRUFXcTRhYVJIdG1CdkxZR1JkWWxZcmt6U1hwZmtJc2FsdzJJSGRXQkdEWUZvdlZNZnEvZEw4OFc3Z1FUT1grWmlQUkZyZXltSmQzWUFsdGdWa2RiTVJuTmZYUFlLWEJxQlNjN0kwOTFMWkxIOGJxZS9XZXpiRmdETnltWTNiNHpPYlFtNm00M1pPU2JNUmk4QXR2UUxvYS9KR05aV0ZONERmdnFzMitpYzdhTUJVcmc4WlMwNFFDU3JNU2J2RUJRTm1udFF0Mk04aG9QTHk4enNiellwczhHblZrMFZEaWxiSDFmdlJ5MnRxTzhTRWRVcGpVMlBQWGtTRFZoVnJOM0tvVEtidzc0eGY3VnZiWWxmYTIzdlFWMjciLCJtYWMiOiIwNDFjMGIxYWU3NTY3ZjM4NDBkMWNlMjcxNWM1YTQ0ZDYyYzExNjk0MmFjMmQ4NjJiMzE3N2RjYWNiOWE3MDJhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZGWnFXa2J4RFc3WXpueFBsNE1Rc2c9PSIsInZhbHVlIjoiYnVOTjgxRFlBYVZzeXVPWGt2ZXl1Wi9VeFFuRTFndnVLWFR4c0x0YUl3OE5sZFRxakZGUnBlZ1MwVXJGdVkzSHVDQmJCbkZGM2ZvT21TMnpTVEFTOHJNUWJid0Npajc4WTEweFNsU3g0YkxraW9Db0FXMUU2dlpvd2drKzRwNkt2TUV0WjNORDNXMXFzME5yNm9LS2poN2R2bW1rdG5BcDFZZHhSRXVuSmprWUNSSU54N0U1ZXp0ZHVmdlMrRXBGVTBWQ1BCWGdMNXFrZXptSTA4YnhWUHQrajZPSkZhTzBINU13V3U0VzF5QjEwZnQ3RUFGOG9EbUQxQ2MvR2xBUktEWmIxTmU0aXJzZk50cHZFUk95ZWhZWmM2eldBYXl6bUpMaGlZeCtPTXRzdTBVRXFiK3hidFRFODlQellwNHI1R1JWb3BNVVZtK2pTb3d0R2M3WEVqR28veXFxL0l0bm4rT2NybkNMcmd2eU1WUkwvRGdtNERmbnNXa3dMbnhFRG5FRHhxYzNzWmxWYnZ0Y21DQkRBVm5MaHBoMGlweXI5SXlHVW5TeE42L3lqMmVRdEJ6eVFzWUlGRU0wZkhCWkU3QkdsZjR3MndzWHZ1YzlaRlJGeVJrcXU3elhWRG85ejJzT0lHSFhKbnR5ZlFoZlRPbE5xb0tNOGl6R0lKR3IiLCJtYWMiOiI4NTIxMjgxMDNkNzU1OTY3NWE0ODQyMDZiMjI0ZTNlNTkyMzlkMzgwNTBlMjJmNDJkMmE4ZmNjNjljYjEwNDc0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5PTWxubXg3N1RmcnY4OVdEK096cGc9PSIsInZhbHVlIjoiTkhUOVhsKzE1R0RtR01rcURxSGd6Ymk2RWNKeGpTbGkwa1czR3ZmYUxyT1BWdU1LWVVjQ0IyZDJzbmM3ZVlLMDhoYjlqcTJGQmlPWUIvT0FYaElYSXRYdDR5a1N1TWp5Znp2ejZDaXFmNVJabjNLbEd6bWgrWk05QzV6ZThYWCtscXRUYVlwVXNHdk9kdTNzZHhkckRodEJ1YXlCQldqbHVkOGFaaVV6c05aempOSHNtcTlMUXhMeklYdkZzZ0hkN1o5Wkt3YXZsVEtqRVZWSTlNM2l6QzViVXY3SVBnMGNvNXJGQS9HRUFXcTRhYVJIdG1CdkxZR1JkWWxZcmt6U1hwZmtJc2FsdzJJSGRXQkdEWUZvdlZNZnEvZEw4OFc3Z1FUT1grWmlQUkZyZXltSmQzWUFsdGdWa2RiTVJuTmZYUFlLWEJxQlNjN0kwOTFMWkxIOGJxZS9XZXpiRmdETnltWTNiNHpPYlFtNm00M1pPU2JNUmk4QXR2UUxvYS9KR05aV0ZONERmdnFzMitpYzdhTUJVcmc4WlMwNFFDU3JNU2J2RUJRTm1udFF0Mk04aG9QTHk4enNiellwczhHblZrMFZEaWxiSDFmdlJ5MnRxTzhTRWRVcGpVMlBQWGtTRFZoVnJOM0tvVEtidzc0eGY3VnZiWWxmYTIzdlFWMjciLCJtYWMiOiIwNDFjMGIxYWU3NTY3ZjM4NDBkMWNlMjcxNWM1YTQ0ZDYyYzExNjk0MmFjMmQ4NjJiMzE3N2RjYWNiOWE3MDJhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZGWnFXa2J4RFc3WXpueFBsNE1Rc2c9PSIsInZhbHVlIjoiYnVOTjgxRFlBYVZzeXVPWGt2ZXl1Wi9VeFFuRTFndnVLWFR4c0x0YUl3OE5sZFRxakZGUnBlZ1MwVXJGdVkzSHVDQmJCbkZGM2ZvT21TMnpTVEFTOHJNUWJid0Npajc4WTEweFNsU3g0YkxraW9Db0FXMUU2dlpvd2drKzRwNkt2TUV0WjNORDNXMXFzME5yNm9LS2poN2R2bW1rdG5BcDFZZHhSRXVuSmprWUNSSU54N0U1ZXp0ZHVmdlMrRXBGVTBWQ1BCWGdMNXFrZXptSTA4YnhWUHQrajZPSkZhTzBINU13V3U0VzF5QjEwZnQ3RUFGOG9EbUQxQ2MvR2xBUktEWmIxTmU0aXJzZk50cHZFUk95ZWhZWmM2eldBYXl6bUpMaGlZeCtPTXRzdTBVRXFiK3hidFRFODlQellwNHI1R1JWb3BNVVZtK2pTb3d0R2M3WEVqR28veXFxL0l0bm4rT2NybkNMcmd2eU1WUkwvRGdtNERmbnNXa3dMbnhFRG5FRHhxYzNzWmxWYnZ0Y21DQkRBVm5MaHBoMGlweXI5SXlHVW5TeE42L3lqMmVRdEJ6eVFzWUlGRU0wZkhCWkU3QkdsZjR3MndzWHZ1YzlaRlJGeVJrcXU3elhWRG85ejJzT0lHSFhKbnR5ZlFoZlRPbE5xb0tNOGl6R0lKR3IiLCJtYWMiOiI4NTIxMjgxMDNkNzU1OTY3NWE0ODQyMDZiMjI0ZTNlNTkyMzlkMzgwNTBlMjJmNDJkMmE4ZmNjNjljYjEwNDc0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}