<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار خصم المخزون في POS</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .result-box {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            min-height: 100px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🧪 اختبار خصم المخزون في أنظمة POS</h1>
        
        <div class="alert alert-info">
            <h5>📋 الهدف من الاختبار:</h5>
            <ul>
                <li>التحقق من أن النظام الكلاسيكي يخصم المخزون بعد إعادة التفعيل</li>
                <li>التحقق من أن النظام المحسن يخصم المخزون بشكل صحيح</li>
                <li>مقارنة النتائج بين النظامين</li>
                <li>التأكد من تحديث جدول warehouse_products</li>
            </ul>
        </div>

        <!-- اختبار دالة warehouse_quantity -->
        <div class="test-section">
            <h3>🔧 اختبار دالة warehouse_quantity</h3>
            <p>اختبار الدالة المسؤولة عن خصم المخزون في كلا النظامين</p>
            
            <div class="row">
                <div class="col-md-6">
                    <label>معرف المنتج:</label>
                    <input type="number" id="testProductId" class="form-control" value="1" min="1">
                </div>
                <div class="col-md-6">
                    <label>معرف المستودع:</label>
                    <input type="number" id="testWarehouseId" class="form-control" value="{{ Auth::user()->warehouse_id ?? 1 }}" min="1">
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <label>الكمية المراد خصمها:</label>
                    <input type="number" id="testQuantity" class="form-control" value="1" min="1">
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button class="btn btn-primary w-100" onclick="testWarehouseQuantity()">
                        🧪 اختبار خصم المخزون
                    </button>
                </div>
            </div>
            
            <div class="result-box" id="warehouseQuantityResult">
                <em>انقر على الزر لبدء الاختبار...</em>
            </div>
        </div>

        <!-- اختبار المخزون الحالي -->
        <div class="test-section">
            <h3>📊 عرض المخزون الحالي</h3>
            <p>عرض الكميات المتاحة في المستودع قبل وبعد الاختبار</p>
            
            <button class="btn btn-info" onclick="checkCurrentStock()">
                📋 عرض المخزون الحالي
            </button>
            
            <div class="result-box" id="currentStockResult">
                <em>انقر على الزر لعرض المخزون...</em>
            </div>
        </div>

        <!-- اختبار تكامل POS -->
        <div class="test-section">
            <h3>🛒 اختبار تكامل POS</h3>
            <p>محاكاة عملية بيع كاملة واختبار خصم المخزون</p>
            
            <div class="alert alert-warning">
                <strong>تحذير:</strong> هذا الاختبار سيقوم بإنشاء فاتورة حقيقية وخصم المخزون!
            </div>
            
            <button class="btn btn-warning" onclick="testPOSIntegration()">
                🛒 اختبار عملية بيع كاملة
            </button>
            
            <div class="result-box" id="posIntegrationResult">
                <em>انقر على الزر لبدء اختبار POS...</em>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إعداد CSRF token
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function showResult(elementId, message, type = 'info') {
            const element = $('#' + elementId);
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colorClass = type === 'success' ? 'success' : 
                              type === 'error' ? 'error' : 
                              type === 'warning' ? 'warning' : 'info';
            
            element.append(`<div class="${colorClass}">[${timestamp}] ${message}</div>`);
            element.scrollTop(element[0].scrollHeight);
        }

        function clearResult(elementId) {
            $('#' + elementId).html('<em>جاري التحميل...</em>');
        }

        function testWarehouseQuantity() {
            clearResult('warehouseQuantityResult');
            
            const productId = $('#testProductId').val();
            const warehouseId = $('#testWarehouseId').val();
            const quantity = $('#testQuantity').val();
            
            if (!productId || !warehouseId || !quantity) {
                showResult('warehouseQuantityResult', '❌ يرجى ملء جميع الحقول', 'error');
                return;
            }
            
            showResult('warehouseQuantityResult', `🔄 اختبار خصم ${quantity} من المنتج ${productId} في المستودع ${warehouseId}...`, 'info');
            
            // محاكاة استدعاء دالة warehouse_quantity
            $.ajax({
                url: '/test-warehouse-quantity',
                method: 'POST',
                data: {
                    product_id: productId,
                    warehouse_id: warehouseId,
                    quantity: quantity,
                    type: 'minus'
                },
                success: function(response) {
                    if (response.success) {
                        showResult('warehouseQuantityResult', '✅ تم خصم المخزون بنجاح!', 'success');
                        showResult('warehouseQuantityResult', `📊 الكمية السابقة: ${response.old_quantity}`, 'info');
                        showResult('warehouseQuantityResult', `📊 الكمية الحالية: ${response.new_quantity}`, 'info');
                    } else {
                        showResult('warehouseQuantityResult', '❌ فشل في خصم المخزون: ' + response.message, 'error');
                    }
                },
                error: function(xhr) {
                    showResult('warehouseQuantityResult', '❌ خطأ في الاتصال: ' + xhr.responseText, 'error');
                }
            });
        }

        function checkCurrentStock() {
            clearResult('currentStockResult');
            
            const warehouseId = $('#testWarehouseId').val();
            
            showResult('currentStockResult', `🔍 جاري جلب المخزون للمستودع ${warehouseId}...`, 'info');
            
            $.ajax({
                url: '/get-warehouse-stock',
                method: 'GET',
                data: {
                    warehouse_id: warehouseId
                },
                success: function(response) {
                    if (response.success) {
                        showResult('currentStockResult', '✅ تم جلب المخزون بنجاح!', 'success');
                        
                        if (response.products && response.products.length > 0) {
                            response.products.forEach(function(product) {
                                showResult('currentStockResult', 
                                    `📦 ${product.name} (ID: ${product.id}): ${product.quantity} قطعة`, 'info');
                            });
                        } else {
                            showResult('currentStockResult', '📭 لا توجد منتجات في هذا المستودع', 'warning');
                        }
                    } else {
                        showResult('currentStockResult', '❌ فشل في جلب المخزون: ' + response.message, 'error');
                    }
                },
                error: function(xhr) {
                    showResult('currentStockResult', '❌ خطأ في الاتصال: ' + xhr.responseText, 'error');
                }
            });
        }

        function testPOSIntegration() {
            clearResult('posIntegrationResult');
            
            showResult('posIntegrationResult', '⚠️ هذا الاختبار سيقوم بإنشاء فاتورة حقيقية!', 'warning');
            showResult('posIntegrationResult', '🔄 جاري اختبار تكامل POS...', 'info');
            
            // هذا مجرد محاكاة - في التطبيق الحقيقي يجب إنشاء راوت خاص للاختبار
            showResult('posIntegrationResult', '📝 لاختبار POS الحقيقي، استخدم واجهة POS مباشرة', 'info');
            showResult('posIntegrationResult', '🔗 انتقل إلى: /pos أو /enhanced-pos', 'info');
        }

        // تحديث المخزون كل 30 ثانية
        setInterval(function() {
            if ($('#currentStockResult').text().includes('تم جلب المخزون بنجاح')) {
                checkCurrentStock();
            }
        }, 30000);
    </script>
</body>
</html>
