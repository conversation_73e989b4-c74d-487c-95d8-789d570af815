{"__meta": {"id": "Xb9187ccf3cf14a78bf0a7426d96f267e", "datetime": "2025-06-28 16:01:27", "utime": **********.567486, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.02904, "end": **********.5675, "duration": 0.5384600162506104, "duration_str": "538ms", "measures": [{"label": "Booting", "start": **********.02904, "relative_start": 0, "end": **********.496043, "relative_end": **********.496043, "duration": 0.46700286865234375, "duration_str": "467ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.496055, "relative_start": 0.46701478958129883, "end": **********.567501, "relative_end": 9.5367431640625e-07, "duration": 0.07144618034362793, "duration_str": "71.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831872, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00464, "accumulated_duration_str": "4.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5430288, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 35.56}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5557768, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 35.56, "width_percent": 12.931}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"produc%' or `sku` LIKE '%<div class=\\\"produc%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;produc%", "%&lt;div class=&quot;produc%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.558764, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 48.491, "width_percent": 51.509}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1626475203 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1626475203\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-849893841 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-849893841\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-340973592 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&lt;div class=&quot;produc</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340973592\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2139945575 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjN2MGVPL1ZsUnZLVnc3aTcwRXJ2UUE9PSIsInZhbHVlIjoiSVdzZTZrZ1ZuTDJRdExqZWlkc2RKdEdiSHh6UWhXc0Y1UzdEdXl6eHg0YUpjMWt5UTJnaHMyZmV6MjY0K05kbkdmWERxZ2NsalFOZDlRVEw4ckUzYVpjMExScmQ2T2lSUHpLd0hub2FPdi9yb0U0aVdjMlZGQUMrdUpUQThZcDNCVVYvNE5mTlZFVUw1MnBNZFl2aUd2WkZMYWY0RGRLQTlLS2pWQjZVZGNvdHVueWM0cVZHbjVHWUtQR29CeHo4NWw1dE9MMGZBSVJ0bnkzblllMkxsd1hIMC84d1dFay9EeFhOM0p3czJPbmRScXo3UVhQcUYzTjVPZGNmWVRTUmRXWlJvbWxuem5JRklQWkx4NE90ZzJoeHk3YzRHUnJ3bnNNVGRpV0ViQWdTVU44M2tUMGVaSDgvRHNBQ3lBbFNQSWxQTkFsdnhtWjVCelhlaHJmbzh0MCtqL1FzTmdzMVZOZGwyM1Zyd0pZL29jVVArdHk2NnAvRFR4V3JWcUNqMURQMnk1ek5aRkJGcUtrTlZHY25YYzR4cE1HSG5zQXUxS2V5UXlxVGFWZWxRZlBKTlhSd003b2w3NkEwWnZEOHplRnQyWkUrQXRRY0pPQWlhTHJXcTl5UGRlY2ZKUVozR29qcXNoWnNCSVJJTUlkTVlJWXVkYnVLVEg0SWVHMlkiLCJtYWMiOiI0MjYwNmYwN2RkNTIwOGMwODYwNWE3MzI0YWQ4NDE5NmVhNTg0NThhNjhkYzM2MjJkOTA1YTZhODNmNzRmODMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImcyTFNpZTY1Vmg2Vk00REtLNlV2OUE9PSIsInZhbHVlIjoidlIyK0ZUUWRTWkJBRUtBTFFWMHRiVzRWQjNFbzRZUTdvZ2lFZkZFcVhYTGVLU09XOVIySnU5Y0tHMkliVVZuSTJFMFkrNk1mZm15VVBiRUNRMnk4cWU4TVpYRGQ4c1llQzM3ZHV2a2FBMHVSRmc2RTJWa3pVZzdRYWNUM3dpcmwwdkREaHZQcDd6ZlZtaXorNHdKSTlETVkwSnBESDV5WTBvUDcvMjF3ay8zTjI3ZnlyQlJWb2ZsTjhpa1YrVVNsdzJ5WUhhRVAzWVdtcnczdWkwcVBOeU5tKzRCSWVIUkxMSDJxMmxTRXpJSUtYRkxhZU1VOEM3NVlKWUFvZ0VxdHlxaE95T1EwdG43Nm9QWTcxaGpMT1VSUTRJQVlaNHdKR1hpcTA3a0lmT1lXWDI2bENnZXhzOFBTZ1NFWUtlRWlneUU0M3M3dUdDK253aWRKY0hOZDQzZVorVm5KdE5IZDJlcmkyN2tVMEk1ZVdMMzFDYjByVkM5VHEzdHdLVUljakdVNGhFQ3dVaFY5Z2s4Z1p2UWZiSFlvbS9mb01NMjRoY1VpOGJlOUtURWk1VWVMTTFKOWhQQno5TjRCNW9Vd3hveW1QNU9kRURXRkRaZEk3ZW41RStLYm9aNmRPdFFGY1R3N1BmS2ozMlM4UWRYaFRQY3JpaWlBN2FzRGluT0siLCJtYWMiOiI1MGFkZDY2NDVkMzkzNTlmNzI4NzE2NzQ4YTQ2N2MxNzFiMjRlY2NlOWU4NmQ4YWJjNTUwMjBjNDE5MjVlMjZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2139945575\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1296788157 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296788157\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1682614875 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVZM2xJTGUvcmljcVozTGhQMk5DbFE9PSIsInZhbHVlIjoiSE9ZU0lndkZ5dVUwclBHVXBCQWFNeDZNVHVVOWozcW1ITElhbUNUeXlPUDFpU1J2eSt5Z1RiK2pGQUFFOWJuVkZ3M3dmR2ZjNDhJYWJKeVFGV0Q4bHlHWTVGOVVZU0pMMnZZUEMrcXRVOFQySGdVVGFDR0paWHp2K1RQMHRyL1dRdWZLVFRPeWo2SzhyODRRTUk5am1sUUN6THBVbUVWOUxvMENZQkZsYjdGS1N3RVE5SDhoYVRxTU5kUS9XSXRqWm5aNkMwbStmMFdKY1JadUpTS1orYW9BU21DL043ZVFvWXViVkp6dUNQZW5ra2RUWUxSY2lSRndlR2hmQ0djK2tnN0xnWkdzVEdWZUxMNWJIVkJKWldQMCtLY2ExT0xTZVE2a29hMW1HbWFaWjZNWWNtR2FiREVrMlZnSU9ZZ1BCb1czeUlIQVFzSXVFbHN2ZWFmdWNacS9ZQXdsQ2JOV1o3Lyt3VVJQRGg1dmlORWVoZkw3MC9HWHQyc3ZoSTBzeUw2TjgxSUpDNVJLQ0doZmpJc0dRSi9ZWHFBc0gwa1lpcTVEdEprd3RWYjVDbXpkK2hucG1ZS2lrYlBGU0l6ZjA1cGNMbThvdHd6T1MxSmlwdlB0Mm1NS2FBY2djdEpTYm5BTzlnV25UYkNXUEhFbjFqYmFoUXVwRnBGY3RUS3QiLCJtYWMiOiI5MDhiMzQ1MGY1M2JiMWFmNjQ2YjI4N2JkNmU2Nzg3MjVkYzIxN2I1OTk4NTE2YjY4MDFhYWVlMDc5ZDUxNTEzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJlYnZhTTdndVIvNVcxeXNjckh5UVE9PSIsInZhbHVlIjoiQ0J6Z2FpdGZVMWhDbndBVjRsMmlFZlJhTzQ5bFJGSDZON25CUTBhWHRScDBMTk1XalNRV2Y4b0NYM251TWc5ZWJVc0hndWRhVHJ0OEhhazZTWi9uRDZ3T2h5dS92ektJbTlDZDVDbi85Wml3R3BZRStoR1Y2all6VHdmQ2dZWWY5UmZwMVBrei9nSTNSU20rMFFsSnRoVkRoY3d6MzhuZEZlanRwdzhnYnNlZGo5Y1NGbEdxOS9CNEUvUC9Xbm1heFUwZkYzRlVoamhadjBJZnB2Z3VjcDFnSHlJRHYyUVFNS0hCVjc3anp6SzYwSVdjd2d6ckJtOHI1azZwa25JdllYQUNJRUd6Wm5NY3lsbWZRRm9pT1kyL3JRcnFOalcxd2k4eU1wZmU0RjEvSDd2cU5hT2gwYy9JNGRYNVFqaGNBblF4S3hKbkprV0RobWhEbGJGSXFXQlJSUzR4UzgySGJLOHlpVUFXMUFXUFQ2Yko0aEN2MjdqcDBpMXBYeUs0N1VRVWlJY211Q2N1eTZ1a0FtMU9sT3N6c08yTHMzM00valM1MDdFbTYxYVBaZFkzOHBSSG9sNzRrLzlkdTBjVngySDFCa3VpRFpIYWdhT0FBcFFDT2FlNm9wVjVuYWh5WHVPZW45Vkk2ZC9VSkVZS1E1Qmo1Mnl1TU1XdWEzTlYiLCJtYWMiOiI4ZmMyMmUxN2Q0NWQwMzA5ZDc2MjhkMDU5MWJhODNiZDk5ODllZmYzNTVjNDIyZTM4YTc2NzkyMzUxNzAyMTA3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVZM2xJTGUvcmljcVozTGhQMk5DbFE9PSIsInZhbHVlIjoiSE9ZU0lndkZ5dVUwclBHVXBCQWFNeDZNVHVVOWozcW1ITElhbUNUeXlPUDFpU1J2eSt5Z1RiK2pGQUFFOWJuVkZ3M3dmR2ZjNDhJYWJKeVFGV0Q4bHlHWTVGOVVZU0pMMnZZUEMrcXRVOFQySGdVVGFDR0paWHp2K1RQMHRyL1dRdWZLVFRPeWo2SzhyODRRTUk5am1sUUN6THBVbUVWOUxvMENZQkZsYjdGS1N3RVE5SDhoYVRxTU5kUS9XSXRqWm5aNkMwbStmMFdKY1JadUpTS1orYW9BU21DL043ZVFvWXViVkp6dUNQZW5ra2RUWUxSY2lSRndlR2hmQ0djK2tnN0xnWkdzVEdWZUxMNWJIVkJKWldQMCtLY2ExT0xTZVE2a29hMW1HbWFaWjZNWWNtR2FiREVrMlZnSU9ZZ1BCb1czeUlIQVFzSXVFbHN2ZWFmdWNacS9ZQXdsQ2JOV1o3Lyt3VVJQRGg1dmlORWVoZkw3MC9HWHQyc3ZoSTBzeUw2TjgxSUpDNVJLQ0doZmpJc0dRSi9ZWHFBc0gwa1lpcTVEdEprd3RWYjVDbXpkK2hucG1ZS2lrYlBGU0l6ZjA1cGNMbThvdHd6T1MxSmlwdlB0Mm1NS2FBY2djdEpTYm5BTzlnV25UYkNXUEhFbjFqYmFoUXVwRnBGY3RUS3QiLCJtYWMiOiI5MDhiMzQ1MGY1M2JiMWFmNjQ2YjI4N2JkNmU2Nzg3MjVkYzIxN2I1OTk4NTE2YjY4MDFhYWVlMDc5ZDUxNTEzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJlYnZhTTdndVIvNVcxeXNjckh5UVE9PSIsInZhbHVlIjoiQ0J6Z2FpdGZVMWhDbndBVjRsMmlFZlJhTzQ5bFJGSDZON25CUTBhWHRScDBMTk1XalNRV2Y4b0NYM251TWc5ZWJVc0hndWRhVHJ0OEhhazZTWi9uRDZ3T2h5dS92ektJbTlDZDVDbi85Wml3R3BZRStoR1Y2all6VHdmQ2dZWWY5UmZwMVBrei9nSTNSU20rMFFsSnRoVkRoY3d6MzhuZEZlanRwdzhnYnNlZGo5Y1NGbEdxOS9CNEUvUC9Xbm1heFUwZkYzRlVoamhadjBJZnB2Z3VjcDFnSHlJRHYyUVFNS0hCVjc3anp6SzYwSVdjd2d6ckJtOHI1azZwa25JdllYQUNJRUd6Wm5NY3lsbWZRRm9pT1kyL3JRcnFOalcxd2k4eU1wZmU0RjEvSDd2cU5hT2gwYy9JNGRYNVFqaGNBblF4S3hKbkprV0RobWhEbGJGSXFXQlJSUzR4UzgySGJLOHlpVUFXMUFXUFQ2Yko0aEN2MjdqcDBpMXBYeUs0N1VRVWlJY211Q2N1eTZ1a0FtMU9sT3N6c08yTHMzM00valM1MDdFbTYxYVBaZFkzOHBSSG9sNzRrLzlkdTBjVngySDFCa3VpRFpIYWdhT0FBcFFDT2FlNm9wVjVuYWh5WHVPZW45Vkk2ZC9VSkVZS1E1Qmo1Mnl1TU1XdWEzTlYiLCJtYWMiOiI4ZmMyMmUxN2Q0NWQwMzA5ZDc2MjhkMDU5MWJhODNiZDk5ODllZmYzNTVjNDIyZTM4YTc2NzkyMzUxNzAyMTA3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682614875\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-332578231 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-332578231\", {\"maxDepth\":0})</script>\n"}}