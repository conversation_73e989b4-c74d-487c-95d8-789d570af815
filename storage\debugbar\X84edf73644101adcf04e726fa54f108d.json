{"__meta": {"id": "X84edf73644101adcf04e726fa54f108d", "datetime": "2025-06-28 15:26:40", "utime": **********.162696, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751124399.696679, "end": **********.162712, "duration": 0.4660329818725586, "duration_str": "466ms", "measures": [{"label": "Booting", "start": 1751124399.696679, "relative_start": 0, "end": **********.081714, "relative_end": **********.081714, "duration": 0.38503479957580566, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.081723, "relative_start": 0.3850438594818115, "end": **********.162715, "relative_end": 2.86102294921875e-06, "duration": 0.08099198341369629, "duration_str": "80.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45630216, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02638, "accumulated_duration_str": "26.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.109334, "duration": 0.02529, "duration_str": "25.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.868}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1441529, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.868, "width_percent": 2.123}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.15052, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.991, "width_percent": 2.009}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1628554798 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1628554798\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1872576299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1872576299\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1964203275 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1964203275\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2076548520 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124397135%7C17%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjgrV2VDanZSb0tkSkQyRUw4UXlhZ2c9PSIsInZhbHVlIjoiYmpmUExPRG9XU0xaQURJMGZKWUV6Sy9wZFppYklRV01kZS8vNElGZ3ZuNjloTDhMcXdQT2krNU5lejZnSVRxSlBYSS9iWUpoYW1KdElwTEt4SVIvclF0SElnbWp3NFNpVXdHT2NrUFI5VnZTWVJNUUdxRmxQc29FMDJnQS9tcjEzWnJFRktMK2w2MDFvZVYvckN6L0xCZnlZdmFpMVMvRFhOSzFqRHlFVGEwTitZVjFpNVFaSyt2LzA3eml0T0QxTzk5b3hvWWVwSzJSaHNxb1BCanE1YUFWWDQyNVMvRFhkSUg4R0Y3YzlZZllpRWhhL2ZneTdBenlnelRKTlZpVXlIYnhGQTFIVFpuRU84WXFFNlFjSTZuaGJ4VnRYRXRPaGt3UzdOb0FwZkdxQTVPL2xrOTgyb1BPMDFvTU45L2thQ0NOVlo5RlV0ZzIyRncwblJzblJJWTV2dmp0c1hvY0ZRdzRCeVAvSnpFSzM1NTRTT3MxQ1hYZVUxbXcySXNuRlp0MG14Tmh3WUdpTnMrQmZpNVlVMkRRQTRaM010b01KYU00UUlXV0puOHprSzRtMVlCRGFwRmtyUlhQT25TbStYUXVkNVZQSzZHUFkwVFYwNlZ1aGdyNWJKeTZMVlBzcHd5MmU5VElySXdVWjNnWSthTndRbHFvckszamkvTm8iLCJtYWMiOiJjMGVkMmEwOTMzYzZlYzZlMWM5NGM1OTk4NDg0Njk3MTI0NGJhZTc5OWI3MmM5NmE1YTNiNmE4ZGFjY2M2MmI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJJV0Qyb1hDTklIRjZiWlFSVm40V0E9PSIsInZhbHVlIjoiOEZrNHdBSmViVnVXcTI3cE5hVHFkZUwxNS9FbVd3UlU0QXdIRndJd0ZJSFNDOHRBWTdqYytUYTZMUW5oNkw5UVVFK1RtdVZjTU01U21rUU5YbFdIZVVON0NzamNYYThvb0VRbDUwSHNNM1hZVitDb0U3NTlRY1NROVlScHRuMWlqUEVQY3BuSzUyTnZQbFUzQnkrQVlqeHRPaXlWMFhtZDNqZStsd243b3d6YUswdmo0VmFPdVRpWVBFSVlUMU9hczY0OXE1eC8zNlB0bEFzeVZxajUxaHdYS1ZCVjc0MWNTeEZwNW5IQjNOWmxBYjR0L1Y1Q0l2UzNQcEhUQ3pKZ0llT1U5bFNPQi9oQm1BdFMrYWNSZjNyNzRPMU13VzFXUUViYk5ZaGQ5Z1F1SXRjWmNxb2E1bXdMemd2cGlyVWp2bjl0cU1HNkh0eWJ5R1B2T3Zjbk1rSWRTYXl0cjBNeTRyc1AzaTZPTm12SGlwWFFMZDlkdEpNL3dDZmVTN2NCZE1UOWRqK1JXWk1teFN4QmJhaE1QRUFJRTNQNC9xeUE5dmVuRXBPT0NxV2NPNFNJVDBOMDRIZ1pBQTFhbnJ1T0ROa1dBNkxtK3k4Y24yTEJSUXdvbzhFVlFnN0hPdzB5SGhCeCttWmFkVWE5emFYeHZhczdLUU40T0MyaThNUy8iLCJtYWMiOiIyNTc4Y2EyZDFlNDMyNzUxY2U1N2JkYjVhODRlMWRlYTNjYWI2NGMwNThhNDIzNjYxYzEyNjdkMzdhY2RlZDM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2076548520\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-560611268 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560611268\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-540097460 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:26:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZEcjk0TlFaMC9CKzZIZzJaSDVyaHc9PSIsInZhbHVlIjoid3dycVgyRW9oSElWZmxPOFRuUmFHSkpndlhKTjZLVjgxVXUzZnZSeE1lOTJiSG9DUjNtUjVhdUtyQmNFbWRFaWltSlN1emg2cllncUZNaUd2dXZrbmtuRTVYZlZwNmY2ZnpzTWlaYWdsa3J5Ymhrb0lQUkFRa0RMdzN1ZVVqVjhvQmFrdFJWcStyUnQvNG1CT2gzeFBZRWV2c0pmYUN0bHBzWUQ2ckZtbk5hMXAxL0I5Z3ZIUWEvMDQ0N1gxZ3F3ekVLbkEwNzg3WWo5ZnZiMkRxSUk1WGVFTmFVZ0NNM3pVRmg2alg1UzVDbm9jODN6ajMyeFpnbS9RMmNITCt0M3NJbXJlaEcvc2pZckk0amRTMzdtK2xWWXY1alhVanozZE9LaXRvZ2QycERFOFRhclk3eWdjNHo0Y0tHa0pvb0h3RUh5dkEwTmdlcVBvcTI4NTVVSEJ0aVlQWXN6K1JDQjdYUXpsczZhVzlYKzRSQzdQVHhzRnBoRWdBa2Z6QzN0dGVYVHd2RExKR1RjZTJVTVFOUk85ak9qMUQ3MVNyVWlZNVZ5cTdtekJvdGN5L2RKL2NGRGlTTzRYQ2FKMDBDQ043Wm9ERDNYS25HRldoWlRpcVVrbUlXTE9rOVErc28vYXpWdFpaMm9RTFdrRnVpeGdOUVdCdFJjTWRHL0ZUaU0iLCJtYWMiOiJkZjAxZjM0ZjljYTg0OWQ3NDM1Njk2NDUyODQxZTViOTJlOTcxOTEzYTM5OGZkNTZkNTgzZjJhZmQ2NzNjZmU5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Iis5SE5tZDFBbC93d1dHdlNBa2pod0E9PSIsInZhbHVlIjoiSFdLbGx2eWxveDVFTHdsME9udm90aUFxVlJVSG94ZlNvUlY4MDVvd0hKbkpMT09rUmZ2bWViSThiRHVIb0dYWkxhM1UvQ3UrZHcwSE91U2xoYVNGOWRneXVMM3FXWE5Pa3EvSjQxVDBjZHpYQWVIay94ME8xTW5sajFJNFBnWFYwMzh6WjRwTmZlaFYwOVMwTEg4QkV3MzNTeE5IU3IyeWFNUi9MRDJ2V1JSMHdkY3RlNU5ZMkV3M0xXWFNUZE1MMDE5NUVYMUszM3Q5YW5iU3J5UlZqZUtGYXR1MkxtV2hCdU1nVjRrUHByUGJ5M3JWd0NjbnVXdFh2bnBvNnYrRUxQWVZiYlpzYXYybVFDNnhpTjk5LzN4Y0oxN3ozbG9WZFBYL0pKaWhCeHRwcXRRNUV4MFNtaEhqRmhiejdlaURaZTQ4cXZ6OEdISWRVZFdlaTQ4eHZRVWVSVEM2YXhId1l2eUE4dzhvZWFwTWNwRU5QWGZ4U2dsczQxSWZ6VDNsdG1sOGl6ZUMrT0Qyck1SMk8rU0U4cDJPWDVDTG1VUEhPTVB0a1JQRnNyN0JHek5zYnBVaG9iM25pMXpaZFcrdTMvazZGZXpVanNpQy9ZSWplVFB0MTR6WjVUMmw3ZkxnR0Evd0tjOHBNUUsrRnRNVGl3ZEEzMzJ4cDFUTUtXVXUiLCJtYWMiOiIxZDVjNzU1YjE3OTEzNWRkNzFhYjg2MjA2Yzc2NGJhMWY1NDNlYzBlOGU0Yjc1YzdhODhiOTI3YjUyODdjOGJjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZEcjk0TlFaMC9CKzZIZzJaSDVyaHc9PSIsInZhbHVlIjoid3dycVgyRW9oSElWZmxPOFRuUmFHSkpndlhKTjZLVjgxVXUzZnZSeE1lOTJiSG9DUjNtUjVhdUtyQmNFbWRFaWltSlN1emg2cllncUZNaUd2dXZrbmtuRTVYZlZwNmY2ZnpzTWlaYWdsa3J5Ymhrb0lQUkFRa0RMdzN1ZVVqVjhvQmFrdFJWcStyUnQvNG1CT2gzeFBZRWV2c0pmYUN0bHBzWUQ2ckZtbk5hMXAxL0I5Z3ZIUWEvMDQ0N1gxZ3F3ekVLbkEwNzg3WWo5ZnZiMkRxSUk1WGVFTmFVZ0NNM3pVRmg2alg1UzVDbm9jODN6ajMyeFpnbS9RMmNITCt0M3NJbXJlaEcvc2pZckk0amRTMzdtK2xWWXY1alhVanozZE9LaXRvZ2QycERFOFRhclk3eWdjNHo0Y0tHa0pvb0h3RUh5dkEwTmdlcVBvcTI4NTVVSEJ0aVlQWXN6K1JDQjdYUXpsczZhVzlYKzRSQzdQVHhzRnBoRWdBa2Z6QzN0dGVYVHd2RExKR1RjZTJVTVFOUk85ak9qMUQ3MVNyVWlZNVZ5cTdtekJvdGN5L2RKL2NGRGlTTzRYQ2FKMDBDQ043Wm9ERDNYS25HRldoWlRpcVVrbUlXTE9rOVErc28vYXpWdFpaMm9RTFdrRnVpeGdOUVdCdFJjTWRHL0ZUaU0iLCJtYWMiOiJkZjAxZjM0ZjljYTg0OWQ3NDM1Njk2NDUyODQxZTViOTJlOTcxOTEzYTM5OGZkNTZkNTgzZjJhZmQ2NzNjZmU5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Iis5SE5tZDFBbC93d1dHdlNBa2pod0E9PSIsInZhbHVlIjoiSFdLbGx2eWxveDVFTHdsME9udm90aUFxVlJVSG94ZlNvUlY4MDVvd0hKbkpMT09rUmZ2bWViSThiRHVIb0dYWkxhM1UvQ3UrZHcwSE91U2xoYVNGOWRneXVMM3FXWE5Pa3EvSjQxVDBjZHpYQWVIay94ME8xTW5sajFJNFBnWFYwMzh6WjRwTmZlaFYwOVMwTEg4QkV3MzNTeE5IU3IyeWFNUi9MRDJ2V1JSMHdkY3RlNU5ZMkV3M0xXWFNUZE1MMDE5NUVYMUszM3Q5YW5iU3J5UlZqZUtGYXR1MkxtV2hCdU1nVjRrUHByUGJ5M3JWd0NjbnVXdFh2bnBvNnYrRUxQWVZiYlpzYXYybVFDNnhpTjk5LzN4Y0oxN3ozbG9WZFBYL0pKaWhCeHRwcXRRNUV4MFNtaEhqRmhiejdlaURaZTQ4cXZ6OEdISWRVZFdlaTQ4eHZRVWVSVEM2YXhId1l2eUE4dzhvZWFwTWNwRU5QWGZ4U2dsczQxSWZ6VDNsdG1sOGl6ZUMrT0Qyck1SMk8rU0U4cDJPWDVDTG1VUEhPTVB0a1JQRnNyN0JHek5zYnBVaG9iM25pMXpaZFcrdTMvazZGZXpVanNpQy9ZSWplVFB0MTR6WjVUMmw3ZkxnR0Evd0tjOHBNUUsrRnRNVGl3ZEEzMzJ4cDFUTUtXVXUiLCJtYWMiOiIxZDVjNzU1YjE3OTEzNWRkNzFhYjg2MjA2Yzc2NGJhMWY1NDNlYzBlOGU0Yjc1YzdhODhiOTI3YjUyODdjOGJjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540097460\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-113515968 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113515968\", {\"maxDepth\":0})</script>\n"}}