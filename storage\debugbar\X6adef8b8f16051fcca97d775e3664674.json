{"__meta": {"id": "X6adef8b8f16051fcca97d775e3664674", "datetime": "2025-06-28 15:43:23", "utime": **********.971345, "method": "GET", "uri": "/financial-operations/sales-analytics/product-performance?warehouse_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.51562, "end": **********.971367, "duration": 0.4557468891143799, "duration_str": "456ms", "measures": [{"label": "Booting", "start": **********.51562, "relative_start": 0, "end": **********.872372, "relative_end": **********.872372, "duration": 0.3567519187927246, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.872382, "relative_start": 0.3567619323730469, "end": **********.97137, "relative_end": 3.0994415283203125e-06, "duration": 0.09898805618286133, "duration_str": "98.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46280352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/product-performance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getProductPerformance", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=493\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:493-600</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.042, "accumulated_duration_str": "42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.908418, "duration": 0.01509, "duration_str": "15.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 35.929}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.931761, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 35.929, "width_percent": 1.333}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` as `category_name`, SUM(pp.quantity) as total_quantity, SUM(pp.price * pp.quantity) as total_revenue, COUNT(DISTINCT p.id) as order_count, CASE\nWHEN ps.expiry_date IS NULL THEN \"لا يوجد تاريخ انتهاء\"\nWHEN ps.expiry_date <= CURDATE() THEN \"منتهي الصلاحية\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN \"خطر عالي\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN \"تحذير\"\nELSE \"صالح\"\nEND as expiry_status, CASE\nWHEN ps.expiry_date IS NULL THEN NULL\nELSE DATEDIFF(ps.expiry_date, CURDATE())\nEND as days_to_expiry from `pos_products` as `pp` inner join `pos` as `p` on `pp`.`pos_id` = `p`.`id` inner join `product_services` as `ps` on `pp`.`product_id` = `ps`.`id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` where `p`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` order by `total_revenue` desc limit 10", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 536}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.935598, "duration": 0.02475, "duration_str": "24.75ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:536", "source": "app/Http/Controllers/SalesAnalyticsController.php:536", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=536", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "536"}, "connection": "kdmkjkqknb", "start_percent": 37.262, "width_percent": 58.929}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` as `category_name`, `wp`.`quantity` as `current_stock`, DATEDIFF(ps.expiry_date, CURDATE()) as days_to_expiry, CASE\nWHEN ps.expiry_date <= CURDATE() THEN \"منتهي الصلاحية\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN \"خطر عالي\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 15 DAY) THEN \"خطر متوسط\"\nELSE \"تحذير\"\nEND as risk_level from `product_services` as `ps` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` where `ps`.`created_by` = 15 and `ps`.`expiry_date` is not null and `ps`.`expiry_date` <= '2025-07-28 15:43:23' order by `days_to_expiry` asc limit 15", "type": "query", "params": [], "bindings": ["15", "2025-07-28 15:43:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 566}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.96204, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:566", "source": "app/Http/Controllers/SalesAnalyticsController.php:566", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=566", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "566"}, "connection": "kdmkjkqknb", "start_percent": 96.19, "width_percent": 3.81}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/product-performance", "status_code": "<pre class=sf-dump id=sf-dump-189516845 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-189516845\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-186746695 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-186746695\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1255106869 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1255106869\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-826458085 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125396906%7C17%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImMzRUFxNWtKaWt0V240S2p0c0FISHc9PSIsInZhbHVlIjoiL1NZWU5ROWhLaTAybmVmazh2VkFjK1FTUjhMTlRJbjRBTEJYaG54elNETHk2bld3bnRKNUxYSlBaQVZXOGc5elZnOVNWZkhEd1VFMDF1UEEvY0VpUFBnOEJpL0dGNDhtaGUweGxRS0JFODJXYXlCa01EbzV1WWJ0UmpPVEJzQUd6VHh2b0xQaWl1dnZWN0wzU2JUVHB3TEU3NjhpS2liMmxsc0t6Q1M3L3BNdnNLNW9aZ3VFeXN5aTVMTGZXMTJJR1NHUW1xdE5XcWRXRzllYXdsU2Y1VFVWWXZhNndQckpqYnpNN0tzR29XM0FvMXlwMmFaalBhNGhvNWxscnJkVkN2L2VBREs2OS84TTVZOENlazYwSGxET0RiTEpvY3JuVHo3aUIrYWl0eWo0VmF6Zi9EeTRLazdsUHdrQ00zMlFNUGJpMnFjNFlFYzZVZDc0eXoxU3U4amEvcndiZDlJR0E0RmhnM0l6dFhWVThIa0dsSDM1bE1HR1NHOXJwcEtkSzFDZUhPWDBUWEJBTjNWclV3eU1QT2FHdG52eDVBZ0NUMXhpZnM0L1dkOUVqRlhQUHkzU3BZeG9lODViQlo1QVlTWnoxVzJNMEpLckI4TFpvOXdFZXFXWkhhNS9RTVBrdGxmZlBpcVh6RW11cVIwZVZWbUFYYXpEWDlGelBYVDkiLCJtYWMiOiI0Yzc4MTZlNWY3YzJjYzE5YjBiZTdlMDlmZmZiMDI0YWUzMWJiYmI0MDc5ZjE4ZmIwNmRiYjM5M2QyZDJjYTNkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik81bFd4Vm1qSjJOWlFtSGNGay9GT3c9PSIsInZhbHVlIjoidlBrcXBOalFFUGtSbDBJc1VWNFU0R3ZsMWtPMHhtcnM3VFJUZFcwVC9ZVEVlQU5zNkU3Q1FVODlxbm9qTjN2UTB1Y1dyb1RSazNMR0ZjRzFra1U5RkxoaTdOYit2VDN3RUR2RlE4M2M4SXlZYWpLOGZ4S2FRYlpDYVFmVklZc3ZRckY2OGlIdzNVTWdqaHJFR2lCZTh3d0FFWnI1QW5nRHZ4N21LS3ZhU0p3ZkJtYkI2eGhqN3FZSUdoL09DbktGQUFjNkVzMXZUSm9ReWdReGljNzV6V20yTWxJckwrMjlSZDVEbXVmc29zbHhLTzZVK0FFa2d0a0pISFBuZ2dkelV4NGVGK2FjRUJpck9NVTVWUXB1WjF4c0w2blFZYjNqR2xmK2hlTmpWeHB5bkpRRHFKZmJpZm1IdVZiRjUwTmRKekdkd2V1U0ptanBQSFRIYnMzR3BjWEdqV01nM1NkUWN6ZDBTSDhVSlRwZEZuUFgwL2tuQmxjZll5bFBzWWdEVWllZUFtMUpKOWlxbEpOYXVVZHMxZiswd1ZKU2VnQmJ5eElKVU1vcmYzL1lYRWQ2UFBnbjFMYURBRUF3WHhTd1EwOW1CMThtS0N3T0pYZFVVdzlKQTljTGphN3lYampLTFpLRzYxVTBGNHR6VE90TWlocERXKytFU1FrbkpCeEUiLCJtYWMiOiI4NWUyMTRkNDAyYjEyZjU1ZmI2ODEwZmExYjQ3NWIzYzU5M2I5MjA3N2FjM2VmOTQ5NDk5YjBiNjBiNGE2MjdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-826458085\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1495918240 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495918240\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-184089874 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:43:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlMydjF0RnZGTXdiZXhhYS9CNkgwTFE9PSIsInZhbHVlIjoiVW9Yb3RXWFMybk1RMXdMZ1FzeVIwanBBMU9zWEo2RzJ0bGhDbUx0Q2xPVGVJVjl0b05NZEljNTdTb0hTMDdtd0FCSnp5aUZPYjlJQmk5MkxGSTRQenJMSElHMDJ5ZUNTa014NmN4dUhmbnFOSkpUMjlLYTlxTVpLMk9vZDQ1K0FrT1JjMDFqN05aeUhraSsvSVFjb3RmeWF5bzJ5ZjgzTzBZS2NLSDMrUGVybVRyNFVwbHRyZzc4SUowazZ6dGhrdXFKR2tXMDJBTWh6YzFoVTN5cTdxM1lnTHliVEVCcmgzYnZMWklJZjN2VUxtc1JzTHVTU2hMcVpTT3VWOWMveFpPRWdmNk4rOHo3L3d6OW5NeFNIeWpBWkgxSGJTMG5Xd0lJbUI0ZXYrRjVBVm9GUnl1blZkWllDekUwK2RGQ3hDUTBXK3EvQVV2OFAyRFpxRzJYaVQyckdmUGdLK3M1RFd1dDZpL2lTU05iNnZJQTVjY1c5cW4zMUhMcWlCQnFOMjBkTW5tcEx3UUVrYnhKYnpWVUU4QktTSnR0bmNKTmx1dG9XbkVlRGVNYnJiTGpCWTgvbHIxWGQrRnFCam1Fc09tdDlhM1lBb2FzUHVHSlAzTzNpZGd1ZlNBWjdnUXMrRlhvalhxT0hvRjdlZENSV25CSUpkUzBuMXplOUFvRk4iLCJtYWMiOiJlOGNhMGJkNDcxYmYwNGNhNTlkYTQyYWQ0NmE2MWFkYmRhODMwNmFiNzQxZmEzYWY2NjY3ZDM1YjYxMDZmMzlmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRhVEtUa2dJYWcxa1J0aC90VDcycUE9PSIsInZhbHVlIjoici83bUx5MnAxQnowS0JOU0xUNzBzZXN5OTY0VW4ycDNnTzgvTjdUd0VBOGhiYWZLMGJ0bzljWVFTNW1rdHhmK3hKV0pRdk5rN2hOcCs2L2twWlhSWDNiN3V0ZXNaRDA0OEZhZ1pHWkY2OHhzWE1tZDZOSXE1bDZqeHlFWFh5RjBFMFFBN3VJUDRxMm9YaFJTVU9SQVpzNUk5SkVKQUFWZ01RZ2VpL2w5ZDU0THluTzZqUnNacXdUS29qQmt6d2VWNFlLZGJlYVY2clovTHY5aHc5a3Q0Nk11ZHdVUVh1R2pMSi9pL0tpQ092RmtQelNOaGhaR1BhSlByemhsbklHT0tCbGlET0VwZGlQK2F3amdSbXpRUyszT2Z5MW44VExuU1lXZnJQa2RLeUswKzBQRVZCTkpvdzErNzBrSndLdFQySGdZVjJwYnlyZCtkYmhOTGh0VkVKZURZQ004cXNIZFl3VDZuTzNhZjRKNWQ3QmsvNXJqUTA0M0hPY0loMjhMSVFXT29IblFsaG5lSXdwWjBrMXRJb1NzblZid3BXRWFMN0VJVWdoL2hvNFlPZzdEOEttWkUzUG91cjVGSzA3TytldWNYTFQzVnV4TC8zTTJQTHlGUWFTdFoxUy8vYkF2aWFTQjN3VjgvVUNNNkp5OFdabUNjL2c0VXVLVjFrbkgiLCJtYWMiOiIwMDg4Zjk3OTkzYjU2ZGRkYjY2OGM1NzNlODE2ZjYxNTdlYTNkMjlhZWM5ZTAyNTUwOGQxYWNjN2RiOWE1ZjIzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlMydjF0RnZGTXdiZXhhYS9CNkgwTFE9PSIsInZhbHVlIjoiVW9Yb3RXWFMybk1RMXdMZ1FzeVIwanBBMU9zWEo2RzJ0bGhDbUx0Q2xPVGVJVjl0b05NZEljNTdTb0hTMDdtd0FCSnp5aUZPYjlJQmk5MkxGSTRQenJMSElHMDJ5ZUNTa014NmN4dUhmbnFOSkpUMjlLYTlxTVpLMk9vZDQ1K0FrT1JjMDFqN05aeUhraSsvSVFjb3RmeWF5bzJ5ZjgzTzBZS2NLSDMrUGVybVRyNFVwbHRyZzc4SUowazZ6dGhrdXFKR2tXMDJBTWh6YzFoVTN5cTdxM1lnTHliVEVCcmgzYnZMWklJZjN2VUxtc1JzTHVTU2hMcVpTT3VWOWMveFpPRWdmNk4rOHo3L3d6OW5NeFNIeWpBWkgxSGJTMG5Xd0lJbUI0ZXYrRjVBVm9GUnl1blZkWllDekUwK2RGQ3hDUTBXK3EvQVV2OFAyRFpxRzJYaVQyckdmUGdLK3M1RFd1dDZpL2lTU05iNnZJQTVjY1c5cW4zMUhMcWlCQnFOMjBkTW5tcEx3UUVrYnhKYnpWVUU4QktTSnR0bmNKTmx1dG9XbkVlRGVNYnJiTGpCWTgvbHIxWGQrRnFCam1Fc09tdDlhM1lBb2FzUHVHSlAzTzNpZGd1ZlNBWjdnUXMrRlhvalhxT0hvRjdlZENSV25CSUpkUzBuMXplOUFvRk4iLCJtYWMiOiJlOGNhMGJkNDcxYmYwNGNhNTlkYTQyYWQ0NmE2MWFkYmRhODMwNmFiNzQxZmEzYWY2NjY3ZDM1YjYxMDZmMzlmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRhVEtUa2dJYWcxa1J0aC90VDcycUE9PSIsInZhbHVlIjoici83bUx5MnAxQnowS0JOU0xUNzBzZXN5OTY0VW4ycDNnTzgvTjdUd0VBOGhiYWZLMGJ0bzljWVFTNW1rdHhmK3hKV0pRdk5rN2hOcCs2L2twWlhSWDNiN3V0ZXNaRDA0OEZhZ1pHWkY2OHhzWE1tZDZOSXE1bDZqeHlFWFh5RjBFMFFBN3VJUDRxMm9YaFJTVU9SQVpzNUk5SkVKQUFWZ01RZ2VpL2w5ZDU0THluTzZqUnNacXdUS29qQmt6d2VWNFlLZGJlYVY2clovTHY5aHc5a3Q0Nk11ZHdVUVh1R2pMSi9pL0tpQ092RmtQelNOaGhaR1BhSlByemhsbklHT0tCbGlET0VwZGlQK2F3amdSbXpRUyszT2Z5MW44VExuU1lXZnJQa2RLeUswKzBQRVZCTkpvdzErNzBrSndLdFQySGdZVjJwYnlyZCtkYmhOTGh0VkVKZURZQ004cXNIZFl3VDZuTzNhZjRKNWQ3QmsvNXJqUTA0M0hPY0loMjhMSVFXT29IblFsaG5lSXdwWjBrMXRJb1NzblZid3BXRWFMN0VJVWdoL2hvNFlPZzdEOEttWkUzUG91cjVGSzA3TytldWNYTFQzVnV4TC8zTTJQTHlGUWFTdFoxUy8vYkF2aWFTQjN3VjgvVUNNNkp5OFdabUNjL2c0VXVLVjFrbkgiLCJtYWMiOiIwMDg4Zjk3OTkzYjU2ZGRkYjY2OGM1NzNlODE2ZjYxNTdlYTNkMjlhZWM5ZTAyNTUwOGQxYWNjN2RiOWE1ZjIzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184089874\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1434722639 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434722639\", {\"maxDepth\":0})</script>\n"}}