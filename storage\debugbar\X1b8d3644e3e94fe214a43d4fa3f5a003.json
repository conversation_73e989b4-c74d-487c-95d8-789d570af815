{"__meta": {"id": "X1b8d3644e3e94fe214a43d4fa3f5a003", "datetime": "2025-06-28 16:21:45", "utime": **********.210174, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127704.722467, "end": **********.210194, "duration": 0.48772716522216797, "duration_str": "488ms", "measures": [{"label": "Booting", "start": 1751127704.722467, "relative_start": 0, "end": **********.143427, "relative_end": **********.143427, "duration": 0.4209599494934082, "duration_str": "421ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.143437, "relative_start": 0.42096996307373047, "end": **********.210196, "relative_end": 1.9073486328125e-06, "duration": 0.06675910949707031, "duration_str": "66.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45700896, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00345, "accumulated_duration_str": "3.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.177749, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.406}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.189498, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.406, "width_percent": 15.362}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.195221, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.768, "width_percent": 16.232}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1776171109 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127660348%7C43%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkV1ekdLYWM5MUlkb3VhOGlSc2pxK2c9PSIsInZhbHVlIjoiSGYxL3JVRUk3TUkzMGQvWjlHd0N0TlVPaXNaSlNOZXQ3RklTa01aaDRERzVCZ2R4MTR6QnZTd0ZRbmo1NXBKTkIyTFV2dTVITzl4RjY1TG1HNElLaUdrWG5MTkhVenhnaXl1QzhPQnNtRjhhWmJkdGRORDkza1F0a0MyV3NSUDBjMDJwTVlYdWN3ZXIxYlJMdng5WSt0WHB1R2hXZUpKQlExS2xpcitCa2FldjNqOTRTcFA3N1ZyY3JKbzErMDJFWlUxTHRKZ2pPNmtacFJ4NG8yQmdvRW1CRXB0VU5JTEpTdFd3Tkl0OGZldlBsbmpIdm9aa0RuTlR5eU1vQjZoNnFCTUtMSW9CZU1Jc2FHVXdDVWVTODJya1lLd3E1dVl6Tk15bG5NcFhRK3daTFZFRzRhR0ZmQjMrNnk0NzJ4ZVFZR1JVaDUzYjB6eCt4YzhtVXdaMUFPYm0rdEU3VkVvWW1zMHhTNS9zV1lwNjdaY0g3UUFQd3ArakhUMFdQQU1jN0NzNkoySWdVbTloQytPdGUzUnhDYjdMc0x2emxPSW1Ud0U4SSt4dmk0K0ZPKzdablRnU2N0bnAxWFAveWxKdW16UFFUTnQrejY2Z1UrVjZTdHFCV0Jtd2ZFNVorcU1kQUdoZnRUdk9lc3J4YVNEQmEyODdHQkQzYi9wSExwNUIiLCJtYWMiOiI0MDJkOWY5YmYzZWEzZDk4MTY5YjFmMzBiYjk4MDk0MDI0ZWRkOTRkN2JhNjVmY2Y0NzI0ZTdmMjA4OWNmMDExIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpMS2IzREZTYTJTT1Zza1o4RnpGZ0E9PSIsInZhbHVlIjoiMlJaR3ZDYXpyd1cwM3h2dU9WYi9OeU9aWjhjK1hkQUFBK011MS83RVNQbmE1ZjgzcUkvRW5ZZEpnQ1dEeGZ0bThGQ0RmbEdoL1d6ZEh2Z05xT3RMblZhYUlKVU5WbUh3MTJKMVplalN1bDh0NFR2VHJCSWZzeThjUzgrVlJNcE5zbGV1aWhncVp2MFc4Y1YrL0NyVGVuQ0RuSWJYS0ZZY29ReWdqNTJLaUQ2NktyWlVnbzNJUEltak42Y1o0M0p1ZDRIU1R1K0lsRU5KN2J5NkJXKy80WE12ZjErdTYyN1lqU2J5UTZERnFKMXc0bUdxd2M3WkVxbFZJQjNkL2FYcEtQUTdhVmM5L0pzbndpR2c1anpWeStVU0xzMk55QmFRcHlGaFFWQVZGZU1DSjRhN1Q2MUphSjhZeUNGcmZscityVEJJVTFYVW1FSUZaUnd6QnBtMnBtZUxsNnNOREpzeWNGTjUyanpRRWl0Y3JTL1hvbHlXMkk0ayt0M2JvZmFDNENVMTVRMEo0N3M4eWJReU5IdkFWR1kzZE1DaStLdzk1eklETUljeG5KSkFvZEMzOG9RZGJ1SzZRNC9aaW9ldHhoNGpxTzZ4d1pVWWJqdU9ZU21jbDBwK084bTFKQ3d3TkIwMnp4YVVUKzVjMXJpSDlFd1RQdWh1alE0OS93M3MiLCJtYWMiOiI5MDUyYTg5MGIwYjBkODE4ODRlMzg3ODllMTk1ZTg2NmQwZDQwYTBjNDkyN2FkNjE0ZTg3MTMwZTA3NTA1Y2NhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1776171109\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-165016342 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-165016342\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-831897654 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:21:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhlQUsvV1dla2h0cERadFFmNWduUXc9PSIsInZhbHVlIjoiOXppSkRTaE9uVmE3YWtOY3JlUTJCWUxlTTdSUmwyaXo2OU9OWXNTNk53RXV5azFzVUt3TnlIU0xUamtzM2ZZL24vKytzWmtnVmF4UlB0TDdnYTdaM05Db2hCc0dvSHhOOXZUa2JUbWNZL3VQc1ByQ3BqaHBPbU5sQ0YxM28xL3RxSzVHaDFaN2hTUGZobW9CczdzdkZvM1dtYU5EUDlaTkFIOVNqYms1cHRhdzdhclRYSmZsVVdXcEd3WmUwb2FzazNta3U4V3pUOHVnM2hRSWRPTXg3ZjZrcTc0SnltMHEyQ2pFUDJ2SGErMThrWEZNcXd1K0hldmQ0QlhRQUxPcmk3UzVHRkVieW5vbEQxT1dwTjNQUjVBRHozdStOQ0ZyeXlPT21mS0xhTC9OamVPVjNpK1ZYLzE2Ymx0ZlgydHZrQ0k4aVZZNE8xVjZSM0hoaEkzS1JkTGVyTUZXTWFZeEpMU2RZL1NiUW84YTB5SDlIOWh0QkpibVV3cHE0K09lRUxlRHlaZ1RhbHpWZFRyMktNOEsxNEFsY1U0MThLSXpnR2lJMVJpSkhhU3hxQmhkb09odys0TjBQV3p6Y3hUS3lkVGV6Slk2cEZ0RTB2OWdjOENSdktNN3BZalQ3bisxdUFpTWU2N1dpVFdhTWpTTXJ3Q1VXem9LY1d2NUVvRjAiLCJtYWMiOiJkY2NmODEzNzFkZmIzMGIxMjA5MjdhYjViYmRlYWQzZTc5MDkzYjA5YzgyMzg4NTEyN2ZkODc4ZjZkZTI3OTgxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJtcjgwZmxZS2pEWDZRb0VBS0w2Nnc9PSIsInZhbHVlIjoiQkpCeUxCeGYzeFROcEkxR1dHQWI0MXdPUVdyS08vTHhlcjlGa25iYkVpQ0lYT2hZdndiV3ZhRTVqYWZGUzVYQWZmc1dzUGlHckZxUXZwSTk3QWVNUHA4YlBSRmRBUlROWFBrb3dKcWwxQ0VFOFFHeW03RjlKUENjczRiQzB0Z0g5bzczNkRuQWdxd3Y4b0o4ZHRvR2xWZHcxdzlaemczbXdpZE8vaXQxbHg4WHhOMk9DVzVvUGhUb1o5cm0xZkJNZnM2WGZkNldGVFd4TlZpM2luY2NoTmlIOFRpRlVrY0ZpREozTlhsZmJ5ODlwcGVUbGdjd3dHajlEOFZoNjRvMW5pc1RIcEdna1NUVENEdXRqcjVwalpmZWVDOEhDeXFQYkhCTnZIOEljTUhyVW00OUlWRTJJVm5NYUgzdmJtWFl2cUdaRlFoUVRYb2U0d05sQlRlZzhiMUp4RHpId3RiYjJJSE1xSnc5ZmtRcWw1ZmNJZWNLbjBMc1paY1UvcWVFWW96bkNuVUM3QnJNZjY0U1FRUTNMZEcwY0xCcU56alNZeFk2NkRGREIxQkZzQVZORVIvK2VrM0MwZU90ODVhNDUxMjI3MGhSQWsrUUkySDBrQmxJUDNWeWFoWUxBT1ZCMlFsaW1JVWVYUHNGN2svSkZlVERxWldFemFJOGpONlMiLCJtYWMiOiI0OTMwNjE1ZTBkMjc3Yjg5NmFiYjM5MDExMWQ4OWIzZGFjMTljNzljNzI3YTZlZDQxNDgwMTg5NTI2NmNhMTUzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:21:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhlQUsvV1dla2h0cERadFFmNWduUXc9PSIsInZhbHVlIjoiOXppSkRTaE9uVmE3YWtOY3JlUTJCWUxlTTdSUmwyaXo2OU9OWXNTNk53RXV5azFzVUt3TnlIU0xUamtzM2ZZL24vKytzWmtnVmF4UlB0TDdnYTdaM05Db2hCc0dvSHhOOXZUa2JUbWNZL3VQc1ByQ3BqaHBPbU5sQ0YxM28xL3RxSzVHaDFaN2hTUGZobW9CczdzdkZvM1dtYU5EUDlaTkFIOVNqYms1cHRhdzdhclRYSmZsVVdXcEd3WmUwb2FzazNta3U4V3pUOHVnM2hRSWRPTXg3ZjZrcTc0SnltMHEyQ2pFUDJ2SGErMThrWEZNcXd1K0hldmQ0QlhRQUxPcmk3UzVHRkVieW5vbEQxT1dwTjNQUjVBRHozdStOQ0ZyeXlPT21mS0xhTC9OamVPVjNpK1ZYLzE2Ymx0ZlgydHZrQ0k4aVZZNE8xVjZSM0hoaEkzS1JkTGVyTUZXTWFZeEpMU2RZL1NiUW84YTB5SDlIOWh0QkpibVV3cHE0K09lRUxlRHlaZ1RhbHpWZFRyMktNOEsxNEFsY1U0MThLSXpnR2lJMVJpSkhhU3hxQmhkb09odys0TjBQV3p6Y3hUS3lkVGV6Slk2cEZ0RTB2OWdjOENSdktNN3BZalQ3bisxdUFpTWU2N1dpVFdhTWpTTXJ3Q1VXem9LY1d2NUVvRjAiLCJtYWMiOiJkY2NmODEzNzFkZmIzMGIxMjA5MjdhYjViYmRlYWQzZTc5MDkzYjA5YzgyMzg4NTEyN2ZkODc4ZjZkZTI3OTgxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJtcjgwZmxZS2pEWDZRb0VBS0w2Nnc9PSIsInZhbHVlIjoiQkpCeUxCeGYzeFROcEkxR1dHQWI0MXdPUVdyS08vTHhlcjlGa25iYkVpQ0lYT2hZdndiV3ZhRTVqYWZGUzVYQWZmc1dzUGlHckZxUXZwSTk3QWVNUHA4YlBSRmRBUlROWFBrb3dKcWwxQ0VFOFFHeW03RjlKUENjczRiQzB0Z0g5bzczNkRuQWdxd3Y4b0o4ZHRvR2xWZHcxdzlaemczbXdpZE8vaXQxbHg4WHhOMk9DVzVvUGhUb1o5cm0xZkJNZnM2WGZkNldGVFd4TlZpM2luY2NoTmlIOFRpRlVrY0ZpREozTlhsZmJ5ODlwcGVUbGdjd3dHajlEOFZoNjRvMW5pc1RIcEdna1NUVENEdXRqcjVwalpmZWVDOEhDeXFQYkhCTnZIOEljTUhyVW00OUlWRTJJVm5NYUgzdmJtWFl2cUdaRlFoUVRYb2U0d05sQlRlZzhiMUp4RHpId3RiYjJJSE1xSnc5ZmtRcWw1ZmNJZWNLbjBMc1paY1UvcWVFWW96bkNuVUM3QnJNZjY0U1FRUTNMZEcwY0xCcU56alNZeFk2NkRGREIxQkZzQVZORVIvK2VrM0MwZU90ODVhNDUxMjI3MGhSQWsrUUkySDBrQmxJUDNWeWFoWUxBT1ZCMlFsaW1JVWVYUHNGN2svSkZlVERxWldFemFJOGpONlMiLCJtYWMiOiI0OTMwNjE1ZTBkMjc3Yjg5NmFiYjM5MDExMWQ4OWIzZGFjMTljNzljNzI3YTZlZDQxNDgwMTg5NTI2NmNhMTUzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:21:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831897654\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-53596177 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53596177\", {\"maxDepth\":0})</script>\n"}}