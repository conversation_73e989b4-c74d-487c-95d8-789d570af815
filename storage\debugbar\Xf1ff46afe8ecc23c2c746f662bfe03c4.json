{"__meta": {"id": "Xf1ff46afe8ecc23c2c746f662bfe03c4", "datetime": "2025-06-28 16:02:24", "utime": **********.412254, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126543.981631, "end": **********.412274, "duration": 0.43064284324645996, "duration_str": "431ms", "measures": [{"label": "Booting", "start": 1751126543.981631, "relative_start": 0, "end": **********.351554, "relative_end": **********.351554, "duration": 0.3699228763580322, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.351567, "relative_start": 0.3699359893798828, "end": **********.412275, "relative_end": 1.1920928955078125e-06, "duration": 0.060708045959472656, "duration_str": "60.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45698032, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00264, "accumulated_duration_str": "2.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.378094, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.318}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3887072, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.318, "width_percent": 17.803}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3941891, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.121, "width_percent": 12.879}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "[]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1147267944 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1147267944\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2004979815 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2004979815\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-77989350 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77989350\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-494993434 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126540952%7C11%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImU5SWlXY05teXBNQkd4M2xHaERORFE9PSIsInZhbHVlIjoieWVxaDNjYkJmU08rL09aWkg0M1dmMUd2S1RxTXRZU09PRXBiNXJQeFkvS0R3VU85OVYzQlBSRmhoamRMSE1NbnFZSXI4ZUJyWi95MXNHeHp3VnNpWkp5UEd3VmhIUWpqWGs4RkN4VGREZFdpSXBGdS84dmRhcEtmQVMxVGxMb01vUXk3V3pxUWFQTTV6TURnbUhNc05zK1krVmNlMmFFczFuRjVhajJrcGNVZ0tnVk52eUthSXVaQllrMTJhY1J6RGhoVDJwcklWTWw2YXhuUEpqY0FWcHN6YzV1YmFweDhkZ01wVnZWeUhha2E5L2NMaGZNa0tyTzg2b3BWU0Q1a2o2Z2txK1FEYmJPVDV6KzByYmNTV09KZy9ETStFck1LV2pDQmhMd09FN2VvUm9VSTNuOU1xbUxxWVVuT2kxNXY5ZmNNY3I5c0h5Y2YyN1BKNTNQcXdnZDlRV0VEZEQ1UGNjaCtpZkdIZTU5ZHVFckEwakxPRWIvUExUV094SnBwT2RtKzdqVzN5ZlpOUm8zN2ZZWldJS29xSVBlUHVyRWIyUk9wUmVJRldRN0tkVTlyamliQnowUkxlOFdwY3UzSURBOWdnZHVxMlNRS2luUGNTUVdoU1g4aDR4Rng3SmZNSTFsNGt2dWFIQXkyUG9Wc2ZxYXN0eFdWQTkzOXB1NkYiLCJtYWMiOiIwODI4MTYyODNkN2I2Y2MwYmE2MjZmZjFlZTAzZDcwZTVhYjlkYjc5YzMwYmJkN2Q2MmUzZGVjZDJjZTNlZGMwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkU4b0RMcHdMVUZlK3R3UVBrYThlYUE9PSIsInZhbHVlIjoiUmhmNVNtMHpMUnZTRWliSkwrVzRSM0k1SEgzN2RKeG14YWRoNi9Sd2kwdFpWV2o2NFgxS0FYOVBqeG85aXdJa1BaVVJhb2ZtckY5Z1dyczlSeUFXUE93Y0FtZUtyZjhWMTFPYnljWUdBTFY5dGJpRkNwNGpRblpqSkdxd1poMlVuOFJxQjJ2RU40YTNBUytsVW1KQ0Q0bTNKd2Z2WVY0dHF6UHdncU12NWdaSDlsM2RHcVFtVDQ5M1k5aU4zYnVHMFBKcERTQW5BYk0zdktpWG5lR1grQUhGN2dvZHRWb0dnMTdwcUg3YWFZbTQ5eDBKNk00OEUwdmd6NjdLRUcyaEhHZzF5MWEwTDNyYTR0NDVvTHBDckhlUFpPSHJZTzAxcVJITWZUc1hkZUZYaElZbUx1ZXhvNExLaitJbncxSkExb080S0tNc3p4WG4vZGN5OTZLK2Z1cHlNY0lZeEFGeVhuNGlYb3BlUGtjU1JPV1NLTXRvcEdRcWdOTzd4NDJhREMzVWNDNGh1MzZyUzJ4VStaRExmM0kwMGMvOWovVWRiemNIZFE2OHhoRjh5NkdxaGU2UGFTMnZXS3ZoaDdRQ0dBYnM3S01OK1JDdkZQb0tONWYwTjJmcWFSR0FBTzFldStrVXpEcWY0MmM5RkNuajhUQW9nSnduVTZkTWdYYnQiLCJtYWMiOiJiZTVlOTVkZjYzZDQ0YjQ2Mjc4MDZmYWYzZWM3NzRjMGM1NzZhNzY1MmEzMzk0NGYyZTczNjFjNzBkMzQxYzU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494993434\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-453689252 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-453689252\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1731130779 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNCMnpORWRURW1vU1lRMVJtRmNJUWc9PSIsInZhbHVlIjoiVWMyaFR2dFBYaWpnMnU3K2RzaGJ0ZkZBWCtyekUxSnFYd3RENFBaREFad2REbVR3ckhlQ3dWNzg0UlJLK2pUeUNSbzBmZk9DTVJncVJGY1FiRXVmNitqcTlYQ2M4YjE5bThHRDhXQ21JaEJlRmFKT3dEdllQV2IwUVJnN1phYVF5YWcrbDd0TmNEMFU4OXBGa09vRTBNWTV6SVQ2bVljbVN1UkhhSGRHTVdVUS9CWUQ2VndITTRjaVo4TmtXbk1RUXcwNGRoR1FUMVVMenJGQW9FYmZSdHJ5Q0ZVcnNPakZOZjZXQS8xQlBBNHFLTmFZdkdEZDRzYXhDT2hBbzYwVFM2MHBtSVViSDE4bXkyKzdkamJtNXlNZVphN25PdTBGUWFOcWpjVVlOcUR4TVNaOTl5Q1N4OXUxc2szQW9kZTZXUzAwS09EN1pQeDM5MkV2TmFyQUthaXptUEgya2lwZXY1SktaSGtleFNMSTliMk9POVBRcnUrNmtUMHloYk4wMzh4UEVxWFl3QTlQWjRQTW8wQ2llRk1jYVc5WE9RTHFmQ0VvYzdZNGpXdGx6MzlSSEVDOUdWbnJyUlMyUFQ0MjgxS2pyQU1rd2JZOFNwbFJTMmZhNUlRenlIdDlpUTNIZ0ZzejlYMVlRd0Y4dlRScnlYaWhINlRGMFVXa2VIMHoiLCJtYWMiOiJiNDZiNjBkNjM4OTVhYjA2MjU1ODdiMWQ3OGU5OGU5OTA2MWZmYzE5MzA5ZTIwMTg0ODM5ZTM5MzgyZGU4NjQ1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikp5YUJkaXQ1SlVKdXptZHFNbVlycWc9PSIsInZhbHVlIjoiTnQ0amxiT0lsdUozTHQzVEU5ZkRXa05KOS9MbkR5d0lVc0VJNjRxNmNjbGo5Ykw1aFBsUVU4SFJvdEVydHQ3UkJUTHBXOHBnRUpyUExuVEl5NFkzYm1uTmhWaDM5NTFwN0dwbEFVRGZUbkkzbFdOWHFNMlN6MEIzaDVnWjVuQ0t4cUFvNVo5UHkvZ2ZBelVjSUhCRkNWSGY0RVpIM3JXUVhTR0VFQWNxemNaM0tVRWg3WGRnOTIwaWo1OENZbm5obWJOYm4vSW1WOTd5ZGRTaitMRW5IMCtHWVZzY0xWMWNxTGplcGlNeFZPQkRWL2Q3aG9LMlhwNGRwUmpyU0lONG9HZ3dtc2NEcVlMbDJMdENKNndMZC9xN1ZwS0E1ZFRaZzNVQ1h1YWFQb05qRGV2WEFPS3VCWlZLVUNKbEhLWHZJREtmMFVrVkl6M3Y4eDhFTFI4dmhjbFRQeUFFTk82WFNwekxvaW5oUVBNNjBSMitTYWVCemk1b3N6QmE1cmt5d1pUSHpsamdGNW1JaS83b2g1MktWS1dndzVUZ1p0QWxhUlZZYTY4a3NOcW1IK1FkVlNFb1dWSUFxcXFwWFdzVThXMHpjS29BNlJFU0NXVm5tbWNQdEVUQTF1ZzhGd0dsWnliWTJEQUE0MUtISWlZQ0kxQmNuY1ZuNE85aG94b0giLCJtYWMiOiI0NmM4MmVmOGIzNTczYjEyMGRhZDAyODJmZjViYjY2MmU1NWMyYTUxNjc1NTE1OTJhMjAxMGUyZTI3NjIyNzMzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNCMnpORWRURW1vU1lRMVJtRmNJUWc9PSIsInZhbHVlIjoiVWMyaFR2dFBYaWpnMnU3K2RzaGJ0ZkZBWCtyekUxSnFYd3RENFBaREFad2REbVR3ckhlQ3dWNzg0UlJLK2pUeUNSbzBmZk9DTVJncVJGY1FiRXVmNitqcTlYQ2M4YjE5bThHRDhXQ21JaEJlRmFKT3dEdllQV2IwUVJnN1phYVF5YWcrbDd0TmNEMFU4OXBGa09vRTBNWTV6SVQ2bVljbVN1UkhhSGRHTVdVUS9CWUQ2VndITTRjaVo4TmtXbk1RUXcwNGRoR1FUMVVMenJGQW9FYmZSdHJ5Q0ZVcnNPakZOZjZXQS8xQlBBNHFLTmFZdkdEZDRzYXhDT2hBbzYwVFM2MHBtSVViSDE4bXkyKzdkamJtNXlNZVphN25PdTBGUWFOcWpjVVlOcUR4TVNaOTl5Q1N4OXUxc2szQW9kZTZXUzAwS09EN1pQeDM5MkV2TmFyQUthaXptUEgya2lwZXY1SktaSGtleFNMSTliMk9POVBRcnUrNmtUMHloYk4wMzh4UEVxWFl3QTlQWjRQTW8wQ2llRk1jYVc5WE9RTHFmQ0VvYzdZNGpXdGx6MzlSSEVDOUdWbnJyUlMyUFQ0MjgxS2pyQU1rd2JZOFNwbFJTMmZhNUlRenlIdDlpUTNIZ0ZzejlYMVlRd0Y4dlRScnlYaWhINlRGMFVXa2VIMHoiLCJtYWMiOiJiNDZiNjBkNjM4OTVhYjA2MjU1ODdiMWQ3OGU5OGU5OTA2MWZmYzE5MzA5ZTIwMTg0ODM5ZTM5MzgyZGU4NjQ1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikp5YUJkaXQ1SlVKdXptZHFNbVlycWc9PSIsInZhbHVlIjoiTnQ0amxiT0lsdUozTHQzVEU5ZkRXa05KOS9MbkR5d0lVc0VJNjRxNmNjbGo5Ykw1aFBsUVU4SFJvdEVydHQ3UkJUTHBXOHBnRUpyUExuVEl5NFkzYm1uTmhWaDM5NTFwN0dwbEFVRGZUbkkzbFdOWHFNMlN6MEIzaDVnWjVuQ0t4cUFvNVo5UHkvZ2ZBelVjSUhCRkNWSGY0RVpIM3JXUVhTR0VFQWNxemNaM0tVRWg3WGRnOTIwaWo1OENZbm5obWJOYm4vSW1WOTd5ZGRTaitMRW5IMCtHWVZzY0xWMWNxTGplcGlNeFZPQkRWL2Q3aG9LMlhwNGRwUmpyU0lONG9HZ3dtc2NEcVlMbDJMdENKNndMZC9xN1ZwS0E1ZFRaZzNVQ1h1YWFQb05qRGV2WEFPS3VCWlZLVUNKbEhLWHZJREtmMFVrVkl6M3Y4eDhFTFI4dmhjbFRQeUFFTk82WFNwekxvaW5oUVBNNjBSMitTYWVCemk1b3N6QmE1cmt5d1pUSHpsamdGNW1JaS83b2g1MktWS1dndzVUZ1p0QWxhUlZZYTY4a3NOcW1IK1FkVlNFb1dWSUFxcXFwWFdzVThXMHpjS29BNlJFU0NXVm5tbWNQdEVUQTF1ZzhGd0dsWnliWTJEQUE0MUtISWlZQ0kxQmNuY1ZuNE85aG94b0giLCJtYWMiOiI0NmM4MmVmOGIzNTczYjEyMGRhZDAyODJmZjViYjY2MmU1NWMyYTUxNjc1NTE1OTJhMjAxMGUyZTI3NjIyNzMzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731130779\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1468704897 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => []\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1468704897\", {\"maxDepth\":0})</script>\n"}}