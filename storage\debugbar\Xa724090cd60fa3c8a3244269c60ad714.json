{"__meta": {"id": "Xa724090cd60fa3c8a3244269c60ad714", "datetime": "2025-06-28 11:24:08", "utime": **********.978219, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.549193, "end": **********.978233, "duration": 0.42904019355773926, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.549193, "relative_start": 0, "end": **********.910329, "relative_end": **********.910329, "duration": 0.36113619804382324, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.910337, "relative_start": 0.3611440658569336, "end": **********.978235, "relative_end": 1.9073486328125e-06, "duration": 0.06789803504943848, "duration_str": "67.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46253080, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0164, "accumulated_duration_str": "16.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.948895, "duration": 0.01609, "duration_str": "16.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.11}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.972471, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 98.11, "width_percent": 1.89}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"893\"\n    \"name\" => \"مرامي بطاطس مشوي18\\10ج\"\n    \"price\" => 12.0\n    \"quantity\" => 1\n    \"total\" => 12.0\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1510166256 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1510166256\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2139328840 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2139328840\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1722844994 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6Im9jNGVLY21tWGwyNkJUMFRLWmpoZ2c9PSIsInZhbHVlIjoiVHBNRG1ZeHBndjM4THl3RDZrL1VkWCtuUXIrRjhCZWtRNlp3dDJZTmxYUkxjcWQyalBmeXpFWHkxeTBPQmNUbXozSFk1cTErc1J6SnRMU1ZMLzlBY09yalJwZlVrR0xCTnRCbzF5Y3RUd1cxZERyc0p0T2Y1NlN5YUlxMGN6WGd5OWNzdEhwY2dwaDlqUi92Yi8xSldpR25DRjRrOURjZEQ4Sk5DVHE1OEtjMjY0ZE55Slo4VU0wQzB0L0xRMG9yVmQ4Yy82Y0wvWm9nSEdsZ2FZMEd0c1J0V2hXSi9INzcyRHh3SWpoRUc5U252ZDdPZXZuRjBvVlRSNjk5LzdDZlQvZDk3eXA2SERBZW4wd05INGh6ZzBlNThlaXNlRGQ2SktRbDhyUmYxbUcya3RnWXhYNzBBVGdYRWgya3NXRXdHYWQ4UHBrMmFqTW5XM2FHYVNzRkZVYnlFUHNPeEo0Unk5ZlYzUWNROVUzbWNjOG9XTlZnc3drbzhqcVhKd0JPM3d6TkNhV0U4aXh2bkJ5Z2RESnZWY2orU3RqQyt3MTJUdUhZOWxnUWRoU3RFVTk4MzJNOWtISW9ZRFZEVGtpTWxYdE1ZY2MzZUdZb3ZhamV6ZDNKR0kxTjhYTHNMUHZzRkJ1NXZ5Z3pqMlNyYUNqQXN3REZmd1E2RU1hVlRMQzEiLCJtYWMiOiIzNjUxNDQxM2NkN2EyZDJmMDNhYmNkNWNkMDZiZDgxOGFlYjQ2MzMwNTZmN2YwOWM5Yjg1OWQ2OWQ3ZWMxMmM5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlY5OEZUdXVUMVIrdVB2d1Y3Y2JmREE9PSIsInZhbHVlIjoiZ0QrbnVLdHBVU0Z1cjRpVDRUS2pVdkh3YlZaZ09EV3VyRUpPeEVCWjRvcGtOWkdhRjA5RnMvU1ArdkdSM05rS2RsN2hHUEFpNVlSOTBMc0Z5ZWZaU3JwcG01SGVjazUzL29oMUJoVWliOCtSTi9RdkIvMkZqbkdDbUJ3eVl5dFdqOGtqM0ZqWGNDcGRxTkE4TElVZklzMGNNZHcvMFRtVUYrbDNNMDhyZEJKdmozR2hldTJZcU5lT203clBWSml4L1RlQi9sZVJaZWRkQUNqZ01rYnF0Qk4zS0YyUm5mK1ppb1BHWEFmWkV2bnVOVVBWaC9IeVhjQmhOR0RBd0xJN1cvN1dvWTg1ZjBwZlFiSTBZa0k3UWt0dFU0NkVTcU5rM2padlJud1YvUVMwUElVTDRTSFJsRXc0SlJEemlmeGp3dmZZUVhvZFhORVI3em8ydmdtcGF6bDFldGFDOXpBaUw4VHJrSFdQWktGWngxTGhwd2FwSUxBZ0t2Z25nUWRxOE1mb01INXlkTU5rbXluWU5qRlhpV2FrUldvVlZpcmRtdVk3ODAvMFNaMlR2NXpXaFNoWEZDVm50WnNzZEplK2pDN0h0ZDd5ek9OcTdkdjRGV1pKRGxLMUhIM00yd21NbjgvUks1TGNMZVQ1aTBvaFFaNWdkSWdqNWZhZEk3RlkiLCJtYWMiOiIxYzg5MzBmOWNkMmExNWU4MzI1Y2FiNmI3MjllMjM2YzVhYmM3OTM4Nzc1ZGRhMmViM2JjN2Q5YmRlYTJmYzg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722844994\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-660209025 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660209025\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1473968709 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:24:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inh5bXdkbDF5U3NGcHNrRmVvN2FKVVE9PSIsInZhbHVlIjoibjFFcUZNU2taa2J5bVFvMncwSUlhVW03NWR2dWdjbnJ0RGVKaDlCcXA4WXNJeDZFRlRLRTBkbjR2V3FKMlBQditaZm83RlIyR3JveWZsUmZXTlVVNmE1NWdmTXE3WUFJU29qUlFsV3RUeDA4eDlpUEgxekIrNmdRMjNaYUNoMkhEZ2xBS1A1b1NFTWRUQXMxSVVoTEpmaDh3RDJ3WDBhTXB1R1VuSWJmc0NJVjdXbytDYzdqSDlDcmRuNFZYNUFReFI3c0gwdHJVdHl2RnJkekZSczdSMGM5Wkh5TitJRlJoQktjZllJN1cwbUZ1ODNsbkg0MHZ2N3pXQldqMjNkYndsU09oY3YxUlRpb3MxaGQwQ1BNd3cxcWtiWEkvMlgrdTA0dnV2eENuUkhHdWVuNDNwcXl1bHlXU3ExVDJMa0w4U2g5NjR5YVNhbGdRdURQN0NndXNqZFNjcG5QZm8vT1hPNURhbW1QbmRVUFVKK1cxQWlWd0duaTU3Sk9DbUZVay9LNFJ0UmNrdVhEa3UyNENTckZjQ2VjU3BDZ1NoRVJOV1QwNnBOdExUTGx3Y2h4V0lsKzlUTEhYQi9KQ3FWUDNoVzUrTFh5ME1YS2QvMnc0cG1tZUZBVlZrVXVQRUtRcm1GYmxQQndnNlFRNk0vQ2NHdWd0WWlwYW01ZEJWa3MiLCJtYWMiOiI3M2JlNzFhOWU1MTgyZDgwNmVhZDM0OTkwOWUzNjYyNzcwNDI3MTE3Yjg1ODJlMjU3MzZlYWZjMWI5N2VjMDUyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im10Znl4ZVdzQ3VLTEVYOU0xOThtblE9PSIsInZhbHVlIjoiV3RGYm1iaTlBYTd6MjVaUWxwUHErS1J4ZDMvTUVSR0U1cFIybXo2YjFTamp6OHQ0Q3F2aEZrWGlrRUtpUENKUWJpRVh5UWVLY3FTUXU0REdhd1NOMk5jenl2ZjBEYzEyNDBOTU5LZHR5b2NoUXZYMGZTYW1HRS8vaVVIeVpDMTR3dHBzamtzTG5idmhJNGEzQ0lPZWIzQ3Q5SGdDZ3Fla084eHpVUC9FTk9IZmYwTDc5bXQ4ZWM3NVYzSTVkc1BqaW5Bekdud1E5bVlOOHlhR0FBZGlPVTRXQUxoUTB0Vks2VUZrT1RYOWlhaHNqVDlhd0MrejBGWlVJeWxCTjczMXA0SkUrYlFBT3drdDI2YitZbFppdXljd0UwRHV4ZVBtT2lZa3EyZGdQcFZvZEI3WE8vR1o1TE12MElucnJxdzNlMHZOMko2UFNqdGdGUG1XYWJqMytnVTBTWjhyNDVWM0VPQUtTNDZnSkJPdmwzU0MzVHR1U2NaMkwyOGdXdGszTCtUQkZNU2hZd0ZkTVV0YU9CMGRNMm9LMS9kUG5CcFhFK1lwVGVLaE5MMHlMVFR6WEdXeEFUbDhKb2E2WmlvNGNLOWtvQ2dEdHBOd2NiMjBEenlKRXNNRmYxL2hzQVo3MjBHeG1DN05pR1pnMDNFbDNwNjR1KzBERENWZFEyRm8iLCJtYWMiOiIxODRjYzVjMjk1MmYyZmM3M2RjYzJlNzVmMTQ1YzNkNjE4MmNmNzFiMTVhMGUxNTNmY2EyMjVhMjQxYjFhOWI2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inh5bXdkbDF5U3NGcHNrRmVvN2FKVVE9PSIsInZhbHVlIjoibjFFcUZNU2taa2J5bVFvMncwSUlhVW03NWR2dWdjbnJ0RGVKaDlCcXA4WXNJeDZFRlRLRTBkbjR2V3FKMlBQditaZm83RlIyR3JveWZsUmZXTlVVNmE1NWdmTXE3WUFJU29qUlFsV3RUeDA4eDlpUEgxekIrNmdRMjNaYUNoMkhEZ2xBS1A1b1NFTWRUQXMxSVVoTEpmaDh3RDJ3WDBhTXB1R1VuSWJmc0NJVjdXbytDYzdqSDlDcmRuNFZYNUFReFI3c0gwdHJVdHl2RnJkekZSczdSMGM5Wkh5TitJRlJoQktjZllJN1cwbUZ1ODNsbkg0MHZ2N3pXQldqMjNkYndsU09oY3YxUlRpb3MxaGQwQ1BNd3cxcWtiWEkvMlgrdTA0dnV2eENuUkhHdWVuNDNwcXl1bHlXU3ExVDJMa0w4U2g5NjR5YVNhbGdRdURQN0NndXNqZFNjcG5QZm8vT1hPNURhbW1QbmRVUFVKK1cxQWlWd0duaTU3Sk9DbUZVay9LNFJ0UmNrdVhEa3UyNENTckZjQ2VjU3BDZ1NoRVJOV1QwNnBOdExUTGx3Y2h4V0lsKzlUTEhYQi9KQ3FWUDNoVzUrTFh5ME1YS2QvMnc0cG1tZUZBVlZrVXVQRUtRcm1GYmxQQndnNlFRNk0vQ2NHdWd0WWlwYW01ZEJWa3MiLCJtYWMiOiI3M2JlNzFhOWU1MTgyZDgwNmVhZDM0OTkwOWUzNjYyNzcwNDI3MTE3Yjg1ODJlMjU3MzZlYWZjMWI5N2VjMDUyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im10Znl4ZVdzQ3VLTEVYOU0xOThtblE9PSIsInZhbHVlIjoiV3RGYm1iaTlBYTd6MjVaUWxwUHErS1J4ZDMvTUVSR0U1cFIybXo2YjFTamp6OHQ0Q3F2aEZrWGlrRUtpUENKUWJpRVh5UWVLY3FTUXU0REdhd1NOMk5jenl2ZjBEYzEyNDBOTU5LZHR5b2NoUXZYMGZTYW1HRS8vaVVIeVpDMTR3dHBzamtzTG5idmhJNGEzQ0lPZWIzQ3Q5SGdDZ3Fla084eHpVUC9FTk9IZmYwTDc5bXQ4ZWM3NVYzSTVkc1BqaW5Bekdud1E5bVlOOHlhR0FBZGlPVTRXQUxoUTB0Vks2VUZrT1RYOWlhaHNqVDlhd0MrejBGWlVJeWxCTjczMXA0SkUrYlFBT3drdDI2YitZbFppdXljd0UwRHV4ZVBtT2lZa3EyZGdQcFZvZEI3WE8vR1o1TE12MElucnJxdzNlMHZOMko2UFNqdGdGUG1XYWJqMytnVTBTWjhyNDVWM0VPQUtTNDZnSkJPdmwzU0MzVHR1U2NaMkwyOGdXdGszTCtUQkZNU2hZd0ZkTVV0YU9CMGRNMm9LMS9kUG5CcFhFK1lwVGVLaE5MMHlMVFR6WEdXeEFUbDhKb2E2WmlvNGNLOWtvQ2dEdHBOd2NiMjBEenlKRXNNRmYxL2hzQVo3MjBHeG1DN05pR1pnMDNFbDNwNjR1KzBERENWZFEyRm8iLCJtYWMiOiIxODRjYzVjMjk1MmYyZmM3M2RjYzJlNzVmMTQ1YzNkNjE4MmNmNzFiMTVhMGUxNTNmY2EyMjVhMjQxYjFhOWI2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473968709\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1604711896 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">893</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1605;&#1585;&#1575;&#1605;&#1610; &#1576;&#1591;&#1575;&#1591;&#1587; &#1605;&#1588;&#1608;&#1610;18\\10&#1580;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1604711896\", {\"maxDepth\":0})</script>\n"}}