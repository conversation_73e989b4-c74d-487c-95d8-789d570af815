{"__meta": {"id": "X11aaff0f8391cb3596021e12f8a89c71", "datetime": "2025-06-28 15:03:05", "utime": **********.461563, "method": "GET", "uri": "/financial-operations/sales-analytics/product-performance?warehouse_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.02212, "end": **********.461576, "duration": 0.4394559860229492, "duration_str": "439ms", "measures": [{"label": "Booting", "start": **********.02212, "relative_start": 0, "end": **********.358682, "relative_end": **********.358682, "duration": 0.336561918258667, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.358691, "relative_start": 0.33657097816467285, "end": **********.461578, "relative_end": 1.9073486328125e-06, "duration": 0.10288691520690918, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46178256, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/product-performance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getProductPerformance", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=493\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:493-600</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.05177999999999999, "accumulated_duration_str": "51.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.391182, "duration": 0.023329999999999997, "duration_str": "23.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 45.056}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.423105, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 45.056, "width_percent": 0.792}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` as `category_name`, SUM(pp.quantity) as total_quantity, SUM(pp.price * pp.quantity) as total_revenue, COUNT(DISTINCT p.id) as order_count, CASE\nWHEN ps.expiry_date IS NULL THEN \"لا يوجد تاريخ انتهاء\"\nWHEN ps.expiry_date <= CURDATE() THEN \"منتهي الصلاحية\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN \"خطر عالي\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN \"تحذير\"\nELSE \"صالح\"\nEND as expiry_status, CASE\nWHEN ps.expiry_date IS NULL THEN NULL\nELSE DATEDIFF(ps.expiry_date, CURDATE())\nEND as days_to_expiry from `pos_products` as `pp` inner join `pos` as `p` on `pp`.`pos_id` = `p`.`id` inner join `product_services` as `ps` on `pp`.`product_id` = `ps`.`id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` where `p`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` order by `total_revenue` desc limit 10", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 536}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4257112, "duration": 0.02682, "duration_str": "26.82ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:536", "source": "app/Http/Controllers/SalesAnalyticsController.php:536", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=536", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "536"}, "connection": "kdmkjkqknb", "start_percent": 45.848, "width_percent": 51.796}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` as `category_name`, `wp`.`quantity` as `current_stock`, DATEDIFF(ps.expiry_date, CURDATE()) as days_to_expiry, CASE\nWHEN ps.expiry_date <= CURDATE() THEN \"منتهي الصلاحية\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN \"خطر عالي\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 15 DAY) THEN \"خطر متوسط\"\nELSE \"تحذير\"\nEND as risk_level from `product_services` as `ps` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` where `ps`.`created_by` = 15 and `ps`.`expiry_date` is not null and `ps`.`expiry_date` <= '2025-07-28 15:03:05' order by `days_to_expiry` asc limit 15", "type": "query", "params": [], "bindings": ["15", "2025-07-28 15:03:05"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 566}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.454016, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:566", "source": "app/Http/Controllers/SalesAnalyticsController.php:566", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=566", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "566"}, "connection": "kdmkjkqknb", "start_percent": 97.644, "width_percent": 2.356}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/product-performance", "status_code": "<pre class=sf-dump id=sf-dump-1957028511 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1957028511\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-679566351 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679566351\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1126473140 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1126473140\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1149712439 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122972644%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iis5d1JMT2I1cE5OUnVzQkJDOW9aY0E9PSIsInZhbHVlIjoiaVV4S0wyUmZzVE14K0FTSmVKVEV5L1lKcnpPanNlbWJxR0c1M1AzbVRGYWtZTDVSVGJ6Rm9wUXJJdXo4OWJWSlRPUjh2QWRJN0x1Z21UcUtQaWZTemZIZUpXQnBiWkhTd1JqUndEV25YekkxcG85ZUJoMHdKSXp3bE4reklSSFJaeUdpdTRvTWdHVVA0WDBXWDRqdkdES1JkYmNiazF1RnBaekY3VlhreEtwK0tiNS9sSE1BYStnczZCOE1VNnFjZ0Zxa215TUVra29pZkdGS3JGQ0ZRb09JbnNyNlJ1a1gyUUdqTVRZb2Z4VXB0dTVUNTJTNkdVbG1KVzd3U2ZGODh4YU56MW92Qmp3cEZIaDA2ektRUWplZ0puNVVGUnRIcHUxOGxIem5jUFcvUnBGOVRpaGRhNTVsSEIvVk5SN1JiclJIeEplZDdGdTFmaFI2UEdjS2w2RTd3Qk1oRmlSL0lKTzBaYjVYZmpvczRQbnYrdWdqcmZHazl6K1UzSktDUzlQR01aV29EekdPTDlFZHFHa1JmZVRzTXBVbjZCdTk5MCtXb3hFbUhTNkxoR2MvTEhrSzgzNEdFT09HM0EwVGN1bjNwR1hRZWFWU3pNQndQRk9QRFRwS09FY2lscVNydXZQN1NnWXRicHdsRVZNWlNvRTc4TUJabXhHWjdsSjAiLCJtYWMiOiI3ZmY3NDFmYTBhYTA5MzA3ZGQyZmZmOThmMjRmOGU3NDhlMmU2NmNhMGRmZTdiOGEzMzVjZTVkZGJlYThhODU3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdCK1k3OXFGN0ozbHFObEQ0UGdOTFE9PSIsInZhbHVlIjoicmtVdklSOHBscE96dk12TVhDK2M3YkpocElHVU5HcVd3MWNFWjg5Q2NMYUk5dmJ0bi9qU1U1eS9GT0hhZnJIeldBTU1yQWF2Q2FFWVFrL25GZlV2MVVWeUxnVXpVQ0xNeGxiWmk2RVpDRXFuMEFTTlROM0NlaTdFWTN1VlRDTE1hN2Q2UHFhN1llTUkyYjAzQWFGKzMwMEFwREFvMnNuNFJ1Mkg0Nm4xUFV6akxIWjIya2w5R1V2VG16TVRYR0UrYVFYMFdscWc0S2pQUVZpSEtzbDhhOFNyTXN6elpjUDdoTnFiaTZmc0JROTBKVlRvSXY4ekdLZHpPenZmMEtVMWRKOUJnVnB5UGhwLzZiNHdrRE9FZE4zY1ZTbVI4WGozd3o0WE9JMmJxbHZNMlZ4ck4zd2NPZEJWN0JwK3pWcVMwMCtuVCtuUU9LWTRnYy83d1hUVmY0OVc1UHk1amg4Yk9KbDNYQVdnYWF2V3JJb0V0cWFTZ2tCYlFsNTZySTRqUXhGN1VwWGdyaHJTcHBzd3ZjckxKcEVqN0hmY3ExWnBackttR1ZQMEtZdGVjdk1xYmtCcFRuYzUvWW1uYWJaYllJajQ3SXV6NmhONjM4OGh5djlGTGN0N2xxbElsRDFDNktoWHpFS01QaER2WHNCWHdjVHBCcWtMNGxmSjlwNysiLCJtYWMiOiI4NWQ0MWJkNGI3NzJjYzdmNWY3ODI5NWIxMmZjOWZiMzI2MzI5ZWQ0Njc1ZDVkNjRjYTM2ZmYxODNkZjIwMzc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1149712439\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-64162676 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-64162676\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1251502266 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:03:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikp5ZXJNc0c1eVNqR1BPRVBKNnZGN0E9PSIsInZhbHVlIjoiaGVoVmVnNklvRDdWTjU0UFVrbjBEajluVUVBVDdxVnNQVVZqeHNCNE92WCtPUDZSNzFBZ3N5b2ZoVmU2eU9ONWliSTFTZmVqTDdpR2t2Yjh5UVA0cFRXTzVqYnZDaHIrNnJucnNJVTZWcVV5d2NzOEJPRFRIejU4dVdHUktHdXJzRjVEOHdiMnhHazRqbzNPNi9YbElhVmJYUGtjSnN1TGdLamRXbTI0QmJCSGduWHdHQjB1elp5dHVvVjJUM1pSLzdYK1FYOGFURTluMWtnQmE2VFArSlVWNy96dmNKU1kxRG0xbkxWSjEvcUJXTjZwbFhhbVZvdzV1dElFV3NiSGsxR2dIOHUrZlI5VGt2MDVIWWVZcVZjSU1wWlFpMXdOYjMrQ0h6bUNUNjdCZUdVY0s1R1FqQzYvN3kvQWx6Q1pndlRZM3J6SVhnYjJ3czZHZWI0Q1ZxT0NDc0dsWjMrQ2Z1VnlEaTBDc05DOE9hbG9tUVRIMWV0aEM0R093bk9JbG51cVpidm1Xdk15NFpVWDR1WC9saUdGZVIyV01uaWRTYUJEQnhjeEVvM1R6dTA1SDEwWmdwTVozdmNsOEFyUEk3M0xodEFLTVpZZWowUnZuZ2tpVWdDdDArbExEV05TK2dibFl2VXdvV05HeFB6YmJOUFpWT3hPcTVVSEFIM0siLCJtYWMiOiIwNzlhOTIxYmMwOTczM2NhZTA0M2I1ZWUyZjIzOTg1ZTk2MDlkNjk3N2ZjNTVlODJiZjk2YTQzMDY2OWFhNmY5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkViZTBkeVR1NXlKYVNPNUtoQjZyTVE9PSIsInZhbHVlIjoiQ0p1ZHZvazhuc1RINjNWWU9UNDBKL0RKMjZqb0ZDT1FYVnJpZTE0L3hocW1mZUZRQmZEM3R2cHhub1c1QmNkUVFxU1BCNmNtaEdyNktPa2I1K3E1SXpWRFdPeWdJajhJSmd0WXVoZkxUdkRNTHpCSXNEeUx4N1BVZXMyak8vRFkxRmorU1MxZ2szZEE4MjdxdG93QlJ0K2daMkVwVFBSaHd2bDdYaHZicWVwS25aaGxPWmNJTUJKTDc5VE9YcmVMakxObG5TaDZtUXhHWUxIOXc4ZEprcCtRdTVDN1VUSkpKczdvZGFBTU9OSy9vR2hMdlVDRkp3YXFZZlVtMVYxTHNCTjgzaTBxOFVqVXFvR1cwSmRQU3lWOTBzcFBoMHdiRllydUlQVkN2eU4zV08xaUh2SmZUY1NudHFvVnhmTmZ6Y3lIaFFQbXo2ZkRkY1dLNVlzS0xHLzNiMGk5MmNiMWxMRzMrQk05NEdZbVZyM0d3SjdjWlgrampiZHIybEs2UlF2Vm1BeEQ0TCt0QzBBT0pqR2o0NlQzYWNTTG1lbG9uNUZCZ2d4amI2b2Y0d0s2SjgwbXVmQjEyNXp4R1kxaEJaQnRFNmJ2QzFGMmhybE1kdzZHRlhjUlNpSlYvMEFmTjhyUk5iMXp0a2xTaXJlR05LVCs4SlFPNDBlektZRmsiLCJtYWMiOiIzNTA0NjUzMGFlMjY5OTc0NTIzNTlkMjAyZjRlOGU0Y2JlNWU2NmY2MTJiYWFkNDAzYWFlMDM0NTM4NWRkOGQ2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikp5ZXJNc0c1eVNqR1BPRVBKNnZGN0E9PSIsInZhbHVlIjoiaGVoVmVnNklvRDdWTjU0UFVrbjBEajluVUVBVDdxVnNQVVZqeHNCNE92WCtPUDZSNzFBZ3N5b2ZoVmU2eU9ONWliSTFTZmVqTDdpR2t2Yjh5UVA0cFRXTzVqYnZDaHIrNnJucnNJVTZWcVV5d2NzOEJPRFRIejU4dVdHUktHdXJzRjVEOHdiMnhHazRqbzNPNi9YbElhVmJYUGtjSnN1TGdLamRXbTI0QmJCSGduWHdHQjB1elp5dHVvVjJUM1pSLzdYK1FYOGFURTluMWtnQmE2VFArSlVWNy96dmNKU1kxRG0xbkxWSjEvcUJXTjZwbFhhbVZvdzV1dElFV3NiSGsxR2dIOHUrZlI5VGt2MDVIWWVZcVZjSU1wWlFpMXdOYjMrQ0h6bUNUNjdCZUdVY0s1R1FqQzYvN3kvQWx6Q1pndlRZM3J6SVhnYjJ3czZHZWI0Q1ZxT0NDc0dsWjMrQ2Z1VnlEaTBDc05DOE9hbG9tUVRIMWV0aEM0R093bk9JbG51cVpidm1Xdk15NFpVWDR1WC9saUdGZVIyV01uaWRTYUJEQnhjeEVvM1R6dTA1SDEwWmdwTVozdmNsOEFyUEk3M0xodEFLTVpZZWowUnZuZ2tpVWdDdDArbExEV05TK2dibFl2VXdvV05HeFB6YmJOUFpWT3hPcTVVSEFIM0siLCJtYWMiOiIwNzlhOTIxYmMwOTczM2NhZTA0M2I1ZWUyZjIzOTg1ZTk2MDlkNjk3N2ZjNTVlODJiZjk2YTQzMDY2OWFhNmY5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkViZTBkeVR1NXlKYVNPNUtoQjZyTVE9PSIsInZhbHVlIjoiQ0p1ZHZvazhuc1RINjNWWU9UNDBKL0RKMjZqb0ZDT1FYVnJpZTE0L3hocW1mZUZRQmZEM3R2cHhub1c1QmNkUVFxU1BCNmNtaEdyNktPa2I1K3E1SXpWRFdPeWdJajhJSmd0WXVoZkxUdkRNTHpCSXNEeUx4N1BVZXMyak8vRFkxRmorU1MxZ2szZEE4MjdxdG93QlJ0K2daMkVwVFBSaHd2bDdYaHZicWVwS25aaGxPWmNJTUJKTDc5VE9YcmVMakxObG5TaDZtUXhHWUxIOXc4ZEprcCtRdTVDN1VUSkpKczdvZGFBTU9OSy9vR2hMdlVDRkp3YXFZZlVtMVYxTHNCTjgzaTBxOFVqVXFvR1cwSmRQU3lWOTBzcFBoMHdiRllydUlQVkN2eU4zV08xaUh2SmZUY1NudHFvVnhmTmZ6Y3lIaFFQbXo2ZkRkY1dLNVlzS0xHLzNiMGk5MmNiMWxMRzMrQk05NEdZbVZyM0d3SjdjWlgrampiZHIybEs2UlF2Vm1BeEQ0TCt0QzBBT0pqR2o0NlQzYWNTTG1lbG9uNUZCZ2d4amI2b2Y0d0s2SjgwbXVmQjEyNXp4R1kxaEJaQnRFNmJ2QzFGMmhybE1kdzZHRlhjUlNpSlYvMEFmTjhyUk5iMXp0a2xTaXJlR05LVCs4SlFPNDBlektZRmsiLCJtYWMiOiIzNTA0NjUzMGFlMjY5OTc0NTIzNTlkMjAyZjRlOGU0Y2JlNWU2NmY2MTJiYWFkNDAzYWFlMDM0NTM4NWRkOGQ2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251502266\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}