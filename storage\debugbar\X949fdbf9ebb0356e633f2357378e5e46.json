{"__meta": {"id": "X949fdbf9ebb0356e633f2357378e5e46", "datetime": "2025-06-28 16:04:29", "utime": **********.707993, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.289589, "end": **********.708017, "duration": 0.4184281826019287, "duration_str": "418ms", "measures": [{"label": "Booting", "start": **********.289589, "relative_start": 0, "end": **********.653515, "relative_end": **********.653515, "duration": 0.3639261722564697, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.653524, "relative_start": 0.3639349937438965, "end": **********.708019, "relative_end": 1.9073486328125e-06, "duration": 0.05449509620666504, "duration_str": "54.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45715064, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026799999999999997, "accumulated_duration_str": "2.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.679678, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.164}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.689626, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.164, "width_percent": 16.045}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.694689, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.209, "width_percent": 16.791}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6Im8vbnJGMSsvbEk2OWYvOEVpZ1FqOFE9PSIsInZhbHVlIjoiSSt1L2tQY2o3SWV6WWpIR3dHNkdudz09IiwibWFjIjoiNTcxM2FjNzExMDVkYzFkYjE0MjE2ZDliOWMwMTY0ZDk2ZjBiNDkzZjVjZTNhMTFkMTc1OTA0YmZhNjQ3MzU1YSIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1457064172 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1457064172\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2125614694 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2125614694\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-139009181 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139009181\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-145804752 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Im8vbnJGMSsvbEk2OWYvOEVpZ1FqOFE9PSIsInZhbHVlIjoiSSt1L2tQY2o3SWV6WWpIR3dHNkdudz09IiwibWFjIjoiNTcxM2FjNzExMDVkYzFkYjE0MjE2ZDliOWMwMTY0ZDk2ZjBiNDkzZjVjZTNhMTFkMTc1OTA0YmZhNjQ3MzU1YSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126667544%7C27%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldKS2gzOFBVQWlVa01oYkhWM3k0SFE9PSIsInZhbHVlIjoiTnVyUzUyRWdVOXJUUmpFNjRoZENvZ1dPSmxkRUd3TlVZU2pqZFdiaVZiZHd2UXdtOUR6S0J4c2ErbWxteWlkY283QjI4OUU2b0s1UGd5YVBteXYzT2NteVNLVVMwUHJoVmpYRjR2TDdrc3V0YU5MWHdGSlY1a3ZuUk8yTEE0ZkQwQnVRRVdxMmZmYk9PWGJKQnJTZVZDdFA5UVUwd2RGWDJrUjVrdEx3aXRUY2dyWEk1N3FFbWwveXhLK1ZCcWRKMm5CRFdOZmg3b3llZElONStPU0hRem1WZjVuYnErRUtBRnF3MWRTbEJRbGFkcWgwZnJVUklSaVFLQk5NaHdVcFRnTi92eWVHdTlIejRJa1A3WXRvT0IxemlCY3MyZ0NVcjVRRk4vNlJXTzdvNTRaMjBBeFpDdjVrYzRHZFBLcStnRWRiaExaSmtFTWIyMk9RbXcvK3JaWTlEMFQ2Y3dYa2lqNGtrQkI3N3FnU1hwdDUzc1lTSGdGbWhkNFdjcENVanZGSG9BUWZoamlDVzkrNWtIMDgxL0x6VjRMRlpUNzA0ZlE4amZTSDZOdFFvKy95OWpCd2xKVHpPY0FEWGRELytPRUM2Sis3bHZ0Z3g4aHoxU2p3K08vYU1pTXptZVc0YVRBejh2cnZpSUo5YUh5aU4wSnRGSkdVb2htUloyVnQiLCJtYWMiOiIxMDc2NzU3MDZmMjBjNDYyZGRlYzNmNzI0ZGQzMDA3NjQ1OWI2YjllNzUxOGFhOTdmMmJlMjRhMTY2NDRhMzQzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRaSExvOFNOdXNOaHVjdzNHV1krUXc9PSIsInZhbHVlIjoiM1ptaDJtNVR2T0k2UFQ4OXA2MjlTcitCNGRGWS92cEJZZk5yZE4zTGh3WU5Zb0lPeVFOb2E0VWE1THNRdmxHZXZLMkNkcVp4ekp1SnNBd2J2TGtYVmNVVThaSThYR2VaUXpQQUV5eVcwZWtQM1lUVjUvZXp1QVZKaVJ2NHgzQURSVVJGaW53RW1tT0NMOHA2aE94YWNxTDVZdm4raDBjajNKVlIrWTNGVEpLZ0RxZDFrZEFPV3cvUUZ1RmkrOE85NlRSbWhyeDBGbEQyQitIUEFobWVRRmRiYm0wcUNGdFpkcTdGZmdmWVBKZU5EWGFMM2IvVlE5T1lNYTZGcXAydDBFNjdtM3ZieXl6YnRIZDNSUjhVNGlSYUhSMGIvN09pektWSUluT2VQQU1wdVZ5M2pyemJ2cTlmUDBiZUp3STc3SlhLckJwT2l1Mlh1VjliSnJMWm0ySVpoRVF2ZVZuYlNpNVo5QnByVENGQ2U5NmZ3U0hiRXFqdjZDRjBRKzlIU0IzdEhBaTI4ckR3TWxpZnFYdDkzamJJdEI2MWVjR3J1MlN1WFp1TTNvdWhxbnVFd3lxd1M5c29DcUVpUit1eFZtckNBUkIzSVNlUGk0MmlUaGltSEpDZmprKzMxSlpGa1dIWHBhQk5jcGpBbWxsbm1EbkJiTVJHcDBIZktYM3IiLCJtYWMiOiI1ZmJiMjUyODU2MTE4MTJmZDZkMDg4NjIzYjRhZDE1NDAwYjAxNGIxYWViZTZmNTFlMGE1MTUyOTc2MTAwNDFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-145804752\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-634052160 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634052160\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1976733481 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:04:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpqaU9mZ25GSG80RXh2RmQvTlVnZHc9PSIsInZhbHVlIjoiZk5IUTJFZk9tZ3dkdkwrKzIvQkV0bCt1elByYUIzOUVPK0gydGJ2ZUNTYm90dE5vRTNQenFGM05lQ1VGaGlkb1Ixa2QyRVNzRC9KSTBweEVkY2pycGFHQk90MHliYjVUUjBrVk5pTkR5YmFSdldwZDdTWEhLbHMwc3YwdnRoN0tpamhEaTN0SGN1dENITnFsRXo2U3BHSjFLYmk1L3pFNGhkQm9MeW1GT0FBNHVQZlkrWTdqR2dkOUt2b2wzZmJMRzJiNXJhbmxmeit4ZlJnYkliMHJ6bktBTEcrdWxZa2IyRXg0Y0pNMENRME9ZWUIwQUp6Q0tMeitRRnp6WWdyQU1hY0V5aytJUlowdmwvUGhNWXJYRVVuOHdKOXJBSTlMb3BYdXNGalVtWDZQK25wTk1IbXp6Zk0wRm1HTkRRK2p4M293MzUvdGF4Kzd6eExKNExMemU4NDJKVDBQY1pNbDNIUlUyRnJMZThCMHdlZFVYbGovRmw5TEV3Z3FkM1RFdGhQS0VTOWpnTW8wSG5uUmprLytOQ1cwdHdVUTNiZFJueDJ1T3RHTUQvbWRmdVAzTXJualRlL0lQWll5VXFWekRjWk1HT3JQQWI1aXNEUUNmdVdGcTJ4QmZFZDhIK0t0VHdVMlcxSFFpVHA1U0cvNVFqME9xdmxJQk9uRWlEd2kiLCJtYWMiOiJlM2Q0MjNmNDZhZDNmMmU0ZGZlOTViY2M1MGIzYmQyODNiNDA5ZWU0ZDQzYmM4OGVlOTA0OTI3NmMxZDIyMTk0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpqUnNLUlV2cTdKUld2WnY0ZWg3OWc9PSIsInZhbHVlIjoiSlk2Q2Q3ZUZudVlRbE1iVlNUTTQ3OVh5d3UvY0Yvc0lnNG9ROXVPa0VVaVFXMVB4UUxtVmZUTC9BN211Nk42a20wS0F5emkwUUtFTXkyVW9pWjRHb3NEbXBmTk9Xd21TbTAzZ0ZoTWpJRTNZcU1xVGl6RFVMR3JXVjZjVHdWbmFQNE5UWWZLdWViSFpmZUI3WGhaV0FteVFEa3ZmdUJ4Q21SOU5SclNVSlZPY3ZoNkFOY3JGbWE2TXQ1ZXpzT0N6T3JYRU9BaVJySWdXZU1BYVhkaFp6NmdMWllZUzFBV1dRMkMrZkFLSFlDMkl5RzZuTDUwK0NFWU16cDNJOFgyallxRXVPZ2NmMEUweWVETFppOElsb0ZvekdaVCtUaXU5OEhrT2o0akRxaytVZ2R2bTRrWHdmV2d5UUh0VEgwbnlLekNPOTJTdmE5R0FBakFVdTVzSVN6eWFHdUpnUHBsWE01NHlKU1FrVGJsaVcxdTdkWFNNaGFiNDM2aGVNbUxFNEtJMTFXaFlRK3F4Um9ac0JoSHJZQnpOMFZhWFYvQ0w1bElNUkkxamZLU0pFT3poUkx3V0NIT2Z2U3RYRHZLSVZ1TnQzVmwrajFTWFFPb2R4SC91YWFlUDd6ekVEMmZuQ1EzNzIvdEhyWGx5K0Vlc0R3RkFlL09HMk5kYkZaaFIiLCJtYWMiOiJiOWQyNmUxNTc2M2U4OWRiNjFmOWE3YmM1ZDAxMGQ4ZjE1NzdjZjhlMmE4YTk4Yjk5ZTYxYTlhZjJlNjZiM2E2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:04:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpqaU9mZ25GSG80RXh2RmQvTlVnZHc9PSIsInZhbHVlIjoiZk5IUTJFZk9tZ3dkdkwrKzIvQkV0bCt1elByYUIzOUVPK0gydGJ2ZUNTYm90dE5vRTNQenFGM05lQ1VGaGlkb1Ixa2QyRVNzRC9KSTBweEVkY2pycGFHQk90MHliYjVUUjBrVk5pTkR5YmFSdldwZDdTWEhLbHMwc3YwdnRoN0tpamhEaTN0SGN1dENITnFsRXo2U3BHSjFLYmk1L3pFNGhkQm9MeW1GT0FBNHVQZlkrWTdqR2dkOUt2b2wzZmJMRzJiNXJhbmxmeit4ZlJnYkliMHJ6bktBTEcrdWxZa2IyRXg0Y0pNMENRME9ZWUIwQUp6Q0tMeitRRnp6WWdyQU1hY0V5aytJUlowdmwvUGhNWXJYRVVuOHdKOXJBSTlMb3BYdXNGalVtWDZQK25wTk1IbXp6Zk0wRm1HTkRRK2p4M293MzUvdGF4Kzd6eExKNExMemU4NDJKVDBQY1pNbDNIUlUyRnJMZThCMHdlZFVYbGovRmw5TEV3Z3FkM1RFdGhQS0VTOWpnTW8wSG5uUmprLytOQ1cwdHdVUTNiZFJueDJ1T3RHTUQvbWRmdVAzTXJualRlL0lQWll5VXFWekRjWk1HT3JQQWI1aXNEUUNmdVdGcTJ4QmZFZDhIK0t0VHdVMlcxSFFpVHA1U0cvNVFqME9xdmxJQk9uRWlEd2kiLCJtYWMiOiJlM2Q0MjNmNDZhZDNmMmU0ZGZlOTViY2M1MGIzYmQyODNiNDA5ZWU0ZDQzYmM4OGVlOTA0OTI3NmMxZDIyMTk0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpqUnNLUlV2cTdKUld2WnY0ZWg3OWc9PSIsInZhbHVlIjoiSlk2Q2Q3ZUZudVlRbE1iVlNUTTQ3OVh5d3UvY0Yvc0lnNG9ROXVPa0VVaVFXMVB4UUxtVmZUTC9BN211Nk42a20wS0F5emkwUUtFTXkyVW9pWjRHb3NEbXBmTk9Xd21TbTAzZ0ZoTWpJRTNZcU1xVGl6RFVMR3JXVjZjVHdWbmFQNE5UWWZLdWViSFpmZUI3WGhaV0FteVFEa3ZmdUJ4Q21SOU5SclNVSlZPY3ZoNkFOY3JGbWE2TXQ1ZXpzT0N6T3JYRU9BaVJySWdXZU1BYVhkaFp6NmdMWllZUzFBV1dRMkMrZkFLSFlDMkl5RzZuTDUwK0NFWU16cDNJOFgyallxRXVPZ2NmMEUweWVETFppOElsb0ZvekdaVCtUaXU5OEhrT2o0akRxaytVZ2R2bTRrWHdmV2d5UUh0VEgwbnlLekNPOTJTdmE5R0FBakFVdTVzSVN6eWFHdUpnUHBsWE01NHlKU1FrVGJsaVcxdTdkWFNNaGFiNDM2aGVNbUxFNEtJMTFXaFlRK3F4Um9ac0JoSHJZQnpOMFZhWFYvQ0w1bElNUkkxamZLU0pFT3poUkx3V0NIT2Z2U3RYRHZLSVZ1TnQzVmwrajFTWFFPb2R4SC91YWFlUDd6ekVEMmZuQ1EzNzIvdEhyWGx5K0Vlc0R3RkFlL09HMk5kYkZaaFIiLCJtYWMiOiJiOWQyNmUxNTc2M2U4OWRiNjFmOWE3YmM1ZDAxMGQ4ZjE1NzdjZjhlMmE4YTk4Yjk5ZTYxYTlhZjJlNjZiM2E2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:04:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976733481\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-884748941 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Im8vbnJGMSsvbEk2OWYvOEVpZ1FqOFE9PSIsInZhbHVlIjoiSSt1L2tQY2o3SWV6WWpIR3dHNkdudz09IiwibWFjIjoiNTcxM2FjNzExMDVkYzFkYjE0MjE2ZDliOWMwMTY0ZDk2ZjBiNDkzZjVjZTNhMTFkMTc1OTA0YmZhNjQ3MzU1YSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-884748941\", {\"maxDepth\":0})</script>\n"}}