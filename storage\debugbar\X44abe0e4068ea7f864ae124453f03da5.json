{"__meta": {"id": "X44abe0e4068ea7f864ae124453f03da5", "datetime": "2025-06-28 16:19:16", "utime": **********.698672, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.277836, "end": **********.698689, "duration": 0.4208528995513916, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.277836, "relative_start": 0, "end": **********.644018, "relative_end": **********.644018, "duration": 0.3661818504333496, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.64403, "relative_start": 0.3661940097808838, "end": **********.69869, "relative_end": 9.5367431640625e-07, "duration": 0.05465984344482422, "duration_str": "54.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45702048, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00268, "accumulated_duration_str": "2.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.672766, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.657}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.683701, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.657, "width_percent": 15.299}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.68926, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.955, "width_percent": 16.045}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-687669586 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-687669586\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2010667258 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2010667258\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1289625666 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1289625666\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1137407671 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127552721%7C36%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlI3cjlXdXQ0ZXBDOXE3UGdGMW5JS2c9PSIsInZhbHVlIjoiOXY3bnRETUFmMVJQR3dkNml3b3RYVHZkSm5PVGpNdVNXbTMxa2NJZEpZOW5PalR3MWhJcE5DdkR1cVhkeis5VTgzL1g2cTFCa0F1ZW8wcUorR3dDYVNUd0RGRWdVbTFOdUErbXR5WlRyamcrVUZtVUtFd3o2cFRZZ1prUXc5RzNzMTZvRjdoTHp5c2tSK1lGZDlKS1g5NHNMT2dhbTFod2ltMVY2bGp2S1dlYkRBZGZQcjQ3ZFY1SmtFWm9USlgydWRkTG8xSHVUY002dFlSajRsQ3pMQmRDZnpZMExqWHFzYU9MUG9EZ2t0aVpXc1ROY0wzZkRjOU5TdlNpL2Myc2Nab1FxL3dsZnZRaEo5RCtKR1hBdDZWQVQ0U3hNenpNMk5VUVdhWWZVenhoV0xuV1YvUWdnaVoyWXlVSkRkeW12d3d4QmxoWTE4OWhKTFdJWFRPMkRNTVoyaDAyeUEyM2JPVjRxUndFSFN6dVlhQS9JRmVVS0RRUXBYVlZVUWxwSkpaQWl3aHVvcXY4QmtNTUxxNXhpbE12eUw1MlFtNmVlRWV0bEdFSFFvM1pFZjNjRXB0b1pNOTRwNTJzZnJGUzVmTFFpcTcyL1A0MGdQRHh1SWVnZ1lhNDJVR1RRTllEWU1LSnlRZHhINHNsdURtMDRFRzY5aDR3MFc5Y3YrYk8iLCJtYWMiOiI0MzVlZTE0YmRkNWU2MTliMTdiMTNhOGY1ZWZjZGEyNGNlNjRiYTM5MGE3N2JlOWUzNmI3OTc2ODY4NDI4MjMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFJbnFRMmVMSm9IVHJsMUFpVURlcEE9PSIsInZhbHVlIjoiTCs0aUV5eWdUcTBOemtxdUVSVmo1U0RUVDVtSGlvVkhTSzNBbEhNZFYzTTNraWlGUURxTk9UMDdIaTIzWlc0bWtkVEE2SlUrVnN0MG55UXZpQW5TdWRFOUJ5RnE1NXBYcGw2VU5rajhadjE3aWdIL1hUVFZPN0s5ZlkzU1ZLcmRvSVJkc1A0WXNiYTJaUlJ5WWlzMFdPdUtPNTZJQ2V2UmdBbERSQlBueFFleTVPVzlRditXWWkvM0V3bTBiUExlZGRYYll4UmFuS0FZblo1QVNFWmt1ME9oNDhnbEVnZEl3UFJqZDBmWDIvdUN3c1N2T04vUFA2eG1DVmNWdGV2dU93QnZMMHBZR2x2azFqS3FsQitoQVh2UWtqWXNsL1luWG03c0U0bjVtUmtBeVdHK005UGw0M1hvNndFa0lTNWhBVXRvUnZZREF1VkdjQURuZzhMWWp0WXJmVWM1N21qamxTazdHdGtvZ203SnR1VEZPWU1LSFUzbGRmTUROakxBQUVXMThBMlEwNnVxN1oyY25SRHJYSHlqRWdBbEZVN0lLTVNTVmNmU3JKVmtDUGtFRG9OUkUwZUNwN2xKRDNBRUVZVDJjbThSOTdiUFBoWXlXamRhUSt3K3BSWUw5Q3ZvSmxTTUFac3BTNzFBT3JCd3VTOUFWQVRNbUZKNGc3Y3ciLCJtYWMiOiJiNzJhNTJmODQ1NzMxOGY1MjU3OGFlOGY4MjZhMTZhNTE3NTM4YWZjZDBmZThkMWExNzA4NjkyYzllY2JmZTM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137407671\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1454443902 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1454443902\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2013315858 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNJelN1M0JwaHZTMlNyc1N2VllzMnc9PSIsInZhbHVlIjoidkQ4YSt3a0Y3UWo0d1NVNzdCcnFlREJyOURVUmNzNFYydmdsTzg4R1gwUFBzdG05T0krZU95SWdCck1JcHYzWHVValMvQmtuWUJIeVNlYnlsZ3B6QnVHRWQwc1JUMHl5SXJla3BibEQycVMyWWNscGpERmNiR2JHWjF6eHN4M3RXQmtnSnIrUnNEUUtsMUVCSStkMGlYUVNNVkhBdjJxSzRvRXVsTjJKNWQ4S0M1KzVoTWVZVEVsZmxFOGxjanB3amp6cVlKTWppZk9XaWt6eWNsM1paNmZLV1FrZXJoQ1BOV1hTb0hRajViSkwrTUFCVXdPU2FhSkJ4WURFZ0ovcEZPS2ZlZnhIOFkwdDY2dnIvZnpJdmtEUDJKeE02ZmtUSUhYbm1kQUVGRTZ2Rm9yanVRUlZkUGIxVTFBbnphaVduMFhhVnh4cllab2k5azRwS2hnYm9KdWt6dGNoUVdQcmJWZm51d0hCdmxGQTFyQkVJRVl6MHh5dzU4YXptTnVqTXFoaUNxcW1VeFVDNnZyVTJLRGNIRENGZHI0YUNXeFFYSUxaSjVxTE14cktEWFZUSUpTZ0E0cXR5OVZOMHpYeU9ta29QYmgyQ09PaDRRYUJwQThjWnVNS0IrbW1tS3VaOEpZdE1idWdxQUdhR21GcnBiU0JFWHN6dTNXR3YxV2giLCJtYWMiOiIxYzA1NGExYzljOTk0YjQ1ODcyZDFiODRmZDEwYjZjNTY4YTA5ODRmNzJiNjQ0MWIyMDFlNjhiMjc4MmRlM2VlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InB3OTFYb0FQa0JvZG5KTno0M0FmTVE9PSIsInZhbHVlIjoiRDJzeFAvNW4wVHUrQWhQRGJhU0MydGxhS09NMGFQRmd4b2pyU0dqbVYxLyt6SERmeGRpQXNpcTRVSDhKTlRQeVY4dmZHT0NrOHY1RHBqdmRaK0RKZlpJOTJMS3BZbDVuOFlOd0E4OVdwbVNYNEtveTlWM2laTzJ3SG5WQmc4NnVNWEpCQXpiNTBIR2hXRmxXcVhPcjg5a01wNGVkR0dEaUZhdS9NNHF5enVucmxPL2ZITitRQTNnb1laQlZ4dy9oWENxRXhUd0JPVEVSWXNVS2NxVFBUL3VmeGJ6aUw2V0hkbHVMeHM0dlFnM2NpYUdOenJWZ2JSL3kwOXh1WjBQNHJNUkliK3NWWllGc3RUczd3ckRubExJZHhtNkhucDRndnYxdDVVWWNjYWVNZHV5WFROUkdTbk5VVXZkeDV5UW5GY3IrdG9mNjZUYnRkbVVLN1BVZUNXV0c5a3FPOVlQMjFvNmRnWmZoYS9ielN0NGNMeDVTOWNJNUJCa2VzdkJOeG1VdGhVZXRsVGtCSG9pSFpEcUtKb1NoRmJTUm90L1QyYVlaU1YxTW83QzNQdnJvUmpsdjMzYy8zU1BSbE85Ty95eU0rUitEMWRLc20zK0M4WlF5clplOE5XSmhwMlNtbS9ualZEWUszTXd0VFNxeElrMzNaOFhya0p3bkdLcjEiLCJtYWMiOiJjNWFlNTYxMjVjYTJmOTg4NzU1MTM5OTc1MTIzYTZlNDQzODhkMzk3NWI4NWQ1Nzk2Yjg1NmY4NmE4MTRmZjYwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNJelN1M0JwaHZTMlNyc1N2VllzMnc9PSIsInZhbHVlIjoidkQ4YSt3a0Y3UWo0d1NVNzdCcnFlREJyOURVUmNzNFYydmdsTzg4R1gwUFBzdG05T0krZU95SWdCck1JcHYzWHVValMvQmtuWUJIeVNlYnlsZ3B6QnVHRWQwc1JUMHl5SXJla3BibEQycVMyWWNscGpERmNiR2JHWjF6eHN4M3RXQmtnSnIrUnNEUUtsMUVCSStkMGlYUVNNVkhBdjJxSzRvRXVsTjJKNWQ4S0M1KzVoTWVZVEVsZmxFOGxjanB3amp6cVlKTWppZk9XaWt6eWNsM1paNmZLV1FrZXJoQ1BOV1hTb0hRajViSkwrTUFCVXdPU2FhSkJ4WURFZ0ovcEZPS2ZlZnhIOFkwdDY2dnIvZnpJdmtEUDJKeE02ZmtUSUhYbm1kQUVGRTZ2Rm9yanVRUlZkUGIxVTFBbnphaVduMFhhVnh4cllab2k5azRwS2hnYm9KdWt6dGNoUVdQcmJWZm51d0hCdmxGQTFyQkVJRVl6MHh5dzU4YXptTnVqTXFoaUNxcW1VeFVDNnZyVTJLRGNIRENGZHI0YUNXeFFYSUxaSjVxTE14cktEWFZUSUpTZ0E0cXR5OVZOMHpYeU9ta29QYmgyQ09PaDRRYUJwQThjWnVNS0IrbW1tS3VaOEpZdE1idWdxQUdhR21GcnBiU0JFWHN6dTNXR3YxV2giLCJtYWMiOiIxYzA1NGExYzljOTk0YjQ1ODcyZDFiODRmZDEwYjZjNTY4YTA5ODRmNzJiNjQ0MWIyMDFlNjhiMjc4MmRlM2VlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InB3OTFYb0FQa0JvZG5KTno0M0FmTVE9PSIsInZhbHVlIjoiRDJzeFAvNW4wVHUrQWhQRGJhU0MydGxhS09NMGFQRmd4b2pyU0dqbVYxLyt6SERmeGRpQXNpcTRVSDhKTlRQeVY4dmZHT0NrOHY1RHBqdmRaK0RKZlpJOTJMS3BZbDVuOFlOd0E4OVdwbVNYNEtveTlWM2laTzJ3SG5WQmc4NnVNWEpCQXpiNTBIR2hXRmxXcVhPcjg5a01wNGVkR0dEaUZhdS9NNHF5enVucmxPL2ZITitRQTNnb1laQlZ4dy9oWENxRXhUd0JPVEVSWXNVS2NxVFBUL3VmeGJ6aUw2V0hkbHVMeHM0dlFnM2NpYUdOenJWZ2JSL3kwOXh1WjBQNHJNUkliK3NWWllGc3RUczd3ckRubExJZHhtNkhucDRndnYxdDVVWWNjYWVNZHV5WFROUkdTbk5VVXZkeDV5UW5GY3IrdG9mNjZUYnRkbVVLN1BVZUNXV0c5a3FPOVlQMjFvNmRnWmZoYS9ielN0NGNMeDVTOWNJNUJCa2VzdkJOeG1VdGhVZXRsVGtCSG9pSFpEcUtKb1NoRmJTUm90L1QyYVlaU1YxTW83QzNQdnJvUmpsdjMzYy8zU1BSbE85Ty95eU0rUitEMWRLc20zK0M4WlF5clplOE5XSmhwMlNtbS9ualZEWUszTXd0VFNxeElrMzNaOFhya0p3bkdLcjEiLCJtYWMiOiJjNWFlNTYxMjVjYTJmOTg4NzU1MTM5OTc1MTIzYTZlNDQzODhkMzk3NWI4NWQ1Nzk2Yjg1NmY4NmE4MTRmZjYwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013315858\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-935322667 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-935322667\", {\"maxDepth\":0})</script>\n"}}