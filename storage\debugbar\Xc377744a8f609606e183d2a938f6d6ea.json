{"__meta": {"id": "Xc377744a8f609606e183d2a938f6d6ea", "datetime": "2025-06-28 14:59:11", "utime": **********.080869, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.668849, "end": **********.080883, "duration": 0.****************, "duration_str": "412ms", "measures": [{"label": "Booting", "start": **********.668849, "relative_start": 0, "end": **********.015341, "relative_end": **********.015341, "duration": 0.*****************, "duration_str": "346ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.015351, "relative_start": 0.*****************, "end": **********.080885, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "65.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0126, "accumulated_duration_str": "12.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.043227, "duration": 0.01148, "duration_str": "11.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.111}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0633838, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.111, "width_percent": 3.333}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.073286, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 94.444, "width_percent": 5.556}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C**********616%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxLYSsvaExEWlRZcE13dkxBV0g5c1E9PSIsInZhbHVlIjoiVStUTUphOWQ3eE5WcEpQRTVrUEZxOVR4QjRrdnc1UktQU2dEWU5yTWNDK0huZUlseUFQeGZTUUNTM2hhVWpaQXU4ODQ2dFh4cVh6Z2g1aFJCeEFYbEE4bDcwcld0clNndTZjK3ZlS3AvUFVXSC93enRiWUJHYnVFTXlnNksxUnN6Q1lYcGczai9kV2d4NWZRM2dvcEdDYUd3amg2ZlZlV0N0Q3Y1bDVoRzEydXFQc3hEaExoZm8vdUZCaFhHOHU0SEx6K3FtY3V6bVVjd3UyUzZYZU5SY2RXSVU5WklmMjNicHVpNmFhRzNXOWppcnlIbkphUjJNVVN3d1dRSUxVK1g1OHRwditycG5MUnVVeTFRRmllVDBVZjZaTlJGejBwTHRSSE1MMDJBVllpdWdOcXpkdDBTMDZUZlZYSlE4RTdwQnJ2eU90VTlNZjNoNHVFajBKNC8vYXVhOFFjNXBBdEpjUGJXNjE0eEZRcFFYUW4yT2ZSNmp6TkxiZGpzRTJ1Zk5icU4rUkIvbGorSkx3L0U0Rm5lby9XVzZ2SnhOcUdRdk9VUytHWXV4Qm5WR2NGaEF6a2orUSs1M0h6YkpKT2ZRMllRSWE1dWlrcCtvUDB1elRIMXVXWVdCUHNEejlDek1YaHJxV1A4bHVUazBDMCtHSnV3N1ZNWDRkNk9YS3YiLCJtYWMiOiJhOTUxNjczMzEzY2FiMDMyZmU0MzhjNTgwNjcxMTZlOTU0NDM4ODY5NzlhY2Q2Y2Y5OTY5YjFlMTc1OTM2N2I0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBrenBaaXRTMWo4R2hBRS93ZS9qN2c9PSIsInZhbHVlIjoiY2M3TEtpaEhnUTM4enFnc3F1eGpSeEpxeUVRMjFucEhMRFNoNlQrcytqUFBRb0IrZ3E3T3AyaG9rWnczdHUxQlBNVXZUaHE4S1FFUzFGK1lsdkhVT3pFd0tndWRaYmN1VUxaYThKTXJoUWpTMUxabjlxZnpCbDIrdnFKL09FSnBYd2dKVEtRa1BMWWptUjZobjRkWUxseEt5Uk9TcndYVDhhUmxqdldDKy9XbytXNVpvdzBLSnl2R3pEN0FtaWFUUkFFQ1V5b1E0UGdHMHZLTGVqcnlyZW0rMUp3UGsrN09VcDIwVWpwTjAzTkZXNjRaSG9YTFlZUEJiZ2hjbmRjTVhjSElFSENpeXFrMW9QZVpOL000N2VDUE1WdkxZSnZkMHY4djFYT3hTVzlteHhKL3VHOG51WjY3MVZ5Q0lIa0pjNEVpbGdlcCt4dlAxVWdEMG1TQUhaQmtjeXhLYS9ScUl1ZUx3dGZhUmZ3ZTZsWnRRN1piRmEyVkRKVlFSU1AzL2hDQUxtR2pTZXVQTDFJRXY4TGVYTDJzRm4xb2F2RC9CU0p2azRLTVJNencxakhubHZKNkozYVB5MUJlb3QwS2dad1JEd0kwRDlhLytWREpOV0FCWnZvTGFmSEhwL1B5Y1FXcndiV3ZYdTR2SFlVei9RajFCY0w1TzNHYlpXUXciLCJtYWMiOiI2NTJlNzczM2UxNDQ1OTlhN2EwMzk4MjY1NTM4MmMzNDdhOTUxYzRjM2Y1NGMyOWM3YTE2NjdlMTBkODNiNDI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1868046408 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6nyGbgT6n8t00JbJ5CSgj7vkpXnxnyISs5XS6j6J</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1868046408\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-612691425 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllhR3dGUUZHMm1kVU5XbmhLUks3NFE9PSIsInZhbHVlIjoidGVZMG85TFhmZk03QUQ3d0NMdmFqWWw5MzA5bks2YjZtVCtpdzBsd2o0cklSZ3lQY2hpaXVocHdGZU1JRkVZNW82azNZdGwzamJnVElGeTB6dnhZTFhDMGNHRFNTcEswT0l5VkhFTFJXdkdnWml2em9WYS9CcExNRXBKUWtVUSs4V1EwZW5GcDMvSzBrUjlqeVEzQmhoVnlxOGRWTmVKMzYvRWNJWlhvRFh3elJKUmNMZk9ndVR3bXJ5cUs0d2ZEQitFYVNISUgrMzJUVG5GaHZXcXc3UVF4Y2Y0NkZyeVh0eStIbW9nT3ZyMmJpVGVWdXdUVlo3aVlhRlY5ZVlOZXdjS2R3M2VtQnlCZG9VRTh4VHdPaXZDZzZnQUNaV09zSzRzR3lqb0gzMGRaVThzOCs5RkQzM3Jmd0ZNTGwzc2dPQ3E1bGd1Vzc4UjFKQ05jUlg5RHNSQU5mOVp4cWVicCt2SXdiZHBkd09rb1R1RXFZSFE1eFJHVHBrTWo3VGhZK3ovelZBMmVPVXBUdHZmTlBMcTZEV28zN0FQUVNnUWRDSm13QlJLWWRhV05SNUUzS256YkZLaWM5R1hReDFWTk5BdTRqU0dLcFU3d0ppMzVtVHBVeERDMi9EbVJ3djFURzFtN1EwU1hxQTZ6WGlQMzUwOEtybzRERms0aHorQWMiLCJtYWMiOiI2OTEzNjhjODYxNzc2YmE1ZWU1MmY1NTNjMTIwMDg4YzBhYTVjNmYzN2VkMDJjMDcyNjM1ODg5NzUyNjk3ZmNkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxsWElSS1ZidzVabEgwR3puN3BnS0E9PSIsInZhbHVlIjoiRnZlejJpdjhwRm9wdXN6S1E5OHNBcWw1a0xlYVVtY2UxR1FHR1IwNWY5RmZyTWRiOHR6czZva1pTemp2RVVnTURrRmJ0TVJNeDRMTnBZdmpzckhMYWYvc1cxUzRKQzJ4NU4rcm1GemZqTUpOTDlsKy8wTDZ4ZHFkZ3BpZEJHc3lEa005UkQ3a1lsTS9XN0tTNlM4dzM4R0lNV0JPS2xkRk1heHhwMGxFUmdaalFMc2dTLzNzK3JOeHJtTmJua0FYNUhGd2R3azZIUmI2eFdnVU9YM0Nkc3hBR3hnbkp2cWN4QlhWNHFEbjFwQnB2ZU84c3NhT2prNklUNmxFRkE4ZG9Cd3Zpa3lnSGVZR3ZJMTZQNDVSMmc4bWIySGNJaUplblJVNnNtbGFndEVWKzZBSUE0WWFBdkh1dGlsYjVlSGVzUzJRTk9LT2F1c3czKzV0N3JTQnpPd3E1bGQ2dURVYXJXK1hNdWZxcjlVZ3BjRnR1UEluYXZ6NzFJamE2QXJvd0VERm5qU2lIdE1qNW5qY2JScG1wNjZ0Rzgwc0lld3h3QkpnbjBiRUV6OUZZdzFEeGVhWUhqRVJTVkRRM2VNUEhuL3grYWFCZVc1aWd6T0ljTlVGT2I1d2hiNERDc2lnSkt5RlI5eWdjTlR5Sm15S3FkaS9wam9EdGI2T29KN00iLCJtYWMiOiI0ZjM1NjVkOGU1NWIzZTViMDMyOTdmOTcxN2VhODQwN2Y2M2QxNDY2YzA0MTFhMzRiNjcxMzQxODgxYzRiYTdhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllhR3dGUUZHMm1kVU5XbmhLUks3NFE9PSIsInZhbHVlIjoidGVZMG85TFhmZk03QUQ3d0NMdmFqWWw5MzA5bks2YjZtVCtpdzBsd2o0cklSZ3lQY2hpaXVocHdGZU1JRkVZNW82azNZdGwzamJnVElGeTB6dnhZTFhDMGNHRFNTcEswT0l5VkhFTFJXdkdnWml2em9WYS9CcExNRXBKUWtVUSs4V1EwZW5GcDMvSzBrUjlqeVEzQmhoVnlxOGRWTmVKMzYvRWNJWlhvRFh3elJKUmNMZk9ndVR3bXJ5cUs0d2ZEQitFYVNISUgrMzJUVG5GaHZXcXc3UVF4Y2Y0NkZyeVh0eStIbW9nT3ZyMmJpVGVWdXdUVlo3aVlhRlY5ZVlOZXdjS2R3M2VtQnlCZG9VRTh4VHdPaXZDZzZnQUNaV09zSzRzR3lqb0gzMGRaVThzOCs5RkQzM3Jmd0ZNTGwzc2dPQ3E1bGd1Vzc4UjFKQ05jUlg5RHNSQU5mOVp4cWVicCt2SXdiZHBkd09rb1R1RXFZSFE1eFJHVHBrTWo3VGhZK3ovelZBMmVPVXBUdHZmTlBMcTZEV28zN0FQUVNnUWRDSm13QlJLWWRhV05SNUUzS256YkZLaWM5R1hReDFWTk5BdTRqU0dLcFU3d0ppMzVtVHBVeERDMi9EbVJ3djFURzFtN1EwU1hxQTZ6WGlQMzUwOEtybzRERms0aHorQWMiLCJtYWMiOiI2OTEzNjhjODYxNzc2YmE1ZWU1MmY1NTNjMTIwMDg4YzBhYTVjNmYzN2VkMDJjMDcyNjM1ODg5NzUyNjk3ZmNkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxsWElSS1ZidzVabEgwR3puN3BnS0E9PSIsInZhbHVlIjoiRnZlejJpdjhwRm9wdXN6S1E5OHNBcWw1a0xlYVVtY2UxR1FHR1IwNWY5RmZyTWRiOHR6czZva1pTemp2RVVnTURrRmJ0TVJNeDRMTnBZdmpzckhMYWYvc1cxUzRKQzJ4NU4rcm1GemZqTUpOTDlsKy8wTDZ4ZHFkZ3BpZEJHc3lEa005UkQ3a1lsTS9XN0tTNlM4dzM4R0lNV0JPS2xkRk1heHhwMGxFUmdaalFMc2dTLzNzK3JOeHJtTmJua0FYNUhGd2R3azZIUmI2eFdnVU9YM0Nkc3hBR3hnbkp2cWN4QlhWNHFEbjFwQnB2ZU84c3NhT2prNklUNmxFRkE4ZG9Cd3Zpa3lnSGVZR3ZJMTZQNDVSMmc4bWIySGNJaUplblJVNnNtbGFndEVWKzZBSUE0WWFBdkh1dGlsYjVlSGVzUzJRTk9LT2F1c3czKzV0N3JTQnpPd3E1bGQ2dURVYXJXK1hNdWZxcjlVZ3BjRnR1UEluYXZ6NzFJamE2QXJvd0VERm5qU2lIdE1qNW5qY2JScG1wNjZ0Rzgwc0lld3h3QkpnbjBiRUV6OUZZdzFEeGVhWUhqRVJTVkRRM2VNUEhuL3grYWFCZVc1aWd6T0ljTlVGT2I1d2hiNERDc2lnSkt5RlI5eWdjTlR5Sm15S3FkaS9wam9EdGI2T29KN00iLCJtYWMiOiI0ZjM1NjVkOGU1NWIzZTViMDMyOTdmOTcxN2VhODQwN2Y2M2QxNDY2YzA0MTFhMzRiNjcxMzQxODgxYzRiYTdhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612691425\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-890954058 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890954058\", {\"maxDepth\":0})</script>\n"}}