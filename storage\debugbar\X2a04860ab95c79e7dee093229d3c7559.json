{"__meta": {"id": "X2a04860ab95c79e7dee093229d3c7559", "datetime": "2025-06-28 15:26:34", "utime": **********.469913, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.045283, "end": **********.469929, "duration": 0.42464590072631836, "duration_str": "425ms", "measures": [{"label": "Booting", "start": **********.045283, "relative_start": 0, "end": **********.419828, "relative_end": **********.419828, "duration": 0.3745448589324951, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.419837, "relative_start": 0.374553918838501, "end": **********.469931, "relative_end": 1.9073486328125e-06, "duration": 0.050093889236450195, "duration_str": "50.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45617200, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0024200000000000003, "accumulated_duration_str": "2.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.445744, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.876}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4553442, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.876, "width_percent": 11.57}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.460904, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.446, "width_percent": 23.554}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-974189529 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-974189529\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1296568973 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1296568973\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1733503614 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733503614\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-570347713 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124349103%7C15%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlQzbndpV01oa3hzeGY1M1VwRnBMVlE9PSIsInZhbHVlIjoiajRuR3dhMGNDT0ZUTldNb1VPNTNQVGF5a005ZFE1OVJ3NC9wM2taT0hDVjg3MnhuMXBubitwR0pxUnpjMktKc29NUWlLZUZXQWtGWmFNOWU2SWU4UHRKcmRIWVpSRUs0VUFsZDBrbUptWkdoUzNpd0ZVU3Ridk92NG8xenN1NjJWTzVqZWs1ZjhOZkdmakJYdi9MUTMyYVZuK1ZyZ0ZtcWg0ZUlJV2I1UXJCUk13cXV2Zm1JRWdYV2JIYnpiaTFPazYxcGNHUEk5Zk5JS2pMclYyUmxWMUo0Z3FpTDJZTWova0NQWEJ6TWY3aHV3YW1pZk5YZGVSNFVIOTAwVExidVdvQUVKUnUrMXhLMms1dkx6UjdtRG1ibHpOVzl0RWhwK05FSW1Rd21wdGt6V3FBTFVrc3orcXZYUGJlQXVYTlc3aWdSYURRZ05GM3RDRm9XWW9zRkVoL1lQM0NkZXB1NWpjd2xqemxvSzdJK2wxdHY1R0lud3o2UTliOW93aVlpTEdkblQ0N1dVUEtxTFBZWXdCS3RQQlhVaWxQRjJxbklWVzBCZkc0SmsrOEV3ZVkyUXBuSVpvUCtaZnh4NUViTW1GQ3p3RnNXNlNscm9tVWI4NHVFOXNkU1luTisvNWdUK29JZFdqNTZZTWt1a20vdW5JMlE5VVdPMEczODRVSnMiLCJtYWMiOiJlZTgwNzIzMzg0ZWUwMGE4YmRkMmUyZTJlODM4MjgyNTUwMzFjZWNjZWY3ZWFjOWZkYjg0YTMzMWY5NGJkYmUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImR2V3VJNDg1ZnNWOHFVaTVzUHZnT0E9PSIsInZhbHVlIjoiVkQ5bml4ODlJSktQVjRFSFJETEhoRThsRzZXMWpOMmlhNFI1UEwwVW1HNHhEVlFkZFY4OEZQeVg2ME5MeHpLd2xaK3JBeS82ejR6dFJwQ0JWVS9NN010WTU3emRQN2RacFRiM2V2NnJaOURNdE1GaEJ3Wi9RS2N6ekRyMStyU3gzT2xjOWJXNnR0SGNhNVNWN09PVnZzQUFaMzl0dDJ2d0tnWDRZMHZaVnl2SWpIaUxSbGhRalFkR0g0TG9oaS8vb1FLSEhmU1hvMVUreGRKM1BMaUxBV3VmakFnSTlwbUdDWW1lKzd5M0VyajVjdUc0V1hhM2hQYXB4QkZnS21ycDdmejJxa3VuVWR4MGJCMU1yNzk2czNqbmRFalZGV3lhNVU3ZmhxQlhLYlZha0dQdVVwNEZlUVVpSzlXVnZiUm03L0NZOU1RZE5DR0dzNlJPUW1XU0xzZ0g2U0JPcXVGSW5OY0xMM3lZOUpHSWVtSE5PS2tDZS9rNzg0MThHdVRHVU5vNGVxYzBNclJWT3dZVlhQSWpwNGRiZ0NtZ3dMOHI2akl3d1Y0S0s4S0VCT2htTGVqSWpxSjl3elcwTE4reDlGazhhWlRtOFlYVTNIdEVPUEQ1TmlVeUxaOWZpNGh6TGx0WkgxNnpRaVhyem9tRTJLRUhPMEI4TGJZLzJ3QUwiLCJtYWMiOiI3YjIyNDRmYWE3YzUzNGVmMTA3MWU0YWE4N2Q5MzIwMTdjMGNmNDkzYTJlYjBmNzlkNjRiODkwZDI3M2MzZTNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570347713\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1376092792 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376092792\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1995574924 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:26:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpZNU5KRGJuVXpZYzNObHFtQ3dtbGc9PSIsInZhbHVlIjoiUUlnWkdLcVZMdnk1SUU4TDYveHVkTmx1WHFIMk1XbHcxQVJBUGZnczVGQ3pYYzZaZUNjK1hmZ3ZYUWE4Rk1hdS83V2hJVjdBOTBjK0tTUmNXdXBjYTF5d3A2TU9HM0JVWmFXZ000bDQ1UEdpRjJ5a1MyOGtKMjJoN1ZUeFBPREVMU1lhbXNZS0I3aVNkZ2lua0JNOEoxQUtpWE9tdlpXbGw4VTFOd2dUbjQzY20vd3BGUlUwVU9FaFVXcFJSemhCeVlZSlhVTEVJQU9DbHpIcXRPN1BiUE9DVUpoYjVDRXAzRVVXTlBoRWVubHRac3NsOTVyWmJRZU5PQ1BQdk5zaFYwT2JCbTVVQThqdHVCRGNNMHQycTMvZXhBWDVRd3ZJZHpMYTMxTWN6SzJPQlkwQVI5cEluRU9FSUdpTWhiM3I3UlRVYWtrTWVKazdKcksrU1dUbmJwV3JXMjl5bFlEODZQRUpGUnRlUVNtKzRCcGRsK0lOR1pwTXNRYkc3Q0YwMFphWUhzOVQ2U1Y5VGg5RFk5R0ZHVTgrYzBZc3U4VTJ0V1FXNDAvcXlabnpWZmk2U1ZCa0dFTkpFd0ZkSDJSMkJaejJGRHEwQXE0Wm5hOWtwcVFYVTlnRVZmSk9QRE9pbjU4ZXVTQzc2MUIzZ3F2YXVCUzhBR2hxNGtrR2kzdloiLCJtYWMiOiI5YTU2OGYzZWFlMjJhZjRkZDRkZjkwNWZlYzg4MzA3YTI1MmNjMTU0N2VkZjMyN2UzY2Q2MzUwY2RmMzYwNTY2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNhN0hkUkd3cGNKMHZjTTZreTdKQ1E9PSIsInZhbHVlIjoiMFRxMENjOFJwZENxVUJUbXRzaWVlVnF3K2JvVDUyN1JCaGRSRG1EaUR5anpoSVZuYzNCb0F4UTJiZzNFalBBR25JNDUyMHRJNkpRcjBUV0F2eWUxbG53TWJEZnZHS2NVeStueW1wOTNJa2pFMUtoSUZ1MTBicnUybGdFM3lpS3UxTjA1NFkvMEJWTmd5cUx6ODlqZFdZTG9ybE5BSG03T1EvL1g2clRORnFLRjdENzQ1THJwS0k3TTR5bEFYeVorNHcraHNaSGljbEZyaVd6Ykk1cDFSaDZWbW9oM0lVUGpsVFkzT3Y3VmxCaVl6VTJaak1obGM4Wmp2SnRXdUkrU00yYldlZzVpcmVTbmRHQWg4YkRqMTVqR2V0dGVCVkhqY3BnS0kzalY0eTdEcUNvN2dkcXR5dEtxb1BxSnl3ZC9peTc3Wm9UTjRkVVZHbEgxcmVFbjFET3Y4T3dES2hiL3MxVGJqMEhvazBWVjNWNnBoalNOYkRNQXdYVTJtQTkwSEcyaWttZ04wWHdDeEt4UVdncmNaNEg2TGFUZ2NRNkVkMXlYVmFZdFpRS21XazE4WWNZMWVGanFjdDlwUG1Lc20vU250b042N2NYazdzdTc4Yk5OSzA2WDJPRCs1VUdEaUsyMThVVXhJTHFXZ3YxSW9Jd0Z5cWNpdXRxR2RZMjQiLCJtYWMiOiJlNWUzOWEwNzJjZjM5MWUyYmMxMTIzMDE3NTYzM2FkOWYyMmNiZjMwMjM4MDMzNTliZTgxNzY1NTI2N2ZiMmU1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpZNU5KRGJuVXpZYzNObHFtQ3dtbGc9PSIsInZhbHVlIjoiUUlnWkdLcVZMdnk1SUU4TDYveHVkTmx1WHFIMk1XbHcxQVJBUGZnczVGQ3pYYzZaZUNjK1hmZ3ZYUWE4Rk1hdS83V2hJVjdBOTBjK0tTUmNXdXBjYTF5d3A2TU9HM0JVWmFXZ000bDQ1UEdpRjJ5a1MyOGtKMjJoN1ZUeFBPREVMU1lhbXNZS0I3aVNkZ2lua0JNOEoxQUtpWE9tdlpXbGw4VTFOd2dUbjQzY20vd3BGUlUwVU9FaFVXcFJSemhCeVlZSlhVTEVJQU9DbHpIcXRPN1BiUE9DVUpoYjVDRXAzRVVXTlBoRWVubHRac3NsOTVyWmJRZU5PQ1BQdk5zaFYwT2JCbTVVQThqdHVCRGNNMHQycTMvZXhBWDVRd3ZJZHpMYTMxTWN6SzJPQlkwQVI5cEluRU9FSUdpTWhiM3I3UlRVYWtrTWVKazdKcksrU1dUbmJwV3JXMjl5bFlEODZQRUpGUnRlUVNtKzRCcGRsK0lOR1pwTXNRYkc3Q0YwMFphWUhzOVQ2U1Y5VGg5RFk5R0ZHVTgrYzBZc3U4VTJ0V1FXNDAvcXlabnpWZmk2U1ZCa0dFTkpFd0ZkSDJSMkJaejJGRHEwQXE0Wm5hOWtwcVFYVTlnRVZmSk9QRE9pbjU4ZXVTQzc2MUIzZ3F2YXVCUzhBR2hxNGtrR2kzdloiLCJtYWMiOiI5YTU2OGYzZWFlMjJhZjRkZDRkZjkwNWZlYzg4MzA3YTI1MmNjMTU0N2VkZjMyN2UzY2Q2MzUwY2RmMzYwNTY2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNhN0hkUkd3cGNKMHZjTTZreTdKQ1E9PSIsInZhbHVlIjoiMFRxMENjOFJwZENxVUJUbXRzaWVlVnF3K2JvVDUyN1JCaGRSRG1EaUR5anpoSVZuYzNCb0F4UTJiZzNFalBBR25JNDUyMHRJNkpRcjBUV0F2eWUxbG53TWJEZnZHS2NVeStueW1wOTNJa2pFMUtoSUZ1MTBicnUybGdFM3lpS3UxTjA1NFkvMEJWTmd5cUx6ODlqZFdZTG9ybE5BSG03T1EvL1g2clRORnFLRjdENzQ1THJwS0k3TTR5bEFYeVorNHcraHNaSGljbEZyaVd6Ykk1cDFSaDZWbW9oM0lVUGpsVFkzT3Y3VmxCaVl6VTJaak1obGM4Wmp2SnRXdUkrU00yYldlZzVpcmVTbmRHQWg4YkRqMTVqR2V0dGVCVkhqY3BnS0kzalY0eTdEcUNvN2dkcXR5dEtxb1BxSnl3ZC9peTc3Wm9UTjRkVVZHbEgxcmVFbjFET3Y4T3dES2hiL3MxVGJqMEhvazBWVjNWNnBoalNOYkRNQXdYVTJtQTkwSEcyaWttZ04wWHdDeEt4UVdncmNaNEg2TGFUZ2NRNkVkMXlYVmFZdFpRS21XazE4WWNZMWVGanFjdDlwUG1Lc20vU250b042N2NYazdzdTc4Yk5OSzA2WDJPRCs1VUdEaUsyMThVVXhJTHFXZ3YxSW9Jd0Z5cWNpdXRxR2RZMjQiLCJtYWMiOiJlNWUzOWEwNzJjZjM5MWUyYmMxMTIzMDE3NTYzM2FkOWYyMmNiZjMwMjM4MDMzNTliZTgxNzY1NTI2N2ZiMmU1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1995574924\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-871337291 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-871337291\", {\"maxDepth\":0})</script>\n"}}