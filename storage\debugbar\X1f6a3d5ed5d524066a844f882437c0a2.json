{"__meta": {"id": "X1f6a3d5ed5d524066a844f882437c0a2", "datetime": "2025-06-28 16:47:11", "utime": **********.271265, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751129230.861687, "end": **********.271279, "duration": 0.4095921516418457, "duration_str": "410ms", "measures": [{"label": "Booting", "start": 1751129230.861687, "relative_start": 0, "end": **********.220227, "relative_end": **********.220227, "duration": 0.35854005813598633, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.220235, "relative_start": 0.3585481643676758, "end": **********.27128, "relative_end": 9.5367431640625e-07, "duration": 0.05104494094848633, "duration_str": "51.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45720512, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00268, "accumulated_duration_str": "2.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.247202, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.448}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.256601, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.448, "width_percent": 16.418}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.262264, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.866, "width_percent": 23.134}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1979971331 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1979971331\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2039178860 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; laravel_session=eyJpdiI6IlVGN2szMHhZTEFYTEo0TDJwZlRBT3c9PSIsInZhbHVlIjoieHRCWHVRRzBZOHhuNVU2MytRamlRdXFzUm5ZMFJxQWpadU8zcTg4RmNGWDNva0hxN3djbWM3d3I0b3B6b2FueWZhZnZKUnR5QnVjMzA1SkJVMGF4VkFhaFlzYWF6cGFXVjdGS0I0TE1aUEdrMVYxTVAybzlTN3hQVml3ZEhnN1B1eUtEQUJ2TDU2aUYrTlFwbXQ5WmViaGd4elJoeTFFYTkwNGRVdWYrTXFseCt2MHE3T3doZndncy83S1R6bnpRNWNXcTgwcFNqK3VUSytiSFhZN2l4dlJRbGIrbStER3NzZ0Y2OG9tdjhIdTV6OFdydDExTUVFd1IrZlVLdlV4Qmk0UmZUcmhpUENuRUhaUVAwRzdkV2M3RkxCc0lUcmhoYmM5MnRDZ0IrRlZOa2M1MkgweGE2aXFGMFA2THRzY3hsMmVEYm9CckdMUDdLUXA0eFU0VTRQOCtLUDVTUXBZU2hGRVJuQ2ppa09Qa25tT0wzVjdPTXM1R3lYTG9hVjM0TzZxdWhQQnJIMVNReXBFRWt4MjJHWWNtSUZwcE1VT3VqOStqWmhPc1R5Y09SQ2ZEY1ZUYmEyYjZLRUYvNUVOM3VqMEtRN291NTZxTkVpUDZ4R0R1ZS9hd29PaEJrYkNMZUFmN0ZsTnBId1hjTTkwYStnaU5NdjJKS3JEY1dXM3MiLCJtYWMiOiI1MTc1OWQyOWY0NjIzZTZjMGQ0MzJiMTc2YjQ0ZmJmYWVkMTBiMDZiYWQ5NjhiMmZmNjJiNTBmYzRiZTM3MDFlIiwidGFnIjoiIn0%3D; _clsk=1mcm0n0%7C1751129196017%7C67%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVxZ21zQXRQQkVoRlRVTHZQbGFod2c9PSIsInZhbHVlIjoic1M4bkYrSFcwckh3N3lESThxeHg2bXVXV29xUWNhVGpCT1QwSm4ycWhBcklTRXkxSjdvYSt6cG1FN1A5aFBWTEhPZDZXV21uRThQckNaL3E3TFovUUxJSDE4NnlCQzBBY0Z6OVhTUmJDM3Y0V3phK1dvZnRjM2w2MXBWYUhNcTRKRE4xNjJ2Vkw5SW1ZSVVnN1FGQ0tyT1J5ZVQ5dTdFRTZPTkhwZ09pOWpLRmFhSVU2dVhwS0pxalZBcFUwai9mYVJqaUltKzZLYzBmRllwd0JMSm11RlVsY1B3enhjUUhwdVFSUFE0ZHl6bGpFdlZpbm01Vnc2bnJBUjBZZW5EOXIzanVpY3RWQ0ZQZnpLa2paV3JpaEpaZWlNVFo3YTAxeHdQemM1a0IzcjduSm5Eb3hGNnM2SSswWko1YWczVWdIYUFCOHFVTEQvaklQMXljTDBKakZFeDArZ3B1NUdRcC9GdUxRbXN4dFd2MFYwcmRvbHQwaUV6RHlrVElmTDdMNk9KOVZZNHIyTFYxR1RSWnlET0hYVm50SEFpNTFzRFhYV0lKUmZrSWxoa2gvN1d3RFpNbGs2MzRaYWd5Q1VKcDl2emkrYkZIdGNNQkthS1RzZ3hhWjY2ME56cFRLR29Cbmp0SHFaMURRR2ZZREVWS2hRUkhoZHVEajNIL2dTeloiLCJtYWMiOiIyNWJkNjg2NzE3NWE0N2E3N2Y4ZDIwNmY3MTBjODAzMDY3ODFiMDgzZDk3OTA5NTZmOWFmOGM5NzJlM2UzMzQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjB3S3FObFVTaXV0dkZFNzNuQUluTlE9PSIsInZhbHVlIjoiSUMxVnZqOGQ0dGk5QXFHV2xxRWJtSXlTUTRMMjM4bnMreEtSakprSGxyanpxTzMwWmxiZ3NrRVFQZy9IV2dpUUl6bWNwbDlKdk5iMVE4RDFUTXdOQ254eDBNamhyWG9DdnJnZWpSdyt3Nmt3K3diby9sVGVyWkhGdHJzT0ltbVFwQzlFR3M1QVUyZnFUdFBrWUZRQ0ZrRmhqMnFsWVZwcWxDbndaNnV1MEtOUzdxTDlsemFyWWhSUFFEem02RzdUNnZsS0NFN2tuZFdRTUtXTEtMSHNlTDdhS3JCSGVteW40bG5aZ09kWjVLMXJPMUF2TlNnWFVaYTdOZFdBN3dlZzB2OXFZMWJlUkJtNmhQNXBmcEVUZ1QwNkpPeThHb1p4WVJ4YUR3RGFTamRhVUdtdzhiRE1xWWZxbzdkZDlnekJDaHBWZ3ZyWU5aUG14Ymt0SHl5bGppelJyVlV1bGFnUkg5NUhIN0NyTjJZYmdDRXk0a0JGNmNKODIyZXlidTlyVVY2VmhVRW9YUU12Q3pqRDNCUGlacjZnRkJwMjF6bVlvZkdleWRCT05PRDFKaUplMytOVldBS0hwME9STWErbkJUVjNzQUJtWlZtbFphc1ZxZnd3Vm9tbHcxY2x3MlBYMEJEUlZwbEpFVHdqenZBQTBOUllsaWYwUWVEV0tNS0wiLCJtYWMiOiJjNmI4MGFlYzkxYWI0MjgwM2ZmODQ1ZGFjOWYzY2MyZDI3NDdkMGVlNzk5ODU1Mjk3YzEyMjdiNzkyNTU5MWNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2039178860\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1442598358 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yEzaZJ27Y4UPdgz1Ok84k6McKnZQhYQlDiwGhQ8c</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442598358\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1760780187 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:47:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNFWExzelVZZWxocDM3dlJqdDdpclE9PSIsInZhbHVlIjoiK2pKYjlEUnZ5NG5ocEdLY0VkSkpVRGxNUE9FQ0ZYOVZYU3hUYWpTNGFKRE5mRml1S3pFRWpCbVJSTkhKRGNzVVB2ZG5vWFFMVkNxU0NoQjlSUDh1NzFoMlhYUXMzclYrMzhpaVJFZ1NkSk9xREVEdWl0MlFMbEtCd0ZManYrT2MzSUphVXRoZFZ5R0hkNytRT002Q0YvVCtKbWF5TGRzMGpXQS9jVWI2dk5pWXYrT2NXTEYzcWNBT2QwS0lrbzlNUTFTaXhCbGN3QldacDYzZ3FBcTZVQm1GUnQ5aDZWd2EzVmlOOFhjS1QwS01vVEd0SFJsZFN5N1dPaStnNUZJRmFBZHk1dDI5N0tKKzZzcGVwL2V5ajNONDV3dU9XZVpIbVRPRHpUYmNPRk1iQVRkNWk0YW55MnFKQklubTZDUUorM200RThZNEJxNVlXckQwKzIvV0JoZTZzWTJ2V2tuRUpHWTg2a0R4OXhiY0Q3M0FOYXNCOEdMZWN3VzIwa1ZFZmtwaWRFaURDWkJudzd5NWlZeUpNMk9LT1A4TFpSTjVTOHI2OXhxcDJxZ0wrYmp4YytidFNmaFJZekR3R2xkRzBMdjVPREVZR2Zsci9rbkxUS2hmTHE1V1F2Si93V0cxeG9RQlRacm5QNGJORlBFeHJWeE5pcS9NOStNd21RcVQiLCJtYWMiOiJmMDA1YWNkNDcwMzM4ZGU4MjJiZjg1YmFkNDY3ZDlkNTEzMGFlNzE4YTdhNjhjYWU5ZTc2NGVkMmU0NTg3ZGY3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:47:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InQreFVNRUtXMW1rdWRyTmtFMUIrbUE9PSIsInZhbHVlIjoiRnVHNHJiZjgxUUt5d002K2RtcDFLUSs3ZWlnRFFFRGVWUnYvOW11Ry9TU3l1emRsOTlZdTlCLzFVcTFZODJrNllFZGpVTS9VN0hBczlPaHRjK3c4b3duVXhpZUZwOWN2Y0JQR1hJdTl6NEpZbm9Bb21lajBlNGlJYkExWkE3RlV4K3pTUi9YeGhzZCtHblNpbkpQRmdKQkJDelFubkJ3VTZmYVd6TlpOc1FHRDN2SEN3SGVuNVBhYXFhaTV6a3hwWHVOY01HTHVsTU9qZ0Z1QkFMaUd2SFNiSS91Smpab255L3A4MG54bFp4UU83Nlh6VDRqbU1ZSWtJWk5od05tb3cxZytudHlMOUUzdmpURC9wWkJQYzI4cnVLTDlqcE1TMXd6aExiVkNYcWlSV3RQR0MyQjdWeU55MVVmWU9DMzJBWlJvTmlLc1N1T0pTajlqS3c3V2FlbmJuWEliUjFHYXBQL3FyRisxanE3MURDZnZGOW5zTTA0Q05DUDVZamE4amNaMkRMYmFTVTV4cmdxSVRubWVzQ1F0QXFnRHlsZnhNc1FodXNQdmZpWk53NEJob0sxOC9KOHlhY2RWazdEMDZ2bUtmbGZqYUQrTlc4M1UyeEVkc204Z0tzakpCeE0rdUVxTXVsYy94S3cxeFdzS005TlVab1ZtaDlBMS9TRGgiLCJtYWMiOiJlNDliY2IzODkxZjc4Y2ZmNTM3OWFlZjkwMDg1NDM5MzhjZmI0MDAyMmE4OWEwYmY5NDViM2QyZDZmYmNkMTYzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:47:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNFWExzelVZZWxocDM3dlJqdDdpclE9PSIsInZhbHVlIjoiK2pKYjlEUnZ5NG5ocEdLY0VkSkpVRGxNUE9FQ0ZYOVZYU3hUYWpTNGFKRE5mRml1S3pFRWpCbVJSTkhKRGNzVVB2ZG5vWFFMVkNxU0NoQjlSUDh1NzFoMlhYUXMzclYrMzhpaVJFZ1NkSk9xREVEdWl0MlFMbEtCd0ZManYrT2MzSUphVXRoZFZ5R0hkNytRT002Q0YvVCtKbWF5TGRzMGpXQS9jVWI2dk5pWXYrT2NXTEYzcWNBT2QwS0lrbzlNUTFTaXhCbGN3QldacDYzZ3FBcTZVQm1GUnQ5aDZWd2EzVmlOOFhjS1QwS01vVEd0SFJsZFN5N1dPaStnNUZJRmFBZHk1dDI5N0tKKzZzcGVwL2V5ajNONDV3dU9XZVpIbVRPRHpUYmNPRk1iQVRkNWk0YW55MnFKQklubTZDUUorM200RThZNEJxNVlXckQwKzIvV0JoZTZzWTJ2V2tuRUpHWTg2a0R4OXhiY0Q3M0FOYXNCOEdMZWN3VzIwa1ZFZmtwaWRFaURDWkJudzd5NWlZeUpNMk9LT1A4TFpSTjVTOHI2OXhxcDJxZ0wrYmp4YytidFNmaFJZekR3R2xkRzBMdjVPREVZR2Zsci9rbkxUS2hmTHE1V1F2Si93V0cxeG9RQlRacm5QNGJORlBFeHJWeE5pcS9NOStNd21RcVQiLCJtYWMiOiJmMDA1YWNkNDcwMzM4ZGU4MjJiZjg1YmFkNDY3ZDlkNTEzMGFlNzE4YTdhNjhjYWU5ZTc2NGVkMmU0NTg3ZGY3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:47:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InQreFVNRUtXMW1rdWRyTmtFMUIrbUE9PSIsInZhbHVlIjoiRnVHNHJiZjgxUUt5d002K2RtcDFLUSs3ZWlnRFFFRGVWUnYvOW11Ry9TU3l1emRsOTlZdTlCLzFVcTFZODJrNllFZGpVTS9VN0hBczlPaHRjK3c4b3duVXhpZUZwOWN2Y0JQR1hJdTl6NEpZbm9Bb21lajBlNGlJYkExWkE3RlV4K3pTUi9YeGhzZCtHblNpbkpQRmdKQkJDelFubkJ3VTZmYVd6TlpOc1FHRDN2SEN3SGVuNVBhYXFhaTV6a3hwWHVOY01HTHVsTU9qZ0Z1QkFMaUd2SFNiSS91Smpab255L3A4MG54bFp4UU83Nlh6VDRqbU1ZSWtJWk5od05tb3cxZytudHlMOUUzdmpURC9wWkJQYzI4cnVLTDlqcE1TMXd6aExiVkNYcWlSV3RQR0MyQjdWeU55MVVmWU9DMzJBWlJvTmlLc1N1T0pTajlqS3c3V2FlbmJuWEliUjFHYXBQL3FyRisxanE3MURDZnZGOW5zTTA0Q05DUDVZamE4amNaMkRMYmFTVTV4cmdxSVRubWVzQ1F0QXFnRHlsZnhNc1FodXNQdmZpWk53NEJob0sxOC9KOHlhY2RWazdEMDZ2bUtmbGZqYUQrTlc4M1UyeEVkc204Z0tzakpCeE0rdUVxTXVsYy94S3cxeFdzS005TlVab1ZtaDlBMS9TRGgiLCJtYWMiOiJlNDliY2IzODkxZjc4Y2ZmNTM3OWFlZjkwMDg1NDM5MzhjZmI0MDAyMmE4OWEwYmY5NDViM2QyZDZmYmNkMTYzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:47:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760780187\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1779638930 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779638930\", {\"maxDepth\":0})</script>\n"}}