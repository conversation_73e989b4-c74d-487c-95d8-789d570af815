{"__meta": {"id": "X1356d9cab50e0839402f208de3362db7", "datetime": "2025-06-28 16:30:29", "utime": **********.350662, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128228.931027, "end": **********.350679, "duration": 0.41965198516845703, "duration_str": "420ms", "measures": [{"label": "Booting", "start": 1751128228.931027, "relative_start": 0, "end": **********.29305, "relative_end": **********.29305, "duration": 0.36202311515808105, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.293059, "relative_start": 0.3620321750640869, "end": **********.350681, "relative_end": 2.1457672119140625e-06, "duration": 0.05762195587158203, "duration_str": "57.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45724296, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00262, "accumulated_duration_str": "2.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.319219, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.779}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.331938, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.779, "width_percent": 18.321}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.337162, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 77.099, "width_percent": 22.901}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImMwU2FScUI2OTZDQ29LYzdKMEllOWc9PSIsInZhbHVlIjoieW52bTQxTnpHZEttc3RxYVpzcU9EQT09IiwibWFjIjoiZDZjZmQ5OWY0OWRkMWI1YzE0YjgzMGU1ZmNkY2NjNTViMmUzOWJhZTMxNTQzZWYyNjQ0MWQ0MzM3MzRmM2UxMSIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1970884319 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1970884319\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2005746583 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImMwU2FScUI2OTZDQ29LYzdKMEllOWc9PSIsInZhbHVlIjoieW52bTQxTnpHZEttc3RxYVpzcU9EQT09IiwibWFjIjoiZDZjZmQ5OWY0OWRkMWI1YzE0YjgzMGU1ZmNkY2NjNTViMmUzOWJhZTMxNTQzZWYyNjQ0MWQ0MzM3MzRmM2UxMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128226556%7C48%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjV6Z0FwaEQwKzJYOGs5VW5XZC9NZXc9PSIsInZhbHVlIjoiSTdyZXFYc1hSN1pWV3N2Y3owOVRWTVdFb2d2UWJjeGlwUklEM0JSS25FQXRHVHI5eGxQUmIwNC9Lbnl1c2xnZXNzYm96TGt0eEpFVHRETnV4bE9zYVVMelYvdkw5V0toVmdQaXNoWVFBOTZvOUdxRjFLd1ZHdllVQm1hOURFL2RneGFBbzFnc0dFWHdUU2RIcmFIVEFSOUhMUys3bTJaY3FSRSsycmw1UUtZSmtkdnpPMHNReUl3eG9lQjdqK0RwT1JkZ0c5bTJUSEJKQWpBN0k0NzR6Y1NUeUVZdlM4alNCT1A2Z2VoVEV6K011SVFDeDlSRU1VZXFYS1A5alViWGpHYzM0M1drbUo3QWlmc1kzQUNSRCtXK1hpV3FBNk9mNkt6ekVkcWdBRi80NzhCV0ptS2g3WW56c2hEOEhhRXRGOWcwa1p6NFNqZUZTOHByYkhqTndDVnVaUFlqbGxIR25CRldkMm05dEhIaExsazBPVkdrWlZ2eUFhSXZtVnhSNGlNMlJ5NXI1NFUvZkVYNmx1R2NOM2tJY2Z2ZDZxbWRLNzByWWxaWDhyTzhOSkpycXNiSHRmSXo2dGgyTnZ5elpVYzFuSVJ4cTZKSkkyQlh5bXM4K1RCRzhpOVFIVnNUV3J3YTF0ejRuQnIrMGVXU2JPZXdhM1lubU05eitrVnoiLCJtYWMiOiI2YTliYjRkYmFiZmFkOTlhOTI2M2JiZmRhMWExZWNhODM5ODJjYTUwYTg2Zjg3MDY2NzBmMTg5NzM0YjE0OWVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJDTSt2R0RhcGxTSHphbUtOUEZlQWc9PSIsInZhbHVlIjoiVmxGTWxqYXFpMTdCRVc2eWRxS1I0N1VvYWtidDB2QkhVYU9ud00wRWU3QTRMc1pOc2VMd2ZNUDJLL3V3SWxUNjZNRkdERzgrc0JFZSs0NG5EL2N0UHltNUhGMlJDTmZhdDE4cnBJdzJ2bmhCUk1GVmxKUVpMSU94emRuWWcwSHVheDc3TmIzMGc0dUpzL1ZaU0JkSmtRQStJL1Y0TXNRekdjeEhobDFqbCtUbmRLM2RCNU1ZUW1NVk5HSHJLNkJpWFRySE1Lb3E2a24vQUdGRSs4b1BNeGhtY2tHMmZ1UEtpd1ljbElyak1XeXlDckdqa2VIZVEyS2V4enh4TmtMTithQWtqTC9FbmJ4Zmp2UVR5MHQ3Mko0R2UrY0NXd2l0TllsdEQ2a2pYR0Jzdk0zME5KOVJiUkFKa2J2b0paZmFqYWduWjh0dmFpVWdFTTJ6TUtMQWs4eW1zT0ptbDNMb1A1S1VYM2dTY2lnM2hDK1EwNXQ3Ky9SS2psdGpFVVkzeDgyNTdwQjlWOXNpc1B2ZEVEVnVVamJDZTdMZVA1enRRZTdYLzRoMTdkS3haU2NSVEVUSFNtaHBMMVBoTDUwY2crRXJ5TXQxSDgxT1NSQkRORlYzZ01FWjk5VzhVQWJVcDBST3hUZ1dnYSs0ajdwY1BwNzVwdDZ6cE9PMElNN0siLCJtYWMiOiJkOWQwMjgwMTgxMTY2MGI4MjU5YTlkZTViNjBhMTNkOTI2MzlmYjYzNWUwZTZlOWJkNThiNDQxZDNmY2NhZjBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005746583\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-554624790 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554624790\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-446327131 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRxWnBxNU1od2MxQUtkblB1OXV4eFE9PSIsInZhbHVlIjoiUkJ5VWlYeDNkTFVTUGZNZHVmR2IzdG14eFh6SWVlWW1tWUZNUzZJeWFrYStOVWFWQm81VXZieWJCcWFnYXpDVXYwSHB2emRMTUlOcjZKTlFzL2M3WGRCYUtvRHM2ekdXcThzZ0JsYkluNWRYS3Btd3ptNU1tZTdHVjlvUUpnQXBlMHRiYXBqaUNST2treHg2NEtyTGowUk1LckhDWGoxZWg1OE1kV3FsZ29hcXdhU3dRd09OSWFjeVJlZ3RucWtZWWlqVzRKRkdzc2RsdXk0aUxRRzVXYjlJYVQ5dGRkeTQzdXpMRndyWnZpZXN0Y0FVMUhpOVJWd1A0Wk5HS0lkT3lmK000T3ZxdWxwSTJZUVZKMnFVWjUybHZoaklscUlQOG1BZ2Viby9GdUU1REFpSnJXaFJiOHR1TmI3SGNkeE1nN2UzS3JyVERTWSszN2s3S2NNZWRXTzZ4OHBFdm1JZnZPcGZFczNMM0F2U0RUcE5PczBoTTFhcDFLRmkrNDlzN1lwWGJNRm9uTkJLRDI2Uk5CeFh5aitLU3lqNzlRUWM5WUI2QmRkRlpqMytJNGI1ZFJQS0RxMlRhUFFibThPMXgrY3JxRkhXdWhuTlM4MVhqZzUxOStMV0gyMmpPM2FQZnlsSkt3NjJnWXBYdCtjMWlIZG5WZHFPakxFTVlmdksiLCJtYWMiOiJhYjJmZThhMzJiYTViYWRkZjdkNWM0NDc2OTdmZjI5YjdiYjA3ZTM5NjY2NGU0Y2RjYTdhYjRhM2U2NTA3Zjg1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IklzeDdxdjRrTHFxcXdNQ0c4WFAzQ0E9PSIsInZhbHVlIjoiR2JQRUNtcVQ4OXdqSFd4c0JyVHZ2c29DNmlhazYrMlVHMTlrNi9DY2xLMEtReFg2dXYvZkhSNE1FVEFDVlJvWndLUnc5YkJKYThqZlQvZDE0bk42VUNnN203a0FtWWNSTUlEdGdXSDFxUEg0WS9zdDE0RjA3OGxsSG8vM1R0MmtvL2VEOC9TYzNEdXZvdVRGVkowcnhPYzdYSmRJNm5MNGp5d0c4YlRDa3BMRmNXTkVCSm9xa1NXRUlUT1BaVWcrZVFON1ltc0hjYXc2bG9VVnUzZUpDQ2MwRG9WRnVRTnFXK3Ezb2U3OGRST3R3WE54dmo0emNSQTZrSDlNemI0MTEzYklETmRUMkV4cGl2M2tTcUVXS0VYZ2Y1WWNDdUpJMmVIYXg3OEF3OUhrVzhKay95WGZCWEl0VksrQ1FIYStZVUtvV2RpaXBOUE5wekR6Q3FRUVJLUGVtbE9NaGRURnNDOUs2TVE5SDdBUm5lMll5ckpBMTc1cEVYTmN5VTNnRlovamFNam0rRitMVEZFbDdhSVlBeUtQdGNEdGlKclQ3dFhFbEt2QTg1cVg4N2daMEFQOEhuT2Q4RklFaFJWbWYwd3RXL0grQnowU1UyS1dQWFYrb1AvVHlhRVlvdzFNYnZGL2JtQmZLZlMzVWt3YWZxcjQwb2RyNmtmWEZBMlYiLCJtYWMiOiI1ZDllMGM2NmIzMzllMDg4ODFhNWQ3ZjY2YzVlOGYxY2VkNWZlM2JkN2Y4MGM1NjMzY2M0ZjVjMTRlMzlmY2RlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRxWnBxNU1od2MxQUtkblB1OXV4eFE9PSIsInZhbHVlIjoiUkJ5VWlYeDNkTFVTUGZNZHVmR2IzdG14eFh6SWVlWW1tWUZNUzZJeWFrYStOVWFWQm81VXZieWJCcWFnYXpDVXYwSHB2emRMTUlOcjZKTlFzL2M3WGRCYUtvRHM2ekdXcThzZ0JsYkluNWRYS3Btd3ptNU1tZTdHVjlvUUpnQXBlMHRiYXBqaUNST2treHg2NEtyTGowUk1LckhDWGoxZWg1OE1kV3FsZ29hcXdhU3dRd09OSWFjeVJlZ3RucWtZWWlqVzRKRkdzc2RsdXk0aUxRRzVXYjlJYVQ5dGRkeTQzdXpMRndyWnZpZXN0Y0FVMUhpOVJWd1A0Wk5HS0lkT3lmK000T3ZxdWxwSTJZUVZKMnFVWjUybHZoaklscUlQOG1BZ2Viby9GdUU1REFpSnJXaFJiOHR1TmI3SGNkeE1nN2UzS3JyVERTWSszN2s3S2NNZWRXTzZ4OHBFdm1JZnZPcGZFczNMM0F2U0RUcE5PczBoTTFhcDFLRmkrNDlzN1lwWGJNRm9uTkJLRDI2Uk5CeFh5aitLU3lqNzlRUWM5WUI2QmRkRlpqMytJNGI1ZFJQS0RxMlRhUFFibThPMXgrY3JxRkhXdWhuTlM4MVhqZzUxOStMV0gyMmpPM2FQZnlsSkt3NjJnWXBYdCtjMWlIZG5WZHFPakxFTVlmdksiLCJtYWMiOiJhYjJmZThhMzJiYTViYWRkZjdkNWM0NDc2OTdmZjI5YjdiYjA3ZTM5NjY2NGU0Y2RjYTdhYjRhM2U2NTA3Zjg1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IklzeDdxdjRrTHFxcXdNQ0c4WFAzQ0E9PSIsInZhbHVlIjoiR2JQRUNtcVQ4OXdqSFd4c0JyVHZ2c29DNmlhazYrMlVHMTlrNi9DY2xLMEtReFg2dXYvZkhSNE1FVEFDVlJvWndLUnc5YkJKYThqZlQvZDE0bk42VUNnN203a0FtWWNSTUlEdGdXSDFxUEg0WS9zdDE0RjA3OGxsSG8vM1R0MmtvL2VEOC9TYzNEdXZvdVRGVkowcnhPYzdYSmRJNm5MNGp5d0c4YlRDa3BMRmNXTkVCSm9xa1NXRUlUT1BaVWcrZVFON1ltc0hjYXc2bG9VVnUzZUpDQ2MwRG9WRnVRTnFXK3Ezb2U3OGRST3R3WE54dmo0emNSQTZrSDlNemI0MTEzYklETmRUMkV4cGl2M2tTcUVXS0VYZ2Y1WWNDdUpJMmVIYXg3OEF3OUhrVzhKay95WGZCWEl0VksrQ1FIYStZVUtvV2RpaXBOUE5wekR6Q3FRUVJLUGVtbE9NaGRURnNDOUs2TVE5SDdBUm5lMll5ckpBMTc1cEVYTmN5VTNnRlovamFNam0rRitMVEZFbDdhSVlBeUtQdGNEdGlKclQ3dFhFbEt2QTg1cVg4N2daMEFQOEhuT2Q4RklFaFJWbWYwd3RXL0grQnowU1UyS1dQWFYrb1AvVHlhRVlvdzFNYnZGL2JtQmZLZlMzVWt3YWZxcjQwb2RyNmtmWEZBMlYiLCJtYWMiOiI1ZDllMGM2NmIzMzllMDg4ODFhNWQ3ZjY2YzVlOGYxY2VkNWZlM2JkN2Y4MGM1NjMzY2M0ZjVjMTRlMzlmY2RlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446327131\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1025499950 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImMwU2FScUI2OTZDQ29LYzdKMEllOWc9PSIsInZhbHVlIjoieW52bTQxTnpHZEttc3RxYVpzcU9EQT09IiwibWFjIjoiZDZjZmQ5OWY0OWRkMWI1YzE0YjgzMGU1ZmNkY2NjNTViMmUzOWJhZTMxNTQzZWYyNjQ0MWQ0MzM3MzRmM2UxMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025499950\", {\"maxDepth\":0})</script>\n"}}