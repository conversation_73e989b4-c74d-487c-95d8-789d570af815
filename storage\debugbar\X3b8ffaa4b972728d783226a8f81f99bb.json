{"__meta": {"id": "X3b8ffaa4b972728d783226a8f81f99bb", "datetime": "2025-06-28 11:21:29", "utime": **********.377911, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109688.956654, "end": **********.377929, "duration": 0.42127490043640137, "duration_str": "421ms", "measures": [{"label": "Booting", "start": 1751109688.956654, "relative_start": 0, "end": **********.280625, "relative_end": **********.280625, "duration": 0.3239710330963135, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.280634, "relative_start": 0.32397985458374023, "end": **********.377931, "relative_end": 2.1457672119140625e-06, "duration": 0.09729719161987305, "duration_str": "97.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43801872, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.020489999999999998, "accumulated_duration_str": "20.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.307738, "duration": 0.02022, "duration_str": "20.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.682}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.330996, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 98.682, "width_percent": 1.318}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "y929FO0OR2qYd721zZmtMrJIBFsWgMiMXXHdl0OR", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1147975896 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1147975896\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2037690944 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2037690944\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=lntfvz%7C1751069310598%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklLcVN3WjNHUnhSMlRZSE5FakpDL2c9PSIsInZhbHVlIjoia0c3NTM5UGJCbU9qQWtGMDhQZmdISVQxTlRjOE5kdDEzMWYvNXJYV2JQdklNK3dHUXRWZjN2NFpteWZJcUFuN1E3Rm9MSlhwMitsRkJoQmpsbzBYa1hjR1h3MkUzSnRZTS9pc0ZVSVEzQ01lUkdSbmJKV016ZVRlVmpFRTNmMXRGd3hiNFBFSWt0SzVEZXBYU0lDN3JQK1BiOHNSU21vNjAzZFVaTzhBbXExQmdacW1PdkdvUnBOcklrVERmYnB4TitwR2t2Q2pOWkxHekkzWWovRTVBZks1ZUVZVkZFYUwyaW1GaUFXdTVNbU9XcnQvSTl5N0xZcSt0YXZ5VHNqbVllYTRTeE1RVml1V0xpWUtSdW9GR3doMWpVZ1kxNTVsaFJDeVpXa2taU09FQXBwUEV0OWp3aUtjVWFkV05sOGJleHUvL0tMS1FmbkF6cHlVMHZZUGxJN3lPamlMT3NrQ0k2STFtdUNuR3M3SzlQaXFiQjduNUJkclhCKzZpSkhiMTFjSlJoQ01Cd3hNRWNvY3dOVSt1OFQ1cEFHRlMrU2luWUFaTFlkYlNkc0ZqT1JWdlgyb1ptQzdzTVFOaHlId1J1T0hZVE42bnJUZGs5MXcyZVMvTXhuOUVGM0hkQUlpRk92MVVJSzhocmhkczZzYXdLNXBrTWlzdHV3dzZpY1giLCJtYWMiOiI2NTVjYmE0MTZiZjZmMmVlMDQ0ZTY2ZWZlZTVmNTllYjg4NmYzYTliZmQ2Yzk0ZDViOTcyNTZhY2Q2YzBmZmVhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjRaSXozWkJTY0MyWTF1aTV6WjBua0E9PSIsInZhbHVlIjoicHJ6Q1dNLzJNeHhUcmRKMlZaMk9Va20zbGo5L3dVcm0wdEJEQ2lxeHFiVDhybXhoUjdHMXpyUEhwOWpaUVJ6R1B3dkhIaFoxcjJ5VElvbGtHQkNUUGJnMWJIWlBOWmJwRmpjLzV1clFNUUdLUUVsTUE1V2NvamVXN3hXNm12VEZlcFBTQnZYbkFnOGErQm5nVmV1TkIwL1l1M0ljQ05UL296YkI5cmJkVi9PNTRBNndRWE52cUk5TEdETm5mMzZlSE91K0JmOVBQVTN0bCtkbEw1MGdWc2Izd2R5cFpHeG80ZE9IOUxjY0Y4RkV4ZkhnR2VBeXJUWTJ4dDN1d1pBSDd0ZXQwYUFVK3BJcWFBUks0QUQ0cWtNUkQyazJoUFgzRGRJOWcrSHh6N3M5Wm1xeEpKdHg2K25QcjAwdWlHRnFHZVQ2SStIVWhqaXhKTzA5KzdxVi9Ma3RDU3Qrb3lnZE9GeUJqMURWK3BpQ1BUdDVpVjkrQ2NtV0NoQnhSMjFvTG1CZDU5dkZIQ2hHVXY2cVhJMTBZb2E1WnJHSmQvV2tGRVhEcEo1K0pOS2N2bXV5YVFyMjhXbG9jdU1uRW1lb3RhVnNldGFjUE43OGpkRzRkdlRaOWJVN2Q1NTRLVDlmWk9KNjNZbmZEUlkrVThUWUZqMFhsWC9vcTBHdjBDQkwiLCJtYWMiOiI1NTY4ZWU1NmUyODZjYWE0OGNiZTQ5NmIzYmJkNDk3MWZkNmY0NWRlZTQyODZlY2JhMTI0ZjczOWI5MGEwNTI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-279088766 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">y929FO0OR2qYd721zZmtMrJIBFsWgMiMXXHdl0OR</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2hP8eHBMtk5urSHEGR7nQ11X4spn4BWThBS2vTaS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-279088766\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-351790464 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:21:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhsV0g1ZzdKYzVaQXdBc2E3RENQZ0E9PSIsInZhbHVlIjoiWVVVUm5RL0VTczB5cDhrR1JiNDREbSt5M3VJTEZvQlBKc0ZpUUhIQXZRV3pjeTk5MWFUUmhsQi9jVDVXK0xmNjQ1a1ZxNE5BM3RyNmJZUytPdVV0T2ViMHRsN2lSeGxGZHl6Nnk4Y215WUFyNjVLZHRXelZyQWlFY3VsRjcrbW5UTWFrbTRHZ3phNDVXNTFNZ2FQK1ozb3ZnVVZBNDZCQkZwaXN4NUc3UTlmR21uYmNUVlBQSGNQSnpqUFRRUTBNUVVZR24rcDlNRFhUa0x1MEljZExUVExnZ21xM3EvYmNxWHlveG9pM3piSG5lc2k2SldCbWF6UWhodjVwZElhcHU1TXFURm1oSFVqK0puUWRSU3VNeFo4azk3Vm4xS3pEbEZQMHgyRGhnQ0dudXAyM1g3cU1EN3A3Z3htZmdpT3BpUmt3ZVRKaVpDcFhhQXV2Z05xYWIvSnFrck1VM1BDY2s1RUlmT0FzNWUrYW8rdEdKOGorMjM0cmQ1R200cm1JRm5vbFVzUTB1ZElKSlJWcnVRR3J5eEdwU1o2SVhNbGZra3FncDkrZzR3NVd1bVB6RVMyNUlmVVh6TUhneENtLytSaWlrWWhzM0dkeVQrZlVJRW40ZWNEWklWWWk0RXlsdXYyQ28yaWZPRHcyVGc4TFV5Q3RqMnJKcURGZFNFcUIiLCJtYWMiOiI0NzZlNWU0ZTJlZjYzNGI1MDZhYjdlNWNkZWEwODQxZjQyZDk4ZGI5NmIwZjQ1ZDczMThkNGQzMTQ0NmQ2MzBkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:21:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Iml4T01uZHA0WVh3RmFzTHcrbXp2K1E9PSIsInZhbHVlIjoiYzdoNzRJaFUwQk9QSUQ0ai9QQTk1N0tsclVNVnlWbTRBOTdhN3hyTTZrYWErQ0NuT29yL0lBcEVPdTZoQmUrUnhaU1J3bnpJQS8ydGRQeVhVVU0vRlZMcW1tc05lNGFTUTh4bHZ2cjRYL2tiUE8xb21lVjhodExId3JSRmRyM1NsUjdGamljNlpkMTNGa1dUWjVpNGZtWHNQTStQdk41ZTlETDdYZVdvN2xma1RpYnRSanhDYVppYUdJT1RYZmowNFR1NDNqM2xzRlMzM1JIM1BPbnV2U2pVUVAwRmxhdkRhNHZsNGU1eFptV1o4dSt6WnF4UVc4TVRJdmdQL0Y5aDBLRjQ3UHdqZ2JUWUVIWVI0dVptaDRoS204NzNqVDFLSEtWeEFoNk1wTXBjU1BxUlltOUxyNGlCNmxXeDFuUHhJb25taVNRNTIvT0h5UXVCQ0tkeFBHeEhUWkovcjcrK3cyeWY5TzFWMTNQSFhsc3RFSmxlQ3BkTzc3V1NxWXA5Rytua1VFTVFsMGdYNGFNZXBVRVRVa0xXV3hmdi9GRVlEZXZZMGJCb1M5SGc2TUlvZVAxV0VmMXBqQlBrWi8vSGZrY1MvUFkrbUZxZU1sbi9SdkhxajJnNkZXWTgwa1FOeXJqSmhSUWxyZk1GeGsxcUZIVkhLVEl6cEUva3FobDgiLCJtYWMiOiJhZWY4MGM4ODE1Y2E2MzYwNzBiZjJjOGE3M2ZhNGQzY2U4MjY0ZTA4MDFhMTM1ZmY1ZTcwOTExNDY0YTRkNDBiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:21:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhsV0g1ZzdKYzVaQXdBc2E3RENQZ0E9PSIsInZhbHVlIjoiWVVVUm5RL0VTczB5cDhrR1JiNDREbSt5M3VJTEZvQlBKc0ZpUUhIQXZRV3pjeTk5MWFUUmhsQi9jVDVXK0xmNjQ1a1ZxNE5BM3RyNmJZUytPdVV0T2ViMHRsN2lSeGxGZHl6Nnk4Y215WUFyNjVLZHRXelZyQWlFY3VsRjcrbW5UTWFrbTRHZ3phNDVXNTFNZ2FQK1ozb3ZnVVZBNDZCQkZwaXN4NUc3UTlmR21uYmNUVlBQSGNQSnpqUFRRUTBNUVVZR24rcDlNRFhUa0x1MEljZExUVExnZ21xM3EvYmNxWHlveG9pM3piSG5lc2k2SldCbWF6UWhodjVwZElhcHU1TXFURm1oSFVqK0puUWRSU3VNeFo4azk3Vm4xS3pEbEZQMHgyRGhnQ0dudXAyM1g3cU1EN3A3Z3htZmdpT3BpUmt3ZVRKaVpDcFhhQXV2Z05xYWIvSnFrck1VM1BDY2s1RUlmT0FzNWUrYW8rdEdKOGorMjM0cmQ1R200cm1JRm5vbFVzUTB1ZElKSlJWcnVRR3J5eEdwU1o2SVhNbGZra3FncDkrZzR3NVd1bVB6RVMyNUlmVVh6TUhneENtLytSaWlrWWhzM0dkeVQrZlVJRW40ZWNEWklWWWk0RXlsdXYyQ28yaWZPRHcyVGc4TFV5Q3RqMnJKcURGZFNFcUIiLCJtYWMiOiI0NzZlNWU0ZTJlZjYzNGI1MDZhYjdlNWNkZWEwODQxZjQyZDk4ZGI5NmIwZjQ1ZDczMThkNGQzMTQ0NmQ2MzBkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:21:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Iml4T01uZHA0WVh3RmFzTHcrbXp2K1E9PSIsInZhbHVlIjoiYzdoNzRJaFUwQk9QSUQ0ai9QQTk1N0tsclVNVnlWbTRBOTdhN3hyTTZrYWErQ0NuT29yL0lBcEVPdTZoQmUrUnhaU1J3bnpJQS8ydGRQeVhVVU0vRlZMcW1tc05lNGFTUTh4bHZ2cjRYL2tiUE8xb21lVjhodExId3JSRmRyM1NsUjdGamljNlpkMTNGa1dUWjVpNGZtWHNQTStQdk41ZTlETDdYZVdvN2xma1RpYnRSanhDYVppYUdJT1RYZmowNFR1NDNqM2xzRlMzM1JIM1BPbnV2U2pVUVAwRmxhdkRhNHZsNGU1eFptV1o4dSt6WnF4UVc4TVRJdmdQL0Y5aDBLRjQ3UHdqZ2JUWUVIWVI0dVptaDRoS204NzNqVDFLSEtWeEFoNk1wTXBjU1BxUlltOUxyNGlCNmxXeDFuUHhJb25taVNRNTIvT0h5UXVCQ0tkeFBHeEhUWkovcjcrK3cyeWY5TzFWMTNQSFhsc3RFSmxlQ3BkTzc3V1NxWXA5Rytua1VFTVFsMGdYNGFNZXBVRVRVa0xXV3hmdi9GRVlEZXZZMGJCb1M5SGc2TUlvZVAxV0VmMXBqQlBrWi8vSGZrY1MvUFkrbUZxZU1sbi9SdkhxajJnNkZXWTgwa1FOeXJqSmhSUWxyZk1GeGsxcUZIVkhLVEl6cEUva3FobDgiLCJtYWMiOiJhZWY4MGM4ODE1Y2E2MzYwNzBiZjJjOGE3M2ZhNGQzY2U4MjY0ZTA4MDFhMTM1ZmY1ZTcwOTExNDY0YTRkNDBiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:21:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351790464\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1009116440 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">y929FO0OR2qYd721zZmtMrJIBFsWgMiMXXHdl0OR</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009116440\", {\"maxDepth\":0})</script>\n"}}