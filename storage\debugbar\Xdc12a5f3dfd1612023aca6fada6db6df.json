{"__meta": {"id": "Xdc12a5f3dfd1612023aca6fada6db6df", "datetime": "2025-06-28 16:30:40", "utime": **********.858315, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[16:30:40] LOG.info: Enhanced POS Add to Cart Request: {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"price\": 2.99,\n    \"name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\",\n    \"is_manual\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.849453, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:40] LOG.info: Manual product check passed {\n    \"is_manual_value\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.849714, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:40] LOG.info: Product found successfully {\n    \"product_id\": \"2299\",\n    \"product_name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.852005, "xdebug_link": null, "collector": "log"}, {"message": "[16:30:40] LOG.info: Product added to Enhanced POS cart successfully {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"cart_count\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.852113, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.452334, "end": **********.858332, "duration": 0.40599799156188965, "duration_str": "406ms", "measures": [{"label": "Booting", "start": **********.452334, "relative_start": 0, "end": **********.784203, "relative_end": **********.784203, "duration": 0.33186912536621094, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.784213, "relative_start": 0.3318791389465332, "end": **********.858334, "relative_end": 2.1457672119140625e-06, "duration": 0.07412099838256836, "duration_str": "74.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46035208, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2015\" onclick=\"\">app/Http/Controllers/PosController.php:2015-2120</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023600000000000003, "accumulated_duration_str": "23.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.816186, "duration": 0.022670000000000003, "duration_str": "22.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.059}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.846704, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.059, "width_percent": 2.161}, {"sql": "select * from `product_services` where `product_services`.`id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2053}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8504798, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2053", "source": "app/Http/Controllers/PosController.php:2053", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2053", "ajax": false, "filename": "PosController.php", "line": "2053"}, "connection": "kdmkjkqknb", "start_percent": 98.22, "width_percent": 1.78}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-1413111166 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1413111166\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1917608505 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1917608505\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2048144990 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2048144990\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128238007%7C50%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVTU1hSVTZ2ZGwyQzUzVmEvYXlOMUE9PSIsInZhbHVlIjoiK1kwSFZIeEFPcG5hdkc1alI4VHF3RndHcHpwQjV2dVhETnRIMnJvdW9SN3hYWVZ0dUQvSm1NNWpGcXFaY05QdzB4UjJTS05mSDgwcW5qTUk0VjRCOThhMG5acnloVG5hb3BKNTQrTW1xMERwaWN6TnFHYkxDZW8yVzhGN2JjaS9YekZXQ2VKZmU0ZnY1SlV4eTVya1YvZ2NNMGFWSWM5YW1XOTdMUVc1UlpZeTJ1NzArUGpjTEpWL3BCQzJRc1AyWjJQU3hQenJIc3phYVBFWHZEVi9oWnBxQnpWMDJqUUZjajZ2VDliVjlsRmJhY3hnSWtJcFBlOFlxcWtMOW9zYW5NVkpaMGxEeGcwTWpDVzk4a0VTKzhZcUZYN2hvM2g0RlhPMTlwMi8yVTlsUXNSWlpwNC93S1d1azZQTDNqaXFuQXNZMzZtWVVpbHFFZUZ3R1NoMXo2VEZOUlJNUklMZW1VT1Z5MEN5MDljUVNsQSsrekU5ME0vaEhpSUE1ZGRrcFo2MG4vdjljM2xKYm1pbUNMUDdxZ2xVU1pHTWQ1T3h6WG9vMUpQOS8rWFR0UStJdW1UMy8yaUZOTUU1U0hXeThJUXNuM2x2Smt5dWl3cTlUbTAyQXl1UW5rQjdYemNSZ3RFMmhKSUVLUExXMnRZSDNVbE1NY2dSUy8welRHQWciLCJtYWMiOiIzNTQ2ZjNjNDA4NTVlMTAxZDhmODhjNmVmZTcwMThhZGUzNGVlMGQ5MzhjODE5NTVmNmUxMjczMTAzYTdhYzBhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ijl3NFlNQkNFRHJzN0lVZkZYNDdNeFE9PSIsInZhbHVlIjoienU2SWppZzNUY0RFVzMwc2c3dzdkRnlFN25FQlZLWVRFSWFIYndvQ2ZjcENCTWlLd0pRZno4K05iM0F1Lys4aWNsalZLQm9LYVE2RUc1Sys5WUY3c0JxMGdhNlU4SSsrVUdYblVtUndsMTNrTVJDOUpTTmJuVFBvNXhEZnhmbDBjWGFPR2hKT2hzdXZkdDEzK2M0THdNZ0U0NDJNRDFvdUxKWHlXYXYybkhacXJJRHpISlJZelhSbDVWTEtPbXdPaEFrNmNNbmdhYURibHo0cnBpRGZXdExQbG5OZ2dmRnBwdERmNWZ4NjJWR2NzR1hDODNZU3RBTUpSckZPR3ROK0F6NkxrY3dtQkR2cU1tUmVzMUluRi81b2wrYlBRZFZSaWhmbXZvOVZzVmwrL1FWQ1JHbXZYMWU3OHp2Q1Z5bWR4aFBMakVmN2VJS0RwQS8wbGVNTHFyUkFqUVFCNnUzZmtNaWJTQjg1MHhZTUJiQkh2RFRlQ0liWCtDNW1FV2swL2pkU3dBeDVuQmlpMS9HTHR4dmFZY29oUlllUFY3dXgwRWwxS1NvNTRXeHNFOHRkS0tRanJCdVBSdmt4U2tPZjQvOWZuQkovNk1tQ2hhaFE3L1p1TXo2Qm5rVStXcmVRUnhCQ3dYVWVHQUp6a3pEdDdFc3cvcTVxOGJCSFErVWUiLCJtYWMiOiI4ZmE4ZGJhNmVlNGQ5NGQwYmY4MjgzZTVjYTMxZjQ3OTAwMmIzNDU4YmEyOTg4MjJjMWQ4ZDJmNWRmNWMyZTkyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-964134935 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-964134935\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1187537259 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlcrTms2WUZYem5jeW1PaHNjZ1F5c0E9PSIsInZhbHVlIjoiZUMvUENFWjlnaUQrajExZ1JiVTkza1BHZlduRjN6N1RRU0c5bXFVUnBQdXB4Wm1lY2FpQkY2SGFEY1JhSm9DVGwxV2ZsZk9STnRaNVg3U2NFYkZRcmhGRmZ3MXBud1hTQ2t3TExYYW1mWFFkS1BVVnBsajV0V0Q4dkd4Mkh1YmZTcitDR0I1MXJkbUZWR2VCOEQ0SUFzM2Z4aEF2ZUlwamRrdWhHWVBaUFEyZXV2SXBhOUpYbWFBL1Bwc0x1VnhyZ3VmbFd5UmprSDd2TkFmWk92V3ZvSkFCNzFIZlB0N1d0NkZQZVQ4WG1kekRReGViMlplcElkU3lvak5OakEwb0ZrMUpmNGF3aHBHT3J5RnZnTm5xVDZtdFZEK0gzSW5mRGdJbHkxd2hZQk5UeTdPSTNVb2cwMlY3RDNubDU5dHdQdU9CT0VCcmxJUlJBQTJjWVRMcC9PaXFkMmNQVTFqOFN3ZVlkdHlBaVZoL1VFR1NyT25URmF6RUdxMEU4UjR1RWFIcFQ4RHU0dVc5RFNJUERvRXdHYUdmdFFDKzY3MVJXQmlORVU1QmZEanFoYXJYTUh0VFhGUEFsRjg1MGpSaktxZ1ZiUGlSeHRSTE4ybkF2T1VrQTdnUk5nMHlUYzFsaUVjVm1JS1Y4K0k4bXJucWoyM09nc2tScWFkUUkzT24iLCJtYWMiOiJjMWExZDUxYjJiMDA1OTljNjZmYTA1MDQ1NmY3NDcyYTBhODk1OGY2NTk0N2Y4ZDBiYzFhNjk4MTI2NzM0ZmNkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRNM0d1YkNnVEtSUlpMV0VsSStqdWc9PSIsInZhbHVlIjoiU1Y3STVTblQweUt5Sm0weU11c2piTjgrY3VKbnBkVHd3NnJkWHFyVlhta0hwS0s5WlpaSitvSUUrcmJtRUlDNHJoY2RqdFMyZmZMTEJHaEVpWHZPSXlkQWVackVwcGcvMHl3UzFMdHpJK1FYMFZxUHUveFZ3NVFXZWR0NDAwZ2xtbVRqUGpXbi82aWNFckwzL0txeDhIRWg2MlQvK3lCNWJoU01tS1pNdXhWZTNZNjNVWlNqWjJmMUxLcXRjRE5xcFNHRHlmbVpkTVVyVytCaDg3ZlFQTmQ2Qjg2elZQT0t2bit4YlMzVjQ4MEhxZUc3d2tFQkJUT1UvVGJSYmpzL3hES20wM2l1MEJRWTd5Q0FRQ2o0NytiU0JKcEF5VHVCZGhBOTRRa2RmWkZkejdDNG96amVvRVZ0b3BFTkF1L3NhWFZ2d3JMenRSZU00YTUyRVNjZW01aTkyRVpsRHA4L1duSTZhNjk5cGVNUS9EdmRWdERIVnJMckRzVUxhZzVpSlZnU1ZuSVA4aC9iM2M2dkdKdndDK21lSTFQVnJ5ZTFYMURaVktrWmNaVkxoWU1pbm5CY296YmJMRXVtWjlIckJDenRPVEEyRWJRTkVRcENvVGR4OWNFSzhXRUhSZXlJU0hMUERsTlFsUU0wdVlMKzNSSlE2N0p2SnhzdVRMcGgiLCJtYWMiOiI1MTQ2YWFiZjM0ZWQwMDdiZDI0MTM4NzQzMTc0YjVkYzllMGQ5ZDQzZTU5OWFlZWQwMmIyYjg5MGZjY2M1NjQ2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlcrTms2WUZYem5jeW1PaHNjZ1F5c0E9PSIsInZhbHVlIjoiZUMvUENFWjlnaUQrajExZ1JiVTkza1BHZlduRjN6N1RRU0c5bXFVUnBQdXB4Wm1lY2FpQkY2SGFEY1JhSm9DVGwxV2ZsZk9STnRaNVg3U2NFYkZRcmhGRmZ3MXBud1hTQ2t3TExYYW1mWFFkS1BVVnBsajV0V0Q4dkd4Mkh1YmZTcitDR0I1MXJkbUZWR2VCOEQ0SUFzM2Z4aEF2ZUlwamRrdWhHWVBaUFEyZXV2SXBhOUpYbWFBL1Bwc0x1VnhyZ3VmbFd5UmprSDd2TkFmWk92V3ZvSkFCNzFIZlB0N1d0NkZQZVQ4WG1kekRReGViMlplcElkU3lvak5OakEwb0ZrMUpmNGF3aHBHT3J5RnZnTm5xVDZtdFZEK0gzSW5mRGdJbHkxd2hZQk5UeTdPSTNVb2cwMlY3RDNubDU5dHdQdU9CT0VCcmxJUlJBQTJjWVRMcC9PaXFkMmNQVTFqOFN3ZVlkdHlBaVZoL1VFR1NyT25URmF6RUdxMEU4UjR1RWFIcFQ4RHU0dVc5RFNJUERvRXdHYUdmdFFDKzY3MVJXQmlORVU1QmZEanFoYXJYTUh0VFhGUEFsRjg1MGpSaktxZ1ZiUGlSeHRSTE4ybkF2T1VrQTdnUk5nMHlUYzFsaUVjVm1JS1Y4K0k4bXJucWoyM09nc2tScWFkUUkzT24iLCJtYWMiOiJjMWExZDUxYjJiMDA1OTljNjZmYTA1MDQ1NmY3NDcyYTBhODk1OGY2NTk0N2Y4ZDBiYzFhNjk4MTI2NzM0ZmNkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRNM0d1YkNnVEtSUlpMV0VsSStqdWc9PSIsInZhbHVlIjoiU1Y3STVTblQweUt5Sm0weU11c2piTjgrY3VKbnBkVHd3NnJkWHFyVlhta0hwS0s5WlpaSitvSUUrcmJtRUlDNHJoY2RqdFMyZmZMTEJHaEVpWHZPSXlkQWVackVwcGcvMHl3UzFMdHpJK1FYMFZxUHUveFZ3NVFXZWR0NDAwZ2xtbVRqUGpXbi82aWNFckwzL0txeDhIRWg2MlQvK3lCNWJoU01tS1pNdXhWZTNZNjNVWlNqWjJmMUxLcXRjRE5xcFNHRHlmbVpkTVVyVytCaDg3ZlFQTmQ2Qjg2elZQT0t2bit4YlMzVjQ4MEhxZUc3d2tFQkJUT1UvVGJSYmpzL3hES20wM2l1MEJRWTd5Q0FRQ2o0NytiU0JKcEF5VHVCZGhBOTRRa2RmWkZkejdDNG96amVvRVZ0b3BFTkF1L3NhWFZ2d3JMenRSZU00YTUyRVNjZW01aTkyRVpsRHA4L1duSTZhNjk5cGVNUS9EdmRWdERIVnJMckRzVUxhZzVpSlZnU1ZuSVA4aC9iM2M2dkdKdndDK21lSTFQVnJ5ZTFYMURaVktrWmNaVkxoWU1pbm5CY296YmJMRXVtWjlIckJDenRPVEEyRWJRTkVRcENvVGR4OWNFSzhXRUhSZXlJU0hMUERsTlFsUU0wdVlMKzNSSlE2N0p2SnhzdVRMcGgiLCJtYWMiOiI1MTQ2YWFiZjM0ZWQwMDdiZDI0MTM4NzQzMTc0YjVkYzllMGQ5ZDQzZTU5OWFlZWQwMmIyYjg5MGZjY2M1NjQ2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1187537259\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}