{"__meta": {"id": "Xf857c30fc0f60079ba95120d9d795686", "datetime": "2025-06-28 16:17:29", "utime": **********.785577, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.335921, "end": **********.78559, "duration": 0.44966888427734375, "duration_str": "450ms", "measures": [{"label": "Booting", "start": **********.335921, "relative_start": 0, "end": **********.723136, "relative_end": **********.723136, "duration": 0.38721489906311035, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.723146, "relative_start": 0.3872249126434326, "end": **********.785592, "relative_end": 2.1457672119140625e-06, "duration": 0.06244611740112305, "duration_str": "62.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46425152, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2441\" onclick=\"\">app/Http/Controllers/PosController.php:2441-2475</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0025, "accumulated_duration_str": "2.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.767299, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.6}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.777544, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.6, "width_percent": 22.4}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-950678950 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-950678950\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-91484776 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-91484776\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1853383268 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1853383268\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1972144757 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127024092%7C32%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ing3bkZFUkloSHBmazFSRTJuanpqamc9PSIsInZhbHVlIjoibnlXOFVmbFVobFJrSEVvWU1UQ2JRNjdHektQWERLU21nVmc5Wi9oMjZwd2ZiMUQrNUtyZ1AzLzEyS2hWbzZyd2RMRXBtM2JVYU1hb1k0K0dDcHZCVzRvcXhZMXR4eGdjZ2s3aHFTMXpYcDNYTUNzZFhMbDJtdWo3NEV3WENXeTNPYlhtclkydVRQRUY4ZGEyZVBobFNoSDNYZkV1TDRXSlFyYzBwVUVlbjMyQkJkMlUzVHFLem40MHpVdnFaWW1waFVNUk56bWdmTFV5M21POVJnLzFXaUlLYWVKYTNKVUtMd3J4N3BMSmZrOXBLVnVLSVlXek8yVTJheHFxeGZPcnA5bWhPcnFpRzV2cWxPdE9zZDVQcVVTajhFL1BnSCs5VXFtMWhrL1czQmo3VGFrMGVhbXl6dnZ1WWRWTlZqMXpMZUd5UyswT3EyeDE3ZVV4UlFkdGluZ0N3UmhkM3VDNjZlalY2TFNaeG9IMlA4c0hxSm1Od2xXRHlOdUtjaWxsb0d0S3FVZy9JNVVGR1V6STJvS0FFSjloK3JtRjZiLzhIWEprR3piZnRiVGFFSE9TdHY5cmhmTi90NFU0QURXTHpKQ3M4T09uczF1R1EzZVpqSk9MdG1SMG83Zmc5RTk1YTNnTUQ3TVpkNnAxL1NpeitPQTNObFJVcFVVeVl1SWQiLCJtYWMiOiI5OWY4MmIxOTNmYTM0OWYzZWI4NTY3ZjE1YjM1N2Q1ZjZkNGY5NTFiYWNmMGI4OGRiM2RiOWVjYzYxMjEwYmM0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhqVm56c2t6eGQvRndnQzlNNTlGemc9PSIsInZhbHVlIjoibEVoOGNSUk1pbno4bUtodmkwcjRDOEswREZSd3JQT1h6SkNVMThtOHRQRkx3UjQvR3VKS3pxZXpwbnQ0K3hGU0swTjJjMFFjQzVQTXR6R0M1dEt4WXEvUWFqZzZPR0NyaUpQN3B5Y21iRjZmS1UyKzJHRjlobnFsV0JSa2tSSEp4VDkwZVlweVR1d0NsbjZVSTk2LzhWcW9yRk1SYlNwWVZzcGkxU0lweDAwa2JMSjZyT2o2aGNMWjNSd2VSNmNleGRvay84Mml1M0E0NXJVVExESm1TYzhNeHkwcG1TaUNaVTNVMFVjeDZiZS9iWWx6RU5lTEVEUWM0cWVQQk1ubnR6Nm9KTkFZUGZCWDhSZFd6WG91SWZ1Ym5FWFNwLzZBWjFtOUdHVkwwamxUT0ZndDhNajhLY2ttUks1d0pKdzI4SG1iZFI5QjhWTGkrallTVndFb1FEWkNzakt3YzRiU0Y3V05iTXUyUzNaQnRNOHduWGZ4OTJBQktWVjROU0JMNjcrSlZEYndzZWQ3VEVPWlA4T2RxN2JHbDJnMVVTWDFUa0N3NTloOFhvUXN2aUdxa1VnV2tFaTZUSngyM1J0QllHUHJsQ0pPUTFaZE9EN0pKWHZyQVA4V1I2dTU2aWtkUVVUcW9QbzlnY04zczQxVjhLSFRQWTJZU29qWDBqeUsiLCJtYWMiOiIxNzYwNDUxOWI5NTcxMTQ0OGQwZTRiNmE4YTQ5YzYxZDMzNjNiNGVjZDUwNTJjMTA1YTNkZjhjNWI2Y2YwOTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972144757\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-843843799 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-843843799\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-833028970 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:17:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpNNUhMYUZMeTJlZzM4WVd1YUxxbkE9PSIsInZhbHVlIjoicnZ6N0VhY0tqN2ttbHdSa1dKNVdwdWwyZDVPWW4zdmhkb1ozT0NmMDQ1NDJsMXQweCtNQW8wOFREU2FBbmNWTDFPUmp1allZOTU0dllvWTQxTkRPWTg0REJMQWtsZ1RRUE1iRGVRQ1JpVnZBR3dMaTNnRFNuRE45MDlicmRKQkpZZEpWeXJORk1XZzdybGFlZE4rdTdtZ3lWNHNFQy9GQUpjcDRndmpmcEJlVGxYVk5VQ09CcHIxWXExS3NYREE0NXlSSXZHbiswY0QrMURudzNIL24wbk9HNnpBQmZ5eHdhaHZaMnBkNVZyMHFMbmxPZkpNR2pLNkt2Y3IvOXl3ZlFCSmZGUkpoY05WOVNpRFdnNUZVU0lMS1FWS0FjMEQ5Q240TEUwZFVoV1c2bFo5b200dUJKUEJQSXhueWpWTG43NExseUZWYmVYbmJSTFBnYW8zMU1FTkF6RjltTlFrZzNERFpxbFU1SDBnanNkWEdmT0c4MzdNMHBpK3FGZHYvTVhGSVByRzFDUVZPUjdCU2V1K0hacVNqUWc3dlpsRXd4WUFBdW0rQjVVeHZZVld6MzhraUZXZ2ZDcXlrMGsyMnI5OGhXZGNOZXd5UzJQZHZEeW0rUkJNVFJwcUh2Y3FGNmF0RExUTU9DNzIrTk4wMWNhMnAxaDV2NGorYVJXcFgiLCJtYWMiOiJiMDllZjU2ODcxMWU1ZDFjYmJiNTIxNGZlMjkzMWQ5ZTM3MjkwZGI0ZGUxNjlmNjJmNzViMTgzMTBlMTQ4ODk3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkQzUnE5OFZRZXhGb1Jla0RqamF6Y0E9PSIsInZhbHVlIjoiYU0zOEs4YWMzUGR3M2ZDTXJ0UzZEQnRpbmxtMFllcVhOYms5WkIxSHQ3Qm9SWWFsbHM4d2dUZHQvdU1RcjdMZzN2SU0yMjRRNkJXNko0RzRLOTJ3TlJhaWlyRExYY1V1cFZFR2JHUHBINzNtaDNOUGdpMFgybWFIYmh2V2tNaVc5UG9vdC9LZGpFUzU4RFN2Q05DVkZ5MHRoU3ZXOFJaWlZ1cGFGcUhvWW44OGxNaStJTkx4ZzJ1aUVTSEY2ek5nTVlNcTFYQUtNSGdOYThoMDFwQXpnQkZWR01TUE1LZXpFR253bjVGb2prbkl5SzhkcXNseG1xL3RZN2Z3cjg1MmMvdmJNVVd2SWdWcHZLeXhkb1ptUzROK21TQ3B4U0RaZDZFbTRxaWRyVzJPVmJSVHo3SXFMUlliaGRQWUZTSUhkTzNNeHEwK3N0VkM1blB0OHlsZmJjOEhDcGFIeENGMTFsVnZHR0EvaDZZTTE3UUptZXNyTEJlN01pMlBQY0E3UXIxWmFFVnhrTkFnZFc3cUNyOXQ5V09IOFg3UXJCNWVwbVBPSXVhWUlLN2xUU1JsckcrWWRqcHIvaG5hckErdFlkZ3RNYjNxNlZJY1YvTDZ5aUJRa0s0UHhaU3BpMWhVU1dRcVBHNFJlWW1wWFRpdGN0djVWNy9waW5FRHlwMDQiLCJtYWMiOiI1ZDdhZWE0ZjIwMmNmN2FiZmZmYmI1YTRjOTkxMDY2YTMyMDg5ZThkOTQ0YmNhYTZiMDExMWVlMmI2MGU0MWEzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpNNUhMYUZMeTJlZzM4WVd1YUxxbkE9PSIsInZhbHVlIjoicnZ6N0VhY0tqN2ttbHdSa1dKNVdwdWwyZDVPWW4zdmhkb1ozT0NmMDQ1NDJsMXQweCtNQW8wOFREU2FBbmNWTDFPUmp1allZOTU0dllvWTQxTkRPWTg0REJMQWtsZ1RRUE1iRGVRQ1JpVnZBR3dMaTNnRFNuRE45MDlicmRKQkpZZEpWeXJORk1XZzdybGFlZE4rdTdtZ3lWNHNFQy9GQUpjcDRndmpmcEJlVGxYVk5VQ09CcHIxWXExS3NYREE0NXlSSXZHbiswY0QrMURudzNIL24wbk9HNnpBQmZ5eHdhaHZaMnBkNVZyMHFMbmxPZkpNR2pLNkt2Y3IvOXl3ZlFCSmZGUkpoY05WOVNpRFdnNUZVU0lMS1FWS0FjMEQ5Q240TEUwZFVoV1c2bFo5b200dUJKUEJQSXhueWpWTG43NExseUZWYmVYbmJSTFBnYW8zMU1FTkF6RjltTlFrZzNERFpxbFU1SDBnanNkWEdmT0c4MzdNMHBpK3FGZHYvTVhGSVByRzFDUVZPUjdCU2V1K0hacVNqUWc3dlpsRXd4WUFBdW0rQjVVeHZZVld6MzhraUZXZ2ZDcXlrMGsyMnI5OGhXZGNOZXd5UzJQZHZEeW0rUkJNVFJwcUh2Y3FGNmF0RExUTU9DNzIrTk4wMWNhMnAxaDV2NGorYVJXcFgiLCJtYWMiOiJiMDllZjU2ODcxMWU1ZDFjYmJiNTIxNGZlMjkzMWQ5ZTM3MjkwZGI0ZGUxNjlmNjJmNzViMTgzMTBlMTQ4ODk3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkQzUnE5OFZRZXhGb1Jla0RqamF6Y0E9PSIsInZhbHVlIjoiYU0zOEs4YWMzUGR3M2ZDTXJ0UzZEQnRpbmxtMFllcVhOYms5WkIxSHQ3Qm9SWWFsbHM4d2dUZHQvdU1RcjdMZzN2SU0yMjRRNkJXNko0RzRLOTJ3TlJhaWlyRExYY1V1cFZFR2JHUHBINzNtaDNOUGdpMFgybWFIYmh2V2tNaVc5UG9vdC9LZGpFUzU4RFN2Q05DVkZ5MHRoU3ZXOFJaWlZ1cGFGcUhvWW44OGxNaStJTkx4ZzJ1aUVTSEY2ek5nTVlNcTFYQUtNSGdOYThoMDFwQXpnQkZWR01TUE1LZXpFR253bjVGb2prbkl5SzhkcXNseG1xL3RZN2Z3cjg1MmMvdmJNVVd2SWdWcHZLeXhkb1ptUzROK21TQ3B4U0RaZDZFbTRxaWRyVzJPVmJSVHo3SXFMUlliaGRQWUZTSUhkTzNNeHEwK3N0VkM1blB0OHlsZmJjOEhDcGFIeENGMTFsVnZHR0EvaDZZTTE3UUptZXNyTEJlN01pMlBQY0E3UXIxWmFFVnhrTkFnZFc3cUNyOXQ5V09IOFg3UXJCNWVwbVBPSXVhWUlLN2xUU1JsckcrWWRqcHIvaG5hckErdFlkZ3RNYjNxNlZJY1YvTDZ5aUJRa0s0UHhaU3BpMWhVU1dRcVBHNFJlWW1wWFRpdGN0djVWNy9waW5FRHlwMDQiLCJtYWMiOiI1ZDdhZWE0ZjIwMmNmN2FiZmZmYmI1YTRjOTkxMDY2YTMyMDg5ZThkOTQ0YmNhYTZiMDExMWVlMmI2MGU0MWEzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833028970\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1891090281 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891090281\", {\"maxDepth\":0})</script>\n"}}