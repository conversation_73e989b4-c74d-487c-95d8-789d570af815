{"__meta": {"id": "X0a4ab5114ab35e5421e2d10c8681bae6", "datetime": "2025-06-28 15:19:20", "utime": **********.386048, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123959.941801, "end": **********.386062, "duration": 0.444260835647583, "duration_str": "444ms", "measures": [{"label": "Booting", "start": 1751123959.941801, "relative_start": 0, "end": **********.318421, "relative_end": **********.318421, "duration": 0.376619815826416, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.318429, "relative_start": 0.37662792205810547, "end": **********.386063, "relative_end": 1.1920928955078125e-06, "duration": 0.06763410568237305, "duration_str": "67.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46286136, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2321\" onclick=\"\">app/Http/Controllers/PosController.php:2321-2355</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.014740000000000001, "accumulated_duration_str": "14.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.355771, "duration": 0.014240000000000001, "duration_str": "14.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.608}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.37858, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.608, "width_percent": 3.392}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-2106513067 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2106513067\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2100262754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2100262754\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1773911456 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1773911456\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-903759191 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123956711%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikt3VXpLVkZrRmQwR0lYNGwxZ2N3Znc9PSIsInZhbHVlIjoiODZRZERzUkRvU25zeDN1SWorbkhTQUI2dWl0Qm02MUJ6T0lTR0RnaFQzT0RDVzRSY3k0TWVxUnNmVmpSOEpSaWJPR29lRVMvMVpZR2dKUjNnTzduNEl2VEpxTUFvYlAvWEQ3QTdVMUFlZkRJV1BPMkpLbFhwUFozTStlbGN5TWIwdC9xOUZxRnFINGxjU2lMV1BmZExKZlpWV08rUktqdXQ3YXJKZ0VndmRXN05NdEdLejdXVEFPb2JXRGFFTDMwMnBDWmVPdW0yUVAwMFY5eDRmbHNLZTFEbHZrYzVUTHpPNnVRUXVWTmNUeEtHS2xScHFnMDQvVmhjblozVW5PSWhQMzdCa2oxS0kzNE1ZWEQyaE0xSVNJdVVycGZHV3EyMkpTcmhieXR0cWlxblF0Z2E5ekt4Rnk5Q08yclcvcUdaZ2F2UCt0TTNHQldwVnNkT095Q1pkbFhkdldNMkxMVjdTZHFtamlabmFwQUxGdFVpR2JXUTc4SFc3em0vVVV3NVZvWlpkZUtobGsrS0R2eXZzM2NLMUZYdDI0VXFXdzZ3RUJBTnIwUXBsWjJmUUcxVkN4MTI5UWhmdTlWN0NsYzd2SWdFMVdRZ0d6YVcxeFBIU2RmUW1MYkhhNnd4RTlNYWs0a1ZsMk5NdnphQkRQbGZOd01tbWF6ekV4dFZxR0EiLCJtYWMiOiJlNGQyM2QzNmRhODZkNDFlMzNjYTM2YWU5YWYyMGIzOTJmMTk5ZmUzOTdlNzA2MDEwOTljMDVkZDgzZjE2NGIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlPN0ZPMzd5RC8zQmpOaTVYSytoVEE9PSIsInZhbHVlIjoiWjgwa2JVOVp0Ky9qU1BIcU5PZGtzWUtjcityU3lqT3JDTlVjY0Q1dTROSkV1NWM0WTJGRFJRWHBndzFmM0RTdzRiNnFuSVBuWnFTK1I0MVBRNjJDZGpHemlkVzYrelNnd3RqWU0yZ3ZvWXh1dVdOVElFTzdQOEoxUm5tek9FeGpZeHlicFZ6N09JaFRrZzB2RHBIalBycHd3SzliaVdBeW5oVnhEUU1wcGFld3p5VHErVmM3azQ5OXNRRW5TZUJteWFJVzBQL0ozVjU2MFd5TW8yRXAraG5WTkdmUUVGYThTYlBQVTAzRWxLak1XSFl5b2djenhZbDQ3L3Bxdmx2TUM4L24wTjZUa3kvS1ZCeDFPV3lKZzhjeVh6VkE3U3BVM0FIVFZPS0ZMOUxmRXhGT2xaNGNCc05UWG1vanl3K09obFk0eUVsajdtdnlmYUlpMU9SYnJsOUp3UXh5ZlpPNGI2ZXBwc0JINVY5Z2cycWJDNWlLUGp1QXRZOEg2dXNlLzM5ekZtLzk2STViWWNCMlRLeG1nNy9oWTQ2c2ZjOWM4RUY2ZUZuNkxiTVVObFdoRFBmZzNqV0VITTRsd0NQd0F0d1dPcldQU2tYS01TTDJqYnZpd01pK1lFeDh5TXFaeXpON3VQZlJPbkRNbm5ONUpDQUlmRXFNUCsrM0gyUG8iLCJtYWMiOiJmMTgyM2U0ZjExYjRiMWIxZGU0NWFmMWJhNmJkMmFiZmUyOGRlZWNmMWYyN2QxY2Q5NzU1ZjM0OGE0M2YwMmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903759191\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1169808841 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169808841\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-10034434 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:19:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVVVVlBTWV4d0pXWU5MWnNUdlZiMmc9PSIsInZhbHVlIjoiQUxja3o3dy9KU29RMDVIZ1lPSXNTbkowZ0V2NGlnbkFVeWtFOHhnOU50U3VWdUZGMWwrK0FreFR0Vy9qemJlRzVwVGNIaTE1RVp2UW0yeWlYbjV3cXU2VlBKY3Ivc1JqMk5yZEdCUFBhVWpRRlU5d3RIVHVuMWR3bGt6Y09oVFRQTW5IK1JqaHo2NW1GOWxxaHU1Y3Z3a0RzUDM4U3pLRGxzOW1SSXVkRUVYZWk3aGkramliSWE4SmtLa1E5V2pDQzZ2VkduUXlDWnFHZXRwTXZyZUliVERUeThTaXBlNFkzMnFkSXdDcExreW5LS0FjRzNuR2t1ZTFKcDZ0V25NOFNaSnFjZVpjdVVOQzNzTnJ4Q0ViTFFTQkY1dFhaV0txc3R1eERSWUd3QlQ1Y0t1cDBWa3JmVG5lYXdweWtNMSsvcXhLRXovYUVLU0RRczF1dDlnYjJFeTYzd3FmekFHbFVUT3FJV0VwZFplbzArd0N4TXNKc1pMeFFDcFozc3FYNWJGS2JWNForWDhzR3ZQUm1FY1VpNFV0YW5XY1FzV0x1Q0pLREdaeHdZNUt1VkRiSm5RTWFmMUh5RWIrendRd25ZbkhtVDgxVWFuUGpGNXZXRGsrN3lncFpCajFYMUs3M0sxRzZsczUvQ2pjNUVFVzBLbTg1MHdyV3dlRFVBaTEiLCJtYWMiOiJkZTI1ZGU4ZTkwZTJjMTNmY2UwNmM5ZWNjMThjMTJhMjcwZDZmYTk1MWEwY2ZiOTRhYzkxMjE1MmIwN2QyY2YxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNnMTJDY3ladzZuTDd6aUZabVVxTGc9PSIsInZhbHVlIjoiaUtCT1lzWGhlVHIrQnZseDRPSlVrK2U5MGk4YkVwV2xnUWVtZHpLWlY4c21hUGtvcjVvNnVyZFJuNlZrOGl2VEhObU9WOHRvSjh6SHZUc1lDSW5zY0FoZERGRnNENEE3cnZXblZVTHpielMrVHR3eFE4cWU0UFY4bDBXTDV6elZmQWM0Qm5jZkFqa2YvTk5OQllwTncyRCtlQ0xLVXpJMHM0ZTMwTTlQM3JIT2Nic0tGdUI1ckZaYjg5dmNOb3BnOTFSRHJhTmNaeWwyQzJoelFIamRUV3Q4S09SME5DcnQvTTRSZUQ5dC84TFNEWWlqb2loNjhvRFdndzRkOFdxaGxSamFDcjJEOTVUVFQ4WDZ5bTNMenVsdW9tNGxPMVR6SlZ0MjU5dmJoK2U0SWJLTnh1MGZGdGoxQ3FPek8zL09GNEdlTlFPOTZUbTNiZGtHbDAzdWIvdXFSZ1NBdGM0S0IzZ1dQVkw5MHg1U2NTQTA3QTZVVlF5ZWNubi9VNGJhZUdxRURxRWtCb3N3d3NZQXI0Z0VVUVpiSzBpMmVKVXc5YmdCZkxUUHJlcHl2elNOTzJyREw3WlhPcDVWRSszcFhWdnpQWTkweVQ4cHJtZXpMK2xqVCs3Z3plclA5ZXNUTzhQVDdXNlYzbC9QNUhqOU4yNzRqS3hmV1NrZE41RFQiLCJtYWMiOiI1NjU3MGFlYzczZTBhYjY0NzMzNGRkMjIyODFlYTJlZDQ4Mzk1YjJkY2QxNTU2MDkzODIyOGVlOTFiMzk4NjRiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:19:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVVVVlBTWV4d0pXWU5MWnNUdlZiMmc9PSIsInZhbHVlIjoiQUxja3o3dy9KU29RMDVIZ1lPSXNTbkowZ0V2NGlnbkFVeWtFOHhnOU50U3VWdUZGMWwrK0FreFR0Vy9qemJlRzVwVGNIaTE1RVp2UW0yeWlYbjV3cXU2VlBKY3Ivc1JqMk5yZEdCUFBhVWpRRlU5d3RIVHVuMWR3bGt6Y09oVFRQTW5IK1JqaHo2NW1GOWxxaHU1Y3Z3a0RzUDM4U3pLRGxzOW1SSXVkRUVYZWk3aGkramliSWE4SmtLa1E5V2pDQzZ2VkduUXlDWnFHZXRwTXZyZUliVERUeThTaXBlNFkzMnFkSXdDcExreW5LS0FjRzNuR2t1ZTFKcDZ0V25NOFNaSnFjZVpjdVVOQzNzTnJ4Q0ViTFFTQkY1dFhaV0txc3R1eERSWUd3QlQ1Y0t1cDBWa3JmVG5lYXdweWtNMSsvcXhLRXovYUVLU0RRczF1dDlnYjJFeTYzd3FmekFHbFVUT3FJV0VwZFplbzArd0N4TXNKc1pMeFFDcFozc3FYNWJGS2JWNForWDhzR3ZQUm1FY1VpNFV0YW5XY1FzV0x1Q0pLREdaeHdZNUt1VkRiSm5RTWFmMUh5RWIrendRd25ZbkhtVDgxVWFuUGpGNXZXRGsrN3lncFpCajFYMUs3M0sxRzZsczUvQ2pjNUVFVzBLbTg1MHdyV3dlRFVBaTEiLCJtYWMiOiJkZTI1ZGU4ZTkwZTJjMTNmY2UwNmM5ZWNjMThjMTJhMjcwZDZmYTk1MWEwY2ZiOTRhYzkxMjE1MmIwN2QyY2YxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNnMTJDY3ladzZuTDd6aUZabVVxTGc9PSIsInZhbHVlIjoiaUtCT1lzWGhlVHIrQnZseDRPSlVrK2U5MGk4YkVwV2xnUWVtZHpLWlY4c21hUGtvcjVvNnVyZFJuNlZrOGl2VEhObU9WOHRvSjh6SHZUc1lDSW5zY0FoZERGRnNENEE3cnZXblZVTHpielMrVHR3eFE4cWU0UFY4bDBXTDV6elZmQWM0Qm5jZkFqa2YvTk5OQllwTncyRCtlQ0xLVXpJMHM0ZTMwTTlQM3JIT2Nic0tGdUI1ckZaYjg5dmNOb3BnOTFSRHJhTmNaeWwyQzJoelFIamRUV3Q4S09SME5DcnQvTTRSZUQ5dC84TFNEWWlqb2loNjhvRFdndzRkOFdxaGxSamFDcjJEOTVUVFQ4WDZ5bTNMenVsdW9tNGxPMVR6SlZ0MjU5dmJoK2U0SWJLTnh1MGZGdGoxQ3FPek8zL09GNEdlTlFPOTZUbTNiZGtHbDAzdWIvdXFSZ1NBdGM0S0IzZ1dQVkw5MHg1U2NTQTA3QTZVVlF5ZWNubi9VNGJhZUdxRURxRWtCb3N3d3NZQXI0Z0VVUVpiSzBpMmVKVXc5YmdCZkxUUHJlcHl2elNOTzJyREw3WlhPcDVWRSszcFhWdnpQWTkweVQ4cHJtZXpMK2xqVCs3Z3plclA5ZXNUTzhQVDdXNlYzbC9QNUhqOU4yNzRqS3hmV1NrZE41RFQiLCJtYWMiOiI1NjU3MGFlYzczZTBhYjY0NzMzNGRkMjIyODFlYTJlZDQ4Mzk1YjJkY2QxNTU2MDkzODIyOGVlOTFiMzk4NjRiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:19:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10034434\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1841290782 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841290782\", {\"maxDepth\":0})</script>\n"}}