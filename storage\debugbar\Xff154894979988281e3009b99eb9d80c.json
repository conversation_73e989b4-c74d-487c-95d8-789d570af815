{"__meta": {"id": "Xff154894979988281e3009b99eb9d80c", "datetime": "2025-06-28 15:43:17", "utime": **********.052614, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125396.538664, "end": **********.05263, "duration": 0.5139658451080322, "duration_str": "514ms", "measures": [{"label": "Booting", "start": 1751125396.538664, "relative_start": 0, "end": 1751125396.983331, "relative_end": 1751125396.983331, "duration": 0.44466686248779297, "duration_str": "445ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751125396.983343, "relative_start": 0.44467878341674805, "end": **********.052632, "relative_end": 2.1457672119140625e-06, "duration": 0.0692892074584961, "duration_str": "69.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45163768, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00356, "accumulated_duration_str": "3.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.022711, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.146}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.036005, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.146, "width_percent": 18.258}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0442631, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.404, "width_percent": 23.596}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-431251393 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-431251393\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-681807221 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-681807221\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1603650291 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603650291\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1918152989 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125383583%7C16%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtwWm1oeG9pbjFFZnJ3ZDgwYm9VMnc9PSIsInZhbHVlIjoibVBBQ2tFcmNwU0VsSG0vVE5tSWpxOWIweUpVRnUrNWZLb1QyR2x5bTRHdUZUSGZBUmRtdjI3K3lhQ3E2clRRSEFPVmsxTStSRDJibjBiWExrdHU1dGlUcThnSi9LNktrR2xqUDVvM2tEK0VRdGhZbm1KeU5pSGN5T2VTMldhdm9YOXdjZjQ0VUN5WEFITEhoaDRCMnJtSmFaR2l2Q0VlbzNuU1NQN0hpYk1KQ1F0SHk3TnU5Y1lnVG41NFhVWUNONUFzNlF2V3ovdzQwbzBWTmJqbllaVUdET1YzbURpVzRsdHlyY2V0ekNSbjhVZ2tGTUhiZmRmNUJwMFRPUEhEYjA1TkczUUZpc1czZEtQWllkZGlabU0xRW9Id252L2dDNjQ2MDNzWnJDQk94Q3g3TFVIT0xEYkNiRTZiTTU1L3R3aWpHZlE4dnppNUNWTkdFaDNDZkplRzBqVTFCU29xdHRwNGFMOXJEM29ZSWxjZlVnZ3U4OW1wN0FIZ3EvbWpEUy9Xb3pleU5tVTkwbm9MRkp3amVvK054eWdqNTluUzNEWUhtcjAvcmtQdC9oMENjTVhSaHVXd3lQOU9BYWNJQ21PRmxrU3dKUjRqcSs1OTBrb0VGK2gwRUlzMFQzbDdyRFZ4Wk9SY3lzblMvQUZaYXBoMDA5WXhsb01IQjNLTDIiLCJtYWMiOiIwOGM0YTI1ZjNlMDg3MGYxYjMyNjM2YmU0YTM2NjM5MDc1YzBiYWRiOWE1YmJlM2JlYTZhNWRjMjM4NzI0MjE4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im81Rk9Td0VrK2NETjNqNEM2RTZZWnc9PSIsInZhbHVlIjoiTTJQRmNXV2xCNG9mc01zZUJoM1NSakxNaE83T0xqQm9tVVhyZUhOcWlBOUFqMmZaVCtsbkpZMXVTNjU1TmFmTDk5UlBuRzY1MnZmUVJObU5DWGY4UUNLczJtZjB2SHVkOVdGaFdJeDQwaEpNaGxCQWxMMjFERVM2VEdZM2lGSVMvVk8xVlJmSHI1OUd1alJpNDdaME91ODFkMGdhQ3hrWVpMeTY4eDVWalYvdDl4RXF4S2xJYWlOVEdQODVXdkFHUkdUVkt1UDg0bWtKc1Uxd1FoajEyVzBCR3hGS3VEOVBKZ3VhUHZQb2xhMEppV01BRUV4RGwzR3JYRURwVE1DTkxLRzFvNG92TVFnejJRSFR3akRPanZFQW1QbERWNFlFOGE1OGdOZWlzaWx6R1g2eUdZcUhMQ0VtQURSazhGbzVFdkRxV3lQUSt5bVJDN0Q4Mk5KZ3daRStTUmVtZ2hUUU9wSWttVGNrQTVYUmN6MVdlSThjV2xHY1hTRytGckdPZVlCV3hscWJnRnhUdmJVNERybkNSM1Fqd0xlZHk3RFhVbllOMlVmekFMckJWVHlDUFFHWFhlbk5RVEp0MWhFSndkYUM0S3p4K2xLSnF2QlhUQjlPQXBrYmdrVVBTTm5MWGdQcTlQMzRHUmFlTkVpN3h0b2wyNy90S0ZCVlhaTS8iLCJtYWMiOiIzZGJjZDgyYTQxYzFjY2Q3ZDBjOGVlN2RkNWY4NGNmZmIzMzRkZTNjODY5Y2RiMzJiMDQ4NWZmODZkYTZlODA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918152989\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-437353383 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437353383\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1295079386 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:43:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlMvQ1JZaVd5SUw5aWVVN2dIVk5MSUE9PSIsInZhbHVlIjoibTRhZTFMS2ZQT2ZhNitYdUQyRUJxeGFtQ1F4ckJOMDE4UlJsVmdieUhwT1dZaUhMcFJ5ZjZnR3pTdmZPa0t3VVg4Y0ptRjZOTVMyT1p6ci82SlgyMVFCTlg2Qy9mYmhlUk9nOHpaRVVrUHViU1YvSkp5RkllS3lkMU9lS2VpemtaMG5HK2hDek13VWJCb0U5b3J6RGx6c09QY0hGQXJ3cDMvTnFTaU5tTEhuTkNvVi9xRmZhVWw1eS9OL0l2SWdrWU9ocGpUSzBCQzI1blE5dUFnVHlHd3YzSlR1blNqZDh0S2tSSURDS2NCVDdIVzZydnJkdEZOOHF0TGdQYlh5dExEcmpIclRWWGZVTkRaSk9DUVRGS0xPTFg3aFVTT2svejVPZlJHQ3FxNXI0VTUrejRRODlRWU9sdG5DNHNQUkhlN1dYWVZ4b3dOM1BUUFhLOXlwYllTUTZqcTFvN0RXNUZWMDBhTXVZSFZCYTFIczdiRHJlc1l4RjJtc0N5djVpRG5FNjBYOU1aTFVSZWo2ZzBxa3F3dkt5dldwbFNCMXg1NXhidHhEdVEwTHZ4UldZRkNqbDBmQXpEQzNnK1NlQ0ltY0VEZVdaVUF3SEJRM1BLUlhqN1RRSnNaeHJGNjZybERBd29kdklVQjBsWnpFaTM3NFR6L2UwODJPNTBsY1MiLCJtYWMiOiI1MjA2ZGQ5NDRmMmY5NzFiNmQ4OWUyYzVkYzlkZTA2ODM1ZjA0MjFkMDMxMjU2ZmIxYzYzNWY3ODFhODBlM2Q2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9ZRVQ4dEp6MjdvYTFaWUpNZEtjbnc9PSIsInZhbHVlIjoiWjFsMDJlVVNCb2dVY3VsR1owZ0k5WVdTQi9nTUcrSloxZnplckliUVVDR0dNSkdNcVZHZkRLQnhzSTFjcWFINXA2N0FPeEFleWlZd0FhYXdnSTY4V0pUQ3NuK1lTYXFTeTI4Wk84VXROTWJ5V1BTVnJwb0VQRE95ZUY5WHhPWFUyMjh2Q0Q4NDRnYlpzVEE2cEhCSDc3ekxBZUpYOEkvL3NuN0xkcUcrbVRONXhuVEpmdk0rQjNSa3FWYmdMaG9vdVIzeXd1RkJvbEphV1dWTDZsVVZiNnh1WHpSQlZrMnpNZHhTL0xla09tNGE4T21qRmtFYnlNR2RxN1hXTkVjRndXU1BsckIyaERaNFRzU3dCdVlmd1M3TDF0aUpGamdNUjlBQVFuTnhzS1BVMWNxWFdCbG8wSU5LVWV0bFdEelZLckdubkpxMkRWVFNSd0pUc2NPQllNYUhjRE9BUG5ZY1FTR0hwMnBEKzJ0RFJLTENNaTA0OFVweUZrbmdVNnhtdXhkYTJJK2hmUUFOa3M5QnRhRTd0K3BMU25acXhtS1JsdjJTcFZIWXBDb0pJZDVFTDdxUEZtQm5KQjhPbTM3RU43QVpvbHpFN3ZESDJMQmFxZDVhZXdMM05RL0JtK2VoVFR5WTFoYkZnV0owVGtXeFQxZTMyeFdBWVJsNjByMUEiLCJtYWMiOiIxMTUwMWQwMWJkMDM3NGM2MmU5YzNiOTMyODdkZjRlZjk1ODU4ZTYzYjMxNDdmNTFlMGFhZDhkZTYwMDhkMGEwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlMvQ1JZaVd5SUw5aWVVN2dIVk5MSUE9PSIsInZhbHVlIjoibTRhZTFMS2ZQT2ZhNitYdUQyRUJxeGFtQ1F4ckJOMDE4UlJsVmdieUhwT1dZaUhMcFJ5ZjZnR3pTdmZPa0t3VVg4Y0ptRjZOTVMyT1p6ci82SlgyMVFCTlg2Qy9mYmhlUk9nOHpaRVVrUHViU1YvSkp5RkllS3lkMU9lS2VpemtaMG5HK2hDek13VWJCb0U5b3J6RGx6c09QY0hGQXJ3cDMvTnFTaU5tTEhuTkNvVi9xRmZhVWw1eS9OL0l2SWdrWU9ocGpUSzBCQzI1blE5dUFnVHlHd3YzSlR1blNqZDh0S2tSSURDS2NCVDdIVzZydnJkdEZOOHF0TGdQYlh5dExEcmpIclRWWGZVTkRaSk9DUVRGS0xPTFg3aFVTT2svejVPZlJHQ3FxNXI0VTUrejRRODlRWU9sdG5DNHNQUkhlN1dYWVZ4b3dOM1BUUFhLOXlwYllTUTZqcTFvN0RXNUZWMDBhTXVZSFZCYTFIczdiRHJlc1l4RjJtc0N5djVpRG5FNjBYOU1aTFVSZWo2ZzBxa3F3dkt5dldwbFNCMXg1NXhidHhEdVEwTHZ4UldZRkNqbDBmQXpEQzNnK1NlQ0ltY0VEZVdaVUF3SEJRM1BLUlhqN1RRSnNaeHJGNjZybERBd29kdklVQjBsWnpFaTM3NFR6L2UwODJPNTBsY1MiLCJtYWMiOiI1MjA2ZGQ5NDRmMmY5NzFiNmQ4OWUyYzVkYzlkZTA2ODM1ZjA0MjFkMDMxMjU2ZmIxYzYzNWY3ODFhODBlM2Q2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9ZRVQ4dEp6MjdvYTFaWUpNZEtjbnc9PSIsInZhbHVlIjoiWjFsMDJlVVNCb2dVY3VsR1owZ0k5WVdTQi9nTUcrSloxZnplckliUVVDR0dNSkdNcVZHZkRLQnhzSTFjcWFINXA2N0FPeEFleWlZd0FhYXdnSTY4V0pUQ3NuK1lTYXFTeTI4Wk84VXROTWJ5V1BTVnJwb0VQRE95ZUY5WHhPWFUyMjh2Q0Q4NDRnYlpzVEE2cEhCSDc3ekxBZUpYOEkvL3NuN0xkcUcrbVRONXhuVEpmdk0rQjNSa3FWYmdMaG9vdVIzeXd1RkJvbEphV1dWTDZsVVZiNnh1WHpSQlZrMnpNZHhTL0xla09tNGE4T21qRmtFYnlNR2RxN1hXTkVjRndXU1BsckIyaERaNFRzU3dCdVlmd1M3TDF0aUpGamdNUjlBQVFuTnhzS1BVMWNxWFdCbG8wSU5LVWV0bFdEelZLckdubkpxMkRWVFNSd0pUc2NPQllNYUhjRE9BUG5ZY1FTR0hwMnBEKzJ0RFJLTENNaTA0OFVweUZrbmdVNnhtdXhkYTJJK2hmUUFOa3M5QnRhRTd0K3BMU25acXhtS1JsdjJTcFZIWXBDb0pJZDVFTDdxUEZtQm5KQjhPbTM3RU43QVpvbHpFN3ZESDJMQmFxZDVhZXdMM05RL0JtK2VoVFR5WTFoYkZnV0owVGtXeFQxZTMyeFdBWVJsNjByMUEiLCJtYWMiOiIxMTUwMWQwMWJkMDM3NGM2MmU5YzNiOTMyODdkZjRlZjk1ODU4ZTYzYjMxNDdmNTFlMGFhZDhkZTYwMDhkMGEwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1295079386\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-511099518 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-511099518\", {\"maxDepth\":0})</script>\n"}}