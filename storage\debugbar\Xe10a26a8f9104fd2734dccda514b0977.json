{"__meta": {"id": "Xe10a26a8f9104fd2734dccda514b0977", "datetime": "2025-06-28 11:24:00", "utime": **********.912718, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.553794, "end": **********.91273, "duration": 0.358936071395874, "duration_str": "359ms", "measures": [{"label": "Booting", "start": **********.553794, "relative_start": 0, "end": **********.862948, "relative_end": **********.862948, "duration": 0.30915403366088867, "duration_str": "309ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.862956, "relative_start": 0.3091621398925781, "end": **********.912732, "relative_end": 1.9073486328125e-06, "duration": 0.04977583885192871, "duration_str": "49.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45704944, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1910\" onclick=\"\">app/Http/Controllers/PosController.php:1910-1968</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00423, "accumulated_duration_str": "4.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8926961, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 36.17}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.902123, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 36.17, "width_percent": 8.511}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%********%' or `sku` LIKE '%********%') limit 10", "type": "query", "params": [], "bindings": ["15", "%********%", "%********%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.904465, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 44.681, "width_percent": 55.319}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-313331179 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-313331179\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-328713958 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-328713958\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1924405870 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"8 characters\">********</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1924405870\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-919468106 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">29</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IjJMd0JXRDRHRkpvMzNMTlNzdFFXRmc9PSIsInZhbHVlIjoiTnE1OEVHMVRhcCtCZUxmbHRRZndMQ0h5UWJTUndpSjVuTlFobnhUSm1yNys3OHRQV2Y2Rkd1NVM0RG1TSWhCWlpnRXBoT0pXTU1OU1N4RytuSEJyRHFUejVCZXNYT2tJeDBpTi9sV3ZjaXpkR1kzL2pqV3BBb2t3a2JPdWhvcGJPOEhsYkx3RlhtZDFTakZ3MExVYnVzRlljUU1yaFNZS3N2WjNkOTRkbFRuc2RSVElScjRRaWR2SnBaR2pWbUQxbXhJWDhLZ0c0RUdvcFRwUVhxZzVDN2tDZTdydzc4RS83WUsxVmFUQUpNNHNONERYcmFxTHA0R0hwNTFDQVlOWmxkRmNrMGN6RDMxMkVSeU5aQTdrdmVXZTBxcFhEQTRNRmNBdCtSMVlYaVRhbXM5bmhiVXhJeFlDdXBQUnB4cjkvWlFjdWthQ085MTNlTWtPd096R0htb1RWZFRyb1JzbDU4MDEzaTRiNzNHN29uU0dLOUFwZ29zMGxyRFY2V0xZeUQ3SnF6STR0REc0QVFYTGl0Z290RmtnZncrZEV1OXJkakJnbFRpaGFXNEZWNnVZa3RtTGNBV2VNK29pd05BZDBUMnJQbVhmNXYwUU53M2NoRExjRE5ZNWMwcmcrbGFTM1NQRytlclhuM3E3MHFlK3BVcmdlNUVRa1VxQlRwNW4iLCJtYWMiOiIxZmZhY2Q0YzYyYzE1MzMwODlmYWEyMjFkMzQyOTY4YTVlNWQ2ZDE0ZGI5ZDI4MGM4MDBkNTc0YjcyYTAyNDAwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlNjU2p3b29jczk0Q2tsRjNtSGVUY0E9PSIsInZhbHVlIjoiMUJvRnRzMEZNNkxWK3BSZ1d0d2JRRnpQWHhZd2o2WlF4YndKT2xrYXZCd05nT2hmeVcwQkp2aXRROGJQVDRmdFkwRmFVTzVyVDVuOWwwQktYelhOdE1BUEFuUTRZSVNYY3hvMWFJSFNqZU04dVRjSWw2UlFWZ2tFVUNvNE9zTlQwWDhOTHkxWHlHR3pkYmtYeDBNMGhHSnAzVjJER1h1cGRzZnVuY1NhM1hwVTdIendBWGlSM0luTmtHSkRLNVhWajY4VUhRNW5UNlBYYkZ2V1BMYzBWL1Q1TURBMHJ3U2QzWTNlRFovMzROaUY5aUExa3NiOWYzS1ZWamxtSFZrc0c4c2FSSVgyTm5WOFRJVzJ1NXB3OXVvUHF4aW5KSUZzZ0lqWVBLSkIweFhZT2tsRFRha3pVR0hjb1o1Uk1vTGpFRHAzSll5SHpURGJ4L0ZEa0l4VDhnZkJZT3M1dlhPSHBTZlppL3lBUXBhblh3MWh1YnE4MFV5d3N1NVlHZmZ3MzN0aFJKTm9ZRHpSbE1ERE05bHZZVUMrOW1WcmU1dUp3cFdtUGwxVFhUUCsraU1ETVdKWXFpRVBqMS9OOHYxYXRzMXZ2cnNLTVNKUXRlYXdLWSs3R29ZVkE5Qk1ZREtQNVRtcjRjbktHWmx6OUE0Y0pnSC9xMUNZK1o5N1VhNG8iLCJtYWMiOiJmODFjZTliNjhhNzZkYzQ3Yzg4ODdlM2M5ZDZmYTk3MWIwODAzZWQ1NWY4OWNmN2IwMWE4NjIzOGQ3ODQ0MGNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919468106\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1633396959 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1633396959\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1071579979 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:24:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhzRkRqb0JQSlZDUjhhd21NRGdSVWc9PSIsInZhbHVlIjoiM2M5eXVZV0dZMkpMdmpNYWJNODN0M0VGTUpPRDViVGhwandYZytGNGtyRlRwQXErdGtWNXp0bytZaWxkSnJPbGd6OXVxeVRsYmhDbkQ0UkxXdm1JMWJzTlRJM014TnE0Mkd6Y0E4VG45UW1ZRndFOWRPUWFvaGdQV1Q5L2JrZDYwSXJTdjRSd2plUFo0cy9GelNrUHhNczJxeTFDVGNwRzEydlo0YW1CNlR6MFdWY3RjUHdBcGZrVm16NFR5RnVaRFZSMkRnSWxzTzR2ZGVNcFE2RTVLQ0E1SVhZdjlRWWszMDFzVTRPeEM0bHBZMEIrdDF6c0lkenNVVlF0dnBaeWtCSW9OR2s4N2wzcWVOdnhWZGlVbWFBNENXbFZuYmZ6ZjhuaWE3QlpDWWhpbDlnRXRNbm1LSjFUL085RkkzQ3hURzVtT0F5bEZVRXhGNnlPYm1DRXg1MmYrdGJPZkVTMUh2MFNRdzNpNEMwdUlsVjBMR1pCS3RMdmY0Z1dTaXZjaGpPaUlNRjUwZUFXekpZeDh5Ujg1VGd2ZlRINURHeXduaUltNnFHdUh2dGtKeGZlb011dHJvM0o4T2pBeE83UEhlYVZMcWVGTGgxYk5jR3V0b0haRWxmbTk2M2Z4OHJ3eVdpQzRXc3VaVG9mT0tDaVE2OVlpeGlKcGZUU2Z1TkMiLCJtYWMiOiIyYjQ4ODA5NTAyNDFiMzMxMWMzMjRjNmIwNjA5NDdlZWVmNWE1YjlhNjk4Y2EyYWU4NjU5MzcwNDk2NWU0ZDhjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZEM0YwanozcUpUd29pWHh0SzZOaHc9PSIsInZhbHVlIjoiaDZaWTZJTm9vKzBudlFJWThPcUh5dE1rVnZ1b05SY2h5Vk1DdlNtS0pCMXE1Z1RnWlpMUFh0aUlTZ3lWM0xVRWMvZTNkR2EwVVF3U2VJMUwydGM2R2o2NjMwcUMvSXBSZk9rMnFnU21KZnRtbVJMRHRDZDdLTmRRVlYxUWx3dGpzNXE3a3I2L081Z28vcS9LSU04UndEaDcxUGpaelVPZEFNYndQRTZJaUk1NE9JbXEzanFQamhPSVpWVzU3eDYrSVVIeDhmSGhxdEs5UGJWVG1ubS9ScWxjUkFYV1JhUi9BbEQ1am5VVUtHL2xDQUo5ZlcxMW9DV0hMdDQ3OXQ3MTZ1ZklEcGJmWUlzVWllSDdVZGFIUithaFExaDRkVEJqazROcDFkSDVPZjRmdU5QZW1uV0hGMHZJTHlTVk5KelFtbitBTURjUU9aaWRkTDVtUVNPeG5ndnFJTHkwL3lMVVVWcklidk9rYnRUK0Zwa09xNDV3U3pkdEcrTG96MDBNcE1JdjNTL3ozTXFmUHdXNVFxSUVpWXN4OGo1bVFWS0pBR01EM3M0SWFwbXpCaFk5RkdxVDdweDhQOTZnMnVQSjhEaFR0dmtGd0JVVjM1OGJlOXdJNkdGNUgrZ2pxK2VyWjcrZzFhckhEQkJWVEY3dXAyMktLVDZESWU4OXlWOFYiLCJtYWMiOiJiMzRiMThkM2UxODY2MGRiOWM2NWI5NTI3NjA2YTRmZjk2ODMyNjhlMmI3Njc0ODlmZTAyZTYyNTJjNmRhYWExIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:24:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhzRkRqb0JQSlZDUjhhd21NRGdSVWc9PSIsInZhbHVlIjoiM2M5eXVZV0dZMkpMdmpNYWJNODN0M0VGTUpPRDViVGhwandYZytGNGtyRlRwQXErdGtWNXp0bytZaWxkSnJPbGd6OXVxeVRsYmhDbkQ0UkxXdm1JMWJzTlRJM014TnE0Mkd6Y0E4VG45UW1ZRndFOWRPUWFvaGdQV1Q5L2JrZDYwSXJTdjRSd2plUFo0cy9GelNrUHhNczJxeTFDVGNwRzEydlo0YW1CNlR6MFdWY3RjUHdBcGZrVm16NFR5RnVaRFZSMkRnSWxzTzR2ZGVNcFE2RTVLQ0E1SVhZdjlRWWszMDFzVTRPeEM0bHBZMEIrdDF6c0lkenNVVlF0dnBaeWtCSW9OR2s4N2wzcWVOdnhWZGlVbWFBNENXbFZuYmZ6ZjhuaWE3QlpDWWhpbDlnRXRNbm1LSjFUL085RkkzQ3hURzVtT0F5bEZVRXhGNnlPYm1DRXg1MmYrdGJPZkVTMUh2MFNRdzNpNEMwdUlsVjBMR1pCS3RMdmY0Z1dTaXZjaGpPaUlNRjUwZUFXekpZeDh5Ujg1VGd2ZlRINURHeXduaUltNnFHdUh2dGtKeGZlb011dHJvM0o4T2pBeE83UEhlYVZMcWVGTGgxYk5jR3V0b0haRWxmbTk2M2Z4OHJ3eVdpQzRXc3VaVG9mT0tDaVE2OVlpeGlKcGZUU2Z1TkMiLCJtYWMiOiIyYjQ4ODA5NTAyNDFiMzMxMWMzMjRjNmIwNjA5NDdlZWVmNWE1YjlhNjk4Y2EyYWU4NjU5MzcwNDk2NWU0ZDhjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZEM0YwanozcUpUd29pWHh0SzZOaHc9PSIsInZhbHVlIjoiaDZaWTZJTm9vKzBudlFJWThPcUh5dE1rVnZ1b05SY2h5Vk1DdlNtS0pCMXE1Z1RnWlpMUFh0aUlTZ3lWM0xVRWMvZTNkR2EwVVF3U2VJMUwydGM2R2o2NjMwcUMvSXBSZk9rMnFnU21KZnRtbVJMRHRDZDdLTmRRVlYxUWx3dGpzNXE3a3I2L081Z28vcS9LSU04UndEaDcxUGpaelVPZEFNYndQRTZJaUk1NE9JbXEzanFQamhPSVpWVzU3eDYrSVVIeDhmSGhxdEs5UGJWVG1ubS9ScWxjUkFYV1JhUi9BbEQ1am5VVUtHL2xDQUo5ZlcxMW9DV0hMdDQ3OXQ3MTZ1ZklEcGJmWUlzVWllSDdVZGFIUithaFExaDRkVEJqazROcDFkSDVPZjRmdU5QZW1uV0hGMHZJTHlTVk5KelFtbitBTURjUU9aaWRkTDVtUVNPeG5ndnFJTHkwL3lMVVVWcklidk9rYnRUK0Zwa09xNDV3U3pkdEcrTG96MDBNcE1JdjNTL3ozTXFmUHdXNVFxSUVpWXN4OGo1bVFWS0pBR01EM3M0SWFwbXpCaFk5RkdxVDdweDhQOTZnMnVQSjhEaFR0dmtGd0JVVjM1OGJlOXdJNkdGNUgrZ2pxK2VyWjcrZzFhckhEQkJWVEY3dXAyMktLVDZESWU4OXlWOFYiLCJtYWMiOiJiMzRiMThkM2UxODY2MGRiOWM2NWI5NTI3NjA2YTRmZjk2ODMyNjhlMmI3Njc0ODlmZTAyZTYyNTJjNmRhYWExIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:24:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1071579979\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1477380473 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477380473\", {\"maxDepth\":0})</script>\n"}}