{"__meta": {"id": "X9a39770b7e8023b840bcadac7904c2be", "datetime": "2025-06-28 16:30:38", "utime": **********.106453, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128237.64623, "end": **********.106468, "duration": 0.460237979888916, "duration_str": "460ms", "measures": [{"label": "Booting", "start": 1751128237.64623, "relative_start": 0, "end": **********.035669, "relative_end": **********.035669, "duration": 0.38943910598754883, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.035678, "relative_start": 0.3894479274749756, "end": **********.10647, "relative_end": 2.1457672119140625e-06, "duration": 0.07079219818115234, "duration_str": "70.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46437704, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2513\" onclick=\"\">app/Http/Controllers/PosController.php:2513-2547</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.015330000000000002, "accumulated_duration_str": "15.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0753338, "duration": 0.014960000000000001, "duration_str": "14.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.586}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.099381, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.586, "width_percent": 2.414}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2112117854 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128229272%7C49%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imd0dy90UEZ1TnhQbi9zUHBVd1o1aWc9PSIsInZhbHVlIjoicDNvamFlWld0RmFVUDFKWjlwTThXVmRBak0xQXpBVE53eTJQYno2a0pMYmFjQ0ZjbUF4NE1rb1JaMjh6aEdEbmJwQlpHRDI0OTBRTVlhRkVhWVdVRmVQTmpKcVZyQnpFZ0laTm1sSmF0MUNnL0JYUmNBdWN3bXNadE9Ec3Q4RE9RcHFObmhQd0dLL1ZiV0hTeXVHYTA1bmlGZlFVS3QyRXZoTzhnTWRSK1lQOWFnZHBrY3Z6USsxWXhxZjYxK01EOVFnaGlqS1NwNFVYbnd0WFZKUzB1RytaRmg1RnhCVk00QnAzNVRjRml1MUlrWjlhY08yTVcxSXM2RHRMeG9hbzNFK3psYmpSbFlQcHovYW1wemh1VjJEcXk0d2JYdDhWWVVZTWlDTVE3cnJxcEdWZCt6cHBlNnp0QUhLRnZpenBlOXNZT2hoUmI3NXFoUWVxU0ppaTFKbUczaHJhdnM0SDllNXI2TnhNVzBqZHorREwrV2U4QTlsT2JLd1RXL0J2MDBNRWRqWGwzNGE2ODhQZzNiQTBkd3NZOTlJYVlPN05Sa3p3QlNnall3ZjVHSmo5RXB6WHFBSGE5c2lTOWgwcWVaS3hOTHk0dzhoUStaNzMvVjJDSUJWQ2E5WHloK01kUTZUa3d0eDhkT3ZaSGFrV1IvWnYwU0Y4cUpsdFo0NmciLCJtYWMiOiIxMGRiNTdlNWM1OTdlY2U0MWJjY2ZmMjBkNmY1ZjgyMGE2ZTEzMDg4NDkxMmVhZDc3OTRkOGIzZmI1NThjNjg3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjYwcWVYNE02eG5VYWVST0NITTdhNkE9PSIsInZhbHVlIjoiaThBTHJPVWVkdVRBOG9TTzI1SFYyWlFESy9ZSy9SellYVEdGY1ZrQ3Z3QTM0dXNWazRkTnVRRU5PbEJTenJCS1cva2Fya1ByUE1iQ1ZrcWd0QVlUZVZFS0JFYndaUWNmVldoS0hzN1psakZ2OHpqTTNVbmtmdU5ZSlpseFE1RGhFWk01MTA2WE8yNTR5YWozdW9NNEF0dXpSemd4QW1qWk12UlZVS0NBWHZycFlRU3JSUWlMR3hWTXdUUFlyMEFzM0RCaTlDdC9hdGFpOElqdTlOQkQyc3ZmUnJMVURUMVBBVTU0UFNZTkkwdkFBWGtoR3JDT0dWUmVPeGlKUVJuSklMTkgyRzM4NDlRLzU2SW1QQnErUTBybWVZOHhnc3laSUc3ZFhSMFd0TmVBbDZoWEFCaXJiZnRSOUhha1o5Z1g4RWoybWxLQXJQeXBUMVdraG9ZMkFKaVZKbmtIWlcrOFRDakU0VE9qVFVmcjVKWFlvZGZRdDZIV0F3QzFiL3BRamtiVHdmbDA5WkIxTG9xaStsY09JclVncVNIMUJWbTdPRXYzTDlObkNyNzFlSGZuRlo5azUxMHVsQW5aREgwbEQxZHk0N0xHUllmV3pjSkkyYmFJaURXdHJlUnE4YmhlUXZ0cGV5eXlpcTBTQUhJU1FST3VYTVlKTS8zaVlDRkkiLCJtYWMiOiIwYjFiYzU0YzJjM2M2ZDRiMzFlNDQwMzllYzQ3NmIzNDY2ZTU4MmQ0NWU2MDEwMTY1NTRjNTYxYWJmNTc4YzgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112117854\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1030328567 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1030328567\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-501235814 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRESUxUZG9PbmFYYzhvWUQ0STczaFE9PSIsInZhbHVlIjoiamEraVVmQ0o0RUppcnNydC9TMmp5dGdvanNEMzJlbGd0WTMwTFgzWkdSM3k1eU9kOFYzaEV0c3ZiVmJ6RjZobEJZU3BoeU4xWUU4VW5RaG9BZ0JPSDdIYjV2NmxZS0FPbGNKem5jaXAwWTBxNm1YZzZiMnVhaHB5bXRMT1dqdkEzNzBJa29iM0pDa3pydVQwb2NWNlJEdzBWUzhyOGlQRThpT0YxdlJqY25sbnlzVDNjME55Y3hWdGtsYTBpZ1JMTllNa25CcDlBMlpKZHFsd2ZVaGxjSGJMUmJnVmhxaXhlbDNEaTJWV09FRmpDM1BkcS9kd2RKTEI3U2RHYUtKR1VyV3lGUEQvV2VBRUxCK05JQ3RBVCtrSnRsZFBQUGVxbFhiWkJYQUZCWms2RlMvcElMUU1pUzBNbzh0SnR2WlFnYTV0Ym5lMHY2RDMvc0RLdzdLWGV6dkw2eTBxNWlteVpRWmNTdTdVYlBuSWVEZVZaTzRxYjRERW5KVzNCSjlKWUJ0NW9raFNNLzNSS0RqOWZJZlB1Sm80dzN2UjlsQXZGa2I1S05UNVVPZlNCVVlmdG0yZUtKOUx4SVN5VXR2eWFvNnFHbHJNdUM5VnYvN1luMHpWTFpPRmdTSGhHbDFtNmZjNE10K2MwY1lTUGpsNVZNNTZDVlpua2luVmtIcnciLCJtYWMiOiJjNjc5YmZjODYwY2FhN2E3NDg1OTAyM2FkMmQ5ZjFiOTU3YjY2Yzk0NzJlYTUyZDgyNjBmNDY4OThmYTJhZDExIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBGSW9iUmxLZzAzM1lFaGN2cUs4TFE9PSIsInZhbHVlIjoiRkR4aHk2eFgzazdGOXdwRFk4eFpnejVVZGR0N1VhdVFnRXVRNEFvTkEwdnFpaSs2Y3Y1UFJweG0yZ0JPVkxsT2k0cDBCN3pybFlXVTlaS0xVdGRMRGI3a2ZRNGl2MHl0TTFheVByalZKZ2p1Z2ZJREd5N2laWSttRzZ4SzdPcERRNS95YlRxaFdEYVpXeEMwRTdBcGpvQzFIZktqakZFWThORUhBc0NSL3FDOXRBUnpxVDVxdytBa0dkZmMvU1F2ZW5aeDYzUzR5bFZqTlVFYlA2dUROcHE4aHBQN2R1S05pWHVnRld4STZPZHp2UmhaSktrbml5V3dpb2h4eGV5TUhmejNGTklmYjUrRksrclpLT1Y1MWh6NjNBSTRxWHFhekpFVEdKcm5pRTV0S2NOb0YrVWdySXFOdlBOd3FhRTJTOEo3RnVJdlFlWGFlQWQzRUoxb2pHN2E3aXBOWit1OUJJeXRoRTBZa09ZWEhHVFlEcjJiVjJOdmpQcklNdXhteUdMU3VHQzBLZUN4b041L1Y2aFY3KzJPKytFS3llckNyYkRDcG5XZ2tuRGJPYTRFMHovZjhuV0lDR0o3NUlLYXRoeVE2TTVCYm9WaytyVzV5M0FVNGxMbW81Nll4VW9pTC90OU04WTVJdzRSVUluaXBWQkNnRzlnRFEzWlF6a0oiLCJtYWMiOiJmNmRiNDE5NDM1YzU2MmY3ZDJkNjQ4MjFmN2E5NjJjYWEyOWEyYjU2MWQ0MzhlZWEyYjU2YzRlNDU4ODIzZTY0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRESUxUZG9PbmFYYzhvWUQ0STczaFE9PSIsInZhbHVlIjoiamEraVVmQ0o0RUppcnNydC9TMmp5dGdvanNEMzJlbGd0WTMwTFgzWkdSM3k1eU9kOFYzaEV0c3ZiVmJ6RjZobEJZU3BoeU4xWUU4VW5RaG9BZ0JPSDdIYjV2NmxZS0FPbGNKem5jaXAwWTBxNm1YZzZiMnVhaHB5bXRMT1dqdkEzNzBJa29iM0pDa3pydVQwb2NWNlJEdzBWUzhyOGlQRThpT0YxdlJqY25sbnlzVDNjME55Y3hWdGtsYTBpZ1JMTllNa25CcDlBMlpKZHFsd2ZVaGxjSGJMUmJnVmhxaXhlbDNEaTJWV09FRmpDM1BkcS9kd2RKTEI3U2RHYUtKR1VyV3lGUEQvV2VBRUxCK05JQ3RBVCtrSnRsZFBQUGVxbFhiWkJYQUZCWms2RlMvcElMUU1pUzBNbzh0SnR2WlFnYTV0Ym5lMHY2RDMvc0RLdzdLWGV6dkw2eTBxNWlteVpRWmNTdTdVYlBuSWVEZVZaTzRxYjRERW5KVzNCSjlKWUJ0NW9raFNNLzNSS0RqOWZJZlB1Sm80dzN2UjlsQXZGa2I1S05UNVVPZlNCVVlmdG0yZUtKOUx4SVN5VXR2eWFvNnFHbHJNdUM5VnYvN1luMHpWTFpPRmdTSGhHbDFtNmZjNE10K2MwY1lTUGpsNVZNNTZDVlpua2luVmtIcnciLCJtYWMiOiJjNjc5YmZjODYwY2FhN2E3NDg1OTAyM2FkMmQ5ZjFiOTU3YjY2Yzk0NzJlYTUyZDgyNjBmNDY4OThmYTJhZDExIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBGSW9iUmxLZzAzM1lFaGN2cUs4TFE9PSIsInZhbHVlIjoiRkR4aHk2eFgzazdGOXdwRFk4eFpnejVVZGR0N1VhdVFnRXVRNEFvTkEwdnFpaSs2Y3Y1UFJweG0yZ0JPVkxsT2k0cDBCN3pybFlXVTlaS0xVdGRMRGI3a2ZRNGl2MHl0TTFheVByalZKZ2p1Z2ZJREd5N2laWSttRzZ4SzdPcERRNS95YlRxaFdEYVpXeEMwRTdBcGpvQzFIZktqakZFWThORUhBc0NSL3FDOXRBUnpxVDVxdytBa0dkZmMvU1F2ZW5aeDYzUzR5bFZqTlVFYlA2dUROcHE4aHBQN2R1S05pWHVnRld4STZPZHp2UmhaSktrbml5V3dpb2h4eGV5TUhmejNGTklmYjUrRksrclpLT1Y1MWh6NjNBSTRxWHFhekpFVEdKcm5pRTV0S2NOb0YrVWdySXFOdlBOd3FhRTJTOEo3RnVJdlFlWGFlQWQzRUoxb2pHN2E3aXBOWit1OUJJeXRoRTBZa09ZWEhHVFlEcjJiVjJOdmpQcklNdXhteUdMU3VHQzBLZUN4b041L1Y2aFY3KzJPKytFS3llckNyYkRDcG5XZ2tuRGJPYTRFMHovZjhuV0lDR0o3NUlLYXRoeVE2TTVCYm9WaytyVzV5M0FVNGxMbW81Nll4VW9pTC90OU04WTVJdzRSVUluaXBWQkNnRzlnRFEzWlF6a0oiLCJtYWMiOiJmNmRiNDE5NDM1YzU2MmY3ZDJkNjQ4MjFmN2E5NjJjYWEyOWEyYjU2MWQ0MzhlZWEyYjU2YzRlNDU4ODIzZTY0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501235814\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}