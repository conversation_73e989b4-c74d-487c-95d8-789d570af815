{"__meta": {"id": "Xeb4aa404be9f86158b233c4938c46c65", "datetime": "2025-06-28 15:01:18", "utime": **********.425432, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122877.969492, "end": **********.425454, "duration": 0.4559619426727295, "duration_str": "456ms", "measures": [{"label": "Booting", "start": 1751122877.969492, "relative_start": 0, "end": **********.364102, "relative_end": **********.364102, "duration": 0.3946099281311035, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.36411, "relative_start": 0.39461803436279297, "end": **********.425456, "relative_end": 2.1457672119140625e-06, "duration": 0.06134605407714844, "duration_str": "61.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45608968, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00245, "accumulated_duration_str": "2.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.395212, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.939}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.405608, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.939, "width_percent": 21.633}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.411087, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.571, "width_percent": 11.429}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-770143490 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-770143490\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-73040149 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-73040149\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1250004452 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250004452\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-830426260 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751122833995%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9vbG5HUkNhdElEcXpDank3aUJWbmc9PSIsInZhbHVlIjoiUUZSMWhXQXgxV3VSbktTRjdIdXRjVWpDNzVlWlRqOE43bUtSMWxrWWcyUytJWDl0aWRUcmZjaUwrbHo3VW1SN2ROOTBxUExhTW1GeC84UDNrTm42SnJ6K3pSaDRwZVdTcEJDbzFDN0tRVTBiV2ZJdUhJVWp0ZUZ4N3FVdytBdnVhaXR4UEI4bzZGVFczdUUxTmozbWRla2l2OE55SzVCNjR6Zm1DWE5nRlBlVmkxNDQ0SFQwdWJ1ZVdFUFlkMTFYRFl1a3pEWFlveGRTeEt2bU1uUzlwVitPd3pSMG9iMXpCVVRuUExnVnNBN1RDQ3NBZlpPSG41aytmT3JqVzY2aFVHanI2Y3d2aW9jY1VZUlNDV3B3Y2pIWTZ5NUpVcEJMaU9SVE9FdHBzOHJ2aDcxUHNzTGpSQ0p4T1N1bVVRNVVoeW52OFZvS1drRlM4UG9lZ3lwV08rMVNmSVNod2NNb2ZuOEhiV3JPOHZxcTVhb1pPdUtKeWVVVlNZb0tzT1cvVjI1MWZkMHBZMG5JSUpBTlNhSDNId1V5azY3d3ZFZm1tZXFRUlBqL1hSZWZhVzNMSW83N3JOV2tyRThhakxRNjMyQ25VOVVIbjZGQkM0OW5xZDFiVnBsVHVXTFF4RmxkUGNnMk4xQlVTLzJHRWhJSGhLdHgxd3RFbEZ2VUZwOXAiLCJtYWMiOiI2NmQ0MmQzZmExOTM2YWZiZTM2ZjNiMGU1Yzc1ODczNzk4OGM2MjU3ZjEzNjM3YzljNDJlNjVjNGNmYzBhMDAzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5FRXZPTjVzMWtNVjIraGhicEVlMUE9PSIsInZhbHVlIjoiU0VUODV6ZjFMbjJmNWhzNVBaTmsxK29WMWlwVWZiUmtOMXg5N1l4bjN0dEg3SWVwUWNTdStpVXV5dTZ2VTdnd0RLMUNRdit6U2ZDb2lDOGRsYTJHNzBpYm1qaFJ1MjdCcWxqRjQ2WUJHbmpjeGNoeW9iL3JaSjV1RlJDVGE3U21LWVN2WkZpRUl1dTlqUE5WRFhBR0lCZzJnV2FYb08rUDJhMk01dGtjWmpwLzZ4L3J0NG12Tk12L0puR1c3Z2dWRnlkSFpscmZGU1R2OE1vOEtyOUNOczZNQ01mTWJtUWpRRHlrR1gzekVGZ0ZDUVdwemtjZ1Z0aGh6NW1GdEpXR05TL0lCbXpmbjRyYVoxQUlYMkdsQzNmL0RnakpMdlc2RGFMRzMrUFVKd0M0TWNZSUJ6aytvb25WTExxYXB4WFd0bnZrNkdUWGw0WThFZ0ZYVXdsWDdpcTJhKzYrQUdDNUhUY2R4YklxS2NLSGpKaGNrT2dKSTk4QmpnRXE5cWNRQ3pyNG5mRFlTQ2hWckQ0aE43Rnd3eUNyaWlYendKb2JQR3pjRnh4ZFFQNnVEL29zTjJYa256NG5xdWpkM2dvMEU1MG1aOVBVVndvZVc3K1JqWVNrUFVlSFE5TGp4QmtwKzYvZnpqSFZuSU96QUpwcnpwTzh6RUZOd2J6RTl3OVkiLCJtYWMiOiIzYzY3NzU2NGNhZDdjY2UxMDJiN2MxMmVkYjM5NGJhMTY4MTYwOGQxZmFmYTY2YjIyYTgzOWQ4NWNlZGJhZDhjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-830426260\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-311291329 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311291329\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:01:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVhblVvSUZUN0Z5SkU4VWo0N3kraUE9PSIsInZhbHVlIjoiK0swZk01RHRleUhTbDRnenduc2lsMGxvSjZQN1ZNZUpqSDVrWjB2UmpjOEhGZ2ZIT3ArRlU5VVZZU2IrK3FnRzV0NU1yVnQ0dUJKR3RHWCtuSmZLcnkydmpBMzdFZVVUNEhrUnFjdEtSbVN3QkNlcFhaWklsVTdQc0xYMGFoQnFkM0VDVm1sOWhrSFREbG9DM3M0RE11TC9kUWNuVzlVQWNsMmE2TjJrVDRRZ2hFWWRKQnc4WHlQc0ZSR01NSDRZSGtPbkxnTE5kRmNrcXQxZW1OWGZCbnpNMm94VmxlR2xTdkFtbXlpQjZLMkpnQ1NlbVdxSE1aN0FlKzRzMU4wNW4vcTdtN1Y0SVpMb3lINld1OERZWTdTTEU2MCtpS2lHWURhSk9oME1Vd01lbVliYXBxS1lLNE9MNjd5SHN6YjhDTUl2OTdiNDhrK2laYzJudzI1Ny9KamgwdW5BYXlJQlJBZTdiM2dhZ3lFRjVxVXZFbmpkaVF1SU0zZ29pUy9jODM4MkRjaVBXamdEcmpBcFkzM1pTYi80Y21ldVR5b1N4YlpMenQ3M1JMejg1Ukpyb2M1RjNtdlo2aFRJYU10SGVxOTg1ZUZ5OFVlZEtsTUM0bysvd29zTFhjanR3U2E1SHBNNDRjb25HU2Z4dmFINHlsamhNMytDWnlGemlNZ28iLCJtYWMiOiJlMzEwMDhhNmJhYjY3ZGI4ZjY0NGFmMzBjODkwMjQ2OTNlYThmODcwYTk3ODgxZmVlYTQ2NDkxMDgwMzEwOWQzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:01:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpKZXF0Ky9ERnRLdzlFZUR2U1RtbUE9PSIsInZhbHVlIjoiVzJCbEVlcVpXaDZ0TTByN3N5bnJOTHpKeno1UGlIdzlSOXA3R0hVR1Jmcnk4MGJ3dDAwQnJFZkFZa1VGMTdQdDQ1TW8ySW8yamVUaERzWllHUUkvY2lzQmRRbHRJUmlOWHlWVXhxME9YZWZCUnR3clVLRUl0eWNTYlhFL2pLTFZmVGpqUitFN0UzZ0taUVplbWtVM3VCcnQ0cThjWWV0NGJDRmlYZ1pXUzFCd2lrTElMNUtTTjBvbkJkd0hmSnM5Sm1GK3htOFlUZWZqdHpmcm5sTmtMQmxuRXlLdlE2dWVNZ3RPMzNYc2VJRngvRGxEWmNGYTM3MnpUVndXZmR2alNqbnYzQitRRE1FMW9xZFExaTA4a3A0OGUxNW1HeG5EMDdGZlF4NHJVVDAwTkM1bFpuUGc4NlNJL2NPQ0IrUjFlYXhuczA2WGlFUnN1R2NrTUNhSUpLeTRJaW9nMVJBOW5RZldYenJ4dWo4QWk4UTc2VWxWdGVUQjNEWVg4T2c1L3RoODRVMERJQ1FQYnFwWnVaR0JjZnc4TXROcEhnNmJjcGJ6aGhvOWoyeThlZzFsd3Rlclp4amFObWJlZDFGNDYrVXBLTUV4RmFKMjROZk1hNllhLy8zUUEwRXIwSmFEZEh5eDkwZy9EKzFIZExCK1h4MWRJNU1KM0ZaVnNxK2siLCJtYWMiOiIyY2RmMjUyNDhjYzk3NjI5Y2U1YzQxYjcwYmE4NDdjMzNmYThkNDhiYWZkOWIyNmRmYzJkOTJhYzMzNDFiNjhlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:01:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVhblVvSUZUN0Z5SkU4VWo0N3kraUE9PSIsInZhbHVlIjoiK0swZk01RHRleUhTbDRnenduc2lsMGxvSjZQN1ZNZUpqSDVrWjB2UmpjOEhGZ2ZIT3ArRlU5VVZZU2IrK3FnRzV0NU1yVnQ0dUJKR3RHWCtuSmZLcnkydmpBMzdFZVVUNEhrUnFjdEtSbVN3QkNlcFhaWklsVTdQc0xYMGFoQnFkM0VDVm1sOWhrSFREbG9DM3M0RE11TC9kUWNuVzlVQWNsMmE2TjJrVDRRZ2hFWWRKQnc4WHlQc0ZSR01NSDRZSGtPbkxnTE5kRmNrcXQxZW1OWGZCbnpNMm94VmxlR2xTdkFtbXlpQjZLMkpnQ1NlbVdxSE1aN0FlKzRzMU4wNW4vcTdtN1Y0SVpMb3lINld1OERZWTdTTEU2MCtpS2lHWURhSk9oME1Vd01lbVliYXBxS1lLNE9MNjd5SHN6YjhDTUl2OTdiNDhrK2laYzJudzI1Ny9KamgwdW5BYXlJQlJBZTdiM2dhZ3lFRjVxVXZFbmpkaVF1SU0zZ29pUy9jODM4MkRjaVBXamdEcmpBcFkzM1pTYi80Y21ldVR5b1N4YlpMenQ3M1JMejg1Ukpyb2M1RjNtdlo2aFRJYU10SGVxOTg1ZUZ5OFVlZEtsTUM0bysvd29zTFhjanR3U2E1SHBNNDRjb25HU2Z4dmFINHlsamhNMytDWnlGemlNZ28iLCJtYWMiOiJlMzEwMDhhNmJhYjY3ZGI4ZjY0NGFmMzBjODkwMjQ2OTNlYThmODcwYTk3ODgxZmVlYTQ2NDkxMDgwMzEwOWQzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:01:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpKZXF0Ky9ERnRLdzlFZUR2U1RtbUE9PSIsInZhbHVlIjoiVzJCbEVlcVpXaDZ0TTByN3N5bnJOTHpKeno1UGlIdzlSOXA3R0hVR1Jmcnk4MGJ3dDAwQnJFZkFZa1VGMTdQdDQ1TW8ySW8yamVUaERzWllHUUkvY2lzQmRRbHRJUmlOWHlWVXhxME9YZWZCUnR3clVLRUl0eWNTYlhFL2pLTFZmVGpqUitFN0UzZ0taUVplbWtVM3VCcnQ0cThjWWV0NGJDRmlYZ1pXUzFCd2lrTElMNUtTTjBvbkJkd0hmSnM5Sm1GK3htOFlUZWZqdHpmcm5sTmtMQmxuRXlLdlE2dWVNZ3RPMzNYc2VJRngvRGxEWmNGYTM3MnpUVndXZmR2alNqbnYzQitRRE1FMW9xZFExaTA4a3A0OGUxNW1HeG5EMDdGZlF4NHJVVDAwTkM1bFpuUGc4NlNJL2NPQ0IrUjFlYXhuczA2WGlFUnN1R2NrTUNhSUpLeTRJaW9nMVJBOW5RZldYenJ4dWo4QWk4UTc2VWxWdGVUQjNEWVg4T2c1L3RoODRVMERJQ1FQYnFwWnVaR0JjZnc4TXROcEhnNmJjcGJ6aGhvOWoyeThlZzFsd3Rlclp4amFObWJlZDFGNDYrVXBLTUV4RmFKMjROZk1hNllhLy8zUUEwRXIwSmFEZEh5eDkwZy9EKzFIZExCK1h4MWRJNU1KM0ZaVnNxK2siLCJtYWMiOiIyY2RmMjUyNDhjYzk3NjI5Y2U1YzQxYjcwYmE4NDdjMzNmYThkNDhiYWZkOWIyNmRmYzJkOTJhYzMzNDFiNjhlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:01:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}