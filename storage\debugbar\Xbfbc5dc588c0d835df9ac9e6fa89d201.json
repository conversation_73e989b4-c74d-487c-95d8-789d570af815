{"__meta": {"id": "Xbfbc5dc588c0d835df9ac9e6fa89d201", "datetime": "2025-06-28 15:04:29", "utime": **********.253315, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123068.771426, "end": **********.253335, "duration": 0.48190903663635254, "duration_str": "482ms", "measures": [{"label": "Booting", "start": 1751123068.771426, "relative_start": 0, "end": **********.166983, "relative_end": **********.166983, "duration": 0.3955569267272949, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.166992, "relative_start": 0.3955659866333008, "end": **********.253338, "relative_end": 3.0994415283203125e-06, "duration": 0.08634614944458008, "duration_str": "86.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45081176, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026400000000000003, "accumulated_duration_str": "26.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.202219, "duration": 0.02542, "duration_str": "25.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.288}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.238346, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.288, "width_percent": 1.78}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.245043, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.068, "width_percent": 1.932}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1965739417 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1965739417\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1169495330 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1169495330\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1280615267 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280615267\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1249107509 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=10&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122996219%7C8%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktFaG0vS1ZqdVZTc3o1S0hGdGx2MWc9PSIsInZhbHVlIjoidVhab21xbkJaVlRTdWY5T0tmV3RxOEZnRk1EQ2h5WjRyS2R6aDlWR2xJQ2hieWFGRGl6MzVJRTVhZTBudUFwc3FzTDI0eFdCckJueUhjTkFDUUptSlhLUWkwVTlqZmZZbytMbG1qbGY0aFdtcGlTeXlFWUY3WTVVZ1VRMnZDdnc2bGpNKzNvNVkxeUQxTVVVRWJEL09Uc3MwV1loK1Q3UnBEUlZiREtUU3hYbU1yQlMyY1pvelFaTGRhTlZkZmI3WDJNRU80NVJrWEVPVlllQzdQYlFHSDBiTzhuODF6eXN1elNoM2NNUnZoWElVTGc2QS82dDJRVEhjOUcrQzJOSFBlTjM3WEpMdUtoQ2hFcHNTKzV1OE50REZnaVl0T1JaZkhxNkFxb1o0dDQ5WnVCanZqdFJJcDVLdUhaRUExSTkyWE52OFgzRFNEUk5lbWNtQmprQ0xha0JlcXhBZlVObUhKbWRObGZQaTVJYlVKR0l0ODdVRjBQd0JYT05vVWpMQ1hERDlHdHdvNUlocWxFUUhqWnYxNG5BMEQ4SmE5UlJYc3F3M29iNXJvM09uYlVYd1pXRTFQTFdMNHo1c29zM3Rmc0oxaHMyUnJwbU15djd3a2pTRHNJYWZSUHZYS0MwaklCUlRKdWlXY1lJN2FlaStqNUluSStFZ1BKbU55ZDgiLCJtYWMiOiJlNWNkMjA0MmVlNWFlOWExZTRmZTY3OWFlYmYzNzBhMDdjOTM5ZDdkNDgyYTU4MDUzZjU4NTk0YzE0N2VlODZlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVQaDUwR3dSYzUzNjliYnA0aHdFL0E9PSIsInZhbHVlIjoicUJUUm42N29WeFpPd25abzZBdmgyQy96cFg2dE1XQ2d0L3djMVFXU3k2YXJVWXFyR2xHMVZ3SFArSkM4LytKNEw4ZGRJdlg0UGdaeG0wMUxlMGYvRjVOM2tMQ28xNVg3TG84WnVRREQxa29HOUNKWGVsd0hzNjh5MDFEcTNsaFB3anZOdklHdGJXQXJuWkIvcnlPR1lxUWZZcWQzRmxvU3hPUitDd1NLNWZqSjNQYTlmYVIvRmtoTXVWZGtyUTB3c2ZCWkYwTW5mMmEyelVucHVvaTkvRUNjeVdlTHk3Q09acEdCNEozQkJMT1hGTzhhcmFwbnV0bVcwQjN0TmV2MVBqai9EYzdkVnd2enhZeERZb0dYWS9kYzVSNGdoK0JqTHZZcXU4UGY1Q1N4ZUZzdnVuTkRuUjlzdTZDcUJFQnlyaDZhMWtzakZ4L0NNV0wvN0lKbW5iTEhPemNsMUk3bUFZOUtubkxFczRYTFFTSVFnbVpjWEZCV2dpWXdOaWVmQ0Z1ZWlQKzN6K25CYjNGYnU4QWp5d001bEhxRmlISFVMdFg5ME1EdFRiSVBpMjhxS2RESi9FVEltSHphYmlQRVg1b3JEMTYzSWJXTHhwTE1pSWhoOG9HUVZ5MkcxUHpldTdxT2FLcUs0OFduZENIN3dLOHBNQmlCdWVZSVhqNVUiLCJtYWMiOiJiOTBjNGI4MjQ2NzEwM2M4MTQxNmE4N2Q4OGFkMWIxZTcxY2U1ZGNiZjExZDUyNDM1ZWU5MjFmYzJiNTk2Y2I5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249107509\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1692096032 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692096032\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1781868455 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:04:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpYcnZObllNSGQwc0pVSWxISVBlMWc9PSIsInZhbHVlIjoiUjR0VWl3d3ZYTENWMjByQ25uME1jOVJnNGRwRmlQcVBWL24yejRlT1BQbzdpTVM3WjNXdVhNNFNIS2I2SGRLeG13MHlkMWVrQXVDRTc1aEhtOFN0WFpLcGJLZk1wRU1lSEtrSXNna0xITVNGWWtudzZibW1yWTVyRE9hS213L1JEbHZVczBzcUpiS3dLY09GdVZ2N1FCc0xYYWx3Um0zYzlTRUlsc2tnRHpTOVZjM3l2dzlOWm00VDFaelFlZE1XYWJZaWFiUjAvc2JBckNhQVlnWkorbW05QzFFeTNMK3pPU1U3ZDRWSTFBQ3hJeTA3VERTb1RwUG1UanZKNzdIRGthdFlXcUk1NFRwYy9tODZjNTFpS3k3eGN1WThhVHBvSGExNWdLRDZGUURzaUpldTYyMytKbkJ3UnpnTXNaWDZkanNBa1VrcFh6VVdPNWU1M0tUTUlleFQvUW1heWI2cC9kYkk2UmRmc3YxQVFsd1Vzc3AyakdBbE9BUkJJb2t1MkNhNVJ5eEZqczR3YVFOeWRWQjFMQ2xWdk9JQmNvUVExclVQNE1reWIvcENRc2xRMU9KbmhaTFdSdHlZWXJCZHBNYVRWK3daMlBKU0tOSlo0ZmFHeEI0Vy9MaHVPTjdHL3VWbW5nUGtnRE14M0gwaDlQOTJ2aEk3WXRJT09hZnoiLCJtYWMiOiI3NGYyZmE2NzhlZjBmYjExYjVkZWQ0OGZiODE0NzhiYTA2NTJkYTE0YTZkMDQwNjBkY2EyNGE5MDQ1Y2MzNTUwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxJVWR2alMxZGNwUmlMejVvQ09odWc9PSIsInZhbHVlIjoiM1NZaHloa2lEU1RyMmtlaGVVVm0xemQvZlRhM05xaVg5N3IzWVdQNmQ5YlVJS1Z2SHgzQVo5aTViSlJGc21QM2N4RkVrcFFiM3NyeXA0a3dlelFvWjY3bHEvODdZNjlHQWhHT1hrZ3pmMFc0Q0l0bzBvSElzSXFHNVlFSzVJSU90UTZRd1BGMEc3MTVCL1FIbnI5ZjhSMXNQTzNhYVNnNjNqbStpSytCR3JmT0FBL3l5aDQ3SCtZdjZsU044SHN6b3FZK0FIb08zMjN4SDZBTlBsaGIrc0x6aFVIbDNybHhvaWtwNzFmOXcraGoxVElVWndiNjVPZUxPcFUycmNZSXVraXltUEtwV0tYb0NYWWM3Q295ZGRlNlowQ1p3aXhMYmIreUxTSjR3ZUN1QzZHV1Nxc0Q1SUlkalRaaHlxN3JRRzNkL1p1TmpocVk5MVp4R2c3eE9rMDgrWllSV1lnSnBoU09sUkJENTdFQjUrOUdWYndMc2p5VXVxWHhIS256eHJ6NVdmTFNuc2Z4M2lXUlhYN2xaZlhqa20wSDQ2SVVUU0JYODFLbFE0V2lTQ3JlMVhLTkxYVEVMZW43VmFkT2VMRFVNWjNtdGI0MHloWnA5V1JaVjNCUEwzNjJxSlR2Y2Y4SGxDWllkSit4U1MxdFFDcXNjKzBtSGJlUzlsT2giLCJtYWMiOiI4NjNlMDUxYjE1YjRiYmVkMzA2NjJjNDFmNjQ5OWM1NDkwY2M1MDQ4ZGYyYzhjMjg4NWM3MzVlNDJmMWQ0ZjRkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpYcnZObllNSGQwc0pVSWxISVBlMWc9PSIsInZhbHVlIjoiUjR0VWl3d3ZYTENWMjByQ25uME1jOVJnNGRwRmlQcVBWL24yejRlT1BQbzdpTVM3WjNXdVhNNFNIS2I2SGRLeG13MHlkMWVrQXVDRTc1aEhtOFN0WFpLcGJLZk1wRU1lSEtrSXNna0xITVNGWWtudzZibW1yWTVyRE9hS213L1JEbHZVczBzcUpiS3dLY09GdVZ2N1FCc0xYYWx3Um0zYzlTRUlsc2tnRHpTOVZjM3l2dzlOWm00VDFaelFlZE1XYWJZaWFiUjAvc2JBckNhQVlnWkorbW05QzFFeTNMK3pPU1U3ZDRWSTFBQ3hJeTA3VERTb1RwUG1UanZKNzdIRGthdFlXcUk1NFRwYy9tODZjNTFpS3k3eGN1WThhVHBvSGExNWdLRDZGUURzaUpldTYyMytKbkJ3UnpnTXNaWDZkanNBa1VrcFh6VVdPNWU1M0tUTUlleFQvUW1heWI2cC9kYkk2UmRmc3YxQVFsd1Vzc3AyakdBbE9BUkJJb2t1MkNhNVJ5eEZqczR3YVFOeWRWQjFMQ2xWdk9JQmNvUVExclVQNE1reWIvcENRc2xRMU9KbmhaTFdSdHlZWXJCZHBNYVRWK3daMlBKU0tOSlo0ZmFHeEI0Vy9MaHVPTjdHL3VWbW5nUGtnRE14M0gwaDlQOTJ2aEk3WXRJT09hZnoiLCJtYWMiOiI3NGYyZmE2NzhlZjBmYjExYjVkZWQ0OGZiODE0NzhiYTA2NTJkYTE0YTZkMDQwNjBkY2EyNGE5MDQ1Y2MzNTUwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxJVWR2alMxZGNwUmlMejVvQ09odWc9PSIsInZhbHVlIjoiM1NZaHloa2lEU1RyMmtlaGVVVm0xemQvZlRhM05xaVg5N3IzWVdQNmQ5YlVJS1Z2SHgzQVo5aTViSlJGc21QM2N4RkVrcFFiM3NyeXA0a3dlelFvWjY3bHEvODdZNjlHQWhHT1hrZ3pmMFc0Q0l0bzBvSElzSXFHNVlFSzVJSU90UTZRd1BGMEc3MTVCL1FIbnI5ZjhSMXNQTzNhYVNnNjNqbStpSytCR3JmT0FBL3l5aDQ3SCtZdjZsU044SHN6b3FZK0FIb08zMjN4SDZBTlBsaGIrc0x6aFVIbDNybHhvaWtwNzFmOXcraGoxVElVWndiNjVPZUxPcFUycmNZSXVraXltUEtwV0tYb0NYWWM3Q295ZGRlNlowQ1p3aXhMYmIreUxTSjR3ZUN1QzZHV1Nxc0Q1SUlkalRaaHlxN3JRRzNkL1p1TmpocVk5MVp4R2c3eE9rMDgrWllSV1lnSnBoU09sUkJENTdFQjUrOUdWYndMc2p5VXVxWHhIS256eHJ6NVdmTFNuc2Z4M2lXUlhYN2xaZlhqa20wSDQ2SVVUU0JYODFLbFE0V2lTQ3JlMVhLTkxYVEVMZW43VmFkT2VMRFVNWjNtdGI0MHloWnA5V1JaVjNCUEwzNjJxSlR2Y2Y4SGxDWllkSit4U1MxdFFDcXNjKzBtSGJlUzlsT2giLCJtYWMiOiI4NjNlMDUxYjE1YjRiYmVkMzA2NjJjNDFmNjQ5OWM1NDkwY2M1MDQ4ZGYyYzhjMjg4NWM3MzVlNDJmMWQ0ZjRkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781868455\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}