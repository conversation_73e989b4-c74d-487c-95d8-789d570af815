{"__meta": {"id": "X71fb2f14eddab9c1b452b04135153a18", "datetime": "2025-06-28 16:30:48", "utime": **********.968497, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.467372, "end": **********.968516, "duration": 0.5011441707611084, "duration_str": "501ms", "measures": [{"label": "Booting", "start": **********.467372, "relative_start": 0, "end": **********.909118, "relative_end": **********.909118, "duration": 0.4417459964752197, "duration_str": "442ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.90913, "relative_start": 0.4417581558227539, "end": **********.968518, "relative_end": 1.9073486328125e-06, "duration": 0.059387922286987305, "duration_str": "59.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46429488, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2513\" onclick=\"\">app/Http/Controllers/PosController.php:2513-2547</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00241, "accumulated_duration_str": "2.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9485831, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.423}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.960233, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.423, "width_percent": 21.577}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1465/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1003328322 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1003328322\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1472586015 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1472586015\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1490049183 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1490049183\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128238007%7C50%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpDMjBseXE1eWZGL3dMdTRVMkUrbHc9PSIsInZhbHVlIjoib3BEbk1lbXpCM1FKYjluQTE4ZzBBZVlBdm1xYWd0eExLSHlWb1JsOGc4ZEx0OEt2UEE0a3hBVFkwSU41T0dVQWZjNmdLZFE0ZkxHL2wraWcreFp3dW10a2o3N01ycU0yNnllMEVCSjFCN2hpS3N0djg4Z3FXSTUvR012ajBrbFNRb3hzSi9idU43SjM3Snp4dEZPeTU2Wk8rTFRRQUJYMUZTYWZyNXcvZXVoUHRWSTJ0WW9BNUo2eThOVXdmdjh2VUs2SFM1L1oyM0s0N21Hb2dFdFdFWkVjOVRyTlo2aG42QXlaS3ppeG9QemZaUlg2MWE2OExQZU0zck16VEkvNlpMZTB5alVIRzgzZVl0RDFJY2FIaE9KcGk0WkoxNEtUb3JtY3VqYk1GbCsxN2hKaHJXWVl2VGxDaHpOaVZPaUtZSTFHdDl5NGo2TUZETzRxb2xkVmtJc0N2dU1Ydmp6QVBpMDN2N01ORkVLMmMxMmxyZXJMeHd0UE1yOEs0aUVKYU1kelhwSS95QkpzTE1UdmZiWVhlWU1mV1V3ckdHenF1SlBlTzZpbGR2bUVvK3ZKY095d09KaHowTDlNUUZER3Zmd3BHTVB1SXN5UGM1SjExbTRHU2VvSzZCaDZ6U3VEdjVwNmkzaXllaW5vUlo0K3QzcUpveWRwcnBVZDlJMGQiLCJtYWMiOiI4Yjk2ZWYxMTg3MDI1ODIzNjZiYmEwMWY0OGMwMGU1ZjlhZDRjMjAzNzI2MDI5ZDVlMDRiNGMzOTQwYzBhMDYyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZiejlKODUxS1BkRjA1RW1KbEgrSUE9PSIsInZhbHVlIjoicm1zVVo4dVhqaFBra3h5TXZzV0Z1MW5CdmRmeHJmcldscGpmZUZ2ZllheC90M0N3OTl3L2MzNVQyKzR1TkplSXYrTDV5bnptZDc3aVVQazFzSG92eEx2RTkxTVdIaTdERzYrT3RuQWRQN1hTblZXb05MaklMbUEyeS91Q1Z6V1huQ3FCOWNIVlQ2TG5ycThpRnZpNmhWMzNKNVQramJBeDFNSEtPODNYejVSdnh6SG1WNHJnZ2piVm9HdjNSRHFmeWgrQmRacCs3VTNRRTV5UW53TmNRQkt2MGRHQm5SVVlRTy9CYXJSVktaUEQ3UGliSjdMcUJZbUVkeTZlZUd3Z2hpOW5FTmlVV05tRVlqODg4RkQvTnR1OCswN2dlQUMwVXl6c055L0l5NEJWcXFBK2pNaVY0M0lGVVhFaHJ5bitJdGlWMG5LQXZXMnZCaWtCbWpLeHl2Vi9FYkF6QmpDeWZDRzJRYTZuNEdXdUhBRUp0d1F0dkhORDVTZGNaUm52UllMeFVyWUZZd1ZLS05zWEhrUU0vY2lZenB0WnArZ3hvNnVIbm1XMy8yZXpNUU5kMitwVFhRU1JkQ3VCUjRqUUVENng2RllZRC9JZkp5dkxFeFBxanhzc1lEd3RKUXdzK1p4OUczUFZ4a1pSSWlBMEg0aU5pSFdEdk05QVZlcUwiLCJtYWMiOiIwZTMwN2NkOWMzYTlmOThjODgwMjBiODAwNTE3YTc2YThkMGVkM2IwOTdjNzg3Y2RkZDlhMzMzMTRhZDRlZWVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-505370101 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505370101\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1554912450 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjB1bjVZOFlGWk1HUFFmZUNyZWtTSnc9PSIsInZhbHVlIjoiODg0Rk1EY3p5Y2RQOG9xR1UwbVRKT2VTT2grUXlxWDBTTTZRa1o4NFkxeThmZis0UTRZRzN2U3g2NzYyZUs2NVYvNk1zOWJNWEN2b0JhQ2o3K3hEdGNVNWl0Z0dNNUxpQmljSkhtd0Q3NUcxaXQzWk1CTE85NjdITFphTFM3NXAyZVUyQTRwVi9lbWV1Zi9KNG8wUWRwQ3RuenU3RCsxcEZXZEtlQzlXZkQxclZJc283cjRrNkswazJWelJOQzZIMXpuU1NEaUs5TWk0YndFMWpmRW16bXY5M0tndXkrRWpzK3EyL2kyMVlnVlM5Zjd5eFpTN05yVkR6aUF3RFdyK2ltN3hqVXRDbm94MUVVM0d5emtLTXlVVUg5dC9qelhSbzk0Q1BRMHVXTWo0VDY5aXpzSGhyM2R4dGUxdGlGalVkV0tEY2hkVzlicWRnaFRxSFkyNlpZamZCeldiOGd1cENwbDBia3J6TlhRN09vNHRqRDZyc0JETFBzdXhBWTJHZERnanJ6QzZ2N1YvOEpIWWtmdkQ1NVVCcTNvYzhvWVZncHF6UmQxZTViTWY0YlJEMEdSaVg0eHNaMGI3dFZNVFF6TmhlSXd5QS9TTEFDV0xYWThiQzNCVDRhMEQvUHFJc3c4YURIb05UZGlpSVhSanhSbEN2WjF3blhmMkp1Wm4iLCJtYWMiOiJjZmE5YTkzZTM3NjkyNmJiOTdmOGE0MDEwNGNmNjk4MDBkNTVhMzgyYWE5Mzg2NmFjNTM3OGQxMWQxZDBkYmMyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRpcnl3aFN4UHpLK3IveWhRSlRQVWc9PSIsInZhbHVlIjoiWFF5eTh1TDZxbVF1RjgrTzJvVGs0aDhFZDhhMDJuZ3BoSHhlbEdhMno1Tm82S0E1WGFsZkwzM2FSTzgzbElTdGlEa1k3RHFjU3lVdWsyTVdMTC9OdGN3dHF6Z25yb0JXZ00ycWVseVpUWHNyTUpZYjhIbHV2UklCOFhpZWhlMjZxcVMzd3RuMURuNGJCY3htZU1EbkdFOFBEODJENjREUG9xeFVGVU5xSkxIV0V2SFRyZGwwOXdacFB0U0hJVlRiMVpacW83eUluUWloeEZEVk9zNGhHQ2xiYWRYbEhrNGZzeEo0L3FHazRXNzVvSlhWMDlCRGNTMnVoTlFOOXd1aFZZY01UVml2V3o4eXBpMGRLckhBTCtHM0NPdUkvN0VieDQ4b1BOK3JlU3NVWGZ2ODNVS016RmwzQlRSMlpUS3pLM2kyaUxhK04yQnRLcTRDa0g0RnFBaEZnc2lHZVlGMEpwUG1mdVBTM1NSSTNCTllwWUpXb01sV0pNUVRjZDlrTWJXVmtNMkNFNGY5czNEWGNYSm4xOUFWSWF3WnBJckcyQm1VVlU1eWdtcThCaGF2dnVhV2hHUVdpcFZlaU1DOEsxc2Vya1R5bS9VYndtM0tYalF3MXRjNGRCOXk1UU4vSS9GVVgrZlIvSHJvRVh0Y3ozQWEyYUE4cXg2Q0grT3IiLCJtYWMiOiIxYjdiMDQ2OGE3ODgzMzIxOTUwMzU4MjRkN2U5Y2M4ZWYxZTUzMzQ5YzkyZTExNWZlZjJhNDJmOWY1ZTUyMmIxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjB1bjVZOFlGWk1HUFFmZUNyZWtTSnc9PSIsInZhbHVlIjoiODg0Rk1EY3p5Y2RQOG9xR1UwbVRKT2VTT2grUXlxWDBTTTZRa1o4NFkxeThmZis0UTRZRzN2U3g2NzYyZUs2NVYvNk1zOWJNWEN2b0JhQ2o3K3hEdGNVNWl0Z0dNNUxpQmljSkhtd0Q3NUcxaXQzWk1CTE85NjdITFphTFM3NXAyZVUyQTRwVi9lbWV1Zi9KNG8wUWRwQ3RuenU3RCsxcEZXZEtlQzlXZkQxclZJc283cjRrNkswazJWelJOQzZIMXpuU1NEaUs5TWk0YndFMWpmRW16bXY5M0tndXkrRWpzK3EyL2kyMVlnVlM5Zjd5eFpTN05yVkR6aUF3RFdyK2ltN3hqVXRDbm94MUVVM0d5emtLTXlVVUg5dC9qelhSbzk0Q1BRMHVXTWo0VDY5aXpzSGhyM2R4dGUxdGlGalVkV0tEY2hkVzlicWRnaFRxSFkyNlpZamZCeldiOGd1cENwbDBia3J6TlhRN09vNHRqRDZyc0JETFBzdXhBWTJHZERnanJ6QzZ2N1YvOEpIWWtmdkQ1NVVCcTNvYzhvWVZncHF6UmQxZTViTWY0YlJEMEdSaVg0eHNaMGI3dFZNVFF6TmhlSXd5QS9TTEFDV0xYWThiQzNCVDRhMEQvUHFJc3c4YURIb05UZGlpSVhSanhSbEN2WjF3blhmMkp1Wm4iLCJtYWMiOiJjZmE5YTkzZTM3NjkyNmJiOTdmOGE0MDEwNGNmNjk4MDBkNTVhMzgyYWE5Mzg2NmFjNTM3OGQxMWQxZDBkYmMyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRpcnl3aFN4UHpLK3IveWhRSlRQVWc9PSIsInZhbHVlIjoiWFF5eTh1TDZxbVF1RjgrTzJvVGs0aDhFZDhhMDJuZ3BoSHhlbEdhMno1Tm82S0E1WGFsZkwzM2FSTzgzbElTdGlEa1k3RHFjU3lVdWsyTVdMTC9OdGN3dHF6Z25yb0JXZ00ycWVseVpUWHNyTUpZYjhIbHV2UklCOFhpZWhlMjZxcVMzd3RuMURuNGJCY3htZU1EbkdFOFBEODJENjREUG9xeFVGVU5xSkxIV0V2SFRyZGwwOXdacFB0U0hJVlRiMVpacW83eUluUWloeEZEVk9zNGhHQ2xiYWRYbEhrNGZzeEo0L3FHazRXNzVvSlhWMDlCRGNTMnVoTlFOOXd1aFZZY01UVml2V3o4eXBpMGRLckhBTCtHM0NPdUkvN0VieDQ4b1BOK3JlU3NVWGZ2ODNVS016RmwzQlRSMlpUS3pLM2kyaUxhK04yQnRLcTRDa0g0RnFBaEZnc2lHZVlGMEpwUG1mdVBTM1NSSTNCTllwWUpXb01sV0pNUVRjZDlrTWJXVmtNMkNFNGY5czNEWGNYSm4xOUFWSWF3WnBJckcyQm1VVlU1eWdtcThCaGF2dnVhV2hHUVdpcFZlaU1DOEsxc2Vya1R5bS9VYndtM0tYalF3MXRjNGRCOXk1UU4vSS9GVVgrZlIvSHJvRVh0Y3ozQWEyYUE4cXg2Q0grT3IiLCJtYWMiOiIxYjdiMDQ2OGE3ODgzMzIxOTUwMzU4MjRkN2U5Y2M4ZWYxZTUzMzQ5YzkyZTExNWZlZjJhNDJmOWY1ZTUyMmIxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554912450\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1465/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}