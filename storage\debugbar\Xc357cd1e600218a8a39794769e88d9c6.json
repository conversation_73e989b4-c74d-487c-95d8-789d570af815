{"__meta": {"id": "Xc357cd1e600218a8a39794769e88d9c6", "datetime": "2025-06-28 11:23:36", "utime": **********.061962, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109815.685254, "end": **********.061974, "duration": 0.37671995162963867, "duration_str": "377ms", "measures": [{"label": "Booting", "start": 1751109815.685254, "relative_start": 0, "end": **********.017544, "relative_end": **********.017544, "duration": 0.3322899341583252, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.017552, "relative_start": 0.33229780197143555, "end": **********.061976, "relative_end": 1.9073486328125e-06, "duration": 0.04442405700683594, "duration_str": "44.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45439112, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032600000000000003, "accumulated_duration_str": "3.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.04441, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75.767}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.054436, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 75.767, "width_percent": 13.19}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.056556, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 88.957, "width_percent": 11.043}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1312987018 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1312987018\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1492290021 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1492290021\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-599364495 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599364495\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6ImZRZWpvNjJUZVR6K3lvVEtPK2NaNlE9PSIsInZhbHVlIjoiK2trYUtnTVc4SFpkZTVIV3dhRGJ3RC9pMG5iVnRrVVgzRSt1K0JjUWpDcTdVUlZVMC9BRlc2L0dRRUtJbTI5MGhLN0hBUlBRNWN5OFVMaTAxa2c2YzQ4RUFWNG5uWmZmMmpXbS81YlRMWGxNRHFNM2FSQlBvZTJON29WQTNpOG5mdmN5ZXZ4Qk4zem9BSm9PQnlqb0NQR2EzUkdLRzZURlFDbk1mZzZ6QTZXZHZkR1B0TVFKbHkwM2JmdlJvN1JZUTBuNnFwZk9ZWnFhaHFRRkZBc1pUbVl2U2lQb0dkSnFWZVllNmJQNXVwbnMrSWhNanl0bnBNUG9icEZRMzBicDNWQkF1Y3BFM1lTQ3VzRGlzdmdMVHN4SHdkelZweGw4STBINXV4YjVoTUpHUEFMZDNFU0lQc2xTMHBodDMwUkhycHdOcW0rYjJoZ25oaHVpWkR1bFRQTUNZR0k4d1NLSk0rMTNWNE5pNXd5RmhlbDE2bE0raENGM3pZMGd5T3UxcDVMQk01TWFHSUplQmkxWi9ZOEhMV1RJRWlCaldGK29qdWgyeFpFWUZjTTViWURtRUxDZWMxYUFySDVMTVF6R01mRzlxSE9EekVJQW5iYm4va1NHVitocXNhVjFEWFh1UkZ4ZmhIS1IwdTUzRGNrcmd2S3ZlQ2lmQWJScVhvYWQiLCJtYWMiOiJkOGExNjhkMDc3YTVkOGY0YjJiZDc1NDYxYzkzMzg1MjM3MTkzMWIwNGU0ZjNjMDVlMDVhNDYzZjUzMDhiOTAyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImFURnQ3Q1RsaWgxbDNZK2lmU3VHR2c9PSIsInZhbHVlIjoiU1RrcnI0VzVRWjFSeUlkTjZPc1BVOHcxcktraWpoVzgyQ1pzSFFCNHhBVWJsZXcrTW9pWnJKUDhQQlpGdUJ0bXNvUXA5by9ZQTRkaXc2a0hSM1NUZXZrNy9YL0lyUkIreDZONnAyRlRBOFlqRVlIakF3UTZBYUsvNGhnZkx0azExUGRKWDNVNlZGODNWUktUQ2hIa0hkdU1TOFhHckZlMnBaUmVkT3ZiYkEyZUZVbXVxaW5OdnFlZFpvcGU0QVdWbnVubHhHS2hGR0hyYURiS2NiTTNKa2s5Q1krMDRvN3c5Sjh2dUdNeXJ0MFlFRnVQdnpQYWV5U1RnSlJpQ083c2xEdHNQU0NobkcybVQwQTlwaVo2Wmx3b3RZZXVYOEF6M0NqclZJWnlZOVdjc25walgrSS84Z0N2UmQ1N2NyZmlmODFCVUs5endxV2FWai9xaE4xUEM1SWZHR3FJdnJBeEFBdHdFZHdvblhWbFJmL213R0ppZGJTSlh6Q1RZV1JvSit2YXZOQ2x2RTJjYkhqZjFTZG5GamhjazNPcW1CdStZSzJiTnNlMlB2YjRXWklWWDZSaHJuYXRBK2RSS1RBdmUrN0MzZitReHpKMFB2WXg3bm9JdnBYVzEyVmtmcW1UaUZCUzNiRE9WeTRXdm4zQzkrVnh0Rk56KzNRRjk4ZFQiLCJtYWMiOiJhYjljZjBhZDk4NDBiMjhlYTZkZDIxOWVhMjkyMTA3NmQ1MjdjODhhYjRmYmNkMTE2NTcxNzc5ZjU0YjU4ZDM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlDOWduYkF5ejZRVXJPWGpVbG0zOUE9PSIsInZhbHVlIjoiYm9VR0FnRE1ralp6dEFFM21udThFKy9YZG5MNXlTV05EeEpTVVU1T0t5Vkt5OGQ3dlpsTnExdEo2TVVFRzY2R1p1akgzZnJ4dG5mN3Q1WTlITGJ5TWtiYjg5aHQxVHMxVUtDZlNQY1BlVFhXWHJuZGdMTmk0cURyWkcrSzQvUEhDQmtLc2RmaGdYckdjMWxvTEJZQ0xmNFlXTTdBaEE2Sm1teFJFWGNXb1hVWlhUUHFNd0kyU21oS3dMQU9wRTYxcXpSRy9qNStTMEw3KytxYzFjQnl1b2JwWTZZSThEajVJVUh6N3lKY0x1blpjRWZLZXBlY0xXaHZ1SFFBWk5UeFo5VzIzc2VoOHArV2tEYmhmWWxnYjdSdXI1SWk4UllWY0N2NjlEZFUyanhlSXFwUmQ5MWVwSG5pUVFvRHVvYU5SYkwwN1orelRWR2Q1V2RHMmxnYzNBM0lYNWhjeGhtRUtkVGdCQ3F4YmhzaTd1WXEwRlNJdURKVHJISGRpbkRnVmFYcHpxckRFdlRsNUNaQllLSytLOWhHcWxkaERxWkxUaEU1RWdGa2J6TkU5QVd2d1REZUJ0MERiNm9SVG03NkR1VkNnZm9lMGpycUxWVURFcWNSZlZRa2RyZzExTXltU1FZemwxZVk0ZHdUN3VIcHN6aXNxUzNFT25wUEFDRzEiLCJtYWMiOiJmNDhjYzRhMjdjOGZiZjI0ODEzOGE0MTA5ZjYxYmI2NTc0NzUwN2Y0MzUyZWIwZjYwNTdkYTQzYWM2ZTY2ODU1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlF4alhkM09ObngzcnY4cmxtTUFPVkE9PSIsInZhbHVlIjoiQVVRSFFsVVNzblNva0JIRGl2bXp1dkljQzJIWWtrR0pIaGlsS2RJTkd5alhWbHpUZjRMSFRaR2NTK2NXOUIvejB3b3JOQWlxa2g3OTBjaUNzelZDallEKzZZbE1hSUdWVUd3U0FWRDB2SmNCWGJycmp3dm4vYUNxdjA0SERESzlOM3BhZ25NaGVITXVmT0NNNkNWSzcvdm1xdFl2NTExK3JlU2ZndStZSU1CdVlMak1KRnpPdmR4cmtOK2VzVENITTU0Q2ZxR3pKd2pwcFk4NG5YekRmNHEzTlhObWhxWW5Ka3p3ZTVHVk1idVVmbGc4TGVRMWE4OTg0ZloraTFQL0h6TFg5Wm40QWVWU2pPWko1MTlIdlhTdlN5K1NSNW94ejYvZTdIZ01Kb3JGbE9nWVNzNFpTTTIzN2t4RzZiZGpoRW5TZzhKK1dHMkQrOGFucG5oTXVoVEdXNldqZ2QwVTJvQWJQUGFlSVhoMndrbkhTeUxtenQ3UnFRM2NRdGJWOTkxUTRaYmh6eHo3MEJCcFliQ0hoN21vRWNvZlVEM0NWWkNqUlVIc2FYZnlMelp1Wnd3c1UvVWFUcEtnVjVKaFFncTZMSHF1VUY4YTF2aE8zV0FEQ2tyM29lNFhuZFdOdnhJVWtGdW16RFlxeTZyZVZyd1oweEVreXQ2VmlQK1oiLCJtYWMiOiI0MGFiZTZhNTBiMTI4M2ViYTdjNmE3ZmRmZGU5YmJlZGRhNzhmNjc0ODRjOWExY2E0MjJhYzI1NmUzMzU3YTY1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlDOWduYkF5ejZRVXJPWGpVbG0zOUE9PSIsInZhbHVlIjoiYm9VR0FnRE1ralp6dEFFM21udThFKy9YZG5MNXlTV05EeEpTVVU1T0t5Vkt5OGQ3dlpsTnExdEo2TVVFRzY2R1p1akgzZnJ4dG5mN3Q1WTlITGJ5TWtiYjg5aHQxVHMxVUtDZlNQY1BlVFhXWHJuZGdMTmk0cURyWkcrSzQvUEhDQmtLc2RmaGdYckdjMWxvTEJZQ0xmNFlXTTdBaEE2Sm1teFJFWGNXb1hVWlhUUHFNd0kyU21oS3dMQU9wRTYxcXpSRy9qNStTMEw3KytxYzFjQnl1b2JwWTZZSThEajVJVUh6N3lKY0x1blpjRWZLZXBlY0xXaHZ1SFFBWk5UeFo5VzIzc2VoOHArV2tEYmhmWWxnYjdSdXI1SWk4UllWY0N2NjlEZFUyanhlSXFwUmQ5MWVwSG5pUVFvRHVvYU5SYkwwN1orelRWR2Q1V2RHMmxnYzNBM0lYNWhjeGhtRUtkVGdCQ3F4YmhzaTd1WXEwRlNJdURKVHJISGRpbkRnVmFYcHpxckRFdlRsNUNaQllLSytLOWhHcWxkaERxWkxUaEU1RWdGa2J6TkU5QVd2d1REZUJ0MERiNm9SVG03NkR1VkNnZm9lMGpycUxWVURFcWNSZlZRa2RyZzExTXltU1FZemwxZVk0ZHdUN3VIcHN6aXNxUzNFT25wUEFDRzEiLCJtYWMiOiJmNDhjYzRhMjdjOGZiZjI0ODEzOGE0MTA5ZjYxYmI2NTc0NzUwN2Y0MzUyZWIwZjYwNTdkYTQzYWM2ZTY2ODU1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlF4alhkM09ObngzcnY4cmxtTUFPVkE9PSIsInZhbHVlIjoiQVVRSFFsVVNzblNva0JIRGl2bXp1dkljQzJIWWtrR0pIaGlsS2RJTkd5alhWbHpUZjRMSFRaR2NTK2NXOUIvejB3b3JOQWlxa2g3OTBjaUNzelZDallEKzZZbE1hSUdWVUd3U0FWRDB2SmNCWGJycmp3dm4vYUNxdjA0SERESzlOM3BhZ25NaGVITXVmT0NNNkNWSzcvdm1xdFl2NTExK3JlU2ZndStZSU1CdVlMak1KRnpPdmR4cmtOK2VzVENITTU0Q2ZxR3pKd2pwcFk4NG5YekRmNHEzTlhObWhxWW5Ka3p3ZTVHVk1idVVmbGc4TGVRMWE4OTg0ZloraTFQL0h6TFg5Wm40QWVWU2pPWko1MTlIdlhTdlN5K1NSNW94ejYvZTdIZ01Kb3JGbE9nWVNzNFpTTTIzN2t4RzZiZGpoRW5TZzhKK1dHMkQrOGFucG5oTXVoVEdXNldqZ2QwVTJvQWJQUGFlSVhoMndrbkhTeUxtenQ3UnFRM2NRdGJWOTkxUTRaYmh6eHo3MEJCcFliQ0hoN21vRWNvZlVEM0NWWkNqUlVIc2FYZnlMelp1Wnd3c1UvVWFUcEtnVjVKaFFncTZMSHF1VUY4YTF2aE8zV0FEQ2tyM29lNFhuZFdOdnhJVWtGdW16RFlxeTZyZVZyd1oweEVreXQ2VmlQK1oiLCJtYWMiOiI0MGFiZTZhNTBiMTI4M2ViYTdjNmE3ZmRmZGU5YmJlZGRhNzhmNjc0ODRjOWExY2E0MjJhYzI1NmUzMzU3YTY1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-975556915 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-975556915\", {\"maxDepth\":0})</script>\n"}}