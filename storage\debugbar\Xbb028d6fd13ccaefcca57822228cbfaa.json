{"__meta": {"id": "Xbb028d6fd13ccaefcca57822228cbfaa", "datetime": "2025-06-28 16:19:19", "utime": **********.913103, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.53014, "end": **********.913116, "duration": 0.38297605514526367, "duration_str": "383ms", "measures": [{"label": "Booting", "start": **********.53014, "relative_start": 0, "end": **********.860432, "relative_end": **********.860432, "duration": 0.3302919864654541, "duration_str": "330ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.860441, "relative_start": 0.33030104637145996, "end": **********.913117, "relative_end": 9.5367431640625e-07, "duration": 0.05267596244812012, "duration_str": "52.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46426048, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2441\" onclick=\"\">app/Http/Controllers/PosController.php:2441-2475</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0021799999999999996, "accumulated_duration_str": "2.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8945072, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.982}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9063401, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.982, "width_percent": 22.018}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1140750429 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1140750429\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1892428840 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127556646%7C37%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJmeDlrRWxNcWZOVzQ2MUUwTVNSNUE9PSIsInZhbHVlIjoicEsvQU1FS01vaEY3a3FFbFo5dDJIdlpLV0ZEVG4zb29Qc0YwU09LbHVPMHNMZGNqbFZPL2JWZWxqSWVrOU1NZlZRQi9wOENPcjBLNlExZ2Rzd1dabzdETDVSK25LbmJKZEQwcmRjZVdjaUxjcnZLQzFCMkNyRjRFTi92cDFYSmNmUXhjYmRQK1Q2Z0R3cUlLN2xmcGtKRFlaQXBNTzN2c25xMlJTa1B1QVpIWWxwRGh5dU1oYUdVR01CRkFsbWk4Tm9nWFFubGFpTk5mV1dUT3pnek1RNlhmN2ExQzBMWWR5SSsxWmJMZll3bUJJeUcwZFUvWlNQcTlQQWJKeDJBR1VsYnMwTGc3V2tCdk5FRGsxa1FRTUhkZHJFQW9kS0wwUWlHajh5cXJtUWx1c0hXNDFJQ3dQbVQ5b3NESS9CbHl3MzNHMkNrZHczcmpQVm8yVWFrME43RjU0NWp6V0ZrNGlvUTk0N0ZaYWJvOE0wVEpBVmR0Q1F3UnVxVFR6SmZtRlIwdFBVVEk5Q1dHS0ZyeWs5eFI3bkNsUGc4SUxHOTh2dlcxTzJpeng2QzF1bEduTFhPU3FYOWhITndGODJqWVJscXVkY3NmLzZQdUlwNWszNWRJNUdCNXNNTVRTZ0xIUWQxVXFuNDczOFdHVlltZTR1RXNrbGV3TEpoakdPcjkiLCJtYWMiOiI0OTRlMjA2NDM5OTJlYTQ2ZmUzNjk3MjNjYzhhOWIwNzNkM2NjMmNkOGZkMTUyNDY0NGRlNWFkM2U5MTkwZTA2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJZaitUMGlQbzBsaGxON1FrWnNVMVE9PSIsInZhbHVlIjoiWFdsK2FIdUZwTU96YnFBMEJseXBXTnZtS3czZDFsRXpBQThFbWtuM3hPSU5IMjZkRGhtSkwxT2VqTHhQbGxicVJydFFubE1qc0lXNEIyaTdvZkRJaVAvNG4xTDEySVArSEhoUnR3V09PSTZYSGpBRytXV0ExckRIMVNMNmh6WVo0aHE1bUVVU2JpOXN2eHBCK1J0RGw3Rkg3TTVYaUlkcTZJbUpGVTFnMi9TQTU4Y0VnTmdVT2MyQ2pOQVFPcTRGZmJjbHhScHpxbVNMYWkxclBFZzFXL29NZUpHcDRWS1hsaVRjZ0M3Z1FOL2cwZmM4WjhTaUZLdlNqUTdxc2lQZHhibEVmUDRDTjJGbzljZTNOTzlqMm9tZlFZK1FOZk93VGpPVjVXRll1Y2tMU255T0M2dlBGMEtTRXh5TytqNU9TOGNKN0FrbHlLV0tPWUZ2RERnQnIzdG90dVRmYXFiaG9SYzk4UjQ0cmM0ZlZJQWszMW02NEtidmVzckR3NTNRZzZxdVhYbjZMTG9DZDd0SGo0T0NPTC80bE0xSzdUQ3Q2SEcremdVcE1SMGJrVWdJS0hGQXBMUjdUckZhbkxRaW5RUmlEWkJSV0QrSEpPeENlS0MzRnQyb2kwcGMrdzVteXovVjB1QmZqTk9DSE5qOVNOcXZpT2J5em1rVjFUYVUiLCJtYWMiOiJkZTIzN2Y4OTFiNDU3ZTcyMWNjNTBjNDAwYmJjNjM5MmZhYWMxZWE5ODUyNTVhODIyYmE1YzEzZjc5ODczZDBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1892428840\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1615231988 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615231988\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1786614846 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBaNFBQY2E4WHV5ZC8wdlNsWXluWkE9PSIsInZhbHVlIjoiMlNMeWNlQmlSNlFaWGJ4NGthMG5pRXhBVk1UNStpU2hKMkpQYTdCNXN3eUllK3pQVzJNQWtsRElMeE53cmNGRDBMN1VpSlVNamZpK0pwSnlLeW8zSVR3VURBeHJRclQvMjlObmQwaFQxeGpsRXJ0Si91TTFjQ3NVVzh5Y3VaVHlWWmZYNUozSys5V2czL0V5ZFo1U2hSWVAzN2hTWENzRkM4czlzUUZhWVFXM2gwMWljUGR5WVpaNUtISU52UmtxTmVHakgzOURYYkVRWDJNSFcyUGxPdnRhcDFTeHhWb0toMVFJd1pURGIrMWtSSklkNFFQcHJ0ZTEwTDRPblhhMnVpSGFoSGc0TE05dTNlZ25KMC90ZVI4dHhZRnpJb255Y09lTVRZM1VSRTd6b1A3SDZmMmUvc3JPa21OV1FKUVlkNnhXLzY1MlpqZGN6bWh2SFdYVUFjQWdhR0V5WUxZMU0rRFJhL1JldmNKbE81N240aFg5MnZIdkxnZmhpcnBaTlpzMEZhbGNFaW52L01EV0p5RUZ4WGVBSmJZbHFyTEtSczVKaDkyWS9zbElsVFNHYTd4aEF1TzRZR1VhOFFHNjBISWt6STN6dHNyL0RrY252N1k5ekxyWFVWL21lK0xydElWTUduNkljU1g3eXBzcUpEZE9Pc3k4ZW96N21NK1IiLCJtYWMiOiI4N2RiMDFiMDE3ZmM4OGEwYThkNGMzMDliYzIwM2I2NDA1ZWM1NTMwY2ZkNGFmYzNiNDE4MDJmYWY5YmE0NTM3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVwYjdSYXhoVTRyUHk3MU1tZ0JZZnc9PSIsInZhbHVlIjoicUdCc21ReXAxM29FV0VFN2NSVStmRzhxM3NYWmFUNUlKTEIzLzhneWl4UHBQbllDTmlYbTZxRllQY0NKWEFBdjBvZk9yYmVTWnhkMDFvckxuMTB3ZkhWd2RsSGRqQWxtYXp6U20zY2RyT3RlZmxSWERnSnZTRCtETXVEY0ovWjNacFZEZnRTcHpOM2RoMzg2QVJKcGxqdjA5RGkyNGdkZi9XVEQwclk3SStsU1VkU3Fjd3dYTkIrb0ZvY3pFOWxNZkltQkhNUFE1WUFidlhKTFk1WHdVNVdzMmpqNmtrcTdMRnlLaGpMWmFrUTNVSkxGRlcybURJTUZQd0dneHlyMERudnNmVSswZENsY3VxY1Q2VVR2WEQ3UHNsS0t5akFkZDJDMVNMUFk3UVNXU25PbUhZNW00S0Zqb1duQmtyTis3OTBiSkJ3RlRiWVFJenVickNQMTlFZUxCTzZIbnFUcTE1bklOV0pWanJWRFFlVEs0SWtDcHVjRVV3d3JjL1NOcENmd0JCZ1Zyb3RKd0ZlbitqcnRUV0o3YWFXT3V5S1R5VE9SWkMvTE1XN0Y5T25GVng4UkRsYXFpSDg5c25LTU85dnU5MERYZ2ZNWXhXamxIdHNxbGJjMjh0TGVualZ6RThLYmFHVmZYbi92YlBFUjhvUXE0UW52WjNUVUFlOVEiLCJtYWMiOiJjMmVhMmFlNDliMWVkOTMzZWIyNDZmMDIyZGU1MjM0OTNmNDQzOWNmY2VlNzc2NDYwODU2M2RmYzViMzJiNmVkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBaNFBQY2E4WHV5ZC8wdlNsWXluWkE9PSIsInZhbHVlIjoiMlNMeWNlQmlSNlFaWGJ4NGthMG5pRXhBVk1UNStpU2hKMkpQYTdCNXN3eUllK3pQVzJNQWtsRElMeE53cmNGRDBMN1VpSlVNamZpK0pwSnlLeW8zSVR3VURBeHJRclQvMjlObmQwaFQxeGpsRXJ0Si91TTFjQ3NVVzh5Y3VaVHlWWmZYNUozSys5V2czL0V5ZFo1U2hSWVAzN2hTWENzRkM4czlzUUZhWVFXM2gwMWljUGR5WVpaNUtISU52UmtxTmVHakgzOURYYkVRWDJNSFcyUGxPdnRhcDFTeHhWb0toMVFJd1pURGIrMWtSSklkNFFQcHJ0ZTEwTDRPblhhMnVpSGFoSGc0TE05dTNlZ25KMC90ZVI4dHhZRnpJb255Y09lTVRZM1VSRTd6b1A3SDZmMmUvc3JPa21OV1FKUVlkNnhXLzY1MlpqZGN6bWh2SFdYVUFjQWdhR0V5WUxZMU0rRFJhL1JldmNKbE81N240aFg5MnZIdkxnZmhpcnBaTlpzMEZhbGNFaW52L01EV0p5RUZ4WGVBSmJZbHFyTEtSczVKaDkyWS9zbElsVFNHYTd4aEF1TzRZR1VhOFFHNjBISWt6STN6dHNyL0RrY252N1k5ekxyWFVWL21lK0xydElWTUduNkljU1g3eXBzcUpEZE9Pc3k4ZW96N21NK1IiLCJtYWMiOiI4N2RiMDFiMDE3ZmM4OGEwYThkNGMzMDliYzIwM2I2NDA1ZWM1NTMwY2ZkNGFmYzNiNDE4MDJmYWY5YmE0NTM3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVwYjdSYXhoVTRyUHk3MU1tZ0JZZnc9PSIsInZhbHVlIjoicUdCc21ReXAxM29FV0VFN2NSVStmRzhxM3NYWmFUNUlKTEIzLzhneWl4UHBQbllDTmlYbTZxRllQY0NKWEFBdjBvZk9yYmVTWnhkMDFvckxuMTB3ZkhWd2RsSGRqQWxtYXp6U20zY2RyT3RlZmxSWERnSnZTRCtETXVEY0ovWjNacFZEZnRTcHpOM2RoMzg2QVJKcGxqdjA5RGkyNGdkZi9XVEQwclk3SStsU1VkU3Fjd3dYTkIrb0ZvY3pFOWxNZkltQkhNUFE1WUFidlhKTFk1WHdVNVdzMmpqNmtrcTdMRnlLaGpMWmFrUTNVSkxGRlcybURJTUZQd0dneHlyMERudnNmVSswZENsY3VxY1Q2VVR2WEQ3UHNsS0t5akFkZDJDMVNMUFk3UVNXU25PbUhZNW00S0Zqb1duQmtyTis3OTBiSkJ3RlRiWVFJenVickNQMTlFZUxCTzZIbnFUcTE1bklOV0pWanJWRFFlVEs0SWtDcHVjRVV3d3JjL1NOcENmd0JCZ1Zyb3RKd0ZlbitqcnRUV0o3YWFXT3V5S1R5VE9SWkMvTE1XN0Y5T25GVng4UkRsYXFpSDg5c25LTU85dnU5MERYZ2ZNWXhXamxIdHNxbGJjMjh0TGVualZ6RThLYmFHVmZYbi92YlBFUjhvUXE0UW52WjNUVUFlOVEiLCJtYWMiOiJjMmVhMmFlNDliMWVkOTMzZWIyNDZmMDIyZGU1MjM0OTNmNDQzOWNmY2VlNzc2NDYwODU2M2RmYzViMzJiNmVkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1786614846\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1151512819 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151512819\", {\"maxDepth\":0})</script>\n"}}