{"__meta": {"id": "Xa75b3cf676ce9efc25a9d98aad006d01", "datetime": "2025-06-28 16:47:14", "utime": **********.537554, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.113463, "end": **********.537571, "duration": 0.42410802841186523, "duration_str": "424ms", "measures": [{"label": "Booting", "start": **********.113463, "relative_start": 0, "end": **********.473837, "relative_end": **********.473837, "duration": 0.36037397384643555, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.473845, "relative_start": 0.360382080078125, "end": **********.537573, "relative_end": 2.1457672119140625e-06, "duration": 0.06372809410095215, "duration_str": "63.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722696, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.012459999999999999, "accumulated_duration_str": "12.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4996781, "duration": 0.011519999999999999, "duration_str": "11.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.456}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.519242, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.456, "width_percent": 2.97}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.524297, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.425, "width_percent": 4.575}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1140006306 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140006306\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1548888910 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; laravel_session=eyJpdiI6IlVGN2szMHhZTEFYTEo0TDJwZlRBT3c9PSIsInZhbHVlIjoieHRCWHVRRzBZOHhuNVU2MytRamlRdXFzUm5ZMFJxQWpadU8zcTg4RmNGWDNva0hxN3djbWM3d3I0b3B6b2FueWZhZnZKUnR5QnVjMzA1SkJVMGF4VkFhaFlzYWF6cGFXVjdGS0I0TE1aUEdrMVYxTVAybzlTN3hQVml3ZEhnN1B1eUtEQUJ2TDU2aUYrTlFwbXQ5WmViaGd4elJoeTFFYTkwNGRVdWYrTXFseCt2MHE3T3doZndncy83S1R6bnpRNWNXcTgwcFNqK3VUSytiSFhZN2l4dlJRbGIrbStER3NzZ0Y2OG9tdjhIdTV6OFdydDExTUVFd1IrZlVLdlV4Qmk0UmZUcmhpUENuRUhaUVAwRzdkV2M3RkxCc0lUcmhoYmM5MnRDZ0IrRlZOa2M1MkgweGE2aXFGMFA2THRzY3hsMmVEYm9CckdMUDdLUXA0eFU0VTRQOCtLUDVTUXBZU2hGRVJuQ2ppa09Qa25tT0wzVjdPTXM1R3lYTG9hVjM0TzZxdWhQQnJIMVNReXBFRWt4MjJHWWNtSUZwcE1VT3VqOStqWmhPc1R5Y09SQ2ZEY1ZUYmEyYjZLRUYvNUVOM3VqMEtRN291NTZxTkVpUDZ4R0R1ZS9hd29PaEJrYkNMZUFmN0ZsTnBId1hjTTkwYStnaU5NdjJKS3JEY1dXM3MiLCJtYWMiOiI1MTc1OWQyOWY0NjIzZTZjMGQ0MzJiMTc2YjQ0ZmJmYWVkMTBiMDZiYWQ5NjhiMmZmNjJiNTBmYzRiZTM3MDFlIiwidGFnIjoiIn0%3D; _clsk=1mcm0n0%7C1751129231224%7C68%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllWUnFYVHFGakJMRExhbno1OVNUeVE9PSIsInZhbHVlIjoiWDFZY2N1cEFUU1dVVFZqQS9ZUVhzaHJ0QVpCam9mVVdUOExWb2ZGcUhRZkROOXRYemJ5ZjVBUyttc0w0ZmFoUzZUY2hQenJxWFUyakI3cWh2ckZSbmJsbHdvNFZqZStKTm56cXA0cWhnSjVWQVpCMTRubjdMaGVsYTZCT0R0SlBhSmdRbFpQbFIyUzhlbmVHcFhFQk5HUXlnUUszTU8zTGZTa3NGWnZIYWVhS2g4OE92c25USEU2eWp2ZUVmdWZTVnEwa2lrNUlicVFJc091ZHBLdytvTWYyTWNMKzF4MVZpSzVoU0NFY3ZIRzhNd2hGSC9MRVo3UlE3NHBxTHM1YVY2QXBZTWwvSXQ5TEFVNjN3WDAwQ29vYWVoNzZPWVlidHd0UHlZYlN6SVFPZ290aWhkdHNtUmNnZVI5NitCNmkySzZzR1pJOHEvVzgwWVVlTXJPWkhJMDBUdG80TFlYZkR0Uk1obE1CMjNqbnV1dFRYMzV6RysxUmRLSEc5OG43M1Fyb0didzIwc2JUMEQvczJ4UzJxN2NVQzlSSDZtVzk4dndUNjJyL3RjRWRCS3dvOFJZbmJRQ3BRYzAwSW1nOUMvdUVxMy9OZDlMZHowVitUaThjbkx3Rnl0YlRvejh2amVhRlRZRHlBcU5HNXkxSjh3ay9nMXZZblJyV1pNR3IiLCJtYWMiOiIyYjY4NzZjYjkxNGRhNTkzOGJkYmNmODNjOWNlMWYyOTMxYmYzY2Y3NmQ0NzVlOTY0ZTdlOWYxMTRmYjBiYzAwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpJa1VOcDBKTk4wMXVVVWc3aEhRaVE9PSIsInZhbHVlIjoiSzZkcGRFaG5HTCt6eUt4eWFLVTJBS2RQUG9UMThRU0tQbktLMXovd0xvOUU2QnBibUkzdkN4a0s4YlZLUWpQNENBRE16cS9rUlk2QlBZVE1JaDZhRndoNjVtZjhLS28vcDFZN2JhTUxMeThjbHd3MmZMbkhnTU94RkM0ZFF1WEQ0NDdoS0I0aWY0UitFUkYwUlM4b1d6RmpTQXNCenY1UldNNDcwcEFDdEdLUzd4bU1vcUMvY01nNm8weXFLTnBWTWVaT2I4ME5UM2hMYnV1U2dxTCtSV0hJNzhmc1RIeCtCU1N3OEd1b1NIcXZEUm83RW1RY3hBbXVlV2dSYVBBQXJUSnFoSk1RQytPNkRIUXNMeWdkdWJ1QzFZN2ptZmFZOWJsblE1NmhsT3R6dWVsMnZXSU1yM3ptVjZucjVJSVRPaEJ6STFlT3JOR3ZodEZxajByOGxiTXB1R0hTUTVHS3F5cVhNZFBSMVNQQlVQbXJDVkpsbDErWVdVNmtrSFUrNU9nTzNENVJaelBCM3ZZTnFsRGJXNUorMW83eFNVQk5GemZ2M1dUQmdaQWcwbWNNT243UFJKTlkvdzhLVUJxcFhLQjFEWHN6UmJRcDNGa1J3c2N2N3psVHBFVmpiVjhSSE9sQ1Y4Y0h2cEhzbS83dHNnUlpVeElDVUtoY3MvYkwiLCJtYWMiOiI0YjI0OWU3YjNiNDE4Njg2MThkZjk4ZmI2ZjY5YTUxZGUwZDg0NjI4ZTNkNmFhMDQyYTYzNDMxMDkzMzE5NmUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1548888910\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1150302208 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yEzaZJ27Y4UPdgz1Ok84k6McKnZQhYQlDiwGhQ8c</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1150302208\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:47:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJJbmd1b3dDTUhsMmRIZ2NNcTZhd0E9PSIsInZhbHVlIjoiUEZEN21QYk5YemF2NUFjS3VINVRXcmt6dkIyTzkrRjhhcUpNQ05UdVBXYWhpcWQ1YWtpTWdCb0dVVXVPWURZZTR3RTBtSU56WFdOaER1eE9UK1NnRGxOM0ljL0thZXEzZ3FFNUpKZ2w5cklWNmcxQmNpVTFjcEFwUFRFMGEwOUxqZnA1TVFrRkpQL3BlMnRqeUNYT1lFUUdWa1pHMWJNaTlXOTY2V1hYUEZCNjJ3OHRwK2VKVFIwQ2JvZE5zYXVJcjNvM3ZXM2FrME9EbXd0YUNkTlRnekxibHN1SWFKU1NFUVlCVEFqeUo2M05UUlJTQ0svcnI0RTRvMk5udVhnT2ZoMjZ6NERnMWpxQ2ZraWJnTllwNGVLM0owZXNsMnJjaHdRRlpSWVhMRmM2YXNSK3pEQVlJVkJPekJGbHhpMUl4czhqbnRmQUtQVnlmZFkwOGdyc2Q1aGJwMUVQR1k4MVRWTVVaQkNuaEEzbThpaXBGckw1UW0xSzc2c0tIWExPb3BaSnhYeXo3UkFSUVJpQ284MDdCV2tIeUFnOE8rTyt1ODIwSlRYQXpjQVJuWXhIZGNFWTVxcjJTc3JsK0lGTWpTMjdzbi9CRFRCbDM3ZHQ2N1RBWnBQRlE0UEFlb01xK2I0TG5FQmZJYVJ6a0xmVWJ2cit3d2dxaGNwZ2UwTEIiLCJtYWMiOiI4ZTYwNTAxODQwOTk1N2E2OWRiMTJmMmVmN2UxNDZmMGUzMmM2YmQwZDRlNDNkNTM4ZTZiYjNkMzljM2MzNDdiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:47:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxvSUhrZFZqSHZCT0ZIc2ZVVk9paUE9PSIsInZhbHVlIjoiUmhQQVNmUUhhb0dsbXpEMExha3FtaEpTcDI3b3pkdk52c04zNmlOdFhBTnJBQmR2dUcyc0NsNWI3R2hLSG9xRHVsK21lNFgrenJ4VU1JMWNWRXArb1M1bUVHWTNWdUZlOHVDcTFRQzV1UFgwaktxN0hEUnFOekY1eE0rS3JOZ0JoS0hLdGtJazM3cHdQeW0zSFpnVFNVNjM0V3h6UVVQalFKamdRam5JQ0lVclRUSzFGd2hpRFFiZncxMDh0dU5abEtoZ3JIa2xEUkM0bEQwVWJRT3Nyc2VmbVJtL1B1bjhSZlhJbjh3eGwxYWk2dUZnaUQxaU1BZXh3L2RQZWpKdTRYMEVKZDBRVDQrdWc4YmlXM2svUFhkdXJneEZTZTBMZVppMHplMU90eUZYRWhlM3FyOGhVek12YmVaV2MwNUVsbnppQ0hJNGdpWW5lMSt6cGNxdVVEbExoSjIrRlBIcHZ1WUx2cG1ZRi85SHdVSW1pcDc3Z0tlME5mM1ZsMmhsckFveHRkN3VMc0t1UHU5ekZMeXVzYnZiNTh2ZU9ObGc0bXEyblBjaGdrSHpBUXBTVGc0aXBKZWJ0bnE2N3NPMFFYN0Z6OHFXZ3ZSeDhOTnVacVdZODdLa2RBeU5hZDdoL3FBck9pTDVHTmk0WXJrYzV1OHNpUkgwQnpIRUVaNXYiLCJtYWMiOiI4MWRkMTVmNmE4MWM2ZWJmOGNlNTlhYmIwZjZiNTBmYTk3NzE1Nzc0MDVlNTNiMGI0YjFlZjA3MDAyNjE4MGQzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:47:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJJbmd1b3dDTUhsMmRIZ2NNcTZhd0E9PSIsInZhbHVlIjoiUEZEN21QYk5YemF2NUFjS3VINVRXcmt6dkIyTzkrRjhhcUpNQ05UdVBXYWhpcWQ1YWtpTWdCb0dVVXVPWURZZTR3RTBtSU56WFdOaER1eE9UK1NnRGxOM0ljL0thZXEzZ3FFNUpKZ2w5cklWNmcxQmNpVTFjcEFwUFRFMGEwOUxqZnA1TVFrRkpQL3BlMnRqeUNYT1lFUUdWa1pHMWJNaTlXOTY2V1hYUEZCNjJ3OHRwK2VKVFIwQ2JvZE5zYXVJcjNvM3ZXM2FrME9EbXd0YUNkTlRnekxibHN1SWFKU1NFUVlCVEFqeUo2M05UUlJTQ0svcnI0RTRvMk5udVhnT2ZoMjZ6NERnMWpxQ2ZraWJnTllwNGVLM0owZXNsMnJjaHdRRlpSWVhMRmM2YXNSK3pEQVlJVkJPekJGbHhpMUl4czhqbnRmQUtQVnlmZFkwOGdyc2Q1aGJwMUVQR1k4MVRWTVVaQkNuaEEzbThpaXBGckw1UW0xSzc2c0tIWExPb3BaSnhYeXo3UkFSUVJpQ284MDdCV2tIeUFnOE8rTyt1ODIwSlRYQXpjQVJuWXhIZGNFWTVxcjJTc3JsK0lGTWpTMjdzbi9CRFRCbDM3ZHQ2N1RBWnBQRlE0UEFlb01xK2I0TG5FQmZJYVJ6a0xmVWJ2cit3d2dxaGNwZ2UwTEIiLCJtYWMiOiI4ZTYwNTAxODQwOTk1N2E2OWRiMTJmMmVmN2UxNDZmMGUzMmM2YmQwZDRlNDNkNTM4ZTZiYjNkMzljM2MzNDdiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:47:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxvSUhrZFZqSHZCT0ZIc2ZVVk9paUE9PSIsInZhbHVlIjoiUmhQQVNmUUhhb0dsbXpEMExha3FtaEpTcDI3b3pkdk52c04zNmlOdFhBTnJBQmR2dUcyc0NsNWI3R2hLSG9xRHVsK21lNFgrenJ4VU1JMWNWRXArb1M1bUVHWTNWdUZlOHVDcTFRQzV1UFgwaktxN0hEUnFOekY1eE0rS3JOZ0JoS0hLdGtJazM3cHdQeW0zSFpnVFNVNjM0V3h6UVVQalFKamdRam5JQ0lVclRUSzFGd2hpRFFiZncxMDh0dU5abEtoZ3JIa2xEUkM0bEQwVWJRT3Nyc2VmbVJtL1B1bjhSZlhJbjh3eGwxYWk2dUZnaUQxaU1BZXh3L2RQZWpKdTRYMEVKZDBRVDQrdWc4YmlXM2svUFhkdXJneEZTZTBMZVppMHplMU90eUZYRWhlM3FyOGhVek12YmVaV2MwNUVsbnppQ0hJNGdpWW5lMSt6cGNxdVVEbExoSjIrRlBIcHZ1WUx2cG1ZRi85SHdVSW1pcDc3Z0tlME5mM1ZsMmhsckFveHRkN3VMc0t1UHU5ekZMeXVzYnZiNTh2ZU9ObGc0bXEyblBjaGdrSHpBUXBTVGc0aXBKZWJ0bnE2N3NPMFFYN0Z6OHFXZ3ZSeDhOTnVacVdZODdLa2RBeU5hZDdoL3FBck9pTDVHTmk0WXJrYzV1OHNpUkgwQnpIRUVaNXYiLCJtYWMiOiI4MWRkMTVmNmE4MWM2ZWJmOGNlNTlhYmIwZjZiNTBmYTk3NzE1Nzc0MDVlNTNiMGI0YjFlZjA3MDAyNjE4MGQzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:47:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}