{"__meta": {"id": "Xf6be1970ad380add8838e235e75825e9", "datetime": "2025-06-28 00:36:50", "utime": **********.387108, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751071009.985076, "end": **********.387126, "duration": 0.4020500183105469, "duration_str": "402ms", "measures": [{"label": "Booting", "start": 1751071009.985076, "relative_start": 0, "end": **********.330936, "relative_end": **********.330936, "duration": 0.34586000442504883, "duration_str": "346ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.330946, "relative_start": 0.3458700180053711, "end": **********.387128, "relative_end": 2.1457672119140625e-06, "duration": 0.056182146072387695, "duration_str": "56.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45598816, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00243, "accumulated_duration_str": "2.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.35805, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.782}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3675082, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.782, "width_percent": 18.519}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.372583, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.3, "width_percent": 10.7}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IkRXc05ObTdrZ0E1ZkpPL0RySjBOanc9PSIsInZhbHVlIjoibFM1c0RYci9Yc3lqQ0VINW12V2RLZz09IiwibWFjIjoiYjUyYjNlYzYwYmU5MjNkOWQ0Mzg5MzgxNmE0YmY2YzMwM2Q5Nzk0ZjQyZWQ5OTg3MmRkYmVkMjU2OWVmNzk3NSIsInRhZyI6IiJ9\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-973855082 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-973855082\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2083658412 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2083658412\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1257782077 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1257782077\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-687288287 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkRXc05ObTdrZ0E1ZkpPL0RySjBOanc9PSIsInZhbHVlIjoibFM1c0RYci9Yc3lqQ0VINW12V2RLZz09IiwibWFjIjoiYjUyYjNlYzYwYmU5MjNkOWQ0Mzg5MzgxNmE0YmY2YzMwM2Q5Nzk0ZjQyZWQ5OTg3MmRkYmVkMjU2OWVmNzk3NSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2817 characters\">laravel_session=eyJpdiI6IjE5YmxhMUw3bXJQWWh3R0habDVwcFE9PSIsInZhbHVlIjoiTkZUZHViY3FpMFMvWUgzVm1OV0JwUHZoMVU1WjA5ck5UbEh4NjBRemY0ejlwWVNadXBvOWxTWVFjQzJMNGxNSTc5NzJwa1dxQ2hqL2VlQUZvMUs4YnV3Z2RhaExoU0p1RE5pdlhQQ1dzN0RpS0xnTWxrcmEvVFZUZERCSnFDQlhEdG03UkROakJlb2huQ24xSWlPcTA0QnpwdnNZaEl5T2JnWkpNMHNSVGpUeGZpWHJNcDBBWVFmaHE4Z0dkYmFlTm5VRWR4YWlaOHNZa2hPaDh2RGdndHB2MVJLZEFZMUU4Nmttek5WOEhIOExCS0p2S3h1Mm83MFRQc1U4NHMzN2lsY1J6ZnNLdVBhOXVCbktKdkNNdUQrcGJHZm9sUDkyRWhHVW90U0twWWw3Q3d1VzlyYzgxWjY2U2MxWWdnOXhaUEJkK0k5dFU1dWEvaDNXNmZtMkY2WUJKdVZSNnlFYVF0Y0JtL3Fac0tRZ3dyWDFwVXNSdmNhNzIyVjBiSHpQbDM1OXBiL1R6WndGT25IMnFYV3ZBNkdsNjB1SU54NXRIUWJBdTM1eDBiditjVzlWcW1DQlVTMkgzSmw3S0xYdDJmRVI4MXNEaENKc1dTbFRMR1pPWUVZc1crMkY4QnNxMDl2eEpvclhUb3hhMjZqYnFqaGVGTEdOWWxSZzBXNUUiLCJtYWMiOiJhMmVhNTI4OTM2NDBkYzJlNTU0MzRmNWUxYTg5YWQ0OGIxNGMyMmZhYzM5MmYzYzllZGEyNWMxZTVjYzdhNWUyIiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=zkpa4v%7C2%7Cfx5%7C0%7C2004; _clsk=9vvwq3%7C1751071005797%7C29%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5LM2E3ZldscTdNR2w2YW5lRFovd1E9PSIsInZhbHVlIjoiZTFsZ1hCWXhqZ1NTM011ZmcyY1d4a3lqS2x0UmtoVitaOUZFNkY3U3FtblgzOW1zS2NoYzNlKzBpSXhnOVY5MHhXOWc3cEM1RW4vQkFCSTgxZzBmNGpSSnJ3Vm9DQklvdGwyZ2pINUtvVlk5VXRNMVpEcm1sSjNscDFxZDM4YzVkWW83U29tdHpnci9YVG0yNHREdVl3ZmE4UEN6YXFLbFNHdDRVcnpzMml4SnZDWVpuWExUWDcwUmFoRExET3kzSVlvc2t0WFNUdFM2VGhFTndDN1VrbkV3OHBwVE9PRjRvWkM4UXhoVms1OCtPWDlHaDhMVXJUbzRNdmRkMFY3TkNtZDY5a0ZIMHJ0RnNSTXIvZ3FCbkZLaWk4MHdWd2tWeDlTYy9KcDFKSWlsWUJucHlHaWtRcW8yVXVxU3BwUGRENThNV2t5Y2hxL1FWUlh5b0VoNHFQb0ltd3B4TFNMUXZLRFF0VWt0cWdOY0ZJNUtKeDE3cFZjT2V2Wlk5OTNhUyt4bU5IL3lEWnNBTkp0N1JTMVUzc3I5QWI3TWtyMno5Tk41TFhTeW9nVGxOMjgvOFBkQklqNU9Da2ZTbHdRdVF0R2lpdFJNSVA3eXErdjdMNnhWZTZVamRueHgxY0tCTUJEdWxTc29lYy9vRjFic1FUWmM4WTlUbDFwcEhBWTgiLCJtYWMiOiJjYmQxNTU2MzI0Y2M1OGM2OWQ5OWVhMmE1YTJlYTI2ZjY5Y2Q3OTMyMzVkY2RlZTAzZTY5YWMwZDZkODgwZDJlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjA5RDhzcWJRZHZFNlR4ek13ODAxNFE9PSIsInZhbHVlIjoiV2Z5VGJYcytZMTB1eG9FTndlNHZ3U2dPVkNaOFZDRnVzWFcvRUl4MTNqTzJtOGJzclFvUWdKQ1NlV2QraVIrT0IzajYxVHVFZ1RyMHU3QkdSMUtoRFNXcHR3N0dIbnlyNUozRityWi9iMXBTWXdkK2MvcHg3aG5TR0Rlck5SUUdFTkRWaFV0S211V0QxeURQa1RKUzR4cURTQjdNVmdmOUdISW1UVXA0aFpsL3p5VzlweUdBcWdPUFBobmlLbU5DdUYybzJxSTd6a3hMSE9QZFRSTXdmUU56ekJYWEZjMXZZQ3dVdU5HT1BUYU0yUWwzYzRwUzZNVWNqTVNibXpBeWxhdVpQQml4QWFTZGRFL2lwaXZqcytIV0Q4QVVFOHdhazR6bWNiOUhKWWp5YXVaZE9GUU1uWVhoQ3IzMVQzM1B6OTIwY1pPUmhaS3VOZEdjUW1uRWR3elUzUDJ1WU5lV0FZSkh6NER0ZEtMb1p3WlVFL0o3MWR1cDl5Sk5Wa2RzRGNLelFXSFgrN2lHVmtka1dCdzJzcWFiTEVHeUF3bmI5S05USE4wMUcwdCtaZ29ibjJ4UWtNeVFaem1zN3RLbTR3L1F5R1JGRkdXMXMyNmhrQTk0LzExM2dydFBLcVAvUHRMS01la2xEREM2ZFFZS0NVTEI3K0hWSUN2Vm5pU3AiLCJtYWMiOiJkNmJkZTA4NDJjYzVmMTkzOTJlMGRmMjQ3Y2ExN2U2NGZhZTIzYjA2YzllMWM2ZTc5ZDMwMGIyMWQ1YzBjMjEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-687288287\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2131406575 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">chEs2sQpmg2fYpUuqAOS1ZcIPLmVIQdx921QTw47</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9XNo0PIFsccsEXzvfQLAUsci7MaweQoaRcTvbnBl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131406575\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1445199750 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 00:36:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlgxcUllQVJtZnhobTBleDBXTGRua1E9PSIsInZhbHVlIjoiTmwrUDZyQ1hZZWlRTmRTSHIvdlptbzFiSlA0aFFzOGJ6UllrMnBOaFVONStSR091eFpYc2dMcDhDRVROK05FTzEzREswMm4yQ2tqYm9pTzhscGNkYTN1RUhNTC9tN1F5UDJhY1drVDk3MUs3SGRnVlBXMVIrejRxYkw3RmlBdUhuU3c4SlFScVVvcmRHY0hObW5VQklhL0VnaWhGd28rWTUxb0gxMERNdzVUWml0cGtpSWpYdi9nVThkNEY4SEo0TDlvSVUxTGI5U3VzRVJmSFg5S0E4c3RiRTJHa0o0Kys3WXBHL1NHNjFkdHA1RFFvMmZQclpDZ3Riam40M3lnVmxyY2JmRU9kWlZPN2syMlEzTmREWk1GSjVxK1Q4M2NNbmxoSHloZWlNS2oyQTVJT21zcjhZeG1EYTU5cngzckVPOTBUWDVNZ09qczc4WjU0VEE0aXZtWXFtSHpYVzVDajVSWitvQ2ZyaTdaQU8zMHNoNW0wUUZBL1JlYUVjU0k4clRPRjhSU1hWdUVNazcxNjVDNVNiNzdlYzJ2dGZ2RUM1eDNDZnJudEp2ZXl1THUydkM1YzBabkQvN3NkZXZkeno1RFZIczZWUHRJeDdVUTlDMkpIdXJVY3FZK0xCS3dPa1oxeGVVV2p0NTcyWEU0NEU1R1V4TmJ3cW91T0xzNlciLCJtYWMiOiI3ZjU0Nzk3NTNjNjM3MjkxMGE1NTE0MjY3ZmIyNjc1NGRkNjU3MzVlYzc4NzFkYmUxYjczMjc0MTJlMDhkMDZhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:36:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9WM1FLeGZyaHg1SC9MMGQramN6MFE9PSIsInZhbHVlIjoiZ3NtZXlNeDh6SzQzbkd3OU03c0hZNndSU3dycGM5dkhibGpDVWlPbEhtZ0wxRldHSTk3VGoyR2lYVE0zd3BNR3hFWWg2YmVMeHpwSGxSMVliMUZqR2dObHRhcWRMM2l0akxoTzZESG0rakp2SG1PcCtvbnltcHlEaXY0bVMvMDVTWlZWRWV6QTZmT1J2OVBxMHVhTzU2YXp6R3NjNVIrcWVJM2o3MVI1SE5GVTBVM0dqeUV1dzlzcXJFM25YNU0wQ2lhR0NqWDl6ZnNkb2lrdndGMUYwMWk5R3lCRGQ2T0FZbFIxcENPMGI3dFBuVkUwQnFCajVZcVN1RUxsRkpMUnhDc2ZoNFZLbmJoRjdiS1Ivc0lkaUlQdjdlNllncGlVczNRRGpUTDAzdzN2ZmxndXRqbkJqdjBQUlpSWmIyempnaHNuNmtjUnVEQUlqYjJjSHV1UEpVU0JOU1QvMEplQ2M2OHViclpBTUxjaUlidjc4dlFtVzN5bGNXL3NnM1RFc0RLdUpaS2NVbVVDNFEyS2lCaDdKdTRWQ2h0ZXhCMS9hZWdRQU1hdU1xdkZqdkZ5S1pESzhYOUltZm10bklpNVBSdjNCUnp2ejNkTmtma1VhMDJuRlBPaVR5Yk9BS2lnazVDd2JyZGc2ckZPeGx5VWJINEd4eDdoY3FaVTBDL3AiLCJtYWMiOiJmYmE3NjNmNDgxMWNlY2UxZTY3MTA1ODBjZjIxNDY0M2I0ZWI4N2IzNDE2NGU5YmEzNTg0NDQyZDU0YTc4OThhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:36:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlgxcUllQVJtZnhobTBleDBXTGRua1E9PSIsInZhbHVlIjoiTmwrUDZyQ1hZZWlRTmRTSHIvdlptbzFiSlA0aFFzOGJ6UllrMnBOaFVONStSR091eFpYc2dMcDhDRVROK05FTzEzREswMm4yQ2tqYm9pTzhscGNkYTN1RUhNTC9tN1F5UDJhY1drVDk3MUs3SGRnVlBXMVIrejRxYkw3RmlBdUhuU3c4SlFScVVvcmRHY0hObW5VQklhL0VnaWhGd28rWTUxb0gxMERNdzVUWml0cGtpSWpYdi9nVThkNEY4SEo0TDlvSVUxTGI5U3VzRVJmSFg5S0E4c3RiRTJHa0o0Kys3WXBHL1NHNjFkdHA1RFFvMmZQclpDZ3Riam40M3lnVmxyY2JmRU9kWlZPN2syMlEzTmREWk1GSjVxK1Q4M2NNbmxoSHloZWlNS2oyQTVJT21zcjhZeG1EYTU5cngzckVPOTBUWDVNZ09qczc4WjU0VEE0aXZtWXFtSHpYVzVDajVSWitvQ2ZyaTdaQU8zMHNoNW0wUUZBL1JlYUVjU0k4clRPRjhSU1hWdUVNazcxNjVDNVNiNzdlYzJ2dGZ2RUM1eDNDZnJudEp2ZXl1THUydkM1YzBabkQvN3NkZXZkeno1RFZIczZWUHRJeDdVUTlDMkpIdXJVY3FZK0xCS3dPa1oxeGVVV2p0NTcyWEU0NEU1R1V4TmJ3cW91T0xzNlciLCJtYWMiOiI3ZjU0Nzk3NTNjNjM3MjkxMGE1NTE0MjY3ZmIyNjc1NGRkNjU3MzVlYzc4NzFkYmUxYjczMjc0MTJlMDhkMDZhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:36:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9WM1FLeGZyaHg1SC9MMGQramN6MFE9PSIsInZhbHVlIjoiZ3NtZXlNeDh6SzQzbkd3OU03c0hZNndSU3dycGM5dkhibGpDVWlPbEhtZ0wxRldHSTk3VGoyR2lYVE0zd3BNR3hFWWg2YmVMeHpwSGxSMVliMUZqR2dObHRhcWRMM2l0akxoTzZESG0rakp2SG1PcCtvbnltcHlEaXY0bVMvMDVTWlZWRWV6QTZmT1J2OVBxMHVhTzU2YXp6R3NjNVIrcWVJM2o3MVI1SE5GVTBVM0dqeUV1dzlzcXJFM25YNU0wQ2lhR0NqWDl6ZnNkb2lrdndGMUYwMWk5R3lCRGQ2T0FZbFIxcENPMGI3dFBuVkUwQnFCajVZcVN1RUxsRkpMUnhDc2ZoNFZLbmJoRjdiS1Ivc0lkaUlQdjdlNllncGlVczNRRGpUTDAzdzN2ZmxndXRqbkJqdjBQUlpSWmIyempnaHNuNmtjUnVEQUlqYjJjSHV1UEpVU0JOU1QvMEplQ2M2OHViclpBTUxjaUlidjc4dlFtVzN5bGNXL3NnM1RFc0RLdUpaS2NVbVVDNFEyS2lCaDdKdTRWQ2h0ZXhCMS9hZWdRQU1hdU1xdkZqdkZ5S1pESzhYOUltZm10bklpNVBSdjNCUnp2ejNkTmtma1VhMDJuRlBPaVR5Yk9BS2lnazVDd2JyZGc2ckZPeGx5VWJINEd4eDdoY3FaVTBDL3AiLCJtYWMiOiJmYmE3NjNmNDgxMWNlY2UxZTY3MTA1ODBjZjIxNDY0M2I0ZWI4N2IzNDE2NGU5YmEzNTg0NDQyZDU0YTc4OThhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:36:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1445199750\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-640611104 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkRXc05ObTdrZ0E1ZkpPL0RySjBOanc9PSIsInZhbHVlIjoibFM1c0RYci9Yc3lqQ0VINW12V2RLZz09IiwibWFjIjoiYjUyYjNlYzYwYmU5MjNkOWQ0Mzg5MzgxNmE0YmY2YzMwM2Q5Nzk0ZjQyZWQ5OTg3MmRkYmVkMjU2OWVmNzk3NSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-640611104\", {\"maxDepth\":0})</script>\n"}}