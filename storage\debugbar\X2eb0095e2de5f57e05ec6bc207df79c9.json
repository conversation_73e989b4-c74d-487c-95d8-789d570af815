{"__meta": {"id": "X2eb0095e2de5f57e05ec6bc207df79c9", "datetime": "2025-06-28 15:04:29", "utime": **********.25735, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123068.773427, "end": **********.257365, "duration": 0.48393797874450684, "duration_str": "484ms", "measures": [{"label": "Booting", "start": 1751123068.773427, "relative_start": 0, "end": **********.199328, "relative_end": **********.199328, "duration": 0.425900936126709, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.199336, "relative_start": 0.42590904235839844, "end": **********.257368, "relative_end": 3.0994415283203125e-06, "duration": 0.05803203582763672, "duration_str": "58.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45068880, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00302, "accumulated_duration_str": "3.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2273111, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.583}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.241428, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.583, "width_percent": 13.907}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.248599, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.49, "width_percent": 23.51}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1152452576 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1152452576\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1234746413 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1234746413\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-421944575 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-421944575\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2040994124 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=10&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122996219%7C8%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktFaG0vS1ZqdVZTc3o1S0hGdGx2MWc9PSIsInZhbHVlIjoidVhab21xbkJaVlRTdWY5T0tmV3RxOEZnRk1EQ2h5WjRyS2R6aDlWR2xJQ2hieWFGRGl6MzVJRTVhZTBudUFwc3FzTDI0eFdCckJueUhjTkFDUUptSlhLUWkwVTlqZmZZbytMbG1qbGY0aFdtcGlTeXlFWUY3WTVVZ1VRMnZDdnc2bGpNKzNvNVkxeUQxTVVVRWJEL09Uc3MwV1loK1Q3UnBEUlZiREtUU3hYbU1yQlMyY1pvelFaTGRhTlZkZmI3WDJNRU80NVJrWEVPVlllQzdQYlFHSDBiTzhuODF6eXN1elNoM2NNUnZoWElVTGc2QS82dDJRVEhjOUcrQzJOSFBlTjM3WEpMdUtoQ2hFcHNTKzV1OE50REZnaVl0T1JaZkhxNkFxb1o0dDQ5WnVCanZqdFJJcDVLdUhaRUExSTkyWE52OFgzRFNEUk5lbWNtQmprQ0xha0JlcXhBZlVObUhKbWRObGZQaTVJYlVKR0l0ODdVRjBQd0JYT05vVWpMQ1hERDlHdHdvNUlocWxFUUhqWnYxNG5BMEQ4SmE5UlJYc3F3M29iNXJvM09uYlVYd1pXRTFQTFdMNHo1c29zM3Rmc0oxaHMyUnJwbU15djd3a2pTRHNJYWZSUHZYS0MwaklCUlRKdWlXY1lJN2FlaStqNUluSStFZ1BKbU55ZDgiLCJtYWMiOiJlNWNkMjA0MmVlNWFlOWExZTRmZTY3OWFlYmYzNzBhMDdjOTM5ZDdkNDgyYTU4MDUzZjU4NTk0YzE0N2VlODZlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVQaDUwR3dSYzUzNjliYnA0aHdFL0E9PSIsInZhbHVlIjoicUJUUm42N29WeFpPd25abzZBdmgyQy96cFg2dE1XQ2d0L3djMVFXU3k2YXJVWXFyR2xHMVZ3SFArSkM4LytKNEw4ZGRJdlg0UGdaeG0wMUxlMGYvRjVOM2tMQ28xNVg3TG84WnVRREQxa29HOUNKWGVsd0hzNjh5MDFEcTNsaFB3anZOdklHdGJXQXJuWkIvcnlPR1lxUWZZcWQzRmxvU3hPUitDd1NLNWZqSjNQYTlmYVIvRmtoTXVWZGtyUTB3c2ZCWkYwTW5mMmEyelVucHVvaTkvRUNjeVdlTHk3Q09acEdCNEozQkJMT1hGTzhhcmFwbnV0bVcwQjN0TmV2MVBqai9EYzdkVnd2enhZeERZb0dYWS9kYzVSNGdoK0JqTHZZcXU4UGY1Q1N4ZUZzdnVuTkRuUjlzdTZDcUJFQnlyaDZhMWtzakZ4L0NNV0wvN0lKbW5iTEhPemNsMUk3bUFZOUtubkxFczRYTFFTSVFnbVpjWEZCV2dpWXdOaWVmQ0Z1ZWlQKzN6K25CYjNGYnU4QWp5d001bEhxRmlISFVMdFg5ME1EdFRiSVBpMjhxS2RESi9FVEltSHphYmlQRVg1b3JEMTYzSWJXTHhwTE1pSWhoOG9HUVZ5MkcxUHpldTdxT2FLcUs0OFduZENIN3dLOHBNQmlCdWVZSVhqNVUiLCJtYWMiOiJiOTBjNGI4MjQ2NzEwM2M4MTQxNmE4N2Q4OGFkMWIxZTcxY2U1ZGNiZjExZDUyNDM1ZWU5MjFmYzJiNTk2Y2I5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040994124\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-844962820 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-844962820\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-26889834 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:04:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJVNnl5bDIvQkY5Ym05RVUra3kwd0E9PSIsInZhbHVlIjoiZGlWSCtlVGJBa1JSZjdsUVJ2L25mVS9QZC9iK0R2ZFNXY2VjN2hXMW5pOUpUMU9Na0xXZEsxSjBVT21HL0ZBL1A4eXhkbXI2Y1pTRnArdTI5eE5GNEE2RGlwNDkzM2QzdTZISG5WcTdhakFDZkxRNUd3d2Fxbk9LaCtaNTl0WTA0RUE0RDNDa090SWVWcTlpRkYxcW04bVBxMlJ2eFpoOUlnQXVuaHVHQmdxMzlGTkh6eXU5bnMwRTVVdG1MbkUyOFo0NnBxWnVjc2xmM2hKRFJNQTcrZ09JRXVRU25BZUcwak8zN2gvK3YxaERoWE4xTkUvMUhjUG5NV1ZXVTBDbGNqT2dBVkd5YlNZak0rMG1IcXhBOFM3OG9MTThNK3RtS2lUaDU0TDZuWDBoNTMwdUdEY1ZKV0RMSSs4K29SQUpqL2txczFma25RZGNjbGwvOWlsUm9SV2ZuVDRGUkcyajdzN0tWNU5RNXNmR1B6ckpib3lnS2QvSkkzN1dsUHExcVpTNkRUNHRrVDZoWDJnRFlLWEdjbUx4T2t4VnYwWkU1TStXWTQzaHI1TzI3UXVaVEVBbjQ4ejJMNXNPa256Ym1aTkUxQzhVSGxrNCszZmQwVW5vRmk2QWsxSjBVZ0ZhelNsSmZKaUhiY2MwdjRDYlgxSzdoU1JYcDJpSDFlZVkiLCJtYWMiOiI2MGJhMGQ2ZmIyNWE3Yjc3M2Y2NDljZDFkMmRhMzRkNGMyZmMxNDhlNDJiYWJmOGRlNTQ4ODgwYjM5Yzg0MGJhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkNvY2xkeFJDaU5CSVYyYlhHYzllc2c9PSIsInZhbHVlIjoicGc5aXY5TkNtQk1xTW9nSHJYOWd1VWRlMHJ1MWZLV3VzeVN2TnRMT1NVZEZscTR0R2p3QlNpVEZQOXpNWG1FTFMydkdHc3BRNnJQOFZlZUE4U1lMcDdFcVVLaTFXNTZxSmY0eGJJa3RtdC81b1pNWHhyeWFGUVZvR0N6OWExWGlQZjg4TEZkdzBXekpMSmVmdXl1QVRjSUJRV3J1K25mUW5qTXJ6R0c0bFNHUUIvT25WM2IzY0lBaXdhdlpsWFVHRDBhcDM5Vlk2cHlIY05vQmkxWlVjQlFFem5OdEJjNWxXUGk2Y0tYWVBLMWhvVlI5Vk1ZVi9rUVhTemZ6TjNYSE9kcTdpT0JSWjFyNkVzNG9jM3JaMGc0dllPR3Y3RS9Uc0laTkUyTi9KMTk1MVBtVHJNV0VraGpYTEx1UThoR0ZIbWEyTEppMW1SUSt2YlVEUXl3RG4xeEVFTlEyVEZOTnQ5K01wZFV5a2V3Mm5EVDZQVVBSMzNvcFdNajFXSUNUQnR2dGQxSXhVdmwzTWI5d1I2b2wwcEUrRHFwbkhrbUpsei9RbEhsUklGaFY3WmgzQ0hqTTd0eTIxenBNMk1YZTA4L21lWGNiWlhxR2FBcml0Ulc0UmJMTjA5VWpjVWtMWG1pWTJtVEl6bGxaQXNJbGpqRHA1ckxyNXVNVm5VQ3QiLCJtYWMiOiIxNTJkMjNiMDg5MzE0NzliNGE4NDJkYmIwZTFhMDJmMGYxOTFiMWYyZTk5MjQwZmJmZjE1YTM5ODc2MTRiNjViIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJVNnl5bDIvQkY5Ym05RVUra3kwd0E9PSIsInZhbHVlIjoiZGlWSCtlVGJBa1JSZjdsUVJ2L25mVS9QZC9iK0R2ZFNXY2VjN2hXMW5pOUpUMU9Na0xXZEsxSjBVT21HL0ZBL1A4eXhkbXI2Y1pTRnArdTI5eE5GNEE2RGlwNDkzM2QzdTZISG5WcTdhakFDZkxRNUd3d2Fxbk9LaCtaNTl0WTA0RUE0RDNDa090SWVWcTlpRkYxcW04bVBxMlJ2eFpoOUlnQXVuaHVHQmdxMzlGTkh6eXU5bnMwRTVVdG1MbkUyOFo0NnBxWnVjc2xmM2hKRFJNQTcrZ09JRXVRU25BZUcwak8zN2gvK3YxaERoWE4xTkUvMUhjUG5NV1ZXVTBDbGNqT2dBVkd5YlNZak0rMG1IcXhBOFM3OG9MTThNK3RtS2lUaDU0TDZuWDBoNTMwdUdEY1ZKV0RMSSs4K29SQUpqL2txczFma25RZGNjbGwvOWlsUm9SV2ZuVDRGUkcyajdzN0tWNU5RNXNmR1B6ckpib3lnS2QvSkkzN1dsUHExcVpTNkRUNHRrVDZoWDJnRFlLWEdjbUx4T2t4VnYwWkU1TStXWTQzaHI1TzI3UXVaVEVBbjQ4ejJMNXNPa256Ym1aTkUxQzhVSGxrNCszZmQwVW5vRmk2QWsxSjBVZ0ZhelNsSmZKaUhiY2MwdjRDYlgxSzdoU1JYcDJpSDFlZVkiLCJtYWMiOiI2MGJhMGQ2ZmIyNWE3Yjc3M2Y2NDljZDFkMmRhMzRkNGMyZmMxNDhlNDJiYWJmOGRlNTQ4ODgwYjM5Yzg0MGJhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkNvY2xkeFJDaU5CSVYyYlhHYzllc2c9PSIsInZhbHVlIjoicGc5aXY5TkNtQk1xTW9nSHJYOWd1VWRlMHJ1MWZLV3VzeVN2TnRMT1NVZEZscTR0R2p3QlNpVEZQOXpNWG1FTFMydkdHc3BRNnJQOFZlZUE4U1lMcDdFcVVLaTFXNTZxSmY0eGJJa3RtdC81b1pNWHhyeWFGUVZvR0N6OWExWGlQZjg4TEZkdzBXekpMSmVmdXl1QVRjSUJRV3J1K25mUW5qTXJ6R0c0bFNHUUIvT25WM2IzY0lBaXdhdlpsWFVHRDBhcDM5Vlk2cHlIY05vQmkxWlVjQlFFem5OdEJjNWxXUGk2Y0tYWVBLMWhvVlI5Vk1ZVi9rUVhTemZ6TjNYSE9kcTdpT0JSWjFyNkVzNG9jM3JaMGc0dllPR3Y3RS9Uc0laTkUyTi9KMTk1MVBtVHJNV0VraGpYTEx1UThoR0ZIbWEyTEppMW1SUSt2YlVEUXl3RG4xeEVFTlEyVEZOTnQ5K01wZFV5a2V3Mm5EVDZQVVBSMzNvcFdNajFXSUNUQnR2dGQxSXhVdmwzTWI5d1I2b2wwcEUrRHFwbkhrbUpsei9RbEhsUklGaFY3WmgzQ0hqTTd0eTIxenBNMk1YZTA4L21lWGNiWlhxR2FBcml0Ulc0UmJMTjA5VWpjVWtMWG1pWTJtVEl6bGxaQXNJbGpqRHA1ckxyNXVNVm5VQ3QiLCJtYWMiOiIxNTJkMjNiMDg5MzE0NzliNGE4NDJkYmIwZTFhMDJmMGYxOTFiMWYyZTk5MjQwZmJmZjE1YTM5ODc2MTRiNjViIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26889834\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1491850553 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491850553\", {\"maxDepth\":0})</script>\n"}}