{"__meta": {"id": "X21ffa68487011823ba0746697b5c28aa", "datetime": "2025-06-28 15:00:41", "utime": **********.219832, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122840.82068, "end": **********.219845, "duration": 0.39916515350341797, "duration_str": "399ms", "measures": [{"label": "Booting", "start": 1751122840.82068, "relative_start": 0, "end": **********.154855, "relative_end": **********.154855, "duration": 0.33417510986328125, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.154864, "relative_start": 0.3341841697692871, "end": **********.219847, "relative_end": 1.9073486328125e-06, "duration": 0.06498289108276367, "duration_str": "64.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45784416, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1973\" onclick=\"\">app/Http/Controllers/PosController.php:1973-2026</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.016589999999999997, "accumulated_duration_str": "16.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.186132, "duration": 0.01613, "duration_str": "16.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.227}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.211621, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.227, "width_percent": 2.773}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"21\"\n    \"name\" => \"حلواني مربى المشمش 800ج\"\n    \"price\" => 13.0\n    \"quantity\" => 1\n    \"total\" => 13.0\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1323784781 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1323784781\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1329627404 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">21</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1581;&#1604;&#1608;&#1575;&#1606;&#1610; &#1605;&#1585;&#1576;&#1609; &#1575;&#1604;&#1605;&#1588;&#1605;&#1588; 800&#1580;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"2 characters\">13</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1329627404\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">174</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751122833995%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imh4WHdXQVFZbnZPYjYybFlWTXg5MEE9PSIsInZhbHVlIjoiNi9IQmNKMWxWbVdwZ2luWlQ1UmVkS1doZTRDMjlUa2h4LzVQM29KaHFOTE5qRHR6bFh0cHdmR04waFlHeStwSHVmb1VtalAveDZxTHNmT21rZUh3bXJjQjloUGJsRExwdWcxbmw0MHJLL3p6V1hUeFI4NGd2blMzalJ2YnA3NUR5K0tsVFVrcFhuS2JMUzBvaFAxMWtIcFRuSFZGUHFKalFSUDA0c2tJbTdna2dESjBXdFB4cTVMRWszczg4Q1VGRk05YTE5SEJHUFFtNlF6UWVaYjd3Nm9WekRPdC9DRFZ1bkdtc2JITUFSK2JZVUR1UWMxeGp5OE0raER0YjBScndmcVg5YzBpa2x3QXBGb0x0RUg1RkoyY1hsVXlKVm1FUFB5QW1NQnVOSkVxc2pVazVYUmZHbDh6dFYvWnFaMUR4a3pWL2FpcTVIRVRvYjJWUmpLQ1VYMEJYVTFCNmV3ekVaQWloVG9xZWVBK3FOQjV5RXUxRFd3SlJic2dlaEVFSmVIcEtCZzc3NFhvTThyeXJqd0Rnd3FrM0Z6Vk45aGdOa1NuWnNvQkZpdDBmVG5hSnVML2FhTG5JYTc3aVFtTk9OazBaNHJJSjFickF1cXRqZTRrWWlqaVhjZUQyUXEvOU9mRzJJR1N6dnY2b2huMmc5ZWU2WmhrNkx2ZTVmY0YiLCJtYWMiOiIyMTQ5ZmVjYWYxMGIwNjZhOTY5NDk4MjI3Y2MxNDYzOTQzMzAyOTdlN2E0ZjAzNzU2MGI0MTExY2RkZGNjNGFjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVoTlhadGR4QTRGbmhOa3VEL1ZyV2c9PSIsInZhbHVlIjoiSVhibDdLdTdHZzM4MEU3TXZsd3VVR1phcEFISFlDaUZwWE5iSytnbFY4dVhGZlZLeHM5dVBJT0VUMUFMQWtmaWRiby90SXJ4U1lxa2l5eTA5ek02dTNac2NrZXVFcUdMcFpjZ2JnRkUwVU1XT2RCN1A2Z2xpODJ1SGUrcnVDaGVwK3VrZkZuYnJjclNmQ0g4aDJBN2FmeWpTdzlyMFA0YUJYSktRb3AxNjB2ZjdXVjYvV0IveCtlc3grRmVua3htTFFTa1NZVjlUeGdPN3Y3UFJDT1lkQ2VqbFQ4bkZFNjVVeVJxSVJIcTRFaUtTbkFuRE1Yc0Z4Y1JRb2piVkhLanc1OFhUcTNrRUw2KzU2UjcydWNXSTVPeUVWYy9qS2xsTjlvKzBiNVZvcEdLZ3RrZGtVaTRhN1JFTGV5RTNtYWQ2aWx0eWhQdjJ1NEJIdzhxWllkM2tYTnEyMGJZTmh1MTZXWUFJTmpVeXpRTnIycFNMRndKWTlxSDFsVXdHaUc3aFFmWmhBUWxHT3pJMUNIRnZxcVF1eXUrVklTUDhrNWRvem5Oelp4cnljNG5kWUZ0bTlYc3V3YXV3S2l5aXBDS2tmOHpGT3JhV25NVXNNSmFrejB5U3hpRndQQS81SGF2cVF1N04rOUFlYmdDeHBRWVdOYUdxUTRTeHp5OGIrKzciLCJtYWMiOiIwNWNiZTAwMjkzZjViNWVjNDRlYjJjODM2MDc5NWM3NDFlZTMxM2E3NzY2NDdjZjJmNDUyNzJkZTY0ZDU2NTljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1087320920 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087320920\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1488053576 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:00:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJKR0hZS05YTzduZUMyeTB6dkZFUUE9PSIsInZhbHVlIjoiR2ZrTE8yL3JrM3VaSkxPdTNyWmhtbFJMcFFTbFluTkwvbUJqQ1dRdnZTbmMzc2Z3MHBsZkF2dDVscFFCeFpPbDcxOVYzenlkU3h5QkJidWRpbDZkRm5iVCttbytkWGkzOHZpRTcrVjlPUnA2NzY3UlN3bDRHY25tTUZWMStvVUNmd01hQWFXMzlzTEtOQkUwVmcyTFNITXd3M21JWURERDc5ZnRneWxYY1FEcStlWGFUbkkwYkw5TXNtdUZuaVRtRDlYNHRqb2Nuc3RuaENDdFdZaGRRMnZFYVZtdUJjYTEzaDN3TS91RWtERWlPaStiN1JabmNjaU8yOHVxYXVUaWtreTZiczRwTExCU1Nka3RCeDNlS0FSTzAxdVJ1RVNqdkRFREo0REhoZTFwVkZSQmFTd3Y3Qkc3ZXRpbTJrNlp1T0ZOTllTclErQzZvRHZBUUVDQ21lNXQvallhUGYxZjhMRFo2R3YxQU02N2hOcGowTzRMeUF3a2FXTkp1czRROWpMWFEyWlprNEh1RGdiMFpNdmIvTmlRd0ZkT3BRQjdaY2ZJZTFWSlMvOVRhb0tEM2RiR3JHM2ZMWlV5UmI5OG1OcC9lTVB6SFp6YklhenBWTkRWOFo3WTViRkk2cFlGTkpzelc4Q0xlaWVRc1kxRGJ4ZGg2eURFSGl5N0NSZ1kiLCJtYWMiOiIyODAzM2FjZGNiYjlmNjRmYTNiYjg4MzA4N2VlZjEyMzBkZTE2NmI3MDBmNzU1ZmFhMzdlNDQ1ODM2OWUxOTZlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJBRE55ZHpzbDM3bWw2WnVyVjlBQmc9PSIsInZhbHVlIjoiZmxpUGxCUkk0a1RObTNGT2VMc0lRalFXN2NrQ29PMU9EajdkdHNXZFhJY0hpd0haanFhb0NEZ2pKaWNSM3c5SmpxaUtjb1dLUEYzSjMxTUJBOVpHRVRpMWlYQTd6ODB4cU5Vb1lwMVlqdGt3b3NxVURPRjlaQ3VYa0VZNWN5WFQ1SWRGSklaSFZtbzlHT0E2L3Z4UkR3Z2J3bS9DSE9lcDNFYTFoSStlS1VLRmE1ZDdNTVpNWUE2ZkxZbE45U1Y0NzlqejMxaXJxS0VZNUs5QzhuZ3ZyM0JHREE4MTd0eWZUT0JzT0d4RGNwSXBzZHJ2dzNNNmV3Qm5zMUo5NE5MNkVuVzF0SlJnUzkvdEFxVWZ2Q1k3dHY2cGtDNVhnaTZiM3Rib0VSM0t2amZ3TVlYTVlZZ2tCNmpuNU43cmhlMFRaVXZ3c0JqY3NuVnN0TjFaakdweTlqQXBBV0gxR2VyeXk4WW5GNklUOWVGK0dMK0RPaXR0MFM0MjA0ZzdNUC8yQ2lpdkZBd0N4VVN1L0VqUElCMnc2WU1XeUhvYk1wSERBVU5uRjA0T1ZIT1g2UWk5aTgxelh4emZOZHpoK1ZjRXlCYWVCckhHcCtQWjBNOENuanJVMkM5Q01UaWxMcFpMYXhXT2NESjVxVUFyQXJONEkvL0xaS25MSmZCeWNHaGIiLCJtYWMiOiJjOGNjMDc2MWY2NTZlMjI0OGRiYzBmYzc4NjE2ZmE5MjdkNzdhMjJjYTNjZTY3NzMwMGRjOTA5YTljNTYxZjhkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:00:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJKR0hZS05YTzduZUMyeTB6dkZFUUE9PSIsInZhbHVlIjoiR2ZrTE8yL3JrM3VaSkxPdTNyWmhtbFJMcFFTbFluTkwvbUJqQ1dRdnZTbmMzc2Z3MHBsZkF2dDVscFFCeFpPbDcxOVYzenlkU3h5QkJidWRpbDZkRm5iVCttbytkWGkzOHZpRTcrVjlPUnA2NzY3UlN3bDRHY25tTUZWMStvVUNmd01hQWFXMzlzTEtOQkUwVmcyTFNITXd3M21JWURERDc5ZnRneWxYY1FEcStlWGFUbkkwYkw5TXNtdUZuaVRtRDlYNHRqb2Nuc3RuaENDdFdZaGRRMnZFYVZtdUJjYTEzaDN3TS91RWtERWlPaStiN1JabmNjaU8yOHVxYXVUaWtreTZiczRwTExCU1Nka3RCeDNlS0FSTzAxdVJ1RVNqdkRFREo0REhoZTFwVkZSQmFTd3Y3Qkc3ZXRpbTJrNlp1T0ZOTllTclErQzZvRHZBUUVDQ21lNXQvallhUGYxZjhMRFo2R3YxQU02N2hOcGowTzRMeUF3a2FXTkp1czRROWpMWFEyWlprNEh1RGdiMFpNdmIvTmlRd0ZkT3BRQjdaY2ZJZTFWSlMvOVRhb0tEM2RiR3JHM2ZMWlV5UmI5OG1OcC9lTVB6SFp6YklhenBWTkRWOFo3WTViRkk2cFlGTkpzelc4Q0xlaWVRc1kxRGJ4ZGg2eURFSGl5N0NSZ1kiLCJtYWMiOiIyODAzM2FjZGNiYjlmNjRmYTNiYjg4MzA4N2VlZjEyMzBkZTE2NmI3MDBmNzU1ZmFhMzdlNDQ1ODM2OWUxOTZlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJBRE55ZHpzbDM3bWw2WnVyVjlBQmc9PSIsInZhbHVlIjoiZmxpUGxCUkk0a1RObTNGT2VMc0lRalFXN2NrQ29PMU9EajdkdHNXZFhJY0hpd0haanFhb0NEZ2pKaWNSM3c5SmpxaUtjb1dLUEYzSjMxTUJBOVpHRVRpMWlYQTd6ODB4cU5Vb1lwMVlqdGt3b3NxVURPRjlaQ3VYa0VZNWN5WFQ1SWRGSklaSFZtbzlHT0E2L3Z4UkR3Z2J3bS9DSE9lcDNFYTFoSStlS1VLRmE1ZDdNTVpNWUE2ZkxZbE45U1Y0NzlqejMxaXJxS0VZNUs5QzhuZ3ZyM0JHREE4MTd0eWZUT0JzT0d4RGNwSXBzZHJ2dzNNNmV3Qm5zMUo5NE5MNkVuVzF0SlJnUzkvdEFxVWZ2Q1k3dHY2cGtDNVhnaTZiM3Rib0VSM0t2amZ3TVlYTVlZZ2tCNmpuNU43cmhlMFRaVXZ3c0JqY3NuVnN0TjFaakdweTlqQXBBV0gxR2VyeXk4WW5GNklUOWVGK0dMK0RPaXR0MFM0MjA0ZzdNUC8yQ2lpdkZBd0N4VVN1L0VqUElCMnc2WU1XeUhvYk1wSERBVU5uRjA0T1ZIT1g2UWk5aTgxelh4emZOZHpoK1ZjRXlCYWVCckhHcCtQWjBNOENuanJVMkM5Q01UaWxMcFpMYXhXT2NESjVxVUFyQXJONEkvL0xaS25MSmZCeWNHaGIiLCJtYWMiOiJjOGNjMDc2MWY2NTZlMjI0OGRiYzBmYzc4NjE2ZmE5MjdkNzdhMjJjYTNjZTY3NzMwMGRjOTA5YTljNTYxZjhkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:00:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1488053576\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">21</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1581;&#1604;&#1608;&#1575;&#1606;&#1610; &#1605;&#1585;&#1576;&#1609; &#1575;&#1604;&#1605;&#1588;&#1605;&#1588; 800&#1580;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>13.0</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>13.0</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}