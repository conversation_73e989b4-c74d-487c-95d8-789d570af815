{"__meta": {"id": "Xf7a3f4cd6fb51e566939acbf275926ff", "datetime": "2025-06-28 15:08:10", "utime": **********.193626, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123289.738585, "end": **********.193642, "duration": 0.45505690574645996, "duration_str": "455ms", "measures": [{"label": "Booting", "start": 1751123289.738585, "relative_start": 0, "end": **********.135278, "relative_end": **********.135278, "duration": 0.39669299125671387, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.135289, "relative_start": 0.39670395851135254, "end": **********.193644, "relative_end": 2.1457672119140625e-06, "duration": 0.058355093002319336, "duration_str": "58.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45784480, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1973\" onclick=\"\">app/Http/Controllers/PosController.php:1973-2026</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00227, "accumulated_duration_str": "2.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.173439, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.7}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.183811, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.7, "width_percent": 16.3}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1599008369 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1599008369\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1558746911 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123286871%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktKb2hzVUZmdHJIRFk0V3gzRUR1a2c9PSIsInZhbHVlIjoib2FtL0U2akxTdEovaU1NeTBBWlEweWlMcVNHVjBhQ1IzVG9rUExUd042TnFIY0x0aDhLQ2tBNnN0LzVkdWpDbE9oR2V2aFhXY1MyYjR1SnJydGdRZWlKZHZ1MTlXblZ1d05oWTdwaGtpOUNRNDZ2c0paQlhTRURLNEZNWDhOYmszZ29rY3BjUkhOWUNTclYrVEw1Qy95dDRGeUNCd0FwalNkN1FFeGFFTzJpV2hHN25Xb1pkbFRtRHptYjdJaHdJSmlaWWtVSXZ0dFE3Vm9sRjBCanhqQmxJc2Y2Sjg2S2wrV0Yra2JxZE9IbHozU21XZWgraDVuVFZ6TWluRkVUb3I0MTZ5WWtrVTVsSnhzVENjcE94YkRNSTdic0o1Y25xTHBZd2ZFSGZQZDFrbFplR2tvdkdhMTltV2N3eVJFbUEzTEdOZ0Jtdmhxb0x0STZEZXd1RzJMMmdZSUlZMU9oR0ZkeVhobTdGSzNsOVJzMndzMWZ4ZytidUVINHQ3TW1wMzFtWG9maitpMHc5SHd3UWdCWk5pakRZUnlDSjBpM2pxcTl3MHo3VWluKzlaamhqQ0g4N1BrY0VpTG02dDdHQ1JudTFxeVd4UU1sNzU3UllUQ3REdFF2ZlloYS94aGFjdjJ2aUNodnMreDcxLzlwVWRZNjk5L2l5STV5WUFJc2siLCJtYWMiOiIzNDMyZTI2YzVjMDlkM2RmZjFlNWE1ODdjYWNiZDEwMDQ3MmE4OTg5ZjkzMDczMDM1YjBmMjYwOTVjMGM2NmE2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InF6RzFuVXRuc09RM21SUEhqbHlwSFE9PSIsInZhbHVlIjoiNXVPNW54ZGpZNnZxYjFieXJlbFdLVTFBK1B1dHdHS1VjVUI1dVdIWGdpWFJpNW5tMnJHL3UvWm81QkJUQzJYZ1Bla29XSnpKRkp3WEpDU0JtdlU1NHZqM2hOTjBxc05SZjU3aThtVEFtQmtRbTJyd3YrZHkwRi9hQzExb3NsUW1YYnRnQmNVR2t4cXd5MUUxMmFteXYvSlB5ZDFCRk1HV08rVlhuM2RFd3lJckI3NzkrZTRzWmNsdDMzeFg0VTBwemp4cEdVQmJtTDRzS080K1QzMEl2S29hU1l0SEZ2ZzV5Sm1pY0RZRE5zZVEyejFBMHpHeHVaMytmMFdiWmh5WnVqckdMcnZzeEdaQWllVHNsbTIzekJrUlFWTnUrRE1OV0hvdmhmV2dLK2xaTXp0RmpPcHd0S2xIbTg5THhkY205OWFoUm1JOUVWMWZNRmF6anNHb0lzdjVxUDR4QXpUUEg0cG9LdFNMb2ZsKys4dWVrTStiTUxqZlU1cWNjakltRWkxdWVVOWxSd0VxZHBOT0R6WHlvVGY3UE0reGZyTEdWRWF4cFdKc2gvY21EZ0tsSW9iNUdMK1V6WFgwWDZwR3JtWGFGbE1XOFFSZExtSXp2NGJZb2ZhSllRamdOekRkeGZIb2xOcWhLdzhDSGpFak5jRDM2WVhvd1l1QytGaTQiLCJtYWMiOiIwNDkyZWEwMzVkNzZjZjAyYmE2ZGZkZjYyZmVlYTlhNDkwYWFhYzM4NWY3YWIwZmY0ZjBkNmQ4OTc0MWU5MDVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558746911\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-800118293 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-800118293\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjMxRWt4L2MwaS9YVzB1eDY1S1pJU2c9PSIsInZhbHVlIjoiK2JhVEFNWEdGV0VOUWE2aVE0UnMxbkNZc2JDemFDNndkcnJQTXY5VUNpUjFQZTZzUU4yOTljU1YwS0pwdjZJN3NyRFR0bFhaRGlHazF1NkEvQThnb1VWUWZXcWpYZllPSTFIYUhUZGpxbUNTdmhEM1poM0s4VldOQXk3VFFnSVU3WFExZDJkekh2YjRac3BSYjgzK0RwbXhBeTd6R3NpSDBjazc5c0twSThtblhKMnBoU2M2Ry9ycHJGZ0ppeUVJci92OGlnVTR2eldlTUdKVVZjNGR5ckRYMWc1M1NFZk1LZ0ZDRXZrMThJRE85amdialNHck5kdGJBZUFCZVJJZnBUSDZKVDdBQ0NIQ3B1K3hLcGo3ZkRISTU2cStUT0lPTWdoVkRYOWZPMW03Wm5DNUw0Z2dhU3dqSTY1ZndsMm1VazZSTjdNUWNQaWg2WFA1MGRtTFE1RVJjRUhaa1ljNHA1NkNOdExVMnZBdGdza3ZNaG00N0pPcHF6clhjNWltNFdnZGVuaGx1SnBMSjJJbG11L1YvUEJObjAwQVh5WlovM0RCWGhid1RWM2FRUDFtUmd0TmEwNTliZUZDbEhxdnUzK08rTVN4RDl1MEdPRStLVDBTaEtpYUU0R2tYZXZjTHVBYkpub1ZBNDB0clZtSGpTeFh6d0N5MlpJcEQrMUEiLCJtYWMiOiIyYzMwYjkwMmJhYzM5ZWJmNWYxNTI5NTNkNmI3NTAwZTc5YTdhYmE5MTVkMzA0ZDM1Y2ExZTI4NWQ3Yjk3NzU1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZhemRUc1IyQ0VLQlF3K0VxNlhQY2c9PSIsInZhbHVlIjoidGpYcjBmL0IrWTJIQkxoRHBiR0E0WTZoOWNTVER3a2NOR1VzQ2c4UU5yRi8rMWdyNHVpdUN6R2c3ekhzTnM5czhYUStTM21yWXVLRmNIMVdxMW1mSUJaVXdJbTJHMTZ2Zi9kSDFaNGJiSURjMGg4a2FrSzBDY2hSeGFNTk5lcW04TU52S1VnRmlyU241TTZmR0NyalF3Z1BkN0JEOWlPUDVWZVVoanhrcy9zMkJVZ2NVNGk4bndGU3lmVERBVllkSGFWeElKNXpzb01XRXJhN2VXOThueFB5WUpERnV4M2VQYWg5aXN6VHdQdzhaSlVMZVdYTWtMUWIxRGNRMkNOY2NHci9OZE0xOVBSbHBDVG5NdlVrMUpCWW5zOGlaMWZUdkRaU1J4MUlnSU1ZSjlqOXdlRlB4S3VjTmhVMmk4ZE1WN0NuT05XeURBaHcwcGZqTXF6OVZ2Sm5lc3hoMmRDTFN5ckR6eW5mUUV6Qi9oVXdZa3dROVpZZWZENXpZTUZOSDlCN1JKVDdBU2N3WExKWlJOT2tQQUFjcC9QK2pBcnNIOFp0WmNQL0t0OXZ1YlJqbFNXbk96K2JCSFpXS2dwS05QK3VGNlZoVC9lYTIralZCamRGMWZ4aUZ2T0srOHBiOGpEaU1qMXhKU2d4bUZvUC8vaGJxOCtUcmpGN2l2VFEiLCJtYWMiOiJhY2M2YTNiZDlhYmEyMWRmM2JiZjgzMzUyNGYxNmIwMzc3YTI0YzVmZDNiNmNmZWE2YjhjN2YzMTQwMTg2M2I1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjMxRWt4L2MwaS9YVzB1eDY1S1pJU2c9PSIsInZhbHVlIjoiK2JhVEFNWEdGV0VOUWE2aVE0UnMxbkNZc2JDemFDNndkcnJQTXY5VUNpUjFQZTZzUU4yOTljU1YwS0pwdjZJN3NyRFR0bFhaRGlHazF1NkEvQThnb1VWUWZXcWpYZllPSTFIYUhUZGpxbUNTdmhEM1poM0s4VldOQXk3VFFnSVU3WFExZDJkekh2YjRac3BSYjgzK0RwbXhBeTd6R3NpSDBjazc5c0twSThtblhKMnBoU2M2Ry9ycHJGZ0ppeUVJci92OGlnVTR2eldlTUdKVVZjNGR5ckRYMWc1M1NFZk1LZ0ZDRXZrMThJRE85amdialNHck5kdGJBZUFCZVJJZnBUSDZKVDdBQ0NIQ3B1K3hLcGo3ZkRISTU2cStUT0lPTWdoVkRYOWZPMW03Wm5DNUw0Z2dhU3dqSTY1ZndsMm1VazZSTjdNUWNQaWg2WFA1MGRtTFE1RVJjRUhaa1ljNHA1NkNOdExVMnZBdGdza3ZNaG00N0pPcHF6clhjNWltNFdnZGVuaGx1SnBMSjJJbG11L1YvUEJObjAwQVh5WlovM0RCWGhid1RWM2FRUDFtUmd0TmEwNTliZUZDbEhxdnUzK08rTVN4RDl1MEdPRStLVDBTaEtpYUU0R2tYZXZjTHVBYkpub1ZBNDB0clZtSGpTeFh6d0N5MlpJcEQrMUEiLCJtYWMiOiIyYzMwYjkwMmJhYzM5ZWJmNWYxNTI5NTNkNmI3NTAwZTc5YTdhYmE5MTVkMzA0ZDM1Y2ExZTI4NWQ3Yjk3NzU1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZhemRUc1IyQ0VLQlF3K0VxNlhQY2c9PSIsInZhbHVlIjoidGpYcjBmL0IrWTJIQkxoRHBiR0E0WTZoOWNTVER3a2NOR1VzQ2c4UU5yRi8rMWdyNHVpdUN6R2c3ekhzTnM5czhYUStTM21yWXVLRmNIMVdxMW1mSUJaVXdJbTJHMTZ2Zi9kSDFaNGJiSURjMGg4a2FrSzBDY2hSeGFNTk5lcW04TU52S1VnRmlyU241TTZmR0NyalF3Z1BkN0JEOWlPUDVWZVVoanhrcy9zMkJVZ2NVNGk4bndGU3lmVERBVllkSGFWeElKNXpzb01XRXJhN2VXOThueFB5WUpERnV4M2VQYWg5aXN6VHdQdzhaSlVMZVdYTWtMUWIxRGNRMkNOY2NHci9OZE0xOVBSbHBDVG5NdlVrMUpCWW5zOGlaMWZUdkRaU1J4MUlnSU1ZSjlqOXdlRlB4S3VjTmhVMmk4ZE1WN0NuT05XeURBaHcwcGZqTXF6OVZ2Sm5lc3hoMmRDTFN5ckR6eW5mUUV6Qi9oVXdZa3dROVpZZWZENXpZTUZOSDlCN1JKVDdBU2N3WExKWlJOT2tQQUFjcC9QK2pBcnNIOFp0WmNQL0t0OXZ1YlJqbFNXbk96K2JCSFpXS2dwS05QK3VGNlZoVC9lYTIralZCamRGMWZ4aUZ2T0srOHBiOGpEaU1qMXhKU2d4bUZvUC8vaGJxOCtUcmpGN2l2VFEiLCJtYWMiOiJhY2M2YTNiZDlhYmEyMWRmM2JiZjgzMzUyNGYxNmIwMzc3YTI0YzVmZDNiNmNmZWE2YjhjN2YzMTQwMTg2M2I1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}