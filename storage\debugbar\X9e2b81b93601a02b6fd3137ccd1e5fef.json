{"__meta": {"id": "X9e2b81b93601a02b6fd3137ccd1e5fef", "datetime": "2025-06-28 15:02:43", "utime": **********.32134, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122962.746565, "end": **********.32136, "duration": 0.5747950077056885, "duration_str": "575ms", "measures": [{"label": "Booting", "start": 1751122962.746565, "relative_start": 0, "end": **********.259232, "relative_end": **********.259232, "duration": 0.5126669406890869, "duration_str": "513ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.259243, "relative_start": 0.5126779079437256, "end": **********.321362, "relative_end": 1.9073486328125e-06, "duration": 0.0621190071105957, "duration_str": "62.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45066480, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00277, "accumulated_duration_str": "2.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.294255, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.119}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.305924, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.119, "width_percent": 12.996}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.312186, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.116, "width_percent": 15.884}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2080532237 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2080532237\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1055933705 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1055933705\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-646073025 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646073025\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2108016553 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122759782%7C5%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik0zVGxZOUE4NmxJTGYrRTI1WjdWR3c9PSIsInZhbHVlIjoiVGtGNFRPSkxuOG5VbVVQK3lTYXhLYmFOQWNRL09BSTJVU2ZsUkJZeFlLcjRnNkNSM0dvS3pLQUJxTU5JUURFVWhmMGVhQm5SbEtZZnRBdjkwTzNONE5KcGZ4VHVncHROQTZURXJXc0tDT2ZGUXAyR3d5S05XQ1VVSDhyZmU2LytNOFhaUFRwYWZSYkgvVlVpSkY0NmdOZzFlaS8wWUt5aWZlVmtlUnJVTHZZM09YdFU3NG9ZSUVGVTBsUnJGRHh1M1U2UVlmNHZaeUZabGhsZ0ZZSUFHVGZHeGEzRzk3TjJ1T25IMUh2citwQ2FSMlJORzNVL2Eva0NLajRmWDFKbkdDZUx2bFR1dURFVU0wUmgrT3RHNFM5MTg1S1VScU90OEc1Z09QSWlDTWx0bndpcndOb0pySWlJTThwVDMybmV4SWFMdFU1cWRVSmhMQUtGRDZlU2wrTDhyUGdVVGhYWmxDVHc1UXU1K1JhSmZ5cFB4UUFRM01pZENNMXA5WFdyaFlWL01RYTYxUWFwdGdOZkg0WTNVOGwvd1ZKamJPNjJzRE44UGViWTk0TEVLWEI2bnZkTGszWFlHVXNOM2orWG4wZ1BiUEdWYjJ1d09LYmNFbTJNeDJQT09XeDAxazhVa1phZ0FhOXhDWDgvWWVUUWtoQ2R0ZVZhOEkrNlpGSTciLCJtYWMiOiI3MWJkM2I2MDVjMzQ4NTI4ZTg5MmQ1NTUyNGRlMWExYTFkZGVmOThkMzY4MjllYWZhZTAwMDY4YjNiY2Q4ZTZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhQaFBBcGU3a1ZLSlJrZXlzVzBSTWc9PSIsInZhbHVlIjoib3FLMmdsRnFrUmExWkUrNENpWGNZRFhScjRTZW0wTFcyL1oxMUNzMyt4L2Ezelh1cnNKeFhLMDhTRXIrZGRRcWIwckh0eTVna2VMMXZ1aXdnR2xaZG5OUzROVllzVDJIQTRCaUxNeTUrQkN3Sm9xWkJkMFpaK1ZFZnk2QjFjTkoyaW0wZnFRcWwyd3JmOVpsU01rSmxiS0o3WGIvcXgzbnZpUUtNcUp6MjREak11Rm1YMUE3UktWTmkvOUpkVEtvdXFxM0hRenlTOUlNdlUweEQ1S3NSb2wrejdUNUhNMHJ3d1p4bzNpWTlBaVJ2NTZpLy9hSlpac3RqRktXUHlnOFJRYWFneDhCUi81QitaeEJmUmRLdmt2Tk1YcUdFYS8ya21sMWgwelFQK0N1RXVKOGtWWStJMXhDaStpMnZQN3AwZi9WNEdBUWFUTnFsZWRDTXhFdEllYlpQaGFlTnkvLzlzSVZHaVJUVFM0aU5CRnp1cE1kZkZSL0ZCNVQvQTBpZEJzNDZSR0JNaS80aWI5WThzcWhhN2NZZ2dXT1loR00yOVFwK0FWL1Bac0Vhblo1dDJRU0lxNlF0aDJxcXFXZjlkZDF6aFV2R1VSZm9CeklIZmVsbmZ3clp3QW0wbHNXTTVDek12Tnk1Wmt5S21LVXJacmlvSlJBcDBwQThxQ08iLCJtYWMiOiJmNThjYjZkODk2MzlhYmRmMWNjNWEzYjJkNWFmYzVlM2I5NGE2Y2MzMDA0YzNlMzcxYjllMmQ1Y2JmZjczOGRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108016553\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-324132529 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324132529\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-23243511 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:02:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJ4ZHNDL3pQdlgzUjNVd2ZKRmkwZmc9PSIsInZhbHVlIjoiUTZYaGNLWDBIa3hsS0tCTG8xalZHWHJzdGV0dUc5WjV1QWlQQ0tNWDVGNzQ0a0tyTUwvelliNWhNaG1nVENSZWdmaUlaelovUUlNaS9Pd2RzNWViYVBnenk5WkFFQmp6YnA5L3Z0bmVLalVhSW5seEN5cmJMb0V5Yjk3RE9VM3BZMzBTU204U2h2eEJiT2kxS0gvcnQ0LzYvM1NVRDhQRktSWEYxL2VrVW96c1BBZUVzdkxGanBicFhOWTNzaDA3bmhXbVhKTVo1RzNiS3VmWSttTzU1UlY2eDRPK09LMlJSM0dhbmdkNVVIRmd5aDM5MUVHTGE1YjVjT1NGKzQ4L0dOYUpvWFI0QmFCc0d0QWlaOG9ZaEtVTXZTdkR4bFNWV1cycWV4eGkyd2t3V09WSnJWb3VjQWpjd0czYlBWK25DeHRBcEx3dGdEZmtVT0ViVXhSS2sxQjB5blRpUnlTYTV0Wk1HcGlCdG5kMmp1MDNVclhWNlBrZlgxcnVUazhyU1RXclo2WnZqMUtRaDBGTkFLUGVlK0VJRjArRmROMkZCa0dGeVJiWEozOXVubUx6aDVDNGdXTGFjcXlZWTBONnQ2TzBiaTdjWXlhOW5pemVZdFZRZ04rcGE1RldpQ29TOHkvcVNEdDM3aFRTV2tnWFlJcGZ0KzV5eXRrR3RzRDUiLCJtYWMiOiI4NjU1MzlmMmFiNWFiZTA4OGM0ZjUxOWJmZTk0NGY3YTQxYjZiMTRlMTc4OThmNzY0YjU5ZDBhYTZmMGU1NzRlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:02:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRPLzEvNWhXWUI0K2N2THJEb0kvWUE9PSIsInZhbHVlIjoiY09zRXNBNk9paGF3eVJrcjhSbGh4d2hiWXlzN0VBQXV2WEc4Vit1RDJmVFpYbnEyWStHd3RGc3FtTzNjZ2dHbDFqaEx2c1hDM0VUN2VKbzRxOTdINEFLbmgveU03OTNGMnh2R1l5dllMTU81ZExydEg3c3YzdFUxbExMK09SMkk4bkUxWm5KeWFyb0VPTjV4aWtYT3N2R3hlRWl5L1E1bURlTkVKU1Q3dUQyZFN1SmFlRnhKMnRGSXNkcXdqOWZWd0EwbmxrMHFhVTU1TW9vTFJ2OFRYS3UrTThLMHZuMmw3QTJlbDFJV2pKY28wTDVTR1Z3Tkh6QWJicWZPUno5cGQvM1VScGpOeXVSb3dPdCs0K0hoR2dwSXJMTnlhSW8xWW05c3BSVXRaNUVpQVB6cnczYkdWa0ZBNkVIbGlZVjV5UHRSdjZlWXgzd0hWVU94czBrTm5sN0lGb2w2aWJqSWc0TWdLc3VlTWR2NjA4eTVReityRjQrbXU3UUprNFVLYmE3NjgxOXlMaGlMaXZYSGJmcWlPR1prc0Nha2hGYSttamszbEcySzZCb0s3UlUxUFBjRWs0YzRmeDNsempJUERUSGxidFhrUGNuTGptOWRYeVNWcDl3cSt6dytCZGdtdThKWHc0TnltR3RaUXJoZGtNZXM3cVBlM2grdVlGRU4iLCJtYWMiOiIzYWIyMjAxNjAwZmViNDExODQ2NDIzZDM4ZmI3ZmVjNTgwZGY1MjU4NTk5NTlhMmUzNTFkOTA2NTBlOTcxMDVjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:02:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJ4ZHNDL3pQdlgzUjNVd2ZKRmkwZmc9PSIsInZhbHVlIjoiUTZYaGNLWDBIa3hsS0tCTG8xalZHWHJzdGV0dUc5WjV1QWlQQ0tNWDVGNzQ0a0tyTUwvelliNWhNaG1nVENSZWdmaUlaelovUUlNaS9Pd2RzNWViYVBnenk5WkFFQmp6YnA5L3Z0bmVLalVhSW5seEN5cmJMb0V5Yjk3RE9VM3BZMzBTU204U2h2eEJiT2kxS0gvcnQ0LzYvM1NVRDhQRktSWEYxL2VrVW96c1BBZUVzdkxGanBicFhOWTNzaDA3bmhXbVhKTVo1RzNiS3VmWSttTzU1UlY2eDRPK09LMlJSM0dhbmdkNVVIRmd5aDM5MUVHTGE1YjVjT1NGKzQ4L0dOYUpvWFI0QmFCc0d0QWlaOG9ZaEtVTXZTdkR4bFNWV1cycWV4eGkyd2t3V09WSnJWb3VjQWpjd0czYlBWK25DeHRBcEx3dGdEZmtVT0ViVXhSS2sxQjB5blRpUnlTYTV0Wk1HcGlCdG5kMmp1MDNVclhWNlBrZlgxcnVUazhyU1RXclo2WnZqMUtRaDBGTkFLUGVlK0VJRjArRmROMkZCa0dGeVJiWEozOXVubUx6aDVDNGdXTGFjcXlZWTBONnQ2TzBiaTdjWXlhOW5pemVZdFZRZ04rcGE1RldpQ29TOHkvcVNEdDM3aFRTV2tnWFlJcGZ0KzV5eXRrR3RzRDUiLCJtYWMiOiI4NjU1MzlmMmFiNWFiZTA4OGM0ZjUxOWJmZTk0NGY3YTQxYjZiMTRlMTc4OThmNzY0YjU5ZDBhYTZmMGU1NzRlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:02:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRPLzEvNWhXWUI0K2N2THJEb0kvWUE9PSIsInZhbHVlIjoiY09zRXNBNk9paGF3eVJrcjhSbGh4d2hiWXlzN0VBQXV2WEc4Vit1RDJmVFpYbnEyWStHd3RGc3FtTzNjZ2dHbDFqaEx2c1hDM0VUN2VKbzRxOTdINEFLbmgveU03OTNGMnh2R1l5dllMTU81ZExydEg3c3YzdFUxbExMK09SMkk4bkUxWm5KeWFyb0VPTjV4aWtYT3N2R3hlRWl5L1E1bURlTkVKU1Q3dUQyZFN1SmFlRnhKMnRGSXNkcXdqOWZWd0EwbmxrMHFhVTU1TW9vTFJ2OFRYS3UrTThLMHZuMmw3QTJlbDFJV2pKY28wTDVTR1Z3Tkh6QWJicWZPUno5cGQvM1VScGpOeXVSb3dPdCs0K0hoR2dwSXJMTnlhSW8xWW05c3BSVXRaNUVpQVB6cnczYkdWa0ZBNkVIbGlZVjV5UHRSdjZlWXgzd0hWVU94czBrTm5sN0lGb2w2aWJqSWc0TWdLc3VlTWR2NjA4eTVReityRjQrbXU3UUprNFVLYmE3NjgxOXlMaGlMaXZYSGJmcWlPR1prc0Nha2hGYSttamszbEcySzZCb0s3UlUxUFBjRWs0YzRmeDNsempJUERUSGxidFhrUGNuTGptOWRYeVNWcDl3cSt6dytCZGdtdThKWHc0TnltR3RaUXJoZGtNZXM3cVBlM2grdVlGRU4iLCJtYWMiOiIzYWIyMjAxNjAwZmViNDExODQ2NDIzZDM4ZmI3ZmVjNTgwZGY1MjU4NTk5NTlhMmUzNTFkOTA2NTBlOTcxMDVjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:02:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23243511\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-57220233 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57220233\", {\"maxDepth\":0})</script>\n"}}