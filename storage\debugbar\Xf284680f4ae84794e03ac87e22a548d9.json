{"__meta": {"id": "Xf284680f4ae84794e03ac87e22a548d9", "datetime": "2025-06-28 16:35:13", "utime": **********.594213, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.183911, "end": **********.594228, "duration": 0.41031694412231445, "duration_str": "410ms", "measures": [{"label": "Booting", "start": **********.183911, "relative_start": 0, "end": **********.542217, "relative_end": **********.542217, "duration": 0.3583059310913086, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.542225, "relative_start": 0.35831379890441895, "end": **********.594231, "relative_end": 2.86102294921875e-06, "duration": 0.05200600624084473, "duration_str": "52.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45728944, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0028499999999999997, "accumulated_duration_str": "2.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.569546, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.526}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.579666, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.526, "width_percent": 14.035}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5849552, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.561, "width_percent": 15.439}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1800331381 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1800331381\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1607928689 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607928689\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128497517%7C54%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFsblZvOWRzY08rRjU0ei8yYkw4R0E9PSIsInZhbHVlIjoiTTRvYllQaCtpbEZacnFuOTB1V0xBVzBWT2ZTQzcyT3RUTjEzSHRkV2pXODlDRDdMY3A0Q0tnNGFwNlZES3cvMlk0K2hDK29HY0s3Q21uZDBYV0E5UW9TVjYrS0ovYXdiSzdxYjNqdi9xT2VkcnpaNWppeFVMTTJTalR0SmZMZyt5Tmd3Rk9Wb3NRQnlmVExYdHRHaUVFMURSVlBUdU1valI5NWlZK2JMM1AxZnRNQW1IR216TDV2RkVyWDZYcWI5RWRZNVVDeGJuQ05OYzBiTGhFZVRrVTIvUXpMajV5OWluOVovSGZmd0w4UTRnYUgzS0g0WU1yR0RBOEpHZUZ5L2NpeVBpejdGZU9TMFpSSTJZU3MyYXVsUlMyZ1JyaUl5dFVFUVZUb0YxVDBmYTBhZEdLekVXK0E1TkZ0a2RXTUtoclE2M2h1TkxMbHd4Tmw4ZFJ6ZGpSeEhlc0JqdUdXT0FmMk05a0N2NXB1UVgvR1lGMm9zQUJQeWtQNEo3NTBWdEpqRmZrbmU4bER1NkJzbjlDSnNNZ3lsNC83aEJlbFQxTUJyWTArUTkvK0tvVnRIYmFqamVablBpMFhXckIzMkhiT212eDFtUW41UGsrbXZ1NjdqcXhWSnRjMzVBU20vaFZ4SGNRbmpwbXk5cXFNZEFKZEt0TmlveEdEWXZJWm8iLCJtYWMiOiI4NzhiMDgzNGNmN2FhY2UzNTlmMTFiNTViNjVmZDY5NjM3MTY3NTMyYmE2ZmQzZGYzOTdkZjRmMzNlMjQwMTBhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdvbytCT0xsbzBCRmUyUGVURnR1Mmc9PSIsInZhbHVlIjoiNEhHUW8vU2ZqeVBORCtGSU1jRzJuQ0VlNmdxNVJWRHJxR2xrSHFIYTYyYkYzQ0NuNkhVWFBSa2drYWJMeVZyUi9MWUczOEpOUzJLSStCaFNqVU9hR2NOTmdkNjNIc0lMZzFjSGlzWXplK0d0YU5qemQ5aUJOOE9zcjVFMnJRSHU3Y2szaFNCRFRtN28vdDZ1TFVLRmpQcW9RakxtcGw1b2VMZlQ4RCtpMUpVa3RxaVp4K1lOdFg2U2xzVVkvSjlrcDZJY1FTTWdJMlJOWVlpVWNxRVo4aVRBSDMrb1RTaXEwRDRIZDJOZ0t1YzAxQ0dGMG9YODFBdGVzS045S2czUHROK0NLU1AwWDJCekJQYlplRU1yNzEwNW0yVEZmYmRoQzNoeThDekNkL1IwQ0VHdnN3d29HaklnV2lWeHdTS1JWbzQ4ZGdyNEhYVHpBa3NQaDExT0hObVUydmRuSzZTZ1l0U2l5SjJVUHBvUmRHZDJSV1lJZHZQTVZsWWtPNW1Xbm9KNXhIdU5PM2FqVzV0ZnhEYmY5TTIzWFZ6Vi94ZFRZa3ZZYUpORzdFa09pSXFBSWFEMTRnejBFOWx5THZFWUdVU3Z2UkNtaXBvcUFsR1lEeUpZWklYODdHc3RsNGFkMEdIUzhFT28yU2l1Y3F2c1VrVzA3MW14ekZiZGtBVVciLCJtYWMiOiJmYWE2MjljN2RkMDVhNzcwZmVjYzVjYTM3ODM2ODZjNDgwZGQ1N2M2NmYwYmI2YTI0ZGY1YTRjOTY1YTk5NDViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-48325096 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-48325096\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2113362078 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:35:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpoWE5FSW9iWUMyVjBMR1c5b0xGVEE9PSIsInZhbHVlIjoiRW1UaWdTbWVuNStsS3ozbnB4Tm9ZT0syN3Bqb2w2QXE1VEpQL1hFRkFWRmNsQ05Bc01OTUFzSDIwUTBhZmlxNGI1RGtjMVVBbG5yY2tDV3dQVGczWXNBS2Ntd2NTeXkzZk03bmMzcUNHUlArV1Z1MWoxYmRYVHJMTUtNRFFEMWRlN3ZCVkRaQituYjdvUUFmVUVZMVNuRWxLN3VFRjdTMG1JeURPL1MwelBZMThlbkpmKzQ3V09wNTZ5VGVLcVpYcXRIZ05jRE9sR0dweG5adlp4c1p3ZFlqdUxJM3RTZWMwbDQrWDdMMk5EbUtKU05KMDFwZEZ0bUdHbVBoVUdpQ1JFckJFbS93UVRLUmlhakdHRXJtTTBLL1VWZTVLWXRVUUsrbE5JSjlpZ01qSmN5WjY0TlVPU2JzY0s4WWs0SVpWYnNsTHdiQ0NWeGtidGxPaSs2ZXBTNERXUEczSWFpNzRWbWUxQmkzM2kvTjQ0QnlqRWh4d3lYc2s2eDdBdDMyT2I3SzM0TFdsU0toVGRJTFJyRCtvQ0JuOHFONjJCNitNWEpwYy9waDF6MHFnaUpQSzl5ZGZVVGt1c0ZsU0J6azVPRVRnWjFEOEpHOS9rV0V6d1gxMERqdzhNNEZoa2tzWVRNV3JjODJnWFNkVlZFdmI4Q3lSZ2NCUk10YXQ2NmsiLCJtYWMiOiJkZTFiY2Q3YjE1MTQzYzdiNDFiMWNmYWRiYWI0MjNiNDhmMzY0MDQxMDliYzA3ZWJjNjhiYjFiMTQzMGFlYTE5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpsS25obUJlcktYM0lUL0pXL0lLV2c9PSIsInZhbHVlIjoiOGg3b1poQi9paHhFaXVsVWlpNC9iZXE5ZHV1WFo5VFczYXlMQWJNY2F0MlhacUhQZ25tcHR0aVQwQnQzWU05dTF2NmljTzE1U3hMem1HNzV1RXhkWFZLeVlaTmpSS2VPa2tJem9pKzNoc2V6Qk4xNFBkWW12d1l1VWhldjZQSFMwZEpmZUE5MW1uWFNlSkZRejhpZjlFZWFWSEU5TFM1WlFBRFVvM0FRK01zUTNRV2pKRkJ5YWJjbjRlN0ZGRHQzWWJZQ2pJY1d2VmNqZ3M2dGVZSjJXdTR4QlZuWXEvZVJ2TDFVdHIvUFVoY3JsZUt4aStXWVFtbHlvdURnMWlQQ2pzZTFiSnFic2VmYjdIZUxBVmxaNFdqckw4T3ZUeEpocjdYd3AzdUs3MDR3c0tCUFZNYTNxRnF6a1RzRnpsTU9CaTZZMExYMnJYWjErYW5LckZPLzlzTzNiZmh4cFZvc290Q2JlcEp2UW41SDhRYXVrdEZIdHlxMGx5SnIwR0lPVUZiaTFmdUFQemMxd2F1eE9XZ1ZnOFNpeWRZelErNkhVTXZaZDVyZ3FUWEZZSWVBRGRPUGY3ZENIekxIUW5jblNLZ1NFSGVrM1B0QThyTFdYVkJqY1hQOU1NUTZkSm9oUWJrTWJHTDczNGhpSGVIZk5DdTc2em5FZVhSUGFncDUiLCJtYWMiOiI2NDgzNjY4NWNmYWNkZDhmZjJlNDdmNjkyZWE2M2Q2ZjUwOGU1MzYwMmIzMjAzNGJkMmFlY2IwZGIxMmJhY2JiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpoWE5FSW9iWUMyVjBMR1c5b0xGVEE9PSIsInZhbHVlIjoiRW1UaWdTbWVuNStsS3ozbnB4Tm9ZT0syN3Bqb2w2QXE1VEpQL1hFRkFWRmNsQ05Bc01OTUFzSDIwUTBhZmlxNGI1RGtjMVVBbG5yY2tDV3dQVGczWXNBS2Ntd2NTeXkzZk03bmMzcUNHUlArV1Z1MWoxYmRYVHJMTUtNRFFEMWRlN3ZCVkRaQituYjdvUUFmVUVZMVNuRWxLN3VFRjdTMG1JeURPL1MwelBZMThlbkpmKzQ3V09wNTZ5VGVLcVpYcXRIZ05jRE9sR0dweG5adlp4c1p3ZFlqdUxJM3RTZWMwbDQrWDdMMk5EbUtKU05KMDFwZEZ0bUdHbVBoVUdpQ1JFckJFbS93UVRLUmlhakdHRXJtTTBLL1VWZTVLWXRVUUsrbE5JSjlpZ01qSmN5WjY0TlVPU2JzY0s4WWs0SVpWYnNsTHdiQ0NWeGtidGxPaSs2ZXBTNERXUEczSWFpNzRWbWUxQmkzM2kvTjQ0QnlqRWh4d3lYc2s2eDdBdDMyT2I3SzM0TFdsU0toVGRJTFJyRCtvQ0JuOHFONjJCNitNWEpwYy9waDF6MHFnaUpQSzl5ZGZVVGt1c0ZsU0J6azVPRVRnWjFEOEpHOS9rV0V6d1gxMERqdzhNNEZoa2tzWVRNV3JjODJnWFNkVlZFdmI4Q3lSZ2NCUk10YXQ2NmsiLCJtYWMiOiJkZTFiY2Q3YjE1MTQzYzdiNDFiMWNmYWRiYWI0MjNiNDhmMzY0MDQxMDliYzA3ZWJjNjhiYjFiMTQzMGFlYTE5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpsS25obUJlcktYM0lUL0pXL0lLV2c9PSIsInZhbHVlIjoiOGg3b1poQi9paHhFaXVsVWlpNC9iZXE5ZHV1WFo5VFczYXlMQWJNY2F0MlhacUhQZ25tcHR0aVQwQnQzWU05dTF2NmljTzE1U3hMem1HNzV1RXhkWFZLeVlaTmpSS2VPa2tJem9pKzNoc2V6Qk4xNFBkWW12d1l1VWhldjZQSFMwZEpmZUE5MW1uWFNlSkZRejhpZjlFZWFWSEU5TFM1WlFBRFVvM0FRK01zUTNRV2pKRkJ5YWJjbjRlN0ZGRHQzWWJZQ2pJY1d2VmNqZ3M2dGVZSjJXdTR4QlZuWXEvZVJ2TDFVdHIvUFVoY3JsZUt4aStXWVFtbHlvdURnMWlQQ2pzZTFiSnFic2VmYjdIZUxBVmxaNFdqckw4T3ZUeEpocjdYd3AzdUs3MDR3c0tCUFZNYTNxRnF6a1RzRnpsTU9CaTZZMExYMnJYWjErYW5LckZPLzlzTzNiZmh4cFZvc290Q2JlcEp2UW41SDhRYXVrdEZIdHlxMGx5SnIwR0lPVUZiaTFmdUFQemMxd2F1eE9XZ1ZnOFNpeWRZelErNkhVTXZaZDVyZ3FUWEZZSWVBRGRPUGY3ZENIekxIUW5jblNLZ1NFSGVrM1B0QThyTFdYVkJqY1hQOU1NUTZkSm9oUWJrTWJHTDczNGhpSGVIZk5DdTc2em5FZVhSUGFncDUiLCJtYWMiOiI2NDgzNjY4NWNmYWNkZDhmZjJlNDdmNjkyZWE2M2Q2ZjUwOGU1MzYwMmIzMjAzNGJkMmFlY2IwZGIxMmJhY2JiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2113362078\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}