{"__meta": {"id": "Xa810d86b6c0941a9ce22be18c8216a59", "datetime": "2025-06-28 16:01:36", "utime": **********.199194, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[16:01:36] LOG.info: Enhanced POS Add to Cart Request: {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"price\": 2.99,\n    \"name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\",\n    \"is_manual\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.189205, "xdebug_link": null, "collector": "log"}, {"message": "[16:01:36] LOG.info: Manual product check passed {\n    \"is_manual_value\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.189439, "xdebug_link": null, "collector": "log"}, {"message": "[16:01:36] LOG.info: Product found successfully {\n    \"product_id\": \"2299\",\n    \"product_name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.191901, "xdebug_link": null, "collector": "log"}, {"message": "[16:01:36] LOG.info: Product added to Enhanced POS cart successfully {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"cart_count\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.192028, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751126495.765888, "end": **********.199214, "duration": 0.43332600593566895, "duration_str": "433ms", "measures": [{"label": "Booting", "start": 1751126495.765888, "relative_start": 0, "end": **********.1434, "relative_end": **********.1434, "duration": 0.37751197814941406, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.14341, "relative_start": 0.37752199172973633, "end": **********.199218, "relative_end": 4.0531158447265625e-06, "duration": 0.055808067321777344, "duration_str": "55.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46007360, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2015\" onclick=\"\">app/Http/Controllers/PosController.php:2015-2120</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00238, "accumulated_duration_str": "2.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.176661, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.647}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.186063, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.647, "width_percent": 13.866}, {"sql": "select * from `product_services` where `product_services`.`id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2053}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1902082, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2053", "source": "app/Http/Controllers/PosController.php:2053", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2053", "ajax": false, "filename": "PosController.php", "line": "2053"}, "connection": "kdmkjkqknb", "start_percent": 81.513, "width_percent": 18.487}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-2132899906 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2132899906\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFOdE9hQU5NYlpGaEd5eHo2L2c1eXc9PSIsInZhbHVlIjoiaG4yWjJ1c01HVGpndWRUeFpvQVk5OEFzcEZIcGtad1A1TVFhYzR1U3JVWHkvNDI0RUVjVUd4WlpHK3piU1dzUkQ4cStaQjQ1Ymd3ZkhHMzRuSkl3WWFMazdobEdISHhja3hUb2g5SDdzbGxoT055eExiZHpxekM1M01tUFc0cHViMWdrZTl1d0lWZUZKK0FVMWtJVHJYOUt5Y3IyMjBjVUszT3VjZ1d6TWFvK05USTZVaVBGOS9hUFZOR2tWZFhlZGl2ZG1WaE5oY3RhVGQ2VWNHWE9Cc0VGazVwRFBNSkttcC9QNXZMVDVHRmpPYlpEeERaU3pCUlJSdjlzcTY2WlhCZGM3bmx3REE1VURaRkorNUJKQkgvVDJHT0E2c0EySExZOEhsbHVoVktnSjZST3Vqd1JjY2k4QUhBVjBydFJLb0t5S1RqSnZOWkVrSmhiTHpuVk5FN2g0RjhpdkxKbWN2Q05rZnlEcXNSaWgvVklhblZ1SmRvZTJVY3ZMM1NZZlhFZnZPVnd1VjNWdkw1T2RDdmpEREo0ejF2c2RDTytpTUlvKzlEU0tuVjVwL2F5NnEvS3lUaGNhRnFkL2RwMFNieFZ4NEl5azB2YkVCTjZtRllsTDN0Mk9za3BoNnFyL3FpTzZUNnE4Q2QwVGxxMWNIMm90bGg4ZTN4V3dGN00iLCJtYWMiOiJiNzYwY2Q1NWM4ZGRmOTkzN2YxNDM1ZmVlMDg4ZWYwM2I2Mjc0MDgwMjViMmUxYzY3ZTc1ZWQxNjNhNjlkYzE2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilc1a1R5NVRURER0YndPR2dPdm5jRXc9PSIsInZhbHVlIjoiRG9ONDRudUNaQUdUSHNFL2pkVDR2cUxYMU8zbE80T0haZXM5S2FrbWdyQjJvekR0RlZvYkptZzRwMjBUNTdQMEtQTmhkQmp2N2RUdmNKZHJMUFpFMVpEaUJDMVdmekRzOWJXYTJqNnE1d0FRVFJLMGhleDMvNkhyUzBTZ2diREhHMk1iMnlzRVB0bkNDTWtLSGxqR1BweWM4VFMzcHVuSlFEZVFNWGJBdzZiMTJ3dkVpZ3V4b3VkcWgvVGtjeEs5eEFwdHR3VEtPMk9icEhQQXlqblJVbmZlYlE1dTR2NHNGLzM5bDhja0FSS2ZLbHlRaVhENHF6MzgvZFJYS25GbmxuYkFMbHplT0xkdmJDZWRkdW9ldTBsWTFnajVNMjBqaDVCa0czOWhtRXZaNnFORGNNaC82MG95ajNpWUk4NldyNlBCSjdGMCsxREt4WmhkbmFHYnVzN2ZpNm9ManJ3ejlxc2lON0R5a0RLRFRJQzZldXA4VVZPeWpjVWg0cmR4T3RydGdteU1PVklvMUkyTFVyZTIzSWJQUktBMVFmdEdEczRkVk85S0RtaG40VVlwUnVVTHlSMVIxa2x2NHp4c1ZHOTZmQjJBcjZNcEozZi9jVVdaMDdZeUFFMDV5TnE0eWVFZHQwaWVlTWtjeCtDZVNEakMrYTNHbERKR3Z0MjMiLCJtYWMiOiIzMDI5OGE5NGMzNzczZjBhZTZmMjAzY2U4MDljMzczNzYxOWRiYzI3NzRkYTBmOTk3MGFjMDI2M2RiOWFkZTQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-569575049 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlB2akt6MDRacE9OMFFmVW5VVlgybmc9PSIsInZhbHVlIjoiZ0IxTE13eTRITHNUL28xQ2dodldneXFzMm5DZXZjUXVHWkxHbGt0T1VuT0tnaHFDL0t3dGxxTHRqRHNhTWc3Mk0zTHo4ckhoQldOTFUvaVhwQVd0ZGlSd2hYc0JQK3BxUGN1NHRsaFZyeW9PbC9YZXdVOWdqZVlOdHgzMC96a0JvdWswc1FoYkl6ZFQvTEdpbllBZ3JwWU9sMjBxb1h3VXRtTnhvemJIL2RvNW11Z2V6VHpJdXpTZGVSOXBrbWxRZDhSNVB4bEltNnY0bWxsUWZsc25kOEJyRFFBMG1tanRTTDI0SWpxMDVUOU55cXpwN3A3SDlsc3NyUmdINmptUkl1YnJmdmFYUEg5K2M1ZnRFR1JvR0J6TGZoRVErZ2ZkRmJJRkJHUGdkeWJSTW40cms1blZOOVpmRnRGYm9ndTFaZ01LUEhlZ1hrbjFUbnJFY3Nib093THNJcE9mcnluejB5cU9NYVplNmRKRDZNNUd1K1NwOGlVbTRiY3hab3cxTDN6YkcxSVBQQWZoMTE1aWRRU01Oa2Q2ZGJKaE0zcU1XcGZCbVZ3Y3duVVVwNUFhQmhuK3EvMWZYZUdPOGdhaC9oY0RFb1RkMjB3Z05MZkk5Si8zN3FKZnpkcm1nYTVJblNkOFhPbVZuSVFpeWM1Z2JGTFI1OHFMbTEwWVpMYWYiLCJtYWMiOiIzNGJiOWUzZDhhZTA0ZGVhOTA4ZDhjZmNiNzI2ODVkYmEyNDhlNWQxYWQyOGQwOGZhMmUxMWU0YTU4MWMxNmJjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBuV0dHRU9pbXQ2NnFTbUdRNTlmS3c9PSIsInZhbHVlIjoiUENBVVI5anBUZUtBWkNsdVJCWWI3R1djcGNsSzJmWW9MTXVrM3pIVnhMamluVjUvV1FFbEpXdEVpQWNTK0draFoxTHVva3llS1preDhxbkhHKzFzZFpqOGM5V1FWTEJkNG5pUXI0TUNKbEhqY0h6UnpBaGhzbC9nb1FBUXVRTWdHTVVRem9OVTJJQVFnVjZnUERPaXFvNG1tRmNlMlJ0ZXRaWThobTJmaWJKSlJuWmxoZzUrQTRkekt2Umk3SmpyRGlhNzRibWtpNkQvN1hKK29WNEh4anAzQjVnMUxLdzZlRkplaFlkZVVHZkJGM2ZOa1hURTVLQ2FEd3hSaHFIdXVUYm1GVEc5cFY4Y1NiTjFKc1M3MUovWVBJZDJYNUZjVmZnUjZxa1hhUEZITzhLQndBRzBCTTc5T3R0UWpzbk5VMEdZRC85TXVnellSWkJYUThsb3MwQjlyZ1JBT3d4blovYXpSaFp4Vkp6cjVhMGxZb0Z3Vmt5RDdIdEpBSVRDS0ZIVlRhQk9rZ0g2TkNBQ2ZuQVBIY29rK2tXK2gwc0FkVVdUY2FOR3NuUVlLVHI1N0l5bnRkT2o0Z2lQb3AzOTRyMkViNWhleGNPRmQ2M2dsUThEOWhjRCs4Y01jbWF5dWFKMTZDQ1RqemwrU0JDSm5ZUS9UQUhScjM2UGh3cysiLCJtYWMiOiI1OTQyZTNmNzkxYjU1OGVhYjRmODQ2NWM4ZmM0M2RlM2IzN2UxOTI1YzAxNTI1NWVlNDkyYTdmOTA4OTcyZmNiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlB2akt6MDRacE9OMFFmVW5VVlgybmc9PSIsInZhbHVlIjoiZ0IxTE13eTRITHNUL28xQ2dodldneXFzMm5DZXZjUXVHWkxHbGt0T1VuT0tnaHFDL0t3dGxxTHRqRHNhTWc3Mk0zTHo4ckhoQldOTFUvaVhwQVd0ZGlSd2hYc0JQK3BxUGN1NHRsaFZyeW9PbC9YZXdVOWdqZVlOdHgzMC96a0JvdWswc1FoYkl6ZFQvTEdpbllBZ3JwWU9sMjBxb1h3VXRtTnhvemJIL2RvNW11Z2V6VHpJdXpTZGVSOXBrbWxRZDhSNVB4bEltNnY0bWxsUWZsc25kOEJyRFFBMG1tanRTTDI0SWpxMDVUOU55cXpwN3A3SDlsc3NyUmdINmptUkl1YnJmdmFYUEg5K2M1ZnRFR1JvR0J6TGZoRVErZ2ZkRmJJRkJHUGdkeWJSTW40cms1blZOOVpmRnRGYm9ndTFaZ01LUEhlZ1hrbjFUbnJFY3Nib093THNJcE9mcnluejB5cU9NYVplNmRKRDZNNUd1K1NwOGlVbTRiY3hab3cxTDN6YkcxSVBQQWZoMTE1aWRRU01Oa2Q2ZGJKaE0zcU1XcGZCbVZ3Y3duVVVwNUFhQmhuK3EvMWZYZUdPOGdhaC9oY0RFb1RkMjB3Z05MZkk5Si8zN3FKZnpkcm1nYTVJblNkOFhPbVZuSVFpeWM1Z2JGTFI1OHFMbTEwWVpMYWYiLCJtYWMiOiIzNGJiOWUzZDhhZTA0ZGVhOTA4ZDhjZmNiNzI2ODVkYmEyNDhlNWQxYWQyOGQwOGZhMmUxMWU0YTU4MWMxNmJjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBuV0dHRU9pbXQ2NnFTbUdRNTlmS3c9PSIsInZhbHVlIjoiUENBVVI5anBUZUtBWkNsdVJCWWI3R1djcGNsSzJmWW9MTXVrM3pIVnhMamluVjUvV1FFbEpXdEVpQWNTK0draFoxTHVva3llS1preDhxbkhHKzFzZFpqOGM5V1FWTEJkNG5pUXI0TUNKbEhqY0h6UnpBaGhzbC9nb1FBUXVRTWdHTVVRem9OVTJJQVFnVjZnUERPaXFvNG1tRmNlMlJ0ZXRaWThobTJmaWJKSlJuWmxoZzUrQTRkekt2Umk3SmpyRGlhNzRibWtpNkQvN1hKK29WNEh4anAzQjVnMUxLdzZlRkplaFlkZVVHZkJGM2ZOa1hURTVLQ2FEd3hSaHFIdXVUYm1GVEc5cFY4Y1NiTjFKc1M3MUovWVBJZDJYNUZjVmZnUjZxa1hhUEZITzhLQndBRzBCTTc5T3R0UWpzbk5VMEdZRC85TXVnellSWkJYUThsb3MwQjlyZ1JBT3d4blovYXpSaFp4Vkp6cjVhMGxZb0Z3Vmt5RDdIdEpBSVRDS0ZIVlRhQk9rZ0g2TkNBQ2ZuQVBIY29rK2tXK2gwc0FkVVdUY2FOR3NuUVlLVHI1N0l5bnRkT2o0Z2lQb3AzOTRyMkViNWhleGNPRmQ2M2dsUThEOWhjRCs4Y01jbWF5dWFKMTZDQ1RqemwrU0JDSm5ZUS9UQUhScjM2UGh3cysiLCJtYWMiOiI1OTQyZTNmNzkxYjU1OGVhYjRmODQ2NWM4ZmM0M2RlM2IzN2UxOTI1YzAxNTI1NWVlNDkyYTdmOTA4OTcyZmNiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569575049\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1003994145 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003994145\", {\"maxDepth\":0})</script>\n"}}