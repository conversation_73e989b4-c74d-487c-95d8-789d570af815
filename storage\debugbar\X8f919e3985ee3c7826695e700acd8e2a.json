{"__meta": {"id": "X8f919e3985ee3c7826695e700acd8e2a", "datetime": "2025-06-28 15:04:36", "utime": **********.152991, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751123075.749197, "end": **********.153004, "duration": 0.4038069248199463, "duration_str": "404ms", "measures": [{"label": "Booting", "start": 1751123075.749197, "relative_start": 0, "end": **********.073074, "relative_end": **********.073074, "duration": 0.32387709617614746, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.073083, "relative_start": 0.3238859176635742, "end": **********.153005, "relative_end": 9.5367431640625e-07, "duration": 0.07992196083068848, "duration_str": "79.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49496056, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02955, "accumulated_duration_str": "29.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.104685, "duration": 0.01326, "duration_str": "13.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 44.873}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.125396, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 44.873, "width_percent": 0.948}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nAND p.warehouse_id = 8\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`warehouse_id` = '8' order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1278589, "duration": 0.01601, "duration_str": "16.01ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 45.821, "width_percent": 54.179}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-1041801390 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1041801390\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-851822295 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851822295\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1038913451 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1038913451\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1956266842 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123070655%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik01QUJOSDNIaHovWi8vTFN6UU45T2c9PSIsInZhbHVlIjoiUEE2bnh6dWMyR0hJVXFxTTlIUklQVjlHZkxiYk1RbUwyd3ZMQ2RJU3p4WExhRitqYXBkdWZ6eXZIWEMrQWJwVnQ1WG9VcHJsUkFzd3lkcjJFVXN6YW9QZjc0MzViYU5UY293OW1xdDN1UHlwbTBXNVRCbHNSVjN0YmVmck1WWU9DQmpzUUhDVnNQMUtJQnpQMjNtZytSdHJhbVh1Q3lrRnZnd3ltMWZTUHZESVpvejZEZDUzVDdSaWVRYVlaTjI1ZmVzWUxVNUxQQitkODk2MVVjcDFEZ2xOWExRbjdGNktxWTEwYmFvMXhoYm5rR05HRVZGek1zSmFTK0FxYmY3TVJGRHBJRFJqMWRoQ01Nell0amFKamo0Y1laZEt4NWp5U2d5b2tsZldmMk10cHExamVXVUdtbEhBQlp6dmR6OCtJU3gydlhoNU8rT1RESEZJTm05cktpTDBmRkgxWk1Jc2dDdE1NcURpbHlrV2VBUkJROXJudThBcjd6QXA0TEZkKzk5QnYvQTNTbXZIUzVSRkIzaG1qdFJ2SmZNemthQUVMdGdzT05MRjZkbWs5TWxRN1ArRkhNVDZWeVlMOGVmYkwwSnEwWnpTbFFpbDdsU3JNZUhFTUJ0ZndwanErVG9sNjkwdWNMRGlhQk44STE3ZEZWZ2hsUmcxNDErSHZrdW8iLCJtYWMiOiJhNzkzNTkyOWFjZTZkYTc1NjM0YmRjNGY4OGE3M2E4YzlmMTRjY2RjZTAzNDgwMzhkMjEwNzZmMWZmOGZiMzljIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBaSTA1L0QwaXUxbzlxMmhXTDF5Unc9PSIsInZhbHVlIjoiR0Zja0hUQkhsZlhMdmg4S0dyVDd6OHFsSnlTbCtZeXFYemFYdlBVRFpFRW16Q3ZKbGdzV2NPNmNjaVR5UUJHclNxMmdVc3JVRmtPRkcwUklNSkxDRmJlaytHRmUycldWZ29BalZQODRQTGNibkViUXR3aUkvVm1UMlBCQTh5OWFVRWpYVVdlbnhOdG55RnFZYWw3THRZeTBJODhEWUQvU2hTQUt2cHpVY3FoMkkvRGV0MjZMWlZtMlFFc3dDK2FiYk5MNWNyUE9rTk4vZC9JYndEbnRpell5VTBnSmVmOEVPZVNma2R6RzZqSlNTMmx6WU51THZkajFxdjNJMDdNWDlTcUlJNXlrbVROcmZaNTZsQnlvNDBERWRyNGFJMGNXRTdVVGNBNjVHVUtFUzhocU9ZbkZ4NFJvTG5WTFhzU243YmRpa0I3YW9jVDgxSXVqOXFaRnQ4UytwR0xtczliZWpRY2hXcWhiT21TdHBiWVFvSk1SMEI0M0M5T01RU1hJbWVOSkNMaE5zajlJY0pySTBnVTJSVUl5QXZJZkFISTdTR3lkWmt6SHYrTTBia3FrV3gvTHZ1NHJuQVp3cC85M1VBSEtqRjVXSHY0bFpuNm1EK2NicStkbUc0U0R1K2huT1JTaDVlVGVxcDBCelo3RFlpemc5OUxjbUJGNFlsc2IiLCJtYWMiOiJmMTJkNTkxZWRiMjdlZjIyYmI2YWUyNWY3ZjdiZjAzODEwY2RhODU5NzA5Njc5NWQzZjkwZGNiYTBhOGU0Mjc5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1956266842\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-344216405 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344216405\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2025849891 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:04:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iks1eERzQjJjN3RlT1dtNGhXQjFrdEE9PSIsInZhbHVlIjoic0JtMTlBVENZdXlZbUlDOUVvbE1QOE91c1JUVHozQkV3U0U4bGUyNmVtUVNmY1U0WWpOODJJU2lqbEttMkxnVE8zWWx0dnVxWW15VktNY0RNWjMzUzZwUS9FQ0E1aGJYYmhDZmwzMXZRejkvMFdqOCt5V0Y4ODdKNmxnVXNCRTQwNFh1WEZUUW9GSXJPYytJVjJmZ3VXZXRFd0tjeGtCeEo0RGdLWmRvNjFUR0hybllHaFp2U3JRVXB4ZHRTUElwWUlWSHRnM2dZODBaOHBuQWNUWFVab2NyamVwSjY1c0lON3h3ckxYc2hST1JzdDBjQ25FZHZhM2xFUmdwY0ZEd3Nvay9wN3lvTENLTjluQ2ZnbDBlbWRDam43YXhQZkhYOFVhVzFudE16aWFVZFUzQzUvTllQeVNTT3RaL1YwNk1aNE9xL1VNYk1SU3l6SFBQQkc2QitkNVhZUitITEI0UnQxaC9tbm4zNm1wRFozMEtUVW1xcXRxbmVKZFZVOVR5MVlDci9odjJaUjl6enMxTUZmc1pzbW0wQnhHWTdveFJwNDlyc3I3MHlyZ1FXSmlXeWs4L245aXY1S20wc0lndWJJdHhmK01RbHUreXYrSFZzYnV1OUp3cTM0YjB6N2JiMGhBR0Q2ZGN2R2lQQUZjdDM5S2ljUkpxUmgwY3IyREsiLCJtYWMiOiI0OGViNTJkYzJhY2VhYTcxMDAzM2YxY2U0ZmVhMWVjZTdlYTQ0MDIyMDI5MWM4OWI1NDA2NDE0NzBiNDU2NzU5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBtTWtjSUJYNWZPa0d2YW9GT0tadVE9PSIsInZhbHVlIjoiNkRZYmpDYzZDWnBwR0NnRFZ1OHhUQkM0cVQyNCtjM2F2TFZmWlRiV3VhZVZyOW9RTTJCQ3IrdTdJMmo3RUcvREZ6UmxoUGh2cDlNUUI1SW5aMGdRWkhGUnkxZW9sbTJGbGovd2NJRXROYVJGNDNIQWtEbG5sd2o5OUZKeDA3Y0p0QUUxZlE4bU4zVGR4UWZ2bU94eVFTcjBGQzdkditINHV1L21KVWlkUmJ6T2VYblBySHcrRUNxRWN5L3pCTXFQYUllcDNRNElSUEFZMTNoRFhGYXNNTEgwbzJ2WFJIMWJPNzJWZmw1dGdPWlRzWE52OW5HZmV3NEpJS3MzdkdzWjkyMnF0MkFJTmJhMXoxRW9iaHRjN3JJR01HNnpZUDdVL0xLeUZ4VlJmYitZUS9CVjhaSlZ0REFFMnZUUVJyTTBKNC9qNk5mOXZpbnVKWWNPTHhWWnNSd1JpUjQ0N3p6L3RTUmhHRWtkc2hYMHZVMDR2Z1pKRUtNbFA0TnkrVU1jcnpmd3ZVU2hzSVBVUE9jVTlFTnRFcDNZUUhNVlUrNExpa0dUaDJ6dDBBOUJ6Z0Qra1FTU2dsdGFBMnpLY3I3Y0pzVUZJam5GQ25FbG5pNmRieko2NGdENHhWN0prYVU1Tm9LZi9LQ3Z4Ym5iY2hJa2Rpem5DT29QdmIrZzJvcy8iLCJtYWMiOiI2Y2E0ZWFhM2ZmYjkxYzExNGIzNjMyNzYyYmNjMzZhZmQ3M2E1MTY3OTg5YmNhYTBlMDA4MWU4NGUwYTNkYzQ3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iks1eERzQjJjN3RlT1dtNGhXQjFrdEE9PSIsInZhbHVlIjoic0JtMTlBVENZdXlZbUlDOUVvbE1QOE91c1JUVHozQkV3U0U4bGUyNmVtUVNmY1U0WWpOODJJU2lqbEttMkxnVE8zWWx0dnVxWW15VktNY0RNWjMzUzZwUS9FQ0E1aGJYYmhDZmwzMXZRejkvMFdqOCt5V0Y4ODdKNmxnVXNCRTQwNFh1WEZUUW9GSXJPYytJVjJmZ3VXZXRFd0tjeGtCeEo0RGdLWmRvNjFUR0hybllHaFp2U3JRVXB4ZHRTUElwWUlWSHRnM2dZODBaOHBuQWNUWFVab2NyamVwSjY1c0lON3h3ckxYc2hST1JzdDBjQ25FZHZhM2xFUmdwY0ZEd3Nvay9wN3lvTENLTjluQ2ZnbDBlbWRDam43YXhQZkhYOFVhVzFudE16aWFVZFUzQzUvTllQeVNTT3RaL1YwNk1aNE9xL1VNYk1SU3l6SFBQQkc2QitkNVhZUitITEI0UnQxaC9tbm4zNm1wRFozMEtUVW1xcXRxbmVKZFZVOVR5MVlDci9odjJaUjl6enMxTUZmc1pzbW0wQnhHWTdveFJwNDlyc3I3MHlyZ1FXSmlXeWs4L245aXY1S20wc0lndWJJdHhmK01RbHUreXYrSFZzYnV1OUp3cTM0YjB6N2JiMGhBR0Q2ZGN2R2lQQUZjdDM5S2ljUkpxUmgwY3IyREsiLCJtYWMiOiI0OGViNTJkYzJhY2VhYTcxMDAzM2YxY2U0ZmVhMWVjZTdlYTQ0MDIyMDI5MWM4OWI1NDA2NDE0NzBiNDU2NzU5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBtTWtjSUJYNWZPa0d2YW9GT0tadVE9PSIsInZhbHVlIjoiNkRZYmpDYzZDWnBwR0NnRFZ1OHhUQkM0cVQyNCtjM2F2TFZmWlRiV3VhZVZyOW9RTTJCQ3IrdTdJMmo3RUcvREZ6UmxoUGh2cDlNUUI1SW5aMGdRWkhGUnkxZW9sbTJGbGovd2NJRXROYVJGNDNIQWtEbG5sd2o5OUZKeDA3Y0p0QUUxZlE4bU4zVGR4UWZ2bU94eVFTcjBGQzdkditINHV1L21KVWlkUmJ6T2VYblBySHcrRUNxRWN5L3pCTXFQYUllcDNRNElSUEFZMTNoRFhGYXNNTEgwbzJ2WFJIMWJPNzJWZmw1dGdPWlRzWE52OW5HZmV3NEpJS3MzdkdzWjkyMnF0MkFJTmJhMXoxRW9iaHRjN3JJR01HNnpZUDdVL0xLeUZ4VlJmYitZUS9CVjhaSlZ0REFFMnZUUVJyTTBKNC9qNk5mOXZpbnVKWWNPTHhWWnNSd1JpUjQ0N3p6L3RTUmhHRWtkc2hYMHZVMDR2Z1pKRUtNbFA0TnkrVU1jcnpmd3ZVU2hzSVBVUE9jVTlFTnRFcDNZUUhNVlUrNExpa0dUaDJ6dDBBOUJ6Z0Qra1FTU2dsdGFBMnpLY3I3Y0pzVUZJam5GQ25FbG5pNmRieko2NGdENHhWN0prYVU1Tm9LZi9LQ3Z4Ym5iY2hJa2Rpem5DT29QdmIrZzJvcy8iLCJtYWMiOiI2Y2E0ZWFhM2ZmYjkxYzExNGIzNjMyNzYyYmNjMzZhZmQ3M2E1MTY3OTg5YmNhYTBlMDA4MWU4NGUwYTNkYzQ3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025849891\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1552485264 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552485264\", {\"maxDepth\":0})</script>\n"}}