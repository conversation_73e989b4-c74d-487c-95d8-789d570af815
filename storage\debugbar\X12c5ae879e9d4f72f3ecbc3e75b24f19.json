{"__meta": {"id": "X12c5ae879e9d4f72f3ecbc3e75b24f19", "datetime": "2025-06-28 15:02:51", "utime": **********.808774, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.327767, "end": **********.808817, "duration": 0.4810500144958496, "duration_str": "481ms", "measures": [{"label": "Booting", "start": **********.327767, "relative_start": 0, "end": **********.747503, "relative_end": **********.747503, "duration": 0.4197361469268799, "duration_str": "420ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.747517, "relative_start": 0.4197502136230469, "end": **********.808823, "relative_end": 6.198883056640625e-06, "duration": 0.061305999755859375, "duration_str": "61.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45068624, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00261, "accumulated_duration_str": "2.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.783143, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 74.33}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.793484, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 74.33, "width_percent": 12.644}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.799223, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.973, "width_percent": 13.027}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1908565531 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1908565531\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-625340656 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-625340656\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-636961559 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636961559\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-389226453 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122962998%7C6%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxEQjNvai9FRlNMcUgxbDJ6RjdNZmc9PSIsInZhbHVlIjoic1RRcS9rMVZhTzNCMTI4cEdXOFRHeW91dVoweG9FaTkyVlBXUUxqczFqMktLd2VKeWNSdlQxSzNTT2gzOFVwbGFBd0djWHRWVEdoRjdyTmh5ZnMzV2ZIK3FNRi9YVW5PdkF4L0IrS0NnWUtNdy82OFVIYVJpZGU2TUt6OHF4VmRwRERQdDdKc2lrbDZKZEZlQnl1WjYrekNpTFV2Y3RqUmN2akxMR0FvUkZ5cnE0TXY3RHUyZXI2QjVyNXBSeDRaMVFsS3FQQVNwTmpRYjJ5Y3JVT0REZExMZDNmMDJnMXJYV01FcjBsMi9qcWRFeW5MQXA4TXRCZytTcUs0cmNCYUU2bVoyTWpvS0lLdnFQUHg3T00vZnZVTGJma2lKSVFIREI0bE5mSXdhQ1F3RElXMEIvMlhhV1lMNjAwOXJya0R5djF4Rnc4VjFRTUFrUnZvV0U0VTcxUmZUTWNsSnkyV3RXZ2tQTldGUUFnVG9CbGNReFNpbXdtaGIwbkhrK3hsVmtieGJXTFhBOVFtT2ZtVk5UdTU5UmgvV2paSjlpZ2dJb2Jqa0JCNXBOSHRZY0ttaGs4OUVYbnZGeGhWMy9Db3dWZEtMS2tKWmFaOFRCMHRHd1RvR2hhWmtFM0xoeHJQa2dBVXgvOVR2REJXUzU4bTBGWnRkZTZESjdVNTBqT1IiLCJtYWMiOiJkNjA0MjhjNWYxYWY2MjRiOGRkNTVjOGY2MzIwZGE2ZTgxOWU0N2M0ZDFlZjBmMGU4MzMxNjQ5MzViYzRmMDk4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Inh0RzNXcjRIZHprRTRPZ0JvMWdjaHc9PSIsInZhbHVlIjoiY3FvUSs1Y0RaU1dTeGRVTDRXbTRNNWZOdWh2WUcvME4yWDdKU252VUIybXBZZUFjQVBsTzFUWVQyZk8wd2txNVFSQkFxM3BiTElSNFpMcnRBNzI1TDFHQm4yYms2VDRtOXNyV3g0UEEwSFpjbU8wbFBYN29DQTBubVhXaVhWWkxGYSt5cVdSUHFRTVhzS3BQNzFUZUU4QUFwb1Z2VUd2UER3NndVc2NpRDZ4cFgzdGlBaXRjRGxQeThpYXJ3aDNYOXVxWEZIY0ZyZVUrWUlQVUw1bUxHbS9vWHFIUk83TWlrN2ExTDNMVXBDMzhndStwOXJwcVBDZmRmcGhYSXBwMTl0ZmdXbXF5U2Rib3I5UjVjMVAxSDJGUUpWU0FXcDJXNmpxYjRuOEdwOUpBV1hIMk5aVVIxZUU5TktJRFFzbXhFMGF1SlFVb3gwV0FBZStXY3BSTGZEREk5Y3JRNmY5c0lpcUpLOXdmcW16Z0hyejE1a1YwSVllVjR4UkNYT0EwQys2NVIwWERHZmlKSHl1eU5uaGZLWUorY2pQV2pNWEFKODI2cmdlejQybUw0Y3BMbmtaVlZNS0VGUU82Z1huZC9hTUl2NHBNaUFzK2VpY2d2Zno0dmovSlo0eXZhRGl6aHdHUVhWcFM4TjdrVjZ2ZGpZRDVtMGF6NTV3Mk84TmQiLCJtYWMiOiJhZTg3YzNkOGI3ODc1Y2YzY2Y1M2FkMGE1NmNjOWQwNTc1ZGZmN2U5MzlmODdjMTliM2U0NTI3OTcyY2ZjMjVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-389226453\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2133535257 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133535257\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1578231492 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:02:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklaanFJZHkxVDRuaDVSSVNveURWNGc9PSIsInZhbHVlIjoiNTdsYVZnYldweUdJeGJsNm84OGIxRjFJb3B6SnlmYy9WQUNEditKOHRxczc5NWhoVHZuNEV6OEVWWEVEYWV3VVIyTTdqdnBlT2ZFUGhmc3d0SjNFbEJmSnR3YzRlREVOazU5aUt5S0JCYndERElqRnJCZmtlL2dGOTc4NTQ4MUxYL25taGxqWWE5NkFkeDNlaG9EUWQxZ3dhTFdLKzlRQ2gwQW9DVGFNTTN5a2hXeDBaTi9reS9Ed0NRdE94TGVhVlkvcWlWSFRtZnBqaDREYUxLZmttTkVuMUdQSjJITE5OOEZRUkl3d1VYTVBmUER2WXNLYnE5ZGd2QUZWQXpzZngwMGVzWjBlL0xZc3hxVWtKcUhadC84aE9mSjJVV29CSkRBdm81YkNLL3lZTVI5aWdDWVJIc1ZwM2FZcEFGV25iU2h6ZmQvcjRpSklkd1BhM2s1NVFzcHRLY2VKSWswU283VHpudEVpMnJtd09qSFBTWHVpM3BnSkZ0a2piTDJHZlVKeDFHU3RkcURsSzdiK0dIQlhmM3RXRldJTFIzd1V0M21GR1ZORkNvN3M3d1NSbG1PdVkvM015bENldHhVZEdJNW9CSXYrVFhyeEtFVmd5TFQyUHdPd2JvTVkydnUzb3dLM3BOaHJTa2ViZDNzdzhuOVhjYXdudlMvdVlMbmUiLCJtYWMiOiJjOTQ5MzAwMGRlZTY1MDlkOTQ4ZTU4NzlhMmRjZDMxZDNhNTQ0ZDRiNDI1MjY1MWNjNjg0NzY0NDNiZjAzMGZiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:02:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImtEd0dzSFAxU2VQRzlmR2g0aDhRYkE9PSIsInZhbHVlIjoiRzV0ZjB3Y28yZ1JvSGppNisxd0RJT3MwL3RHTDhydzljZXBKN0t5R2M5TW9tdVdTdkhuR09kRjBHSHhLd2t3V3NkNVZnY1BZeE9HdVBWemlOMTc2YTE5cXJXN2xveUxMUlpabkdxaTNHV3NqR2ZDeXVTaU55WTZDZlpDZUlmZENSMEtvZjVSYXhHQklMZ3pXQ20vc1lEZ1NKQ3hGSlNvWm1KZkJRSUV3aGRIUko0M2VKSUpyTklLRjJvYWwyOUNFdDIzR3U4VmZzNUtYbEhvQ1I1RnAwelMybWlIUW82dmVoR2k3ODJOWW1Xcm12WVEvQUp3RXJlVDdSZ0wyZWhXckJFUGRjV2FuTUxBN3I2cHNraHYxWXZWOHhmZ3pqZGxqbE5CNC9ETU52cjVaTVo4cW83NGJ6NzR4TlRrcnRTdW9zV29rK0FiRWcrOGFDTmJaRkRSLzlSN3l6c3M1dCs3eC92STZhQXRSR1BzdExyalNxVjJ6WTBHbGc0ck81VjRUcFNpbUwvWDhkbFB6YzNCeGFIQWxHZFQyRGx5Wkp2Z0lJQjZ3YnpnczNKdzROMlhTMVBvdWRmc2RKcUlOYWVPMTlTOFNBOHplbFpUeGZLQkR3YmZaYkVVSGJzOTFUVDFZTE84QWFLOE5MeUdaQlVhY3YrUkVzTk1PRGVBbFVWelgiLCJtYWMiOiI1NTgyZmFmMzdhOGQ4MTU2OTRmMGMzYTM3NjFlYjg2YjlkOWQzNGI1OGI1OWNhMDFlNzVkN2JiZTM4YmVhNGMzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:02:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklaanFJZHkxVDRuaDVSSVNveURWNGc9PSIsInZhbHVlIjoiNTdsYVZnYldweUdJeGJsNm84OGIxRjFJb3B6SnlmYy9WQUNEditKOHRxczc5NWhoVHZuNEV6OEVWWEVEYWV3VVIyTTdqdnBlT2ZFUGhmc3d0SjNFbEJmSnR3YzRlREVOazU5aUt5S0JCYndERElqRnJCZmtlL2dGOTc4NTQ4MUxYL25taGxqWWE5NkFkeDNlaG9EUWQxZ3dhTFdLKzlRQ2gwQW9DVGFNTTN5a2hXeDBaTi9reS9Ed0NRdE94TGVhVlkvcWlWSFRtZnBqaDREYUxLZmttTkVuMUdQSjJITE5OOEZRUkl3d1VYTVBmUER2WXNLYnE5ZGd2QUZWQXpzZngwMGVzWjBlL0xZc3hxVWtKcUhadC84aE9mSjJVV29CSkRBdm81YkNLL3lZTVI5aWdDWVJIc1ZwM2FZcEFGV25iU2h6ZmQvcjRpSklkd1BhM2s1NVFzcHRLY2VKSWswU283VHpudEVpMnJtd09qSFBTWHVpM3BnSkZ0a2piTDJHZlVKeDFHU3RkcURsSzdiK0dIQlhmM3RXRldJTFIzd1V0M21GR1ZORkNvN3M3d1NSbG1PdVkvM015bENldHhVZEdJNW9CSXYrVFhyeEtFVmd5TFQyUHdPd2JvTVkydnUzb3dLM3BOaHJTa2ViZDNzdzhuOVhjYXdudlMvdVlMbmUiLCJtYWMiOiJjOTQ5MzAwMGRlZTY1MDlkOTQ4ZTU4NzlhMmRjZDMxZDNhNTQ0ZDRiNDI1MjY1MWNjNjg0NzY0NDNiZjAzMGZiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:02:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImtEd0dzSFAxU2VQRzlmR2g0aDhRYkE9PSIsInZhbHVlIjoiRzV0ZjB3Y28yZ1JvSGppNisxd0RJT3MwL3RHTDhydzljZXBKN0t5R2M5TW9tdVdTdkhuR09kRjBHSHhLd2t3V3NkNVZnY1BZeE9HdVBWemlOMTc2YTE5cXJXN2xveUxMUlpabkdxaTNHV3NqR2ZDeXVTaU55WTZDZlpDZUlmZENSMEtvZjVSYXhHQklMZ3pXQ20vc1lEZ1NKQ3hGSlNvWm1KZkJRSUV3aGRIUko0M2VKSUpyTklLRjJvYWwyOUNFdDIzR3U4VmZzNUtYbEhvQ1I1RnAwelMybWlIUW82dmVoR2k3ODJOWW1Xcm12WVEvQUp3RXJlVDdSZ0wyZWhXckJFUGRjV2FuTUxBN3I2cHNraHYxWXZWOHhmZ3pqZGxqbE5CNC9ETU52cjVaTVo4cW83NGJ6NzR4TlRrcnRTdW9zV29rK0FiRWcrOGFDTmJaRkRSLzlSN3l6c3M1dCs3eC92STZhQXRSR1BzdExyalNxVjJ6WTBHbGc0ck81VjRUcFNpbUwvWDhkbFB6YzNCeGFIQWxHZFQyRGx5Wkp2Z0lJQjZ3YnpnczNKdzROMlhTMVBvdWRmc2RKcUlOYWVPMTlTOFNBOHplbFpUeGZLQkR3YmZaYkVVSGJzOTFUVDFZTE84QWFLOE5MeUdaQlVhY3YrUkVzTk1PRGVBbFVWelgiLCJtYWMiOiI1NTgyZmFmMzdhOGQ4MTU2OTRmMGMzYTM3NjFlYjg2YjlkOWQzNGI1OGI1OWNhMDFlNzVkN2JiZTM4YmVhNGMzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:02:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578231492\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1732496131 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1732496131\", {\"maxDepth\":0})</script>\n"}}