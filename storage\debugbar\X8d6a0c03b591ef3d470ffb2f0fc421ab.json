{"__meta": {"id": "X8d6a0c03b591ef3d470ffb2f0fc421ab", "datetime": "2025-06-28 16:17:23", "utime": **********.369444, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127442.945081, "end": **********.369459, "duration": 0.4243779182434082, "duration_str": "424ms", "measures": [{"label": "Booting", "start": 1751127442.945081, "relative_start": 0, "end": **********.310719, "relative_end": **********.310719, "duration": 0.36563801765441895, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.31073, "relative_start": 0.3656489849090576, "end": **********.369461, "relative_end": 2.1457672119140625e-06, "duration": 0.0587310791015625, "duration_str": "58.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45853536, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00515, "accumulated_duration_str": "5.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.346315, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 47.767}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.357836, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 47.767, "width_percent": 6.796}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%POS Summary%' or `sku` LIKE '%POS Summary%') limit 10", "type": "query", "params": [], "bindings": ["15", "%POS Summary%", "%POS Summary%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.360376, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 54.563, "width_percent": 45.437}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-381162851 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-381162851\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-417853069 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-417853069\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1949678096 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"11 characters\">POS Summary</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949678096\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1391775870 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">32</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127024092%7C32%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNmTnlEbTgyOFdaOG5IM1B1ZWVUZFE9PSIsInZhbHVlIjoiV3JxV043UVhnYVNLbzF3Vncxa2trcUliUXh2OElLdnAveVpQNFBTRjF5Y1RIZ0g1cVNHNlNTV3hVazBvRVdFS1QzK0RPQURHV3k1Uk1Ra0hLbzQvOThROTlBSE1KYWJZczVNUDNKZlEvSTJlQWNYOTQ0VUZTWnQrdzAxcHZOa1h3a0REb0d0WDVwQnVQMjJWQlVVVzJZU1NLK25tQVJnVDVaMHVKd3ZMTzZ5S2ZyZFMzZGJwMzNhNDNZbUZmazI4ZGpSQmdzNnZhWHoyM3ZxbHBGbFhLdTRRSDIrUDZHUURqZm11c0hZY3I2bnZLNHZRdEI0WWZURllMTVFiQk85UWNpWU1uTjRxWEpDSGFXeUI3TG5sdlpmbG15ZWNrU3lGVHJ3ZkpBT1FPS2lkM2NKRlBjUEpJcVFYQVBnK3BLcXQ4c0NyTDdZKzdaSVlvakpLaXFqeGM0Yk5TNmxBZUw2dFFWSW5uWDdEN3FUV08rYi9wcWoySXYySnhtdWM1ZFJzN3JNc0ptTm4wQ3lUUnNvcGNxeWwzMGRDS3M2Rm1oV3JFaDlSTldBTmIxUXNvQ0IrdUpQcFRCRkNZMk5nKzFYSXNHaGtXNkJVVHBkRjVaWEZ1RS81WjNsOFBsbnhlZ3FFRHdicjZWOGl6S3ZaTHlZLzR4YTNDdnZMS2tQcnRpSCsiLCJtYWMiOiJkZmI5MTU3ZTFjOTNjZjU4MzM1MGRmNGJlOTJiYTlmNjE3ZjkxYjQzMzJjN2ZlMzE4MTQyN2M2MDE2MTJhOGRjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZPT2xPcjhyVjQxTXdHOUtpTjJHTEE9PSIsInZhbHVlIjoiTHd4RlhjYjkybXFVYXN1czIycjVxWDlzRk5uYlBiNFAxamkzUUUzbTNKUFBZeVJ4S3E1cTEyeTdGd1JkOGMrSnB0R0ZuMEcrS2lHVWc2R3AzNVpLeklIRGt6RGRjeHdRdmxLZ0swTkVZdHpkL0tPM01ZbGVKa0t4NXhQVjd2US80V3czSmQvZzVNWnNLU2dTeTFpTjFTUjg4M1hnVGNsQUdXeHhCY2Vzd25LMnZkRzFPMEE4QkZlakhvdndMU0Iydk5JVkNDRjkyTVBJUGl2R0tDSm9OVVhzdE8wekdNQUFqZ0tNa05wRkZLL0ZWRFdaLzRCc1Y3cU9jMWZMeTJydUtmS1phdXQ4QURldjZ1OUwrSXpMcHBVNmVMVmt6YUxhdkt2SGlidVhjUlI3NnB0MWNPUU5BQ200R1dCV0E4Ly9JN2Q5UmpsMnAvQm1MZkNrT3J5UjVUNFpWOHRxM01Mc0RaazdrTDI3eFZ3ZFAvTkY0WC9BT0NTenJzYlIyaE1KeG9mem1OL0MwUFBjWjZVc2NZK1QwSzNuZnl3a3crQVBEMmU0RWhKNXVRZWw5ZXdtL2dSY0wzc0JOVkk0b3dxMGNMZzlFMzY1b3NCSVR6d2krODlqOWtUdGl1dnRWeXJ2R1FrZnhXdldVOW5lNEtabW5DZnRsa3Q3c1NudkpBRkQiLCJtYWMiOiJlMjI3OGEzNTgxMjUwNDdhNTA4YTJlYWIyNGFlNWJkZDU2MjVlNWI2MjMxNDUyYWI1YjQyMjgxMTY3MjM4YmQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1391775870\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-518434701 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518434701\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-760134116 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:17:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjV2d3RiL3VsQzgzc0ppNkhUZ09wV0E9PSIsInZhbHVlIjoiekNETjRjRm5JMks5SnphUzFIMVNhMWkzZTRNUU1QV1FIZ0c1N0xhcEM1ZW5hK3pPMjNxUU5URDN2L1I0QWM2L1hxWnFzdjFJNEkvdXZBS1BYdG5CQUI1UjRSR09uWkdwU1JKVHRXSVJxOVNCSEtEVCtZZjRKS1lYMlE4Y1RFU0o1VHhBZFRTR0dmWFA2Mk4yWm5KWVQrVlR2WmRlRkplYTdTZ0loSWhMMFVnNlZWM25xRnhtTmVaNXJCb3NEMlpMVFNaKzNBc09HVUtCQm02RkpVU0tJcENtekR0WU8vVHg1eGsvK0tJQkpIVnhMSEpLalJqcTIxelJTbEI1eXZBRnloSXo3NW5PVzljV0pEWHJBOWQ3VFpuN3o0SHVyT2RaVFlTSzVUYVRzRlhFa25EaEJTb1VadzlJcVFUTjJScXpZbmQ5d1NlaGtMcEZUNzRLMkpoUjF4UFkzVU50RDB4Y1grR04rNmM4S2pJaUJEWER0MExvbzRiV1R5UVJsV1F4WXBVL20rUmkwenR6c1Q5bFBEeFcyY1VmZlRZbWhES3QrQU4zZ2p2SWllaGxlTzBUWm1SUE9NZE5rWVVaaVFDamdTWlJkemxOWTdKcEtvU08yL0pRQTdQZm1IWDZQWDFqQlZyNEs2REg0d1lhL0RUeFdZakR3c2ZvY2FFdHk3WUoiLCJtYWMiOiIyYjFhOGFlZWQxMGE5ZjE0NGM5MjVhYzQwMDVmMzI3OTljZGJlYTMwZGZkOWFjMDIyZDBkZTg3MWM0OGE4OTBlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVuTnYrdEtFY3hhdkQ4OWRyOEpvTEE9PSIsInZhbHVlIjoiL1JsTjRRTWRTSkpycXlGYUlXQ0ttV3lmaUVOUkVodTdjMGo2NERjSmIxbXdQd05NWkJrTER2VFFpWEFxVWR3b3o0KzIySFMrelFRYXNEc2xvTjVEZ2VtZnJjdGRJRkxMb2QyZGcxT013ZkVKTVU2bSsvcXNWWUo3bWRtU3ZVK2pUMlNpbTVhL0Q3SUtlQkF6Qi9KMm5naGdvWG9IeElYRXNNMTJ4VkdRbXBONnlJYU1HS21tQ3BaZURDZnlDZW0vblZIWFNtOHBwUmdMYWcvS3dSTXVWazR4eTh4R3Z3dDJLaFZVQ2dNTWxuM2c2NXBheVlveWtlR2pjTHUxdkU2WTFTT1V0OVZnRFQxSnBnMllCaEJydGlxcUdJbWVJbXU1YzJjaUNtYVRqVURvR0JHcmR1b1F3V3hjRjA1U0RPa2cvb0RqdkRQb3J3RFB0dmNBMis2Zzkvc0RVdHM2Q2V3ODY3cktDUWQxYWFIMUcwTzA2OFgvck9jWEtoa3drcWVFRjlydTFoRGdnbE0zRzJkNUNydkxKeVRLU1JYN1N0MEkyOUlGaURxWS9idWY5cGRRWThVTWtIOVlYWGpId0dQdndUL1VYQ2lvVi8rbzZtVVpzaTA5dUNHdWtVWmpwaHJnNXkxV0ptOFprWTlOMW5UbG5QTWhVTWl0aUlJZWJyc2giLCJtYWMiOiIyMWE3ZjQyZDAzOWQzZDBkNmYyMDM0MDQ3M2IyYTU0ZDk0ZmYwYTAyZDJmNDQzYTU1ODQyZmM1OWQ0NmFmZDVlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjV2d3RiL3VsQzgzc0ppNkhUZ09wV0E9PSIsInZhbHVlIjoiekNETjRjRm5JMks5SnphUzFIMVNhMWkzZTRNUU1QV1FIZ0c1N0xhcEM1ZW5hK3pPMjNxUU5URDN2L1I0QWM2L1hxWnFzdjFJNEkvdXZBS1BYdG5CQUI1UjRSR09uWkdwU1JKVHRXSVJxOVNCSEtEVCtZZjRKS1lYMlE4Y1RFU0o1VHhBZFRTR0dmWFA2Mk4yWm5KWVQrVlR2WmRlRkplYTdTZ0loSWhMMFVnNlZWM25xRnhtTmVaNXJCb3NEMlpMVFNaKzNBc09HVUtCQm02RkpVU0tJcENtekR0WU8vVHg1eGsvK0tJQkpIVnhMSEpLalJqcTIxelJTbEI1eXZBRnloSXo3NW5PVzljV0pEWHJBOWQ3VFpuN3o0SHVyT2RaVFlTSzVUYVRzRlhFa25EaEJTb1VadzlJcVFUTjJScXpZbmQ5d1NlaGtMcEZUNzRLMkpoUjF4UFkzVU50RDB4Y1grR04rNmM4S2pJaUJEWER0MExvbzRiV1R5UVJsV1F4WXBVL20rUmkwenR6c1Q5bFBEeFcyY1VmZlRZbWhES3QrQU4zZ2p2SWllaGxlTzBUWm1SUE9NZE5rWVVaaVFDamdTWlJkemxOWTdKcEtvU08yL0pRQTdQZm1IWDZQWDFqQlZyNEs2REg0d1lhL0RUeFdZakR3c2ZvY2FFdHk3WUoiLCJtYWMiOiIyYjFhOGFlZWQxMGE5ZjE0NGM5MjVhYzQwMDVmMzI3OTljZGJlYTMwZGZkOWFjMDIyZDBkZTg3MWM0OGE4OTBlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVuTnYrdEtFY3hhdkQ4OWRyOEpvTEE9PSIsInZhbHVlIjoiL1JsTjRRTWRTSkpycXlGYUlXQ0ttV3lmaUVOUkVodTdjMGo2NERjSmIxbXdQd05NWkJrTER2VFFpWEFxVWR3b3o0KzIySFMrelFRYXNEc2xvTjVEZ2VtZnJjdGRJRkxMb2QyZGcxT013ZkVKTVU2bSsvcXNWWUo3bWRtU3ZVK2pUMlNpbTVhL0Q3SUtlQkF6Qi9KMm5naGdvWG9IeElYRXNNMTJ4VkdRbXBONnlJYU1HS21tQ3BaZURDZnlDZW0vblZIWFNtOHBwUmdMYWcvS3dSTXVWazR4eTh4R3Z3dDJLaFZVQ2dNTWxuM2c2NXBheVlveWtlR2pjTHUxdkU2WTFTT1V0OVZnRFQxSnBnMllCaEJydGlxcUdJbWVJbXU1YzJjaUNtYVRqVURvR0JHcmR1b1F3V3hjRjA1U0RPa2cvb0RqdkRQb3J3RFB0dmNBMis2Zzkvc0RVdHM2Q2V3ODY3cktDUWQxYWFIMUcwTzA2OFgvck9jWEtoa3drcWVFRjlydTFoRGdnbE0zRzJkNUNydkxKeVRLU1JYN1N0MEkyOUlGaURxWS9idWY5cGRRWThVTWtIOVlYWGpId0dQdndUL1VYQ2lvVi8rbzZtVVpzaTA5dUNHdWtVWmpwaHJnNXkxV0ptOFprWTlOMW5UbG5QTWhVTWl0aUlJZWJyc2giLCJtYWMiOiIyMWE3ZjQyZDAzOWQzZDBkNmYyMDM0MDQ3M2IyYTU0ZDk0ZmYwYTAyZDJmNDQzYTU1ODQyZmM1OWQ0NmFmZDVlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760134116\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-422632659 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-422632659\", {\"maxDepth\":0})</script>\n"}}