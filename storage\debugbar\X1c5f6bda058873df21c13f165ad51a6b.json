{"__meta": {"id": "X1c5f6bda058873df21c13f165ad51a6b", "datetime": "2025-06-28 16:02:14", "utime": **********.129771, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126533.702962, "end": **********.129784, "duration": 0.4268221855163574, "duration_str": "427ms", "measures": [{"label": "Booting", "start": 1751126533.702962, "relative_start": 0, "end": **********.076906, "relative_end": **********.076906, "duration": 0.3739440441131592, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.076916, "relative_start": 0.37395405769348145, "end": **********.129786, "relative_end": 1.9073486328125e-06, "duration": 0.05287003517150879, "duration_str": "52.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46420976, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2428\" onclick=\"\">app/Http/Controllers/PosController.php:2428-2462</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00222, "accumulated_duration_str": "2.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.11314, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.829}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1232421, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.829, "width_percent": 21.171}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:8 [\n    \"product_id\" => 0\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => \"2.99\"\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => true\n    \"tax_rate\" => \"0\"\n    \"original_invoice_id\" => 1460\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1131852473 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1131852473\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2039487523 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2039487523\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126520354%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJYbDI5Z3EzQjJjVThLS0xxY1grclE9PSIsInZhbHVlIjoiRVcxK0J2YXljUTBGRnVRSEpqaG5uRlNBT2xxQndxMnFrWkpKMWY1U3dlSzI1MWtMRFUzMHNua3hzQ0ZOMFBnYVE2bng4QUF4Z2dzenlyYTNkOXU2QWl2M3dydVQyemtDSkdCU01nNktqZ1VXcFJmQm80OHFac3lXUXQxSXc5ZXZrNzEvczlkQmdDRFRPODlJazhmNU0xT0s1VThzcHFTM0dzd2w5cTIvbzRRb1R2U1JFemNMbSs5UW5OWlZRRlNFN1NGRlBMd2NHNTVPVGFETGROd2tpd1I1NWRjY1RvbFFFSHFqQkhoM09MeGpKT3pVQkViVVF3SjRYOUw1VEg4VUZQQkJJNXJCRFVFN1FXb0NyRUJNMCtZTGNjSW5CS2F2aXN2WHhFS0FIUE5jMGQ4cWh6Z1RRSFcrK1hSWWt4VjAwSU12UVVuQjlleWs4dXFSSWVmZGNUb3Q4SzNHL0lwVkVva0MyQ2h3ZDNZandVR3oxWHU1RFBUdmViRGNUSUZNeVZIN256YURBTDhIUVhFVDJrUk4zaHhXUTh1WlpsaFlVNU55YTRqSUM4VnM5K21BNnFITVJlcVhQbmxCL3BjdmRUY1FaSnZMbGVOcVIwN3lSNEk5T3hCQ2lrM3Q0WlV3d3dlN01SWjhCMmZjWDZ1NnhJUUllR25pQlpxUittdGgiLCJtYWMiOiJiMzhhYzAxNjJjMDExOTcwMWM2ZGI5NDJkMTllZTdhOWJiYzE2NDJhMjQ0NWNmODc0NDU5YzllYTNjMmJkMmNmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtzaXNnNjVjVkpZelNaUGIvRnpXTlE9PSIsInZhbHVlIjoia3NLbmJMd091SFVZYUJQU2c5WmVEYW10QlVqdXlydEJRbTZtcGNXeHhCWUZ3K2Fyb0hxTCtNTThCWGdiL2szSEg3KzdybER2V1dBbWZwRS9LdTRnUWpNVS96bWZzUGJmRXhpNlc1SzJGNEd1Q1ArN2FpTzc5STFXQ0VWTDVFYkQ1YWVwMTJJNlRDQnk0NCtPUm45VTFTR2phWmVUS3dUWGNxQndTT2FFcHNaN1pwSC9jbGZtVDhjRi9WU1dkM041bmxPY2tJY3JjNGN3STFCeXBHQm1NUjV1a0VuNDlzdkhZVHo3QjFhN2xhSU1kMVFGc2NTOWZRVTMxZEtXUEdNejVyYzBlWkR4bTRMTmpwZkdOZCtQUkcyY0picThiNkloQWNqSjl2LzV3aVBSUWpoWVJkd21PcUtZUU1Nc0dpZ1M3Y3NrTS9MS25iL2xUUmZ5OE5zT05FWFRkaFF1QzBQRFFYWmpwUjBqeDlWd0g5OHc0bjMxNXM0dzM5b0E4M0FqY0dkUEp1ODlmQUVoQXl6ZEMzb0NKTXU4V1ZFaXZNZmJ4TUNscFpCVEhLRnNVbEJSaU1aV0dYZzFuSm5XR3B4T1BwWWl6TGp3aWhjcU80aGdXWkxMK0ZqR3VyR2JJUTBWcUlSQlRLZFJIaEVubGlVZTVmWmVlOFRWYnFQQ2ErY24iLCJtYWMiOiJhZDU5NTcxMGRmNjZmYjZmZWI4MTEyMmQ3OTFkYzIzYzEwMWIwNTBiYTVlNzI4OWQ3YjAyNWFhYmRlZmUxNmU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-568235187 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-568235187\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-162825604 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inh2cmg0LzY0b3NJVmlSK1F3cnN1ZHc9PSIsInZhbHVlIjoiTCtuQXMvRS96SGhhQ2g1MnY5UmlqWlg3cDlUSUluMCt3YUljaytxYkJVaDdqNEdUeWhRVjU4QXZoWUpna1kxdFFlWkZZWThJZXpuQ1NHK3RvbEh3VkVrL0VWSDFxblZjSHZ4cWFTRUJ0eW1EUFl3K2lYS1VrRXQ1aGdZVm1LSUh4NnBYNzNaWmdRSGNlSXBJUERGd3JMSjJsTzNVcmNWb2luamhUV2JSbHU5TlBVQ3ZLM2N6WEdLR0ZpMG5iSGVWQ1puNWl0TCtqaGpUd0V5RERIZUl6LytHR3N6OEd4Q1RLdVNObjJtZVBrK0JvN1JGOThVV0Q1Qll6TVNtd2lRalZlanBmTVlYV3lQOTRyZkRLQkdremE1UlFhVHYzYzlTYmh5Mnd3TWJsOFFGY1pUMWh1Z0xpVUgxUmRHT2Z1RGpUNnZkQ1Mza1c3Q2FWRXNtY3lNQ1hDcmN2YzJVMElxVDBFWjlBd3poRWFWb0N5dXdGRmFjMDdxS04yTzNrVWdXSGJJcktoZG5HTi9ycXNIM09QVktMR2U3QVVuY040VE0yZmdHaGlyanpPM2ZWSlRPSnBRY2hLakhaNVgySnh1Y29mQWZRcGpEUUFYWkRSbVhXYURvQ0F4bE1POHlZWjRmd1hPek5JUStpNDBQYW13a3lXL05CZk43UnI5UitKQUYiLCJtYWMiOiJjNjM2YTI1MGYxODE0ZmNhMTJhMjQzZGQ0MzhlMjRkMTc4MmQxODc1ZTQxYmFlNjk2MjUyZmU3NWMwNWZiZjg4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlNKM0RqWUZRMkhCVEhPeG1qWHhhR3c9PSIsInZhbHVlIjoiQWF4MUdyalAzUEtSZHNjZ0dkWlMvcUdSd25sR2plQUNVTkRib2lwVGZJdy82dXJ5MGtmd2RsSnBQUUo2cGY0Ryt1U05pWmh6QS9pYTBKbVUwNTZFckJjanBXejBLSURnY2xZODJFN2NnNEpRUy9JazhZVHh6aVVaNkFMeHd0VU9mMFZqNjFxZS9TV0N2VDNNSjI5Tkp6cytpNXlWRFlFT1pjd25oT3VSRHJTTzJSTjdISEcwdTdiSTVtbHBTcnVDdklXNGVUZGxzR3RxRHRscGpqVzhUZzIwRU4vMFIxaDNWcHEzS2t1eDFpcXJBczhPQTBkelFkZ0hCeXdHV3FPUW5oRDFUaUlRSG1qb09vMVh4WEdmOEhMMlJoV1FoQ21mN1J0cW5Sd0htVDY1TFYrWDRNRnYwVUZwaTA4aFRMMko0clJlQUordWZqTzl3RzFjQi92Z0lOUlVqOWl1MlBvTXhqdnRTbVhZRkRMNHpHcnhrWFdYczJ3alYzaEM2V2pMbXgyMWFJdWRRbnRTNkVhYmpOdWRxYzl0b09qNWY3L2lUQmVaRWx1eFdsWFBvcjBXNEFPUW9YdUs0ZG9WT01mQTZSWTdDUGluK1Ixbk12Tm8wbllFa3N3RnZUaEpTYmwyc2lDZjVzRTVmWS9IdmdmQ0QvZWo1WGVCUlBROTdyL1IiLCJtYWMiOiI3OTRiYjgzZGZhZjg1YzBjMDY0NGYxNTBmNGVlMjNkM2U4NTU3M2Y5ZDMwZTdhMDRkNmRhN2NmNGE0ZmFkYTJjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inh2cmg0LzY0b3NJVmlSK1F3cnN1ZHc9PSIsInZhbHVlIjoiTCtuQXMvRS96SGhhQ2g1MnY5UmlqWlg3cDlUSUluMCt3YUljaytxYkJVaDdqNEdUeWhRVjU4QXZoWUpna1kxdFFlWkZZWThJZXpuQ1NHK3RvbEh3VkVrL0VWSDFxblZjSHZ4cWFTRUJ0eW1EUFl3K2lYS1VrRXQ1aGdZVm1LSUh4NnBYNzNaWmdRSGNlSXBJUERGd3JMSjJsTzNVcmNWb2luamhUV2JSbHU5TlBVQ3ZLM2N6WEdLR0ZpMG5iSGVWQ1puNWl0TCtqaGpUd0V5RERIZUl6LytHR3N6OEd4Q1RLdVNObjJtZVBrK0JvN1JGOThVV0Q1Qll6TVNtd2lRalZlanBmTVlYV3lQOTRyZkRLQkdremE1UlFhVHYzYzlTYmh5Mnd3TWJsOFFGY1pUMWh1Z0xpVUgxUmRHT2Z1RGpUNnZkQ1Mza1c3Q2FWRXNtY3lNQ1hDcmN2YzJVMElxVDBFWjlBd3poRWFWb0N5dXdGRmFjMDdxS04yTzNrVWdXSGJJcktoZG5HTi9ycXNIM09QVktMR2U3QVVuY040VE0yZmdHaGlyanpPM2ZWSlRPSnBRY2hLakhaNVgySnh1Y29mQWZRcGpEUUFYWkRSbVhXYURvQ0F4bE1POHlZWjRmd1hPek5JUStpNDBQYW13a3lXL05CZk43UnI5UitKQUYiLCJtYWMiOiJjNjM2YTI1MGYxODE0ZmNhMTJhMjQzZGQ0MzhlMjRkMTc4MmQxODc1ZTQxYmFlNjk2MjUyZmU3NWMwNWZiZjg4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlNKM0RqWUZRMkhCVEhPeG1qWHhhR3c9PSIsInZhbHVlIjoiQWF4MUdyalAzUEtSZHNjZ0dkWlMvcUdSd25sR2plQUNVTkRib2lwVGZJdy82dXJ5MGtmd2RsSnBQUUo2cGY0Ryt1U05pWmh6QS9pYTBKbVUwNTZFckJjanBXejBLSURnY2xZODJFN2NnNEpRUy9JazhZVHh6aVVaNkFMeHd0VU9mMFZqNjFxZS9TV0N2VDNNSjI5Tkp6cytpNXlWRFlFT1pjd25oT3VSRHJTTzJSTjdISEcwdTdiSTVtbHBTcnVDdklXNGVUZGxzR3RxRHRscGpqVzhUZzIwRU4vMFIxaDNWcHEzS2t1eDFpcXJBczhPQTBkelFkZ0hCeXdHV3FPUW5oRDFUaUlRSG1qb09vMVh4WEdmOEhMMlJoV1FoQ21mN1J0cW5Sd0htVDY1TFYrWDRNRnYwVUZwaTA4aFRMMko0clJlQUordWZqTzl3RzFjQi92Z0lOUlVqOWl1MlBvTXhqdnRTbVhZRkRMNHpHcnhrWFdYczJ3alYzaEM2V2pMbXgyMWFJdWRRbnRTNkVhYmpOdWRxYzl0b09qNWY3L2lUQmVaRWx1eFdsWFBvcjBXNEFPUW9YdUs0ZG9WT01mQTZSWTdDUGluK1Ixbk12Tm8wbllFa3N3RnZUaEpTYmwyc2lDZjVzRTVmWS9IdmdmQ0QvZWo1WGVCUlBROTdyL1IiLCJtYWMiOiI3OTRiYjgzZGZhZjg1YzBjMDY0NGYxNTBmNGVlMjNkM2U4NTU3M2Y5ZDMwZTdhMDRkNmRhN2NmNGE0ZmFkYTJjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-162825604\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2138894700 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>original_invoice_id</span>\" => <span class=sf-dump-num>1460</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138894700\", {\"maxDepth\":0})</script>\n"}}