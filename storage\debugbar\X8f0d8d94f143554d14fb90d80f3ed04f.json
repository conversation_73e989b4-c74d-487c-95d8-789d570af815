{"__meta": {"id": "X8f0d8d94f143554d14fb90d80f3ed04f", "datetime": "2025-06-28 16:19:08", "utime": **********.147853, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127547.705666, "end": **********.14788, "duration": 0.4422140121459961, "duration_str": "442ms", "measures": [{"label": "Booting", "start": 1751127547.705666, "relative_start": 0, "end": **********.079555, "relative_end": **********.079555, "duration": 0.3738889694213867, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.079562, "relative_start": 0.37389588356018066, "end": **********.147882, "relative_end": 1.9073486328125e-06, "duration": 0.06832003593444824, "duration_str": "68.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45700024, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01632, "accumulated_duration_str": "16.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.107191, "duration": 0.015210000000000001, "duration_str": "15.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.199}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.131098, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.199, "width_percent": 3.493}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.13656, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.691, "width_percent": 3.309}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6InBrTlNWeXVVemg3QituQXNPY2tJVEE9PSIsInZhbHVlIjoiN053YlhQRSt4bTl2Ry9sMlFZMWVZQT09IiwibWFjIjoiMzU0ZTJlNWIzMmQ2ODhmOWIyMDYwNzZjNGRkYjRiMTVhYzIwYmQ3NWRlOTNmNDgzOTQ1ZGI0ZDc0NzBmOGYzMCIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1807424176 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1807424176\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-413128711 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-413128711\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-275076344 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275076344\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-534357669 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InBrTlNWeXVVemg3QituQXNPY2tJVEE9PSIsInZhbHVlIjoiN053YlhQRSt4bTl2Ry9sMlFZMWVZQT09IiwibWFjIjoiMzU0ZTJlNWIzMmQ2ODhmOWIyMDYwNzZjNGRkYjRiMTVhYzIwYmQ3NWRlOTNmNDgzOTQ1ZGI0ZDc0NzBmOGYzMCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127544265%7C34%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVnSXNDMEdzREUxWjNlTnNIL2pCTXc9PSIsInZhbHVlIjoiK2l1aEZFMWYrVDVTZGhJK3lSRXFaWFEraDFmOCs0YlEvQjI0anZHUDlURTNTWCtKZlZDUWxZdGJmeWNFQmk4TUxHOCtZTzZPc1ZlakkxaWJQTzRXOCtqMG1majdCSjlJcnN1aFd4aVZ6a3VhaEJJTmQyTUFaQUdqdkw5NGEzMWVQZFl2MEowdmhPZkduVkFaWEpQUG91VDYzNFRXOU1zWVV1b1gwM0RTakRkQ3g0SGRFSkE1THE0d3RmMTc2RXgwWnFteEZFYVdhSWdENGMvYU44cTd2QkRvVmh2dGxJbjVISndqYk96T0Z1ZjJXL1VTTzNENitMVWNMRWltRFJDc0RZb1p1ODArU3M5Ky9YWFlJdFNDNXVkYi9NTGFTMUdsZW1KVFozODVIMTlTcWRWV3RxckRZOVA4bUk4VnZGVTVRQllyeEpPWExTS2VLQkUxaWlTeDBRWm9NTFczb2lJR0pyTWQrckU4a2E2WUpaSFZlaERZU2UwQkg5bnJMd1hPVlc4N2oxbWVTdkFpWHFwZ2RWd3F2Z0dqck9Fa2N5OXVuWVpTTU1uMXlRNHZoMnhnbG5HRFk2OGhJTVZ3NmpQRmlreldtcVBValRkVzRBd0p6QWJFOFJydjVsOUsvdHNxdmJaVGpYMlJINy81b1hoZHBsNVM4N2dUVHRSWXR6cUYiLCJtYWMiOiIyMjZjNDM0YmQ2YmMxNWQ0Mzc3Yjg4NmUyNDIwYTdlNGQ2MWFlZGQyZGU1MjI0NmZjZmVjNDA4MmQyNjJiNWRlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBTVlNxUWN6MDVySjU4RHRKNHRtNlE9PSIsInZhbHVlIjoiVWxvQUNaaGVDTmdmTnppV25uQ1llWnVvem1FWWU4dDUzeXhrRjdtRFF1b1NqMXFlaUdMcnQ0V2FpWG9lWWc5d1hTZmtMWERCZ3E4a2l2RnhBcXRiRUYyMnBNS0FsMEwvaGpwWEtublVWSzdtY2VpZ05LNnNMY01Wdzc4cmFHTGNqRHNtT1pIeXVFc3ZhQUx1M0JhK3QwNXRqcldiWGs2ejRSanUxRVNWbjE0RGk0QVkxeGVtY3ZxT3ZVODRqOURkM09BRkVSblMzeXdWemNVVnJtcFhHTjM2NjhkN2NVZnZvYTVYaXEyVHZwQTFEWWNrNUpzY3l3cjlWLyt6T3B6bFErUnNLSUZpVFBVZjFIVUhnaFVCemdFMlVFV1lmZzlTb244b1lCRnBQSmoxUGpDVUJvbjhUeGt6d0JESUdSLzZKb05ENVFpL0d2TlRCZHp5ZitYME0xMysvN0t2TXprQS9pV3J0VzEwdG9KdjhpNms3SUtwRFpnMFlQZ1hWTUhQZmYycTdTandKRVpjMHNCa1kzOUdHK2x4YVV4b29tODNGYXpYckErdGVibTVRNmlnQlFlMHRrMXRQc3kyM2VmYlJPN3hidHA2c0QyRFl5R0MvaHBoQWFYTzZiN2NMelhYTHdRMmlzY0EwcHFMNmYxbEhiVi9zVVIrSVVDNG1abTgiLCJtYWMiOiJiY2MzOGM4MDAxMjRkZjc1OTI0OTA3YjIwNTQ5OGI0NWViZWI0NTM1OTgzNTFhZTBkZDFmMjE3NTA0ZTU5MmQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-534357669\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1333749275 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333749275\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikc2ZGhZcjNCVHlMb0NiT3RMd3pVRmc9PSIsInZhbHVlIjoicUJxcGZsdVlMaGRZK1d5UjMvZHRvbm9ldmVQZGhnSEgycExsc2oxamZRS0piaDhGQjdBNlU1RHlMUTNCaG4wd3VZcFh3TmZRSGVhSGlleEljUmJwRmZ6UFlsZjIwLzFnWTBNaVpYdTZCclBwVUVCV2VnM053cS8rT2MzQmVEaVZ6VUYxY0tFNFJPMU1uSUt1RUdEemtXWGlsTVBXL3E3dXh3MTA1dSsrSHpWZHdFMzB4ankyQzY5Y2ttMmN4eStWUEd4ZXZWSHlETm16TVpqbFlTVUM3MnlLQThnbzJvdkxUTGNQWFNkaHFnNk1Ea3Jnd05nSGJ0d0FKbEczZ29oQ09hbGF5TnF3OThUSndEbW5xMFJSbk9YblA3d1hYMkhCU09nZHlPNVRUWW1pc0dLYlVjRngzeCt2U1A2bkg3a2p3NERCTlk1MENiN1Z4U3JsRDVXcWxCTkp4cUZMQ0h2M2hVeFpyME9jdndrZTBWSWhiOXBSUUdFOVhPaGtVaFkySC9mOGRLRnJzQ2NPU3pvWjVjTENvRHFlbDdCc1NUaDkrV1ZKbkxPQi9KTUNmZGNQbDZaT3dSRytFd3Z1SWRuSEZzTHZOdm85NFhHL21lNUZSMktxZ3BzN3lXRzZCZlZLV3FaV0VmY1FtdnJ4M0VSb1l5ZzVzWnZBYzRkZEFmTjciLCJtYWMiOiI5NTUyOTk3NGQ2NTY4NTFiOTAzMTcxY2U3NDZkNTg3MWY1ZmFiMWQ3MjYzODg3ODhhMTE1YzZjMDA4MTY2Y2VmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZRbFkyQitMeWlUN1c5bi9TOWxYN0E9PSIsInZhbHVlIjoiRTVsSXdXVkE4Zk8xMzRFUzlrejhSdU02TWFScE1RNVk5K0hYYklXdEdsRGdPSzlPZmxBZTF0cmR4Wkl2ejl6MmpoVGZxcVUyUjgrdG83SkdCM0UwNlViVGoxU1R6RWhsWFJuRmJ5SnJTOFRNMkV6YVhoditQVEQ1a1FjbG14RTNjTjRpMVhMNGoxZnBzcmJJVnJ0UkhLbXUyQjdVRVc2dTNGenlMbys1ZEI0YU9FZnZSRER6QzlxUWpETktnRm1mSUFKcUZ4UjZ3NXc0NzJaYVN2cndPSGlJbEhLelpIS0JmWk0wcDZVOHBwY3h1dGk1SlpER0lId2wyaS9mYmZJWG1VZ3VLR2FTKzNUM0pveVNmbkZybVNucXlqMFlIVm5sRnllZEpkSFlxb0EwSVpna3E1d2VjeVhGQ1ZhRVptMGY4T3dxb2dWb0VOL1pwMmhQdyt5STBJRU42SXA2cWc5VjNGRFNKWDdUR2o3elhYY01ZR0FVNVVEdFU4Kzl1bnF6UmdBd0M5d09iZjR4UjFXMWZxSDY0SklRMmhPRWpwRHRMd3RoeE0ybFRxUzV4dDBaQnFtMytYYUs5S1k0alE4MDhMYXAzaTZ3dXZsTlhHMHoyazNRVW9oOE5XUjUycUhyalprbkw4NEtLMTV1Z09YTFNRTnRyVTFHQkM4T0J5TWciLCJtYWMiOiJjMmUxYzBlZmZiZWM3MzAyYzFhOTYyMmY2MmFlMTNiYjVjMTU4ZjM0YTczZjZiMTVhNTRjZDRlODJiYTFjODEyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikc2ZGhZcjNCVHlMb0NiT3RMd3pVRmc9PSIsInZhbHVlIjoicUJxcGZsdVlMaGRZK1d5UjMvZHRvbm9ldmVQZGhnSEgycExsc2oxamZRS0piaDhGQjdBNlU1RHlMUTNCaG4wd3VZcFh3TmZRSGVhSGlleEljUmJwRmZ6UFlsZjIwLzFnWTBNaVpYdTZCclBwVUVCV2VnM053cS8rT2MzQmVEaVZ6VUYxY0tFNFJPMU1uSUt1RUdEemtXWGlsTVBXL3E3dXh3MTA1dSsrSHpWZHdFMzB4ankyQzY5Y2ttMmN4eStWUEd4ZXZWSHlETm16TVpqbFlTVUM3MnlLQThnbzJvdkxUTGNQWFNkaHFnNk1Ea3Jnd05nSGJ0d0FKbEczZ29oQ09hbGF5TnF3OThUSndEbW5xMFJSbk9YblA3d1hYMkhCU09nZHlPNVRUWW1pc0dLYlVjRngzeCt2U1A2bkg3a2p3NERCTlk1MENiN1Z4U3JsRDVXcWxCTkp4cUZMQ0h2M2hVeFpyME9jdndrZTBWSWhiOXBSUUdFOVhPaGtVaFkySC9mOGRLRnJzQ2NPU3pvWjVjTENvRHFlbDdCc1NUaDkrV1ZKbkxPQi9KTUNmZGNQbDZaT3dSRytFd3Z1SWRuSEZzTHZOdm85NFhHL21lNUZSMktxZ3BzN3lXRzZCZlZLV3FaV0VmY1FtdnJ4M0VSb1l5ZzVzWnZBYzRkZEFmTjciLCJtYWMiOiI5NTUyOTk3NGQ2NTY4NTFiOTAzMTcxY2U3NDZkNTg3MWY1ZmFiMWQ3MjYzODg3ODhhMTE1YzZjMDA4MTY2Y2VmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZRbFkyQitMeWlUN1c5bi9TOWxYN0E9PSIsInZhbHVlIjoiRTVsSXdXVkE4Zk8xMzRFUzlrejhSdU02TWFScE1RNVk5K0hYYklXdEdsRGdPSzlPZmxBZTF0cmR4Wkl2ejl6MmpoVGZxcVUyUjgrdG83SkdCM0UwNlViVGoxU1R6RWhsWFJuRmJ5SnJTOFRNMkV6YVhoditQVEQ1a1FjbG14RTNjTjRpMVhMNGoxZnBzcmJJVnJ0UkhLbXUyQjdVRVc2dTNGenlMbys1ZEI0YU9FZnZSRER6QzlxUWpETktnRm1mSUFKcUZ4UjZ3NXc0NzJaYVN2cndPSGlJbEhLelpIS0JmWk0wcDZVOHBwY3h1dGk1SlpER0lId2wyaS9mYmZJWG1VZ3VLR2FTKzNUM0pveVNmbkZybVNucXlqMFlIVm5sRnllZEpkSFlxb0EwSVpna3E1d2VjeVhGQ1ZhRVptMGY4T3dxb2dWb0VOL1pwMmhQdyt5STBJRU42SXA2cWc5VjNGRFNKWDdUR2o3elhYY01ZR0FVNVVEdFU4Kzl1bnF6UmdBd0M5d09iZjR4UjFXMWZxSDY0SklRMmhPRWpwRHRMd3RoeE0ybFRxUzV4dDBaQnFtMytYYUs5S1k0alE4MDhMYXAzaTZ3dXZsTlhHMHoyazNRVW9oOE5XUjUycUhyalprbkw4NEtLMTV1Z09YTFNRTnRyVTFHQkM4T0J5TWciLCJtYWMiOiJjMmUxYzBlZmZiZWM3MzAyYzFhOTYyMmY2MmFlMTNiYjVjMTU4ZjM0YTczZjZiMTVhNTRjZDRlODJiYTFjODEyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2111359362 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InBrTlNWeXVVemg3QituQXNPY2tJVEE9PSIsInZhbHVlIjoiN053YlhQRSt4bTl2Ry9sMlFZMWVZQT09IiwibWFjIjoiMzU0ZTJlNWIzMmQ2ODhmOWIyMDYwNzZjNGRkYjRiMTVhYzIwYmQ3NWRlOTNmNDgzOTQ1ZGI0ZDc0NzBmOGYzMCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111359362\", {\"maxDepth\":0})</script>\n"}}