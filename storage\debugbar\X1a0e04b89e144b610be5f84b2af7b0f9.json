{"__meta": {"id": "X1a0e04b89e144b610be5f84b2af7b0f9", "datetime": "2025-06-28 11:23:46", "utime": **********.695757, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.236945, "end": **********.695773, "duration": 0.4588279724121094, "duration_str": "459ms", "measures": [{"label": "Booting", "start": **********.236945, "relative_start": 0, "end": **********.564262, "relative_end": **********.564262, "duration": 0.3273169994354248, "duration_str": "327ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.564272, "relative_start": 0.32732701301574707, "end": **********.695774, "relative_end": 1.1920928955078125e-06, "duration": 0.1315021514892578, "duration_str": "132ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50871600, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.06348999999999999, "accumulated_duration_str": "63.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.592196, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.473}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.602371, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 2.473, "width_percent": 1.04}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('10', 0, 22, 8, '2025-06-28 11:23:46', '2025-06-28 11:23:46')", "type": "query", "params": [], "bindings": ["10", "0", "22", "8", "2025-06-28 11:23:46", "2025-06-28 11:23:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.613362, "duration": 0.05458, "duration_str": "54.58ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 3.512, "width_percent": 85.966}, {"sql": "select * from `financial_records` where (`shift_id` = 51) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["51"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.670668, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 89.479, "width_percent": 1.166}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (51, '10', 22, '2025-06-28 11:23:46', '2025-06-28 11:23:46')", "type": "query", "params": [], "bindings": ["51", "10", "22", "2025-06-28 11:23:46", "2025-06-28 11:23:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.672859, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 90.644, "width_percent": 5.812}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-28 11:23:46' where `id` = 22", "type": "query", "params": [], "bindings": ["0", "2025-06-28 11:23:46", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.677642, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 96.456, "width_percent": 3.544}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "success": "Opening Balance has been set successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-33022421 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-33022421\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1842443454 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1842443454\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1016917737 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016917737\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1134085026 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">66</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6InRXanVROUlNT2FpQkNNb1JZVkRqOHc9PSIsInZhbHVlIjoiWTQwU2RKbmVCdDNvdTc5aUY4bU9TVC9oTUxCc0tHMURFbjliSndrRk9VSy95akE1Z2JjYldpbjJ3Uk1NdmNJT013VTB2aFhrWlJSMjR3VWwzaEtET0pTS0ZHSmVmTC9ZTDFZelRHdVh6M2Z3VGVBSGdaK2xCMEswTFhacStjUUt0eFh6RUJkTnBPcUZrdE42RkNXVytMYlpUQytWU2QvLy9hb3NwQkQrQVRrZnJFM3dydXlUb0JaZElvb2xsMzRHNnBTRDg5NXNReEN5VmJsNDA1bjBwa1BwN0QzSGhnUmhSVkp2Qit0b2Q1MjJSdENtLzhrTHNUKzlISHptUFBmbVNJV0JuKzJuRWl3d3BIT2R4T3BoNG9kUi9ZZFl3M1R4OWFUeVRMOHlFZnhvaExOeS9MeDNvQVZkSmVVRUN0dmU1emZsUHU1Q1pJMU5CSmcvKzZxc09Yd1VPRDBtTTFRKzltWXBMTU9aalYxQjJ4OS81UHRJVm82UEFXMkJwNzkwbkdkYXJqeGV0UVFIWm9kbjI1MEZpVXMrdVdlZkdaQng3T1oxYU5RUDRZNDREaDFVUXpqODBkRkNhb0ppcXgrN1ZjYUZNYUV4WEcvOFVrTFhCZllDUkcvQnNYekFndjVqQkF4SFFyODVyclVFVGpZU2ljM1R1Ump1Z3JpdDVEZkwiLCJtYWMiOiI4MjJjNDQxZTBiYWJlZmExYmMxYTg2OTBlOTczYjBiYjQ1NmVlMmE2MWE3OWI4YjI3MjFkMTQzYWIzNTYwMDRmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBpNUZtWHlGU1ZQalVOK1RsYldVd0E9PSIsInZhbHVlIjoiMHZwditrMFR6Si9nWk1mV2k1dmJ1VUhheDJQWktzVnFBM2wvR3loVnJ3TTh2anNrakxwNXZ1TmJSMzJwWlplWi9RMlk5eDRFZnpNYmRHZ3MzVnFhelNDbXE3K2c3MERmTU5KTFN5OE5YM1VTNW1MWU05T1VLbkE0bCtWRU8wcjlFazlhM3NIWHh3WGw5b3IwY3gwRjRZRG9zcGNXWGdFSmtkN0FkVDZrdjFiaHJqWTEyV00wTnljRjdtR0ZwSTA5ODQxSUo4OEhSSTA4eXNWT2xCSUxvVjBhbFlHQzAydFU5aFpHZ3dtSkVXem5XZXg1clZsYTA0V0RLckV1QXhHek5Sb0U5RmN6N21DY0pPS3pmVm4vRThBLzdNdnVtN2p5dW01NzRFQnVpR3pPK2NlYWFoUTRobVBESGRWNTlUNXJkck0vWkIwMG1lb1lIRi94b1VmenlLTU5OdG9oeEZrSGQxU25NcVhaTWduWUNKWlRkRWZmMmkwMFZQbXcvZmRROHlUK3l4L2JIbkZEV1ZvTmhxTVQwM3ZHWEI3bXhyY1dzK1YzMGVrRjVzQWV4NXNVeWp1Nzd5T3hhTmVhalFRRFdNQndxM20yeFA0MC9mSlAxMUt3Q1d0ZjlxR1ZaNk1uanlEMkkrd0taVml2TGhLd3kxTkE3cGQ3N05DZ2U3eDYiLCJtYWMiOiIxMjMzOGVkNjc1YzNkMWZjMzAyZjI1ZmQ1NzY3NzNlYzNkOTMwMzNmZjQ0NzUzODgwZWMwNTk5MWQwZGM3M2E2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134085026\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1905304419 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905304419\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-280442282 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJYeEVhdmZ0aSszaGF6TXhrSGh2bmc9PSIsInZhbHVlIjoiQ0tOOEI4OWdiRVFIZElRUWFNZFk4dDBhUG9GV1hqbjkySmZjSFhCOFhYemRrUEQwSWJ0V2VxWGhzdFVweWlXbDQ5SUUxdGZjdEZveE9xT0Roc1BWS1p5ZldzNlhMVmxvV0hOb1JncUdqSEdKcHQ5aUY0VHpsakoxV2VIZlhjN3p3MzUyOFZneUcrT0JhdkZqMzVwMXdOQWtHeTlSREliVVkvVXVza3dzSTdvcFg0bEF3RmM4YXI1WU11TjdoRnBEN1c3TzRDT1RyZ1ViRzgzQ0E2NkNXT2FIWjF6Z3A3VzdYODNZTDUrOVgxSExLSUZyZlhPQkQvWTd1a0hYQ0dNMDdUenNHa2JXL0xKNC91cURSWDJvK01jN2o3V2RzQzRhRVBXVnVEYzBTSjNGMXg2RHdnZ1NFTkZTbDZod056ZkpuSmt2Ynd4Wi9pSE03cGdVc01CR0ZOOTcrUTVVb01YSUxWei96ZW94WVJZb3JXWmFCZzFybWRVaHh5aFY0RjZGZzNDQ092TXRrMCtGbnBsSkJrUnBsYTFVZStNTEZ6UEVWd3QyS2VHRVJyZHJMZDRMbTFwSHZ6aktPT2ZQenVZWVlUSDMwVzFsOVRqQ3JoVjBJVkptYlJ0QVd4T2hBc3JsUHlNcklqK05iblNoR0JUZzlBQ09wbWVyWittdXNQb0YiLCJtYWMiOiIxN2FjZTAyMDM5OTA0N2ZhNjVlZWI0MmM5MzlmMWY0NDVlNGFkODM2ZmFhZmE0NzQ4OTMzNjhiMDBlMWE5MWQ0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlYrb29LeFFhamJyaEN4ZlJTdjlkelE9PSIsInZhbHVlIjoiSHd5LzlHcmxZRTR3U0VHUXBDWTQzNkNHaktJZk04cjZwZmtKRFhRa2psVU5Qa01kMDYzTmVJOU1sNjhZTm5ST2NLQjRWZkM4WEo4TmU3N3UySmtEV20wN2haN3FndEhiSk9TaUZ4N1htVEsxWG5JVnFsemNVRjU5M3JZZGRZTFBXOGN1bEp2MVJlOUlQY1pVR2krQmVPZVc5S2loMGJINmg5aUZhRjZ1bUNpYmEzWENrbEY1c1BMYnBQbDJhcHB0bW5vTEp4UWpPY0gxUzNMUlIzYkltY3drbmU1eHRiYW5qQTlvbU1iSitWK3d5S2p6VWdkaDlVRkszMXNFLzNsdzhseDhDaDdONzZ2cWF4Tk1uZzVzWjkxTE5Ma3Z6dHBNMVV5MUt6ZkRUZzlhUVQ4aXQ1QkNvTGJ4d3hXV0V5RFFDMVVjdDZlbEx0VENvOHNOZFZHMEE0S2dwSE5MYW1PeFBWZEZnT2Q0a2RzcUN5UW9xMW1QK05CaGFjRWQ5eTNEQUhLcHU5OW1YZjVDWU5YWjhURDJEcGtlVndKcDVLcVlLd3NFVWJxNnpVb1NrYVZ1NW9LcXEyWGY5eHhBblE2R2cwdzJaUEhObU9ZajdZeTVsbVBYSVUrWjhHV1dhcEhSZFNVMlY5MVNnRk04bjh3ZGczZUlSamNwKy84dm5XODgiLCJtYWMiOiIzM2VhMWJjMWQ3ZGFmMmMxMDRlZmYyYmIzODQ4MzVlMGVhZTUxMmY4NGIyNThmMWM2NTcxOGJkNGFjNWM1ZTY5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJYeEVhdmZ0aSszaGF6TXhrSGh2bmc9PSIsInZhbHVlIjoiQ0tOOEI4OWdiRVFIZElRUWFNZFk4dDBhUG9GV1hqbjkySmZjSFhCOFhYemRrUEQwSWJ0V2VxWGhzdFVweWlXbDQ5SUUxdGZjdEZveE9xT0Roc1BWS1p5ZldzNlhMVmxvV0hOb1JncUdqSEdKcHQ5aUY0VHpsakoxV2VIZlhjN3p3MzUyOFZneUcrT0JhdkZqMzVwMXdOQWtHeTlSREliVVkvVXVza3dzSTdvcFg0bEF3RmM4YXI1WU11TjdoRnBEN1c3TzRDT1RyZ1ViRzgzQ0E2NkNXT2FIWjF6Z3A3VzdYODNZTDUrOVgxSExLSUZyZlhPQkQvWTd1a0hYQ0dNMDdUenNHa2JXL0xKNC91cURSWDJvK01jN2o3V2RzQzRhRVBXVnVEYzBTSjNGMXg2RHdnZ1NFTkZTbDZod056ZkpuSmt2Ynd4Wi9pSE03cGdVc01CR0ZOOTcrUTVVb01YSUxWei96ZW94WVJZb3JXWmFCZzFybWRVaHh5aFY0RjZGZzNDQ092TXRrMCtGbnBsSkJrUnBsYTFVZStNTEZ6UEVWd3QyS2VHRVJyZHJMZDRMbTFwSHZ6aktPT2ZQenVZWVlUSDMwVzFsOVRqQ3JoVjBJVkptYlJ0QVd4T2hBc3JsUHlNcklqK05iblNoR0JUZzlBQ09wbWVyWittdXNQb0YiLCJtYWMiOiIxN2FjZTAyMDM5OTA0N2ZhNjVlZWI0MmM5MzlmMWY0NDVlNGFkODM2ZmFhZmE0NzQ4OTMzNjhiMDBlMWE5MWQ0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlYrb29LeFFhamJyaEN4ZlJTdjlkelE9PSIsInZhbHVlIjoiSHd5LzlHcmxZRTR3U0VHUXBDWTQzNkNHaktJZk04cjZwZmtKRFhRa2psVU5Qa01kMDYzTmVJOU1sNjhZTm5ST2NLQjRWZkM4WEo4TmU3N3UySmtEV20wN2haN3FndEhiSk9TaUZ4N1htVEsxWG5JVnFsemNVRjU5M3JZZGRZTFBXOGN1bEp2MVJlOUlQY1pVR2krQmVPZVc5S2loMGJINmg5aUZhRjZ1bUNpYmEzWENrbEY1c1BMYnBQbDJhcHB0bW5vTEp4UWpPY0gxUzNMUlIzYkltY3drbmU1eHRiYW5qQTlvbU1iSitWK3d5S2p6VWdkaDlVRkszMXNFLzNsdzhseDhDaDdONzZ2cWF4Tk1uZzVzWjkxTE5Ma3Z6dHBNMVV5MUt6ZkRUZzlhUVQ4aXQ1QkNvTGJ4d3hXV0V5RFFDMVVjdDZlbEx0VENvOHNOZFZHMEE0S2dwSE5MYW1PeFBWZEZnT2Q0a2RzcUN5UW9xMW1QK05CaGFjRWQ5eTNEQUhLcHU5OW1YZjVDWU5YWjhURDJEcGtlVndKcDVLcVlLd3NFVWJxNnpVb1NrYVZ1NW9LcXEyWGY5eHhBblE2R2cwdzJaUEhObU9ZajdZeTVsbVBYSVUrWjhHV1dhcEhSZFNVMlY5MVNnRk04bjh3ZGczZUlSamNwKy84dm5XODgiLCJtYWMiOiIzM2VhMWJjMWQ3ZGFmMmMxMDRlZmYyYmIzODQ4MzVlMGVhZTUxMmY4NGIyNThmMWM2NTcxOGJkNGFjNWM1ZTY5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-280442282\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1895815068 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Opening Balance has been set successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1895815068\", {\"maxDepth\":0})</script>\n"}}