{"__meta": {"id": "X9dd787b3dd9d70f9ec9ec2acd51cba78", "datetime": "2025-06-28 15:26:41", "utime": **********.222662, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751124400.788852, "end": **********.222676, "duration": 0.4338240623474121, "duration_str": "434ms", "measures": [{"label": "Booting", "start": 1751124400.788852, "relative_start": 0, "end": **********.155181, "relative_end": **********.155181, "duration": 0.3663289546966553, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.15519, "relative_start": 0.36633801460266113, "end": **********.222677, "relative_end": 9.5367431640625e-07, "duration": 0.06748700141906738, "duration_str": "67.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45727464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00752, "accumulated_duration_str": "7.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.191899, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 23.138}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2039342, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 23.138, "width_percent": 6.117}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*************%' or `sku` LIKE '%*************%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*************%", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.206632, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 29.255, "width_percent": 35.372}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.212215, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 64.628, "width_percent": 30.851}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1995}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.216768, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1995", "source": "app/Http/Controllers/PosController.php:1995", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1995", "ajax": false, "filename": "PosController.php", "line": "1995"}, "connection": "kdmkjkqknb", "start_percent": 95.479, "width_percent": 4.521}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1891086222 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1891086222\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1038908356 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1038908356\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1586262758 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1586262758\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1321096019 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124397135%7C17%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBSSytZeUJuaW91VE9LSnpGNWw0dFE9PSIsInZhbHVlIjoiYXNDUmd4S2JrZzZUcWdObkc1U2ZvQXFWQ0NpMkYyZ3RxMjNMS1lyQXAwN0dseGx5TTkydEFzZFhhUFM1K21zY3orc1craWZndUZQL1hsRFpZWnZXUVcrK2R3UWdBVG5kbm9GTGdaaFJ3QUNPb3NDbWpFL3V3ZGk0MVEyRjFHK1lURnhvR0w1aWJYTFdIejAyTTdQNHJoeVdOSGNIWEp2cDJkY2YraXpyM1hVM3U0K2ZYU2RFT1gwR0IvQ0hOS2hVc2ZvTFFuT1lGdnlhTGYwdldJc0NsWVZIZGFlVjBFMG1YZUtBblAyOWNvc0dPNTdzMHY2eTFCclh6MmFpR2U4cFpieEtPWGxkQ0d4MkkyOGZIL1h6eU00aHhuUW0xbHpidzl0V3NEczF5KzJlK1lmM056OGdRenBQZml2cXdKTk5sRGtycDNhaGJsbGpXZ0FKRXFSckxZUkJ5Q2I3aGNvODYvNE9SeW81RkpOc2k1cGVlcHJ6c3FsMHFJMGpjWG5tRzd4eldKQ2h2UXI4L2JCdTUxZE1PcU1BSHNZVUJTaTAzYnA3N3VjMFJMUzlLanRQSkVjUlUyc1Q2Y0xwUXZvREFDSFdTTzdsUnQxczdyMzFHc0NUbmF3YkF4Qm5ENUtyUmJMcENtSWhubTNxZkNPd0VudWMyRmdDTXRJemhveU4iLCJtYWMiOiI3OWQ5YTU2Y2IwMGE5NDkyMjdmNWY4YTMyMjg3Nzk0YTA1NTkwMmRiOGY3MTBlZGI2OWYwMjk0YzAxNTdlMmM2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtxdUtJOFA3Tm1lYS9HWCtPL2prUWc9PSIsInZhbHVlIjoiZEJxaHhtZVFaSmRrSjBubW1JTVpuSUxtRVF6eEUzZHAwNnl6dzVPV2dyZVZkNUZ3MWhpV0NHandybWlSY2FlQUJMMWY1ajYvbGFJUTNuTnVISEJyd0d3UTJuUWp6bDhURlY4VG9Qa3V5RHlOM1Vjak11cVNDbFFIZFY0MDFyOGtWWlFpWFpVRzZuTlI1ODZoQmYzUThUcjR3dk5jbXdxTlI3YWZzaTZUV2pPQmp0eVM2dlhxcE1nUjVlbVhvSGl0ZnBxVDhGWUEvUkFQTjRmL0EwdHdQL3JER0RaaGVwNmYvRU5vaE5PS09NdkxJNUNiLzBCVDBSc09NOHNNbzRwRkpTOWpZaWJjNi9aaGliS25nekw2NkhIRmxPVDlNVUdkZWZpQUIrTnhOM1kxcitTYW12NjNpTUFhU1lGdHBTYVB3MVcxb3k4b3BKV3NDNFAyVzFzL1BBSGhqMTNvM0FmTytYWTl4SDlzWmFlbWc1Y3EwM0RMeFZ3NEovWW1hZDJGSk9FMDhGWlYxc2lDcDFpK24wTTVKS0xZRmtaNnJjU1gxSlhKWkV3dFNKYTNYbE5Icko0eFhQNVZGeWJuL3lVRXcybzQyWlAySmRvVnJQaVpqbTRCNUxtY2JhZThBRExVMDV1cDViSGFPZm5DWVNCQ3NZbkdOMGdDRS92SzZLR0oiLCJtYWMiOiJjZWI0MmFlNGJlYTU0N2Y5YTY5MTRjODNiZTcxYjZmMjkzNzJkOTk0YjMxODVkNGIxNDJkNWUyYTFlODIwMTE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1321096019\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-411768375 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411768375\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-221141284 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:26:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxlOTc2TE03SCtDVm50L002c0g4TVE9PSIsInZhbHVlIjoiVW1oZ0d4QjJrWEN0MEtRazRtUFU0M29Tcnl0b1MvU2NiNGRKVEhuVHYvZkg5aHdCMTdRWTJjQjNIUXRpc2JGQ3FWWHFoVHRLTTN4Wld5Q1BXajdLdER3TGd1aTMzUmdrM3VYeEc3Ky9ZWU1nTDd3UWU2L3VBRXNDOHNCd1ZkNEJKQ2p0bHJHWTdFcHV0akR4UnArTDR0ZUphU1FEQTZKV2JHQWk3RUp3Q0VFZjdaZUpmU05lTUs1Ykk4ZW1qTk5hU2E4TXRVdDRuWXBFYy9QQnNWRXJCOU1QTkFMQ2JJTCtEWkhmUmJoZFljTGNLTCs1TXZDc3lVNHlBcEFqalJOaHplbm1HSC9IdlJZR0xaTkhiS1dsYmtZVG1iajd1d2s2bGVad1FXVW9yL21Lbm0vK0N4RW4wZUc5enZpZkJ3TWlRREFYZmpKQXdpR0g5WGpiODByTzNUdnZqdGtFbkh3ejFQc1VtcjA3U09ocGRCRXRGU1JWaWNWTG1PY2xEbkdKNFNMUXc1Q1NPd2xWakt1N3M3MGZJQzEyaVRyMTllY04wOVVibGg5Tk5IWGhza2V0Sm1hUjVLRU1SbDQ1VDhoVnZPc00wNUV4amFaUkc5QTB5YUU2Y0J5bENjVHNvNEtjWmFtK3FQUndTWVErTmYzTitsdElsVTdsUUszZXFvUTEiLCJtYWMiOiIzZDEwNWIzNjdkOTRlNDJiMmNiZDY5Yjg1ZTUyYTBjYzkwODNiNmJjZTYxMjAyYTU3MDU1YWU1MWVjZTJlYjFiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1pOW03Z0l3ejJZS3ZLRDNDTE1hU3c9PSIsInZhbHVlIjoiUjBhVVRLYWQzTnNoYit5RWRBWHJPZk90K1RSUDJ4N2VrMEFiai9IcEk5akM4MjBvaS9US2FodjhWd2hEU3hYbFY4UzFycytZdGRDdGpiNVZRTTJlcS9UajZMWkR5WG5sQ3M2cSt4dU9UbjN5UENCeGdVMnJyM3BnR1I0Z3c3Zkc4V3NKRXlrNzQxMlhLUllPV2ZJV1VGZW5IYlczUHphVitKdU1mQWZRcm1TSWNraHBSRlFiMWV6WkY5N3UxblZ5bkJCaGthSnRzUHZNbnNtQ2pMTGxKM3h4bktKZTdoZWNmNDUyeU9JOXRId2owTU1Dc2psQ1hWKzc1V241LzhSNHlGWnlURlNocTA4N25FbUE5cEVQOWF5dzVOOW8rbklVOGVyTkdXK1lncHJ0VFo2SDZkQk80cklOY2w1bWZFaWZaSDRsa3R3bkhLbnpQbWptank4aUZVeklrdTlEQjRWcHdOR3dtZXZtc3N4UXZYUDZDQ29tRWVyaWN5b0p1Tk5WaVJROTN5QU1MZFFYZmdqY2U0eDB2SDJhQUtwMUNjSnJ3enVRRXlRSTNsblA4elVrU0NqQnlhbE4vL3pBUVF6V2JqTWJybXI5NXQxR3ZJbjdobndUS2YwTHU1aTMrRVNtWE9qT0llKytON0JXQnpNbW4wampVQlE1YnV1VmhvUlUiLCJtYWMiOiI3NGU3YmJhY2E4MzhjOTY4YzU0NWZjMmUxYjA4MzM4OWZkYjE0MzllZmQxNjJlOWJiYWJmNmExNDNmMjJhYWQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxlOTc2TE03SCtDVm50L002c0g4TVE9PSIsInZhbHVlIjoiVW1oZ0d4QjJrWEN0MEtRazRtUFU0M29Tcnl0b1MvU2NiNGRKVEhuVHYvZkg5aHdCMTdRWTJjQjNIUXRpc2JGQ3FWWHFoVHRLTTN4Wld5Q1BXajdLdER3TGd1aTMzUmdrM3VYeEc3Ky9ZWU1nTDd3UWU2L3VBRXNDOHNCd1ZkNEJKQ2p0bHJHWTdFcHV0akR4UnArTDR0ZUphU1FEQTZKV2JHQWk3RUp3Q0VFZjdaZUpmU05lTUs1Ykk4ZW1qTk5hU2E4TXRVdDRuWXBFYy9QQnNWRXJCOU1QTkFMQ2JJTCtEWkhmUmJoZFljTGNLTCs1TXZDc3lVNHlBcEFqalJOaHplbm1HSC9IdlJZR0xaTkhiS1dsYmtZVG1iajd1d2s2bGVad1FXVW9yL21Lbm0vK0N4RW4wZUc5enZpZkJ3TWlRREFYZmpKQXdpR0g5WGpiODByTzNUdnZqdGtFbkh3ejFQc1VtcjA3U09ocGRCRXRGU1JWaWNWTG1PY2xEbkdKNFNMUXc1Q1NPd2xWakt1N3M3MGZJQzEyaVRyMTllY04wOVVibGg5Tk5IWGhza2V0Sm1hUjVLRU1SbDQ1VDhoVnZPc00wNUV4amFaUkc5QTB5YUU2Y0J5bENjVHNvNEtjWmFtK3FQUndTWVErTmYzTitsdElsVTdsUUszZXFvUTEiLCJtYWMiOiIzZDEwNWIzNjdkOTRlNDJiMmNiZDY5Yjg1ZTUyYTBjYzkwODNiNmJjZTYxMjAyYTU3MDU1YWU1MWVjZTJlYjFiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1pOW03Z0l3ejJZS3ZLRDNDTE1hU3c9PSIsInZhbHVlIjoiUjBhVVRLYWQzTnNoYit5RWRBWHJPZk90K1RSUDJ4N2VrMEFiai9IcEk5akM4MjBvaS9US2FodjhWd2hEU3hYbFY4UzFycytZdGRDdGpiNVZRTTJlcS9UajZMWkR5WG5sQ3M2cSt4dU9UbjN5UENCeGdVMnJyM3BnR1I0Z3c3Zkc4V3NKRXlrNzQxMlhLUllPV2ZJV1VGZW5IYlczUHphVitKdU1mQWZRcm1TSWNraHBSRlFiMWV6WkY5N3UxblZ5bkJCaGthSnRzUHZNbnNtQ2pMTGxKM3h4bktKZTdoZWNmNDUyeU9JOXRId2owTU1Dc2psQ1hWKzc1V241LzhSNHlGWnlURlNocTA4N25FbUE5cEVQOWF5dzVOOW8rbklVOGVyTkdXK1lncHJ0VFo2SDZkQk80cklOY2w1bWZFaWZaSDRsa3R3bkhLbnpQbWptank4aUZVeklrdTlEQjRWcHdOR3dtZXZtc3N4UXZYUDZDQ29tRWVyaWN5b0p1Tk5WaVJROTN5QU1MZFFYZmdqY2U0eDB2SDJhQUtwMUNjSnJ3enVRRXlRSTNsblA4elVrU0NqQnlhbE4vL3pBUVF6V2JqTWJybXI5NXQxR3ZJbjdobndUS2YwTHU1aTMrRVNtWE9qT0llKytON0JXQnpNbW4wampVQlE1YnV1VmhvUlUiLCJtYWMiOiI3NGU3YmJhY2E4MzhjOTY4YzU0NWZjMmUxYjA4MzM4OWZkYjE0MzllZmQxNjJlOWJiYWJmNmExNDNmMjJhYWQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221141284\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1107289425 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107289425\", {\"maxDepth\":0})</script>\n"}}