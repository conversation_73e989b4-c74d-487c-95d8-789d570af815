{"__meta": {"id": "X76e892d0db87bbb54d8f911d82e5b71a", "datetime": "2025-06-28 16:02:24", "utime": **********.412268, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126543.981631, "end": **********.412286, "duration": 0.43065500259399414, "duration_str": "431ms", "measures": [{"label": "Booting", "start": 1751126543.981631, "relative_start": 0, "end": **********.35361, "relative_end": **********.35361, "duration": 0.3719789981842041, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.353619, "relative_start": 0.37198805809020996, "end": **********.412288, "relative_end": 1.9073486328125e-06, "duration": 0.05866885185241699, "duration_str": "58.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45695872, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00217, "accumulated_duration_str": "2.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3793402, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.673}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3892, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.673, "width_percent": 14.286}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.394398, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.959, "width_percent": 23.041}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "[]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-944688518 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-944688518\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-616839040 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-616839040\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1263536315 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263536315\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2094301915 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126540952%7C11%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImU5SWlXY05teXBNQkd4M2xHaERORFE9PSIsInZhbHVlIjoieWVxaDNjYkJmU08rL09aWkg0M1dmMUd2S1RxTXRZU09PRXBiNXJQeFkvS0R3VU85OVYzQlBSRmhoamRMSE1NbnFZSXI4ZUJyWi95MXNHeHp3VnNpWkp5UEd3VmhIUWpqWGs4RkN4VGREZFdpSXBGdS84dmRhcEtmQVMxVGxMb01vUXk3V3pxUWFQTTV6TURnbUhNc05zK1krVmNlMmFFczFuRjVhajJrcGNVZ0tnVk52eUthSXVaQllrMTJhY1J6RGhoVDJwcklWTWw2YXhuUEpqY0FWcHN6YzV1YmFweDhkZ01wVnZWeUhha2E5L2NMaGZNa0tyTzg2b3BWU0Q1a2o2Z2txK1FEYmJPVDV6KzByYmNTV09KZy9ETStFck1LV2pDQmhMd09FN2VvUm9VSTNuOU1xbUxxWVVuT2kxNXY5ZmNNY3I5c0h5Y2YyN1BKNTNQcXdnZDlRV0VEZEQ1UGNjaCtpZkdIZTU5ZHVFckEwakxPRWIvUExUV094SnBwT2RtKzdqVzN5ZlpOUm8zN2ZZWldJS29xSVBlUHVyRWIyUk9wUmVJRldRN0tkVTlyamliQnowUkxlOFdwY3UzSURBOWdnZHVxMlNRS2luUGNTUVdoU1g4aDR4Rng3SmZNSTFsNGt2dWFIQXkyUG9Wc2ZxYXN0eFdWQTkzOXB1NkYiLCJtYWMiOiIwODI4MTYyODNkN2I2Y2MwYmE2MjZmZjFlZTAzZDcwZTVhYjlkYjc5YzMwYmJkN2Q2MmUzZGVjZDJjZTNlZGMwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkU4b0RMcHdMVUZlK3R3UVBrYThlYUE9PSIsInZhbHVlIjoiUmhmNVNtMHpMUnZTRWliSkwrVzRSM0k1SEgzN2RKeG14YWRoNi9Sd2kwdFpWV2o2NFgxS0FYOVBqeG85aXdJa1BaVVJhb2ZtckY5Z1dyczlSeUFXUE93Y0FtZUtyZjhWMTFPYnljWUdBTFY5dGJpRkNwNGpRblpqSkdxd1poMlVuOFJxQjJ2RU40YTNBUytsVW1KQ0Q0bTNKd2Z2WVY0dHF6UHdncU12NWdaSDlsM2RHcVFtVDQ5M1k5aU4zYnVHMFBKcERTQW5BYk0zdktpWG5lR1grQUhGN2dvZHRWb0dnMTdwcUg3YWFZbTQ5eDBKNk00OEUwdmd6NjdLRUcyaEhHZzF5MWEwTDNyYTR0NDVvTHBDckhlUFpPSHJZTzAxcVJITWZUc1hkZUZYaElZbUx1ZXhvNExLaitJbncxSkExb080S0tNc3p4WG4vZGN5OTZLK2Z1cHlNY0lZeEFGeVhuNGlYb3BlUGtjU1JPV1NLTXRvcEdRcWdOTzd4NDJhREMzVWNDNGh1MzZyUzJ4VStaRExmM0kwMGMvOWovVWRiemNIZFE2OHhoRjh5NkdxaGU2UGFTMnZXS3ZoaDdRQ0dBYnM3S01OK1JDdkZQb0tONWYwTjJmcWFSR0FBTzFldStrVXpEcWY0MmM5RkNuajhUQW9nSnduVTZkTWdYYnQiLCJtYWMiOiJiZTVlOTVkZjYzZDQ0YjQ2Mjc4MDZmYWYzZWM3NzRjMGM1NzZhNzY1MmEzMzk0NGYyZTczNjFjNzBkMzQxYzU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2094301915\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-867369413 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867369413\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1527799959 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjcwMW92bHpad1VaVWloNGdZY25kc0E9PSIsInZhbHVlIjoiZ2NSMFdFWjZvcmphVGwvWkdrL3VVL2JHNFRLYzNOTEE4eERJUy9Ib1lPYlMwN240QzR4bkp5REZKa21iOVFXck9yRlQrd2xxRGpVSFJLWFJJVXovL21UUG40TlJPL2I4d2Z6aWlsNmFSRVdNbFdEWnpBNjZFUlJWd05rTXppdlRjSld5RE5SVzNHVU43VzlQbmZ6aG1MYzVHL0VVMXFRZWNZdUo3c3FyNklyRDM2QVhzQnowNW8zZ3FXejhLeHVaQjRzbW1QMDMzSlRDSHFVS0I2RFAwZGZkR2hTeUVPb2lnb1pqOE0zK2dqbjRjT0RreCtnUkNIdVYyN1h1aW9ZU3VqZU1FRUl2bm40bkRITW5BbDVjZm9qd0l1S1NVT2s1U2RKdHpEWUovdmNuV2tkdS8wTjUrQ3JFUEpSZUlTWGhHUGRSempQVTQ2OTBqMVFMeFZwMkE1cGR4VzhHam5GeGs2Q0s1SXBQR0U4WU9sL2lSdUExRDd0eGRhRk91SzMvSE15WEQ3UzIxYzVmMDJyZWV3dmMzL3UrbHZ3N2JiaW9QSElaOG44c1lQV2QzM0ZjRXppZlpEbHpCeEVyNk84Um9PbERGVlB3ZWFnMnhvdVdNdFBwekVBRk04UEp1TSsreUVOMmRCd1RvUTR0UTdLZFRGYXhCWExtay84NnJzdmkiLCJtYWMiOiJmMjYyZjM0MGE1Y2JkODU2OGNmNDU1ZmRmZDg5MjIwNGMzMTViZDBhOTg1NGVkYTQyNWZkZDc0YWQzMzUzZWJmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVXS3RVbkVYNWc5UlRvODNDT2dENHc9PSIsInZhbHVlIjoia2VkZHJrTkZtU0xCUDJIb3RzVXBBNXdENHBlbTNlaUkrdi9NTGJydXdzeE53c3JVQncra1N6S3hZWWFSZGhrWU1MbmkvWHpUMG9JbExNUVNqa0wrU2F5amVYYVI5Y0dOL21NMlBlcnJHWUNQUUlMVm9SNm9LNkh4d3NuYjNOSUVDK0xybG12QnM4WDVBY3J1STZIQzVvWDZ0c1lmc3MvMlNmSmFBamZsWHpiSUt4VTdwdGUxSnI5U2VFWDdYMEFBK0xpWXd2U29MWURBZEkvb1ppSUgrQnlGbFFwTjljL0tmempiOFVzMm9vTGNzNE44SzRLd0d2ZVpIQ3RWbENtcjBWRHBTWGd2dWZ2TnJFajRDZ1BVdTRvZHdrVlBERHdjekNQMmdwR3d6SCtqbU01VDZGYUlqRGZGTXc5anErNFNDM1FwdytGYnJidWxodTRweXAxWXJnaHR2cnB6a29VcHhmdTZIdVhOQ3Zoc2hRdExkN1pLbGduODVGcXJEaThUWGhDVzZhMzQybDRDZm5kWCtWTVg2TnJvNVE4T0w3NlBpQ3hnbXpmc0FvNmpPSFBMTVBCOTAzZzZ1LzhiZ002Q08rcTV2dFhDNHZLQTM1aEJNZ0tUcDJBNnBMUnRwYVVEdFlrdk50dmNSR0lmakVOdGtQQUszdTZ6RndMSGVBWGciLCJtYWMiOiIxMGY2YzQwOTQ5ZWRjZTQ4ODZkMGEzOTYzMjQxMmQ1Y2Y5YTk3OGFiZThkNDIyOTMyYWNmM2FkZjg2N2Q0MzY4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjcwMW92bHpad1VaVWloNGdZY25kc0E9PSIsInZhbHVlIjoiZ2NSMFdFWjZvcmphVGwvWkdrL3VVL2JHNFRLYzNOTEE4eERJUy9Ib1lPYlMwN240QzR4bkp5REZKa21iOVFXck9yRlQrd2xxRGpVSFJLWFJJVXovL21UUG40TlJPL2I4d2Z6aWlsNmFSRVdNbFdEWnpBNjZFUlJWd05rTXppdlRjSld5RE5SVzNHVU43VzlQbmZ6aG1MYzVHL0VVMXFRZWNZdUo3c3FyNklyRDM2QVhzQnowNW8zZ3FXejhLeHVaQjRzbW1QMDMzSlRDSHFVS0I2RFAwZGZkR2hTeUVPb2lnb1pqOE0zK2dqbjRjT0RreCtnUkNIdVYyN1h1aW9ZU3VqZU1FRUl2bm40bkRITW5BbDVjZm9qd0l1S1NVT2s1U2RKdHpEWUovdmNuV2tkdS8wTjUrQ3JFUEpSZUlTWGhHUGRSempQVTQ2OTBqMVFMeFZwMkE1cGR4VzhHam5GeGs2Q0s1SXBQR0U4WU9sL2lSdUExRDd0eGRhRk91SzMvSE15WEQ3UzIxYzVmMDJyZWV3dmMzL3UrbHZ3N2JiaW9QSElaOG44c1lQV2QzM0ZjRXppZlpEbHpCeEVyNk84Um9PbERGVlB3ZWFnMnhvdVdNdFBwekVBRk04UEp1TSsreUVOMmRCd1RvUTR0UTdLZFRGYXhCWExtay84NnJzdmkiLCJtYWMiOiJmMjYyZjM0MGE1Y2JkODU2OGNmNDU1ZmRmZDg5MjIwNGMzMTViZDBhOTg1NGVkYTQyNWZkZDc0YWQzMzUzZWJmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVXS3RVbkVYNWc5UlRvODNDT2dENHc9PSIsInZhbHVlIjoia2VkZHJrTkZtU0xCUDJIb3RzVXBBNXdENHBlbTNlaUkrdi9NTGJydXdzeE53c3JVQncra1N6S3hZWWFSZGhrWU1MbmkvWHpUMG9JbExNUVNqa0wrU2F5amVYYVI5Y0dOL21NMlBlcnJHWUNQUUlMVm9SNm9LNkh4d3NuYjNOSUVDK0xybG12QnM4WDVBY3J1STZIQzVvWDZ0c1lmc3MvMlNmSmFBamZsWHpiSUt4VTdwdGUxSnI5U2VFWDdYMEFBK0xpWXd2U29MWURBZEkvb1ppSUgrQnlGbFFwTjljL0tmempiOFVzMm9vTGNzNE44SzRLd0d2ZVpIQ3RWbENtcjBWRHBTWGd2dWZ2TnJFajRDZ1BVdTRvZHdrVlBERHdjekNQMmdwR3d6SCtqbU01VDZGYUlqRGZGTXc5anErNFNDM1FwdytGYnJidWxodTRweXAxWXJnaHR2cnB6a29VcHhmdTZIdVhOQ3Zoc2hRdExkN1pLbGduODVGcXJEaThUWGhDVzZhMzQybDRDZm5kWCtWTVg2TnJvNVE4T0w3NlBpQ3hnbXpmc0FvNmpPSFBMTVBCOTAzZzZ1LzhiZ002Q08rcTV2dFhDNHZLQTM1aEJNZ0tUcDJBNnBMUnRwYVVEdFlrdk50dmNSR0lmakVOdGtQQUszdTZ6RndMSGVBWGciLCJtYWMiOiIxMGY2YzQwOTQ5ZWRjZTQ4ODZkMGEzOTYzMjQxMmQ1Y2Y5YTk3OGFiZThkNDIyOTMyYWNmM2FkZjg2N2Q0MzY4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1527799959\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-765579462 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => []\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765579462\", {\"maxDepth\":0})</script>\n"}}