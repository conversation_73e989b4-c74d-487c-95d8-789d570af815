{"__meta": {"id": "X2bcbe3fd3a5d5dfea68d0f707f1c4ad9", "datetime": "2025-06-28 15:09:05", "utime": **********.463662, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.045206, "end": **********.463675, "duration": 0.4184689521789551, "duration_str": "418ms", "measures": [{"label": "Booting", "start": **********.045206, "relative_start": 0, "end": **********.410775, "relative_end": **********.410775, "duration": 0.3655688762664795, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.410787, "relative_start": 0.36558103561401367, "end": **********.463677, "relative_end": 1.9073486328125e-06, "duration": 0.05288982391357422, "duration_str": "52.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46269312, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00206, "accumulated_duration_str": "2.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.446881, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.184}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4569452, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.184, "width_percent": 22.816}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1457/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1029000847 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1029000847\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1237658239 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1237658239\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-764778382 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-764778382\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-55240924 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123332501%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVpSW5oeEF0K0lvZk55MW1JRWE4aFE9PSIsInZhbHVlIjoiMkdHcGEzQ0ZDaHNFM01zcXN1K0FUdGkzTkJUNzF3bXNWYVZRSXFiQXd4b2hPQnVNZ3JXVVNna2pvNDFsSXhEQVUwcFZUeXhjV2Y2L0J5Qm1tSTRRc2E5azRsbFd3Y3V4NmpvNStDZlc4dSs5ZUt2VFhKTWIxUWl0dWRXaUNkK2o0dE1vSG11T0t2RU51NzJCYVNGdmsveHpoWnRFR3pHUmVtNWJXUyt2ODhXRWpCVFU4UDFFckZQaG95YllIQVhvc1F6THBJM3FlblkzTnV1V3kwUjZBWmd0Zk5RS2J4aDFTRThIOEJYeUx3aDlJOHZKMGNBRnFlZ09KMHRDMWxQWHZDU0N1Zmt6RHNvQXgwYzBFUmp5dW1LQmpxM01NWEd5TmVSNkhPaWU2ejR2bGdYZnJselVSVHBlZzM2ekJyc0Z2cmhBZEU3OG05WXhwcFU0cGFBWjgyQmtmT2hBSDRweGUxbXlleHRSMlFNcWc4cVd2UEJmTTJuQnc5SjlaZnVnc1psSktENU5GMUpVRGp2TXFVdHBhb1R5ZjlzK21vaW11cjBNeDd5U3lVQVZNTy9Sc2hHclJBSTRwNDVZRHlHb1F2WnJFTzVzUC9IcnBYNk5BK0VQVGpFMnNBZTFuUldTazhtQVlGVGtSeXRsNWkyKzlHKzFna1QxeGhXbXBaMjUiLCJtYWMiOiJiNDE4Y2ZhNjY5NTQwZTBiOWYxZDIyOTllOTA1YTg2MjUzYjVkMTY2ZDY2YjcxMjdkZTlhZTgyZTg4MjM3OTFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlNtUVNRSXFqVmhybkhOdVZ0eFp0R1E9PSIsInZhbHVlIjoiVElsVEt1SzZHdVJEU2pyUFpvc3N6NCt4Mnlzc0w3QlBUWGZoakJ2Ujd3NjVwa09CR3drNVJoeitUTE52WEJnbm5Zbm5ieUpSdENvb2VnbTVQOHBsN3VOM0MwUlcwdkZrd1RDZkdPcUY2TmtQS2RhM0dVTFNNekNJUmh3Y1VSWXJPSTVLcDZTMk9BNVIwd2NGNlFINkhNRkt2YTJxUmk3T0FaUkh0eDJkWTBqN2ROWThJNUpZaUdLS1NIeXBydEJDYVlvcW1PbFdsWm5zVG1DUmhZbUFyeElmdnREZVVNeENyMmgwZkFidXZkMXBTUm9jY2FhcW5mRmhRS0hMelgzZi9rL3hBQ0g5c041TUVCeFRhdUUraDZZYTU4ZVM1K0lXcFo1ZFhrejhRazAvbDI5dDBBbDNTTWtXWkdybjdCaXhXejM5Y090L01qS0N5TGs3ek1KVU13Q2xyYXF5YzBzczlsS0w3RzV2R01wTW13NFNzRmxBaUZnbHUzclBSM1JIampBOXFqYXR4OUZxRVdJQjVDNmlCL1FWVGxRaHQ2cWo2RTVVMGc0SVg3Wm9nbjhmekM2Rll5Q0FUN0JHODRJeUxTMDhzVTBuN1hCREI4NlBnSXhtY1lsRThNc3VkeXR2dDNzTGsyaEovMW5RTldERVJ2cmdVOUViUjJSUThCaUUiLCJtYWMiOiI4MjVlZWYyMTU3OTJjMmJmNmM2ZjUyY2VlMTVhOGYzMzUyNGQwZDYyZDgxNmI1MWRhMThiOWIzODFlNDdkMzcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55240924\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-573348641 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:09:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImF3TCtVTDVXcGlSOUhNT2twNlFyVXc9PSIsInZhbHVlIjoidmIvRnErbjNIU1l1TjVxckZEZ0VjMk9SVDhwWDdYdzdkN3VrNFg2Rk5ScWZ1dTlqZE5HSjFNcHdoVktJcVh4dGNvQUdDcDRKVHdwa0dLNDVrZHNhKy9KUUNyMnFpdVl3dmh0T2hLeUl4V3UvK2lSczdLSmpuRk1sVEUrK2xwQU1ScnZMWE4xWXFQckpxOFRxU3ZVRE1ySGJrbDI0SFpUNnhBM1QwS1djR1lnQ1Z5ckQzOUF6WmJ5emNmdFJoYlNEdCtsc3BoNVBndXNFZG56czI5Q05IMnZyZEFJL21RVHlTWk83Y1NTa1FEcHVtVlh6YzBHSGRlNkl5eERmaStXVmoyT0hJc2t1NnNnbkl4ZVNWL1REUDgyZEhrNUlaOVJaaXp2d25UYjd4U0FEU0FvNXlDYmJjM3AzNTFOdUlLY3ZXRk82VGU0OVpUclRmRW52V3Nwb2NtanFCaGNrL0NZcTFVdkp6Zms4VUZVYUQ0akV5RXFOcU1BRG9GajFNWVFSanJrVU1jekJHeWh1S2Vmam9BZnd2T1VDM0E4R2syYWlmN2ZycVNCUnl0Mmw4SnY2NHNXZjJISlk5YzBmT1N6Z1YvZ2V4bVFOQXdwZzFra1ZBdXZEeHd3SHJwQ2U3aHJjZ2Znd2FKOWFaWmZHSXkvSDNpM0JrUDlHRjdTTi9yOWsiLCJtYWMiOiIyY2Q5Njg3Y2M3MTFlODYzNmY4MDM1ODlmOGFhMDBiZTIxNzcyZTZiY2VkOWJjMGM3ZjFiMWIyYzc2YzNlNTg2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:09:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Inpyb3JuWEUyVGdYc2hBMkdZUW9qaUE9PSIsInZhbHVlIjoiTXV4R1ZKWGYzUGdsK0kxWkUwS09MdEloWk9uckNVUExvQy9rN2FZMWg0REgrQk9aVEpZRFc1Y2F0bjQ5QUNLM1k0cHpvTkI0S0VRM215UjQ0NCtGekpMb0s2a0JYTXNaTk1uVHcvMWpRUEthUkpZT0xLdlFKMG5GZDZrRFZLNk5PMzJ2bDcyVkxWRndTQmR3SGlYT0lqMlVWcmw0Uit1RmlsOFZxU1MvSUdkcFU3RFdRTEdpams3ZkN1YmJiSUNRL2REcDdSVlA1N21OQUhpVWhURSt3U3M2NXNNakI2dnZMeG12OEZLR1ZiYlNDcWcySVJSb3NSRzFJTXk5cEYyRy90L1VXZzNMSUxPbWE3dHJQU0ZtL0FZNkhNaEVHMDZSZysvTTZJaHYwbTlmdGdRZE5TSnJxMW1ZNkpCQzFzbkN5MTVLWTE5MkRISDEyLzRRY0lJSTlxRmZxOGZIOVRlaUxXQzY5WFhDNFI1ay83bC9qd1ZHUytWOTNQMnUrYThIZEViL21CQzYrYzltSzFWZHd5U0RJSDVxNGZkcU1WeUF2M1UxNDVMcUNvK2loVExZWWlDZEprTjJWbGErZ1JqNTZvNHNNbGswWGkxYW4rNFk4V2hxLytVT1ArWDd3UE9xeWczdDVLRG5oOUNrdENBOUdRMm1IMCtxblB3TFdnYUgiLCJtYWMiOiJmNDk1ZDY0YzQwNjk2YmMyNjQzYWJmOGM3NGFkMDVhNWE1YzUzYzU0OWY4ZTNkNDk2OTAwYjg2MjE4NzA0MTY2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:09:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImF3TCtVTDVXcGlSOUhNT2twNlFyVXc9PSIsInZhbHVlIjoidmIvRnErbjNIU1l1TjVxckZEZ0VjMk9SVDhwWDdYdzdkN3VrNFg2Rk5ScWZ1dTlqZE5HSjFNcHdoVktJcVh4dGNvQUdDcDRKVHdwa0dLNDVrZHNhKy9KUUNyMnFpdVl3dmh0T2hLeUl4V3UvK2lSczdLSmpuRk1sVEUrK2xwQU1ScnZMWE4xWXFQckpxOFRxU3ZVRE1ySGJrbDI0SFpUNnhBM1QwS1djR1lnQ1Z5ckQzOUF6WmJ5emNmdFJoYlNEdCtsc3BoNVBndXNFZG56czI5Q05IMnZyZEFJL21RVHlTWk83Y1NTa1FEcHVtVlh6YzBHSGRlNkl5eERmaStXVmoyT0hJc2t1NnNnbkl4ZVNWL1REUDgyZEhrNUlaOVJaaXp2d25UYjd4U0FEU0FvNXlDYmJjM3AzNTFOdUlLY3ZXRk82VGU0OVpUclRmRW52V3Nwb2NtanFCaGNrL0NZcTFVdkp6Zms4VUZVYUQ0akV5RXFOcU1BRG9GajFNWVFSanJrVU1jekJHeWh1S2Vmam9BZnd2T1VDM0E4R2syYWlmN2ZycVNCUnl0Mmw4SnY2NHNXZjJISlk5YzBmT1N6Z1YvZ2V4bVFOQXdwZzFra1ZBdXZEeHd3SHJwQ2U3aHJjZ2Znd2FKOWFaWmZHSXkvSDNpM0JrUDlHRjdTTi9yOWsiLCJtYWMiOiIyY2Q5Njg3Y2M3MTFlODYzNmY4MDM1ODlmOGFhMDBiZTIxNzcyZTZiY2VkOWJjMGM3ZjFiMWIyYzc2YzNlNTg2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:09:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Inpyb3JuWEUyVGdYc2hBMkdZUW9qaUE9PSIsInZhbHVlIjoiTXV4R1ZKWGYzUGdsK0kxWkUwS09MdEloWk9uckNVUExvQy9rN2FZMWg0REgrQk9aVEpZRFc1Y2F0bjQ5QUNLM1k0cHpvTkI0S0VRM215UjQ0NCtGekpMb0s2a0JYTXNaTk1uVHcvMWpRUEthUkpZT0xLdlFKMG5GZDZrRFZLNk5PMzJ2bDcyVkxWRndTQmR3SGlYT0lqMlVWcmw0Uit1RmlsOFZxU1MvSUdkcFU3RFdRTEdpams3ZkN1YmJiSUNRL2REcDdSVlA1N21OQUhpVWhURSt3U3M2NXNNakI2dnZMeG12OEZLR1ZiYlNDcWcySVJSb3NSRzFJTXk5cEYyRy90L1VXZzNMSUxPbWE3dHJQU0ZtL0FZNkhNaEVHMDZSZysvTTZJaHYwbTlmdGdRZE5TSnJxMW1ZNkpCQzFzbkN5MTVLWTE5MkRISDEyLzRRY0lJSTlxRmZxOGZIOVRlaUxXQzY5WFhDNFI1ay83bC9qd1ZHUytWOTNQMnUrYThIZEViL21CQzYrYzltSzFWZHd5U0RJSDVxNGZkcU1WeUF2M1UxNDVMcUNvK2loVExZWWlDZEprTjJWbGErZ1JqNTZvNHNNbGswWGkxYW4rNFk4V2hxLytVT1ArWDd3UE9xeWczdDVLRG5oOUNrdENBOUdRMm1IMCtxblB3TFdnYUgiLCJtYWMiOiJmNDk1ZDY0YzQwNjk2YmMyNjQzYWJmOGM3NGFkMDVhNWE1YzUzYzU0OWY4ZTNkNDk2OTAwYjg2MjE4NzA0MTY2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:09:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573348641\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2143524553 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1457/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2143524553\", {\"maxDepth\":0})</script>\n"}}