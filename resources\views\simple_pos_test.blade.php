<!DOCTYPE html>
<html>
<head>
    <title>اختبار Enhanced POS البسيط</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; border: none; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>🧪 اختبار Enhanced POS البسيط</h1>
    
    <div class="test-section">
        <h3>اختبار مباشر لإضافة منتج</h3>
        <button class="btn-primary" onclick="testDirectAddToCart()">اختبار إضافة منتج مباشرة</button>
        <div id="results" style="margin-top: 20px;"></div>
    </div>
    
    <div class="test-section">
        <h3>اختبار البحث عن منتج</h3>
        <button class="btn-primary" onclick="testSearchProducts()">اختبار البحث</button>
        <div id="searchResults" style="margin-top: 20px;"></div>
    </div>
    
    <div class="test-section">
        <h3>فحص السلة</h3>
        <button class="btn-primary" onclick="testGetCart()">فحص السلة</button>
        <div id="cartResults" style="margin-top: 20px;"></div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // CSRF Token setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        function testDirectAddToCart() {
            // استخدام بيانات ثابتة للاختبار
            const testData = {
                product_id: 1, // معرف منتج ثابت للاختبار
                name: 'منتج تجريبي',
                price: 10.00,
                quantity: 1,
                tax_rate: 0,
                is_manual: false
            };
            
            showResult('results', 'جاري اختبار إضافة منتج...', 'info');
            showResult('results', 'البيانات المرسلة: ' + JSON.stringify(testData, null, 2), 'info');
            
            $.ajax({
                url: '{{ route("pos.enhanced.add_to_cart") }}',
                method: 'POST',
                data: testData,
                success: function(response) {
                    showResult('results', '✅ نجح الاختبار!', 'success');
                    showResult('results', 'الاستجابة: ' + JSON.stringify(response, null, 2), 'success');
                },
                error: function(xhr) {
                    showResult('results', '❌ فشل الاختبار!', 'error');
                    showResult('results', 'رمز الخطأ: ' + xhr.status, 'error');
                    
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        showResult('results', 'رسالة الخطأ: ' + errorResponse.message, 'error');
                        showResult('results', 'تفاصيل الخطأ: ' + JSON.stringify(errorResponse, null, 2), 'error');
                    } catch (e) {
                        showResult('results', 'نص الخطأ الخام: ' + xhr.responseText, 'error');
                    }
                }
            });
        }
        
        function testSearchProducts() {
            showResult('searchResults', 'جاري اختبار البحث...', 'info');
            
            $.ajax({
                url: '{{ route("pos.enhanced.search") }}',
                method: 'POST',
                data: {
                    query: 'test'
                },
                success: function(response) {
                    showResult('searchResults', '✅ نجح البحث!', 'success');
                    showResult('searchResults', 'النتائج: ' + JSON.stringify(response, null, 2), 'success');
                },
                error: function(xhr) {
                    showResult('searchResults', '❌ فشل البحث!', 'error');
                    showResult('searchResults', 'الخطأ: ' + xhr.responseText, 'error');
                }
            });
        }
        
        function testGetCart() {
            showResult('cartResults', 'جاري فحص السلة...', 'info');
            
            $.ajax({
                url: '{{ route("pos.enhanced.get_cart") }}',
                method: 'GET',
                success: function(response) {
                    showResult('cartResults', '✅ تم فحص السلة!', 'success');
                    showResult('cartResults', 'محتويات السلة: ' + JSON.stringify(response, null, 2), 'success');
                },
                error: function(xhr) {
                    showResult('cartResults', '❌ فشل فحص السلة!', 'error');
                    showResult('cartResults', 'الخطأ: ' + xhr.responseText, 'error');
                }
            });
        }
        
        function showResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            
            container.innerHTML += 
                '<div class="' + colorClass + '">[' + timestamp + '] ' + message + '</div>';
        }
    </script>
    
    <hr>
    <h3>🔗 روابط:</h3>
    <p><a href="{{ route('pos.enhanced.index') }}">Enhanced POS</a></p>
    <p><a href="{{ route('debug.enhanced.pos') }}">تشخيص Enhanced POS</a></p>
    <p><a href="{{ route('dashboard') }}">لوحة التحكم</a></p>
</body>
</html>
