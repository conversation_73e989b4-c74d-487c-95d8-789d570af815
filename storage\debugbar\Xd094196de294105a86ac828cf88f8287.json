{"__meta": {"id": "Xd094196de294105a86ac828cf88f8287", "datetime": "2025-06-28 15:26:40", "utime": **********.160585, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751124399.697679, "end": **********.160608, "duration": 0.46292901039123535, "duration_str": "463ms", "measures": [{"label": "Booting", "start": 1751124399.697679, "relative_start": 0, "end": **********.090437, "relative_end": **********.090437, "duration": 0.3927578926086426, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.090446, "relative_start": 0.39276695251464844, "end": **********.160611, "relative_end": 2.86102294921875e-06, "duration": 0.07016491889953613, "duration_str": "70.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45617664, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016919999999999998, "accumulated_duration_str": "16.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.118524, "duration": 0.0161, "duration_str": "16.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.154}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.144562, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.154, "width_percent": 2.128}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.150661, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.281, "width_percent": 2.719}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-46804282 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-46804282\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1961071047 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1961071047\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1196012633 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196012633\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1766626427 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124397135%7C17%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjgrV2VDanZSb0tkSkQyRUw4UXlhZ2c9PSIsInZhbHVlIjoiYmpmUExPRG9XU0xaQURJMGZKWUV6Sy9wZFppYklRV01kZS8vNElGZ3ZuNjloTDhMcXdQT2krNU5lejZnSVRxSlBYSS9iWUpoYW1KdElwTEt4SVIvclF0SElnbWp3NFNpVXdHT2NrUFI5VnZTWVJNUUdxRmxQc29FMDJnQS9tcjEzWnJFRktMK2w2MDFvZVYvckN6L0xCZnlZdmFpMVMvRFhOSzFqRHlFVGEwTitZVjFpNVFaSyt2LzA3eml0T0QxTzk5b3hvWWVwSzJSaHNxb1BCanE1YUFWWDQyNVMvRFhkSUg4R0Y3YzlZZllpRWhhL2ZneTdBenlnelRKTlZpVXlIYnhGQTFIVFpuRU84WXFFNlFjSTZuaGJ4VnRYRXRPaGt3UzdOb0FwZkdxQTVPL2xrOTgyb1BPMDFvTU45L2thQ0NOVlo5RlV0ZzIyRncwblJzblJJWTV2dmp0c1hvY0ZRdzRCeVAvSnpFSzM1NTRTT3MxQ1hYZVUxbXcySXNuRlp0MG14Tmh3WUdpTnMrQmZpNVlVMkRRQTRaM010b01KYU00UUlXV0puOHprSzRtMVlCRGFwRmtyUlhQT25TbStYUXVkNVZQSzZHUFkwVFYwNlZ1aGdyNWJKeTZMVlBzcHd5MmU5VElySXdVWjNnWSthTndRbHFvckszamkvTm8iLCJtYWMiOiJjMGVkMmEwOTMzYzZlYzZlMWM5NGM1OTk4NDg0Njk3MTI0NGJhZTc5OWI3MmM5NmE1YTNiNmE4ZGFjY2M2MmI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJJV0Qyb1hDTklIRjZiWlFSVm40V0E9PSIsInZhbHVlIjoiOEZrNHdBSmViVnVXcTI3cE5hVHFkZUwxNS9FbVd3UlU0QXdIRndJd0ZJSFNDOHRBWTdqYytUYTZMUW5oNkw5UVVFK1RtdVZjTU01U21rUU5YbFdIZVVON0NzamNYYThvb0VRbDUwSHNNM1hZVitDb0U3NTlRY1NROVlScHRuMWlqUEVQY3BuSzUyTnZQbFUzQnkrQVlqeHRPaXlWMFhtZDNqZStsd243b3d6YUswdmo0VmFPdVRpWVBFSVlUMU9hczY0OXE1eC8zNlB0bEFzeVZxajUxaHdYS1ZCVjc0MWNTeEZwNW5IQjNOWmxBYjR0L1Y1Q0l2UzNQcEhUQ3pKZ0llT1U5bFNPQi9oQm1BdFMrYWNSZjNyNzRPMU13VzFXUUViYk5ZaGQ5Z1F1SXRjWmNxb2E1bXdMemd2cGlyVWp2bjl0cU1HNkh0eWJ5R1B2T3Zjbk1rSWRTYXl0cjBNeTRyc1AzaTZPTm12SGlwWFFMZDlkdEpNL3dDZmVTN2NCZE1UOWRqK1JXWk1teFN4QmJhaE1QRUFJRTNQNC9xeUE5dmVuRXBPT0NxV2NPNFNJVDBOMDRIZ1pBQTFhbnJ1T0ROa1dBNkxtK3k4Y24yTEJSUXdvbzhFVlFnN0hPdzB5SGhCeCttWmFkVWE5emFYeHZhczdLUU40T0MyaThNUy8iLCJtYWMiOiIyNTc4Y2EyZDFlNDMyNzUxY2U1N2JkYjVhODRlMWRlYTNjYWI2NGMwNThhNDIzNjYxYzEyNjdkMzdhY2RlZDM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766626427\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1535833470 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1535833470\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1911278611 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:26:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtNcTNCWW9vNXRJcVVzc1RUazNRbWc9PSIsInZhbHVlIjoiRkR6WFJpTVJvZlVGWXo3R1NQY015RzlJTzJyVmFFUWg1RWVXejM1TkY3OStFcm5ZMFdiMm4xUTh2ZmUzSzludnJnWEJZTjZnZTN5eUdaSTFhRWpuRFJzN1UvQTRLRmlJbEhSS3E2c1VFbWVoZWExRyszYlM5aEYzMXFFNWRMZEhjTkt6UEhzR25vYzNPOFREdlJsVEZKbjkwZzZCU0ZFTTdXVG9aR1FMUDVmc0pqSGpZVzl3dHJzOVNKS0FXTUhXdTVYcGl0czNtWVBGMTVjL1RDbU1BOGxST014c1BodDhkejI0UTN3WW9HbWk1RzRiZHZLR3NkTU0rZXhOUk5QRjc3b1NuNTFVR1VBaHlpMTFuRm9xdW5ZMWMyMHVQNFd6QzVyMXFHdENjN1Rsd1MyQnNtaFZoRGovSXFxT01vekhETk1mSFRKZ3hFTTF4TkNiYkcvbkFNTTY5YkdQR0NkSFU3amZGMUZRdGk2ZTNFWUszYStDK29iYmNHSGZwSTlSeWsxUWtMcm5WWTN4NFlJRy9VVmFyQ2JGTzJybzhYN0NoZVRxdm5vUHh1WjdnZjdjTi9tR3VtV2FJSmxJT3lEME1MMEVZbmIyVDlhdlNPUmpmVmF0ek4zN2JMS2UyTDRBY0Q5UjZHWXRJUW1MNGVyeGhPMGRnSFduWk1WRFNFa1EiLCJtYWMiOiJmNTBmMzVjNTE1OWMyODIzYzg4MzMxNjkzMTM0NmYyNGYwMDAwMzE5NmUwZjgyYzRmZjY5NjYyMjZhZDVkNmNlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjhCdGhDdWxQRzhPamV5S1Nvc28zRGc9PSIsInZhbHVlIjoibVM5d3FWRTdiYSthUVhhRDRiZGVYQU1sTkdLSURsOVhacnVUc3lPMWRVQmpjaXhvc2MrV0lLM3BIYWtkTWtGUXpUd2Era1M2K1M3bkJrZy9yVGhwaVptRGtSYnVXa2phZTVJZWVKUFRjaUZvdW1SeEhibUlwSVZjeDJXNTRuSHBxZ0pHRVYzdG15dHlYdldGeVZqcWR6eGJmdU9SYTc5Tm10NlJMYng2cUNoYStXclptRmxBd28rQWd1MmJwQVU5VTFEdmtjcjZGSnpreFJJZ1B5ejhMNmhzNXRNYnJtQzlGY01DeE5mMy9od2F3UWNQMk10cktac2dmQklkdDZtVGJOcnNVM2FPS0lEc2F3NER5UkRFajZMbWhFakZuOE95MmlxelBOemdKZ0pzeVZrendSU2VnVXQ2WVluZTNvM1VFN0xoYTA0SUVkTkMvaG1SR0ZNbjIwcTRjL3ZFM0xKZHcvd2RWNm80L3JyMUc2OGhsRy9qcDRwTHlISEl3SmhzRXRhR0Y1MVRoemxWaWhibVdYQzVnOE4ySTNjNFlOTDRwakpLZXRJQ05vaWNxRHBHdVFUTVBuZi9HM21YUTRFOGh2OXc0ZStnL2llR0V6OWcydUpVOXFhRXAxamRVZUFyMGhVMHpubmwvcDNERFlPb0dZY2pMdEd4RU9YNXpyTy8iLCJtYWMiOiIzYTQyZWE1ZjllYjczNmU3YzFkOWM5MTg2NDEwN2Q4ZTc3N2E2NTlmNTcxNTA3NjBkZGJhOGU3MGJkMjE0NzY1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:26:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtNcTNCWW9vNXRJcVVzc1RUazNRbWc9PSIsInZhbHVlIjoiRkR6WFJpTVJvZlVGWXo3R1NQY015RzlJTzJyVmFFUWg1RWVXejM1TkY3OStFcm5ZMFdiMm4xUTh2ZmUzSzludnJnWEJZTjZnZTN5eUdaSTFhRWpuRFJzN1UvQTRLRmlJbEhSS3E2c1VFbWVoZWExRyszYlM5aEYzMXFFNWRMZEhjTkt6UEhzR25vYzNPOFREdlJsVEZKbjkwZzZCU0ZFTTdXVG9aR1FMUDVmc0pqSGpZVzl3dHJzOVNKS0FXTUhXdTVYcGl0czNtWVBGMTVjL1RDbU1BOGxST014c1BodDhkejI0UTN3WW9HbWk1RzRiZHZLR3NkTU0rZXhOUk5QRjc3b1NuNTFVR1VBaHlpMTFuRm9xdW5ZMWMyMHVQNFd6QzVyMXFHdENjN1Rsd1MyQnNtaFZoRGovSXFxT01vekhETk1mSFRKZ3hFTTF4TkNiYkcvbkFNTTY5YkdQR0NkSFU3amZGMUZRdGk2ZTNFWUszYStDK29iYmNHSGZwSTlSeWsxUWtMcm5WWTN4NFlJRy9VVmFyQ2JGTzJybzhYN0NoZVRxdm5vUHh1WjdnZjdjTi9tR3VtV2FJSmxJT3lEME1MMEVZbmIyVDlhdlNPUmpmVmF0ek4zN2JMS2UyTDRBY0Q5UjZHWXRJUW1MNGVyeGhPMGRnSFduWk1WRFNFa1EiLCJtYWMiOiJmNTBmMzVjNTE1OWMyODIzYzg4MzMxNjkzMTM0NmYyNGYwMDAwMzE5NmUwZjgyYzRmZjY5NjYyMjZhZDVkNmNlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjhCdGhDdWxQRzhPamV5S1Nvc28zRGc9PSIsInZhbHVlIjoibVM5d3FWRTdiYSthUVhhRDRiZGVYQU1sTkdLSURsOVhacnVUc3lPMWRVQmpjaXhvc2MrV0lLM3BIYWtkTWtGUXpUd2Era1M2K1M3bkJrZy9yVGhwaVptRGtSYnVXa2phZTVJZWVKUFRjaUZvdW1SeEhibUlwSVZjeDJXNTRuSHBxZ0pHRVYzdG15dHlYdldGeVZqcWR6eGJmdU9SYTc5Tm10NlJMYng2cUNoYStXclptRmxBd28rQWd1MmJwQVU5VTFEdmtjcjZGSnpreFJJZ1B5ejhMNmhzNXRNYnJtQzlGY01DeE5mMy9od2F3UWNQMk10cktac2dmQklkdDZtVGJOcnNVM2FPS0lEc2F3NER5UkRFajZMbWhFakZuOE95MmlxelBOemdKZ0pzeVZrendSU2VnVXQ2WVluZTNvM1VFN0xoYTA0SUVkTkMvaG1SR0ZNbjIwcTRjL3ZFM0xKZHcvd2RWNm80L3JyMUc2OGhsRy9qcDRwTHlISEl3SmhzRXRhR0Y1MVRoemxWaWhibVdYQzVnOE4ySTNjNFlOTDRwakpLZXRJQ05vaWNxRHBHdVFUTVBuZi9HM21YUTRFOGh2OXc0ZStnL2llR0V6OWcydUpVOXFhRXAxamRVZUFyMGhVMHpubmwvcDNERFlPb0dZY2pMdEd4RU9YNXpyTy8iLCJtYWMiOiIzYTQyZWE1ZjllYjczNmU3YzFkOWM5MTg2NDEwN2Q4ZTc3N2E2NTlmNTcxNTA3NjBkZGJhOGU3MGJkMjE0NzY1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:26:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1911278611\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1838041404 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1838041404\", {\"maxDepth\":0})</script>\n"}}