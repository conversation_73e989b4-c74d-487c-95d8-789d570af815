{"__meta": {"id": "X4634f4a8794877b7023fb25c6674148e", "datetime": "2025-06-28 15:25:49", "utime": **********.084102, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751124348.607552, "end": **********.084119, "duration": 0.47656702995300293, "duration_str": "477ms", "measures": [{"label": "Booting", "start": 1751124348.607552, "relative_start": 0, "end": **********.022315, "relative_end": **********.022315, "duration": 0.4147629737854004, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.022325, "relative_start": 0.41477298736572266, "end": **********.084121, "relative_end": 1.9073486328125e-06, "duration": 0.061795949935913086, "duration_str": "61.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46307584, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2335\" onclick=\"\">app/Http/Controllers/PosController.php:2335-2369</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00234, "accumulated_duration_str": "2.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0631168, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.205}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.075532, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.205, "width_percent": 21.795}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-682070130 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-682070130\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-251442271 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-251442271\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-354204920 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-354204920\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1436108134 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751124345871%7C14%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBzWVNmSEZRU2JlZSs0d1YzVUZUZXc9PSIsInZhbHVlIjoiWmdqNXJPNnJHSkVJWXpWL3R3KzZhblQxck82cW9sbEJjZkh4UlViN0krYUdmNlN4ZUI5SjdKUWdJbmw0VmVxd3hqVmRPYkRhYXVIOHh6ZXBJM01CZzVHZjBKWjJ2OUtLMDh0TnlhcWRjZDlldzlKU3p4SHh1WGNoMnJ6L2piWHp4QXhPU1FMNm9GektONGFzK2xraERHWnR3VktPY2ViRW9jR1dOYUtpVTRjRXNRMmlBQXZFclROcXF0R2tEbU51eFZSN3JTM0ptSmhEb3FteXVFcEloSFh0Nmd4U3JCMmYyTEZkYkpHdjkyZkJwb3hQQVJMQlFWR1NJSTZHQVdKQUYxR0VFN0lwVlRzRmRjalp2enFvV2lmTGxNcDdGc05wNC8xRDdvNXVtc1pVT0FqbmJwZmZWeUs0QVlJZDdRZldBQ1AzYzNoWnNOaWhhdy9HUTE0UkhjOW1rZ0wxcWZyRkxxWEIydllYWDBnRWdReHJrY3c1NHhzQVJpTTlnSjdRWmwxT01PT3U1QVRraHdIQ3dPUnVlaWx6SXBDYk91cVY1cTZNbW9SZkRkNFFaQVc5U05QTklWVkFFTHhWdS95dkxwaGU3SVFGYXgxMHZEclcvUTd6ek1rZlYvQmxLeTE4M0F1VisvMS9Ka3Z5R2tJZjhMWUUyNVpJYndBUDdmMWoiLCJtYWMiOiIyZjg3MmQ1YzE0YjllNGRkNDE4MjljMGJlN2I4Yjg1N2ZkZDIxOTBjZmI0NTZlMDM4MGM5NzMwNWNlYzkzOTQwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlBodDV5ZUJHWnR1MjgrakZWaUtMTFE9PSIsInZhbHVlIjoiYzhIekZGVGFHWUVRcTIwdHdDamZZK3BHSk0wa1JQeFpIekN3MFpISDhVOU9sZDBGVjVmc0RISjRnTjhIM3NseThENmJUUHV5S3o1TVk3ZlNzTWtDdGlmWEV1ZGRrSDhjR1NXTVlUQ2lDRzQ4aEtSWVdDa1paNlBxdXluUlREMU9NanEvV0JKSHl1RytUNTBuSE9aakdXTGxXa0twUkdHZEV6eVptUEFEUEYvT3FCYTVtM2dQUTJXeU9jbFFXbmRSMmhwaFM0bXlSSXNsRDF4Mm1jZ2d0ZFBMbXU0ZjB5WFVVUmRITHdBUGt6elFjZjNJaFp3VkJiZVpSOW5IaW1VWGZQdDd3M3VFeEdNU3JLNitETzk1VGhxVnhQQ3JadkNZMjlrRW5IVjJkdzIvY0dtMTYrTTI3Qm0yWnc1Ty9ndFBYTDF1clRQSzN0YVdFb21xbnpUWTk3VVBTVGhNU2lwQ3dTbHQ0Q3FhWnZEV0pHMk41ajg5MFBoS0dPV3o1NWlyRng2czJlZ3ZDUDNnZDdjK1VKRHRKYUUyQVFTRkVmaWJSQ0VDb2t4S3F4T0FlckgrK21kbGtCWndaM1VlWVQ5VGZWbWFpYmFXVU01d1FNT2lGK0QzTGxkWTFkZ0hIRzJBYVAvUVY5ZTR5WVI3Slp2NnhpZmd5NzRMMHlMNXBuaW0iLCJtYWMiOiJjN2ZkMTUxNTU3YjRlZTMwNTg4MDZiNTE0YzI0YjVjZDIxYmVhMmYzZWZhNmZkYTI0NzVjYzg3YjA5NTExNGRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1436108134\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1709017983 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709017983\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:25:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFZN1JValNlQjgrUWJpakJkNDNtaVE9PSIsInZhbHVlIjoiSk1FclU2MGNMbWVlYTBJUy9MV2RlL3pkSUg5Q1hjVjdSWlA1YUVPYWFOcCt6K25ZeVFCbHZpT0J2L04wempDejJGNXdlOXZhZ3JicStDOUhXZWs1NkI5N01xZFNKUGFCcDg3aml0VVRDK2RmRmErY29WaDJ6TE53NDByQjhzWVV1czA1dlV4dlNxbnpMKzRrdFNsT1RqY0ZpanFtMU1IMG1xMzE0K3B5MDRtOXNaNUEzUkNoc1MyQVZMOGpiUnpNSi8yM2ExTVlPMVdFdzVOWXVZZ1dydEM0WGs4MUc2ekNOV0pZZkU1Z09HV1ZESXVVRGE2V2xsWnAyU2g2QzZvL3Z5ck53YVUwNXhFaFlvaEFnNi85YmNOaDVwY2o3emJNaENsbTAzcy9UbjRkS3NLdFRPZWY4MmNUNER1cEIwdjRIbnZKTmJVOGc4T0ZjM0dQWmhxc1FiNmt6bDRFUEFENG0wa21rbFQwclhUcjNqcmFnNzVWeFk0SmdOOVZrd2g3S0xsVGlreTl5QUdmV084c1RQRDZpTTMzQnc2dXJOeDMvOWpRd3JoeU1MdGxydTRuYUhDNzhtMGFBckRBbklDR2lCS00zeHVHOHRQYkN1ZS8rZ2llM0s4ZUFzUkJWbmNVMUVHNDRWR2Jsdk5FQW1TT21VNC9YK0Zrb3dTalF1OWoiLCJtYWMiOiJlMDE2NzE0ODFjMTMwZWViYjRmNmVkYmJhNTBjODVjOTU4MTgxZTgyMjI3MzM1NTE4ZGY3ZjExNGUxOTZlMmFiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:25:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRrdEtyMUs0Z0FRUWpRSHlXTFppdmc9PSIsInZhbHVlIjoici9WSG1yWlU2Tjl3YUdWdWE2bmxiYTc1a2VUajVUbkp4ZVg5QTFzZkg1Mkw0em42cDN3eUJBd29qRjE1YUR2a0JIYXUyZ21WbWRIOVFyRVMyZzcxcTI4SGF2Q292eXYyUEJRbEFaU1NVa1VsS2hrcEVlbncrWW9pQ0FVZkRiQldwZUNZeFV5T0pCclI4MTBoM0o2L0dqV3MzcENFbDV5VzB3ajJMMUF0R3E3K2RLYnBZWC9QdFlrakdMM05LNDFmQXZhSHJSRFpRTEdmQzJ6NVBVbk1QMGVneDZjT0FTTVBqWFBQUzUwQm1HRjQrTFphakwzL0dwMkdYSWo3TUw4bjlRdjkwd0NwVndYRVJJM3FKejFkSGgwTlBFZ0dmVWZrS3pLTzFFUUl2SXV6Z3lYK0dsWHhwV3NQQnp5dHlMeHIwN25wQ1JzazI0OVVlTkhzeGpkcEtLbkczTVJyVDV2a3ZETUlGSzlhVVVvTlViTmhuYW9oSHBTc0hoN3kwckZXeGJUOVJmdzhuMFUrY0Y3c1M4bnFoWk1vV29lejNzTDcyeEgyRzhQL3ZPS2YxcER1aDdmYTFlTTBKS05CQTZqVVhiTWZiZ1FGaFN1aWVkMEJHY1dmS1pjQmloREppMjRUNloxMk04RXhzM05hZnVJaWppNDBMalJTdFA0SnBrRGMiLCJtYWMiOiI1NGEzZDZlNjRlZGRlMWIwNDVmNGYzN2QwYzgxOWI3NTgxNzg1ZTA0N2FhNzM5OTQ1NTRkY2FhNDc4ODk0Yjg1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:25:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFZN1JValNlQjgrUWJpakJkNDNtaVE9PSIsInZhbHVlIjoiSk1FclU2MGNMbWVlYTBJUy9MV2RlL3pkSUg5Q1hjVjdSWlA1YUVPYWFOcCt6K25ZeVFCbHZpT0J2L04wempDejJGNXdlOXZhZ3JicStDOUhXZWs1NkI5N01xZFNKUGFCcDg3aml0VVRDK2RmRmErY29WaDJ6TE53NDByQjhzWVV1czA1dlV4dlNxbnpMKzRrdFNsT1RqY0ZpanFtMU1IMG1xMzE0K3B5MDRtOXNaNUEzUkNoc1MyQVZMOGpiUnpNSi8yM2ExTVlPMVdFdzVOWXVZZ1dydEM0WGs4MUc2ekNOV0pZZkU1Z09HV1ZESXVVRGE2V2xsWnAyU2g2QzZvL3Z5ck53YVUwNXhFaFlvaEFnNi85YmNOaDVwY2o3emJNaENsbTAzcy9UbjRkS3NLdFRPZWY4MmNUNER1cEIwdjRIbnZKTmJVOGc4T0ZjM0dQWmhxc1FiNmt6bDRFUEFENG0wa21rbFQwclhUcjNqcmFnNzVWeFk0SmdOOVZrd2g3S0xsVGlreTl5QUdmV084c1RQRDZpTTMzQnc2dXJOeDMvOWpRd3JoeU1MdGxydTRuYUhDNzhtMGFBckRBbklDR2lCS00zeHVHOHRQYkN1ZS8rZ2llM0s4ZUFzUkJWbmNVMUVHNDRWR2Jsdk5FQW1TT21VNC9YK0Zrb3dTalF1OWoiLCJtYWMiOiJlMDE2NzE0ODFjMTMwZWViYjRmNmVkYmJhNTBjODVjOTU4MTgxZTgyMjI3MzM1NTE4ZGY3ZjExNGUxOTZlMmFiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:25:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRrdEtyMUs0Z0FRUWpRSHlXTFppdmc9PSIsInZhbHVlIjoici9WSG1yWlU2Tjl3YUdWdWE2bmxiYTc1a2VUajVUbkp4ZVg5QTFzZkg1Mkw0em42cDN3eUJBd29qRjE1YUR2a0JIYXUyZ21WbWRIOVFyRVMyZzcxcTI4SGF2Q292eXYyUEJRbEFaU1NVa1VsS2hrcEVlbncrWW9pQ0FVZkRiQldwZUNZeFV5T0pCclI4MTBoM0o2L0dqV3MzcENFbDV5VzB3ajJMMUF0R3E3K2RLYnBZWC9QdFlrakdMM05LNDFmQXZhSHJSRFpRTEdmQzJ6NVBVbk1QMGVneDZjT0FTTVBqWFBQUzUwQm1HRjQrTFphakwzL0dwMkdYSWo3TUw4bjlRdjkwd0NwVndYRVJJM3FKejFkSGgwTlBFZ0dmVWZrS3pLTzFFUUl2SXV6Z3lYK0dsWHhwV3NQQnp5dHlMeHIwN25wQ1JzazI0OVVlTkhzeGpkcEtLbkczTVJyVDV2a3ZETUlGSzlhVVVvTlViTmhuYW9oSHBTc0hoN3kwckZXeGJUOVJmdzhuMFUrY0Y3c1M4bnFoWk1vV29lejNzTDcyeEgyRzhQL3ZPS2YxcER1aDdmYTFlTTBKS05CQTZqVVhiTWZiZ1FGaFN1aWVkMEJHY1dmS1pjQmloREppMjRUNloxMk04RXhzM05hZnVJaWppNDBMalJTdFA0SnBrRGMiLCJtYWMiOiI1NGEzZDZlNjRlZGRlMWIwNDVmNGYzN2QwYzgxOWI3NTgxNzg1ZTA0N2FhNzM5OTQ1NTRkY2FhNDc4ODk0Yjg1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:25:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}