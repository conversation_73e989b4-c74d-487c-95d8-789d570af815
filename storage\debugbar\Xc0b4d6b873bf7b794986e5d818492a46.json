{"__meta": {"id": "Xc0b4d6b873bf7b794986e5d818492a46", "datetime": "2025-06-28 15:04:38", "utime": **********.580251, "method": "GET", "uri": "/financial-operations/product-analytics/stagnant-products?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.169072, "end": **********.580265, "duration": 0.4111931324005127, "duration_str": "411ms", "measures": [{"label": "Booting", "start": **********.169072, "relative_start": 0, "end": **********.512863, "relative_end": **********.512863, "duration": 0.34379100799560547, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.512873, "relative_start": 0.34380102157592773, "end": **********.580267, "relative_end": 1.9073486328125e-06, "duration": 0.06739401817321777, "duration_str": "67.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49183856, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/stagnant-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getStagnantProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.stagnant-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=513\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:513-582</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01679, "accumulated_duration_str": "16.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.544904, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 11.554}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.554651, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 11.554, "width_percent": 2.025}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, ps.purchase_price * wp.quantity as stock_value, DATEDIFF(NOW(), ps.updated_at) as days_since_update, `ps`.`expiry_date`, CASE\nWHEN ps.expiry_date IS NOT NULL THEN DATEDIFF(ps.expiry_date, NOW())\nELSE NULL\nEND as days_to_expiry from `product_services` as `ps` inner join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT DISTINCT pp.product_id\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nAND p.warehouse_id = 8\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`quantity` > 0 and `sales`.`product_id` is null and `wp`.`warehouse_id` = '8' order by `stock_value` desc", "type": "query", "params": [], "bindings": ["15", "0", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 561}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5570772, "duration": 0.01451, "duration_str": "14.51ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:561", "source": "app/Http/Controllers/ProductAnalyticsController.php:561", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=561", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "561"}, "connection": "kdmkjkqknb", "start_percent": 13.58, "width_percent": 86.42}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/stagnant-products", "status_code": "<pre class=sf-dump id=sf-dump-902964555 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-902964555\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-425461592 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-425461592\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1709908393 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1709908393\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-936175283 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123070655%7C10%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iks1eERzQjJjN3RlT1dtNGhXQjFrdEE9PSIsInZhbHVlIjoic0JtMTlBVENZdXlZbUlDOUVvbE1QOE91c1JUVHozQkV3U0U4bGUyNmVtUVNmY1U0WWpOODJJU2lqbEttMkxnVE8zWWx0dnVxWW15VktNY0RNWjMzUzZwUS9FQ0E1aGJYYmhDZmwzMXZRejkvMFdqOCt5V0Y4ODdKNmxnVXNCRTQwNFh1WEZUUW9GSXJPYytJVjJmZ3VXZXRFd0tjeGtCeEo0RGdLWmRvNjFUR0hybllHaFp2U3JRVXB4ZHRTUElwWUlWSHRnM2dZODBaOHBuQWNUWFVab2NyamVwSjY1c0lON3h3ckxYc2hST1JzdDBjQ25FZHZhM2xFUmdwY0ZEd3Nvay9wN3lvTENLTjluQ2ZnbDBlbWRDam43YXhQZkhYOFVhVzFudE16aWFVZFUzQzUvTllQeVNTT3RaL1YwNk1aNE9xL1VNYk1SU3l6SFBQQkc2QitkNVhZUitITEI0UnQxaC9tbm4zNm1wRFozMEtUVW1xcXRxbmVKZFZVOVR5MVlDci9odjJaUjl6enMxTUZmc1pzbW0wQnhHWTdveFJwNDlyc3I3MHlyZ1FXSmlXeWs4L245aXY1S20wc0lndWJJdHhmK01RbHUreXYrSFZzYnV1OUp3cTM0YjB6N2JiMGhBR0Q2ZGN2R2lQQUZjdDM5S2ljUkpxUmgwY3IyREsiLCJtYWMiOiI0OGViNTJkYzJhY2VhYTcxMDAzM2YxY2U0ZmVhMWVjZTdlYTQ0MDIyMDI5MWM4OWI1NDA2NDE0NzBiNDU2NzU5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBtTWtjSUJYNWZPa0d2YW9GT0tadVE9PSIsInZhbHVlIjoiNkRZYmpDYzZDWnBwR0NnRFZ1OHhUQkM0cVQyNCtjM2F2TFZmWlRiV3VhZVZyOW9RTTJCQ3IrdTdJMmo3RUcvREZ6UmxoUGh2cDlNUUI1SW5aMGdRWkhGUnkxZW9sbTJGbGovd2NJRXROYVJGNDNIQWtEbG5sd2o5OUZKeDA3Y0p0QUUxZlE4bU4zVGR4UWZ2bU94eVFTcjBGQzdkditINHV1L21KVWlkUmJ6T2VYblBySHcrRUNxRWN5L3pCTXFQYUllcDNRNElSUEFZMTNoRFhGYXNNTEgwbzJ2WFJIMWJPNzJWZmw1dGdPWlRzWE52OW5HZmV3NEpJS3MzdkdzWjkyMnF0MkFJTmJhMXoxRW9iaHRjN3JJR01HNnpZUDdVL0xLeUZ4VlJmYitZUS9CVjhaSlZ0REFFMnZUUVJyTTBKNC9qNk5mOXZpbnVKWWNPTHhWWnNSd1JpUjQ0N3p6L3RTUmhHRWtkc2hYMHZVMDR2Z1pKRUtNbFA0TnkrVU1jcnpmd3ZVU2hzSVBVUE9jVTlFTnRFcDNZUUhNVlUrNExpa0dUaDJ6dDBBOUJ6Z0Qra1FTU2dsdGFBMnpLY3I3Y0pzVUZJam5GQ25FbG5pNmRieko2NGdENHhWN0prYVU1Tm9LZi9LQ3Z4Ym5iY2hJa2Rpem5DT29QdmIrZzJvcy8iLCJtYWMiOiI2Y2E0ZWFhM2ZmYjkxYzExNGIzNjMyNzYyYmNjMzZhZmQ3M2E1MTY3OTg5YmNhYTBlMDA4MWU4NGUwYTNkYzQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-936175283\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1641527320 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641527320\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-832529804 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:04:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndmUXEwcEZ3bzRWeGQxdnVSSEJxd1E9PSIsInZhbHVlIjoiazh6enhiQkprQjRBYXV5VG4waW9CTml2QlE0MUtvNEFXNVVvREVYbHhhdjl2azJHOW9rL3MyaTRubHUxNnF1eWhleC9rek1BWFdVWkNmcTh6OTVJL0R2U1djOStYN29XMTF5V016eVlGVWZCajNFakUvVmFTbU82aTJKMmNRZTZSUzBQRXdIWk9SaTJYRUFqekJvWENDYUFyQXJ6V1Z6TlErajRXclIxL3lxYnhGaVJBUE1paXBuOHBFNkNlSWFSVnlzQlc5QTNodDA0bUx1V2k0eFR5TUw4UnBsT2VWcU1hdEZEemtISjB3OVFyUUV1R0xFbWxnQURjM0kzQVdEaENFUnA0TEFsazVMclNNNVppVDRLTjVvNFhlUnBhUTZ0OWFiZXcrWEt0dTNWWjUzeWY1SkdTZ1U2MVp4M3VJL3BFc0RQckRxWFVPaGx5N3p2MGNjTTdJaTFnNFh6cVhnYlpiR1FtUnE3Wmpmc0tXbm5uSkpOb0p5V0tXWFFmTFNpVE9UblVlNmFHRG5yVnRCNzlTSkQ2WjRuZ1l0YndzOGE5Qk1LazBna0xscVlKQUt6a0g3N3d5MUVocUtzcGxMb3NaSVE5VktPdGV6N0JyS29ORUtzcjRGVE1XbUtZbVRNM0luVmpEd1pyWmFKcnhxcU1La0hJTHVrUXhYdEM1emIiLCJtYWMiOiJmYTgwMzM1NDQ0MzY1M2UyMzVlMzAxZjlmYmJhZTVjNGZhNTc3MzEyZjE0OTcxMDIwYzNmZDdkYTE0NzYzN2Q1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJ4eVZQUnNORXFLYTBFWjVOSVhxaVE9PSIsInZhbHVlIjoid29rNHZiN3BCR0hpVXMxOGNyeGFpNEdqb0Z3N3UxM0d5aHNuVTROdkptanZ3WHo4Ry9sckZMTmJkeDFmajNkMVpMMy94dlNCeFZ4TlBpZ0tGd1IzeUZtc3drSE53Y2xTcTVxUm85SDZFTUo5dkpMTTNEV3JFa3lYUmlwUzZLTk8zSDRSbGhqWG4yU0pJcnk1L2R3ak5LRTMwOWlqU3lmSVpJV2VueEwya091TDY0cTZTcnpTaVo5bVBnMWtCR2J4cXBiUmQybGxLNkRVVytnM1lzOG5IUmRFL21LL3AxaHIzaXVTYkpFbTZiUlN3Sng2VWhzSUl3T29oNjViLzVjQjFFRW5kZHVKaFYxWExqTStocFV3MmZER1lFZ1k0dTk0bGpiUzR2ZU1qUGt3UnVDQ1pwdkFXRExLU2k1NHAxbGNWMURWYU4zeTJuQmtXTnBVY2tJT25RKytvQ2VjR2FidE82TWoySUpxTFg3eXlTcjJrbjI1Y2FZbTdrRkNxSDJWc1NpYzVhVG1iTVBObmV5aVhxMU5oK1ZHbnRIQ0tuZE5PeGw1aTA2U1pxR2x4UkJNZGpKcjBUM1N2SDBFUWU1b1VGTGxnMFJyTG00V3EvL2UrMUlpaS80bG5MY3Z3MnZCcTN2TzZlK3UwYzJZWmQ4aDl6RG5aMHBNUEh2d1BaaWgiLCJtYWMiOiJjMThhNDFmOTdkMDBkNjVjNmMxOGQzMzJlNTQ3ODA2NTFkZWFlZGY2ZmY3MWJjNDMyNDNkZjYzYzJjODQ3YTJlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndmUXEwcEZ3bzRWeGQxdnVSSEJxd1E9PSIsInZhbHVlIjoiazh6enhiQkprQjRBYXV5VG4waW9CTml2QlE0MUtvNEFXNVVvREVYbHhhdjl2azJHOW9rL3MyaTRubHUxNnF1eWhleC9rek1BWFdVWkNmcTh6OTVJL0R2U1djOStYN29XMTF5V016eVlGVWZCajNFakUvVmFTbU82aTJKMmNRZTZSUzBQRXdIWk9SaTJYRUFqekJvWENDYUFyQXJ6V1Z6TlErajRXclIxL3lxYnhGaVJBUE1paXBuOHBFNkNlSWFSVnlzQlc5QTNodDA0bUx1V2k0eFR5TUw4UnBsT2VWcU1hdEZEemtISjB3OVFyUUV1R0xFbWxnQURjM0kzQVdEaENFUnA0TEFsazVMclNNNVppVDRLTjVvNFhlUnBhUTZ0OWFiZXcrWEt0dTNWWjUzeWY1SkdTZ1U2MVp4M3VJL3BFc0RQckRxWFVPaGx5N3p2MGNjTTdJaTFnNFh6cVhnYlpiR1FtUnE3Wmpmc0tXbm5uSkpOb0p5V0tXWFFmTFNpVE9UblVlNmFHRG5yVnRCNzlTSkQ2WjRuZ1l0YndzOGE5Qk1LazBna0xscVlKQUt6a0g3N3d5MUVocUtzcGxMb3NaSVE5VktPdGV6N0JyS29ORUtzcjRGVE1XbUtZbVRNM0luVmpEd1pyWmFKcnhxcU1La0hJTHVrUXhYdEM1emIiLCJtYWMiOiJmYTgwMzM1NDQ0MzY1M2UyMzVlMzAxZjlmYmJhZTVjNGZhNTc3MzEyZjE0OTcxMDIwYzNmZDdkYTE0NzYzN2Q1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJ4eVZQUnNORXFLYTBFWjVOSVhxaVE9PSIsInZhbHVlIjoid29rNHZiN3BCR0hpVXMxOGNyeGFpNEdqb0Z3N3UxM0d5aHNuVTROdkptanZ3WHo4Ry9sckZMTmJkeDFmajNkMVpMMy94dlNCeFZ4TlBpZ0tGd1IzeUZtc3drSE53Y2xTcTVxUm85SDZFTUo5dkpMTTNEV3JFa3lYUmlwUzZLTk8zSDRSbGhqWG4yU0pJcnk1L2R3ak5LRTMwOWlqU3lmSVpJV2VueEwya091TDY0cTZTcnpTaVo5bVBnMWtCR2J4cXBiUmQybGxLNkRVVytnM1lzOG5IUmRFL21LL3AxaHIzaXVTYkpFbTZiUlN3Sng2VWhzSUl3T29oNjViLzVjQjFFRW5kZHVKaFYxWExqTStocFV3MmZER1lFZ1k0dTk0bGpiUzR2ZU1qUGt3UnVDQ1pwdkFXRExLU2k1NHAxbGNWMURWYU4zeTJuQmtXTnBVY2tJT25RKytvQ2VjR2FidE82TWoySUpxTFg3eXlTcjJrbjI1Y2FZbTdrRkNxSDJWc1NpYzVhVG1iTVBObmV5aVhxMU5oK1ZHbnRIQ0tuZE5PeGw1aTA2U1pxR2x4UkJNZGpKcjBUM1N2SDBFUWU1b1VGTGxnMFJyTG00V3EvL2UrMUlpaS80bG5MY3Z3MnZCcTN2TzZlK3UwYzJZWmQ4aDl6RG5aMHBNUEh2d1BaaWgiLCJtYWMiOiJjMThhNDFmOTdkMDBkNjVjNmMxOGQzMzJlNTQ3ODA2NTFkZWFlZGY2ZmY3MWJjNDMyNDNkZjYzYzJjODQ3YTJlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832529804\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}