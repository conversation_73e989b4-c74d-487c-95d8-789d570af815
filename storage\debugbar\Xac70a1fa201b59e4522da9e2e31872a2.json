{"__meta": {"id": "Xac70a1fa201b59e4522da9e2e31872a2", "datetime": "2025-06-28 16:01:24", "utime": **********.367025, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126483.864252, "end": **********.367055, "duration": 0.502802848815918, "duration_str": "503ms", "measures": [{"label": "Booting", "start": 1751126483.864252, "relative_start": 0, "end": **********.304428, "relative_end": **********.304428, "duration": 0.44017601013183594, "duration_str": "440ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.304438, "relative_start": 0.4401860237121582, "end": **********.367058, "relative_end": 3.0994415283203125e-06, "duration": 0.06261992454528809, "duration_str": "62.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831984, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00546, "accumulated_duration_str": "5.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.341634, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 33.15}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.353162, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 33.15, "width_percent": 12.637}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦%", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.357157, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 45.788, "width_percent": 54.212}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-930241490 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-930241490\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2091880845 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2091880845\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-664574633 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"46 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;&#128230;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-664574633\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1869498370 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">94</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilg4WThGNDFsK0VodWlwMjBwMEpQakE9PSIsInZhbHVlIjoiNUdWekhURHZIV2V2RFNpaUtxK2VjVUZSSU5NVTd4WDh6ZGlpQjBMTnNUWDlFMUxZSEJ3YXE2djlHYU94OU8vejJkNkJLT0oxbVl4eHdlVXJEdjU0bXduMUVSNDB0czhmT0tkMUpDRDFvWFk5YzIvU0NWbGF4SkNCMHU4aGRJaytnRkR2bkRoYzdmRnBUenhJcWU2eFNUNW84Lzk1YloyNG01MWFJenBHZ2JYdUpEWjhUT3JYN2o3TXl1bzNJamJRMm1oRzJoSTNlSFYyYnpmNzc1NVRpQnp4WW5tSzZEUEJqVTN2aGl0Q3hNWklUVWNKS2FIc3hudVlZalBBR1VJOTlvQmgyZnN6akNlZS80YTZpanc0NUl1VXVSVmtxOWpLb0tNSDNFTzI3R1pvNXJaTE9hWVI4M1ZPclpDa0Z3UGZBc2lFNjkzTlZvNzhJSnhSSEowYXpvWSttdk85NEp5RWxmcWpiMm0vVDJjbGxPTktkNWs2QmVKWmlscUZ5dm5qdFp0ZlBQRExxOTJvRHg1ZmMxdG1pZGljK25jSzRxZi9LK1RSeXh5S3NRSUtpejJ2dmNOSk9iNXZpMHFJTGtFczg1T3d6K3RYRENZOEp5Vk81R3JZVFU4aW9nSmYwRkdwQ2x1YnpxOS96cGNDYURzL0ZmVEJnWHFHZ1Jjc1Z6QnciLCJtYWMiOiJmYmFiZGU3MTc2YTcwYmRlMzI3ZGZiNGIxNGVhOTQzMWJmMDJlZGJmODljZDMxNjkxN2E1ZTgwZjFmNzRlMjMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRwUnFnS05EQy92cXhINGE5V0piTXc9PSIsInZhbHVlIjoiRURXdURvWG9uOVNtUW50RTV6bVFZYk83QUMwUDhma1BzTkwzWmVWME45TVo3QWpsS0VobGRWL3p3azk2VmNmL3JlU2xSN0ZPWjhVMUFINXU1S2VpZmVXTDlmditoNFpSVTF2U0M2dmZLNDNieElFNTVmemwxZVBwaEVrN2ZtWGRQK1ZKdnF2NjNDWEhpVW1sMVRVUHNmOFQ1U3hjcFhvZFBmNHMyOURBTDRLaURhZG1aYTFLRGtvQkpVWk1CQjJDY0FvSFBwcUNKUVM5MnRsRkl4U0tMQ2EwRTRyK1hIZklyQmJHL2lkdDZxZ0l3c292OUZuOEtxaHEyODRsbkhITUM2clkyaStLUUw4ZmZhT21ITWtVN2xxWG12aTdvNENGempkTlF4d09jY0ZFb2RuQjZVMDhCTThGT205Njl0Ris2REZSWFE5TDFucm40Ni9lclJTMXplV0M4SmtXVWlqUUpFc1BCUzBYL1FqQ0xzeXFOd3hYcUQzVkhNc3RVcmZsSjZUMVdhODRacjVKV3VzeS9xcE90TzhkNCtSR1lIdno2Rmd6ZGQvZm9HcllCN1VQR0pBOWFoTkZQMG5rZ1lmY0RibVZoSHVDVFd0ajBiRlBJOTcrK2dyWEZOTlJLbmxjcGZxYzdRNTBOUndYUkc3MElKaGN2aSszZDB0RTZwUEoiLCJtYWMiOiJmMmUwODllZWRhNDYwM2RhNDZlMjM1ZjQ1ZGRiOGI3YTc3MzQxZjkzYTdmNmYxZmFmZTgyM2JmYzkxNzBjYjBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869498370\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1844948869 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844948869\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJVd0tBVk9CblNOdzc0K0pkczUySEE9PSIsInZhbHVlIjoiOHFJUys4bWx0Vmh3T2ZxUVRhN2F4WllxdlRkT0RZQ2QweHRkeXpjZmpJcDRYb3ZPVElTSm4vOUI2NHpQWVFNbDZWTitOWXpLYWJjd1NaL3E1S0l4SHFwVGlhS203YS9jTDRuOE9DT3duTHlxd2ZlT1RTRmEvcy8wKzcyc3hka0t3ZDZwMy9zOWJvRmxGckRnTDRhUHRudXVxT0VwcGk5SThQc0NFMmo5dXRYUlVvcElYQjFJMkNkYjhRc3ZqQkxta3NNN2hQOFBBZEZoZ1YvaVdzaS9oeTBGVkV3cnpoNCtKc2pHTkRyejd3OVI0QWJ0Y0lGdVo5ZGM3REdOdHBTbWIwM0FmS2J5UCt3Sk5IRVdvMzlhM0s4MkFndnN6VndVeWJEZEpwYzYvZzRXQ3YwaXVpWlNtYlBpMmlaR0dVdkRvakI0Qm85OCttNjgrdjR0UXpWenZydGF3WEpjUWRycldNZDYzdDVMUlpQSzk4NnpTS2N6V0MzWFc0bzhvRzg4eG8va2QyRkNFWitkcXdOdGNMNXhlb3A4blVwLzZMT2NjLzBLdGhiQVNCVjF2OTkxdjVKMENVUGEwZ25JandLQmhtdTJhTUJvd3pCSnE3bEkvZDBrcVc0aDl5eWZoMXRTckx3eHNjanVEV2tIWUJKWE5FaXJLcCtiYUZnb0gvUVciLCJtYWMiOiI3MmYxNTg2NDY0ZjY5M2JhOGU1MDk4ODI3OTZhMGVjOWZhMjdkZTQ1ZGM5YzY3Y2Y4ZDhjMTNiYTBiZjhlMjBkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IldyNndkc2VyZDZMYkI1NWVBN3Fsenc9PSIsInZhbHVlIjoiUmUzRDNJNGdINVFpaVR3WmpWOEttclVGMzdKRnI1NHczYlV3dEl4M2RVVDhGaFJQTmc5ekJQS04yQzBhZmNrbk94bGRhc2dYMlpmMXBRWmV1WmJGcEpyUEtSK0VMR0RXVkhWZ2RNOU90NlJlcXMzQ1N2MWliUWl1TFN1WkQxclFKZ3JoUHNrQTNFd0dJcFhNZGIveWlQdlFJTkU0RTIvWFRUeFRveU9oMEkxRzdKVmpwQmpHYlU3dWNXT01uVElTTnU5d2xuWlhYd3Z2WWU3aTM5d08zbVhpOXlLZzByRXlSL0puOHh5MXZSRmN1bmxEYnpjYTZOOWhXMEtPTEtJTmpuM25TbzluL3c3M2V0Qm82VTc0K3FJdVhRbEMzdmN4bmJzMVFWdnBLMGNISmFxbEQ0RTlXajhqYnZPOFpsKzFjbmdJelhXVWNSR2g2UUMzOVI3Q0JXak8xb292SzYzMVpjcmE0N3lBZ2h3MEM5c1NEbVovaWk5WFZuYmgxS09xeXViZVhGWENIVjEva0ZTc2FhTlVkR0lwdDZmcStjSW1zQmRKU05wVHdsS1p6QUtydG4wSGZWVDA4dEM1ZUo4WDg1bVlybUZGZkozMlg4dW9VeW15U2Jrd1VPdTVoejA5bVkxWnNTUnZXUXBhN1ZYZ3ZvQmhydS9BQnhWeGRRRnoiLCJtYWMiOiIyMzQyNjgyZjYyMDY5NDBmODJmMTE2NThiZTRmNGViMjk4MTJmZjhlYTM4N2M0YWExYzRhNjA2ODNiN2VhOWY5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJVd0tBVk9CblNOdzc0K0pkczUySEE9PSIsInZhbHVlIjoiOHFJUys4bWx0Vmh3T2ZxUVRhN2F4WllxdlRkT0RZQ2QweHRkeXpjZmpJcDRYb3ZPVElTSm4vOUI2NHpQWVFNbDZWTitOWXpLYWJjd1NaL3E1S0l4SHFwVGlhS203YS9jTDRuOE9DT3duTHlxd2ZlT1RTRmEvcy8wKzcyc3hka0t3ZDZwMy9zOWJvRmxGckRnTDRhUHRudXVxT0VwcGk5SThQc0NFMmo5dXRYUlVvcElYQjFJMkNkYjhRc3ZqQkxta3NNN2hQOFBBZEZoZ1YvaVdzaS9oeTBGVkV3cnpoNCtKc2pHTkRyejd3OVI0QWJ0Y0lGdVo5ZGM3REdOdHBTbWIwM0FmS2J5UCt3Sk5IRVdvMzlhM0s4MkFndnN6VndVeWJEZEpwYzYvZzRXQ3YwaXVpWlNtYlBpMmlaR0dVdkRvakI0Qm85OCttNjgrdjR0UXpWenZydGF3WEpjUWRycldNZDYzdDVMUlpQSzk4NnpTS2N6V0MzWFc0bzhvRzg4eG8va2QyRkNFWitkcXdOdGNMNXhlb3A4blVwLzZMT2NjLzBLdGhiQVNCVjF2OTkxdjVKMENVUGEwZ25JandLQmhtdTJhTUJvd3pCSnE3bEkvZDBrcVc0aDl5eWZoMXRTckx3eHNjanVEV2tIWUJKWE5FaXJLcCtiYUZnb0gvUVciLCJtYWMiOiI3MmYxNTg2NDY0ZjY5M2JhOGU1MDk4ODI3OTZhMGVjOWZhMjdkZTQ1ZGM5YzY3Y2Y4ZDhjMTNiYTBiZjhlMjBkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IldyNndkc2VyZDZMYkI1NWVBN3Fsenc9PSIsInZhbHVlIjoiUmUzRDNJNGdINVFpaVR3WmpWOEttclVGMzdKRnI1NHczYlV3dEl4M2RVVDhGaFJQTmc5ekJQS04yQzBhZmNrbk94bGRhc2dYMlpmMXBRWmV1WmJGcEpyUEtSK0VMR0RXVkhWZ2RNOU90NlJlcXMzQ1N2MWliUWl1TFN1WkQxclFKZ3JoUHNrQTNFd0dJcFhNZGIveWlQdlFJTkU0RTIvWFRUeFRveU9oMEkxRzdKVmpwQmpHYlU3dWNXT01uVElTTnU5d2xuWlhYd3Z2WWU3aTM5d08zbVhpOXlLZzByRXlSL0puOHh5MXZSRmN1bmxEYnpjYTZOOWhXMEtPTEtJTmpuM25TbzluL3c3M2V0Qm82VTc0K3FJdVhRbEMzdmN4bmJzMVFWdnBLMGNISmFxbEQ0RTlXajhqYnZPOFpsKzFjbmdJelhXVWNSR2g2UUMzOVI3Q0JXak8xb292SzYzMVpjcmE0N3lBZ2h3MEM5c1NEbVovaWk5WFZuYmgxS09xeXViZVhGWENIVjEva0ZTc2FhTlVkR0lwdDZmcStjSW1zQmRKU05wVHdsS1p6QUtydG4wSGZWVDA4dEM1ZUo4WDg1bVlybUZGZkozMlg4dW9VeW15U2Jrd1VPdTVoejA5bVkxWnNTUnZXUXBhN1ZYZ3ZvQmhydS9BQnhWeGRRRnoiLCJtYWMiOiIyMzQyNjgyZjYyMDY5NDBmODJmMTE2NThiZTRmNGViMjk4MTJmZjhlYTM4N2M0YWExYzRhNjA2ODNiN2VhOWY5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2074032003 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2074032003\", {\"maxDepth\":0})</script>\n"}}