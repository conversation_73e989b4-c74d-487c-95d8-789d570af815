{"__meta": {"id": "X305d94b2eb69b90f11dd3f58ce15324f", "datetime": "2025-06-28 16:03:36", "utime": **********.073957, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126615.630446, "end": **********.073976, "duration": 0.4435300827026367, "duration_str": "444ms", "measures": [{"label": "Booting", "start": 1751126615.630446, "relative_start": 0, "end": **********.007501, "relative_end": **********.007501, "duration": 0.37705492973327637, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.007513, "relative_start": 0.37706708908081055, "end": **********.073978, "relative_end": 1.9073486328125e-06, "duration": 0.06646490097045898, "duration_str": "66.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45712304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01265, "accumulated_duration_str": "12.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.03794, "duration": 0.01193, "duration_str": "11.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.308}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.057912, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.308, "width_percent": 2.292}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.062831, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.601, "width_percent": 3.399}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-373597772 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-373597772\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1998237739 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1998237739\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1113449623 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113449623\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126613193%7C18%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNBblRWN3RSalVBaEgrV0RWbHROU2c9PSIsInZhbHVlIjoiTzAzaTJreXZNNkZjVmdzL2k1Ykl0RVNjSmlNL2FSMGUreUhMY3NFRjh2SVBoNHFLVFFsVVhmaXpiM0R0UWxWODJQV1gvNE1YaG41aUVjMVlrd0Nvc2FrZHhVRzNnSStwbXNvYmhIQ0tGUUdESjZRQXg2M2J0Q05LN1RiWktmU0YveitEK2VkZWhBN0kwNzVtQzZlZGVlTlM3MEpOYXpua2xEUGVKWEFkaURoaVBva2xLRllNMWNXTnRtNWxSMjJTYUxJMzlpMTgzcW01NXRacjU3ODM4ajhtaGYzUGhKOUxhV3JTM1Voc2RoSzk4ZTBJRGlsMjM2SmpDZFhtVWtoUy9rYVBEQmhldkNzbnhzeEdMTjVuUWJKdDl5OU93Lzd2V1B2RDdLV3BtMUl0UVNyRVUxemViWmJWQ3NRRnNocmMzVGRSN0VRTUZRZXVEN0dKSlpTVUkveDVhSC83cXdOZU5RRlNFS1VWdDNCNU0zdVVyZlhiR29ZVjFGNkZJR3RhOGFpck4rTlkveE1WamN2VUhBVjdQdUMvWVJhRXJzVVIvVlVrYm1zRHRFMEV5bEwzc3pVUjByRm1jeDd0V2Rpb2lhKzN1ZVFveGtRbXc2ZmpVOFMxaXd0S1RxT1dFK1JuWVE4UjUyRTBPamhOaE4vM3lFVzduWThMOUF0eGM3TVIiLCJtYWMiOiJkOWNiMzA5NjhjYmQyZTliYTZlZTg5ZjZhZDFjZDE3MTE0YzMyNjc5ZmVhNjE2ZWI2ZGU1NzNmMjFlNzdmMjFmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpvNUlKSUhXdG5YdUJRZ2pYcGFzdnc9PSIsInZhbHVlIjoiN2ErYzdua3ZOWkpzSGlyd1lQKyt3RFZhbmZsTDRUVng4Z0tUeENuOWJzbUx5SlpvaEJTbm9xN1N6ZkFtT2pSMUFjNlp5VW0zdGIyVUNrZW55SnExNjl3N2dyU1EzOXZMcHZkb3RUZFRJbytkSUhROVg1bmRYQ1hpVjlLRjJVeDVRTkZPZDk5bzRCSExHRVVyWEVsbktWbDlvcnZYcm5uYzYzcVdDUVhPaXdsL3FkQkRpdXZFSU1UOHptT3lKemF0aVEyVEhtRW9YZmZoRm04bmN6dFpXak0zTmVramlZWjdMNUg4Rk4rZCtjcDcvUklBR0VlN2JkN2hTdkNNTHdJZDFBUWhySXJrY3F5Nk9JU2R1TnRsQTAybWVJV1BiVWhBbXdRRkltMVN4V2taSEVSWU5jM3ZranhSOTJ1NkdReUhvMDV5U21xQ05WUkQyaU83bkZ4WkZpRGI0QlRPbHhpRFpMaEs3VVVocXBhQ09tNXplYXVwSlpPU0Y3QTNmNytQbFpEUEh2aE5DTDdTbGZRWmlMQk9Ja3lwR3g2WXAzWndha0N2VnExVmFEU2t5a3dsdHA2YWZSSHRyVlc1eFcyV1hpdTZaMGVZV1diY0JtWnFvSERFNG5teW5FN1Q4M3RDank5alcxSEZFcUVtaW1YUHNKQkRXOUZzeGtUb1UzQkMiLCJtYWMiOiJhOGU3Yzk5YTBiZmQ3ODI3NDkzZGYxN2E3MzlkMjkzYzAyMzVkYTUzNGZjOWZhMzQxYzEyZDc5ZDJlODVjN2UzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-183011577 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183011577\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-39373452 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtSVDVRTEUzU1NEK1FUcHd3Z1hWWGc9PSIsInZhbHVlIjoiNDdFZ1grYWh4c0lRY0ZMV0NzSDQ4cmtRZ1J3Wkg2WVQwTi9wWXVJM09ORUJRSzA4RlVxTmhqTExoYVA2cHJSaWtlb2VOVU9BdFVScTVQa2JPb21GRjJ3YUdHRDh0cm9TTmhQM21yL1hSY3ZCcm05bkdPM2hhYVhZbmh6cU1QSExZK1o4NC9ON2Zhdk1BUDNNL014RnFDMXdFWHBrV0JVa1ZnUFNkQ1dLR1g3R3RXVFk4bFdFeTk1aVBrV0drUWN4L1RncGlzM1NKdkVHRG9KWmVYSVg4N1Mrc1BGNXhZVmh4MFNvOVl3NlU3eml4UGRabGlpYjJZT3FxN0FPcGlpc2RnKzVjNWNSVEkxWEd1bFFjQzJ1TG5uL0xCeVByRDRUdVJ6TS92QitnQTVLeWJNeDNxbi9QMGw2S0k4cGw3RW9nSncrckR3ZzFPVlJpRFlxejNTWTAwdW41a05PUitqa0JYK2o3Y0Q2Z0x2MUo4UXJ2Z2NIZ0xYc2VYTlIwYzY0Z1Byc2VHMzAyNTVYcWI0aExrRTR1OXJHbTF4RG9UMld4UlpSbEJKU3VDUnk2LzZjSWFndW1Vbk1QbDlkVERnNDZTN2RUY1JhS0pLV3VMOFJ6aGwrMnNYWjhDTTFuQmthN3JvK1A1RHBsalNrcGRSZVd0R09kVHRwZ0FQeENnNWwiLCJtYWMiOiIzMTVlODM1NTVhNjM0MDdlM2NkOGVjMTYyZGZiMThlYWFmMTkyOWMzNjY0OGE1N2YyOWY1ZWZhYzM1YWYxOWVlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZXT1FWSnp2ZFhlTE41cTAwbFlSYXc9PSIsInZhbHVlIjoiOS9GU1Fpc2JpYldaSDc0WDI2SkllajJPbkJtdEZuOGtFbHllTFAzVEh4TWdMWlV4TTRvZzcxWElDWkRzV1AwRWY1RlFjN20yQ01mL1k2eG9hU3k1Y1lGNWNCTHhQcVZROGIxczFsdWNrQjB6aEpVbFhDSXhXdlBSZzBYckVIbTRQa0VSWjdvbkMrNUlQb0lGY3JiVU9jRDVFckJaTjNCek94VTkwSlViY0Z0Zi9oUlFWS1JYUVlUUk42UFFiMytqOGFaTkd6K2hNTHA2NGZYYU51U2MxUFA4eWRrOHR0aUZqWnRSaGtJMDhBbEk3R2ZUVHRTRlptYzBtUUFRbHc1RzVocy9ZUmVmTndiYk9SVVJZMG1QL09JbGUreDAzd2pIbXEwR21RY1R3VWNrekY5TitCVFZKN05pdHh6Y2pQcldDNkF5djhDTjdUWk5BejI5OEwvZ1cxV0dseTJkOERpSUhpSDFTSUtjWVVGaStNZ3N3dFAybWdHZnJBRVpiQk4xRGZWZXk1Mm4wa0x5MnVpaUsvcC8veURTaTlJb3RpVTN5Y1M0ai95VTdTV1QyeDdFMjU1d2dYd2pZSUV6NlhPd0NvclVCWXhHTm91aVZKSXNIRmEvMUNKUHpNOHlkT29Ydm55S3NLbldxNU9ZYVg3SkRDemZCU2UweXEwMDBhaUsiLCJtYWMiOiIzNTRmYjhjNDFlYmYyYTgzNjE0NzVjMDAzNGI0ZDA0M2JhNmJkZTExZDcxNGQxOWRlZmFhYTMyMzJkMzU2ZGI4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtSVDVRTEUzU1NEK1FUcHd3Z1hWWGc9PSIsInZhbHVlIjoiNDdFZ1grYWh4c0lRY0ZMV0NzSDQ4cmtRZ1J3Wkg2WVQwTi9wWXVJM09ORUJRSzA4RlVxTmhqTExoYVA2cHJSaWtlb2VOVU9BdFVScTVQa2JPb21GRjJ3YUdHRDh0cm9TTmhQM21yL1hSY3ZCcm05bkdPM2hhYVhZbmh6cU1QSExZK1o4NC9ON2Zhdk1BUDNNL014RnFDMXdFWHBrV0JVa1ZnUFNkQ1dLR1g3R3RXVFk4bFdFeTk1aVBrV0drUWN4L1RncGlzM1NKdkVHRG9KWmVYSVg4N1Mrc1BGNXhZVmh4MFNvOVl3NlU3eml4UGRabGlpYjJZT3FxN0FPcGlpc2RnKzVjNWNSVEkxWEd1bFFjQzJ1TG5uL0xCeVByRDRUdVJ6TS92QitnQTVLeWJNeDNxbi9QMGw2S0k4cGw3RW9nSncrckR3ZzFPVlJpRFlxejNTWTAwdW41a05PUitqa0JYK2o3Y0Q2Z0x2MUo4UXJ2Z2NIZ0xYc2VYTlIwYzY0Z1Byc2VHMzAyNTVYcWI0aExrRTR1OXJHbTF4RG9UMld4UlpSbEJKU3VDUnk2LzZjSWFndW1Vbk1QbDlkVERnNDZTN2RUY1JhS0pLV3VMOFJ6aGwrMnNYWjhDTTFuQmthN3JvK1A1RHBsalNrcGRSZVd0R09kVHRwZ0FQeENnNWwiLCJtYWMiOiIzMTVlODM1NTVhNjM0MDdlM2NkOGVjMTYyZGZiMThlYWFmMTkyOWMzNjY0OGE1N2YyOWY1ZWZhYzM1YWYxOWVlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZXT1FWSnp2ZFhlTE41cTAwbFlSYXc9PSIsInZhbHVlIjoiOS9GU1Fpc2JpYldaSDc0WDI2SkllajJPbkJtdEZuOGtFbHllTFAzVEh4TWdMWlV4TTRvZzcxWElDWkRzV1AwRWY1RlFjN20yQ01mL1k2eG9hU3k1Y1lGNWNCTHhQcVZROGIxczFsdWNrQjB6aEpVbFhDSXhXdlBSZzBYckVIbTRQa0VSWjdvbkMrNUlQb0lGY3JiVU9jRDVFckJaTjNCek94VTkwSlViY0Z0Zi9oUlFWS1JYUVlUUk42UFFiMytqOGFaTkd6K2hNTHA2NGZYYU51U2MxUFA4eWRrOHR0aUZqWnRSaGtJMDhBbEk3R2ZUVHRTRlptYzBtUUFRbHc1RzVocy9ZUmVmTndiYk9SVVJZMG1QL09JbGUreDAzd2pIbXEwR21RY1R3VWNrekY5TitCVFZKN05pdHh6Y2pQcldDNkF5djhDTjdUWk5BejI5OEwvZ1cxV0dseTJkOERpSUhpSDFTSUtjWVVGaStNZ3N3dFAybWdHZnJBRVpiQk4xRGZWZXk1Mm4wa0x5MnVpaUsvcC8veURTaTlJb3RpVTN5Y1M0ai95VTdTV1QyeDdFMjU1d2dYd2pZSUV6NlhPd0NvclVCWXhHTm91aVZKSXNIRmEvMUNKUHpNOHlkT29Ydm55S3NLbldxNU9ZYVg3SkRDemZCU2UweXEwMDBhaUsiLCJtYWMiOiIzNTRmYjhjNDFlYmYyYTgzNjE0NzVjMDAzNGI0ZDA0M2JhNmJkZTExZDcxNGQxOWRlZmFhYTMyMzJkMzU2ZGI4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-39373452\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1514664605 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1514664605\", {\"maxDepth\":0})</script>\n"}}