{"__meta": {"id": "X96d302cec7d0131be477b441153ee319", "datetime": "2025-06-28 00:36:45", "utime": **********.960546, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.453389, "end": **********.960575, "duration": 0.5071861743927002, "duration_str": "507ms", "measures": [{"label": "Booting", "start": **********.453389, "relative_start": 0, "end": **********.812988, "relative_end": **********.812988, "duration": 0.35959911346435547, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.812998, "relative_start": 0.35960912704467773, "end": **********.960577, "relative_end": 1.9073486328125e-06, "duration": 0.14757895469665527, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45600376, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00259, "accumulated_duration_str": "2.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9224648, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.32}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.934336, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.32, "width_percent": 20.463}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.941017, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.784, "width_percent": 16.216}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-471519527 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-471519527\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1969505166 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1969505166\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1240890819 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240890819\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2033442327 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2817 characters\">laravel_session=eyJpdiI6IjE5YmxhMUw3bXJQWWh3R0habDVwcFE9PSIsInZhbHVlIjoiTkZUZHViY3FpMFMvWUgzVm1OV0JwUHZoMVU1WjA5ck5UbEh4NjBRemY0ejlwWVNadXBvOWxTWVFjQzJMNGxNSTc5NzJwa1dxQ2hqL2VlQUZvMUs4YnV3Z2RhaExoU0p1RE5pdlhQQ1dzN0RpS0xnTWxrcmEvVFZUZERCSnFDQlhEdG03UkROakJlb2huQ24xSWlPcTA0QnpwdnNZaEl5T2JnWkpNMHNSVGpUeGZpWHJNcDBBWVFmaHE4Z0dkYmFlTm5VRWR4YWlaOHNZa2hPaDh2RGdndHB2MVJLZEFZMUU4Nmttek5WOEhIOExCS0p2S3h1Mm83MFRQc1U4NHMzN2lsY1J6ZnNLdVBhOXVCbktKdkNNdUQrcGJHZm9sUDkyRWhHVW90U0twWWw3Q3d1VzlyYzgxWjY2U2MxWWdnOXhaUEJkK0k5dFU1dWEvaDNXNmZtMkY2WUJKdVZSNnlFYVF0Y0JtL3Fac0tRZ3dyWDFwVXNSdmNhNzIyVjBiSHpQbDM1OXBiL1R6WndGT25IMnFYV3ZBNkdsNjB1SU54NXRIUWJBdTM1eDBiditjVzlWcW1DQlVTMkgzSmw3S0xYdDJmRVI4MXNEaENKc1dTbFRMR1pPWUVZc1crMkY4QnNxMDl2eEpvclhUb3hhMjZqYnFqaGVGTEdOWWxSZzBXNUUiLCJtYWMiOiJhMmVhNTI4OTM2NDBkYzJlNTU0MzRmNWUxYTg5YWQ0OGIxNGMyMmZhYzM5MmYzYzllZGEyNWMxZTVjYzdhNWUyIiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=zkpa4v%7C2%7Cfx5%7C0%7C2004; _clsk=9vvwq3%7C1751070959457%7C28%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkgwZHdVWWM0NTFSVE53VWlYWHpPTGc9PSIsInZhbHVlIjoiaDVwWG9mRVdoMCs5NllvcXVTSFVkZXpaZEF1enZYYThmYWFGYWRWc05pWW9XcktuajFsd1dUY0JqQ0I1MzBlZXJ3UmFOY1UxSWxkVXVzK0lQcEdTc3VsdU8wdllLYzNidCtvYlFVaEtFS2Znblc4eDJmVHpMVnFXRERNK3RvRjdiVlRiaUZoYzZtUjBrQi9sbWZkcm1teG82MGkwNnZseWJxUWo2VkVPMlN6bCtMYmtXSVBBTlI0RlE5R0oyWlNubG4zc1Q4Wk01dEhUTTlFck5pckN3WTRRbHVvQkpSWUoxcVdBRFoyVXAwT3VXaGJ3cUt3a1NScFlScjkzMFc4QUdnbGlJR0Z0bHZRd0lZUXVDdVhsQ05YL2gxTFk0Wmt6amh4ZjNCdElwZzA3SFR4ckVoZkhxU1J5SWZOYVhnZERnV3pibm9JNTVLcktkek9LckdwZzRydkpzazJ6RXBiS25uOC96a0ROSHZKa2dhOHVsbDg4dFgyT25sVTZnUThrNE54ekhLUFZlQW1HM25LOVhMV1diRU1pWFpaSzJwU01NUHV5ZDFqaFBPQlFaLzJ3cExKRFJlVUZzS3BvUFprMHBkUXZtUkowWXlCTTZCV2JwOW1scnFITkIwNGxrbTIrZ1Z4eFFScVcyZmQ1a0xUdFFlU3c1RHFnK1pCWStnOUYiLCJtYWMiOiI4YzgxZGEyY2VhMzlkZDM0ZDJiZjU1YTQwZjQxNWJiNWRjNTUyNmRjYmFjMDgzMDk3ZDZhY2M5MzkxZWRlNTA2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpYUy9FQ0QxcCttc1VUMmFrWVFodmc9PSIsInZhbHVlIjoiTXpmdmhYYk5aOEtLOFBVQ253ajl1OTdKbTh5VUhYTktMSjB2QmtVMTVQKzRKdXlrOWVtYkZxc0o4Z1NqNENURFBOYjd3UlFVbjZPNDh2dzNZLzZPYmtQMnVPNXR4NFJNV1NUK1ZWaWxjN2JtVjhSYlZrL0ZBa0VLK3lDMTlOWE5CMzBTc2FTUDl5OWpBaUs2WUE3b09Vamx1bW40bGpiSkNMdkZKUHhMcW1sUzBLd0xtazZ3bjVQSXZxd2dsL0Y1QUpUM3c0YUdmWDdQbHVuTldsYmFhZWhRbnZHM3haS1RRR1NjV2VQdVAwSFlBc3FuTTByN2JsUFpEaTlWYUVRRHFkS1RJSEMrcjRaUU5WRnpOYTI5dndRTmxrSGRuRGRKZXg0V2JoS0JvVkdSZzlTOC9KODhiaUFYZlBwZzlnNVR1Z1EzNVdvL1dXOHczV0crTnlOTUtTL21CYUh2bkU0bHdDTkFndDhUNEx2TTNRdVFJL2hSd2ZGNDNYRm1RU2hTbjIzcFhmZ1M1SWlaOHJpaUVjK3hiWGJlaTNXREU5VnlOczVVaHZ2cWk5TlJVRlhOcmd6SXF0aVpSYlg3dEttc1FtVC8vcU9yRWtDS2d5di9ZcVV0MnFRcmVKaUNyaVJZZDJMVUNFanNCMmFVY2R0R3NMUDRWZDBwUnI1T2pKWGwiLCJtYWMiOiJmNmY0N2ZiZDNmODZiNTJmYTk3NzgyYjY1MDMxNzk1ZDMwNzYzMzM1NjU4MzNiZDQ4NzA0YTQxNzZhMDc5NjIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033442327\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2059781379 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">chEs2sQpmg2fYpUuqAOS1ZcIPLmVIQdx921QTw47</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9XNo0PIFsccsEXzvfQLAUsci7MaweQoaRcTvbnBl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059781379\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-966909163 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 00:36:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBFSDcrckRJZXY2RTFqa0I1Q2x4R1E9PSIsInZhbHVlIjoiNEZsRnVzS2tYdHR5bExkNjAzUmVlTUxNNnhjRzJVNUVzSWlXV0t4ZUJIZDNEYlJmdlVHbnpyaWRXVGxXK01kZXV2dFUyZ0Y4MEZ3djBCS09DbnowbG8zQW9lY1V1RllGMS9TZmUrcFlpU2Uzbkdrb0VTaTAxUEpWTTZoQnB6UmNUb1lZVXh0eTlXVDRHZEJIRER0QVdsTnl3b095YjVWVFZXOEIycXJJd1Bjci9kTnJKSmR1VTlJTHNKY21lSlNDUjBia04zQ0pQcGg0amY4N2NpbDUxM2xLQXFYd1gvbnF3QWcxc2QvVHVVaXhMMGFWNklGampyUkkyOWFNTkU2SkZLcGpKdUJWbW16Rzh4QjFSWGpRUDJWVzAwSS9IdlZndTIyMGsyYkZSSzFnL0U2dzFxT1JrNGw3TzBHZDgwNUMzd0YwVzFaMDRlN09QWFZMRHpMRVArcFhqdHp0TkI3M0l4d1JuMStHRis5bWQ3NlBEVTNwSEVoV3htSk9JRDRKUUFpdlN0K3pJMWFwY2ZKQWx3NWFKMjgzcmlWWTVKM0VpK2Y4dGFtZENjQmpuck9YTmVxNG9FMndRQ1pYcnpnNEpCMDVJa1BmaExhYlBobTNablE0WjlGM2V3RXVnRUs5WlN2Y25ZOEdZY0pYN0ZqOVpBUFYyMzVhbWtLeWMyMzMiLCJtYWMiOiJkMzRiYTY4MzQzZmJiMGQxMmNjNjk2MThhZDRkZmFiZmVjZGRmNGZmMjI2NjU0NDc0NjIxNWY4NjdhN2YzZDYxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:36:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJieXdvRURHWmF3dC9QY0JXSDRSMHc9PSIsInZhbHVlIjoiYzJsRDlUWmFmTUJWZFRaZldzU3k3dmhLazJ1Nms3VjVGL3dUKzJJRFJDVG5uaHBCcjhMZGRwaDIyVWNQMU5CT3VicC9jbTJ5aWt0aldSNkt5enBESlI2L01adFQ2VGtaUUNlNUtaVnZkK1dhck9aVjFkUzdhQzhtT0xTdWp4dm83L0d5S2dObk1YLzgwQnIxeGZDZGlQYThmWFIvdTdxMTVkeFpQNE9IMHhPMHptZ0NDYUNKVWhJNVZseE9ncjE2dlJRUEdRVUtUdWt4RTMrMFkvVGw2S1JyRlpsd2NRWVU5MW1HZXZ5OFcvSTBpN1R5TlhsS0QvTmU1TnhEaVRSZ3JKZHM0RW0zbUM5di9BR2V3Z0hVKzA4ZmhBMGpRRFRhMkpWQVFUOHJWQXM4Q3ZvanBmWXdWWHVlUVdKOTNhbkVRREdnZWU3SGxzRThhQXlKb292TGpwWTlVRWgyV1c4amk3cy83TXAzeGN4TnJIQUVaQklUSkZiWndkTUFrSmd2eEdHYkc1clRyVUdjWTNmNWpMdVpzQitaSUJrVklnTE0wSDlFOFpWaUxJczRnSXpCUElKTmJ3eFpMMFhSYlkzSU9FV2g5cDd6UHhnMXIySmNDS0N6QXFSQmd0aWVFemlNZVFucVhzNnJmUjI1YUNRMUlWZFVBbWxyVGV6cjNMOWIiLCJtYWMiOiIwMWY3MjJiOTc4NGZmYzMzZmFlZWVhNTk3MzA2ZDc4YjhiMTNmMzFhNDE1NTIxMmIyZTdiOTQ3NzE1MGY5NmU3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:36:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBFSDcrckRJZXY2RTFqa0I1Q2x4R1E9PSIsInZhbHVlIjoiNEZsRnVzS2tYdHR5bExkNjAzUmVlTUxNNnhjRzJVNUVzSWlXV0t4ZUJIZDNEYlJmdlVHbnpyaWRXVGxXK01kZXV2dFUyZ0Y4MEZ3djBCS09DbnowbG8zQW9lY1V1RllGMS9TZmUrcFlpU2Uzbkdrb0VTaTAxUEpWTTZoQnB6UmNUb1lZVXh0eTlXVDRHZEJIRER0QVdsTnl3b095YjVWVFZXOEIycXJJd1Bjci9kTnJKSmR1VTlJTHNKY21lSlNDUjBia04zQ0pQcGg0amY4N2NpbDUxM2xLQXFYd1gvbnF3QWcxc2QvVHVVaXhMMGFWNklGampyUkkyOWFNTkU2SkZLcGpKdUJWbW16Rzh4QjFSWGpRUDJWVzAwSS9IdlZndTIyMGsyYkZSSzFnL0U2dzFxT1JrNGw3TzBHZDgwNUMzd0YwVzFaMDRlN09QWFZMRHpMRVArcFhqdHp0TkI3M0l4d1JuMStHRis5bWQ3NlBEVTNwSEVoV3htSk9JRDRKUUFpdlN0K3pJMWFwY2ZKQWx3NWFKMjgzcmlWWTVKM0VpK2Y4dGFtZENjQmpuck9YTmVxNG9FMndRQ1pYcnpnNEpCMDVJa1BmaExhYlBobTNablE0WjlGM2V3RXVnRUs5WlN2Y25ZOEdZY0pYN0ZqOVpBUFYyMzVhbWtLeWMyMzMiLCJtYWMiOiJkMzRiYTY4MzQzZmJiMGQxMmNjNjk2MThhZDRkZmFiZmVjZGRmNGZmMjI2NjU0NDc0NjIxNWY4NjdhN2YzZDYxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:36:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJieXdvRURHWmF3dC9QY0JXSDRSMHc9PSIsInZhbHVlIjoiYzJsRDlUWmFmTUJWZFRaZldzU3k3dmhLazJ1Nms3VjVGL3dUKzJJRFJDVG5uaHBCcjhMZGRwaDIyVWNQMU5CT3VicC9jbTJ5aWt0aldSNkt5enBESlI2L01adFQ2VGtaUUNlNUtaVnZkK1dhck9aVjFkUzdhQzhtT0xTdWp4dm83L0d5S2dObk1YLzgwQnIxeGZDZGlQYThmWFIvdTdxMTVkeFpQNE9IMHhPMHptZ0NDYUNKVWhJNVZseE9ncjE2dlJRUEdRVUtUdWt4RTMrMFkvVGw2S1JyRlpsd2NRWVU5MW1HZXZ5OFcvSTBpN1R5TlhsS0QvTmU1TnhEaVRSZ3JKZHM0RW0zbUM5di9BR2V3Z0hVKzA4ZmhBMGpRRFRhMkpWQVFUOHJWQXM4Q3ZvanBmWXdWWHVlUVdKOTNhbkVRREdnZWU3SGxzRThhQXlKb292TGpwWTlVRWgyV1c4amk3cy83TXAzeGN4TnJIQUVaQklUSkZiWndkTUFrSmd2eEdHYkc1clRyVUdjWTNmNWpMdVpzQitaSUJrVklnTE0wSDlFOFpWaUxJczRnSXpCUElKTmJ3eFpMMFhSYlkzSU9FV2g5cDd6UHhnMXIySmNDS0N6QXFSQmd0aWVFemlNZVFucVhzNnJmUjI1YUNRMUlWZFVBbWxyVGV6cjNMOWIiLCJtYWMiOiIwMWY3MjJiOTc4NGZmYzMzZmFlZWVhNTk3MzA2ZDc4YjhiMTNmMzFhNDE1NTIxMmIyZTdiOTQ3NzE1MGY5NmU3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:36:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966909163\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1469635899 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469635899\", {\"maxDepth\":0})</script>\n"}}