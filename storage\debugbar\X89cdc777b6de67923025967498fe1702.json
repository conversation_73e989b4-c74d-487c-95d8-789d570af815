{"__meta": {"id": "X89cdc777b6de67923025967498fe1702", "datetime": "2025-06-28 16:19:12", "utime": **********.814887, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.371009, "end": **********.814906, "duration": 0.4438967704772949, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.371009, "relative_start": 0, "end": **********.752338, "relative_end": **********.752338, "duration": 0.381328821182251, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.752346, "relative_start": 0.38133692741394043, "end": **********.814908, "relative_end": 2.1457672119140625e-06, "duration": 0.0625619888305664, "duration_str": "62.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45703976, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026499999999999996, "accumulated_duration_str": "2.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.784324, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.906}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.794676, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.906, "width_percent": 18.113}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8002162, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.019, "width_percent": 16.981}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1225466955 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1225466955\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1456624618 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1456624618\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1644713024 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1644713024\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127548066%7C35%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpacUpMY29EbHFTV25JbU55blVUTVE9PSIsInZhbHVlIjoiTDdQZ0o4Y3pmcXYxZmNXS2NDeFJjNVFpRlJ4Y2dhTzdQc1ZkN0JISytNbHhMb1BsMzlUazlhQ0cyVkIwVXpqbU9FcEFTVzRONnA5TUVZcWRiQ1JURnJBdmdROUtORUF6VjczVnBiR0dmWHBtamJhVkNNVThiaDFRZzJIZFZoOUkrQTFsNkhEOFRUMThDallUZVN0V2ljMWtOMEVLUkdJSFkxMWgzRkNNcGlpellteFZWUEhKSUZNM28vVDc1MVJCcFRydFpiZVYvRWgybVp4NVlJUmgvVGNId0xOMWFFZ0p0RHNJVzlORm5JeUc0RE5SemV6NUwxeFA5NGkxSkd4M2xITkZJV0hrTzJhcFJRbGVuZ096OUpuSGhqdEJVZ3FlamliVEgyaVBnVktKeVlGRnIwNFZ5Z1VjN0grQVhLeWRGNjNPOU1kZitiZTVRbGVWSjhKekE4Q1FGOXlXaVV6ZkUxbTJMVG15M2diQjRLUHoyTmVSTjJxMEhoSnI3SFZFOVJEdXF4clF1N2NHOVh0N1FDdWxjZ1l2a1pITWtBblpCNFU1YjVydUhISmRaTG9RdzNXc2luRW1scHEzU256eDVxZ2JYNVoraC9NME9ocXlNRHFrRUV5YXlrRzlPLzdFZkN4cEJzRXZWeURsUVBBUWNrcEZjNXFqM05lQWZGRkoiLCJtYWMiOiIzZmMzYjExNzNjYWNkMjQ5YzQxNzRjZmU1MjIzMDJiMzMxOWU3YWJlMmUzMzMwNjg4NjM2N2FlYTEzYmUxMzgzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitpcnYwaXljTGJ4dXdTNzlFQTBIZ3c9PSIsInZhbHVlIjoiWHFDRW0yL3ZVVkZnVEN3VnpIbUMwaHJiMGt3eDZRVW9nRFVzalRoZHJRdnRuVzFZWWQ4MmlhajRKUnh2cVRhN3QzUVVqaTRRTkdEUHMrMGZ0M3FTdy8wRG0yRk9GYlliQUpYdS9jbmRTUmpVemNzYmJpcnRVWHBhRzd5bmJXLzJFdGxTb09pVlVPQUc3UnpYRllsUEczMVViMDM5K0VzS1AvK0l6U0xsa2E3ZTQ1Ly9IVzUvVENac1FHcktrUGpXeUJ3aENYRndMeGFjUi9UQ21wQURtR0FUS1dPck5RV29SeVQxS0laTFRuRlJFTWZNWG8vOTBsSXpycGU5QXhka21PZjFldTFSZWNaZ29HVTBYTnNVbTZuUGx6djZqaG9JOHBna1NyTmc0OWo0MEFTYk9ESXMzTE1jaWNGOEEzN0dMeDdMSUhjdlNObU8zdGx0Z20xZjFvZVBTU3drRHhraDZabzBzc0ljSHh4bTNnMlpLS3dFSEEzV2tIV3NhTzAyRE5kUE04YitSa0xOOUZBODF4ekoyeGtsaWliOVl4VU1vTFBuUVJWQnB0alVFUDBhWmxqUllJMFpoNUFGKzZ0Z0k2WlJKdS9PSTRNTGpXa2NVbU1CdEI2L0RuaXE4YjRqaFM2d0oyYUtrblBVOWt5NWNBa25PTG1mdjl1RE9uenYiLCJtYWMiOiIxODYwYzE4MWNiMDcyODQzMjJlZjVlYjY3MDI5MTRiNTYzYzMxMjM5OGEwMTZjMjdjMTJmZjM2YzhjMWI5NmNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1353939759 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlgxclpyTkJsRGRldnpqVm5FMFkzeEE9PSIsInZhbHVlIjoidnI0eG91Y2NqN0ZaWTdYMVNoWlpvTUEwNWVJKzFBL2cxemlxY0NHajNZUHNlU0sxcWFRb0M2TDlwaHdmRHduMW5FQm53aEJXQWUxK09IT01LaEFha1VCa0VUcXBqblorVVN2VUpvN2dTUlVJTFJhQ3RZZmFUa1RZNHlCS1lLV25qeWFDQzZYTk9YbmJzTnU0alZUbFp0MWtCa1piY3plQUM4V0krZFVuU3cyU2FXM1RXV1RkUVZ1N1NJT1Q0cEF0ZW8zT1B1RS85RXM1TFBURFhEOW90TTdkVHAzL0gvanNncStKN1ZtY1pwL0ZGUm5sYXduTjF3bVlwUHVFdm1GYzlFb2wvWi9mdjZsemV0QzNteXhOQzlTaCsvWGpEblZBMHI5enAxNHRWeUtEWlg1bElMbXpIOXVCdWxBKzMyYmkxaDdzSE9mV0NwdWhrZU5oaVNxZy9JSDhsZlRuRzhvL3V6Ykk4SFRVeUxOTG4zNENnNEt0SVBYUW5hOUtTRWlhZitINHhHbjB3aTlJMG1ueWE5QmtxQzhMMjd5N3g1SWFMTEhDYmZMR0svRS8yejhvc0RzcUd6djZHa3lCMmh4c0VGay8yWWVBVFV2WUZaSFhjb1RyQnR2dFlCS3Y5cVB6UmlhTkxwV3laVHFTUVdSTXVzbnlGM01YajVmNnUveXoiLCJtYWMiOiIyMTdkNWJlYzAzNDMwZmZiZDk2NmE5YTk2ODBjZjdmNDc4NWNlMDA3NmM2OTk0MTUwYzdkYTJkNDBlMmNiMzkwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRPd1pjWktmZnhBSCs1cXJnYVozU0E9PSIsInZhbHVlIjoiV1EvNmJaNUJwVGd3QWlIdm9FdHhwbjZBL1dYcWJpZmJCbllRNkRxbCtvOWdSZnpxWFZjRU9VRjRXMVdyTERqY0xlb3MvSVFVVnhjVzI1bFh0RHpFU1dXZ3ZYb2IwUVBaUVJqTXFQWkl6TWpNejdPa1NidXRhQnNacDNXdjN3Wjh5YmFxR1IvQmtYYWJxQ2ZzNzVFR0ZxTEZsay92RUJkTWFsamFUWUxnNVJhbWFLK2JEQnBaRWxKZ0NwRG5KbjBibHYrKzBBZ2M2bjNEN1hvK3g5YUluT3lCd2wyVDNua0lPaWptMzNHb0JNQldnRkZDYUpIUDVKR0NsZkN6dW9LTmVnR1k2cXlGTHZpRDVsVTZKaWNPZFAzL013eXR4VERuRXE1R29RSWZ0blVQTmdQbXFTZFo5Q1JzTzY1dU01aCtLMTNUc3RhNHR0RHRnc1U1dUFqRU84dTdCZ2QxT0F1QzdCVFp2dHp5TnZRS2JLYytOeWE4TnR6UVJ3U21FYTVPSkExY1p4Z2lkWkN0cE9HVGdRV1dzaFNZNy9sMFlDMllDWTZoZVV2VXltTDI0dHYrNGxRRUZCWXlMc2FacUNPTnRCSTBXRldyR3JlN2cyUEcvd0hMTVJIVWloVlFNYUFRdVNIM2VaTlZrVVRsRllxd2hIa0tnN0ltVXJodkg1dkUiLCJtYWMiOiJhODEwYmIyYjQzOTk0Y2FiMDA4ODgzZDE5OGRlMzQ3NTI3NDkxODMwYzFiNjI2MTJmZmYzYmQ5NDhlYjU2YTAwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlgxclpyTkJsRGRldnpqVm5FMFkzeEE9PSIsInZhbHVlIjoidnI0eG91Y2NqN0ZaWTdYMVNoWlpvTUEwNWVJKzFBL2cxemlxY0NHajNZUHNlU0sxcWFRb0M2TDlwaHdmRHduMW5FQm53aEJXQWUxK09IT01LaEFha1VCa0VUcXBqblorVVN2VUpvN2dTUlVJTFJhQ3RZZmFUa1RZNHlCS1lLV25qeWFDQzZYTk9YbmJzTnU0alZUbFp0MWtCa1piY3plQUM4V0krZFVuU3cyU2FXM1RXV1RkUVZ1N1NJT1Q0cEF0ZW8zT1B1RS85RXM1TFBURFhEOW90TTdkVHAzL0gvanNncStKN1ZtY1pwL0ZGUm5sYXduTjF3bVlwUHVFdm1GYzlFb2wvWi9mdjZsemV0QzNteXhOQzlTaCsvWGpEblZBMHI5enAxNHRWeUtEWlg1bElMbXpIOXVCdWxBKzMyYmkxaDdzSE9mV0NwdWhrZU5oaVNxZy9JSDhsZlRuRzhvL3V6Ykk4SFRVeUxOTG4zNENnNEt0SVBYUW5hOUtTRWlhZitINHhHbjB3aTlJMG1ueWE5QmtxQzhMMjd5N3g1SWFMTEhDYmZMR0svRS8yejhvc0RzcUd6djZHa3lCMmh4c0VGay8yWWVBVFV2WUZaSFhjb1RyQnR2dFlCS3Y5cVB6UmlhTkxwV3laVHFTUVdSTXVzbnlGM01YajVmNnUveXoiLCJtYWMiOiIyMTdkNWJlYzAzNDMwZmZiZDk2NmE5YTk2ODBjZjdmNDc4NWNlMDA3NmM2OTk0MTUwYzdkYTJkNDBlMmNiMzkwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRPd1pjWktmZnhBSCs1cXJnYVozU0E9PSIsInZhbHVlIjoiV1EvNmJaNUJwVGd3QWlIdm9FdHhwbjZBL1dYcWJpZmJCbllRNkRxbCtvOWdSZnpxWFZjRU9VRjRXMVdyTERqY0xlb3MvSVFVVnhjVzI1bFh0RHpFU1dXZ3ZYb2IwUVBaUVJqTXFQWkl6TWpNejdPa1NidXRhQnNacDNXdjN3Wjh5YmFxR1IvQmtYYWJxQ2ZzNzVFR0ZxTEZsay92RUJkTWFsamFUWUxnNVJhbWFLK2JEQnBaRWxKZ0NwRG5KbjBibHYrKzBBZ2M2bjNEN1hvK3g5YUluT3lCd2wyVDNua0lPaWptMzNHb0JNQldnRkZDYUpIUDVKR0NsZkN6dW9LTmVnR1k2cXlGTHZpRDVsVTZKaWNPZFAzL013eXR4VERuRXE1R29RSWZ0blVQTmdQbXFTZFo5Q1JzTzY1dU01aCtLMTNUc3RhNHR0RHRnc1U1dUFqRU84dTdCZ2QxT0F1QzdCVFp2dHp5TnZRS2JLYytOeWE4TnR6UVJ3U21FYTVPSkExY1p4Z2lkWkN0cE9HVGdRV1dzaFNZNy9sMFlDMllDWTZoZVV2VXltTDI0dHYrNGxRRUZCWXlMc2FacUNPTnRCSTBXRldyR3JlN2cyUEcvd0hMTVJIVWloVlFNYUFRdVNIM2VaTlZrVVRsRllxd2hIa0tnN0ltVXJodkg1dkUiLCJtYWMiOiJhODEwYmIyYjQzOTk0Y2FiMDA4ODgzZDE5OGRlMzQ3NTI3NDkxODMwYzFiNjI2MTJmZmYzYmQ5NDhlYjU2YTAwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353939759\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1210764831 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210764831\", {\"maxDepth\":0})</script>\n"}}