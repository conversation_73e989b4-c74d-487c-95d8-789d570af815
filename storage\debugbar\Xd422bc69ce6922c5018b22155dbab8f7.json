{"__meta": {"id": "Xd422bc69ce6922c5018b22155dbab8f7", "datetime": "2025-06-28 15:03:09", "utime": **********.330402, "method": "GET", "uri": "/financial-operations/sales-analytics/customer-analytics?warehouse_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122988.902731, "end": **********.330414, "duration": 0.42768311500549316, "duration_str": "428ms", "measures": [{"label": "Booting", "start": 1751122988.902731, "relative_start": 0, "end": **********.239788, "relative_end": **********.239788, "duration": 0.33705711364746094, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.239797, "relative_start": 0.3370661735534668, "end": **********.330416, "relative_end": 1.9073486328125e-06, "duration": 0.09061884880065918, "duration_str": "90.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46226592, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/customer-analytics", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getCustomerAnalytics", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.customers", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=359\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:359-488</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.02629, "accumulated_duration_str": "26.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.273303, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 5.782}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.282606, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 5.782, "width_percent": 2.016}, {"sql": "select count(*) as aggregate from `customers` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 368}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.286314, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:368", "source": "app/Http/Controllers/SalesAnalyticsController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=368", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 7.798, "width_percent": 1.521}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 378}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.289598, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:378", "source": "app/Http/Controllers/SalesAnalyticsController.php:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=378", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "378"}, "connection": "kdmkjkqknb", "start_percent": 9.319, "width_percent": 2.51}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 387}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.291996, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:387", "source": "app/Http/Controllers/SalesAnalyticsController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=387", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 11.83, "width_percent": 3.157}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30') and not exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` < '2025-06-01')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 406}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.294434, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:406", "source": "app/Http/Controllers/SalesAnalyticsController.php:406", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=406", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "406"}, "connection": "kdmkjkqknb", "start_percent": 14.987, "width_percent": 3.918}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30') and not exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` < '2025-06-01')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 421}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.296787, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:421", "source": "app/Http/Controllers/SalesAnalyticsController.php:421", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=421", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "421"}, "connection": "kdmkjkqknb", "start_percent": 18.905, "width_percent": 1.103}, {"sql": "select `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`, COUNT(DISTINCT pos.id) as total_orders, COALESCE(SUM(pos_payments.amount), 0) as total_spent, COALESCE(AVG(pos_payments.amount), 0) as avg_order_value, MAX(pos.pos_date) as last_purchase_date, MIN(pos.pos_date) as first_purchase_date from `pos` inner join `customers` on `pos`.`customer_id` = `customers`.`id` left join `pos_payments` on `pos`.`id` = `pos_payments`.`pos_id` where `pos`.`created_by` = 15 and `pos`.`pos_date` between '2025-06-01' and '2025-06-30' group by `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 766}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 427}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.298683, "duration": 0.013560000000000001, "duration_str": "13.56ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:766", "source": "app/Http/Controllers/SalesAnalyticsController.php:766", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=766", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "766"}, "connection": "kdmkjkqknb", "start_percent": 20.008, "width_percent": 51.579}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 776}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.313797, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:776", "source": "app/Http/Controllers/SalesAnalyticsController.php:776", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=776", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "776"}, "connection": "kdmkjkqknb", "start_percent": 71.586, "width_percent": 15.063}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 776}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.319105, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:776", "source": "app/Http/Controllers/SalesAnalyticsController.php:776", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=776", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "776"}, "connection": "kdmkjkqknb", "start_percent": 86.649, "width_percent": 9.471}, {"sql": "select `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`, COUNT(DISTINCT pos_v2.id) as total_orders, COALESCE(SUM(pos_v2_payments.amount), 0) as total_spent, COALESCE(AVG(pos_v2_payments.amount), 0) as avg_order_value, MAX(pos_v2.pos_date) as last_purchase_date, MIN(pos_v2.pos_date) as first_purchase_date from `pos_v2` inner join `customers` on `pos_v2`.`customer_id` = `customers`.`id` left join `pos_v2_payments` on `pos_v2`.`id` = `pos_v2_payments`.`pos_id` where `pos_v2`.`created_by` = 15 and `pos_v2`.`pos_date` between '2025-06-01' and '2025-06-30' group by `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 802}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3229551, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:802", "source": "app/Http/Controllers/SalesAnalyticsController.php:802", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=802", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "802"}, "connection": "kdmkjkqknb", "start_percent": 96.12, "width_percent": 3.88}]}, "models": {"data": {"App\\Models\\Customer": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 11, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/customer-analytics", "status_code": "<pre class=sf-dump id=sf-dump-1778125682 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1778125682\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1743493107 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1743493107\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-512765942 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-512765942\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-213302913 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122972644%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVBejhkNnR3czBCL1RocWJScXRXR1E9PSIsInZhbHVlIjoiZS9RSzNKZ0pWTndjdU9ocjE3Qlo5S0JwaG4waXRvdjhFVzU5ekN5VkR5MVRhaUZkQmVpRy9WZ0dIemlXcXFFTnN0OEtPOWRMamh6R2xFek43N1JQQTlJZ1J3MWZoVUMyNG1HN2pCUDdhbWs1dDVCbUFpeVhyMjZlU3UxVlg3eSttREp0VjFkQXRocGdQeEZFYzN4UGtPUDQwR0U3TURZTXcraUpUbVdrbVdYei9XQThkVG1KVERsTk1QSVRGWEY3d09USTZJTUh5bVUvT2lVTFpmRC9Zai9BY2xwSmV6L05uQWJtc3YrZWswMlNxajAvWmgvTmU1Vmkwakh1U2cxUmY2YW0vMXRiektLREZBOURSRTltTS9PSkxZMlh1ZmlaczhaZFVGRUk1L3BnM2piZDRwRHlWemdtWTc1THl4eUc2TTJNcyt5bFUrSlFOL3VQcGpOY2tzRVE4U1ZITitGZ3ZjVVhzVTVqRzBzWjgyVlZ2ckRCRjBkSkNMOVh2T0JQaWVSMkk1dzhSRmxHSlQ3dDNRaE8wTlJJVkQydVFqVzVsYVlJMGloOXVDMTFpUktFR1BWZ0xUclJvYzVRT25CaFpCaDJyM3U5b1VjZENWcmJ4UlJCVHU1OWNrYlRFM1dYS3FNOVpBU3FrbGM3VXJpb3VIUXdCaW51dXhocGozKy8iLCJtYWMiOiIxNDkxZjdiZDM0Y2YxNzE5Nzk2NDdlZjJhYjk5OTI2MGU3ZTM3ZDI5ZDc4M2Q5Y2MzZDRlZWM4N2EyMGJiMGQwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InkyM1MvWWZ0YjRiSFFqd3NoL2dTTnc9PSIsInZhbHVlIjoiWHpuZlNxd2VJRndoSmZRWkhSVFhZMmU3Q3VMVTE3Q3FpNjBNbHcrMGZLQUR4MzA4WTV4RGZaZlBlUlN1bURQeHBTZjQ3cDFtMzd4NjdrdzVtRU0xUGM2bzh2aGlzNmQ4NEdxdFU4MjlUZ0lSdXpxMVZWYUd5am5tY3lLK2Z3eURXajd3WlpsZnNaZWRIYjA4NWMyT1hONVljRkg1ekx3blZDY1B3Vi9Ob2szeWkyZGxPSnhKQklwc0hwU1Y2UmtQT1lSZUhoOERDS1dKOTRzV3EwaXFZTEJLeWowRU1BeS85Z3VpZFk3SHQvc3hqaUEvK0hpb1FYa3hQYmRDNFZoVEdZb3QwVWQxM0ZWTzV1MWJVUlg4NHlHZk9yQWFiV0RqT1ZwZFJlbHgycE9PUnRiVVZ4cDRYTlpROVNOeXhNSmhvTkhJb0lTazVlTTZmODFUMldNbE1sd3dwRi82QmxGV1NILzd1elg5QUQ2Y0tSdWNILzVDeWk4NUIxNE1yaTJXTmR6cXlRcFRHME04eFpUY2g2SHpVeFhkeE80RU9lUzB2VXljSXdwd1dhR3p2VHc0QmtTVXJjbi9sU2RUK2lRYUNMd004WWRYUXNGVGhDSko3eGxheFd4YmhnYWFuNVowN3J6MC9ScGVjdUpVUTYwbDhoUVJZbTBRSE9waUV4UUUiLCJtYWMiOiJiM2ZjY2Q2NGIxY2M2MGFkMDY1YzJlYjM2NjQyNmE1OTk3NzdlODYzNWNmYmRkNDlmMWRhYWRmNDU1Mzc4NWM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-213302913\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-77713259 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77713259\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1307463818 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:03:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxJbWxZL1A4U1crQ1BNTzIxbWNFZWc9PSIsInZhbHVlIjoiMmh2Mk8yaUJJOEhZUUE0ekJnZHBBbzJYaFlxdkJ4UDZLOThRN3BKc0tNRTZ4RnR4TDZ3WnVmRFVuSitiVWRrSVZycVBuTVBpOTYwQ09zOXQxdjBrSUV3Q0pKb0QzZmxIWFlqQ0hvNGxya2FwWWZabXB1cDlJaUs4WERzTDFzOForUFBWY3djV2xISThjeStab3dDbWp5V0ZXWkpVVm9aMU1OdHVoK2ZFWEJudVp4VmcyNHdWMlpFOFZkTjV5WG9SNmdwWWpjKzFLaE83eGg5Tm1HdTZISnVEOE1ENmJ1aE9scFM4Y2hieFBNckEvNzc0NzdzbW04MEFYMHhyMW1QRTNjSDB1eUM5bVRaVGlRRjNQcWlXU2hsVW1EbzR0VFpvdTNSbXJIZ1gxWVd1N25MV2J1ZS9Hd0ZYSEYwOE5VWWVHQ0d1RFZDUUpLaVF2TTBxMUhiekVpWWJDWkptTFVHTkhsODNLU29tZ0VERzZacmZVTHZRZXo2TmZXdDNMaHpOKytBcm1CMS93MjBCOExzMXQrdldNaEJIWERqeVlZWCtYdkhBYnBLMUV0aldiSjRRU0xqRFRpaTRRVk9jUWErdHEzL0hNWExoRDkyUkw2cnNUaHdQUy9UdnkwNVVMaWRkRm4wWnBuZlVaSDQzN2p1TXl4bkJiYm03cTFPOUtDYXEiLCJtYWMiOiI3MTliM2YyODQxZGQ0ZGMxZDQzYTA4ODc5OTdhNTg4N2E5YmJkNDQwOTZhYjhiNTQ0MzBjNDFlODc5N2M2OThmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlAyZXNLVkV1ZFIyOTU1Mzd5eXFEMGc9PSIsInZhbHVlIjoiYmNnRTJ5Ri8vazIrLzE5a0ltZzM2YnlncTRyUUxOaS9GOFVhZHI5blJnclgyQ05hRFo1YVd6amFDR0ZLYnZOMGxjajVERkQzNGtRTkF2TEVBd09UOW1lMldMMDY4T3ZCNk0rd1pYY2hCYjFIaHdNUkphVlNadXJTaWFSVmtHVjFka1VycU1IRGFGc0l4UDl0aEpaR1JETU9BMDh1dEpZYkNvdmo1UExGczBaYzVMYkc3ckpyUEluMmpEVk05ekNqczNyVk5pWXVrUUhYYW1IcFNQaVNMam1KcjZBVjcrUzJZT3pTMXE4YS9qbjVGWUd3ZGJaWVFYZlFadlZ6cGtmajg5R3N2dmdOOTFvOTdUTTB1Z1BBclk0RENsQTRPeXNRNm9oeUxDblpKdmU3Y1dmSkZlNWh5ZGRWYkFVTm5KRFU2N3lGbGtxMlVPanZSU1prQ01XZHRGUjJSNm5QQ2J0cHY3czJLQUNXUTAydEpqekQyNnJGeGlYY0ZBbDJDSTNaUGszeHBSdkVlcXlzN05ZRlozNzVkbWZqMEh0bHpBS3RyY0JlQ1VLN3FrQ2xWL3hRZ1plYlcxYU5HQi8xV0xEdEVZYmNUOHU2bTVZMEhhQkhXVXkzSE1xV29aNVZxSjR1S0FqNnIwUkhHbWxTMHFvOGQ1aktSdGNrVXN6Mk9PT0wiLCJtYWMiOiIyYWJhZDA0NmZlYjMwMTYzN2NlMjI3NTY0NjE5ODJjZmUzOTE0MzExYzI1NTFmYTE5NTUzMTQ4Y2FkNmNiZDc2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxJbWxZL1A4U1crQ1BNTzIxbWNFZWc9PSIsInZhbHVlIjoiMmh2Mk8yaUJJOEhZUUE0ekJnZHBBbzJYaFlxdkJ4UDZLOThRN3BKc0tNRTZ4RnR4TDZ3WnVmRFVuSitiVWRrSVZycVBuTVBpOTYwQ09zOXQxdjBrSUV3Q0pKb0QzZmxIWFlqQ0hvNGxya2FwWWZabXB1cDlJaUs4WERzTDFzOForUFBWY3djV2xISThjeStab3dDbWp5V0ZXWkpVVm9aMU1OdHVoK2ZFWEJudVp4VmcyNHdWMlpFOFZkTjV5WG9SNmdwWWpjKzFLaE83eGg5Tm1HdTZISnVEOE1ENmJ1aE9scFM4Y2hieFBNckEvNzc0NzdzbW04MEFYMHhyMW1QRTNjSDB1eUM5bVRaVGlRRjNQcWlXU2hsVW1EbzR0VFpvdTNSbXJIZ1gxWVd1N25MV2J1ZS9Hd0ZYSEYwOE5VWWVHQ0d1RFZDUUpLaVF2TTBxMUhiekVpWWJDWkptTFVHTkhsODNLU29tZ0VERzZacmZVTHZRZXo2TmZXdDNMaHpOKytBcm1CMS93MjBCOExzMXQrdldNaEJIWERqeVlZWCtYdkhBYnBLMUV0aldiSjRRU0xqRFRpaTRRVk9jUWErdHEzL0hNWExoRDkyUkw2cnNUaHdQUy9UdnkwNVVMaWRkRm4wWnBuZlVaSDQzN2p1TXl4bkJiYm03cTFPOUtDYXEiLCJtYWMiOiI3MTliM2YyODQxZGQ0ZGMxZDQzYTA4ODc5OTdhNTg4N2E5YmJkNDQwOTZhYjhiNTQ0MzBjNDFlODc5N2M2OThmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlAyZXNLVkV1ZFIyOTU1Mzd5eXFEMGc9PSIsInZhbHVlIjoiYmNnRTJ5Ri8vazIrLzE5a0ltZzM2YnlncTRyUUxOaS9GOFVhZHI5blJnclgyQ05hRFo1YVd6amFDR0ZLYnZOMGxjajVERkQzNGtRTkF2TEVBd09UOW1lMldMMDY4T3ZCNk0rd1pYY2hCYjFIaHdNUkphVlNadXJTaWFSVmtHVjFka1VycU1IRGFGc0l4UDl0aEpaR1JETU9BMDh1dEpZYkNvdmo1UExGczBaYzVMYkc3ckpyUEluMmpEVk05ekNqczNyVk5pWXVrUUhYYW1IcFNQaVNMam1KcjZBVjcrUzJZT3pTMXE4YS9qbjVGWUd3ZGJaWVFYZlFadlZ6cGtmajg5R3N2dmdOOTFvOTdUTTB1Z1BBclk0RENsQTRPeXNRNm9oeUxDblpKdmU3Y1dmSkZlNWh5ZGRWYkFVTm5KRFU2N3lGbGtxMlVPanZSU1prQ01XZHRGUjJSNm5QQ2J0cHY3czJLQUNXUTAydEpqekQyNnJGeGlYY0ZBbDJDSTNaUGszeHBSdkVlcXlzN05ZRlozNzVkbWZqMEh0bHpBS3RyY0JlQ1VLN3FrQ2xWL3hRZ1plYlcxYU5HQi8xV0xEdEVZYmNUOHU2bTVZMEhhQkhXVXkzSE1xV29aNVZxSjR1S0FqNnIwUkhHbWxTMHFvOGQ1aktSdGNrVXN6Mk9PT0wiLCJtYWMiOiIyYWJhZDA0NmZlYjMwMTYzN2NlMjI3NTY0NjE5ODJjZmUzOTE0MzExYzI1NTFmYTE5NTUzMTQ4Y2FkNmNiZDc2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307463818\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1071515753 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1071515753\", {\"maxDepth\":0})</script>\n"}}