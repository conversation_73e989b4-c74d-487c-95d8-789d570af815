{"__meta": {"id": "Xd69b7010091b9f89028700f22be5d0ab", "datetime": "2025-06-28 15:49:17", "utime": **********.938385, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.480692, "end": **********.938398, "duration": 0.4577059745788574, "duration_str": "458ms", "measures": [{"label": "Booting", "start": **********.480692, "relative_start": 0, "end": **********.865957, "relative_end": **********.865957, "duration": 0.3852651119232178, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.865966, "relative_start": 0.38527417182922363, "end": **********.9384, "relative_end": 2.1457672119140625e-06, "duration": 0.0724339485168457, "duration_str": "72.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45819624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01494, "accumulated_duration_str": "14.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.905285, "duration": 0.01194, "duration_str": "11.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.92}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.926346, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.92, "width_percent": 3.614}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%Manual products are not allowed in Enhanced POS%' or `sku` LIKE '%Manual products are not allowed in Enhanced POS%') limit 10", "type": "query", "params": [], "bindings": ["15", "%Manual products are not allowed in Enhanced POS%", "%Manual products are not allowed in Enhanced POS%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.92906, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 83.534, "width_percent": 16.466}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-246126367 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-246126367\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-402426640 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-402426640\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-90760283 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Manual products are not allowed in Enhanced POS</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-90760283\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1269788091 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">68</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125486476%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRLbXVHU2tndFZnRzJTZmk1cWVvelE9PSIsInZhbHVlIjoiZmlkUiszQlRJVnFvZXpaN3pISjYrZ3F0Sm5GdnhhVnRZakx6aE5LZUsrUFZHVHluaDdaU1dlbFBsWjhPc1JHOXF5akxPRjEzWHdQZU1jczBIcTVkZmFJeFJLeHJJbEUvSUNraGFkMWc1c0hEWU9COXdldUoyTTMvTjduRUwxTWlsY0hYaVJiZ05kVGNtL0xFek42OFRXVlNlTjNtaGJPNk9pdzVXVGdEOGg0YmZKYlllRm1FZkhqeVoweUwzTzJnQm5Yb2MxSlMyWi9LdWx0R3RiaFNsQVlwaVE0MVcveGd2UHk3QUp3UWdWN0QrVGRwRGJaOThuS0toZE5TYWRucEplKzBSS1NqZnNHTXVEcjh5dEVYTmhxRWZVQ01NNmx1ckUzUFdSMnJ0SExORzNRQm50di91bS80aGl4K3psRmFqWUU4UTZ5Zy81cldtMlE0VzAzcVdiWStObzJNbFdOWUp2QmFWTU9QazhvdjZGbW5iN0dBM0Jka3pSNFJVRjRSd0l3SWJsb2R1WWw0UUgxcmxTQU9pVUgyWWNDdjVXVWlVaHkyL3ByVGsvTDB4ck5FSDRHemxUSFljRzR2NUp4ZUZYUGZleHhiWjI0YzNXbEMwc3dyQ1dOTWJrT2hWZ0VjS0VwMVFhcjJmWTlyeS91WnFlYVBidU1ocjMxUWNHR2oiLCJtYWMiOiIzMDdjYTg0MjU0N2NhMGE1ZmY1Mzk3NWY3YzVmZDJkN2E3NzlhNDdlZGM5YjhkN2I2ZjFlMGY1NjU4YzI1MTNlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZUTGdTckV4Znl3Z3M4WU1ab1Jmb2c9PSIsInZhbHVlIjoiTnlINURDbytSSktHemIrbkpzdnRORXU5bnV5THcveEl2TUdLMC9uREU5WWpTbUZzRFBqWmw0d1I2aTFSa0NJSUJpUDNqMzdiZ243dVpVczl2eVhVZnlGaGFhYndzd2taR1g2d3NrMVp0OFVDWDFvR1o2Uk5OQk1yK0FjL3RQdG9xb2JhRWxNYnlIeEpXcUpSeEV3VVMwWVpqUkd3b3FHVEtoNFZzZy9DNjlmUmhJSHlGTjdNYU95dnpGUUhXam9UVUMyYjZsV2xqVlFXdmlPaE1jUEZFREV6ZmlBak5zbmovY2t4V0phYWlPcEZvRndiV1F5cHBjSzVBNnZteDB3Wno5WlVSZEtkZTZzeEJSWmk3QkR6aEs2UDBJblRGSTlzejhyell4aUxUOU9BVDU5K3Z5bkRLRURYUmpLcVZqUlVYbGNpWm9SdUJpNjN2Y3RBcXorK09mdEJrUEZpa0dzUXhlMEQvcitQVGNHelJSOGxTUzlNZldwTTlKcHF2SFBKY011RFE4Vm1HWFlPN0ZnZUhFMklsYVRZdlFEcjZVM1p3dWRMWjBRbTFaRktkQlI2QzloVlk2NWcrVVBqM3FhdjBsWURGRmg1ODdHWXIzdkRWM3NjTHpuWmdmbkN1RHNialVDR012QktYTitUcDJWcDB0cjA2WDBadTY4b2ovV1kiLCJtYWMiOiI1MTU1MzAyMTdiZjI0MTBlNWQ2MzEzM2U4OTg5ZGEwOWM2YTdlYTU4NWFmOWVlNWZlY2Y4OWMwNTZkZDRmNTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1269788091\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1274539812 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274539812\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1999657545 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFOV0pKZFQwTjBlR3RSNHIvb2ppdnc9PSIsInZhbHVlIjoiZGo3cmRHVUNQY0s0N204anZhNmV2UElxa2V0cnlRU2JaVno5T0pnRGRJcFpGd0JaMklHV3VrREVERlJzQXg2YjY2alBnTDNrR0F1MVRwekorREd2MmVsd2lSY3dJRm1GeEl4aEh6VTBpSFl2VEJsTllFVHRvQkJDYzdyamJNb3VEcmpTdzE1ZC9xUk5Ed1dkMW9GbG56bFFQRWExYUtmT1NMUzk5N2x5dkNxdUxpR29QY3gwQ3hpRERVMXViUWpsL3BBd3FGVmcxMnUvVmFTSDJGWTdUbXNQUEJkd053OGFSUVNibXZNZERkWXZ1aHFxVU9hcFVaL1JLUFRwd1NENk5GcE5LNVY1a2Q0czNSZkRYdCsxUlhGYjRxdnRlZVFsbjBOVFZEdmNraTAzcHNkV2gzV0U2VWgvclh2RUxUTVY5VWNXTXl5UXdlOFM3a1JlbXg5SGM3SzIxczU0QUhLWlB0R3BCYnVIV2VpdUZIUHlHTGdEbDUwSCt2STloWUhORXVRcXFDekppamw0UFZQKzZmUTZuSjJRZ1d2WERzYm94Uit5SXhKMXUybUs2bXByV3ZybzJ2Z3V2ZXFuUE1MaS9UcERyeVFBaUhyVDRxTWJmbk5wbWl5RFhVZmViSTBGMk5yZ0pmSklFZkhHVTBUTG9rR0pCSTNoL2NWSGtDS3MiLCJtYWMiOiIzMTM0ZmI2OGE4OGY2M2I1ZDFmZTMxMGJmMGZkOGEwNzkwZjYxMDcwMGY2N2JhOTQwYTQ3ZTZlOTE1YmU0OTg5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRoTFJ2aDBRM3YzTk5BY29PSzU0Ymc9PSIsInZhbHVlIjoiOWNsUHJyeTBrUC93Z1llaG1kdlJIQldCcmtLMlZRRlE2cmFONjUrT00ydHJrLzdyOGVhVnRDcXBtQkJRWnhxSUtHYmwyeFRlalFpa2lTdTlremdIai9PY3F0THI5ekZMcCsvNUtZN29wdW9wak1KbTR4MWdaeHM2N3NBWmRzTDY3Uk9XNVRmdHpSWEk3eVdBTEZTN3pGTVkxWXhRenJZSVdTR21qMkhiTnQ1eWk5MmJzNDRxZVJSYTNka1FmOWZjWmg2cUZBZEZQd3NoaTYrR1ZSM3N5cXUxTFpNcHZFMmhvRzJNYXFHcVZzTngwaUdrTGhXWHhscEtoOGhkK3dTMDNtM0JYKzlaWTZlOGxBV1VLZlgxTmEreDA5dURxZ2JNK3ZMVU5HSENRNG8wdTdXaDZ3Z3VmaHJ5MmtqVGFnMFdaeXN4blNVVzdYVTgzLzF5UzRlTjB1ZHFBcUVRTG1FZ3d1ODlieVFBUyt6bFhXQ0xBR3dVcU00TXIwaUlqbHJxVzB0ZklQOUY4M3NWd1cvLzI5ODlHRm5ET0tScHNmRHIyVTV6ektVdktoVFpqVEhJVmlDcGZaOUJHSVBPQWVKZmRaQ3Zrb2RnTktYNnJaQUdJNW8zRC95d0NhYzFJT1p2Q1c1R09XdUJ4TnltY2lDRnRoempqam1zZEFTeXFWdnYiLCJtYWMiOiIxODUxNzllZmNmNjExN2IxMjM0OTZlZWVkODc0ZDA3Zjk0ZjA1ZDUxNjNlOWJjOGQ5NjRiNjU0MGYxNzgyNDUzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFOV0pKZFQwTjBlR3RSNHIvb2ppdnc9PSIsInZhbHVlIjoiZGo3cmRHVUNQY0s0N204anZhNmV2UElxa2V0cnlRU2JaVno5T0pnRGRJcFpGd0JaMklHV3VrREVERlJzQXg2YjY2alBnTDNrR0F1MVRwekorREd2MmVsd2lSY3dJRm1GeEl4aEh6VTBpSFl2VEJsTllFVHRvQkJDYzdyamJNb3VEcmpTdzE1ZC9xUk5Ed1dkMW9GbG56bFFQRWExYUtmT1NMUzk5N2x5dkNxdUxpR29QY3gwQ3hpRERVMXViUWpsL3BBd3FGVmcxMnUvVmFTSDJGWTdUbXNQUEJkd053OGFSUVNibXZNZERkWXZ1aHFxVU9hcFVaL1JLUFRwd1NENk5GcE5LNVY1a2Q0czNSZkRYdCsxUlhGYjRxdnRlZVFsbjBOVFZEdmNraTAzcHNkV2gzV0U2VWgvclh2RUxUTVY5VWNXTXl5UXdlOFM3a1JlbXg5SGM3SzIxczU0QUhLWlB0R3BCYnVIV2VpdUZIUHlHTGdEbDUwSCt2STloWUhORXVRcXFDekppamw0UFZQKzZmUTZuSjJRZ1d2WERzYm94Uit5SXhKMXUybUs2bXByV3ZybzJ2Z3V2ZXFuUE1MaS9UcERyeVFBaUhyVDRxTWJmbk5wbWl5RFhVZmViSTBGMk5yZ0pmSklFZkhHVTBUTG9rR0pCSTNoL2NWSGtDS3MiLCJtYWMiOiIzMTM0ZmI2OGE4OGY2M2I1ZDFmZTMxMGJmMGZkOGEwNzkwZjYxMDcwMGY2N2JhOTQwYTQ3ZTZlOTE1YmU0OTg5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRoTFJ2aDBRM3YzTk5BY29PSzU0Ymc9PSIsInZhbHVlIjoiOWNsUHJyeTBrUC93Z1llaG1kdlJIQldCcmtLMlZRRlE2cmFONjUrT00ydHJrLzdyOGVhVnRDcXBtQkJRWnhxSUtHYmwyeFRlalFpa2lTdTlremdIai9PY3F0THI5ekZMcCsvNUtZN29wdW9wak1KbTR4MWdaeHM2N3NBWmRzTDY3Uk9XNVRmdHpSWEk3eVdBTEZTN3pGTVkxWXhRenJZSVdTR21qMkhiTnQ1eWk5MmJzNDRxZVJSYTNka1FmOWZjWmg2cUZBZEZQd3NoaTYrR1ZSM3N5cXUxTFpNcHZFMmhvRzJNYXFHcVZzTngwaUdrTGhXWHhscEtoOGhkK3dTMDNtM0JYKzlaWTZlOGxBV1VLZlgxTmEreDA5dURxZ2JNK3ZMVU5HSENRNG8wdTdXaDZ3Z3VmaHJ5MmtqVGFnMFdaeXN4blNVVzdYVTgzLzF5UzRlTjB1ZHFBcUVRTG1FZ3d1ODlieVFBUyt6bFhXQ0xBR3dVcU00TXIwaUlqbHJxVzB0ZklQOUY4M3NWd1cvLzI5ODlHRm5ET0tScHNmRHIyVTV6ektVdktoVFpqVEhJVmlDcGZaOUJHSVBPQWVKZmRaQ3Zrb2RnTktYNnJaQUdJNW8zRC95d0NhYzFJT1p2Q1c1R09XdUJ4TnltY2lDRnRoempqam1zZEFTeXFWdnYiLCJtYWMiOiIxODUxNzllZmNmNjExN2IxMjM0OTZlZWVkODc0ZDA3Zjk0ZjA1ZDUxNjNlOWJjOGQ5NjRiNjU0MGYxNzgyNDUzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999657545\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-68091669 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-68091669\", {\"maxDepth\":0})</script>\n"}}