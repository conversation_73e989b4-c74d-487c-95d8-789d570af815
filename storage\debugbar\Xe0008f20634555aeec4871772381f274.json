{"__meta": {"id": "Xe0008f20634555aeec4871772381f274", "datetime": "2025-06-28 16:03:42", "utime": **********.7872, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.357983, "end": **********.787215, "duration": 0.4292318820953369, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.357983, "relative_start": 0, "end": **********.724423, "relative_end": **********.724423, "duration": 0.3664398193359375, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.724433, "relative_start": 0.36644983291625977, "end": **********.787217, "relative_end": 1.9073486328125e-06, "duration": 0.06278395652770996, "duration_str": "62.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45531568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013989999999999999, "accumulated_duration_str": "13.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.755887, "duration": 0.01304, "duration_str": "13.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.209}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.777153, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.209, "width_percent": 3.717}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.77961, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 96.926, "width_percent": 3.074}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1031829680 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1031829680\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1169913443 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1169913443\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2076782048 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2076782048\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1604621141 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126618953%7C20%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitoQXIrZUF5OGJKSisrQmRYY2hVVkE9PSIsInZhbHVlIjoiT1hIdlNrVERpenZDOWxxdDVZZmdjUngxNmhpMG5DZFdrVzB6dDZPRG1scDdENS9rQmc0OVZOTGxYOEZZRm9YaTg3R1g1N2tIODFSc2E3UlNtd2Z2ZDNjTEhJZXNHd3hIMHNMMktGa3hKK0N2NVczRU9UOHdtbUtsY211NGw2bDN1NlNlWlJXeHBsR2FQUWZTRU9aRmlOamU4VHpKQk1QS0hZZ2g1aHNLVDBpdWtZbThRWkdxcnhaNklPZ3QvQThoV2RwNFVtb2dUWU85d1hQMDEvSGxPSnFBV2d3QnhQcTJlMkJwRlFNUWpXK3pPdFFNZ2NNYjBrbldPVjhrSUxxdnhpWEUrQWYySUNPUDJnMkZ3YzVtdkhGTWU5eWd5YjYxNXNoYW5kZnJUaldQMGpJbStuMmFiR3h5Wmt6dTNTY3BtVHF5RFRUMFVwb0lQaHBLMk9FRWFUYmhkWCtRU1UxTVlkeHE0Tmk1VXdpVXBnNUZIdEFZeEFRQVowNXFhUXFiL3RNNWJLRmVKVDBxWkFERDBkeEU5RmFrVG52NWJIcSs4N3dGVEhTek1aVVRoY0k4cDBoY3Iyd3hRa2p6YmYzdWFmRVBSVEl6aW9kNGwzOE04NndtWjdTdDdObzZ5MXlNb21RMk1BcEpRcVBQK3RDYUZIUmtQWjdyNlpyWExGUmIiLCJtYWMiOiI5MzAwZjI4MDc5NTYwZTg5MDU1Y2Q2OTMwZGE4ZTQwNzRiM2FmNzgxMGVjY2IzMDBjOTdhZTdhYTY3YWEyNGU4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVXS29pUnd2bkVxcDRKRWgwanlwVkE9PSIsInZhbHVlIjoiZERPNkFWWmNlMEQwQ2tldEY2WmhJc0xPVzNCQW0wTWE1ZDU5eHpaMUpkU0owS3Z0UW84Wi80dysrdWpMQlhWZ3hiaWF3SDlDUHZVNnRVQkg3OWVWdjNjWTZuZ3dFSVZYTTQvL0ExWVd6ekt1TkhLZ3VXL3pXN1ZQSkUxNk9XaFNsZTVQQURWWlh6UTRqd0J1MGttUG4wMjJTWmN1b3FsazRkMEZEeXZJeDZ6N3VTWUMyUW1tUXRDbWk2WjRwdjlRaEpuZXByaUJ1dCtrbjBBbW93QUIyZzkwNURDQXpUM0Roa3NjNkJnZEFZS2o2QTJ4d0NEUFRQcDZuRXN6cHN1WkhwUkZ5czFUY3VyMGhxRlFxODhZREhqZVYrVENNRW5iRWJPSWVTK1pHR1JHZG1EUXBycEhlQXhGeWNwMG5OQzRxaGlWUjNOdGI2WUE0REZOakt5UkEyRFgyMFQweFdSc1FiKzBWb3RBUm1pSnIxUUdIMDFjTGNnNTBMLzdua3JTSm5sajIza0Rla0JpL3grdk9LN0ZjN1ZFUDhrcHhobHdMcDhscGs0VStubW1MYUlycDlqMS9JUzVZLytTV0xkamlHZDZHdzViNzZsS2JHbmpjUFc2eFU1U2tmR2R3K0k5SVNreGsrSGkyT1JLVlBoY1pZUnl0QStoQ2pqL2VIZWEiLCJtYWMiOiJlYjI4YjFkNTRhMDQ4YzMwMDkwNDVhMzFmOGVjNWFiZWVmOTdiMzA5OGRhYzAxZDQ5NWRjZThhZDAzYzU2NzkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1604621141\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1894038278 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894038278\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-174176673 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InB5SWpUdFpuTEdRMDBOR3lmYk5ISHc9PSIsInZhbHVlIjoiQVcwNUM5dmpFK3Y3ZTQyUUdiZFpjYTFaQjZMN2dUL05QS2hnZ0paV2xDN0tKby9XWjRRM0M2VVhSN1VhK0E4a1ZUQWtnWVFybXdITWk1Sk1MVjRWTFVhN1FrUTk4UUR6UmxhMUdPR1g0VUkvYUxDTUZUT2M2NDZxMWpvRXZjVllhVm9PZ0t6RW12K3lvUTNSSVpZVGdNbGtmWStpWHRiUWdIbHV5RzdmVUlIU3dFcGUzTTFGNUI2Sk1PVDJoa0JGSFA2V0RvcEhiZTRBNmVrcXM5RkFYeEZCdXZFRld5SnNuNXVnbVdmM0ROSHB5dEZoMVdYcUxVUk4vMFJQWUYxRFgyUWxJMkdKWGd4dms0ODFlN2JNckN0VHZ2dUgyMXpyYXk5aFh1em9Oc1p5Z2crZ3NuelcrQ3FtQmhzMmZxTHNMZ1JId0g2UVFmZG90ZjkwRnhuSlZHc1VoN0ovSVB5UmlvQ1RheC9BZ1Zld2tadUVIUG50aVFRa3lIZWRLbmtJc3d2YnJXWVBIbmczWFkreGFvbldNN005ODljbEd5VXAyNUdTRDN5dHkwZ2xkTEVXckIxWlBiTHUxUTAyVk4raDZaYTBqMkJvOWdtT2R5NmFGZnpGd3MzNEdxWnpUd1BUVFl3cEdIaklPdlJBOEJkMExqeWdwMDZSTDUyUFJkOGoiLCJtYWMiOiJiNTM2NGU4M2U3ZDQ2NGRlYzI0NTEwYjFlNDBkMzA2MGFiYmM0NWI2MTcwODZlMTU0NmY0MWUxOGI1NzIwMDJlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdGTFVRcmlGNUpuTTNhcFNPRGU4Z0E9PSIsInZhbHVlIjoiU0xiZUJXeDJHaXNTRzA5eE12aVhMR0tZTkQ5bVlSQ0Q0bUpES0R0UGU4Zm8vWXU5N3Q3bnNpYkRYM0NUT0FJYllwOS9adTJlOE9BUGJNTmE2dW5PN3hBS1R1dXUzTFBLOWhONXdmUVV5Z3NkT21KdVBZM3BSMktkei9ka2ZJeUF1YTg5UGlPdld3SE5zUnV3dFZwZ04wVkpPT3NrSzdIU2RyeG9ZQ1c3czlWenZ4b01oNVpmM2hsYVlTaEV0QzdEMStZVWNmR2JsQTBHZElDRnczcHhpR1RiSmhXZDM2SXJWcHdtNU0rbm1UU1JMT2c3RVFJdzh1ci84eXpCYjJJUnEweGRRYlkxa1BMT3BnRzRVY01mSGlGbVRjN3BzTVE0ampyQVRvOUU1a2FQR0RQb0s4T0tRV2E5b3hJUjQ1V0trdHBObzN4ZjFTNmlzOURkMHJnRTQ4dGRnUzdoWENqQ1hPRlJqNWxDMGlDZzZqWWxoZXAzUVJtd2VLeGp3OGw4WHgrTkFpSWxleGI1Z1lPWlFaeVRHWDlNbEtBVFIxU0ltWlRkUnkyOEYwZzRnNHcvVGE5NnliWFBWck1DdTNsT0hxWjlQWW4vWHlFUXhmQ3pOYzlreXlwUzkzYWlMODZ2WmZtVkR4MUlWSHlMUXpxakFiZDBCZUlKOERPcDlPNU0iLCJtYWMiOiJmMzFjNGIwNzRjNjlhZjM4NWE3YjJiZDBhNzkyM2IyNTU5OGI3NTdmMzc0Y2VjYTY3YjRiODlmNjlhMDI2NWNjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InB5SWpUdFpuTEdRMDBOR3lmYk5ISHc9PSIsInZhbHVlIjoiQVcwNUM5dmpFK3Y3ZTQyUUdiZFpjYTFaQjZMN2dUL05QS2hnZ0paV2xDN0tKby9XWjRRM0M2VVhSN1VhK0E4a1ZUQWtnWVFybXdITWk1Sk1MVjRWTFVhN1FrUTk4UUR6UmxhMUdPR1g0VUkvYUxDTUZUT2M2NDZxMWpvRXZjVllhVm9PZ0t6RW12K3lvUTNSSVpZVGdNbGtmWStpWHRiUWdIbHV5RzdmVUlIU3dFcGUzTTFGNUI2Sk1PVDJoa0JGSFA2V0RvcEhiZTRBNmVrcXM5RkFYeEZCdXZFRld5SnNuNXVnbVdmM0ROSHB5dEZoMVdYcUxVUk4vMFJQWUYxRFgyUWxJMkdKWGd4dms0ODFlN2JNckN0VHZ2dUgyMXpyYXk5aFh1em9Oc1p5Z2crZ3NuelcrQ3FtQmhzMmZxTHNMZ1JId0g2UVFmZG90ZjkwRnhuSlZHc1VoN0ovSVB5UmlvQ1RheC9BZ1Zld2tadUVIUG50aVFRa3lIZWRLbmtJc3d2YnJXWVBIbmczWFkreGFvbldNN005ODljbEd5VXAyNUdTRDN5dHkwZ2xkTEVXckIxWlBiTHUxUTAyVk4raDZaYTBqMkJvOWdtT2R5NmFGZnpGd3MzNEdxWnpUd1BUVFl3cEdIaklPdlJBOEJkMExqeWdwMDZSTDUyUFJkOGoiLCJtYWMiOiJiNTM2NGU4M2U3ZDQ2NGRlYzI0NTEwYjFlNDBkMzA2MGFiYmM0NWI2MTcwODZlMTU0NmY0MWUxOGI1NzIwMDJlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdGTFVRcmlGNUpuTTNhcFNPRGU4Z0E9PSIsInZhbHVlIjoiU0xiZUJXeDJHaXNTRzA5eE12aVhMR0tZTkQ5bVlSQ0Q0bUpES0R0UGU4Zm8vWXU5N3Q3bnNpYkRYM0NUT0FJYllwOS9adTJlOE9BUGJNTmE2dW5PN3hBS1R1dXUzTFBLOWhONXdmUVV5Z3NkT21KdVBZM3BSMktkei9ka2ZJeUF1YTg5UGlPdld3SE5zUnV3dFZwZ04wVkpPT3NrSzdIU2RyeG9ZQ1c3czlWenZ4b01oNVpmM2hsYVlTaEV0QzdEMStZVWNmR2JsQTBHZElDRnczcHhpR1RiSmhXZDM2SXJWcHdtNU0rbm1UU1JMT2c3RVFJdzh1ci84eXpCYjJJUnEweGRRYlkxa1BMT3BnRzRVY01mSGlGbVRjN3BzTVE0ampyQVRvOUU1a2FQR0RQb0s4T0tRV2E5b3hJUjQ1V0trdHBObzN4ZjFTNmlzOURkMHJnRTQ4dGRnUzdoWENqQ1hPRlJqNWxDMGlDZzZqWWxoZXAzUVJtd2VLeGp3OGw4WHgrTkFpSWxleGI1Z1lPWlFaeVRHWDlNbEtBVFIxU0ltWlRkUnkyOEYwZzRnNHcvVGE5NnliWFBWck1DdTNsT0hxWjlQWW4vWHlFUXhmQ3pOYzlreXlwUzkzYWlMODZ2WmZtVkR4MUlWSHlMUXpxakFiZDBCZUlKOERPcDlPNU0iLCJtYWMiOiJmMzFjNGIwNzRjNjlhZjM4NWE3YjJiZDBhNzkyM2IyNTU5OGI3NTdmMzc0Y2VjYTY3YjRiODlmNjlhMDI2NWNjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174176673\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1772278649 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772278649\", {\"maxDepth\":0})</script>\n"}}