{"__meta": {"id": "X1bb199e3a3f276e728024e3be4e6d75d", "datetime": "2025-06-28 16:01:28", "utime": **********.637549, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.129698, "end": **********.637577, "duration": 0.5078790187835693, "duration_str": "508ms", "measures": [{"label": "Booting", "start": **********.129698, "relative_start": 0, "end": **********.571575, "relative_end": **********.571575, "duration": 0.4418768882751465, "duration_str": "442ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.571585, "relative_start": 0.44188690185546875, "end": **********.637581, "relative_end": 4.0531158447265625e-06, "duration": 0.06599617004394531, "duration_str": "66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45846552, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.005170000000000001, "accumulated_duration_str": "5.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.613005, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 40.619}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.624988, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 40.619, "width_percent": 9.478}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div clas%' or `sku` LIKE '%<div clas%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div clas%", "%&lt;div clas%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6279092, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 50.097, "width_percent": 49.903}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1017228672 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1017228672\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1277439020 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1277439020\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-358008912 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&lt;div clas</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-358008912\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1997495728 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">32</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImM2VGwrQzRDUE8wVytmWTdvRmNabVE9PSIsInZhbHVlIjoieEtiS201RU5sUTkveTNnck9hUkFsbHBCaXo2RThZdU9veWhUdTRKRjViRlVhVUcyZFpiRlo4a2NCczZqTllqWkJUOVdUemJwTTgraHRYZjVCNm95dzc1M3ZDWUlhdnIvazFqWHhXMHJLY3NYTncxTHdOc1dRV0lheDdBbEE0SUdiNFBEV1V4bXFqTFQyb2RJMDR2VVYrNTZ6QXdXK0R4ZDdNVklOQldURW93UW90OU5OdzVHNDh0YzJxY09WdzZlR0lMZnphODhVWDBhcW81d2hwTzJjOWJhT3R1NlZSR092U2xYTHZaSmhJUndQYU56VGNZSVhsOFRPdHVSbVd5UVFleHhCV0Qxa2N6WmR4eEhLS3JjUUtjZURDTzFFcWxCN21zY0ttdmZ4Z285TkpxcVZaWDBaa21Hd01COVZUeFVTaExtcXdQaWdFRU10Y0ZuN0Vwd1JJTjdiUjdxYnZCKy9MZnhBQm9vTWVZZnhKb2NIVmk2Z0duNkJ4UkdoLzdZM1MrVWxCK3I2OXh1Qm0rU0pITndOeUFYc2xEVmVMWUErWnFnM1RybXAvSkFYTFlHSmJpL3NmdFp0Q1Q1eEdqVi9aUDJPN2tGb040Y3hHcUF1SXNMMUJWZXV0MjVmdGpUb1BHSHpNT1dlOE5FOUNZaVFnczQxbnllYm5NZ291TTUiLCJtYWMiOiI0ZjE4NmRkZmI1ZDEyMmVkMmFjNzc3N2FkMWFkYWU2ZDg4MzRiMmYwYzNlMzA4ZDYxMWZjMDMwNTc3YmU5ZGJhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjYwa1c4bjJmU2hIQlRnQVlvVW1UZ0E9PSIsInZhbHVlIjoiL0RtYTlXc090NUNQMWNrQjhqTGpVMyttR3FrS2g3bHlUTC9pUG9ZaDdhRmRlOG1BNW9rRCswOVVKTml4dXcrKzJvYWxOMm1paDl3ejdmYWY4bmVDTjNxUk80aTZyQXVOZnpWOG1nM0tDQ1ZuYzRhKy9heVAvSUhrblFnNzFPNy9oS05JSUgxd0o0cjRHS1JDQUtuT3pUdU12bkdDZWdyRDZvVGQ0a3lKWUNMWHBJak9Na05XaDZWMU5VNC9rZW4wdHNuMVp5a3NwMG5uRkVoSWt3em5BcXJNNEVLazduZFFHbHZteUoydldFNkxwaXVmdXU0MFQycVd0d2IzNmNXbnVidFN0eTlwNGtNQlB0OXZYaU5PL1d6TVlYeURpd1JNNzUrY0xoaUZzL2hrSC9vWHpQNnlxT3NRUUlmZmpxOUYva3FrQ1lLaFBGZ1R4L0M2UnlYOGhHTXV2eTkweVZ4YXltUjg1Z2xlNDNEVXo3Nmhvd3VCUitEcXlIb01YSHRkODVzdlRIT3ZURm1xTGVwamx0U3RaRzQ5dERIclhQN2FlTU5YRzRBRDh0ZGZBZHZaRzhWMDNMWmFDSGRkNlZNUGVCTXdiMS9BcWtCRTdPcGJiZm5wQ1MrellRRU1VVVNvbW9RQmpvbEZUeFdwUWhFS0UyR05QZkY1UnVoaVY4dFQiLCJtYWMiOiI5OTc2MWMzNmZjMGFiNjFjOWU2OTQ2YTQxM2Y1OWJiNjkwZDYzYzgyOTVhNTNmNjFmYTM1ZmZkMjZmYTAxOGM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997495728\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1557218136 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557218136\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-745483827 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IisyVFJQcXUwbkhSWEJJT09tSHBXUmc9PSIsInZhbHVlIjoiMW4vRENtWnNRelV4OWlDWWVCWnRUOHhMVTlGWE0vZ3lia2NnTHBqaENCZTZrZlNMdlM3QXFyWEwwR0VtQ0lqa1hlK0ZRZHJieU9GSXZmYlVxZE4zTDQ0UHVwWGhVTklRUTNVUVJNR3FDVkZQQUZKdG50TEc1WDR6ZjFhSm5KMlp3Y0Q0S1ZnTERlR05qU1NiL2ltNGgvbGlnUVluNjJyRjgwR0hCdG9QL09IZnl4NTJzUWNBSDREdHRHN1plcXpXejZlQ00rR05DZXArOGNxMEFOU1FMdnlzWm1rOWNodmRVNWJ2di9ndHBOVmYxUVRoV1k0Zmo2b0FROEl4ZnBzR0FvN1ROZ3ZxZFNwK3FmWUcxSUdEOVgybWlrR2x2ektLeWNXYmlGVjZ3elRobXFPZUJzdTUyeG1vYTA1aU9PWE5kOUZsK043TkdRdC9vZGtLb3ZOSWRVWngrSUFOcHgvMThyWDBIblBpd0o3eUhQaUYzUVI3UDNKWDhaTWdxZmNYbnpOYm4xSVYwNjBGSVRiYTlvRms5NGZVdGNDUWZHdTNiT1BCVjIrK3lia0g3OXFNbVpBUnhBQlBFRUF4TE8rejRUSy9zMkVzNFU5aUswa3lIbFRrU3U0Vng2K0p2ekJSaFJOY1F6dHMzTldib2YzSXhCNVQrWXZyRHRxakt4eFIiLCJtYWMiOiI3MjM0NDQwY2E0NjI3YWMyZWVkYWFhY2NiZTA1MTU4ZmRlZjYxOGYxNTljM2U3MzY5MzU1MjUyNGVmYzhiMmQyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNLZ1plYkVoRGRUdUh5S2pBUHF0NWc9PSIsInZhbHVlIjoiUTJoY3ErTnluZ251N0EyTy9XZEJQaGgwK2ttUW1wdUZjdnBuSlZnV1NicVVmc0hlTjR5eSs0Ky9SSjJOd05JSzZtb2M5Um1YNlRWUXZvYk1zK0VUZjliYmx3L0VId3RTbzROOVdTcWtEWjRya3hqRzFiNHdqSUJIK25mSmtNRVU5MWE0bFpNV2FYL1NweFFCRy9FSlNEa3BHSGI4emdmeklTeFd3VmVhaFpraEJYUmtycmdaQW01bWJ2VHpkWkJabWhhSkFVcGdtOGVkZjZTVnl6Z1ZXdURYd3FOTndycWlXZ1Z2R3pxQTJLcWJ0MzBjNnVyb0hBM01tNmYxdi9PcTJ4YkZUWkE3MVNZWHR6ZVVkaGhnNkxueUZHaVlJckJiMlRQQjlmZXZEaFM5ekxzbkhZTzB5OGlWZkRPVk1XcTRtTU5MalRRdFdJZ3ZhTmVKc2dkQ25OcVZHUFl1cTVPRVZPaUhaelM5dW94eDdnWFZDWGtYd2dJTjV1V09xVmR0SVUxUTVrVEZvOVU5VlhWSGF3dDd2N2p3S3dSUmhxczJUSjBRRnpySERwNmxZeGRuc2RqeHJyUGpaUmJWcVBVNWxOQkh3V1N3anRRcUdncjlrYVFYc21kWjhzdXFyTXE3N1R4VWdKTGtQUEdkSm5TRitqN2lrNnVUK1hON3hSa3QiLCJtYWMiOiI2NGYyOWUwYmYzNGI1ZDczZmU5YzVmYTA1YjllZjAxZjFiOWJmMDZjYzBiY2FkZDgyM2E1ZWQxNzRkMTQ3YzdkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IisyVFJQcXUwbkhSWEJJT09tSHBXUmc9PSIsInZhbHVlIjoiMW4vRENtWnNRelV4OWlDWWVCWnRUOHhMVTlGWE0vZ3lia2NnTHBqaENCZTZrZlNMdlM3QXFyWEwwR0VtQ0lqa1hlK0ZRZHJieU9GSXZmYlVxZE4zTDQ0UHVwWGhVTklRUTNVUVJNR3FDVkZQQUZKdG50TEc1WDR6ZjFhSm5KMlp3Y0Q0S1ZnTERlR05qU1NiL2ltNGgvbGlnUVluNjJyRjgwR0hCdG9QL09IZnl4NTJzUWNBSDREdHRHN1plcXpXejZlQ00rR05DZXArOGNxMEFOU1FMdnlzWm1rOWNodmRVNWJ2di9ndHBOVmYxUVRoV1k0Zmo2b0FROEl4ZnBzR0FvN1ROZ3ZxZFNwK3FmWUcxSUdEOVgybWlrR2x2ektLeWNXYmlGVjZ3elRobXFPZUJzdTUyeG1vYTA1aU9PWE5kOUZsK043TkdRdC9vZGtLb3ZOSWRVWngrSUFOcHgvMThyWDBIblBpd0o3eUhQaUYzUVI3UDNKWDhaTWdxZmNYbnpOYm4xSVYwNjBGSVRiYTlvRms5NGZVdGNDUWZHdTNiT1BCVjIrK3lia0g3OXFNbVpBUnhBQlBFRUF4TE8rejRUSy9zMkVzNFU5aUswa3lIbFRrU3U0Vng2K0p2ekJSaFJOY1F6dHMzTldib2YzSXhCNVQrWXZyRHRxakt4eFIiLCJtYWMiOiI3MjM0NDQwY2E0NjI3YWMyZWVkYWFhY2NiZTA1MTU4ZmRlZjYxOGYxNTljM2U3MzY5MzU1MjUyNGVmYzhiMmQyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNLZ1plYkVoRGRUdUh5S2pBUHF0NWc9PSIsInZhbHVlIjoiUTJoY3ErTnluZ251N0EyTy9XZEJQaGgwK2ttUW1wdUZjdnBuSlZnV1NicVVmc0hlTjR5eSs0Ky9SSjJOd05JSzZtb2M5Um1YNlRWUXZvYk1zK0VUZjliYmx3L0VId3RTbzROOVdTcWtEWjRya3hqRzFiNHdqSUJIK25mSmtNRVU5MWE0bFpNV2FYL1NweFFCRy9FSlNEa3BHSGI4emdmeklTeFd3VmVhaFpraEJYUmtycmdaQW01bWJ2VHpkWkJabWhhSkFVcGdtOGVkZjZTVnl6Z1ZXdURYd3FOTndycWlXZ1Z2R3pxQTJLcWJ0MzBjNnVyb0hBM01tNmYxdi9PcTJ4YkZUWkE3MVNZWHR6ZVVkaGhnNkxueUZHaVlJckJiMlRQQjlmZXZEaFM5ekxzbkhZTzB5OGlWZkRPVk1XcTRtTU5MalRRdFdJZ3ZhTmVKc2dkQ25OcVZHUFl1cTVPRVZPaUhaelM5dW94eDdnWFZDWGtYd2dJTjV1V09xVmR0SVUxUTVrVEZvOVU5VlhWSGF3dDd2N2p3S3dSUmhxczJUSjBRRnpySERwNmxZeGRuc2RqeHJyUGpaUmJWcVBVNWxOQkh3V1N3anRRcUdncjlrYVFYc21kWjhzdXFyTXE3N1R4VWdKTGtQUEdkSm5TRitqN2lrNnVUK1hON3hSa3QiLCJtYWMiOiI2NGYyOWUwYmYzNGI1ZDczZmU5YzVmYTA1YjllZjAxZjFiOWJmMDZjYzBiY2FkZDgyM2E1ZWQxNzRkMTQ3YzdkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745483827\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1472835679 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1472835679\", {\"maxDepth\":0})</script>\n"}}