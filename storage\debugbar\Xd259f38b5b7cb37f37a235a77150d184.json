{"__meta": {"id": "Xd259f38b5b7cb37f37a235a77150d184", "datetime": "2025-06-28 14:59:19", "utime": **********.480246, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.039724, "end": **********.480262, "duration": 0.4405379295349121, "duration_str": "441ms", "measures": [{"label": "Booting", "start": **********.039724, "relative_start": 0, "end": **********.422244, "relative_end": **********.422244, "duration": 0.3825199604034424, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.422255, "relative_start": 0.38253092765808105, "end": **********.480264, "relative_end": 1.9073486328125e-06, "duration": 0.05800890922546387, "duration_str": "58.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45066432, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00273, "accumulated_duration_str": "2.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.455729, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.033}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.466751, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.033, "width_percent": 17.949}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.472787, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.982, "width_percent": 15.018}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-925464455 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-925464455\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2093856769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2093856769\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-447857954 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-447857954\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-60433189 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122755125%7C4%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZ5YnNYamVLZnpNQzVtbE1tUzRpYWc9PSIsInZhbHVlIjoiazJuSytVUlp2cFVMcW5GZVRqMXhKUWhaTU9kODdYeHhicm82ZnNYYUZQY0d5cEQ2OXU0bGg1RVozMU9idEtaaFZpYkYxYmNFZTN0MDkyZE5mNUNUMDZuR2JnMzUrb3MwL1AvV1BOZEdCSTNPSmJMNzJjbUl3WHZWcmpyWXpVZUNEOUJhK25iWlBtWnZDK0tQOXVjSUtQekpEZ2E4YkZyRlF2bkpQK3pqL0xtNHE4VHpBZXpRZzZ6M1BZZ0FpTEtwWHFiVjdNc2FQMXNORWQ3b29uczk5WHJFb21YNlVOcnV6MWpVbDJFZ3Q1TjdvRjdGdlBVcUptVEVkZVoraTlvYnpLOW9vUFdPbTRXdVJVNFZUVlVKWlhvZnNSdUs5d3NHbUlLZ3FpZGVOa1g3S0JYN2t4WWR3TXVHR09aY245dytyZk5vVzlzVTdYcUI2MlpHbEIySzlUVURQdWMwS3NOMUR2MWpPQUJVV3RFQ0hxbnRIUTZIeEk3cXh4QXRaQmRDbzdvbTlvY1BXVWlJWkIzMTFTZXlTUmk5cCtBOERnODFkRzFPZGY0YlNuWTlFNmZ0dGxNTjRXWWFjUnZZRzAxc2RBWUJYdyt1aXRHRllBVnFZaHNZOTdNR3dnTVZPVWFpYTliRXRyb2pLOHdCbU9WaFg1WFBmTHJ5WU9URHB2UlEiLCJtYWMiOiJhZmNiNmViNGI3YWU2Y2QxMWUzNDg0YzMzY2I4M2NkNGMwZDQ4NzJjYTAxZmVhYjMxZWNjZWQ3OTgwOTZhZTNiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBJanl6L3RKTUFTMS9WaVFqRFdla0E9PSIsInZhbHVlIjoiQ01MZXRDT1poUlVLOTFJbldEdlpHMG5EY1NyQTJkYVNsbHFubG5BWVo2ZEJhYnJvSXVkL2tORDBnY3BCZGRkRkc5ZmZFd3ZNUHVCL3I0dm1lTUhKcG1JbzkxSFVQL1h1dTRHT1F6OUFHd2Rkb29NSWFYRGdNb1RIalFJUkFmVFdmL2g5cGNJdmNmYXlESHArTW9HNWdIend4QmVrUk83Z0NwVWxYb1MyWXB1OHhMcjhISUNsMGEvSnpJNnJoczN6aUpjTDJxOUVPcC9JRUpvYlNObGd5Qi9TU3ZFVnpFNkd6cVNycjZQdjFWV0pWMWZIdnhsZWhtbnIwUFZ1VkhpY3RNWlllWWpIdFhUUEJ5MXhHdkNTd3Z6K3o5ZmN2R1lMWXV0S1c5dDFqd0JUdjEvUWQ5R3YzYUYrK1l2dENKVUFISkxsTEI5K1ZSVEloRWxSTDVmME5oLzJuMHpEZmJEb00wV1ZGRU5uOGxRak5MTEN6Nkc1Q3V5UlJNajVLVFp4N29id3FJL0xTd3RLNEVRQnVPWTdJSkxyeHR3V1JvR1Rad2dsR04rVWpTbEwwRHRuazNhek0rSDg4MkFuR1JGc2J0b2FTMHdsZ1czUi80am1wcmRyWmxaZk41MU1VVUUralJQVGFoNFM1WUc5UmFIRWYxSmg0R0thNzc4V3c4bFIiLCJtYWMiOiJkODU1YzI5ODk1MzAwMDUwYjU4OTUxOTlhOWY5MTQzNjNlMWU4ZDMzN2ZlNzkxNTczZmViY2FiMzVlOTk5ZDljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60433189\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-411827818 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411827818\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1646548133 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkR0VG1WRU4xRkVObG90Q0g1bnhuMnc9PSIsInZhbHVlIjoiaEp3ZE42cXQveVJUSzFjNkNFTVZjM0NUaW5WcGR5d09oR29VT3FTcEdTQ1Q2M1QwNnRNZlhSY2JKRmZpM1I5ZDNUMVR6WHhrRFVldU9BOHF2ek9naFdieWp0cnpYRnJJU3M1U0U2VGp2U2FIVXNqaWYramRkTytJczU4U09SeHhQd0VHOFYxNFlLNDI2ZkV1cEl0ckdIejZscXhlWlVFK3FrNkRqTnoyalg1TUJSaDJFSE1uZG1TS2pvTFY3bG5ZdFlBWUhRNXIxQ2IzcitLdmJSa1JGMVhmMFZscE56L3c4ZVhSM1dVdTBJNUxhaVZ3djh6SEI4R24yYXZPbHNLV3p0bXlLalV6WnloNUF4U2FTSVkvdEtTZzUvdzlRNlQ1UzdMRExYU1c3SzgwYmhwNFpmN0ZsUC9QN1Y1OTRTZlR4UzJoayt5OG9qMWJtS0xTc1pUTzJIbm5raTF4TC9OdllWOW16NW9vNUw1QlBsaWhXVmZCcGVHYVhBZWpxQTNBbkRRR2pzWGZnd2dSaVIrbVIvS09zNU9NYmh2T1dLSzd5NW5RT09YT3dYWFRMKy91ck1rb2M1NkMwajk4Risvek95dzRSd2lPOTJJbDY2cXhld0dTblpZaWdqTTZjMkZvTzVNek9GMWt5V2JMaTU1THNObnpIVzNwRTcwVkU4VUIiLCJtYWMiOiIyMWQ2NGY2ZDI2MThiYTg4ZDFhZWFlY2MyMjM1OWViMjMxNjcwZDhlNDM4ZDg5NTkwZTVjNTA1OGEyMTBkZDhhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVkc3NWdU5xUnlnWmpoQktxbnZwanc9PSIsInZhbHVlIjoibVhLSEwzbXZtY2JEWTh1dXNxQkQ4Z2p1OFR3aGdDdTBLaDJHMlZ6K2c5SkFJR0YveE51T3A4UUU2TTB3aVd1RUdBWE5FUDB0TG01akFDcERuQzI3cUh1NXBtNTZCZ3pZOHNMdFR1cHFwaUpXRmFVUUo2UDcyUW9sTC9tWHJXc2R6SWoxOURlWjhtaFVMUXRNeHRPLzd1cGgwaTRZVStQVjNoZloyZVFYTmlWaHlHRVlqNzZMUnpxS3NSYVdSOVFkQTA4QmZ5WnU2cXVBUmJHeHVmdkRLQlplOTVyaExRTWJodHByZVdmQVhhMjFwSTB6YVJjc2VFVDZ1ZDBvbDhFVHIwZjAvN0Vla3Btd3lwVE95aHRKNkxJZXJwWW02ZUdwTW9yVGFJZGl6Q2ZyQ0gzYUJ6c0VIN2RiWGlrbVhFUUpkM0NVaUZUYjJJVFE5TVZUcExraHpDZERIUDZ0eUhQSWhoaFhwaGl2ZllBaVVDZkpoRk12MkhiYTJMOGRLaEhnVTgzYWxnak9ZSFN5TlAwSDcrODZ2Z0xhKzE0VmEwZUJQSWJRZjl5RHp2UFRSd0R4V29lcTVrZ29vSUtMdlFWTkxkMUJ1d05ZT2FReXJ5MWtmaTFYdlJSQ2RyUnB6QjhPMmswNHllL05iWis4RlhGbThmWlF5SEtBajNOdUJYMTEiLCJtYWMiOiI0MGYxM2FkNDU5YjZjNWQ3NjdlMGU2N2E3MGQ3YjQzNTAxMWVkMzFkMDRmYTYyMmU0NDU4NWE3MjQ4ODM5OGYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkR0VG1WRU4xRkVObG90Q0g1bnhuMnc9PSIsInZhbHVlIjoiaEp3ZE42cXQveVJUSzFjNkNFTVZjM0NUaW5WcGR5d09oR29VT3FTcEdTQ1Q2M1QwNnRNZlhSY2JKRmZpM1I5ZDNUMVR6WHhrRFVldU9BOHF2ek9naFdieWp0cnpYRnJJU3M1U0U2VGp2U2FIVXNqaWYramRkTytJczU4U09SeHhQd0VHOFYxNFlLNDI2ZkV1cEl0ckdIejZscXhlWlVFK3FrNkRqTnoyalg1TUJSaDJFSE1uZG1TS2pvTFY3bG5ZdFlBWUhRNXIxQ2IzcitLdmJSa1JGMVhmMFZscE56L3c4ZVhSM1dVdTBJNUxhaVZ3djh6SEI4R24yYXZPbHNLV3p0bXlLalV6WnloNUF4U2FTSVkvdEtTZzUvdzlRNlQ1UzdMRExYU1c3SzgwYmhwNFpmN0ZsUC9QN1Y1OTRTZlR4UzJoayt5OG9qMWJtS0xTc1pUTzJIbm5raTF4TC9OdllWOW16NW9vNUw1QlBsaWhXVmZCcGVHYVhBZWpxQTNBbkRRR2pzWGZnd2dSaVIrbVIvS09zNU9NYmh2T1dLSzd5NW5RT09YT3dYWFRMKy91ck1rb2M1NkMwajk4Risvek95dzRSd2lPOTJJbDY2cXhld0dTblpZaWdqTTZjMkZvTzVNek9GMWt5V2JMaTU1THNObnpIVzNwRTcwVkU4VUIiLCJtYWMiOiIyMWQ2NGY2ZDI2MThiYTg4ZDFhZWFlY2MyMjM1OWViMjMxNjcwZDhlNDM4ZDg5NTkwZTVjNTA1OGEyMTBkZDhhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVkc3NWdU5xUnlnWmpoQktxbnZwanc9PSIsInZhbHVlIjoibVhLSEwzbXZtY2JEWTh1dXNxQkQ4Z2p1OFR3aGdDdTBLaDJHMlZ6K2c5SkFJR0YveE51T3A4UUU2TTB3aVd1RUdBWE5FUDB0TG01akFDcERuQzI3cUh1NXBtNTZCZ3pZOHNMdFR1cHFwaUpXRmFVUUo2UDcyUW9sTC9tWHJXc2R6SWoxOURlWjhtaFVMUXRNeHRPLzd1cGgwaTRZVStQVjNoZloyZVFYTmlWaHlHRVlqNzZMUnpxS3NSYVdSOVFkQTA4QmZ5WnU2cXVBUmJHeHVmdkRLQlplOTVyaExRTWJodHByZVdmQVhhMjFwSTB6YVJjc2VFVDZ1ZDBvbDhFVHIwZjAvN0Vla3Btd3lwVE95aHRKNkxJZXJwWW02ZUdwTW9yVGFJZGl6Q2ZyQ0gzYUJ6c0VIN2RiWGlrbVhFUUpkM0NVaUZUYjJJVFE5TVZUcExraHpDZERIUDZ0eUhQSWhoaFhwaGl2ZllBaVVDZkpoRk12MkhiYTJMOGRLaEhnVTgzYWxnak9ZSFN5TlAwSDcrODZ2Z0xhKzE0VmEwZUJQSWJRZjl5RHp2UFRSd0R4V29lcTVrZ29vSUtMdlFWTkxkMUJ1d05ZT2FReXJ5MWtmaTFYdlJSQ2RyUnB6QjhPMmswNHllL05iWis4RlhGbThmWlF5SEtBajNOdUJYMTEiLCJtYWMiOiI0MGYxM2FkNDU5YjZjNWQ3NjdlMGU2N2E3MGQ3YjQzNTAxMWVkMzFkMDRmYTYyMmU0NDU4NWE3MjQ4ODM5OGYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1646548133\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}