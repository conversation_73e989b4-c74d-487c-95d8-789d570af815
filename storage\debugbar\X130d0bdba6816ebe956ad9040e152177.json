{"__meta": {"id": "X130d0bdba6816ebe956ad9040e152177", "datetime": "2025-06-28 15:49:59", "utime": **********.176094, "method": "GET", "uri": "/financial-operations/product-analytics/stagnant-products?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125798.755741, "end": **********.176107, "duration": 0.4203660488128662, "duration_str": "420ms", "measures": [{"label": "Booting", "start": 1751125798.755741, "relative_start": 0, "end": **********.105, "relative_end": **********.105, "duration": 0.3492591381072998, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.105008, "relative_start": 0.34926700592041016, "end": **********.176109, "relative_end": 2.1457672119140625e-06, "duration": 0.07110118865966797, "duration_str": "71.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49288968, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/stagnant-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getStagnantProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.stagnant-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=513\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:513-582</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01738, "accumulated_duration_str": "17.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1395, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 10.069}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.149326, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 10.069, "width_percent": 2.532}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, ps.purchase_price * wp.quantity as stock_value, DATEDIFF(NOW(), ps.updated_at) as days_since_update, `ps`.`expiry_date`, CASE\nWHEN ps.expiry_date IS NOT NULL THEN DATEDIFF(ps.expiry_date, NOW())\nELSE NULL\nEND as days_to_expiry from `product_services` as `ps` inner join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT DISTINCT pp.product_id\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nAND p.warehouse_id = 8\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`quantity` > 0 and `sales`.`product_id` is null and `wp`.`warehouse_id` = '8' order by `stock_value` desc", "type": "query", "params": [], "bindings": ["15", "0", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 561}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.151999, "duration": 0.015189999999999999, "duration_str": "15.19ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:561", "source": "app/Http/Controllers/ProductAnalyticsController.php:561", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=561", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "561"}, "connection": "kdmkjkqknb", "start_percent": 12.601, "width_percent": 87.399}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/stagnant-products", "status_code": "<pre class=sf-dump id=sf-dump-1782765718 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1782765718\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-770904653 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-770904653\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2039699716 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2039699716\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125788432%7C28%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijk1OEMrMVhGWDFoWFkzcHRyR1NJUWc9PSIsInZhbHVlIjoiUUtFZndXSkhVcElNVUkwVkRaU21hdEU2R2dhbGU0YTRZS0kyQzZMM0FhQ1JTY1BkMVQ4UlhEM21MajhMZXVTSGdTbXdVL2dVbFJyS3BOV0thYmtvODFJYmdwMDBHSVl3a29ma0lOcWwzbU5pRUhLa0RORmdGZGxLOTVSaGppVldKbkRMTVBkeDRER2NxYmVCd25mMFBablppeTNxczZvQkFPNTdjZnE1WTMvWmZJY0tkTEZLcUxJQURkRGxRdE0xSjZTZy9uV0lWUlBRZW1EVDhOTWVTM0dtNEEzak92V3RwUzJkeFNMR29kWlVKSkhnR0lsMEFaNEFGNzZzMThsbmx2eGFhUlJDcWlkeXl4UnpSQ1ZVdW5hMWR4TnNJSU9FMGR4U3Q5T2g4R3lDUWd6clJNemtxSGtBSkN3dXYyL3h0eEY0MmNrREtvYzdmSGF2b2ZqYlFkYmNDQ3NWTzBFR1lBeE5SWFljUEZlUXp2V3dXNnpRT2h3cXJKaXRCbEtGY01maWJlY1ZsV3ExUk5ub2w0LzhHZGE1V3BWemtycTBLa0xvKzlqY2dtYjREZUpuSWtUQytQbU1UU0V5clVoRWJuUnVUMzI4eGpsbmxLRDltTWJRVVUwbU5sSXBwYnhOVEhYcExVYXd3ZndiRmNndmQ3OVFaTUJORnVjM0RzOUMiLCJtYWMiOiI2ZGVhYjRmZGJkMDgzYzE2MzE1NmNlYjM4MWYxYmMxZWYxNmQzN2YzNTU2Mjc4MmVhY2E2MDhkNjc5MDU5YTRmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InIzbFh3NEZpZlRhMVJLMGcvdXI0Z1E9PSIsInZhbHVlIjoiWGRsM3B3SGp3bFNnTzJNQnNsZ2h3N3FmNDh4WUhhalQ5VmxCZkFqUkVQNVgxUjFlOWFaWFJhd0pzTHJPaUJoVUtFOVhtUzJFa0VuWUVwZDR5MWhPcVNha1o1ekdEVzhkMWQ1alJEWjB4RGtyNFdYd0RqaFZEKzEzWHYzU3FOWU5rcmM1N2lHWTdraGF1QTJCZXJtOEZ3Q3h0WGVkVDBSNXA3VnFFTmdxYjQwM1kydjkwdjZDRTFNamNFdndLdW5uaHY5WXEweWFTNytqMGZySG1ET1poRUFudU9qeUM0K3EzQ2x5Vmk2VHlFM1JodWlrT0hPTEFIWWFnSWVsbm9rNVZaSTF6TFc0eUF5SmdhZjdvdnQwZGxqTHU5cHQ5anpHMHl5ZE45ZTJVajc2VW4vSWVKMzY4UUJVaytac3BSNjNmaEFtUCthZTk3TjdCbEk3OVFKS2xBK2sva2lQQ1I0TmV5K3hSV0x2UkprUno1R0VmSU9nZm85Q0RPZEREaW0wSnJaVE5vUnJGVkNCbDNIQ293bHAzdGJoalh1YTFicG5qZ1lLdE5jZXFIMmVEMEFmTXBUVEhTMjdzS3hjRnBnSjVjYWRHT2ptcmo5V3o1U1hIMW9pT1RMY2dVdDNWRHdEdiszQlRqNTl6dzF1MkdsdWJNMnZ2TlRPbHNqdkhLTGIiLCJtYWMiOiI0NTg2Njg1ZmI1NjY0ZDcyYmZlNTE5ZmRmNzQ5NGNiMDFjNThiMTg0N2MzYmY5YjY5NzM1ZGMwZTU2ZGVmMTM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2008751367 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008751367\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9ZcXloQm1uYWxJYmRaOEdzbUdOeXc9PSIsInZhbHVlIjoiek9KcGNhRlB6cEJuaSsweXJEVGFpN0gwZXMvVW5uTGVaZGlvOGlTSGxwRzJLNnVrdE9FVW5GTldmRDF0SFBJUjB4c0YwbWU0ZlF1L3VDVDNoeG80alFyckJuaXJnRXBwMVlSVDc4SkdtMTF6Zlo0bGhrUTBDNldTbHp5U2F4UjdJQlZPSDFmeXlHdXJrS0VDbjBEQ2lTWWJQZnhCUFVvemNITUhPZFUvNFBYa0tRdTFKdmhtMU93MDV5VGNURktCWDUwRFpyQzdBTW5YcnNYUXhEbFNLdTI2M3ZhZmhKbEQrbW9nQlAwaTQ5bkt0M3VNS2FMNGJETUwvRzNvQ2E0ZFVLRGJPVDg2TjVoRkc5M1hraEhwV3pYY25lU21DV0FkUWw5b0JIVm5ESzM2YndXYnh5SDNLeGFCMWJpbmlKQnZ2TkQzWWF6Yy82S2U1aGRGcURXVlVLV1VPdExpWmMrbHptTStldzlFakZCaVY1OG9ZbXowKzBNNDF6angvby8rVVBKQmtKWER6aVhDcjBwaVNKY2ovcXlnNGM3azM0c0tDS1ZCUFRWcUZNSW95N0psbzRZd2psVkZIdTJrTVkvL2hUMEIxN3Q4TGxWT0lqdlFyTFpyZjc5aDUwZ3lqcGMrYTBodHpSMno5UDJGeHJMU0xMY09GbUkzN1FlV0ROaFEiLCJtYWMiOiJiYWMwNDJjZDE0MzVmMmUwNjIwMzJhNzQxNGE1MGVhOGM5YWE5NDI3OTUyZmMyOTMwNjA4Njc5M2I3MDI4NjZmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IndZczFQT0VVTGM0eGJhbVR4OWdqRGc9PSIsInZhbHVlIjoiaVBSK21MRndyQSt0Ny9nMllSQUJxQnpudTZ1cDFkQU8zaDluanJwZXZrS1dLSktEZnBWUVM2YUt0alZGYXhEM21teEo4L0xNaHZpOTlQcFB1SnJXTE1pd2dkUGd1b2pEUUhNMnE4QlNaUHArMWNMekVlN1FTeE1rak1SaWU4K0FUSEY1Zlhmc2xXbzlPK01IOHROUXVpVFJWU05KdFJWTG1YUzNuOWZrVUFYM2M0aEZnN25OemRyNjd0eHRoZCt0dlFBdWlEclpuUU9RaEpzZ1ZoNC9lUDNmRFFVVDlsYUZUa1FiYm56MjQ0M3ZXR0pvcGthdUI3VTAxT1A5WVBmZnlvblYyeHJkYldzSDNkdnhiMnhyZ1JYRjVyUmlBMzRNVFFNUkliS3BvQUlydENYMWZtMUcrRkhUUm0rYVNHMG5MRmduUkdtNWU3VkI5Vm5ic1c1QTdCNVcyMTcweWw3TXNPNWYxVnBvVTh1RUJ5YkVkNG9WajlIa3lrL1VvUjF6dDA5QVJMUzI2bWlGQ1NQTkdrTGlHTDN6Qk9lVkt0ZUNiaDNwQU0rVEpXdnhtTGJPVzFjU3pUWjJrRVp0OWtFS2VLblRJeTJJMjd5VTRUK0FDYlhpZHdIMlhJbUNJZkw4alB4ZVFTNWtOT2hPZ1hZamU1V1BBampHZ2Fib1lheXQiLCJtYWMiOiJkYzRjNGQzZGJkYzMyMTVmZGM4ZGQwMTRlYTVmNzk3NjBiZDM5YjRhOWIwOTc0OTZmOTk5YjljOTQ0MjU5OTVkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9ZcXloQm1uYWxJYmRaOEdzbUdOeXc9PSIsInZhbHVlIjoiek9KcGNhRlB6cEJuaSsweXJEVGFpN0gwZXMvVW5uTGVaZGlvOGlTSGxwRzJLNnVrdE9FVW5GTldmRDF0SFBJUjB4c0YwbWU0ZlF1L3VDVDNoeG80alFyckJuaXJnRXBwMVlSVDc4SkdtMTF6Zlo0bGhrUTBDNldTbHp5U2F4UjdJQlZPSDFmeXlHdXJrS0VDbjBEQ2lTWWJQZnhCUFVvemNITUhPZFUvNFBYa0tRdTFKdmhtMU93MDV5VGNURktCWDUwRFpyQzdBTW5YcnNYUXhEbFNLdTI2M3ZhZmhKbEQrbW9nQlAwaTQ5bkt0M3VNS2FMNGJETUwvRzNvQ2E0ZFVLRGJPVDg2TjVoRkc5M1hraEhwV3pYY25lU21DV0FkUWw5b0JIVm5ESzM2YndXYnh5SDNLeGFCMWJpbmlKQnZ2TkQzWWF6Yy82S2U1aGRGcURXVlVLV1VPdExpWmMrbHptTStldzlFakZCaVY1OG9ZbXowKzBNNDF6angvby8rVVBKQmtKWER6aVhDcjBwaVNKY2ovcXlnNGM3azM0c0tDS1ZCUFRWcUZNSW95N0psbzRZd2psVkZIdTJrTVkvL2hUMEIxN3Q4TGxWT0lqdlFyTFpyZjc5aDUwZ3lqcGMrYTBodHpSMno5UDJGeHJMU0xMY09GbUkzN1FlV0ROaFEiLCJtYWMiOiJiYWMwNDJjZDE0MzVmMmUwNjIwMzJhNzQxNGE1MGVhOGM5YWE5NDI3OTUyZmMyOTMwNjA4Njc5M2I3MDI4NjZmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IndZczFQT0VVTGM0eGJhbVR4OWdqRGc9PSIsInZhbHVlIjoiaVBSK21MRndyQSt0Ny9nMllSQUJxQnpudTZ1cDFkQU8zaDluanJwZXZrS1dLSktEZnBWUVM2YUt0alZGYXhEM21teEo4L0xNaHZpOTlQcFB1SnJXTE1pd2dkUGd1b2pEUUhNMnE4QlNaUHArMWNMekVlN1FTeE1rak1SaWU4K0FUSEY1Zlhmc2xXbzlPK01IOHROUXVpVFJWU05KdFJWTG1YUzNuOWZrVUFYM2M0aEZnN25OemRyNjd0eHRoZCt0dlFBdWlEclpuUU9RaEpzZ1ZoNC9lUDNmRFFVVDlsYUZUa1FiYm56MjQ0M3ZXR0pvcGthdUI3VTAxT1A5WVBmZnlvblYyeHJkYldzSDNkdnhiMnhyZ1JYRjVyUmlBMzRNVFFNUkliS3BvQUlydENYMWZtMUcrRkhUUm0rYVNHMG5MRmduUkdtNWU3VkI5Vm5ic1c1QTdCNVcyMTcweWw3TXNPNWYxVnBvVTh1RUJ5YkVkNG9WajlIa3lrL1VvUjF6dDA5QVJMUzI2bWlGQ1NQTkdrTGlHTDN6Qk9lVkt0ZUNiaDNwQU0rVEpXdnhtTGJPVzFjU3pUWjJrRVp0OWtFS2VLblRJeTJJMjd5VTRUK0FDYlhpZHdIMlhJbUNJZkw4alB4ZVFTNWtOT2hPZ1hZamU1V1BBampHZ2Fib1lheXQiLCJtYWMiOiJkYzRjNGQzZGJkYzMyMTVmZGM4ZGQwMTRlYTVmNzk3NjBiZDM5YjRhOWIwOTc0OTZmOTk5YjljOTQ0MjU5OTVkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}