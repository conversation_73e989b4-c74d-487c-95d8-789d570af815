{"__meta": {"id": "X14e69d1cff76ac2f8954d10d4fb4016f", "datetime": "2025-06-28 16:17:29", "utime": **********.849966, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.337921, "end": **********.849994, "duration": 0.5120730400085449, "duration_str": "512ms", "measures": [{"label": "Booting", "start": **********.337921, "relative_start": 0, "end": **********.765004, "relative_end": **********.765004, "duration": 0.42708301544189453, "duration_str": "427ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.765018, "relative_start": 0.4270970821380615, "end": **********.849996, "relative_end": 2.1457672119140625e-06, "duration": 0.08497810363769531, "duration_str": "84.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45702048, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02301, "accumulated_duration_str": "23.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.800857, "duration": 0.0218, "duration_str": "21.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.741}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.83247, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.741, "width_percent": 2.216}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.838893, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.958, "width_percent": 3.042}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1330764535 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1330764535\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1942936803 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1942936803\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-933197045 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933197045\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1717928472 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127024092%7C32%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ing3bkZFUkloSHBmazFSRTJuanpqamc9PSIsInZhbHVlIjoibnlXOFVmbFVobFJrSEVvWU1UQ2JRNjdHektQWERLU21nVmc5Wi9oMjZwd2ZiMUQrNUtyZ1AzLzEyS2hWbzZyd2RMRXBtM2JVYU1hb1k0K0dDcHZCVzRvcXhZMXR4eGdjZ2s3aHFTMXpYcDNYTUNzZFhMbDJtdWo3NEV3WENXeTNPYlhtclkydVRQRUY4ZGEyZVBobFNoSDNYZkV1TDRXSlFyYzBwVUVlbjMyQkJkMlUzVHFLem40MHpVdnFaWW1waFVNUk56bWdmTFV5M21POVJnLzFXaUlLYWVKYTNKVUtMd3J4N3BMSmZrOXBLVnVLSVlXek8yVTJheHFxeGZPcnA5bWhPcnFpRzV2cWxPdE9zZDVQcVVTajhFL1BnSCs5VXFtMWhrL1czQmo3VGFrMGVhbXl6dnZ1WWRWTlZqMXpMZUd5UyswT3EyeDE3ZVV4UlFkdGluZ0N3UmhkM3VDNjZlalY2TFNaeG9IMlA4c0hxSm1Od2xXRHlOdUtjaWxsb0d0S3FVZy9JNVVGR1V6STJvS0FFSjloK3JtRjZiLzhIWEprR3piZnRiVGFFSE9TdHY5cmhmTi90NFU0QURXTHpKQ3M4T09uczF1R1EzZVpqSk9MdG1SMG83Zmc5RTk1YTNnTUQ3TVpkNnAxL1NpeitPQTNObFJVcFVVeVl1SWQiLCJtYWMiOiI5OWY4MmIxOTNmYTM0OWYzZWI4NTY3ZjE1YjM1N2Q1ZjZkNGY5NTFiYWNmMGI4OGRiM2RiOWVjYzYxMjEwYmM0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhqVm56c2t6eGQvRndnQzlNNTlGemc9PSIsInZhbHVlIjoibEVoOGNSUk1pbno4bUtodmkwcjRDOEswREZSd3JQT1h6SkNVMThtOHRQRkx3UjQvR3VKS3pxZXpwbnQ0K3hGU0swTjJjMFFjQzVQTXR6R0M1dEt4WXEvUWFqZzZPR0NyaUpQN3B5Y21iRjZmS1UyKzJHRjlobnFsV0JSa2tSSEp4VDkwZVlweVR1d0NsbjZVSTk2LzhWcW9yRk1SYlNwWVZzcGkxU0lweDAwa2JMSjZyT2o2aGNMWjNSd2VSNmNleGRvay84Mml1M0E0NXJVVExESm1TYzhNeHkwcG1TaUNaVTNVMFVjeDZiZS9iWWx6RU5lTEVEUWM0cWVQQk1ubnR6Nm9KTkFZUGZCWDhSZFd6WG91SWZ1Ym5FWFNwLzZBWjFtOUdHVkwwamxUT0ZndDhNajhLY2ttUks1d0pKdzI4SG1iZFI5QjhWTGkrallTVndFb1FEWkNzakt3YzRiU0Y3V05iTXUyUzNaQnRNOHduWGZ4OTJBQktWVjROU0JMNjcrSlZEYndzZWQ3VEVPWlA4T2RxN2JHbDJnMVVTWDFUa0N3NTloOFhvUXN2aUdxa1VnV2tFaTZUSngyM1J0QllHUHJsQ0pPUTFaZE9EN0pKWHZyQVA4V1I2dTU2aWtkUVVUcW9QbzlnY04zczQxVjhLSFRQWTJZU29qWDBqeUsiLCJtYWMiOiIxNzYwNDUxOWI5NTcxMTQ0OGQwZTRiNmE4YTQ5YzYxZDMzNjNiNGVjZDUwNTJjMTA1YTNkZjhjNWI2Y2YwOTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1717928472\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1462547183 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1462547183\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-810166915 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:17:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhtTERraDN3dVFHNG9COWxJaDZXS0E9PSIsInZhbHVlIjoicEU4dDNnZ3ZycjE4M2RvZ3JnSmhhTXRuL0hsVnRYbnFmYkd1UTRFUy90eVhNUXdCTEFDb0hKTzhTcXVleDJKK0pDdUpjVVc3ell5bk05U0VIK1hZdDRYUTlQVWtXdXo5UXVhWjM3OFVhc29kT2RqU1I5MzNSN2hKa0JzQnVja3gzV1pIeXBpYUQ3a3ZDKysyaUY1ZEZhMkZ4blgzVldDRlRTb01hNXk5bnJqVVVkUDU2T0N2ZGxhUllNTGpQVjNoQUltNVA0YTR0bS9pbytOMFdadllEZ1FYRjVoOUlKcUIzL045WU5Vc29JRXVxOUgxTnhpc1V4dHlHZGg1T3kyNjcrby9BZmJqSEhNTktxVTUzcE81WW1qcnRKOEh1a0IzUmJrOUt6dXBTZG5KZzJwNENBKzNwaVAxRlAyV3JYUmZmWjRBRjl3SkVKUmxycHZvRmlPYWRFdUppMlYwQWJ5RmhneHBFeGt1Y0YveGI1bkxHMUtaVmppcXFuYW9hK2NUVnBvckg5WlNtb283TG83NHZWU2doU25WSWc4YlBNNkYyTWhUVm1kcTdld091WnMxY2p5dDhGdFllMDF3aHZmUzFrcjhuS2tOeDlVbnIrdjJXSWViZVlYTmRBcElvKzFOMDlTNTZVREk2S3g5dnNwRTJ3VDB4QU9zLytTWGU2dVYiLCJtYWMiOiIyZjQxZjc1NWIzYmY1NTEzM2M5ZDIwZDAwMjhjYWQzYjlhNDkxMzQzMWVjNzhhMTE5NjA1Mzk1NzUxMDY1MDFlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlV1eUh6bk1jRkJMSXphUzRYZU1nbkE9PSIsInZhbHVlIjoiSEVXMGdUcWZvWGp2TmhlN21BZ0lZQnlTUDBuM05MUzUzdGlrUXRKdVlGd3FEbzZWMWwxNitTSzBRSUtpYWVuRkZFTmZ4M1NxaysxZUNqb1MxTWNDMDdXUFV1anhUWXR3SXpIdWxOYmVIL2hRdDNDMnIwaGFON0pNQkw0VHdrZE9LekZRU2hUbmFiQW0yS1R3REwxR05TV0NFVlRuUFgvcTlUYyszOHZ0SGwvNDQ1aXJ0Tm1mZzl2cFJrRklrNW9pUGgzQk9mY1FndjcxeG5rNHc5T3FRT3FMUTI3RDlaTnpXN1lXTUdEVlJTZlg4UG1uTDNWT21GdmZScmdRZHVJeTdraCtrYTNRVXBxekhiQ1JQRmpIMW9vVXhKczVkS0FvbTlueEdteE9EbUpiR1RrbmNlNks1MFp3QjhRSThJbnRBVkhvcy8yamNRbWkyWWg0UDFLcFJHYnQyN0NNcjhsUEZmcDBTZ1g3UDZBdm5qb05hTFA5aE80NkpRQmtMRlZDTWhBVlEraW1tNXdDbDdXbVVxWWRtRXNtU2VJOU9hNXV5a0hRNVNFS2JQYlBIRlplMjhoUzVsRG1PRENrTVluNnVRZFhxL1ZvVElBaU9XM25MRTZnS0hmcHY0bDdEVmIva1dJSlNaWWk2cTZPazhMT2IvMWN3SmkxK25yRXl2MVUiLCJtYWMiOiI4YWI2NjhhNDE2MWJmMzE3NDc1OTMxOTA1MzVlYTQ1Zjg2MDU3ZGVjOTkxNTFlMmUyMTM1MGU5MzAzMjUyZWRjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:17:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhtTERraDN3dVFHNG9COWxJaDZXS0E9PSIsInZhbHVlIjoicEU4dDNnZ3ZycjE4M2RvZ3JnSmhhTXRuL0hsVnRYbnFmYkd1UTRFUy90eVhNUXdCTEFDb0hKTzhTcXVleDJKK0pDdUpjVVc3ell5bk05U0VIK1hZdDRYUTlQVWtXdXo5UXVhWjM3OFVhc29kT2RqU1I5MzNSN2hKa0JzQnVja3gzV1pIeXBpYUQ3a3ZDKysyaUY1ZEZhMkZ4blgzVldDRlRTb01hNXk5bnJqVVVkUDU2T0N2ZGxhUllNTGpQVjNoQUltNVA0YTR0bS9pbytOMFdadllEZ1FYRjVoOUlKcUIzL045WU5Vc29JRXVxOUgxTnhpc1V4dHlHZGg1T3kyNjcrby9BZmJqSEhNTktxVTUzcE81WW1qcnRKOEh1a0IzUmJrOUt6dXBTZG5KZzJwNENBKzNwaVAxRlAyV3JYUmZmWjRBRjl3SkVKUmxycHZvRmlPYWRFdUppMlYwQWJ5RmhneHBFeGt1Y0YveGI1bkxHMUtaVmppcXFuYW9hK2NUVnBvckg5WlNtb283TG83NHZWU2doU25WSWc4YlBNNkYyTWhUVm1kcTdld091WnMxY2p5dDhGdFllMDF3aHZmUzFrcjhuS2tOeDlVbnIrdjJXSWViZVlYTmRBcElvKzFOMDlTNTZVREk2S3g5dnNwRTJ3VDB4QU9zLytTWGU2dVYiLCJtYWMiOiIyZjQxZjc1NWIzYmY1NTEzM2M5ZDIwZDAwMjhjYWQzYjlhNDkxMzQzMWVjNzhhMTE5NjA1Mzk1NzUxMDY1MDFlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlV1eUh6bk1jRkJMSXphUzRYZU1nbkE9PSIsInZhbHVlIjoiSEVXMGdUcWZvWGp2TmhlN21BZ0lZQnlTUDBuM05MUzUzdGlrUXRKdVlGd3FEbzZWMWwxNitTSzBRSUtpYWVuRkZFTmZ4M1NxaysxZUNqb1MxTWNDMDdXUFV1anhUWXR3SXpIdWxOYmVIL2hRdDNDMnIwaGFON0pNQkw0VHdrZE9LekZRU2hUbmFiQW0yS1R3REwxR05TV0NFVlRuUFgvcTlUYyszOHZ0SGwvNDQ1aXJ0Tm1mZzl2cFJrRklrNW9pUGgzQk9mY1FndjcxeG5rNHc5T3FRT3FMUTI3RDlaTnpXN1lXTUdEVlJTZlg4UG1uTDNWT21GdmZScmdRZHVJeTdraCtrYTNRVXBxekhiQ1JQRmpIMW9vVXhKczVkS0FvbTlueEdteE9EbUpiR1RrbmNlNks1MFp3QjhRSThJbnRBVkhvcy8yamNRbWkyWWg0UDFLcFJHYnQyN0NNcjhsUEZmcDBTZ1g3UDZBdm5qb05hTFA5aE80NkpRQmtMRlZDTWhBVlEraW1tNXdDbDdXbVVxWWRtRXNtU2VJOU9hNXV5a0hRNVNFS2JQYlBIRlplMjhoUzVsRG1PRENrTVluNnVRZFhxL1ZvVElBaU9XM25MRTZnS0hmcHY0bDdEVmIva1dJSlNaWWk2cTZPazhMT2IvMWN3SmkxK25yRXl2MVUiLCJtYWMiOiI4YWI2NjhhNDE2MWJmMzE3NDc1OTMxOTA1MzVlYTQ1Zjg2MDU3ZGVjOTkxNTFlMmUyMTM1MGU5MzAzMjUyZWRjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:17:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810166915\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1682830924 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682830924\", {\"maxDepth\":0})</script>\n"}}