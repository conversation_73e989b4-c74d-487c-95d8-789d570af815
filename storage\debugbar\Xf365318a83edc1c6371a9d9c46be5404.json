{"__meta": {"id": "Xf365318a83edc1c6371a9d9c46be5404", "datetime": "2025-06-28 15:43:37", "utime": **********.989134, "method": "GET", "uri": "/financial-operations/sales-analytics/customer-analytics?warehouse_id=9&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.560556, "end": **********.989147, "duration": 0.4285910129547119, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.560556, "relative_start": 0, "end": **********.901924, "relative_end": **********.901924, "duration": 0.3413679599761963, "duration_str": "341ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.901933, "relative_start": 0.34137701988220215, "end": **********.989148, "relative_end": 9.5367431640625e-07, "duration": 0.08721494674682617, "duration_str": "87.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46327248, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/customer-analytics", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getCustomerAnalytics", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.customers", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=359\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:359-488</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.02134, "accumulated_duration_str": "21.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.936099, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 9.981}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.946104, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 9.981, "width_percent": 2.109}, {"sql": "select count(*) as aggregate from `customers` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 368}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.949136, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:368", "source": "app/Http/Controllers/SalesAnalyticsController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=368", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 12.09, "width_percent": 2.484}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30' and `warehouse_id` = '9')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 378}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.953635, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:378", "source": "app/Http/Controllers/SalesAnalyticsController.php:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=378", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "378"}, "connection": "kdmkjkqknb", "start_percent": 14.574, "width_percent": 4.171}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30' and `warehouse_id` = '9')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 387}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.956358, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:387", "source": "app/Http/Controllers/SalesAnalyticsController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=387", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 18.744, "width_percent": 1.781}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30' and `warehouse_id` = '9') and not exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` < '2025-06-01' and `warehouse_id` = '9')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "9", "2025-06-01", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 406}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.958158, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:406", "source": "app/Http/Controllers/SalesAnalyticsController.php:406", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=406", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "406"}, "connection": "kdmkjkqknb", "start_percent": 20.525, "width_percent": 3.796}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30' and `warehouse_id` = '9') and not exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` < '2025-06-01' and `warehouse_id` = '9')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "9", "2025-06-01", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 421}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.960339, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:421", "source": "app/Http/Controllers/SalesAnalyticsController.php:421", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=421", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "421"}, "connection": "kdmkjkqknb", "start_percent": 24.321, "width_percent": 1.64}, {"sql": "select `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`, COUNT(DISTINCT pos.id) as total_orders, COALESCE(SUM(pos_payments.amount), 0) as total_spent, COALESCE(AVG(pos_payments.amount), 0) as avg_order_value, MAX(pos.pos_date) as last_purchase_date, MIN(pos.pos_date) as first_purchase_date from `pos` inner join `customers` on `pos`.`customer_id` = `customers`.`id` left join `pos_payments` on `pos`.`id` = `pos_payments`.`pos_id` where `pos`.`created_by` = 15 and `pos`.`pos_date` between '2025-06-01' and '2025-06-30' and `pos`.`warehouse_id` = '9' group by `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 766}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 427}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.962379, "duration": 0.00977, "duration_str": "9.77ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:766", "source": "app/Http/Controllers/SalesAnalyticsController.php:766", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=766", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "766"}, "connection": "kdmkjkqknb", "start_percent": 25.961, "width_percent": 45.783}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 776}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9735382, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:776", "source": "app/Http/Controllers/SalesAnalyticsController.php:776", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=776", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "776"}, "connection": "kdmkjkqknb", "start_percent": 71.743, "width_percent": 12.793}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 776}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.977624, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:776", "source": "app/Http/Controllers/SalesAnalyticsController.php:776", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=776", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "776"}, "connection": "kdmkjkqknb", "start_percent": 84.536, "width_percent": 11.621}, {"sql": "select `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`, COUNT(DISTINCT pos_v2.id) as total_orders, COALESCE(SUM(pos_v2_payments.amount), 0) as total_spent, COALESCE(AVG(pos_v2_payments.amount), 0) as avg_order_value, MAX(pos_v2.pos_date) as last_purchase_date, MIN(pos_v2.pos_date) as first_purchase_date from `pos_v2` inner join `customers` on `pos_v2`.`customer_id` = `customers`.`id` left join `pos_v2_payments` on `pos_v2`.`id` = `pos_v2_payments`.`pos_id` where `pos_v2`.`created_by` = 15 and `pos_v2`.`pos_date` between '2025-06-01' and '2025-06-30' and `pos_v2`.`warehouse_id` = '9' group by `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 802}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.981549, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:802", "source": "app/Http/Controllers/SalesAnalyticsController.php:802", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=802", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "802"}, "connection": "kdmkjkqknb", "start_percent": 96.157, "width_percent": 3.843}]}, "models": {"data": {"App\\Models\\Customer": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics?date=2025-06-01&warehouse_id=9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/customer-analytics", "status_code": "<pre class=sf-dump id=sf-dump-2108069135 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2108069135\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-342228855 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-342228855\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1487081221 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1487081221\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"84 characters\">http://localhost/financial-operations/sales-analytics?warehouse_id=9&amp;date=2025-06-01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125411738%7C18%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjAxZ1R0U0RDMmpFWGYxYm90NzZhS1E9PSIsInZhbHVlIjoiTjkzYWxOMURxWEtsR0phVzJCeDVXbWhuZVN4TWdTaDBlaWJiMFB2T0VhcFpGRUU0c05sK3BXUFZIbHNKN3Y5dVVEL2haM2lLVXRvKzFsVHMzMDR0dDdEQUkrbmFwbmttS1JQa3FJZCthNnJrdEM4ZEl1eExKNWFWaHp3WDljNGd2OW5yTjRlWlZvZTJ1M01RNE9BV0poaVprNk43bmwvM2RiYjEvUlRQWE51TlA2eHMraWZDQjZOSmtaZFh2eTFKaHZaZm1CL2h4blR2eU9GODEwUTZEbnNzdHBqMXlObEYrNzg1Um81N1I5enNEK09CMVBIbDlMUk93V0FTV0FHM3p5YWI4RlRqRVNVclI1NHErM1Z2ZDkvR0ovZGx3a0hrYUFZQTk0a2kra0J0dHBKNFhtWTNKSHA2WVVvaUUxT2RZL0wxTHVVUkZsZDNEdlZEbk5hZmtKdksvUmEwdzN4Vm92SFdsaHZRT3RpU0ltcUFvUlp3eHJHRlFaTnhwcEtxYlprU3IzNXBxOGMvSG1saVBDV0YzeDBhNzlOcEtjYUJtaWVhQStpMUcrU3d1NStqWjBFTWNHQnNuQkxGemVweldBWFFWU2lKdk40Y3ozMitCZllsOTBiVkMxekhDbHIxSFZqM256cW1ldmVKZSt3OHJZdFlxN0kzTTBWeXduLysiLCJtYWMiOiIwMTM5MDZjYmMxY2EwMzMyNWRjNmM0NjJiZDY3Yjc0M2ZhOGYzYWYwZDhiYmY4OTQyZDdlNDA4NGM4YWY2NjA1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVkbFpXT3gzYTNVd3BXZkNkK2Z1OUE9PSIsInZhbHVlIjoieGhUZ2tXU2drYUIva1VyNlRFRi9pcExVQVF1MXFqRFB6UU9GZW9hS1RBUTQvSE5PMEFEUTgvV3lPbktuUTFCS2Z0NFQ1TWhyQkVrRmYzRVgzVVlGajlqUmxIMDRWMlJWYnJ0ZnRyVWdxc0lYTVQrUS9PcmFwSnlPKythbnRyc012MGpPYUVNbWN2eUdheDBnTkEzTmlrSE5Yb1A0SFFSaTdqMlZ0Tkw4NXBqV3dGUXZnWEp2MWxDclkvaytRVDNTem0wL01NdXAxRVZMMlhDZHNmRzFTR2E2VXFHSEltRjhRUFVKdjhldy9vVVF4cklzYVc3dFVxZm9YV21maFJ3VG1IT2lrbTZuR0RPclpaTGZHeXZvQS82RzFoRXpHM0xFQjFlMjVFMGZ2VWZsZUIzQUtDaW82cEpEMHVFVVpvOGJzMUl1cTgrSEFBUXg2QXZvSVlVUFhqVDJmeExnNFJmWmhRaFpxQkR4eURBRnNHSXIzZWx4OG1rRE1mWnBERVJuakxyWFJuanJqbUxwZzY3R2hiNHR0aCt0TjllaU5jRUVJK1Q3cFd4UGQzVGxjQnBENVpLUURDUExUa1FCdXptOTZCSTJ4NjNneG9Zdmk4YmZMWFF2RW0yMTRGby9hdmtacjFEOW82REtXRExldm5BZS9XU1BlaVpySWlDQlErZGIiLCJtYWMiOiJiN2Y5ZDVkMzI4ZjQ1ZGU2OWZiYTJjNDRjMDhhMDhjZTljNzUxMTZmZDExZjU3YmI5NDVjMzM5YmViYjBmYTI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-772718130 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-772718130\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1880522058 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:43:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBjRXBKVngxU1pyRmo3ZWNtMmo0V2c9PSIsInZhbHVlIjoiRWk4TFR1SHR2Y2tPZ2JTK2RhbzMrdm5zampCZjJaOWlIR1Y0NC95ZnVwTkNtakhLc0ZDb2tJbjE4clYzRkM1TkhPaWE1M0JhcnRHcmZ4Wk9USEJXS1BPRGxsbm5HVlZ0cUZWUi9Ldi9wSzRsSWR6cnhxdnF4RlRpWEFLRXhVcFcrUG8zWWdVRlJmY21QQldmWTlKeEd0NmJVbUcvcEFaZi9FclVBY002ODV5eEVNMDFXeWdFcm52cGxyR2lwd1kyYzFXd0Zya1kvZmk4Y2VrM0QxejN3OGNCUndsOWtSKzFERmUxNStSTVNJUjNlaG8wR3A4VTRLSTFTQUNyOFBmcWdoazdxd0ZSdERyTGRSNGwyeGJMZU1RQVBoS0pwY1hBVWl0UFN1RzV5bU9SYWJMdWJzUHZhQ1FwU3BDNGFqZDNVRm5UVFJGSU8wZ3hXTmg2R0trSW9pUXZXRERGM0ZycGtGZXFSNjBCQ1h2UjRuWWoyam8zQkEvUXhOYXZHVkNkRTNHSHJQREYwMFEwSXU4M2RNNTBQZ01jKzRNWGtIbjJ5MFo5MmNNaHcwVlNJckRxY1pWY3A0ZmlLSEo2MDZaeXExU0tIMkFnblRWZTdTbE5JWE5ERGZvZUxGUStQaDlPU0JrbW1KUlExU25JdERqSmxOTzlzTmFQV05xTnZpNzciLCJtYWMiOiJmM2U4YmVlYzRmMzdjMWZlOWYzNGU4ZTg2YzRlNTE3Njc3ZWQ0ZGE2YTE3MTRiZTE0ZGU5M2E5ZDA0OWFhZDBmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRpNnYrZnVVcnJPY1ovSS9kbkVuWVE9PSIsInZhbHVlIjoiMjBUTnFhalNram8xaDE2RXNZQ2cyejN6cUtMM04vR3NPZVFCay8zeERVL0pHNWFENnFNK2RsdTJ2b2xIWWpoNVNGL1NmbEV3ZUVxTk9PVkFLd2V2d0ZYUjBSTE1MMVJPV0V3Q2VVWVVCZDJkQmdkcGtoOWJZVnZhdlZ4S0tlMHp6SVN4YW10eE0zc1FMQTNzWjdlNXNUMUhGWWZYTGlJelZ5c1htVWxiaVVwbndkbHRjMlhra0YzbWdIbnYxR2NVMk1Yb0Y3SCs3OE1DRzd4aTFWZy8raFdyY1VVd3oyUFk0UDNnWXg5UlZNNzhJZ08ySW9jSktVZ2dJMVBVcnJ4SFlrRW1EWGlDR3ExNWxseTBBejNXdU9OSUo2amhRYlNFSGE5bzkreWVESlhIeERuT0pZd3FNczBTNVI5bGRRWG45b0VtcEs1RkppekZQUmVtRGsrT1AycEQwVHVPOWZ0TTlpVEVFd3hnYS8yQlhMSGNPQ3lUR0ZPZnF6UTZRNWtxRzhrNFZ1aktxelpxSTR0S0traEcrUmRFT2kveXZyY0k1UXFhRW5pQXVhbmN1eEFHVnY3cXFQSlM5RHVGN0FhUG1NRHM1NS90bEw1akNENFVISmhyWkFub0RBejRHeWk1d1hlQysvcGxzdk5zY21qQ0dWN09pdnJoRnZmVktNY1MiLCJtYWMiOiI2MDY2OTViNGYyNTcyYzg1NTZmMzNmNDdlZTkwODg2MzA2Mzg2OWQ3MjEzNjc1NTMwM2YwZmU1ZGViYThhZTBlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:43:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBjRXBKVngxU1pyRmo3ZWNtMmo0V2c9PSIsInZhbHVlIjoiRWk4TFR1SHR2Y2tPZ2JTK2RhbzMrdm5zampCZjJaOWlIR1Y0NC95ZnVwTkNtakhLc0ZDb2tJbjE4clYzRkM1TkhPaWE1M0JhcnRHcmZ4Wk9USEJXS1BPRGxsbm5HVlZ0cUZWUi9Ldi9wSzRsSWR6cnhxdnF4RlRpWEFLRXhVcFcrUG8zWWdVRlJmY21QQldmWTlKeEd0NmJVbUcvcEFaZi9FclVBY002ODV5eEVNMDFXeWdFcm52cGxyR2lwd1kyYzFXd0Zya1kvZmk4Y2VrM0QxejN3OGNCUndsOWtSKzFERmUxNStSTVNJUjNlaG8wR3A4VTRLSTFTQUNyOFBmcWdoazdxd0ZSdERyTGRSNGwyeGJMZU1RQVBoS0pwY1hBVWl0UFN1RzV5bU9SYWJMdWJzUHZhQ1FwU3BDNGFqZDNVRm5UVFJGSU8wZ3hXTmg2R0trSW9pUXZXRERGM0ZycGtGZXFSNjBCQ1h2UjRuWWoyam8zQkEvUXhOYXZHVkNkRTNHSHJQREYwMFEwSXU4M2RNNTBQZ01jKzRNWGtIbjJ5MFo5MmNNaHcwVlNJckRxY1pWY3A0ZmlLSEo2MDZaeXExU0tIMkFnblRWZTdTbE5JWE5ERGZvZUxGUStQaDlPU0JrbW1KUlExU25JdERqSmxOTzlzTmFQV05xTnZpNzciLCJtYWMiOiJmM2U4YmVlYzRmMzdjMWZlOWYzNGU4ZTg2YzRlNTE3Njc3ZWQ0ZGE2YTE3MTRiZTE0ZGU5M2E5ZDA0OWFhZDBmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRpNnYrZnVVcnJPY1ovSS9kbkVuWVE9PSIsInZhbHVlIjoiMjBUTnFhalNram8xaDE2RXNZQ2cyejN6cUtMM04vR3NPZVFCay8zeERVL0pHNWFENnFNK2RsdTJ2b2xIWWpoNVNGL1NmbEV3ZUVxTk9PVkFLd2V2d0ZYUjBSTE1MMVJPV0V3Q2VVWVVCZDJkQmdkcGtoOWJZVnZhdlZ4S0tlMHp6SVN4YW10eE0zc1FMQTNzWjdlNXNUMUhGWWZYTGlJelZ5c1htVWxiaVVwbndkbHRjMlhra0YzbWdIbnYxR2NVMk1Yb0Y3SCs3OE1DRzd4aTFWZy8raFdyY1VVd3oyUFk0UDNnWXg5UlZNNzhJZ08ySW9jSktVZ2dJMVBVcnJ4SFlrRW1EWGlDR3ExNWxseTBBejNXdU9OSUo2amhRYlNFSGE5bzkreWVESlhIeERuT0pZd3FNczBTNVI5bGRRWG45b0VtcEs1RkppekZQUmVtRGsrT1AycEQwVHVPOWZ0TTlpVEVFd3hnYS8yQlhMSGNPQ3lUR0ZPZnF6UTZRNWtxRzhrNFZ1aktxelpxSTR0S0traEcrUmRFT2kveXZyY0k1UXFhRW5pQXVhbmN1eEFHVnY3cXFQSlM5RHVGN0FhUG1NRHM1NS90bEw1akNENFVISmhyWkFub0RBejRHeWk1d1hlQysvcGxzdk5zY21qQ0dWN09pdnJoRnZmVktNY1MiLCJtYWMiOiI2MDY2OTViNGYyNTcyYzg1NTZmMzNmNDdlZTkwODg2MzA2Mzg2OWQ3MjEzNjc1NTMwM2YwZmU1ZGViYThhZTBlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:43:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1880522058\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-29130194 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"84 characters\">http://localhost/financial-operations/sales-analytics?date=2025-06-01&amp;warehouse_id=9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-29130194\", {\"maxDepth\":0})</script>\n"}}