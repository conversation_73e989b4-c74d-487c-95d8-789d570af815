{"__meta": {"id": "X584194737301faacf04a7e3b4b79d9e4", "datetime": "2025-06-28 15:49:31", "utime": **********.453763, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125770.95348, "end": **********.453779, "duration": 0.5002989768981934, "duration_str": "500ms", "measures": [{"label": "Booting", "start": 1751125770.95348, "relative_start": 0, "end": **********.378313, "relative_end": **********.378313, "duration": 0.4248330593109131, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.378325, "relative_start": 0.42484498023986816, "end": **********.45378, "relative_end": 9.5367431640625e-07, "duration": 0.0754549503326416, "duration_str": "75.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46281288, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0077599999999999995, "accumulated_duration_str": "7.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.420586, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 24.613}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.431701, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 24.613, "width_percent": 7.088}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4353428, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 31.701, "width_percent": 12.242}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.437944, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 43.943, "width_percent": 38.789}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.443089, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 82.732, "width_percent": 6.057}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.445105, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 88.789, "width_percent": 11.211}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-68588948 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-68588948\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1016805942 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016805942\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-567009042 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-567009042\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-567007455 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125735645%7C25%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndCa2Z5QytJMVJialhianBrQUNFQXc9PSIsInZhbHVlIjoiL2tNLzAvRHVDd3QzcGVvd0lUd0pFRFhybGZ3Z0ZEN2dEZ0RsMDR1ZGcwU2Y5Wml6cUkrNmNpOTBHc1VYUzR6djV4RU1wRVVDODFIVjZ6c043YnNZMWhSUk1tdjQrcjdjVndEWjdHT0V1MzFRQVQrOFJseDk3UGRoT3Y0anp2R2lVdkp2TnZMVTVMOHdvOUloaTZIZWZyeEIxWDhJdGdWTDJEUmVqK2ozS0dMTndHYTh0ZzhlM3lSY2ZvYVY5MnVONmxnOTA0ZHZ0QmFkd2QxLzdtQ1h4eE5yaVRqVTgxZmZITk1DQzcwUFVRT1pCeGhmRkZCUEI3ajJ2cXZ0SllYSFdaSXJKRW1IT09vZ2RPOUM5MFczVHJURDFlcldOOEN3YXpONis2cG5kVzVPRDlFU29lL3Y3VHBNRXdmVkZOeUVQOFkrMVl0Y0liUkNhczFMRUIybFJ3YmpvZHpkSVcyMnZYTldhaDNTMU83dEVLbCtOS2NkNmhKeFpRUkpUNEx0WkVqcERwQW8zMzRXN0c3cnMyN3pSa0VhQ1dJK3lEMkdRTEhTVTZoN0pBNkRNYk1rRS9ialp4SE9ValNObU5sTU1lVnpBdENwbkI1L1J0NVBCYnlkRVBIZXhBcnloeXpxQkNHMHYvOHZDVGJhUlZ6dm5ZeFpwOG94MlpzV210SlQiLCJtYWMiOiJiY2U5YjJkMjRjOGEwMmI4YjEyMDM1NGY3YWU0MTE2MTYzZjRkZDE4YmU4NmQ3NGNkM2Y5NGQ1NmY0MzI3MGE4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InNZZnJ2eUN4US9rejYyTEwxN1M5ZVE9PSIsInZhbHVlIjoidVlPYzRWaTNxNkVvYzRjeEJLZWh1ZHAycyt6Y3ZobjE4MkFuQ0RtVklkeDh6czNwRk42cktNM1VscGp4YytQUldjdzR4bzFONUZWV0xJajRVU1VIZkZxWktidHlIR2lVYkFxVFA3VUJVSm1FcWZsaGJoRG9pSlNZNkcrZ1ZFQWxwSWNhK2c0eCtsNE5SMHhTU2thSjZ0M2NLazJwSUVhalk4Ym1vbW91ZGFobVkvcXpUTjgvYkduMGp3YytkVGFFcnNsb3VqVDlCUWtTaGt3Yy83bnJzNFIrenlkWHYrZjVDL0pXWnZhc1pmYmtpbEhGNE1vZ0Vkc2hMK0JBWkNEVHRBRmlHT0tCa3pVWkduMnVMYXlXTG5iZktFOWlxT3lOZ2JQMDR3TnJXa1FCRlpOK3Ayak15UXF6N2J4Nys3SGt3TitDNFJhVkdXZHlKSDhhVDNyTHppWHR3TENHTHVxUzFVSkNZQ3VUdDc5czA2RWpVMThBMlJnNlYxRmlycEk5Mk5hZFhIUGpZbHFJL0lQY0xKM3JHZ2g5UGZLTldCaFp6bStEZjhKdTY3SmdqY2xDOFN6bVUxVDZVSENWNThqNUUxSjlSb2RUdTYwWldMSUpzSDhGczBWMUxNSXFnRjJQNDF6MGkrRE81UDVZdXJVR1ovTzA2T2tsZmlIZ2hRemQiLCJtYWMiOiJmNWRmOWQ0NjIwYzk2Mjc4MjQ5ZGNkMzIzNDEzNTBmMDJkYTJjZTk2ZjFmOTZhNDgzYmIzN2E3YzliM2QzYjcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-567007455\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1062147618 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1062147618\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkE1VlVSVmFnVWNRZVRXU080VS9abnc9PSIsInZhbHVlIjoiRDF5OVFoRUNMNmp3eGNrQjByWlo1dXJrWGtQK09HbkkvMCtRdWg2Nmg1dDlnS0ZQL0J4YXUyNTRkNDZKTWx5UUdUOTJnYW1jRFZVN2RMRXR1SUZjNm9lWkZKVCtkb0Y4aFN0YnhLbGlMSlQ0d3YvUUt0eUdZYVF2dFM0b0piZTR6VmxZUUUzaVQ2VjA2UUxuQ3JoOFdiUTRGV200cE41dnBwYVdYanNtZ3FDd1NUNWtuZGJ6aWRodUNMVS82Mm5QanUvdkNKMGxjalZ6d21XZG9YS0FhYnlhNmk5TWcxdERJY05mSHZoSjR0a0hjUVVnU3owbVZMODA4bFVDT2NlQ2FyMXI2LzNGNXRiMzUrRDhwcHFzT21lZFVuOE9sWjJkdWxIRUUrOUM3NzRTYnpTUURTK0dCdGJUNlI1Tk5PZDQzY3VOQ1orQzFiVFUxVVFKWWRLcE9BbE9rU0JpYzhvZjZFVmUzUFlVTGtHUHhLUGpMQlVFR2RvbWZuVUlpWVdkcFBLaTJQUUtJSFRoekJqQnJiVzB4M0hKclJDQ0ZhOExxOUZLazJhSlZNajNOQ2QycTF3cXJPQURGNmw5eHYwQ0F0ekJNMDNMOUZ0ZDRTYUZkR2o2UFdmY2FhLzcxdU42Ylhldk9hOEFWaHlYSUVZNCtuY2M3b0tQODA5bitkay8iLCJtYWMiOiI0NTQzZDI2ZTc5YmZlNmRjM2I1YTdkMGE0YjEwOTlkNzI1MDFhOTUyODA3ZTYwYTU4YjM0M2Y0NDg3ODlmNDhmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imtsekl0djluZXA5cUZrU2JLRjhxL3c9PSIsInZhbHVlIjoicHBvcnZPQ0xsamg3c3FGVG5BMi9xNFB0T2l5Y0Y2emR3R2tLeUd1aDNrTFNKSCtCZVFuSlY5Y0pGSHVPZzFseFBsbGxDR1B0ZGE5bURhYWF3VGdZOFhlV1JKVS9WblVOMDBMaVRHc1p4TTBEQlRMdXFuN1AvcW9BRFZYWUtwYkVsUW1kVytJV0ZwR1A3ZzdmQTF2NFlBTUJQY0ZkaVVBVTJHcndLQjlTbzZrZVdEZWJJdEd6akVUcUd2L3JQL3NuMVNBbEIveHZLT2ZrK1B6UmpwMHMxWC9ialJVWjMzVnltbFRMc01KanpWYWJtQ21GWnFnTEdQK2tUd29KQ2pHNkdiSHVlanpiOU5POGczLzIrR3lZZnM2V0xtTGlaNnNUMmoyWFRyV1NBM2lEdlhqb1dVTXJkK1hXNVczSVhUZEpQWXZLK1JjNWdSbHppeXVoYVJsYWRrOGJmT1NzK044cTM3TVlMZ25WWjMvRng2VVNMYk9xdHNTVFhzYlFyblVkU2ErUmNGejVNeXgwYldtNFlxSGRnN29vQlZXb2JRYS9FQkJFdC9ENlFwMzE4Vnd4SHh6YVljTXpDVWR1MVZhM2J0YlhXN2YrbGZjQ2hKZnBDQ3hRdzVqTjRuaGE2RGRidU1vZWVtajVwYkxTbWRWNjJFOVFFQ3BCZHQ1YjhPdlYiLCJtYWMiOiIwZmE4MGIwMjFkMWYxYjExZGFkNTgzNzdmNzM5MjM4YTRjZGVmOTkwZjc0YWIyNzdmMmE3NWU4OGQ5NzI0NjVmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkE1VlVSVmFnVWNRZVRXU080VS9abnc9PSIsInZhbHVlIjoiRDF5OVFoRUNMNmp3eGNrQjByWlo1dXJrWGtQK09HbkkvMCtRdWg2Nmg1dDlnS0ZQL0J4YXUyNTRkNDZKTWx5UUdUOTJnYW1jRFZVN2RMRXR1SUZjNm9lWkZKVCtkb0Y4aFN0YnhLbGlMSlQ0d3YvUUt0eUdZYVF2dFM0b0piZTR6VmxZUUUzaVQ2VjA2UUxuQ3JoOFdiUTRGV200cE41dnBwYVdYanNtZ3FDd1NUNWtuZGJ6aWRodUNMVS82Mm5QanUvdkNKMGxjalZ6d21XZG9YS0FhYnlhNmk5TWcxdERJY05mSHZoSjR0a0hjUVVnU3owbVZMODA4bFVDT2NlQ2FyMXI2LzNGNXRiMzUrRDhwcHFzT21lZFVuOE9sWjJkdWxIRUUrOUM3NzRTYnpTUURTK0dCdGJUNlI1Tk5PZDQzY3VOQ1orQzFiVFUxVVFKWWRLcE9BbE9rU0JpYzhvZjZFVmUzUFlVTGtHUHhLUGpMQlVFR2RvbWZuVUlpWVdkcFBLaTJQUUtJSFRoekJqQnJiVzB4M0hKclJDQ0ZhOExxOUZLazJhSlZNajNOQ2QycTF3cXJPQURGNmw5eHYwQ0F0ekJNMDNMOUZ0ZDRTYUZkR2o2UFdmY2FhLzcxdU42Ylhldk9hOEFWaHlYSUVZNCtuY2M3b0tQODA5bitkay8iLCJtYWMiOiI0NTQzZDI2ZTc5YmZlNmRjM2I1YTdkMGE0YjEwOTlkNzI1MDFhOTUyODA3ZTYwYTU4YjM0M2Y0NDg3ODlmNDhmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imtsekl0djluZXA5cUZrU2JLRjhxL3c9PSIsInZhbHVlIjoicHBvcnZPQ0xsamg3c3FGVG5BMi9xNFB0T2l5Y0Y2emR3R2tLeUd1aDNrTFNKSCtCZVFuSlY5Y0pGSHVPZzFseFBsbGxDR1B0ZGE5bURhYWF3VGdZOFhlV1JKVS9WblVOMDBMaVRHc1p4TTBEQlRMdXFuN1AvcW9BRFZYWUtwYkVsUW1kVytJV0ZwR1A3ZzdmQTF2NFlBTUJQY0ZkaVVBVTJHcndLQjlTbzZrZVdEZWJJdEd6akVUcUd2L3JQL3NuMVNBbEIveHZLT2ZrK1B6UmpwMHMxWC9ialJVWjMzVnltbFRMc01KanpWYWJtQ21GWnFnTEdQK2tUd29KQ2pHNkdiSHVlanpiOU5POGczLzIrR3lZZnM2V0xtTGlaNnNUMmoyWFRyV1NBM2lEdlhqb1dVTXJkK1hXNVczSVhUZEpQWXZLK1JjNWdSbHppeXVoYVJsYWRrOGJmT1NzK044cTM3TVlMZ25WWjMvRng2VVNMYk9xdHNTVFhzYlFyblVkU2ErUmNGejVNeXgwYldtNFlxSGRnN29vQlZXb2JRYS9FQkJFdC9ENlFwMzE4Vnd4SHh6YVljTXpDVWR1MVZhM2J0YlhXN2YrbGZjQ2hKZnBDQ3hRdzVqTjRuaGE2RGRidU1vZWVtajVwYkxTbWRWNjJFOVFFQ3BCZHQ1YjhPdlYiLCJtYWMiOiIwZmE4MGIwMjFkMWYxYjExZGFkNTgzNzdmNzM5MjM4YTRjZGVmOTkwZjc0YWIyNzdmMmE3NWU4OGQ5NzI0NjVmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}