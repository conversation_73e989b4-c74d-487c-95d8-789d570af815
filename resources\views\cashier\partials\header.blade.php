<!-- Cashier Header -->
<div class="cashier-header">
    <div class="row align-items-center mb-4">
        <div class="col-md-6">
            <div class="cashier-info">
                <h3 class="mb-1">
                    <i class="ti ti-cash-register text-primary"></i>
                    {{ __('Cashier System') }}
                </h3>
                <p class="text-muted mb-0">
                    <i class="ti ti-user"></i>
                    {{ __('Cashier') }}: {{ Auth::user()->name }}
                </p>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="shift-info text-end">
                <div class="mb-2">
                    <span class="badge bg-success">
                        <i class="ti ti-building-store"></i>
                        {{ $warehouse->name ?? __('Branch') }}
                    </span>
                </div>
                
                <div class="shift-balance">
                    <small class="text-muted">{{ __('Shift Balance') }}</small>
                    <h4 class="text-primary mb-0">
                        {{ number_format($shiftBalance ?? 0, 2) }} {{ __('SAR') }}
                    </h4>
                </div>
                
                <div class="shift-time">
                    <small class="text-muted">
                        <i class="ti ti-clock"></i>
                        {{ __('Shift Time') }}: {{ $shiftTime ?? '00:00' }}
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="quick-actions mb-3">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary btn-sm" id="newSaleBtn">
                <i class="ti ti-plus"></i> {{ __('New Sale') }}
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" id="holdSaleBtn">
                <i class="ti ti-pause"></i> {{ __('Hold Sale') }}
            </button>
            <button type="button" class="btn btn-outline-info btn-sm" id="recallSaleBtn">
                <i class="ti ti-refresh"></i> {{ __('Recall Sale') }}
            </button>
            <button type="button" class="btn btn-outline-warning btn-sm" id="shiftReportBtn">
                <i class="ti ti-report"></i> {{ __('Shift Report') }}
            </button>
        </div>
    </div>
</div>
