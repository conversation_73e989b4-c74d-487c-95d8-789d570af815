{"__meta": {"id": "Xd9f8b4990ce996cfd3e998c609bfceeb", "datetime": "2025-06-28 16:34:32", "utime": **********.360064, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128471.850409, "end": **********.36008, "duration": 0.5096709728240967, "duration_str": "510ms", "measures": [{"label": "Booting", "start": 1751128471.850409, "relative_start": 0, "end": **********.293692, "relative_end": **********.293692, "duration": 0.4432830810546875, "duration_str": "443ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.293701, "relative_start": 0.44329190254211426, "end": **********.360082, "relative_end": 1.9073486328125e-06, "duration": 0.06638097763061523, "duration_str": "66.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46462864, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2553\" onclick=\"\">app/Http/Controllers/PosController.php:2553-2587</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.011479999999999999, "accumulated_duration_str": "11.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3328822, "duration": 0.01117, "duration_str": "11.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.3}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3523881, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.3, "width_percent": 2.7}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1755786799 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1755786799\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1504076758 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1504076758\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128238007%7C50%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllXeVRneTRSN2Z5d2VVV3Z1TGtTQVE9PSIsInZhbHVlIjoidEtvcFJWMlhpTHJmYmwxMTJJSWlNMHg3eGFkQ1RhOWNES0hibW9IMFdKZGVFSkFaRVV1dzQrSCsxS3lRTEx5RUw1TEJnZ2twWE94MmFYdXRPdERNSUEwMllGUWQxbFZsZVBsN28xaGFKaGgyNGlrUFU4YWcrMnVhYlVrbm5OUGJpZWxvU0lDa2M4eEpVZkFKTDZ5SXFCcFFaYm1oa1QvK28vMExuWjdiRGZlZUtPeUZta0ZlK3lwdWZ5a2krcFNudEgxSjJZeGE3T3plM05SY2JDbVZFQnF5Q3FVZklmandqeEhRc1p3RnRRM0t5dEY0OGZieFBMMHhCaFp4aTE3b0VxWlI3THRuRTVzVFFMWXJwNmh6eGY4MCszTVRmNk5ZOUU4czhlZlBhU1ZXUzBNQkhoSGVtdHF3Z2huU0x5V1VOSXdwWDlWc0psd1d6c0Y0emJDaVpvRUU5UnlxbzJrVFRLRytndHdheVZjalZqY2d1QmdEWHdiMVF3bU1hMCs3Nmhxcks4b3QwZWQ1Q3JHc0RyeUJZM2NnbDc4cnA0WjNHRm1QWC9YWEpNNXJEK2VSMldMa1UyRmhKQitmUy8yOUFXMnhvN085ZDBJTlExTTJuN0VvV3BnSFFPUnliSk1yUGtQSGpXKzZZSjVyREwwYUEvNWhqRlM0RTFwSHhUcVQiLCJtYWMiOiI0NWM2Y2M0NzExZTY4MGQ5NWJmNTg4NDFjNzczOWRlMmNjOTFjMjI3NTFhZWMxMTA2OTMxYTg5YTU5MTNkNGFlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjN6WXBORGtzN3FoeUMvaWIxalFVREE9PSIsInZhbHVlIjoiQVZ6a05hcFJMWEtqZE8zZFdwSFNDTWVKZGw0Yisrc1NOQjFhd3Z2NmhNSStKOCs4NnM4dHhLd1E2cGQ2VFJJbVVWQ3dYNEFEbjFVMmFIU0hJc01FV2NlZmV5SG5EM2ZBdHRucFVUOUFrM1JMbWNGcHQ0bWEyOHk1bm1wT3JEVFRySlhJNWJzTXJ1MksvMXRNZ2MwYXdRbWQ2dWVWcnhPUkJoekJ0NDZwNXRvSDBHK1ROR0RFbHExL21wajdhclVpam1GTEVEMVhycThZZXJRWFZIaTg4VVY5L2ZpMG9HeWFWMzV5M0RYWXdsNW5NT29ja0I3VTVtR1J4R2ZoM3dWWHYwR3ZHWTYxTThXbHhzMk4vQ05HanpZTW9Ea0xtcDJtYUJJbnlMZ3BRMkhsUUx1anpiejdFTWhJTGNuS3lJb0VGVWYvekdPbWdIcTVSWFlPUzFQb3h6WFE4TENPS0lybWJDUVFIQUxxaG81WVlYUUNkY0VRRlJva2dPMWZOZHV0NkZTYmY5RCtxR0EyWWYzQUl3eE5WTm5BRnlJQVFaclgyMEJScExUbmRYdDZaZ2NmVC9KTUZ0NVlONjdVYkJUdC9oemVRUkxFMzQrYTVjTlFTVTRGRlphbkNtcExRcENBTTh3cGdHS2l5Vm1JU2VyR1U0VkYrSDF4WnRaT3FXREsiLCJtYWMiOiJiYjJjZjA4MzA0ZTc5MzI4MjE1NTFjZWJmOTMwMmY4ZWVmMmQ1ZmNhNjFhZWZjNTY4MGNmYjg2YzVmYWUxOTdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-800506161 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:34:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImF5TnVQQWZyelhlemJZOWRnQS9ZdkE9PSIsInZhbHVlIjoiQnVuaThWOUM2VnJnblk0S2JTaUFIVkthUk90VmozTmlKSWxnekZqL0JUTloyeG1wTW1QcU1NbEtENUhFclZIa3czUUpFNkgrZXRMVjR4aDlWdUJaNkxYRUpSTFNXZUVIUXRHSXFFcHNXcjRQUUJZM1YwblhreTNCamhRMzY4Yk84MC90eGRGKzE2LzJuYlk5OHgrUmV5SURyazRHLzdmOGVoaUZjcXVLYTdPTTlJQ1F3T0plb2liRG1nd2lqdU1TeG9FL2tHUTVnUHM4Y2gzaWFicGVkUVA1UFI2N2VjNE5rTUV5UFlyME9OaEUwelcxSVRSTDVIUElscGlackR1RnB5YnV6Z3lzSGtqVWs1ZSsrMWs4MFNHcndIWG90SXNuM1dydUR1Z01DTGQ4d1NVZERrV0V4NGFqWTFNMlVEMjhxeFFybEQyeTA4L2wwQTNhc3B2dUFJbTVMT09McDdlQldUYUVkSEpJV0FLczBZdGtiTHpXWEFKZ2w2QzJyTW9HMXMxWm8ydDVYQ0l1QTFyVU5aUG1MWGlxTHJYOC8zKzZkd21meDFJU2FEWUdRSnBpdXhBS2l4L05LYVplRzEvV0tZQjhyZGNhclczL01BQTNmcTVXM2NLdjlWVHdEOXNrdllCRDZISEpVNmN4Y25ueThJWmwzandISSsyNzE5WVciLCJtYWMiOiIxMzBkYWRkOTJjNDg1NjVlODMyOWIxZDUwYjE2NGJiMTUwM2ExZjI1YzU5NzE0ZjQ1NzNjYmYxMjJlNzgwYTAzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpTSWVxL1FIRTBic056ZHB3dFlObHc9PSIsInZhbHVlIjoiNjJxbFY5R3RKbFRNZGczYVp1RzJjWXZWN2lqc2RMUHQvYkkzTHhlaGJjeVhmaHhxT0dCUXh4c1ZJWXFjM3BjYi81Z3ZxbmI5WDNodU5HY2xrR1M4UExIcjQzMEVvZ2JCajd6QUlDVVpqT2R6Ym9OQ0V5VDI2WjRhTWpaMEo5NkFRY3dYaXVla3Y0VldSMmFNN2dFVG85enY3OUgrSEtaV1FFVFAxSFlkNE4xTnl2cmlZbXFZbDNWQUEzMGZBOEZXS1J1L0UrT2VreGVwWkxWSDJQUlBZUk8vYkdSakxObEtjYjNjakI3TGtsTDdvVmZpOVpyMFhGOEtOVkRoakQvVC8vcjdTQUVjT0hzUkJvRjNLblQ5OWthdENYalR3bFRsNVQ2elcyeFFtdngzZEkwbTZyell5SFJiak1XSHJTZk4wb0dIdmpYSitXTjBQK0laYkVudlZBdnpBMUZhREsrSlNLUHBPTFVLb01iMGNGbklQR3NJVTdybmhtZExnaEorVDFGMWFkdFI3MGZvbUZmSC9pU3Q2NjBhVkZ2M0NDNzlRL2MyYlh0Zmg2bHB3UlcxYjgvSmNxRCtMWmlEejVDK055YllKSTdtY2QzSXhmOWIyaTNJS1diM3lTdzNNR0NGWmcvaVIwZk5ZNjVSOU5vaHpWOG9IMUd3TmMwS0JwaFoiLCJtYWMiOiJjNzEzYWJjZTBjYzQ5YzgzZWZjY2M5NzE2Nzc5MTFhMGQyYTc4ZjE2MTg0NjU4M2Y3MjE2ZTZkNzQwMTc5MzM5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:34:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImF5TnVQQWZyelhlemJZOWRnQS9ZdkE9PSIsInZhbHVlIjoiQnVuaThWOUM2VnJnblk0S2JTaUFIVkthUk90VmozTmlKSWxnekZqL0JUTloyeG1wTW1QcU1NbEtENUhFclZIa3czUUpFNkgrZXRMVjR4aDlWdUJaNkxYRUpSTFNXZUVIUXRHSXFFcHNXcjRQUUJZM1YwblhreTNCamhRMzY4Yk84MC90eGRGKzE2LzJuYlk5OHgrUmV5SURyazRHLzdmOGVoaUZjcXVLYTdPTTlJQ1F3T0plb2liRG1nd2lqdU1TeG9FL2tHUTVnUHM4Y2gzaWFicGVkUVA1UFI2N2VjNE5rTUV5UFlyME9OaEUwelcxSVRSTDVIUElscGlackR1RnB5YnV6Z3lzSGtqVWs1ZSsrMWs4MFNHcndIWG90SXNuM1dydUR1Z01DTGQ4d1NVZERrV0V4NGFqWTFNMlVEMjhxeFFybEQyeTA4L2wwQTNhc3B2dUFJbTVMT09McDdlQldUYUVkSEpJV0FLczBZdGtiTHpXWEFKZ2w2QzJyTW9HMXMxWm8ydDVYQ0l1QTFyVU5aUG1MWGlxTHJYOC8zKzZkd21meDFJU2FEWUdRSnBpdXhBS2l4L05LYVplRzEvV0tZQjhyZGNhclczL01BQTNmcTVXM2NLdjlWVHdEOXNrdllCRDZISEpVNmN4Y25ueThJWmwzandISSsyNzE5WVciLCJtYWMiOiIxMzBkYWRkOTJjNDg1NjVlODMyOWIxZDUwYjE2NGJiMTUwM2ExZjI1YzU5NzE0ZjQ1NzNjYmYxMjJlNzgwYTAzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpTSWVxL1FIRTBic056ZHB3dFlObHc9PSIsInZhbHVlIjoiNjJxbFY5R3RKbFRNZGczYVp1RzJjWXZWN2lqc2RMUHQvYkkzTHhlaGJjeVhmaHhxT0dCUXh4c1ZJWXFjM3BjYi81Z3ZxbmI5WDNodU5HY2xrR1M4UExIcjQzMEVvZ2JCajd6QUlDVVpqT2R6Ym9OQ0V5VDI2WjRhTWpaMEo5NkFRY3dYaXVla3Y0VldSMmFNN2dFVG85enY3OUgrSEtaV1FFVFAxSFlkNE4xTnl2cmlZbXFZbDNWQUEzMGZBOEZXS1J1L0UrT2VreGVwWkxWSDJQUlBZUk8vYkdSakxObEtjYjNjakI3TGtsTDdvVmZpOVpyMFhGOEtOVkRoakQvVC8vcjdTQUVjT0hzUkJvRjNLblQ5OWthdENYalR3bFRsNVQ2elcyeFFtdngzZEkwbTZyell5SFJiak1XSHJTZk4wb0dIdmpYSitXTjBQK0laYkVudlZBdnpBMUZhREsrSlNLUHBPTFVLb01iMGNGbklQR3NJVTdybmhtZExnaEorVDFGMWFkdFI3MGZvbUZmSC9pU3Q2NjBhVkZ2M0NDNzlRL2MyYlh0Zmg2bHB3UlcxYjgvSmNxRCtMWmlEejVDK055YllKSTdtY2QzSXhmOWIyaTNJS1diM3lTdzNNR0NGWmcvaVIwZk5ZNjVSOU5vaHpWOG9IMUd3TmMwS0JwaFoiLCJtYWMiOiJjNzEzYWJjZTBjYzQ5YzgzZWZjY2M5NzE2Nzc5MTFhMGQyYTc4ZjE2MTg0NjU4M2Y3MjE2ZTZkNzQwMTc5MzM5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:34:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-800506161\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}