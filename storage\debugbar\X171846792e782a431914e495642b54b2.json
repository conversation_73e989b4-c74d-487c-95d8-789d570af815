{"__meta": {"id": "X171846792e782a431914e495642b54b2", "datetime": "2025-06-28 15:08:52", "utime": **********.5162, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.076587, "end": **********.516227, "duration": 0.4396400451660156, "duration_str": "440ms", "measures": [{"label": "Booting", "start": **********.076587, "relative_start": 0, "end": **********.46399, "relative_end": **********.46399, "duration": 0.3874030113220215, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.463998, "relative_start": 0.38741111755371094, "end": **********.516229, "relative_end": 1.9073486328125e-06, "duration": 0.0522308349609375, "duration_str": "52.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46254592, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0018, "accumulated_duration_str": "1.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.498953, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.509356, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 75, "width_percent": 25}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1350058537 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1350058537\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1629491869 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1629491869\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-562088744 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-562088744\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-519404356 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123308213%7C8%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImttSU5QYTV1cGw4UlM0WWV4L2dabWc9PSIsInZhbHVlIjoid3NtTGJ2RFZ1ckFabFQ0cFRKbTFSTVZWTWJIRUtuUjdXbHBiQUJRRVNNaDE3QnA5aVdUTm0xV09NMms2ejhEek9rR0VWNk9JZFlYeWg5ZTN1Mk02Vnh3RG0wWXpvNStwdFEzRDNpNnhPUUxmR25IajRpUHBIQXlyUnlQTW1aTzJISERqRnVKQnVHNzZiSDJRL1dQNTVsRGtNYllxNkdrKzhoZGRDUStNcEZMWTFjdytueGZibnlZS1ErNkNOMnNPOCtPL1ZCUGhWd2U5aEhXNHIvd3ZsWEViVDZ6Vmp3VGVGTTd2REhJQ1VORXJFTWlDWFByNnBEUkFVb2VsZmw3OGZRTXo1ajFiM0s5MTZGWFk3UnBMSU1WcERkQmdhYUFaaGVDUzFobXlNQnJWcFFzVGhaKzJQeDJQZnUyZmdZSytSMnNoV2h2U0F6VzRUeWlxZmNqRyt2MHphNnVDZG9RcUt1UEYwOHIwZGljd2V4ciszeHo2OHFEZXR3bTFjWmorSlo3d01EN2llOEYrMWNjTXFYVXd4RkNLQVFCWTFxaSthNXlJM1doQjdmOGVXS2N4WG1aWkYxZ1U3dUo1SEtQT25LSXFHOWU3MlJack5ickpwWUVBYm9uMXpSRU5CQXhqTXY4WERjYWlFVVN0V0ZDVjVxdnd5OXU0eEkzUTJBQjciLCJtYWMiOiJkMmQwMDFkYTY0YWExOWUxMDc0NjcyMTVhMGU4MzRiMzFmODJmZjczZjIxZjgxNzMwOGJjZjg1M2QyMjFmZmM2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBnS0NEVzNUaUhPYlZidnQ1a2k5NVE9PSIsInZhbHVlIjoiSm9LcHlERXlQcUZ5dDhmTU1WOExnZHlLdFNrUVV4aDI0d2Ivcy83VUxSV2FNbWdZQnNaRGJYYURIZXhsOEVtdnB3QXQ5UldkSGl1V1hFMkNVQk00WnN5QTRENlFtSVJYQnBvckZ1S21CeDJYWGVJL2lYNS9nZzNuNnd6MDJKSkRmZGVZQVErNmk4ZGRQa1lzL2I2eTh4SGcwYzRhbk9VWnd0cTRqZy9jTWFXdWFPM3NRRElKcDVsY2VRNGkzZlhERHlPS2lZcWdOK1pBaWo1YkN4QjBVVVA5aWFMVk9GN2YvL3RBYWpnZFNWK0xkNWRUcW96VEFQMXRveEh1ZFlOZEJGNjJKbzJJVzRESE9yZVc5YmgvK2lBdEk5aVNDNyttVmgrODNMSGlsb3JMQmRqd3ZXYnpFWG1IZVVJSjFqRGh5TXBQSEJHcFE3ejlqVXdHWUlUcnlFS1BFYXZqaDk4a2ZLRmxjY2x3ZEtrYnlYd1dYRWZqeHRIOUx5bjhlR29MUzZCRWRiMHY0WitEdU1hTG43YzdCZldtSG0rMGdHbWE5aVMwYXNRTjR3cTZPT29GbDFQVWd3d2h6aitYbCtxTWFjdFhCdUh6VnVpd3ovaVNnTWI5czNLQUE0ZzFVMlQvSjFzcERxT1R4VEN5dGE4QVptVWM2d05hMGM2dnI2ZlYiLCJtYWMiOiI0NmVhMjIzNWYzOWNlN2EwNDI4MDg4MzRlMGQ0ZDBjNDYwMzA1MTI5ODlmN2YzZWVlYzM0OTY2NDUzN2QyOWE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-519404356\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1616907757 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1616907757\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-191790491 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:08:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkF6a0xWWHJCdnVhSVJmT2VIM0NDa2c9PSIsInZhbHVlIjoiU0ZEaUtBT1pzbzM0Q0dTbVpIQUdYR25vaEkwVm1TbnIxQXNldUE4ekN2R0xnOURvTnJMb1hxa1RpSG5iRXRXZ1BxZXFlMHF0MWwzZGk3L3Mzd0lLSkplU0c1TGFWRXJnblhJV2trMnFwQ09TdzJNQmlPeit2ZlhlOGVva2JRR29nck5vTUFjNThkS2RrQmNDcys1ZTBnYnROTjJxb01VUHBkdEF4amV3aGVVZ3JpL0tVcFgvdzhEQ1Z0TEtYRVBxeTZCd0E3dEZZdXpGUzM5VkRpUlhFTC9Xc2h2aE0zWWIyb3FxRDJDRWlpeERYeFRaQVlCSWk0WjQrcW1NNWVOMzk3UHJzcjNVSWYweUVLSnFCTEFickwrWEVNM0lHakZOYWV4ME1kb2pGQ3ZLU0g3d1BOQ2JndnZyNlRWTElpcWlxSVRxMnNrOW8raFFPakc1YVNsSkh1WXB4a3RCWk10cllRU1o2R2c5enBrVUZla2Rvd3NtckVGMnU0K24xVldOWWVWdUhGNm1hQTEwbFZIdW1ybXgrZDFRZzg5emhYaUpQKzI4aVpEM3RtMnBHbnVOWlhvbi8rdVF3bjNwMzVRM3IvWmNnTTNoTXdLTGwzMFhUdDd1ZTFBN3BWZWJvem9CQ1VUdVByME1GZlNmNlRKdDd1NGtudFdsUE93ZHJEZXoiLCJtYWMiOiIwNzgwOWQzOGE1NWRjZmE3MjBmYzViYTRlMDE4NDQ3YTY3NmRmYzI3OGFlMmY5MWQzNTUwN2MxMWViZWU2ZTdlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikl5eWEwdHV0dlVhVlBCQU9FK1B5QlE9PSIsInZhbHVlIjoiQi9IMWttL3NzbC9QTXlqTnUxdGQ0ejRpU1g1UXZkRmVFSlBYUmNzczhsbVRxKy94N3VmeXBxa1ZQVHN3SjFqWEpxdDNUYVZwUjlOTXBjZFdFeFliS0dnWEVldEFFcXN0S2VUSS9zeExJVkFuNzFjYmlPdHRmVUNIMFAvdHVMOUtVaGdDazVqSHZ6VU56emk0eWJJWnZ6clBxUjN2MmZWWnU2anhjZ0hhUDZ4SnZ4WkRhNmN2VFVPZnpISVBOMUJVeXNud2xQUkU0N29rR0o3UGw1SmNjTXNQS2gwaGVYemNJNEU2TW1iYlNwZlFTMytZREV0MG5VRnRpNXZDUThlV0k3T1NnbHVHblNOUnVRODNMazgvSkQrYUswajIwQ0lpVWlUL1I0RmV6TE1JYXNOczNGTGhmNTBGaEtybWpCWm5rWmVMb1NPZjRzU0wvSjlyQlQwdmdjdTZ2Mk1PY3h0WGs5TWk0WUdaK0V1Q0kwOU9XNmI0QVdYb21oRFF2Y0t3aXRUaFFSdEx0b0dja0NjSktnTzY3STk0TU1rRG1zZFQyWWtKM0JkY0RsZnU0L21EWlVqYUd5YjhBN3djdENLV1ZJVFZNVnVFMlEvVFI5aFB4TEtzTitzV3YyOHBSZlh6M3pTVk1wM2kzbmxyUVpBUFlwd2RrVnlrMXZtQ3cyaWsiLCJtYWMiOiIyMzRjMTdmZGU1MzcyNzdkZjlhYWE3NTkxMDJiZjQ4ZmUyNmQ2ZjNmYjY4NjI2ZmRmMWVkMWM2OWEyY2U5MzM5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:08:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkF6a0xWWHJCdnVhSVJmT2VIM0NDa2c9PSIsInZhbHVlIjoiU0ZEaUtBT1pzbzM0Q0dTbVpIQUdYR25vaEkwVm1TbnIxQXNldUE4ekN2R0xnOURvTnJMb1hxa1RpSG5iRXRXZ1BxZXFlMHF0MWwzZGk3L3Mzd0lLSkplU0c1TGFWRXJnblhJV2trMnFwQ09TdzJNQmlPeit2ZlhlOGVva2JRR29nck5vTUFjNThkS2RrQmNDcys1ZTBnYnROTjJxb01VUHBkdEF4amV3aGVVZ3JpL0tVcFgvdzhEQ1Z0TEtYRVBxeTZCd0E3dEZZdXpGUzM5VkRpUlhFTC9Xc2h2aE0zWWIyb3FxRDJDRWlpeERYeFRaQVlCSWk0WjQrcW1NNWVOMzk3UHJzcjNVSWYweUVLSnFCTEFickwrWEVNM0lHakZOYWV4ME1kb2pGQ3ZLU0g3d1BOQ2JndnZyNlRWTElpcWlxSVRxMnNrOW8raFFPakc1YVNsSkh1WXB4a3RCWk10cllRU1o2R2c5enBrVUZla2Rvd3NtckVGMnU0K24xVldOWWVWdUhGNm1hQTEwbFZIdW1ybXgrZDFRZzg5emhYaUpQKzI4aVpEM3RtMnBHbnVOWlhvbi8rdVF3bjNwMzVRM3IvWmNnTTNoTXdLTGwzMFhUdDd1ZTFBN3BWZWJvem9CQ1VUdVByME1GZlNmNlRKdDd1NGtudFdsUE93ZHJEZXoiLCJtYWMiOiIwNzgwOWQzOGE1NWRjZmE3MjBmYzViYTRlMDE4NDQ3YTY3NmRmYzI3OGFlMmY5MWQzNTUwN2MxMWViZWU2ZTdlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikl5eWEwdHV0dlVhVlBCQU9FK1B5QlE9PSIsInZhbHVlIjoiQi9IMWttL3NzbC9QTXlqTnUxdGQ0ejRpU1g1UXZkRmVFSlBYUmNzczhsbVRxKy94N3VmeXBxa1ZQVHN3SjFqWEpxdDNUYVZwUjlOTXBjZFdFeFliS0dnWEVldEFFcXN0S2VUSS9zeExJVkFuNzFjYmlPdHRmVUNIMFAvdHVMOUtVaGdDazVqSHZ6VU56emk0eWJJWnZ6clBxUjN2MmZWWnU2anhjZ0hhUDZ4SnZ4WkRhNmN2VFVPZnpISVBOMUJVeXNud2xQUkU0N29rR0o3UGw1SmNjTXNQS2gwaGVYemNJNEU2TW1iYlNwZlFTMytZREV0MG5VRnRpNXZDUThlV0k3T1NnbHVHblNOUnVRODNMazgvSkQrYUswajIwQ0lpVWlUL1I0RmV6TE1JYXNOczNGTGhmNTBGaEtybWpCWm5rWmVMb1NPZjRzU0wvSjlyQlQwdmdjdTZ2Mk1PY3h0WGs5TWk0WUdaK0V1Q0kwOU9XNmI0QVdYb21oRFF2Y0t3aXRUaFFSdEx0b0dja0NjSktnTzY3STk0TU1rRG1zZFQyWWtKM0JkY0RsZnU0L21EWlVqYUd5YjhBN3djdENLV1ZJVFZNVnVFMlEvVFI5aFB4TEtzTitzV3YyOHBSZlh6M3pTVk1wM2kzbmxyUVpBUFlwd2RrVnlrMXZtQ3cyaWsiLCJtYWMiOiIyMzRjMTdmZGU1MzcyNzdkZjlhYWE3NTkxMDJiZjQ4ZmUyNmQ2ZjNmYjY4NjI2ZmRmMWVkMWM2OWEyY2U5MzM5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:08:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-191790491\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-328202676 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328202676\", {\"maxDepth\":0})</script>\n"}}