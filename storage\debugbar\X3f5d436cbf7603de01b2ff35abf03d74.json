{"__meta": {"id": "X3f5d436cbf7603de01b2ff35abf03d74", "datetime": "2025-06-28 16:19:16", "utime": **********.714169, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.277836, "end": **********.714183, "duration": 0.43634700775146484, "duration_str": "436ms", "measures": [{"label": "Booting", "start": **********.277836, "relative_start": 0, "end": **********.638473, "relative_end": **********.638473, "duration": 0.36063694953918457, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.638482, "relative_start": 0.36064600944519043, "end": **********.714185, "relative_end": 1.9073486328125e-06, "duration": 0.07570290565490723, "duration_str": "75.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46425152, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2441\" onclick=\"\">app/Http/Controllers/PosController.php:2441-2475</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.018810000000000004, "accumulated_duration_str": "18.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6778219, "duration": 0.018170000000000002, "duration_str": "18.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.598}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.706005, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.598, "width_percent": 3.402}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1445369532 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1445369532\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1421631223 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1421631223\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2073008827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2073008827\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-924614621 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127552721%7C36%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlI3cjlXdXQ0ZXBDOXE3UGdGMW5JS2c9PSIsInZhbHVlIjoiOXY3bnRETUFmMVJQR3dkNml3b3RYVHZkSm5PVGpNdVNXbTMxa2NJZEpZOW5PalR3MWhJcE5DdkR1cVhkeis5VTgzL1g2cTFCa0F1ZW8wcUorR3dDYVNUd0RGRWdVbTFOdUErbXR5WlRyamcrVUZtVUtFd3o2cFRZZ1prUXc5RzNzMTZvRjdoTHp5c2tSK1lGZDlKS1g5NHNMT2dhbTFod2ltMVY2bGp2S1dlYkRBZGZQcjQ3ZFY1SmtFWm9USlgydWRkTG8xSHVUY002dFlSajRsQ3pMQmRDZnpZMExqWHFzYU9MUG9EZ2t0aVpXc1ROY0wzZkRjOU5TdlNpL2Myc2Nab1FxL3dsZnZRaEo5RCtKR1hBdDZWQVQ0U3hNenpNMk5VUVdhWWZVenhoV0xuV1YvUWdnaVoyWXlVSkRkeW12d3d4QmxoWTE4OWhKTFdJWFRPMkRNTVoyaDAyeUEyM2JPVjRxUndFSFN6dVlhQS9JRmVVS0RRUXBYVlZVUWxwSkpaQWl3aHVvcXY4QmtNTUxxNXhpbE12eUw1MlFtNmVlRWV0bEdFSFFvM1pFZjNjRXB0b1pNOTRwNTJzZnJGUzVmTFFpcTcyL1A0MGdQRHh1SWVnZ1lhNDJVR1RRTllEWU1LSnlRZHhINHNsdURtMDRFRzY5aDR3MFc5Y3YrYk8iLCJtYWMiOiI0MzVlZTE0YmRkNWU2MTliMTdiMTNhOGY1ZWZjZGEyNGNlNjRiYTM5MGE3N2JlOWUzNmI3OTc2ODY4NDI4MjMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFJbnFRMmVMSm9IVHJsMUFpVURlcEE9PSIsInZhbHVlIjoiTCs0aUV5eWdUcTBOemtxdUVSVmo1U0RUVDVtSGlvVkhTSzNBbEhNZFYzTTNraWlGUURxTk9UMDdIaTIzWlc0bWtkVEE2SlUrVnN0MG55UXZpQW5TdWRFOUJ5RnE1NXBYcGw2VU5rajhadjE3aWdIL1hUVFZPN0s5ZlkzU1ZLcmRvSVJkc1A0WXNiYTJaUlJ5WWlzMFdPdUtPNTZJQ2V2UmdBbERSQlBueFFleTVPVzlRditXWWkvM0V3bTBiUExlZGRYYll4UmFuS0FZblo1QVNFWmt1ME9oNDhnbEVnZEl3UFJqZDBmWDIvdUN3c1N2T04vUFA2eG1DVmNWdGV2dU93QnZMMHBZR2x2azFqS3FsQitoQVh2UWtqWXNsL1luWG03c0U0bjVtUmtBeVdHK005UGw0M1hvNndFa0lTNWhBVXRvUnZZREF1VkdjQURuZzhMWWp0WXJmVWM1N21qamxTazdHdGtvZ203SnR1VEZPWU1LSFUzbGRmTUROakxBQUVXMThBMlEwNnVxN1oyY25SRHJYSHlqRWdBbEZVN0lLTVNTVmNmU3JKVmtDUGtFRG9OUkUwZUNwN2xKRDNBRUVZVDJjbThSOTdiUFBoWXlXamRhUSt3K3BSWUw5Q3ZvSmxTTUFac3BTNzFBT3JCd3VTOUFWQVRNbUZKNGc3Y3ciLCJtYWMiOiJiNzJhNTJmODQ1NzMxOGY1MjU3OGFlOGY4MjZhMTZhNTE3NTM4YWZjZDBmZThkMWExNzA4NjkyYzllY2JmZTM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-924614621\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-827534342 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827534342\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1094818044 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImQ0SnBpYlJiNEhudTRMcVFyUG9aK3c9PSIsInZhbHVlIjoiSmRmR1FTeTVrWHhwaXZxVzRwK2had0pGS0lCckNDWjV1M3pFZ1B3OW5pSDBwMEc4cWMzYURxN05lK2VvbTBiYmNBNEc0RUVaZ0c3NHd0T05BZjlZK3ZGcSt6cnc4ODVPQUkrZm5JTTV2aFJkMGx3b2pkUGllK3FNM2lTUHF4UC9HUzAvZGVxOTRFUzFtSXhRVmZWRUtZU29GUHVFYUIwWmtTY0RCWGtGVVdjdzAzTG5JQnVBaUs2STB0MDVtVEJlalA0Sy8wU25GZEYxaHF5Q0c4K2luUFNUZ2FlOWVDcG8zbXkyREdSbnplUlhBQjQyWmpYdGNsc0YrRWRMODB3OXY5eTlkR2pZTmc2UDFodCt2V201MURnM3l3VTlmM2phUkh6a1dCWUVLTTNGY1JDZnAwSXFnUFdBMXp2MnozL2w4T3BxMWhDYkorRjluNDRHSFpEbGluSG9ZRFlvVlhXZlM2anIxeWN3ZWpaS2tXRWU1bXlmTWFMRmZycjJkVnU3U3Blb1BkUG1LVTFIY0djUjRRYjRCTE5jbEQveU52U1NsQ3NEbm1NOUNKTkY5TG9MaDlUekZjeTZxV1p5K29FdEtjdjVUWEpCTmt6MGtSVSs3THk1WlhmaDZGRm5tckhjdHo4ay80bUp0QmZtK1pzeG5keGQ3YjdYVTd2SmlBKzYiLCJtYWMiOiI5NGFiNzljZmJiNTYwOTIyMjE3NThmYjYzYThmNzJiMmQ2NjdmMTAwMDM5YTYzZDE0YWFhMmVhMzZiZTUwNDI3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJybGtEdWp3dWJibXhnYTJ4dFNLbmc9PSIsInZhbHVlIjoiU3hUVGpub1ltMi83VmNFOENXVEZ0MkdtN3ltS2k1MTZxNWtkemVQajdTVzVpTXFLNzZYTDh3L2FNSkx1OXdOOFhrbVY5MVV5UVNuUEw3Qmg1dU9raVYrNVdJQ2EvcHhGR3BBSWc1SVBBNk5lZEp2NTd5VjRCR2ZVdExNSkhpN1lHOXF1ejlHUXd4NjNsSEgvMHl0TVUyUWJSdCtMcWlneXRoOTVpMmpleWdDRURYSnQwL09WTlV3dTBSQStkcHI5SXh1a1RUSGJocWxSMnZNRDk2S3QxS3VFZ0M2VFRXeHRiZE10Mi9sTHhDRGNWTWRhQ0JwRlFRTDNJdW5Pc1ZjU3hSeFBvZGdSTWFRZDRhN3EvZ2E4QjkzWDZMbUYyNWNzNFJpdjZFNitLcXBHazZrRFZTSHIxNWt6VURLTDIzc3NzR2hpV2UvN29JaEt6ZkhjY0xrYmRudUtsQ0lScXRzaCszYzUrWEdHeGRrM3pEcWVrYm8vdHZ1K05lS1JLY3RBNzlDMzdoeXpJSklPT1doYy9iblBUcGxYd2p5SUtwYXZLNnRBanZET244MTRWdDBKRmovd0lSeWpiT09RN0xkeWI5K0dtd3prb0J1YmhTbncycTFyNFk3NlBMS3pjRHhzbWJ3cHk0Q3djNmRKbEQzSGo5WGFiZ1QrSVU2bzA0VzEiLCJtYWMiOiI3NTRmNzYzMzc4OWJjMjBiZDYwNzQ1MGFmYjkxYTBlZDYwMTM4ZjBiMzVhNTY2MGU4ZDU0NWUyYjk2NDgyMDdlIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImQ0SnBpYlJiNEhudTRMcVFyUG9aK3c9PSIsInZhbHVlIjoiSmRmR1FTeTVrWHhwaXZxVzRwK2had0pGS0lCckNDWjV1M3pFZ1B3OW5pSDBwMEc4cWMzYURxN05lK2VvbTBiYmNBNEc0RUVaZ0c3NHd0T05BZjlZK3ZGcSt6cnc4ODVPQUkrZm5JTTV2aFJkMGx3b2pkUGllK3FNM2lTUHF4UC9HUzAvZGVxOTRFUzFtSXhRVmZWRUtZU29GUHVFYUIwWmtTY0RCWGtGVVdjdzAzTG5JQnVBaUs2STB0MDVtVEJlalA0Sy8wU25GZEYxaHF5Q0c4K2luUFNUZ2FlOWVDcG8zbXkyREdSbnplUlhBQjQyWmpYdGNsc0YrRWRMODB3OXY5eTlkR2pZTmc2UDFodCt2V201MURnM3l3VTlmM2phUkh6a1dCWUVLTTNGY1JDZnAwSXFnUFdBMXp2MnozL2w4T3BxMWhDYkorRjluNDRHSFpEbGluSG9ZRFlvVlhXZlM2anIxeWN3ZWpaS2tXRWU1bXlmTWFMRmZycjJkVnU3U3Blb1BkUG1LVTFIY0djUjRRYjRCTE5jbEQveU52U1NsQ3NEbm1NOUNKTkY5TG9MaDlUekZjeTZxV1p5K29FdEtjdjVUWEpCTmt6MGtSVSs3THk1WlhmaDZGRm5tckhjdHo4ay80bUp0QmZtK1pzeG5keGQ3YjdYVTd2SmlBKzYiLCJtYWMiOiI5NGFiNzljZmJiNTYwOTIyMjE3NThmYjYzYThmNzJiMmQ2NjdmMTAwMDM5YTYzZDE0YWFhMmVhMzZiZTUwNDI3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJybGtEdWp3dWJibXhnYTJ4dFNLbmc9PSIsInZhbHVlIjoiU3hUVGpub1ltMi83VmNFOENXVEZ0MkdtN3ltS2k1MTZxNWtkemVQajdTVzVpTXFLNzZYTDh3L2FNSkx1OXdOOFhrbVY5MVV5UVNuUEw3Qmg1dU9raVYrNVdJQ2EvcHhGR3BBSWc1SVBBNk5lZEp2NTd5VjRCR2ZVdExNSkhpN1lHOXF1ejlHUXd4NjNsSEgvMHl0TVUyUWJSdCtMcWlneXRoOTVpMmpleWdDRURYSnQwL09WTlV3dTBSQStkcHI5SXh1a1RUSGJocWxSMnZNRDk2S3QxS3VFZ0M2VFRXeHRiZE10Mi9sTHhDRGNWTWRhQ0JwRlFRTDNJdW5Pc1ZjU3hSeFBvZGdSTWFRZDRhN3EvZ2E4QjkzWDZMbUYyNWNzNFJpdjZFNitLcXBHazZrRFZTSHIxNWt6VURLTDIzc3NzR2hpV2UvN29JaEt6ZkhjY0xrYmRudUtsQ0lScXRzaCszYzUrWEdHeGRrM3pEcWVrYm8vdHZ1K05lS1JLY3RBNzlDMzdoeXpJSklPT1doYy9iblBUcGxYd2p5SUtwYXZLNnRBanZET244MTRWdDBKRmovd0lSeWpiT09RN0xkeWI5K0dtd3prb0J1YmhTbncycTFyNFk3NlBMS3pjRHhzbWJ3cHk0Q3djNmRKbEQzSGo5WGFiZ1QrSVU2bzA0VzEiLCJtYWMiOiI3NTRmNzYzMzc4OWJjMjBiZDYwNzQ1MGFmYjkxYTBlZDYwMTM4ZjBiMzVhNTY2MGU4ZDU0NWUyYjk2NDgyMDdlIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094818044\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1654860301 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654860301\", {\"maxDepth\":0})</script>\n"}}