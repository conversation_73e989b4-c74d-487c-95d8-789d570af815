{"__meta": {"id": "X2f770cef0817366b9e67bcde65c7d025", "datetime": "2025-06-28 11:23:51", "utime": **********.763826, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.384847, "end": **********.763838, "duration": 0.37899112701416016, "duration_str": "379ms", "measures": [{"label": "Booting", "start": **********.384847, "relative_start": 0, "end": **********.716187, "relative_end": **********.716187, "duration": 0.33134007453918457, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.716196, "relative_start": 0.33134913444519043, "end": **********.76384, "relative_end": 1.9073486328125e-06, "duration": 0.04764389991760254, "duration_str": "47.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46266912, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2289\" onclick=\"\">app/Http/Controllers/PosController.php:2289-2323</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00251, "accumulated_duration_str": "2.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.747609, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 85.657}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.757915, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 85.657, "width_percent": 14.343}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1881029890 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1881029890\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-422417997 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-422417997\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1071819694 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1071819694\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1476895930 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6Ilg3ZlUzTzJsUXpLd2JzeG9xQUpuWHc9PSIsInZhbHVlIjoiZS9jbG13TmF1bVg0K2tMaDc1M2IyVGZIaUYxUUxkR1prRmo5RWVzdStMaGNKb3QwbklpWXovd0NJRGxEVm5LTFltZXBwcXRYWWNMSkJ3YjZITWF2L2pjYmZxd2x1YmE1MjJRam45NmtwdFlIUGF4M0IwdXdzT1c0cVhoSlpIZUovRWpQZ1VBSkZDN09uclZFSHlLTm5hSDRBVEdnaWlUOVllb2x5VlV2R3o4ek44OFBCUmgzclFjbGZwN0F2NG9nKzEycFgxQmlvOU1iS3VvNTZaSFdVTG95bFRkTi9oUklqVEdlVU9yQ1ExNWVtK1RtSzg0VVZFejNza2tvZmxqdThzeFRQdTlIUXR3bUpLRnRGLzRxSTZZN1hYR0dBaTBTUDBTNkQvcVFUQzZIZzdrSGNvaWE3Y20rR0JsTnpOOFVxRlRlWGF3ZWUxSFhUeldRdkgxK3hOZzJ4SmZETjMxREJUREpQKzZ1NU0rQnJiRDU0ZDZvTXV0MEtuTHRTY0FBU1UyVDRwcTJGWEt0bmtjZldnaC9YVlJZUVp2U2oxZVE1MnltM29aSmF4S1NJYnlFaXVRRDhnenZxdDQ1dlF2TWI1K1JuaUFnTitXd0FCV1Vpd2VJM282QTVEb2VWdUxtc3JJbEZsQjFjRTJQcFptcTlVQkZIWmRHQzBkRG9TUW4iLCJtYWMiOiIxYzM0YjUxNWY4YTY1YzIwMDRjOWVlYzdmZTU0YjZjZGE4NjRjYmNjM2NlMjI2NjAxMTgzMmMzMjAwNjZiYThiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhjTGRSRXNJRWJrZ3BFeU1WVzRnSGc9PSIsInZhbHVlIjoiYThEL2djdkVBcWJGNklZY1pvZTNlZnYwa1F2ckpZUGkyanRIY3I0enVVV3dwZHVmVDJHWVpGbVNlTnpCbmh1S2dVb2g4SlFZK21YcHE5aVd4dk9lVHZhUFhxWHl4ZVpUUzNBL2NONU5IenVwUm5Tc0QvME1SQWJ4VzBIMzF4dk04QlpRK05PVkx3K3lqZWtzbUxBbmlZL1dPTWF0eml2YWlwTGhyMk5QRVJaWG5pK1FuNlZGVXpuaEVlU1QzaTFVanVEVThzeC9Bd3FyWWdCRHBCRDFhbU1ZNnZrRXgxQnF2aVVUUjdac1c1TlZDTE10VEs1ZERsWUVSanFrREM4dXpzMXhBQ3Y0K0d1WjRTdCtNWitlR24zVHpkSDBsNnVERERrRk40a0h3cE5veGRucDZiQXRSNGVNNjQ3UVhTOVBIdlZJa1BoejhIVzNaTHdmOGZHejlaVWd1NFV0Rk94WWFjQ0pXSklWYlJONkJuWjlhVXFwQVRDd1kvd1JJU2EwL2llTWppUWdTVzUwSlBtUnNPOEpzdHVXa2FjS2paM05vYkxWZ1JkYjlhVUhjMUY0VldNckpzakNjTzNBSjZKWVBPSkhsVVMzSWIrRFBTMDZzcGxDcGloUlI0NEo5R0J5cXVudk5wZFpKRFNCNFFEcXBTTVZxSThjZU1wcHl1UTIiLCJtYWMiOiJmM2UxNWI5OGZkNTI0YmNjYzk1YTI4YTAwODc4YjZiNDViMmY0YzNhZWM4ZWI0YjBiNjA4OTU5ZDY4MDMxM2YzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1476895930\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1507619987 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507619987\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-45019215 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVsQ3pHNHlKemQ4MC9TNm5SZXY4anc9PSIsInZhbHVlIjoia1F6MjdJYzUweFZSTE5kcXVoNjE3dEJWa2FJLzl5UlhWOFBUc0M2eFhjeEdnNXBFcU9DWW9ERE81dy9QWmZOUlo4WWQ0TUI3VEU3QWRVaWl6d2V3cnE5V01mTDAzbXhGSDJXRWRrbU5aNm16b3JtcDZRV1dqUHRaMkxzME5zTFdWTGxPOVlkSUI1dmlOOWxmUk1LMkJOeEFEeXlLVkU5Nk9FTEIzSlZURExKWExQT1JENm8va1pyL1M0MHFpZmI2QmVRRGJHREJSclNiVTBreFRIMll2N3E4M1pBSU5GSEY0dXJCZ29XaVRUTXlEbVZrOVZzMmtZcStLdmMrRmJyTTZ4UW51VFBRMytJT3ZsZFhKc1F2TXE2THVqeXl1ZFFmK2pYbEJUTDhhMy9vdHlNU1NmSVk4ZFQxR2Y3ZXZUclV1WTd4bHV6OVlGY1R1anFQcTc4dnJSUXBCWlVtK3Y1KzhQeTZmVjl0dE5mNkV0M1YrM0lSbHZSUjluU21rWUpyV3hpZ0k1czZPNjNkdUNKMWErNmVJbXhYVVlFem9rZXE4TXdGMlNMNWh3WU1VV3dsamdBbjNPZ3RFYmNrNE5Pb29mYS91N0VLQmxEOFZGTjdnS1NHcnBEMVZ2QXhXY0FwYkZIWDhFVVVBNUYzS3BVMUZRS3kzdm9ybHp1MWJWWSsiLCJtYWMiOiI3OWU0M2MzYjc1NGZmNjlhODI1MDY0OTFiNTBiODdmZTdjMmZmNDQ2YmZjODhmNzczMGFmNzdlN2RmNDRhNWMzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Inc4QWQzTk1BQ0pOdVg1N1lCOGttb3c9PSIsInZhbHVlIjoiMWdGRUhEOG04STkvWlUwWGw5dFZKVDVTT2lScGtZVDRhZS8xd1ZOejYxZkFZa0RkekhCN09zT0E1UDUzQ0ZGbkZvQ1Z5SjN6YWZ4dkp2VkF1YnM4RFpxenZUY0N6YUd2SXY2Zlh4MXd4VFBpeTlGcGFybkU1QXBpUnpDN0g2YytqczdLYVF3NnlnZzdZcTY2RWo4d1hLRW4rR3psMjNsNHB2ZlM1YStDd1dkZ2dPMzRxVW5ETjFUbEZDTDJzSCtCeVJVQzFSR29VV0doelBWWFJka0pRRERiU3luOVR2M2x4aGJ4ZTBmYjMwaTdDWjhOUWJ2dWRXUStSWHJEVUF2bGtGYzM4dVFNYnhFeU1qOWpqa1ErWnZCM28vL1ZsT0NpOS8yS2ZWSXh3TGMxaytPc1lDOUpsV2NxSDFyZ1Q5U3Rpc3NJRjdnUGtZUWc0eWQ3cEh6ck1GVGpUUFBVMTBMNmxlS2xqbWZDdTFNZFBrSDFxVUZ3bkFaV3VBRytWdk5sTzY3bWlIUk9zbHF4ZDQwc3Y2SkNramNBNC8zSGVGK0FKanpWSVBQdCt4dG8wZFBjWmNlalA3MEpsSlNyN3hGaHl2U1pFT1JXUkFaK3lxSW5ab3NXdU4yRWdZMTFKT1puQnhCRHlmU1BKZ2dhdkthVGNiSVN1TjBqVGNaeFg0RXQiLCJtYWMiOiIxOGM4YWM4N2ZiNTI4OGM1MTBlMzY5NGI1NzhmNzgxNDQ1NGRiMjE1OTQyZTAxMmRkNDM3YmJiODVmZTFkMWE1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVsQ3pHNHlKemQ4MC9TNm5SZXY4anc9PSIsInZhbHVlIjoia1F6MjdJYzUweFZSTE5kcXVoNjE3dEJWa2FJLzl5UlhWOFBUc0M2eFhjeEdnNXBFcU9DWW9ERE81dy9QWmZOUlo4WWQ0TUI3VEU3QWRVaWl6d2V3cnE5V01mTDAzbXhGSDJXRWRrbU5aNm16b3JtcDZRV1dqUHRaMkxzME5zTFdWTGxPOVlkSUI1dmlOOWxmUk1LMkJOeEFEeXlLVkU5Nk9FTEIzSlZURExKWExQT1JENm8va1pyL1M0MHFpZmI2QmVRRGJHREJSclNiVTBreFRIMll2N3E4M1pBSU5GSEY0dXJCZ29XaVRUTXlEbVZrOVZzMmtZcStLdmMrRmJyTTZ4UW51VFBRMytJT3ZsZFhKc1F2TXE2THVqeXl1ZFFmK2pYbEJUTDhhMy9vdHlNU1NmSVk4ZFQxR2Y3ZXZUclV1WTd4bHV6OVlGY1R1anFQcTc4dnJSUXBCWlVtK3Y1KzhQeTZmVjl0dE5mNkV0M1YrM0lSbHZSUjluU21rWUpyV3hpZ0k1czZPNjNkdUNKMWErNmVJbXhYVVlFem9rZXE4TXdGMlNMNWh3WU1VV3dsamdBbjNPZ3RFYmNrNE5Pb29mYS91N0VLQmxEOFZGTjdnS1NHcnBEMVZ2QXhXY0FwYkZIWDhFVVVBNUYzS3BVMUZRS3kzdm9ybHp1MWJWWSsiLCJtYWMiOiI3OWU0M2MzYjc1NGZmNjlhODI1MDY0OTFiNTBiODdmZTdjMmZmNDQ2YmZjODhmNzczMGFmNzdlN2RmNDRhNWMzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Inc4QWQzTk1BQ0pOdVg1N1lCOGttb3c9PSIsInZhbHVlIjoiMWdGRUhEOG04STkvWlUwWGw5dFZKVDVTT2lScGtZVDRhZS8xd1ZOejYxZkFZa0RkekhCN09zT0E1UDUzQ0ZGbkZvQ1Z5SjN6YWZ4dkp2VkF1YnM4RFpxenZUY0N6YUd2SXY2Zlh4MXd4VFBpeTlGcGFybkU1QXBpUnpDN0g2YytqczdLYVF3NnlnZzdZcTY2RWo4d1hLRW4rR3psMjNsNHB2ZlM1YStDd1dkZ2dPMzRxVW5ETjFUbEZDTDJzSCtCeVJVQzFSR29VV0doelBWWFJka0pRRERiU3luOVR2M2x4aGJ4ZTBmYjMwaTdDWjhOUWJ2dWRXUStSWHJEVUF2bGtGYzM4dVFNYnhFeU1qOWpqa1ErWnZCM28vL1ZsT0NpOS8yS2ZWSXh3TGMxaytPc1lDOUpsV2NxSDFyZ1Q5U3Rpc3NJRjdnUGtZUWc0eWQ3cEh6ck1GVGpUUFBVMTBMNmxlS2xqbWZDdTFNZFBrSDFxVUZ3bkFaV3VBRytWdk5sTzY3bWlIUk9zbHF4ZDQwc3Y2SkNramNBNC8zSGVGK0FKanpWSVBQdCt4dG8wZFBjWmNlalA3MEpsSlNyN3hGaHl2U1pFT1JXUkFaK3lxSW5ab3NXdU4yRWdZMTFKT1puQnhCRHlmU1BKZ2dhdkthVGNiSVN1TjBqVGNaeFg0RXQiLCJtYWMiOiIxOGM4YWM4N2ZiNTI4OGM1MTBlMzY5NGI1NzhmNzgxNDQ1NGRiMjE1OTQyZTAxMmRkNDM3YmJiODVmZTFkMWE1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45019215\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}