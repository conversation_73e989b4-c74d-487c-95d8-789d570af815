{"__meta": {"id": "Xd0563e439c6b7065f244ca0b02b4ac59", "datetime": "2025-06-28 11:23:59", "utime": **********.339285, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751109838.871528, "end": **********.339298, "duration": 0.4677700996398926, "duration_str": "468ms", "measures": [{"label": "Booting", "start": 1751109838.871528, "relative_start": 0, "end": **********.255911, "relative_end": **********.255911, "duration": 0.3843832015991211, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.255922, "relative_start": 0.38439416885375977, "end": **********.3393, "relative_end": 1.9073486328125e-06, "duration": 0.08337783813476562, "duration_str": "83.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45704720, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1910\" onclick=\"\">app/Http/Controllers/PosController.php:1910-1968</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026010000000000002, "accumulated_duration_str": "26.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.295394, "duration": 0.02067, "duration_str": "20.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.469}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.324703, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.469, "width_percent": 2.537}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%********%' or `sku` LIKE '%********%') limit 10", "type": "query", "params": [], "bindings": ["15", "%********%", "%********%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.328264, "duration": 0.00468, "duration_str": "4.68ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 82.007, "width_percent": 17.993}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-835916784 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-835916784\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1841548414 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1841548414\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-844810965 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"8 characters\">********</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-844810965\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-430060568 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">29</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IktmZzNIUnBrdEdITS9oc3lNV0NUQVE9PSIsInZhbHVlIjoiZ1BzSjU5L0tFeTNFSFVjUS9UOXp6djVkbG9xUFVXR2tiMkVpLzNJZUtMRmY5MTB5UkhlM1V4dkovQUNxcVFBRlM0Qm02NnNBK2U3RTh1dHE3UGRQRkJZcHE2SEw4UjNCbURpRmdQVWVYMUFCQUE2REpCcXh3WEh0eFpmMnpIM3B2RWN2bHd6WWxpTHAyVUtQZGptQVkvWEJlK2piWFAwUmtkbjJ5UFU3NndIV2taNGRWb3FRZ2FkMVZmM0FMRjZNNTJwMzBDbkNqVVBrZVM5b2Y1QXdWWHI0Y3hwWUh5aHVVQUFCbVlZTkJDNllBUndjNCtWMmlmaFRXYTYvYlZtWTdibmwzbFM4L244UFlCS3hHYVoyd0kzS0pHNVlvMmRxdVp6RGpaQTNuVW9WRDl3YWdzSXQzdzY2bENseEhadVNmdkc4eHh6b0FlQXAyNEd5bzBsKzJtWlNhZkpuNzcvT2Q4OG5Hc1NSMGc1WDZmRXZaUUpkZUszZVZ4enBrRTZEYWxmTFVZRm8xUUtSNTJWUWVKazJ6aUpHRjlNamJ4VnJUTGgyU0ZnOExQRVpkcFpwY3g5MXpvcmdCN1pHYVR4a0R3TVZNdVByM1BsUVNONnFpQXN6Lzc3VmpSY0FxcHI3YlpWUEZVbWdIWEN4NFh3WDd3VzFpL1I4MWZzV2dsbDYiLCJtYWMiOiJlNTAyMzUwNzRhZjQ0ZGRhYWZjN2I5ODA3OGMzNTZkMzg3YzMyMTY2MjUzZjEwNzRhMTg3MzY2ZjBmOGE3NjJlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVteEVRci9BY0x1cjdsL0ZqSW40aFE9PSIsInZhbHVlIjoicUN5VytTaTIyMXRyUTNucGloYXU2elFpdUxrSGxRNDhhQWR4WERxbTBORnVTUzA1OEpNU01SZkNRaDRSbG1PbW0rVFh6Zmpubm1JR3FNU0RVY3BYMzNid04wTUhxZWZ5R0svRHU5ZC9rZmlyTjFhU29aNUZ3YjhWK25sbzIzdnJ4dVhNYzNTdUF3WTIxRis2UzlEWEtWSjRYd1Y4SkZ3UkR2L3B1c0tLNzBQSXpwYzJjNWMrZEFXMzFqMEwydVZGYTVkTUdpYU16ZitjL3lCMGprdkQxay9ObEFMVGE2S29Zbng0MHptZEdDcVg2N1ZXL3cydlJHSVRoYW1LQ0xLWUE5aEFaV0ErSkJ1aEhtZUZscUJCaDJlWm1rdDRRKzNtRkpFM0tHNE8vOGRKbHM1WVYxYnRObEkydWJWYmV0dU9IbUl3a25JT2F6TlUzcVB3MVVQSUtjWHJrTEdJL2NxUEEvWUcyaUxMVnkzUENMVThMUXdLYnc5Qy9CL09WZ00zWEJIY2wrU0tuV2tqL29jZVVNWFNmcEJNWWtDSWxyaHV1N0NHdXBmSUhQQlU3Z1ROOUJaOXNKa3dOaXM1OGJlNnV4L3RZWDRLbWpNMDBBRFc1L0t0ZWFOUHQxallkQjFkVmFhQ2s2a2ZSblg2V0FybDhibHpyTXBlZzdqUkdnMEgiLCJtYWMiOiIyZWRjNjBiM2E0NjVjNzBiZTgwOTZkYTBmYTA5Yjg5ZWZkMTcxMDUxNmIxMzZjNWYwZTc4M2FiNzNjMmNhNjA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-430060568\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1801457008 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:23:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikd2S1JpTFduREJhRnV4YkRsMFJSRFE9PSIsInZhbHVlIjoiMVhwQ1hJL1Y4dEJpcVRzTXYvUTdjWU5ZbGNGT2VGd2tkUnFrcjdMQjF3bnFaRklWR0JKbWdkeTFmNDN1Z0V3NDQrY2dqWC9hSDVuMEJXYVVKZjJYdjZDczVodllqR1NBN0xsN3J5UXJsKzZXNFBIOU9FL0lnaXloSXFhNU92NDBIOEsyK1lZNk9rYjI4bjI4d3hod0tkdVFJRWw2cnpYSHd6RjlPZ3VtVm5rQmRPT1FndjliTVJEd3VXd1VnNU1hUk9qR2FuVW5oakFTNjJONFRyeWcwNkNCU2RZSlBnTmdMS2dEaTVoVFp1ajlnTFpkajJOTkNZemE0cmRxc1R5NTcyQnVrR0VtRDBOWmQ2Y0lqSVBFTS9WNUZmR2xzS1lmbDFPZE5WNkEzS0FlK1hUREpPTjdZTC9ON002aW1SbWhDTjhzQVZPT3RnUU5DWHpkRlZhaE5xc29VcGM0WkRETHQ4aFl6YTZDRTlObFhGRktWSlJkN1JRQktFVDBKVjlheHBFeWFIN3dVb2dmSU0vU25vRDdZOHF3bndqTkQ3ajc1d2x5cWlHWC9NMTM3bWs5MjhmVGh4dVN2WHMya0pHQVZpVDhDWlZTc3QrZkRWYzBmRTBtM1RqQUNOOFFydkx0YUhhOUJTSkoycWNWMDdqbkgrZlpkSk9FRWJvdk45SkoiLCJtYWMiOiI4ZmMyMmRiMzU4Yzg2ZTg1ODA3NjNhM2U4NmUyNTdkMWUxYjdkYTUwMDAwOTRmNjA0Y2MxODNlMWE3ODMxMjU1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFJbkZhNVN1ZWhjUE1iMUtHUC9CNlE9PSIsInZhbHVlIjoiMUJyTWQrWmd0L1FzS0pOcHN1cjNrZnZMOFdqS2VGRDRpb3FnRm9xMVRsbUNtUzJTQUZQclJFQ1hMSWI2R1Fha2ZXM2ZyRWtrRVNpMFd6Z0ZoRWFtOHVCL3U5dkNOOVEvdk1tRkJCVFJKeldNdkNTK1A1RVpwSy9ITmQ3N3kxK3JUcmxxM0tFSnM0MmhBdytjeDNJaHpYWkY0RHdGK0JDRExuRXVmRzh2Rmk0ZFFBSW9JaEh5UUx6ZzJtRW1tZUJveGc0Q2FaTVdLa2dPbHZrdXR1RlROZURjTHZQMmtOQkxWZ3hQRk41TE1IaGRTTWp0WjF3VXlDVmRTSnkzOW9STUE2U0Q5b0hRT2xnMFVDWi9QZDM3SjN1TGVZNlc0WTQ5aXBqM3htTkdyMVpvVDZNZkNxaTlnUlZtM3poaUYxcURJZGk0U0hPNVlYYnNwQ2hxTTY3bVRITGgrME5GMVNSMHlzQ3ZBbjIyMVVXRmdtNDZwaWVoemlnVWhnNU1uYlk1V2I1NWEvVllETGRCQkJEQUFZNm92Y1FHN0VWbThHVksxMUJ3ekdjMTNKalQrRjd3c2N4VzRIZkVIcGQrUG5jRW5RRTQxUmF6QUd2SjBCU1p3SXpoMHdGcitPRUNhMHVLSFhxL2FnSlV0MTJ6MDZOaE9SbXZPSnVuMStSQnBJVGwiLCJtYWMiOiIwNmZiOTFkMDFiZGYwNGY2NzZjYmVmYWZmNjkyZjk4ZDRlM2FlNDZiZjM1NjQ5NDRiMjRiOWJjYmU5YmZlYWJkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:23:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikd2S1JpTFduREJhRnV4YkRsMFJSRFE9PSIsInZhbHVlIjoiMVhwQ1hJL1Y4dEJpcVRzTXYvUTdjWU5ZbGNGT2VGd2tkUnFrcjdMQjF3bnFaRklWR0JKbWdkeTFmNDN1Z0V3NDQrY2dqWC9hSDVuMEJXYVVKZjJYdjZDczVodllqR1NBN0xsN3J5UXJsKzZXNFBIOU9FL0lnaXloSXFhNU92NDBIOEsyK1lZNk9rYjI4bjI4d3hod0tkdVFJRWw2cnpYSHd6RjlPZ3VtVm5rQmRPT1FndjliTVJEd3VXd1VnNU1hUk9qR2FuVW5oakFTNjJONFRyeWcwNkNCU2RZSlBnTmdMS2dEaTVoVFp1ajlnTFpkajJOTkNZemE0cmRxc1R5NTcyQnVrR0VtRDBOWmQ2Y0lqSVBFTS9WNUZmR2xzS1lmbDFPZE5WNkEzS0FlK1hUREpPTjdZTC9ON002aW1SbWhDTjhzQVZPT3RnUU5DWHpkRlZhaE5xc29VcGM0WkRETHQ4aFl6YTZDRTlObFhGRktWSlJkN1JRQktFVDBKVjlheHBFeWFIN3dVb2dmSU0vU25vRDdZOHF3bndqTkQ3ajc1d2x5cWlHWC9NMTM3bWs5MjhmVGh4dVN2WHMya0pHQVZpVDhDWlZTc3QrZkRWYzBmRTBtM1RqQUNOOFFydkx0YUhhOUJTSkoycWNWMDdqbkgrZlpkSk9FRWJvdk45SkoiLCJtYWMiOiI4ZmMyMmRiMzU4Yzg2ZTg1ODA3NjNhM2U4NmUyNTdkMWUxYjdkYTUwMDAwOTRmNjA0Y2MxODNlMWE3ODMxMjU1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFJbkZhNVN1ZWhjUE1iMUtHUC9CNlE9PSIsInZhbHVlIjoiMUJyTWQrWmd0L1FzS0pOcHN1cjNrZnZMOFdqS2VGRDRpb3FnRm9xMVRsbUNtUzJTQUZQclJFQ1hMSWI2R1Fha2ZXM2ZyRWtrRVNpMFd6Z0ZoRWFtOHVCL3U5dkNOOVEvdk1tRkJCVFJKeldNdkNTK1A1RVpwSy9ITmQ3N3kxK3JUcmxxM0tFSnM0MmhBdytjeDNJaHpYWkY0RHdGK0JDRExuRXVmRzh2Rmk0ZFFBSW9JaEh5UUx6ZzJtRW1tZUJveGc0Q2FaTVdLa2dPbHZrdXR1RlROZURjTHZQMmtOQkxWZ3hQRk41TE1IaGRTTWp0WjF3VXlDVmRTSnkzOW9STUE2U0Q5b0hRT2xnMFVDWi9QZDM3SjN1TGVZNlc0WTQ5aXBqM3htTkdyMVpvVDZNZkNxaTlnUlZtM3poaUYxcURJZGk0U0hPNVlYYnNwQ2hxTTY3bVRITGgrME5GMVNSMHlzQ3ZBbjIyMVVXRmdtNDZwaWVoemlnVWhnNU1uYlk1V2I1NWEvVllETGRCQkJEQUFZNm92Y1FHN0VWbThHVksxMUJ3ekdjMTNKalQrRjd3c2N4VzRIZkVIcGQrUG5jRW5RRTQxUmF6QUd2SjBCU1p3SXpoMHdGcitPRUNhMHVLSFhxL2FnSlV0MTJ6MDZOaE9SbXZPSnVuMStSQnBJVGwiLCJtYWMiOiIwNmZiOTFkMDFiZGYwNGY2NzZjYmVmYWZmNjkyZjk4ZDRlM2FlNDZiZjM1NjQ5NDRiMjRiOWJjYmU5YmZlYWJkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:23:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801457008\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2102587506 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2102587506\", {\"maxDepth\":0})</script>\n"}}