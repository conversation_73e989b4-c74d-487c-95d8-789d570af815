{"__meta": {"id": "X8584ae0fb36f870cbdeddff04065bd3f", "datetime": "2025-06-28 11:21:05", "utime": 1751109665.407263, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.465354, "end": 1751109665.407277, "duration": 0.9419231414794922, "duration_str": "942ms", "measures": [{"label": "Booting", "start": **********.465354, "relative_start": 0, "end": **********.852879, "relative_end": **********.852879, "duration": 0.3875250816345215, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.852887, "relative_start": 0.38753294944763184, "end": 1751109665.40728, "relative_end": 2.86102294921875e-06, "duration": 0.5543930530548096, "duration_str": "554ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45980304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.996476, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": 1751109665.134435, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": 1751109665.38175, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1751109665.400383, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.12571000000000002, "accumulated_duration_str": "126ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.900052, "duration": 0.024050000000000002, "duration_str": "24.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 19.131}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.926219, "duration": 0.05489, "duration_str": "54.89ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 19.131, "width_percent": 43.664}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.983733, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 62.795, "width_percent": 2.227}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751109665.104357, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 65.023, "width_percent": 0.74}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751109665.323014, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 65.762, "width_percent": 0.517}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4090}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751109665.333915, "duration": 0.039310000000000005, "duration_str": "39.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4090", "source": "app/Models/Utility.php:4090", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4090", "ajax": false, "filename": "Utility.php", "line": "4090"}, "connection": "kdmkjkqknb", "start_percent": 66.28, "width_percent": 31.27}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4091}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751109665.375764, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4091", "source": "app/Models/Utility.php:4091", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4091", "ajax": false, "filename": "Utility.php", "line": "4091"}, "connection": "kdmkjkqknb", "start_percent": 97.55, "width_percent": 0.31}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751109665.377385, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 97.86, "width_percent": 0.31}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq24\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": 1751109665.396745, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 98.17, "width_percent": 1.83}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DbkmW0B1QB6x4DfsCQT5q6jxXald2UMQHCx201Ej", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-707583963 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-707583963\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1181332498 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1181332498\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1450156512 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1450156512\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=lntfvz%7C1751069310598%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijg3ZDc1SFFvZC9GTWNsdkMreExBU2c9PSIsInZhbHVlIjoiUkQ2bkV3S2hzS0IrdURiK0xpaU1QVEJIQ1liNmI1YnJEc3BmVDkwTDlHc1JIdzd1VHZoSG1GN2FBOXFJYktIUDdpMUFGeFpKWFdsbWV0bEsya1YzMkoyTUFaTk14VTIweU81ajJnQ3ZTTnBYTmNTeUkyM2dQeWlHeWFDZlhNMzduNFp6SkhBcmdhUmVINUZtV2ZWSVFFK3J6MXRmaUpxTDdESll4OTlUUENqSWdEbXJ4Unh6VTNiZ1VFaEJUelVCbXZkS3ZhK080aVZlZzVmUTNWVkVmNWtUUVJRc2p6VGJjUzBGZlJmSjVBa2h1dG9GdFpETHBoN29JcXFZQ3JVYXVaV3J2UGxFeitGQjVwUTM3dy85ZDV1L25aT05jbXl3ZVZuZVFoQnRTdllrUXY4M1cvUzhwbEFsRGJwRy9GZnVSMlR2SFplUnhJbDc4Ujhlb0U1MXJZNGdpdTA4RDBqRTlQQWRrY3F5aE00TVRVczdEZVgzbTFTd2dTQy9GV3hzdUZTQmFuZmY2dU5peExoTDdFRCtWU3ZnSUl4WklpdmV1Ym55VldONGpoU2tJWFRtVXVkMlRjMHpHMHVYa2tqQSt4TmNvdUtMWmNLaExsRGc3eTJxQ0ZnK2lFUFZtOU1HNjV1MkR2ZENTUCtqUVVyeWRudkRTcFBMQjlkN0xxZ1IiLCJtYWMiOiI4NWM0YTJmNTNhZDEwZDQ0M2Q5OGY4NzJiNGJjMzVjODJkOWUyNjZmMGMyZjUxYWI4YThmMzM1OWEzNjhlYzQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJud3kwdFFYSE4vYU56WElJUU92NFE9PSIsInZhbHVlIjoibFR5QU85TFNqdjJiN2xIaGo5VzFYKzhBQzhSTXlVeURHaldoMU80UTFDY0EyMHp0aGt2bUc3bnY2bUdoQUJ6SWlQMTNGQk53amVjMlBFK2UyZFZ3YkJwOWxEOEFJdkttdzg4ay81K1hEQXVOdlc4UnR2Z0JyRmNnQ3pZODdUY2VHTmwweHZqQVNwcmVMVVgvRno2SGZvQ2R2dFhzQkZTdHRWUU1DRzVlOU4rSi9hR3l6Y2NpYXRUN1UvcEFpbFVoVHVQalFuTmZCSHhwNGo1MXM2NHRTOHp3SysrNDhaaHJnZzdnQUdQS0dUWWd4QkFqTUw5NTRMcG1BSytKUXYrSDlXZnV4cmlpU245K3YzUHJkMU5LZWxlbWpRSXpOaGhhOVNCWnFLL1k3S01rbTdLN0VHOHFKL2tsZnhidWZwZmhpN2dFVjVsenZFYkE3QXZacW41TjFwT1c3dEl0ZDdIdjA2UUo5cE5GcE0xMkNLTlE3eWdOSDZpZmZLNHNHUVQrQW9nWkI5MDdGNEV1Ty93VDUrNVRxMFplOHVWU25uUEp2dFJaNWFZVEI0Z0JoeCswVzJWMDVjQWl1MTE5OGVsWjMxQUNCNXhmOU1wWlJCMFR1SGxSbEw0YUQycm5VTmtvRjl0cWFHZWRudHRxMXNEbUFiYTJScFlpYjhjdEk3OXUiLCJtYWMiOiI3N2U0OTM0MzU0Y2VkOTgzNGZlNzVkZTM4ZDNkM2M1NzFjODFiYzY5M2YyNDgyM2JkYzgyMGE2MDNmN2NkNmEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbkmW0B1QB6x4DfsCQT5q6jxXald2UMQHCx201Ej</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pYtYtkiPDCe3ahm1CDwyHzMGeTCvDicAQoasw6d4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-780187502 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:21:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldmajlQTjlBbyt4RUh1enBvOU42WHc9PSIsInZhbHVlIjoiUi9DSWMxSzFpYnZBQjRpSktud3VKaC9VNnF0b0dJQ0pxbWZBSHJsbk91QXdYUWRIN3RMK01UVk4rb1RzSTZud210WXpKalVYS2sxc3lqY2cyM28xb0ttRldTWkgrRSt1NUxqc205ZWZuR2NybUV5WHdWY3JIeVU3VEdKYm5lVzRGTUs5am5PUndWQ0Uvckh2elI1bW1yZmVGTHhiY2NVMzM0RzBSZGZ5QmJTMVhuNXU5azBiUEo2TkNHV01HM3V6MHFJU2VRdDVvN0V1amVLR0dRWFNiK29ZZDNFNFA2dGJwUDArbHh2VEI2cUJUcE0vckNaWW5BSXBVc1RqRVV1aWpJeEMvT2JnWmFnWU0wdkZ5bEhTNk85b2ZXRWtDOEhlRWQ3aUh1VVVJUjZPYi82MHhTM0pOTnk3YThaWXYrVENtWGZHZFUzZm1VdUlzSUl1SVFSZ1RxSHErODljWHBQTEN4MVhzdWI1SmUwMHNQQzMvTUZacytKakxrNEoxUlZuVWx3NGN5ckRMcm9TZmRyNHFCWjhCT1loSDdlL2cxNjkvQVdMN3VTK0o3TjJQUlNKS2dqM21ac2liTGtiZVlCaDV5dnVNc3h5bS9JNlBweFRLQVFGR0JVZkZTYzBjeWJOSkFYS3VWTk9yWWJaanRvaXFqUUc3NitxYlpmOVpIU00iLCJtYWMiOiIwYTA4Yjc5MjlkNmNkMGIzZWIwNGNlMTQyZGU1NzgzMTBlNWE0Zjc5NDM0MmM0YWE2MmIzNmNkN2YxMDExYTZjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:21:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJzSVVrWk1UdTdGWnhjMU0wWUZ3U2c9PSIsInZhbHVlIjoiTzRDbW1RMFUrU0p4aUVZSFJmSTVocWc3OFhmcFVWd0RlSW4yK1Y0eGtiQzR1WG9QZ0RwakZEVUErcERnVW1WSUZTcGg1MlVQM25pSXRTRmkvREFPSFZhSk5ZdGdCeE12T2lqNmVDdGo3dWhHSml3anRYNzB0Wjd2bWplM01FY0VnOEd5SEhTMHJTbkx2NHZKTmRmMlNkRHpzNXJmNUxVUEMydFcraFQ3MS9KVXd1MTVuZVNuQkNMU2xqY2VRRWlxZkNBSHhwZm1SQWxMYk1kTGpDQTZNdGNKMUdmcURBbzlaTzFrQ0RNTUVibWJ4aWtlMnZ2WkVBeEFTcklXTXFwNm1vc2RiZGNGRTJ0Nnd4QjJrYzZiYkh1RTZMWlVCOGk4Q1ZCTjdMYjRsUG9yektIYjZWbGwvbmM5eksyTFljenJaRnlkWHNjQ3d5ZU9VQWxYSkJvNkt5M2FOcU8rQ1JMZGlnSDA5M1hyQ3hkSGJMSTl1WFFuQVpSNjZpSCsyWk51T21YSzAyTGVaekl2dEorbGYzVHpheGoxZlFKWFRPNHhHcENLbzJSdElzQk1MeHpPdkNPeGN6dFZJcVg3azB3T0dYRGFtRVBBS0hVYnJKajBEN3pTVzRtTlFvcko5b3p4S2l4VU1OQUlwNm5YcE5TVy80V2RGWjJaaG9UTWsrS2giLCJtYWMiOiI1M2U2YWVkMmJkNTMyNTIxZDJiNTFmOGZiMTkzMmI1Y2I1N2YzMTcxZjExY2E0ZWZjMWQwMGUyYzhmZGY4NTMwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:21:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldmajlQTjlBbyt4RUh1enBvOU42WHc9PSIsInZhbHVlIjoiUi9DSWMxSzFpYnZBQjRpSktud3VKaC9VNnF0b0dJQ0pxbWZBSHJsbk91QXdYUWRIN3RMK01UVk4rb1RzSTZud210WXpKalVYS2sxc3lqY2cyM28xb0ttRldTWkgrRSt1NUxqc205ZWZuR2NybUV5WHdWY3JIeVU3VEdKYm5lVzRGTUs5am5PUndWQ0Uvckh2elI1bW1yZmVGTHhiY2NVMzM0RzBSZGZ5QmJTMVhuNXU5azBiUEo2TkNHV01HM3V6MHFJU2VRdDVvN0V1amVLR0dRWFNiK29ZZDNFNFA2dGJwUDArbHh2VEI2cUJUcE0vckNaWW5BSXBVc1RqRVV1aWpJeEMvT2JnWmFnWU0wdkZ5bEhTNk85b2ZXRWtDOEhlRWQ3aUh1VVVJUjZPYi82MHhTM0pOTnk3YThaWXYrVENtWGZHZFUzZm1VdUlzSUl1SVFSZ1RxSHErODljWHBQTEN4MVhzdWI1SmUwMHNQQzMvTUZacytKakxrNEoxUlZuVWx3NGN5ckRMcm9TZmRyNHFCWjhCT1loSDdlL2cxNjkvQVdMN3VTK0o3TjJQUlNKS2dqM21ac2liTGtiZVlCaDV5dnVNc3h5bS9JNlBweFRLQVFGR0JVZkZTYzBjeWJOSkFYS3VWTk9yWWJaanRvaXFqUUc3NitxYlpmOVpIU00iLCJtYWMiOiIwYTA4Yjc5MjlkNmNkMGIzZWIwNGNlMTQyZGU1NzgzMTBlNWE0Zjc5NDM0MmM0YWE2MmIzNmNkN2YxMDExYTZjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:21:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJzSVVrWk1UdTdGWnhjMU0wWUZ3U2c9PSIsInZhbHVlIjoiTzRDbW1RMFUrU0p4aUVZSFJmSTVocWc3OFhmcFVWd0RlSW4yK1Y0eGtiQzR1WG9QZ0RwakZEVUErcERnVW1WSUZTcGg1MlVQM25pSXRTRmkvREFPSFZhSk5ZdGdCeE12T2lqNmVDdGo3dWhHSml3anRYNzB0Wjd2bWplM01FY0VnOEd5SEhTMHJTbkx2NHZKTmRmMlNkRHpzNXJmNUxVUEMydFcraFQ3MS9KVXd1MTVuZVNuQkNMU2xqY2VRRWlxZkNBSHhwZm1SQWxMYk1kTGpDQTZNdGNKMUdmcURBbzlaTzFrQ0RNTUVibWJ4aWtlMnZ2WkVBeEFTcklXTXFwNm1vc2RiZGNGRTJ0Nnd4QjJrYzZiYkh1RTZMWlVCOGk4Q1ZCTjdMYjRsUG9yektIYjZWbGwvbmM5eksyTFljenJaRnlkWHNjQ3d5ZU9VQWxYSkJvNkt5M2FOcU8rQ1JMZGlnSDA5M1hyQ3hkSGJMSTl1WFFuQVpSNjZpSCsyWk51T21YSzAyTGVaekl2dEorbGYzVHpheGoxZlFKWFRPNHhHcENLbzJSdElzQk1MeHpPdkNPeGN6dFZJcVg3azB3T0dYRGFtRVBBS0hVYnJKajBEN3pTVzRtTlFvcko5b3p4S2l4VU1OQUlwNm5YcE5TVy80V2RGWjJaaG9UTWsrS2giLCJtYWMiOiI1M2U2YWVkMmJkNTMyNTIxZDJiNTFmOGZiMTkzMmI1Y2I1N2YzMTcxZjExY2E0ZWZjMWQwMGUyYzhmZGY4NTMwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:21:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780187502\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-947986143 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbkmW0B1QB6x4DfsCQT5q6jxXald2UMQHCx201Ej</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-947986143\", {\"maxDepth\":0})</script>\n"}}