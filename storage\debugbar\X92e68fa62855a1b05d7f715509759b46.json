{"__meta": {"id": "X92e68fa62855a1b05d7f715509759b46", "datetime": "2025-06-28 16:01:26", "utime": **********.393866, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126485.890929, "end": **********.39388, "duration": 0.50295090675354, "duration_str": "503ms", "measures": [{"label": "Booting", "start": 1751126485.890929, "relative_start": 0, "end": **********.325382, "relative_end": **********.325382, "duration": 0.43445301055908203, "duration_str": "434ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.325391, "relative_start": 0.4344620704650879, "end": **********.393881, "relative_end": 1.1920928955078125e-06, "duration": 0.06849002838134766, "duration_str": "68.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831904, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0058, "accumulated_duration_str": "5.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.364487, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 31.034}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.378436, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 31.034, "width_percent": 11.897}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id%", "%&lt;div class=&quot;product-stock&quot; id%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.382791, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 42.931, "width_percent": 57.069}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-85003697 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-85003697\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1242242392 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1242242392\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1465320991 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&lt;div class=&quot;product-stock&quot; id</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465320991\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1654242383 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZnd1V0UEo4R2RQU09kVkFEMWJManc9PSIsInZhbHVlIjoiN3d6dUlWMzJPeEk1TldkR0dWMit5ZTRabVVZOG53bGdOdTBxUW9YZE1oazIyZTU1Y3JFd1oveUNEUkJkanJjY0tvNDlYM1JkVjl2WlRvWjFCZnlTRWxaSWw4NTRETUJaTXR1UGR6c0hENXZuVDdsMGYrbFFBS2ZvY2RuNTVEcTRQKzlRU1F5S0pKc0krM1F0STZxOGxUUCtvOGJFREpPUkFUeitnMjRmTytTRUYxYWVqNHR3RXRRMEExT0hTSWc0Lytuajl2eHhUVFkwSUdUakU4WiszMW5NZ2dlRUhhamNmYytpaGZvcG9mdnVnUTM3Sk56NWpjU1NZQWdsaGFJSEgzeGNqdFlFYzJkQWN0cWpmbVUyZmU0anNQZHBtaVhPTzFmdlErOE16VitWUHhqWC82cEttc3BNc1llbFZXcFNVK2ZkLzQ5cm1SV2tqTmM2bTNWTmYyaHFnVHNUdXhqV3RsWDczMjBlUDBsMnRtYVArS0Q2MHVLZ21PekRvWXZWczdTMjBiWDRNTzRPdVhrd0YvR3lhNlQ1M0VBMHU0MzN4dCs1YW9pWFhvakJjUTRVK2xGRVBiVkVNU0FBQ2VUSHBGNVNmQlYva3RZM2hCS3BxMy9peDRGbExOcHUrNlh0ODAxMXREaXEvUWdlUjRiN1J4N3FmVlBEanhEWjZVbnMiLCJtYWMiOiJhNjIyMmZlYjEyY2RhOTcwZjJiNWNmMjBjNDJkNzQ2ZjhkZWQ5Nzg5NzcwYWM5ODdmMTliNGJlZmZkMjQzMWJjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im92VWxNVFJlUVc0ZFdPQ0J1UHg2bEE9PSIsInZhbHVlIjoiQmdENlFHaml3ZWlPckFmZ2hraWlNdlRwTVdIanJueDFLQllaMVB0bUVyOGQrVWdFdXcyN1MwUm9HeFZNd3Z5a1FTdXZwWnh5Q25MSVFxUHc2TWl6NmtxV2JWQlRuWGpRRmNpejZERW1tU0Z4UVp1NUVyMHFDaTIrdi9namZHK3YwN3YwU2U3NTA0aCtXYzNxMnJBSFdFbmdzQjA3Ti9vdXVUV0RvQ0JxRS9RaGxaTnNzZGo2ZmhXSTZpYjdKc2dxdUl0MWJYRmVxTFVWSzE5Vkd3ZVZ6UWN4aFVUNDNGRGdQY3FUK0R2c0pCcDhmOW43d3hLUVlwK1UxbGZxc2VBVEZDQnlmNkM3RXdOSUg0S0NHdUdpMEhJMkt0TUxESmNuOGhKYVZ4bVVOT0k1dEZ5UmNwTVBtTVdPbnYrTTZ0REU1MVVNK3R6WVhQV2ZHMGNKdCthemhTTHJ1b1REbUVZcnkweWRsUDRlWkd5WE8xVHNxdTVpY1FxczdrSWZBM081ZUVIRWl6Y0JaNWU1RExjcEdmeGtHeHAwY2R3bGYxVG5EMTh3ZER1cHBRQ0k0TXQ5VmRuelZuQTFxam52bG5kZVc4MVk5QW54eTBqOWV3MXVNeklWOFBlQ01QOW9Way9rdFY5MnJ1Yk9RTGZhOWM1b1QrQjEzREZQVGZOWUlROCsiLCJtYWMiOiJhZjAwNGY5ZmZjNThhNDMzNmIzZjRmYTFlNmYzNjgwZjA5ZDRkOWM5ZGQ1YTdlNTg3MmI1NjZiMGQzNjlhNzNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654242383\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1079890152 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFiWUt4SGw1VEd0V3FlMTZjcTdzSUE9PSIsInZhbHVlIjoibHU2cFlOclBYa25OeUFLOWdBM0VSdGZtejdGVnMycklKKy8wNHpFRThuc1lmdFczckQwNTZvakxlNGg4V0o5THlyNUUyczZsQU0yWHlIZWRSV09iNWZRMG5TTjI5cDhoL2pHYjd5a1RtUmR4b1kzSVM2eEhaeW0rK2x3OTVhdDJNQUhMWXlrQjREdVlZa2d4cVp6eWZWOGVFZnJIQVJqakw3Z28rbE9OSVZrdEVXOXBRU0ZnbU81d2xpRmpvb0liQ05wc1BMdFByM25xYVQvNDFGM3dsQXkvUkZ2T0pReVFBODBQaVFVU01mTi95SDBGZDh4eERXZXRjUXZ0Nm5VRHJ0bDRMVWw1c3BBOHdJcm9ENHdDU1JXRXdGUlZTSkh3VlpVSUNVaGNqc1d5dHFTRHBXYTNKYWJ5cld3aW9aZlBkV1B3RGFvd3JBZ2FKMmo0dHFQc3lzSE51dUNuVlJLUHM0b3VEMDRtWFFmMHJFd3hQUHl0T3piZlMrdnFIbG52d1orTk5rdGMzWlNmUm05YmprTlRlSzlld2UzRFRkVHlGM21Wd21TNTlzTFh3a2dxWllBRGw4SlA4TjdmWjVGTjZPK1ZLNitIRnNsVVM5RHZpZTFMVHZGZmE0Mmtyem1CY3JSVGgzOFdpZHVwZktOUDhFTElxdzRHbXRKcko1Z3kiLCJtYWMiOiIyYjZiMDVkMDhiMmM2NTgxMjUxMTM3Yjg4M2MyYWI4MDA2YmQ1MDQxYTU5MzZjZDQ0MTNmYzY5OTY2NWIwZWRiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikw4b1B2WmMyNi9tcjQ0QjZ3NWdEc2c9PSIsInZhbHVlIjoidm5MclpIS2NOaEEyWG9sc3FyMFNiRjhaTjdVbWV6bXlEbDQ4RklSQXg0aUZWMGpTS1MrZ2pxMlJWQjhiMjYvdXdubkMzRnNDdUl3M2RlRHM3NnFUMVZkdjlnNGR5UjZtNW9xRXN4TmswRVVHYXhmb094TVNiZVBMeGFWV21LZFRNL2FRTGFoTnJCbm5LNGxpeEs0Z0dIbllOYlM1ZGViL29VMEpWZVhMVGsrMFJUUG5YZ0VDOCtyNkc3T3d5ZFpuR2RrT09sWVpKa2UyVDBZQ0NyYzhwYW9Fak0ycnBLWDZJdnpGMTR5RmpHYUZkR01waHZOcXJINzFHMWNlZ2s2L1JzbnNxRUYvQlpMaWtMNG5LaW53eVNmTE84ZG1KcFFoQ2dZSXA1TDN0U3FtbUFwbkFmT2R0ZEw2QkRSYklKVkZUQmZGdndjZkc1eDBnTXpTL1FmWSszWWtzMjQvTXZTL05ZRFlXQ3Uza1pIZXAxWnZSRVlWbHgvZFQvZUxSQWZad0t1UW9WUElYZ3hHMjU2L0Z2YWtybmdXeUpHOUJ3UzdkcHhSV1RGdWd5cHhKbEtiQzJiT0FROStidHNNcXVsSCtRck5HYkh5OVBBUXBUZFUyZ3RkQXV6eEx2S0M1UWxsck8xVU4vTGVLUXNuaFRhV1FnU0J2dW4vZ3BreW9qK3oiLCJtYWMiOiIzZDU5MzI4NDVkNzRiNmRkYmFiNDQ1OGY3ODMwYTFjYjBkYmNkYTI1MWQxMzVlMjMwZDQ5NWIzMjc4YTc0M2EyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFiWUt4SGw1VEd0V3FlMTZjcTdzSUE9PSIsInZhbHVlIjoibHU2cFlOclBYa25OeUFLOWdBM0VSdGZtejdGVnMycklKKy8wNHpFRThuc1lmdFczckQwNTZvakxlNGg4V0o5THlyNUUyczZsQU0yWHlIZWRSV09iNWZRMG5TTjI5cDhoL2pHYjd5a1RtUmR4b1kzSVM2eEhaeW0rK2x3OTVhdDJNQUhMWXlrQjREdVlZa2d4cVp6eWZWOGVFZnJIQVJqakw3Z28rbE9OSVZrdEVXOXBRU0ZnbU81d2xpRmpvb0liQ05wc1BMdFByM25xYVQvNDFGM3dsQXkvUkZ2T0pReVFBODBQaVFVU01mTi95SDBGZDh4eERXZXRjUXZ0Nm5VRHJ0bDRMVWw1c3BBOHdJcm9ENHdDU1JXRXdGUlZTSkh3VlpVSUNVaGNqc1d5dHFTRHBXYTNKYWJ5cld3aW9aZlBkV1B3RGFvd3JBZ2FKMmo0dHFQc3lzSE51dUNuVlJLUHM0b3VEMDRtWFFmMHJFd3hQUHl0T3piZlMrdnFIbG52d1orTk5rdGMzWlNmUm05YmprTlRlSzlld2UzRFRkVHlGM21Wd21TNTlzTFh3a2dxWllBRGw4SlA4TjdmWjVGTjZPK1ZLNitIRnNsVVM5RHZpZTFMVHZGZmE0Mmtyem1CY3JSVGgzOFdpZHVwZktOUDhFTElxdzRHbXRKcko1Z3kiLCJtYWMiOiIyYjZiMDVkMDhiMmM2NTgxMjUxMTM3Yjg4M2MyYWI4MDA2YmQ1MDQxYTU5MzZjZDQ0MTNmYzY5OTY2NWIwZWRiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikw4b1B2WmMyNi9tcjQ0QjZ3NWdEc2c9PSIsInZhbHVlIjoidm5MclpIS2NOaEEyWG9sc3FyMFNiRjhaTjdVbWV6bXlEbDQ4RklSQXg0aUZWMGpTS1MrZ2pxMlJWQjhiMjYvdXdubkMzRnNDdUl3M2RlRHM3NnFUMVZkdjlnNGR5UjZtNW9xRXN4TmswRVVHYXhmb094TVNiZVBMeGFWV21LZFRNL2FRTGFoTnJCbm5LNGxpeEs0Z0dIbllOYlM1ZGViL29VMEpWZVhMVGsrMFJUUG5YZ0VDOCtyNkc3T3d5ZFpuR2RrT09sWVpKa2UyVDBZQ0NyYzhwYW9Fak0ycnBLWDZJdnpGMTR5RmpHYUZkR01waHZOcXJINzFHMWNlZ2s2L1JzbnNxRUYvQlpMaWtMNG5LaW53eVNmTE84ZG1KcFFoQ2dZSXA1TDN0U3FtbUFwbkFmT2R0ZEw2QkRSYklKVkZUQmZGdndjZkc1eDBnTXpTL1FmWSszWWtzMjQvTXZTL05ZRFlXQ3Uza1pIZXAxWnZSRVlWbHgvZFQvZUxSQWZad0t1UW9WUElYZ3hHMjU2L0Z2YWtybmdXeUpHOUJ3UzdkcHhSV1RGdWd5cHhKbEtiQzJiT0FROStidHNNcXVsSCtRck5HYkh5OVBBUXBUZFUyZ3RkQXV6eEx2S0M1UWxsck8xVU4vTGVLUXNuaFRhV1FnU0J2dW4vZ3BreW9qK3oiLCJtYWMiOiIzZDU5MzI4NDVkNzRiNmRkYmFiNDQ1OGY3ODMwYTFjYjBkYmNkYTI1MWQxMzVlMjMwZDQ5NWIzMjc4YTc0M2EyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1079890152\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-692945146 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-692945146\", {\"maxDepth\":0})</script>\n"}}