{"__meta": {"id": "Xee9f0744c91e767103968069b6561233", "datetime": "2025-06-28 15:38:24", "utime": **********.677067, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.190118, "end": **********.677083, "duration": 0.4869649410247803, "duration_str": "487ms", "measures": [{"label": "Booting", "start": **********.190118, "relative_start": 0, "end": **********.612611, "relative_end": **********.612611, "duration": 0.42249298095703125, "duration_str": "422ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.612623, "relative_start": 0.42250490188598633, "end": **********.677084, "relative_end": 9.5367431640625e-07, "duration": 0.06446099281311035, "duration_str": "64.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45687592, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00347, "accumulated_duration_str": "3.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.648811, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.741}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.661209, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.741, "width_percent": 14.121}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6677759, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.862, "width_percent": 16.138}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2074774704 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2074774704\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2133523516 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2133523516\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2080622665 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080622665\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-821195266 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751124481231%7C1%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRWOEZKR2gweXlPOGh5K2RWaTJjTmc9PSIsInZhbHVlIjoiS2JZMDFjVXVxQzNMMW9vaml3V1ZRb2JCTVI4MnFvckVaT2RrNGw4QmFkVWtjWkJ4YWRpV0Z4bmt3QThzWVVzMUNWMUZFa08rSnR2amh3MmZUVnAvYVhPdmxiVEJDWDVpTnVFUDdzY1A3OFF1WXB1WkFBam1qYklFQXRoaENnTHZTWWp0V0x4L3ZSaE9GM2szM0dxVzM2NVFUSmgxY096YXgyY1kzYWczR21aai8remJIajh6Ync2Y2xPUk1qR0x3TW8xb1p3emx4Rk51djJwTFpubkoreXlLcjVoaFVtenV6ZFVSNWxiQlhxdkhRVlhxSHFCaVNBcGtpV0tmWUUwU29CcVg2QnBPeXExaUsvY0I1ZDY4b1FZek4yTHpLZVZrbHFpSHQ0UVArdEpHNzYzd2JQeWJwZDZMeEVmaDdtRHl1TjBUM1EwUnN6N05pd0RPdGV0dkJLUGltRWF0cDVPa01ZbUxNeDFmL000YjVjejBKWFBpdmdERjdqS09Td010YTNhRlhKWU05R01BWldKWlpmWVl3Z0p0S3BCRkoxcUtMR3ZJeUdZdXphTEJZN2I1RGhhR1grajl0UTYyKzRrYUhoaEVDMUprQXhvQVljQzQ0UXU3c0ZqaFlidHVMKzdDUy8yM2ExVVlYYmxYa05oOXRlRzVlbWpzK0s4RVh2dUIiLCJtYWMiOiIxZTRkNGJkZjQzNTFjM2Y0YjFiMGE0Yjk5OTlhYWY3YmJmZDIxZDJiMzc2ZTZkNGIxODM4YzFmODk2ZTBjMDYxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdOZDRxTnE2dWJQMzU1WjlVNExmTHc9PSIsInZhbHVlIjoiYzhNVzg2N2FZNjdFYWhEZi84dzJtWFZ2Y20vdEo4M2RHWndVZjFyYUZJTFk1Rk5iVjdkWHdEdGFiQkFlN3JEVktMdUNtWFBYMHpvZCtuMW1IVmVQRTVzQ2Q2c1dsTnlqV2lrcTQ2R0htZHNVMTRGR3lET1JFckY1eUM3NXNRb2VNOFU1c3o5b0ZRNXg1ZXlkWUVtQkRUZ2Q2QzNwbzUwYm1jYisyMWUzVGNQTS91Q1dkWXFvTEIvTElzVkxCSGpmZHo2dGppRU9HWk9VdDluL2xkOGh4dW8rZzZWN1RQUkF4dlR4ajhTQnkwc1lhN1pURmpHYkVrVlkzaDBuTXhmRXkrYkg2SkcrdDVhdGkvOVpaaEg5UStaaEZCamY4QTMrUU5UQTBibFoyMHo1OWV3amhNRlVvN2dWb1YrUFllTGFPMFVPVGVsOFpaSG0rM3g4bWNBbUVUT2tsVzJnZ0x4MEU1YjVrNTJXTkU0MVFXZ1BWMGh6SFUrcjBoS1YzWk4xNDQ5bDhCNWozL2FmVXJBVTMxVC9HUlJHTmJTMmhlOGVvelh1TnQyc0dqeDMvRTRrRllmdHZLcTY2Z1M4TTdkZXFrZGxzR0pYMVdyUDRIeVBiQlNVRWNiT0s4bm1GMGNYMEh6WExrQmErUk93Sm5tQXhOQ2lPSTJoVlFqOFp0L0kiLCJtYWMiOiI2Y2VjYTZmOWUyNDMyYWIxNGY0OTJmZjEwYjViODRhZWQwMTM3Yjk3ODg2ZDY2MWIzZTViM2E0YWQzMTVkMWQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821195266\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1521321387 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1521321387\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1223558226 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:38:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRrdjh1Q1I4TEE4ZHZaMXdXSmh6ZGc9PSIsInZhbHVlIjoiYThRYThZM1EreTBiR1BEY3A1RnRHaDFBVjZScjBUM1hZdlBydDlDMk84UXFYQWZpQ2ZFODhnYlVHM2hMOEJsUHZmOEE0U2QxSjdUVmptczBOaGI3SGt5WVJzSXpNSW1CbXM2NWF1dlNwTGwxY2hmd2g4WmFDNXVjaWlLRzZYZjdCWXliV0xXTisvWXdJRkptUUhBazhTL2lodkJ0YzhWNlBrZ284RmJGM3lwNmw0SkpCTnR2Q3FBUmtOdi81UjV2K1crZHNMR3lFWUlzMU1iVWswQmphQUlxTEdjenNNUnBLYnhNUUZCNTluRGpsNDlsb3N5MitTVUJmcVJBcVdtYXVRakt0ZjJOcEx5a1pReFg0eXFtWHNVR0kzTlE4VFdYRVpaZktHcExlSVplSGhFVHA3NG9yc2JId0VwczlZZmt6a2MwSVJMRmphYUtYeXRWd3lEbk56Q1JVazkzaVRTRitoZFQxSDQxMWFLVmJ4bTZpZ2ZlaDZMOHFJZm8xazdPWm5iaXlFQzI4Qmo1UmpmeG9sNXhmVUUxdnRpRkU5Rlh1UkZ2eFlxMkN2a0wxOVZmbGo4TkZLTkhKTHdtQkpaRm44Mmc3NGhaWFdTYjYyUG55KzJSUHVyVkpNYzdKWHFUNmJ4c25DLy9PNEE4MTd3Y3p6L3R2SThrc1hkMUdkVVEiLCJtYWMiOiI3NzlhNjVkMmVjNDI0YjExNGUwYmIwMzg0NTU2Mzc0M2RhMDc3NDU3ZmQ3MjVkMjMwN2VhMzc2MTY0MjY2MTk0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5DVUdqdDF3QXorK0U0blppZ0J6dEE9PSIsInZhbHVlIjoiOVgrT1BPalU2TnlkQWtocVdtd0pGYkFyQmxLVDNTZ2I4TXhIdittYzBjNmRQcERqaXpMc0ZnL3VvSEVhWjAxakc4aWg5MlhmTTk0WTNsNmh0dHF3RHJPb0dEd2JWTTBqd0hVSzlzWjdVdTJVbnBaeTVYT1JZWG9jVG05Ui9tTmxIK1JPa3dDb3hVRXpUUk56aEZ3NzlJdzhrUzg4U3FuT3d2TWJ0dkxUOEpzTEloZVZKcUdVT3QzUW05amZtRWZUaVg0QWl6NlhZTnJsU05SaVU0THJWbmtNOVh0ZjNEZE15L21RaTU5M1RkOU5tYklkUzJBM3N1aU9CMkNVellhRWdIeVNJS2xVZURaOVZpUXFQMVp5SlRDSENoMUpzTHBUbWRWOThJTUFSNW04RXlRbG5CNEk3aUJhcEhYSDNZTjdxQnIyOEZPYk5WUHlJSXNFTXZBTG0vSDZJRkZZblA4M1lKUTRxWEdLT2dJR0todmpNcGRuL3JFSmM1OERCNzdmVjJHTWk3ODNiTWpXV0dNTGU5WjdVVWVkWVRBeGlSSWd1MEgwK2JxT3BEc1l1YW0vU0xhMjJ5clF2OTFyVUQ3TGdKdUQwaW1RanNkY0dkMkFxeVZ0UElpeWhsNGpDOEJkL3RUTjV5ejdxaE1Ic214TkdmdWhFNXZNUkZINmNQWU4iLCJtYWMiOiIyMDIxNmVkYTJiNDI5YTVmMGFkOGI2ZWFmZDIxYjE0YzBlNjk5NjU4OTNmNGNlM2U0ZjQ1NTMwY2ZlNDIzY2ZhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:38:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRrdjh1Q1I4TEE4ZHZaMXdXSmh6ZGc9PSIsInZhbHVlIjoiYThRYThZM1EreTBiR1BEY3A1RnRHaDFBVjZScjBUM1hZdlBydDlDMk84UXFYQWZpQ2ZFODhnYlVHM2hMOEJsUHZmOEE0U2QxSjdUVmptczBOaGI3SGt5WVJzSXpNSW1CbXM2NWF1dlNwTGwxY2hmd2g4WmFDNXVjaWlLRzZYZjdCWXliV0xXTisvWXdJRkptUUhBazhTL2lodkJ0YzhWNlBrZ284RmJGM3lwNmw0SkpCTnR2Q3FBUmtOdi81UjV2K1crZHNMR3lFWUlzMU1iVWswQmphQUlxTEdjenNNUnBLYnhNUUZCNTluRGpsNDlsb3N5MitTVUJmcVJBcVdtYXVRakt0ZjJOcEx5a1pReFg0eXFtWHNVR0kzTlE4VFdYRVpaZktHcExlSVplSGhFVHA3NG9yc2JId0VwczlZZmt6a2MwSVJMRmphYUtYeXRWd3lEbk56Q1JVazkzaVRTRitoZFQxSDQxMWFLVmJ4bTZpZ2ZlaDZMOHFJZm8xazdPWm5iaXlFQzI4Qmo1UmpmeG9sNXhmVUUxdnRpRkU5Rlh1UkZ2eFlxMkN2a0wxOVZmbGo4TkZLTkhKTHdtQkpaRm44Mmc3NGhaWFdTYjYyUG55KzJSUHVyVkpNYzdKWHFUNmJ4c25DLy9PNEE4MTd3Y3p6L3R2SThrc1hkMUdkVVEiLCJtYWMiOiI3NzlhNjVkMmVjNDI0YjExNGUwYmIwMzg0NTU2Mzc0M2RhMDc3NDU3ZmQ3MjVkMjMwN2VhMzc2MTY0MjY2MTk0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5DVUdqdDF3QXorK0U0blppZ0J6dEE9PSIsInZhbHVlIjoiOVgrT1BPalU2TnlkQWtocVdtd0pGYkFyQmxLVDNTZ2I4TXhIdittYzBjNmRQcERqaXpMc0ZnL3VvSEVhWjAxakc4aWg5MlhmTTk0WTNsNmh0dHF3RHJPb0dEd2JWTTBqd0hVSzlzWjdVdTJVbnBaeTVYT1JZWG9jVG05Ui9tTmxIK1JPa3dDb3hVRXpUUk56aEZ3NzlJdzhrUzg4U3FuT3d2TWJ0dkxUOEpzTEloZVZKcUdVT3QzUW05amZtRWZUaVg0QWl6NlhZTnJsU05SaVU0THJWbmtNOVh0ZjNEZE15L21RaTU5M1RkOU5tYklkUzJBM3N1aU9CMkNVellhRWdIeVNJS2xVZURaOVZpUXFQMVp5SlRDSENoMUpzTHBUbWRWOThJTUFSNW04RXlRbG5CNEk3aUJhcEhYSDNZTjdxQnIyOEZPYk5WUHlJSXNFTXZBTG0vSDZJRkZZblA4M1lKUTRxWEdLT2dJR0todmpNcGRuL3JFSmM1OERCNzdmVjJHTWk3ODNiTWpXV0dNTGU5WjdVVWVkWVRBeGlSSWd1MEgwK2JxT3BEc1l1YW0vU0xhMjJ5clF2OTFyVUQ3TGdKdUQwaW1RanNkY0dkMkFxeVZ0UElpeWhsNGpDOEJkL3RUTjV5ejdxaE1Ic214TkdmdWhFNXZNUkZINmNQWU4iLCJtYWMiOiIyMDIxNmVkYTJiNDI5YTVmMGFkOGI2ZWFmZDIxYjE0YzBlNjk5NjU4OTNmNGNlM2U0ZjQ1NTMwY2ZlNDIzY2ZhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:38:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223558226\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1117779102 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1117779102\", {\"maxDepth\":0})</script>\n"}}