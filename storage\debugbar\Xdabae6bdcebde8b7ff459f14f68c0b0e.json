{"__meta": {"id": "Xdabae6bdcebde8b7ff459f14f68c0b0e", "datetime": "2025-06-28 16:35:01", "utime": **********.385284, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751128500.986892, "end": **********.385297, "duration": 0.3984050750732422, "duration_str": "398ms", "measures": [{"label": "Booting", "start": 1751128500.986892, "relative_start": 0, "end": **********.32961, "relative_end": **********.32961, "duration": 0.34271812438964844, "duration_str": "343ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.329617, "relative_start": 0.3427250385284424, "end": **********.385299, "relative_end": 1.9073486328125e-06, "duration": 0.05568194389343262, "duration_str": "55.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46463760, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2553\" onclick=\"\">app/Http/Controllers/PosController.php:2553-2587</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00229, "accumulated_duration_str": "2.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.367838, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.603}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3791502, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.603, "width_percent": 21.397}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1585503437 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1585503437\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128497517%7C54%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtOSXpBcVBubGRDTXBDMVlaWkhmckE9PSIsInZhbHVlIjoiNzB0TUJ5K2VOenMvT3JYNGF5N2xvMCtoNEkrK054MU5HZDgrcFFXdE5tOEYwMC9kSXhibGM0SWFsMlhUN09HZ3pXMnNxQnRrelNsS21ZZ3VZeThIa0FpTnQxMlI2OERBYTJnRTNValJGZklxcERSUDcrWFU0V285RWxvQjUyWjZCZUFSZE9VU2djV2hqUmJOMXBTVElrcXVzVmVqeHJBSktDRjJSMjdwMUJrK1Q4bDloZSt4VjlOcWZVMFB2ZW1HZTJBNmdjTUhncWhaV1BxM05WcFRBbWdIeTk0WlZmWkhIck0yM3ljcGFDME01TXhBODNnb2hXRE84UVpSUjlkeXJQSEhwYTQ0bTljQlJnR3JhZ2krdm5ZYkw2SURJc1A2eVdiR2hFQ1VLSW5QZTJQYmt1eWZabURIb0d4bEJWMWt0RHhtMWh4QVZ6bzEwTE4rRlhxV0FSMW5iMi9mNXNCQnpuYVNEelM4cmI5bitxMHhpTUFTaEFxWjNNdTdTZ0pEczVaR0lDRmVhQ2wvOFp6N2pyWWJYdFdXM25iR0Z3ZWNHRGNKemhTWEx3WVlxZWtSVi9TR2VCT0o3T1M2ZHlPRGhsUU5VemVTVExBVDdBWmZFSloxOUZGYnF2MDhvZDFqaHB2YWNWci9qSnkzQW4xenBjL1NFd1pHd0kvbTdnV3giLCJtYWMiOiJmZjRjOTYwYjExMjliZGMwNzU3NDk5ZjhjZjdmYTczMjg5N2RjMGQzNTJjYWYzZTAzZGE0Mzc4NGVjNWNkZmI3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ijh6aG5rNkYxN3I4RFRGY2JKMmJEYUE9PSIsInZhbHVlIjoicnZnVVlYYnNwWWx5WHp5bCttRjlNeEdnWVhNOVUvWjFPS2NlWEtYZnJha2lmUlBaaHFUWjJkZS9JNDgxbTNveHY5bTFuWEFXYmdBdWphTzBlYVFCZEpDM3RMUis3b2ExWkIraGROMUp4TmgxQm9IUGs5Rk16SktpSTJsSVMyRnBLUWlBOGppVjIxeElmTVk4ai9DQnIwQmFMbzlmUkNiQWpUZk92UXR3WG1CSFYvUFhOT2I2ZFhlS1VGMVpGdnVHVVpMSDFDaWlzN3puRGMxNkxiN0RwQTdrVjZvY0tSeGdrdFJJWURKd2pJYTkwakdzc3hxYlRjVmNMdU5RREJjaTZra3lTZEp1Y2hjSDQrMGt2cmoxWXdqekVBRHFYSnpoY012bGcyQjN3Unk1WGw1N05zNW91blFRQS9POTNDWEN4blA5d1NLcjlMUzA3eHRvVVowbU9XbXJHSUpCSWJsdEpwaXJYelpFZkpDUTZMK05YODhLVzQ2ckkxdXNNZHpvNFRaQUswQUtEVWRaUHdKYlp0cG5OSlRVWE05WklBd01WR2NFcTFwbnk1SzBFK1RXbFl3TGowczkrK3NMcHB6UnF0aStTYlFIVlRXMzV3S05GL0xET1YvUWZLYXU1bDgyeng0dk1SaFJIUGttcW1iNElOZFV3M1lsZDlhQzFYQmUiLCJtYWMiOiIyZDgxMGMwNGY0M2U1YTBmNWY1NGY3ODlhZmE2YjBmMzk2NjE3N2NhMjViNjEyOGM3ZmU3ZWFkOTcxOGJlYmQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-690846831 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690846831\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-167372390 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:35:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdxcGo3b1Z4aTFiS0ExYmsyQ1lERHc9PSIsInZhbHVlIjoiQW94T1Z4c3lRcVo1eUlFeDVJL3J0QTdhUVpvakgzRDNQS1BRWFRaOVVlWGtyNlZXTFNLSU9Nb3lKRmdJd0dZRytURG1RMmMzaVhkcVAwdXptQ0xIdXNuYXdpRVE0WUFQYnBmK092ZXc3TTBnczZsc3hiTTIxSHQwZDBjT2VwRXZ1ekFva3c3c2dTbG0xWmZVKzdsTlFudXpwSzNrdjNoVEpDVmdXMGNsYXVySlpad1NZc1hSaDRPWStnWWFSY0Jrb3dtMnhFcGhJMElnTnVoWEwyQXdlQzI2NlBjQ0txL2Y3RTQ2MDUva0VySlVnR0FQMzduSU1PUEVaMmxiakhKRy9scWFESTlWSktOVnpjb3NMaUhNeUlUbm1oK0JmaGJyWmdzVHVLV2owMGdwaVcrR1hIcTQ4L0VQQ1E1SitvQ2hZNlp5OHJWVFVCZ1JDV1NmaEIzdG9LYnZyeDV6WWVMazZGZm92ZkxVelptRWhVQkxEa3dTWnlqZlFMS0dhdkpZWm9jUFV5MWVaS1Noby8vR3RiWVZFZHhmd0FYV09DQ253aWkzVlU4eDFWbHFPbm9hdmZaaWtyQVV0VkRDSGtTUzdURUJNc085M0t5UmtFVHZhM1NTaTlvai84dFBuQ2hRMk00ZmllTlBWQUIzaEU4akhHWjloaUt6SnhqZnZhNWYiLCJtYWMiOiJhMzVjNDEzM2Q3YjdiYmNjMWVjN2FiMTY5NWRiYmVlZGQ1NjczYzIwZGZhOWQ1Zjc3OTNiMTgyYzA3Nzc1Njk1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlY2elpHSFNjcW1pYkhOZVJ5REw4cVE9PSIsInZhbHVlIjoiNXk5MmVpTFZES3l0R0p6Y2VIK1BDbHRtZStPeTQrOS95SkV4QSt1VytIT3FmcHhOME53NEcwWDNEb3hycm1wdUo0MXdodjdZMmRNd2hqdk5pZFVTK1kzbkd1aXg4Ni9sbFhOcnBhQzdmTjUwRGRvczErUGpQZmt2dldlQzJVdHBUNTJVK1E2ZG9Cb05xK21EeTMrOU0zTGt0MFdMYmorMGFCc3ZLS0xhQTNHTGNIQ21KL1JwYjZrdUlzVjJpQ3NpdGNsZkVhRDF0OCtreUY2WWZSZkhiWkU5RExZYVFFR2QrbVdCQ2Zjd1N4TE93K1lSRnlYVnJOcTRSMExaQzkyZFVOUjNQcm91dkEvZlF4ME11akdqUkprSERPUDdjcmpMWlB5bU52R0hyZjEwcWdCTmZwMnBJa0lVcXRxM1BsMWEzbUFnYlNqMExwVHJ2cDFyNWJDMXQzUm8xZmJiai8yK0VhMVRYV2ZLaTZiUXJ5b3daR3F2OWVleThVTSs5VGx6MUtRL0hwWENNS2Q5UjA3NUlLempkZ091Z3U2OGMwdlNmUnFIUHVuK2FvQ3hIdzBvSXF6VThkWStYNkRIVGRIQm5jcTFYcnV0bDRuN0dtSDZMYTMrR2ZtbnNsamh1bGtNVDlLTURrcEJTYzdDV3MwVGIvS3pNY0JucmlwQkdsRk8iLCJtYWMiOiJkYmVlYTVhMTE3M2Q1YWM4MWE0N2I3YzcwNDFjZjQxYThkY2I5NjA2ZjA1NmI5Y2RlN2FhZjNiZTc0NzZkZDczIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:35:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdxcGo3b1Z4aTFiS0ExYmsyQ1lERHc9PSIsInZhbHVlIjoiQW94T1Z4c3lRcVo1eUlFeDVJL3J0QTdhUVpvakgzRDNQS1BRWFRaOVVlWGtyNlZXTFNLSU9Nb3lKRmdJd0dZRytURG1RMmMzaVhkcVAwdXptQ0xIdXNuYXdpRVE0WUFQYnBmK092ZXc3TTBnczZsc3hiTTIxSHQwZDBjT2VwRXZ1ekFva3c3c2dTbG0xWmZVKzdsTlFudXpwSzNrdjNoVEpDVmdXMGNsYXVySlpad1NZc1hSaDRPWStnWWFSY0Jrb3dtMnhFcGhJMElnTnVoWEwyQXdlQzI2NlBjQ0txL2Y3RTQ2MDUva0VySlVnR0FQMzduSU1PUEVaMmxiakhKRy9scWFESTlWSktOVnpjb3NMaUhNeUlUbm1oK0JmaGJyWmdzVHVLV2owMGdwaVcrR1hIcTQ4L0VQQ1E1SitvQ2hZNlp5OHJWVFVCZ1JDV1NmaEIzdG9LYnZyeDV6WWVMazZGZm92ZkxVelptRWhVQkxEa3dTWnlqZlFMS0dhdkpZWm9jUFV5MWVaS1Noby8vR3RiWVZFZHhmd0FYV09DQ253aWkzVlU4eDFWbHFPbm9hdmZaaWtyQVV0VkRDSGtTUzdURUJNc085M0t5UmtFVHZhM1NTaTlvai84dFBuQ2hRMk00ZmllTlBWQUIzaEU4akhHWjloaUt6SnhqZnZhNWYiLCJtYWMiOiJhMzVjNDEzM2Q3YjdiYmNjMWVjN2FiMTY5NWRiYmVlZGQ1NjczYzIwZGZhOWQ1Zjc3OTNiMTgyYzA3Nzc1Njk1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlY2elpHSFNjcW1pYkhOZVJ5REw4cVE9PSIsInZhbHVlIjoiNXk5MmVpTFZES3l0R0p6Y2VIK1BDbHRtZStPeTQrOS95SkV4QSt1VytIT3FmcHhOME53NEcwWDNEb3hycm1wdUo0MXdodjdZMmRNd2hqdk5pZFVTK1kzbkd1aXg4Ni9sbFhOcnBhQzdmTjUwRGRvczErUGpQZmt2dldlQzJVdHBUNTJVK1E2ZG9Cb05xK21EeTMrOU0zTGt0MFdMYmorMGFCc3ZLS0xhQTNHTGNIQ21KL1JwYjZrdUlzVjJpQ3NpdGNsZkVhRDF0OCtreUY2WWZSZkhiWkU5RExZYVFFR2QrbVdCQ2Zjd1N4TE93K1lSRnlYVnJOcTRSMExaQzkyZFVOUjNQcm91dkEvZlF4ME11akdqUkprSERPUDdjcmpMWlB5bU52R0hyZjEwcWdCTmZwMnBJa0lVcXRxM1BsMWEzbUFnYlNqMExwVHJ2cDFyNWJDMXQzUm8xZmJiai8yK0VhMVRYV2ZLaTZiUXJ5b3daR3F2OWVleThVTSs5VGx6MUtRL0hwWENNS2Q5UjA3NUlLempkZ091Z3U2OGMwdlNmUnFIUHVuK2FvQ3hIdzBvSXF6VThkWStYNkRIVGRIQm5jcTFYcnV0bDRuN0dtSDZMYTMrR2ZtbnNsamh1bGtNVDlLTURrcEJTYzdDV3MwVGIvS3pNY0JucmlwQkdsRk8iLCJtYWMiOiJkYmVlYTVhMTE3M2Q1YWM4MWE0N2I3YzcwNDFjZjQxYThkY2I5NjA2ZjA1NmI5Y2RlN2FhZjNiZTc0NzZkZDczIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:35:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167372390\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}