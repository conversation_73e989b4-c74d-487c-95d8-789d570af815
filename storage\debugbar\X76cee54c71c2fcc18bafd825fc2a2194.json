{"__meta": {"id": "X76cee54c71c2fcc18bafd825fc2a2194", "datetime": "2025-06-28 16:30:26", "utime": **********.65282, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.224919, "end": **********.652837, "duration": 0.4279179573059082, "duration_str": "428ms", "measures": [{"label": "Booting", "start": **********.224919, "relative_start": 0, "end": **********.597882, "relative_end": **********.597882, "duration": 0.37296295166015625, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.59789, "relative_start": 0.3729708194732666, "end": **********.652839, "relative_end": 1.9073486328125e-06, "duration": 0.054949045181274414, "duration_str": "54.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45723696, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00259, "accumulated_duration_str": "2.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.624732, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.409}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.63438, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.409, "width_percent": 16.988}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6394598, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.398, "width_percent": 16.602}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1946715181 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1946715181\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1263480408 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751128218636%7C47%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik04QUdBMnArcElFVUZSWkg4NDMvZ3c9PSIsInZhbHVlIjoid3Vjb2VKUW1DR0lDcFFFaUx5WFBiYzVVWjFtWXNFcVhRcHpQbTM5dDlVeW9NY2hYenBzc0ZnU2tRV2FzbGRGRklGSXFMc2JYdWlnemFnbW14UFFFT2krRVJNUGVrYXp1YSswcWkwNTVzeEtMZDkrY2VBcjdRbWozM3dRaHZ5QWFDMnUwMGViaXNFT003TGQ5M2RDZ1JjaEd4dDc4NW9kS3IyLzlReVdkVHlyL3hlL1M0N2xVYnUvdk9Pd2F4eVFCMExrS01rTEpTUkVkam5GYitJY244NXQySVVXZXg0dWhXZlp2bzN1ZlRSM0JXMDBlTkw5Wk5kMkdJZnlNTTlZNndLVnhuSHpRSGRGaDUxNk5EcS9TMFBGRkorWVJ0VGkyM0FCcnRUNnlCbDlTakNmaXZ4TmFJZjBUbFBkM0NFVit1dWpxNkZUc1psYlZ2ZEo0YldOdFJ1a05taERMYXRMK2M3bmlHTjB5RmIxT0txaUFoUStYdWQ0ZFR1ZS9aNVRsazUzTFE5b0JhRGxYZ2ZkZ05OSkNrQ2lOY1VXMlRkSUw3dlVQTklwMDJjSDRWZzdPWFRibTVMQ3hHM0NwL3NsOThCazI1WUhzQllDaHltSC9RbjZHTjY2YmVFK3NRRkFYTmdRd2hIQzdyS3pTT0poRVd0TWxNSE54SWtJWDRTSXMiLCJtYWMiOiJmYTFjMWQ0YzY3Y2NlOTc2ZDVhMjliZGYxNGIxMWFkYTVhY2I0YjJlNTExY2NjMzJjOGU1NzAwY2E0NjgxYzA4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkxTN0U4K3RLM1FlYlBzZzdTYkpDRXc9PSIsInZhbHVlIjoiM2JwWjJNNUtNNkhYNTZYcEsySGFrOTFpekZQazVlQ2luYVdiYzAwUXJucEFvTG9vZ01SRXpCdDR3L280SVpoUEIxVnRnelNqZGdqYm5Mb0pQWmR2R0s5OFJXQ25VWXBkRmFtbXdvM2g3RExKNHRkWXRXd1A0ZlFPU3lwY3pTYmZZRWQ2OUJkd2lRTThqOHNXbVpHVHJHakZ5WUt0UEZkaWtxcjJWb1YvQ0l6RkJCRlBEOGNpSWYrTTBFQjNJQjk2U1crbURNSVpTNFNkK0NaVC9wUnViVE00VEZMVmVudmJIc2xxL2NiZU5Xd2pwZGVFeTFON01aaEsrQW1Ja3Jvem9pckE2ejNpTkN2dHJoRm0yQU5tamgya29ZMEV6WFdCRkoyUUlJUVUxcWZ4NStLWjdBa2huNUl5ZFBpaHBnS0gyeXZCL2NhOGI4WlRjaGt1Z1ZYbE1wY29xcEQ4c3kvNE90SjJSclNwNHRFZFF3N3Fka2gzcjZacWQ3N3NscUJvRmU5TTZBMXZFbVVZNzdseHBZVm15Zzh2ck1LdjdlTlpXZmtZaFBDMFlMdVpPTXlZWnptZnY0WUUvNmpoNUNqZ2RoNWc1YzNGcUowbjd0czNUa3hJalU5ZVpSbUNnWDVvNUxSaStQRFNiOHdnbjFRVkk0blNpeDhpeUVLaWlsdDUiLCJtYWMiOiJhMGQ2ZDcxNTc3MjdlODc2YWU2MTU3NmU3YzQ0ZDNiZGJmMjkyMWFmNWQ2Mzg1OGI4Mzk2ZmRmOThjNTVhODUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263480408\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-713497358 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713497358\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1571087062 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:30:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilh2ZEpYbmUreUVFUElPS0hRQk50TlE9PSIsInZhbHVlIjoiOS9saDMvYTAxcGlyZFJxcXl6UlVadEdscVhPTGJPOWpBZGdVUHhuYUtBdTRoOHZ1UTgxLzgrOUNUMGNBcmg1cUZxK1RVeEtDcEEzb0U1dUF0V3dQcXRpZVBVL0VubzVPU3oycEJwTUR2M05sMTBRTjNSTG1nWG5yTmZGRXF1NGF4S1c1Ukp5SExwejZJM0QrT2VDYnBMQ3hrTzFIM0kxMVBHZWNEVzlEOTFRNzBpVHcwSDNXKzBhS21WZnNURDFnaldJb2E5U1ZDNDR6YnoxNE1Bb3RRblhObkVNeis3ZWE0c2JSZURBZ2ZRMWxTTThub05qMWdvZmFZSGhxUHViYTZHODZ2RDUwWGl1MFJ0MmFQM21ZZFNwbDV4d0tjRXBNbmFyV0hQSmt3Q1ZRRmd3UEJhTk5zZEl4bldmSmdncjhMYjhIRnRmTUp4TlZoN0xEMHlPVnRTT01DWGN0NERmOVdCRllnK21Ec2l0VXp6S2JWZWhncnoyMmg5aHJWSkdLUElpdlB6TnY5aURKb2NmMkhDZm9LS0hDT0wwRXpqdEc2VThGS0JzazhHQkx6ZTNEYmRXaWUzeHkzVXBna3RXTzBLVTUrL3ZPUnpxMnMrcXp3UTBveGFRQVI1STZ1eWhoTmxYcjRQRjdBZ3FIYzdJeWRnL3dHQS9NdXJkek5XeXQiLCJtYWMiOiIzZWVkOWU0Nzc2N2IyZDliZDQ1ZWYxZjdjODcyMmU1NzlhYzZhOWY1YWI4YzkwYzUyMTg4YTA5MjkxNGRkYTdiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNaNUIycnFGMWNtaE5ENFBEVTgzeGc9PSIsInZhbHVlIjoiSUNKejRkQmg1MktXRS9vbzBPbEoxSm91Lyt2KzJqaG1uRWJ2NHdxZHMwSGRTQTFudzBibEtTTkU5eXVoV2kvYnQ3QTJZU0dPazQ1Q3hoeVZ0d09PQWp1UGhTdjNsYjZaai9mTDZyOXpnWTJqU0VPa3hjS3pMOHlGeVdtM0xmZEhEdzlmZEVMODdVdzlEQ1QzOW1pYVdUemRIaXlBNUVkemZObDhGRWE0TnF3QjhEMStvMEVtdUgvQytBem53Wlo0NEFFMjJUUXF6NDExc0JLbXZURVllS05NejV4aVpsajhPWFlCdUtnZGNOeEpwM0dkbUJRQ2xFejdoNUNISmkxZ2M2M0Nrb20vNWdqL01RUElDUHQ1dTVCSjhWamNaWGZVK21OQ0krNDh1L0M1dHdzL2lpaFd2RlpwekpUdUhteFlyNHN2Wnl4Y3dKQktBNFlwU0N3YVQxY0d0Y01VMWczWXFqOWRKS0RZNlRnWCtmTldkais4R296bWk5Z29CODJmV1pEU2xVb1ZOM2tWRkZ1N0poTjJLV1VscDRjL2FTM29vTDNHeHoyVng3S1RtQ2lqRktpRkswVXMxM2FoZTExeGZ2UUxReXE0ZEY5am5pNFdCWHkzUGRUSnk4d0Z6V3NyQzIvVFdsczVkcFFFbzduUG00SFoxcFprZEhlV0orTS8iLCJtYWMiOiI5NzA5ZGI1MzE0M2I5OTg1ZTI1YTUzMDA4Y2Y5MTZmZmYwNWE3MmQ5NTU0YzRmNjM2MzIyYjRiZGY2NTBkYWMwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:30:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilh2ZEpYbmUreUVFUElPS0hRQk50TlE9PSIsInZhbHVlIjoiOS9saDMvYTAxcGlyZFJxcXl6UlVadEdscVhPTGJPOWpBZGdVUHhuYUtBdTRoOHZ1UTgxLzgrOUNUMGNBcmg1cUZxK1RVeEtDcEEzb0U1dUF0V3dQcXRpZVBVL0VubzVPU3oycEJwTUR2M05sMTBRTjNSTG1nWG5yTmZGRXF1NGF4S1c1Ukp5SExwejZJM0QrT2VDYnBMQ3hrTzFIM0kxMVBHZWNEVzlEOTFRNzBpVHcwSDNXKzBhS21WZnNURDFnaldJb2E5U1ZDNDR6YnoxNE1Bb3RRblhObkVNeis3ZWE0c2JSZURBZ2ZRMWxTTThub05qMWdvZmFZSGhxUHViYTZHODZ2RDUwWGl1MFJ0MmFQM21ZZFNwbDV4d0tjRXBNbmFyV0hQSmt3Q1ZRRmd3UEJhTk5zZEl4bldmSmdncjhMYjhIRnRmTUp4TlZoN0xEMHlPVnRTT01DWGN0NERmOVdCRllnK21Ec2l0VXp6S2JWZWhncnoyMmg5aHJWSkdLUElpdlB6TnY5aURKb2NmMkhDZm9LS0hDT0wwRXpqdEc2VThGS0JzazhHQkx6ZTNEYmRXaWUzeHkzVXBna3RXTzBLVTUrL3ZPUnpxMnMrcXp3UTBveGFRQVI1STZ1eWhoTmxYcjRQRjdBZ3FIYzdJeWRnL3dHQS9NdXJkek5XeXQiLCJtYWMiOiIzZWVkOWU0Nzc2N2IyZDliZDQ1ZWYxZjdjODcyMmU1NzlhYzZhOWY1YWI4YzkwYzUyMTg4YTA5MjkxNGRkYTdiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNaNUIycnFGMWNtaE5ENFBEVTgzeGc9PSIsInZhbHVlIjoiSUNKejRkQmg1MktXRS9vbzBPbEoxSm91Lyt2KzJqaG1uRWJ2NHdxZHMwSGRTQTFudzBibEtTTkU5eXVoV2kvYnQ3QTJZU0dPazQ1Q3hoeVZ0d09PQWp1UGhTdjNsYjZaai9mTDZyOXpnWTJqU0VPa3hjS3pMOHlGeVdtM0xmZEhEdzlmZEVMODdVdzlEQ1QzOW1pYVdUemRIaXlBNUVkemZObDhGRWE0TnF3QjhEMStvMEVtdUgvQytBem53Wlo0NEFFMjJUUXF6NDExc0JLbXZURVllS05NejV4aVpsajhPWFlCdUtnZGNOeEpwM0dkbUJRQ2xFejdoNUNISmkxZ2M2M0Nrb20vNWdqL01RUElDUHQ1dTVCSjhWamNaWGZVK21OQ0krNDh1L0M1dHdzL2lpaFd2RlpwekpUdUhteFlyNHN2Wnl4Y3dKQktBNFlwU0N3YVQxY0d0Y01VMWczWXFqOWRKS0RZNlRnWCtmTldkais4R296bWk5Z29CODJmV1pEU2xVb1ZOM2tWRkZ1N0poTjJLV1VscDRjL2FTM29vTDNHeHoyVng3S1RtQ2lqRktpRkswVXMxM2FoZTExeGZ2UUxReXE0ZEY5am5pNFdCWHkzUGRUSnk4d0Z6V3NyQzIvVFdsczVkcFFFbzduUG00SFoxcFprZEhlV0orTS8iLCJtYWMiOiI5NzA5ZGI1MzE0M2I5OTg1ZTI1YTUzMDA4Y2Y5MTZmZmYwNWE3MmQ5NTU0YzRmNjM2MzIyYjRiZGY2NTBkYWMwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:30:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1571087062\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}