{"__meta": {"id": "Xc23164a96885e2526c142f6235a5ca95", "datetime": "2025-06-28 15:46:38", "utime": **********.136526, "method": "GET", "uri": "/financial-operations/product-analytics/top-selling?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125597.607648, "end": **********.13654, "duration": 0.5288920402526855, "duration_str": "529ms", "measures": [{"label": "Booting", "start": 1751125597.607648, "relative_start": 0, "end": **********.035938, "relative_end": **********.035938, "duration": 0.42829012870788574, "duration_str": "428ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.035946, "relative_start": 0.4282979965209961, "end": **********.136542, "relative_end": 2.1457672119140625e-06, "duration": 0.10059618949890137, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47299888, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/top-selling", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getTopSellingProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.top-selling", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=206\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:206-364</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02994, "accumulated_duration_str": "29.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0746, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 6.413}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.085887, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 6.413, "width_percent": 1.169}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 235}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.089416, "duration": 0.01045, "duration_str": "10.45ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:235", "source": "app/Http/Controllers/ProductAnalyticsController.php:235", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=235", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "235"}, "connection": "kdmkjkqknb", "start_percent": 7.582, "width_percent": 34.903}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 240}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.102488, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:240", "source": "app/Http/Controllers/ProductAnalyticsController.php:240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=240", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "240"}, "connection": "kdmkjkqknb", "start_percent": 42.485, "width_percent": 9.686}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_v2_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos_v2` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 260}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.107562, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:260", "source": "app/Http/Controllers/ProductAnalyticsController.php:260", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=260", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "260"}, "connection": "kdmkjkqknb", "start_percent": 52.171, "width_percent": 2.806}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, COALESCE(SUM(wp.quantity), 0) as stock_quantity from `product_services` as `ps` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` where `ps`.`id` in (1325, 298, 299, 1808, 165, 1458, 1247, 310, 311, 639, 164, 1297, 1083, 1697, 1102, 1195, 993, 1829, 162, 1287, 2121, 155, 309, 2028, 1668, 1701, 143, 457, 1090, 159, 160, 476, 674, 1745, 312, 163, 175, 1743, 2007, 18, 300, 484, 1096, 1101, 1415, 681, 1106, 1197, 1467, 1661, 1688, 1859, 1981, 896, 1112, 1118, 1744, 2165, 157, 231, 597, 1081, 1563, 2170, 924, 1088, 1105, 1110, 1111, 1323, 156, 237, 976, 1117, 1147, 1294, 1803, 1806, 1894, 2240, 166, 179, 433, 1092, 1126, 1343, 1681, 1702, 1989, 2041, 2074, 2299, 158, 242, 385, 516, 753, 972, 1181, 1338, 1461, 1767, 1846, 1899, 1979, 1983, 1995, 2256, 46, 170, 176, 456, 568, 758, 1009, 1099, 1103, 1116, 1312, 1403, 1473, 1527, 1667, 1693, 1705, 1716, 1727, 1967, 2018, 3, 142, 232, 596, 611, 751, 1084, 1104, 1319, 1353, 1363, 1405, 1663, 1807, 1838, 1960, 2077, 2116, 2120, 2172, 2286, 145, 251, 448, 470, 595, 606, 607, 608, 1040, 1086, 1108, 1114, 1115, 1214, 1638, 1650, 1655, 1813, 2015, 2186, 2187, 2297, 215, 313, 383, 386, 423, 429, 449, 474, 496, 598, 754, 1119, 1139, 1207, 1265, 1282, 1296, 1324, 1354, 1452, 1455, 1541, 1543, 1660, 1686, 1699, 1723, 1726, 1766, 1809, 1820, 1844, 1897, 1962, 1970, 1973, 1975, 1984, 2029, 2073, 2119, 2146, 2193, 2208, 2266, 2, 7, 147, 148, 150, 152, 153, 171, 250, 254, 337, 445, 462, 464, 515, 522, 668, 669, 1053, 1094, 1253, 1313, 1315, 1322, 1374, 1376, 1388, 1510, 1651, 1653, 1683, 1718, 1746, 1817, 1830, 1843, 1880, 1944, 1964, 1980, 1985, 2014, 2016, 2042, 2072, 2118, 2164, 2225, 2226, 2233, 2239, 2300, 144, 146, 180, 183, 218, 269, 350, 356, 359, 362, 384, 427, 440, 465, 490, 495, 497, 500, 512, 518, 521, 603, 638, 666, 757, 760, 952, 1025, 1100, 1156, 1191, 1270, 1298, 1306, 1372, 1385, 1407, 1475, 1515, 1643, 1645, 1647, 1659, 1671, 1685, 1687, 1704, 1706, 1717, 1733, 1763, 1802, 1825, 1839, 1842, 1845, 1851, 1895, 1956, 1965, 1968, 1982, 1994, 2017, 2030, 2114, 2145, 2149, 2155, 2162, 2167, 2171, 2174, 2177, 2183, 2224, 2232, 2236, 2237, 2254, 2255, 40, 41, 45, 72, 88, 119, 151, 181, 202, 219, 236, 238, 243, 246, 252, 253, 255, 314, 324, 326, 330, 353, 354, 358, 367, 393, 432, 446, 450, 460, 461, 468, 478, 479, 548, 556, 562, 566, 591, 605, 632, 641, 836, 900, 942, 977, 1010, 1034, 1044, 1095, 1107, 1131, 1140, 1144, 1198, 1275, 1286, 1302, 1348, 1370, 1408, 1428, 1436, 1440, 1443, 1517, 1519, 1537, 1550, 1646, 1673, 1690, 1695, 1696, 1703, 1720, 1724, 1725, 1728, 1740, 1761, 1779, 1810, 1816, 1822, 1823, 1892, 1893, 1898, 1955, 1969, 1971, 1978, 1987, 2000, 2025, 2026, 2027, 2076, 2086, 2103, 2112, 2117, 2138, 2156, 2158, 2173, 2188, 2192, 2197, 2201, 2204, 2213, 2220, 2228, 2231, 2244, 2295, 2296, 2301, 6, 11, 13, 19, 31, 33, 38, 43, 80, 93, 109, 112, 114, 154, 161, 168, 173, 185, 207, 216, 222, 228, 239, 247, 261, 291, 302, 339, 346, 348, 349, 351, 352, 361, 400, 406, 434, 435, 436, 439, 441, 451, 452, 455, 469, 471, 481, 485, 488, 498, 514, 523, 525, 531, 535, 538, 542, 543, 558, 565, 574, 609, 610, 613, 618, 637, 648, 649, 664, 667, 714, 747, 755, 803, 842, 846, 857, 904, 905, 910, 918, 928, 941, 1001, 1014, 1023, 1038, 1049, 1054, 1091, 1098, 1134, 1135, 1149, 1157, 1177, 1184, 1199, 1204, 1248, 1263, 1266, 1273, 1277, 1280, 1283, 1285, 1300, 1305, 1308, 1316, 1317, 1358, 1371, 1406, 1438, 1457, 1459, 1470, 1474, 1478, 1495, 1502, 1511, 1512, 1522, 1530, 1536, 1545, 1553, 1627, 1633, 1634, 1649, 1677, 1694, 1698, 1707, 1708, 1709, 1711, 1712, 1713, 1715, 1719, 1721, 1722, 1732, 1748, 1750, 1755, 1757, 1762, 1765, 1789, 1814, 1819, 1821, 1824, 1826, 1828, 1840, 1841, 1847, 1849, 1863, 1881, 1896, 1906, 1910, 1914, 1915, 1920, 1937, 1941, 1953, 1959, 1961, 1966, 1986, 1991, 1996, 2001, 2010, 2034, 2037, 2040, 2049, 2050, 2057, 2058, 2068, 2070, 2081, 2087, 2098, 2099, 2100, 2113, 2115, 2122, 2124, 2131, 2141, 2151, 2152, 2153, 2157, 2159, 2181, 2189, 2190, 2194, 2199, 2202, 2206, 2211, 2214, 2217, 2227, 2230, 2241, 2249, 2252, 2262, 2268, 2285, 2292, 2303, 2305, 2306, 2307) and `ps`.`created_by` = 15 group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name`, `psu`.`name`", "type": "query", "params": [], "bindings": ["1325", "298", "299", "1808", "165", "1458", "1247", "310", "311", "639", "164", "1297", "1083", "1697", "1102", "1195", "993", "1829", "162", "1287", "2121", "155", "309", "2028", "1668", "1701", "143", "457", "1090", "159", "160", "476", "674", "1745", "312", "163", "175", "1743", "2007", "18", "300", "484", "1096", "1101", "1415", "681", "1106", "1197", "1467", "1661", "1688", "1859", "1981", "896", "1112", "1118", "1744", "2165", "157", "231", "597", "1081", "1563", "2170", "924", "1088", "1105", "1110", "1111", "1323", "156", "237", "976", "1117", "1147", "1294", "1803", "1806", "1894", "2240", "166", "179", "433", "1092", "1126", "1343", "1681", "1702", "1989", "2041", "2074", "2299", "158", "242", "385", "516", "753", "972", "1181", "1338", "1461", "1767", "1846", "1899", "1979", "1983", "1995", "2256", "46", "170", "176", "456", "568", "758", "1009", "1099", "1103", "1116", "1312", "1403", "1473", "1527", "1667", "1693", "1705", "1716", "1727", "1967", "2018", "3", "142", "232", "596", "611", "751", "1084", "1104", "1319", "1353", "1363", "1405", "1663", "1807", "1838", "1960", "2077", "2116", "2120", "2172", "2286", "145", "251", "448", "470", "595", "606", "607", "608", "1040", "1086", "1108", "1114", "1115", "1214", "1638", "1650", "1655", "1813", "2015", "2186", "2187", "2297", "215", "313", "383", "386", "423", "429", "449", "474", "496", "598", "754", "1119", "1139", "1207", "1265", "1282", "1296", "1324", "1354", "1452", "1455", "1541", "1543", "1660", "1686", "1699", "1723", "1726", "1766", "1809", "1820", "1844", "1897", "1962", "1970", "1973", "1975", "1984", "2029", "2073", "2119", "2146", "2193", "2208", "2266", "2", "7", "147", "148", "150", "152", "153", "171", "250", "254", "337", "445", "462", "464", "515", "522", "668", "669", "1053", "1094", "1253", "1313", "1315", "1322", "1374", "1376", "1388", "1510", "1651", "1653", "1683", "1718", "1746", "1817", "1830", "1843", "1880", "1944", "1964", "1980", "1985", "2014", "2016", "2042", "2072", "2118", "2164", "2225", "2226", "2233", "2239", "2300", "144", "146", "180", "183", "218", "269", "350", "356", "359", "362", "384", "427", "440", "465", "490", "495", "497", "500", "512", "518", "521", "603", "638", "666", "757", "760", "952", "1025", "1100", "1156", "1191", "1270", "1298", "1306", "1372", "1385", "1407", "1475", "1515", "1643", "1645", "1647", "1659", "1671", "1685", "1687", "1704", "1706", "1717", "1733", "1763", "1802", "1825", "1839", "1842", "1845", "1851", "1895", "1956", "1965", "1968", "1982", "1994", "2017", "2030", "2114", "2145", "2149", "2155", "2162", "2167", "2171", "2174", "2177", "2183", "2224", "2232", "2236", "2237", "2254", "2255", "40", "41", "45", "72", "88", "119", "151", "181", "202", "219", "236", "238", "243", "246", "252", "253", "255", "314", "324", "326", "330", "353", "354", "358", "367", "393", "432", "446", "450", "460", "461", "468", "478", "479", "548", "556", "562", "566", "591", "605", "632", "641", "836", "900", "942", "977", "1010", "1034", "1044", "1095", "1107", "1131", "1140", "1144", "1198", "1275", "1286", "1302", "1348", "1370", "1408", "1428", "1436", "1440", "1443", "1517", "1519", "1537", "1550", "1646", "1673", "1690", "1695", "1696", "1703", "1720", "1724", "1725", "1728", "1740", "1761", "1779", "1810", "1816", "1822", "1823", "1892", "1893", "1898", "1955", "1969", "1971", "1978", "1987", "2000", "2025", "2026", "2027", "2076", "2086", "2103", "2112", "2117", "2138", "2156", "2158", "2173", "2188", "2192", "2197", "2201", "2204", "2213", "2220", "2228", "2231", "2244", "2295", "2296", "2301", "6", "11", "13", "19", "31", "33", "38", "43", "80", "93", "109", "112", "114", "154", "161", "168", "173", "185", "207", "216", "222", "228", "239", "247", "261", "291", "302", "339", "346", "348", "349", "351", "352", "361", "400", "406", "434", "435", "436", "439", "441", "451", "452", "455", "469", "471", "481", "485", "488", "498", "514", "523", "525", "531", "535", "538", "542", "543", "558", "565", "574", "609", "610", "613", "618", "637", "648", "649", "664", "667", "714", "747", "755", "803", "842", "846", "857", "904", "905", "910", "918", "928", "941", "1001", "1014", "1023", "1038", "1049", "1054", "1091", "1098", "1134", "1135", "1149", "1157", "1177", "1184", "1199", "1204", "1248", "1263", "1266", "1273", "1277", "1280", "1283", "1285", "1300", "1305", "1308", "1316", "1317", "1358", "1371", "1406", "1438", "1457", "1459", "1470", "1474", "1478", "1495", "1502", "1511", "1512", "1522", "1530", "1536", "1545", "1553", "1627", "1633", "1634", "1649", "1677", "1694", "1698", "1707", "1708", "1709", "1711", "1712", "1713", "1715", "1719", "1721", "1722", "1732", "1748", "1750", "1755", "1757", "1762", "1765", "1789", "1814", "1819", "1821", "1824", "1826", "1828", "1840", "1841", "1847", "1849", "1863", "1881", "1896", "1906", "1910", "1914", "1915", "1920", "1937", "1941", "1953", "1959", "1961", "1966", "1986", "1991", "1996", "2001", "2010", "2034", "2037", "2040", "2049", "2050", "2057", "2058", "2068", "2070", "2081", "2087", "2098", "2099", "2100", "2113", "2115", "2122", "2124", "2131", "2141", "2151", "2152", "2153", "2157", "2159", "2181", "2189", "2190", "2194", "2199", "2202", "2206", "2211", "2214", "2217", "2227", "2230", "2241", "2249", "2252", "2262", "2268", "2285", "2292", "2303", "2305", "2306", "2307", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 319}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.111649, "duration": 0.01348, "duration_str": "13.48ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:319", "source": "app/Http/Controllers/ProductAnalyticsController.php:319", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=319", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "319"}, "connection": "kdmkjkqknb", "start_percent": 54.977, "width_percent": 45.023}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/top-selling", "status_code": "<pre class=sf-dump id=sf-dump-949421878 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-949421878\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2007941809 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2007941809\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-151977927 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-151977927\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-789887582 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125596119%7C20%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkUzYnhjOElieXBhUDVIM0JOVXpzREE9PSIsInZhbHVlIjoiUUFhQmFuMGRBdktZTUVjOTdkSCtrMzJNWjZyK0dxNy94OWo3c3ZiNXZqeFRzVVg0WUNoUG5TcnZZc2hhem1nZ2g1bGpIQ3ZSejU4azJkTHovRHFpMm82M0l3SWFhemxFL0c5bVdVVlAxemlzcGdaM25RQTdodlN5RjZ1R1VGZlBlaHR1VVRzcWIxM2t5NXVIU2VDaVUrdmVwVnVibkFZc28wVXI4QnNSU3NyT29WTCtLNDBaOHJVdWVvVDFSOEJTUDFXbE1jTXpjNkJOZjZvZHdRUyt0SlY3R092VkVJOWE1Y1VQTElOQzhlSkFzS3NhbmJVS2VETXI5NEhNbEJ1REpzenNhSjkxa1U2TmxQbE5XZXNqekpKVG1TdXB1S3Y5NkdKSlZBSjduYk5tdmIwZUlkR3o2U0RpSDN1dEQ4M2d0ZHhMQ2s4Wmg0bVNNS1pROHBoZFNxWHRJUVEvNit6U0dnNUFrRmNZeDl4eFp5dFQrWUlYd2ovVElGQXMzb3dKSTQ3cWJtcENOUmlZVFdhUWJPZGlLZmY5OTlZU09Ybk9UZ3FMVU1KQUNTcjFGUWYvL2VRRUZmL1ZZL08vSzJGdWpiOVdUNlhRazAwbW1EMmNqUUVxWVJvZHBHQUNrQmdGOW92Zk1HVWJoSTRaZTNZWWJyQWFjTGNSVExhZUEvV3MiLCJtYWMiOiJjOWFlYmUxMjk2YWQ4NGM3YThjY2IwM2E2NDBhNTQxZTU4NGRhOTkxN2VjMTM4YmYwZDdhMWUxNjlmZGNlZmZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVUMHJ4RG5OZlBGNWNMRTN0THAzdUE9PSIsInZhbHVlIjoiS2t4a2s1OEZmakR6NkhSRXBSMFVuaHgxQmVVYmNTeW85ZzFac0t2eXI2Y0syQXI5UUJIYUJoSmJLUFYrMXRiMWExUFJPc2ZTMWdTdlQyVFdodEFOdUJEdjdtVFBHekNBODFPbk94NGgrNGRoVDduSTFKa2xtWkIrbEZ6enBldkllNktydmowd3J3UDhPSWxvUSt0SFVsMU1vdzJMNHdNcmRNMDBtZ1A2UHFhdFpsSlFTNnl0ZkVBVDA5MlZHWGZBbktZSmg0cVE5NXJ0SDUweUl0NE12NnRMd1VMc1V3OWw1UlRzek85eGlWd2NFTjZtSkh3YVo1QlRIeXNseGhqUzF6SVVjTUE0NEpjNDVNUUhDNE5tYkp3Zmp0UjdDUjJQRmJmRFB4Z0taQlA1dWI4Wkw0S1NjaFJnalJ3U1VWaGpDd3VNOWFqemVaYlFwcGxzMEh0WXVQaGpHb2YvUTZtMW9wQ1lRbGZUVEFKWjZPaG9ab3pWdUVvYkFnZmUrYk12aVpzZ0dYQkh2UmNZRXFRMlBSWkZHeTJ5TEdkc2NCelgzMzZ5YmVkTnZYUkwvaEp6REpVTGxPWXVrMnlCTncwR1hwZzYyZ3BEekxOOFpPVUMwb29KNGgwUVFkTzV5ZTFHTDBlYll6OEs3dUJNTnFybTVxd25tTTgzT2J0OXJIR1giLCJtYWMiOiI3ZGRhNmI0YmEyMzJmMWM2YjNmNWEzMzJjYjA5NzA1YWM4YjUxY2Y0NDY5ZTI2ODY1OTY4ZGJlMGI5NzYwNTgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-789887582\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-57902076 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57902076\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1035344916 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:46:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhHd0w0a0UwOWR5VmYxRm1ubEQyUUE9PSIsInZhbHVlIjoiRnVQVU9BZWdWdTFCUWcxcjZrU2ZJME9TMzhxOTd4bFZYeVAvUFJPTzd4NHR5S3VVVkZhVFZkUVRKU0xJVGlGYUFCVnJROFZlRHN6cnh1WFNsMXJ4OE45azZmclVOandmZjdUalV2amsrVWJqZXdUbGpiTTBWMTFtYUcvK1JZOEcvNlZ3SStHMlI0bHl1dWxJNloyQ3FIZWo4S1FNdHNHT0E1dk5lY1M4bTZUaDI0V0ZuZzQrS1lmeEVITnFsaVNWam1yZTdDYzhESlFxRWpHTktFUTRibnJxYndjS2xtZ2hWRUd1NXd0N0JhTjJENzBXSXlJaTRwdVRBZTY4Y0wrT0ZMbGlHdWZFVWx0OFREbGJzb1R2b3NHNlpUNXBmdTAyTEtYZFM2SE9jL2hGRHlFeC9ZNWNNYysyVGNBVmtuRSt3djVkek9OYkFkYlVzS2paOGNBaS8zRVpGcUdlTG9wY3NGZFdEU3ZNTStBSmdtRUR3ZitWSTdBeG56TmJPMWpYSFdQcnE2akJQQkdicXRUSnozZ09FNkd3QWNYVEtHQW5FRnNSTVVFSUx5VmNkZVMrbW1iYjdRMzliekNSZEdrdXJFZEFtM1YwbXhYK04yL25HcnVvcTk2QVlEV0RZS0hFVWVWdXZwNEZ0bEFHa0c1aXY1QzJENzByNU1LM3JxY1kiLCJtYWMiOiJhNjA3Zjk4OTMxM2RjMjI1NmVkMGE1NzY3NzBiMzZiYjlkZTVlMzE1MTE2NjIzZjYxZjhlYTkyNThkNjM4NjIyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNTTmIremxuN2Ria00wRm0zS25lZHc9PSIsInZhbHVlIjoiY0V5elVNUlBCc2MrRVJmbG5XTzVaYjJyYXd3N2FGUlJId1kwT1BnM3Y3SSs3Ky9OdzNLRjFxd0pKRGtqSjZMWnVTSk10aDI2ZmtxdkN6NGJ2aUpoVHcyb2xITk9ocWV2VHlldjVveVVUUXhCcWJvdnRuMnhrWFJITWppY2llVHVTcVJCUCtiY0FmMm5IWWZuNmNrY01HWjBjMStEMmdHZkJsaWd5NURCeWs1N2ozR2pQVFh2bGg0aWZZMUJtVXM4cnU4ZU1GZFI0dk1zVnhkTERJdnhwSG50Ym1kQ1lZcXhKTHoycVRJbDBGNUFGR0RJbXBFS3BxZGR3ZllSVEhGazhBN0NxZU1BU3RvS2RDOUVtc0xIMU1UZ3gvYW5lVmw0eWxFaU8wQ3hmellVM2VYUmRiMVk1bkNJRFpTQW0yelZNZUxTZlNKWGdSWDFCTEE3am50TkppUHZWRFJDUk01SGpyVDlNMnN3Nk8zbm55dy9JUVozT3JkVnR0OE0vUVBmaW4yM2I1THQ3eC9JellSUkZTbHB6c3htVjBuNVhkYzlyR2JOTWRiRU11eVJGTytsR2RUS0k2eTF2VWszTElBaXhrL1pSVWt3RW0za1JZUHByNUtRQ1V1NGVzUVlpYzZQeXdMR3d3ZjNwMWZKNGN0NkVMajdzQWFCQVlEUjBLM1UiLCJtYWMiOiJiZWFhZTM5YmJlMGI5MGUxODBhZjNhMGFmN2UxYjkzMjk3MzMwNDU1MTRhMWQyMzg2MGIyMzJmZDgyYzFlNWY1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhHd0w0a0UwOWR5VmYxRm1ubEQyUUE9PSIsInZhbHVlIjoiRnVQVU9BZWdWdTFCUWcxcjZrU2ZJME9TMzhxOTd4bFZYeVAvUFJPTzd4NHR5S3VVVkZhVFZkUVRKU0xJVGlGYUFCVnJROFZlRHN6cnh1WFNsMXJ4OE45azZmclVOandmZjdUalV2amsrVWJqZXdUbGpiTTBWMTFtYUcvK1JZOEcvNlZ3SStHMlI0bHl1dWxJNloyQ3FIZWo4S1FNdHNHT0E1dk5lY1M4bTZUaDI0V0ZuZzQrS1lmeEVITnFsaVNWam1yZTdDYzhESlFxRWpHTktFUTRibnJxYndjS2xtZ2hWRUd1NXd0N0JhTjJENzBXSXlJaTRwdVRBZTY4Y0wrT0ZMbGlHdWZFVWx0OFREbGJzb1R2b3NHNlpUNXBmdTAyTEtYZFM2SE9jL2hGRHlFeC9ZNWNNYysyVGNBVmtuRSt3djVkek9OYkFkYlVzS2paOGNBaS8zRVpGcUdlTG9wY3NGZFdEU3ZNTStBSmdtRUR3ZitWSTdBeG56TmJPMWpYSFdQcnE2akJQQkdicXRUSnozZ09FNkd3QWNYVEtHQW5FRnNSTVVFSUx5VmNkZVMrbW1iYjdRMzliekNSZEdrdXJFZEFtM1YwbXhYK04yL25HcnVvcTk2QVlEV0RZS0hFVWVWdXZwNEZ0bEFHa0c1aXY1QzJENzByNU1LM3JxY1kiLCJtYWMiOiJhNjA3Zjk4OTMxM2RjMjI1NmVkMGE1NzY3NzBiMzZiYjlkZTVlMzE1MTE2NjIzZjYxZjhlYTkyNThkNjM4NjIyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNTTmIremxuN2Ria00wRm0zS25lZHc9PSIsInZhbHVlIjoiY0V5elVNUlBCc2MrRVJmbG5XTzVaYjJyYXd3N2FGUlJId1kwT1BnM3Y3SSs3Ky9OdzNLRjFxd0pKRGtqSjZMWnVTSk10aDI2ZmtxdkN6NGJ2aUpoVHcyb2xITk9ocWV2VHlldjVveVVUUXhCcWJvdnRuMnhrWFJITWppY2llVHVTcVJCUCtiY0FmMm5IWWZuNmNrY01HWjBjMStEMmdHZkJsaWd5NURCeWs1N2ozR2pQVFh2bGg0aWZZMUJtVXM4cnU4ZU1GZFI0dk1zVnhkTERJdnhwSG50Ym1kQ1lZcXhKTHoycVRJbDBGNUFGR0RJbXBFS3BxZGR3ZllSVEhGazhBN0NxZU1BU3RvS2RDOUVtc0xIMU1UZ3gvYW5lVmw0eWxFaU8wQ3hmellVM2VYUmRiMVk1bkNJRFpTQW0yelZNZUxTZlNKWGdSWDFCTEE3am50TkppUHZWRFJDUk01SGpyVDlNMnN3Nk8zbm55dy9JUVozT3JkVnR0OE0vUVBmaW4yM2I1THQ3eC9JellSUkZTbHB6c3htVjBuNVhkYzlyR2JOTWRiRU11eVJGTytsR2RUS0k2eTF2VWszTElBaXhrL1pSVWt3RW0za1JZUHByNUtRQ1V1NGVzUVlpYzZQeXdMR3d3ZjNwMWZKNGN0NkVMajdzQWFCQVlEUjBLM1UiLCJtYWMiOiJiZWFhZTM5YmJlMGI5MGUxODBhZjNhMGFmN2UxYjkzMjk3MzMwNDU1MTRhMWQyMzg2MGIyMzJmZDgyYzFlNWY1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035344916\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2043134052 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043134052\", {\"maxDepth\":0})</script>\n"}}