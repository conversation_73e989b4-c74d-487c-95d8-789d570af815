{"__meta": {"id": "Xd24f267e0903f03d426c0586e92bad48", "datetime": "2025-06-28 16:02:00", "utime": **********.503529, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.007115, "end": **********.503551, "duration": 0.49643611907958984, "duration_str": "496ms", "measures": [{"label": "Booting", "start": **********.007115, "relative_start": 0, "end": **********.433803, "relative_end": **********.433803, "duration": 0.42668819427490234, "duration_str": "427ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.433814, "relative_start": 0.426699161529541, "end": **********.503553, "relative_end": 1.9073486328125e-06, "duration": 0.06973886489868164, "duration_str": "69.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46406296, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2428\" onclick=\"\">app/Http/Controllers/PosController.php:2428-2462</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01682, "accumulated_duration_str": "16.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.470008, "duration": 0.01618, "duration_str": "16.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.195}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.494532, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.195, "width_percent": 3.805}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1966749825 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1966749825\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1508471270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1508471270\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1024765053 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBvL2EwczVhSW5vTFRLL290ank2aFE9PSIsInZhbHVlIjoiNU5DNjIwNER3Zy9TalZjT3ZFVDZLdWh1U1dYY25WMlNqVWh6cUc1ZG82VWlsaW5YTmlsUUtObis5WUFocDE3NjlqNlRvcnl0NkZqSU5VWXZuazdrQitCUmlUU015YWxBcEg0dEhBN2xEYy85WFdsaWk1eGM5bjZFZXZGZFdNL1pSQWxSSGtBUWlyMm4zSlprUUpSakJhZFhQL2lMZXk1YUhiSmxyVXUrYTk1ZVgyenl1V3pHWEE3QVhIQzIzMERiT0MrbTMrYW5idFg0TkxWelVnZ0Q4RExkT1VONU53TlFCRTYyZEhkaXdzakhHNGdrZjQvRG5YZlNOV0pHZXRnSVN1cDhtVmMwd0pzZjBJazNkYW4vOGwyeHNSSGlhTFplblp5S1V0Wmg4VDVtS2ZzVFIwMWhXa0liTHpwOVdGTnFIVlZjazRCMUlVSTU4YmxyUVF0UDVDV3RvOE1NOGVDU0UvcVE2WDJ4c2lUN2tXT0RrU0dQNnFCZmIyTEVLM29YMDRZSnlVTlhDWFdwTkdwd1FTclFZbXJia3FiTmwxWkh0bWY3bjdlNlYvK2pnV0kyV1Z6NFFUY0FmZzNtSXUrREpOUUdHQTdMQ3RIQVgrT2VoVnUwcmNTRGFMZmh6Q3B2ekc3OHB6YTNoWHVnYWtkUkEyekp2blNCd1J5eFZBdlgiLCJtYWMiOiJmMjZjZmExNzA3MmE3YzQ0ODkwMDM4MmEzNzU4MjhmOWY1MTZmNjJkNDIwN2ViNzJiZWJmYWIzMmQ2MThmMzdmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlvQnlRaFNXdkdDcW1qVFBGWmpKZGc9PSIsInZhbHVlIjoiMkZsOGU5SkN5bUFLcG0vSk1qK3N0RitSUXdzQThtK1FkSllhR0pXVjE1WkN4cXQyR1UyTDhVd21Va01zNkhzVy9UWGV4UHQ2SjVkLy9lRUl0NlhDWjU2VnVIeXRRb0d6S0tmZXhybnZ3aXJsZTl2bWN5ZGQvN3pDenlIVWlRT29jSGt3Qm9icDVPcmZxVk5zTzJOTk1UdDFvN3ZyMThaN2lZM2hnYjBpbGFZSkU1Q0NWa2pINEtTMzdONlFjM1JzWmZQRVZoNmxOUHpXaFBnVm1nRGFGaFlNVWhRSG9uenFHbWc4cVJWc3htRlNaaGdtbFVHMWtkVUM5S2R0Q1lZRGFMbTlTUXVDZ05jQytPNDhiRUZRbS9mcEFFNHpIdDVvUlllY252cEhlRzI5Zlg2ZzBDTDVCdzB0bkJJbFRzT2t1c3RiSFBVN3prVFlwYUczSWp1d1pPZ1N3aGcwNitpdGdsM2tWaVJhbG9wZjN3anVkWXQzVFZWbG5QQnlWbjRueUltRjR1ZEZwTk13K0I4cmNCT1kxc2ZHQXB3akp0dUR3UDJuenlvYW80Z3F6WFJmcFd5ZCtBb3NBUkRwTUlqSVMyVTRZa0paQkl2SzY1QVFtTmY4L3Ftc3hPUlV4akRCcjZ5M2RCSVZKWHdUaHZ3dkFGZVdkVmhRUVd6OFZ2ZmUiLCJtYWMiOiIzYzk1NDg1ZmE3NjkwNjEzODc1NjFhYmIzNGJlMGQ5NWNiOTEzYjQ5NzY1ZjExMGMxYzQxMWQ2ZTBjZjRhOGM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024765053\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-537897708 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-537897708\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1862355809 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:02:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpZdVBkZ3kwSFJubXltSDY0RG01Y0E9PSIsInZhbHVlIjoiTGdOME9QZFR6bXVwcWN1TjUrQys3MXZtOEtOV0ZVdFU5YUMycmlJczY5NGlIRDI2VHlITlFZdDdVU1dOVllQZmMxeDAybW1HL2IydTBkekFMbGJaYUlIdnVGbGtEUE1oTzNHd3htWjRWZGZRTk54TEt0aHNZQTBpUlRodHcwVHBTdWNtRVArNzY0V2hOQlRNV1paQXdUek9UL0U4ZWpiYXEvdXpKamRPbnJHT2ZyYy9uOStsV3cxaUhyNm5VTUt1WmJBWmJSQjdacHluYzU3dFozbG45ZGFUVjhZSXp5Ym03OHhHS0FaQ2JvT20zYlY1cnN1Z2pMaVFpbEJucWRyZGVaTkdWMVc0T1NuYWZGdWxFNndlendrNnRBTjZuRTJwSkcvSkVDVlpHVUtGLzdiME54THFSWnNvaHFUa2hqU3J5b1BrUnN5ZWd4VlBIUlNIN2ZQWnk4dm8vUGh1SThjay95TU80cmp5VnBpQjUvbEU2bG5xWE9uVEM3RnB0MHFKTDJkT0ZJdTVZNkFydjlyb0NWZHRVNHNXZDgvcGpXZGFGNk5FeDFIYnpHTjhYdGxBcS8yYmFRektXZ25PTHNoY1BZZHU5dk50aTl6RS9wN1ZESU1FNkM2cTlmK3Z0cTczM2xGMGlRTTJpUXdzRWxNZDNobHZ0WkpnN2dxRzJxRUQiLCJtYWMiOiJmZjhjNGEyMjE0YjFlZjhlMzM2NWUwYWRjN2E4MzVkODIzNWFlMzQ1MzE1ZDk4Mzc4NTNkYjFjNWI3ODMwMjlhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZSa0RNVmpyV2VWdEpiaE95elhOV0E9PSIsInZhbHVlIjoiOGNYTkxwa3NhakllVDZjMmp5eHM5R3Z1VitmSzkzOW9ldzAvaXhkR3ZGNW5ZUTN6WDBSZFZHRzRTaFpmbVpJYi9mN1llRUN3eDdybkg3cHM4a0dFQlBJWGFhRGlaOHU2STF4ZUlLNU03VUZHamo4RXRvVFIwNTl1SVR1eEZxNllENEdZNU0vWk9BakVPU202eTVJcjg3UXdRdDZBMFI1ajAxRmhOV1JxaWFEdGlQckc0VnJVQkdvZkttd0Nidll0bytzZ3JlSnl0SEtyYUpPZitYMGN3TkZxM2JaaXVlMmE5d2RETnI5bklFVkZpQklKbHNmRUVpQldwOW05dDNIOXdpYVlSR2lVRE5va2VHOUphL1FoRk9mNkJjOUcwZXoxY0EzTisreGR4ZTU3MlVaSXZpaVI1VmpBQm5ZUVJkZlRpM3JXcTViSHhDR254NU0vbUtCVkxHeWhFbWxjWS8yb0FLR2VYdFNSbmZxdXMvRmI3ZHNoM3JVNlY5RG9ySmlwYmZSUTNMaW9rbGdOLzJBWDdjQnBkMjJlc0Y5emhSV3BuOVJMRnlJQjFUUmFkenYzaWRzTU14dFltMC9XOVZjV3JuWWs0L200eWxpeFB2K0pRenZBdzQwQ2o2NExkVnkvU01sYzltV3MyZFVUYWMrYyt2UGlkemY2QnhjeWxQT2IiLCJtYWMiOiJiYjkwNjQ1M2M0MGYwNmY5M2I2NmQwMDQzNTMwNDE0YzIyNmM0Nzg1OTA4ZTc5YjkzM2ZiMTMxZGQxZjYzMzUyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:02:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpZdVBkZ3kwSFJubXltSDY0RG01Y0E9PSIsInZhbHVlIjoiTGdOME9QZFR6bXVwcWN1TjUrQys3MXZtOEtOV0ZVdFU5YUMycmlJczY5NGlIRDI2VHlITlFZdDdVU1dOVllQZmMxeDAybW1HL2IydTBkekFMbGJaYUlIdnVGbGtEUE1oTzNHd3htWjRWZGZRTk54TEt0aHNZQTBpUlRodHcwVHBTdWNtRVArNzY0V2hOQlRNV1paQXdUek9UL0U4ZWpiYXEvdXpKamRPbnJHT2ZyYy9uOStsV3cxaUhyNm5VTUt1WmJBWmJSQjdacHluYzU3dFozbG45ZGFUVjhZSXp5Ym03OHhHS0FaQ2JvT20zYlY1cnN1Z2pMaVFpbEJucWRyZGVaTkdWMVc0T1NuYWZGdWxFNndlendrNnRBTjZuRTJwSkcvSkVDVlpHVUtGLzdiME54THFSWnNvaHFUa2hqU3J5b1BrUnN5ZWd4VlBIUlNIN2ZQWnk4dm8vUGh1SThjay95TU80cmp5VnBpQjUvbEU2bG5xWE9uVEM3RnB0MHFKTDJkT0ZJdTVZNkFydjlyb0NWZHRVNHNXZDgvcGpXZGFGNk5FeDFIYnpHTjhYdGxBcS8yYmFRektXZ25PTHNoY1BZZHU5dk50aTl6RS9wN1ZESU1FNkM2cTlmK3Z0cTczM2xGMGlRTTJpUXdzRWxNZDNobHZ0WkpnN2dxRzJxRUQiLCJtYWMiOiJmZjhjNGEyMjE0YjFlZjhlMzM2NWUwYWRjN2E4MzVkODIzNWFlMzQ1MzE1ZDk4Mzc4NTNkYjFjNWI3ODMwMjlhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZSa0RNVmpyV2VWdEpiaE95elhOV0E9PSIsInZhbHVlIjoiOGNYTkxwa3NhakllVDZjMmp5eHM5R3Z1VitmSzkzOW9ldzAvaXhkR3ZGNW5ZUTN6WDBSZFZHRzRTaFpmbVpJYi9mN1llRUN3eDdybkg3cHM4a0dFQlBJWGFhRGlaOHU2STF4ZUlLNU03VUZHamo4RXRvVFIwNTl1SVR1eEZxNllENEdZNU0vWk9BakVPU202eTVJcjg3UXdRdDZBMFI1ajAxRmhOV1JxaWFEdGlQckc0VnJVQkdvZkttd0Nidll0bytzZ3JlSnl0SEtyYUpPZitYMGN3TkZxM2JaaXVlMmE5d2RETnI5bklFVkZpQklKbHNmRUVpQldwOW05dDNIOXdpYVlSR2lVRE5va2VHOUphL1FoRk9mNkJjOUcwZXoxY0EzTisreGR4ZTU3MlVaSXZpaVI1VmpBQm5ZUVJkZlRpM3JXcTViSHhDR254NU0vbUtCVkxHeWhFbWxjWS8yb0FLR2VYdFNSbmZxdXMvRmI3ZHNoM3JVNlY5RG9ySmlwYmZSUTNMaW9rbGdOLzJBWDdjQnBkMjJlc0Y5emhSV3BuOVJMRnlJQjFUUmFkenYzaWRzTU14dFltMC9XOVZjV3JuWWs0L200eWxpeFB2K0pRenZBdzQwQ2o2NExkVnkvU01sYzltV3MyZFVUYWMrYyt2UGlkemY2QnhjeWxQT2IiLCJtYWMiOiJiYjkwNjQ1M2M0MGYwNmY5M2I2NmQwMDQzNTMwNDE0YzIyNmM0Nzg1OTA4ZTc5YjkzM2ZiMTMxZGQxZjYzMzUyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:02:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1862355809\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-89619905 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89619905\", {\"maxDepth\":0})</script>\n"}}