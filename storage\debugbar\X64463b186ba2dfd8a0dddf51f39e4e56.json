{"__meta": {"id": "X64463b186ba2dfd8a0dddf51f39e4e56", "datetime": "2025-06-28 15:04:30", "utime": **********.82047, "method": "GET", "uri": "/financial-operations/product-analytics/top-selling?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.341234, "end": **********.820484, "duration": 0.4792499542236328, "duration_str": "479ms", "measures": [{"label": "Booting", "start": **********.341234, "relative_start": 0, "end": **********.747022, "relative_end": **********.747022, "duration": 0.40578794479370117, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.747039, "relative_start": 0.4058051109313965, "end": **********.820486, "relative_end": 2.1457672119140625e-06, "duration": 0.07344698905944824, "duration_str": "73.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46097704, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/top-selling", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getTopSellingProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.top-selling", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=206\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:206-364</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01074, "accumulated_duration_str": "10.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.78613, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 13.501}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7970939, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 13.501, "width_percent": 3.911}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' and `p`.`warehouse_id` = '8' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 235}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.799937, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:235", "source": "app/Http/Controllers/ProductAnalyticsController.php:235", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=235", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "235"}, "connection": "kdmkjkqknb", "start_percent": 17.412, "width_percent": 26.816}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 240}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.804285, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:240", "source": "app/Http/Controllers/ProductAnalyticsController.php:240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=240", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "240"}, "connection": "kdmkjkqknb", "start_percent": 44.227, "width_percent": 25.605}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_v2_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos_v2` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' and `p`.`warehouse_id` = '8' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 260}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8086908, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:260", "source": "app/Http/Controllers/ProductAnalyticsController.php:260", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=260", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "260"}, "connection": "kdmkjkqknb", "start_percent": 69.832, "width_percent": 4.004}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, COALESCE(SUM(wp.quantity), 0) as stock_quantity from `product_services` as `ps` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` and `wp`.`warehouse_id` = '8' where `ps`.`id` in (2299, 2300, 2204, 1527, 1545, 2037, 2077, 2201, 2202, 2285, 2303, 2305, 2306) and `ps`.`created_by` = 15 group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name`, `psu`.`name`", "type": "query", "params": [], "bindings": ["8", "2299", "2300", "2204", "1527", "1545", "2037", "2077", "2201", "2202", "2285", "2303", "2305", "2306", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 319}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.811006, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:319", "source": "app/Http/Controllers/ProductAnalyticsController.php:319", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=319", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "319"}, "connection": "kdmkjkqknb", "start_percent": 73.836, "width_percent": 26.164}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/top-selling", "status_code": "<pre class=sf-dump id=sf-dump-754419320 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-754419320\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-686312242 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-686312242\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1181794498 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1181794498\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751123069134%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkI5LzBGR1JrSE5tM3plSHFUZVB5elE9PSIsInZhbHVlIjoiMWJnWVROdlg5RHJYTmhDbFFPV0JhaC9VSGJvQVdDUFE5VkxLUjlNU1dRbHg2dDUrZzJUZzVqeVdQSWJQRWtjMjRiOG5IT3VZTGRlUFd0bS82OFRDdmp3NVlWQWlyeEc5WWNTbHJVVnlsNWJqcUppdk0zcUxaOVd4djhQaE0vSG1JT0ZzRitrNFhHbSttLzZVc3FpYy8wRFdNWkRBK2pML1NTSW0wRTZxT0pkY3NZdENiZ0xHSnRldXM5Y0dPYXdXeWhwQWNCeFZCWHpDQmJraklsNitRYTRQWFVkNVlGK1o0T1dMcDRoWW9UL3NnS2xtQUtITlltN2cyaDFGKzB6N1g3ZGVUendKT2dyamFQN25XaTY4YWN6K1Nxc1NUa2VYR0dJT0pPd2pVVXNsMHQ5Wjd0QktkamFTRmRlOWtpVEpHNG5YYW5VQ2xrTXdpK0xlMlh3MlFFN0c5TjZOcFlsMzEwYVVLRWFzbWl3UExwcWRJd2ZzUVNuREdCYzR3eGlObHRmRDNKdXhYdzZYaTBGWnFlckhuVGZ3YmlKaGlxUFp6bjF5N0Y5V1VETkZkQUhiWVlYZ3JKb3Boc0docVBjMDk0SjBSTjVOYlRrNDgrdGRTQ1RtVHR6Q3R3QWF2VGdiMnVET21Va3pFR0I3dE1wWUhobEZVdGtpMFY2N0dsSU8iLCJtYWMiOiIzYWUyMTAyNDM3ZWExZGQzY2RlMDYwYTIxYzJkMDE5OTY2MGM1YzkyOTY5N2E3ZDg1YWFlODEzZmZhMDkyZGZjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik9aQW1DaFJHSjBVTEZya2lQMFdpenc9PSIsInZhbHVlIjoiQ3FNQWJ4NUhMWFBjc25lbTBkVUFuNVZsK1BqM2s0cEVDUGxvS1p5T1ViM2p2Wnd6ZVBaNjRKT2pJUG83cHZXTHN4aVplaEFCRlBaNkFndXhTYjlrQ2J6NW5OWlB3WVAwK2dTTXpYT0hlbHVlQ2R5M2xVejVjZ3hkOWdJMGNNTnlUK0F3MG44djR6Sll3dWlWRjV3NTNKc2Z0VmFMTWxZb2dTN2daLzNSL2VGUmNSbUJya1Z1b0xmTzJlSWlMVWNWSGM2cjR5ZUpHZFRhcTZSVU9Wdm9TMWY5ellMMlNCZldldm9FY0xDek1aU1YwSmIyTkR1WEJmVUhXNEt2NHh6bVE3RWo1RERyTUNxN1pqVXA1MGdKZFJrMi94RUtXNUJkS0hFSUtGbVJXbmVRenRCVGZoZzVFL05nNTNlNDY5WklVckkzNFVwek9oekJmdmJLUnl5WnZHc0RmaitLNXBXVWZXbmNrc2I5U0ZpRDdMamc1bTlWUlJ4RE9tWEtVR3NxNW1yWFFTb2M0bmt5M2RidmtFOFRnWHZxTHUySGFKbXIrTW5xalhtaW54WXlmZzhnNWo2djBDNzRGMnp4ZnlzODVSTjJSS3F6cU9UdW1tdWk1SDlmOExRVGc5NzQ0SFhKUlE5bC9FSExIM1Z6MzA1VW04YUovRXZGdXBwL0NnNlgiLCJtYWMiOiIzZmJmM2MyMmJiODliNzQ2M2U3MDYxMDU0YjcwN2M1OThkNjIyZTUxYjM3ZDdiM2NiZmZjNGU4MmQ2MWEwOTYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-954377098 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:04:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpkcXg4NGtBZmJ5dmJVNlBiZlByQmc9PSIsInZhbHVlIjoibk91QldIUi9DYXlwWHVrbmJocGUxWWRUV0NvK1dub1NGK21vWFR6WHZ1dXZmODZqZWhaNS9JRE1WdXJ1aUlMZWExN214TUZndldFNDVZZ2cyTXZicDF0UExKVVV0VGRKUHVSZmV4TWhzK2dvUy9LWlpPVUtOcWdBN0NaQlpLYWVjYXdHYkZBOVkrUHhIaVV2aFhycUF2OG9rK1pMOUVQa0k1a0hncVFmdEZzQnp5Nk9vOThzM1IxTHNpc1daU09RSVc3UEgwUmF6N0FISXpaR2xpbTdQSlRUU1VjaDRxN3R0VGREOC9JNWtJZVptSHpJbzZ5KzF3eDNId21hMEZDTjUvQU84R09BSU5lb0t2OXNhQ3VyNGN0Y2EzWC9MZGxhVlNTS1JibEliQzlqd3BpOEU0MmYvZXBHemF6VGljWE9WWmkwdHNUSXJMY251dTJJSlFlWVIxRUhNNG4ySmZ3U2FnN0g2Qnh1VkhpQk9IaXR4bDVDaDJuU1c2aUZiT3ZOR1Q5VUZRdEJtbWYvOW1zT01VdG9LYUcvbXJ4T3dQWXJLL3JYcGhXSGNMSW5TMDcralRaNzZlYWVZWnRqRnE0em5mTHlLeDBGNHZCb3ovUURGc3ZURVB6ZVBFanZSMkxFejhFWlA4Q2NEcFlrd2hmTko4a0xOc3dua21IMnVuWWQiLCJtYWMiOiI4ZjRjZTZmOWU1YWFhMDEyMTFmMzkwYmZlY2QyODgyMjA5MTkyMWQ5ZDc2YjQ2ZDJmNzE2MGQyN2QzMDRlYjBjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBIN1dnMnd1ZG5wYzlyT3k5ZzhJYXc9PSIsInZhbHVlIjoiTXRHYTRvbzZ0a2VnR3FmWU9rNEc5UkNBdkpxd29DcG03OUZ3ZU1KU09iRGJDU3IyQTZlOGRlYTRFNDNOR2thVWhUT1ZPTnFQTTdhNmlrRjZnaG94TEZHQVJuZHU2OG11Y1Nvd1VVYlBYVGtkblViUUg0MC9talI5cVBrN0FUSUZzd29ZUmxlNmdFYm53L0pNSVBNM1JVOXhTSG9rK3hGQTk0MjMxWS81ZzhmTHJmV3VOamY1S1ZmOUR4V2RuWlN4QmM5VGhhSVNJRVNSOVRhTUw3aW5sN3h0alpaVVVad2MxVEJ6MHdmamFjQmtEWksvcFZxdGVzT3Z3NHgralI2M1ZNemQzM2ZORzh4cEF6bGgvYUg0WkxDWllXeElUWU5aZU5wbEVkVjhuQmF6Rm1lay9KemdXbDB3a1k1VUNMTFQ4OFBpSkVMcXB4RUhldmpzb1BNUmZSdVptcG4xK3Z3QXdYYlhOR3lsZjZKakU2ZEZBV1YrM0RhYXRoK09FUXFudUJicmltTldvV0tkVVB5Vm14RFdoMitjR2srY3l1Yy9WaCszWVlhTkFUUXlIS2NjSDhoTEdtWC94TWxxRnRBVXA2YllSNlIwWnhiMmFuRU9IN3g5YUNibWpyVHh5Z2xqNllQZFNvTlh3OWxPcFVWaElVNzVMVzdqSXdLazRPMjYiLCJtYWMiOiIwODJlOTUwOTRjMzhhNjI0ZTI5ZDBiMGNmODU4YzI0ZjMxMDVkOGQzYjQ2ZmU5NTkzODljZTdmMDQwN2M3Nzk2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:04:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpkcXg4NGtBZmJ5dmJVNlBiZlByQmc9PSIsInZhbHVlIjoibk91QldIUi9DYXlwWHVrbmJocGUxWWRUV0NvK1dub1NGK21vWFR6WHZ1dXZmODZqZWhaNS9JRE1WdXJ1aUlMZWExN214TUZndldFNDVZZ2cyTXZicDF0UExKVVV0VGRKUHVSZmV4TWhzK2dvUy9LWlpPVUtOcWdBN0NaQlpLYWVjYXdHYkZBOVkrUHhIaVV2aFhycUF2OG9rK1pMOUVQa0k1a0hncVFmdEZzQnp5Nk9vOThzM1IxTHNpc1daU09RSVc3UEgwUmF6N0FISXpaR2xpbTdQSlRUU1VjaDRxN3R0VGREOC9JNWtJZVptSHpJbzZ5KzF3eDNId21hMEZDTjUvQU84R09BSU5lb0t2OXNhQ3VyNGN0Y2EzWC9MZGxhVlNTS1JibEliQzlqd3BpOEU0MmYvZXBHemF6VGljWE9WWmkwdHNUSXJMY251dTJJSlFlWVIxRUhNNG4ySmZ3U2FnN0g2Qnh1VkhpQk9IaXR4bDVDaDJuU1c2aUZiT3ZOR1Q5VUZRdEJtbWYvOW1zT01VdG9LYUcvbXJ4T3dQWXJLL3JYcGhXSGNMSW5TMDcralRaNzZlYWVZWnRqRnE0em5mTHlLeDBGNHZCb3ovUURGc3ZURVB6ZVBFanZSMkxFejhFWlA4Q2NEcFlrd2hmTko4a0xOc3dua21IMnVuWWQiLCJtYWMiOiI4ZjRjZTZmOWU1YWFhMDEyMTFmMzkwYmZlY2QyODgyMjA5MTkyMWQ5ZDc2YjQ2ZDJmNzE2MGQyN2QzMDRlYjBjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBIN1dnMnd1ZG5wYzlyT3k5ZzhJYXc9PSIsInZhbHVlIjoiTXRHYTRvbzZ0a2VnR3FmWU9rNEc5UkNBdkpxd29DcG03OUZ3ZU1KU09iRGJDU3IyQTZlOGRlYTRFNDNOR2thVWhUT1ZPTnFQTTdhNmlrRjZnaG94TEZHQVJuZHU2OG11Y1Nvd1VVYlBYVGtkblViUUg0MC9talI5cVBrN0FUSUZzd29ZUmxlNmdFYm53L0pNSVBNM1JVOXhTSG9rK3hGQTk0MjMxWS81ZzhmTHJmV3VOamY1S1ZmOUR4V2RuWlN4QmM5VGhhSVNJRVNSOVRhTUw3aW5sN3h0alpaVVVad2MxVEJ6MHdmamFjQmtEWksvcFZxdGVzT3Z3NHgralI2M1ZNemQzM2ZORzh4cEF6bGgvYUg0WkxDWllXeElUWU5aZU5wbEVkVjhuQmF6Rm1lay9KemdXbDB3a1k1VUNMTFQ4OFBpSkVMcXB4RUhldmpzb1BNUmZSdVptcG4xK3Z3QXdYYlhOR3lsZjZKakU2ZEZBV1YrM0RhYXRoK09FUXFudUJicmltTldvV0tkVVB5Vm14RFdoMitjR2srY3l1Yy9WaCszWVlhTkFUUXlIS2NjSDhoTEdtWC94TWxxRnRBVXA2YllSNlIwWnhiMmFuRU9IN3g5YUNibWpyVHh5Z2xqNllQZFNvTlh3OWxPcFVWaElVNzVMVzdqSXdLazRPMjYiLCJtYWMiOiIwODJlOTUwOTRjMzhhNjI0ZTI5ZDBiMGNmODU4YzI0ZjMxMDVkOGQzYjQ2ZmU5NTkzODljZTdmMDQwN2M3Nzk2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:04:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-954377098\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1259160805 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1259160805\", {\"maxDepth\":0})</script>\n"}}