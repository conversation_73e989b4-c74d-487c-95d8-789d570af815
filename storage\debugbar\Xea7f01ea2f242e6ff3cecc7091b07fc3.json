{"__meta": {"id": "Xea7f01ea2f242e6ff3cecc7091b07fc3", "datetime": "2025-06-28 15:49:31", "utime": **********.492883, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125770.954479, "end": **********.492898, "duration": 0.5384190082550049, "duration_str": "538ms", "measures": [{"label": "Booting", "start": 1751125770.954479, "relative_start": 0, "end": **********.423935, "relative_end": **********.423935, "duration": 0.4694559574127197, "duration_str": "469ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.423943, "relative_start": 0.4694640636444092, "end": **********.4929, "relative_end": 1.9073486328125e-06, "duration": 0.06895685195922852, "duration_str": "68.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45166000, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0036100000000000004, "accumulated_duration_str": "3.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.461635, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 57.064}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.477611, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 57.064, "width_percent": 18.283}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4845989, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 75.346, "width_percent": 24.654}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1700637511 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1700637511\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-766503557 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-766503557\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1982144445 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982144445\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1642189299 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125735645%7C25%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndCa2Z5QytJMVJialhianBrQUNFQXc9PSIsInZhbHVlIjoiL2tNLzAvRHVDd3QzcGVvd0lUd0pFRFhybGZ3Z0ZEN2dEZ0RsMDR1ZGcwU2Y5Wml6cUkrNmNpOTBHc1VYUzR6djV4RU1wRVVDODFIVjZ6c043YnNZMWhSUk1tdjQrcjdjVndEWjdHT0V1MzFRQVQrOFJseDk3UGRoT3Y0anp2R2lVdkp2TnZMVTVMOHdvOUloaTZIZWZyeEIxWDhJdGdWTDJEUmVqK2ozS0dMTndHYTh0ZzhlM3lSY2ZvYVY5MnVONmxnOTA0ZHZ0QmFkd2QxLzdtQ1h4eE5yaVRqVTgxZmZITk1DQzcwUFVRT1pCeGhmRkZCUEI3ajJ2cXZ0SllYSFdaSXJKRW1IT09vZ2RPOUM5MFczVHJURDFlcldOOEN3YXpONis2cG5kVzVPRDlFU29lL3Y3VHBNRXdmVkZOeUVQOFkrMVl0Y0liUkNhczFMRUIybFJ3YmpvZHpkSVcyMnZYTldhaDNTMU83dEVLbCtOS2NkNmhKeFpRUkpUNEx0WkVqcERwQW8zMzRXN0c3cnMyN3pSa0VhQ1dJK3lEMkdRTEhTVTZoN0pBNkRNYk1rRS9ialp4SE9ValNObU5sTU1lVnpBdENwbkI1L1J0NVBCYnlkRVBIZXhBcnloeXpxQkNHMHYvOHZDVGJhUlZ6dm5ZeFpwOG94MlpzV210SlQiLCJtYWMiOiJiY2U5YjJkMjRjOGEwMmI4YjEyMDM1NGY3YWU0MTE2MTYzZjRkZDE4YmU4NmQ3NGNkM2Y5NGQ1NmY0MzI3MGE4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InNZZnJ2eUN4US9rejYyTEwxN1M5ZVE9PSIsInZhbHVlIjoidVlPYzRWaTNxNkVvYzRjeEJLZWh1ZHAycyt6Y3ZobjE4MkFuQ0RtVklkeDh6czNwRk42cktNM1VscGp4YytQUldjdzR4bzFONUZWV0xJajRVU1VIZkZxWktidHlIR2lVYkFxVFA3VUJVSm1FcWZsaGJoRG9pSlNZNkcrZ1ZFQWxwSWNhK2c0eCtsNE5SMHhTU2thSjZ0M2NLazJwSUVhalk4Ym1vbW91ZGFobVkvcXpUTjgvYkduMGp3YytkVGFFcnNsb3VqVDlCUWtTaGt3Yy83bnJzNFIrenlkWHYrZjVDL0pXWnZhc1pmYmtpbEhGNE1vZ0Vkc2hMK0JBWkNEVHRBRmlHT0tCa3pVWkduMnVMYXlXTG5iZktFOWlxT3lOZ2JQMDR3TnJXa1FCRlpOK3Ayak15UXF6N2J4Nys3SGt3TitDNFJhVkdXZHlKSDhhVDNyTHppWHR3TENHTHVxUzFVSkNZQ3VUdDc5czA2RWpVMThBMlJnNlYxRmlycEk5Mk5hZFhIUGpZbHFJL0lQY0xKM3JHZ2g5UGZLTldCaFp6bStEZjhKdTY3SmdqY2xDOFN6bVUxVDZVSENWNThqNUUxSjlSb2RUdTYwWldMSUpzSDhGczBWMUxNSXFnRjJQNDF6MGkrRE81UDVZdXJVR1ovTzA2T2tsZmlIZ2hRemQiLCJtYWMiOiJmNWRmOWQ0NjIwYzk2Mjc4MjQ5ZGNkMzIzNDEzNTBmMDJkYTJjZTk2ZjFmOTZhNDgzYmIzN2E3YzliM2QzYjcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642189299\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1392996211 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392996211\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1768398570 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjN3OEV5TStyRFZkUFhzUHRjenhCbVE9PSIsInZhbHVlIjoiNTB6Mkwxa3BLbkY2c3Rtd0cyUW9HbFVsTCtEUTdRNHdrTmcxMGpYYngwQUtDWW9JaG03OHJMY2J2R0xUazZGSUhaMjhkYSsvN3I5Q0V2TFhyczhZN1BVRWRMSDk5V3VRRzRyWitackx3ak11cEI0aTNYa0N2ZFY1OGFKZURXSS9BVlE0NEZSSnhUaHkyTUlTZXdXZWtRKzBtZzFxTDNqaTBFZnZ6ZFFLcFVaV2dpbWRlZjQ0Qnd4NWlsY3RvWkpSM2E5T1dnMmZWN2JtUWd3QTd6T2hKSFV0MExHeURvZk1COGs2cGZGU1Jqd3laTmJlb1QvSVlKcTNITXdFVXNOam1mREJzYW1LQ1hPNlNlWHpDMDRJL1UvWUsvc0F4MEovc3prb1VsSHdaVkhYanhrWFYwSVJQRXFlalRqY3hYd2JUS0U1V05OKzJxS3pET0JaSTZuTFZWMk9hRzZ2NXNobXRXV1NRYmdNbWNaY2g4aUNGQUgxNFNLNFZVemlZYnRQdzZQNVJBWU9ZaDJzVkw1ZlUxcExISGErcmtjNWVTaEtzV216eXU3WkMrbkxRR1VObXExZERRSGNEbmlOaHBPSmo2T3A0ZW1OakwrSUpjNlptNFJld1ZVdkdCZk1GUnFLWUxqV2tCazIrYytsdnNNNVQ4VTVaaEVmRWNQa01rUzYiLCJtYWMiOiJjZDFkMTc1NmVhZmI4Zjk4NWY1MGM4YTk5ZDdlN2VlNjMzMzIxY2JkYTlhOTgxMWViNDc1YWZjYWE4YjlhMWJiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJDQ011YWZFNURPOFRtcmUxSGZSUXc9PSIsInZhbHVlIjoiYW5oK1BSTmhFa0IweFY3clNKWkpWT1dJSU5KWFlCVGMwcmlpdzR0RlAvb2x3MStrK0JmQXhiYzlsT2JiUDBGZERycU5LVm1UTU84dkcvMHU2UWY5clVueURVdVBReVNzUGNUMlVVRTBMVWhIVFpIaG1oRlRWYkJpZmxYRTkxTjVtUFM3Mi9wMHVma3pwTVk1dU9jbklxcTEwQ1JQUkhlOE1mRG4rWXpFckdhcDhmYkhxaDF6V2ZsMHFGdmlGejlwV1grZHdIdjlGZGNYS0oxL2pJZU5YUU1XTDJNRHpyUGdTUVBPUGl6cGJYcDM3OEFWOTlyQ3FCSDVvblU0WUlDTEVjN1UyNFlVYWF0Yzg5OXkvcWxQVjM1aVlNRGNYWlJrZmNvbHdYYTZ1UmZ3UVRCNmhHRDdVQjA3K3VLVnE2ck91azAvZHFUU1M4dnYzVmRyYnVRaTJ6TE9TVDhLR2pWL0FRQTQ3dGRPQnJaZjFhZDd0ck41TDMrS1IzMmVmL0kxaGxIQ2NORXBiaGNSdG1ES2RPQ0hXRFJWUVN2TjNFelZpb3JXNFZVNE40d3Q0NFRCRVBMMS95V2ZYQk0rUTEwT3NVb3RUUW5WaE51Nkh0aDRUZGFqMkcxM3gvbTY5YUljZ1dzL3lrQXU0dWwrU2V3L0pqQnZncnhkWUw2a3ZMRloiLCJtYWMiOiI5NzI3OWYxZGJiNmFlM2YxZTllNDBhMjMxNTliZDc1M2UwZmM0ZjU4NjRlZGE4YzRjODA2OTA4ZjMwNWFjNzM5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjN3OEV5TStyRFZkUFhzUHRjenhCbVE9PSIsInZhbHVlIjoiNTB6Mkwxa3BLbkY2c3Rtd0cyUW9HbFVsTCtEUTdRNHdrTmcxMGpYYngwQUtDWW9JaG03OHJMY2J2R0xUazZGSUhaMjhkYSsvN3I5Q0V2TFhyczhZN1BVRWRMSDk5V3VRRzRyWitackx3ak11cEI0aTNYa0N2ZFY1OGFKZURXSS9BVlE0NEZSSnhUaHkyTUlTZXdXZWtRKzBtZzFxTDNqaTBFZnZ6ZFFLcFVaV2dpbWRlZjQ0Qnd4NWlsY3RvWkpSM2E5T1dnMmZWN2JtUWd3QTd6T2hKSFV0MExHeURvZk1COGs2cGZGU1Jqd3laTmJlb1QvSVlKcTNITXdFVXNOam1mREJzYW1LQ1hPNlNlWHpDMDRJL1UvWUsvc0F4MEovc3prb1VsSHdaVkhYanhrWFYwSVJQRXFlalRqY3hYd2JUS0U1V05OKzJxS3pET0JaSTZuTFZWMk9hRzZ2NXNobXRXV1NRYmdNbWNaY2g4aUNGQUgxNFNLNFZVemlZYnRQdzZQNVJBWU9ZaDJzVkw1ZlUxcExISGErcmtjNWVTaEtzV216eXU3WkMrbkxRR1VObXExZERRSGNEbmlOaHBPSmo2T3A0ZW1OakwrSUpjNlptNFJld1ZVdkdCZk1GUnFLWUxqV2tCazIrYytsdnNNNVQ4VTVaaEVmRWNQa01rUzYiLCJtYWMiOiJjZDFkMTc1NmVhZmI4Zjk4NWY1MGM4YTk5ZDdlN2VlNjMzMzIxY2JkYTlhOTgxMWViNDc1YWZjYWE4YjlhMWJiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJDQ011YWZFNURPOFRtcmUxSGZSUXc9PSIsInZhbHVlIjoiYW5oK1BSTmhFa0IweFY3clNKWkpWT1dJSU5KWFlCVGMwcmlpdzR0RlAvb2x3MStrK0JmQXhiYzlsT2JiUDBGZERycU5LVm1UTU84dkcvMHU2UWY5clVueURVdVBReVNzUGNUMlVVRTBMVWhIVFpIaG1oRlRWYkJpZmxYRTkxTjVtUFM3Mi9wMHVma3pwTVk1dU9jbklxcTEwQ1JQUkhlOE1mRG4rWXpFckdhcDhmYkhxaDF6V2ZsMHFGdmlGejlwV1grZHdIdjlGZGNYS0oxL2pJZU5YUU1XTDJNRHpyUGdTUVBPUGl6cGJYcDM3OEFWOTlyQ3FCSDVvblU0WUlDTEVjN1UyNFlVYWF0Yzg5OXkvcWxQVjM1aVlNRGNYWlJrZmNvbHdYYTZ1UmZ3UVRCNmhHRDdVQjA3K3VLVnE2ck91azAvZHFUU1M4dnYzVmRyYnVRaTJ6TE9TVDhLR2pWL0FRQTQ3dGRPQnJaZjFhZDd0ck41TDMrS1IzMmVmL0kxaGxIQ2NORXBiaGNSdG1ES2RPQ0hXRFJWUVN2TjNFelZpb3JXNFZVNE40d3Q0NFRCRVBMMS95V2ZYQk0rUTEwT3NVb3RUUW5WaE51Nkh0aDRUZGFqMkcxM3gvbTY5YUljZ1dzL3lrQXU0dWwrU2V3L0pqQnZncnhkWUw2a3ZMRloiLCJtYWMiOiI5NzI3OWYxZGJiNmFlM2YxZTllNDBhMjMxNTliZDc1M2UwZmM0ZjU4NjRlZGE4YzRjODA2OTA4ZjMwNWFjNzM5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1768398570\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-699290853 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-699290853\", {\"maxDepth\":0})</script>\n"}}