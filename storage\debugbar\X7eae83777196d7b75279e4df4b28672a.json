{"__meta": {"id": "X7eae83777196d7b75279e4df4b28672a", "datetime": "2025-06-28 16:03:38", "utime": **********.736223, "method": "GET", "uri": "/pos-financial-record/opening-balance", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[16:03:38] LOG.info: Opening Balance Request Started {\n    \"user_id\": 22,\n    \"warehouse_id\": 8,\n    \"is_sale_session_new\": 1,\n    \"has_manage_pos_permission\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.724079, "xdebug_link": null, "collector": "log"}, {"message": "[16:03:38] LOG.info: Returning opening balance view", "message_html": null, "is_string": false, "label": "info", "time": **********.725091, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.328759, "end": **********.736242, "duration": 0.4074831008911133, "duration_str": "407ms", "measures": [{"label": "Booting", "start": **********.328759, "relative_start": 0, "end": **********.660298, "relative_end": **********.660298, "duration": 0.3315391540527344, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.660307, "relative_start": 0.33154797554016113, "end": **********.736243, "relative_end": 9.5367431640625e-07, "duration": 0.07593607902526855, "duration_str": "75.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52271248, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.73163, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record/opening-balance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@opinningBalace", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=262\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:262-323</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00342, "accumulated_duration_str": "3.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6925979, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 53.801}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.70334, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 53.801, "width_percent": 13.158}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.717768, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 66.959, "width_percent": 24.561}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7199361, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 91.52, "width_percent": 8.48}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2017839675 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017839675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.723425, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1519931816 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1519931816\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.725, "xdebug_link": null}]}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/pos-financial-record/opening-balance", "status_code": "<pre class=sf-dump id=sf-dump-1351562750 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1351562750\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1216233899 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1216233899\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-834631281 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-834631281\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1907759264 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126616176%7C19%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNtYUlSMUxWM2ZtYW1Db1E5Q3M1aXc9PSIsInZhbHVlIjoiNHNxWTVkby81Q2JqOGpKeDFEaEIrUFRzQ1NmYmlRRVlHMXJjWG5JUUhzVC9sRHM2RkdtRlVRcmF3UVFMNXl3bWk4NG1mZDFqa0hCSWZPNC84RVgxeUl0bUZIVnZlM1pEV2xqQkxzYlNUb0wxZ28vZ1FjNWFUalZKQmdkR1BVZUVJekVOMXNxMzZwMXJObnVQQi81Tm9abWRmU3QwUlFYVXRRTmJaZXJpK24yd3BUbjB0eFFzYWtKWW1QVWk2ZEFmVUhOMUVZZUNncW1jQmZyVmhMYjNhUWUvNUlucHZQdkJBZXhrNUJtclBTWVZXZlRkaTJSSFdVb1lrRXZ1UVZmUG5MU2tmcWdnSE9VSHY1VkhkdDZLcXF1MEVrU0xlc2Z5VTFsR0hEYUZ4SytTakJlR0dwR0d3WHd2MzZQc3pnbkxqcmVYTmwwWlFZR09tMjA5MHVhVm4xa0ZpS1crZlYyeVhWbjU3NEtMSTRoblJ1UnhDZW94elB1U1Vtc1VaZmVGWVFMVVBmbHgySnVteEMrU3o5MGdTVUYxZkhoRVd3RWJtdGIzMEF5OVJBbUJNSlU4U0JTWVNHTThnMStmQU1yL1Z4WmZiWWRlRnc1NGh3SWFsd3J5eTVLaFdBTTlqN2gxaWRDbG14VG0xNlh1WDdiTUVVVm81WUk3elV1L2xpeGQiLCJtYWMiOiI0MmY4YjU5YmNiNTJmOWMxZTAxYjY0ODQyYzkwNTVmYzZhYTYxNjBmYjRmNWYxOTAyNGI5YWUwZWQzMmM3M2I1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImRBb1lXZ05FNk12dDBDRUtyaGxLRVE9PSIsInZhbHVlIjoiWWRaazIrZlVQUkFoWEh5WUJrdjdMY3hlVkc0NG9XZ3ZNcndTYlRkdmhycHRKZnYvanR1Yk16aXh5UkZzb1B6ZUdFWDFtcEFwTlE5eGdpeWVZY3FsdzNlcmZmTkkvYjBGQVRjdVhmR3AwbnJjNzBJS3h0c2xxdkJBOHczcGU0R1lEb3d3NUZodHgzd1NTZ09pQlRDMjhlNEh3eEp4TkdnK0lOL2NLUkRGTGw1N202QTlJL3BVblBmeUFmSVNoTjAxTW90R3g1bnNGaUV3ZG9yVlFvWXloQU9kbHpIY3YxUlJIUC93VC9iUVFqZDd0SFFzekovdXhWaVRwM0I2RHV5Y1M0Z2ZTS2RGamxBcDFkenQzczNPU212WVBvRDNFRC80Z1MvOEd3RkVHL2hXQTNpMXFJQkVWTXhyaDdSUEFqRnYvdys4U1F0WFJrTjZRSk1HMVBocVhCZ1JnMkNwTmxJN3pNSjZqTjZadmN6cDlFRlZVWnFuRFpEUFVheC80UlZ3Y2k3VEc2bktCbWhmSGRHKzJGR2JKZm9RNkdsQjNUWkpYbzMvRjNxb3plSklZeXM0YlNSYVpabW9idis5NE9Dc0pRQWhZWWFyK1BrUXAwTUhQSzU2czlxZjVyZTZ3RlY1ajZRUThMZlFuNjRibnFZTG9Mc3MrK21LcEhZZTZQN0siLCJtYWMiOiJkOTQxYjk0MjQ5OWY2ZjhiZjRmMDBkYzYyYjI1NzIzZTdiNTU0OTRlMjBkMmVkMTgzZmE3ZjJiYWYzODU5YzkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907759264\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1296484977 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296484977\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1376655614 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNLclB2R0NLS2x5RkZwUlpnWHZtQ1E9PSIsInZhbHVlIjoiM1dhZUxKdUloT05nVSszTXJabEhHVkt1cSttc2JyZzJJYVhib1dhL1dYbThhTVhoVVEzMU1mbWlhYjdFOTBLWS9oYWMxKzZHVzBROFA5NGhxTmRjL1NyNDZ6NW5CUEs2NlBva0JnQkdMUG9abEZqcjNKMXRnUk85cGZNckVTclN2YkxmSlFtbGpFMTdGditycGVCWGhydnFRTWxhK3poVCsyOStqa2tPcFpOSGNBR01CZmp1b25wK0c4Wm1EaGo2Nkk2ejVZaUVvM2xDSXdaRmNtWms3NnRneVRhMXdhVHh4K0xicFVuZjlKdnkvSzJ1Z3JnQ2JHVW5XczlXWEhzUHZXZUg1N29JeXJJOFlBWGpzYlRjSFhjQ3dmVUZKbzhYQkZqSXQ1WG5VSzZ6L0I1VHpWV0FpS1poOTdOUW00ZDlHZyt3bEx0TjAyR0VvUERFWnhQOTZ3LzQ1V1BVSFJFNHNlQ0x6S01ETDFYcDI4UVV4RWk2bWI3TXR6U0lSbW5iSWNWQk8vNlRXQ3pwb2diMnBWckNQbGJML0xGdHhRbkNScE9GTWtwU3RSN2dVTGlTTHdnUUlPUmpId0p2aFdhVWhteVlWSWxWNHNQK0JSaUNKSXV4VXlQVSt1anF5VWRoZkFRRW9KZkZ5MEYvN2NNR3k2RnJlN2x4d2NxL0JoT3YiLCJtYWMiOiI0MWY5MmVmMTI1MDk3Mjc4Yzg5OTAxOGE2MGJmOTVjZGY5MjZmNGFmNGZhYmQ4ODRkNTg4NDc1ZjNiZjUzZjA1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitMZFBOS2ZWZUFKbDN0NmxFMXhhQXc9PSIsInZhbHVlIjoiZnJ6aWpWUzRET0NVN0pXOEtoUk1qeEMrY1ZoV0FoQ2VZOVpoNjdwci92OXYyZ05Kby9aZlZDWVNvZ1hXSHF5WXlsd21qRnEwVXpwaXYwYnhKS2JOZ1BKeUNObUtHem1GSkNRbnVuWVBHUlhSUUt0VzRkSExUOTFEQTdGSXg5YmZXREQ5RmdFaDJQTjQ0SWZuMkVybFRWTlZmNHVIRFEvMVBtQUJCY2s4VTc1V3RmRUNPQ3dhRXo1OFU4dWphaFFwQW9SaFp1TGkvZ1hlUzhYRnF1cWsvNUVDVDEzRGM1dStpRmxVSG9UVG12U0wwSFNwc3NsczBKdXRnUEJDbUN1TXB6TXV4ZzlWTUY0UFArak1WWDZlUXNPNEZIRUI3YW1JbjlERmMrSldPbCtLRzFtUysxOWIyY0liY2FuQmdIMDloWGJNQTJhQ0F4ZG1Ld1RvbEdmeUZ5T3dQQUNLR3V6bEdlQVF5QzRFcXRNL3lJT3ZQdmpnTUFZdW00aENkY1JzSUlBVHM4R01HN1ArMVBVVnhnQ1dmc0lReDE5RnZIMWFXOFlkUHNlRHRBT1BuUjkvZG9sUFdaSGdrVGkyOUVpeWtXbFpRSHQ4QnhLL0gyVG9sbUNYNlJoN2lnSkJValdqdzZMTyttRlBTYytMUFQ1MWJnTkxOeGZtZDBTN2pJYWUiLCJtYWMiOiIxNTUzYWFjYWEzNjE0MzRkZTBhZDY3NzU5ZjA2ZmQ4MjA4NWMzY2JiNWQ1YTg3ODRiZmU0MTYzNDQ4NTJlOGYwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNLclB2R0NLS2x5RkZwUlpnWHZtQ1E9PSIsInZhbHVlIjoiM1dhZUxKdUloT05nVSszTXJabEhHVkt1cSttc2JyZzJJYVhib1dhL1dYbThhTVhoVVEzMU1mbWlhYjdFOTBLWS9oYWMxKzZHVzBROFA5NGhxTmRjL1NyNDZ6NW5CUEs2NlBva0JnQkdMUG9abEZqcjNKMXRnUk85cGZNckVTclN2YkxmSlFtbGpFMTdGditycGVCWGhydnFRTWxhK3poVCsyOStqa2tPcFpOSGNBR01CZmp1b25wK0c4Wm1EaGo2Nkk2ejVZaUVvM2xDSXdaRmNtWms3NnRneVRhMXdhVHh4K0xicFVuZjlKdnkvSzJ1Z3JnQ2JHVW5XczlXWEhzUHZXZUg1N29JeXJJOFlBWGpzYlRjSFhjQ3dmVUZKbzhYQkZqSXQ1WG5VSzZ6L0I1VHpWV0FpS1poOTdOUW00ZDlHZyt3bEx0TjAyR0VvUERFWnhQOTZ3LzQ1V1BVSFJFNHNlQ0x6S01ETDFYcDI4UVV4RWk2bWI3TXR6U0lSbW5iSWNWQk8vNlRXQ3pwb2diMnBWckNQbGJML0xGdHhRbkNScE9GTWtwU3RSN2dVTGlTTHdnUUlPUmpId0p2aFdhVWhteVlWSWxWNHNQK0JSaUNKSXV4VXlQVSt1anF5VWRoZkFRRW9KZkZ5MEYvN2NNR3k2RnJlN2x4d2NxL0JoT3YiLCJtYWMiOiI0MWY5MmVmMTI1MDk3Mjc4Yzg5OTAxOGE2MGJmOTVjZGY5MjZmNGFmNGZhYmQ4ODRkNTg4NDc1ZjNiZjUzZjA1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitMZFBOS2ZWZUFKbDN0NmxFMXhhQXc9PSIsInZhbHVlIjoiZnJ6aWpWUzRET0NVN0pXOEtoUk1qeEMrY1ZoV0FoQ2VZOVpoNjdwci92OXYyZ05Kby9aZlZDWVNvZ1hXSHF5WXlsd21qRnEwVXpwaXYwYnhKS2JOZ1BKeUNObUtHem1GSkNRbnVuWVBHUlhSUUt0VzRkSExUOTFEQTdGSXg5YmZXREQ5RmdFaDJQTjQ0SWZuMkVybFRWTlZmNHVIRFEvMVBtQUJCY2s4VTc1V3RmRUNPQ3dhRXo1OFU4dWphaFFwQW9SaFp1TGkvZ1hlUzhYRnF1cWsvNUVDVDEzRGM1dStpRmxVSG9UVG12U0wwSFNwc3NsczBKdXRnUEJDbUN1TXB6TXV4ZzlWTUY0UFArak1WWDZlUXNPNEZIRUI3YW1JbjlERmMrSldPbCtLRzFtUysxOWIyY0liY2FuQmdIMDloWGJNQTJhQ0F4ZG1Ld1RvbEdmeUZ5T3dQQUNLR3V6bEdlQVF5QzRFcXRNL3lJT3ZQdmpnTUFZdW00aENkY1JzSUlBVHM4R01HN1ArMVBVVnhnQ1dmc0lReDE5RnZIMWFXOFlkUHNlRHRBT1BuUjkvZG9sUFdaSGdrVGkyOUVpeWtXbFpRSHQ4QnhLL0gyVG9sbUNYNlJoN2lnSkJValdqdzZMTyttRlBTYytMUFQ1MWJnTkxOeGZtZDBTN2pJYWUiLCJtYWMiOiIxNTUzYWFjYWEzNjE0MzRkZTBhZDY3NzU5ZjA2ZmQ4MjA4NWMzY2JiNWQ1YTg3ODRiZmU0MTYzNDQ4NTJlOGYwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376655614\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1836414018 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836414018\", {\"maxDepth\":0})</script>\n"}}