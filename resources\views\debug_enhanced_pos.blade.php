<!DOCTYPE html>
<html>
<head>
    <title>تشخيص Enhanced POS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; border: none; }
        .btn-success { background: #28a745; color: white; border: none; }
        input, select { padding: 5px; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔍 تشخيص Enhanced POS - مشكلة إضافة المنتجات</h1>
    
    <div class="debug-section">
        <h3>📋 معلومات النظام</h3>
        <p><strong>المستخدم:</strong> {{ Auth::user()->name }}</p>
        <p><strong>المستودع:</strong> {{ Auth::user()->warehouse_id ?? 'غير محدد' }}</p>
        <p><strong>الوقت:</strong> {{ now() }}</p>
    </div>
    
    <div class="debug-section">
        <h3>🧪 اختبار إضافة منتج للسلة</h3>
        
        <div style="margin: 10px 0;">
            <label>اختر منتج للاختبار:</label>
            <select id="testProduct">
                <option value="">اختر منتج...</option>
                @php
                    $products = \App\Models\ProductService::where('created_by', Auth::user()->creatorId())
                        ->where('type', 'product')
                        ->take(10)
                        ->get();
                @endphp
                @foreach($products as $product)
                    <option value="{{ $product->id }}" 
                            data-name="{{ $product->name }}" 
                            data-price="{{ $product->sale_price }}">
                        {{ $product->name }} - {{ $product->sale_price }} ريال
                    </option>
                @endforeach
            </select>
        </div>
        
        <div style="margin: 10px 0;">
            <label>الكمية:</label>
            <input type="number" id="testQuantity" value="1" min="1">
        </div>
        
        <button class="btn-primary" onclick="testAddToCart()">اختبار إضافة للسلة</button>
        <button class="btn-success" onclick="clearResults()">مسح النتائج</button>
    </div>
    
    <div class="debug-section">
        <h3>📊 نتائج الاختبار</h3>
        <div id="testResults">
            <p class="info">لم يتم تشغيل أي اختبار بعد</p>
        </div>
    </div>
    
    <div class="debug-section">
        <h3>🔍 فحص السلة الحالية</h3>
        <button class="btn-primary" onclick="checkCart()">فحص السلة</button>
        <div id="cartResults" style="margin-top: 10px;">
            <p class="info">انقر على "فحص السلة" لعرض محتويات السلة</p>
        </div>
    </div>
    
    <div class="debug-section">
        <h3>📝 سجل الأخطاء</h3>
        <div id="errorLog">
            <p class="info">لا توجد أخطاء مسجلة</p>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // CSRF Token setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        function testAddToCart() {
            const productSelect = document.getElementById('testProduct');
            const selectedOption = productSelect.options[productSelect.selectedIndex];
            
            if (!selectedOption.value) {
                showResult('يرجى اختيار منتج للاختبار', 'error');
                return;
            }
            
            const productData = {
                product_id: selectedOption.value,
                name: selectedOption.getAttribute('data-name'),
                price: parseFloat(selectedOption.getAttribute('data-price')),
                quantity: parseInt(document.getElementById('testQuantity').value) || 1,
                tax_rate: 0,
                is_manual: false
            };
            
            showResult('جاري إرسال الطلب...', 'info');
            showResult('البيانات المرسلة: ' + JSON.stringify(productData, null, 2), 'info');
            
            $.ajax({
                url: '{{ route("pos.enhanced.add_to_cart") }}',
                method: 'POST',
                data: productData,
                success: function(response) {
                    showResult('✅ نجح الطلب!', 'success');
                    showResult('الاستجابة: ' + JSON.stringify(response, null, 2), 'success');
                },
                error: function(xhr) {
                    showResult('❌ فشل الطلب!', 'error');
                    showResult('رمز الخطأ: ' + xhr.status, 'error');
                    showResult('رسالة الخطأ: ' + xhr.responseText, 'error');
                    
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        showResult('تفاصيل الخطأ: ' + JSON.stringify(errorResponse, null, 2), 'error');
                    } catch (e) {
                        showResult('لا يمكن تحليل رسالة الخطأ', 'warning');
                    }
                }
            });
        }
        
        function checkCart() {
            $.ajax({
                url: '{{ route("pos.enhanced.get_cart") }}',
                method: 'GET',
                success: function(response) {
                    document.getElementById('cartResults').innerHTML = 
                        '<h4 class="success">محتويات السلة:</h4>' +
                        '<pre>' + JSON.stringify(response, null, 2) + '</pre>';
                },
                error: function(xhr) {
                    document.getElementById('cartResults').innerHTML = 
                        '<p class="error">خطأ في جلب محتويات السلة: ' + xhr.responseText + '</p>';
                }
            });
        }
        
        function showResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            
            resultsDiv.innerHTML += 
                '<div class="' + colorClass + '">[' + timestamp + '] ' + message + '</div>';
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="info">تم مسح النتائج</p>';
            document.getElementById('errorLog').innerHTML = '<p class="info">لا توجد أخطاء مسجلة</p>';
        }
    </script>
    
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <hr>
    <h3>🔗 روابط مفيدة:</h3>
    <p><a href="{{ route('pos.enhanced.index') }}">Enhanced POS</a></p>
    <p><a href="{{ route('test.enhanced.pos') }}">اختبار Enhanced POS</a></p>
    <p><a href="{{ route('dashboard') }}">لوحة التحكم</a></p>
</body>
</html>
