{"__meta": {"id": "Xd2e59eea052d51d08d37177c9d5cc7c6", "datetime": "2025-06-28 16:10:24", "utime": **********.175202, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751127023.740775, "end": **********.175216, "duration": 0.43444085121154785, "duration_str": "434ms", "measures": [{"label": "Booting", "start": 1751127023.740775, "relative_start": 0, "end": **********.121165, "relative_end": **********.121165, "duration": 0.380389928817749, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.121175, "relative_start": 0.3803999423980713, "end": **********.175218, "relative_end": 2.1457672119140625e-06, "duration": 0.05404305458068848, "duration_str": "54.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46423072, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2428\" onclick=\"\">app/Http/Controllers/PosController.php:2428-2462</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00202, "accumulated_duration_str": "2.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1572568, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 82.178}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.167976, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 82.178, "width_percent": 17.822}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1597506063 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1597506063\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-427521568 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-427521568\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1699094482 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1699094482\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1975208708 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127021650%7C31%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFsTHdUeXZwVjhxZjNKa1FuRC84Q1E9PSIsInZhbHVlIjoiaXVFUXVFbXZBbnNjWWxZQjdwUVZqcmpEUUFNTFR0bEJ2YzFXZW5GMnE1eWFsUzNkMDdiVkUxQm5Ya29XWDRYU1AzT01KZFFzZ0ozRW0yaXBNM3R5OFB2VXVuNUpWYVBMcVNRTWtrbkNMZnhYZTBrRS9FbXZreVpRS2VidXA3NlNuTmZxcUxQOFptdmpMU3ZNU2ExYXhHdEhlSjVPeE9NUUR1cUQvOW5QUXhJdE03MVBDbXYzVDFSSWNBaUlWdUZLcVoydVJWcnVhQ1NZVUNycUEvTDhFUXR4SnFPYjBxcnZHdWdtcFJkMWgvUklyOHJoMDFBT1VlLzBNUmlaclNGaUpzUEQyRy9wanljbnZtQnB3YUNSQ0tLLzVPUkhiRzNQVlZOblo2VlJVa09PV3ZNblBVTjZvUmxlRGtYY2VDNEJ6THRBL09jaTA4NThNdFdRb1dvdzl5RXYwWm1hK0tpZktOOExuOHBCd0NnZTlXMEo3YVk3UkVwTGRtMnRrdHRkMzBlbXRTQlFwSWpUM1J2RTVZOGJLTnBHRG5vN1Y3V25yNFJFVlBHUTBnd3M3RndwRkVNSFg3b2Z2ZGVuVzNOZU9CSStSQUhTV2s5VkFqWkJxeHhRNWxwTDh2OVk4WTVmZitMM1RIN2dyNjFiZ0M3dTBXcWdZMnVmREhVS2F1TkQiLCJtYWMiOiI2MDEzNTMwNTE4ZmMzOThhNTQ4N2M1Y2NkZTExODFhZjhlYzNiMWU1MWFlN2VjNDA3MGFkZTFhN2Y4YzIzNGM0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhsUUkra1cvY2FYWThqQm5ydXNsNGc9PSIsInZhbHVlIjoiTGJYWWlLNktLdkluYlJ2VHd4Rjd5RXZiemNFeWZEOEJCNzh1eTd2R21PUllPNzhvenUyanVjWnZ3enNEdmJ4NjQ4RmpZRncrd3VRT0hPYnpPa2NiekNyUW9iaEVVY2wzNFZ4cXBnUFRQRXRlc05oeFFMdG5jRloxU2pMWUhuMG40Z2RHN1dYb1piUDU0YWNwUGh6TkxsRCtJVmNBUDVjalZwRUhBbStXQnFiWFhHTzZ3UWdUdW1WaVFLeFVVY2JBK0J1eXBYeENrSzk1dHFmMmd0UEYvaUcyRWlSNHpvNjZjeHY2N3RJQUF0SEdyQ2FiSDJmUzdLY2ticE9ES2E2cUVzOU9wTGVNYUZFaHNSQ0FTRzlMa21xY3FBcHhueVhWZ1dpQXZibEpPbWdUQlVJZEFjYlhtWlIvV0FlMXhaWWhKKzQrZ1Z3YVcySlNRVlNoUFB0UE54VHp3VFIzWll6dzkwNkd2NGhRckhUQUllOS9WTjNEdU0vZkJXOU83OU9peHB6S1JkdDFFdVZRbjFISDkrb0RtK1RUTzVKSnNyRWZYR2M1N1dxK2NsWElBdVRtR3VZVkVSbUh3L25yRFlsRFo0a1JnbnBhZWpuNDZYNThGb2FTcEdSc2Rka25hbTFTcnZhcDZSOG8xNXJZYU5QZk9LQ21Fdm04NUlMTVRNaWUiLCJtYWMiOiI4OTIwMmRiZjFmNGQxOGYwYzdmZDYxODhjZjk5YTAwYTM2ZDYwN2Q1OWIwZTljNzgwNDU4ZTkwZGZlNzhlMTU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1975208708\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1174409179 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1174409179\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-260167687 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:10:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNteTFmQ21FTGxCWThqVk1PM0Qwc3c9PSIsInZhbHVlIjoiZTE0d0dOYi9vTHZtMEI5U2JSeTk5RXZ1S2cwbHhBVGdJUm9jdUpIbjB0OWY3OGJPZTRya2JUaC9rTHhSTC9PNzZ3cUNNeDVpZW1ZTW5hejExY2JKM3pNeU5LK3NaWFpqbFdDZ29pVGY5MyttTGJoYUZWZ0RSaFgwZkRpMDk4ZXpMbUpUSlhQMW1OZnBGZVhEcUF6WWErVVhjd2VMc1VkbFZtYmVGSW1xbGtzbWRkRERXQ1E4OXBIeW9Oc2hOSFNFZytEeU81U3R6N2NYaVlvSTl5TlFKc2h5N2hNMUthNVhHRnprRmF2YlhVTWNZOUpzTFZKK1Y0aTVVUkJsRFYxTFBrS2s3TkhaRHg4Z1ltTnArcm9IbkV5YVduSlBEUHJwVkxnZXJOS2pLdEhNb3U4TFU1RWxscWZVemRUaUxucFhSME5FUXRiSzN0MGh5WGlvWU5sZWhBbm9Ja3RPYWtHU01qY2ZGYzdjeENTZFE0a2dzZzN6M01WNGZ4TFVOYjJ1dU1jYkhOajJnWFZMUlRpNUs4Y2M1M3d3WnBSaVJoTkNnTmpXQURQM2s2bXcweDJqaVlrSDNkL3pIUnZBT21CVVVNWGlqd3p2ZWN1ckhTVXBOZ0VVZkRSWUV2MWc5Rk85cE5NMVVuZTRZdERreHBJckFmeFBwcjR1blJLK0NrbEciLCJtYWMiOiJlNWU1ZGI5NWMzMGIwOTZjYmU4Mzg1YmQxZmE5MzUzYjk0OGI5M2ZhZGE2NzdhMmY4M2M3ZjU0MTc1Nzk5ZTA3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkV5UlVCSms0dnNIVUJYM2k0RXczdHc9PSIsInZhbHVlIjoiN0RwRlJWTWJiZVY1SUhNMzZHKzhjaGRxcTRqUVVLcHJwMGMraS9wZHdZLzR3MEtvMmszVjJWYjBFT1NiOG5mV1pRb29zazg3dmtmbUFiYlJmckpITzJUSFlBUDVPVTFRRHpMQnF2QjduOGJvc092LzBZWFZjcDc4L2M2TFhGOUNlNEdJblhEdTdKQlFSdnNQSlF0RVRVMDVIYTU0KzZQYm9zamdkdnFDbkgrU3MwYVlUcnRtV3dyK0E4VG8xcm1Hc3J4RTBrNHJ1dGF6OTAwcGpXM1pVSFlhSGdsaDIzRjgxYVlqQ1FwVTdpay9XQXNjUzFvV01QM3NkcjQzanJCUWJwREVYRyszTkRpRk5zTFcrUjRTdTVFY2dlMHpKbm44OHd4WXk3c21pMHM2K3V5NmkwS2JEUFRBVDZNa1RpUzJ4WTB1UXZXeWtINkhkVXROV1ZnVi9LNmxPVDFnVkRjSjFHYjZCRXJCQ0hFL2p0dkgyVEcrdW1MaDluYWk5TWtRRktKOVphWmpQMENqbTVLZFRMUGFwbG1ncm1mNEJRektHbElwNlpIMTZYRGhNeDZSTll4c0NNb0MydVo0WFp6YmpyQTZkOTJQYXhHam1ya0ZEaHJudGF1cU5nOEtJRXhOcFNnN2pjTElFUnV3L3RueHVabWR1dVNyQnpybHprU3EiLCJtYWMiOiIzMDZkMjQyN2JhYTM5ZTFjZTVmZjYyYzI2ZWMwMDEwYzg5ZWFmMDZmOTM0MzQ4MjdhYzZjN2I5ZWJhYmY4NDAxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNteTFmQ21FTGxCWThqVk1PM0Qwc3c9PSIsInZhbHVlIjoiZTE0d0dOYi9vTHZtMEI5U2JSeTk5RXZ1S2cwbHhBVGdJUm9jdUpIbjB0OWY3OGJPZTRya2JUaC9rTHhSTC9PNzZ3cUNNeDVpZW1ZTW5hejExY2JKM3pNeU5LK3NaWFpqbFdDZ29pVGY5MyttTGJoYUZWZ0RSaFgwZkRpMDk4ZXpMbUpUSlhQMW1OZnBGZVhEcUF6WWErVVhjd2VMc1VkbFZtYmVGSW1xbGtzbWRkRERXQ1E4OXBIeW9Oc2hOSFNFZytEeU81U3R6N2NYaVlvSTl5TlFKc2h5N2hNMUthNVhHRnprRmF2YlhVTWNZOUpzTFZKK1Y0aTVVUkJsRFYxTFBrS2s3TkhaRHg4Z1ltTnArcm9IbkV5YVduSlBEUHJwVkxnZXJOS2pLdEhNb3U4TFU1RWxscWZVemRUaUxucFhSME5FUXRiSzN0MGh5WGlvWU5sZWhBbm9Ja3RPYWtHU01qY2ZGYzdjeENTZFE0a2dzZzN6M01WNGZ4TFVOYjJ1dU1jYkhOajJnWFZMUlRpNUs4Y2M1M3d3WnBSaVJoTkNnTmpXQURQM2s2bXcweDJqaVlrSDNkL3pIUnZBT21CVVVNWGlqd3p2ZWN1ckhTVXBOZ0VVZkRSWUV2MWc5Rk85cE5NMVVuZTRZdERreHBJckFmeFBwcjR1blJLK0NrbEciLCJtYWMiOiJlNWU1ZGI5NWMzMGIwOTZjYmU4Mzg1YmQxZmE5MzUzYjk0OGI5M2ZhZGE2NzdhMmY4M2M3ZjU0MTc1Nzk5ZTA3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkV5UlVCSms0dnNIVUJYM2k0RXczdHc9PSIsInZhbHVlIjoiN0RwRlJWTWJiZVY1SUhNMzZHKzhjaGRxcTRqUVVLcHJwMGMraS9wZHdZLzR3MEtvMmszVjJWYjBFT1NiOG5mV1pRb29zazg3dmtmbUFiYlJmckpITzJUSFlBUDVPVTFRRHpMQnF2QjduOGJvc092LzBZWFZjcDc4L2M2TFhGOUNlNEdJblhEdTdKQlFSdnNQSlF0RVRVMDVIYTU0KzZQYm9zamdkdnFDbkgrU3MwYVlUcnRtV3dyK0E4VG8xcm1Hc3J4RTBrNHJ1dGF6OTAwcGpXM1pVSFlhSGdsaDIzRjgxYVlqQ1FwVTdpay9XQXNjUzFvV01QM3NkcjQzanJCUWJwREVYRyszTkRpRk5zTFcrUjRTdTVFY2dlMHpKbm44OHd4WXk3c21pMHM2K3V5NmkwS2JEUFRBVDZNa1RpUzJ4WTB1UXZXeWtINkhkVXROV1ZnVi9LNmxPVDFnVkRjSjFHYjZCRXJCQ0hFL2p0dkgyVEcrdW1MaDluYWk5TWtRRktKOVphWmpQMENqbTVLZFRMUGFwbG1ncm1mNEJRektHbElwNlpIMTZYRGhNeDZSTll4c0NNb0MydVo0WFp6YmpyQTZkOTJQYXhHam1ya0ZEaHJudGF1cU5nOEtJRXhOcFNnN2pjTElFUnV3L3RueHVabWR1dVNyQnpybHprU3EiLCJtYWMiOiIzMDZkMjQyN2JhYTM5ZTFjZTVmZjYyYzI2ZWMwMDEwYzg5ZWFmMDZmOTM0MzQ4MjdhYzZjN2I5ZWJhYmY4NDAxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260167687\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1086237239 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1086237239\", {\"maxDepth\":0})</script>\n"}}