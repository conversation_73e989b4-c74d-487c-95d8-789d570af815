{"__meta": {"id": "X342f657c36bd4180b90d757b6f904054", "datetime": "2025-06-28 15:49:48", "utime": **********.608299, "method": "GET", "uri": "/financial-operations/product-analytics/top-selling?warehouse_id=8&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.112321, "end": **********.608312, "duration": 0.4959909915924072, "duration_str": "496ms", "measures": [{"label": "Booting", "start": **********.112321, "relative_start": 0, "end": **********.521582, "relative_end": **********.521582, "duration": 0.40926098823547363, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.521592, "relative_start": 0.4092710018157959, "end": **********.608314, "relative_end": 2.1457672119140625e-06, "duration": 0.08672213554382324, "duration_str": "86.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46202784, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/top-selling", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getTopSellingProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.top-selling", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=206\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:206-364</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.029020000000000004, "accumulated_duration_str": "29.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.556487, "duration": 0.015390000000000001, "duration_str": "15.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 53.032}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.580209, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 53.032, "width_percent": 1.826}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' and `p`.`warehouse_id` = '8' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 235}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.584245, "duration": 0.00532, "duration_str": "5.32ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:235", "source": "app/Http/Controllers/ProductAnalyticsController.php:235", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=235", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "235"}, "connection": "kdmkjkqknb", "start_percent": 54.859, "width_percent": 18.332}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 240}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.59139, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:240", "source": "app/Http/Controllers/ProductAnalyticsController.php:240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=240", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "240"}, "connection": "kdmkjkqknb", "start_percent": 73.191, "width_percent": 14.611}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_v2_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos_v2` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' and `p`.`warehouse_id` = '8' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 260}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.597075, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:260", "source": "app/Http/Controllers/ProductAnalyticsController.php:260", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=260", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "260"}, "connection": "kdmkjkqknb", "start_percent": 87.802, "width_percent": 1.93}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, COALESCE(SUM(wp.quantity), 0) as stock_quantity from `product_services` as `ps` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` and `wp`.`warehouse_id` = '8' where `ps`.`id` in (2299, 2300, 2204, 1527, 1545, 2037, 2077, 2201, 2202, 2285, 2303, 2305, 2306) and `ps`.`created_by` = 15 group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name`, `psu`.`name`", "type": "query", "params": [], "bindings": ["8", "2299", "2300", "2204", "1527", "1545", "2037", "2077", "2201", "2202", "2285", "2303", "2305", "2306", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 319}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.599062, "duration": 0.00298, "duration_str": "2.98ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:319", "source": "app/Http/Controllers/ProductAnalyticsController.php:319", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=319", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "319"}, "connection": "kdmkjkqknb", "start_percent": 89.731, "width_percent": 10.269}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/top-selling", "status_code": "<pre class=sf-dump id=sf-dump-779057351 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-779057351\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-105918340 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105918340\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-893136510 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-893136510\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-792493493 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125775754%7C27%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBUYUlzSGw4ZFA0WXFwQmhWdVRVUFE9PSIsInZhbHVlIjoienI3dmZ2WDJERHlVS2Njbkc1Wlp6azE3ai8rUk4wQStmRDF1L28zblo0SGFicEdud3MzYkRRa2FMY04xeDliMGVvTDFaQXNSYXRtUFQ4dTRyc2ZyZ2Q1L2VVRW9YZ2tiWFBBWWFBdEJ1RW1LYU4wc0pqWW42Wm1KaTN2cWxDSzJ0MGxHeitSQXNzZ29LeTZ6S0RsaGVYcVVLTDZlNDlEa202WFk3eUEzSHpyd2FjdmdhWmN3bzB0YXZFblFvYzFSRWhzS3ZRMHIrOXpZUEkvbVUzSHR4Vzh6cUcyb2lrUDBWZ2x2NE8xd3IxY0wySGRYMTV0N21tN3Jwd3E2bGhnWDZJM2NvZjhSeWtxc1haMVltbUFmc3VqZXBGTVUzYzJLaWpWQUlXUUYvNjhud0VGRDY5eW9zc1E5ZGFVZzBjdjV1RTJwTHRkcVZQMjlPL1BlRHJ2RFlpNlkyZlJlak9vOHBJT0pVb0xCTXphRVRxRmpFZnhKbE1ZQlcwdUp1d2I4SGc1d3RCNFRsNjBCb2pxUExzWE9DWUNQY1lCc2U4VVl3ZlBQaHFZcHBGb0NQS1BneG5RdWRCQ1ZTZXdaNWlLbFMrNzNrNmZVYjl6OW53NktJZDYwaVNKTGlNNGt6RUlVMWhNWWd1ODVjUkU0NENGTFNhWXdZS2tPWTR5K21BQkciLCJtYWMiOiI5MTQ4MDAwYzcwYjNjNGM3ZmM4NzY3YTU1YWYxMWIwZjg1MTdkMDQwNDE5NDdhZjJkZDIwNzAxMWQxMDU4NGQzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjAyVWpmTVVEQ0ZwWjFsYlFjejd3SHc9PSIsInZhbHVlIjoidDVjKzhVbmZqWU1WdHZNc01tNmlkVFRRckxJZ1I2dTJiajhBTmVxOEF5QnVONGZjY0phdFFESzJzMGtqQzNKMC9Memg5enlwSE8vME02dFE3aVUxVzJoM1FkQWhpb0t5MWIrM0xrNEo0MGNXeEExMTZhV2Z5bEV0eUgwMERRNzlXRVVOV25LelMrNUd5NC9TQjRTRFBXTTk5eFprSXZKUVNQRDB4L2dMcHZsOFIxVVEvQlExQ2VlOXlEdnlhaDZMbnpyQ0NPUVFaNERvUDlvaWNzNHhGWG0xditaM3lOK3VkYk5VNWRqeXl4ZTJhZ09iblFkNWlkZ1BQNUN2MUVNc0pqcVBIdVBQK3RJcFpEMDZNdGREelU4MjhOaUZrN2xWUktWeE9aays0cGp6ayt1amplQ2VMWTQ1My9JZENnelFJRHF6UUlpNUhVbEtrRmtGdWJrQ3poMFQrcGt3b0RkZlBwUDdkV0ZRcHdmNFdqYkFmV3liQzRRVG1mUklKQkhHRllpOXY1L1BsaERKNGxyOW95QnNDSWtKVmtEQ2szYzlWd3k1RUpwVytKRENMN2wzVkczMUluWW1iekt1TkFwQ09tZ3ZWM2tZTGNGcU96VXRPbGFZemt0RWwwZWNTZm9ScUYydUV4MWIvYVZGT3dwb1d4Yklkd01iUmVQNldYVEYiLCJtYWMiOiIwZGJiOWZjYmQ2YjZhMjc0NDRkZjdlNWQ2M2NhZDFlZjdlZDM5MWZlYzc3ODgwOTE2NWY4YjhlMjIwM2Q0ZmZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-792493493\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1927697669 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927697669\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1929720683 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFzSHd5eDZlOGxFRzh1cU1KWGlrUmc9PSIsInZhbHVlIjoiNnREMlgyd3BSM3Y5L04vcHRLVWJMN1BjMnkrM0xHdjdoVXN5Y1R5Q2hpeVIyUk5VMmFMcmF2ZmlqWkZDTWl2RTNueDRqeGw0VEthZURiOW5Za1lTSHMxWHJJajJRZ2ZHVzBWNkloWTdZZndsdUN6U0NVY3lCTm41dGlyMmRoc2lQU0ZxNTk5UVZtYmY2R2JiMndGdnhVWUNFZVFKUGxQUklWZHZTNHlIWjBiejlQMjNCd0pZdnFNMzhrbjEvSVNFejJjVkJuelZxRXAzZzRTa0trUytINnhId2J3K2pIdXpMNGdWMGlvVDU1UkVEYjJwZnlnZGxVc0pZNE41UDh1ZFZzYnI3SVlGUXVYc0VKUkdlamhmTlpLUlIyS1lUTWdEZTVQRHRiOWUvUUR5Z0FqTWRYV01ZcWtQMVhQZExKNlIwVGw3TkcycUhkcVBWbjlPSXVqb1JHbGtKSVlMRHFNVEhGMEd4UmJaaXVRVnNxa0doRGQyK2JpMlM4d0NTN0syL1FWZlhjeTFJc1hyMzgzazFaZVZDYi9VTnlOc1BOejMyWWw4U3VoK3RZa3luazA1MkF2LzNEbE5YL096QkFqdERHLzJkOUluVFpEZk9KbVExc2pET2FzZmRMaVdrejFFR01aaTM2V0s1K0lPSC9USURIcnozNWM1ODU5V0E5QXkiLCJtYWMiOiIyOWJlYmQ1NWIyZWI0M2JjNmYxNDUzMWFlNzI3M2M1Y2NkODQyZjIxMTllM2YyZDExYjlmMGE0MTRlMWZiYjEzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imxva1JwL09TNEdPUnJiY2Qyb0ZpRFE9PSIsInZhbHVlIjoiNUhjVnpLZzFOU0ZxNnBydjAwQVE2d1VIU1c2MWx5OFdaVTJVQ0JxMEtTclpTY3p2VWJaK015SmFMNUpoWHh4YTVZQXJCQ3hyYXFVcHY4WEFwU29kNjg3Y1M3QTNsa25HaE5jRTJxcmtoV3NqeGFQZzg4blMxY0E5ci9lMzM0VWJ4OU9abUwvM0RrWnlQdmlkZGxCWjRLeVVIczNKdnU4TFByS09Bb2w0Y3hQanlnYlJaMkZYcTBhb25VTHhRd1Exb1NyNVNNSURuV2VJWFU0YUt4cmxLckhBaWxuR1RESmpyc2pKTzlwYUxXTmN1ekVHQTNMbUw0Wi8yN2dvRFdqZmk4Q3MySEl3dVN5eVRTQitSbnJRaHUyc3l4MEJIQmlOY2JvQUFzSFpNb3RYKy94NjJJbzJFQ1czVFkvc0VvZnVRWTFpOHB6bVdINUlCeko1N3dEYTVkZ2RqNGhTK0E1a1o2S1VVb3RBRXozSUg2VlRBbEJRWmpKV2hnM2Jud2pyTFR6S01QaUREWnIxTHlTajNxZW1Ic0pFRlJJRDFSc1NxR05QVHNLOVZrMzB0dVJydWQwNU1IYUk5YTY3WDNremRpTk83andESnNmU3o4SGFKL2pIMG42NXdkUmhTVkxjK3hmaXRFMkZiRDVMaU10ZllqMXVMNXlOalZlUnlrNkEiLCJtYWMiOiJjODBkMGU1Mjc4MGFkNzI1ZmYxZTg5MWRlNTI3ZWRlMTU2MGRlN2Y4MzNmZDRlODFlZWIzYmQxYzFlOGEwZjMyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFzSHd5eDZlOGxFRzh1cU1KWGlrUmc9PSIsInZhbHVlIjoiNnREMlgyd3BSM3Y5L04vcHRLVWJMN1BjMnkrM0xHdjdoVXN5Y1R5Q2hpeVIyUk5VMmFMcmF2ZmlqWkZDTWl2RTNueDRqeGw0VEthZURiOW5Za1lTSHMxWHJJajJRZ2ZHVzBWNkloWTdZZndsdUN6U0NVY3lCTm41dGlyMmRoc2lQU0ZxNTk5UVZtYmY2R2JiMndGdnhVWUNFZVFKUGxQUklWZHZTNHlIWjBiejlQMjNCd0pZdnFNMzhrbjEvSVNFejJjVkJuelZxRXAzZzRTa0trUytINnhId2J3K2pIdXpMNGdWMGlvVDU1UkVEYjJwZnlnZGxVc0pZNE41UDh1ZFZzYnI3SVlGUXVYc0VKUkdlamhmTlpLUlIyS1lUTWdEZTVQRHRiOWUvUUR5Z0FqTWRYV01ZcWtQMVhQZExKNlIwVGw3TkcycUhkcVBWbjlPSXVqb1JHbGtKSVlMRHFNVEhGMEd4UmJaaXVRVnNxa0doRGQyK2JpMlM4d0NTN0syL1FWZlhjeTFJc1hyMzgzazFaZVZDYi9VTnlOc1BOejMyWWw4U3VoK3RZa3luazA1MkF2LzNEbE5YL096QkFqdERHLzJkOUluVFpEZk9KbVExc2pET2FzZmRMaVdrejFFR01aaTM2V0s1K0lPSC9USURIcnozNWM1ODU5V0E5QXkiLCJtYWMiOiIyOWJlYmQ1NWIyZWI0M2JjNmYxNDUzMWFlNzI3M2M1Y2NkODQyZjIxMTllM2YyZDExYjlmMGE0MTRlMWZiYjEzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imxva1JwL09TNEdPUnJiY2Qyb0ZpRFE9PSIsInZhbHVlIjoiNUhjVnpLZzFOU0ZxNnBydjAwQVE2d1VIU1c2MWx5OFdaVTJVQ0JxMEtTclpTY3p2VWJaK015SmFMNUpoWHh4YTVZQXJCQ3hyYXFVcHY4WEFwU29kNjg3Y1M3QTNsa25HaE5jRTJxcmtoV3NqeGFQZzg4blMxY0E5ci9lMzM0VWJ4OU9abUwvM0RrWnlQdmlkZGxCWjRLeVVIczNKdnU4TFByS09Bb2w0Y3hQanlnYlJaMkZYcTBhb25VTHhRd1Exb1NyNVNNSURuV2VJWFU0YUt4cmxLckhBaWxuR1RESmpyc2pKTzlwYUxXTmN1ekVHQTNMbUw0Wi8yN2dvRFdqZmk4Q3MySEl3dVN5eVRTQitSbnJRaHUyc3l4MEJIQmlOY2JvQUFzSFpNb3RYKy94NjJJbzJFQ1czVFkvc0VvZnVRWTFpOHB6bVdINUlCeko1N3dEYTVkZ2RqNGhTK0E1a1o2S1VVb3RBRXozSUg2VlRBbEJRWmpKV2hnM2Jud2pyTFR6S01QaUREWnIxTHlTajNxZW1Ic0pFRlJJRDFSc1NxR05QVHNLOVZrMzB0dVJydWQwNU1IYUk5YTY3WDNremRpTk83andESnNmU3o4SGFKL2pIMG42NXdkUmhTVkxjK3hmaXRFMkZiRDVMaU10ZllqMXVMNXlOalZlUnlrNkEiLCJtYWMiOiJjODBkMGU1Mjc4MGFkNzI1ZmYxZTg5MWRlNTI3ZWRlMTU2MGRlN2Y4MzNmZDRlODFlZWIzYmQxYzFlOGEwZjMyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929720683\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-933564496 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933564496\", {\"maxDepth\":0})</script>\n"}}