{"__meta": {"id": "X0af3d3c834b98a442c7bb3cf738e6c93", "datetime": "2025-06-28 16:01:29", "utime": **********.041908, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126488.531102, "end": **********.041923, "duration": 0.5108211040496826, "duration_str": "511ms", "measures": [{"label": "Booting", "start": 1751126488.531102, "relative_start": 0, "end": 1751126488.970377, "relative_end": 1751126488.970377, "duration": 0.43927502632141113, "duration_str": "439ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751126488.970389, "relative_start": 0.4392869472503662, "end": **********.041925, "relative_end": 1.9073486328125e-06, "duration": 0.07153606414794922, "duration_str": "71.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45831816, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00702, "accumulated_duration_str": "7.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.01543, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 28.632}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.026934, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 28.632, "width_percent": 8.405}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div%' or `sku` LIKE '%<div%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div%", "%&lt;div%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.030271, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 37.037, "width_percent": 62.963}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-2141285884 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2141285884\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-701317534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-701317534\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-663728760 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&lt;div</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663728760\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-79630835 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">27</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImM2VGwrQzRDUE8wVytmWTdvRmNabVE9PSIsInZhbHVlIjoieEtiS201RU5sUTkveTNnck9hUkFsbHBCaXo2RThZdU9veWhUdTRKRjViRlVhVUcyZFpiRlo4a2NCczZqTllqWkJUOVdUemJwTTgraHRYZjVCNm95dzc1M3ZDWUlhdnIvazFqWHhXMHJLY3NYTncxTHdOc1dRV0lheDdBbEE0SUdiNFBEV1V4bXFqTFQyb2RJMDR2VVYrNTZ6QXdXK0R4ZDdNVklOQldURW93UW90OU5OdzVHNDh0YzJxY09WdzZlR0lMZnphODhVWDBhcW81d2hwTzJjOWJhT3R1NlZSR092U2xYTHZaSmhJUndQYU56VGNZSVhsOFRPdHVSbVd5UVFleHhCV0Qxa2N6WmR4eEhLS3JjUUtjZURDTzFFcWxCN21zY0ttdmZ4Z285TkpxcVZaWDBaa21Hd01COVZUeFVTaExtcXdQaWdFRU10Y0ZuN0Vwd1JJTjdiUjdxYnZCKy9MZnhBQm9vTWVZZnhKb2NIVmk2Z0duNkJ4UkdoLzdZM1MrVWxCK3I2OXh1Qm0rU0pITndOeUFYc2xEVmVMWUErWnFnM1RybXAvSkFYTFlHSmJpL3NmdFp0Q1Q1eEdqVi9aUDJPN2tGb040Y3hHcUF1SXNMMUJWZXV0MjVmdGpUb1BHSHpNT1dlOE5FOUNZaVFnczQxbnllYm5NZ291TTUiLCJtYWMiOiI0ZjE4NmRkZmI1ZDEyMmVkMmFjNzc3N2FkMWFkYWU2ZDg4MzRiMmYwYzNlMzA4ZDYxMWZjMDMwNTc3YmU5ZGJhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjYwa1c4bjJmU2hIQlRnQVlvVW1UZ0E9PSIsInZhbHVlIjoiL0RtYTlXc090NUNQMWNrQjhqTGpVMyttR3FrS2g3bHlUTC9pUG9ZaDdhRmRlOG1BNW9rRCswOVVKTml4dXcrKzJvYWxOMm1paDl3ejdmYWY4bmVDTjNxUk80aTZyQXVOZnpWOG1nM0tDQ1ZuYzRhKy9heVAvSUhrblFnNzFPNy9oS05JSUgxd0o0cjRHS1JDQUtuT3pUdU12bkdDZWdyRDZvVGQ0a3lKWUNMWHBJak9Na05XaDZWMU5VNC9rZW4wdHNuMVp5a3NwMG5uRkVoSWt3em5BcXJNNEVLazduZFFHbHZteUoydldFNkxwaXVmdXU0MFQycVd0d2IzNmNXbnVidFN0eTlwNGtNQlB0OXZYaU5PL1d6TVlYeURpd1JNNzUrY0xoaUZzL2hrSC9vWHpQNnlxT3NRUUlmZmpxOUYva3FrQ1lLaFBGZ1R4L0M2UnlYOGhHTXV2eTkweVZ4YXltUjg1Z2xlNDNEVXo3Nmhvd3VCUitEcXlIb01YSHRkODVzdlRIT3ZURm1xTGVwamx0U3RaRzQ5dERIclhQN2FlTU5YRzRBRDh0ZGZBZHZaRzhWMDNMWmFDSGRkNlZNUGVCTXdiMS9BcWtCRTdPcGJiZm5wQ1MrellRRU1VVVNvbW9RQmpvbEZUeFdwUWhFS0UyR05QZkY1UnVoaVY4dFQiLCJtYWMiOiI5OTc2MWMzNmZjMGFiNjFjOWU2OTQ2YTQxM2Y1OWJiNjkwZDYzYzgyOTVhNTNmNjFmYTM1ZmZkMjZmYTAxOGM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79630835\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-56365 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-56365\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-391177285 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldGa0Z5RHB1RTFRQnl1RUQ3aW5jNnc9PSIsInZhbHVlIjoiUTBQN3JOWSswL1JyazBuNmxVSWwzNFhJNGdVMnkvWEduTHRUOFpSWHBkUTk3QjcybUNob3ptR1l0bENMczZZWkdBSiswZUxCSmVCUEllSEZxTVEyaTdDZ1dpTktPeEkwRDRpbnVCaDFNL0VUZ3RBV1BDSFlZNUxBTnZDVlRSdzIyYUFJdytTamNVLy9yV05KdlBWN2NCMmRMY0s5TE1YRHBaTy9ZU25ZYUorZFFTMFo1ZHEwQUY2MmVRaXA2SEh6dFB5MmVITlFDeXNnN1NIR2NkL0c0VENBdXVVQ0RlNG9QRks5N2tsRExpbENkaW1KVUh5OGZpWkJSdWl2WXFKUFAvdmJWUWZsVnRSTElCTXh0eW9EQzA1V1pWaDN2Y0xTK1pvS2tXeCtidkhqOW1ZNmIydGlUeUgrdk9STXp3M3dsUXBxTUY4ZXNYM3hSN2hNc2ZESjhwaVE1bWMrV0o1U1FpYmU4UXM5eXhwcUhMYnR0WXRHaFVnK2tnZUdRRXkwRkl4N1hZRDgxck45RGtzWnFZaitxYzlkVzJLT2h0V1dZd0hmTmJUNXUxcXlLanhBSklkYW1acjQvRFh0djJKWHB3T1hoVkVZQTlxdHdQQ1BHY3MxZ2QxNGhrdld3emlyS1FUK1BaV05nTVBxYjlWbkpzTzgxVWtzVjdIUDhEMUwiLCJtYWMiOiJhYTg5N2ZkYWVkMDRiN2Y1YjQ5ZjNjNDA2ODVjODgxYjk5OTJjMzQzYjEwOGFhYmU3ZmIyMzJkMDU4MGRiNWIzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImptSGZFcGcxM29WaWoyQ1ZUTDFrOVE9PSIsInZhbHVlIjoiM2xKTWlHQmdZQnZWL2ZjNWY5dWQwNnVPMm9KOW54T3oyNXFKQXBzVXQxUEZUT0duWjN2dmFXazlic0RpYXFOUTJtUHJnQVdCNnk1TGpIRVozeXJSOG94azdUMC9XS0ZMV1Zxbm9vYTZLdDR4cmc1Qi9naXJqTTZuOU1UN2R3eXVqYzdSZVpjWU9DTFlEZC9TYXJ4MzZIa051c0dHT2NnWFVsVUtHNnczcFgwN0JaTThMcERqZUU5bzkxY25pUTlJTnlucEhuWmFRWDZDQkI5T0xHVmFIVXJRRDdnTW1DMFVMNEsxSFBuN2tEMC9NdDA3UGt2c1BlcVB2QkhGNjR4M29TcnJRVjcyRU1pVHFBaWhwUFlNSGJkQnpkdm1rcVNXWlVuOHRmczRkakFjVjU0UExXOG1Sc1YydWJSdS9WZG12STVvbmg5U0JXSU5DbDF2aVpvQmNGY3haYk5hVGNvNnQ2Y1p0VGdFMU1qaVBmakNyRUVHU3lZR0lkOVU4S0VpTE5SLzZUZm0xUnl5U3BQNm0yTjVKcXpndWNjNFNoMHFkSkY3VlRXdllISnl4eCtOKzRiLytrSGtQMDdIWjJLT3dDeTNwaVJUVU9jc3V6NW0vanpodmdCNnk1S3BRNlVnU1JmMjhYVzQ2dytLdHE0Sk1SL2ZmQlVOcGpyQVhNNlQiLCJtYWMiOiJjMWY0OWVlOWJmOTNlMjRhNzY5NDQ3ZDUxMGZmNzE4M2MzMzgyOGI1M2ZkZWI1NzQ0Y2UxMjg1NDNmZGNlZWYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldGa0Z5RHB1RTFRQnl1RUQ3aW5jNnc9PSIsInZhbHVlIjoiUTBQN3JOWSswL1JyazBuNmxVSWwzNFhJNGdVMnkvWEduTHRUOFpSWHBkUTk3QjcybUNob3ptR1l0bENMczZZWkdBSiswZUxCSmVCUEllSEZxTVEyaTdDZ1dpTktPeEkwRDRpbnVCaDFNL0VUZ3RBV1BDSFlZNUxBTnZDVlRSdzIyYUFJdytTamNVLy9yV05KdlBWN2NCMmRMY0s5TE1YRHBaTy9ZU25ZYUorZFFTMFo1ZHEwQUY2MmVRaXA2SEh6dFB5MmVITlFDeXNnN1NIR2NkL0c0VENBdXVVQ0RlNG9QRks5N2tsRExpbENkaW1KVUh5OGZpWkJSdWl2WXFKUFAvdmJWUWZsVnRSTElCTXh0eW9EQzA1V1pWaDN2Y0xTK1pvS2tXeCtidkhqOW1ZNmIydGlUeUgrdk9STXp3M3dsUXBxTUY4ZXNYM3hSN2hNc2ZESjhwaVE1bWMrV0o1U1FpYmU4UXM5eXhwcUhMYnR0WXRHaFVnK2tnZUdRRXkwRkl4N1hZRDgxck45RGtzWnFZaitxYzlkVzJLT2h0V1dZd0hmTmJUNXUxcXlLanhBSklkYW1acjQvRFh0djJKWHB3T1hoVkVZQTlxdHdQQ1BHY3MxZ2QxNGhrdld3emlyS1FUK1BaV05nTVBxYjlWbkpzTzgxVWtzVjdIUDhEMUwiLCJtYWMiOiJhYTg5N2ZkYWVkMDRiN2Y1YjQ5ZjNjNDA2ODVjODgxYjk5OTJjMzQzYjEwOGFhYmU3ZmIyMzJkMDU4MGRiNWIzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImptSGZFcGcxM29WaWoyQ1ZUTDFrOVE9PSIsInZhbHVlIjoiM2xKTWlHQmdZQnZWL2ZjNWY5dWQwNnVPMm9KOW54T3oyNXFKQXBzVXQxUEZUT0duWjN2dmFXazlic0RpYXFOUTJtUHJnQVdCNnk1TGpIRVozeXJSOG94azdUMC9XS0ZMV1Zxbm9vYTZLdDR4cmc1Qi9naXJqTTZuOU1UN2R3eXVqYzdSZVpjWU9DTFlEZC9TYXJ4MzZIa051c0dHT2NnWFVsVUtHNnczcFgwN0JaTThMcERqZUU5bzkxY25pUTlJTnlucEhuWmFRWDZDQkI5T0xHVmFIVXJRRDdnTW1DMFVMNEsxSFBuN2tEMC9NdDA3UGt2c1BlcVB2QkhGNjR4M29TcnJRVjcyRU1pVHFBaWhwUFlNSGJkQnpkdm1rcVNXWlVuOHRmczRkakFjVjU0UExXOG1Sc1YydWJSdS9WZG12STVvbmg5U0JXSU5DbDF2aVpvQmNGY3haYk5hVGNvNnQ2Y1p0VGdFMU1qaVBmakNyRUVHU3lZR0lkOVU4S0VpTE5SLzZUZm0xUnl5U3BQNm0yTjVKcXpndWNjNFNoMHFkSkY3VlRXdllISnl4eCtOKzRiLytrSGtQMDdIWjJLT3dDeTNwaVJUVU9jc3V6NW0vanpodmdCNnk1S3BRNlVnU1JmMjhYVzQ2dytLdHE0Sk1SL2ZmQlVOcGpyQVhNNlQiLCJtYWMiOiJjMWY0OWVlOWJmOTNlMjRhNzY5NDQ3ZDUxMGZmNzE4M2MzMzgyOGI1M2ZkZWI1NzQ0Y2UxMjg1NDNmZGNlZWYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391177285\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1969735612 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969735612\", {\"maxDepth\":0})</script>\n"}}