{"__meta": {"id": "X95fd774cd93e8f3cc6a9f3b9838f6195", "datetime": "2025-06-28 16:01:22", "utime": **********.357297, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126481.802479, "end": **********.357317, "duration": 0.5548379421234131, "duration_str": "555ms", "measures": [{"label": "Booting", "start": 1751126481.802479, "relative_start": 0, "end": **********.28742, "relative_end": **********.28742, "duration": 0.4849410057067871, "duration_str": "485ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.287435, "relative_start": 0.4849560260772705, "end": **********.357319, "relative_end": 2.1457672119140625e-06, "duration": 0.06988406181335449, "duration_str": "69.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45832144, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00516, "accumulated_duration_str": "5.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.329407, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 38.953}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.343072, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 38.953, "width_percent": 10.465}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦 المخزون المتاح: 2%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦 المخزون المتاح: 2%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦 المخزون المتاح: 2%", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦 المخزون المتاح: 2%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.346059, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 49.419, "width_percent": 50.581}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-612712123 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-612712123\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1894532082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1894532082\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1925368651 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;&#128230; &#1575;&#1604;&#1605;&#1582;&#1586;&#1608;&#1606; &#1575;&#1604;&#1605;&#1578;&#1575;&#1581;: 2</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925368651\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-739214035 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">179</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldWT2twSzFCLzA0Q2ZqcXh2dE50aXc9PSIsInZhbHVlIjoiTkhyT1pQSXRFTnJXVmlXZkR0L0N2R2t4VUoyQnptcnBZQmFsSFVMZjB0TStBN25hanZUZlNnM2drNHVUNGU4WmtRZTdWcVhHajVLQ1Rtc1ZVdjdBcEk4QkhKQ1JnTW5BYVhOYW1na2dEenRkMXg4WnhHZXplNnNVc1I0eitXWE1XL2tqWmRFek5mRExLc0xQVGdOVXM4ZlBndXYweVpmcFFKdkRmbFZrR1kvcm9BcHVMeFNEVjNEU2RJQzd2MVczSFN6UUQ3NnJaUHd4bVZlYlZPVURhcElEV3Z4M1I3c2Qvell2VGhuWjJjNjdPL3JWV0M4RGJDb1V1VUxvNi8ydndodEZRRTQrcFYycUJhV0VEK01IN3Q2WHdMOWpDWTBlNWJIc0ZtZkphb0J0QTlJc1NCS1ZjTzNMZkR5Ly8yMGI2d2VUKytCWUpucmVxRWxvNjJiYytFSlpGQ3UrbFlaVmpPd0FWR2ZJbjVDNjVIZWNRMHdpUDNENlVRUjhMVkp0d29GZ1kxU0tsekRUZXFlWkZQRDY2aDVteEpWbUVTVlRGSGlTT1IxN3hPN2oyS2ZrSkR2bStDOC9CZzhNK1libVZUZ2pIcFRmVHN0cWtLMlhUTU9mM3ptUHhpc3pFSGtKTWlFYXdNVSthMVpKMHZoWjBFbi9LSVp1eVh1Y0wxT20iLCJtYWMiOiIyNTM4MDRlMWJmOWE1NjY5ZTE2Zjk5YmQ2NWQ1NjAzZmU3OGRlNDdkZGM2NTM5ZWU0MjhkMWJmM2YzZmY5NmM2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9UV3VMMkFSd05CZk96aU5kTWk2UUE9PSIsInZhbHVlIjoiWDViNUZjZ1dLVmhqU0swdGQ5VzlsUjB5VEsyZ1kvSkhQZ3hlUFVJM0d0Z3NkQUtZQisza0MrZjdwT2Z2QmZiNWtZTUxvbkxJcEJkNDZUTndxc1k1S3JpZkkzUjVzdXZZVnJjTVFsUXZRMS83QW4vWEdObEVQNkcwYmdCRnBsNmtManU5R1NVNDdQSGgvdi93RWgwZTN5RkZ6QWJEREdHbEp0UnlUOUM2TUNrT0wxbDliVXdEUzk0UnZINWJSYkVvcEprM1Z6MFB3T2VqRW45RmhZMlNYRGdvUWljSExqL01vanlRdTIwZkVJUmEySFUzSlZXWDhmZFhsM2JtT3JYS3FQTEZZY2FaSU9Ra1FlSlRvdlU2NzBMZHprcGFkK2tuMUwwaXFNeDZOdCtpcjVyVVdiZENQbVlSK1Y1NnpwZkl1bWFJS0Q0Vk5jOWpFZSt4U2YzcVQ4bExBRkRJNjNCK1pSUDg1NUlaQXM5VzY1WHY5YkJ6N2FkOXdLOE1BaXZwaUxIa0R6K1hEUUVUeFpGNEwyNE93QmxVK3JpZjNQVWxmTlhUcGx1SzJGcDExTjFUVWQxMDNzdWlOTjJYa0hzeDgyVHlhZFFtS0tSUTJEcGVodDlkMmZSZFgrMUkvM2RyYnF6dlhYUzNFdHRwNXZrMk53S2VrcWw3UHFGUVdHeE0iLCJtYWMiOiJlMzU4YmQ4YjNiNjc2ZGQ2NDVhZjI1MmJlYzgxMzU3MjE0ZGE3MGI1YWU2NGZlNDc2NDRkOGYzZDBlYzEwYzg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739214035\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-880226371 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-880226371\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-982355358 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRSWjlOakVoMGtKMjloR2dVNVU3alE9PSIsInZhbHVlIjoiZVVzck9ST3M4NnJTSnVGQTlaYlJ6NWpxbkRVYnBaQzV4bG05b3RnQW9QOFh3YVRhWjhodyt2QnR6dkRNOWxDNkJwZ2ZnL3laRjQrRFBkY2xaMlkrUTJaN0s2QzRYYm9PM3dXWXRPa1ZJRk16ZDFwVnpHRGxtbVhPTjJjanhZTzBFOEgrYVFtUEZmbGdFVmZ3NVVDTkRBd25RdlFEWDE1NkVLUmZZMm8zN0VuU2ZKcEhKMU5CSFRoNlFpUjRTN3A5RS85SUw5SmM3QzJXd0t4eWxIYm1VTURoMzN1eEdta3N4NkYwT3ZRU0xWb202WXd4NS9uaUdFZHo3Tm1vWG44RndPRVpFZitKM1JNMGFrTnUyUjFod1k3dm90RWFaTDdpcEtoUEdzWDhPdGZNMTAyZXRqTjFvUHNmM1BzT2NIQzhQMFpJWFJBM0hEMW5EcElZeDN6b05oamtHZHBKZmZxSGdnOHVUVHp3RVlXVHk2MFhuZ3RLYnduakp2Nk5MOGZPZWpTYWFXcU9PNU1yZUJOQ0M3dWJYY1QwTStkTENPcE5YbHRoM2Ezd2JUMEZ2T3M5L1FkMHlJSEdSWnlwcUdQRHVESVpyYkpjSnVPVE5Pd3BDMUdpMTdkTTRMTlFqWFNrbERhUmRIWCs1M1VjcTRJK0N2OWc0OUZvalpUOStvMEciLCJtYWMiOiIwYmUwZjU0NzdmY2JiY2M4NDJlMzAxZjk2OWZkMzUwOTY2OTA5YTI2YmE5MDY2OWUyNzg5NmY0NGRiM2Q1NGUyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkN6OXUxRFM3cDF5cExJQzdsN2NYTXc9PSIsInZhbHVlIjoiL3EvK1RBN2NkMHVaMXUyajhmZjB6Nk9OVXExZ1k3endKY2drMk1DbFljMkQyeG4yZTJNRFVXQURvRmNwUkVpVVB1SnNZNTJZL2pmSjZBeVdqMTd3eWN0T2ZYL1NVY2JqK3pYT0hkeitQd25GRVdNVlpZUVRXaU9UNksvaVVxVVdsakdWOWxITnJ1c1JsVDZpN2RaRGJ2bitTS2pHVUF3ZTlZczArWnN1eGVUTnVmSzhnampKOHR2UTFrU0UyNHZwV3dyUDdkMmlPNjNOc01tN3ltQXp6ai9QdW42WW1sdG1iSjJvdlR0YmVmUXNzOEtiaWlYUWU4UWdUMTk3UGpHRWxoRjBOTitPNEZTeHhsaDZIeVI0bk5WZGh0RE04ZWtTdVF2d09PcUcwWFNhc04xdDZkcDJXQ0E2ZGJtSnVCdGtFbUJPTDI4TklJdkUwa3RkL0dteHY2M1hlby9pZDlGWDlIVHp5WE5sY2c0RUJMLzFwSkNlQVp5NEY2MlBoamF2RzZ5Z0I1QnAwVDd0WFhXMjJZSW5LSUljd0RlNFdWOVhJRG8ya0VnZFk3UkhINUtlVGNxRll1ZWtEY3hab3ZCQlg4cFprNGxBM3R4OVAyVXpKS0F6NkE3RzdGMHFjbEhXNWpwRjBBYzdBSkR1L09QbTlxOHRIbUtnczE3M0RCT0EiLCJtYWMiOiI3ZTViMGU1ZWEwZWVmZWQ2ZjZlZmUwNjdiYmYxZTExMmY1ZDM3NTY1MWQyN2Y0NTY2OTg3MjdlYjY4ZDY4ZDI5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRSWjlOakVoMGtKMjloR2dVNVU3alE9PSIsInZhbHVlIjoiZVVzck9ST3M4NnJTSnVGQTlaYlJ6NWpxbkRVYnBaQzV4bG05b3RnQW9QOFh3YVRhWjhodyt2QnR6dkRNOWxDNkJwZ2ZnL3laRjQrRFBkY2xaMlkrUTJaN0s2QzRYYm9PM3dXWXRPa1ZJRk16ZDFwVnpHRGxtbVhPTjJjanhZTzBFOEgrYVFtUEZmbGdFVmZ3NVVDTkRBd25RdlFEWDE1NkVLUmZZMm8zN0VuU2ZKcEhKMU5CSFRoNlFpUjRTN3A5RS85SUw5SmM3QzJXd0t4eWxIYm1VTURoMzN1eEdta3N4NkYwT3ZRU0xWb202WXd4NS9uaUdFZHo3Tm1vWG44RndPRVpFZitKM1JNMGFrTnUyUjFod1k3dm90RWFaTDdpcEtoUEdzWDhPdGZNMTAyZXRqTjFvUHNmM1BzT2NIQzhQMFpJWFJBM0hEMW5EcElZeDN6b05oamtHZHBKZmZxSGdnOHVUVHp3RVlXVHk2MFhuZ3RLYnduakp2Nk5MOGZPZWpTYWFXcU9PNU1yZUJOQ0M3dWJYY1QwTStkTENPcE5YbHRoM2Ezd2JUMEZ2T3M5L1FkMHlJSEdSWnlwcUdQRHVESVpyYkpjSnVPVE5Pd3BDMUdpMTdkTTRMTlFqWFNrbERhUmRIWCs1M1VjcTRJK0N2OWc0OUZvalpUOStvMEciLCJtYWMiOiIwYmUwZjU0NzdmY2JiY2M4NDJlMzAxZjk2OWZkMzUwOTY2OTA5YTI2YmE5MDY2OWUyNzg5NmY0NGRiM2Q1NGUyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkN6OXUxRFM3cDF5cExJQzdsN2NYTXc9PSIsInZhbHVlIjoiL3EvK1RBN2NkMHVaMXUyajhmZjB6Nk9OVXExZ1k3endKY2drMk1DbFljMkQyeG4yZTJNRFVXQURvRmNwUkVpVVB1SnNZNTJZL2pmSjZBeVdqMTd3eWN0T2ZYL1NVY2JqK3pYT0hkeitQd25GRVdNVlpZUVRXaU9UNksvaVVxVVdsakdWOWxITnJ1c1JsVDZpN2RaRGJ2bitTS2pHVUF3ZTlZczArWnN1eGVUTnVmSzhnampKOHR2UTFrU0UyNHZwV3dyUDdkMmlPNjNOc01tN3ltQXp6ai9QdW42WW1sdG1iSjJvdlR0YmVmUXNzOEtiaWlYUWU4UWdUMTk3UGpHRWxoRjBOTitPNEZTeHhsaDZIeVI0bk5WZGh0RE04ZWtTdVF2d09PcUcwWFNhc04xdDZkcDJXQ0E2ZGJtSnVCdGtFbUJPTDI4TklJdkUwa3RkL0dteHY2M1hlby9pZDlGWDlIVHp5WE5sY2c0RUJMLzFwSkNlQVp5NEY2MlBoamF2RzZ5Z0I1QnAwVDd0WFhXMjJZSW5LSUljd0RlNFdWOVhJRG8ya0VnZFk3UkhINUtlVGNxRll1ZWtEY3hab3ZCQlg4cFprNGxBM3R4OVAyVXpKS0F6NkE3RzdGMHFjbEhXNWpwRjBBYzdBSkR1L09QbTlxOHRIbUtnczE3M0RCT0EiLCJtYWMiOiI3ZTViMGU1ZWEwZWVmZWQ2ZjZlZmUwNjdiYmYxZTExMmY1ZDM3NTY1MWQyN2Y0NTY2OTg3MjdlYjY4ZDY4ZDI5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-982355358\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-420769655 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420769655\", {\"maxDepth\":0})</script>\n"}}