{"__meta": {"id": "X8d958485cd4e089c2a39ca252ef5a804", "datetime": "2025-06-28 16:01:19", "utime": **********.9577, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.578772, "end": **********.957712, "duration": 0.3789398670196533, "duration_str": "379ms", "measures": [{"label": "Booting", "start": **********.578772, "relative_start": 0, "end": **********.90742, "relative_end": **********.90742, "duration": 0.3286478519439697, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.907428, "relative_start": 0.3286559581756592, "end": **********.957714, "relative_end": 2.1457672119140625e-06, "duration": 0.050286054611206055, "duration_str": "50.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45846920, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00472, "accumulated_duration_str": "4.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.93709, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 42.585}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.946596, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 42.585, "width_percent": 8.263}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦 المخزون المتاح: 2 قطعة</div>%' or `sku` LIKE '%<div class=\\\"product-stock\\\" id=\\\"productStock\\\">📦 المخزون المتاح: 2 قطعة</div>%') limit 10", "type": "query", "params": [], "bindings": ["15", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦 المخزون المتاح: 2 قطعة&lt;/div&gt;%", "%&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;📦 المخزون المتاح: 2 قطعة&lt;/div&gt;%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.949162, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 50.847, "width_percent": 49.153}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-825657254 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-825657254\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-433906938 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-433906938\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1265971200 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"75 characters\">&lt;div class=&quot;product-stock&quot; id=&quot;productStock&quot;&gt;&#128230; &#1575;&#1604;&#1605;&#1582;&#1586;&#1608;&#1606; &#1575;&#1604;&#1605;&#1578;&#1575;&#1581;: 2 &#1602;&#1591;&#1593;&#1577;&lt;/div&gt;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1265971200\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2017860127 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">216</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126477151%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im8wZ280WHBGWW1pQlB1djJVU2FWQ3c9PSIsInZhbHVlIjoiMWdCaEhhZ1YzOUJLZmx1aVp5bXlLVnhmZ1B0ZStXYjRGUkRVeE83YkwydVA4VlpsTVpsSW9yRHhCL2xTWU9xb3JGNWNXdGd2cWFmdVY5UVdDWXZCQ2FPRVhqY1RTaDZKNnpVZTVBRVRmUFc2OVE1dFdIeXV3L0FJblVQbUxtNUZ0cU1zRzZLWlpkMklYeVRJajNtTnFSRUVBYitXZXorb0EzRFRNK0lQdGtUbHVBR3MzQ1krcEhocm9OTW52NTRBbEthUlI5VnZrSkhqM3ZjUDJUd1hmTlVYSnd3MTNaSUQ0VzhTekpnZjUrRW42R2xPY2IxZDdsMDF1NTU2RnQ0T0lvUUhRekdEaFdIVjlORE5iaTZvRGp6K1dQdUwwVCtMTUV1ZkVlaFBWZHRQYUp4VDVIejhyMmVsUEJ5VlpXclMvR3ZMeHJqLzlnMTBpS3pndVoxL2luZVNGM1JRenEzOVZmWmpaSkdORXN3Ui8xR05jWnB1VWFtMkpxakRmWFRlSTdzMDkyN20vVkdjZnJIOElGeFRKSmpPR2VKdzMrMFQ5KzlLZ3RsTm9rR2g4eGRSSTE2YlV5VUVDclFXTVlLcHp1QVlCRzdLRGt4NjFUeGJJU0prcFdKWEpwMVIzbWExUDU2cWdNWkpGM3BlOW0yajdZUXJwamxrcFpWRXAxRE8iLCJtYWMiOiI1MTAyMTk4YmViODM5ZDQ1NDEwYjFkYzE1NDEzNGQxNTQzMTIxNGY0ZGNmMzdjMjM2NDMwODk3ZGNiODI4YTJiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImxNUFcwcGNzbVY0YTR0dmd3TGRHZWc9PSIsInZhbHVlIjoidmN2c1RhQ1FqcG5DRnhORk5uTnErWUtnczhLQ1JkZ2t3dEhnR29lQTdNcWRVOVVJTXFULys1RHd1Zkx6ZE9nRkNSdTkvMFJTZThTZTRiWGxhdDhpN2tya3dLY2VCUDd2cncwOFB1NTYzQXcvK1k1TjRzdzM4UEd3aldmMTh0WUJiOHVURGFwV2F3Mlh1VmJWbXlOQ2kyM05FQmZ3UTVIT3VPcXBMWmk1SnMwaVExMXFRMDZZcE9tR1gySDJIc2kvNFVDQTY0NWpHQlhlRDZKcVZSY25BRDVmMTdSbW91V2wwZDdqOG5XK0Q3ckpFb2pjM3RIdVhMczFzRW1HOExSVkdCQjRYMkM3L2hCMS9ISUlsT1hlWEVOa3FJK1ZoZko4RDQ2d1UxYng5RnNrSXRYcG41T2JQdjJYUFB1dnM2Q1luQ2tGRmNYd3ZEU1FRVEhSdnhIWnBiMlNMMmJuNWpVcGtvOXpCTjZLWXRJRmViSEZmVnJud3MvMkNZZUtPL1RZWUZpTjRZOGw4WkY0RGdsaFNmUk5tdCs5Q2xYWE1rT0JweU9RU2JBb0ZPK3BtRHlKdlF1WHhoNnljMUt0bzI0eDB4R3NUWVBSbEdNSTlrcGxIeVNnMk1aRVppemIwQXpkT2dhSTlmYUxyTDVRTHF0S0o4Z0o0dWFTTzVaOGFLL0kiLCJtYWMiOiJkOWNjZjk3MmVlNDJlNzRiMzY3OWZkYjI5ZjE1NzY2N2NlZDJkMjM1YzdkYmYxMGI0MjA4ODZlZGQyY2ZjOThhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017860127\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1306534795 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306534795\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1593277759 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:01:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ild6dUtOZnpSeFRIemo0YU9OajU3MWc9PSIsInZhbHVlIjoiejMrMzZGNFBFTFEyb2hCZTJpZ3dWQ0lzOEltaHE3elkzVzJwRmdaOUJUQUJkVTdjMHAyVTJrMFB4c25pVXBWaDFoOU1nUEdhaWdiRFBlTnhYaDVhSTJ3N3NFbjc4R1JoWWFIWUVUYW9CWk51b01BdWV5VjFESng0T1JDUlhlOHFoNEpPeUk3bnZER1NqTi9EV3dTWFI2Zy9ScUtpMlRTTTVzU0VmUjU5S0Q2WkJZdnY3K3l5L3VJQi9pUDF0Yk5xUkxUcExIblBxcm9tUzBZZ09JcXVTaVBTWFIrWGVITFdNV3o2MS8rZHNSUEVZNUpoRHpZZWFMZTBOL2p3eXBoQWF5Mk1kWU8xbDhVWTAxeWdvaDJPVXA0R2tYaTYyRkRhR2JSV1ByUjlDS29tWGZqNmFjS1hNTHFrS2lCb0tHYXFrZHNReUZ3L1BYVmNtMWZmNkUzZFBPZ214SGYyUHl4MDFDK1BERnErVC9RdUdEa0Nzdm92VmM1bFpaT3NnOTVFaFJ6VUpWaVpHdGhrMm1hbUpramhYV254c2RXWW9jUkRWYXNnZllCUU01SHdFM05BdjFBQ3RQVTR4U1h2ampPaGtQYmYzcDUzTlYvR0RpRFFSN2twNFB4Q1lTVTdNQTc0eDVwRzNsRjdrVDlHczdpTC9uenIvMzVsWUtXaGYxQjYiLCJtYWMiOiI3MDlhYmNmZGQzNzJmMDA3ZDFjYjU2ZTIwY2JjYjk0MTE4NDY5OTI1NmMzYjlhOTdjZmI3ZDcxOTFkYzNhZGY0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InN3Vm5zSHZCNUhZUWZUWitJcTVoSHc9PSIsInZhbHVlIjoiQ2tsRkN4dlhHQ2tZNExmTkNUa2I0V2pGWlhzVW5MVCsxekU4US9xS0VyVURMWnZRbHcvblBOeFJJS3lsM0FaOWRpckpYbGk1ZWFDd1habXFUQzh2eE1xN09DV2hDeDlHaEE0N3RtZHk0RVBrak9TRExabE12T2NJbFBwSWlGRVprNnA4b0xncXFYeTFVS1lIYk5zUWJXTG1tWlZDSzR3OE5oeVhjQTQ2TGFrcnJzZ2YrbVl1SlFNMlRJWEtCUm54Rm05RncvQkFxd0gzY1MwM2htZVNBL0xHbUZWbDdtTjROT2czcVBDdGdlYm5oQ3JJNHpVTDg1Nkw0SjdxQkRmREZXOFdnbUZPTEl3SDJYZ0VJdEVMdTNNQUNKRkVUaHM4VW8yQ1FhYmluM3ZuY01GSkh2ZjJ2dkhOdFJtcWsyZGV6OVlhNUhzdHhWSjFlVXc4TGw2d2VrQVdOdzdLdjd4aGlsazN0eHE1RytRZlVoSmQxc29sMDdOZ09FdzNXaUlsZ3pTaXFMYzNuOGFROURKZXg4WmtUN1dDeEtqQnZ4SWJ2TTF1cnc0UjhOUGtBTmUybEJEemlmbGFVYjVMMlVVMmpzZjROVkk1NEpyOXJOUTlucEVGdFJuS0pBU3h1Mk9NZXg5T0xjSmVWcUVBNWMwOFZYWHdTd2o2ZDlRdytiK1IiLCJtYWMiOiI4YTE3OTAwNjEzYTlmZWFmMTI2MmMzOTRjYWEyYzZlZmM1ODc0M2U0ZGQ4ZGIyN2M2YjM5YmI0MDg3MzgwMjUwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:01:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ild6dUtOZnpSeFRIemo0YU9OajU3MWc9PSIsInZhbHVlIjoiejMrMzZGNFBFTFEyb2hCZTJpZ3dWQ0lzOEltaHE3elkzVzJwRmdaOUJUQUJkVTdjMHAyVTJrMFB4c25pVXBWaDFoOU1nUEdhaWdiRFBlTnhYaDVhSTJ3N3NFbjc4R1JoWWFIWUVUYW9CWk51b01BdWV5VjFESng0T1JDUlhlOHFoNEpPeUk3bnZER1NqTi9EV3dTWFI2Zy9ScUtpMlRTTTVzU0VmUjU5S0Q2WkJZdnY3K3l5L3VJQi9pUDF0Yk5xUkxUcExIblBxcm9tUzBZZ09JcXVTaVBTWFIrWGVITFdNV3o2MS8rZHNSUEVZNUpoRHpZZWFMZTBOL2p3eXBoQWF5Mk1kWU8xbDhVWTAxeWdvaDJPVXA0R2tYaTYyRkRhR2JSV1ByUjlDS29tWGZqNmFjS1hNTHFrS2lCb0tHYXFrZHNReUZ3L1BYVmNtMWZmNkUzZFBPZ214SGYyUHl4MDFDK1BERnErVC9RdUdEa0Nzdm92VmM1bFpaT3NnOTVFaFJ6VUpWaVpHdGhrMm1hbUpramhYV254c2RXWW9jUkRWYXNnZllCUU01SHdFM05BdjFBQ3RQVTR4U1h2ampPaGtQYmYzcDUzTlYvR0RpRFFSN2twNFB4Q1lTVTdNQTc0eDVwRzNsRjdrVDlHczdpTC9uenIvMzVsWUtXaGYxQjYiLCJtYWMiOiI3MDlhYmNmZGQzNzJmMDA3ZDFjYjU2ZTIwY2JjYjk0MTE4NDY5OTI1NmMzYjlhOTdjZmI3ZDcxOTFkYzNhZGY0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InN3Vm5zSHZCNUhZUWZUWitJcTVoSHc9PSIsInZhbHVlIjoiQ2tsRkN4dlhHQ2tZNExmTkNUa2I0V2pGWlhzVW5MVCsxekU4US9xS0VyVURMWnZRbHcvblBOeFJJS3lsM0FaOWRpckpYbGk1ZWFDd1habXFUQzh2eE1xN09DV2hDeDlHaEE0N3RtZHk0RVBrak9TRExabE12T2NJbFBwSWlGRVprNnA4b0xncXFYeTFVS1lIYk5zUWJXTG1tWlZDSzR3OE5oeVhjQTQ2TGFrcnJzZ2YrbVl1SlFNMlRJWEtCUm54Rm05RncvQkFxd0gzY1MwM2htZVNBL0xHbUZWbDdtTjROT2czcVBDdGdlYm5oQ3JJNHpVTDg1Nkw0SjdxQkRmREZXOFdnbUZPTEl3SDJYZ0VJdEVMdTNNQUNKRkVUaHM4VW8yQ1FhYmluM3ZuY01GSkh2ZjJ2dkhOdFJtcWsyZGV6OVlhNUhzdHhWSjFlVXc4TGw2d2VrQVdOdzdLdjd4aGlsazN0eHE1RytRZlVoSmQxc29sMDdOZ09FdzNXaUlsZ3pTaXFMYzNuOGFROURKZXg4WmtUN1dDeEtqQnZ4SWJ2TTF1cnc0UjhOUGtBTmUybEJEemlmbGFVYjVMMlVVMmpzZjROVkk1NEpyOXJOUTlucEVGdFJuS0pBU3h1Mk9NZXg5T0xjSmVWcUVBNWMwOFZYWHdTd2o2ZDlRdytiK1IiLCJtYWMiOiI4YTE3OTAwNjEzYTlmZWFmMTI2MmMzOTRjYWEyYzZlZmM1ODc0M2U0ZGQ4ZGIyN2M2YjM5YmI0MDg3MzgwMjUwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:01:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1593277759\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1487731516 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487731516\", {\"maxDepth\":0})</script>\n"}}