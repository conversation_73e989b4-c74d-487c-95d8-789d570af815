{"__meta": {"id": "X373ef19f4f0643ec621a6f80dcedf4ef", "datetime": "2025-06-28 16:10:16", "utime": **********.984484, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.573937, "end": **********.984498, "duration": 0.41056108474731445, "duration_str": "411ms", "measures": [{"label": "Booting", "start": **********.573937, "relative_start": 0, "end": **********.912212, "relative_end": **********.912212, "duration": 0.3382749557495117, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.912221, "relative_start": 0.3382840156555176, "end": **********.984499, "relative_end": 9.5367431640625e-07, "duration": 0.07227802276611328, "duration_str": "72.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48250936, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00673, "accumulated_duration_str": "6.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9428432, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.288}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.951997, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.288, "width_percent": 5.052}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.964705, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 27.34, "width_percent": 9.807}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.966536, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 37.147, "width_percent": 8.172}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.970929, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 45.319, "width_percent": 35.215}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.976153, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 80.535, "width_percent": 19.465}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1528037349 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1528037349\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.96997, "xdebug_link": null}]}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1646030828 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1646030828\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-928466337 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-928466337\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1465806080 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1465806080\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126882941%7C30%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJpQ1FPRkxzVS83blFnVDJsSW0zeEE9PSIsInZhbHVlIjoiWW51L3VCWmZKU1F6eE5haUdQZzd1WGxTTURtbUtFSVdOVkYzY2lqZmFRSHNnWnpZSGhwcU9BdS84OFlKdjkxNFRmS2dBbTB5WUZBODcycXNLNmNJUmJhQlVoaHJidzgyRUN0SzdzdEZPWkYyQXVBdUFYOGpSbVhPMEpnME50MUhVZmExeVJkeWJpNUpYcldhbXBiMVFCR01EM0tnemxmbkVoQjNZSjVnd1YvWEVFZ2xieGFaOUt5bEFqTVkwRkZXTzVFTHVWWFlGemc1eHdrY0JBN0g2K3ZhS0tGc0IrUGliMVVSeG1XOGFrd2swVm84c0g2Tmc1aS90R1BBQ1hOZE05eUoyc1JlY3dBVHVOc01iT2l4UlFraG5Calg2VVB1WTltVzN5d3FmMFFwMS94ZGpYdUR5K0luWFpIY3ltTi9YUHh4VkVxTDhnSXo5VzJoZlA3VittTlgwMWZqNmxpRDQzVG01ZW9yRE4xallOTCtzZkUxaGR5cWdVT2xIZE0zblA5S2YwVzFRby9KV0ZTQllSallScmJteEl3SVMrU0dmLzZlZzc4YUhCQ3JlL2dUMzhPajFSNmg4eVJWdkFLTE5FbXhxTFRFTHMvMTlQTmZIVzRnZGViVm8wby9PWjFVelpsdE1ZZVR3alZsTFgvT3FvMWduYWM4alZGOGlzZEkiLCJtYWMiOiIyMGJjZTlhNjcyOTJkNWQ1ZDRmZTFmNGE1MDEzMWQ1NTBiYzkxNDZhNzEzYThkZTQwNDhkMWI5MjY2MjgyNDU1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhIU2J5S0RDWEtLL3ppSVZvSUFIQVE9PSIsInZhbHVlIjoiYzRUd2lqSy9LbDBnWWE1c0orMmtpOGl2NkZtME9OTmU2cEZ0Q2VEUTgvNllhMTFLSUxlc2R0ZUVRTHdMN0t4UFJUZEhwUHkwZEY2UTJsK0NtbnBHQ2c1c0hFYlVxbnNmZTFhQnF6S1o5RWxvNEpsZ093MnM4clJqUlVRQWk0U0w3ZnBPbFBuOHVHYy94SVovVlZNZ2ZyWVQ0WXBxS28vVkpQYTVzWlkvaXpyWHJkUVdYK3B5SlFsYVdkajdNZFdVRkgydWFpN2RmQzc0YXNOdFVyc1pEVXlKbkRwMnJUK0xVRGtuVUtsaHZUbUxPQW5WczhZK1ZIOWhBRzVZNDJONUY0Z2o3SGJ4NmpGYlEvekhUZlB0Tm05bm11S0MvQVZReWNKNUFyYnJWenBxUlBJeFp5NkVnNmJIRElmQ2hoNlMybGp4aXR2bUM0Ym96SGM2Wmc2Wmh5UkY5d0NHTFNha3FSdDRya1IyTkdsdjA3T0tCdlRSN1lvL0pRbUcvcjV6QWNLTElTdDdqL0dSb1JoWTNwTitNdllqbmlnV05YeGRtTzAvMzFUaFczNTZDeEo4R0lnL1Y0WitHdzlYMWdpUkJMTk83N3R6YUlVakU5SCtiVkNRL3E0aS9zbXh5YnRYUEFKRUNGTmdNbjEyK2NEMWJaRDRuQ2xCa2Z5ajJFZGoiLCJtYWMiOiI3Yjk0YzdiZDVhNWIwNjZiN2RkY2I5NmQ1NzVjM2FmMjlkNDU3MjBhYTBjNzk3MDE1OTJkYTkwNjYyZTQzYjJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-16350523 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16350523\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1999683645 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:10:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkQvTzdMWUNVZGIxVi92bkJyVklRTEE9PSIsInZhbHVlIjoiR05kZjIwY1hlNjBVOWZQelpaVXRGaVlRaTJWVWdyTVZ6OTFMRms5eGRnK3pHZFRMbXUrWHNzSkhraG9lZTc2RGpMcXRJUDV3VVB1WWkyTVFkL3ZxMVFGbUxaY3V5RURHVVNQdTlZWW14YThZbFV1eWpyMzduY1Z4N0JQZ1liR2dwbHVmZWxBYVQ2dlROYkJnNTdtSDhmeEtFTS9zUHRzcDlJN1c0SG13UjYwWG9yemJpR1NTbU9HUEJLQ2FSQjVoMi9KSWNJS1l1aUtFTzNqQWpyVzBwT011d0NRS3c4YnRySmV3K0J6a3dpaHhSM05NalJ5U2pFb2Z4N2twT0w0QnRIbGVCQUNQaUkzaS94MWVkTVhMM3dhSjAyQ3NoRExkRGJmYiszZURoeFpISy9CWHdBNnhTMlJ1YU5POWZIeXdFeTdhU2RVeERNQXFHTUpERmZ4TlhMWkx5cGl1QVN1VDNkenFHdkZ4K1dtKzdqa2xheEVaV3ZJRGdVY2NZbjZzNUNSeWtBbkJkcjlmd1luVGRzejYvZE1rSm1DOUV5WmxUdkMyZFEyYU9pdlhSUHFjZmxXbW9ubGErQ29aNWRHdHEyb1A5TjdzZVlZSHU2S3A0aU0zcGoxbnN2ZzBVb3hrak8ybW05SWh1eHArbXBKTjVCZkxkTXBIVlBtTmJ6bXYiLCJtYWMiOiI2YTIxNGMzNTgyMjMwOTZkM2NhMjI3ZmFkYTAwOTIxNTQ2OTNiNGNjYzk2N2Y1ZTc5N2NhNWQxMjQzMGVjZWQ3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjY3Z1VESGQwcmVHMXd5R0ZHd1JoUnc9PSIsInZhbHVlIjoiTXRXZWVBQjczYWxXYjEvQW9QQUtQTHpQSXhmeDNPMHRZRGtPS1ZiTkZFYVFCZGwxUXl2aUl1VlBkTUpLd2ZRd1FGb0hmaEM2VW1QU3pnc3lhaG5KWmJNdkVrd0swMWxUanpzRENzOUNYSkcvOEFnQjhFT0xLbmgvQVdMQWFlKzVBVUowbVp3d2w0VUd4Q2VUQWRrQ05scWhrNlhjTEkrL0RaVUp6TUV6YXdIdkk2RklJaUs5dEhZdXF0WFBwMVY2aVlyMWdLOFB0Uy91S3ZJWTNmQTc2WWFMZkh4WVA4ei9UREd1dzJXTkFwS09jUndIeDB3NVQ3c042dFpBamFndDVINk92OHZ5M2E3eEtHaGFTV1YyUTB5bVg3MitRc21VdG4vTlpnbDVxSUIwTkNmYUt1RTVyQkY2MFlkOTR3dzJjQmVJNlJWa3lnMjVIOEk3UTJCV1RXaUw1d1Q4eXhaWm4wbzB3cjBwUkMvd3NUUi9DRklwZ0JBejJWeUhGOThJRnkxYW4zN01LOUY0eHh3YWQ4VStjbkZWeHAxQjZkcUsrbUtkcXNwenAxN2FpL0wrNkdWclJ3dlVpQ25FM1BodTJBeXhSQUZpZVJPT01Nc1FVZkNwTnVQUVZBT1I3QmE1VmFyc2RrdzR2WWZtcjlHbEdpbnRKUm1HUUFwQXptVUQiLCJtYWMiOiIxNTMyMjRiNTg2OGRkYjAyOTUzYzZjOTE0MDRjOGZmNzBmYTY3ZWNjODQwMDM5NzQ4YzU4NzRjNjU2OGM4NGQ3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:10:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkQvTzdMWUNVZGIxVi92bkJyVklRTEE9PSIsInZhbHVlIjoiR05kZjIwY1hlNjBVOWZQelpaVXRGaVlRaTJWVWdyTVZ6OTFMRms5eGRnK3pHZFRMbXUrWHNzSkhraG9lZTc2RGpMcXRJUDV3VVB1WWkyTVFkL3ZxMVFGbUxaY3V5RURHVVNQdTlZWW14YThZbFV1eWpyMzduY1Z4N0JQZ1liR2dwbHVmZWxBYVQ2dlROYkJnNTdtSDhmeEtFTS9zUHRzcDlJN1c0SG13UjYwWG9yemJpR1NTbU9HUEJLQ2FSQjVoMi9KSWNJS1l1aUtFTzNqQWpyVzBwT011d0NRS3c4YnRySmV3K0J6a3dpaHhSM05NalJ5U2pFb2Z4N2twT0w0QnRIbGVCQUNQaUkzaS94MWVkTVhMM3dhSjAyQ3NoRExkRGJmYiszZURoeFpISy9CWHdBNnhTMlJ1YU5POWZIeXdFeTdhU2RVeERNQXFHTUpERmZ4TlhMWkx5cGl1QVN1VDNkenFHdkZ4K1dtKzdqa2xheEVaV3ZJRGdVY2NZbjZzNUNSeWtBbkJkcjlmd1luVGRzejYvZE1rSm1DOUV5WmxUdkMyZFEyYU9pdlhSUHFjZmxXbW9ubGErQ29aNWRHdHEyb1A5TjdzZVlZSHU2S3A0aU0zcGoxbnN2ZzBVb3hrak8ybW05SWh1eHArbXBKTjVCZkxkTXBIVlBtTmJ6bXYiLCJtYWMiOiI2YTIxNGMzNTgyMjMwOTZkM2NhMjI3ZmFkYTAwOTIxNTQ2OTNiNGNjYzk2N2Y1ZTc5N2NhNWQxMjQzMGVjZWQ3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjY3Z1VESGQwcmVHMXd5R0ZHd1JoUnc9PSIsInZhbHVlIjoiTXRXZWVBQjczYWxXYjEvQW9QQUtQTHpQSXhmeDNPMHRZRGtPS1ZiTkZFYVFCZGwxUXl2aUl1VlBkTUpLd2ZRd1FGb0hmaEM2VW1QU3pnc3lhaG5KWmJNdkVrd0swMWxUanpzRENzOUNYSkcvOEFnQjhFT0xLbmgvQVdMQWFlKzVBVUowbVp3d2w0VUd4Q2VUQWRrQ05scWhrNlhjTEkrL0RaVUp6TUV6YXdIdkk2RklJaUs5dEhZdXF0WFBwMVY2aVlyMWdLOFB0Uy91S3ZJWTNmQTc2WWFMZkh4WVA4ei9UREd1dzJXTkFwS09jUndIeDB3NVQ3c042dFpBamFndDVINk92OHZ5M2E3eEtHaGFTV1YyUTB5bVg3MitRc21VdG4vTlpnbDVxSUIwTkNmYUt1RTVyQkY2MFlkOTR3dzJjQmVJNlJWa3lnMjVIOEk3UTJCV1RXaUw1d1Q4eXhaWm4wbzB3cjBwUkMvd3NUUi9DRklwZ0JBejJWeUhGOThJRnkxYW4zN01LOUY0eHh3YWQ4VStjbkZWeHAxQjZkcUsrbUtkcXNwenAxN2FpL0wrNkdWclJ3dlVpQ25FM1BodTJBeXhSQUZpZVJPT01Nc1FVZkNwTnVQUVZBT1I3QmE1VmFyc2RrdzR2WWZtcjlHbEdpbnRKUm1HUUFwQXptVUQiLCJtYWMiOiIxNTMyMjRiNTg2OGRkYjAyOTUzYzZjOTE0MDRjOGZmNzBmYTY3ZWNjODQwMDM5NzQ4YzU4NzRjNjU2OGM4NGQ3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:10:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999683645\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1671012833 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1671012833\", {\"maxDepth\":0})</script>\n"}}