{"__meta": {"id": "Xecd88211a3da3ec99f61a338fdb0f898", "datetime": "2025-06-28 15:02:43", "utime": **********.415205, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751122962.744564, "end": **********.415221, "duration": 0.6706569194793701, "duration_str": "671ms", "measures": [{"label": "Booting", "start": 1751122962.744564, "relative_start": 0, "end": **********.260522, "relative_end": **********.260522, "duration": 0.5159578323364258, "duration_str": "516ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.260533, "relative_start": 0.5159690380096436, "end": **********.415223, "relative_end": 1.9073486328125e-06, "duration": 0.15468978881835938, "duration_str": "155ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46161536, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.04107999999999999, "accumulated_duration_str": "41.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.303571, "duration": 0.023289999999999998, "duration_str": "23.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 56.694}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3372002, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 56.694, "width_percent": 1.534}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.341651, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 58.228, "width_percent": 2.702}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.344492, "duration": 0.00462, "duration_str": "4.62ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 60.93, "width_percent": 11.246}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.372912, "duration": 0.01057, "duration_str": "10.57ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 72.176, "width_percent": 25.73}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.385428, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 97.907, "width_percent": 2.093}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-1585614958 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-1585614958\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-201778047 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201778047\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1774648359 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1774648359\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1670496168 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122759782%7C5%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik0zVGxZOUE4NmxJTGYrRTI1WjdWR3c9PSIsInZhbHVlIjoiVGtGNFRPSkxuOG5VbVVQK3lTYXhLYmFOQWNRL09BSTJVU2ZsUkJZeFlLcjRnNkNSM0dvS3pLQUJxTU5JUURFVWhmMGVhQm5SbEtZZnRBdjkwTzNONE5KcGZ4VHVncHROQTZURXJXc0tDT2ZGUXAyR3d5S05XQ1VVSDhyZmU2LytNOFhaUFRwYWZSYkgvVlVpSkY0NmdOZzFlaS8wWUt5aWZlVmtlUnJVTHZZM09YdFU3NG9ZSUVGVTBsUnJGRHh1M1U2UVlmNHZaeUZabGhsZ0ZZSUFHVGZHeGEzRzk3TjJ1T25IMUh2citwQ2FSMlJORzNVL2Eva0NLajRmWDFKbkdDZUx2bFR1dURFVU0wUmgrT3RHNFM5MTg1S1VScU90OEc1Z09QSWlDTWx0bndpcndOb0pySWlJTThwVDMybmV4SWFMdFU1cWRVSmhMQUtGRDZlU2wrTDhyUGdVVGhYWmxDVHc1UXU1K1JhSmZ5cFB4UUFRM01pZENNMXA5WFdyaFlWL01RYTYxUWFwdGdOZkg0WTNVOGwvd1ZKamJPNjJzRE44UGViWTk0TEVLWEI2bnZkTGszWFlHVXNOM2orWG4wZ1BiUEdWYjJ1d09LYmNFbTJNeDJQT09XeDAxazhVa1phZ0FhOXhDWDgvWWVUUWtoQ2R0ZVZhOEkrNlpGSTciLCJtYWMiOiI3MWJkM2I2MDVjMzQ4NTI4ZTg5MmQ1NTUyNGRlMWExYTFkZGVmOThkMzY4MjllYWZhZTAwMDY4YjNiY2Q4ZTZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhQaFBBcGU3a1ZLSlJrZXlzVzBSTWc9PSIsInZhbHVlIjoib3FLMmdsRnFrUmExWkUrNENpWGNZRFhScjRTZW0wTFcyL1oxMUNzMyt4L2Ezelh1cnNKeFhLMDhTRXIrZGRRcWIwckh0eTVna2VMMXZ1aXdnR2xaZG5OUzROVllzVDJIQTRCaUxNeTUrQkN3Sm9xWkJkMFpaK1ZFZnk2QjFjTkoyaW0wZnFRcWwyd3JmOVpsU01rSmxiS0o3WGIvcXgzbnZpUUtNcUp6MjREak11Rm1YMUE3UktWTmkvOUpkVEtvdXFxM0hRenlTOUlNdlUweEQ1S3NSb2wrejdUNUhNMHJ3d1p4bzNpWTlBaVJ2NTZpLy9hSlpac3RqRktXUHlnOFJRYWFneDhCUi81QitaeEJmUmRLdmt2Tk1YcUdFYS8ya21sMWgwelFQK0N1RXVKOGtWWStJMXhDaStpMnZQN3AwZi9WNEdBUWFUTnFsZWRDTXhFdEllYlpQaGFlTnkvLzlzSVZHaVJUVFM0aU5CRnp1cE1kZkZSL0ZCNVQvQTBpZEJzNDZSR0JNaS80aWI5WThzcWhhN2NZZ2dXT1loR00yOVFwK0FWL1Bac0Vhblo1dDJRU0lxNlF0aDJxcXFXZjlkZDF6aFV2R1VSZm9CeklIZmVsbmZ3clp3QW0wbHNXTTVDek12Tnk1Wmt5S21LVXJacmlvSlJBcDBwQThxQ08iLCJtYWMiOiJmNThjYjZkODk2MzlhYmRmMWNjNWEzYjJkNWFmYzVlM2I5NGE2Y2MzMDA0YzNlMzcxYjllMmQ1Y2JmZjczOGRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1670496168\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-238773279 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-238773279\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:02:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjladWF6Z1dWK0xMREZxbzRGT0RvMnc9PSIsInZhbHVlIjoiaXRGQURlT2xxNml3aHpuQ0lRYWMwQ1BORHora1BPTWFaQk1BVStGQTFFUUVMYW1oWTBGNHd3emJ5UGhoblR1MVFHNUpWOVZIenZNdFI0N0JOa2FLazNIQlhXZlBKT1ZxbzVMRFp5RjNZODZPU09NQ0Y5OXF4eDFMdnhDRnEwMkpOS0FFT1hYSjhiTHlpQkREQ3VOMVQya3NjVkJPUUFhYmswVXRiNVFtQ0w1QjhrZno0aTMzODgyYWY2V1RManFITStOTFRKM3lTWEJqcWttRFd2VnNRb3BuMmpPckNkY3pvbWdkeFA1U0gzVXdNVFczeHVNWFQrczlaVHJIWUh4MnRtbmpHWUlkNlpZUkZTWWc2bDRDaEJEbXN1cE9EbVZ2NHpqd0lEZ0lLKzRCVEtUZ1V0SlNVMlhOMWpkODBFSlZPWVc4SkFldkNlOFdaZjJMZ1ZMOGhpZ1RMVFBzNTVNb2JDY3NIQ2tIbXBmLy9kSGRiQ3Y3ekU3dEhPbUZrVjZEMXlFbitrcjNyQ3EzL1I1MjdmaDBZZkgvbXlXRlhyRVU2TWRCRk1KcFFvYVduRUJSZFhyQVVEQ3lGcXBkd2JOaXp5YmtvUndCa0hGOTVmZGR3R01TT0EzcVZoS1AxYmxTaWlhYjl6R0tEcytYWGd6Z1ovN1NXeDdRQjRaM1B4M0ciLCJtYWMiOiI0MDFlNDY5MDE3YjU2NTJhNDllMDBlNzA3NjY2NzIxYmUwZjAxNWNkY2RjODkwYjA1ZjBiMzgwODRjOTIzMGZjIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:02:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRuK09ocUdHNGZ4NkNMZkh6YXhxVlE9PSIsInZhbHVlIjoiWi9HN0VPeCtQNVl5VVJVYXFPNWt5TE9kZTNBcnhDUVFqTEFDL0MxL2NmTlh3akUrdEdOT0dDaU1wOUpyaG5ORUo5d2pSbzhjczExVmZhdEJlL1B2UFhOZlhTK0NudzVEQ2h0S2xzbnVBYVJmM3JaUXdwTEhFTTNrYksvazhOUXd4c1JOYU9ncmI5cTBieVRldGgwYWM0bnpiMkNKQnFRMjJwZ1ZjalJXa2VhQWlkMXkzaWVYSFN3N2dEdjlHaFlaV2I3Y0U4bWV2UWovaTZ0ZEU4T1R5RTMya2tNcFUrOTl3TDFVdnlvNXJGNlRkbUNPZjZmWHgyTTJlZUxpUk81SmRyeDlqOEtlVzE5MXdwdFl4aG92RzBIcnFWRkpMSXUzYXVWUTBJMzJPV0d1bjJVa1k5R2VDZ1B2M2xab3ZPQVpxSFpUTkkvS0pRRTN4MVAyNFZ5ZjFWc0FkZDNaMzZxOVJlTnZxbTFkWFBWaHB4b1BzNXFaRmFHbVJJQ0FIcldEbndBMVp0THJ0ZWt1TnFrZlArS1FuQnJlMkgvb1k1ODgydWV1Z21GVHlJZHhkWWx2aXVUd1JiQ09IaFFWamtZeDcyalJXNE92RzVzKzJOZldzcU9GUkJETW9JaGVRbmVGZGFNT3ZrN0xITS94cWFmL1FmUWs2alpNZWVIMzA5cHAiLCJtYWMiOiIxMjYzMjA1YTBiYTQ2NmJhZjViYzY1OTMxNzY1MTA4MzI0ODUyZjMyZDI2MjQzZDhlMWNkMmJlM2Q4ZDVmMDQ3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:02:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjladWF6Z1dWK0xMREZxbzRGT0RvMnc9PSIsInZhbHVlIjoiaXRGQURlT2xxNml3aHpuQ0lRYWMwQ1BORHora1BPTWFaQk1BVStGQTFFUUVMYW1oWTBGNHd3emJ5UGhoblR1MVFHNUpWOVZIenZNdFI0N0JOa2FLazNIQlhXZlBKT1ZxbzVMRFp5RjNZODZPU09NQ0Y5OXF4eDFMdnhDRnEwMkpOS0FFT1hYSjhiTHlpQkREQ3VOMVQya3NjVkJPUUFhYmswVXRiNVFtQ0w1QjhrZno0aTMzODgyYWY2V1RManFITStOTFRKM3lTWEJqcWttRFd2VnNRb3BuMmpPckNkY3pvbWdkeFA1U0gzVXdNVFczeHVNWFQrczlaVHJIWUh4MnRtbmpHWUlkNlpZUkZTWWc2bDRDaEJEbXN1cE9EbVZ2NHpqd0lEZ0lLKzRCVEtUZ1V0SlNVMlhOMWpkODBFSlZPWVc4SkFldkNlOFdaZjJMZ1ZMOGhpZ1RMVFBzNTVNb2JDY3NIQ2tIbXBmLy9kSGRiQ3Y3ekU3dEhPbUZrVjZEMXlFbitrcjNyQ3EzL1I1MjdmaDBZZkgvbXlXRlhyRVU2TWRCRk1KcFFvYVduRUJSZFhyQVVEQ3lGcXBkd2JOaXp5YmtvUndCa0hGOTVmZGR3R01TT0EzcVZoS1AxYmxTaWlhYjl6R0tEcytYWGd6Z1ovN1NXeDdRQjRaM1B4M0ciLCJtYWMiOiI0MDFlNDY5MDE3YjU2NTJhNDllMDBlNzA3NjY2NzIxYmUwZjAxNWNkY2RjODkwYjA1ZjBiMzgwODRjOTIzMGZjIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:02:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRuK09ocUdHNGZ4NkNMZkh6YXhxVlE9PSIsInZhbHVlIjoiWi9HN0VPeCtQNVl5VVJVYXFPNWt5TE9kZTNBcnhDUVFqTEFDL0MxL2NmTlh3akUrdEdOT0dDaU1wOUpyaG5ORUo5d2pSbzhjczExVmZhdEJlL1B2UFhOZlhTK0NudzVEQ2h0S2xzbnVBYVJmM3JaUXdwTEhFTTNrYksvazhOUXd4c1JOYU9ncmI5cTBieVRldGgwYWM0bnpiMkNKQnFRMjJwZ1ZjalJXa2VhQWlkMXkzaWVYSFN3N2dEdjlHaFlaV2I3Y0U4bWV2UWovaTZ0ZEU4T1R5RTMya2tNcFUrOTl3TDFVdnlvNXJGNlRkbUNPZjZmWHgyTTJlZUxpUk81SmRyeDlqOEtlVzE5MXdwdFl4aG92RzBIcnFWRkpMSXUzYXVWUTBJMzJPV0d1bjJVa1k5R2VDZ1B2M2xab3ZPQVpxSFpUTkkvS0pRRTN4MVAyNFZ5ZjFWc0FkZDNaMzZxOVJlTnZxbTFkWFBWaHB4b1BzNXFaRmFHbVJJQ0FIcldEbndBMVp0THJ0ZWt1TnFrZlArS1FuQnJlMkgvb1k1ODgydWV1Z21GVHlJZHhkWWx2aXVUd1JiQ09IaFFWamtZeDcyalJXNE92RzVzKzJOZldzcU9GUkJETW9JaGVRbmVGZGFNT3ZrN0xITS94cWFmL1FmUWs2alpNZWVIMzA5cHAiLCJtYWMiOiIxMjYzMjA1YTBiYTQ2NmJhZjViYzY1OTMxNzY1MTA4MzI0ODUyZjMyZDI2MjQzZDhlMWNkMmJlM2Q4ZDVmMDQ3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:02:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-928333180 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928333180\", {\"maxDepth\":0})</script>\n"}}