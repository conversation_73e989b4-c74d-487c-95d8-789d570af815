{"__meta": {"id": "X65109bd37c76eb0fd128082225259e69", "datetime": "2025-06-28 14:59:11", "utime": **********.475518, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.084549, "end": **********.475531, "duration": 0.*****************, "duration_str": "391ms", "measures": [{"label": "Booting", "start": **********.084549, "relative_start": 0, "end": **********.4209, "relative_end": **********.4209, "duration": 0.****************, "duration_str": "336ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.420909, "relative_start": 0.*****************, "end": **********.475533, "relative_end": 1.9073486328125e-06, "duration": 0.054624080657958984, "duration_str": "54.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00247, "accumulated_duration_str": "2.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.448172, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.611}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.45787, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.611, "width_percent": 11.336}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.468748, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 78.947, "width_percent": 21.053}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122750616%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBBWWhuTXlhcXBRSVJoRU0vNnJDbFE9PSIsInZhbHVlIjoiNyt0WTZwMTJNNE9xb29YNjBuK1hZZGxWdWRxeGVxc1FOb2NaR3FncFFuK2pTVFdlRnQxVGx5SDFHdXYxY0Jud1FVNXBTT01aOFI1eFJUYWQ4SExCNmt2aDkrRHZ4S3VoSWRkeDBKNkZhRDZRb1FLRGlqanp6cVUyZlBsWnFHdGFtYnA0Q29pK01qTUpKOE1EaE1SUXpocVdHQk5Ka3gyU0VHa0l3QWcyNTZZbmozWm4vQVJBVXZBZXRzYkZrY0dabDNwK3lWRlpDSXZVNG9BcGZmMElEQXFXM2hVNWVCRW1aYnBQNjVRekhpeitSeEdyd21RcUdvWktQS2lOMUVkL2pFN2Zlazk2VTRJTE1WZVpKc0FIcWQ1Vkg5T3QzczFMclY2ZmdWOSs4WDU0RFNXb294SkZIYk1YRWtEamQxeElyWW80NEptNjU1Y3BSQ1hJQ3VYOVYvaVBXbVVHRy92TzVHV25zVFFOZmtMNktoUnRlQTN2ZUw0dHJqWWtGT3BNd3pPbDlkeUtUNm4xSEt2VThFdkVuN1lMa1dpZVI5cE04dzVuZEJ0cTBvRmdCYjVzdjhEWGp3SWVHcXdxUmZVWVBWOUwvUFB5ZmVTeG5EY3d2MUYvM0lsbHRQQTROUGNPZEE5QnJkRXlwa0xXRFR2d3MxNG1uaEJXQ1Y0aWZvTWQiLCJtYWMiOiJiODcyY2U5MWI1Zjg4Y2VjNDMyNmFlNzAzNjQ2M2I1MGVjYWJiZGY5MWIwMDE5NmNmYzk0YzM3MGViNTk4YzJjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik00NTdPK3hLSGtKNWZPZ0lDcWJsY1E9PSIsInZhbHVlIjoibUFuak5BNU5EU2xHZHFEMVFGSlJETmJnNHJMSmdSbEZwL3dHMlpSWlY0Zm1OSEIwUVc3a0ZjelpKS0pmQktrNTBFM3JNbkZwQVBwUmd1dktKR2pzMjBua2FSTjcwdlRSVnhUUnBpZ3Mrdk51VWh6ZU1WcEFyUkFvL0JncXZuRUowUGllcW03VFFtZWdKSEVKSTdtVzZsYnVBVksvRmJybEZrYVVHRHVQTGRFMllqTFlzT3lDVFVXL01hck55QTI3OFVBSmJQNmJtTTl1dVpzSHoyenFpb2wyampPSnA5VzhxNGVVUmkxVjdrZ1NuaHRyb2MyTkd5TVgvOHZUTktWdXZqTDF4cld2UlVIVUlEdGVGaFBzay9zeUVFbDZlUTB1THd2UTdoa1lQQUtoSFpZUjlsUmFOdTUwM2t4Z0kzQ28xazU2c3dRV3VoYlJTdXRqekdXL2d2ZTY5bE14ektzdnplZzRmaHl4MnBEVCs5aVpNSHVPWGVIbkErRms1ekg5aHlqVnF2RTVqSTh4bmZsQXdNNW10QVRaU2dQY3pCaGFwSDVJbWRaaDBFNGdCVmJscTFHNGJPamIzUm00OU1rS01qM2Q1d1JXMkhNN00zNjA5QlA0eW5DU014aGtEUjRxc1JkMFd1VnNhUWNmYmVvWnl5QSt4bStqUWkzM1hKK3MiLCJtYWMiOiI0ODhmY2QzODIxNzNmN2JkZDRhNjUwNmQzZDViMmYwNDlhMTNlYjdmZDFhNTE1Mjg1YjRiMjkwYTIxOGZjZTk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1405438971 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6nyGbgT6n8t00JbJ5CSgj7vkpXnxnyISs5XS6j6J</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405438971\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-316273490 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkwvN09RWXNjL1hzTHF3MVM4dk5SaWc9PSIsInZhbHVlIjoiQzFyWlFrRkdaZ1VLZ2VEeXU1dzBHYXd3VENaSzRYaWN3dklEelRCRkFsejkrL29uN3hnZU56SzZuWU9Jclo3SjJsOFdtQjNYVjN3dXhId3ZMa2xnS01aS1ZoczdOOUZ6eUFMN3lLZmdPd2dJS0E0eHJiRXRJY0xpa01SaHhEb0t6UGI4V2lRdzRxRHd4VWR3S0k3MHcrenZycVlvTnBCRk1vUFhxR1R0WllRWG15TC8zMEFXeUs2QklBMHJLblNWUFJIaWdzTEhYQ2tBeXhzb2FHV1lmU0xlQWNFcjZTLzVWU3dYQ3B1Y1AySnpjRVhhbWdlWTlnQytOemp4L2NpZG5YN0pPQlJHYTlxbVVQdnhoRmxzSXJBNWVRVWZXSitxZmk4V1FkcHZJMDlHUkpaU3hFdFZrRnVnc2pmT3djR0dpSG0vOG12YkM3eE50WktlVHFrRVdQM2gzRkdBNkVkUUZ3RnBzbytkajFhVmFGZmVJelpDaGdrdVUxbGExSnJkWnpDb1NISGxYTGM0VTFsdjI4MVFKdEM2VkJsY0thNVROUFBnWStnMUwveFI2a2lNSDVoZlVyUXJCM3F6d21qVm16c1AvQ0tWMmpVenl4SmZhVklZakNiL1o2WmU4OERhZTNBdVAxS2hlYnZUWVo1SXJOL0g3ZDdLZVJwb3RiNmYiLCJtYWMiOiJiMTU5YTg2NzQzY2NhNGIwZDVhYjk0NWEwMjgwY2JlZmY0ZjdjMjlhOTUxNGIxZTk2MzQ5N2I0ZTVkNTU0YWNmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFEQnYzN1JidlZIVlN4dk9Xb1gyb3c9PSIsInZhbHVlIjoieC90Sm5aZjJ1YWErbmRvV2xpTWlzVytmRG5GR09remFhQWljU2hlQjZWNmkvcUJDQzlJOGwyVmRPTU8rdVNlNk5OL0dVU2FnU3JFLy8xa0R1bEVQNGRkT3hUVnlBTXV1bDdUdSs2WnJkZkVMUGJxOVQ3bHdqYzczdFZjTjRkTkRvbklaMlJCUUNZUG5mcXpSeGFpa25Hc2lVY1YxTmRraFNCSjAxV1hqanZuMHJEOXhEeWtiQU1aQUt6WmthaCtkaDJtSDBTbEVyRG4vaXNOcnZlc3lIUUFlVWpwdGI4VG5jaHJqUEREQ25uUVZZejJ4RFZqQ2pwQ2RvUlRlNlBQMy9pVk9WUkZlS0pXZCtKS0dVOWVlQ28wREhNclgrbHpSWXZEc2JseHVMTm04R2NJV2l3VFZ3Njl3d0Mwa2lPQ2tBY3c5cUZIZndEbGpBaDRqTDNHODZKRnhTc0FJT3ZWZnEwZXNOblhiWXUzMUg0UDRtRnhQWVh0djFvMlI0aGphdS9aUEJJRU4rNXNLQzF2TkthS0RvK3gxRjJqcGVtOFd0UU9tZU5FMFJwc24zeFpGS2hrTVFwdVlXczdjOG5VZVQ1SVVGRkRjckl2ekFNY0EzUzdka2pTZi9VVEZSRXliT0hqb2c0eTJralpyNU1DaWJab29UUEE0UVFJUDlHRmEiLCJtYWMiOiJkMWQ2NjZkMGJiNjA4NWEyOTcxZmFlZjI3OWI4ZDU1OGEyMjNiNThiODIzNDgxOGExNzVmYmE2ZDY3NDBhNTNhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkwvN09RWXNjL1hzTHF3MVM4dk5SaWc9PSIsInZhbHVlIjoiQzFyWlFrRkdaZ1VLZ2VEeXU1dzBHYXd3VENaSzRYaWN3dklEelRCRkFsejkrL29uN3hnZU56SzZuWU9Jclo3SjJsOFdtQjNYVjN3dXhId3ZMa2xnS01aS1ZoczdOOUZ6eUFMN3lLZmdPd2dJS0E0eHJiRXRJY0xpa01SaHhEb0t6UGI4V2lRdzRxRHd4VWR3S0k3MHcrenZycVlvTnBCRk1vUFhxR1R0WllRWG15TC8zMEFXeUs2QklBMHJLblNWUFJIaWdzTEhYQ2tBeXhzb2FHV1lmU0xlQWNFcjZTLzVWU3dYQ3B1Y1AySnpjRVhhbWdlWTlnQytOemp4L2NpZG5YN0pPQlJHYTlxbVVQdnhoRmxzSXJBNWVRVWZXSitxZmk4V1FkcHZJMDlHUkpaU3hFdFZrRnVnc2pmT3djR0dpSG0vOG12YkM3eE50WktlVHFrRVdQM2gzRkdBNkVkUUZ3RnBzbytkajFhVmFGZmVJelpDaGdrdVUxbGExSnJkWnpDb1NISGxYTGM0VTFsdjI4MVFKdEM2VkJsY0thNVROUFBnWStnMUwveFI2a2lNSDVoZlVyUXJCM3F6d21qVm16c1AvQ0tWMmpVenl4SmZhVklZakNiL1o2WmU4OERhZTNBdVAxS2hlYnZUWVo1SXJOL0g3ZDdLZVJwb3RiNmYiLCJtYWMiOiJiMTU5YTg2NzQzY2NhNGIwZDVhYjk0NWEwMjgwY2JlZmY0ZjdjMjlhOTUxNGIxZTk2MzQ5N2I0ZTVkNTU0YWNmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFEQnYzN1JidlZIVlN4dk9Xb1gyb3c9PSIsInZhbHVlIjoieC90Sm5aZjJ1YWErbmRvV2xpTWlzVytmRG5GR09remFhQWljU2hlQjZWNmkvcUJDQzlJOGwyVmRPTU8rdVNlNk5OL0dVU2FnU3JFLy8xa0R1bEVQNGRkT3hUVnlBTXV1bDdUdSs2WnJkZkVMUGJxOVQ3bHdqYzczdFZjTjRkTkRvbklaMlJCUUNZUG5mcXpSeGFpa25Hc2lVY1YxTmRraFNCSjAxV1hqanZuMHJEOXhEeWtiQU1aQUt6WmthaCtkaDJtSDBTbEVyRG4vaXNOcnZlc3lIUUFlVWpwdGI4VG5jaHJqUEREQ25uUVZZejJ4RFZqQ2pwQ2RvUlRlNlBQMy9pVk9WUkZlS0pXZCtKS0dVOWVlQ28wREhNclgrbHpSWXZEc2JseHVMTm04R2NJV2l3VFZ3Njl3d0Mwa2lPQ2tBY3c5cUZIZndEbGpBaDRqTDNHODZKRnhTc0FJT3ZWZnEwZXNOblhiWXUzMUg0UDRtRnhQWVh0djFvMlI0aGphdS9aUEJJRU4rNXNLQzF2TkthS0RvK3gxRjJqcGVtOFd0UU9tZU5FMFJwc24zeFpGS2hrTVFwdVlXczdjOG5VZVQ1SVVGRkRjckl2ekFNY0EzUzdka2pTZi9VVEZSRXliT0hqb2c0eTJralpyNU1DaWJab29UUEE0UVFJUDlHRmEiLCJtYWMiOiJkMWQ2NjZkMGJiNjA4NWEyOTcxZmFlZjI3OWI4ZDU1OGEyMjNiNThiODIzNDgxOGExNzVmYmE2ZDY3NDBhNTNhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-316273490\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1133087890 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133087890\", {\"maxDepth\":0})</script>\n"}}