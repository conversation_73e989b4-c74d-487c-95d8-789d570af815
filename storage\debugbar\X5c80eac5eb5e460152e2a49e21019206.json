{"__meta": {"id": "X5c80eac5eb5e460152e2a49e21019206", "datetime": "2025-06-28 15:44:49", "utime": **********.580145, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.216722, "end": **********.580158, "duration": 0.3634359836578369, "duration_str": "363ms", "measures": [{"label": "Booting", "start": **********.216722, "relative_start": 0, "end": **********.530532, "relative_end": **********.530532, "duration": 0.313809871673584, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.530541, "relative_start": 0.31381893157958984, "end": **********.58016, "relative_end": 1.9073486328125e-06, "duration": 0.04961895942687988, "duration_str": "49.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45819624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1952\" onclick=\"\">app/Http/Controllers/PosController.php:1952-2010</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00447, "accumulated_duration_str": "4.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5596368, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 40.492}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.569499, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 40.492, "width_percent": 7.606}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%Manual products are not allowed in Enhanced POS%' or `sku` LIKE '%Manual products are not allowed in Enhanced POS%') limit 10", "type": "query", "params": [], "bindings": ["15", "%Manual products are not allowed in Enhanced POS%", "%Manual products are not allowed in Enhanced POS%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1982}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.571907, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1982", "source": "app/Http/Controllers/PosController.php:1982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1982", "ajax": false, "filename": "PosController.php", "line": "1982"}, "connection": "kdmkjkqknb", "start_percent": 48.098, "width_percent": 51.902}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1944675211 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1944675211\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-442904677 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-442904677\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-511916004 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Manual products are not allowed in Enhanced POS</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-511916004\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">68</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125486476%7C3%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNVdGZFZ3JPZlI1WlJrSXRTQ2ZTR3c9PSIsInZhbHVlIjoiOGlzNEhWNEV0NlpUektCc1h3SGlyT1FUUXZtWDZ4TlNvVURkMEdKNEd0SGpUb3JtYzZNWmpNYTJjSEplK0ZPZ1ZhUlRsTUNaL1FUbjVPcURRVWVpS2tndWlCOWVveTFwZnlaZmhPUlErTkNWay9XcjJ5TStUaXhoeHRlNnJGdS83N3FETjhYb00vVUVLNlVnRmdFRHNOcUQwU2ExQlpQL0NKU21jQ09mWXVkUHU4Nk5nRjVOQlRaZStDK2l3M2FpUSs2ek8raVNjM1RLL0RtNHh5Wms0QUNZSFhPN09mRkxiVkxRSWhXWXNPQm5GUThaeE52R3gyY1RwYlBlWCt0VkNDVy85dWlLL2hWd3VRYi93ZHh2VWdnQVdrcmlUN0RnbG0xNkxjL1pzY3VqeVFQM2tqTlhoSWVPUk5ibVVlaGFiWW1qeHc2eTZleUNpdFhPV2YrVERpRDJZVlJuNVBNNUdFN2l1Z3k0OE5lWDVWUm96QldNV0RCcGxrSkZCbnpkTzdCSG5jbDFMeThEOS81dFQwK2hZcUxlMjJGakptcUROcGFWc0xpY2p5NXVqdTZnRFdSTEZJZEJTUDV3QUxJbHpmb3dxSUlUSDB6b3c3Q0FFWEN5dy9nTXEvTTlaS3dhWEI3bDRrZ0lJQzRxK0gzRmNxQ2tuZGtva3FWZlUvTlQiLCJtYWMiOiI4NWEwYTM1YTVmM2NlZDQ0ZDE5YWU3ZTljNTNjYjUzYzI4NGEzMDU3MzFiMDAzOTExMzhjZTQyOWViYjBmNzg0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlwSmZmanV4amFCYzQvc2YxRTMyV1E9PSIsInZhbHVlIjoiTGhPWldyY0k2cU5jWlNNWjA4Z3JyS0xtb3AzMjdlL3llUk5YcTIyYkdjVnJoRjJMc25EWDRwODRIQ2pGc0Q1bVJWc014Tm9vSkFkMG9teUdLVFZEOW42ZHAzaFBMYlphM0xyZENxZVJXLzlVZitJdjl3REhsa2txVE5Fenp2dFJ4MFNpTStYZnJseElESU5oRUZnR09keVowZzRVdEo3VTloZ2ZhdDJwRHVXQnVmMm92SGZaOXArdGJkeUduYWVrbjlRTWZjZGI1akwxczdWZ1YyakF4VHBrUG51YjB2WjBYY2cvWk1UNm1udkdrK1dEblAwMW1XRkU1Y3NIWjE4SWhhQTBsSXpXcTRidityWW9oM1hJdHpPc0huRkFXdC9jcFN4VFpwaDB0M2t6MU9yOXA5ZkF5a25vdjNJSENGR3MyQTZQWTJrcDF1N1lsUEIyNmdpcXpSb0RMWiszNkcwdjRjVEdlbk1obnkxM0UxOWpST2JMV2FJNmhrNkVwbFRDZkJ0NStzRHZmRjhlT3V5YkpuZlpUK1YxaTZEcGxYTEVZNGxJcW13MWpFL1R5VGtKVEJrZ3RhTEhaTGVMYWZmdE9adEh4aFkyZkVBSGt5SmFqY25udFhrZjBTcGFHRWxpSGVLUjBXaFVRaG5oVVRzU3JzT1ppQjVYTFNLZG5UbFQiLCJtYWMiOiIwNmM1MDNiNGQ4NjI4M2MzNWFjYTQzY2VlZjdhMjVhMzhjNWYzNzk0ZWE1NDcxM2RmNTY5MGJlM2M1NGU4YTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2063218333 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:44:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBrTmNTU1VVM3lFbTJQSXdTNHF5MWc9PSIsInZhbHVlIjoiUC8wMEEvdjJpK0VlenVHVnRkZXBpN0QyaW8yalJmcDlMOTh0K1g1NEUrZkVBME9wakgxbWFya1BhU2krQXVTZzVhZjRmYUtqVCtOSEt4U3NyUm11SXdIc0NHbm5TTzdOUFNZMUVXNlJ3K3dWa01hdXFBR1FTVlViS2lOU1p0Ykt2MzJ1Sm1hWjkxNGNxR3ljK2ZhY290UnBUQ1NDYnZ6V1Q2WHJWdjBaMVhKN3dWYXFORG5lOUtMaUR5VUpMK0hhd3hzOU1FNFd3Zld3Z1dpcmxOZ1p0b3ZzZVpacjJVbGhteDk2aUVMenJ0MGZRWkxHTlZPM1VUQTJBSFJ2RHFqNkNsTHZSNTBrOFJYWjJnTE8zQytmZ2dWbkhRcTdhY1VUUGxpVTVKblNnNzF6ZzBYT3doTXVFSFhxWnlvcTMwSGN6Zjh3b0lzMXRteWVrWHhLZmgwT1d0VGFoVjZoUFJueHhKelNjVDAvQVVHMjUwWGRaVndYYnZmMmcwZEhDMDVFampva1hDWXI3NTFtNWpIbThrcVdBeXVid21mQ2RNNU14YWg3eWU4VWtKcDcvbElvNERBMW5sVW1nbXllNkxNbWNsdjF6SjE0a3NLRUFZK2NiWlY2V0lKb2FqSGNyNXczZkZCeHczbDJiSlZjM09LM0hudXVWdU41YStmd3k0T0siLCJtYWMiOiJmOTRkOTgwN2U0ZTI5NTY0NDRhMWJiYmQyNTg1MWJkYTI2ZTNmNTkwZTliYTk0Y2VmMmVhZDA2ZWQ1ZTc1YzBiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:44:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFnbGkweURoUVNubGlDaTFkenZxOWc9PSIsInZhbHVlIjoiK0ZDYzJjdlRkRlZMblU3VkV1T2k0cFY1MElVQjdHVmhFd3RnVVpuUmt6aGRlbmlMdTBCZEs4NWRLRVEwN0FROTRySDFzOXA1cllhRUZveEM2eXZFd29FSTN3V2ZRdlFZUUZXN3VOa25tNGFpbFpBVHdMVTZ4a1AvR29ENkpZMy9NaHdvZzNQVXczdlgxTnhKZlJuQmlUQ1pSTTdGNmxrLzh3TDR4eWh3VFBrVVJpYWMwYXRTbEhXM25vem1kNmNZbWlYQ3pGUmNtRDk4WUlDZ2RkWFFiY1QvVUZyZDlKUzN6a2cxYTNmM1ZRaUVrc1VqNUY2azlMQkx0SUl5NlUyUjB2dkNidHBKTWJQdE9zT2tnd0dFMXN1ZnJwWjJEaUI2elZqZFFrQVRGWDNKekVqTkl6bkxaT3pYdG1CMVBSU1JBZzgwL05rMjBqMzErN29Da0VDb28vV1RTazZCeWdEcTlxZVRYVXNMZE9nanJRR2kyUTBZU2JkQTBDVkFEdC8vbEdKQ0lKZVBCTFJvR2g4WWlTUTY1ZmpWZ1dabmRPMXZIcHZ2R1JwZ1J1QnJPY2NlMlhwY1k2MWdTWXNYQVkrZXhhTnlPRnNid1lYTmpGV3JmbUsveG5yYjV3ejZpYXdyMXlPaitucktIbnIwNlR1M0RJc1cwRWVGUGRwamU2NkIiLCJtYWMiOiJhM2E0NTVmYTVkMGM4YWJmM2NiZTk5OWI3OTRkZjdiZTVhZDA0ZTliNThiNDc1ZTQ1MjY1MDcwODQwZWI3NDk1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:44:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBrTmNTU1VVM3lFbTJQSXdTNHF5MWc9PSIsInZhbHVlIjoiUC8wMEEvdjJpK0VlenVHVnRkZXBpN0QyaW8yalJmcDlMOTh0K1g1NEUrZkVBME9wakgxbWFya1BhU2krQXVTZzVhZjRmYUtqVCtOSEt4U3NyUm11SXdIc0NHbm5TTzdOUFNZMUVXNlJ3K3dWa01hdXFBR1FTVlViS2lOU1p0Ykt2MzJ1Sm1hWjkxNGNxR3ljK2ZhY290UnBUQ1NDYnZ6V1Q2WHJWdjBaMVhKN3dWYXFORG5lOUtMaUR5VUpMK0hhd3hzOU1FNFd3Zld3Z1dpcmxOZ1p0b3ZzZVpacjJVbGhteDk2aUVMenJ0MGZRWkxHTlZPM1VUQTJBSFJ2RHFqNkNsTHZSNTBrOFJYWjJnTE8zQytmZ2dWbkhRcTdhY1VUUGxpVTVKblNnNzF6ZzBYT3doTXVFSFhxWnlvcTMwSGN6Zjh3b0lzMXRteWVrWHhLZmgwT1d0VGFoVjZoUFJueHhKelNjVDAvQVVHMjUwWGRaVndYYnZmMmcwZEhDMDVFampva1hDWXI3NTFtNWpIbThrcVdBeXVid21mQ2RNNU14YWg3eWU4VWtKcDcvbElvNERBMW5sVW1nbXllNkxNbWNsdjF6SjE0a3NLRUFZK2NiWlY2V0lKb2FqSGNyNXczZkZCeHczbDJiSlZjM09LM0hudXVWdU41YStmd3k0T0siLCJtYWMiOiJmOTRkOTgwN2U0ZTI5NTY0NDRhMWJiYmQyNTg1MWJkYTI2ZTNmNTkwZTliYTk0Y2VmMmVhZDA2ZWQ1ZTc1YzBiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:44:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFnbGkweURoUVNubGlDaTFkenZxOWc9PSIsInZhbHVlIjoiK0ZDYzJjdlRkRlZMblU3VkV1T2k0cFY1MElVQjdHVmhFd3RnVVpuUmt6aGRlbmlMdTBCZEs4NWRLRVEwN0FROTRySDFzOXA1cllhRUZveEM2eXZFd29FSTN3V2ZRdlFZUUZXN3VOa25tNGFpbFpBVHdMVTZ4a1AvR29ENkpZMy9NaHdvZzNQVXczdlgxTnhKZlJuQmlUQ1pSTTdGNmxrLzh3TDR4eWh3VFBrVVJpYWMwYXRTbEhXM25vem1kNmNZbWlYQ3pGUmNtRDk4WUlDZ2RkWFFiY1QvVUZyZDlKUzN6a2cxYTNmM1ZRaUVrc1VqNUY2azlMQkx0SUl5NlUyUjB2dkNidHBKTWJQdE9zT2tnd0dFMXN1ZnJwWjJEaUI2elZqZFFrQVRGWDNKekVqTkl6bkxaT3pYdG1CMVBSU1JBZzgwL05rMjBqMzErN29Da0VDb28vV1RTazZCeWdEcTlxZVRYVXNMZE9nanJRR2kyUTBZU2JkQTBDVkFEdC8vbEdKQ0lKZVBCTFJvR2g4WWlTUTY1ZmpWZ1dabmRPMXZIcHZ2R1JwZ1J1QnJPY2NlMlhwY1k2MWdTWXNYQVkrZXhhTnlPRnNid1lYTmpGV3JmbUsveG5yYjV3ejZpYXdyMXlPaitucktIbnIwNlR1M0RJc1cwRWVGUGRwamU2NkIiLCJtYWMiOiJhM2E0NTVmYTVkMGM4YWJmM2NiZTk5OWI3OTRkZjdiZTVhZDA0ZTliNThiNDc1ZTQ1MjY1MDcwODQwZWI3NDk1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:44:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063218333\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1073794829 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073794829\", {\"maxDepth\":0})</script>\n"}}