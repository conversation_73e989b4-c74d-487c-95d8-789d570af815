{"__meta": {"id": "X031ac64837011ceea29a74277103dc51", "datetime": "2025-06-28 15:09:32", "utime": **********.653365, "method": "POST", "uri": "/enhanced-pos/search-products", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.237908, "end": **********.653379, "duration": 0.41547107696533203, "duration_str": "415ms", "measures": [{"label": "Booting", "start": **********.237908, "relative_start": 0, "end": **********.587381, "relative_end": **********.587381, "duration": 0.3494729995727539, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.587389, "relative_start": 0.34948110580444336, "end": **********.65338, "relative_end": 9.5367431640625e-07, "duration": 0.06599092483520508, "duration_str": "65.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45692416, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.search", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1910\" onclick=\"\">app/Http/Controllers/PosController.php:1910-1968</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00675, "accumulated_duration_str": "6.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6232681, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.222}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6330361, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.222, "width_percent": 6.667}, {"sql": "select * from `product_services` where `created_by` = 15 and (`name` LIKE '%*************%' or `sku` LIKE '%*************%') limit 10", "type": "query", "params": [], "bindings": ["15", "%*************%", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.636289, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 28.889, "width_percent": 37.037}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`product_id` in (2299) and `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1940}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6420681, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1940", "source": "app/Http/Controllers/PosController.php:1940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1940", "ajax": false, "filename": "PosController.php", "line": "1940"}, "connection": "kdmkjkqknb", "start_percent": 65.926, "width_percent": 30.074}, {"sql": "select * from `taxes` where `taxes`.`id` = '' and `taxes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1953}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6463602, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1953", "source": "app/Http/Controllers/PosController.php:1953", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1953", "ajax": false, "filename": "PosController.php", "line": "1953"}, "connection": "kdmkjkqknb", "start_percent": 96, "width_percent": 4}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/1457/thermal/print\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1713682549 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1713682549\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1844902926 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1844902926\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-44840016 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-44840016\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-853618965 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=k5cf8r%7C1751123332501%7C9%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImF3TCtVTDVXcGlSOUhNT2twNlFyVXc9PSIsInZhbHVlIjoidmIvRnErbjNIU1l1TjVxckZEZ0VjMk9SVDhwWDdYdzdkN3VrNFg2Rk5ScWZ1dTlqZE5HSjFNcHdoVktJcVh4dGNvQUdDcDRKVHdwa0dLNDVrZHNhKy9KUUNyMnFpdVl3dmh0T2hLeUl4V3UvK2lSczdLSmpuRk1sVEUrK2xwQU1ScnZMWE4xWXFQckpxOFRxU3ZVRE1ySGJrbDI0SFpUNnhBM1QwS1djR1lnQ1Z5ckQzOUF6WmJ5emNmdFJoYlNEdCtsc3BoNVBndXNFZG56czI5Q05IMnZyZEFJL21RVHlTWk83Y1NTa1FEcHVtVlh6YzBHSGRlNkl5eERmaStXVmoyT0hJc2t1NnNnbkl4ZVNWL1REUDgyZEhrNUlaOVJaaXp2d25UYjd4U0FEU0FvNXlDYmJjM3AzNTFOdUlLY3ZXRk82VGU0OVpUclRmRW52V3Nwb2NtanFCaGNrL0NZcTFVdkp6Zms4VUZVYUQ0akV5RXFOcU1BRG9GajFNWVFSanJrVU1jekJHeWh1S2Vmam9BZnd2T1VDM0E4R2syYWlmN2ZycVNCUnl0Mmw4SnY2NHNXZjJISlk5YzBmT1N6Z1YvZ2V4bVFOQXdwZzFra1ZBdXZEeHd3SHJwQ2U3aHJjZ2Znd2FKOWFaWmZHSXkvSDNpM0JrUDlHRjdTTi9yOWsiLCJtYWMiOiIyY2Q5Njg3Y2M3MTFlODYzNmY4MDM1ODlmOGFhMDBiZTIxNzcyZTZiY2VkOWJjMGM3ZjFiMWIyYzc2YzNlNTg2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Inpyb3JuWEUyVGdYc2hBMkdZUW9qaUE9PSIsInZhbHVlIjoiTXV4R1ZKWGYzUGdsK0kxWkUwS09MdEloWk9uckNVUExvQy9rN2FZMWg0REgrQk9aVEpZRFc1Y2F0bjQ5QUNLM1k0cHpvTkI0S0VRM215UjQ0NCtGekpMb0s2a0JYTXNaTk1uVHcvMWpRUEthUkpZT0xLdlFKMG5GZDZrRFZLNk5PMzJ2bDcyVkxWRndTQmR3SGlYT0lqMlVWcmw0Uit1RmlsOFZxU1MvSUdkcFU3RFdRTEdpams3ZkN1YmJiSUNRL2REcDdSVlA1N21OQUhpVWhURSt3U3M2NXNNakI2dnZMeG12OEZLR1ZiYlNDcWcySVJSb3NSRzFJTXk5cEYyRy90L1VXZzNMSUxPbWE3dHJQU0ZtL0FZNkhNaEVHMDZSZysvTTZJaHYwbTlmdGdRZE5TSnJxMW1ZNkpCQzFzbkN5MTVLWTE5MkRISDEyLzRRY0lJSTlxRmZxOGZIOVRlaUxXQzY5WFhDNFI1ay83bC9qd1ZHUytWOTNQMnUrYThIZEViL21CQzYrYzltSzFWZHd5U0RJSDVxNGZkcU1WeUF2M1UxNDVMcUNvK2loVExZWWlDZEprTjJWbGErZ1JqNTZvNHNNbGswWGkxYW4rNFk4V2hxLytVT1ArWDd3UE9xeWczdDVLRG5oOUNrdENBOUdRMm1IMCtxblB3TFdnYUgiLCJtYWMiOiJmNDk1ZDY0YzQwNjk2YmMyNjQzYWJmOGM3NGFkMDVhNWE1YzUzYzU0OWY4ZTNkNDk2OTAwYjg2MjE4NzA0MTY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853618965\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1042811109 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1042811109\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1024340892 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:09:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imx2U05IZGVLU2xIWXphR1EzdDA0Q1E9PSIsInZhbHVlIjoidEF2c3pSTHRObnFrQVUvaWthQ29jL0cxMlNUaWlOOXpYVG5YN1RHRFNoeTRobUF2c011SjFCcTIzaDZDTVJQck1Hb3VqdktOR0YyS1hUNWcwZm1zMExDSzRwcEkvcDlJOHFZRGtkZk1qdkZDbGFQa2ZJbVd1WWZna3RQTmRIYTFvczIwOWxWZ2w4M1VnQXQ3b0NQWkZtZStGVUl6eVIxQTYxTERYdFNxQkVlNzQwZ09CbU16dXVmaUVGMnlBOWM3Q1lFT3pHTGZkeGlWcGdZd1FZMndHZ3JyUSs2S0xsUnJvdnBkeW03dGV5YnpEUFNwdlY0dG16ckZLQVVVeWtKN3lEVTZuWjczNlNCT0tQaFhaWWNrdlNaRnk2dWFZQTZNU2lFZ092VGtIZWpUY0VzMjhhOUhUNlFBUGpKbGNqTS9rMHMvYVkrNHcxV0FXMkZrcFQ1R0ZvYmRZVzg2K1BuM3d4Nm5GWjV4dGp0Z1BGYW9LVnJuS25BRnpmV0xKQnpnNHhiQ08vVEg4cStnYnFXUzNUdGtmc2xXUjlxdGZJL296elVqMkpTSTF2SVlraWhUc0VaalJDcDE3L0VMcHIwczMrbTMyTDBnWGQ2d2VuWldPMmdyOVdZNFRxaGpkNjdCckpBcjAzZHBmNWdMT3kwZmVvb3FYdVRtOWNHeWVheVciLCJtYWMiOiJhMTM0ZjE5MTU0YTQ0YzQzNGU1NWU5MGY4OWMyN2I5N2UyYTE4ZTgwNzZkNmNjMmRkZjExMzg0ZTA2MzlmMWRhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:09:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjgxOXE5ejg2Q1ZTTkVxcFZ0ei9USUE9PSIsInZhbHVlIjoiTVVWenl2SG9HWWI4d3lqOW5vTjlLQ1dWcmliWGVkMGhQODBRSDZNUXY5NGpnTnFvU3R2MG1kZHlrRnJIQkZOTDZYcHVwY3VwckNiK0J1QmhEWFh1Sm1xdGVhNS9CdFJOUzlQRXlGd05tZ1UrNUVyYm0zeG1BYTlVS1dCOGsvSEduVlIyOTJFOC9iakI5QTlLWUlxalk1T1FWaVFGZDhWRllBSDl6bDJDYUJ6b1YzOTNkUTVla3lQL0NXZEhQMEhSdkd6MkRDdVcxeHJvTjlReUhyU01LeFcvRXRHTHVSN0N6Mk5Hb1dzVFJ2WnR4K2hhYmV2TzdQQ1k2R01pNDRyei9RVU9SUUl3TXpkRTBFVEdkVFVQN0xIM2lVT3dsN3J6UmV4TXg0UFVNSFZMY1p6VndxeVgxbHZTZ3Erc2RXT1RtNXVOOTBrTXpTQW9YL1VvZjRlU2dOZlBMN2xldmVBcjlCaVBBUnpFbEo5bEVCN0xGREcrcEI1YjZ6K2pMTksxNnZDZFhlR0p0MVdLNHdyLzlVa2dsd1E2djZsYkpOeU04NFhOVEFTODZJMTM3NlRBNE02Mm93Ykc4dTdFM3JlQWhScFEweUtDczVaa2tXWVNrOGNRYXZJM05FMWNjQmNLL0hpWnFzUXZtWnFjdDlnSkJaT2VBYSs2UTlLSGovY1giLCJtYWMiOiI1YTBlMzc0Mzc3YjRlMGQ2Y2IyNmIwY2EyMDdmNzY0MTE5YjBjOTBhZWUxOTIxZTc2ZDg3M2E4ZjIxNTAzYWE3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:09:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imx2U05IZGVLU2xIWXphR1EzdDA0Q1E9PSIsInZhbHVlIjoidEF2c3pSTHRObnFrQVUvaWthQ29jL0cxMlNUaWlOOXpYVG5YN1RHRFNoeTRobUF2c011SjFCcTIzaDZDTVJQck1Hb3VqdktOR0YyS1hUNWcwZm1zMExDSzRwcEkvcDlJOHFZRGtkZk1qdkZDbGFQa2ZJbVd1WWZna3RQTmRIYTFvczIwOWxWZ2w4M1VnQXQ3b0NQWkZtZStGVUl6eVIxQTYxTERYdFNxQkVlNzQwZ09CbU16dXVmaUVGMnlBOWM3Q1lFT3pHTGZkeGlWcGdZd1FZMndHZ3JyUSs2S0xsUnJvdnBkeW03dGV5YnpEUFNwdlY0dG16ckZLQVVVeWtKN3lEVTZuWjczNlNCT0tQaFhaWWNrdlNaRnk2dWFZQTZNU2lFZ092VGtIZWpUY0VzMjhhOUhUNlFBUGpKbGNqTS9rMHMvYVkrNHcxV0FXMkZrcFQ1R0ZvYmRZVzg2K1BuM3d4Nm5GWjV4dGp0Z1BGYW9LVnJuS25BRnpmV0xKQnpnNHhiQ08vVEg4cStnYnFXUzNUdGtmc2xXUjlxdGZJL296elVqMkpTSTF2SVlraWhUc0VaalJDcDE3L0VMcHIwczMrbTMyTDBnWGQ2d2VuWldPMmdyOVdZNFRxaGpkNjdCckpBcjAzZHBmNWdMT3kwZmVvb3FYdVRtOWNHeWVheVciLCJtYWMiOiJhMTM0ZjE5MTU0YTQ0YzQzNGU1NWU5MGY4OWMyN2I5N2UyYTE4ZTgwNzZkNmNjMmRkZjExMzg0ZTA2MzlmMWRhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:09:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjgxOXE5ejg2Q1ZTTkVxcFZ0ei9USUE9PSIsInZhbHVlIjoiTVVWenl2SG9HWWI4d3lqOW5vTjlLQ1dWcmliWGVkMGhQODBRSDZNUXY5NGpnTnFvU3R2MG1kZHlrRnJIQkZOTDZYcHVwY3VwckNiK0J1QmhEWFh1Sm1xdGVhNS9CdFJOUzlQRXlGd05tZ1UrNUVyYm0zeG1BYTlVS1dCOGsvSEduVlIyOTJFOC9iakI5QTlLWUlxalk1T1FWaVFGZDhWRllBSDl6bDJDYUJ6b1YzOTNkUTVla3lQL0NXZEhQMEhSdkd6MkRDdVcxeHJvTjlReUhyU01LeFcvRXRHTHVSN0N6Mk5Hb1dzVFJ2WnR4K2hhYmV2TzdQQ1k2R01pNDRyei9RVU9SUUl3TXpkRTBFVEdkVFVQN0xIM2lVT3dsN3J6UmV4TXg0UFVNSFZMY1p6VndxeVgxbHZTZ3Erc2RXT1RtNXVOOTBrTXpTQW9YL1VvZjRlU2dOZlBMN2xldmVBcjlCaVBBUnpFbEo5bEVCN0xGREcrcEI1YjZ6K2pMTksxNnZDZFhlR0p0MVdLNHdyLzlVa2dsd1E2djZsYkpOeU04NFhOVEFTODZJMTM3NlRBNE02Mm93Ykc4dTdFM3JlQWhScFEweUtDczVaa2tXWVNrOGNRYXZJM05FMWNjQmNLL0hpWnFzUXZtWnFjdDlnSkJaT2VBYSs2UTlLSGovY1giLCJtYWMiOiI1YTBlMzc0Mzc3YjRlMGQ2Y2IyNmIwY2EyMDdmNzY0MTE5YjBjOTBhZWUxOTIxZTc2ZDg3M2E4ZjIxNTAzYWE3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:09:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024340892\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1151286614 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/pos/1457/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151286614\", {\"maxDepth\":0})</script>\n"}}