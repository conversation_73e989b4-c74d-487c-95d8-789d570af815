{"__meta": {"id": "X2d720c65ae39d72679c59d8349eb038f", "datetime": "2025-06-28 16:19:03", "utime": **********.920146, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.49738, "end": **********.920159, "duration": 0.4227790832519531, "duration_str": "423ms", "measures": [{"label": "Booting", "start": **********.49738, "relative_start": 0, "end": **********.867186, "relative_end": **********.867186, "duration": 0.36980605125427246, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.867194, "relative_start": 0.3698139190673828, "end": **********.920161, "relative_end": 1.9073486328125e-06, "duration": 0.052967071533203125, "duration_str": "52.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45699424, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00272, "accumulated_duration_str": "2.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8950899, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.809}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9049702, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.809, "width_percent": 17.279}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9112022, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.088, "width_percent": 16.912}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-498724063 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-498724063\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-501215270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-501215270\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2108051666 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108051666\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1376323603 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751127449834%7C33%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFLalFERTcvMDg4dGRsa1BrSjFUbXc9PSIsInZhbHVlIjoiVWJaSStrWVN0bEt0djluUHJ0WkNVNHZtSlM3NVlEbHhxSUM3cUxVZGd0TkoyZnFwc0dpU2xvdS9ZaFRDa29aSEJvMzBJRVJQN3o3aHpTa2p4V0djOWtDVUpVaTlDODk4aU0zS1VidHR2VlBtOHZocFhYY1FIdlBCWXozeW9XMUdqMHpaZCszeFFpZGhQaVdoRDBLNmw4WkpxcWdkVmZ3WkRYeWJlK1cvSUM2Nzc2V1RnYnZuTjArMkFRc2VOV3hVL1Y0N0FEcTBNV1dhcGY5ZWhYd2lMOHV6d1NBOXQ2OHhHLys5dzc5N3dtSzZST0JtSW5DTVFiWWRVM2llWHVnbzJXTWVYdktkZjVHNXNNVW9HSGdFOUI3UVRQMGdqRVdidkE0SUZqeHc2RCtBL2Y5QzFsRytxUlVBNXkxSysyYWNIelRVbFBuNXpIRFd0UkxZaXhNVGR2TWh0Yzl2d0RHeHJqaEE3Z3hlM21uTUxZWEFPSHd5cVpnSmgxc25QUE1WVWdQYzFDNEl3NTNRZE9ERHVVWnhCZVhRd05qQlJXaG5hUEI0N1JSRWtQM3E5SjFGL0J6TGkyaExrWHdEMmQyZjdpLzlGcXdzZDlrNUQvZXpkRmt4WFMrMnZnd2ZBSjc1ZC94U21mLzJRTVF2NWJubFEwWGRIWjJTeGU3T2NKaS8iLCJtYWMiOiI5MDY4NWU5MjBiYTc2NmI3ZDk2NWI5YjA5NjY2MTIzM2Y5YjQ2M2Q0YjBiZGY2MWQwOTRkYWMzZDYxZTE4Y2M1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVvd21DbVA3YnhnOHc3U0s1NHo4a2c9PSIsInZhbHVlIjoiK0xpVy9ndUh3T3lRR05BUFNrbGUxdFAyMTRFZWNRU0pxTUpQejNOdDI2WUhHWFNjV3IrTTJzV2VlRG51OVVaYXZpSGFiTXBmT3E4Y0ZMV0FXNnI5Ti9iWnhlTkZ1MFpZS052dFBMQktZNU9TZGs1Q0puY01seDJSY0dkYWNOdFkyZDRmR3ppbmFNaUlacWRUQzBVUXUyY3hKZmhWNnd6TDI3Q2xPS2pRWEt6bWRQdTJLZmprKytLdjU1d215dXpWVVBnTUF0TVE0S3hiSXJsWFNNY21zVFpjU092cjlrVlUwWVUreFFTMEVwL0QxUllPS0pzM0RXUVFRK0oycSt3bm9qYTQrQnc0VHBCYUtnK1o1N0RBQ2xSNy8za2dnczRmK3NRRTM4MUdqSjAvME1NUmFTbXJyeEpEbmRTNG4vWDVYMGRXNzF4STAraWtQUC9TcXl4Y1ZISUxTSk40eGkrRUtaTUxkclNreFRyYXpWbmJMSmU1KzYvaUFOOUppZTgzVS9JbWRXejJqN25IVGxEenZVaFJlU2xnRU54cU9VT29UQzJrdzBqMWcwNTBCbndOTXJsYUpiRXhKdU9UbjZCSUFkcHlEYlMyOTBqU1NTcldnelQ0K0w5M2F6Vm40dU1LZ0M4OVdPQWNPa0xtUXZWS1pXSzBkYTg4UHhkUHlOKzgiLCJtYWMiOiI1YjBjMmQ5OTljNjI2MTc4NGU4MTJkZTVmYjcxN2JjMzE0YzE2Yzc1NzczNDY3NzY5YTJlZmRkMDg2YmU2OTMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376323603\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-575623799 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:19:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlY3UzFqQzlncmY0V25kT1lKaWNwaVE9PSIsInZhbHVlIjoiVjhlMkNNZ1p0cXV5dFd0andiMDUzNVdrK21rWE5mMktsdzRnN3JkV3NHTW5tdzNYeUptYTF0a29vRGx1L3RaTHBmcGxVczNvTGp4VStXMmYyK3FoSUJCS09UYzdBQm1DZ3kwTmY2SE5tQm9DK0s3NkZoU3JHUFhmdTFFNlFkVlJYUERReUZQb1gyRDRSdWRwMlRoajdNSGtPMXkzVy9hcUdBcTM4cmV4cTZyTmFuZ0dCYnBOZWltYmVpSFQwdjQvK212Nm56dkFLYWM5QXlKYVBnOEh2SnNqVi9lQTBXQ3RDT01nNU5ObDlTOUdYUC9FVHVOQWFySERua1RRSFROK2RrN0lNWkhIUzNrMFp0NHliMUgwRW1LUlBxMmFZVFkvYTd1VVdyL3U4MkJYSiszYVVSVkV4Z3JDWndMekdNTkJkRmlVaEN0b0lLZFdDQ1hGdXVBb1JJbXcwS045Z3RzQ1J4N3FnaVN6K3JWZEVHa21oc3hxbzM2M0xuYkZGWXg3MjIwdGNHL1FYTGZOQThMMXgxckxKcGpxdnlBQkNGZHpJNko5SHQ4NzJkV2RkQmVzTzZxTFFYdXZmUjg1QXNpSzRVSnllV2FxL2Qyd0pkUHAyWDV3VUpob21JSmZ4Sk5SNGMwLzk0NGJNVnBCam1VZGxJUTVmNVdXaTgrcVpNaTciLCJtYWMiOiJjN2NhMjRmOTllNzcxM2Q5YzE4ZDUzYmYyNWY2ODMwNDAzNzYxMmQ2ZTFjNjM5NzhkZmI3NTc5ZjFhNjU2NmVkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImttVXdmUjg4TElVWkVJMm9zYmtNZHc9PSIsInZhbHVlIjoiT056OGxKSm5lbi90blpKTjhGcW4vKzlhYjc0emtqb051Z0pLYWpGV1lQMU84Q0pDQ1JSU1h2QWpKUjdtYUFWVitrSy9OL1hPYnRYQUVvZjl0Wi82OVhUeHJjRk5pY0ZFUXVCK0FmeXE4WWE1MEZRdFkwWWJEak5MUFJRS2piRW5ObHdFSmlqZ0g4QklCa2Q1cXY1TXpzT2hRKzM5QlFqNVJhNUYrbGNPZ0dJVnBWcjBKQm9WbWpNOE5ROTdLNDVlNVpTdHJjOHh6Ym5oSkxxQ3dLT0ZCUDVlWWZZaGpIUTV0UUEvUm02T1hiQjk1OVBIQ1FlOEFDWGswM0RBeTJDYVF0OUR4ejJoeEllT0dCQUJoSG1EbmpyYk1RVWZBM0lDMjBMUXRCeno0QUgvdHFYeFpvTG40aWI2U0pmcWlaTGdJMmJjNkU0TnNvWlJvNVZqWWJqNkRvTitNY0RMczNTamc1OHVzMlpSKzk1b29hZzIxTnNFWGZNNThhOWtQME4wcFphbUJMSjBGMHRxeVJSOGJrU3c4bE9ucGI1Z3RydStCRTl5dHlUU1lJUkNDYy9CNUwvWnhvUHVTamNtanFGdWZhTXhHTDNVS3RhVUdmeFcrazFBL2VRZXVscWJDa25ySXlaREtsSCtqWUZ5c0Z0RlpXYndjdnBybWZvTlRoM2YiLCJtYWMiOiJhYjQ5NWE4ZDU0MjRlNjU2MTY5ZWE0Zjg5YzM2ODZkMmUzYmI1Y2IxNDA1MTI4MzFhNGY1Mjc1NjZlZjQwYmY2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:19:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlY3UzFqQzlncmY0V25kT1lKaWNwaVE9PSIsInZhbHVlIjoiVjhlMkNNZ1p0cXV5dFd0andiMDUzNVdrK21rWE5mMktsdzRnN3JkV3NHTW5tdzNYeUptYTF0a29vRGx1L3RaTHBmcGxVczNvTGp4VStXMmYyK3FoSUJCS09UYzdBQm1DZ3kwTmY2SE5tQm9DK0s3NkZoU3JHUFhmdTFFNlFkVlJYUERReUZQb1gyRDRSdWRwMlRoajdNSGtPMXkzVy9hcUdBcTM4cmV4cTZyTmFuZ0dCYnBOZWltYmVpSFQwdjQvK212Nm56dkFLYWM5QXlKYVBnOEh2SnNqVi9lQTBXQ3RDT01nNU5ObDlTOUdYUC9FVHVOQWFySERua1RRSFROK2RrN0lNWkhIUzNrMFp0NHliMUgwRW1LUlBxMmFZVFkvYTd1VVdyL3U4MkJYSiszYVVSVkV4Z3JDWndMekdNTkJkRmlVaEN0b0lLZFdDQ1hGdXVBb1JJbXcwS045Z3RzQ1J4N3FnaVN6K3JWZEVHa21oc3hxbzM2M0xuYkZGWXg3MjIwdGNHL1FYTGZOQThMMXgxckxKcGpxdnlBQkNGZHpJNko5SHQ4NzJkV2RkQmVzTzZxTFFYdXZmUjg1QXNpSzRVSnllV2FxL2Qyd0pkUHAyWDV3VUpob21JSmZ4Sk5SNGMwLzk0NGJNVnBCam1VZGxJUTVmNVdXaTgrcVpNaTciLCJtYWMiOiJjN2NhMjRmOTllNzcxM2Q5YzE4ZDUzYmYyNWY2ODMwNDAzNzYxMmQ2ZTFjNjM5NzhkZmI3NTc5ZjFhNjU2NmVkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImttVXdmUjg4TElVWkVJMm9zYmtNZHc9PSIsInZhbHVlIjoiT056OGxKSm5lbi90blpKTjhGcW4vKzlhYjc0emtqb051Z0pLYWpGV1lQMU84Q0pDQ1JSU1h2QWpKUjdtYUFWVitrSy9OL1hPYnRYQUVvZjl0Wi82OVhUeHJjRk5pY0ZFUXVCK0FmeXE4WWE1MEZRdFkwWWJEak5MUFJRS2piRW5ObHdFSmlqZ0g4QklCa2Q1cXY1TXpzT2hRKzM5QlFqNVJhNUYrbGNPZ0dJVnBWcjBKQm9WbWpNOE5ROTdLNDVlNVpTdHJjOHh6Ym5oSkxxQ3dLT0ZCUDVlWWZZaGpIUTV0UUEvUm02T1hiQjk1OVBIQ1FlOEFDWGswM0RBeTJDYVF0OUR4ejJoeEllT0dCQUJoSG1EbmpyYk1RVWZBM0lDMjBMUXRCeno0QUgvdHFYeFpvTG40aWI2U0pmcWlaTGdJMmJjNkU0TnNvWlJvNVZqWWJqNkRvTitNY0RMczNTamc1OHVzMlpSKzk1b29hZzIxTnNFWGZNNThhOWtQME4wcFphbUJMSjBGMHRxeVJSOGJrU3c4bE9ucGI1Z3RydStCRTl5dHlUU1lJUkNDYy9CNUwvWnhvUHVTamNtanFGdWZhTXhHTDNVS3RhVUdmeFcrazFBL2VRZXVscWJDa25ySXlaREtsSCtqWUZ5c0Z0RlpXYndjdnBybWZvTlRoM2YiLCJtYWMiOiJhYjQ5NWE4ZDU0MjRlNjU2MTY5ZWE0Zjg5YzM2ODZkMmUzYmI1Y2IxNDA1MTI4MzFhNGY1Mjc1NjZlZjQwYmY2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:19:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575623799\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-704287817 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-704287817\", {\"maxDepth\":0})</script>\n"}}