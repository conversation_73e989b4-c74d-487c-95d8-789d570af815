{"__meta": {"id": "X23c9c9cfe4cc3bd5a1867fb13ee02cc6", "datetime": "2025-06-28 15:49:48", "utime": **********.560875, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.11332, "end": **********.560889, "duration": 0.4475688934326172, "duration_str": "448ms", "measures": [{"label": "Booting", "start": **********.11332, "relative_start": 0, "end": **********.506243, "relative_end": **********.506243, "duration": 0.39292287826538086, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.506252, "relative_start": 0.3929319381713867, "end": **********.56089, "relative_end": 9.5367431640625e-07, "duration": 0.054637908935546875, "duration_str": "54.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45180952, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00314, "accumulated_duration_str": "3.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5375829, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.153}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.547903, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.153, "width_percent": 11.783}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.553617, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.936, "width_percent": 20.064}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics?date_from=2025-06-01&date_to=2025-06-30&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1403773090 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1403773090\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-555262767 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-555262767\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1466452680 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466452680\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-807627913 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?warehouse_id=8&amp;date_from=2025-06-01&amp;date_to=2025-06-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125775754%7C27%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBUYUlzSGw4ZFA0WXFwQmhWdVRVUFE9PSIsInZhbHVlIjoienI3dmZ2WDJERHlVS2Njbkc1Wlp6azE3ai8rUk4wQStmRDF1L28zblo0SGFicEdud3MzYkRRa2FMY04xeDliMGVvTDFaQXNSYXRtUFQ4dTRyc2ZyZ2Q1L2VVRW9YZ2tiWFBBWWFBdEJ1RW1LYU4wc0pqWW42Wm1KaTN2cWxDSzJ0MGxHeitSQXNzZ29LeTZ6S0RsaGVYcVVLTDZlNDlEa202WFk3eUEzSHpyd2FjdmdhWmN3bzB0YXZFblFvYzFSRWhzS3ZRMHIrOXpZUEkvbVUzSHR4Vzh6cUcyb2lrUDBWZ2x2NE8xd3IxY0wySGRYMTV0N21tN3Jwd3E2bGhnWDZJM2NvZjhSeWtxc1haMVltbUFmc3VqZXBGTVUzYzJLaWpWQUlXUUYvNjhud0VGRDY5eW9zc1E5ZGFVZzBjdjV1RTJwTHRkcVZQMjlPL1BlRHJ2RFlpNlkyZlJlak9vOHBJT0pVb0xCTXphRVRxRmpFZnhKbE1ZQlcwdUp1d2I4SGc1d3RCNFRsNjBCb2pxUExzWE9DWUNQY1lCc2U4VVl3ZlBQaHFZcHBGb0NQS1BneG5RdWRCQ1ZTZXdaNWlLbFMrNzNrNmZVYjl6OW53NktJZDYwaVNKTGlNNGt6RUlVMWhNWWd1ODVjUkU0NENGTFNhWXdZS2tPWTR5K21BQkciLCJtYWMiOiI5MTQ4MDAwYzcwYjNjNGM3ZmM4NzY3YTU1YWYxMWIwZjg1MTdkMDQwNDE5NDdhZjJkZDIwNzAxMWQxMDU4NGQzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjAyVWpmTVVEQ0ZwWjFsYlFjejd3SHc9PSIsInZhbHVlIjoidDVjKzhVbmZqWU1WdHZNc01tNmlkVFRRckxJZ1I2dTJiajhBTmVxOEF5QnVONGZjY0phdFFESzJzMGtqQzNKMC9Memg5enlwSE8vME02dFE3aVUxVzJoM1FkQWhpb0t5MWIrM0xrNEo0MGNXeEExMTZhV2Z5bEV0eUgwMERRNzlXRVVOV25LelMrNUd5NC9TQjRTRFBXTTk5eFprSXZKUVNQRDB4L2dMcHZsOFIxVVEvQlExQ2VlOXlEdnlhaDZMbnpyQ0NPUVFaNERvUDlvaWNzNHhGWG0xditaM3lOK3VkYk5VNWRqeXl4ZTJhZ09iblFkNWlkZ1BQNUN2MUVNc0pqcVBIdVBQK3RJcFpEMDZNdGREelU4MjhOaUZrN2xWUktWeE9aays0cGp6ayt1amplQ2VMWTQ1My9JZENnelFJRHF6UUlpNUhVbEtrRmtGdWJrQ3poMFQrcGt3b0RkZlBwUDdkV0ZRcHdmNFdqYkFmV3liQzRRVG1mUklKQkhHRllpOXY1L1BsaERKNGxyOW95QnNDSWtKVmtEQ2szYzlWd3k1RUpwVytKRENMN2wzVkczMUluWW1iekt1TkFwQ09tZ3ZWM2tZTGNGcU96VXRPbGFZemt0RWwwZWNTZm9ScUYydUV4MWIvYVZGT3dwb1d4Yklkd01iUmVQNldYVEYiLCJtYWMiOiIwZGJiOWZjYmQ2YjZhMjc0NDRkZjdlNWQ2M2NhZDFlZjdlZDM5MWZlYzc3ODgwOTE2NWY4YjhlMjIwM2Q0ZmZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-807627913\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1020181575 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020181575\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2120781049 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:49:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkliMDhxS3pRb2JxcTRYTlR2THExRHc9PSIsInZhbHVlIjoiOW5Kd1QxS3RYT2xFaXJoOUR1WGpZOXlxWlRCV09QRE9pRXR6NVZ2am1pakhOcFA0VS9tMDAzNlcwMWZoOXZmR1R5VGJvLzkybGhoVmhEQnRGeDJDOWNRc3JpWkZxbVRUcmpmNjNyV0F4eUxrNG9kek9ET1MwQyt1dmZtUitSQkptUGllTlNyRndqMk9LdE0wMTlqaEdiM09rYkdUR3VlSHAxcXdIdHl2Y3lRdDI4ODZIQ1hGRmx4Rjg1VnVNYVYwZzZ5a1pXNGJkQ0dxWm9veDdvMWpERysrSG9mNDFTdGdHQ0xUTElUV3dlaWpWS3ZCb2cybk9vWVhieVNwbThSYW5odE4rZVFvOERtc2RRaDdSMXNUVm9JV0ZzMDhma01JcUlXQ3ltQUd2SjQ1RXVlaHVjWlFPLzF4MFlaWXR2TUdIaTA0K3hoT2JZbXpKM1R2ZFlJZjl3a3ZLdWJZZXdoQ3krT0p2enhPZEovay83Y3N0NUQyaWZma3BHdkl0MWNLMmk0WS9VS0lUdU5WUHdnYmlFWEgwc1lSd3h0N25JN3hjc2V3N3lscll6dVJhZkw1WFdaTFpHV3VDN1VGRkd6Y1A0MkNlbkM4MkdaN0hXKzFKQXJiYng5RUN1NWttVU9DTkRWc0dxays5d2JtZlVYNTFkVDd5aVJXTUQwU2tyS2oiLCJtYWMiOiIxZmUzMTY5NWQ1ZWU3OGY3MzZiYmRmMzg5MDNlZjI1OGVmMzkzMmExZjBlMzhkM2ViZTU4OTI1NDM3ODQ5Y2MyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktnVXNmTnRMSVBQaWNFZnBNYjJnbWc9PSIsInZhbHVlIjoiSStMT04veTcxKzBldVZ2UkgzYjhHZ0t4NGErK0NDcExJUlE2UEQ5bTFOTS85SnkrcVYwcjlrcEdxYjZ1b2tqemRNYlZjK3MwVGdxeWZkVDVlQXdjQVZyMVNkblZYYjJHZlJNNjJaWi9WTjdaZkpIRHpHMUROVmJ4bzVaVGd4QlRXbVBBMnpWaVVaTGIyK2pEWThQRWlwNjFuS3JGRnJGYlJqUWloWTUxTGNPSUZiTTl0NnRwd2RkZmNmZlY0b3ZaejZOeGVmQ3Y2MG91bkNtM3Ewa3BPRlpXWVZBVys2ZHNzMVM5T2x0MDdyVzkwVnlTcVFNMkw5QmtOSXJ2NG5wYVFVdXI3ME41bUNGa1lUaldSVFM0cVo4UjEwUjRnekZYRU5tblBtUW0rblplcUZ1bllkVy9Ya3VXWC93WEhCc01Ba24ydnBmU3hKR1BlRXhZNHNsNzkrdDk3L1VQY2xuWGRNRGp0N0tia2hjbGJpRXROdlk3cGl6WjM3TEFqU3lHRTFSVnowMlRZOEMwVFhsc2pKTUI4Y1UwdW1ZQUxEZUFWOEFnRHJWb0M5SE1xRW5pUiswMlRobkNaN0Z1bzZycWMwTEZ3b0FXRkQwbXNQcXpOUXVuMUh5cG1mcGN5WTlUS3hSeDE5NUF0bzgvN1BoRWZaQk5LL2xwY2JheUU5OUYiLCJtYWMiOiJhNWNhN2Q1ZmQ4ZGMxNjQ1NGRjNjhiMDBkNzAwNjdmY2I1YzEyY2E1MWVmNGU1ZTA5YzY5MjUxMGJhYzdlNTc5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:49:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkliMDhxS3pRb2JxcTRYTlR2THExRHc9PSIsInZhbHVlIjoiOW5Kd1QxS3RYT2xFaXJoOUR1WGpZOXlxWlRCV09QRE9pRXR6NVZ2am1pakhOcFA0VS9tMDAzNlcwMWZoOXZmR1R5VGJvLzkybGhoVmhEQnRGeDJDOWNRc3JpWkZxbVRUcmpmNjNyV0F4eUxrNG9kek9ET1MwQyt1dmZtUitSQkptUGllTlNyRndqMk9LdE0wMTlqaEdiM09rYkdUR3VlSHAxcXdIdHl2Y3lRdDI4ODZIQ1hGRmx4Rjg1VnVNYVYwZzZ5a1pXNGJkQ0dxWm9veDdvMWpERysrSG9mNDFTdGdHQ0xUTElUV3dlaWpWS3ZCb2cybk9vWVhieVNwbThSYW5odE4rZVFvOERtc2RRaDdSMXNUVm9JV0ZzMDhma01JcUlXQ3ltQUd2SjQ1RXVlaHVjWlFPLzF4MFlaWXR2TUdIaTA0K3hoT2JZbXpKM1R2ZFlJZjl3a3ZLdWJZZXdoQ3krT0p2enhPZEovay83Y3N0NUQyaWZma3BHdkl0MWNLMmk0WS9VS0lUdU5WUHdnYmlFWEgwc1lSd3h0N25JN3hjc2V3N3lscll6dVJhZkw1WFdaTFpHV3VDN1VGRkd6Y1A0MkNlbkM4MkdaN0hXKzFKQXJiYng5RUN1NWttVU9DTkRWc0dxays5d2JtZlVYNTFkVDd5aVJXTUQwU2tyS2oiLCJtYWMiOiIxZmUzMTY5NWQ1ZWU3OGY3MzZiYmRmMzg5MDNlZjI1OGVmMzkzMmExZjBlMzhkM2ViZTU4OTI1NDM3ODQ5Y2MyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktnVXNmTnRMSVBQaWNFZnBNYjJnbWc9PSIsInZhbHVlIjoiSStMT04veTcxKzBldVZ2UkgzYjhHZ0t4NGErK0NDcExJUlE2UEQ5bTFOTS85SnkrcVYwcjlrcEdxYjZ1b2tqemRNYlZjK3MwVGdxeWZkVDVlQXdjQVZyMVNkblZYYjJHZlJNNjJaWi9WTjdaZkpIRHpHMUROVmJ4bzVaVGd4QlRXbVBBMnpWaVVaTGIyK2pEWThQRWlwNjFuS3JGRnJGYlJqUWloWTUxTGNPSUZiTTl0NnRwd2RkZmNmZlY0b3ZaejZOeGVmQ3Y2MG91bkNtM3Ewa3BPRlpXWVZBVys2ZHNzMVM5T2x0MDdyVzkwVnlTcVFNMkw5QmtOSXJ2NG5wYVFVdXI3ME41bUNGa1lUaldSVFM0cVo4UjEwUjRnekZYRU5tblBtUW0rblplcUZ1bllkVy9Ya3VXWC93WEhCc01Ba24ydnBmU3hKR1BlRXhZNHNsNzkrdDk3L1VQY2xuWGRNRGp0N0tia2hjbGJpRXROdlk3cGl6WjM3TEFqU3lHRTFSVnowMlRZOEMwVFhsc2pKTUI4Y1UwdW1ZQUxEZUFWOEFnRHJWb0M5SE1xRW5pUiswMlRobkNaN0Z1bzZycWMwTEZ3b0FXRkQwbXNQcXpOUXVuMUh5cG1mcGN5WTlUS3hSeDE5NUF0bzgvN1BoRWZaQk5LL2xwY2JheUU5OUYiLCJtYWMiOiJhNWNhN2Q1ZmQ4ZGMxNjQ1NGRjNjhiMDBkNzAwNjdmY2I1YzEyY2E1MWVmNGU1ZTA5YzY5MjUxMGJhYzdlNTc5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:49:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120781049\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1150419775 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost/financial-operations/product-analytics?date_from=2025-06-01&amp;date_to=2025-06-30&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1150419775\", {\"maxDepth\":0})</script>\n"}}