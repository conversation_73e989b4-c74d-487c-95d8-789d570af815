{"__meta": {"id": "X109fafbcd9ff8f5f7e998175dca90622", "datetime": "2025-06-28 15:44:45", "utime": **********.901918, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.460286, "end": **********.901932, "duration": 0.44164609909057617, "duration_str": "442ms", "measures": [{"label": "Booting", "start": **********.460286, "relative_start": 0, "end": **********.850084, "relative_end": **********.850084, "duration": 0.3897981643676758, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.850092, "relative_start": 0.38980603218078613, "end": **********.901934, "relative_end": 1.9073486328125e-06, "duration": 0.05184197425842285, "duration_str": "51.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46373864, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2388\" onclick=\"\">app/Http/Controllers/PosController.php:2388-2422</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00199, "accumulated_duration_str": "1.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.885664, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 81.407}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.895094, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 81.407, "width_percent": 18.593}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-451049251 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-451049251\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-570557116 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-570557116\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-340524935 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-340524935\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-795888239 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751125105407%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlYwZ2lueUUxSXFHaVdBbWZIckprU2c9PSIsInZhbHVlIjoicEt1Q3hveXBPclFEVUExcHprYWNDelRwVzNhM2g1ZlBNczVHaW11bVhKMzh4UUV0R1hLcjB1TTgyMUF2dnZYZUJjanBTL0NQek44SlJOZTJjS21MdDdqbDZxaFE0dVRmM3Arb0kvTGtFZzRncWVOeXFFemQzaG9KenE5NU16enhNRkZDYWp1ajZGZFpoWUcyaDQvekVOYjIzUjBvam1nc0dUREF5RDJiaUtzcXAwV1hteDFMcjBKN1M1TVVIU0RZSWo2NjlMN1IwWjVJTUFQLzJyRnFwOVBQRlVRVG02eFJwaGgrN01jVE9CZlpwK2R1ZkhXZkkxOGZQQlFOSkhXd1hRdjVQSTYvWldIaDhhYkVCbGw2V0FxUkJqM05mbHR5SVFCM01zd3dxZTVMSmVUR3dudm5zd0hPMExNN2NMRjFvNzE2THZrblNjRGdEQmJDTTVTTUFydkFCK29tZ24wZUo4TWxyMXBMOWxpZnBRck9Ib24rczVEUUJRdGJ0WVVQUHBROWwwSDR6eVVncElYNTVZalNuNWZuSjh4NVQvaGdPL3pIeU9VU1N5YXdFSS9tbkVoY01BVklXU1BCa201ekRlNDBKYTR6ajRrMC9VS0lOTG1ycThxY3ZPNHNOUm53Znd4Z3VrZWFTMTdZUHI1V2NJdWU0bERwbGhjOVJDeksiLCJtYWMiOiJjZDAxZDA3MTA2N2VmNGVjOWViN2RjN2Y0ZGY2YjMxYThjNDNmMDIxN2E4ZTVmMWNkODE3N2U4NDU4ZGI5YzVhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZMZ2lSL0dxbUExbXR4WC8xTEJGa0E9PSIsInZhbHVlIjoiYlNXYWY4SHZTUnE2NzhMOC9nTnZlRC9RK2RKdEVwelQwMlBzeDRESU1KL3p4TlJaOEQ3U3h0ckFIRWRGNFNxUFMyUXAvTVoxSzlPQURoY3h5KzZhbDVCeXR6aHZYVTZ6eXRwVVAxdXE1SmloWDgxUUtpeVVzdFg5VEg5WHczVy94Q2RhZWhNU3dzcVlydmJaZFkvZlNSUHkybmtBenAvQStpaDRwMlkzUUZHZnFaeWNlb2xIR2hNckhISUZPZWNCd0tIenI2OHViK0FRNU1zRlBRbFlqUDRxU1cvT1VUS2Y4TkozM2tuS1NySXpsS2dGRmhmSU5iN0h0QlNtdlJxY3VIZnA4YUlqZFdkOHpHeG1YYi9ZbmJId2ZHVWNLbjZ2Q2dFb09KcEpQN1d0V295bnJYNEdHaUtkcFVud0hBemxPZHdTd2dBVTNKcXlSNlhYZ2dkWmVsTnBxMkxGV2JwdzNLTEI2WFBGT2hDczhkbnJPZjJwSWJKOGVjTFZQYjZGRkM4dU15NFNpWFVnMmd3TWVtY0NVWFkzZFdaTlY3aFNUN0xzSEFGUjY5dFZJMHA1SmdlVjJyQXNDcG5iNUM3eURKbE1RWjc0WkpOV0U0K2JIbDlCQXp4M0pZU00xVTRqTUMzU21KSWoyWnNjTHdwaUl5TzVZcDc5T1BEM1ZDM1MiLCJtYWMiOiIzY2FkZWIyMDMzYjY5ZmRkZTlkZWM3ZDIyYmUxNDljMjBlOThjOGM2ODIxMGJkMTU0ODVjYjUzOGVhZDRmYTMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795888239\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-626955143 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-626955143\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2083768696 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:44:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9kNW9LaVR4aHdUQS9lUzlWYjBka3c9PSIsInZhbHVlIjoiY2JPbmJCbHh5MWFHUDhObG1pakxuNFZ2ZlM0OXNzZkhZak1YK2lLVjA3eTE5SGFYYW80Wk9KblEwaFppSWFNVlpnNWVWN1ZhSE03NVh6c21PZ0MyVHZMdFB4Z3lxNEtzUVpBZ1hlYklheWt2R3BMaDJya1R0VWF2RVFVNnVMNGRrRVN2UHJRNkN2M25JMUN0UjlVZ0ZaYUtYaFdNR0pJYWE1a1RIcFdrL0x4M0plZFZkVDIxR2pwSk1xMS9KTFpWOWV1UU4xQkt1bGYxZDFsaE9NTnR2bWVsOGl3RnhISWFIcVBxRmQxcDI2S1dyZHp2VDlNS01KdWJUNnJJaHo3ZHh4YWNaOFBVdmEyTGtHWFl0d2tvY216NEo2eU9wdzVJQXhFUFZ1Y1JwaTN3N01YUFRtOUNVeGhVUFVmV0FFQ0xRb3VnWGtsUGlCZUgxaFU3UEVROTFNZ0pYeThtV0l1VkRmMXF5Z2xZU1NQUDBiZ3hRa20wVGtucUFSRW1QVERjSnZ5V0lpM2NxbDZhMlJXbEtiVDR2TmtZSmhMbFhlRElHcGV2NTlFeHBVcm1UNG4yRnFmaFZDVm5XNWVrSzJ3YVYybmZEVTQxUmxBaGNTS1J1Nnp2ZjNRUWlhelVTbDRxK0NPZE9ydTFIN2RLejJML1J5WXpCUnExdnp5TGZYSjgiLCJtYWMiOiI2NzhlOTYyZmZkYzQ0YWEzNjU0ODhhZTkwOWU0ZTA0NWY0ZDg2MTc0MTc2ZDg1Njg5MDA0OGVjYWY2NzE1Mjg1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:44:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBXbnZjN1ROcHRUYjRXVUsxdW9HbVE9PSIsInZhbHVlIjoid2hBMFZCNjA4R3F5b0Jsbk45Nlh1Ym1jRXczVURDVTFpTVZiKzMzanZEWEtMRFZ3OFN1L0Z2d3NCbUhETUU3ejNKa1lSNk9WNDJPMERUMlVuUGYxT1ZBK3JoclZGbFVpZUJCYlAxcW8vbjBIL2JBKzhlamE3K2k0d1dCQnhwQk1RQlRGQlozMUppU1h3cEJoWG85ZVRuRWYwWHBnQ01zTVdxNElUaXJ0RFBRc1plcVVJUkZtY2dkQ3JzS1N1TXRsbmtXMllUenFaTEtGK1BZcEhvNkl0dEE2dGZ4YXBxQjFpcnMxbE05WGlud0I2Q1RqVFhaSTdWenVENkluVG91TUlMMUdXRFB1SXVrVW0xYjdZRHBhaWVhM2FUQ0dkcXpSdEJCUnVLVFMzNGh0NXYrSmtHcEtmNE9rVXRmRkw2NlJNK3N6NEZncHIrNDVveEg5em1ZZTlXWHJ3M1JNVFhlUGFiMExYL2RqYlJNN0YybHVhTjEwekZBb2UxNGJva0s3S2ZMUHpsOHJpSFpsRE52U2FOM2N2MzJNaVdWL0ZUald6bVdkNjYzeHNSTHFYQmNxVVFLR1ZPb0NsQ1puS3ErbjRRekZMMWZvdkNhQjdscjNlNzNHUHN6bnppYjlQckIzZStEVjFScjlyTGZUS3hCbUJsQWZkaXBXT1c4TEI1dU8iLCJtYWMiOiI1MGM5ZmU0NGQwOGNmOWZjOGQ0ZGI3M2MwNDU3ODE4MGIzODM0NTc4NjgwZmY1MmZmZjczODg2OGVjMzg3NGEyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:44:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9kNW9LaVR4aHdUQS9lUzlWYjBka3c9PSIsInZhbHVlIjoiY2JPbmJCbHh5MWFHUDhObG1pakxuNFZ2ZlM0OXNzZkhZak1YK2lLVjA3eTE5SGFYYW80Wk9KblEwaFppSWFNVlpnNWVWN1ZhSE03NVh6c21PZ0MyVHZMdFB4Z3lxNEtzUVpBZ1hlYklheWt2R3BMaDJya1R0VWF2RVFVNnVMNGRrRVN2UHJRNkN2M25JMUN0UjlVZ0ZaYUtYaFdNR0pJYWE1a1RIcFdrL0x4M0plZFZkVDIxR2pwSk1xMS9KTFpWOWV1UU4xQkt1bGYxZDFsaE9NTnR2bWVsOGl3RnhISWFIcVBxRmQxcDI2S1dyZHp2VDlNS01KdWJUNnJJaHo3ZHh4YWNaOFBVdmEyTGtHWFl0d2tvY216NEo2eU9wdzVJQXhFUFZ1Y1JwaTN3N01YUFRtOUNVeGhVUFVmV0FFQ0xRb3VnWGtsUGlCZUgxaFU3UEVROTFNZ0pYeThtV0l1VkRmMXF5Z2xZU1NQUDBiZ3hRa20wVGtucUFSRW1QVERjSnZ5V0lpM2NxbDZhMlJXbEtiVDR2TmtZSmhMbFhlRElHcGV2NTlFeHBVcm1UNG4yRnFmaFZDVm5XNWVrSzJ3YVYybmZEVTQxUmxBaGNTS1J1Nnp2ZjNRUWlhelVTbDRxK0NPZE9ydTFIN2RLejJML1J5WXpCUnExdnp5TGZYSjgiLCJtYWMiOiI2NzhlOTYyZmZkYzQ0YWEzNjU0ODhhZTkwOWU0ZTA0NWY0ZDg2MTc0MTc2ZDg1Njg5MDA0OGVjYWY2NzE1Mjg1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:44:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBXbnZjN1ROcHRUYjRXVUsxdW9HbVE9PSIsInZhbHVlIjoid2hBMFZCNjA4R3F5b0Jsbk45Nlh1Ym1jRXczVURDVTFpTVZiKzMzanZEWEtMRFZ3OFN1L0Z2d3NCbUhETUU3ejNKa1lSNk9WNDJPMERUMlVuUGYxT1ZBK3JoclZGbFVpZUJCYlAxcW8vbjBIL2JBKzhlamE3K2k0d1dCQnhwQk1RQlRGQlozMUppU1h3cEJoWG85ZVRuRWYwWHBnQ01zTVdxNElUaXJ0RFBRc1plcVVJUkZtY2dkQ3JzS1N1TXRsbmtXMllUenFaTEtGK1BZcEhvNkl0dEE2dGZ4YXBxQjFpcnMxbE05WGlud0I2Q1RqVFhaSTdWenVENkluVG91TUlMMUdXRFB1SXVrVW0xYjdZRHBhaWVhM2FUQ0dkcXpSdEJCUnVLVFMzNGh0NXYrSmtHcEtmNE9rVXRmRkw2NlJNK3N6NEZncHIrNDVveEg5em1ZZTlXWHJ3M1JNVFhlUGFiMExYL2RqYlJNN0YybHVhTjEwekZBb2UxNGJva0s3S2ZMUHpsOHJpSFpsRE52U2FOM2N2MzJNaVdWL0ZUald6bVdkNjYzeHNSTHFYQmNxVVFLR1ZPb0NsQ1puS3ErbjRRekZMMWZvdkNhQjdscjNlNzNHUHN6bnppYjlQckIzZStEVjFScjlyTGZUS3hCbUJsQWZkaXBXT1c4TEI1dU8iLCJtYWMiOiI1MGM5ZmU0NGQwOGNmOWZjOGQ0ZGI3M2MwNDU3ODE4MGIzODM0NTc4NjgwZmY1MmZmZjczODg2OGVjMzg3NGEyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:44:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083768696\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-483482469 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483482469\", {\"maxDepth\":0})</script>\n"}}