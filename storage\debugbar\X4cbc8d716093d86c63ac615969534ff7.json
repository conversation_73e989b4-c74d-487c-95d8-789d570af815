{"__meta": {"id": "X4cbc8d716093d86c63ac615969534ff7", "datetime": "2025-06-28 16:03:55", "utime": **********.119763, "method": "GET", "uri": "/enhanced-pos/get-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126634.673254, "end": **********.119776, "duration": 0.4465219974517822, "duration_str": "447ms", "measures": [{"label": "Booting", "start": 1751126634.673254, "relative_start": 0, "end": **********.046031, "relative_end": **********.046031, "duration": 0.37277698516845703, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.04604, "relative_start": 0.3727860450744629, "end": **********.119778, "relative_end": 1.9073486328125e-06, "duration": 0.07373785972595215, "duration_str": "73.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46423288, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET enhanced-pos/get-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@getEnhancedCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.get_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2428\" onclick=\"\">app/Http/Controllers/PosController.php:2428-2462</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.024169999999999997, "accumulated_duration_str": "24.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.081618, "duration": 0.02388, "duration_str": "23.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.8}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.113476, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 98.8, "width_percent": 1.2}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/get-cart", "status_code": "<pre class=sf-dump id=sf-dump-1926670239 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1926670239\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1831486160 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1831486160\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2049450313 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2049450313\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1134650061 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126629815%7C23%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJtUHlOMndMNmk2Z3gzcHZ6K0g0VkE9PSIsInZhbHVlIjoielVwNGEvSjdZY3RVTzBCa1hOU25VaHVqc0k2WlRkMjNvZUFTU0g0NSszb0svMWVKcnRRTlhza1NtT3Q4S29aRmowY3o0dmZGVWYvYUdJOENiUmQ3eCtlZWdTTTIxcXhNYlMwUUdvWDFrbXZZTjZEeW0rOFl2MldERlNScTRwQitLamYyQy9JYS8rRkUvVGE0aUNRMklzMHErODYxcTRmN0xtbmRPN0VuM1RLZWJQeExoUERyWmZhTmhuMTJnT20wa2VUbEh3bkxITzFMQm5WaXA4VVpnaGgzUlF3ejhpcDZXemw3RVNQdDFJTmJhbjJvaVdyd0JTNlhTLzVpZ0trd0t1TFhxWnJUMXNScU9GQlhobnZjbWttWnFHaytCRHVhRTlKdklvQ2R2anZPTGxlTDcyYjZVZklGbG45SGhmbjBWaTNpT2JvK3dJa2VTMlhDbysrTG1naGJwajNoYmRXaG43TVBkS05aVGxlOHJySFhmaEg1bHRrVUdsejExbjd2NzhMZFNvLzdsSWR3TmNLUWJTM3JYLzg4ZTVIeXVUVEM4cC9wS0V1cGFLOUgxbXBzM3NNQWhCcGhYbUU3OTBGbTlJWnpiZkR6TGZNMHE5VHBNakJRbko1ZGJLUDlhdHA3UXpybTFBM281VnlPZnFCMlc4UDl3eGlmbTRmb3BMaXciLCJtYWMiOiI2ZjVhYzQ3MWQxYmE0MDhjOTZiMDMzMGUyOWY0M2IzOTM2ODIzNjdhMmE2YjVlZWQzYjdlN2QyZjJiNmZiYzk4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1qRkFWWGVjbTFDSlJWUWNDeG5HMWc9PSIsInZhbHVlIjoiOHBMVXZDOEJmSnJrKzdmRHErZ25ORkR4M0FhWktxWmFyeTFsZ2VWU2pJS0l5QU9KNGVpemFld2xuY2k4L3p0Qk1WV1FWaUJ0NHNKUnBHME9wS3BlRlZKa1lLNk90cEozV1Z5bjhNakRyVmlHb3JXVjdtQllXdDBHckIxc2RyK1BOb3VXQmR3aFNvTmpWMStRK2tUNkZnK0RXbjBBSXVCalZFbnhqb29yUU8zN1RhUzVrakRJaVFETitzaTFnZCtZSndYWkkvc2VCcm1CVHU4UUxMSFBZaTFMT3VHZ09LQlBkQzBBWWtqNERMQnRTNERqWDg3TUdXOGxDdS9pV25wUTM2WE54YzRuNmdwNnNiRTI4TlpiTU8vMk1MWkRTRVU0cWhrUEY5Sy84Y3c2K0s3VVBkZUJkc3UxOElsYnZZSGhZdkk3cUJ5QmpqVWVQUk5ISVVHN1Zpc3pPdG9LQ1ZKaE5zV2FKZDN6YUw4QzB6ZGx6QlJJQXdhaDBhcTlmWWxqazlEMllWUVZJTDFoajRMc21xdEFSczA3SXppOFlBN0N6SWhTVmp0ZTA2MzNSdzJnL0EzRTA5bWF6clhSK1JqZTJlUFN0eFpWR3JnbzRDTjRDWnBldmZIL3JDN1Npd2dsVTJzeWNMN293SkhlRUlmVFpPNlNZTkJKQUVNbk9YVHUiLCJtYWMiOiIzZGMxZjZmOGQyYjMwZmFhNzQwNTNjM2QxNDI2YjhmZTRkYjJhYzVmNmM1M2UzNzEyZDA2OGE5NDBkZDFhMDgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134650061\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1167454008 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1167454008\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-224887729 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBLb2ZMRGN4QVJjRWdxWHdlT25ld3c9PSIsInZhbHVlIjoiTWRDMGdRYmtHMTI4TTlzUlBreUdCeWdDVEtQdXB6UXVMcHNaem1QV1B2S3RWUkJ5cHEySXB2WmZxbGw5Ymhjd0U5Q3JScHRleWg1ZUVWRGFCc3hOVENCV3NuYjhnSWxjdElWMTRjY1VGRnVtNVJRemUzNFM1dVVDRnVMRGJuWkN4aGFTQ3lFVWlEQ3FRWWlGWlY5Vkp6QnNJL0dlM3l3bjBpeTNxNEkwbkNpNTB2aGt0dkFvR2grNVJYWGNNL3FxMjBPdmJaK0FjaG90MndFU2dZTEdlNmFUODBVWmdZWWFiMEx4cUxuUHdNWVFNVEs1OGR5WFpvd2Mwb21ITWR3bytud3J1bTVpRGVWaEpRb1oySEJqYmxPWE5EdndPd2gwTnZEZ2NSR2ZLWEVwRlpWUCtmdFVDb3Q3NCs3U2lUZVFqYm1qNHNpemdGWTRsZVJGend1M0hkS3pBenRKdDduZ3h1NzlBOUg1bm5ycy9GSjNUN1JROEpuQVh6KzNXMjI2anBwTWU2R1lDZG9Vb1p0T3ZVOTJVWS93WFpCYS9CZmlodkJENHdIRlFobXVtazNvUGpPWWRqTG9EcG9BWHdLSjFRU2FKRktkeUpBQjFTK0RiQ0k2TjZYNml2a1NYT21SdFRQK2I1MDd3VTViR0x5THZacU1Tbmc1eXEweThaNWEiLCJtYWMiOiI2NGZhN2Q4NDA3ODkzNWJlNWZjYWM5Njk3ZmQ3NmVjNTViMjc5Nzg0NjdmYTM2MDU4NDc4NzIzMmRhNDI5ODIzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFIVW5nVXBJTVZKSkJpNnpvYnppbGc9PSIsInZhbHVlIjoieDdIOWlzRjJWd3JTRzV2ZVpuOHNEUE1IRHFHS3NRMmE0bjZGdmdTWkNJMTNXVUJBcDBua3BpbWZwK0dqdzdMUENGNnA3a3M4R0RNVVNWU0JDY3NIeTJwaURWdU9RNFpkTi9JTVBZd05MckwzRkdSUFBwOFFRbE1neFBHdUlINTlicFA3Y01wTFltMTZUNXIzNjRDMFJ0NW9lUmJKOUF5NkpZT1pZTUwzN0pJUGlZa01hWWw3Q3VnKzZIM05pTE5VN3ZiMDNzaXBaMUU1SGdtcnRaVzhZc013NzNxSVNiZ2Q3VWZNZ241WTZ2cDdzWk9yNlcwbytwQVdVM24xeFZRRHRRUU00VUhnenlUWHpuS1psdWlINElwZk9QQndxR0kzdlNzU1c1TnVpSHJxNWo4ZTVXZTZJR0hkWVVXNVhTQVBRT0REL1p0K0NaLzF5dHZ3aWFMUzBiMU81N0ZpR1lwNVIzVzRJOUNXSzcxVE4vL1dRRkdmNm1EcVhlM3c2a1lmbjA2QjhYRHJWdDMvcnZQZDFDcmxDOGpYK0VFU3NqeFJCV3habmdyU3hoVSszMHE2aXdOTmtocVA3anI0RWxxR3pXL00rK2xKYjk1M2JadXp2RzgzS2lKa2lncHY5UVczS1dhb3ZkUWJYU2EvNU5YTCt3czhRaWwrQnBDSVhPSFYiLCJtYWMiOiJmN2UyOTQwN2M5ZWVmMWE0MDhjNmMyMmQ1MzkxZmNlOWJiZjQwNGU3YmVjMDg5NmZlNGNjM2JlMGM2OGZiYmEzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBLb2ZMRGN4QVJjRWdxWHdlT25ld3c9PSIsInZhbHVlIjoiTWRDMGdRYmtHMTI4TTlzUlBreUdCeWdDVEtQdXB6UXVMcHNaem1QV1B2S3RWUkJ5cHEySXB2WmZxbGw5Ymhjd0U5Q3JScHRleWg1ZUVWRGFCc3hOVENCV3NuYjhnSWxjdElWMTRjY1VGRnVtNVJRemUzNFM1dVVDRnVMRGJuWkN4aGFTQ3lFVWlEQ3FRWWlGWlY5Vkp6QnNJL0dlM3l3bjBpeTNxNEkwbkNpNTB2aGt0dkFvR2grNVJYWGNNL3FxMjBPdmJaK0FjaG90MndFU2dZTEdlNmFUODBVWmdZWWFiMEx4cUxuUHdNWVFNVEs1OGR5WFpvd2Mwb21ITWR3bytud3J1bTVpRGVWaEpRb1oySEJqYmxPWE5EdndPd2gwTnZEZ2NSR2ZLWEVwRlpWUCtmdFVDb3Q3NCs3U2lUZVFqYm1qNHNpemdGWTRsZVJGend1M0hkS3pBenRKdDduZ3h1NzlBOUg1bm5ycy9GSjNUN1JROEpuQVh6KzNXMjI2anBwTWU2R1lDZG9Vb1p0T3ZVOTJVWS93WFpCYS9CZmlodkJENHdIRlFobXVtazNvUGpPWWRqTG9EcG9BWHdLSjFRU2FKRktkeUpBQjFTK0RiQ0k2TjZYNml2a1NYT21SdFRQK2I1MDd3VTViR0x5THZacU1Tbmc1eXEweThaNWEiLCJtYWMiOiI2NGZhN2Q4NDA3ODkzNWJlNWZjYWM5Njk3ZmQ3NmVjNTViMjc5Nzg0NjdmYTM2MDU4NDc4NzIzMmRhNDI5ODIzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFIVW5nVXBJTVZKSkJpNnpvYnppbGc9PSIsInZhbHVlIjoieDdIOWlzRjJWd3JTRzV2ZVpuOHNEUE1IRHFHS3NRMmE0bjZGdmdTWkNJMTNXVUJBcDBua3BpbWZwK0dqdzdMUENGNnA3a3M4R0RNVVNWU0JDY3NIeTJwaURWdU9RNFpkTi9JTVBZd05MckwzRkdSUFBwOFFRbE1neFBHdUlINTlicFA3Y01wTFltMTZUNXIzNjRDMFJ0NW9lUmJKOUF5NkpZT1pZTUwzN0pJUGlZa01hWWw3Q3VnKzZIM05pTE5VN3ZiMDNzaXBaMUU1SGdtcnRaVzhZc013NzNxSVNiZ2Q3VWZNZ241WTZ2cDdzWk9yNlcwbytwQVdVM24xeFZRRHRRUU00VUhnenlUWHpuS1psdWlINElwZk9QQndxR0kzdlNzU1c1TnVpSHJxNWo4ZTVXZTZJR0hkWVVXNVhTQVBRT0REL1p0K0NaLzF5dHZ3aWFMUzBiMU81N0ZpR1lwNVIzVzRJOUNXSzcxVE4vL1dRRkdmNm1EcVhlM3c2a1lmbjA2QjhYRHJWdDMvcnZQZDFDcmxDOGpYK0VFU3NqeFJCV3habmdyU3hoVSszMHE2aXdOTmtocVA3anI0RWxxR3pXL00rK2xKYjk1M2JadXp2RzgzS2lKa2lncHY5UVczS1dhb3ZkUWJYU2EvNU5YTCt3czhRaWwrQnBDSVhPSFYiLCJtYWMiOiJmN2UyOTQwN2M5ZWVmMWE0MDhjNmMyMmQ1MzkxZmNlOWJiZjQwNGU3YmVjMDg5NmZlNGNjM2JlMGM2OGZiYmEzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224887729\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}