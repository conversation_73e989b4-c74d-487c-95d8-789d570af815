{"__meta": {"id": "X2514ee58ff479708cf8407e596521e8e", "datetime": "2025-06-28 15:46:36", "utime": **********.294325, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751125595.799475, "end": **********.294341, "duration": 0.49486613273620605, "duration_str": "495ms", "measures": [{"label": "Booting", "start": 1751125595.799475, "relative_start": 0, "end": **********.218264, "relative_end": **********.218264, "duration": 0.4187891483306885, "duration_str": "419ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.218272, "relative_start": 0.41879701614379883, "end": **********.294343, "relative_end": 1.9073486328125e-06, "duration": 0.07607102394104004, "duration_str": "76.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45163840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020959999999999996, "accumulated_duration_str": "20.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.248757, "duration": 0.020239999999999998, "duration_str": "20.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.565}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.278765, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.565, "width_percent": 1.622}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.28478, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.187, "width_percent": 1.813}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-740628303 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-740628303\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-994829915 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-994829915\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1811059703 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1811059703\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-465322295 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751125587718%7C19%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImsyWU5lUHhHSjdHMEE0SWFYcEd6QlE9PSIsInZhbHVlIjoiM2Z6cGk0MmJxSE5wL21HckwyWEhJZE9Hdi9PSlBHUWpQY0ZwdWVwQ3pOVzRnTVJ3WlhUYXZpanZGdTZ3cVhYVmF6ZUEzUk1oYWtiMWFsaVF4VFlreEF6UWQzdXdHQzdQamx4c0FtNDRpc2tFTkxTTUw1bDZ0dk9qRTNEaEk4NTRkOGRPa3BydjZ4TGh2bm1pZHQ4NnlpUXo5WTNKNndIMGdKTFdtS2dUU3NuWlRzWkw3T25qQ2NoQW1oOHBFc0dEaGJ2SHVlWHN1NW5vbnNWR1lxamFnak80bnZYbTczaTNjZXpSaExJVy9xYmxMMEJONFd5YU9kd1psdXAralhNVHFnK3dBMXVsMTgxbXE1MzdYRGpWd0dQdS8yWVNBaFowZHlkRmh5cnMrbW8yVVptcE9yMWFzbGo5Mm1pV091RVFkVmFDUWxRMElQWHVWM3VPeWExV0FJekF6am5ObFZvdzJ5S0hGcTNGa0FQUWYzNEFzb0dwOU8xNG9TVUhoaXdPNnZBWTFDYjMwS3h3ajZ0LzE2L1A4UDUyc2hldUdLSnVORzdTSEQ5R21jdldKR1RGTGNaTWJyR0NxSDJUaS9TRHl4N2lCOHFmdjdUcnlETHhDR2hLd0hFK0xTYTBzZm9mRit5MXNOa2loUktZdGVQTnlWWURnc0tQSGdvY0F4bXQiLCJtYWMiOiJmOWEyMzgxNTVmZmNmNzI0OGFkZTkwOGRhNTY2OTNkMDNkYTk5NTM2ZTBhM2NhZDZjYTYyMTY2N2FjM2I2MDI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InA3SlF3SENNb0RHK0oxLzRNbkhUTXc9PSIsInZhbHVlIjoiZTFzNitod2VBdmYzTVp4TGJON2lYaEpDNHVub0dudWc2M3QxcmRJbFBGaENPTUlCcW9KRHlJNkJtQ0IwTkZKZ1lYWXRjSnFpUnI4Q3dTaURZcThUNXJqMUR6QTM1blI4ZDV6ZjFwRUl5amQzcjdoU1d5UWhMOGFJa1IwOTBmMllqcGdWOGxBZ1E0azR5d29WUUxVU2RuZjVsSllsdGwwUGY2S0E5dmRkbXBIR2VxdXNVa3g5Zkg0eTJQbHhhMXVmWU4reGFZUEtKbGx2cm1OVHVnN1FlQ281R1UyS2Y5VG1NMGttK0tWSnpsbTNEeFpoSVhtZXNWRHQrNVJORS93ekZVUVQrSU43UEtNQVF1Y0dqb2wwUkJUb0VnSVYwSnJxS0kxd1N2TGFxK1V1WS8reStYbWxBanNPZ29XVUFJbVEzdE9VOUlBNTJYYndLQnZMeTA2Y2xwUHlVVUFLaVg0bnhzRml0VUs4OWxaNS9mcVY1Y3lYZlFHOXl5R3hHTjlGM0VJT0p5UzZhREtyMC9Ga1hQdVVqTkxFVzNnZ2I0M21MbkNRRzFaY25QaDVYMEd3UnJvTkdmY2JxQ3ZrQVNFdnozYndUMVpCWkVuT2RGWGlseGZxYnAvSy83R3BJcDR5SkZ0d09xdHFKVzdoZFVPeWVSTllFYjRDdWtNaDhlTk8iLCJtYWMiOiJhMDdmYmEzYjVhYWZkYzljZjE1ZDEzOWE4YzBkMjU5ZWI3YWM1ZjgyZDdlNjA4YzgzMjA0ZDI5ODU2ZTNhODg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-465322295\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-472694597 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-472694597\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1406817285 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:46:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ing3andYMy9YcWRBWVcvOW1TaGFsMnc9PSIsInZhbHVlIjoieHpXaG11dExFWDhvZXhZWnppY05YSUc0dW9WMzNMbi9SaUVxaDJUekFSTUtEeGFFVGhxMTg4WTdNaURiNTFLSjVhdm1kZk56c29BWGZ4a3VRUzJCVE80V0hCa2FCODd2cmJDUmliRmdKeDYzYWhWcDNPbkFQUUQ2cU4vcTVpZ0c3QWJoNGkwTVptdjZVOVQ5VS94TXVUSVFMRUozdXl2OXpjTGFSNWZ0K2JnQXMxV0cwZlVxbVNJOXhvV09rS3M1ZmpGdWZ3a2dEcTRuY2x3RVBqenFrZlVMWFJHakNwSzVIamhQdGdHUTR4UU16ZkY5T3NSNmVETEtMTEJYRE5YL1VZc2RYSlowMDlQOWRUbkhrb0RFUGF2V2w5TDNXRDhLVjREbDczanRzSVVIbUdFM2tORGJKOWJidGR0TkNxWHBxL2xtOE1jdytEZlRPUmJVckUyTjlqME0vNzFsOGhpNDJSL2pBU1doR2pCMWRxcUEweFQ1cHQ2cEJVb1hONjl2a2o5dUNWVmtMNVB4TGZNbkYvN1FPZGJvbi84cTQxOWh3ZWg0S0VEMTcxU0JqTDZrRE9xZ0VIYTVYT0dQbGRVNzUrMUs0ZUp2ckZvSTJyN1d2QVF1a3ZkTTV1MFNuSEpnOUZCc0VuekttSUVlUlhOdnlvUnVadTJvaWhVeWVUbDkiLCJtYWMiOiIzMThmYzRiODQzNWJiNWNmMjA2MmZmNzQ2NDRiYTY0YTVjZjU3YjJhYTVkZThmNjM3YmI3YjAyYmE2ODRlMmI4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik52ODlibCtyeG9LeWJSOGo1VjNCdEE9PSIsInZhbHVlIjoiM2VpVVZOMDdkY2J4TG5SdytWYlhHdmNjbE5zZ0h0NjhNM2ZLRjJubXdiWVZtSDhEZ2lEbCthamtscVFkR2lxWTJMQ1g5eHBGNU5CL0pIWTVHSGJNVGhyS0NJWGNpMTQ0WEZCbm1GSGozMDJVaks0dzVWVXd2WGNOd0pPcGNic1NkdVZVLzFLZmRZUWp0OXNlMEI0VHRBZU9Tcm51QWIzUDhCUkl3V3BuZ3JaNlBQWTBNb2tkajB2Ris3cUppSGhHOHFZeVVDcTBMU2o0MDNMU3VnWGQvQ200b3EvVytnOUtkdFpCd3prQjhGV1UvbnhwRVl5bE5JQU0rVGgxcjE5OEFtRUlENXdlTURhc0F6ZURkL1h4ZURZaXNkZmFEc1pCWHRJYlZwTC8xMnQ1NzcvczRnc0Q0WGY4UERmRTBDTE5TZFZidHZMYmV0QW1QbnpUTVFCL0pBeGp2Zm50ZzgwYVJRYnZScjF2RVB6c0VjTzc5K2J1OUt6SFMwNjQ2OVlNZk9YNWFsenpnY2x3UXNnb3JtL1ZrcENDdVVhTHBoZGdFNTJvcmtYWW14U0l1dEJoK2hFQWdSTmV2MFk4VFgxY040SXEweDlMMkVKUEt2WklNdHF5WklLUUdoakZOajg0dExwdWE0V2hSWVhUbUJ4YkZJUVZCakJFbmpzcC8zWlUiLCJtYWMiOiI1MDRhOTY3ZTk2M2ZhNWM1NjMxNDZmNjZiZmMxOTZiMjI1ZTNjZTZiYWZhMTUyYmFmNmJmZjgzMjBiNjk2ZjBhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:46:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ing3andYMy9YcWRBWVcvOW1TaGFsMnc9PSIsInZhbHVlIjoieHpXaG11dExFWDhvZXhZWnppY05YSUc0dW9WMzNMbi9SaUVxaDJUekFSTUtEeGFFVGhxMTg4WTdNaURiNTFLSjVhdm1kZk56c29BWGZ4a3VRUzJCVE80V0hCa2FCODd2cmJDUmliRmdKeDYzYWhWcDNPbkFQUUQ2cU4vcTVpZ0c3QWJoNGkwTVptdjZVOVQ5VS94TXVUSVFMRUozdXl2OXpjTGFSNWZ0K2JnQXMxV0cwZlVxbVNJOXhvV09rS3M1ZmpGdWZ3a2dEcTRuY2x3RVBqenFrZlVMWFJHakNwSzVIamhQdGdHUTR4UU16ZkY5T3NSNmVETEtMTEJYRE5YL1VZc2RYSlowMDlQOWRUbkhrb0RFUGF2V2w5TDNXRDhLVjREbDczanRzSVVIbUdFM2tORGJKOWJidGR0TkNxWHBxL2xtOE1jdytEZlRPUmJVckUyTjlqME0vNzFsOGhpNDJSL2pBU1doR2pCMWRxcUEweFQ1cHQ2cEJVb1hONjl2a2o5dUNWVmtMNVB4TGZNbkYvN1FPZGJvbi84cTQxOWh3ZWg0S0VEMTcxU0JqTDZrRE9xZ0VIYTVYT0dQbGRVNzUrMUs0ZUp2ckZvSTJyN1d2QVF1a3ZkTTV1MFNuSEpnOUZCc0VuekttSUVlUlhOdnlvUnVadTJvaWhVeWVUbDkiLCJtYWMiOiIzMThmYzRiODQzNWJiNWNmMjA2MmZmNzQ2NDRiYTY0YTVjZjU3YjJhYTVkZThmNjM3YmI3YjAyYmE2ODRlMmI4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik52ODlibCtyeG9LeWJSOGo1VjNCdEE9PSIsInZhbHVlIjoiM2VpVVZOMDdkY2J4TG5SdytWYlhHdmNjbE5zZ0h0NjhNM2ZLRjJubXdiWVZtSDhEZ2lEbCthamtscVFkR2lxWTJMQ1g5eHBGNU5CL0pIWTVHSGJNVGhyS0NJWGNpMTQ0WEZCbm1GSGozMDJVaks0dzVWVXd2WGNOd0pPcGNic1NkdVZVLzFLZmRZUWp0OXNlMEI0VHRBZU9Tcm51QWIzUDhCUkl3V3BuZ3JaNlBQWTBNb2tkajB2Ris3cUppSGhHOHFZeVVDcTBMU2o0MDNMU3VnWGQvQ200b3EvVytnOUtkdFpCd3prQjhGV1UvbnhwRVl5bE5JQU0rVGgxcjE5OEFtRUlENXdlTURhc0F6ZURkL1h4ZURZaXNkZmFEc1pCWHRJYlZwTC8xMnQ1NzcvczRnc0Q0WGY4UERmRTBDTE5TZFZidHZMYmV0QW1QbnpUTVFCL0pBeGp2Zm50ZzgwYVJRYnZScjF2RVB6c0VjTzc5K2J1OUt6SFMwNjQ2OVlNZk9YNWFsenpnY2x3UXNnb3JtL1ZrcENDdVVhTHBoZGdFNTJvcmtYWW14U0l1dEJoK2hFQWdSTmV2MFk4VFgxY040SXEweDlMMkVKUEt2WklNdHF5WklLUUdoakZOajg0dExwdWE0V2hSWVhUbUJ4YkZJUVZCakJFbmpzcC8zWlUiLCJtYWMiOiI1MDRhOTY3ZTk2M2ZhNWM1NjMxNDZmNjZiZmMxOTZiMjI1ZTNjZTZiYWZhMTUyYmFmNmJmZjgzMjBiNjk2ZjBhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:46:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1406817285\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-896106594 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-896106594\", {\"maxDepth\":0})</script>\n"}}