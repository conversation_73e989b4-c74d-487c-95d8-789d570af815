{"__meta": {"id": "Xa2bf4353852e2695e836ebd65739b1c9", "datetime": "2025-06-28 16:09:20", "utime": **********.661954, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.187719, "end": **********.661969, "duration": 0.47424983978271484, "duration_str": "474ms", "measures": [{"label": "Booting", "start": **********.187719, "relative_start": 0, "end": **********.569088, "relative_end": **********.569088, "duration": 0.38136887550354004, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.569098, "relative_start": 0.3813788890838623, "end": **********.66197, "relative_end": 9.5367431640625e-07, "duration": 0.09287190437316895, "duration_str": "92.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48251272, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.021500000000000002, "accumulated_duration_str": "21.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.60221, "duration": 0.01581, "duration_str": "15.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.535}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.628244, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.535, "width_percent": 2.791}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.6418738, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 76.326, "width_percent": 2.744}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.643717, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.07, "width_percent": 1.767}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6481588, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 80.837, "width_percent": 11.767}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.653149, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 92.605, "width_percent": 7.395}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1210446487 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210446487\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.647065, "xdebug_link": null}]}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1766155801 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1766155801\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-345983643 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-345983643\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-882787558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-882787558\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1520959901 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126882941%7C30%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVHVnJaR0VtSzB6RDJ6TC9GdFRKWlE9PSIsInZhbHVlIjoiVmlFa2pvZmxQNnV0b09wV2JaZ2ZWUHpxTzYvWkFQdzFnYXdRTXhNTCttMXlGNHdSeHFkZ3R1QVZjcGVuZVk1dittdFRlUWZ2UWxqNlpwQ2NoQzBxWEVMR2d4TkRhd3ltZlMzamhCT01hak81c3BweGdkdktmT1Bac2MwZ2pMVWhzUXkxVWNXalZ6aG1lcGwyVnhzTWhJZHhjS3FSdHNVZDUwUFo2V1ZsaUgzamMyWTFNdnBKNW9ReTNpR1hhQ1ZLMHkwa3J0czFVQjByYkhqMFJzdWZvcWNLYjVjdkFjZGxXZklDQ2lsK1htTUR2UUt2ejRUUjZSU0EwdjFCN0E4b2djc0M2ZlBUTm9nOWpGTDdxeWlhNzh6Sm50czJrTFJDNi9PUWNxOU5vQ0NHWXZqeWhPRWsrK0xWamxqMnBjSCtFUWhLT1E2UlBTNXNKaXU0QTB4dWlaN1cxeG9SR0RvWC9KLzFETnN5T1RuSE1HcXd1NEp3TCtKbk5LSllkZWVYOXBZakFFZ3JnYmVkVVR5RW5sYktYall3M0RIU2F4enJZdW81WjkwU0xqdmxxNDZ0d21KUDJHVkZXajJKUENpU3pyUzY2QWRJL2UvYjdsK0lWaTZ1dmdoeUJDKzNIRVFFRVQ3dGRVZWZDQnNHN3RCRzZJWGVkdTlTTldrMlhyV1IiLCJtYWMiOiJhMGIwY2FhMWY1ODczY2UxZjg5OTI0OTQ4NWY4OTA4ODBhMjVjZDYzNmM3ZmIyM2ZlMWE5MTBmOTg1MTM2MzdlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1PZmVQdWNBVmFXcEI1azZ4amhkdGc9PSIsInZhbHVlIjoiK1hkWlhaaHhJYjNUTTYvNnMrYndEUlBLM1ZSeVRTOSsvL0xNV1ArOGhzTEF2d09tOVozemZ1SWt5UFlmY2xoRVhzMXl3WGxHMlF4NGFRTkhtaW1CM044TUNaaWRRSU9EcGl0OG9sZmF3SkYya3RFbkdoVlJVcUdWa3luMGx2WlpFayt0U0JKTTl0YXZKdUpiTHd6K2JYYUFhbVl2L2theCtMMU0zeXoreVZEY3owdm1nRVQ0dTY1M0dqbDVMRjQ0N1dxZVpJd1pXa1ZjejdySEZJYjBTVStxVGprK0ZtZ3FacTlNajZNVDlLcWp3Wmg2dzl4VG44TWZXR0xzaHliQ2ZOa3lRQ3EyenozN2plMUFob0hTSVptNUEvaEpCRzV0QXcxL1Q4Y1VRNTd5ZU1NSmZUNmppbDdVSnhqMXpZTE5yRk1DTjZ5VUlpd1VjN1VRREd6RGtBMzRBcEFsT3BRMW1CQ1RmbnExQytnZnJRZGE2cURwWlpuMnUrOVZGbkdGRUxldkIwREhnckFONnNYRHlPcFZKTnU3NHBUSCtWWUR3bEFBZFJneDhsTWtYc08vSncraElWTGdIZ3pXYW4wU3VodE9ZWlBid0oxelVQZkw2TFJKT2x5blNTLzBGOU9RZkdkN3F0aVlYaG9IcmhwUS9hYnY2aWFPN3YzcHJCbTUiLCJtYWMiOiIwYWQyODAwOTA2ZWJkODU4YmIxYzkyYmM0OWFhYWI1ZjQxNzFkMTcxYjQyZGFmYjM2MWQ4NjMxOTI0ZWJmZDlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1520959901\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1934176722 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:09:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJ0bDlQekY2YWF4aExHaXd1U1NROGc9PSIsInZhbHVlIjoiSm5xN1lyUHB4bUZCakJXN2trU1E3bEJJSU5PYURiSGw4cGx2ck9zZk9nN01WS2hBMGNNalFUQndzZzNwb1NFb0VMVjgxS0g0ZFRzL3lPRUtIRUwxYVpQM0lvSzdUQmpSektURVZMTEtDQlk1WFpCczNpUFJnbE9CYlJzM2dWUXFOcHErWUJ4aElKbXUxeTJmSjlKd3ZpcXNCY2VmS2srK2JGQXVLdjhoNjk4MjdQa2xDWGYrbHFndDRvK2JnZDhpM3prRW5QdjlSNHo3TXY5SjVST2xxenkwYTJzSTlkeVJXOGlkb1pxV2tsa1lmdkpCdFI3Ui9YK2psUVFZeU1ETGRhQndyL2ZwanJGTTFYTDgyYWJ1UWFlNzhXYW43emdwTmloYnRpbzF6cm5HRUhmMXdmdlNTSU56NXhCQk9NVzZHdGtSdC93QmROcmlGRnhpVHk3OCt3V1I5bkM1bXltWk9Hcy9JMFAydENZMGpvNU9uU2ZWNWJUK285N0NweFJoZ3R5WWMrT3ZrWHdWcElnb3d4REhaL25kVGtvZWIwU0E3RTliNllHamVxSFF0dzJlZmhMZEdZWk9yWk1VZEU1bTd3azJxREdJN05meU1jMGY3Tml1enNDeWsrV2JJNnFZMGNDMDYvb2pmNUVqWm11aGlKZDI5L1ZPcVhXMTNhaFEiLCJtYWMiOiIwOWVkMGEzYjZlYjU5NDM3M2RiNDRkN2IzYzEyZDQ3MTE2NGQ5MjhkNzE2YjYxYTdlY2I3MzllODNhZjJjMzE2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:09:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1JbWVVck5UK0g0WXdPZXAxcmZPMGc9PSIsInZhbHVlIjoiNWFOaFUwQTlOUXJZWGYzM0lLR0hkL2RvWWlJclNlVFdYdzU4cFBkWk5GeGtaQ3FMeG9aOStrREZmZHc5b0t5UUROcEVDU1N3YXRHTVQ4bzBJS1F1L1J5RDlORzdheWk3WGVFWVZsVG43dVZoVGlGc0JVM08zYXVJanpTSm5YM1U1NHVBNGkwbG9aeHYya2w1dUo3NjJDVXF0Wm8zaVBmNVJnbVhMNFd5VXB2Q2w0UWRkUzh1dzRzOERQbVI4a2crTlV3RDY5WjE4allzSXBiWG5TNUFJcDgrS1NwN3UxeGtTa3QwUkFKUWhManZxcXgrdTVxNEZuM2x6aFVXaVJ4WDVXenZBOGlWNitQaXJlWlhmZ2lGZk5MQWtFZEJxVGNJRkt3SFA2d3pLSkxKQVdiSFdBb09CaXFLdmxsZTRRd2UvV2tWSUhUdEp5aElGZG56c1hrVFpkQmNLbzNEVHU0NU81TStrMmk5LzBWWDQ2cWZUSlE5ZEUzMjJVUUtHb004UTE4N25VcWR1MW5mdldVUjU2MVJjdXdqRFYwT3ROWVhsN1FLY1Y0WG1IOTlBVVRNSzJXQkJEN3U2eGU3akRIQTZnOGpQaTlHeHY3dktKZDhQTEM4ZFFSTlMydmxNOWRIS0lNVDUxeFhkNVFBVDJHNXpvb0ZwbGJNRWkxZzVJOFMiLCJtYWMiOiJhNThlODY5ZmE4NGUwYzUxZTA0NDc1MzRkYzkzODdhMzJkNTA1ZjEyNzY1Yzk2NjQ3ZDczNDg4YjdmNGU1ZTg1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:09:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJ0bDlQekY2YWF4aExHaXd1U1NROGc9PSIsInZhbHVlIjoiSm5xN1lyUHB4bUZCakJXN2trU1E3bEJJSU5PYURiSGw4cGx2ck9zZk9nN01WS2hBMGNNalFUQndzZzNwb1NFb0VMVjgxS0g0ZFRzL3lPRUtIRUwxYVpQM0lvSzdUQmpSektURVZMTEtDQlk1WFpCczNpUFJnbE9CYlJzM2dWUXFOcHErWUJ4aElKbXUxeTJmSjlKd3ZpcXNCY2VmS2srK2JGQXVLdjhoNjk4MjdQa2xDWGYrbHFndDRvK2JnZDhpM3prRW5QdjlSNHo3TXY5SjVST2xxenkwYTJzSTlkeVJXOGlkb1pxV2tsa1lmdkpCdFI3Ui9YK2psUVFZeU1ETGRhQndyL2ZwanJGTTFYTDgyYWJ1UWFlNzhXYW43emdwTmloYnRpbzF6cm5HRUhmMXdmdlNTSU56NXhCQk9NVzZHdGtSdC93QmROcmlGRnhpVHk3OCt3V1I5bkM1bXltWk9Hcy9JMFAydENZMGpvNU9uU2ZWNWJUK285N0NweFJoZ3R5WWMrT3ZrWHdWcElnb3d4REhaL25kVGtvZWIwU0E3RTliNllHamVxSFF0dzJlZmhMZEdZWk9yWk1VZEU1bTd3azJxREdJN05meU1jMGY3Tml1enNDeWsrV2JJNnFZMGNDMDYvb2pmNUVqWm11aGlKZDI5L1ZPcVhXMTNhaFEiLCJtYWMiOiIwOWVkMGEzYjZlYjU5NDM3M2RiNDRkN2IzYzEyZDQ3MTE2NGQ5MjhkNzE2YjYxYTdlY2I3MzllODNhZjJjMzE2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:09:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1JbWVVck5UK0g0WXdPZXAxcmZPMGc9PSIsInZhbHVlIjoiNWFOaFUwQTlOUXJZWGYzM0lLR0hkL2RvWWlJclNlVFdYdzU4cFBkWk5GeGtaQ3FMeG9aOStrREZmZHc5b0t5UUROcEVDU1N3YXRHTVQ4bzBJS1F1L1J5RDlORzdheWk3WGVFWVZsVG43dVZoVGlGc0JVM08zYXVJanpTSm5YM1U1NHVBNGkwbG9aeHYya2w1dUo3NjJDVXF0Wm8zaVBmNVJnbVhMNFd5VXB2Q2w0UWRkUzh1dzRzOERQbVI4a2crTlV3RDY5WjE4allzSXBiWG5TNUFJcDgrS1NwN3UxeGtTa3QwUkFKUWhManZxcXgrdTVxNEZuM2x6aFVXaVJ4WDVXenZBOGlWNitQaXJlWlhmZ2lGZk5MQWtFZEJxVGNJRkt3SFA2d3pLSkxKQVdiSFdBb09CaXFLdmxsZTRRd2UvV2tWSUhUdEp5aElGZG56c1hrVFpkQmNLbzNEVHU0NU81TStrMmk5LzBWWDQ2cWZUSlE5ZEUzMjJVUUtHb004UTE4N25VcWR1MW5mdldVUjU2MVJjdXdqRFYwT3ROWVhsN1FLY1Y0WG1IOTlBVVRNSzJXQkJEN3U2eGU3akRIQTZnOGpQaTlHeHY3dktKZDhQTEM4ZFFSTlMydmxNOWRIS0lNVDUxeFhkNVFBVDJHNXpvb0ZwbGJNRWkxZzVJOFMiLCJtYWMiOiJhNThlODY5ZmE4NGUwYzUxZTA0NDc1MzRkYzkzODdhMzJkNTA1ZjEyNzY1Yzk2NjQ3ZDczNDg4YjdmNGU1ZTg1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:09:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1934176722\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-58022827 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58022827\", {\"maxDepth\":0})</script>\n"}}