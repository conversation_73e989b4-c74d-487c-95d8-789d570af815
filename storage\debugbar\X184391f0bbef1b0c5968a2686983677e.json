{"__meta": {"id": "X184391f0bbef1b0c5968a2686983677e", "datetime": "2025-06-28 16:03:33", "utime": **********.367886, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751126612.846129, "end": **********.367901, "duration": 0.5217721462249756, "duration_str": "522ms", "measures": [{"label": "Booting", "start": 1751126612.846129, "relative_start": 0, "end": **********.286783, "relative_end": **********.286783, "duration": 0.44065403938293457, "duration_str": "441ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.286795, "relative_start": 0.44066596031188965, "end": **********.367902, "relative_end": 9.5367431640625e-07, "duration": 0.08110713958740234, "duration_str": "81.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45699776, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02514, "accumulated_duration_str": "25.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3196769, "duration": 0.02409, "duration_str": "24.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.823}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3528361, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.823, "width_percent": 1.83}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.35918, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.653, "width_percent": 2.347}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-868049705 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-868049705\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1753360378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1753360378\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-891953513 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891953513\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1705640299 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126608536%7C17%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlTakdjRGhNSFMza2hleHo1YnpURUE9PSIsInZhbHVlIjoiME5MR1l0UVhCNlNjZVQvdnM4TTJHNFl2a0FvSGd2a29lbFNxaTNIM2Y3OWh5UmNYL2VBcmZMSHJuVmxoQjY5WHAvTlRFZ0Z5azZrS3RqSDNUZ3NFdG9qNnkyL2ZCbzBlTnJac0NVT1Z5VTFoNGVhT0NBZXhrVFdvUEhydkhMT2hwN0hXWkVnUlU2dEgzbGZwR0RKNjk2ZzVXTWlmV1NnRmRUV1RjanlGYk9KS0hrYm9qRTV3THFkcll0bDdqb200R0VjTmRNYXhuQzEwdWVRcDVMZ1ZJMWhuOTIyWDIyZTNiWndTZFJ4ME5OWjFMZ0s0NUZUYkluc09vVGF6cGtmRkRPcFVYU25tYjFxTGpsOS9reUpYaWQzR0k4ZjdheTVONGgrT21EUTE0eTlNYmQ3TFpRa2pIU3h1eXlrZXlqVXl3L2VOVjlwSDdtcGM1RFNQazJYUi9nc0tsd1dmRVRPWEdiczgzd1NVd3A0aU5weWc1dE14am92aG9tOWNyOGtwQzE3T3B1OFJ6cTR5Wlc4b01BaXhYUng0NkFhbmY0U0JyNVN1VmVpbUtlVmlqdlY4OGFIaXo4Z21vVFJTenNPL1BUUDhTRXdkTWxOeENSRGY2Q09XbzYxVmNvakxhcDVGZTh4MVBuazJENFVLMm5kNlcxVU1iL1BWVGVjU1pzN0IiLCJtYWMiOiIwZWViYjA2NTgwMjU0OWM3MmU4YmI5NTAwYTZkMjFhYjJkOTFkNDFiYzYyM2ZkNDljOGQwZGYxNzZlMDFkMDVlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImpReEpmL2tDN25oWG93NjV0U1ZlZlE9PSIsInZhbHVlIjoiZ0svSXU2TnpXcllSaFJGSDJISEY1VHF2Q2xPN1dvR0tMZVJHOCtYY3Z5cGVsaithZDV6Nk1PSXZHWDNSZndtU3VnYm93KzQ4VDM0dmVDbUx2NHhGSlFkUUQ0RlRqR3M1endKRHA1N2Y3N3g5bFdkNWdZYWVYZjVnMCtnVzFob213MGRTS1dkSDFEcDlDeDNjN1JlamJwSHF6aGJVQndnL05LMGFEN1dLeE1jNjR0YncydlloMXAxR2U0REtzWURjOHFtcHJ5VVovcUlmS2xRWnk1cFFLdTJWc0F2NVM1ZEVpdHJyOUFldDMvd2o5dFVpMmtkemp3blE5WnJZT2RnYmc5Rm1UTkY5ZEp1NDMxcnRubUpoSGZ1TC9RSjJuRlBlNGNLcE92T2FERHlLRGt2MmZOSmUxSVBXVkZoRlNJZk1adnRDdkx1TnhaaS9MTUNUNWl2K1Z0WTRlR205ZGUrV3dYK3B3U21JbVkrUWcrNzQ0VEVxNGRkbUhmamVHSDhtajVhOU41TGdYRXExeUZlYjhnL2IwTklSSi9waVV0NGw3SEJnc2FwdytJMkphTFJ1QzdxQzdPcjNRcCs5TUdyS2Q2YjJYV3dQZTNHU1RZOHgzWFhIMktLVFhHdzFRMjVNWk5GZlUvbERaalhGVURoejV5Nk9CNXloVUZXL0hoQmgiLCJtYWMiOiIxM2JkODU5ZDhjYjlmYjI1YWMyYjlmZjQ0ZWQzNzc5YjBjYWViMzc5MjBlMDE5MmVmOTI1MTViNzRmNTcxYmJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705640299\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-636076505 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636076505\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-472456133 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBrMjJTMDhrVVRKS2hFQk9xbVZMRGc9PSIsInZhbHVlIjoiRzNHajk3em1wdEp2NzBsS2NKczNpWm1FSVdvNnl4ei9aTWdYaXJTZEdsN3lzaWlTT1RPb0RuMCs1NlpqTDFQeGp1dWJ5a3krTGpsVkF0WStDbUtSbFphQVZUQnc1WEUvZDhRMmNVdXRGSW1mNUtnd1R2QmkreFhOZnVTTTd5VS9uVm9wS20weUd5S1Jsc1AxdHBNc2lJbmlmcEo3QUFTckUzZjY5VG81VWFmQWdKYmZhLzNHdlVrdlRuTHhua2Y3NjZMMWUrRXd1QjhQVVBFUkkrTWt2NitLbzFhUSs2bldwaW1icHNHVlA1eDEwTlZmcmgzSHhveXVzSXI0dEJIeVJPbTJSQitXeXhNalFCbUE2TTVQRDV2SmZCVzA3RXZzaEdKSGgzU2c4bUkxMm42aU1EUXQ4UFZMY1NaT2N4TzlNNys4cUVoZVQ4WEJOTkxHaVBibWJXVWpIeDl5ditTekFDbmVkMXgxVW9HdEtybU4zTlpEY2QxdisxeERSUm5zQ250YmUxWExFdXI5cnFXQUhjVHY3QXh3SWZHOW1aQjBRMHdNNTBLR05IYUhwOG9lOFR3ckZrSGVpU1hISjYzdCtiT3U3ajVTeXdzelBZa1Vta0tTWUQ3aVFZZ2hVQ0lUVUI4Ung2cU9hdFEzNS9YdTBCenBldC9yaExtT2hieVEiLCJtYWMiOiIzYzRiNDVhZWI5MDRkYzIzNGQ4NmE1ZmM4OWIyZjEyNjY3YWVhYmE5NGMwNGMwZDZlZTRmODEzYjE1Mzk2OGNhIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkhPQ2RYaDNUcUp5RFVOZUoyaUlzdWc9PSIsInZhbHVlIjoiamxnWCtZSnBUNmthRzFZdHpQck14NzYrNVNsU3RsUFcwcitKNFRaNHY4MWJEYTJEdDJxRnArZ2JRU2ZoS3RrSS96U0RGakFJcGZSTC94d2JrcWJvb0lXaUpJVDB5Wmc3aDBHMzJkcWI1TWNYZSt1TWFqK01BeFRGT3hvaS9iSVZib0VaNzAweUwvNlczcHBEelZnYzYzTXd3UXdxcFRKLzU4QnlWMTZoRTc1Z2ZyYTJhZGdUWmxrcEhVQ2pmRWVCYU9EanZUbzZTbk84L1VVckRUaDRzbisvRlh2Mk1ZTUhabmwzV2VEaVZwVlhVRWRXem1ZL0YxR3JIYitTV25NU3IzZ29BQUJ3M0F0dmJCckZjN0xlY3hlZkJYU28yVHBTRHE2Z1BkVldrM1lNcS9TVi9Jakx1MEYya2ZRUVhGVWowK093VldpWk9NVmFrd0o5Y0l3QTlRd3hOTEgrR2ZJVVBTYVBZcmttd3FpKzdEOU9JczZIdDNYdXUzQWp6MTdwVHFlaTRrLzk4dnhDeUdDUTlJOHcvRUo0VEpyWTROaWdGRFpHMHZLblEvZE1RbU5sS2JjSzd0M0pUWHMrMW0zMHdxMVo0b1RFZ3JOc2hQOS9XYTc0dEtiVWlHYm9temFuczUwWTgvYVg2d3lPa2hpZ2dYSDVDcHJwZzcyRVZPejgiLCJtYWMiOiIyNzIzODBhNDg0ZjE1YjkxZWYzN2ZlOTg5YWMyZmUzNzAwNWNiOTZjYWQ0NWZiMmQxY2NkODQ0MmI5YjI3OGMxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBrMjJTMDhrVVRKS2hFQk9xbVZMRGc9PSIsInZhbHVlIjoiRzNHajk3em1wdEp2NzBsS2NKczNpWm1FSVdvNnl4ei9aTWdYaXJTZEdsN3lzaWlTT1RPb0RuMCs1NlpqTDFQeGp1dWJ5a3krTGpsVkF0WStDbUtSbFphQVZUQnc1WEUvZDhRMmNVdXRGSW1mNUtnd1R2QmkreFhOZnVTTTd5VS9uVm9wS20weUd5S1Jsc1AxdHBNc2lJbmlmcEo3QUFTckUzZjY5VG81VWFmQWdKYmZhLzNHdlVrdlRuTHhua2Y3NjZMMWUrRXd1QjhQVVBFUkkrTWt2NitLbzFhUSs2bldwaW1icHNHVlA1eDEwTlZmcmgzSHhveXVzSXI0dEJIeVJPbTJSQitXeXhNalFCbUE2TTVQRDV2SmZCVzA3RXZzaEdKSGgzU2c4bUkxMm42aU1EUXQ4UFZMY1NaT2N4TzlNNys4cUVoZVQ4WEJOTkxHaVBibWJXVWpIeDl5ditTekFDbmVkMXgxVW9HdEtybU4zTlpEY2QxdisxeERSUm5zQ250YmUxWExFdXI5cnFXQUhjVHY3QXh3SWZHOW1aQjBRMHdNNTBLR05IYUhwOG9lOFR3ckZrSGVpU1hISjYzdCtiT3U3ajVTeXdzelBZa1Vta0tTWUQ3aVFZZ2hVQ0lUVUI4Ung2cU9hdFEzNS9YdTBCenBldC9yaExtT2hieVEiLCJtYWMiOiIzYzRiNDVhZWI5MDRkYzIzNGQ4NmE1ZmM4OWIyZjEyNjY3YWVhYmE5NGMwNGMwZDZlZTRmODEzYjE1Mzk2OGNhIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkhPQ2RYaDNUcUp5RFVOZUoyaUlzdWc9PSIsInZhbHVlIjoiamxnWCtZSnBUNmthRzFZdHpQck14NzYrNVNsU3RsUFcwcitKNFRaNHY4MWJEYTJEdDJxRnArZ2JRU2ZoS3RrSS96U0RGakFJcGZSTC94d2JrcWJvb0lXaUpJVDB5Wmc3aDBHMzJkcWI1TWNYZSt1TWFqK01BeFRGT3hvaS9iSVZib0VaNzAweUwvNlczcHBEelZnYzYzTXd3UXdxcFRKLzU4QnlWMTZoRTc1Z2ZyYTJhZGdUWmxrcEhVQ2pmRWVCYU9EanZUbzZTbk84L1VVckRUaDRzbisvRlh2Mk1ZTUhabmwzV2VEaVZwVlhVRWRXem1ZL0YxR3JIYitTV25NU3IzZ29BQUJ3M0F0dmJCckZjN0xlY3hlZkJYU28yVHBTRHE2Z1BkVldrM1lNcS9TVi9Jakx1MEYya2ZRUVhGVWowK093VldpWk9NVmFrd0o5Y0l3QTlRd3hOTEgrR2ZJVVBTYVBZcmttd3FpKzdEOU9JczZIdDNYdXUzQWp6MTdwVHFlaTRrLzk4dnhDeUdDUTlJOHcvRUo0VEpyWTROaWdGRFpHMHZLblEvZE1RbU5sS2JjSzd0M0pUWHMrMW0zMHdxMVo0b1RFZ3JOc2hQOS9XYTc0dEtiVWlHYm9temFuczUwWTgvYVg2d3lPa2hpZ2dYSDVDcHJwZzcyRVZPejgiLCJtYWMiOiIyNzIzODBhNDg0ZjE1YjkxZWYzN2ZlOTg5YWMyZmUzNzAwNWNiOTZjYWQ0NWZiMmQxY2NkODQ0MmI5YjI3OGMxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-472456133\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-983377618 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-983377618\", {\"maxDepth\":0})</script>\n"}}