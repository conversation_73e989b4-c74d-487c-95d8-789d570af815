<!DOCTYPE html>
<html>
<head>
    <title>استكشاف أخطاء Enhanced POS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .highlight { background: #ffffcc; padding: 10px; border-radius: 5px; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🔧 استكشاف أخطاء Enhanced POS</h1>
    
    <div class="section">
        <h2>❌ المشكلة المبلغ عنها</h2>
        <div class="error">
            <p><strong>رسالة الخطأ:</strong> "Manual products are not allowed in Enhanced POS"</p>
            <p><strong>الوصف:</strong> لا يتم إضافة أي منتج للسلة حتى المنتجات العادية</p>
        </div>
    </div>
    
    <div class="section">
        <h2>🔍 التشخيص المنجز</h2>
        <div class="info">
            <h4>التعديلات المطبقة:</h4>
            <ul>
                <li>✅ إضافة تسجيل مفصل في الكنترولر</li>
                <li>✅ تحسين فحص المنتجات اليدوية</li>
                <li>✅ إضافة تسجيل في JavaScript</li>
                <li>✅ تعطيل فحص المنتجات اليدوية مؤقتاً للتشخيص</li>
            </ul>
        </div>
    </div>
    
    <div class="section">
        <h2>🧪 أدوات التشخيص المتاحة</h2>
        <div class="highlight">
            <h4>صفحات الاختبار:</h4>
            <ul>
                <li><a href="{{ route('simple.pos.test') }}">اختبار Enhanced POS البسيط</a></li>
                <li><a href="{{ route('debug.enhanced.pos') }}">تشخيص Enhanced POS المفصل</a></li>
                <li><a href="{{ route('test.enhanced.pos') }}">اختبار Enhanced POS الشامل</a></li>
            </ul>
        </div>
    </div>
    
    <div class="section">
        <h2>📋 خطوات التشخيص</h2>
        <div class="info">
            <h4>للتحقق من المشكلة:</h4>
            <ol>
                <li><strong>افتح صفحة الاختبار البسيط:</strong> <a href="{{ route('simple.pos.test') }}">simple-pos-test</a></li>
                <li><strong>انقر على "اختبار إضافة منتج مباشرة"</strong></li>
                <li><strong>راجع النتائج:</strong> هل تظهر رسالة نجاح أم خطأ؟</li>
                <li><strong>تحقق من السجلات:</strong> راجع ملف <code>storage/logs/laravel.log</code></li>
            </ol>
        </div>
    </div>
    
    <div class="section">
        <h2>🔧 الحلول المقترحة</h2>
        
        <div class="warning">
            <h4>الحل الأول: تعطيل فحص المنتجات اليدوية مؤقتاً</h4>
            <p><strong>الحالة:</strong> ✅ مطبق حالياً</p>
            <p><strong>الوصف:</strong> تم تعطيل فحص المنتجات اليدوية مؤقتاً للسماح بإضافة المنتجات العادية</p>
        </div>
        
        <div class="info">
            <h4>الحل الثاني: تحسين فحص البيانات</h4>
            <div class="code">
// في الكنترولر
$isManual = $request->get('is_manual', false);
$isManualCheck = ($isManual === true || $isManual === 'true' || $isManual === 1 || $isManual === '1');

if ($isManualCheck) {
    // منع المنتجات اليدوية
}
            </div>
        </div>
        
        <div class="success">
            <h4>الحل الثالث: التحقق من البيانات المرسلة</h4>
            <div class="code">
// في JavaScript
const requestData = {
    product_id: currentProduct.id,
    name: currentProduct.name,
    price: currentProduct.price,
    quantity: quantity,
    tax_rate: currentProduct.tax_rate || 0,
    is_manual: false  // تأكد من أن هذه القيمة false
};
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>📊 حالة النظام الحالية</h2>
        <div class="highlight">
            <h4>الإعدادات الحالية:</h4>
            <ul>
                <li><strong>فحص المنتجات اليدوية:</strong> <span class="warning">معطل مؤقتاً</span></li>
                <li><strong>التسجيل:</strong> <span class="success">مفعل</span></li>
                <li><strong>خصم المخزون:</strong> <span class="success">مفعل</span></li>
                <li><strong>الكميات السالبة:</strong> <span class="success">مسموحة</span></li>
            </ul>
        </div>
    </div>
    
    <div class="section">
        <h2>⚡ اختبار سريع</h2>
        <div style="text-align: center;">
            <button onclick="window.open('{{ route('simple.pos.test') }}', '_blank')" 
                    style="padding: 15px 30px; background: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;">
                🧪 تشغيل اختبار سريع
            </button>
        </div>
    </div>
    
    <div class="section">
        <h2>📞 التواصل للدعم</h2>
        <div class="info">
            <p>إذا استمرت المشكلة بعد تطبيق الحلول:</p>
            <ul>
                <li>راجع ملف السجلات: <code>storage/logs/laravel.log</code></li>
                <li>تحقق من وحدة تحكم المتصفح (F12)</li>
                <li>استخدم أدوات التشخيص المتاحة</li>
                <li>قدم تفاصيل الخطأ مع لقطات الشاشة</li>
            </ul>
        </div>
    </div>
    
    <hr>
    <h3>🔗 روابط مفيدة:</h3>
    <p><a href="{{ route('pos.enhanced.index') }}">Enhanced POS الرئيسي</a></p>
    <p><a href="{{ route('simple.pos.test') }}">اختبار بسيط</a></p>
    <p><a href="{{ route('debug.enhanced.pos') }}">تشخيص مفصل</a></p>
    <p><a href="{{ route('dashboard') }}">لوحة التحكم</a></p>
</body>
</html>
