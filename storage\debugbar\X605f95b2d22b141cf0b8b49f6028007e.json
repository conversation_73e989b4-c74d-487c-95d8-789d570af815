{"__meta": {"id": "X605f95b2d22b141cf0b8b49f6028007e", "datetime": "2025-06-28 00:36:45", "utime": **********.960546, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.453389, "end": **********.960569, "duration": 0.5071799755096436, "duration_str": "507ms", "measures": [{"label": "Booting", "start": **********.453389, "relative_start": 0, "end": **********.808726, "relative_end": **********.808726, "duration": 0.35533714294433594, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.808737, "relative_start": 0.3553481101989746, "end": **********.960575, "relative_end": 6.198883056640625e-06, "duration": 0.15183806419372559, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45612928, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00265, "accumulated_duration_str": "2.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.922375, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.66}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.934333, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.66, "width_percent": 20.377}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.940561, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.038, "width_percent": 13.962}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1510548451 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1510548451\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1769732018 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1769732018\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1446143041 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1446143041\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1775719736 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2817 characters\">laravel_session=eyJpdiI6IjE5YmxhMUw3bXJQWWh3R0habDVwcFE9PSIsInZhbHVlIjoiTkZUZHViY3FpMFMvWUgzVm1OV0JwUHZoMVU1WjA5ck5UbEh4NjBRemY0ejlwWVNadXBvOWxTWVFjQzJMNGxNSTc5NzJwa1dxQ2hqL2VlQUZvMUs4YnV3Z2RhaExoU0p1RE5pdlhQQ1dzN0RpS0xnTWxrcmEvVFZUZERCSnFDQlhEdG03UkROakJlb2huQ24xSWlPcTA0QnpwdnNZaEl5T2JnWkpNMHNSVGpUeGZpWHJNcDBBWVFmaHE4Z0dkYmFlTm5VRWR4YWlaOHNZa2hPaDh2RGdndHB2MVJLZEFZMUU4Nmttek5WOEhIOExCS0p2S3h1Mm83MFRQc1U4NHMzN2lsY1J6ZnNLdVBhOXVCbktKdkNNdUQrcGJHZm9sUDkyRWhHVW90U0twWWw3Q3d1VzlyYzgxWjY2U2MxWWdnOXhaUEJkK0k5dFU1dWEvaDNXNmZtMkY2WUJKdVZSNnlFYVF0Y0JtL3Fac0tRZ3dyWDFwVXNSdmNhNzIyVjBiSHpQbDM1OXBiL1R6WndGT25IMnFYV3ZBNkdsNjB1SU54NXRIUWJBdTM1eDBiditjVzlWcW1DQlVTMkgzSmw3S0xYdDJmRVI4MXNEaENKc1dTbFRMR1pPWUVZc1crMkY4QnNxMDl2eEpvclhUb3hhMjZqYnFqaGVGTEdOWWxSZzBXNUUiLCJtYWMiOiJhMmVhNTI4OTM2NDBkYzJlNTU0MzRmNWUxYTg5YWQ0OGIxNGMyMmZhYzM5MmYzYzllZGEyNWMxZTVjYzdhNWUyIiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=zkpa4v%7C2%7Cfx5%7C0%7C2004; _clsk=9vvwq3%7C1751070959457%7C28%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkgwZHdVWWM0NTFSVE53VWlYWHpPTGc9PSIsInZhbHVlIjoiaDVwWG9mRVdoMCs5NllvcXVTSFVkZXpaZEF1enZYYThmYWFGYWRWc05pWW9XcktuajFsd1dUY0JqQ0I1MzBlZXJ3UmFOY1UxSWxkVXVzK0lQcEdTc3VsdU8wdllLYzNidCtvYlFVaEtFS2Znblc4eDJmVHpMVnFXRERNK3RvRjdiVlRiaUZoYzZtUjBrQi9sbWZkcm1teG82MGkwNnZseWJxUWo2VkVPMlN6bCtMYmtXSVBBTlI0RlE5R0oyWlNubG4zc1Q4Wk01dEhUTTlFck5pckN3WTRRbHVvQkpSWUoxcVdBRFoyVXAwT3VXaGJ3cUt3a1NScFlScjkzMFc4QUdnbGlJR0Z0bHZRd0lZUXVDdVhsQ05YL2gxTFk0Wmt6amh4ZjNCdElwZzA3SFR4ckVoZkhxU1J5SWZOYVhnZERnV3pibm9JNTVLcktkek9LckdwZzRydkpzazJ6RXBiS25uOC96a0ROSHZKa2dhOHVsbDg4dFgyT25sVTZnUThrNE54ekhLUFZlQW1HM25LOVhMV1diRU1pWFpaSzJwU01NUHV5ZDFqaFBPQlFaLzJ3cExKRFJlVUZzS3BvUFprMHBkUXZtUkowWXlCTTZCV2JwOW1scnFITkIwNGxrbTIrZ1Z4eFFScVcyZmQ1a0xUdFFlU3c1RHFnK1pCWStnOUYiLCJtYWMiOiI4YzgxZGEyY2VhMzlkZDM0ZDJiZjU1YTQwZjQxNWJiNWRjNTUyNmRjYmFjMDgzMDk3ZDZhY2M5MzkxZWRlNTA2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpYUy9FQ0QxcCttc1VUMmFrWVFodmc9PSIsInZhbHVlIjoiTXpmdmhYYk5aOEtLOFBVQ253ajl1OTdKbTh5VUhYTktMSjB2QmtVMTVQKzRKdXlrOWVtYkZxc0o4Z1NqNENURFBOYjd3UlFVbjZPNDh2dzNZLzZPYmtQMnVPNXR4NFJNV1NUK1ZWaWxjN2JtVjhSYlZrL0ZBa0VLK3lDMTlOWE5CMzBTc2FTUDl5OWpBaUs2WUE3b09Vamx1bW40bGpiSkNMdkZKUHhMcW1sUzBLd0xtazZ3bjVQSXZxd2dsL0Y1QUpUM3c0YUdmWDdQbHVuTldsYmFhZWhRbnZHM3haS1RRR1NjV2VQdVAwSFlBc3FuTTByN2JsUFpEaTlWYUVRRHFkS1RJSEMrcjRaUU5WRnpOYTI5dndRTmxrSGRuRGRKZXg0V2JoS0JvVkdSZzlTOC9KODhiaUFYZlBwZzlnNVR1Z1EzNVdvL1dXOHczV0crTnlOTUtTL21CYUh2bkU0bHdDTkFndDhUNEx2TTNRdVFJL2hSd2ZGNDNYRm1RU2hTbjIzcFhmZ1M1SWlaOHJpaUVjK3hiWGJlaTNXREU5VnlOczVVaHZ2cWk5TlJVRlhOcmd6SXF0aVpSYlg3dEttc1FtVC8vcU9yRWtDS2d5di9ZcVV0MnFRcmVKaUNyaVJZZDJMVUNFanNCMmFVY2R0R3NMUDRWZDBwUnI1T2pKWGwiLCJtYWMiOiJmNmY0N2ZiZDNmODZiNTJmYTk3NzgyYjY1MDMxNzk1ZDMwNzYzMzM1NjU4MzNiZDQ4NzA0YTQxNzZhMDc5NjIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775719736\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1097735885 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">chEs2sQpmg2fYpUuqAOS1ZcIPLmVIQdx921QTw47</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9XNo0PIFsccsEXzvfQLAUsci7MaweQoaRcTvbnBl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097735885\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1455083505 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 00:36:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpqTUR5dEl1NHcwY2hsOUVqeFBFRVE9PSIsInZhbHVlIjoicld2TE0yYlphblg1aXNYNFBzT0VkRVpyYlo2a3lqdFhBL25uTTNFb2dOVFMxeko3S2ZSekhqUkdEQkQ4SlFYWnF1ekE5dzBRaHJSdFVONVhzQXMweFFZeG5TQ1h0MkJyTkVUMzZoRjNNZDF0TUdQbVRiQXBxUUNVWXlsampTZzRjQlk0MDJhSk92U2VIc284Y2gxUHkySE5UbFBoUlo3M2I0RWF2L1ozaktSTStoM2wwNFJ5bDVxb2Zmam5vcEVueHRuMm1QbFZBTGF6ZXBjWXpxV2RJUEJGeHNTcnNMb240OXpoUzZwV0ppbVc2NlNXcElrYlFvVktkKzdjMkQwWGhPS2MwQ3cxcHdDL0RSdmpXWnpaVUEyWHQ4SVhmcXBWUWVpNlhrMUppQ0VuTHd0VnZlQ2NJNmU2Qjk3L3A3VFhIeGdGbkdvWDUyK0Jud1ZlcFk4TmZ4VnBNR3pSeEpVSkJDay9xUUtQN21xUE1TTU9Ydml3Zjlwclk3T1Z5RHZaVnI5bUQ3REt6cVlZbnFoWVBHRDhSeUszUlJNRVdROU00UHJrMGVLUm5nZVEzeXI3ZlQ5SmtWODdMVEhnRlN0NVRZczgzZFkvTTZKWlFJSFZ0Zjhqb1JBT2xiMU9jcTBwaW1EWVAwTHU4UmY3VEJHZmJzUVZieTRGdTEybVU2amoiLCJtYWMiOiIzMzVjZTZkODJhM2U1ZmY0NzIwMzg3NWQ0MGViYWM3MWUyZjRjYzkyNzE5NTNiYzM4OTNjYzU3ZjI2ZmYzMjNmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:36:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZac0JldGNLWk5NWmFIT3VpTEx6TXc9PSIsInZhbHVlIjoib1dxV1k2cTB0cWN3U2FSN1dBWXlyODFVMm1TZE43S2NtcFBxMTM4dWpyaGtWaDVlN0hRMWl3NXRlSUpqd2I3T3lmR0tVNExpUW4zOUtlaG1FcU5BVjRFOUl4TXlWelVzQUVZc3JDNG1Ub3dvaCsrMU9iUEc3UXN3eFhoeUhPck5ib2F3MEI4eFRobTY1RWRhRWZISVRhNzBsWFNMMUlPOS9jdFdXNk9sVnpMaGFhZGJ4bHd1eVRqTG1aS0JXWGhlQUR6L2NzdVM4US9pZTBVVkxmdVRxNjMwVjFUL3BSN2t0ZUxaNlk1WlV2anA1UjlMMHpMOTFOQzhGM2tUakJ3MGIzdk10UjlYSUpycEJmMXFSUVVuMmZYT3lNUFViL3lRcVJvOTBOZ2dXQWp4eG1RcnRDMWx0UVh1eThVdy9XL1R6N3BBYWNPZ1VOY2FyVmFwSExFdUtZcGI2RlRTQTdpTDZ4M3ArK0ZDSXcvUHJJcEh3T3FGVTNFaHdsemF2OWR5Q1hPUUFUNGovdlZIL3A4VXZQMjRvbWQ0ZkFTMS9jcmRZOTk1ZUp4OFhwUmpNUlVSZUp4QjNnaGdXLzJWZUdpdlpnNjhVVkY0bGtPN1lZZk9DSUtXS2MyMng5KzVUZXZ3WHpCVHZXdDFwT1BMdmZHWTNDVC9Ia0tyWmtZS3llSVoiLCJtYWMiOiJlZDQ4N2VjMWRjZmE4YjQxNjBkZDcxM2JiOWY1ZGRkZGE3MWZhOGNlMTEwYzU0Njg2NGY3MzhlZmZhMjEwZjQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 02:36:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpqTUR5dEl1NHcwY2hsOUVqeFBFRVE9PSIsInZhbHVlIjoicld2TE0yYlphblg1aXNYNFBzT0VkRVpyYlo2a3lqdFhBL25uTTNFb2dOVFMxeko3S2ZSekhqUkdEQkQ4SlFYWnF1ekE5dzBRaHJSdFVONVhzQXMweFFZeG5TQ1h0MkJyTkVUMzZoRjNNZDF0TUdQbVRiQXBxUUNVWXlsampTZzRjQlk0MDJhSk92U2VIc284Y2gxUHkySE5UbFBoUlo3M2I0RWF2L1ozaktSTStoM2wwNFJ5bDVxb2Zmam5vcEVueHRuMm1QbFZBTGF6ZXBjWXpxV2RJUEJGeHNTcnNMb240OXpoUzZwV0ppbVc2NlNXcElrYlFvVktkKzdjMkQwWGhPS2MwQ3cxcHdDL0RSdmpXWnpaVUEyWHQ4SVhmcXBWUWVpNlhrMUppQ0VuTHd0VnZlQ2NJNmU2Qjk3L3A3VFhIeGdGbkdvWDUyK0Jud1ZlcFk4TmZ4VnBNR3pSeEpVSkJDay9xUUtQN21xUE1TTU9Ydml3Zjlwclk3T1Z5RHZaVnI5bUQ3REt6cVlZbnFoWVBHRDhSeUszUlJNRVdROU00UHJrMGVLUm5nZVEzeXI3ZlQ5SmtWODdMVEhnRlN0NVRZczgzZFkvTTZKWlFJSFZ0Zjhqb1JBT2xiMU9jcTBwaW1EWVAwTHU4UmY3VEJHZmJzUVZieTRGdTEybVU2amoiLCJtYWMiOiIzMzVjZTZkODJhM2U1ZmY0NzIwMzg3NWQ0MGViYWM3MWUyZjRjYzkyNzE5NTNiYzM4OTNjYzU3ZjI2ZmYzMjNmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:36:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZac0JldGNLWk5NWmFIT3VpTEx6TXc9PSIsInZhbHVlIjoib1dxV1k2cTB0cWN3U2FSN1dBWXlyODFVMm1TZE43S2NtcFBxMTM4dWpyaGtWaDVlN0hRMWl3NXRlSUpqd2I3T3lmR0tVNExpUW4zOUtlaG1FcU5BVjRFOUl4TXlWelVzQUVZc3JDNG1Ub3dvaCsrMU9iUEc3UXN3eFhoeUhPck5ib2F3MEI4eFRobTY1RWRhRWZISVRhNzBsWFNMMUlPOS9jdFdXNk9sVnpMaGFhZGJ4bHd1eVRqTG1aS0JXWGhlQUR6L2NzdVM4US9pZTBVVkxmdVRxNjMwVjFUL3BSN2t0ZUxaNlk1WlV2anA1UjlMMHpMOTFOQzhGM2tUakJ3MGIzdk10UjlYSUpycEJmMXFSUVVuMmZYT3lNUFViL3lRcVJvOTBOZ2dXQWp4eG1RcnRDMWx0UVh1eThVdy9XL1R6N3BBYWNPZ1VOY2FyVmFwSExFdUtZcGI2RlRTQTdpTDZ4M3ArK0ZDSXcvUHJJcEh3T3FGVTNFaHdsemF2OWR5Q1hPUUFUNGovdlZIL3A4VXZQMjRvbWQ0ZkFTMS9jcmRZOTk1ZUp4OFhwUmpNUlVSZUp4QjNnaGdXLzJWZUdpdlpnNjhVVkY0bGtPN1lZZk9DSUtXS2MyMng5KzVUZXZ3WHpCVHZXdDFwT1BMdmZHWTNDVC9Ia0tyWmtZS3llSVoiLCJtYWMiOiJlZDQ4N2VjMWRjZmE4YjQxNjBkZDcxM2JiOWY1ZGRkZGE3MWZhOGNlMTEwYzU0Njg2NGY3MzhlZmZhMjEwZjQ4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 02:36:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1455083505\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-90447931 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZa6gepJ3oq7CLDa7mTXBMptQFWCOM3OuOeJlhGk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-90447931\", {\"maxDepth\":0})</script>\n"}}