{"__meta": {"id": "X22e715426298693134ed2ce9e601bf1a", "datetime": "2025-06-28 16:03:54", "utime": **********.666953, "method": "POST", "uri": "/enhanced-pos/add-to-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[16:03:54] LOG.info: Enhanced POS Add to Cart Request: {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"price\": 2.99,\n    \"name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\",\n    \"is_manual\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.658165, "xdebug_link": null, "collector": "log"}, {"message": "[16:03:54] LOG.info: Manual product check passed {\n    \"is_manual_value\": \"false\",\n    \"is_manual_type\": \"string\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.658326, "xdebug_link": null, "collector": "log"}, {"message": "[16:03:54] LOG.info: Product found successfully {\n    \"product_id\": \"2299\",\n    \"product_name\": \"\\u062c\\u0627\\u0644\\u0643\\u0633\\u064a \\u0643\\u064a\\u0643 \\u0627\\u0644\\u0643\\u0631\\u0627\\u0645\\u064a\\u0644 30\\u062c\\u0645\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.660405, "xdebug_link": null, "collector": "log"}, {"message": "[16:03:54] LOG.info: Product added to Enhanced POS cart successfully {\n    \"product_id\": \"2299\",\n    \"quantity\": 1,\n    \"cart_count\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.660515, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.27886, "end": **********.666972, "duration": 0.38811182975769043, "duration_str": "388ms", "measures": [{"label": "Booting", "start": **********.27886, "relative_start": 0, "end": **********.61327, "relative_end": **********.61327, "duration": 0.3344099521636963, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.61328, "relative_start": 0.33441996574401855, "end": **********.666974, "relative_end": 2.1457672119140625e-06, "duration": 0.05369400978088379, "duration_str": "53.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46014088, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST enhanced-pos/add-to-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@enhancedAddToCart", "namespace": null, "prefix": "", "where": [], "as": "pos.enhanced.add_to_cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2015\" onclick=\"\">app/Http/Controllers/PosController.php:2015-2120</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00239, "accumulated_duration_str": "2.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.645458, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.109}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6559212, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.109, "width_percent": 18.828}, {"sql": "select * from `product_services` where `product_services`.`id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 2053}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.658926, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2053", "source": "app/Http/Controllers/PosController.php:2053", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2053", "ajax": false, "filename": "PosController.php", "line": "2053"}, "connection": "kdmkjkqknb", "start_percent": 84.937, "width_percent": 15.063}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/enhanced-pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "enhanced_pos_cart": "array:1 [\n  0 => array:7 [\n    \"product_id\" => \"2299\"\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"price\" => 2.99\n    \"quantity\" => 1\n    \"total\" => 2.99\n    \"is_manual\" => \"false\"\n    \"tax_rate\" => \"0\"\n  ]\n]"}, "request": {"path_info": "/enhanced-pos/add-to-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126629815%7C23%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRzYmFSV1RrWXU5K052TFNScVNWZ1E9PSIsInZhbHVlIjoidTVveDJBZ3RmK0p0OG9sbkMvVEVaUkIraVh1RktlQ05KMWduSWR0dkVoWW5mYVBTWi9DSWU2Z25XNDkxdG50Y1JrT0xCVjhDeWR6MUJqZXNsblVGSW9sRVUwK2RZdzBPWDF6YVRsa1lyS0pwWHlhdnp2OHF1Y01vY1gzTnVpeTdFaXg0M2UybjJ1dXNwZTFQRkRlTHhpY2k0dWFJaVZZMExCSXQ5QnJXeVZRTHRJZ3l3VEtaTTRzMElPUFdlRDFRenorQWJRYTNOYjBFM05kYVVqbHowdG44WUFwVmhIajhvY3RMRG5CSnVQSzV5aU1KOVRhYzRrSzdZUER0SEpwMjYxN2tLVVFFa2lrVG84anR6UngyZHBzY2dabG5LbkpCU0FBRnU2MXlLVjN2endneDNub0l3SnNic25nSWtqRmF2SFlvMlo1dHJvZklHbTBxcy9lb2tTRU9CN296enBlY0JaRjBkcVpuSjhKNU1LU2h0eUJXRlJVekJQdTNkTEY5LzlLbHVBRUZTVE95VzBrTTNHT2hUTnJYZnM2TkNFMGJJdmxHVG9FYndtZHVsbmRlSEF3ZlJSWlNZREJBdmdjc251WFVxOGxvSUk4OENPZkp3V3dSMlgvV3FSZVFTajRpQUpibzFBdjByMUpaMzdJMGJmL1NxWFVCYlFOUGVsbm8iLCJtYWMiOiIyMDBiMjZhYjcyM2YwODBjMjM1YjJkODFhOGJhOWY4OGQ1M2ZhNzI5MTEyZWM1MDFhNjQ5YTliODczOTJhZDhmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik02NWtqQzZyWm9xdHlSbFZTT0ZndEE9PSIsInZhbHVlIjoiYVFKall6ZXJLR0hXbFVibVB2SnRCeW5JZDFnWEQ3QUtlY0JsNHdENXI0RVNNb2RqbDU2VDVpY2MyRkErbDYvU2krWElVRm9tYy9NV0Q1dDNKZjkyM1J5U1QrMjA5V1FFc1R4T2x2MkhqdzViK2l4a0RLa3kvaU0reE1vRFM2RVMxUzEwdUZ0ZFRZenRPOU9mSjdBY0ordlY2Z2d0ZEl2MnRtUTZ4dlJ4VVMzWUdFazNOT2ZFNnF1SDg3S0NCRVk4eWxuYzYyZi9NdFUzUXBPYklUWVJHMURIVWhxbTlIdEQ5dDJ1SzUzWCs4MDgvVkZ5UC9MVnFKZk1hM1lPbDVSZkJQdWowMTFKT1llMnVNamJ2ZHBJMUlNZmNJMmNUS1gzZTB3dFpqZ1JnUmNtTUFETE1NcXBrQkJoY1dtWFVCVm82eThER0krSlZNeHhSbHVwNFV4MElPbzI0d01WN1Z6TEdpTW12U1JVYWxTM2RMWlBraU9CdEQ4VmM4ZUJ3UmdvSllaaW81aUNMVFl1L3BONGpYODBCVlFLRkRkUUNQMEE2VDArSzBQOUVjdGxQNTVKdzJ5bXdWNVBGQXJQTTVHSXcwVG03TU9XNlBVWU1GTnR2eVpNNnVsalYxSS9VYmptVVUwREJMamtuTVFMUDBNeGFXT1p5MVFvMTZ0dnFjbTIiLCJtYWMiOiJmY2Y3ODFhMTNhYWE3ZjMyZTBjNmUwYzczODM1Yjc2ZDIzNzU2NzI5ZjA2Yzg1NTc1ZGM0YzM0NzA1NDliNWQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-346643730 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-346643730\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1208335778 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJtUHlOMndMNmk2Z3gzcHZ6K0g0VkE9PSIsInZhbHVlIjoielVwNGEvSjdZY3RVTzBCa1hOU25VaHVqc0k2WlRkMjNvZUFTU0g0NSszb0svMWVKcnRRTlhza1NtT3Q4S29aRmowY3o0dmZGVWYvYUdJOENiUmQ3eCtlZWdTTTIxcXhNYlMwUUdvWDFrbXZZTjZEeW0rOFl2MldERlNScTRwQitLamYyQy9JYS8rRkUvVGE0aUNRMklzMHErODYxcTRmN0xtbmRPN0VuM1RLZWJQeExoUERyWmZhTmhuMTJnT20wa2VUbEh3bkxITzFMQm5WaXA4VVpnaGgzUlF3ejhpcDZXemw3RVNQdDFJTmJhbjJvaVdyd0JTNlhTLzVpZ0trd0t1TFhxWnJUMXNScU9GQlhobnZjbWttWnFHaytCRHVhRTlKdklvQ2R2anZPTGxlTDcyYjZVZklGbG45SGhmbjBWaTNpT2JvK3dJa2VTMlhDbysrTG1naGJwajNoYmRXaG43TVBkS05aVGxlOHJySFhmaEg1bHRrVUdsejExbjd2NzhMZFNvLzdsSWR3TmNLUWJTM3JYLzg4ZTVIeXVUVEM4cC9wS0V1cGFLOUgxbXBzM3NNQWhCcGhYbUU3OTBGbTlJWnpiZkR6TGZNMHE5VHBNakJRbko1ZGJLUDlhdHA3UXpybTFBM281VnlPZnFCMlc4UDl3eGlmbTRmb3BMaXciLCJtYWMiOiI2ZjVhYzQ3MWQxYmE0MDhjOTZiMDMzMGUyOWY0M2IzOTM2ODIzNjdhMmE2YjVlZWQzYjdlN2QyZjJiNmZiYzk4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1qRkFWWGVjbTFDSlJWUWNDeG5HMWc9PSIsInZhbHVlIjoiOHBMVXZDOEJmSnJrKzdmRHErZ25ORkR4M0FhWktxWmFyeTFsZ2VWU2pJS0l5QU9KNGVpemFld2xuY2k4L3p0Qk1WV1FWaUJ0NHNKUnBHME9wS3BlRlZKa1lLNk90cEozV1Z5bjhNakRyVmlHb3JXVjdtQllXdDBHckIxc2RyK1BOb3VXQmR3aFNvTmpWMStRK2tUNkZnK0RXbjBBSXVCalZFbnhqb29yUU8zN1RhUzVrakRJaVFETitzaTFnZCtZSndYWkkvc2VCcm1CVHU4UUxMSFBZaTFMT3VHZ09LQlBkQzBBWWtqNERMQnRTNERqWDg3TUdXOGxDdS9pV25wUTM2WE54YzRuNmdwNnNiRTI4TlpiTU8vMk1MWkRTRVU0cWhrUEY5Sy84Y3c2K0s3VVBkZUJkc3UxOElsYnZZSGhZdkk3cUJ5QmpqVWVQUk5ISVVHN1Zpc3pPdG9LQ1ZKaE5zV2FKZDN6YUw4QzB6ZGx6QlJJQXdhaDBhcTlmWWxqazlEMllWUVZJTDFoajRMc21xdEFSczA3SXppOFlBN0N6SWhTVmp0ZTA2MzNSdzJnL0EzRTA5bWF6clhSK1JqZTJlUFN0eFpWR3JnbzRDTjRDWnBldmZIL3JDN1Npd2dsVTJzeWNMN293SkhlRUlmVFpPNlNZTkJKQUVNbk9YVHUiLCJtYWMiOiIzZGMxZjZmOGQyYjMwZmFhNzQwNTNjM2QxNDI2YjhmZTRkYjJhYzVmNmM1M2UzNzEyZDA2OGE5NDBkZDFhMDgxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJtUHlOMndMNmk2Z3gzcHZ6K0g0VkE9PSIsInZhbHVlIjoielVwNGEvSjdZY3RVTzBCa1hOU25VaHVqc0k2WlRkMjNvZUFTU0g0NSszb0svMWVKcnRRTlhza1NtT3Q4S29aRmowY3o0dmZGVWYvYUdJOENiUmQ3eCtlZWdTTTIxcXhNYlMwUUdvWDFrbXZZTjZEeW0rOFl2MldERlNScTRwQitLamYyQy9JYS8rRkUvVGE0aUNRMklzMHErODYxcTRmN0xtbmRPN0VuM1RLZWJQeExoUERyWmZhTmhuMTJnT20wa2VUbEh3bkxITzFMQm5WaXA4VVpnaGgzUlF3ejhpcDZXemw3RVNQdDFJTmJhbjJvaVdyd0JTNlhTLzVpZ0trd0t1TFhxWnJUMXNScU9GQlhobnZjbWttWnFHaytCRHVhRTlKdklvQ2R2anZPTGxlTDcyYjZVZklGbG45SGhmbjBWaTNpT2JvK3dJa2VTMlhDbysrTG1naGJwajNoYmRXaG43TVBkS05aVGxlOHJySFhmaEg1bHRrVUdsejExbjd2NzhMZFNvLzdsSWR3TmNLUWJTM3JYLzg4ZTVIeXVUVEM4cC9wS0V1cGFLOUgxbXBzM3NNQWhCcGhYbUU3OTBGbTlJWnpiZkR6TGZNMHE5VHBNakJRbko1ZGJLUDlhdHA3UXpybTFBM281VnlPZnFCMlc4UDl3eGlmbTRmb3BMaXciLCJtYWMiOiI2ZjVhYzQ3MWQxYmE0MDhjOTZiMDMzMGUyOWY0M2IzOTM2ODIzNjdhMmE2YjVlZWQzYjdlN2QyZjJiNmZiYzk4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1qRkFWWGVjbTFDSlJWUWNDeG5HMWc9PSIsInZhbHVlIjoiOHBMVXZDOEJmSnJrKzdmRHErZ25ORkR4M0FhWktxWmFyeTFsZ2VWU2pJS0l5QU9KNGVpemFld2xuY2k4L3p0Qk1WV1FWaUJ0NHNKUnBHME9wS3BlRlZKa1lLNk90cEozV1Z5bjhNakRyVmlHb3JXVjdtQllXdDBHckIxc2RyK1BOb3VXQmR3aFNvTmpWMStRK2tUNkZnK0RXbjBBSXVCalZFbnhqb29yUU8zN1RhUzVrakRJaVFETitzaTFnZCtZSndYWkkvc2VCcm1CVHU4UUxMSFBZaTFMT3VHZ09LQlBkQzBBWWtqNERMQnRTNERqWDg3TUdXOGxDdS9pV25wUTM2WE54YzRuNmdwNnNiRTI4TlpiTU8vMk1MWkRTRVU0cWhrUEY5Sy84Y3c2K0s3VVBkZUJkc3UxOElsYnZZSGhZdkk3cUJ5QmpqVWVQUk5ISVVHN1Zpc3pPdG9LQ1ZKaE5zV2FKZDN6YUw4QzB6ZGx6QlJJQXdhaDBhcTlmWWxqazlEMllWUVZJTDFoajRMc21xdEFSczA3SXppOFlBN0N6SWhTVmp0ZTA2MzNSdzJnL0EzRTA5bWF6clhSK1JqZTJlUFN0eFpWR3JnbzRDTjRDWnBldmZIL3JDN1Npd2dsVTJzeWNMN293SkhlRUlmVFpPNlNZTkJKQUVNbk9YVHUiLCJtYWMiOiIzZGMxZjZmOGQyYjMwZmFhNzQwNTNjM2QxNDI2YjhmZTRkYjJhYzVmNmM1M2UzNzEyZDA2OGE5NDBkZDFhMDgxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208335778\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1690579820 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/enhanced-pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>enhanced_pos_cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>is_manual</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1690579820\", {\"maxDepth\":0})</script>\n"}}