{"__meta": {"id": "Xec593ab18b7a495cd844eaeffd60e291", "datetime": "2025-06-28 14:59:05", "utime": **********.900666, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.457019, "end": **********.900679, "duration": 0.*****************, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.457019, "relative_start": 0, "end": **********.838601, "relative_end": **********.838601, "duration": 0.*****************, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.838609, "relative_start": 0.****************, "end": **********.90068, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "62.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00313, "accumulated_duration_str": "3.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.871134, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 54.313}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.883488, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 54.313, "width_percent": 16.933}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.892867, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 71.246, "width_percent": 28.754}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C**********018%7C2%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhQQzhZaW1ia1RoVHhFK3lvZ3NLVUE9PSIsInZhbHVlIjoiQmlqSWJTaWduOW1TY3RzdFd5dmhZay9VaWxaR0FwblZFNnE0YWhObHFJMnFFSnZIZUFYdjMxWnd0NU9ZbUJkVXBVZE9BNHUza01ycUM5aGtJVVRtSlB6R1NoUjZoWHVCcnltZVdDaFAvUVhSbE4rL3V2djZ2M2hxdS85WTRuMEpMb3JOK1VMMlVJaWhNZkx2YnRnK0xESHBjb1ZLTjcwQVA1Uys2TE9Qbi96V2J1bllrOEp4UlNOQzRlT2lJdEM1UXJzK1FhY1hKdlpvbTd2N0hsaFYzZ3hiNjZzaXRKSFRReGkwL1lJL1N5OUxJRHdabkp0eTFGVU9ma3V3NmIxYW0vOGxjcGYzdTQvbWltMm5MRzJLZEwvNng0K0x2b3VySFZDQlVvbC9JNTB1MWE5em9TOTZTTnBpV0UwTTlOSWFJOW0yVlRYbWtMenhXdzhHMnVlTXRmdzdiY0V4eHJJamQyQysxbEpkNzBPUFhoak1XY09WT241QVdkeDVNeDZybmc4VFNDR3ByUTFXK2psTmFmd0pxdm1HQXpZT2xodXhNMjFpRmRjVDI1NzhPNVRFTUQyNEx0N0RBKyswQnVZVWMybE9iZzVjODk5SE5RdmZkK0JNazRtOURsTktrTUtIWWNoQU1ubnlEMGUrOVppQ0NyeW5iaEp0LzVMNmMzb3EiLCJtYWMiOiI0ZmNhYWVjMWViNTIxNWQwNjY3NjRjZTI3MjQ3N2IxMTQ2N2IzMzlkODhkZjgxOWFlODcyMjJhMjAxNGQ3NDliIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktVU095cjZGN0JEY1hQS2grWW81ZUE9PSIsInZhbHVlIjoibFNiL2I3MGtlT0tvdTBiVW9PWTd6RFpabDlJR3NLSExleEtJV1BWS3pYdTloVjVyUDFkWnhWK3UxL2laaUkxTHhxRWUzYStrS0xqeEJFek4xYnBuREZJNW5xUGRiRTBRcWVjSUhFbUxibHpJa0dLL0M2UFBsWU44Zm5PdUtoc21ackVMVmNkK1lsTnhrZXlZRG5ZdWNteW55RGlTRkNNbEhjbmphaFNUUU80SzVJMXUyT29pSHFNMklPRjB6dnJGTis0ZWpnODFLYUpTVmhybUkyTWZ0TlA0cE5oTjFiYzQxZ1c2eE1UTjNrN2sxL2hDU05LZnFXbGxzdG5KYUEraisxd0lmTi83eWRFVFBkdWpqbHk5aUFlWnhMUTIxbmN3bys4dlMxOUlnM0dncngvK2hSRnozS3JBWlk4MkZ5K1NWbmEzQVZzNXgzdHYxY1NsbTRZTFg0YllCYUxBbDBMVHorOUt4OU1WellvclM2eDdJM05hVHMrRHFTcXo2YVlEbEg0MDlFZUdYSzIvN1cyZktDYXJCVHlwdzVWak8zSHlOWGZTWFFkY1p1R0pZajIyZ1pYT3BNSTdlNHgzTWl5emVlNlBCVXZxS2szTExSa2FmaGw5UHJpNzl5SExqT3oyWUQ4UlNYMG4xYlF5TlF0dHliNmZULzlHSzRORE5NL0EiLCJtYWMiOiJjZjJiYzFhMzBhOWNhZGRmMTM3ZmM0NGMyNjNmYzcxMTBjYTcyMjUzZWI2YWE3NTMyMWE1ZTBlZGIxYWY2NDdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-187839910 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6nyGbgT6n8t00JbJ5CSgj7vkpXnxnyISs5XS6j6J</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-187839910\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-953482001 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:59:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImptVkU4ZzZ1L00zemhrSkswU05TVkE9PSIsInZhbHVlIjoiNDdCTVAvYkpMUExjYTdVUHFZYmM4N2NSRGUvTjA0RDJjaXZmakdNZ29qdUpZUkM2U3JneDI4TWdhdjlmc0lienNzMnpLSHd5U1ZGdWdqRExVODBiVHFucjZKNGpQM3UrYmkvMnZmN2ljSm5XQjNXc3ZMWWpjUXA3QjhPcVNFMlhSUm41ejhEWkFyNDdKMDAzUnpjZitNc0ViS0dPN05RcXNVaysrckJmSGVSQkdpMVAvVktkMHBpeFZlUUQ5U0QyUTFaU0ZmRTVGY1lJU3AzRml5RjRjSGF3UDRDSzloTUdFanl5cGFNUHNNRjFZdXZLalpHMm5vMkFIb1I3cmVZaXBpR3BEdHZiaEtsVmNXWU9CbWt3a2U5eTE2aDM5ZDdDNDBMVWxRQlRSaDBpUWwxaTBZNlc2WlBCaFdCMTFiWWxibUlzV0cxRk1PYUswcW1Od1ZGR0dZM0NJRFM1U0JraDJic2NzVEFEeFNmTlEvQ3pHZFo2NGZsd0RIMy9JVnUxVFdsT3podENoc3J2QjdGWG84VjF3TDZaYnpsODZzNFJ3TmJDNDAvR2lyQzFBZW9zNnMrOWVvZzNJenNWSjBkcmJCbXlKQXlkZXJFZFNUK3pjaldaRU0wakZodjY4YnpjcWhFYm4yaXNRV2EvTFlLY0VPVm1qMTBpV1Z2VjBwNGEiLCJtYWMiOiI2MGNjZmUyYjk0NGRmMzU2N2RkM2NmNDI3ZWIyOTBkZmE5NjE5OWQwNjRmNmRjYjVlMmZiYjNjYzA0ZGU4YTY3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdDZStQaDJPYVNEei9NU0xVUktqSFE9PSIsInZhbHVlIjoibktwREZxSlBIS1BlS2NqYVptbDl0dWNORFFxVmlqcE1mOHhqd2djd0hKa3FGUVYxRDJsYlVrOUUvN3ArQzlBdjFwZmVibnhMZDhzaUZwQ1hzY3h3WUhLbXNqV2N4QUlTbzBtTi9ETktrbnE2Z1BNQS9COVVMZlE4VHNFL2VHWTJOZ0ZGREZHQm5TeXFlM3FCcDVLeWNhQi9nVyt3L2F0N0FaKzdRVXF4NzVhUWs0amtncDJmWXFsQTMyUnFYZ0xlbHZXby9oRG5FTFVRd3NIMjBlYi9jRFFIRXV2N0duSnh2Q2RnaWhTdnZ1cTg3Umh6a2dzSzFzbDZmQ2xxbDY3Z2JhUk8zSytscnlzbS8yZUI5cjVIbTBLcjE1ODVjODBZT0htUlpwTFErMnRBSFNodXpkVnNlM015dmdWTmV3WkRad1JrL2RpYk5wOFpKMlhDV2NFcHh2dXhNSlpsZlE5dXlwdGNmZWJyQU5oejBjaVNoMktGUkZ6MHgxaC9PMU9qR0pObVJOeUdWRm5ENFA2MmsyMmZzQTREcnliMTZmanIyR3pYbnZtbnZZMkw0UVR1ZVpOVmRNaDkzZHJ6eStyNC9MZlJJRGxEOGdzUWZwSXdZQ24zRzhsY3FiVndkZWtObXB5L04rQ054ZG1xTWZJLzJrOURrTElUL2U2N2RVSEoiLCJtYWMiOiI3NjRmMThlMzcyNTkxYTFlMjU2NWYxNTg2YThiMTlkMWJkNmNjOWM1MGE1YWMwMmZmYzA0YTg3ODg4YTFjM2M2IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:59:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImptVkU4ZzZ1L00zemhrSkswU05TVkE9PSIsInZhbHVlIjoiNDdCTVAvYkpMUExjYTdVUHFZYmM4N2NSRGUvTjA0RDJjaXZmakdNZ29qdUpZUkM2U3JneDI4TWdhdjlmc0lienNzMnpLSHd5U1ZGdWdqRExVODBiVHFucjZKNGpQM3UrYmkvMnZmN2ljSm5XQjNXc3ZMWWpjUXA3QjhPcVNFMlhSUm41ejhEWkFyNDdKMDAzUnpjZitNc0ViS0dPN05RcXNVaysrckJmSGVSQkdpMVAvVktkMHBpeFZlUUQ5U0QyUTFaU0ZmRTVGY1lJU3AzRml5RjRjSGF3UDRDSzloTUdFanl5cGFNUHNNRjFZdXZLalpHMm5vMkFIb1I3cmVZaXBpR3BEdHZiaEtsVmNXWU9CbWt3a2U5eTE2aDM5ZDdDNDBMVWxRQlRSaDBpUWwxaTBZNlc2WlBCaFdCMTFiWWxibUlzV0cxRk1PYUswcW1Od1ZGR0dZM0NJRFM1U0JraDJic2NzVEFEeFNmTlEvQ3pHZFo2NGZsd0RIMy9JVnUxVFdsT3podENoc3J2QjdGWG84VjF3TDZaYnpsODZzNFJ3TmJDNDAvR2lyQzFBZW9zNnMrOWVvZzNJenNWSjBkcmJCbXlKQXlkZXJFZFNUK3pjaldaRU0wakZodjY4YnpjcWhFYm4yaXNRV2EvTFlLY0VPVm1qMTBpV1Z2VjBwNGEiLCJtYWMiOiI2MGNjZmUyYjk0NGRmMzU2N2RkM2NmNDI3ZWIyOTBkZmE5NjE5OWQwNjRmNmRjYjVlMmZiYjNjYzA0ZGU4YTY3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdDZStQaDJPYVNEei9NU0xVUktqSFE9PSIsInZhbHVlIjoibktwREZxSlBIS1BlS2NqYVptbDl0dWNORFFxVmlqcE1mOHhqd2djd0hKa3FGUVYxRDJsYlVrOUUvN3ArQzlBdjFwZmVibnhMZDhzaUZwQ1hzY3h3WUhLbXNqV2N4QUlTbzBtTi9ETktrbnE2Z1BNQS9COVVMZlE4VHNFL2VHWTJOZ0ZGREZHQm5TeXFlM3FCcDVLeWNhQi9nVyt3L2F0N0FaKzdRVXF4NzVhUWs0amtncDJmWXFsQTMyUnFYZ0xlbHZXby9oRG5FTFVRd3NIMjBlYi9jRFFIRXV2N0duSnh2Q2RnaWhTdnZ1cTg3Umh6a2dzSzFzbDZmQ2xxbDY3Z2JhUk8zSytscnlzbS8yZUI5cjVIbTBLcjE1ODVjODBZT0htUlpwTFErMnRBSFNodXpkVnNlM015dmdWTmV3WkRad1JrL2RpYk5wOFpKMlhDV2NFcHh2dXhNSlpsZlE5dXlwdGNmZWJyQU5oejBjaVNoMktGUkZ6MHgxaC9PMU9qR0pObVJOeUdWRm5ENFA2MmsyMmZzQTREcnliMTZmanIyR3pYbnZtbnZZMkw0UVR1ZVpOVmRNaDkzZHJ6eStyNC9MZlJJRGxEOGdzUWZwSXdZQ24zRzhsY3FiVndkZWtObXB5L04rQ054ZG1xTWZJLzJrOURrTElUL2U2N2RVSEoiLCJtYWMiOiI3NjRmMThlMzcyNTkxYTFlMjU2NWYxNTg2YThiMTlkMWJkNmNjOWM1MGE1YWMwMmZmYzA0YTg3ODg4YTFjM2M2IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:59:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953482001\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-961821408 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-961821408\", {\"maxDepth\":0})</script>\n"}}