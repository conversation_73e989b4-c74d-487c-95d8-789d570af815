{"__meta": {"id": "Xcee0367c7df4f7874bf5811d57737ae3", "datetime": "2025-06-28 16:03:28", "utime": **********.561371, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.121319, "end": **********.561384, "duration": 0.4400649070739746, "duration_str": "440ms", "measures": [{"label": "Booting", "start": **********.121319, "relative_start": 0, "end": **********.500009, "relative_end": **********.500009, "duration": 0.3786900043487549, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.50002, "relative_start": 0.37870097160339355, "end": **********.561385, "relative_end": 9.5367431640625e-07, "duration": 0.06136488914489746, "duration_str": "61.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45712304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013600000000000001, "accumulated_duration_str": "13.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.527643, "duration": 0.0128, "duration_str": "12.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.118}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.548056, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.118, "width_percent": 2.868}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5533218, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.985, "width_percent": 3.015}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1878700195 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1878700195\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1666733813 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1666733813\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1461553468 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1461553468\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1305790808 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=lmy3tk%7C2%7Cfx5%7C0%7C2005; _clsk=1mcm0n0%7C1751126591625%7C16%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlphamJ5WGRBaCt6cm9LaXQrNEhsUFE9PSIsInZhbHVlIjoiSjF4UWRyUllaM2RNOThPbDE3VDhicFh4MS9UZE5hVXgwNmloM043TlJUTTlFSC9nd1NzUzJXaGl0Ykdhb0RrWXZqOXdzR3RNdjAybVh2aVdBQlROOXpMVlphRUNKajdRS2U3WUpYczUyRm9Pc1R4bjlXWFdVTGhrd0dYU09SSGpEcnJxaWY1SnQrdkZoN0ppQUFyRWZ6cWU5NTQyQW9BQzN4YWFsSEFaSDB5Q3hLbGFpNW1ReGVFMGdqUWZ5TzF3SEw4VXpreVR2U05nTGZTcWx4REpvZElHVHJmY3RVTzNLeUM3aUxtS2JIc0ZPRjh4Vkd3aERPclR4eEd6dk9qa3B0RlpDbmtSM24waUJVMEtIWlZTc05sUWxZZXFtdTA5VWFUam41a3BVa0NIUDd5Si90eVFXNFRwVFEzdzk5L1YzcnFQa0I1VnFIM3h2OWpEU1ZkWGFsZHJwa2djcS9nYWo2N1lXUlErT1FWaWx0ekVPM3hPdWQ4MXZWMXJEdVEyMEVVU2pOOThUVVFsMlpBaCtYc3c0Q1FZVHhCdmNpMkM2SWlyckM2WkQvNTJmaGhFOUJBTWlWZi95T3JqSkZnOVZQRkxENVgvZHJGckFWeitYTzU3enlBSlYrMjJVSWQvNzJPUkpJTU1IZHJxTnNCdVZseTZUUWlaVkZBSDRDazkiLCJtYWMiOiJjZWFjZWQ3OGNlNDA0MTY1MjYzZjdlMWM0NDBmMGRiNGY1NTdiZTc4NDgxZWJhMjY0YTA1N2Y0MzM3M2JiODU3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJxSkIzd2dDNHZCektRTjduQlJXK0E9PSIsInZhbHVlIjoiWk5IWGZWMGlobmdYV24wNER0Uzd3YmVvSEx1Wjd5VUdPQ2FjOGFNV2RJREFmWHB0SFpmVjEreXBUR3I1N2o2N0RqV1NHRkN4dXRhRHBsSFhncUhDS1FzaEdHdDBiNjRPanVRSkdiditTaHkzbzdyMXlUQmhXU2ZBQlNWL1dMRy9TT1hPZWNJa0FpN05yenJOcnQveGRoN2M5U0ViYVk3RzZGb0txQWxWdnZjN1AxY0NhemVabE1VdTBuQk02M0NaNUkwQUk5V2xGc0xSeEdCOWlPcEdPOGxrMm5RVlVFRWJmemQ5NjJyL1h0U1FkcmZzdG1NbG9VdXJhZmlFSGVyTXFudGZ3U0YrMW1GRTlGbkdaWk9GTXBaYk5PODZLaDEwZ01Rbmp6dUpUbGtKcjJpT054UEduUlRuOEF4RnhaZnorckZtVlQwUFBmSGkrUGZnc1piUW56clJySE92NXo5eUxJZnpNd1NIK0VWTWh1SGdiQUU2bkh3NlBEWDZESU9iUy8ra3VLYWc1YnlBM2xBdXlpd0tUa0IzQTNzeDVvcXB3enNaV0dFbTZ6a3NORnp5Mm8rUkFhaktwdkpMUExlQjJmSDRkYWJkMHdpSEpqZ3poUU45a2ROY1Y1eFFsMFJycmRpTXFFZFBlVHpqWXJjMkh1VlBxOFVaZkx1VHZ4dFIiLCJtYWMiOiIyYjJkYWRlNDM2M2MyZGM1MjgxMjUxNGNkMTRiNzNlNDA0YmYxMzkzM2I1NDAwMjE4ZDM1ZWJmMGFiOWRmMWYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305790808\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1692275406 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJDOVwtinwv2ISuOlwspqY4F3ZCw2OW0LzS7NsCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692275406\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-792909286 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 16:03:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilo3d0tabDZXMjVnQ2IxMjdGS2plZ1E9PSIsInZhbHVlIjoiR1JDa0RJdi9MazBZSWh6L3dVSWVxK2pIQTc3VVVNcmJNT2RkZndSYUVHVWlsNWRoeUl5VzcyRzQveVpHb29Cektzbjk4QjZIb0RzK1VyQlFIRExWRWI2SVB2SGhxZHRRYy8wRGdwMi9RZHh5d0hpSHl2TGxGcmZ4Z2NXTmdqTy9OdHVGSzVkSXlZbGJwSGoyeGRnY2tpR2p6b1hpRFJyQ2lHNktPS1h5MGFRejhXWE5hTjdGZ0FSZTV2cjd3d2tyZExpYndubzhEUEZpenNPcCttSzlDUStxZG4zS0JJa28yZmYySVJ0SWE4elg2bndaVk8yNE9EeVNqbjBZNkJzcTZ0WklmLzl3MUVNMnJGYlRVak1YWUp4MmFRcUl1c0U3N244aEduYWdCdEtLdGE4bW9HZ2o1aGdiKzE4QTRZY2o3MUpDWHg4NjArSWNtWElOSjBDTHRzMkc5SkM2OG0vQXZSYkc2aHRPY3lOdnlWYnFNWnpyeXBpZmJBWmYvZEZZSWlObUVqbmVCZEtMWHhZVS9UZ1JKK2ZLUG5XOXhUVkRUM3g4NUtCZ2g1ZSszT2Zxalh5Qm1NRzgwaVF1WnJBQVJOK2owS3pyVEpqMkpZbzlWL3RFSWhhckF4OWtIZW1zTmVwUlhjNDBkcVJYcFdFb2JnRHVhODJlS1hrZEpXTCsiLCJtYWMiOiJhNmQ5NTQ4Yjk2MzA4YThjYmFjY2VlNWJmMjM3ZTFhMGRiN2VkMmI1ZDVlMThiNjFhOTc4YjQ4ZTY0MjdmN2IwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImhQYzE1Umt3SWxGSzZMbGs1Q0wwRlE9PSIsInZhbHVlIjoiTklJanlHcHluaE8zWTIxNUU0eXk2akNKajl6bEZ6NlV3S2JVeE5DV2NvbnNON254WHlVNXA3MS9aelF2SlJDaWtIWm9aZ2hMZWVKZ2hIOG0rbmZ1c3lrM0t0amxZaDJFQllxTHF3QjlSMy9TU0tweXh3Y3hpOC9xZ2xLUWQ3aWt1SjJkS1BOMHVrRWJrU1BPR0M5N2MzNDFieUJiM2VSQjI5UVRYYkpxMDQ3dFRMMzRoWlQ4eDJ3L0pKaU51VFdMczVBZytpQ1loU1FVcGZFbFQvWnBGME0yMllOc2doMlpkRFpmc2c4ZDlxSHBmZjE0RUxiNWVtTkJhbElHRS9kdHlLQWxDSlBiQ1RNLzFFNTdyNWJhdXQ0UVM5ZGJIVlBjT3hLNDhpOG9NdkdkRjljWHlXSzlPamZKMU1YbmJPZnBLK25USVM1eWpZTGswSVNIMiszTENMR0pjbktscW9Ya1ZheVI2bkhaSk1DdVZWZmhTSDRuRytzcW1tUVRERy9lNkkxdUZRbThzSlVzN1cvcHdpY2owZmhXZGZFZ2VDZnZIUS8zSERPbzlRNXoxaUpscTZhdG4vSml0c1VDMVlyMFhrN0hpOGFVODh5S1dldXlLNlFqeVp6Z2c5QThIVnBnY1FnemNUMDdtdS83a0phRVk1Ymk2T2dybzQ1RWVrNm4iLCJtYWMiOiI2YzdlYWVkMjJlNzZhYmQ2MmYzOTljNTM4ODMyYmU1Yzc3NTVhYTI3NDQ0NjBkY2FmZTBhNzliNzJiMTE5MDYwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 18:03:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilo3d0tabDZXMjVnQ2IxMjdGS2plZ1E9PSIsInZhbHVlIjoiR1JDa0RJdi9MazBZSWh6L3dVSWVxK2pIQTc3VVVNcmJNT2RkZndSYUVHVWlsNWRoeUl5VzcyRzQveVpHb29Cektzbjk4QjZIb0RzK1VyQlFIRExWRWI2SVB2SGhxZHRRYy8wRGdwMi9RZHh5d0hpSHl2TGxGcmZ4Z2NXTmdqTy9OdHVGSzVkSXlZbGJwSGoyeGRnY2tpR2p6b1hpRFJyQ2lHNktPS1h5MGFRejhXWE5hTjdGZ0FSZTV2cjd3d2tyZExpYndubzhEUEZpenNPcCttSzlDUStxZG4zS0JJa28yZmYySVJ0SWE4elg2bndaVk8yNE9EeVNqbjBZNkJzcTZ0WklmLzl3MUVNMnJGYlRVak1YWUp4MmFRcUl1c0U3N244aEduYWdCdEtLdGE4bW9HZ2o1aGdiKzE4QTRZY2o3MUpDWHg4NjArSWNtWElOSjBDTHRzMkc5SkM2OG0vQXZSYkc2aHRPY3lOdnlWYnFNWnpyeXBpZmJBWmYvZEZZSWlObUVqbmVCZEtMWHhZVS9UZ1JKK2ZLUG5XOXhUVkRUM3g4NUtCZ2g1ZSszT2Zxalh5Qm1NRzgwaVF1WnJBQVJOK2owS3pyVEpqMkpZbzlWL3RFSWhhckF4OWtIZW1zTmVwUlhjNDBkcVJYcFdFb2JnRHVhODJlS1hrZEpXTCsiLCJtYWMiOiJhNmQ5NTQ4Yjk2MzA4YThjYmFjY2VlNWJmMjM3ZTFhMGRiN2VkMmI1ZDVlMThiNjFhOTc4YjQ4ZTY0MjdmN2IwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImhQYzE1Umt3SWxGSzZMbGs1Q0wwRlE9PSIsInZhbHVlIjoiTklJanlHcHluaE8zWTIxNUU0eXk2akNKajl6bEZ6NlV3S2JVeE5DV2NvbnNON254WHlVNXA3MS9aelF2SlJDaWtIWm9aZ2hMZWVKZ2hIOG0rbmZ1c3lrM0t0amxZaDJFQllxTHF3QjlSMy9TU0tweXh3Y3hpOC9xZ2xLUWQ3aWt1SjJkS1BOMHVrRWJrU1BPR0M5N2MzNDFieUJiM2VSQjI5UVRYYkpxMDQ3dFRMMzRoWlQ4eDJ3L0pKaU51VFdMczVBZytpQ1loU1FVcGZFbFQvWnBGME0yMllOc2doMlpkRFpmc2c4ZDlxSHBmZjE0RUxiNWVtTkJhbElHRS9kdHlLQWxDSlBiQ1RNLzFFNTdyNWJhdXQ0UVM5ZGJIVlBjT3hLNDhpOG9NdkdkRjljWHlXSzlPamZKMU1YbmJPZnBLK25USVM1eWpZTGswSVNIMiszTENMR0pjbktscW9Ya1ZheVI2bkhaSk1DdVZWZmhTSDRuRytzcW1tUVRERy9lNkkxdUZRbThzSlVzN1cvcHdpY2owZmhXZGZFZ2VDZnZIUS8zSERPbzlRNXoxaUpscTZhdG4vSml0c1VDMVlyMFhrN0hpOGFVODh5S1dldXlLNlFqeVp6Z2c5QThIVnBnY1FnemNUMDdtdS83a0phRVk1Ymk2T2dybzQ1RWVrNm4iLCJtYWMiOiI2YzdlYWVkMjJlNzZhYmQ2MmYzOTljNTM4ODMyYmU1Yzc3NTVhYTI3NDQ0NjBkY2FmZTBhNzliNzJiMTE5MDYwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 18:03:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-792909286\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-511101256 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWWX3CXnNp783AXEpvQ6WiBdrPujaBATKor4YoYd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-511101256\", {\"maxDepth\":0})</script>\n"}}