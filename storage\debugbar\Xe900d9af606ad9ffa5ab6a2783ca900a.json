{"__meta": {"id": "Xe900d9af606ad9ffa5ab6a2783ca900a", "datetime": "2025-06-28 11:22:33", "utime": 1751109753.003118, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.538895, "end": 1751109753.003131, "duration": 0.4642360210418701, "duration_str": "464ms", "measures": [{"label": "Booting", "start": **********.538895, "relative_start": 0, "end": **********.913233, "relative_end": **********.913233, "duration": 0.37433815002441406, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.913242, "relative_start": 0.3743472099304199, "end": 1751109753.003133, "relative_end": 2.1457672119140625e-06, "duration": 0.08989095687866211, "duration_str": "89.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45297328, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0028099999999999996, "accumulated_duration_str": "2.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9844139, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.986}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.995646, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.986, "width_percent": 16.014}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-106385307 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-106385307\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-977592420 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-977592420\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-415254222 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-415254222\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1662899665 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IkhkRXlrN3hPUit3TUtlRFhvYWZZSVE9PSIsInZhbHVlIjoiZHF1MjdoMytrcmlycUdOLzdBWEd6ZmFZY2NJbmNHOWdiU1hXQ3NGMzV0ZWRkdzNjY2YzWWFpcmU3Z3VKaE5TNXh2WFZ0TkFUNEhsK1dQNDI5N1dhQ2NIY1lBMWpLMnlMbnQ0bnM3N2tTQklxWUZLUmJZSUhJMHhBaDk4ZzBqZkdSKzNzUzJDaVF5S0V2Vld2VW1OajJPTk9jWmdzcTJab3hkelRjdlQwcE9iQVlTRkVTTE9HVWFlRnRqMmJWZllkS2hLeG1xUlRJSjZCVmtJZS9kc0hzcVcwTHQyM3Rxc1ZtSVJPOXlSeTQvV0tqVSsyODhHNnVsQTVQQWZKN21pZ3cwOE5wVjV1UjdVZTc1MzcvMVhVbzhJeUlaYUtnY3VpMmhqeUV2MS9VR2JpUVlhV0VtOVlMcmp4S1pGbU1PT24wUFJKS2xuaGdYTm1WMnVXdXdTVXBQZkhpNmNHQmRUaUtuV0t6Qmg5Ylhua0d4cU1KL1NkVDdHZFIyTTdmRHo5ZU9oOXkrb2pXTElsbkxETU83UnRKQlp6T2M3L3BJMFFuRXNnTlFqdDdvV3d4NEt5ZWhOUG9Pamp6UWxKZWU3NXFHM0ZvdktNSW05MWpTOXZFb3h5akR6OVJlZGxJbEs5T2xFUTFtcUdXNHN1ZUdlNUZ2aVp6Vm9yZUQ4eVd5Y0YiLCJtYWMiOiI4Yjg3OThmMzNmN2UyZmMwYTM0MDQ4MTFkYWJiOWJiYTAyZDk4Y2NlZjk4N2NlZDdmMTM0MjJkNzVhMDRjNjY4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhPSmw4T3JyWEJHTCtEazRKdmpFUnc9PSIsInZhbHVlIjoiTC9GczR2MDRBU1lLWGEybHNQTUdQM1MxV2FFTHNGYUdZSGFEbnNFMmVFU0tZTVlYTSs2ZEkrMzg1eEtkaThUMHEwM1hsbE9jK1F0TjN0U3dBQk9KUytqUG9ta3pHOEptNWhPTUZoenVWT1kyYW01QUZHT2Jyc1RReW16SEVxQUlHZHI1ZS8ycjhmcUlRYWVWY1lJcTF4S2VvUlYrZ0JQRk50bVFGY3pWbndZQXNPNWFpTUx1cWFoOTVTUTE1dGdVVTNhb3gydnNjd2VPMWVUdnBQZHBUbmdOVFJadnordUdkRDJUalQxK0RVWjFSUmg4K2VQMUpQbFVXWUt5ZktIRStydC93ZzFVbGhlV3BJK1JLNTUzamxjaDNGL2hpSWcrSVBtL2N3WmNBb1gyOTZSTUlsNVZUVVUrTko3K2czUlVMalBRRWJGc0toUGE1ZWJnZ0x6TVhZRDFRc0hEWXZIL01tczhKeHlvQjdiOFc4MGFWWUNVL0pGSlFjOGZhSzhlSnkxMkRmUVFXM0tVc2lwZXZlYzlEbzlLUHlvdmp0TmF0OHl1TTl1TTFWNFBFZ1diblU3MGRDMlZ0ZVMyQ1hrSFY4Z0kyK015ZTJwbituOHErbUJhTFM3ZnZwd2EzSHRPK2hjZDBjUnRCMlFncDdibSt0T05FRytKNzZ5R0gzT24iLCJtYWMiOiI5OWY5MGYzY2Q3ZThkMmQyMDExYWViNGI3ZWViOTE0Y2UzYzc4OThiMzk4YjYyOGYzY2JjZDBhZWQ0ZjMwNWQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662899665\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-787630518 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0qglG3KN3FeSk3uXGiEyo52F26BW5xWASgb9bhi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-787630518\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2137177511 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 11:22:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhZdXduaFdKZDZTUnI1Vm5IM0FDMVE9PSIsInZhbHVlIjoic0lZRWVDeVFkS0lLMnlNZld4RDhUWkpxNEFoaEJzVTl5TUtuY09aWGl0bmRXV2lZWjlmdWlPTGhUNCtQY3NXSWRXU3RtK2M2U2NZSDRzSmRaQmUyeEZqNDdhZW55VytmYWNTWUJpRzZ5Snh1c1JPb1JEM1Fyb1YzaDJWSEZKczNXZmRqdHNjeTZSck5ESFF2cjRmWnVmNDJORys4cXNhbzdmRG42OVBJZEpxR09JdHJoNEZLMGV0QWRQYjlkWUhlVGI2SUx1ako1SDlCSkpTRk1hZjhXcFF1cjE2cE02TGh0ZG9nV01wOVZWbk44RUtjYktqZFNlK2dhYjlYZjFPc1AxMjhDdCt6UHczL3ltSDRZb2RYSGdFZkl3TExvRHJ0REhNKzVJOHo3YXE1eVo1U1NvUkhmRjBSYXp4NmphWFQ0NUtnekwwNG82Vm1SdXNCdFRXY2NUallvZFVueDNjUm03Qm92ekdQcEVydlRhZ2dqUVFOSm1YdVcvKytrb2cxVjYvUHR1VzNCWVpNNit3NUVFVlF6Y3lCdUhSVmFjbzcyNFp0SktNQWE2V28xN2VOV1daUmZqUjY1c0FTajZ4UUswTWVmeXY0VHNldkNRaHdoVU41UEVTWlZ4azcvY2w1cWJFMytNb3BaamJic1F1bVltVmNFS083TGtwOUllQmIiLCJtYWMiOiIwMTg2NTA3NDM2YzJjYmVhY2MyZTI3ZmIzYmIyYTI5YzQ5NTE5ZTI1YmU1N2IwNTBhNDNkZTM2NTNmYzNlMjBmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Iml5Q0locXRzdGsvM21EVmFoa2lZM1E9PSIsInZhbHVlIjoiUENGN0F6cFg2QWsxdzB3ZUd6TWx6RXBXZWNWZzZ1VzJzNVVhUXMyTE51UnZSN0lsT2lRUGw5ejg4WnFrTUdLVEJmUDl1bVROeHpoZUhIaEVOZWY2VXpSRm10c2JjQTROUnlvNUVzZFNreUd2SFcybkdYWTY2NlBYc2k2a1BlZ0t6V2hYaXlSbEw2VzVHaGs1VFNFU25tZlBXbC83OGpETTdNYlpWaE05cmtmc3FveVczRG9xZWZCMU9QOTE5RnZsdHBZekkxdkRoMENYYzFicS9VeVZVMjJNU2NUc1Nib2duQlAzQW0zQXh3L2taY0twVEcwNHdPbGlBL0g3eTh2UU5rM2ttWWRRdWllc3N5UGNJeSt4NGNyWFRCaE9udEdTL0xrd0FDV0V2RGdSRmI4OHdaWit3THo3UERqQXFieXlYTDArcG16MThuM05oR014cEFzTkE5MFhJYTc5dkpKa29qNUtYTk9OTmE3bXJZc2tMYU5heEJRSW43UU4zeFpEZ3NVK2oxRERrWktoSVkva2dGeHRFK0Rla1JuWklZWEI3c1F3WWI3SUI0TFZDbEpUTnE4SEptWmVjL0YrVWlPOEo5WEllSEliUUUrbEJIMXF6R0FkSTd3TTk3cVdMYVVaWlM0bm5iL2JOSGViVTFaMlBNVEhqNzFxZU05Z0xJVHUiLCJtYWMiOiIyZTBlNmNhODM0Yjk5OGVlZTA1MzRiZjA4M2VkODczYWQxNGNjMGNhMjc2NjBkM2I3MzE0NDQ1ZmI0YjAxYTYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 13:22:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhZdXduaFdKZDZTUnI1Vm5IM0FDMVE9PSIsInZhbHVlIjoic0lZRWVDeVFkS0lLMnlNZld4RDhUWkpxNEFoaEJzVTl5TUtuY09aWGl0bmRXV2lZWjlmdWlPTGhUNCtQY3NXSWRXU3RtK2M2U2NZSDRzSmRaQmUyeEZqNDdhZW55VytmYWNTWUJpRzZ5Snh1c1JPb1JEM1Fyb1YzaDJWSEZKczNXZmRqdHNjeTZSck5ESFF2cjRmWnVmNDJORys4cXNhbzdmRG42OVBJZEpxR09JdHJoNEZLMGV0QWRQYjlkWUhlVGI2SUx1ako1SDlCSkpTRk1hZjhXcFF1cjE2cE02TGh0ZG9nV01wOVZWbk44RUtjYktqZFNlK2dhYjlYZjFPc1AxMjhDdCt6UHczL3ltSDRZb2RYSGdFZkl3TExvRHJ0REhNKzVJOHo3YXE1eVo1U1NvUkhmRjBSYXp4NmphWFQ0NUtnekwwNG82Vm1SdXNCdFRXY2NUallvZFVueDNjUm03Qm92ekdQcEVydlRhZ2dqUVFOSm1YdVcvKytrb2cxVjYvUHR1VzNCWVpNNit3NUVFVlF6Y3lCdUhSVmFjbzcyNFp0SktNQWE2V28xN2VOV1daUmZqUjY1c0FTajZ4UUswTWVmeXY0VHNldkNRaHdoVU41UEVTWlZ4azcvY2w1cWJFMytNb3BaamJic1F1bVltVmNFS083TGtwOUllQmIiLCJtYWMiOiIwMTg2NTA3NDM2YzJjYmVhY2MyZTI3ZmIzYmIyYTI5YzQ5NTE5ZTI1YmU1N2IwNTBhNDNkZTM2NTNmYzNlMjBmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Iml5Q0locXRzdGsvM21EVmFoa2lZM1E9PSIsInZhbHVlIjoiUENGN0F6cFg2QWsxdzB3ZUd6TWx6RXBXZWNWZzZ1VzJzNVVhUXMyTE51UnZSN0lsT2lRUGw5ejg4WnFrTUdLVEJmUDl1bVROeHpoZUhIaEVOZWY2VXpSRm10c2JjQTROUnlvNUVzZFNreUd2SFcybkdYWTY2NlBYc2k2a1BlZ0t6V2hYaXlSbEw2VzVHaGs1VFNFU25tZlBXbC83OGpETTdNYlpWaE05cmtmc3FveVczRG9xZWZCMU9QOTE5RnZsdHBZekkxdkRoMENYYzFicS9VeVZVMjJNU2NUc1Nib2duQlAzQW0zQXh3L2taY0twVEcwNHdPbGlBL0g3eTh2UU5rM2ttWWRRdWllc3N5UGNJeSt4NGNyWFRCaE9udEdTL0xrd0FDV0V2RGdSRmI4OHdaWit3THo3UERqQXFieXlYTDArcG16MThuM05oR014cEFzTkE5MFhJYTc5dkpKa29qNUtYTk9OTmE3bXJZc2tMYU5heEJRSW43UU4zeFpEZ3NVK2oxRERrWktoSVkva2dGeHRFK0Rla1JuWklZWEI3c1F3WWI3SUI0TFZDbEpUTnE4SEptWmVjL0YrVWlPOEo5WEllSEliUUUrbEJIMXF6R0FkSTd3TTk3cVdMYVVaWlM0bm5iL2JOSGViVTFaMlBNVEhqNzFxZU05Z0xJVHUiLCJtYWMiOiIyZTBlNmNhODM0Yjk5OGVlZTA1MzRiZjA4M2VkODczYWQxNGNjMGNhMjc2NjBkM2I3MzE0NDQ1ZmI0YjAxYTYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 13:22:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137177511\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1557784869 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KH9lpPB074dNGTuPG504Qf5rihJ2zsh5I73pZEhl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557784869\", {\"maxDepth\":0})</script>\n"}}