{"__meta": {"id": "X5316e907bbb815321dba297e05aa2c83", "datetime": "2025-06-28 15:03:07", "utime": **********.443329, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.034574, "end": **********.443342, "duration": 0.4087679386138916, "duration_str": "409ms", "measures": [{"label": "Booting", "start": **********.034574, "relative_start": 0, "end": **********.367503, "relative_end": **********.367503, "duration": 0.3329288959503174, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.367512, "relative_start": 0.33293795585632324, "end": **********.443343, "relative_end": 9.5367431640625e-07, "duration": 0.07583093643188477, "duration_str": "75.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46176232, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.020410000000000005, "accumulated_duration_str": "20.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.400945, "duration": 0.01514, "duration_str": "15.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 74.179}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.424906, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 74.179, "width_percent": 2.254}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4276469, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "kdmkjkqknb", "start_percent": 76.433, "width_percent": 3.479}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.429502, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "kdmkjkqknb", "start_percent": 79.912, "width_percent": 14.454}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.434619, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "kdmkjkqknb", "start_percent": 94.366, "width_percent": 2.744}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '15'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4364438, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "kdmkjkqknb", "start_percent": 97.109, "width_percent": 2.891}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-303318206 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-303318206\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1579671884 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1579671884\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1917488770 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1917488770\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx5%7C0%7C1998; _clsk=mwmaeg%7C1751122972644%7C7%7C1%7Cz.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikp5ZXJNc0c1eVNqR1BPRVBKNnZGN0E9PSIsInZhbHVlIjoiaGVoVmVnNklvRDdWTjU0UFVrbjBEajluVUVBVDdxVnNQVVZqeHNCNE92WCtPUDZSNzFBZ3N5b2ZoVmU2eU9ONWliSTFTZmVqTDdpR2t2Yjh5UVA0cFRXTzVqYnZDaHIrNnJucnNJVTZWcVV5d2NzOEJPRFRIejU4dVdHUktHdXJzRjVEOHdiMnhHazRqbzNPNi9YbElhVmJYUGtjSnN1TGdLamRXbTI0QmJCSGduWHdHQjB1elp5dHVvVjJUM1pSLzdYK1FYOGFURTluMWtnQmE2VFArSlVWNy96dmNKU1kxRG0xbkxWSjEvcUJXTjZwbFhhbVZvdzV1dElFV3NiSGsxR2dIOHUrZlI5VGt2MDVIWWVZcVZjSU1wWlFpMXdOYjMrQ0h6bUNUNjdCZUdVY0s1R1FqQzYvN3kvQWx6Q1pndlRZM3J6SVhnYjJ3czZHZWI0Q1ZxT0NDc0dsWjMrQ2Z1VnlEaTBDc05DOE9hbG9tUVRIMWV0aEM0R093bk9JbG51cVpidm1Xdk15NFpVWDR1WC9saUdGZVIyV01uaWRTYUJEQnhjeEVvM1R6dTA1SDEwWmdwTVozdmNsOEFyUEk3M0xodEFLTVpZZWowUnZuZ2tpVWdDdDArbExEV05TK2dibFl2VXdvV05HeFB6YmJOUFpWT3hPcTVVSEFIM0siLCJtYWMiOiIwNzlhOTIxYmMwOTczM2NhZTA0M2I1ZWUyZjIzOTg1ZTk2MDlkNjk3N2ZjNTVlODJiZjk2YTQzMDY2OWFhNmY5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkViZTBkeVR1NXlKYVNPNUtoQjZyTVE9PSIsInZhbHVlIjoiQ0p1ZHZvazhuc1RINjNWWU9UNDBKL0RKMjZqb0ZDT1FYVnJpZTE0L3hocW1mZUZRQmZEM3R2cHhub1c1QmNkUVFxU1BCNmNtaEdyNktPa2I1K3E1SXpWRFdPeWdJajhJSmd0WXVoZkxUdkRNTHpCSXNEeUx4N1BVZXMyak8vRFkxRmorU1MxZ2szZEE4MjdxdG93QlJ0K2daMkVwVFBSaHd2bDdYaHZicWVwS25aaGxPWmNJTUJKTDc5VE9YcmVMakxObG5TaDZtUXhHWUxIOXc4ZEprcCtRdTVDN1VUSkpKczdvZGFBTU9OSy9vR2hMdlVDRkp3YXFZZlVtMVYxTHNCTjgzaTBxOFVqVXFvR1cwSmRQU3lWOTBzcFBoMHdiRllydUlQVkN2eU4zV08xaUh2SmZUY1NudHFvVnhmTmZ6Y3lIaFFQbXo2ZkRkY1dLNVlzS0xHLzNiMGk5MmNiMWxMRzMrQk05NEdZbVZyM0d3SjdjWlgrampiZHIybEs2UlF2Vm1BeEQ0TCt0QzBBT0pqR2o0NlQzYWNTTG1lbG9uNUZCZ2d4amI2b2Y0d0s2SjgwbXVmQjEyNXp4R1kxaEJaQnRFNmJ2QzFGMmhybE1kdzZHRlhjUlNpSlYvMEFmTjhyUk5iMXp0a2xTaXJlR05LVCs4SlFPNDBlektZRmsiLCJtYWMiOiIzNTA0NjUzMGFlMjY5OTc0NTIzNTlkMjAyZjRlOGU0Y2JlNWU2NmY2MTJiYWFkNDAzYWFlMDM0NTM4NWRkOGQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1145885111 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iYT4nrzPUxGxSzPJk97IlyR7LzP8xJoJLvINJh9A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145885111\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1628981536 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 15:03:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVBejhkNnR3czBCL1RocWJScXRXR1E9PSIsInZhbHVlIjoiZS9RSzNKZ0pWTndjdU9ocjE3Qlo5S0JwaG4waXRvdjhFVzU5ekN5VkR5MVRhaUZkQmVpRy9WZ0dIemlXcXFFTnN0OEtPOWRMamh6R2xFek43N1JQQTlJZ1J3MWZoVUMyNG1HN2pCUDdhbWs1dDVCbUFpeVhyMjZlU3UxVlg3eSttREp0VjFkQXRocGdQeEZFYzN4UGtPUDQwR0U3TURZTXcraUpUbVdrbVdYei9XQThkVG1KVERsTk1QSVRGWEY3d09USTZJTUh5bVUvT2lVTFpmRC9Zai9BY2xwSmV6L05uQWJtc3YrZWswMlNxajAvWmgvTmU1Vmkwakh1U2cxUmY2YW0vMXRiektLREZBOURSRTltTS9PSkxZMlh1ZmlaczhaZFVGRUk1L3BnM2piZDRwRHlWemdtWTc1THl4eUc2TTJNcyt5bFUrSlFOL3VQcGpOY2tzRVE4U1ZITitGZ3ZjVVhzVTVqRzBzWjgyVlZ2ckRCRjBkSkNMOVh2T0JQaWVSMkk1dzhSRmxHSlQ3dDNRaE8wTlJJVkQydVFqVzVsYVlJMGloOXVDMTFpUktFR1BWZ0xUclJvYzVRT25CaFpCaDJyM3U5b1VjZENWcmJ4UlJCVHU1OWNrYlRFM1dYS3FNOVpBU3FrbGM3VXJpb3VIUXdCaW51dXhocGozKy8iLCJtYWMiOiIxNDkxZjdiZDM0Y2YxNzE5Nzk2NDdlZjJhYjk5OTI2MGU3ZTM3ZDI5ZDc4M2Q5Y2MzZDRlZWM4N2EyMGJiMGQwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InkyM1MvWWZ0YjRiSFFqd3NoL2dTTnc9PSIsInZhbHVlIjoiWHpuZlNxd2VJRndoSmZRWkhSVFhZMmU3Q3VMVTE3Q3FpNjBNbHcrMGZLQUR4MzA4WTV4RGZaZlBlUlN1bURQeHBTZjQ3cDFtMzd4NjdrdzVtRU0xUGM2bzh2aGlzNmQ4NEdxdFU4MjlUZ0lSdXpxMVZWYUd5am5tY3lLK2Z3eURXajd3WlpsZnNaZWRIYjA4NWMyT1hONVljRkg1ekx3blZDY1B3Vi9Ob2szeWkyZGxPSnhKQklwc0hwU1Y2UmtQT1lSZUhoOERDS1dKOTRzV3EwaXFZTEJLeWowRU1BeS85Z3VpZFk3SHQvc3hqaUEvK0hpb1FYa3hQYmRDNFZoVEdZb3QwVWQxM0ZWTzV1MWJVUlg4NHlHZk9yQWFiV0RqT1ZwZFJlbHgycE9PUnRiVVZ4cDRYTlpROVNOeXhNSmhvTkhJb0lTazVlTTZmODFUMldNbE1sd3dwRi82QmxGV1NILzd1elg5QUQ2Y0tSdWNILzVDeWk4NUIxNE1yaTJXTmR6cXlRcFRHME04eFpUY2g2SHpVeFhkeE80RU9lUzB2VXljSXdwd1dhR3p2VHc0QmtTVXJjbi9sU2RUK2lRYUNMd004WWRYUXNGVGhDSko3eGxheFd4YmhnYWFuNVowN3J6MC9ScGVjdUpVUTYwbDhoUVJZbTBRSE9waUV4UUUiLCJtYWMiOiJiM2ZjY2Q2NGIxY2M2MGFkMDY1YzJlYjM2NjQyNmE1OTk3NzdlODYzNWNmYmRkNDlmMWRhYWRmNDU1Mzc4NWM5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 17:03:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVBejhkNnR3czBCL1RocWJScXRXR1E9PSIsInZhbHVlIjoiZS9RSzNKZ0pWTndjdU9ocjE3Qlo5S0JwaG4waXRvdjhFVzU5ekN5VkR5MVRhaUZkQmVpRy9WZ0dIemlXcXFFTnN0OEtPOWRMamh6R2xFek43N1JQQTlJZ1J3MWZoVUMyNG1HN2pCUDdhbWs1dDVCbUFpeVhyMjZlU3UxVlg3eSttREp0VjFkQXRocGdQeEZFYzN4UGtPUDQwR0U3TURZTXcraUpUbVdrbVdYei9XQThkVG1KVERsTk1QSVRGWEY3d09USTZJTUh5bVUvT2lVTFpmRC9Zai9BY2xwSmV6L05uQWJtc3YrZWswMlNxajAvWmgvTmU1Vmkwakh1U2cxUmY2YW0vMXRiektLREZBOURSRTltTS9PSkxZMlh1ZmlaczhaZFVGRUk1L3BnM2piZDRwRHlWemdtWTc1THl4eUc2TTJNcyt5bFUrSlFOL3VQcGpOY2tzRVE4U1ZITitGZ3ZjVVhzVTVqRzBzWjgyVlZ2ckRCRjBkSkNMOVh2T0JQaWVSMkk1dzhSRmxHSlQ3dDNRaE8wTlJJVkQydVFqVzVsYVlJMGloOXVDMTFpUktFR1BWZ0xUclJvYzVRT25CaFpCaDJyM3U5b1VjZENWcmJ4UlJCVHU1OWNrYlRFM1dYS3FNOVpBU3FrbGM3VXJpb3VIUXdCaW51dXhocGozKy8iLCJtYWMiOiIxNDkxZjdiZDM0Y2YxNzE5Nzk2NDdlZjJhYjk5OTI2MGU3ZTM3ZDI5ZDc4M2Q5Y2MzZDRlZWM4N2EyMGJiMGQwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InkyM1MvWWZ0YjRiSFFqd3NoL2dTTnc9PSIsInZhbHVlIjoiWHpuZlNxd2VJRndoSmZRWkhSVFhZMmU3Q3VMVTE3Q3FpNjBNbHcrMGZLQUR4MzA4WTV4RGZaZlBlUlN1bURQeHBTZjQ3cDFtMzd4NjdrdzVtRU0xUGM2bzh2aGlzNmQ4NEdxdFU4MjlUZ0lSdXpxMVZWYUd5am5tY3lLK2Z3eURXajd3WlpsZnNaZWRIYjA4NWMyT1hONVljRkg1ekx3blZDY1B3Vi9Ob2szeWkyZGxPSnhKQklwc0hwU1Y2UmtQT1lSZUhoOERDS1dKOTRzV3EwaXFZTEJLeWowRU1BeS85Z3VpZFk3SHQvc3hqaUEvK0hpb1FYa3hQYmRDNFZoVEdZb3QwVWQxM0ZWTzV1MWJVUlg4NHlHZk9yQWFiV0RqT1ZwZFJlbHgycE9PUnRiVVZ4cDRYTlpROVNOeXhNSmhvTkhJb0lTazVlTTZmODFUMldNbE1sd3dwRi82QmxGV1NILzd1elg5QUQ2Y0tSdWNILzVDeWk4NUIxNE1yaTJXTmR6cXlRcFRHME04eFpUY2g2SHpVeFhkeE80RU9lUzB2VXljSXdwd1dhR3p2VHc0QmtTVXJjbi9sU2RUK2lRYUNMd004WWRYUXNGVGhDSko3eGxheFd4YmhnYWFuNVowN3J6MC9ScGVjdUpVUTYwbDhoUVJZbTBRSE9waUV4UUUiLCJtYWMiOiJiM2ZjY2Q2NGIxY2M2MGFkMDY1YzJlYjM2NjQyNmE1OTk3NzdlODYzNWNmYmRkNDlmMWRhYWRmNDU1Mzc4NWM5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 17:03:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1628981536\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1438220193 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2Ga71Bh8C5UIRJgD5L8CG3PlN1aW8gWPM1PfyTxT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1438220193\", {\"maxDepth\":0})</script>\n"}}